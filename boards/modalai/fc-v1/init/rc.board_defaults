#!/bin/sh
#
# board specific defaults
#------------------------------------------------------------------------------

#
# Summary:
#   Flight Core can be used on many airframes, but is meant to be paired with
#   VOXL (either through a cable or in the combo board flavor).  For this reason
#   this script has a bit more Logic (aka <PERSON>) than normal.
#
# Flight Core Version Information:
#   V106 - Flight Core Stand Alone configuration
#   V110 - Flight Core VOXL-Flight configuration
#

#
# Common settings across Flight Core configurations
#


# TELEM2 is used to connect to VOXL via the MAV_1 mavlink channel
# MAV_0 & TELEM1 are left alone for use with SIK radio links
param set-default MAV_1_CONFIG 102
param set-default MAV_1_MODE 2
param set-default SER_TEL2_BAUD 921600

safety_button start
