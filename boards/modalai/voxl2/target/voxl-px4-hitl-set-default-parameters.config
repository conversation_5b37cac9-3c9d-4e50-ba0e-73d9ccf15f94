# Param setting and declaring file

param select /data/px4/param/hitl_parameters

# Sys config parameters
param set SYS_AUTOSTART 1001
param set SYS_HITL 1

# Make sure we are running at 800Hz on IMU
param set IMU_GYRO_RATEMAX 800

# Some parameters for control allocation
param set CA_ROTOR_COUNT 4
param set CA_AIRFRAME 0
param set CA_ROTOR_COUNT 4
param set CA_ROTOR0_PX 0.15
param set CA_ROTOR0_PY 0.25
param set CA_ROTOR1_PX -0.15
param set CA_ROTOR1_PY -0.19
param set CA_ROTOR2_PX 0.15
param set CA_ROTOR2_PY -0.25
param set CA_ROTOR2_KM -0.05
param set CA_ROTOR3_PX -0.15
param set CA_ROTOR3_PY 0.19
param set CA_ROTOR3_KM -0.05

# Sensor calibration parameters
param set CAL_ACC0_ID 2490378
param set CAL_ACC0_PRIO 50
param set CAL_ACC0_XOFF -0.018255233764648438
param set CAL_ACC0_XSCALE 1.000119328498840332
param set CAL_ACC0_YOFF 0.097194194793701172
param set CAL_ACC0_YSCALE 1.003928661346435547
param set CAL_ACC0_ZOFF 0.081269264221191406
param set CAL_ACC0_ZSCALE 0.992971897125244141
param set CAL_ACC1_PRIO 50
param set CAL_ACC2_PRIO 50
param set CAL_ACC3_PRIO 50
param set CAL_AIR_CMODEL 0
param set CAL_AIR_TUBED_MM 1.500000000000000000
param set CAL_AIR_TUBELEN 0.200000002980232239
param set CAL_GYRO0_ID 2490378
param set CAL_GYRO0_PRIO 50
param set CAL_GYRO0_XOFF 0.013671654276549816
param set CAL_GYRO0_YOFF -0.000422076700488105
param set CAL_GYRO0_ZOFF -0.003227389883249998
param set CAL_GYRO1_PRIO 50
param set CAL_GYRO2_PRIO 50
param set CAL_GYRO3_PRIO 50
param set CAL_MAG0_ID 396809
param set CAL_MAG0_PRIO 75
param set CAL_MAG0_ROT 0
param set CAL_MAG0_XODIAG -0.011825157329440117
param set CAL_MAG0_XOFF -0.011212680488824844
param set CAL_MAG0_XSCALE 1.001187443733215332
param set CAL_MAG0_YODIAG 0.022539729252457619
param set CAL_MAG0_YOFF -0.030884368345141411
param set CAL_MAG0_YSCALE 0.940797865390777588
param set CAL_MAG0_ZODIAG -0.006671304814517498
param set CAL_MAG0_ZOFF -0.097350947558879852
param set CAL_MAG0_ZSCALE 1.050295352935791016
param set CAL_MAG1_PRIO 50
param set CAL_MAG2_PRIO 50
param set CAL_MAG3_PRIO 50

# Commander parameters
param set COM_CPU_MAX 0
param set COM_DISARM_PRFLT -1
param set COM_RC_IN_MODE 1
param set NAV_RCL_ACT 1
param set COM_FLIGHT_UUID 15

# EKF2 parameters
param set EKF2_ABL_LIM 0.8
param set EKF2_EV_DELAY 5.0
param set EKF2_IMU_POS_X 0.027
param set EKF2_IMU_POS_Y 0.009
param set EKF2_IMU_POS_Z -0.019
param set EKF2_MAG_DECL 3.2

# Control allocator parameters for HIL
param set HIL_ACT_FUNC1 101
param set HIL_ACT_FUNC2 102
param set HIL_ACT_FUNC3 103
param set HIL_ACT_FUNC4 104
param set PWM_MAIN_FUNC1 101
param set PWM_MAIN_FUNC2 102
param set PWM_MAIN_FUNC3 103
param set PWM_MAIN_FUNC4 104

# Mavlink parameters
param set MAV_TYPE 2

# Autotune parameters
param set MC_AT_EN 1

# RC Mapping parameters
param set RC_MAP_PITCH 2
param set RC_MAP_ROLL 1
param set RC_MAP_THROTTLE 3
param set RC_MAP_YAW 4
param set RC_MAP_FLTMODE 6
param set RC_MAP_KILL_SW 7

# CBRK parameters
param set CBRK_SUPPLY_CHK 894281

# Landing parameters
param set LND_FLIGHT_T_LO 483791313

param save
