
#!/bin/sh
# PX4 commands need the 'px4-' prefix in bash.
# (px4-alias.sh is expected to be in the PATH)
. px4-alias.sh

# uorb start

param select /data/px4/param/parameters

# Load in all of the current parameters that have been saved in the file
param load

# Fake Accel calibration parameters for testing
param set CAL_ACC0_ID 2490378
param set CAL_ACC0_PRIO 75
param set CAL_ACC0_ROT -1
param set CAL_ACC0_XOFF -0.005
param set CAL_ACC0_XSCALE 1.008
param set CAL_ACC0_YOFF 0.008
param set CAL_ACC0_YSCALE 1.0008
param set CAL_ACC0_ZOFF -0.1
param set CAL_ACC0_ZSCALE 1.008

# Fake gyro calibration parameters for testing
param set CAL_GYRO0_ID 2490378
param set CAL_GYRO0_PRIO 75
param set CAL_GYRO0_ROT 0
param set CAL_GYRO0_XOFF -0.0008
param set CAL_GYRO0_YOFF -0.03
param set CAL_GYRO0_ZOFF 0.002

param save

sleep 2

/usr/bin/px4-shutdown
