#!/bin/sh
#
# board specific sensors init
#------------------------------------------------------------------------------
board_adc start

# Internal ICM-20602
icm20602 -s -b 1 -R 8 start

# Internal SPI bus BMI088 accel & gyro
if bmi088 -A -s -b 5 -R 8 start
then
	bmi088 -G -s -b 5 -R 8 start
else
	# otherwise try BMI085
	bmi085 -A -s -b 5 -R 8 start
	bmi085 -G -s -b 5 -R 8 start
fi

# Internal ICM-20948 (with magnetometer)
icm20948 -s -b 1 -R 8 -M start

# Interal DPS310 (barometer)
dps310 -s -b 2 start
