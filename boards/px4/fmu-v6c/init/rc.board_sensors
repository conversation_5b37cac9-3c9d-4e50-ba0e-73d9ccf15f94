#!/bin/sh
#
# PX4 FMUv6C specific board sensors init
#------------------------------------------------------------------------------
board_adc start

if ver hwtypecmp V6C000002 V6C002002
then
       # Internal SPI BMI088 accel/gyro
       bmi088 -A -R 4 -s start
       bmi088 -G -R 4 -s start
else
       # Internal SPI bus BMI055 accel/gyro
       bmi055 -A -R 4 -s start
       bmi055 -G -R 4 -s start
fi

# Internal SPI bus ICM42688p
icm42688p -R 6 -s start

# Internal barometer on I2C4 (The same bus is also exposed externally, and therefore marked as external)
ms5611 -X -b 4 -a 0x77 start

# Internal compass on IMU I2C4 (The same bus is also exposed externally, and therefore marked as external)
ist8310 -X -b 4 -a 0xc  start

# External compass on GPS/I2C1 (the 3rd external bus): standard Holybro Pixhawk 4 or CUAV V5 GPS/compass puck (with lights, safety button, and buzzer)
ist8310 -X -b 1 -R 10 start
