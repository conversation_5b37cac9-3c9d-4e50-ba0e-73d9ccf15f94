#
# This file is autogenerated: PLEASE DO NOT EDIT IT.
#
# You can use "make menuconfig" to make any modifications to the installed .config file.
# You can then do "make savedefconfig" to generate a new defconfig file that includes your
# modifications.
#
# CONFIG_DEV_NULL is not set
CONFIG_ARCH="arm"
CONFIG_ARCH_BOARD_CUSTOM=y
CONFIG_ARCH_BOARD_CUSTOM_DIR="../../../../boards/px4/io-v2/nuttx-config"
CONFIG_ARCH_BOARD_CUSTOM_DIR_RELPATH=y
CONFIG_ARCH_BOARD_CUSTOM_NAME="px4"
CONFIG_ARCH_CHIP="stm32"
CONFIG_ARCH_CHIP_STM32=y
CONFIG_ARCH_CHIP_STM32F100C8=y
CONFIG_ARMV7M_USEBASEPRI=y
CONFIG_ARM_MPU_EARLY_RESET=y
CONFIG_BOARD_LOOPSPERMSEC=2000
CONFIG_DEBUG_FULLOPT=y
CONFIG_DEBUG_SYMBOLS=y
CONFIG_DEFAULT_SMALL=y
CONFIG_DISABLE_MOUNTPOINT=y
CONFIG_FDCLONE_DISABLE=y
CONFIG_FDCLONE_STDIO=y
CONFIG_HAVE_CXX=y
CONFIG_HAVE_CXXINITIALIZE=y
CONFIG_IDLETHREAD_STACKSIZE=316
CONFIG_INIT_ENTRYPOINT="user_start"
CONFIG_INIT_STACKSIZE=1100
CONFIG_MM_FILL_ALLOCATIONS=y
CONFIG_MM_SMALL=y
CONFIG_NAME_MAX=12
CONFIG_PREALLOC_TIMERS=0
CONFIG_RAM_SIZE=8192
CONFIG_RAM_START=0x20000000
CONFIG_RAW_BINARY=y
CONFIG_SERIAL_TERMIOS=y
CONFIG_STACK_COLORATION=y
CONFIG_START_DAY=30
CONFIG_START_MONTH=11
CONFIG_STDIO_DISABLE_BUFFERING=y
CONFIG_STM32_ADC1=y
CONFIG_STM32_DISABLE_IDLE_SLEEP_DURING_DEBUG=y
CONFIG_STM32_DMA1=y
CONFIG_STM32_JTAG_SW_ENABLE=y
CONFIG_STM32_USART1=y
CONFIG_STM32_USART2=y
CONFIG_STM32_USART3=y
CONFIG_STM32_USART_SINGLEWIRE=y
CONFIG_TASK_NAME_SIZE=0
CONFIG_USART1_RXBUFSIZE=64
CONFIG_USART1_RXDMA=y
CONFIG_USART1_SERIAL_CONSOLE=y
CONFIG_USART1_TXBUFSIZE=32
CONFIG_USART2_RXBUFSIZE=64
CONFIG_USART2_TXBUFSIZE=64
CONFIG_USART3_RXBUFSIZE=64
CONFIG_USART3_RXDMA=y
CONFIG_USART3_TXBUFSIZE=64
CONFIG_USEC_PER_TICK=1000
