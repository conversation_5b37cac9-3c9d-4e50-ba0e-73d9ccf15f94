/* Static */
*(.text.arm_ack_irq)
*(.text.arm_doirq)
*(.text.arm_svcall)
*(.text.arm_switchcontext)
*(.text.board_autoled_on)
*(.text.clock_timer)
*(.text.exception_common)
*(.text.hrt_absolute_time)
*(.text.hrt_call_enter)
*(.text.hrt_tim_isr)
*(.text.imxrt_configwaitints)
*(.text.imxrt_dma_callback)
*(.text.imxrt_dmach_interrupt)
*(.text.imxrt_dmach_xfrsetup)
*(.text.imxrt_dmaterminate)
*(.text.imxrt_edma_interrupt)
*(.text.imxrt_endwait)
*(.text.imxrt_gpio3_16_31_interrupt)
*(.text.imxrt_interrupt)
*(.text.imxrt_lpi2c_isr)
*(.text.imxrt_lpspi_exchange)
*(.text.imxrt_recvdma)
*(.text.imxrt_tcd_free)
*(.text.imxrt_timerisr)
*(.text.imxrt_usbinterrupt)
*(.text.irq_dispatch)
*(.text.ioctl)
*(.text.memcpy)
*(.text.memset)
*(.text.nxsched_add_blocked)
*(.text.nxsched_add_prioritized)
*(.text.nxsched_add_readytorun)
*(.text.nxsched_get_files)
*(.text.nxsched_get_tcb)
*(.text.nxsched_merge_pending)
*(.text.nxsched_process_timer)
*(.text.nxsched_remove_blocked)
*(.text.nxsched_remove_readytorun)
*(.text.nxsched_resume_scheduler)
*(.text.nxsched_suspend_scheduler)
*(.text.nxsem_add_holder)
*(.text.nxsem_add_holder_tcb)
*(.text.nxsem_clockwait)
*(.text.nxsem_foreachholder)
*(.text.nxsem_freecount0holder)
*(.text.nxsem_freeholder)
*(.text.nxsem_post)
*(.text.nxsem_release_holder)
*(.text.nxsem_restore_baseprio)
*(.text.nxsem_tickwait)
*(.text.nxsem_timeout)
*(.text.nxsem_trywait)
*(.text.nxsem_wait)
*(.text.nxsem_wait_uninterruptible)
*(.text.nxsig_timedwait)
*(.text.sched_lock)
*(.text.sched_note_resume)
*(.text.sched_note_suspend)
*(.text.sched_unlock)
*(.text.strcmp)
*(.text.sq_addafter)
*(.text.sq_addlast)
*(.text.sq_rem)
*(.text.sq_remafter)
*(.text.sq_remfirst)
*(.text.uart_connected)
*(.text.up_block_task)
*(.text.up_unblock_task)
*(.text.wd_timer)
*(.text.wd_start)
*(.text._do_memcpy)
