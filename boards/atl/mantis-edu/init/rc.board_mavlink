#!/bin/sh
#
# board specific MAVLink startup script.
#------------------------------------------------------------------------------

# MAVLink instance for Gimbal control & generic WiFi connection (e.g. QGC)
set GIMBAL_TTY /dev/ttyS3
set MAV_RATE 40000
set BAUDRATE 500000

mavlink start -r ${MAV_RATE} -d ${GIMBAL_TTY} -b ${BAUDRATE} -x
mavlink stream -d ${GIMBAL_TTY} -s SYSTEM_TIME -r 0.5
mavlink stream -d ${GIMBAL_TTY} -s AUTOPILOT_STATE_FOR_GIMBAL_DEVICE -r 20
mavlink stream -d ${GIMBAL_TTY} -s GIMBAL_DEVICE_SET_ATTITUDE -r 20

# optical flow
mavlink start -d /dev/ttyS2 -m custom -b 500000
