#!/bin/sh
#
# Mamba F405 MK2 specific board sensors init
#------------------------------------------------------------------------------

board_adc start

if ! mpu6000 -R 6 -s start
then
	# some boards such as the Hobbywing XRotor F4 G2 use the ICM-20602
	icm20602 -s -R 6 start
fi

if ! hmc5883 -T -X start
then	
	if mpu9250_i2c -X -b 2 -a 0x68 -R 28 start; then
		sleep 1 # wait for mpu9250 to be configured with bypass enabled
		ak8963 -X -b 2 -R 30 start # -R 34
		sleep 1
		mpu9250_i2c stop
		ak8963 -X -b 2 -R 30 start # -R 34
	fi
fi

bmp280 -X -b 2 start
