#
# For a description of the syntax of this configuration file,
# see misc/tools/kconfig-language.txt.
#
config BOARD_HAS_PROBES
	bool "Board provides GPIO or other Hardware for signaling to timing analyze."
	default y
	---help---
		This board provides GPIO 6-wr-SPI_INT_N, 6-wr-<PERSON><PERSON>_CS_N, 6-wr-<PERSON>I_RDY_N as
		PROBE_1-3 to provide timing signals from selected drivers.

config BOARD_USE_PROBES
	bool "Enable the use of the board provided GPIO Probes"
	default n
	depends on BOARD_HAS_PROBES

	---help---
		Select to use GPIO GPIO 6-wr-SPI_INT_N, 6-wr-<PERSON>I_CS_N, 6-wr-<PERSON><PERSON>_RDY_N as PROBE_1-3
		to provide timing signals from selected drivers.
