#
# This file is autogenerated: PLEASE DO NOT EDIT IT.
#
# You can use "make menuconfig" to make any modifications to the installed .config file.
# You can then do "make savedefconfig" to generate a new defconfig file that includes your
# modifications.
#
# CONFIG_FS_PROCFS_EXCLUDE_ENVIRON is not set
# CONFIG_NET_ETHERNET is not set
# CONFIG_NET_IPv4 is not set
# CONFIG_NSH_CMDOPT_HEXDUMP is not set
# CONFIG_S32K1XX_EDMA_HOE is not set
CONFIG_ARCH="arm"
CONFIG_ARCH_BOARD_CUSTOM=y
CONFIG_ARCH_BOARD_CUSTOM_DIR="../../../../boards/nxp/ucans32k146/nuttx-config"
CONFIG_ARCH_BOARD_CUSTOM_DIR_RELPATH=y
CONFIG_ARCH_BOARD_CUSTOM_NAME="px4"
CONFIG_ARCH_CHIP="s32k1xx"
CONFIG_ARCH_CHIP_S32K146=y
CONFIG_ARCH_CHIP_S32K14X=y
CONFIG_ARCH_CHIP_S32K1XX=y
CONFIG_ARCH_STACKDUMP=y
CONFIG_ARMV7M_MEMCPY=y
CONFIG_ARMV7M_USEBASEPRI=y
CONFIG_BCH=y
CONFIG_BOARDCTL_RESET=y
CONFIG_BOARD_LOOPSPERMSEC=3997
CONFIG_BUILTIN=y
CONFIG_DEBUG_FEATURES=y
CONFIG_DEBUG_FULLOPT=y
CONFIG_DEBUG_HARDFAULT_ALERT=y
CONFIG_DEBUG_SYMBOLS=y
CONFIG_DEBUG_TCBINFO=y
CONFIG_DISABLE_MQUEUE=y
CONFIG_DISABLE_POSIX_TIMERS=y
CONFIG_EXAMPLES_HELLO=y
CONFIG_FS_CROMFS=y
CONFIG_FS_PROCFS=y
CONFIG_FS_PROCFS_MAX_TASKS=16
CONFIG_FS_ROMFS=y
CONFIG_HAVE_CXX=y
CONFIG_HAVE_CXXINITIALIZE=y
CONFIG_I2C=y
CONFIG_I2CTOOL_DEFFREQ=100000
CONFIG_I2CTOOL_MAXADDR=0x7f
CONFIG_I2CTOOL_MAXBUS=0
CONFIG_I2CTOOL_MINADDR=0x00
CONFIG_INIT_ENTRYPOINT="nsh_main"
CONFIG_INIT_STACKSIZE=3500
CONFIG_INTELHEX_BINARY=y
CONFIG_LIBC_STRERROR=y
CONFIG_LPI2C0_DMA=y
CONFIG_LPUART0_BAUD=38400
CONFIG_LPUART0_IFLOWCONTROL=y
CONFIG_LPUART0_OFLOWCONTROL=y
CONFIG_LPUART0_RXBUFSIZE=600
CONFIG_LPUART0_RXDMA=y
CONFIG_LPUART0_TXBUFSIZE=1100
CONFIG_LPUART0_TXDMA=y
CONFIG_LPUART1_RXBUFSIZE=128
CONFIG_LPUART1_RXDMA=y
CONFIG_LPUART1_SERIAL_CONSOLE=y
CONFIG_LPUART1_TXBUFSIZE=128
CONFIG_LPUART1_TXDMA=y
CONFIG_MOTOROLA_SREC=y
CONFIG_MTD=y
CONFIG_MTD_PARTITION=y
CONFIG_MTD_PARTITION_NAMES=y
CONFIG_NET=y
CONFIG_NETDEV_IFINDEX=y
CONFIG_NET_CAN=y
CONFIG_NET_CAN_NOTIFIER=y
CONFIG_NET_CAN_RAW_TX_DEADLINE=y
CONFIG_NET_CAN_SOCK_OPTS=y
CONFIG_NET_TIMESTAMP=y
CONFIG_NSH_ARCHINIT=y
CONFIG_NSH_BUILTIN_APPS=y
CONFIG_NSH_CROMFSETC=y
CONFIG_NSH_DISABLE_PRINTF=y
CONFIG_NSH_DISABLE_TRUNCATE=y
CONFIG_NSH_LINELEN=128
CONFIG_NSH_MAXARGUMENTS=15
CONFIG_NSH_NESTDEPTH=8
CONFIG_NSH_ROMFSETC=y
CONFIG_NSH_ROMFSSECTSIZE=128
CONFIG_NSH_STRERROR=y
CONFIG_NSH_VARS=y
CONFIG_PREALLOC_TIMERS=4
CONFIG_PSEUDOTERM=y
CONFIG_RAM_SIZE=126976
CONFIG_RAM_START=0x1fff0000
CONFIG_RAW_BINARY=y
CONFIG_RR_INTERVAL=200
CONFIG_RTC=y
CONFIG_S32K1XX_EDMA=y
CONFIG_S32K1XX_EDMA_EDBG=y
CONFIG_S32K1XX_EDMA_ELINK=y
CONFIG_S32K1XX_EDMA_NTCD=64
CONFIG_S32K1XX_EEEPROM=y
CONFIG_S32K1XX_FLEXCAN0=y
CONFIG_S32K1XX_FLEXCAN1=y
CONFIG_S32K1XX_GPIOIRQ=y
CONFIG_S32K1XX_LPI2C0=y
CONFIG_S32K1XX_LPI2C_DMA=y
CONFIG_S32K1XX_LPI2C_DYNTIMEO=y
CONFIG_S32K1XX_LPSPI0=y
CONFIG_S32K1XX_LPSPI0_DMA=y
CONFIG_S32K1XX_LPSPI_DMA=y
CONFIG_S32K1XX_LPUART0=y
CONFIG_S32K1XX_LPUART1=y
CONFIG_S32K1XX_PORTAINTS=y
CONFIG_S32K1XX_PORTBINTS=y
CONFIG_S32K1XX_PORTCINTS=y
CONFIG_S32K1XX_PORTDINTS=y
CONFIG_S32K1XX_PORTEINTS=y
CONFIG_S32K1XX_RTC=y
CONFIG_SCHED_HPWORK=y
CONFIG_SCHED_HPWORKPRIORITY=249
CONFIG_SCHED_HPWORKSTACKSIZE=1280
CONFIG_SCHED_INSTRUMENTATION=y
CONFIG_SCHED_INSTRUMENTATION_EXTERNAL=y
CONFIG_SCHED_INSTRUMENTATION_SWITCH=y
CONFIG_SCHED_LPWORK=y
CONFIG_SCHED_LPWORKPRIORITY=50
CONFIG_SCHED_LPWORKSTACKSIZE=1632
CONFIG_SCHED_WAITPID=y
CONFIG_SERIAL_IFLOWCONTROL_WATERMARKS=y
CONFIG_SERIAL_TERMIOS=y
CONFIG_SIG_DEFAULT=y
CONFIG_SPITOOL_MAXBUS=2
CONFIG_STACK_COLORATION=y
CONFIG_START_DAY=18
CONFIG_START_MONTH=8
CONFIG_START_YEAR=2019
CONFIG_STDIO_DISABLE_BUFFERING=y
CONFIG_SYMTAB_ORDEREDBYNAME=y
CONFIG_SYSTEM_I2CTOOL=y
CONFIG_SYSTEM_NSH=y
CONFIG_SYSTEM_SPITOOL=y
CONFIG_WATCHDOG=y
