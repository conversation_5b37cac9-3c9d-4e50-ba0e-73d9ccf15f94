#
# NXP S32K146 - 1x ARM Cortex-M4 @ up to 180 MHz
#

adapter_khz 4000
transport select swd

if { [info exists CHIPNAME] } {
	set _CHIPNAME $CHIPNAME
} else {
	set _CHIPNAME s32k146
}

#
# M4 JTAG mode TAP
#
if { [info exists M4_JTAG_TAPID] } {
	set _M4_JTAG_TAPID $M4_JTAG_TAPID
} else {
	set _M4_JTAG_TAPID 0x4ba00477
}

#
# M4 SWD mode TAP
#
if { [info exists M4_SWD_TAPID] } {
	set _M4_SWD_TAPID $M4_SWD_TAPID
} else {
	set _M4_SWD_TAPID 0x2ba01477
}

source [find target/swj-dp.tcl]

if { [using_jtag] } {
	set _M4_TAPID $_M4_JTAG_TAPID
} else {
	set _M4_TAPID $_M4_SWD_TAPID
}

swj_newdap $_CHIPNAME m4 -irlen 4 -ircapture 0x1 -irmask 0xf \
				-expected-id $_M4_TAPID

target create $_CHIPNAME.m4 cortex_m -chain-position $_CHIPNAME.m4

# S32K146 has 64+60 KB contiguous SRAM
if { [info exists WORKAREASIZE] } {
	set _WORKAREASIZE $WORKAREASIZE
} else {
	set _WORKAREASIZE 0x1F000
}
$_CHIPNAME.m4 configure -work-area-phys 0x1FFF0000 \
                        -work-area-size $_WORKAREASIZE -work-area-backup 0

$_CHIPNAME.m4 configure -rtos nuttx

if { ![using_hla] } {
	cortex_m reset_config vectreset
}
