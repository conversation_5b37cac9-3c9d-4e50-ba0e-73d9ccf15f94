#!/bin/sh
#
# NXP MR-CANHUBK3 specific board sensors init
#------------------------------------------------------------------------------
if ver hwtypecmp MR-CANHUBK3-ADAP
then
	icm42688p -c 2 -b 3 -R 0 -S -f 15000 start
	# Internal magnetometer on I2c on ADAP
	bmm150 -X -a 18 start
	ist8310 -X -b 1 -R 10 start
	# ADC for voltage input sensing
	board_adc start

	# External SPI bus ICM20649
	icm20649 -b 4 -S -R 10 start

	# External SPI bus ICM42688p
	icm42688p -c 1 -b 3 -R 10 -S -f 15000 start
else
	bmm150 -X start
	# External compass on GPS1/I2C1 (the 3rd external bus): standard Holybro Pixhawk 4 or CUAV V5 GPS/compass puck (with lights, safety button, and buzzer)
	ist8310 -X -b 2 -R 10 start

	# External SPI bus ICM20649
	icm20649 -b 4 -S -R 6 start

	# External SPI bus ICM42688p
	icm42688p -c 1 -b 3 -R 6 -S -f 15000 start
fi

# External compass on GPS1/I2C1 (the 3rd external bus): Drotek RTK GPS with LIS3MDL Compass
lis3mdl -X -b 2 -R 2 start

# Disable startup of internal baros if param is set to false
if param compare SENS_INT_BARO_EN 1
then
	bmp388 -X -a 0x77 start
fi
