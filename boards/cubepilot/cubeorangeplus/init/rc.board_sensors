#!/bin/sh
#
# Board specific sensors init
#------------------------------------------------------------------------------
board_adc start

# Variants
# 1. Isolated {ICM42688p, ICM20948(with mag)}, body-fixed {ICM20649}
# 2. Isolated {ICM42688p, ICM42688p}, body-fixed  {ICM20649, ICM45686, AK09918}
# 3. Isolated {ICM42688p, ICM42688p}, body-fixed  {ICM45686, AK09918}
# 4. Isolated {ICM45686, ICM45686}, body-fixed  {ICM45686, AK09918}

# SPI4 is isolated, SPI1 is body-fixed

# SPI4, isolated
ms5611 -s -b 4 start

if icm42688p -s -b 4 -R 10 -q start -c 15
then
	if ! icm20948 -s -b 4 -R 10 -M -q start
	then
		icm42688p -s -b 4 -R 6 start -c 13
	fi
else
	icm45686 -s -b 4 -R 10 start -c 15
	icm45686 -s -b 4 -R 6 start -c 13
fi

# SPI1, body-fixed
if icm45686 -s -b 1 -R 3 -q start
then
	# if we detected the ICM45686 we also ought to have an AK09918
	ak09916 start -I -R 13
else
	icm20649 -s -b 1 start

fi

ms5611 -s -b 1 start

