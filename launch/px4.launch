<?xml version="1.0"?>
<launch>
    <!-- Posix SITL PX4 launch script -->
    <!-- Launches Only PX4 SITL. This can be used by external projects -->

    <!-- PX4 config arguments -->
    <arg name="est" default="ekf2"/>
    <arg name="vehicle" default="iris"/>
    <arg name="ID" default="0"/>
    <arg name="interactive" default="true"/>

    <env name="PX4_SIM_MODEL" value="gazebo-classic_$(arg vehicle)" />
    <arg unless="$(arg interactive)" name="px4_command_arg1" value="-d"/>
    <arg     if="$(arg interactive)" name="px4_command_arg1" value=""/>
    <node name="sitl_$(arg ID)" pkg="px4" type="px4" output="screen" args="$(find px4)/build/px4_sitl_default/etc -s etc/init.d-posix/rcS -i $(arg ID) $(arg px4_command_arg1)">
    </node>
</launch>
