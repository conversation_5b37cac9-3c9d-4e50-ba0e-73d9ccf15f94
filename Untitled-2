graph TD
    A[GPS硬件设备] --> B[GPS驱动 gps.cpp]
    B --> C[sensor_gps uORB主题]
    C --> D[GPS_RAW_INT MAVLink流]
    C --> E[VehicleGPSPosition模块]
    E --> F[vehicle_gps_position uORB主题]
    F --> G[EKF2估计器]
    G --> H[vehicle_global_position]
    G --> I[GLOBAL_POSITION_INT MAVLink流]
    
    subgraph "GPS驱动层"
        B1[GPS协议解析]
        B2[GPS数据验证]
        B3[sensor_gps发布]
        B1 --> B2
        B2 --> B3
    end
    
    subgraph "sensor_gps主题"
        C1[实例0 - 主GPS]
        C2[实例1 - 备用GPS]
        C3[原始GPS数据]
        C4[未经融合处理]
        C5[直接来自GPS驱动]
    end
    
    subgraph "GPS_RAW_INT处理"
        D1[订阅sensor_gps实例0]
        D2[直接数据转换]
        D3[格式转换为MAVLink]
        D4[发送到地面站]
        D1 --> D2
        D2 --> D3
        D3 --> D4
    end
    
    subgraph "VehicleGPSPosition处理"
        E1[订阅多个sensor_gps]
        E2[GPS融合算法]
        E3[GPS选择逻辑]
        E4[发布融合结果]
        E1 --> E2
        E2 --> E3
        E3 --> E4
    end
    
    subgraph "EKF2处理"
        G1[订阅vehicle_gps_position]
        G2[GPS数据验证]
        G3[坐标转换]
        G4[卡尔曼滤波融合]
        G5[状态估计输出]
        G1 --> G2
        G2 --> G3
        G3 --> G4
        G4 --> G5
    end
    
    subgraph "数据特征对比"
        J1[GPS_RAW_INT特征]
        J2[- 原始GPS数据]
        J3[- 未经EKF融合]
        J4[- 未经VehicleGPSPosition处理]
        J5[- 直接来自GPS驱动]
        J6[- 单GPS实例数据]
        
        K1[GLOBAL_POSITION_INT特征]
        K2[- EKF融合后数据]
        K3[- 经过状态估计]
        K4[- 多传感器融合]
        K5[- 更高精度和稳定性]
        K6[- 滤波后的位置]
    end
    
    subgraph "代码证据"
        L1[GPS_RAW_INT.hpp第60行]
        L2[uORB::Subscription _sensor_gps_sub{ORB_ID(sensor_gps), 0}]
        L3[直接订阅sensor_gps实例0]
        L4[无任何处理或融合]
    end
    
    B --> B1
    C --> C1
    C1 --> C3
    C2 --> C3
    C3 --> C4
    C4 --> C5
    
    D --> D1
    E --> E1
    G --> G1
    
    J1 --> J2
    J2 --> J3
    J3 --> J4
    J4 --> J5
    J5 --> J6
    
    K1 --> K2
    K2 --> K3
    K3 --> K4
    K4 --> K5
    K5 --> K6
    
    L1 --> L2
    L2 --> L3
    L3 --> L4