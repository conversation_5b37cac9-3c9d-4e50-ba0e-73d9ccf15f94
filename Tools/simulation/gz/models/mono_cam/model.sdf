<?xml version="1.0" encoding="UTF-8"?>
<sdf version='1.9'>
  <model name='mono_cam'>
    <pose>0 0 0 0 0 0</pose>
    <self_collide>false</self_collide>
    <static>false</static>
    <link name="mono_cam/base_link">
      <inertial>
        <pose>0.03 0.03 0.03 0 0 0</pose>
        <mass>0.050</mass>
        <inertia>
          <ixx>0.00004</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.00004</iyy>
          <iyz>0</iyz>
          <izz>0.00004</izz>
        </inertia>
      </inertial>
      <sensor name="imager" type="camera">
        <pose>0 0 0 0 0 0</pose>
        <camera>
          <horizontal_fov>1.74</horizontal_fov>
          <image>
            <width>640</width>
            <height>480</height>
          </image>
          <clip>
            <near>0.1</near>
            <far>3000</far>
          </clip>
        </camera>
        <always_on>1</always_on>
        <update_rate>30</update_rate>
        <visualize>true</visualize>
        <topic>camera</topic>
      </sensor>
      <gravity>true</gravity>
      <velocity_decay/>
    </link>
  </model>
</sdf>
