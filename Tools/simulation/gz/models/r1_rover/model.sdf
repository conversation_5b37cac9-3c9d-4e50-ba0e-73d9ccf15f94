<sdf version='1.6'>
	<model name='r1_rover'>
    <link name='base_link'>
	    	<pose>0 0 0 0 -0 0</pose>
			<inertial>
				<pose>0 0 0 0 -0 0</pose>
				<mass>20.0</mass>
				<inertia>
					<ixx>0.37083</ixx>
					<ixy>0.0</ixy>
					<ixz>0.0</ixz>
					<iyy>0.37083</iyy>
					<iyz>0.0</iyz>
					<izz>0.53333</izz>
				</inertia>
			</inertial>
			<collision name='odom_fixed_joint_lump__base_link_collision'>
				<pose>0 0 0.15 0 -0 0</pose>
				<geometry>
					<box>
					<size>0.4 0.4 0.25</size>
					</box>
				</geometry>
				<surface>
					<contact>
						<ode/>
					</contact>
					<friction>
						<ode/>
					</friction>
				</surface>
			</collision>
			<visual name='odom_fixed_joint_lump__base_link_visual'>
				<pose>0 0 0 0 -0 0</pose>
				<geometry>
					<mesh>
						<scale>1 1 1</scale>
						<uri>model://r1_rover/meshes/chassis_link.STL</uri>
					</mesh>
				</geometry>
				<material>
					<diffuse>1.0 1.0 1.0</diffuse>
					<specular>1.0 1.0 1.0</specular>
				</material>
			</visual>
			<visual name='odom_fixed_joint_lump__top_link_visual_1'>
				<pose>0 0 0.114486 0 -0 0</pose>
				<geometry>
					<mesh>
						<scale>1 1 1</scale>
						<uri>model://r1_rover/meshes/top_link.STL</uri>
					</mesh>
				</geometry>
				<material>
					<diffuse>1.0 1.0 1.0</diffuse>
					<specular>1.0 1.0 1.0</specular>
				</material>
			</visual>
			<visual name='odom_fixed_joint_lump__battery_link_visual_2'>
				<pose>-0.09302 -0.000128 0.114486 0 -0 0</pose>
				<geometry>
					<mesh>
						<scale>1 1 1</scale>
						<uri>model://r1_rover/meshes/battery_link.STL</uri>
					</mesh>
				</geometry>
				<material>
					<diffuse>0.0 0.0 0.0</diffuse>
					<specular>0.5 0.5 0.5</specular>
				</material>
			</visual>
			<visual name='odom_fixed_joint_lump__housing_link_visual_3'>
				<pose>0.03473 0 0.114486 0 -0 0</pose>
				<geometry>
					<mesh>
						<scale>1 1 1</scale>
						<uri>model://r1_rover/meshes/housing_link.STL</uri>
					</mesh>
				</geometry>
				<material>
					<diffuse>0.0 0.0 0.0</diffuse>
					<specular>0.5 0.5 0.5</specular>
				</material>
			</visual>
			<visual name='odom_fixed_joint_lump__l_antenna_link_visual_4'>
				<pose>-0.1565 0.0762 0.114486 0 -0 0</pose>
				<geometry>
					<mesh>
						<scale>1 1 1</scale>
						<uri>model://r1_rover/meshes/antenna_link.STL</uri>
					</mesh>
				</geometry>
				<material>
					<diffuse>0.0 0.0 0.0</diffuse>
					<specular>0.5 0.5 0.5</specular>
				</material>
			</visual>
			<visual name='odom_fixed_joint_lump__r_antenna_link_visual_5'>
				<pose>-0.1565 -0.0762 0.114486 0 -0 0</pose>
				<geometry>
					<mesh>
						<scale>1 1 1</scale>
						<uri>model://r1_rover/meshes/antenna_link.STL</uri>
					</mesh>
				</geometry>
				<material>
					<diffuse>0.0 0.0 0.0</diffuse>
					<specular>0.5 0.5 0.5</specular>
				</material>
			</visual>
			<velocity_decay/>
			<velocity_decay/>
			<velocity_decay/>
			<velocity_decay/>
			<velocity_decay/>
			<gravity>1</gravity>
			<velocity_decay/>
			<sensor name="imu_sensor" type="imu">
				<always_on>1</always_on>
				<update_rate>250</update_rate>
				<imu>
					<angular_velocity>
						<x>
						<noise type="gaussian">
							<mean>0</mean>
							<stddev>0.0003394</stddev>
							<dynamic_bias_stddev>3.8785e-05</dynamic_bias_stddev>
							<dynamic_bias_correlation_time>1000</dynamic_bias_correlation_time>
						</noise>
						</x>
						<y>
						<noise type="gaussian">
							<mean>0</mean>
							<stddev>0.0003394</stddev>
							<dynamic_bias_stddev>3.8785e-05</dynamic_bias_stddev>
							<dynamic_bias_correlation_time>1000</dynamic_bias_correlation_time>
						</noise>
						</y>
						<z>
						<noise type="gaussian">
							<mean>0</mean>
							<stddev>0.0003394</stddev>
							<dynamic_bias_stddev>3.8785e-05</dynamic_bias_stddev>
							<dynamic_bias_correlation_time>1000</dynamic_bias_correlation_time>
						</noise>
						</z>
					</angular_velocity>
					<linear_acceleration>
						<x>
						<noise type="gaussian">
							<mean>0</mean>
							<stddev>0.004</stddev>
							<dynamic_bias_stddev>0.006</dynamic_bias_stddev>
							<dynamic_bias_correlation_time>300</dynamic_bias_correlation_time>
						</noise>
						</x>
						<y>
						<noise type="gaussian">
							<mean>0</mean>
							<stddev>0.004</stddev>
							<dynamic_bias_stddev>0.006</dynamic_bias_stddev>
							<dynamic_bias_correlation_time>300</dynamic_bias_correlation_time>
						</noise>
						</y>
						<z>
						<noise type="gaussian">
							<mean>0</mean>
							<stddev>0.004</stddev>
							<dynamic_bias_stddev>0.006</dynamic_bias_stddev>
							<dynamic_bias_correlation_time>300</dynamic_bias_correlation_time>
						</noise>
						</z>
					</linear_acceleration>
				</imu>
			</sensor>
			<sensor name="air_pressure_sensor" type="air_pressure">
				<always_on>1</always_on>
				<update_rate>50</update_rate>
				<air_pressure>
					<pressure>
						<noise type="gaussian">
							<mean>0</mean>
							<stddev>0.01</stddev>
						</noise>
					</pressure>
				</air_pressure>
			</sensor>
			<sensor name="navsat_sensor" type="navsat">
				<always_on>1</always_on>
				<update_rate>30</update_rate>
			</sensor>
		</link>
	  	<link name='lf_wheel_link'>
			<pose>0.15 0.16317 0.0215 0 -0 0</pose>
			<inertial>
				<pose>0 0 0 1.57079632679 0 0</pose>
				<mass>0.414</mass>
				<inertia>
					<ixx>0.00068682</ixx>
					<ixy>0</ixy>
					<ixz>0</ixz>
					<iyy>0.00068682</iyy>
					<iyz>0</iyz>
					<izz>0.00097299</izz>
				</inertia>
			</inertial>
			<collision name='lf_wheel_link_collision'>
				<pose>0 0 0 1.57079632679 0 0</pose>
				<geometry>
					<cylinder>
						<radius>0.0686</radius>
						<length>0.0762</length>
					</cylinder>
				</geometry>
				<max_contacts>1</max_contacts>
				<surface>
					<friction>
						<torsional>
							<coefficient>0.25</coefficient>
							<use_patch_radius>1</use_patch_radius>
							<surface_radius>0.0686</surface_radius>
						</torsional>
						<ode>
							<mu>1.0</mu>
							<mu2>1.0</mu2>
							<fdir1>1 0 0</fdir1>
							<slip1>0.0</slip1>
							<slip2>0.0</slip2>
						</ode>
					</friction>
					<bounce>
						<restitution_coefficient>0</restitution_coefficient>
						<threshold>1e6</threshold>
					</bounce>
					<contact>
						<ode>
							<min_depth>0.001</min_depth>
							<max_vel>0.0</max_vel>
							<kp>1.0e6</kp>
							<kd>100.0</kd>
						</ode>
					</contact>
				</surface>
			</collision>
			<visual name='lf_wheel_link_visual'>
				<pose>0 0 0 0 -0 0</pose>
				<geometry>
					<mesh>
						<scale>1 1 1</scale>
						<uri>model://r1_rover/meshes/wheel_link.STL</uri>
					</mesh>
				</geometry>
				<material>
					<diffuse>0.0 0.0 0.0</diffuse>
					<specular>0.5 0.5 0.5</specular>
				</material>
			</visual>
			<gravity>1</gravity>
			<velocity_decay/>
		</link>
		<joint name='motor_0' type='revolute'>
			<child>lf_wheel_link</child>
			<parent>base_link</parent>
			<axis>
				<xyz>0 1 0</xyz>
				<limit>
					<lower>-1e+16</lower>
					<upper>1e+16</upper>
				</limit>
				<dynamics>
					<spring_reference>0</spring_reference>
					<spring_stiffness>0</spring_stiffness>
				</dynamics>
				<use_parent_model_frame>1</use_parent_model_frame>
			</axis>
		</joint>
	  	<link name='lb_wheel_link'>
	    	<pose>-0.15 0.16317 0.0215 0 -0 0</pose>
	    	<inertial>
			<pose>0 0 0 1.57079632679 0 0</pose>
			<mass>0.414</mass>
				<inertia>
					<ixx>0.00068682</ixx>
					<ixy>0</ixy>
					<ixz>0</ixz>
					<iyy>0.00068682</iyy>
					<iyz>0</iyz>
					<izz>0.00097299</izz>
				</inertia>
			</inertial>
			<collision name='lb_wheel_link_collision'>
				<pose>0 0 0 1.57079632679 0 0</pose>
				<geometry>
					<cylinder>
						<radius>0.0686</radius>
						<length>0.0762</length>
					</cylinder>
				</geometry>
				<max_contacts>1</max_contacts>
				<surface>
					<friction>
					<torsional>
						<coefficient>0.25</coefficient>
						<use_patch_radius>1</use_patch_radius>
						<surface_radius>0.0686</surface_radius>
					</torsional>
					<ode>
						<mu>1.0</mu>
						<mu2>1.0</mu2>
						<fdir1>1 0 0</fdir1>
						<slip1>0.0</slip1>
						<slip2>0.0</slip2>
					</ode>
					</friction>
					<bounce>
						<restitution_coefficient>0</restitution_coefficient>
						<threshold>1e6</threshold>
					</bounce>
					<contact>
					<ode>
						<min_depth>0.001</min_depth>
						<max_vel>0.0</max_vel>
						<kp>1.0e6</kp>
						<kd>100.0</kd>
					</ode>
					</contact>
				</surface>
	    	</collision>
			<visual name='lb_wheel_link_visual'>
				<pose>0 0 0 0 -0 0</pose>
					<geometry>
						<mesh>
							<scale>1 1 1</scale>
							<uri>model://r1_rover/meshes/wheel_link.STL</uri>
						</mesh>
					</geometry>
					<material>
						<diffuse>0.0 0.0 0.0</diffuse>
						<specular>0.5 0.5 0.5</specular>
					</material>
			</visual>
			<gravity>1</gravity>
			<velocity_decay/>
	 	</link>
		<joint name='motor_1' type='revolute'>
			<child>lb_wheel_link</child>
			<parent>base_link</parent>
			<axis>
				<xyz>0 1 0</xyz>
				<limit>
					<lower>-1e+16</lower>
					<upper>1e+16</upper>
				</limit>
				<dynamics>
					<spring_reference>0</spring_reference>
					<spring_stiffness>0</spring_stiffness>
				</dynamics>
				<use_parent_model_frame>1</use_parent_model_frame>
			</axis>
		</joint>
	  	<link name='rf_wheel_link'>
			<pose>0.15 -0.16317 0.0215 0 -0 0</pose>
			<inertial>
				<pose>0 0 0 1.57079632679 0 0</pose>
				<mass>0.414</mass>
				<inertia>
					<ixx>0.00068682</ixx>
					<ixy>0</ixy>
					<ixz>0</ixz>
					<iyy>0.00068682</iyy>
					<iyz>0</iyz>
					<izz>0.00097299</izz>
				</inertia>
			</inertial>
			<collision name='rf_wheel_link_collision'>
				<pose>0 0 0 1.57079632679 0 0</pose>
				<geometry>
					<cylinder>
						<radius>0.0686</radius>
						<length>0.0762</length>
					</cylinder>
				</geometry>
				<max_contacts>1</max_contacts>
				<surface>
					<friction>
					<torsional>
					<coefficient>0.25</coefficient>
					<use_patch_radius>1</use_patch_radius>
					<surface_radius>0.0686</surface_radius>
					</torsional>
					<ode>
					<mu>1.0</mu>
					<mu2>1.0</mu2>
					<fdir1>1 0 0</fdir1>
					<slip1>0.0</slip1>
					<slip2>0.0</slip2>
					</ode>
					</friction>
					<bounce>
					<restitution_coefficient>0</restitution_coefficient>
					<threshold>1e6</threshold>
					</bounce>
					<contact>
					<ode>
					<min_depth>0.001</min_depth>
					<max_vel>0.0</max_vel>
					<kp>1.0e6</kp>
					<kd>100.0</kd>
					</ode>
					</contact>
				</surface>
			</collision>
			<visual name='rf_wheel_link_visual'>
				<pose>0 0 0 0 -0 0</pose>
				<geometry>
					<mesh>
						<scale>1 1 1</scale>
						<uri>model://r1_rover/meshes/wheel_link.STL</uri>
					</mesh>
		  		</geometry>
		  		<material>
		    		<diffuse>0.0 0.0 0.0</diffuse>
		    		<specular>0.5 0.5 0.5</specular>
		  		</material>
			</visual>
			<gravity>1</gravity>
			<velocity_decay/>
		</link>
		<joint name='motor_2' type='revolute'>
			<child>rf_wheel_link</child>
			<parent>base_link</parent>
			<axis>
				<xyz>0 -1 0</xyz>
				<limit>
					<lower>-1e+16</lower>
					<upper>1e+16</upper>
				</limit>
				<dynamics>
					<spring_reference>0</spring_reference>
					<spring_stiffness>0</spring_stiffness>
				</dynamics>
				<use_parent_model_frame>1</use_parent_model_frame>
			</axis>
		</joint>
	  	<link name='rb_wheel_link'>
			<pose>-0.15 -0.16317 0.0215 0 -0 0</pose>
			<inertial>
				<pose>0 0 0 1.57079632679 0 0</pose>
				<mass>0.414</mass>
				<inertia>
					<ixx>0.00068682</ixx>
					<ixy>0</ixy>
					<ixz>0</ixz>
					<iyy>0.00068682</iyy>
					<iyz>0</iyz>
					<izz>0.00097299</izz>
				</inertia>
			</inertial>
			<collision name='rb_wheel_link_collision'>
				<pose>0 0 0 1.57079632679 0 0</pose>
				<geometry>
					<cylinder>
						<radius>0.0686</radius>
						<length>0.0762</length>
					</cylinder>
				</geometry>
				<max_contacts>1</max_contacts>
				<surface>
					<friction>
						<torsional>
							<coefficient>0.25</coefficient>
							<use_patch_radius>1</use_patch_radius>
							<surface_radius>0.0686</surface_radius>
						</torsional>
						<ode>
							<mu>1.0</mu>
							<mu2>1.0</mu2>
							<fdir1>1 0 0</fdir1>
							<slip1>0.0</slip1>
							<slip2>0.0</slip2>
						</ode>
					</friction>
					<bounce>
						<restitution_coefficient>0</restitution_coefficient>
						<threshold>1e6</threshold>
					</bounce>
					<contact>
						<ode>
							<min_depth>0.001</min_depth>
							<kp>1e8</kp>
						</ode>
					</contact>
				</surface>
			</collision>
			<visual name='rb_wheel_link_visual'>
				<pose>0 0 0 0 -0 0</pose>
				<geometry>
					<mesh>
						<scale>1 1 1</scale>
						<uri>model://r1_rover/meshes/wheel_link.STL</uri>
					</mesh>
				</geometry>
				<material>
					<diffuse>0.0 0.0 0.0</diffuse>
					<specular>0.5 0.5 0.5</specular>
				</material>
			</visual>
			<gravity>1</gravity>
			<velocity_decay/>
	 	</link>
		<joint name='motor_3' type='revolute'>
			<child>rb_wheel_link</child>
			<parent>base_link</parent>
			<axis>
				<xyz>0 -1 0</xyz>
				<limit>
					<lower>-1e+16</lower>
					<upper>1e+16</upper>
				</limit>
				<dynamics>
					<spring_reference>0</spring_reference>
					<spring_stiffness>0</spring_stiffness>
				</dynamics>
				<use_parent_model_frame>1</use_parent_model_frame>
			</axis>
		</joint>
	  	<static>0</static>
		<plugin filename="gz-sim-joint-controller-system" name="gz::sim::systems::JointController">
			<joint_name>motor_0</joint_name>
			<sub_topic>command/motor_speed</sub_topic>
			<use_actuator_msg>true</use_actuator_msg>
			<actuator_number>1</actuator_number>
			<p_gain>10.0</p_gain>
		</plugin>
		<plugin filename="gz-sim-joint-controller-system" name="gz::sim::systems::JointController">
			<joint_name>motor_1</joint_name>
			<sub_topic>command/motor_speed</sub_topic>
			<use_actuator_msg>true</use_actuator_msg>
			<actuator_number>1</actuator_number>
			<p_gain>10.0</p_gain>
		</plugin>
		<plugin filename="gz-sim-joint-controller-system" name="gz::sim::systems::JointController">
			<joint_name>motor_2</joint_name>
			<sub_topic>command/motor_speed</sub_topic>
			<use_actuator_msg>true</use_actuator_msg>
			<actuator_number>0</actuator_number>
			<p_gain>10.0</p_gain>
		</plugin>
		<plugin filename="gz-sim-joint-controller-system" name="gz::sim::systems::JointController">
			<joint_name>motor_3</joint_name>
			<sub_topic>command/motor_speed</sub_topic>
			<use_actuator_msg>true</use_actuator_msg>
			<actuator_number>0</actuator_number>
			<p_gain>10.0</p_gain>
		</plugin>
		<plugin
			filename="gz-sim-joint-state-publisher-system"
			name="gz::sim::systems::JointStatePublisher">
			<joint_name>motor_0</joint_name>
			<joint_name>motor_1</joint_name>
			<joint_name>motor_2</joint_name>
			<joint_name>motor_3</joint_name>
		</plugin>
	</model>
</sdf>
