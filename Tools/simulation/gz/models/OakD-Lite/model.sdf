<?xml version="1.0" encoding="UTF-8"?>
<sdf version='1.9'>
  <model name='<PERSON><PERSON>-Li<PERSON>'>
    <pose>0 0 0 0 0 0</pose>
    <self_collide>false</self_collide>
    <static>false</static>
    <link name="OakD-Lite/base_link">
      <inertial>
        <pose>0.00358 -0.03 .014 0 0 0</pose>
        <mass>0.061</mass>
        <inertia>
          <ixx>0.0000460804</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.0000055421</iyy>
          <iyz>0</iyz>
          <izz>0.0000436519</izz>
        </inertia>
      </inertial>
      <visual name="OakD-Lite/visual">
        <pose>0 0 0 0 0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://OakD-Lite/meshes/OakDLite.dae</uri>
          </mesh>
        </geometry>
      </visual>
      <collision name="OakD-Lite/collision">
        <pose>0.00358 -0.03 .014 0 0 0</pose>
        <geometry>
          <box>
            <size>0.0175 0.091 0.028</size>
          </box>
        </geometry>
      </collision>
      <sensor name="IMX214" type="camera">
        <pose>0.01233 -0.03 .01878 0 0 0</pose>
        <camera>
          <horizontal_fov>1.204</horizontal_fov>
          <image>
            <width>1920</width>
            <height>1080</height>
          </image>
          <clip>
            <near>0.1</near>
            <far>100</far>
          </clip>
        </camera>
        <always_on>1</always_on>
        <update_rate>30</update_rate>
        <visualize>true</visualize>
        <topic>camera</topic>
      </sensor>
      <sensor name="StereoOV7251" type="depth_camera">
        <pose>0.01233 -0.03 .01878 0 0 0</pose>
        <camera>
          <horizontal_fov>1.274</horizontal_fov>
          <image>
            <width>640</width>
            <height>480</height>
            <format>R_FLOAT32</format>
          </image>
          <clip>
            <near>0.2</near>
            <far>19.1</far>
          </clip>
        </camera>
        <always_on>1</always_on>
        <update_rate>30</update_rate>
        <visualize>true</visualize>
        <topic>depth_camera</topic>
      </sensor>
      <gravity>true</gravity>
      <velocity_decay/>
    </link>
    <!-- <plugin filename="ignition-gazebo-sensors-system" name="ignition::gazebo::systems::Sensors">
      <render_engine>ogre2</render_engine>
    </plugin> -->
  </model>
</sdf>
