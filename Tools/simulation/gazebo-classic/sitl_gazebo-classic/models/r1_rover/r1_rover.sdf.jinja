<sdf version='1.6'>
  <model name='r1_rover'>
    <link name='base_link'>
      <pose>0 0 0 0 -0 0</pose>
      <inertial>
        <pose>0 0 0 0 -0 0</pose>
        <mass>20.0</mass>
        <inertia>
          <ixx>0.37083</ixx>
          <ixy>0.0</ixy>
          <ixz>0.0</ixz>
          <iyy>0.37083</iyy>
          <iyz>0.0</iyz>
          <izz>0.53333</izz>
        </inertia>
      </inertial>
      <collision name='odom_fixed_joint_lump__base_link_collision'>
        <pose>0 0 0.15 0 -0 0</pose>
        <geometry>
          <box>
            <size>0.4 0.4 0.25</size>
          </box>
        </geometry>
        <surface>
          <contact>
            <ode/>
          </contact>
          <friction>
            <ode/>
          </friction>
        </surface>
      </collision>
      <visual name='odom_fixed_joint_lump__base_link_visual'>
        <pose>0 0 0 0 -0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://r1_rover/meshes/chassis_link.STL</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Grey</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <visual name='odom_fixed_joint_lump__top_link_visual_1'>
        <pose>0 0 0.114486 0 -0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://r1_rover/meshes/top_link.STL</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/White</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <visual name='odom_fixed_joint_lump__battery_link_visual_2'>
        <pose>-0.09302 -0.000128 0.114486 0 -0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://r1_rover/meshes/battery_link.STL</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/FlatBlack</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <visual name='odom_fixed_joint_lump__housing_link_visual_3'>
        <pose>0.03473 0 0.114486 0 -0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://r1_rover/meshes/housing_link.STL</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/FlatBlack</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <visual name='odom_fixed_joint_lump__l_antenna_link_visual_4'>
        <pose>-0.1565 0.0762 0.114486 0 -0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://r1_rover/meshes/antenna_link.STL</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/FlatBlack</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <visual name='odom_fixed_joint_lump__r_antenna_link_visual_5'>
        <pose>-0.1565 -0.0762 0.114486 0 -0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://r1_rover/meshes/antenna_link.STL</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/FlatBlack</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <velocity_decay/>
      <velocity_decay/>
      <velocity_decay/>
      <velocity_decay/>
      <velocity_decay/>
      <gravity>1</gravity>
      <velocity_decay/>
    </link>
    <link name='lb_wheel_link'>
      <pose>-0.15 0.16317 0.0215 0 -0 0</pose>
      <inertial>
        <pose>0 0 0 1.57079632679 0 0</pose>
        <mass>0.414</mass>
        <inertia>
          <ixx>0.00068682</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.00068682</iyy>
          <iyz>0</iyz>
          <izz>0.00097299</izz>
        </inertia>
      </inertial>
      <collision name='lb_wheel_link_collision'>
        <pose>0 0 0 1.57079632679 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.0686</radius>
            <length>0.0762</length>
          </cylinder>
        </geometry>
        <max_contacts>1</max_contacts>
        <surface>
          <friction>
            <torsional>
              <coefficient>0.25</coefficient>
              <use_patch_radius>1</use_patch_radius>
              <surface_radius>0.0686</surface_radius>
            </torsional>
            <ode>
              <mu>1.0</mu>
              <mu2>0.45</mu2>
              <fdir1>1 0 0</fdir1>
              <slip1>0.0</slip1>
              <slip2>0.0</slip2>
            </ode>
          </friction>
          <bounce>
            <restitution_coefficient>0</restitution_coefficient>
            <threshold>1e6</threshold>
          </bounce>
          <contact>
            <ode>
              <min_depth>0.001</min_depth>
              <max_vel>0.0</max_vel>
              <kp>1.0e6</kp>
              <kd>100.0</kd>
            </ode>
          </contact>
        </surface>
      </collision>
      <visual name='lb_wheel_link_visual'>
        <pose>0 0 0 0 -0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://r1_rover/meshes/wheel_link.STL</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/FlatBlack</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <gravity>1</gravity>
      <velocity_decay/>
    </link>
    <joint name='lb_wheel_joint' type='revolute'>
      <child>lb_wheel_link</child>
      <parent>base_link</parent>
      <axis>
        <xyz>0 1 0</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
        <use_parent_model_frame>1</use_parent_model_frame>
      </axis>
    </joint>
    <link name='lf_wheel_link'>
      <pose>0.15 0.16317 0.0215 0 -0 0</pose>
      <inertial>
        <pose>0 0 0 1.57079632679 0 0</pose>
        <mass>0.414</mass>
        <inertia>
          <ixx>0.00068682</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.00068682</iyy>
          <iyz>0</iyz>
          <izz>0.00097299</izz>
        </inertia>
      </inertial>
      <collision name='lf_wheel_link_collision'>
        <pose>0 0 0 1.57079632679 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.0686</radius>
            <length>0.0762</length>
          </cylinder>
        </geometry>
        <max_contacts>1</max_contacts>
        <surface>
          <friction>
            <torsional>
              <coefficient>0.25</coefficient>
              <use_patch_radius>1</use_patch_radius>
              <surface_radius>0.0686</surface_radius>
            </torsional>
            <ode>
              <mu>1.0</mu>
              <mu2>0.45</mu2>
              <fdir1>1 0 0</fdir1>
              <slip1>0.0</slip1>
              <slip2>0.0</slip2>
            </ode>
          </friction>
          <bounce>
            <restitution_coefficient>0</restitution_coefficient>
            <threshold>1e6</threshold>
          </bounce>
          <contact>
            <ode>
              <min_depth>0.001</min_depth>
              <max_vel>0.0</max_vel>
              <kp>1.0e6</kp>
              <kd>100.0</kd>
            </ode>
          </contact>
        </surface>
      </collision>
      <visual name='lf_wheel_link_visual'>
        <pose>0 0 0 0 -0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://r1_rover/meshes/wheel_link.STL</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/FlatBlack</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <gravity>1</gravity>
      <velocity_decay/>
    </link>
    <joint name='lf_wheel_joint' type='revolute'>
      <child>lf_wheel_link</child>
      <parent>base_link</parent>
      <axis>
        <xyz>0 1 0</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
        <use_parent_model_frame>1</use_parent_model_frame>
      </axis>
    </joint>
    <link name='rb_wheel_link'>
      <pose>-0.15 -0.16317 0.0215 0 -0 0</pose>
      <inertial>
        <pose>0 0 0 1.57079632679 0 0</pose>
        <mass>0.414</mass>
        <inertia>
          <ixx>0.00068682</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.00068682</iyy>
          <iyz>0</iyz>
          <izz>0.00097299</izz>
        </inertia>
      </inertial>
      <collision name='rb_wheel_link_collision'>
        <pose>0 0 0 1.57079632679 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.0686</radius>
            <length>0.0762</length>
          </cylinder>
        </geometry>
        <max_contacts>1</max_contacts>
        <surface>
          <friction>
            <torsional>
              <coefficient>0.25</coefficient>
              <use_patch_radius>1</use_patch_radius>
              <surface_radius>0.0686</surface_radius>
            </torsional>
            <ode>
              <mu>1.0</mu>
              <mu2>0.45</mu2>
              <fdir1>1 0 0</fdir1>
              <slip1>0.0</slip1>
              <slip2>0.0</slip2>
            </ode>
          </friction>
          <bounce>
            <restitution_coefficient>0</restitution_coefficient>
            <threshold>1e6</threshold>
          </bounce>
          <contact>
            <ode>
              <min_depth>0.001</min_depth>
              <kp>1e8</kp>
            </ode>
          </contact>
        </surface>
      </collision>
      <visual name='rb_wheel_link_visual'>
        <pose>0 0 0 0 -0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://r1_rover/meshes/wheel_link.STL</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/FlatBlack</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <gravity>1</gravity>
      <velocity_decay/>
    </link>
    <joint name='rb_wheel_joint' type='revolute'>
      <child>rb_wheel_link</child>
      <parent>base_link</parent>
      <axis>
        <xyz>0 1 0</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
        <use_parent_model_frame>1</use_parent_model_frame>
      </axis>
    </joint>
    <link name='rf_wheel_link'>
      <pose>0.15 -0.16317 0.0215 0 -0 0</pose>
      <inertial>
        <pose>0 0 0 1.57079632679 0 0</pose>
        <mass>0.414</mass>
        <inertia>
          <ixx>0.00068682</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.00068682</iyy>
          <iyz>0</iyz>
          <izz>0.00097299</izz>
        </inertia>
      </inertial>
      <collision name='rf_wheel_link_collision'>
        <pose>0 0 0 1.57079632679 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.0686</radius>
            <length>0.0762</length>
          </cylinder>
        </geometry>
        <max_contacts>1</max_contacts>
        <surface>
          <friction>
            <torsional>
              <coefficient>0.25</coefficient>
              <use_patch_radius>1</use_patch_radius>
              <surface_radius>0.0686</surface_radius>
            </torsional>
            <ode>
              <mu>1.0</mu>
              <mu2>0.45</mu2>
              <fdir1>1 0 0</fdir1>
              <slip1>0.0</slip1>
              <slip2>0.0</slip2>
            </ode>
          </friction>
          <bounce>
            <restitution_coefficient>0</restitution_coefficient>
            <threshold>1e6</threshold>
          </bounce>
          <contact>
            <ode>
              <min_depth>0.001</min_depth>
              <max_vel>0.0</max_vel>
              <kp>1.0e6</kp>
              <kd>100.0</kd>
            </ode>
          </contact>
        </surface>
      </collision>
      <visual name='rf_wheel_link_visual'>
        <pose>0 0 0 0 -0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://r1_rover/meshes/wheel_link.STL</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/FlatBlack</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <gravity>1</gravity>
      <velocity_decay/>
    </link>
    <joint name='rf_wheel_joint' type='revolute'>
      <child>rf_wheel_link</child>
      <parent>base_link</parent>
      <axis>
        <xyz>0 1 0</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
        <use_parent_model_frame>1</use_parent_model_frame>
      </axis>
    </joint>
    <static>0</static>
    <link name='rover/imu_link'>
      <pose>0 0 .2 0 0 0</pose>
      <inertial>
        <pose>0 0 0 0 0 0</pose>
        <mass>0.015</mass>
        <inertia>
          <ixx>1e-05</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>1e-05</iyy>
          <iyz>0</iyz>
          <izz>1e-05</izz>
        </inertia>
      </inertial>
      <!--<visual name='imu_box'>
        <pose>0 0 0 0 0 0</pose>
        <geometry>
          <box>
            <size>.05 .05 .02</size>
          </box>
        </geometry>
        <material>
          <emissive>1.0 0.0 0.0 1</emissive>
        </material>
      </visual>--> <!--uncomment visual block to see IMU-->
    </link>
    <joint name='rover/imu_joint' type='revolute'>
      <child>rover/imu_link</child>
      <parent>base_link</parent>
      <axis>
        <xyz>1 0 0</xyz>
        <limit>
          <lower>0</lower>
          <upper>0</upper>
          <effort>0</effort>
          <velocity>0</velocity>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
        <use_parent_model_frame>1</use_parent_model_frame>
      </axis>
    </joint>
    <include>
      <uri>model://gps</uri>
      <pose>0 0 0.12 0 0 0</pose>
      <name>gps</name>
    </include>
    <joint name='gps_joint' type='fixed'>
      <child>gps::link</child>
      <parent>base_link</parent>
    </joint>
    <plugin name='gazebo_imu_plugin' filename='libgazebo_imu_plugin.so'>
      <robotNamespace></robotNamespace>
      <linkName>rover/imu_link</linkName>
      <imuTopic>/imu</imuTopic>
      <gyroscopeNoiseDensity>0.0003394</gyroscopeNoiseDensity>
      <gyroscopeRandomWalk>3.8785e-05</gyroscopeRandomWalk>
      <gyroscopeBiasCorrelationTime>1000.0</gyroscopeBiasCorrelationTime>
      <gyroscopeTurnOnBiasSigma>0.0087</gyroscopeTurnOnBiasSigma>
      <accelerometerNoiseDensity>0.004</accelerometerNoiseDensity>
      <accelerometerRandomWalk>0.006</accelerometerRandomWalk>
      <accelerometerBiasCorrelationTime>300.0</accelerometerBiasCorrelationTime>
      <accelerometerTurnOnBiasSigma>0.196</accelerometerTurnOnBiasSigma>
    </plugin>
    <plugin name='groundtruth_plugin' filename='libgazebo_groundtruth_plugin.so'>
      <robotNamespace/>
    </plugin>
    <plugin name='magnetometer_plugin' filename='libgazebo_magnetometer_plugin.so'>
      <robotNamespace/>
      <pubRate>20</pubRate>
      <noiseDensity>0.0004</noiseDensity>
      <randomWalk>6.4e-06</randomWalk>
      <biasCorrelationTime>600</biasCorrelationTime>
      <magTopic>/mag</magTopic>
    </plugin>
    <plugin name='barometer_plugin' filename='libgazebo_barometer_plugin.so'>
      <robotNamespace/>
      <pubRate>10</pubRate>
      <baroTopic>/baro</baroTopic>
    </plugin>
    <plugin name='mavlink_interface' filename='libgazebo_mavlink_interface.so'>
      <robotNamespace></robotNamespace>
      <imuSubTopic>/imu</imuSubTopic>
      <magSubTopic>/mag</magSubTopic>
      <baroSubTopic>/baro</baroSubTopic>
      <mavlink_addr>INADDR_ANY</mavlink_addr>
      <mavlink_tcp_port>{{ mavlink_tcp_port }}</mavlink_tcp_port>
      <mavlink_udp_port>{{ mavlink_udp_port }}</mavlink_udp_port>
      <serialEnabled>false</serialEnabled>
      <serialDevice>/dev/ttyACM0</serialDevice>
      <baudRate>921600</baudRate>
      <qgc_addr>INADDR_ANY</qgc_addr>
      <qgc_udp_port>14550</qgc_udp_port>
      <sdk_addr>INADDR_ANY</sdk_addr>
      <sdk_udp_port>14540</sdk_udp_port>
      <hil_mode>false</hil_mode>
      <hil_state_level>false</hil_state_level>
      <enable_lockstep>true</enable_lockstep>
      <use_tcp>true</use_tcp>
      <motorSpeedCommandPubTopic>/gazebo/command/motor_speed</motorSpeedCommandPubTopic>
      <control_channels>
        <channel name="left_front_wheel_drive">
          <input_index>0</input_index>
          <input_offset>0</input_offset>
          <input_scaling>40</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>velocity</joint_control_type>
          <joint_name>lf_wheel_joint</joint_name>
          <joint_control_pid>
            <p>0.1</p>
            <i>0</i>
            <d>0</d>
            <iMax>800</iMax>
            <iMin>-800</iMin>
            <cmdMax>1200</cmdMax>
            <cmdMin>-1200</cmdMin>
          </joint_control_pid>
        </channel>
        <channel name="left_back_wheel_drive">
          <input_index>1</input_index>
          <input_offset>0</input_offset>
          <input_scaling>40</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>velocity</joint_control_type>
          <joint_name>lb_wheel_joint</joint_name>
          <joint_control_pid>
            <p>0.1</p>
            <i>0</i>
            <d>0</d>
            <iMax>800</iMax>
            <iMin>-800</iMin>
            <cmdMax>1200</cmdMax>
            <cmdMin>-1200</cmdMin>
          </joint_control_pid>
        </channel>
        <channel name="right_front_wheel_drive">
          <input_index>5</input_index>
          <input_offset>0</input_offset>
          <input_scaling>40</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>velocity</joint_control_type>
          <joint_name>rf_wheel_joint</joint_name>
          <joint_control_pid>
            <p>0.1</p>
            <i>0</i>
            <d>0</d>
            <iMax>800</iMax>
            <iMin>-800</iMin>
            <cmdMax>1200</cmdMax>
            <cmdMin>-1200</cmdMin>
          </joint_control_pid>
        </channel>
        <channel name="right_back_wheel_drive">
          <input_index>6</input_index>
          <input_offset>0</input_offset>
          <input_scaling>40</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>velocity</joint_control_type>
          <joint_name>rb_wheel_joint</joint_name>
          <joint_control_pid>
            <p>0.1</p>
            <i>0</i>
            <d>0</d>
            <iMax>800</iMax>
            <iMin>-800</iMin>
            <cmdMax>1200</cmdMax>
            <cmdMin>-1200</cmdMin>
          </joint_control_pid>
        </channel>
      </control_channels>
    </plugin>
  </model>
</sdf>
