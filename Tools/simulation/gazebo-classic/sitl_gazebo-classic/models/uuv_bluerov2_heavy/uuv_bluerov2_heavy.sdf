<?xml version='1.0'?>
<sdf version='1.6'>
    <model name="BlueROV2 Heavy">
        <pose>0 0 0 0 0 0</pose>

        <link name="base_link">
            <inertial>
                <mass>11</mass>
                <inertia>
                    <ixx>0.1</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>0.1</iyy>
                    <iyz>0</iyz>
                    <izz>0.1</izz>
                </inertia>
            </inertial>

            <visual name="base_link_visual">
                <geometry>
                    <mesh>
                        <uri>model://uuv_bluerov2_heavy/meshes/BlueROV2heavy.dae</uri>
                    </mesh>

                    <!-- <cylinder>
                        <radius>.05</radius>
                        <length>0.2</length>
                    </cylinder> -->
                </geometry>
            </visual>

        </link>


<!-- GPS for getting the local pose -->
    <include>
      <uri>model://gps</uri>
      <pose>0 0 0 0 0 0</pose>
      <name>gps</name>
    </include>
    <joint name='gps_joint' type='fixed'>
      <parent>base_link</parent>
      <child>gps::link</child>
    </joint>



<!-- Start of Thrusters  -->
        <link name="thruster1">
            <pose>0.14 -0.10 0 0 1.570796 0.78539815</pose>
            <inertial>
                <mass>1e-10</mass>
                <inertia>
                    <ixx>1e-06</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>1e-06</iyy>
                    <iyz>0</iyz>
                    <izz>1e-06</izz>
                </inertia>
            </inertial>
            <visual name="thruster1_visual">
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://uuv_bluerov2_heavy/meshes/prop.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <script>
                        <name>Gazebo/Green</name>
                        <uri>model://uuv_bluerov2_heavy/meshes/prop.dae</uri>
                    </script>
                </material>
            </visual>
        </link>

        <joint name="thruster1_joint" type="revolute">
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
            </axis>
            <parent>base_link</parent>
            <child>thruster1</child>
        </joint>

        <plugin name="thruster1_model" filename="libgazebo_motor_model.so">
            <robotNamespace />
            <reversible>true</reversible>
            <jointName>thruster1_joint</jointName>
            <linkName>thruster1</linkName>
            <turningDirection>ccw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1100</maxRotVelocity>
            <motorConstant>10</motorConstant>
            <momentConstant>0.01</momentConstant>
            <rotorDragCoefficient>0</rotorDragCoefficient>
            <rollingMomentCoefficient>0</rollingMomentCoefficient>
            <commandSubTopic>/gazebo/command/motor_speed</commandSubTopic>
            <motorNumber>0</motorNumber>
            <motorSpeedPubTopic>/motor_speed/0</motorSpeedPubTopic>
            <rotorVelocitySlowdownSim>0.025</rotorVelocitySlowdownSim>
        </plugin>

        <link name="thruster2">
            <pose>0.14 0.10 0 0 1.570796 -0.78539815</pose>
            <inertial>
                <mass>1e-10</mass>
                <inertia>
                    <ixx>1e-06</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>1e-06</iyy>
                    <iyz>0</iyz>
                    <izz>1e-06</izz>
                </inertia>
            </inertial>
            <visual name="thruster2_visual">
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://uuv_bluerov2_heavy/meshes/prop.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <script>
                        <name>Gazebo/Green</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
            </visual>
        </link>

        <joint name="thruster2_joint" type="revolute">
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
            </axis>
            <parent>base_link</parent>
            <child>thruster2</child>
        </joint>

        <plugin name="thruster2_model" filename="libgazebo_motor_model.so">
            <robotNamespace />
            <reversible>true</reversible>
            <jointName>thruster2_joint</jointName>
            <linkName>thruster2</linkName>
            <turningDirection>ccw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1100</maxRotVelocity>
            <motorConstant>10</motorConstant>
            <momentConstant>0.01</momentConstant>
            <rotorDragCoefficient>0</rotorDragCoefficient>
            <rollingMomentCoefficient>0</rollingMomentCoefficient>
            <commandSubTopic>/gazebo/command/motor_speed</commandSubTopic>
            <motorNumber>1</motorNumber>
            <motorSpeedPubTopic>/motor_speed/1</motorSpeedPubTopic>
            <rotorVelocitySlowdownSim>0.025</rotorVelocitySlowdownSim>
        </plugin>

        <link name="thruster3">
            <pose>-0.14 -0.10 0 0 1.570796 2.356194</pose>
            <inertial>
                <mass>1e-10</mass>
                <inertia>
                    <ixx>1e-06</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>1e-06</iyy>
                    <iyz>0</iyz>
                    <izz>1e-06</izz>
                </inertia>
            </inertial>
            <visual name="thruster3_visual">
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://uuv_bluerov2_heavy/meshes/prop.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <script>
                        <name>Gazebo/Blue</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
            </visual>
        </link>

        <joint name="thruster3_joint" type="revolute">
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
            </axis>
            <parent>base_link</parent>
            <child>thruster3</child>
        </joint>

        <plugin name="thruster3_model" filename="libgazebo_motor_model.so">
            <robotNamespace />
            <reversible>true</reversible>
            <jointName>thruster3_joint</jointName>
            <linkName>thruster3</linkName>
            <turningDirection>ccw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1100</maxRotVelocity>
            <motorConstant>-10</motorConstant>
            <momentConstant>-0.01</momentConstant>
            <rotorDragCoefficient>0</rotorDragCoefficient>
            <rollingMomentCoefficient>0</rollingMomentCoefficient>
            <commandSubTopic>/gazebo/command/motor_speed</commandSubTopic>
            <motorNumber>2</motorNumber>
            <motorSpeedPubTopic>/motor_speed/2</motorSpeedPubTopic>
            <rotorVelocitySlowdownSim>0.025</rotorVelocitySlowdownSim>
        </plugin>

        <link name="thruster4">
            <pose>-0.14 0.10 0 0 1.570796 -2.356194</pose>
            <inertial>
                <mass>1e-10</mass>
                <inertia>
                    <ixx>1e-06</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>1e-06</iyy>
                    <iyz>0</iyz>
                    <izz>1e-06</izz>
                </inertia>
            </inertial>
            <visual name="thruster4_visual">
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://uuv_bluerov2_heavy/meshes/prop.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <script>
                        <name>Gazebo/Blue</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
            </visual>
        </link>

        <joint name="thruster4_joint" type="revolute">
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
            </axis>
            <parent>base_link</parent>
            <child>thruster4</child>
        </joint>

        <plugin name="thruster4_model" filename="libgazebo_motor_model.so">
            <robotNamespace />
            <reversible>true</reversible>
            <jointName>thruster4_joint</jointName>
            <linkName>thruster4</linkName>
            <turningDirection>ccw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1100</maxRotVelocity>
            <motorConstant>-10</motorConstant>
            <momentConstant>-0.01</momentConstant>
            <rotorDragCoefficient>0</rotorDragCoefficient>
            <rollingMomentCoefficient>0</rollingMomentCoefficient>
            <commandSubTopic>/gazebo/command/motor_speed</commandSubTopic>
            <motorNumber>3</motorNumber>
            <motorSpeedPubTopic>/motor_speed/3</motorSpeedPubTopic>
            <rotorVelocitySlowdownSim>0.025</rotorVelocitySlowdownSim>
        </plugin>

        <link name="thruster5">
            <pose>0.12 -0.22 0.06 0 0 0</pose>
            <inertial>
                <mass>1e-10</mass>
                <inertia>
                    <ixx>1e-06</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>1e-06</iyy>
                    <iyz>0</iyz>
                    <izz>1e-06</izz>
                </inertia>
            </inertial>
            <visual name="thruster5_visual">
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://uuv_bluerov2_heavy/meshes/prop.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <script>
                        <name>Gazebo/Green</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
            </visual>
        </link>

        <joint name="thruster5_joint" type="revolute">
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
            </axis>
            <parent>base_link</parent>
            <child>thruster5</child>
        </joint>

        <plugin name="thruster5_model" filename="libgazebo_motor_model.so">
            <robotNamespace />
            <reversible>true</reversible>
            <jointName>thruster5_joint</jointName>
            <linkName>thruster5</linkName>
            <turningDirection>ccw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1100</maxRotVelocity>
            <motorConstant>10</motorConstant>
            <momentConstant>0.01</momentConstant>
            <rotorDragCoefficient>0</rotorDragCoefficient>
            <rollingMomentCoefficient>0</rollingMomentCoefficient>
            <commandSubTopic>/gazebo/command/motor_speed</commandSubTopic>
            <motorNumber>4</motorNumber>
            <motorSpeedPubTopic>/motor_speed/4</motorSpeedPubTopic>
            <rotorVelocitySlowdownSim>0.025</rotorVelocitySlowdownSim>
        </plugin>

        <link name="thruster6">
            <pose>0.12 0.22 0.06 0 0 0</pose>
            <inertial>
                <mass>1e-10</mass>
                <inertia>
                    <ixx>1e-06</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>1e-06</iyy>
                    <iyz>0</iyz>
                    <izz>1e-06</izz>
                </inertia>
            </inertial>
            <visual name="thruster6_visual">
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://uuv_bluerov2_heavy/meshes/prop.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <script>
                        <name>Gazebo/Blue</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
            </visual>
        </link>

        <joint name="thruster6_joint" type="revolute">
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
            </axis>
            <parent>base_link</parent>
            <child>thruster6</child>
        </joint>

        <plugin name="thruster6_model" filename="libgazebo_motor_model.so">
            <robotNamespace />
            <reversible>true</reversible>
            <jointName>thruster6_joint</jointName>
            <linkName>thruster6</linkName>
            <turningDirection>ccw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1100</maxRotVelocity>
            <motorConstant>-10</motorConstant>
            <momentConstant>-0.01</momentConstant>
            <rotorDragCoefficient>0</rotorDragCoefficient>
            <rollingMomentCoefficient>0</rollingMomentCoefficient>
            <commandSubTopic>/gazebo/command/motor_speed</commandSubTopic>
            <motorNumber>5</motorNumber>
            <motorSpeedPubTopic>/motor_speed/5</motorSpeedPubTopic>
            <rotorVelocitySlowdownSim>0.025</rotorVelocitySlowdownSim>
        </plugin>


        <link name="thruster7">
            <pose>-0.12 -0.22 0.06 0 0 0</pose>
            <inertial>
                <mass>1e-10</mass>
                <inertia>
                    <ixx>1e-06</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>1e-06</iyy>
                    <iyz>0</iyz>
                    <izz>1e-06</izz>
                </inertia>
            </inertial>
            <visual name="thruster7_visual">
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://uuv_bluerov2_heavy/meshes/prop.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <script>
                        <name>Gazebo/Blue</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
            </visual>
        </link>

        <joint name="thruster7_joint" type="revolute">
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
            </axis>
            <parent>base_link</parent>
            <child>thruster7</child>
        </joint>

        <plugin name="thruster7_model" filename="libgazebo_motor_model.so">
            <robotNamespace />
            <reversible>true</reversible>
            <jointName>thruster7_joint</jointName>
            <linkName>thruster7</linkName>
            <turningDirection>ccw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1100</maxRotVelocity>
            <motorConstant>-10</motorConstant>
            <momentConstant>-0.01</momentConstant>
            <rotorDragCoefficient>0</rotorDragCoefficient>
            <rollingMomentCoefficient>0</rollingMomentCoefficient>
            <commandSubTopic>/gazebo/command/motor_speed</commandSubTopic>
            <motorNumber>6</motorNumber>
            <motorSpeedPubTopic>/motor_speed/6</motorSpeedPubTopic>
            <rotorVelocitySlowdownSim>0.025</rotorVelocitySlowdownSim>
        </plugin>

        <link name="thruster8">
            <pose>-0.12 0.22 0.06 0 0 0</pose>
            <inertial>
                <mass>1e-10</mass>
                <inertia>
                    <ixx>1e-06</ixx>
                    <ixy>0</ixy>
                    <ixz>0</ixz>
                    <iyy>1e-06</iyy>
                    <iyz>0</iyz>
                    <izz>1e-06</izz>
                </inertia>
            </inertial>
            <visual name="thruster8_visual">
                <geometry>
                    <mesh>
                        <scale>1 1 1</scale>
                        <uri>model://uuv_bluerov2_heavy/meshes/prop.dae</uri>
                    </mesh>
                </geometry>
                <material>
                    <script>
                        <name>Gazebo/Green</name>
                        <uri>file://media/materials/scripts/gazebo.material</uri>
                    </script>
                </material>
            </visual>
        </link>

        <joint name="thruster8_joint" type="revolute">
            <axis>
                <xyz>0 0 1</xyz>
                <limit>
                    <lower>-1e+16</lower>
                    <upper>1e+16</upper>
                </limit>
            </axis>
            <parent>base_link</parent>
            <child>thruster8</child>
        </joint>

        <plugin name="thruster8_model" filename="libgazebo_motor_model.so">
            <robotNamespace />
            <reversible>true</reversible>
            <jointName>thruster8_joint</jointName>
            <linkName>thruster8</linkName>
            <turningDirection>ccw</turningDirection>
            <timeConstantUp>0.0125</timeConstantUp>
            <timeConstantDown>0.025</timeConstantDown>
            <maxRotVelocity>1100</maxRotVelocity>
            <motorConstant>10</motorConstant>
            <momentConstant>0.01</momentConstant>
            <rotorDragCoefficient>0</rotorDragCoefficient>
            <rollingMomentCoefficient>0</rollingMomentCoefficient>
            <commandSubTopic>/gazebo/command/motor_speed</commandSubTopic>
            <motorNumber>7</motorNumber>
            <motorSpeedPubTopic>/motor_speed/7</motorSpeedPubTopic>
            <rotorVelocitySlowdownSim>0.025</rotorVelocitySlowdownSim>
        </plugin>

        <plugin name="magnetometer_plugin" filename="libgazebo_magnetometer_plugin.so">
            <robotNamespace />
            <pubRate>100</pubRate>
            <noiseDensity>0.0004</noiseDensity>
            <randomWalk>6.4e-06</randomWalk>
            <biasCorrelationTime>600</biasCorrelationTime>
            <magTopic>/mag</magTopic>
        </plugin>

        <plugin name="barometer_plugin" filename="libgazebo_barometer_plugin.so">
            <robotNamespace />
            <pubRate>50</pubRate>
            <baroTopic>/baro</baroTopic>
        </plugin>

        <plugin name="uuv_plugin" filename="libgazebo_uuv_plugin.so">
            <robotNamespace />
            <baseLinkName>base_link</baseLinkName>
            <addedMassLinear>1.11 2.8 2.8</addedMassLinear>
            <addedMassAngular>0.00451 0.0163 0.0163</addedMassAngular>
            <dampingLinear>5.39 17.36 17.36</dampingLinear>
            <dampingAngular>0.00114 0.007 0.007</dampingAngular>
            <commandSubTopic>/gazebo/command/motor_speed</commandSubTopic>
            <buoyancy>
                <link_name>base_link</link_name>
                <origin>0 0 0</origin>
                <compensation>1</compensation>
                <height_scale_limit>0.05</height_scale_limit>
            </buoyancy>
        </plugin>

        <plugin name="mavlink_interface" filename="libgazebo_mavlink_interface.so">
            <robotNamespace />
            <imuSubTopic>/imu</imuSubTopic>
            <magSubTopic>/mag</magSubTopic>
            <baroSubTopic>/baro</baroSubTopic>
            <mavlink_addr>INADDR_ANY</mavlink_addr>
            <mavlink_udp_port>14560</mavlink_udp_port>
            <mavlink_tcp_port>4560</mavlink_tcp_port>
            <serialEnabled>0</serialEnabled>
            <serialDevice>/dev/ttyACM0</serialDevice>
            <baudRate>921600</baudRate>
            <qgc_addr>INADDR_ANY</qgc_addr>
            <qgc_udp_port>14550</qgc_udp_port>
            <sdk_addr>INADDR_ANY</sdk_addr>
            <sdk_udp_port>14540</sdk_udp_port>
            <hil_mode>false</hil_mode>
            <hil_state_level>false</hil_state_level>
            <vehicle_is_tailsitter>false</vehicle_is_tailsitter>
            <send_vision_estimation>0</send_vision_estimation>
            <send_odometry>0</send_odometry>
            <enable_lockstep>true</enable_lockstep>
            <use_tcp>true</use_tcp>
            <motorSpeedCommandPubTopic>/gazebo/command/motor_speed</motorSpeedCommandPubTopic>

            <!-- control channels, this way for every channel different settings can be realized -->
            <control_channels>
                <channel name="thruster1">
                    <input_index>0</input_index>
                    <input_offset>0</input_offset>
                    <input_scaling>1</input_scaling>
                    <zero_position_disarmed>0</zero_position_disarmed>
                    <zero_position_armed>0</zero_position_armed>
                    <joint_control_type>velocity</joint_control_type>
                    <joint_name>thruster1_joint</joint_name>
                </channel>
                <channel name="thruster2">
                    <input_index>1</input_index>
                    <input_offset>0</input_offset>
                    <input_scaling>1</input_scaling>
                    <zero_position_disarmed>0</zero_position_disarmed>
                    <zero_position_armed>0</zero_position_armed>
                    <joint_control_type>velocity</joint_control_type>
                    <joint_name>thruster2_joint</joint_name>
                </channel>
                <channel name="thruster3">
                    <input_index>2</input_index>
                    <input_offset>0</input_offset>
                    <input_scaling>1</input_scaling>
                    <zero_position_disarmed>0</zero_position_disarmed>
                    <zero_position_armed>0</zero_position_armed>
                    <joint_control_type>velocity</joint_control_type>
                    <joint_name>thruster3_joint</joint_name>
                </channel>
                <channel name="thruster4">
                    <input_index>3</input_index>
                    <input_offset>0</input_offset>
                    <input_scaling>1</input_scaling>
                    <zero_position_disarmed>0</zero_position_disarmed>
                    <zero_position_armed>0</zero_position_armed>
                    <joint_control_type>velocity</joint_control_type>
                    <joint_name>thruster4_joint</joint_name>
                </channel>
                <channel name="thruster5">
                    <input_index>4</input_index>
                    <input_offset>0</input_offset>
                    <input_scaling>1</input_scaling>
                    <zero_position_disarmed>0</zero_position_disarmed>
                    <zero_position_armed>0</zero_position_armed>
                    <joint_control_type>velocity</joint_control_type>
                    <joint_name>thruster5_joint</joint_name>
                </channel>
                <channel name="thruster6">
                    <input_index>5</input_index>
                    <input_offset>0</input_offset>
                    <input_scaling>1</input_scaling>
                    <zero_position_disarmed>0</zero_position_disarmed>
                    <zero_position_armed>0</zero_position_armed>
                    <joint_control_type>velocity</joint_control_type>
                    <joint_name>thruster6_joint</joint_name>
                </channel>
                <channel name="thruster7">
                    <input_index>6</input_index>
                    <input_offset>0</input_offset>
                    <input_scaling>1</input_scaling>
                    <zero_position_disarmed>0</zero_position_disarmed>
                    <zero_position_armed>0</zero_position_armed>
                    <joint_control_type>velocity</joint_control_type>
                    <joint_name>thruster7_joint</joint_name>
                </channel>
                <channel name="thruster8">
                    <input_index>7</input_index>
                    <input_offset>0</input_offset>
                    <input_scaling>1</input_scaling>
                    <zero_position_disarmed>0</zero_position_disarmed>
                    <zero_position_armed>0</zero_position_armed>
                    <joint_control_type>velocity</joint_control_type>
                    <joint_name>thruster8_joint</joint_name>
                </channel>
            </control_channels>
        </plugin>

        <plugin name="gazebo_imu_plugin" filename="libgazebo_imu_plugin.so">
            <robotNamespace />
            <linkName>base_link</linkName>
            <imuTopic>/imu</imuTopic>
            <gyroscopeNoiseDensity>0.0003394</gyroscopeNoiseDensity>
            <gyroscopeRandomWalk>3.8785e-05</gyroscopeRandomWalk>
            <gyroscopeBiasCorrelationTime>1000.0</gyroscopeBiasCorrelationTime>
            <gyroscopeTurnOnBiasSigma>0.0087</gyroscopeTurnOnBiasSigma>
            <accelerometerNoiseDensity>0.004</accelerometerNoiseDensity>
            <accelerometerRandomWalk>0.006</accelerometerRandomWalk>
            <accelerometerBiasCorrelationTime>300.0</accelerometerBiasCorrelationTime>
            <accelerometerTurnOnBiasSigma>0.196</accelerometerTurnOnBiasSigma>
        </plugin>

    </model>
</sdf>
