<sdf version='1.6' xmlns:xacro='http://ros.org/wiki/xacro'>
  <xacro:arg name='visual_material' default='DarkGrey' />
  <xacro:arg name='mavlink_addr' default='INADDR_ANY' />
  <xacro:arg name='mavlink_udp_port' default='14560' />
  <model name="delta_wing">
    <pose>0 0 0 0 0 1.5707</pose>
    <include>
      <uri>model://zephyr_delta_wing</uri>
      <pose>0 0 0.2 0 0 0</pose>
    </include>

    <link name="skid_pad">
      <inertial>
        <mass>0.1</mass>
        <inertia>
          <ixx>0.0043137104</ixx>
          <ixy>0</ixy>
          <iyy>0.00387382402</iyy>
          <ixz>0</ixz>
          <iyz>0</iyz>
          <izz>0.00809845106</izz>
        </inertia>
      </inertial>
      <!--
      <visual name="skid_pad">
        <pose>0 -0.05 0.025 0 0 0</pose>
        <geometry>
          <box>
            <size>.5 1.0 .05</size>
          </box>
        </geometry>
      </visual>
      -->
      <collision name="skid_pad">
        <pose>0 -0.07 0.025 0 0 0</pose>
        <geometry>
          <box>
            <size>1.0 0.5 .05</size>
          </box>
        </geometry>
        <surface>
          <contact>
            <ode>
              <kp>100000000</kp>
              <kd>1</kd>
              <min_depth>0.001</min_depth>
              <max_vel>1.0</max_vel>
            </ode>
          </contact>
          <friction>
            <ode>
              <!--
              <slip2>20</slip2>
              -->
              <mu>0.3</mu>
              <mu2>0</mu2>
              <fdir1>1 0 0</fdir1>
            </ode>
          </friction>
        </surface>
      </collision>
      <visual name="team_color">
        <pose>0 0 0.2 0 0 0</pose>
        <geometry>
          <mesh>
            <uri>model://zephyr_delta_wing/meshes/wing.dae</uri>
            <submesh>
              <name>Wing</name>
              <center>true</center>
            </submesh>
            <scale>1.02 1.02 1.02</scale>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/$(arg visual_material)</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <sensor name="camera" type="camera">
        <pose>0 6 0.0 0 0.0 -1.5707</pose>
        <camera>
          <horizontal_fov>1.347</horizontal_fov>
          <image>
            <width>640</width>
            <height>480</height>
          </image>
          <clip>
            <near>0.1</near>
            <far>1000</far>
          </clip>
        </camera>
        <always_on>0</always_on>
        <update_rate>15</update_rate>
        <visualize>false</visualize>
      </sensor>
      <sensor name="camera_wide" type="camera">
        <pose>0 5 2.0 0 0.3 -1.5707</pose>
        <camera>
          <horizontal_fov>2.0</horizontal_fov>
          <image>
            <width>640</width>
            <height>480</height>
          </image>
          <clip>
            <near>0.1</near>
            <far>1000</far>
          </clip>
        </camera>
        <always_on>0</always_on>
        <update_rate>15</update_rate>
        <visualize>false</visualize>
      </sensor>
    </link>
    <joint name="skid_pad_joint" type="revolute">
      <parent>zephyr_delta_wing::wing</parent>
      <child>skid_pad</child>
      <axis>
        <limit>
          <lower>0</lower>
          <upper>0</upper>
        </limit>
        <xyz>1 0 0</xyz>
        <use_parent_model_frame>true</use_parent_model_frame>
      </axis>
    </joint>

    <link name='delta_wing/imu_link'>
      <pose>0 0 0.2 0 0 -1.5707</pose>
      <inertial>
        <pose>0 0 0 0 0 0</pose>
        <mass>0.15</mass>
        <inertia>
          <ixx>1e-04</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>1e-04</iyy>
          <iyz>0</iyz>
          <izz>1e-04</izz>
        </inertia>
      </inertial>
    </link>
    <joint name='delta_wing/imu_joint' type='revolute'>
      <child>delta_wing/imu_link</child>
      <parent>zephyr_delta_wing::wing</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <lower>0</lower>
          <upper>0</upper>
        </limit>
        <dynamics>
          <damping>0.1</damping>
        </dynamics>
        <use_parent_model_frame>1</use_parent_model_frame>
      </axis>
      <physics>
        <ode>
          <implicit_spring_damper>1</implicit_spring_damper>
        </ode>
      </physics>
    </joint>

    <!--
    <link name="pusher">
      <pose>0 0.1174 0.2 1.5707 0 0</pose>
      <inertial>
        <mass>0.1</mass>
        <inertia>
          <ixx>0.000283137104</ixx>
          <ixy>0</ixy>
          <iyy>0.000387382402</iyy>
          <ixz>0</ixz>
          <iyz>0</iyz>
          <izz>0.000469845106</izz>
        </inertia>
      </inertial>
      <visual name="pusher">
        <pose>0 0 0 0 0 0</pose>
        <geometry>
          <cylinder>
            <length>.005</length>
            <radius>.15</radius>
          </cylinder>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/uctf.material</uri>
            <name>Uctf/LightGrayTransparent</name>
          </script>
        </material>
      </visual>
    </link>
    <joint name="pusher_joint" type="revolute">
      <parent>zephyr_delta_wing::wing</parent>
      <child>pusher</child>
      <axis>
        <dynamics>
          <damping>0.002</damping>
        </dynamics>
        <xyz>0 0 1</xyz>
      </axis>
      <physics>
        <ode>
          <implicit_spring_damper>1</implicit_spring_damper>
        </ode>
      </physics>
    </joint>
    -->

    <!-- copied from Plane model
    <plugin name='pusher' filename='libgazebo_motor_model.so'>
      <robotNamespace>delta_wing</robotNamespace>
      <jointName>pusher_joint</jointName>
      <linkName>pusher</linkName>
      <turningDirection>ccw</turningDirection>
      <timeConstantUp>0.0125</timeConstantUp>
      <timeConstantDown>0.025</timeConstantDown>
      <maxRotVelocity>3100</maxRotVelocity>
      <motorConstant>45.0e-06</motorConstant>
      <momentConstant>1e-08</momentConstant>
      <commandSubTopic>/gazebo/command/motor_speed</commandSubTopic>
      <motorNumber>4</motorNumber>
      <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
      <rollingMomentCoefficient>1e-08</rollingMomentCoefficient>
      <motorSpeedPubTopic>/motor_speed/4</motorSpeedPubTopic>
      <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
      <gazebo_joint_control_pid>
        <p>0.1</p>
        <i>0</i>
        <d>0</d>
        <iMax>0</iMax>
        <iMin>0</iMin>
        <cmdMax>1</cmdMax>
        <cmdMin>-1</cmdMin>
      </gazebo_joint_control_pid>
    </plugin>
    -->
    <include>
      <uri>model://gps</uri>
      <pose>0 0 0 0 0 0</pose>
      <name>gps</name>
    </include>
    <joint name='gps_joint' type='fixed'>
      <child>gps::link</child>
      <parent>base_link</parent>
    </joint>
    <plugin name='gazebo_imu_plugin' filename='libgazebo_imu_plugin.so'>
      <robotNamespace>delta_wing</robotNamespace>
      <linkName>delta_wing/imu_link</linkName>
      <imuTopic>/imu</imuTopic>
      <gyroscopeNoiseDensity>0.0003394</gyroscopeNoiseDensity>
      <gyroscopeRandomWalk>3.8785e-05</gyroscopeRandomWalk>
      <gyroscopeBiasCorrelationTime>1000.0</gyroscopeBiasCorrelationTime>
      <gyroscopeTurnOnBiasSigma>0.0087</gyroscopeTurnOnBiasSigma>
      <accelerometerNoiseDensity>0.004</accelerometerNoiseDensity>
      <accelerometerRandomWalk>0.006</accelerometerRandomWalk>
      <accelerometerBiasCorrelationTime>300.0</accelerometerBiasCorrelationTime>
      <accelerometerTurnOnBiasSigma>0.196</accelerometerTurnOnBiasSigma>
    </plugin>
    <plugin name='groundtruth_plugin' filename='libgazebo_groundtruth_plugin.so'>
      <robotNamespace/>
    </plugin>
    <plugin name='magnetometer_plugin' filename='libgazebo_magnetometer_plugin.so'>
      <robotNamespace/>
      <pubRate>100</pubRate>
      <noiseDensity>0.0004</noiseDensity>
      <randomWalk>6.4e-06</randomWalk>
      <biasCorrelationTime>600</biasCorrelationTime>
      <magTopic>/mag</magTopic>
    </plugin>
    <plugin name='barometer_plugin' filename='libgazebo_barometer_plugin.so'>
      <robotNamespace/>
      <pubRate>50</pubRate>
      <baroTopic>/baro</baroTopic>
    </plugin>
    <plugin name='mavlink_interface' filename='libgazebo_mavlink_interface.so'>
      <robotNamespace>delta_wing</robotNamespace>
      <imuSubTopic>/imu</imuSubTopic>
      <magSubTopic>/mag</magSubTopic>
      <baroSubTopic>/baro</baroSubTopic>
      <mavlink_addr>$(arg mavlink_addr)</mavlink_addr>
      <mavlink_udp_port>$(arg mavlink_udp_port)</mavlink_udp_port>
      <serialEnabled>false</serialEnabled>
      <serialDevice>/dev/ttyACM0</serialDevice>
      <baudRate>921600</baudRate>
      <qgc_addr>INADDR_ANY</qgc_addr>
      <qgc_udp_port>14550</qgc_udp_port>
      <hil_mode>false</hil_mode>
      <hil_state_level>false</hil_state_level>
      <motorSpeedCommandPubTopic>/gazebo/command/motor_speed</motorSpeedCommandPubTopic>
      <control_channels>
        <channel name="not_used_1">
          <input_index>0</input_index>
          <input_offset>0</input_offset>
          <input_scaling>0</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>velocity</joint_control_type>
        </channel>
        <channel name="not_used_2">
          <input_index>1</input_index>
          <input_offset>0</input_offset>
          <input_scaling>0</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>velocity</joint_control_type>
        </channel>
        <channel name="rudder">
          <input_index>2</input_index>
          <input_offset>0</input_offset>
          <input_scaling>0</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>position</joint_control_type>
        </channel>
        <channel name="flaps">
          <input_index>3</input_index>
          <input_offset>0</input_offset>
          <input_scaling>0</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>position</joint_control_type>
        </channel>
        <channel name="throttle">
          <input_index>4</input_index>
          <input_offset>1</input_offset>
          <input_scaling>324.6</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>velocity</joint_control_type>
          <joint_control_pid>
            <p>0.1</p>
            <i>0</i>
            <d>0</d>
            <iMax>0.0</iMax>
            <iMin>0.0</iMin>
            <cmdMax>2</cmdMax>
            <cmdMin>-2</cmdMin>
          </joint_control_pid>
          <joint_name>zephyr_delta_wing::propeller_joint</joint_name>
        </channel>
        <channel name="left_aileron">
          <input_index>5</input_index>
          <input_offset>0</input_offset>
          <!-- joint limits [-0.524, 0.524] -->
          <input_scaling>0.524</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>position</joint_control_type>
          <joint_name>zephyr_delta_wing::flap_left_joint</joint_name>
          <joint_control_pid>
            <p>10.0</p>
            <i>0</i>
            <d>0</d>
            <iMax>0</iMax>
            <iMin>0</iMin>
            <cmdMax>20</cmdMax>
            <cmdMin>-20</cmdMin>
          </joint_control_pid>
        </channel>
        <channel name="right_aileron">
          <input_index>6</input_index>
          <input_offset>0</input_offset>
          <!-- joint limits [-0.524, 0.524] -->
          <input_scaling>0.524</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>position</joint_control_type>
          <joint_name>zephyr_delta_wing::flap_right_joint</joint_name>
          <joint_control_pid>
            <p>10.0</p>
            <i>0</i>
            <d>0</d>
            <iMax>0</iMax>
            <iMin>0</iMin>
            <cmdMax>20</cmdMax>
            <cmdMin>-20</cmdMin>
          </joint_control_pid>
        </channel>
        <channel name="elevator">
          <input_index>7</input_index>
          <input_offset>0</input_offset>
          <input_scaling>0.524</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>position</joint_control_type>
        </channel>
      </control_channels>
    </plugin>
  </model>
</sdf>
