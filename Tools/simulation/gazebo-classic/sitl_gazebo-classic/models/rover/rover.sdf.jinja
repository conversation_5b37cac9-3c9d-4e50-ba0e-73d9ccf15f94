<?xml version="1.0" ?>
<sdf version="1.5">
  <model name="rover">
    <static>false</static>
    <link name="base_link">
      <inertial>
        <!-- subtracted wheel weights from dry weight of 771 kg -->
        <!-- http://www.polaris.com/en-us/ranger-utv/side-by-sides/ranger-ev/specifications -->
        <mass>720.0</mass>
        <inertia>
          <ixx>140</ixx>
          <ixy>0.0</ixy>
          <iyy>550</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>550</izz>
        </inertia>
        <!-- chassis c.o.g. near lateral/longitudinal center, height of 0.4 m -->
        <pose>0.1 0 0.4 0 0 0</pose>
      </inertial>
      <visual name="visual">
        <pose>0 0 0 0 0 -1.570796</pose>
        <geometry>
          <mesh>
            <uri>model://rover/meshes/polaris.dae</uri>
            <submesh>
              <name>Body</name>
              <center>false</center>
            </submesh>
          </mesh>
        </geometry>
        <material>
          <script>
            <uri>model://rover/materials/scripts</uri>
            <uri>model://rover/materials/textures</uri>
            <name>PolarisXP900/Diffuse</name>
          </script>
        </material>
      </visual>
      <collision name="base_link_bottom">
        <pose>0.2 0.0 0.335 0 0 0</pose>
        <geometry>
          <box>
            <size>1.34 1.65746 0.06</size>
          </box>
        </geometry>
      </collision>
      <collision name="cargo_bottom">
        <pose>-1.0 0.0 0.921 0.0 0.0 0.0</pose>
        <geometry>
          <box>
            <size>1.04609 1.6998 0.01</size>
          </box>
        </geometry>
      </collision>
      <collision name="cargo_front">
        <pose>-0.495 0.0 1.06 0.0 0.0 0.0</pose>
        <geometry>
          <box>
            <size>0.05 1.69982 0.27</size>
          </box>
        </geometry>
      </collision>
      <collision name="cargo_back">
        <pose>-1.465 0.0 1.06 0.0 0.0 0.0</pose>
        <geometry>
          <box>
            <size>0.05 1.69982 0.27</size>
          </box>
        </geometry>
      </collision>
      <collision name="cargo_left">
        <pose>-0.97 0.82491 1.06 0.0 0.0 0.0</pose>
        <geometry>
          <box>
            <size>1.04609 0.05 0.27</size>
          </box>
        </geometry>
      </collision>
      <collision name="cargo_right">
        <pose>-0.97 -0.82491 1.06 0.0 0.0 0.0</pose>
        <geometry>
          <box>
            <size>1.04609 0.05 0.27</size>
          </box>
        </geometry>
      </collision>
      <collision name="seat">
        <pose>0.0 0.0 0.62 0 0 0</pose>
        <geometry>
          <box>
            <size>0.52167 1.37206 0.53369</size>
          </box>
        </geometry>
        <surface>
          <contact>
            <ode>
              <min_depth>0.01</min_depth>
            </ode>
          </contact>
        </surface>
      </collision>
      <collision name="mud_seat">
        <pose>0.0 0.0 0.86 0 0 0</pose>
        <geometry>
          <box>
            <size>0.52167 1.30 0.1</size>
          </box>
        </geometry>
        <surface>
          <contact>
            <collide_without_contact>true</collide_without_contact>
          </contact>
        </surface>
      </collision>
      <sensor name="seat_contact" type="contact">
        <always_on>true</always_on>
        <update_rate>1000</update_rate>
        <contact>
          <collision>mud_seat</collision>
        </contact>
      </sensor>
      <collision name="seat_back">
        <pose>-0.26 0.0 1.125 0.0 -0.2 0.0</pose>
        <geometry>
          <box>
            <size>0.06 1.37206 0.6</size>
          </box>
        </geometry>
      </collision>
      <collision name="engine">
        <pose>1.12 0.0 0.7 0.0 0.0 0.0</pose>
        <geometry>
          <box>
            <size>0.58 1.3 0.8</size>
          </box>
        </geometry>
      </collision>
      <collision name="rollcage_top_left">
        <pose>-0.02 0.76 1.936 0 1.60 0</pose>
        <geometry>
          <cylinder>
            <radius>0.03</radius>
            <length>0.68</length>
          </cylinder>
        </geometry>
      </collision>
      <collision name="rollcage_top_right">
        <pose>-0.02 -0.76 1.936 0 1.60 0</pose>
        <geometry>
          <cylinder>
            <radius>0.03</radius>
            <length>0.68</length>
          </cylinder>
        </geometry>
      </collision>
      <collision name="rollcage_top_front">
        <pose>0.315 0.0 1.93 0 -1.0 0</pose>
        <geometry>
          <box>
            <size>0.01299 1.54 0.10226</size>
          </box>
        </geometry>
      </collision>
      <collision name="rollcage_top_back">
        <pose>-0.410 0.0 1.88 0 0 0</pose>
        <geometry>
          <box>
            <size>0.01299 1.54 0.10226</size>
          </box>
        </geometry>
      </collision>
      <collision name="rollcage_front_left">
        <pose>0.602 0.755 1.45 0 -0.54 0</pose>
        <geometry>
          <cylinder>
            <radius>0.03</radius>
            <length>1.15</length>
          </cylinder>
        </geometry>
      </collision>
      <collision name="rollcage_front_right">
        <pose>0.602 -0.755 1.45 0 -0.54 0</pose>
        <geometry>
          <cylinder>
            <radius>0.03</radius>
            <length>1.15</length>
          </cylinder>
        </geometry>
      </collision>
      <collision name="rollcage_back_left">
        <pose>-0.4 0.755 1.45 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.03</radius>
            <length>1.00</length>
          </cylinder>
        </geometry>
      </collision>
      <collision name="rollcage_back_right">
        <pose>-0.4 -0.755 1.45 0 0 0</pose>
        <geometry>
          <cylinder>
            <radius>0.03</radius>
            <length>1.00</length>
          </cylinder>
        </geometry>
      </collision>
      <collision name="rollcage_X_1">
        <pose>-0.411 0.04 1.445 0.397 0 0</pose>
        <geometry>
          <box>
            <size>0.01392 1.557245 0.078</size>
          </box>
        </geometry>
      </collision>
      <collision name="rollcage_X_2">
        <pose>-0.40 -0.04 1.445 -0.397 0 0</pose>
        <geometry>
          <box>
            <size>0.01392 1.557245 0.078</size>
          </box>
        </geometry>
      </collision>
      <collision name="central_hump1_collision">
        <pose>0.55 -0.1 0.4 0 0 0</pose>
        <geometry>
          <box>
            <size>0.6 0.15 0.1</size>
          </box>
        </geometry>
      </collision>
      <collision name="central_hump2_collision">
        <pose>0.7 -0.05 0.45 0 -0.5 0</pose>
        <geometry>
          <box>
            <size>0.20 0.05 0.1</size>
          </box>
        </geometry>
      </collision>
      <collision name="central_hump3_collision">
        <pose>0.798 -0.125 0.478 0 -0.8 0</pose>
        <geometry>
          <box>
            <size>0.129 0.1 0.05</size>
          </box>
        </geometry>
      </collision>
      <collision name="central_hump4_collision">
        <pose>0.8135 -0.05 0.45 0 0 0</pose>
        <geometry>
          <box>
            <size>0.10 0.05 0.1835</size>
          </box>
        </geometry>
      </collision>
      <collision name="central_hump5_collision">
        <pose>0.84 -0.125 0.45 0 0 0</pose>
        <geometry>
          <box>
            <size>0.03 0.1 0.1835</size>
          </box>
        </geometry>
      </collision>
      <collision name="central_hump6_collision">
        <pose>0.82 -0.125 0.475 0 0 0</pose>
        <geometry>
          <box>
            <size>0.03 0.1 0.05</size>
          </box>
        </geometry>
      </collision>
      <visual name="cargo_visual">
        <pose>-1.0 0 1.03230 0 0 -1.5707</pose>
        <geometry>
          <mesh>
            <uri>model://rover/meshes/polaris.dae</uri>
            <submesh>
              <name>Bed</name>
              <center>true</center>
            </submesh>
          </mesh>
        </geometry>
        <material>
          <script>
            <uri>model://rover/materials/scripts</uri>
            <uri>model://rover/materials/textures</uri>
            <name>PolarisXP900/Diffuse</name>
          </script>
        </material>
      </visual>
      <visual name="tailgate_visual">
        <pose>-1.492 0 1.03 0 0 -1.5707</pose>
        <geometry>
          <mesh>
            <uri>model://rover/meshes/polaris.dae</uri>
            <submesh>
              <name>Tail_Gate</name>
              <center>true</center>
            </submesh>
          </mesh>
        </geometry>
        <material>
          <script>
            <uri>model://rover/materials/scripts</uri>
            <uri>model://rover/materials/textures</uri>
            <name>PolarisXP900/Diffuse</name>
          </script>
        </material>
      </visual>
      <visual name="front_left_brake_visual">
        <pose>1.12 -0.57488 0.35516 3.1415 0 1.5707</pose>
        <geometry>
          <mesh>
            <uri>model://rover/meshes/polaris.dae</uri>
            <submesh>
              <name>Brake_Front_Left</name>
              <center>true</center>
            </submesh>
          </mesh>
        </geometry>
        <material>
          <script>
            <uri>model://rover/materials/scripts</uri>
            <uri>model://rover/materials/textures</uri>
            <name>PolarisXP900/Diffuse</name>
          </script>
        </material>
      </visual>
      <visual name="front_right_brake_visual">
        <pose>1.12 0.57488 0.35516 3.1415 0 1.5707</pose>
        <geometry>
          <mesh>
            <uri>model://rover/meshes/polaris.dae</uri>
            <submesh>
              <name>Brake_Front_Right</name>
              <center>true</center>
            </submesh>
          </mesh>
        </geometry>
        <material>
          <script>
            <uri>model://rover/materials/scripts</uri>
            <uri>model://rover/materials/textures</uri>
            <name>PolarisXP900/Diffuse</name>
          </script>
        </material>
      </visual>
      <visual name="central_hump1">
        <pose>0.55 -0.1 0.4 0 0 0</pose>
        <geometry>
          <box>
            <size>0.6 0.15 0.1</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/DarkGrey</name>
          </script>
        </material>
      </visual>
      <visual name="central_hump2">
        <pose>0.7 -0.05 0.45 0 -0.5 0</pose>
        <geometry>
          <box>
            <size>0.20 0.05 0.1</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/DarkGrey</name>
          </script>
        </material>
      </visual>
      <visual name="central_hump3">
        <pose>0.798 -0.125 0.478 0 -0.8 0</pose>
        <geometry>
          <box>
            <size>0.129 0.1 0.05</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/DarkGrey</name>
          </script>
        </material>
      </visual>
      <visual name="central_hump4">
        <pose>0.8135 -0.05 0.45 0 0 0</pose>
        <geometry>
          <box>
            <size>0.10 0.05 0.1835</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/DarkGrey</name>
          </script>
        </material>
      </visual>
      <visual name="central_hump5">
        <pose>0.84 -0.125 0.45 0 0 0</pose>
        <geometry>
          <box>
            <size>0.03 0.1 0.1835</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/DarkGrey</name>
          </script>
        </material>
      </visual>
      <visual name="central_hump6">
        <pose>0.82 -0.125 0.475 0 0 0</pose>
        <geometry>
          <box>
            <size>0.03 0.1 0.05</size>
          </box>
        </geometry>
        <material>
          <script>
            <uri>file://media/materials/scripts/gazebo.material</uri>
            <name>Gazebo/DarkGrey</name>
          </script>
        </material>
      </visual>
    </link>
    <link name='rover/imu_link'>
      <pose>0 0 0 0 0 0</pose>
      <inertial>
        <pose>0 0 0 0 0 0</pose>
        <mass>0.015</mass>
        <inertia>
          <ixx>1e-05</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>1e-05</iyy>
          <iyz>0</iyz>
          <izz>1e-05</izz>
        </inertia>
      </inertial>
    </link>
    <joint name='rover/imu_joint' type='revolute'>
      <child>rover/imu_link</child>
      <parent>base_link</parent>
      <axis>
        <xyz>1 0 0</xyz>
        <limit>
          <lower>0</lower>
          <upper>0</upper>
          <effort>0</effort>
          <velocity>0</velocity>
        </limit>
        <dynamics>
          <spring_reference>0</spring_reference>
          <spring_stiffness>0</spring_stiffness>
        </dynamics>
        <use_parent_model_frame>1</use_parent_model_frame>
      </axis>
    </joint>
    <link name="front_left_wheel">
      <pose>1.20223 0.71562 0.34697 -1.52 0.0 0.0</pose>
      <inertial>
        <mass>12</mass>
        <!-- estimated from http://www.rzrforums.net/wheels-tires/1729-tire-wheel-weights-most-sizes.html -->
        <inertia>
          <ixx>0.5</ixx>
          <ixy>0.0</ixy>
          <iyy>0.5</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>1.0</izz>
        </inertia>
      </inertial>
      <collision name="collision">
        <geometry>
          <cylinder>
            <radius>0.3175</radius>
            <length>0.2794</length>
          </cylinder>
        </geometry>
        <max_contacts>1</max_contacts>
        <surface>
          <friction>
            <ode>
              <mu>1.0</mu>
              <mu2>1.0</mu2>
              <fdir1>0 0 1</fdir1>
              <slip1>0.0</slip1>
              <slip2>0.0</slip2>
            </ode>
          </friction>
          <contact>
            <ode>
              <min_depth>0.005</min_depth>
              <kp>1e8</kp>
            </ode>
          </contact>
        </surface>
      </collision>
      <visual name="tire_visual">
        <pose>0 0 0 3.14159 1.570796 0</pose>
        <geometry>
          <mesh>
            <uri>model://rover/meshes/polaris.dae</uri>
            <scale>1.003700111 0.886200464 0.886200464</scale>
            <submesh>
              <name>Wheel_Front_Left</name>
              <center>true</center>
            </submesh>
          </mesh>
        </geometry>
        <material>
          <script>
            <uri>model://rover/materials/scripts</uri>
            <uri>model://rover/materials/textures</uri>
            <name>PolarisXP900/Diffuse</name>
          </script>
        </material>
      </visual>
    </link>
    <link name="front_right_wheel">
      <pose>1.20223 -0.71562 0.34697 1.52 0.0 0.0</pose>
      <inertial>
        <mass>12</mass>
        <!-- estimated from http://www.rzrforums.net/wheels-tires/1729-tire-wheel-weights-most-sizes.html -->
        <inertia>
          <ixx>0.5</ixx>
          <ixy>0.0</ixy>
          <iyy>0.5</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>1.0</izz>
        </inertia>
      </inertial>
      <collision name="collision">
        <geometry>
          <cylinder>
            <radius>0.3175</radius>
            <length>0.2794</length>
          </cylinder>
        </geometry>
        <max_contacts>1</max_contacts>
        <surface>
          <friction>
            <ode>
              <mu>1.0</mu>
              <mu2>1.0</mu2>
              <fdir1>0 0 1</fdir1>
              <slip1>0.0</slip1>
              <slip2>0.0</slip2>
            </ode>
          </friction>
          <contact>
            <ode>
              <min_depth>0.005</min_depth>
              <kp>1e8</kp>
            </ode>
          </contact>
        </surface>
      </collision>
      <visual name="visual">
        <pose>0 0 0 0 -1.570796 0</pose>
        <geometry>
          <mesh>
            <uri>model://rover/meshes/polaris.dae</uri>
            <scale>1.003700111 0.886200464 0.886200464</scale>
            <submesh>
              <name>Wheel_Front_Right</name>
              <center>true</center>
            </submesh>
          </mesh>
        </geometry>
        <material>
          <script>
            <uri>model://rover/materials/scripts</uri>
            <uri>model://rover/materials/textures</uri>
            <name>PolarisXP900/Diffuse</name>
          </script>
        </material>
      </visual>
    </link>
    <link name="rear_left_wheel">
      <pose>-0.99377 0.71562 0.34697 -1.52 0.0 0.0</pose>
      <inertial>
        <mass>12</mass>
        <!-- estimated from http://www.rzrforums.net/wheels-tires/1729-tire-wheel-weights-most-sizes.html -->
        <inertia>
          <ixx>0.5</ixx>
          <ixy>0.0</ixy>
          <iyy>0.5</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>1.0</izz>
        </inertia>
      </inertial>
      <collision name="collision">
        <geometry>
          <cylinder>
            <radius>0.3175</radius>
            <length>0.2794</length>
          </cylinder>
        </geometry>
        <max_contacts>1</max_contacts>
        <surface>
          <friction>
            <ode>
              <mu>1.0</mu>
              <mu2>1.0</mu2>
              <fdir1>0 0 1</fdir1>
              <slip1>0.0</slip1>
              <slip2>0.0</slip2>
            </ode>
          </friction>
          <contact>
            <ode>
              <min_depth>0.005</min_depth>
              <kp>1e8</kp>
            </ode>
          </contact>
        </surface>
      </collision>
      <visual name="visual">
        <pose>0 0 0 0 -1.570796 0</pose>
        <geometry>
          <mesh>
            <uri>model://rover/meshes/polaris.dae</uri>
            <scale>1.003700111 0.886200464 0.886200464</scale>
            <submesh>
              <name>Wheels_Rear_Left</name>
              <center>true</center>
            </submesh>
          </mesh>
        </geometry>
        <material>
          <script>
            <uri>model://rover/materials/scripts</uri>
            <uri>model://rover/materials/textures</uri>
            <name>PolarisXP900/Diffuse</name>
          </script>
        </material>
      </visual>
    </link>
    <link name="rear_right_wheel">
      <pose>-0.99377 -0.71562 0.34697 1.52 0.0 0.0</pose>
      <inertial>
        <mass>12</mass>
        <!-- estimated from http://www.rzrforums.net/wheels-tires/1729-tire-wheel-weights-most-sizes.html -->
        <inertia>
          <ixx>0.5</ixx>
          <ixy>0.0</ixy>
          <iyy>0.5</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>1.0</izz>
        </inertia>
      </inertial>
      <collision name="collision">
        <geometry>
          <cylinder>
            <radius>0.3175</radius>
            <length>0.2794</length>
          </cylinder>
        </geometry>
        <max_contacts>1</max_contacts>
        <surface>
          <friction>
            <ode>
              <mu>1.0</mu>
              <mu2>1.0</mu2>
              <fdir1>0 0 1</fdir1>
              <slip1>0.0</slip1>
              <slip2>0.0</slip2>
            </ode>
          </friction>
          <contact>
            <ode>
              <min_depth>0.005</min_depth>
              <kp>1e8</kp>
            </ode>
          </contact>
        </surface>
      </collision>
      <visual name="visual">
        <pose>0 0 0 0 -1.570796 0</pose>
        <geometry>
          <mesh>
            <uri>model://rover/meshes/polaris.dae</uri>
            <scale>1.003700111 0.886200464 0.886200464</scale>
            <submesh>
              <name>Wheels_Rear_Right</name>
              <center>true</center>
            </submesh>
          </mesh>
        </geometry>
        <material>
          <script>
            <uri>model://rover/materials/scripts</uri>
            <uri>model://rover/materials/textures</uri>
            <name>PolarisXP900/Diffuse</name>
          </script>
        </material>
      </visual>
    </link>
    <link name="front_right_wheel_steering_block">
      <pose>1.20223 -0.5 0.35515 1.570796 0.0 0.0</pose>
      <inertial>
        <mass>1</mass>
        <inertia>
          <ixx>0.01</ixx>
          <ixy>0.0</ixy>
          <iyy>0.01</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>0.01</izz>
        </inertia>
      </inertial>
      <collision name="collision">
        <geometry>
          <cylinder>
            <radius>0.05</radius>
            <length>0.01</length>
          </cylinder>
        </geometry>
      </collision>
    </link>
    <link name="front_left_wheel_steering_block">
      <pose>1.20223 0.5 0.35515 1.570796 0.0 0.0</pose>
      <inertial>
        <mass>1</mass>
        <inertia>
          <ixx>0.01</ixx>
          <ixy>0.0</ixy>
          <iyy>0.01</iyy>
          <ixz>0.0</ixz>
          <iyz>0.0</iyz>
          <izz>0.01</izz>
        </inertia>
      </inertial>
      <collision name="collision">
        <geometry>
          <cylinder>
            <radius>0.05</radius>
            <length>0.01</length>
          </cylinder>
        </geometry>
      </collision>
    </link>
    <joint type="revolute" name="front_left_steering_joint">
      <child>front_left_wheel_steering_block</child>
      <parent>base_link</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <lower>-0.7727</lower>
          <upper>0.7727</upper>
        </limit>
        <dynamics>
          <damping>50.0</damping>
        </dynamics>
        <use_parent_model_frame>true</use_parent_model_frame>
      </axis>
      <physics>
        <ode>
          <cfm_damping>1</cfm_damping>
          <limit>
            <cfm>0.000000</cfm>
            <erp>0.900000</erp>
          </limit>
        </ode>
      </physics>
    </joint>
    <joint type="revolute" name="front_left_wheel_joint">
      <child>front_left_wheel</child>
      <parent>front_left_wheel_steering_block</parent>
      <axis>
        <xyz>0 1 0.05</xyz>
        <use_parent_model_frame>true</use_parent_model_frame>
      </axis>
    </joint>
    <joint type="revolute" name="front_right_steering_joint">
      <child>front_right_wheel_steering_block</child>
      <parent>base_link</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <lower>-0.7727</lower>
          <upper>0.7727</upper>
        </limit>
        <dynamics>
          <damping>50.0</damping>
        </dynamics>
        <use_parent_model_frame>true</use_parent_model_frame>
      </axis>
      <physics>
        <ode>
          <cfm_damping>1</cfm_damping>
          <limit>
            <cfm>0.000000</cfm>
            <erp>0.900000</erp>
          </limit>
        </ode>
      </physics>
    </joint>
    <joint type="revolute" name="front_right_wheel_joint">
      <child>front_right_wheel</child>
      <parent>front_right_wheel_steering_block</parent>
      <axis>
        <xyz>0 1 -0.05</xyz>
        <use_parent_model_frame>true</use_parent_model_frame>
      </axis>
    </joint>
    <joint type="revolute" name="rear_left_wheel_joint">
      <child>rear_left_wheel</child>
      <parent>base_link</parent>
      <axis>
        <xyz>0 1 0.05</xyz>
        <use_parent_model_frame>true</use_parent_model_frame>
      </axis>
    </joint>
    <joint type="revolute" name="rear_right_wheel_joint">
      <pose>0.0 0.0 -0.1 0 0 0</pose>
      <child>rear_right_wheel</child>
      <parent>base_link</parent>
      <axis>
        <xyz>0 1 -0.05</xyz>
        <use_parent_model_frame>true</use_parent_model_frame>
      </axis>
    </joint>
<!--     <joint type="revolute" name="rear_differential_joint">
      <child>rear_right_wheel</child>
      <parent>rear_left_wheel</parent>
      <axis>
        <xyz>0 1 0</xyz>
        <dynamics>
          <damping>70</damping>
        </dynamics>
        <use_parent_model_frame>true</use_parent_model_frame>
      </axis>
      <physics>
        <ode>
          <erp>0</erp>
          <cfm>1000</cfm>
          <cfm_damping>1</cfm_damping>
        </ode>
      </physics>
    </joint> -->
    <include>
      <uri>model://gps</uri>
      <pose>0 0 0 0 0 0</pose>
      <name>gps</name>
    </include>
    <joint name='gps_joint' type='fixed'>
      <child>gps::link</child>
      <parent>base_link</parent>
    </joint>
    <plugin name='barometer_plugin' filename='libgazebo_barometer_plugin.so'>
      <robotNamespace/>
      <pubRate>10</pubRate>
      <baroTopic>/baro</baroTopic>
    </plugin>
    <plugin name='groundtruth_plugin' filename='libgazebo_groundtruth_plugin.so'>
      <robotNamespace/>
    </plugin>
    <plugin name='magnetometer_plugin' filename='libgazebo_magnetometer_plugin.so'>
      <robotNamespace/>
      <pubRate>100</pubRate>
      <noiseDensity>0.0004</noiseDensity>
      <randomWalk>6.4e-06</randomWalk>
      <biasCorrelationTime>600</biasCorrelationTime>
      <magTopic>/mag</magTopic>
    </plugin>
    <plugin name='mavlink_interface' filename='libgazebo_mavlink_interface.so'>
      <robotNamespace/>
      <imuSubTopic>/imu</imuSubTopic>
      <magSubTopic>/mag</magSubTopic>
      <baroSubTopic>/baro</baroSubTopic>
      <mavlink_addr>INADDR_ANY</mavlink_addr>
      <mavlink_tcp_port>{{ mavlink_tcp_port }}</mavlink_tcp_port>
      <mavlink_udp_port>{{ mavlink_udp_port }}</mavlink_udp_port>
      <serialEnabled>{{ serial_enabled }}</serialEnabled>
      <serialDevice>{{ serial_device }}</serialDevice>
      <baudRate>{{ serial_baudrate }}</baudRate>
      <qgc_addr>INADDR_ANY</qgc_addr>
      <qgc_udp_port>14550</qgc_udp_port>
      <sdk_addr>INADDR_ANY</sdk_addr>
      <sdk_udp_port>14540</sdk_udp_port>
      <hil_mode>{{ hil_mode }}</hil_mode>
      <hil_state_level>0</hil_state_level>
      <send_vision_estimation>0</send_vision_estimation>
      <send_odometry>1</send_odometry>
      <enable_lockstep>1</enable_lockstep>
      <use_tcp>1</use_tcp>
      <motorSpeedCommandPubTopic>/gazebo/command/motor_speed</motorSpeedCommandPubTopic>
      <control_channels>
        <!--
        <channel name="front_left_wheel_drive">
          <input_index>3</input_index>
          <input_offset>0</input_offset>
          <input_scaling>10</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>velocity_kinematic</joint_control_type>
          <joint_name>front_left_wheel_joint</joint_name>
        </channel>
        <channel name="front_right_wheel_drive">
          <input_index>4</input_index>
          <input_offset>0</input_offset>
          <input_scaling>10</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>velocity_kinematic</joint_control_type>
          <joint_name>front_right_wheel_joint</joint_name>
        </channel>
      -->
        <channel name="rear_left_wheel_drive">
          <input_index>5</input_index>
          <input_offset>0</input_offset>
          <input_scaling>40</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>velocity</joint_control_type>
          <joint_name>rear_left_wheel_joint</joint_name>
          <joint_control_pid>
            <p>50</p>
            <i>20</i>
            <d>2</d>
            <iMax>800</iMax>
            <iMin>-800</iMin>
            <cmdMax>1200</cmdMax>
            <cmdMin>-1200</cmdMin>
          </joint_control_pid>
        </channel>
        <channel name="rear_right_wheel_drive">
          <input_index>6</input_index>
          <input_offset>0</input_offset>
          <input_scaling>40</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>velocity</joint_control_type>
          <joint_name>rear_right_wheel_joint</joint_name>
          <joint_control_pid>
            <p>50</p>
            <i>20</i>
            <d>2</d>
            <iMax>800</iMax>
            <iMin>-800</iMin>
            <cmdMax>1200</cmdMax>
            <cmdMin>-1200</cmdMin>
          </joint_control_pid>
        </channel>
        <channel name="left_wheel_steering">
          <input_index>0</input_index>
          <input_offset>0</input_offset>
          <input_scaling>-0.9</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>position</joint_control_type>
          <joint_name>front_left_steering_joint</joint_name>
          <joint_control_pid>
            <p>80</p>
            <i>40</i>
            <d>0.1</d>
            <iMax>40</iMax>
            <iMin>-40</iMin>
            <cmdMax>80</cmdMax>
            <cmdMin>-80</cmdMin>
          </joint_control_pid>
        </channel>
        <channel name="right_wheel_steering">
          <input_index>1</input_index>
          <input_offset>0</input_offset>
          <input_scaling>-0.9</input_scaling>
          <zero_position_disarmed>0</zero_position_disarmed>
          <zero_position_armed>0</zero_position_armed>
          <joint_control_type>position</joint_control_type>
          <joint_name>front_right_steering_joint</joint_name>
          <joint_control_pid>
            <p>80</p>
            <i>40</i>
            <d>0.1</d>
            <iMax>40</iMax>
            <iMin>-40</iMin>
            <cmdMax>80</cmdMax>
            <cmdMin>-80</cmdMin>
          </joint_control_pid>
        </channel>
      </control_channels>
    </plugin>
    <plugin name='gazebo_imu_plugin' filename='libgazebo_imu_plugin.so'>
      <robotNamespace></robotNamespace>
      <linkName>rover/imu_link</linkName>
      <imuTopic>/imu</imuTopic>
      <gyroscopeNoiseDensity>0.0003394</gyroscopeNoiseDensity>
      <gyroscopeRandomWalk>3.8785e-05</gyroscopeRandomWalk>
      <gyroscopeBiasCorrelationTime>1000.0</gyroscopeBiasCorrelationTime>
      <gyroscopeTurnOnBiasSigma>0.0087</gyroscopeTurnOnBiasSigma>
      <accelerometerNoiseDensity>0.004</accelerometerNoiseDensity>
      <accelerometerRandomWalk>0.006</accelerometerRandomWalk>
      <accelerometerBiasCorrelationTime>300.0</accelerometerBiasCorrelationTime>
      <accelerometerTurnOnBiasSigma>0.196</accelerometerTurnOnBiasSigma>
    </plugin>

  </model>
</sdf>
