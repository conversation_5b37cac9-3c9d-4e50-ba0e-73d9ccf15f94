{
    "configurations": [
        {
            "name": "PX4",
            "includePath": [
                "${workspaceFolder}/**",
            ],
            "defines": [],
            "macFrameworkPath": [],
            "configurationProvider": "ms-vscode.cmake-tools",
            "cppStandard": "c++14",
            "cStandard": "c11",
            "forcedInclude": [
                 "@PX4_BINARY_DIR@/px4_boardconfig.h",
                 "@PX4_SOURCE_DIR@/platforms/common/include/px4_platform_common/defines.h"
                 ]
        }
    ],
    "version": 4
}
