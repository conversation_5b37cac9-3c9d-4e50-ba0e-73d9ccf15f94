/****************************************************************************
 *
 *   Copyright (c) 2019 PX4 Development Team. All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 * 3. Neither the name PX4 nor the names of its contributors may be
 *    used to endorse or promote products derived from this software
 *    without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS
 * OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 * AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
 * ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 ****************************************************************************/

#pragma once

#include "WorkItem.hpp"
#include <px4_platform_common/sem.h>

namespace px4
{

/**
 * @class WorkItemSingleShot
 * Run a method on a specific work queue and wait for it to exit.
 * Example usage:
 *   WorkItemSingleShot initializer(px4::device_bus_to_wq(device_id.devid), instantiate, &data);
 *   initializer.ScheduleNow();
 *   initializer.wait();
 */
class WorkItemSingleShot : public px4::WorkItem
{
public:
	using worker_method = void (*)(void *arg);

	WorkItemSingleShot(const px4::wq_config_t &config, worker_method method, void *argument);
	WorkItemSingleShot(const px4::WorkItem &work_item, worker_method method, void *argument);

	~WorkItemSingleShot();

	WorkItemSingleShot() = delete;

	// no copy, assignment, move, move assignment
	WorkItemSingleShot(const WorkItemSingleShot &) = delete;
	WorkItemSingleShot &operator=(const WorkItemSingleShot &) = delete;
	WorkItemSingleShot(WorkItemSingleShot &&) = delete;
	WorkItemSingleShot &operator=(WorkItemSingleShot &&) = delete;

	void wait();
protected:
	void Run() override;
private:
	void *_argument;
	worker_method _method;
	px4_sem_t _sem;
};

} // namespace px4
