/* declarations of builtin command list - automatically generated, do not edit */

#pragma once

#include <px4_platform_common/tasks.h>	// px4_main_t
#include <map>
#include <string>

// Maps an app name to it's function.
typedef std::map<std::string, px4_main_t> apps_map_type;

// Initialize an apps map.
__EXPORT void init_app_map(apps_map_type &apps);

// List an apps map.
__EXPORT void list_builtins(apps_map_type &apps);
