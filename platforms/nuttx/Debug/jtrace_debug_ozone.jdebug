/*********************************************************************
*                 (c) SEGGER Microcontroller GmbH                    *
*                      The Embedded Experts                          *
*                         www.segger.com                             *
**********************************************************************
                                                                      
File          : jtrace_debug_ozone.jdebug
Created       : 26 Oct 2020 14:30
Ozone Version : V3.20e
*/

/*********************************************************************
*                                                                     
*       OnProjectLoad                                                 
*                                                                     
* Function description                                                
*   Project load routine. Required.                                   
*                                                                     
**********************************************************************
*/                                                                    
void OnProjectLoad (void) {
  //
  // Dialog-generated settings
  //
  Project.SetTraceSource ("Trace Pins");
  Project.SetDevice ("STM32H753II");
  Project.SetHostIF ("USB", "");
  Project.SetTargetIF ("SWD");
  Project.SetTIFSpeed ("4 MHz");
  Project.AddSvdFile ("$(InstallDir)/Config/CPU/Cortex-M7F.svd");
  Project.AddSvdFile ("$(InstallDir)/Config/Peripherals/ARMv7M.svd");
  //
  // User settings
  //
  Edit.Preference (PREF_TIMESTAMP_FORMAT, TIMESTAMP_FORMAT_INST_CNT);
  Edit.SysVar (VAR_TRACE_TIMESTAMPS_ENABLED, 0);
  Edit.SysVar (VAR_TRACE_CORE_CLOCK, 480000000);
}

/*********************************************************************
*                                                                     
*       OnStartupComplete                                             
*                                                                     
* Function description                                                
*   Called when program execution has reached/passed                  
*   the startup completion point. Optional.                           
*                                                                     
**********************************************************************
*/                                                                    
//void OnStartupComplete (void) {                                     
//}                                                                   

/*********************************************************************
*                                                                     
*      TargetReset                                                    
*                                                                     
* Function description                                                
*   Replaces the default target device reset routine. Optional.       
*                                                                     
* Notes                                                               
*   This example demonstrates the usage when                          
*   debugging a RAM program on a Cortex-M target device               
*                                                                     
**********************************************************************
*/                                                                    
//void TargetReset (void) {                                           
//                                                                    
//  unsigned int SP;                                                  
//  unsigned int PC;                                                  
//  unsigned int VectorTableAddr;                                     
//                                                                    
//  VectorTableAddr = Program.GetBaseAddr();                          
//                                                                    
//  if (VectorTableAddr != 0xFFFFFFFF) {                              
//    SP = Target.ReadU32(VectorTableAddr);                           
//    Target.SetReg("SP", SP);                                      
//  } else {                                                          
//    Util.Log("Project file error: failed to get program base");   
//  }                                                                 
//                                                                    
//  PC = Elf.GetEntryPointPC();                                       
//                                                                    
//  if (PC != 0xFFFFFFFF) {                                           
//    Target.SetReg("PC", PC);                                      
//  } else if (VectorTableAddr != 0xFFFFFFFF) {                       
//    PC = Target.ReadU32(VectorTableAddr + 4);                       
//    Target.SetReg("PC", PC);                                      
//}                                                                   

/*********************************************************************
*                                                                     
*       BeforeTargetReset                                             
*                                                                     
* Function description                                                
*   Event handler routine. Optional.                                  
*                                                                     
**********************************************************************
*/                                                                    
//void BeforeTargetReset (void) {                                     
//}                                                                   

/*********************************************************************
*                                                                     
*       AfterTargetReset                                              
*                                                                     
* Function description                                                
*   Event handler routine. Optional.                                  
*    - Sets the PC register to program reset value.                   
*    - Sets the SP register to program reset value on Cortex-M.       
*                                                                     
**********************************************************************
*/                                                                    
void AfterTargetReset (void) {
  unsigned int SP;                                                
  unsigned int PC;                                                
  unsigned int VectorTableAddr;                                   
                                                                  
  VectorTableAddr = Elf.GetBaseAddr();                            
                                                                  
  if (VectorTableAddr == 0xFFFFFFFF) {                            
    Util.Log("Project file error: failed to get program base"); 
  } else {                                                        
    SP = Target.ReadU32(VectorTableAddr);                         
    Target.SetReg("SP", SP);                                    
                                                                  
    PC = Target.ReadU32(VectorTableAddr + 4);                     
    Target.SetReg("PC", PC);                                    
  }
}
/*********************************************************************
*                                                                     
*       DebugStart                                                    
*                                                                     
* Function description                                                
*   Replaces the default debug session startup routine. Optional.     
*                                                                     
**********************************************************************
*/                                                                    
//void DebugStart (void) {                                            
//}                                                                   

/*********************************************************************
*                                                                     
*       TargetConnect                                                 
*                                                                     
* Function description                                                
*   Replaces the default target IF connection routine. Optional.      
*                                                                     
**********************************************************************
*/                                                                    
//void TargetConnect (void) {                                         
//}                                                                   

/*********************************************************************
*                                                                     
*       BeforeTargetConnect                                           
*                                                                     
* Function description                                                
*   Event handler routine. Optional.                                  
*                                                                     
**********************************************************************
*/                                                                    
void BeforeTargetConnect (void) {                                   
  Project.SetJLinkScript("FMUv6_Trace.pex");
}                                                                   

/*********************************************************************
*                                                                     
*       AfterTargetConnect                                            
*                                                                     
* Function description                                                
*   Event handler routine. Optional.                                  
*                                                                     
**********************************************************************
*/                                                                    
//void AfterTargetConnect (void) {                                 
//}                                                                   

/*********************************************************************
*                                                                     
*       TargetDownload                                                
*                                                                     
* Function description                                                
*   Replaces the default program download routine. Optional.          
*                                                                     
**********************************************************************
*/                                                                    
//void TargetDownload (void) {                                        
//}                                                                   

/*********************************************************************
*                                                                     
*       BeforeTargetDownload                                          
*                                                                     
* Function description                                                
*   Event handler routine. Optional.                                  
*                                                                     
**********************************************************************
*/                                                                    
//void BeforeTargetDownload (void) {                                  
//}                                                                   

/*********************************************************************
*                                                                     
*      AfterTargetDownload                                            
*                                                                     
* Function description                                                
*   Event handler routine. Optional.                                  
*    - Sets the PC register to program reset value.                   
*    - Sets the SP register to program reset value on Cortex-M.       
*                                                                     
**********************************************************************
*/                                                                    
void AfterTargetDownload (void) {
  unsigned int SP;                                                
  unsigned int PC;                                                
  unsigned int VectorTableAddr;                                   
                                                                  
  VectorTableAddr = Elf.GetBaseAddr();                            
  Util.Log("___");                                                               
  if (VectorTableAddr == 0xFFFFFFFF) {                            
    Util.Log("Project file error: failed to get program base"); 
  } else {                                                        
    SP = Target.ReadU32(VectorTableAddr);                         
    Target.SetReg("SP", SP);                                    
                                                                  
    PC = Target.ReadU32(VectorTableAddr + 4);                     
    Target.SetReg("PC", PC);                                    
  }
}

/*********************************************************************
*                                                                     
*       BeforeTargetDisconnect                                        
*                                                                     
* Function description                                                
*   Event handler routine. Optional.                                  
*                                                                     
**********************************************************************
*/                                                                    
//void BeforeTargetDisconnect (void) {                                
//}                                                                   

/*********************************************************************
*                                                                     
*       AfterTargetDisconnect                                         
*                                                                     
* Function description                                                
*   Event handler routine. Optional.                                  
*                                                                     
**********************************************************************
*/                                                                    
//void AfterTargetDisconnect (void) {                                 
//}                                                                   

/*********************************************************************
*                                                                     
*       AfterTargetHalt                                               
*                                                                     
* Function description                                                
*   Event handler routine. Optional.                                  
*                                                                     
**********************************************************************
*/                                                                    
//void AfterTargetHalt (void) {                                       
//}                                                                   

/*********************************************************************
*                                                                     
*       BeforeTargetResume                                            
*                                                                     
* Function description                                                
*   Event handler routine. Optional.                                  
*                                                                     
**********************************************************************
*/                                                                    
//void BeforeTargetResume (void) {                                    
//}                                                                   

/*********************************************************************
*                                                                     
*       OnSnapshotLoad                                                
*                                                                     
* Function description                                                
*   Called upon loading a snapshot. Optional.                         
*                                                                     
* Additional information                                              
*   This function is used to restore the target state in cases        
*   where values cannot simply be written to the target.              
*   Typical use: GPIO clock needs to be enabled, before               
*   GPIO is configured.                                               
*                                                                     
**********************************************************************
*/                                                                    
//void OnSnapshotLoad (void) {                                        
//}                                                                   

/*********************************************************************
*                                                                     
*       OnSnapshotSave                                                
*                                                                     
* Function description                                                
*   Called upon saving a snapshot. Optional.                          
*                                                                     
* Additional information                                              
*   This function is usually used to save values of the target        
*   state which can either not be trivially read,                     
*   or need to be restored in a specific way or order.                
*   Typically use: Memory Mapped Registers,                           
*   such as PLL and GPIO configuration.                               
*                                                                     
**********************************************************************
*/                                                                    
//void OnSnapshotSave (void) {                                        
//}                                                                   

/*********************************************************************
*                                                                     
*       OnError                                                       
*                                                                     
* Function description                                                
*   Called when an error ocurred. Optional.                           
*                                                                     
**********************************************************************
*/                                                                    
//void OnError (const char* sErrorMsg) {                              
//}                                                                   

