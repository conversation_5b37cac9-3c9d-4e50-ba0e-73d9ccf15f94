#! /bin/sh

if command -v gdb-multiarch &> /dev/null
then
	GDB_CMD=$(command -v gdb-multiarch)

elif command -v arm-none-eabi-gdb &> /dev/null
then
	GDB_CMD=$(command -v arm-none-eabi-gdb)

else
	echo "gdb arm-none-eabi or multi-arch not found"
	exit 1
fi

@JLinkGDBServerExe_PATH@ -device @DEBUG_DEVICE@ -rtos "@JLINK_RTOS_PATH@" -select usb -endian little -if SWD -speed auto -ir -LocalhostOnly 1 -strict -vd -singlerun &

cd @PX4_BINARY_DIR@ && ${GDB_CMD} -silent -nh \
	-iex "set auto-load safe-path @PX4_BINARY_DIR@" \
	-ix=@PX4_SOURCE_DIR@/platforms/nuttx/NuttX/nuttx/tools/nuttx-gdbinit \
	-ex "target remote localhost:2331" \
	-ex "monitor reset 0" \
	-ex "load" \
	-ex "monitor reset 0" \
	-ex "continue" \
	@PX4_CONFIG@.elf

# exit with status of last command
exit $?
