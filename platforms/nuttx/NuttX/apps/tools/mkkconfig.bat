@Echo off

REM apps/tools/mkkconfig.bat
REM
REM Licensed to the Apache Software Foundation (ASF) under one or more
REM contributor license agreements.  See the NOTICE file distributed with
REM this work for additional information regarding copyright ownership.  The
REM ASF licenses this file to you under the Apache License, Version 2.0 (the
REM "License"); you may not use this file except in compliance with the
REM License.  You may obtain a copy of the License at
REM
REM   http://www.apache.org/licenses/LICENSE-2.0
REM
REM Unless required by applicable law or agreed to in writing, software
REM distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
REM WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
REM License for the specific language governing permissions and limitations
REM under the License.
REM

REM Parse command line arguments

SET menu=""
SET kconfig=Kconfig

:ArgLoop
IF "%1"=="" GOTO :EndOfLoop
IF "%1"=="-m" GOTO :SetMenu
IF "%1"=="-o" GOTO :SetKconfig
IF "%1"=="-h" GOTO :ShowUsage

Echo ERROR: Unrecognized option %1
GOTO :ShowUsage

:SetDebug
SET debug=-d
GOTO :NextArg

:SetMenu
SHIFT
SET menu=%1
GOTO :NextArg

:SetKconfig
SHIFT
SET kconfig=%1

:NextArg
SHIFT
GOTO :ArgLoop

REM Check input Parameters

:EndOfLoop
IF EXIST %kconfig% (
  Del /f /q %kconfig%
REM   IF %ERRORLEVEL% GTR 0 (
REM     Echo ERROR: failed to remove %kconfig%
REM     GOTO :End
REM   )
)

REM Get the current directory
SET APPSDIR=%cd%
SET APPSDIR=%APPSDIR:\=/%

Echo # > %kconfig%
Echo # For a description of the syntax of this configuration file, >> %kconfig%
Echo # see the file kconfig-language.txt in the NuttX tools repository. >> %kconfig%
Echo # >> %kconfig%
Echo # This file is autogenerated, do not edit. >> %kconfig%
Echo # >> %kconfig%
Echo[ >> %kconfig%

IF %menu% NEQ "" (
  Echo menu %menu% >> %kconfig%
)

DIR /B /A:D >_tmp_.dat

FOR /F "tokens=*" %%s IN (_tmp_.dat) do (
  IF EXIST %%s\Kconfig (
    Echo source "%APPSDIR%/%%s/Kconfig" >> %kconfig%
  )
)
DEL _tmp_.dat

IF %menu% NEQ "" (
  Echo endmenu # %menu% >> %kconfig%
)

GOTO :End

REM Exit showing usage

:ShowUsage
Echo USAGE: %0  [-d] [-m ^<menu^>] [-o ^<kconfig-file^>]
Echo        %0  [-h]
Echo Where:
Echo  ^<-d^>:
Echo    Enables debug output
Echo  -m ^<menu^>:
Echo    Menu description
Echo  -o ^<kconfig-file^>:
Echo    Identifies the specific configuration for the selected ^<board-name^>.
Echo    This must correspond to a sub-directory under the board directory at
Echo    under nuttx/boards/^<arch^>/^<chip^>/^<board^>/.
Echo  ^<-h^>:
Echo    Prints this message and exits.

REM Exit

:End

