#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config FSUTILS_IPCFG
	bool "IP Configuration File Support"
	default n
	depends on NET_IPv4 || NET_IPv6
	---help---
		Enables support for an IP configuration file that holds IP
		address and/or DHCP configuration information for a
		specific network device

if FSUTILS_IPCFG

config IPCFG_WRITABLE
	bool "Writable IP Configuration"
	default y
	---help---
		Can be used to disable writing to the IP Configuration file.  This
		would be necessary if, for example, the IP Configuration file were
		provided on a read-only file system.

config IPCFG_BINARY
	bool "Binary IP Configuration File"
	default n
	---help---
		By default, the IP configuration file is an ASCII human readable
		file with <varialble>=<value> pairs.  A Binary interface may be used
		for more constrained media such as EEPROM.

config IPCFG_PATH
	string "IP Configuration File Directory"
	default "/etc/sysconfig/network-scripts"
	---help---
		Specifies the full path to the directory or mountpoint that holds
		the IP Configuration files.  Each individual configuration file
		within this directory will have names like ipcfg-eth0.

		If CONFIG_IPCFG_CHARDEV is select, this setting is interpreted
		differently.  In this case, CONFIG_IPCFG_PATH is the full path
		to the character driver.

config IPCFG_CHARDEV
	bool "Character Driver"
	default n
	depends on IPCFG_BINARY
	---help---
		In some use cases, where there is only a single network device and
		only a single network device, a character driver on, perhaps, EEPROM
		may be used.  In this case there is not file system and the
		CONFIG_IPCFG_PATH will refer to that character driver.

		NOTE that this configuration is only support with CONFIG_IPCFG_BINARY.

config IPCFG_OFFSET
	int "Data offset"
	default 0
	depends on IPCFG_CHARDEV
	---help---
		Seek to this offset before reading or writing the IP Configuration.
		This is only support for the character driver device.  This permits
		some formatting of, say, EEPROM, so that multiple, different
		configurations can be maintained at different offsets into the IP
		Configuration File.

endif # FSUTILS_IPCFG
