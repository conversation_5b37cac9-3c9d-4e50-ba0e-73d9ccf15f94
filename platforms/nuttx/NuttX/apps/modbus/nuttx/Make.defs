############################################################################
# apps/modbus/nuttx/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

CSRCS += portother.c

ifeq ($(CONFIG_MODBUS_SLAVE),y)
CSRCS += portevent.c portserial.c porttimer.c
endif

ifeq ($(CONFIG_MB_RTU_MASTER),y)
CSRCS += portother_m.c portserial_m.c porttimer_m.c portevent_m.c
endif

DEPPATH += --dep-path nuttx
VPATH += :nuttx
CFLAGS += ${shell $(INCDIR) "$(CC)" $(APPDIR)/modbus/nuttx}
