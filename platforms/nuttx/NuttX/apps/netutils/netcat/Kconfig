#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config NETUTILS_NETCAT
	tristate "NetCat tool"
	default n
	depends on NET_TCP
	---help---
		Enable the NetCat TCP/IP swiss army tool

if NETUTILS_NETCAT

config NETUTILS_NETCAT_PROGNAME
	string "Program name"
	default "netcat"
	---help---
		This is the name of the program that will be use when the NSH ELF
		program is installed.

config NETUTILS_NETCAT_PRIORITY
	int "netcat task priority"
	default 100

config NETUTILS_NETCAT_STACKSIZE
	int "netcat stack size"
	default DEFAULT_TASK_STACKSIZE

config NETUTILS_NETCAT_SENDFILE
	bool "Use sendfile() in netcat when possible"
	default y
	depends on NET_SENDFILE
	---help---
		This option enables using sendfile() in netcat client mode
		if a normal file (not stdin) is sent. If the option is enabled
		but stdin is sent rather than a normal file, netcat falls back
		to the combination of read() and write().
		Using sendfile() provides a higher performance compared to
		the combination of read() and write().

config NETUTILS_NETCAT_BUFSIZE
	int "netcat I/O buffer size"
	default 256
	---help---
		The I/O buffer is used in the netcat server mode.
		The I/O buffer is also used in the netcat client mode only if
		sendfile() is not applicable.

endif
