# Network Utilities / `netcat` tool

netcat TCP/IP Swiss army knife

It was re-implemented from scratch for NuttX


## DEMO ##

[![weboftwins-osvehicle-2020-rzr](
https://files.mastodon.social/media_attachments/files/105/163/916/892/863/178/original/05468e28b4463f95.png
)](
https://mastodon.social/@rzr/105225153152922220#weboftwins-osvehicle-2020-rzr
"weboftwins-osvehicle-2020-rzr")

  * https://purl.org/rzr/weboftwins

## USAGE ##

Usage is straightforward:

    nsh> help ; netcat
    Usage: netcat [-l] [destination] [port] [file]

    nsh> renew eth0 ; ifconfig

    eth0    Link encap:Ethernet HWaddr 52:13:FF:FF:FF:FF at UP
            inet addr:************ DRaddr:************* Mask:*************

In the following examples, following configuration is used:

- target (nuttx) is ************
- host (linux) is ************

### Server ###

As a server on NuttX and Linux's netcat as client

    nsh> netcat -l

    sh> cat /proc/version | netcat ************ 31337
    Linux ...

Default port is 31337 but it can changed.

    nsh> renew eth0 ; ifconfig ; netcat -l
    log: net: listening on :31337
    Linux ...

### Client ###

Start Server on GNU/Linux:

    sh> ip addr show && netcat -l 31337

Client side on nuttx, we create

    nsh> help ; renew eth0 ; ifconfig
    nsh> netcat ************ 31337 /proc/version

### Using pipes ###

    mkfifo /dev/fifo
    netcat ************ 31337 /proc/fifo
    help > /dev/fifo

    fxos8700cq > /dev/fifo &
    fxos8700cq [7:100]
    netcat ************ 31337  /dev/fifo

### Resources ###

  * <https://en.wikipedia.org/wiki/Netcat>
  * <https://purl.org/rzr/weboftwins>
  * <https://github.com/rzr/aframe-smart-home/issues/3>