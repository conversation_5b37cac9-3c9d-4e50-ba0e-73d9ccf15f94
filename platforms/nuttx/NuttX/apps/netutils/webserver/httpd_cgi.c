/****************************************************************************
 * apps/netutils/webserver/httpd_cgi.c
 * Web server script interface
 * Author: <PERSON> <<EMAIL>>
 *
 * Copyright (c) 2001-2006, <PERSON>.
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 * 3. The name of the author may not be used to endorse or promote
 *    products derived from this software without specific prior
 *    written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS
 * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
 * GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <string.h>

#include "netutils/httpd.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/

/****************************************************************************
 * Private Data
 ****************************************************************************/

struct httpd_cgi_call *cgi_calls = NULL;

/****************************************************************************
 * Public Functions
 ****************************************************************************/

/****************************************************************************
 * Name: httpd_cgi_register
 ****************************************************************************/

void httpd_cgi_register(struct httpd_cgi_call *cgi_call)
{
  if (cgi_calls == NULL)
    {
      cgi_calls = cgi_call;
    }
  else
    {
      cgi_call->next = cgi_calls;
      cgi_calls = cgi_call;
    }
}

/****************************************************************************
 * Name: httpd_cgi
 ****************************************************************************/

httpd_cgifunction httpd_cgi(char *name)
{
  struct httpd_cgi_call *cgi_call = cgi_calls;
  while (cgi_call != NULL)
    {
      if (strncmp(cgi_call->name, name, strlen(cgi_call->name)) == 0)
        {
          return cgi_call->function;
        }

      cgi_call = cgi_call->next;
    }

  return NULL;
}
