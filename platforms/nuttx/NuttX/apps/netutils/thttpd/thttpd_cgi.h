/****************************************************************************
 * apps/netutils/thttpd/thttpd_cgi.h
 * CGI support
 *
 *   Copyright (C) 2009 <PERSON>. All rights reserved.
 *   Author: <PERSON> <<EMAIL>>
 *
 * Derived from the file libhttpd.c in the original THTTPD package:
 *
 *   Copyright (C) 1995,1998,1999,2000,2001 by <PERSON><PERSON> <<EMAIL>>.
 *   All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR AND CONTRIBUTORS ``AS IS'' AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR OR CONTRIBUTORS BE LIABLE
 * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
 * OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 *
 ****************************************************************************/

#ifndef __APPS_NETUTILS_THTTPD_THTTPD_CGI_H
#define __APPS_NETUTILS_THTTPD_THTTPD_CGI_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

#include "config.h"
#include "libhttpd.h"

#if defined(CONFIG_THTTPD) && defined(CONFIG_THTTPD_CGI_PATTERN)

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/

extern int cgi(httpd_conn *hc);
#if CONFIG_THTTPD_CGI_TIMELIMIT > 0
struct timeval;
static void cgi_kill(ClientData client_data, struct timeval *now);
#endif

#endif /* CONFIG_THTTPD && CONFIG_THTTPD_CGI_PATTERN */
#endif /* __HTTPD_CGI_H */
