#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config NETUTILS_WEBCLIENT
	bool "uIP web client"
	default n
	depends on NET_TCP
	select LIBC_NETDB
	select NET_SOCKOPTS
	select NETUTILS_NETLIB_GENERICURLPARSER
	---help---
		Enable support for the uIP web client.

if NETUTILS_WEBCLIENT

config NSH_WGET_USERAGENT
	string "wget User Agent"
	default "NuttX/6.xx.x (; http://www.nuttx.org/)"

config WEBCLIENT_TIMEOUT
	int "Request and receive timeouts"
	default 10

endif
