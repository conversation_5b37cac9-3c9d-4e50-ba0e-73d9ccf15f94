#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config NETUTILS_DHCPC
	bool "DHCP client"
	default n
	depends on NET_UDP && NET_BROADCAST && NET_IPv4
	select NET_UDP_BINDTODEVICE if !NET_UDP_NO_STACK
	---help---
		Enable support for the DHCP client.

if NETUTILS_DHCPC

config NETUTILS_DHCPC_HOST_NAME
	string "DHCP client host name"
	default "nuttx"

config NETUTILS_DHCPC_RECV_TIMEOUT_MS
	int "Number of receive timeout in millisecond"
	default 3000
	---help---
		This is the timeout value when dhcp client receives response

config NETUTILS_DHCPC_RETRIES
	int "Number of retries for dhcp client request"
	default 3
	---help---
		This setting determines how many times resolver retries request
		until failing.

config NETUTILS_DHCPC_BOOTP_FLAGS
	hex "Flags of Bootstrap"
	default 0x8000
	---help---
		This setting to set the BOOTP broadcast flags.
		Reference RFC1542: Clarifications and Extensions for the
		Bootstrap Protocol.

endif
