#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config NETUTILS_NETLIB
	bool "Network support library"
	default n
	depends on NET
	select IEEE802154_LIBMAC if WIRELESS_IEEE802154
	---help---
		Enable support for the network support library.

if NETUTILS_NETLIB

config NETUTILS_NETLIB_GENERICURLPARSER
	bool "Build the generic URL parser"
	default n
	---help---
		If this option is selected, a generic URL parser
		is included in the build. It is more flexible than
		the basic netlib_parsehttpurl routine.
endif
