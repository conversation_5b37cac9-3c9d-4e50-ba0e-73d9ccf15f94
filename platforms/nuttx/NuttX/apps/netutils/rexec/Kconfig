#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config NETUTILS_REXEC
	tristate "Remote execution client"
	default n
	---help---
		Enable support for the remote execution client.

if NETUTILS_REXEC

config NETUTILS_REXEC_PRIORITY
	int "Remote execution client task priority"
	default 100

config NETUTILS_REXEC_STACKSIZE
	int "Remote execution client stack size"
	default DEFAULT_TASK_STACKSIZE

endif
