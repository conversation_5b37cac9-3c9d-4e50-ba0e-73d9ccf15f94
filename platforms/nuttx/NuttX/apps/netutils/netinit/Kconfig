#
#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

menuconfig NETUTILS_NETINIT
	bool "Network initialization"
	default n
	depends on NET
	select NETUTILS_NETLIB
	---help---
		This option enables/disables all support for common network initialization.

if NETUTILS_NETINIT

config NETINIT_NETLOCAL
	bool "Local network initialization"
	default n
	---help---
		If this option is selected, then this logic will only initialize the local
		attributes of the network:  The MAC address if needed and any IP
		addresses as needed.  More importantly, it will not do the following:

		- It will not bring the network up.  That can be done later with the
		  an ifup() call.
		- It will not associate any wireless devices to an access point.
		- It will not attempt to obtain an IP address if DHCPC is selected.
		  This may be done later from the apps/system/dhcpc 'renew' command.
		- It will not start the NTPC daemon.  This may be done later from
		  the with the apps/system/ntpc 'ntpcstart' command.

		This option permits you to divide the network configuration into two
		parts:  The local configuration of the network device and the dynamic
		configuration of the device in the network.  This may be important in
		an environment when, for example, you need to manually scan for
		available access points and associate the wireless driver with an
		access point.

config NETINIT_THREAD
	bool "Network initialization thread"
	default n
	depends on !DISABLE_PTHREAD && !NETINIT_NETLOCAL
	---help---
		NuttX is brought up through a series of sequential initialization
		steps.  This includes networking. If the network is available on
		reset, then there is really no issue.  Negotiating the link will
		take only a second or so and the delay to application startup is
		normally acceptable.

		But if there is no network connected, then the start-up delay can
		be very long depending upon things like the particular PHY, driver
		timeout delay times and number of retries.  A failed negotiation
		can potentially take a very long time, perhaps as much as a
		minute... Long enough that you might think that the board would
		never come up!

		One solution is enabled by this option.  If NETINIT_THREAD
		is selected, the network bring-up will occur in parallel with
		the application on a separate thread.  In this case, the application
		will start immediately with the network becoming available some time
		later (if at all).  This thread will terminate once it successfully
		initializes the network.

		NOTES:  If no network is connected, the network bring-up will fail
		and the network initialization thread will simply exit.  There are
		no retries and no mechanism to know if the network initialization
		was successful.  Furthermore, there is currently no support for
		detecting loss of network connection.  Lots of things to do!

if NETINIT_THREAD

config NETINIT_MONITOR
	bool "Monitor link state"
	default n
	depends on (ARCH_PHY_INTERRUPT || ARCH_PHY_POLLED) && NETDEV_PHY_IOCTL && NET_UDP
	---help---
		By default the net initialization thread will bring-up the network
		then exit, freeing all of the resources that it required.  This is a
		good behavior for systems with limited memory.

		If this option is selected, however, then the network initialization
		thread will persist forever; it will monitor the network status.  In
		the event that the network goes down (for example, if a cable is
		removed), then the thread will monitor the link status and
		attempt to bring the network back up.  In this case the resources
		required for network initialization are never released.

if NETINIT_MONITOR
config NETINIT_ESTABLISH_POLL_RATE
	int "The poll rate in seconds, to check for link establishment."
	default 2
	depends on !ARCH_PHY_INTERRUPT && ARCH_PHY_POLLED
	---help---
		The network monitor will check the PHY link state every
		NETINIT_ESTABLISH_POLL_RATE seconds for link establishment.

config NETINIT_LOSS_POLL_RATE
	int "The poll rate in seconds, to check for link loss."
	default 3
	depends on !ARCH_PHY_INTERRUPT && ARCH_PHY_POLLED
	---help---
		The network monitor will check the PHY link state every
		NETINIT_LOSS_POLL_RATE seconds for link loss.

config NETINIT_DHCP_FALLBACK
	int "The number of failed DHCP attempts to fall back to using static settings"
	default 5
	depends on NETINIT_DHCPC && !ARCH_PHY_INTERRUPT && ARCH_PHY_POLLED && FSUTILS_IPCFG
	---help---
		When the network monitor is used with FSUTILS_IPCFG if, the
		the BOOTPROTO_FALLBACK is the active protocol. NETINIT_DHCP_FALLBACK will
		be used as the number of times, DHCP will be be attempted before
		falling back to using the static settings. The time will be roughly
		NETINIT_LOSS_POLL_RATE * NETINIT_DHCP_FALLBACK seconds.
		Setting this value to 0, will disable this feature.

config NETINIT_SIGNO
	int "Notification signal number"
	default 18
	depends on ARCH_PHY_INTERRUPT && !ARCH_PHY_POLLED
	---help---
		The network monitor logic will receive signals when there is any
		change in the link status.  This setting may be used to customize
		that signal number in order to avoid conflicts.

config NETINIT_RETRYMSEC
	int "Network bring-up retry period (msec)"
	default 2000
	---help---
		When the network is down, the initialization thread will periodically
		try to bring the network up.  This can be a time consuming operation
		so is done only periodically with that period specified by this
		selection in milliseconds.

endif # NETINIT_MONITOR

config NETINIT_THREAD_STACKSIZE
	int "Network initialization thread stack size"
	default DEFAULT_TASK_STACKSIZE

config NETINIT_THREAD_PRIORITY
	int "Network initialization thread priority"
	default 80
	---help---
		This should be set to a priority lower than most tasks.  The network
		PHY polling is CPU intensive and can interfere with the usability of
		of threads competing for CPU bandwidth.

config NETINIT_RETRY_MOUNTPATH
int "Network initialization retry mount path count"
default 0
---help---
	This should be set if the filesystem get mounted after netinit got started.
	The netinit thread will check for the mount path before continuing.

endif # NETINIT_THREAD

config NETINIT_DEBUG
	bool "Network init debug"
	default n
	depends on DEBUG_FEATURES
	---help---
		Normally debug output is controlled by DEBUG_NET.  However, that
		will generate a LOT of debug output, especially if CONFIG_DEBUG_INFO is
		also selected.  This option is intended to force verbose debug
		output from the network initialization logic even if CONFIG_DEBUG_NET
		or CONFIG_DEBUG_INFO are not selected.  This allows for focused, unit-
		level debug of the network initialization logic.

# No IP address if 6LOWPAN selected; but Ethernet has precedence.

menu "IP Address Configuration"
	depends on NET_ETHERNET || !NET_6LOWPAN

config NETINIT_DHCPC
	bool "Use DHCP to get IP address"
	default n
	depends on NETUTILS_DHCPC
	---help---
		Obtain the IP address via DHCP.

		Per RFC2131 (p. 9), the DHCP client must be prepared to receive DHCP
		messages of up to 576 bytes (excluding Ethernet, IP, or UDP headers and FCS).

if NET_IPv4

comment "IPv4 Addresses"

config NETINIT_IPADDR
	hex "Target IPv4 address"
	default 0x0a000002
	depends on !NETINIT_DHCPC
	---help---
		If NETINIT_DHCPC is NOT set, then the static IP address must be provided.
		This is a 32-bit integer value in host order.  So, as an example,
		0x0a000002 would be ********.

config NETINIT_DRIPADDR
	hex "Router IPv4 address"
	default 0x0a000001
	---help---
		Default router IP address (aka, Gateway).  This is a 32-bit integer
		value in host order.  So, as an example, 0x0a000001 would be ********.

config NETINIT_NETMASK
	hex "IPv4 Network mask"
	default 0xffffff00
	---help---
		Network mask.  This is a 32-bit integer value in host order.  So, as
		an example, 0xffffff00 would be *************.

endif # NET_IPv4

if NET_IPv6 && !NET_ICMPv6_AUTOCONF

comment "Target IPv6 address"

config NETINIT_IPv6ADDR_1
	hex "[0]"
	default 0xfc00
	range 0x0 0xffff
	---help---
		If NET_ICMPv6_AUTOCONF is NOT set, then the static IP address must be
		provided. This is a 16-bit integer value in host order.  Each of the
		eight values forming the full IP address must be specified
		individually.  This is the first of the 8-values.  The default for
		all eight values is fc00::2.

config NETINIT_IPv6ADDR_2
	hex "[1]"
	default 0x0000
	range 0x0 0xffff
	---help---
		If NET_ICMPv6_AUTOCONF is NOT set, then the static IP address must be
		provided. This is a 16-bit integer value in host order.  Each of the
		eight values forming the full IP address must be specified
		individually.  This is the second of the 8-values.  The default for
		all eight values is fc00::2.

config NETINIT_IPv6ADDR_3
	hex "[2]"
	default 0x0000
	range 0x0 0xffff
	---help---
		If NET_ICMPv6_AUTOCONF is NOT set, then the static IP address must be
		provided. This is a 16-bit integer value in host order.  Each of the
		eight values forming the full IP address must be specified
		individually.  This is the third of the 8-values.  The default for
		all eight values is fc00::2.

config NETINIT_IPv6ADDR_4
	hex "[3]"
	default 0x0000
	range 0x0 0xffff
	---help---
		If NET_ICMPv6_AUTOCONF is NOT set, then the static IP address must be
		provided. This is a 16-bit integer value in host order.  Each of the
		eight values forming the full IP address must be specified
		individually.  This is the fourth of the 8-values.  The default for
		all eight values is fc00::2.

config NETINIT_IPv6ADDR_5
	hex "[4]"
	default 0x0000
	range 0x0 0xffff
	---help---
		If NET_ICMPv6_AUTOCONF is NOT set, then the static IP address must be
		provided. This is a 16-bit integer value in host order.  Each of the
		eight values forming the full IP address must be specified
		individually.  This is the fifth of the 8-values.  The default for
		all eight values is fc00::2.

config NETINIT_IPv6ADDR_6
	hex "[5]"
	default 0x0000
	range 0x0 0xffff
	---help---
		If NET_ICMPv6_AUTOCONF is NOT set, then the static IP address must be
		provided. This is a 16-bit integer value in host order.  Each of the
		eight values forming the full IP address must be specified
		individually.  This is the sixth of the 8-values.  The default for
		all eight values is fc00::2.

config NETINIT_IPv6ADDR_7
	hex "[6]"
	default 0x0000
	range 0x0 0xffff
	---help---
		If NET_ICMPv6_AUTOCONF is NOT set, then the static IP address must be
		provided. This is a 16-bit integer value in host order.  Each of the
		eight values forming the full IP address must be specified
		individually.  This is the seventh of the 8-values.  The default for
		all eight values is fc00::2.

config NETINIT_IPv6ADDR_8
	hex "[7]"
	default 0x0002
	range 0x0 0xffff
	---help---
		If NET_ICMPv6_AUTOCONF is NOT set, then the static IP address must be
		provided. This is a 16-bit integer value in host order.  Each of the
		eight values forming the full IP address must be specified
		individually.  This is the last of the 8-values.  The default for
		all eight values is fc00::2.

comment "Router IPv6 address"

config NETINIT_DRIPv6ADDR_1
	hex "[0]"
	default 0xfc00
	range 0x0 0xffff
	---help---
		Default router IP address (aka, Gateway).  This is a 16-bit integer
		value in host order.  Each of the eight values forming the full IP
		address must be specified individually.  This is the first of the
		8-values.  The default for all eight values is fc00::1.

config NETINIT_DRIPv6ADDR_2
	hex "[1]"
	default 0x0000
	range 0x0 0xffff
	---help---
		Default router IP address (aka, Gateway).  This is a 16-bit integer
		value in host order.  Each of the eight values forming the full IP
		address must be specified individually.  This is the second of the
		8-values.  The default for all eight values is fc00::1.

config NETINIT_DRIPv6ADDR_3
	hex "[2]"
	default 0x0000
	range 0x0 0xffff
	---help---
		Default router IP address (aka, Gateway).  This is a 16-bit integer
		value in host order.  Each of the eight values forming the full IP
		address must be specified individually.  This is the third of the
		8-values.  The default for all eight values is fc00::1.

config NETINIT_DRIPv6ADDR_4
	hex "[3]"
	default 0x0000
	range 0x0 0xffff
	---help---
		Default router IP address (aka, Gateway).  This is a 16-bit integer
		value in host order.  Each of the eight values forming the full IP
		address must be specified individually.  This is the fourth of the
		8-values.  The default for all eight values is fc00::1.

config NETINIT_DRIPv6ADDR_5
	hex "[4]"
	default 0x0000
	range 0x0 0xffff
	---help---
		Default router IP address (aka, Gateway).  This is a 16-bit integer
		value in host order.  Each of the eight values forming the full IP
		address must be specified individually.  This is the fifth of the
		8-values.  The default for all eight values is fc00::1.

config NETINIT_DRIPv6ADDR_6
	hex "[5]"
	default 0x0000
	range 0x0 0xffff
	---help---
		Default router IP address (aka, Gateway).  This is a 16-bit integer
		value in host order.  Each of the eight values forming the full IP
		address must be specified individually.  This is the sixth of the
		8-values.  The default for all eight values is fc00::1.

config NETINIT_DRIPv6ADDR_7
	hex "[6]"
	default 0x0000
	range 0x0 0xffff
	---help---
		Default router IP address (aka, Gateway).  This is a 16-bit integer
		value in host order.  Each of the eight values forming the full IP
		address must be specified individually.  This is the seventh of the
		8-values.  The default for all eight values is fc00::1.

config NETINIT_DRIPv6ADDR_8
	hex "[7]"
	default 0x0001
	range 0x0 0xffff
	---help---
		Default router IP address (aka, Gateway).  This is a 16-bit integer
		value in host order.  Each of the eight values forming the full IP
		address must be specified individually.  This is the last of the
		8-values.  The default for all eight values is fc00::1.

comment "IPv6 Network mask"

config NETINIT_IPv6NETMASK_1
	hex "[0]"
	default 0xffff
	range 0x0 0xffff
	---help---
		Network mask.  This is a 16-bit integer value in host order.  Each
		of the eight values forming the full IP address must be specified
		individually.  This is the first of the 8-values.  The default for
		all eight values is fe00::0.

config NETINIT_IPv6NETMASK_2
	hex "[1]"
	default 0xffff
	range 0x0 0xffff
	---help---
		Network mask.  This is a 16-bit integer value in host order.  Each
		of the eight values forming the full IP address must be specified
		individually.  This is the second of the 8-values.  The default for
		all eight values is fe00::0.

config NETINIT_IPv6NETMASK_3
	hex "[2]"
	default 0xffff
	range 0x0 0xffff
	---help---
		Network mask.  This is a 16-bit integer value in host order.  Each
		of the eight values forming the full IP address must be specified
		individually.  This is the third of the 8-values.  The default for
		all eight values is fe00::0.

config NETINIT_IPv6NETMASK_4
	hex "[3]"
	default 0xffff
	range 0x0 0xffff
	---help---
		Network mask.  This is a 16-bit integer value in host order.  Each
		of the eight values forming the full IP address must be specified
		individually.  This is the fourth of the 8-values.  The default for
		all eight values is fe00::0.

config NETINIT_IPv6NETMASK_5
	hex "[4]"
	default 0xffff
	range 0x0 0xffff
	---help---
		Network mask.  This is a 16-bit integer value in host order.  Each
		of the eight values forming the full IP address must be specified
		individually.  This is the fifth of the 8-values.  The default for
		all eight values is fe00::0.

config NETINIT_IPv6NETMASK_6
	hex "[5]"
	default 0xffff
	range 0x0 0xffff
	---help---
		Network mask.  This is a 16-bit integer value in host order.  Each
		of the eight values forming the full IP address must be specified
		individually.  This is the sixth of the 8-values.  The default for
		all eight values is fe00::0.

config NETINIT_IPv6NETMASK_7
	hex "[6]"
	default 0xffff
	range 0x0 0xffff
	---help---
		Network mask.  This is a 16-bit integer value in host order.  Each
		of the eight values forming the full IP address must be specified
		individually.  This is the seventh of the 8-values.  The default for
		all eight values is fe00::0.

config NETINIT_IPv6NETMASK_8
	hex "[7]"
	default 0x0000
	range 0x0 0xffff
	---help---
		Network mask.  This is a 16-bit integer value in host order.  Each
		of the eight values forming the full IP address must be specified
		individually.  This is the eighth of the 8-values.  The default for
		all eight values is fe00::0.

endif # NET_IPv6 && !NET_ICMPv6_AUTOCONF
endmenu # IP Address Configuration

config NETINIT_DNS
	bool "Use DNS"
	default n
	depends on NETDB_DNSCLIENT
	---help---
		Configure to use a DNS.

config NETINIT_DNSIPADDR
	hex "DNS IP address"
	default 0xa0000001
	depends on NETINIT_DNS
	---help---
		Configure the DNS address.  This is a 32-bit integer value in host
		order.  So, as an example, 0xa0000001 would be ********.

config NETINIT_NOMAC
	bool "Hardware has no MAC address"
	default n
	---help---
		Set if your Ethernet hardware has no built-in MAC address.
		If set, a bogus MAC will be assigned.

if NETINIT_NOMAC

choice
	prompt "MAC address selection"
	default NETINIT_SWMAC
	---help---
		If the hardware as no MAC address, then this logic must assign an
		address to the hardware before it brings the network up.  This
		choice allows you select the source of that MAC address.

config NETINIT_SWMAC
	bool "Fixed address"
	---help---
		With this choice, you can assign a fixed MAC address determined by
		a NuttX configuration option.

endchoice # MAC address selection

config NETINIT_MACADDR_1
	hex "Fixed MAC address (bytes 0-3)"
	default 0xdeadbeef
	depends on NETINIT_SWMAC && (NET_ETHERNET || NET_6LOWPAN)
	---help---
		If the hardware has no built-in MAC address and if the NETINIT_SWMAC
		option is selected, then the fixed, software-assigned MAC address
		MAC address must provided with this selection.  This setting provides
		the least significant 4 bytes of the 6-8 byte MAC address.

config NETINIT_MACADDR_2
	hex "Fixed MAC address (bytes 4-5/7)"
	default 0x00e0 if NET_ETHERNET
	default 0x00fade00 if !NET_ETHERNET && NET_6LOWPAN
	depends on NETINIT_SWMAC && (NET_ETHERNET || NET_6LOWPAN)
	---help---
		If the hardware has no built-in MAC address and if the NETINIT_SWMAC
		option is selected, then the fixed, software-assigned MAC address
		MAC address must provided with this selection.  This setting provides
		the least significant 2-4 bytes of the 6-8 byte MAC address.

endif # NETINIT_NOMAC

menu "WAPI Configuration"
	depends on NET && WIRELESS_WAPI

config NETINIT_WAPI_STAMODE
	int "Wireless mode of operation"
	default 2
	range 0 8
	---help---
		Mode of operation.  See the IW_MODE_* definitions in
		include/nuttx/wireless/wireless.  The default value corresponds to
		IW_MODE_INFRA

config NETINIT_WAPI_AUTHWPA
	hex "IW_AUTH_WPA_VERSION value"
	default 0x00000004
	range 0x00000001 0x00000004
	---help---
		IW_AUTH_WPA_VERSION values.  See the IW_AUTH_WPA_VERSION_* definitions
		in include/nuttx/wireless/wireless.  The default value corresponds to
		IW_AUTH_WPA_VERSION_WPA2.  NOTE that this is a bit-encoded field.  The
		only valid values are 0x00000001, 0x00000002, and 0x00000004

config NETINIT_WAPI_CIPHERMODE
	hex "IW_AUTH_PAIRWISE_CIPHER and IW_AUTH_GROUP_CIPHER values"
	default 0x00000008
	range 0x00000001 0x00000010
	---help---
		IW_AUTH_PAIRWISE_CIPHER and IW_AUTH_GROUP_CIPHER values. See the
		IW_AUTH_CIPHER_* definitions in include/nuttx/wireless/wireless.
		The default value corresponds to IW_AUTH_CIPHER_CCMP.  NOTE that
		this is a bit-encoded field.  The only valid values are 0x00000001,
		0x00000002,0x00000004, ... 0x00000010

config NETINIT_WAPI_ALG
	int "Algorithm"
	default 3
	range 0 13
	---help---
		Algorithm.  See enum wpa_alg_e in apps/include/wireless/wapi.h.  The
		default corresponds to WPA_ALG_CCMP.

config NETINIT_WAPI_SSID
	string "SSID"
	default ""

config NETINIT_WAPI_PASSPHRASE
	string "Passprhase"
	default ""

endmenu # WAPI Configuration
endif # NETUTILS_NETINIT
