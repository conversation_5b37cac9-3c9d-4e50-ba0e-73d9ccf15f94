#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config INDUSTRY_FOC
	bool "FOC Library"
	default n
	select LIBDSP
	---help---
		Enable or disable the FOC Library

if INDUSTRY_FOC

config INDUSTRY_FOC_CORDIC
	bool "Enable CORDIC support"
	default n
	---help---
		Enable support for CORDIC hardware acceleration

if INDUSTRY_FOC_CORDIC

config INDUSTRY_FOC_CORDIC_DEVPATH
	string "CORDIC device path"
	default "/dev/cordic0"

config INDUSTRY_FOC_CORDIC_ANGLE
	bool "Enable CORDIC for phase angle"
	default n

config INDUSTRY_FOC_CORDIC_DQSAT
	bool "Enable CORDIC for dq saturation"
	default n

endif # INDUSTRY_FOC_CORDIC

config INDUSTRY_FOC_FIXED16
	bool "Enable support for fixed16"
	default n
	---help---
		Enable support for FOC fixed16 calculations

config INDUSTRY_FOC_FLOAT
	bool "Enable support for float"
	default n
	---help---
		Enable support for FOC float calculations

config INDUSTRY_FOC_HANDLER_PRINT
	bool "FOC handler state printer"
	default n
	---help---
		Enable support for FOC handler state printer

config INDUSTRY_FOC_ANGLE_OPENLOOP
	bool "FOC angle open-loop handler"
	default y
	---help---
		Enable support for open-loop angle handler

config INDUSTRY_FOC_ANGLE_OSMO
	bool "Enable slidmode angle observer"
	default n
	---help---
		Enable support for SMO observer handler

config INDUSTRY_FOC_ANGLE_ONFO
	bool "Enable fluxlink angle observer"
	default n
	---help---
		Enable support for NFO observer handler

config INDUSTRY_FOC_ANGLE_QENCO
	bool "FOC angle quadrature encoder handler"
	default n
	depends on SENSORS_QENCODER
	---help---
		Enable support for quadrature encoder angle handler

config INDUSTRY_FOC_ANGLE_HALL
	bool "FOC angle 3-phase hall handler"
	default n
	depends on SENSORS_HALL3PHASE
	---help---
		Enable support for 3-phase hall sensor angle handler

if INDUSTRY_FOC_ANGLE_HALL

choice
	prompt "FOC Hall sensor type"
	default INDUSTRY_FOC_ANGLE_HALL_120DEG

config INDUSTRY_FOC_ANGLE_HALL_120DEG
	bool "Hall sensor 120 deg"

config INDUSTRY_FOC_ANGLE_HALL_60DEG
	bool "Hall sensor 60 deg (not tested)"

endchoice # FOC Hall sensor type

config INDUSTRY_FOC_ANGLE_HALL_EST
	bool "FOC Hall angle estimation"
	default n

endif # INDUSTRY_FOC_ANGLE_HALL

config INDUSTRY_FOC_CONTROL_PI
	bool "FOC PI controller"
	default y
	---help---
		Enable support for classic FOC PI controller

config INDUSTRY_FOC_MODULATION_SVM3
	bool "FOC SVM3 modulation"
	default y
	---help---
		Enable support for FOC 3-phase space vector modulation

config INDUSTRY_FOC_MODEL_PMSM
	bool "FOC PMSM model support"
	select INDUSTRY_FOC_HAVE_MODEL
	default n
	---help---
		Enable support for PMSM model

config INDUSTRY_FOC_HAVE_MODEL
	bool
	default n

config INDUSTRY_FOC_ALIGN
	bool "FOC sensor alignment routine"
	default n
	---help---
		Enable support for angle sensor alignment routine (zero offset + direction)

if INDUSTRY_FOC_ALIGN

config INDUSTRY_FOC_ALIGN_INDEX
	bool "FOC alignment index search support"
	default n
	---help---
		Enable support for sensor alignment index search

endif # INDUSTRY_FOC_ALIGN

config INDUSTRY_FOC_IDENT
	bool "FOC motor identification routine"
	default n
	---help---
		Enable support for motor identification routine (phase resistance and phase inductance)

if INDUSTRY_FOC_IDENT

config INDUSTRY_FOC_IDENT_FLUX
	bool "FOC flux linkage identification support"
	default n
	---help---
		Enable support for flux linkage identification

endif # INDUSTRY_FOC_IDENT

config INDUSTRY_FOC_VELOCITY_ODIV
	bool "FOC velocity DIV observer"
	default n
	---help---
		Enable support for the velocity DIV observer

config INDUSTRY_FOC_VELOCITY_OPLL
	bool "FOC velocity PLL observer"
	default n
	---help---
		Enable support for the velocity PLL observer

endif
