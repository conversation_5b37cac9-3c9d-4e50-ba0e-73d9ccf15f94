/****************************************************************************
 * apps/include/graphics/nxwidgets/cnxtimer.hxx
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/****************************************************************************
 *
 * Portions of this package derive from Woopsi (http://woopsi.org/) and
 * portions are original efforts.  It is difficult to determine at this
 * point what parts are original efforts and which parts derive from Woopsi.
 * However, in any event, the work of  Antony Dzeryn will be acknowledged
 * in most NxWidget files.  Thanks Antony!
 *
 *   Copyright (c) 2007-2011, Antony Dzeryn
 *   All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * * Redistributions of source code must retain the above copyright
 *   notice, this list of conditions and the following disclaimer.
 * * Redistributions in binary form must reproduce the above copyright
 *   notice, this list of conditions and the following disclaimer in the
 *   documentation and/or other materials provided with the distribution.
 * * Neither the names "Woopsi", "Simian Zombie" nor the
 *   names of its contributors may be used to endorse or promote products
 *   derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY Antony Dzeryn ``AS IS'' AND ANY
 * EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL Antony Dzeryn BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 ****************************************************************************/

#ifndef __APPS_INCLUDE_GRAPHICS_NXWIDGETS_CNXTIMER_HXX
#define __APPS_INCLUDE_GRAPHICS_NXWIDGETS_CNXTIMER_HXX

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

#include <stdint.h>
#include <stdbool.h>
#include <ctime>

#include <nuttx/wqueue.h>

#include "graphics/nxwidgets/cnxwidget.hxx"

/****************************************************************************
 * Pre-Processor Definitions
 ****************************************************************************/

/****************************************************************************
 * Implementation Classes
 ****************************************************************************/

#if defined(__cplusplus)

namespace NXWidgets
{
  class CWidgetcontrol;

  /**
   * Timer widget.  It can drive time-based events, animations, etc.
   *
   * Using the timer is simple:
   *  - Create an instance of the CNxTimer and add it as a child to a widget.
   *  - Call the instance's "start()" method.
   *  - Catch the timer's action event and call any code that should run.
   */

  class CNxTimer : public CNxWidget
  {
  protected:
    struct work_s     m_work;       /**< Work queue entry */
    uint32_t          m_timeout;    /**< The timeout value in milliseconds */
    bool              m_isRunning;  /**< Indicates whether or not the timer is running */
    bool              m_isRepeater; /**< Indicates whether or not the timer repeats */

    /**
     * Static function called from work queue when the timeout expires.
     *
     * @param arg Pointer to the CNxTimer instance.
     */

    static void workQueueCallback(FAR void *arg);

    /**
     * Copy constructor is protected to prevent usage.
     */

    inline CNxTimer(const CNxTimer &timer) : CNxWidget(timer) { }

  public:

    /**
     * Constructor.
     *
     * @param pWidgetControl The controlling widget for the display.
     * @param timeout Time, in milliseconds, before the timer fires an
     *   EVENT_ACTION event.
     * @param repeat If true, the timer will fire multiple events.  If false,
     *   the timer will fire just once and stop.
     */

    CNxTimer(CWidgetControl *pWidgetControl, uint32_t timeout, bool repeat);

    /**
     * Destructor.
     */

    ~CNxTimer(void);

    /**
     * Resets the (running) timer to its initial timeout value.  This
     * call does nothing if the timer is not running.
     */

    void reset(void);

    /**
     * Starts the timer.  This call does nothing if the timer is already
     * running.
     */

    void start(void);

    /**
     * Stops the timer.  Does nothing if the timer is not running.
     */

    void stop(void);

    /**
     * Returns true if the timer is currently running.
     */

    inline bool isRunning() const { return m_isRunning; }

    /**
     * Set the timeout of this timer.  This timeout value will not
     * take effect until start() or reset() is called.
     *
     * @param timeout The number of milliseconds that this timer will run
     *   before firing an event.
     */

    inline void setTimeout(uint32_t timeout)
    {
      m_timeout = timeout;
    }

    /**
     * Return the timeout of this timer.
     *
     * @return The number of milliseconds that this timer will run before
     *   firing an event.
     */

    inline uint32_t getTimeout(void) const
    {
      return m_timeout;
    }
  };
}

#endif // __cplusplus

#endif // __APPS_INCLUDE_GRAPHICS_NXWIDGETS_CNXTIMER_HXX
