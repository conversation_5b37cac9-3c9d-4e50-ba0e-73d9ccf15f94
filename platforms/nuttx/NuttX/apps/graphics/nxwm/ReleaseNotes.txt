NxWidgets-1.0
=============

The initial version of NxWidgets was released on March 22, 2012.  This
package has been used in several projects prior to its release to open
source and is considered stable its initial release (although not all
features have been fully verified).

NxWidgets-1.1
=============

The 2nd release of the NxWidgets package was made on May 19, 2012.  The
headline new feature in this release is:

* NxWM, the tiny window manager based on NX and NxWidgets. NxWM is true
  multiple window manager but only one window is displayed at a time.
  This simplification helps performance on LCD based products (in the
  same way that a tiled window manager helps) and also makes the best
  use of small displays.  It is awkward from a human factors point-of-view
  trying to manage multiple windows on a small display.

  The window manager consists of a task bar with icons representing the
  running tasks.  If you touch the task's icon, it comes to the top.  Each
  window has a toolbar with (1) a title, (2) a minimize button, and (3) a
  stop application button using the standard icons for these things. User
  input via a touchscreen or mouse and keyboard is supported.

  There is always a start window that is available in the task bar.  When
  you touch the start window icon, it brings up the start window containing
  icons representing all of the available applications.  If you touch an
  icon in the start window, it will be started and added to the task bar.

  There is a base class that defines an add-on application and an
  interface that supports incorporation of new application.  The only
  application that is provided is NxConsole.  This is an  NSH session
  running in a window.  You should be able to select the NX icon in the start
  menu and create as many NSH sessions in windows as you want. (keybard input
  still comes through serial).

  This initial release of NxWM should be considered an alpha release.
  All know issues are listed in the top-level TODO list.

Other important new features include:

* NxWidgets::IBitmap and NxWidgets::CRlePalettBitmap:  Extended to
  support different color look-up-tables (LUTs).  This allows images
  to be highlighted when touched, for example.
* NxWidgets::CImage:  Extended to support button-like behavior.  CImage
  widgets now work well as touchable icons.
* NxWidgets::CWidgetControl:  Add a semaphore to force clients to wait if the
  size or position of the window is not yet known (multi-user mode only).
  Enabled and verified creation of framed windows.
* NxWidgets::CWindowEventHandler and NxWidgets::CWindowEventHandler:  Add
  support for getting callbacks for window events.
* DOxygen documentation support.  Contributed by Jose Pablo Carballo.

An effort is underway to simplify NxWidgets and reduce its memory footprint:

* NxWidgets::CNxWidget: Removed support for "shelving" widgets, reference
  constants, and close types. Remove all support for widgets in a "vertical"
  hierarchy.  Now widgets exist in a flat, two-dimensional space and should
  not overlap.  Removed built-in modal loops.

Bug fixes include changes to the following (see the ChangeLog for details):

* NxWidgets::CRlePalettBitmap, NxWidgets::CRlePalettBitmap,
  NxWidgets::CGraphicsPort, NxWidgets::CWidgetControl

NxWidgets-1.2
=============

The 3rd release of the NxWidgets package was made on June 15, 2012.  This
release depends on NuttX-6.19 or above and should not be used with older
NuttX releases.

New features and enhancements include:

* NxWM::CNxConsole:  The NxWM NxConsole windows now correctly receive keyboard
  input.  In version 1.1, NxConsoles received input from /dev/console.  That,
  of course, leads to chaos when there a multiple windows trying to get input
  from the same device.  There is now a keyboard listener task that will
  accept input from /dev/console and inject that input into the NX keyboard
  interface.  NX will give the keyboard input only to the top window that
  has focus, eliminating the chaos.

* NxWM::CHexcalculator:  The NxWM demonstration/unit test now includes a hex
  calculator applications.  This demonstrates the use of arrays of buttons
  within a window.

Bugfixes:

* NxWM::CTaskbar:  Was inappropriately minimizing some windows.  Fixes
  some window raising/lowering bugs.

* NXWidgets::CButtonArray:  Fix some coloring problems when rending button
  text in cases where we cannot read from the display device.

As well as other, less critical bugs (see the ChangeLog for details)

NxWidgets-1.3
=============

The 4th release of the NxWidgets package was made on September 29, 2012.  This
release depends on NuttX-6.22 or above and should not be used with older
NuttX releases.

There are no functional changes in this release of NxWidgets.  This release
is required in order to retain compatibility with the most recent versions
of NuttX.  These comptibility changes include:

* Naming of function entry points
* Build system changes
* Changes needed for the NuttX configuration tool

NxWidgets-1.4
=============

The 5th release of the NxWidgets package was made on December 20, 2012.  This
release depends on NuttX-6.24 or above and should not be used with older
NuttX releases.  This release corresponds to SVN revision r5447.

Note: Nearly all changes between 1.3 and 1.4 were the result of the efforts
of Petteri Aimonen.

Additional new features and extended functionality in Version 1.4:

* NxWdigets::CNxServer: Reduce start-up delay
* NxWM::CApplicationWindow: Option to eliminate minimize button.
* NxWM::CTaskbar: Option to eliminate the background image; highlight current
  icon in the task bar.
* NxWidgets::CNumericEdit: New widget. Basically a CLabel with +/- buttons.
* NxWidgets::CNxTimer:  Replace the original signal-based solution with a
  work queue-based solution.  This raises some isses about using the
  internal work queues from user space.
* Build System:  Misc improvements.
* Tools: bitmap_converter.py will convert images to NxWidgets RLE-encoded format.

Bugfixes (see the change log for details).  Some of these are very important
(marked *critical*):

* NxWidgets::CCallback: Misplaced #endif
* NxWM::CCalculator and NxWM::CStartWindow: Add forward declaration for icon images.
* glych_cycle:  Correct width, destructor needs to be public.
* NxWidgets:CText and NxWidgets:CNumericEdite:  Fix some memory freeing bugs
* NxWidgets::CScrollingPanel:  It is borderless for now because there was no
  easy way to redraw only the required part of the border.

NxWidgets-1.5
=============

The 6th release of the NxWidgets package was made on February 1, 2013.  This
release depends on NuttX-6.25 or above and should not be used with older
NuttX releases.  This release corresponds to SVN revision r5595.

Note: Nearly all of the changes between 1.4 and 1.5 were the result of the
efforts of Petteri Aimonen.

Additional new features and extended functionality in Version 1.5:

* CNxWidgets::CTabPanel:  A new widget contributed by Petteri Aimonen.
  This widget provides a tab panel, which has a button bar at the top
  and panels below it. Pressing a button will select the corresponding
  panel.
* NxWidgets::CGraphicsPort: Many times we only want a constant background.
  In that case the old code filled the background, read it back, rendered
  the text and then wrote it back. When used with an LCD this causes
  some screen flicker. Added a variant of drawText that takes background
  color, so that the background and text can both be rendered at one go.
* NxWM::CScrollingPanel::scrollChildren(): Avoid unnecessary redraws in
  CScrollingPanel
* Kconfig: Added several "enabling" settings that must be selected to
  override the default setting.
* UnitTests:  Changes for compatibility with NuttX-6.25

Bugfixes:

* NxWidgets::CGraphicsPort::move():  Fix typo bug in bounding rectangle
  calculation.
* NxWM::CCycleButton:  Remove the separator from CCycleButton. It draws in
  wrong place, and doesn't look very good in the correct place either.
* NxWidgets::CLabel: The label was drawn as a single rectangular region,
  then a text was added to the on top of this.  The result is that the
  text would flicker when the CLabel was updated. With this change, the
  two step update is replaced with a five step update:  The background
  is updated as four rectangulear regions (leaving the previous text in
  place), then the new text is updated.  This eliminates the flicker.
* CGraphicsPort::_drawText:  Renamed from CGraphicsPort::drawText in order
  to eliminate some naming collisions when overloaded in some configurations
  (i.e., when both bool and nx_pixel_t are uint8_t).

NxWidgets-1.6
=============

The 7th release of the NxWidgets package was made on March 15, 2013.  This
release depends on NuttX-6.26 or above and should not be used with older
NuttX releases.  This release corresponds to SVN revision r5745.

Note: All of the changes between 1.5 and 1.6 were the result of the
efforts of Petteri Aimonen.

Additional new features and extended functionality in Version 1.6:

* NXWidgets::CNxWidget:  Add an inline function to get the current style.
* NXWidgets::CCycleButton: Make CCycleButton change state in onPreRelease().
  This way the new value is already available when a listener gets the
  action event.
* NxWidgets::CImage:  Two enhancements:
  1) Allow changing the bitmap even after the control has been created.
  2) Allow giving 'null' to have the control draw no image at all.

* NxWM::CTaskBar: Make a some methods of CTaskbar virtual to allow
  customizations.
* NxWM::CTaskBar:  Allow windows with null icon. This makes sense for e.g.
  full screen windows.
* NxWM::CApplicationWindow:  Add config options to override NxWM
  stop/minimize icons.
* NwWM::CStartWindow, NxWM::CWindowMessenger: Get rid of the start window
  thread. Instead, handle all events through the USRWORK work queue.
  For me, this was necessary because I would open some files in button
  handlers and close them in NxTimer handlers. If these belonged to
  different tasks, the close operation would fail.  Further benefits:
  + Gets rid of one task and message queue.
  + Reduces the amount of code required
  + Decouples CStartWindow from everything else - now it is just a window
    with application icons, not an integral part of the event logic.
  + All events come from the same thread, which reduces the possibility of
    multithreading errors in user code.
  + The user code can also send events to USRWORK, so that everything gets
    serialized nicely without having to use so many mutexes.
  Drawbacks:
  - Currently the work state structure is malloc()ed, causing one allocation
    and free per each input event. Could add a memory pool for these later, but
    the speed difference doesn't seem noticeable.
  - The work queue will add ~50 ms latency to input events. This is however
    configurable, and the delay is anyway short enough that it is unnoticeable.

Bugfixes:

* NxWidgets::CWidgetControl:  Added another semaphore, boundssem, which
  is set as soon as the screen bounds are known.  This corrects two
  problems:
  1) Due to the way nxgl_rectsize computes the size, it will never
     be 0,0 like CWidgetControl expects. Therefore the size is considered
     valid even though it has not been set yet.
  2) After the check is fixed to test for > 1, NxWM window creation will
     hang. This is due to the fact that it uses the screen bounds for
     determining window size. This was being blocked on geosem, which
     is only posted after the size has been set.
* NxWidgets::CNxTimer:  Previously repeated timers were re-enabled after
  the timer action event. Consequently, if the action event handler tried
  to stop the timer, the request would be ignored.  Changes the order
  so that the timer is re-enabled before the callback.  There is still
  no risk of re-entrancy, because everything executes on the USRWORK work
  queue.
* NxWidgets::CLabel: Fix backward conditional compilation in the "flicker
  free" logic.
* NxWidgets::CMultiLineTestBox: Fix text placement error.  From Petteri
  Aimonen.

* NxWidgets/tools/bitmap_converter.py: Fix bitmap_converter.py so that
  it works with indexed input images.

NxWidgets-1.7
=============

The 8th release of the NxWidgets package was made on April 28, 2013.  This
release depends on NuttX-6.26 or above and should not be used with older
NuttX releases.

Note: All of the changes between 1.6 and 1.7 were the result of the
efforts of Petteri Aimonen.

Additional new features and extended functionality in Version 1.7:

* NxWidgets::CGlyphButton: Generate action event, like CButton does.
* NxWM::CTaskBar:  Add option CONFIG_NXWM_TASKBAR_NO_BORDER to suppress
  drawing of the border on the taskbar.
* NxWidgets::CNxTimer:  Add function to check if CNxTimer is running.
* NxWidgets::CNxWidgets:  Allow overriding of the checkCollision() method.

Bugfixes:

* NxWidgets bitmap_converter.py: Fix bug when image width > 255
* NxWM::CScrollbarPanel:  Fix spelling error in class name: CScollbarPanel
  should be CScrollbarPanel
* NxWidgets::CGlyphButton:  Prevent drawing outside of the bitmap size.

NxWidgets-1.8
=============

The 9th release of the NxWidgets package was made on June 14, 2013.  This
release depends on NuttX-6.26 or above and should not be used with older
NuttX releases.

Note: Most of the changes between 1.7 and 1.8 were the result of the
efforts of Ken Pettit and Petteri Aimonen.

Additional new features and extended functionality in Version 1.8:

* NxWM::CMediaPlayer: Shell application for an MP3 Media Player (from Ken
  Pettit).
* NxWidgets::CGlyphSliderHorizontal and NxWidgets::CGlyphSliderHorizontalGrip:
  New widgets added by Ken Pettit.  Includes a Unit Test.
* NxWidgets::CImage:  Allow a NULL pointer for a bitmap (from Petteri
  Aimonen).
* NxWidgets::CNumericEdit:  Delay before auto-incrementing now varies: A
  longer delay is required to start auto-incrementing and speed increases
  while pressed (from Petteri Aimonen).
* NxWM::CTaskbar: Add a method to redraw the taskbar and the current
  application.  This should only be necessary if the display loses state due
  to e.g. powerdown or other manual intervention (from Petteri Aimonen).

Bugfixes:

* NxWM::CCalibration: Fix a race condition that would cause the calibration
  screen to fail to come up when its icon was touched (from Ken Pettit).
* Default priorities for NxWidget and NxWM threads should be 100, not 50, to
  be consistent with other default priorities.
* NxWidgets::CGlyphSliderHorizontal:  Fix a drawing error (from Ken Pettit).
* Update the way that NSH the Unit Tests are registered as built-in NSH
  applications.

NxWidgets-1.9
=============

The 10th release of the NxWidgets package was made on October 28, 2013.  This
release depends on NuttX-6.26 or above and should not be used with older
NuttX releases.

Additional new features and extended functionality in Version 1.9:

* NxWM::CCalibration.cxx/hxx:  If CONFIG_NXWM_CALIBRATION_MESSAGES is
  defined then CCalibration will provide some instructions in the center
  of the display.
* NxWM::CCalibration.cxx/hxx:  Add options to collect and average
  multiple samples at each location.  Also logic to discard the most
  extreme samples before averaging.
* NxWidgets::CScaledBitmap:  This new class is a wrapper for an class
  the exports IBitMap.  It will perform scaling via bi-linear interpolation
  so that images can be scaled to any size desired.
* NxWM::CTaskbar:: Can now be configured to scale taskbar icons using
  NxWidgets::CScaledBitmap.
* NxWidgets/libnxwidgets/src/glyph_nxlogo320x320.cxx:  Add a big, 320x320
  NuttX logo image.  The older, smaller NuttX logo was renamed from
  nxlogo to nxlogo160x160 in all places.
* NxWM::CCalibration and NxWM::CTouchscreen:  Add a complex touchscreen
  scaling algorithm to handling the case where the measured X values also
  vary with y position (and vice versa).

Bugfixes:

(There are no bugfixes recorded for this release).

NxWidgets-1.10
==============

The 11th release of the NxWidgets package was made on December 7, 2013.  This
release depends on NuttX-6.26 or above and should not be used with older
NuttX releases.

Additional new features and extended functionality in Version 1.10:

* NxWM:Calibration and UnitTests/nxwm:  Add logic to enable saving and
  recovering touchscreen calibration data.  This depends upon having
  platform-dependent support for storage of configuration data.  Portions
  suggested by Ken Pettit.

NxWidgets-1.11
==============

The 12th release of the NxWidgets package was made on January 20, 2014.  This
release depends on NuttX-6.33 or above and should not be used with older
NuttX releases.

Additional new features and extended functionality in Version 1.11:

* Primarily changes associated with the NuttX kernel build where the
  graphics application lies in user space and the graphics server lies in
  protected kernel space.  This change is mostly a matter of repartitioning
  functionality and should not cause in any functional differences.

NxWidgets-1.12
==============

The 13th release of the NxWidgets package was made on August 14, 2014.  This
release depends on NuttX-7.3 or above and should not be used with older
NuttX releases.

Additional new features and extended functionality in Version 1.12:

* NxWM: Rename all ICON glyph files to include the size of the ICON as part
  of the file name.  This allows us to support different sizes for the same
  ICON
* NxWM: Add larger versions (approx 50x50) versions of all ICONs.
* NxWM::CToolBar:  Toolbar font ID can now be separately configured.
* NxWM::CMediaPlayer:  Will now dynamically position player controls
  based on the display and image sizes.
* NxWM::CMediaPlayer:  Add options to manage spacing of media player
  buttons and borders vs. borderless buttons.
* NxWidget::CImage: Now includes methods to align images in the widget
  region.
* NxWM::CMediaPlayer now uses the new CImage methods to align media controls
  in buttons.
* NxWidgets::CStickyImage:  A version of CImage that sticks in the selected
  state when clicked.
* NxMW::CMediaPlayer: Now supports a pause button.
* NxMW::CMediaPlayer: Convert CLabel to a CListBox to support selection of
  one of many media files to play.  This should be a CScrollingTextBox to
  support an indefinitely large number of media files.
* NxWidgets::CGlyphSliderHorizontal: No longer uses a hard-coded slider
  height; the slider height is now provided as a parameter, replacing the
  widget height which is now calculated from the glip image height.
* NxWM::CMediaPlayer:  Now calculates the volume slider height from the grip
  image height.
* NxWidgets:CTabPanel:  Make CTabPanel raise action event when tab is
  changed.  Also add a function for getting the currently selected tab
  index.  From Petteri Aimonen.
* NxWidgets/Kconfig: Add configuration options to filter CMediaPlayer files
  based on extension.
* NxWM::CMediaPlayer:  Add logic to read files from the media mountpoint,
  filter them by extension, and display the file names in the listbox.
* NxWM::CMediaPlayer:  Now integrated with the NxPlayer library.
* NxWidgets::CNxString: Add a getAllocSize() method to make it easier to
  convert CNxStrings to NUL-terminated C strings.
* NxWM::CMediaPlayer: Add a CLabel that show the current fast forward/rewind
  speed.
* NxWM::CKeyboard: Add support for a USB keyboard as the NxConsole and text
  widget input.

Bugfixes:

* NxWidgets:  Fix typo RBG -> RGB.  This makes NxWidgets incompatible with
  versions of NuttX 7.2 and below.
* NxWidgets::CImage: Fix setImageLeft and setImageTop methods
* NxMW::CMediaPlayer: Correctly catch the volume level change event.
* NxWidgets::CGraphicsPort:  Fix drawBitmapGreyScale method.  Actually drew
  nothing because of several errors in positioning and buffer usage.
* NxWidgets::CImage:  Should not attempt to draw the "empty" regions at the
  top and the bottom of the image box in greyscale.
* NxWidgets::CNxString:  Make destructor virtual.  From Petteri Aimonen.
* NxWidgets::CTaskbar:  Fix type of return value from openRawWindow()
  method.  From Petteri Aimonen.
* NxWidgets::CTabPanel:  Modify behavior in showPage() method.  Eliminate a
  duplicate redraw that caused flickering; re-order some logic so that
  widgets are hidden before operated on.  From Petteri Aimonen.
* NxWidgets::CNxTimer:  Make CNxTimer stop itself before destruction.
  Otherwise a call to an invalid work queue entry may occur.  From Petteri
  Aimonen.
* NxWM::IApplicationFactory: Add a do-nothing, virtual destructor to
  eliminate a warning.
* XWidgets::CNxWidget: Remove an unused bit field.

NxWidgets-1.13
==============

The 14th release of the NxWidgets package was made on September 28, 2014.  This
release depends on NuttX-7.5 or above and should not be used with older
NuttX releases.

Additional new features and extended functionality in Version 1.13:

* There are no new features in this release.  This release simply updates
  some naming that is necessary for NxWidgets to build successfully with
  NuttX-7.5 and above.

Bugfixes:

* There are no new bug fixes in this release.

NxWidgets-1.14
==============

The 15th release of the NxWidgets package was made on November 26, 2014.  This
release depends on NuttX-7.6 or above and should not be used with older
NuttX releases.

Additional new features and extended functionality in Version 1.14:

* There are no new features in this release.  This release simply correct
  all UnitTest/ files to account for the corrected syslog() prototype in
  NuttX 7.6.  That change introduced an incompatibility between NuttX 7.6
  and previous versions of the UnitTest code.

Bugfixes:

* There are no new bug fixes in this release.


NxWidgets-1.15
==============

The 16th release of the NxWidgets package was made on April 14, 2015.  This
release depends on NuttX-7.24 or above and should not be used with older
NuttX releases.

Additional new features and extended functionality in Version 1.15:

* The primary purpose of this release is update the NxWidgets and NxWM
  implementations to reflect changes in naming and interfaces in the
  NuttX 7.24 release
* NxWidgets:: INxWindow, CNxWindow, CBgWindow, CNxTkWindow, CNxToolbar,
  CGraphicsPort updated to include a "caps" parameter in all drawLine()
  methods.  This permits drawing a circular cap at the end of lines and
  better supports joining of line segments.

Bugfixes:

* There are no new bug fixes in this release.

NxWidgets-1.16
==============

The 17th release of the NxWidgets package was made on June 1, 2016.  This
release depends on NuttX-7.16 or above and should not be used with older
NuttX releases.

The TAG for this release is nuttx-7.16

Additional new features and extended functionality in Version 1.15:

* The NxWidgets/ directory has been removed from the NuttX GIT repository
  and now stands alone in its own repository (2015-06-27).

Bugfixes\:

* NxWM::CTouchscreen: When boardctl() fails, need to print errno not the
  returned value which will always be -1.
* NxWidgets::CNxServer:  Correct use of the BOARDIOC_GRAPHICS_SETUP
  boardctl() call.
* CListBox unit test:  Should use RAND_MAX, not MAX_RAND.
* Framebuffer driver parameters have changed.  This introduces an
  incompatibility with versions of NuttX prior to 7.16.
* UnitTests: Add Make.defs files to all UnitTests directories.  This is
  required by the current apps/Makefile.  Hmmm..  This means that no one
  has built the NxWidgets packages in the last several releases!
* UnitTests: Add some default definitions to handle the (unusual) case
  where 'make clean' is called with no .config or Make.defs file installed

NxWidgets-1.17
==============

The 18th release of the NxWidgets package was made on July 25, 2016.  This
release depends on NuttX-7.17 or above and should not be used with older
NuttX releases.

The TAG for this release is nuttx-7.17

Additional new features and extended functionality in Version 1.17:

* There are no new features in this release.  There are only updates
  necessary to use NxWidgets with other changes to NuttX-7.17.  These
  changes include:

  - Changes to debug macro names.  dbg() and vdbg() are replaces with
    info(), warn(), and err().
  - Changes to Unittests/ Makefiles to deal with changes in the apps/
    build system.
  - Changes to pre-processor include statements needed to handle
    reorganization of the apps/include directory.
  - Update include paths to adapt to changes made to apps/ include path
    logic

Bugfixes\:

* None in this release

NxWidgets-1.18
==============

The 19th release of the NxWidgets package was made on December 26, 2016.  This
release depends on NuttX-7.19 or above and should not be used with older
NuttX releases.

The TAG for this release is nuttx-7.19

Additional new features and extended functionality in Version 1.18:

* libnxwidgets:  Allow NxWidgets::CListDataItems subclasses access to text
  and value.  From Petteri Aimonen.
* Add setting task name via prctl() to NxWidgets::CNxServer and
  NxWM:CTouchScreen.  From Petteri Aimonen.
* libnxwidgets:  In NxWidgets::CText, allow setting negative line spacing.
  This is useful to have text take up less space, as quite many fonts have
  large line spacings built-in.  From Petteri Aimonen.
* libnxwidgets: Allow setting line spacing in NxWidgets::CMultiLineTextBox.
  From Petteri Aimonen.
* CNxServer: Remove NX server task.  Now calls nx_start (indirectly through
  boardctrl(BOARDIOC_NX_START) to start the NX Server kernel thread.

Bugfixes:

* NxWM needs the path to the apps/include directory.
* NxWidgets:  Fix CListBox crash when clicked below the last item.  From
  Petteri Aimonen.
* libnxwdigets:  Fix memory leak in CNxWindow and make behaviour match
  CNxTkWindow.  From Petteri Aimonen.
* libnxwidgets:  Fix default text colors in NxWidgets::CListBox.  From
  Petteri Aimonen.
* libnxwidgets:  Avoid unnecessary redraw in NxWidgets::CListBox. The
  ListData will raise an event that will cause a redraw anyway.  Redrawing
  in click() caused double redraws.  From Petteri Aimonen.
* libnxwidgets:  Fix coordinate handling in NxWidgets::CMultiLineTextBox.
  Related to 'Thu Feb 21 16:47:01 2013 Fix text placement in
  CMultiLineTextbox.' There was a few similar bugs remaining in calculating
  of rows to show and in clearing the background.  From Petteri Aimonen.
* CNxServer: Can't use printf in this context.

NxWidgets-1.19
==============

The 20th release of the NxWidgets package was made on March 2, 2018.  This
release depends on NuttX-7.24 or above and should not be used with older
NuttX releases.

The TAG for this release is nuttx-7.24

Additional new features and extended functionality in Version 1.19:

  (There are no new graphics features.  This release is for NuttX
   compatibility only and includes a few additional bugfixes)

Bugfixes and Updates for Compatibility with NuttX-7.24:

* Correct NX Server message queue names.  Should not be /dev/nx*.  They must
  be relative to /dev/mqueue so "nxabc" will appear at "/var/nxabc".
* strcasecmp, strncasecmp, bzero, bcmp, and bcopy should be defined in
  strings.h, not string.h.  bzero, bcmp, and bcopy are legacy functions; the
  contemporary counterparts should be used instead.
* Unitests/nxwm:  Change includes because up_cxxinitialize() is now
  prototyped in apps/include/platfor/cxxinitialize.h.
* Prototype of nsh_telnetstart() has changed.
* NxWidgets:  Fix work_s initialization in cwindowmessenger.hxx. From Sakari
  Kapanen.
* Remove all support for single user mode.
* Remove unused CONFIG_NXWIDGETS_EXTERNINIT configuration variable.
* CTaskBar: Fix type checking issue with GCC 6  From Sakari Kapanen.
* nxwm:  Eliminate BOARDIOC_TSCTEST_SETUP (2018-01-17).
* In NxWM unit test, call boardctl(BOARDIOC_INIT) if available and
  appropriate (2018-01-17).
* Fix a dependency in Kconfig:  CONFIG_NXWIDGET_SERVERINIT is definitely
  supported in the PROTECTED and KERNEL build modes (2018-01-18).
