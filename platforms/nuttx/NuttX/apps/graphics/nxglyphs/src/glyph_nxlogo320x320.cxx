/****************************************************************************
 * apps/graphics/nxglyphs/src/glyph_nxlogo320x320.cxx
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

#include <sys/types.h>
#include <stdint.h>
#include <stdbool.h>

#include <nuttx/nx/nxglib.h>
#include <nuttx/video/fb.h>
#include <nuttx/video/rgbcolors.h>

#include "graphics/nxwidgets/nxconfig.hxx"
#include "graphics/nxwidgets/crlepalettebitmap.hxx"
#include "graphics/nxglyphs.hxx"

/****************************************************************************
 * Pre-Processor Definitions
 ****************************************************************************/

#define BITMAP_NROWS     320
#define BITMAP_NCOLUMNS  320
#define BITMAP_NLUTCODES 7

/****************************************************************************
 * Private Bitmap Data
 ****************************************************************************/

using namespace NXWidgets;

/* RGB24 (8-8-8) Colors */

#if CONFIG_NXWIDGETS_BPP == 24 ||  CONFIG_NXWIDGETS_BPP == 32

static const uint32_t g_nuttxLut[BITMAP_NLUTCODES] =
{
  0x000000, 0x60b0e0, 0x3c34e4, 0x2c2884, 0xd0e4f0, 0xfcfcfc, 0x5c98bc  /* Codes 0-6 */
};

/* RGB16 (565) Colors */

#elif CONFIG_NXWIDGETS_BPP == 16

static const uint16_t g_nuttxLut[BITMAP_NLUTCODES] =
{
  0x0000, 0x659c, 0x41bd, 0x3151, 0xd73e, 0xffff, 0x64d8  /* Codes 0-6 */
};

/* 8-bit color lookups.  NOTE:  This is really dumb!  The lookup index is 8-bits and it used
 * to lookup an 8-bit value.  There is no savings in that!  It would be better to just put
 * the 8-bit color/greyscale value in the run-length encoded image and save the cost of these
 * pointless lookups.  But these pointless lookups do make the logic compatible with the
 * 16- and 24-bit types.
 */

#elif CONFIG_NXWIDGETS_BPP == 8
#  ifdef CONFIG_NXWIDGETS_GREYSCALE

/* 8-bit Greyscale */

static const uint8_t g_nuttxLut[BITMAP_NLUTCODES] =
{
  0x02, 0x9f, 0x4c, 0x35, 0xe1, 0xfe, 0x8c  /* Codes 0-6 */
};

#  else /* CONFIG_NXWIDGETS_GREYSCALE */

/* RGB8 (332) Colors */

static const nxgl_mxpixel_t g_nuttxLut[BITMAP_NLUTCODES] =
{
  0x00, 0x7b, 0x4b, 0x26, 0xff, 0xff, 0x77  /* Codes 0-6 */
};

#  endif
#else
# error "Unsupported pixel format"
#endif

static const struct SRlePaletteBitmapEntry g_nuttxRleEntries[] =
{
  {154, 0}, {  2, 1}, { 10, 2}, {154, 0},  /* Row 0 */
  {153, 0}, {  2, 1}, { 12, 2}, {153, 0},  /* Row 1 */
  {152, 0}, {  2, 1}, { 14, 2}, {152, 0},  /* Row 2 */
  {151, 0}, {  2, 1}, { 16, 2}, {151, 0},  /* Row 3 */
  {150, 0}, {  2, 1}, {  6, 2}, {  3, 3}, {  9, 2}, {150, 0},  /* Row 4 */
  {149, 0}, {  2, 1}, {  6, 2}, {  5, 3}, {  9, 2}, {149, 0},  /* Row 5 */
  {148, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  3, 1}, {  2, 3}, {  9, 2}, {148, 0},  /* Row 6 */
  {147, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, {  3, 2}, {  2, 3}, {  9, 2},  /* Row 7 */
  {147, 0},
  {146, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {  2, 3}, {  9, 2},  /* Row 8 */
  {146, 0},
  {145, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, {  7, 2}, {  2, 3}, {  9, 2},  /* Row 9 */
  {145, 0},
  {144, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, {  9, 2}, {  2, 3}, {  9, 2},  /* Row 10 */
  {144, 0},
  {143, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 11, 2}, {  2, 3}, {  9, 2},  /* Row 11 */
  {143, 0},
  {142, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 13, 2}, {  2, 3}, {  9, 2},  /* Row 12 */
  {142, 0},
  {141, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 15, 2}, {  2, 3}, {  9, 2},  /* Row 13 */
  {141, 0},
  {140, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 17, 2}, {  2, 3}, {  9, 2},  /* Row 14 */
  {140, 0},
  {139, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 19, 2}, {  2, 3}, {  9, 2},  /* Row 15 */
  {139, 0},
  {138, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 21, 2}, {  2, 3}, {  9, 2},  /* Row 16 */
  {138, 0},
  {137, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 23, 2}, {  2, 3}, {  9, 2},  /* Row 17 */
  {137, 0},
  {136, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 25, 2}, {  2, 3}, {  9, 2},  /* Row 18 */
  {136, 0},
  {135, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 27, 2}, {  2, 3}, {  9, 2},  /* Row 19 */
  {135, 0},
  {134, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 29, 2}, {  2, 3}, {  9, 2},  /* Row 20 */
  {134, 0},
  {133, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 31, 2}, {  2, 3}, {  9, 2},  /* Row 21 */
  {133, 0},
  {132, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 33, 2}, {  2, 3}, {  9, 2},  /* Row 22 */
  {132, 0},
  {131, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 35, 2}, {  2, 3}, {  9, 2},  /* Row 23 */
  {131, 0},
  {130, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 37, 2}, {  2, 3}, {  9, 2},  /* Row 24 */
  {130, 0},
  {129, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 39, 2}, {  2, 3}, {  9, 2},  /* Row 25 */
  {129, 0},
  {128, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 41, 2}, {  2, 3}, {  9, 2},  /* Row 26 */
  {128, 0},
  {127, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 43, 2}, {  2, 3}, {  9, 2},  /* Row 27 */
  {127, 0},
  {126, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 45, 2}, {  2, 3}, {  9, 2},  /* Row 28 */
  {126, 0},
  {125, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 47, 2}, {  2, 3}, {  9, 2},  /* Row 29 */
  {125, 0},
  {124, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 49, 2}, {  2, 3}, {  9, 2},  /* Row 30 */
  {124, 0},
  {123, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 51, 2}, {  2, 3}, {  9, 2},  /* Row 31 */
  {123, 0},
  {122, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 53, 2}, {  2, 3}, {  9, 2},  /* Row 32 */
  {122, 0},
  {121, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 55, 2}, {  2, 3}, {  9, 2},  /* Row 33 */
  {121, 0},
  {120, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 57, 2}, {  2, 3}, {  9, 2},  /* Row 34 */
  {120, 0},
  {119, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 59, 2}, {  2, 3}, {  9, 2},  /* Row 35 */
  {119, 0},
  {118, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 61, 2}, {  2, 3}, {  9, 2},  /* Row 36 */
  { 56, 0}, { 10, 4}, { 52, 0},
  {117, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 63, 2}, {  2, 3}, {  9, 2},  /* Row 37 */
  { 53, 0}, { 14, 4}, { 50, 0},
  {116, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 65, 2}, {  2, 3}, {  9, 2},  /* Row 38 */
  { 50, 0}, { 18, 4}, { 48, 0},
  {115, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 67, 2}, {  2, 3}, {  9, 2},  /* Row 39 */
  { 48, 0}, { 20, 4}, { 47, 0},
  {114, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 24, 2}, { 10, 4}, { 35, 2},  /* Row 40 */
  {  2, 3}, {  9, 2}, { 46, 0}, { 22, 4}, { 46, 0},
  {113, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 23, 2}, { 14, 4}, { 34, 2},  /* Row 41 */
  {  2, 3}, {  9, 2}, { 45, 0}, { 23, 4}, { 45, 0},
  { 48, 0}, { 11, 4}, { 53, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 23, 2},  /* Row 42 */
  { 17, 4}, { 33, 2}, {  2, 3}, {  9, 2}, { 43, 0}, { 25, 4}, { 44, 0},
  { 46, 0}, { 14, 4}, { 51, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 23, 2},  /* Row 43 */
  { 19, 4}, { 33, 2}, {  2, 3}, {  9, 2}, { 42, 0}, { 26, 4}, { 43, 0},
  { 44, 0}, { 18, 4}, { 48, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 23, 2},  /* Row 44 */
  { 21, 4}, { 33, 2}, {  2, 3}, {  9, 2}, { 40, 0}, { 28, 4}, { 42, 0},
  { 43, 0}, { 20, 4}, { 46, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 23, 2},  /* Row 45 */
  { 23, 4}, { 33, 2}, {  2, 3}, {  9, 2}, { 39, 0}, { 28, 4}, { 42, 0},
  { 42, 0}, { 22, 4}, { 44, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 23, 2},  /* Row 46 */
  { 25, 4}, {  8, 3}, { 25, 2}, {  2, 3}, {  9, 2}, { 37, 0}, { 30, 4}, { 41, 0},
  { 42, 0}, { 23, 4}, { 42, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1}, { 24, 2},  /* Row 47 */
  { 26, 4}, {  9, 3}, { 24, 2}, {  2, 3}, {  9, 2}, { 36, 0}, { 14, 4}, {  2, 5},
  { 14, 4}, { 41, 0},
  { 41, 0}, { 25, 4}, {  1, 0}, {  9, 6}, { 30, 0}, {  2, 1}, {  6, 2}, {  2, 3},  /* Row 48 */
  {  2, 1}, { 24, 2}, { 28, 4}, {  9, 3}, { 24, 2}, {  2, 3}, {  9, 2}, { 34, 0},
  { 12, 4}, {  8, 5}, { 12, 4}, {  2, 6}, { 38, 0},
  { 40, 0}, { 27, 4}, { 11, 6}, { 27, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1},  /* Row 49 */
  { 25, 2}, { 28, 4}, { 10, 3}, { 24, 2}, {  2, 3}, {  9, 2}, { 33, 0}, { 11, 4},
  { 10, 5}, { 11, 4}, {  3, 6}, { 37, 0},
  { 39, 0}, { 29, 4}, { 12, 6}, { 24, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1},  /* Row 50 */
  { 25, 2}, { 11, 4}, {  6, 5}, { 13, 4}, { 10, 3}, { 24, 2}, {  2, 3}, {  9, 2},
  { 31, 0}, { 11, 4}, { 12, 5}, { 11, 4}, {  3, 6}, { 36, 0},
  { 39, 0}, { 29, 4}, { 12, 6}, { 23, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1},  /* Row 51 */
  { 26, 2}, { 10, 4}, {  8, 5}, { 12, 4}, { 11, 3}, { 24, 2}, {  2, 3}, {  9, 2},
  { 30, 0}, { 11, 4}, { 12, 5}, { 11, 4}, {  4, 6}, { 35, 0},
  { 38, 0}, { 31, 4}, { 12, 6}, { 21, 0}, {  2, 1}, {  6, 2}, {  2, 3}, {  2, 1},  /* Row 52 */
  { 27, 2}, { 10, 4}, {  9, 5}, { 12, 4}, { 11, 3}, { 24, 2}, {  2, 3}, {  9, 2},
  { 28, 0}, { 11, 4}, { 14, 5}, { 10, 4}, {  5, 6}, { 34, 0},
  { 38, 0}, { 12, 4}, {  5, 5}, { 14, 4}, { 12, 6}, { 20, 0}, {  2, 1}, {  6, 2},  /* Row 53 */
  {  2, 3}, {  2, 1}, { 27, 2}, { 10, 4}, { 11, 5}, { 11, 4}, { 12, 3}, { 24, 2},
  {  2, 3}, {  9, 2}, { 27, 0}, { 11, 4}, { 14, 5}, { 10, 4}, {  6, 6}, { 33, 0},
  { 37, 0}, { 11, 4}, {  8, 5}, { 14, 4}, { 12, 6}, { 18, 0}, {  2, 1}, {  6, 2},  /* Row 54 */
  {  2, 3}, {  2, 1}, { 28, 2}, { 10, 4}, { 12, 5}, { 11, 4}, { 12, 3}, { 24, 2},
  {  2, 3}, {  9, 2}, { 25, 0}, { 11, 4}, { 15, 5}, { 10, 4}, {  7, 6}, { 32, 0},
  { 37, 0}, { 10, 4}, { 10, 5}, { 13, 4}, { 12, 6}, { 17, 0}, {  2, 1}, {  6, 2},  /* Row 55 */
  {  2, 3}, {  2, 1}, { 29, 2}, {  9, 4}, { 13, 5}, { 11, 4}, { 12, 3}, { 25, 2},
  {  2, 3}, {  9, 2}, { 24, 0}, { 11, 4}, { 15, 5}, { 10, 4}, {  7, 6}, { 32, 0},
  { 36, 0}, { 10, 4}, { 11, 5}, { 14, 4}, { 12, 6}, { 15, 0}, {  2, 1}, {  6, 2},  /* Row 56 */
  {  2, 3}, {  2, 1}, { 30, 2}, {  9, 4}, { 14, 5}, { 11, 4}, { 12, 3}, { 25, 2},
  {  2, 3}, {  9, 2}, { 22, 0}, { 11, 4}, { 15, 5}, { 11, 4}, {  8, 6}, { 31, 0},
  { 36, 0}, { 10, 4}, { 12, 5}, { 13, 4}, { 12, 6}, { 14, 0}, {  2, 1}, {  6, 2},  /* Row 57 */
  {  2, 3}, {  2, 1}, { 31, 2}, {  9, 4}, { 14, 5}, { 11, 4}, { 12, 3}, { 26, 2},
  {  2, 3}, {  9, 2}, { 21, 0}, { 11, 4}, { 15, 5}, { 11, 4}, {  8, 6}, { 31, 0},
  { 36, 0}, {  9, 4}, { 14, 5}, { 13, 4}, { 12, 6}, { 12, 0}, {  2, 1}, {  6, 2},  /* Row 58 */
  {  2, 3}, {  2, 1}, { 32, 2}, {  8, 4}, { 16, 5}, { 11, 4}, { 12, 3}, { 26, 2},
  {  2, 3}, {  9, 2}, { 19, 0}, { 11, 4}, { 15, 5}, { 11, 4}, { 10, 6}, { 30, 0},
  { 36, 0}, {  9, 4}, { 14, 5}, { 13, 4}, { 12, 6}, { 11, 0}, {  2, 1}, {  6, 2},  /* Row 59 */
  {  2, 3}, {  2, 1}, { 33, 2}, {  8, 4}, { 16, 5}, { 11, 4}, { 12, 3}, { 27, 2},
  {  2, 3}, {  9, 2}, { 18, 0}, { 11, 4}, { 15, 5}, { 11, 4}, { 10, 6}, { 30, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, { 13, 4}, { 12, 6}, {  9, 0}, {  2, 1}, {  6, 2},  /* Row 60 */
  {  2, 3}, {  2, 1}, { 34, 2}, {  8, 4}, { 17, 5}, { 11, 4}, { 12, 3}, { 27, 2},
  {  2, 3}, {  9, 2}, { 16, 0}, { 11, 4}, { 15, 5}, { 11, 4}, { 11, 6}, { 30, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, { 13, 4}, { 12, 6}, {  8, 0}, {  2, 1}, {  6, 2},  /* Row 61 */
  {  2, 3}, {  2, 1}, { 35, 2}, {  8, 4}, { 17, 5}, { 11, 4}, { 12, 3}, { 28, 2},
  {  2, 3}, {  9, 2}, { 15, 0}, { 11, 4}, { 15, 5}, { 11, 4}, { 12, 6}, { 29, 0},
  { 36, 0}, {  8, 4}, { 17, 5}, { 13, 4}, { 12, 6}, {  6, 0}, {  2, 1}, {  6, 2},  /* Row 62 */
  {  2, 3}, {  2, 1}, { 36, 2}, {  8, 4}, { 18, 5}, { 11, 4}, { 12, 3}, { 28, 2},
  {  2, 3}, {  9, 2}, { 13, 0}, { 11, 4}, { 15, 5}, { 11, 4}, { 13, 6}, { 29, 0},
  { 36, 0}, {  8, 4}, { 17, 5}, { 13, 4}, { 12, 6}, {  5, 0}, {  2, 1}, {  6, 2},  /* Row 63 */
  {  2, 3}, {  2, 1}, { 37, 2}, {  8, 4}, { 18, 5}, { 11, 4}, { 12, 3}, { 29, 2},
  {  2, 3}, {  9, 2}, { 12, 0}, { 11, 4}, { 15, 5}, { 11, 4}, { 13, 6}, { 29, 0},
  { 36, 0}, {  8, 4}, { 18, 5}, { 13, 4}, { 12, 6}, {  3, 0}, {  2, 1}, {  6, 2},  /* Row 64 */
  {  2, 3}, {  2, 1}, { 38, 2}, {  8, 4}, { 19, 5}, { 11, 4}, { 12, 3}, { 29, 2},
  {  2, 3}, {  9, 2}, { 10, 0}, { 11, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 29, 0},
  { 36, 0}, {  8, 4}, { 18, 5}, { 13, 4}, { 12, 6}, {  2, 0}, {  2, 1}, {  6, 2},  /* Row 65 */
  {  2, 3}, {  2, 1}, { 39, 2}, {  8, 4}, { 19, 5}, { 11, 4}, { 12, 3}, { 30, 2},
  {  2, 3}, {  9, 2}, {  9, 0}, { 11, 4}, { 15, 5}, { 11, 4}, { 13, 6}, { 30, 0},
  { 36, 0}, {  8, 4}, { 19, 5}, { 13, 4}, { 12, 6}, {  2, 1}, {  6, 2}, {  2, 3},  /* Row 66 */
  {  2, 1}, { 40, 2}, {  8, 4}, { 20, 5}, { 11, 4}, { 12, 3}, { 30, 2}, {  2, 3},
  {  9, 2}, {  7, 0}, { 11, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 30, 0},
  { 36, 0}, {  8, 4}, { 19, 5}, { 13, 4}, { 11, 6}, {  1, 3}, {  1, 1}, {  6, 2},  /* Row 67 */
  {  2, 3}, {  2, 1}, { 41, 2}, {  8, 4}, { 20, 5}, { 11, 4}, { 12, 3}, { 31, 2},
  {  2, 3}, {  9, 2}, {  6, 0}, { 11, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 30, 0},
  { 36, 0}, {  8, 4}, { 20, 5}, { 13, 4}, {  9, 6}, {  3, 3}, {  5, 2}, {  2, 3},  /* Row 68 */
  {  2, 1}, { 42, 2}, {  8, 4}, { 21, 5}, { 11, 4}, { 12, 3}, { 31, 2}, {  2, 3},
  {  9, 2}, {  4, 0}, { 11, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 31, 0},
  { 36, 0}, {  8, 4}, { 20, 5}, { 13, 4}, {  8, 6}, {  4, 3}, {  4, 2}, {  2, 3},  /* Row 69 */
  {  2, 1}, { 43, 2}, {  8, 4}, { 21, 5}, { 11, 4}, { 12, 3}, { 32, 2}, {  2, 3},
  {  9, 2}, {  3, 0}, { 11, 4}, { 15, 5}, { 10, 4}, { 15, 6}, { 31, 0},
  { 36, 0}, {  8, 4}, { 21, 5}, { 13, 4}, {  6, 6}, {  6, 3}, {  2, 2}, {  2, 3},  /* Row 70 */
  {  2, 1}, { 44, 2}, {  8, 4}, { 22, 5}, { 11, 4}, { 12, 3}, { 32, 2}, {  2, 3},
  {  9, 2}, {  1, 0}, { 11, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 32, 0},
  { 36, 0}, {  8, 4}, { 21, 5}, { 13, 4}, {  5, 6}, {  7, 3}, {  1, 2}, {  2, 3},  /* Row 71 */
  {  2, 1}, { 45, 2}, {  8, 4}, { 22, 5}, { 11, 4}, { 12, 3}, { 33, 2}, {  2, 3},
  {  9, 2}, { 11, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 32, 0},
  { 36, 0}, {  8, 4}, { 22, 5}, { 13, 4}, {  3, 6}, { 10, 3}, {  2, 1}, { 46, 2},  /* Row 72 */
  {  8, 4}, { 23, 5}, { 11, 4}, { 12, 3}, { 33, 2}, {  2, 3}, {  7, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 33, 0},
  { 36, 0}, {  8, 4}, { 22, 5}, { 13, 4}, {  2, 6}, { 11, 3}, {  1, 1}, { 47, 2},  /* Row 73 */
  {  8, 4}, { 23, 5}, { 11, 4}, { 12, 3}, { 34, 2}, {  2, 3}, {  6, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 33, 0},
  { 36, 0}, {  8, 4}, { 23, 5}, { 13, 4}, { 12, 3}, { 48, 2}, {  8, 4}, { 24, 5},  /* Row 74 */
  { 11, 4}, { 12, 3}, { 34, 2}, {  2, 3}, {  4, 2}, { 11, 4}, { 15, 5}, { 11, 4},
  { 14, 6}, { 34, 0},
  { 36, 0}, {  8, 4}, { 23, 5}, { 13, 4}, { 13, 3}, { 47, 2}, {  8, 4}, { 24, 5},  /* Row 75 */
  { 11, 4}, { 12, 3}, { 35, 2}, {  2, 3}, {  3, 2}, { 11, 4}, { 15, 5}, { 11, 4},
  { 14, 6}, { 34, 0},
  { 36, 0}, {  8, 4}, { 24, 5}, { 13, 4}, { 12, 3}, { 47, 2}, {  8, 4}, { 25, 5},  /* Row 76 */
  { 11, 4}, { 12, 3}, { 35, 2}, {  2, 3}, {  1, 2}, { 11, 4}, { 15, 5}, { 11, 4},
  { 14, 6}, { 35, 0},
  { 36, 0}, {  8, 4}, { 24, 5}, { 13, 4}, { 12, 3}, { 47, 2}, {  8, 4}, { 25, 5},  /* Row 77 */
  { 11, 4}, { 12, 3}, { 36, 2}, {  2, 3}, { 11, 4}, { 15, 5}, { 11, 4}, { 14, 6},
  { 35, 0},
  { 36, 0}, {  8, 4}, { 25, 5}, { 13, 4}, { 12, 3}, { 46, 2}, {  8, 4}, { 26, 5},  /* Row 78 */
  { 11, 4}, { 12, 3}, { 36, 2}, { 11, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 36, 0},
  { 36, 0}, {  8, 4}, { 25, 5}, { 13, 4}, { 12, 3}, { 46, 2}, {  8, 4}, { 26, 5},  /* Row 79 */
  { 11, 4}, { 12, 3}, { 36, 2}, { 11, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 36, 0},
  { 36, 0}, {  8, 4}, { 26, 5}, { 13, 4}, { 12, 3}, { 45, 2}, {  8, 4}, { 27, 5},  /* Row 80 */
  { 11, 4}, { 12, 3}, { 34, 2}, { 11, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 37, 0},
  { 36, 0}, {  8, 4}, { 26, 5}, { 13, 4}, { 12, 3}, { 45, 2}, {  8, 4}, { 27, 5},  /* Row 81 */
  { 11, 4}, { 12, 3}, { 34, 2}, { 11, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 37, 0},
  { 36, 0}, {  8, 4}, { 27, 5}, { 13, 4}, { 12, 3}, { 44, 2}, {  8, 4}, { 28, 5},  /* Row 82 */
  { 11, 4}, { 12, 3}, { 32, 2}, { 11, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 38, 0},
  { 36, 0}, {  8, 4}, { 27, 5}, { 13, 4}, { 12, 3}, { 44, 2}, {  8, 4}, { 28, 5},  /* Row 83 */
  { 11, 4}, { 12, 3}, { 32, 2}, { 11, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 38, 0},
  { 36, 0}, {  8, 4}, { 28, 5}, { 13, 4}, { 12, 3}, { 43, 2}, {  8, 4}, { 29, 5},  /* Row 84 */
  { 11, 4}, { 12, 3}, { 30, 2}, { 11, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 39, 0},
  { 36, 0}, {  8, 4}, { 28, 5}, { 13, 4}, { 12, 3}, { 43, 2}, {  8, 4}, { 29, 5},  /* Row 85 */
  { 11, 4}, { 12, 3}, { 30, 2}, { 11, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 39, 0},
  { 36, 0}, {  8, 4}, { 29, 5}, { 13, 4}, { 12, 3}, { 42, 2}, {  8, 4}, { 14, 5},  /* Row 86 */
  {  1, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 28, 2}, { 11, 4}, { 15, 5}, { 11, 4},
  { 14, 6}, { 40, 0},
  { 36, 0}, {  8, 4}, { 29, 5}, { 13, 4}, { 12, 3}, { 42, 2}, {  8, 4}, { 14, 5},  /* Row 87 */
  {  1, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 28, 2}, { 11, 4}, { 15, 5}, { 11, 4},
  { 14, 6}, { 40, 0},
  { 36, 0}, {  8, 4}, { 30, 5}, { 13, 4}, { 12, 3}, { 41, 2}, {  8, 4}, { 14, 5},  /* Row 88 */
  {  2, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 26, 2}, { 11, 4}, { 15, 5}, { 11, 4},
  { 14, 6}, { 41, 0},
  { 36, 0}, {  8, 4}, { 30, 5}, { 13, 4}, { 12, 3}, { 41, 2}, {  8, 4}, { 14, 5},  /* Row 89 */
  {  2, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 26, 2}, { 11, 4}, { 15, 5}, { 11, 4},
  { 14, 6}, { 41, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  1, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 40, 2},  /* Row 90 */
  {  8, 4}, { 14, 5}, {  3, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 24, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 42, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  1, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 40, 2},  /* Row 91 */
  {  8, 4}, { 14, 5}, {  3, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 24, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 42, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  2, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 39, 2},  /* Row 92 */
  {  8, 4}, { 14, 5}, {  4, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 22, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 43, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  2, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 39, 2},  /* Row 93 */
  {  8, 4}, { 14, 5}, {  4, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 22, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 43, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  3, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 38, 2},  /* Row 94 */
  {  8, 4}, { 14, 5}, {  5, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 20, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 44, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  3, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 38, 2},  /* Row 95 */
  {  8, 4}, { 14, 5}, {  5, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 20, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 44, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  4, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 37, 2},  /* Row 96 */
  {  8, 4}, { 14, 5}, {  6, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 18, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 45, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  4, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 37, 2},  /* Row 97 */
  {  8, 4}, { 14, 5}, {  6, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 18, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 45, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  5, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 36, 2},  /* Row 98 */
  {  8, 4}, { 14, 5}, {  7, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 16, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, {  2, 3}, { 12, 6}, { 46, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  5, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 36, 2},  /* Row 99 */
  {  8, 4}, { 14, 5}, {  7, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 16, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, {  3, 3}, { 11, 6}, { 46, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  6, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 35, 2},  /* Row 100 */
  {  8, 4}, { 14, 5}, {  8, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 14, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, {  5, 3}, {  9, 6}, { 47, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  6, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 35, 2},  /* Row 101 */
  {  8, 4}, { 14, 5}, {  8, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 14, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, {  6, 3}, {  8, 6}, { 47, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  7, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 34, 2},  /* Row 102 */
  {  8, 4}, { 14, 5}, {  9, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 12, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, {  8, 3}, {  6, 6}, { 48, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  7, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 34, 2},  /* Row 103 */
  {  8, 4}, { 14, 5}, {  9, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 12, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, {  9, 3}, {  5, 6}, { 48, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 33, 2},  /* Row 104 */
  {  8, 4}, { 14, 5}, { 10, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 10, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 11, 3}, {  3, 6}, { 49, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 33, 2},  /* Row 105 */
  {  8, 4}, { 14, 5}, { 10, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 10, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 12, 3}, {  2, 6}, { 49, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  9, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 32, 2},  /* Row 106 */
  {  8, 4}, { 14, 5}, { 11, 4}, { 15, 5}, { 11, 4}, { 12, 3}, {  8, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 14, 3}, { 50, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  9, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 32, 2},  /* Row 107 */
  {  8, 4}, { 14, 5}, { 11, 4}, { 15, 5}, { 11, 4}, { 12, 3}, {  8, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 15, 3}, { 49, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, { 10, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 31, 2},  /* Row 108 */
  {  8, 4}, { 14, 5}, { 12, 4}, { 15, 5}, { 11, 4}, { 12, 3}, {  6, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 15, 3}, {  2, 2}, { 48, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, { 10, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 31, 2},  /* Row 109 */
  {  8, 4}, { 14, 5}, { 12, 4}, { 15, 5}, { 11, 4}, { 12, 3}, {  6, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 15, 3}, {  3, 2}, { 47, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 30, 2},  /* Row 110 */
  {  8, 4}, { 14, 5}, { 13, 4}, { 15, 5}, { 11, 4}, { 12, 3}, {  4, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 14, 3}, {  6, 2}, { 46, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 30, 2},  /* Row 111 */
  {  8, 4}, { 14, 5}, { 13, 4}, { 15, 5}, { 11, 4}, { 12, 3}, {  4, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 14, 3}, {  7, 2}, { 45, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, { 12, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 29, 2},  /* Row 112 */
  {  8, 4}, { 14, 5}, { 14, 4}, { 15, 5}, { 11, 4}, { 12, 3}, {  2, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 16, 3}, {  7, 2}, { 44, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, { 12, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 29, 2},  /* Row 113 */
  {  8, 4}, { 14, 5}, { 14, 4}, { 15, 5}, { 11, 4}, { 12, 3}, {  2, 2}, { 11, 4},
  { 15, 5}, { 11, 4}, { 14, 3}, {  1, 2}, {  2, 3}, {  7, 2}, { 43, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, { 13, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 28, 2},  /* Row 114 */
  {  8, 4}, { 14, 5}, { 15, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 11, 4}, { 15, 5},
  { 11, 4}, { 14, 3}, {  3, 2}, {  2, 3}, {  7, 2}, { 42, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, { 13, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 28, 2},  /* Row 115 */
  {  8, 4}, { 14, 5}, { 15, 4}, { 15, 5}, { 11, 4}, { 12, 3}, { 11, 4}, { 15, 5},
  { 11, 4}, { 14, 3}, {  4, 2}, {  2, 3}, {  7, 2}, { 41, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, { 14, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 27, 2},  /* Row 116 */
  {  8, 4}, { 14, 5}, { 16, 4}, { 15, 5}, { 11, 4}, { 10, 3}, { 11, 4}, { 15, 5},
  { 11, 4}, { 14, 3}, {  6, 2}, {  2, 3}, {  7, 2}, { 40, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, { 14, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 27, 2},  /* Row 117 */
  {  8, 4}, { 14, 5}, { 16, 4}, { 15, 5}, { 11, 4}, { 10, 3}, { 11, 4}, { 15, 5},
  { 11, 4}, { 14, 3}, {  7, 2}, {  2, 3}, {  7, 2}, { 39, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, { 15, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 26, 2},  /* Row 118 */
  {  8, 4}, { 14, 5}, { 17, 4}, { 15, 5}, { 11, 4}, {  8, 3}, { 11, 4}, { 15, 5},
  { 11, 4}, { 14, 3}, {  9, 2}, {  2, 3}, {  7, 2}, { 38, 0},
  { 35, 0}, {  1, 1}, {  8, 4}, { 16, 5}, { 15, 4}, { 14, 5}, { 13, 4}, { 12, 3},  /* Row 119 */
  { 26, 2}, {  8, 4}, { 14, 5}, { 17, 4}, { 15, 5}, { 11, 4}, {  8, 3}, { 11, 4},
  { 15, 5}, { 11, 4}, { 14, 3}, { 10, 2}, {  2, 3}, {  7, 2}, { 37, 0},
  { 34, 0}, {  2, 1}, {  8, 4}, { 16, 5}, { 16, 4}, { 14, 5}, { 13, 4}, { 12, 3},  /* Row 120 */
  { 25, 2}, {  8, 4}, { 14, 5}, { 18, 4}, { 15, 5}, { 11, 4}, {  6, 3}, { 11, 4},
  { 15, 5}, { 11, 4}, { 14, 3}, { 12, 2}, {  2, 3}, {  7, 2}, { 36, 0},
  { 33, 0}, {  2, 1}, {  1, 2}, {  8, 4}, { 16, 5}, { 16, 4}, { 14, 5}, { 13, 4},  /* Row 121 */
  { 12, 3}, { 25, 2}, {  8, 4}, { 14, 5}, { 18, 4}, { 15, 5}, { 11, 4}, {  6, 3},
  { 11, 4}, { 15, 5}, { 11, 4}, { 14, 3}, { 13, 2}, {  2, 3}, {  7, 2}, { 35, 0},
  { 32, 0}, {  2, 1}, {  2, 2}, {  8, 4}, { 16, 5}, { 17, 4}, { 14, 5}, { 13, 4},  /* Row 122 */
  { 12, 3}, { 24, 2}, {  8, 4}, { 14, 5}, {  8, 4}, {  1, 3}, { 10, 4}, { 15, 5},
  { 11, 4}, {  4, 3}, { 11, 4}, { 15, 5}, { 11, 4}, { 14, 3}, { 15, 2}, {  2, 3},
  {  7, 2}, { 34, 0},
  { 31, 0}, {  2, 1}, {  3, 2}, {  8, 4}, { 16, 5}, { 17, 4}, { 14, 5}, { 13, 4},  /* Row 123 */
  { 12, 3}, { 24, 2}, {  8, 4}, { 14, 5}, {  8, 4}, {  1, 3}, { 10, 4}, { 15, 5},
  { 11, 4}, {  4, 3}, { 11, 4}, { 15, 5}, { 11, 4}, { 14, 3}, { 16, 2}, {  2, 3},
  {  7, 2}, { 33, 0},
  { 30, 0}, {  2, 1}, {  4, 2}, {  8, 4}, { 16, 5}, { 18, 4}, { 14, 5}, { 13, 4},  /* Row 124 */
  { 12, 3}, { 23, 2}, {  8, 4}, { 14, 5}, {  8, 4}, {  2, 3}, { 10, 4}, { 15, 5},
  { 11, 4}, {  2, 3}, { 11, 4}, { 15, 5}, { 11, 4}, { 14, 3}, { 18, 2}, {  2, 3},
  {  7, 2}, { 32, 0},
  { 29, 0}, {  2, 1}, {  5, 2}, {  8, 4}, { 16, 5}, { 18, 4}, { 14, 5}, { 13, 4},  /* Row 125 */
  { 12, 3}, { 23, 2}, {  8, 4}, { 14, 5}, {  8, 4}, {  2, 3}, { 10, 4}, { 15, 5},
  { 11, 4}, {  2, 3}, { 11, 4}, { 15, 5}, { 11, 4}, { 14, 3}, { 19, 2}, {  2, 3},
  {  7, 2}, { 31, 0},
  { 28, 0}, {  2, 1}, {  5, 2}, {  1, 3}, {  8, 4}, { 16, 5}, { 19, 4}, { 14, 5},  /* Row 126 */
  { 13, 4}, { 12, 3}, { 22, 2}, {  8, 4}, { 14, 5}, {  8, 4}, {  3, 3}, { 10, 4},
  { 15, 5}, { 22, 4}, { 15, 5}, { 11, 4}, { 14, 3}, { 21, 2}, {  2, 3}, {  7, 2},
  { 30, 0},
  { 27, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  8, 4}, { 16, 5}, { 19, 4}, { 14, 5},  /* Row 127 */
  { 13, 4}, { 12, 3}, { 22, 2}, {  8, 4}, { 14, 5}, {  8, 4}, {  3, 3}, { 10, 4},
  { 15, 5}, { 22, 4}, { 15, 5}, { 11, 4}, { 14, 3}, { 22, 2}, {  2, 3}, {  7, 2},
  { 29, 0},
  { 26, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  1, 1}, {  8, 4}, { 16, 5}, {  8, 4},  /* Row 128 */
  {  1, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 21, 2}, {  8, 4}, { 14, 5},
  {  8, 4}, {  4, 3}, { 10, 4}, { 15, 5}, { 20, 4}, { 15, 5}, { 11, 4}, { 14, 3},
  { 24, 2}, {  2, 3}, {  7, 2}, { 28, 0},
  { 25, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, {  8, 4}, { 16, 5}, {  8, 4},  /* Row 129 */
  {  1, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 21, 2}, {  8, 4}, { 14, 5},
  {  8, 4}, {  4, 3}, { 10, 4}, { 15, 5}, { 20, 4}, { 15, 5}, { 11, 4}, { 14, 3},
  { 25, 2}, {  2, 3}, {  7, 2}, { 27, 0},
  { 24, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, {  1, 2}, {  8, 4}, { 16, 5},  /* Row 130 */
  {  8, 4}, {  2, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 20, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, {  5, 3}, { 10, 4}, { 15, 5}, { 18, 4}, { 15, 5}, { 11, 4},
  { 14, 3}, { 27, 2}, {  2, 3}, {  7, 2}, { 26, 0},
  { 23, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, {  2, 2}, {  8, 4}, { 16, 5},  /* Row 131 */
  {  8, 4}, {  2, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 20, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, {  5, 3}, { 10, 4}, { 15, 5}, { 18, 4}, { 15, 5}, { 11, 4},
  { 14, 3}, { 28, 2}, {  2, 3}, {  7, 2}, { 25, 0},
  { 22, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, {  3, 2}, {  8, 4}, { 16, 5},  /* Row 132 */
  {  8, 4}, {  3, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 19, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, {  6, 3}, { 10, 4}, { 15, 5}, { 16, 4}, { 15, 5}, { 11, 4},
  { 14, 3}, { 30, 2}, {  2, 3}, {  7, 2}, { 24, 0},
  { 21, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, {  4, 2}, {  8, 4}, { 16, 5},  /* Row 133 */
  {  8, 4}, {  3, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 19, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, {  6, 3}, { 10, 4}, { 15, 5}, { 16, 4}, { 15, 5}, { 11, 4},
  { 14, 3}, { 31, 2}, {  2, 3}, {  7, 2}, { 23, 0},
  { 20, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {  8, 4}, { 16, 5},  /* Row 134 */
  {  8, 4}, {  4, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 18, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, {  7, 3}, { 10, 4}, { 15, 5}, { 14, 4}, { 15, 5}, { 11, 4},
  { 14, 3}, { 33, 2}, {  2, 3}, {  7, 2}, { 22, 0},
  { 19, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, {  6, 2}, {  8, 4}, { 16, 5},  /* Row 135 */
  {  8, 4}, {  4, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 18, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, {  7, 3}, { 10, 4}, { 15, 5}, { 14, 4}, { 15, 5}, { 11, 4},
  { 14, 3}, { 34, 2}, {  2, 3}, {  7, 2}, { 21, 0},
  { 18, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, {  7, 2}, {  8, 4}, { 16, 5},  /* Row 136 */
  {  8, 4}, {  5, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 17, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, {  8, 3}, { 10, 4}, { 15, 5}, { 12, 4}, { 15, 5}, { 11, 4},
  { 14, 3}, { 36, 2}, {  2, 3}, {  7, 2}, { 20, 0},
  { 17, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, {  8, 2}, {  8, 4}, { 16, 5},  /* Row 137 */
  {  8, 4}, {  5, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 17, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, {  8, 3}, { 10, 4}, { 15, 5}, { 12, 4}, { 15, 5}, { 11, 4},
  { 14, 3}, { 37, 2}, {  2, 3}, {  7, 2}, { 19, 0},
  { 16, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, {  9, 2}, {  8, 4}, { 16, 5},  /* Row 138 */
  {  8, 4}, {  6, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 16, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, {  9, 3}, { 10, 4}, { 15, 5}, { 10, 4}, { 15, 5}, { 11, 4},
  { 14, 3}, { 39, 2}, {  2, 3}, {  7, 2}, { 18, 0},
  { 15, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, { 10, 2}, {  8, 4}, { 16, 5},  /* Row 139 */
  {  8, 4}, {  6, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 16, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, {  9, 3}, { 10, 4}, { 15, 5}, { 10, 4}, { 15, 5}, { 11, 4},
  { 14, 3}, { 40, 2}, {  2, 3}, {  7, 2}, { 17, 0},
  { 14, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, { 11, 2}, {  8, 4}, { 16, 5},  /* Row 140 */
  {  8, 4}, {  7, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 15, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, { 10, 3}, { 10, 4}, { 15, 5}, {  8, 4}, { 15, 5}, { 11, 4},
  { 14, 3}, { 42, 2}, {  2, 3}, {  7, 2}, { 16, 0},
  { 13, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, { 12, 2}, {  8, 4}, { 16, 5},  /* Row 141 */
  {  8, 4}, {  7, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 15, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, { 10, 3}, { 10, 4}, { 15, 5}, {  8, 4}, { 15, 5}, { 11, 4},
  { 14, 3}, { 43, 2}, {  2, 3}, {  7, 2}, { 15, 0},
  { 12, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, { 13, 2}, {  8, 4}, { 16, 5},  /* Row 142 */
  {  8, 4}, {  8, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 14, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, { 11, 3}, { 10, 4}, { 15, 5}, {  6, 4}, { 15, 5}, { 11, 4},
  { 14, 3}, { 45, 2}, {  2, 3}, {  7, 2}, { 14, 0},
  { 11, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, { 14, 2}, {  8, 4}, { 16, 5},  /* Row 143 */
  {  8, 4}, {  8, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 14, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, { 11, 3}, { 10, 4}, { 15, 5}, {  6, 4}, { 15, 5}, { 11, 4},
  { 14, 3}, { 46, 2}, {  2, 3}, {  7, 2}, { 13, 0},
  { 10, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, { 15, 2}, {  8, 4}, { 16, 5},  /* Row 144 */
  {  8, 4}, {  9, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 13, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, { 12, 3}, { 10, 4}, { 15, 5}, {  4, 4}, { 15, 5}, { 11, 4},
  { 14, 3}, { 48, 2}, {  2, 3}, {  7, 2}, { 12, 0},
  {  9, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, { 16, 2}, {  8, 4}, { 16, 5},  /* Row 145 */
  {  8, 4}, {  9, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 13, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, { 12, 3}, { 10, 4}, { 15, 5}, {  4, 4}, { 15, 5}, { 11, 4},
  { 14, 3}, { 49, 2}, {  2, 3}, {  7, 2}, { 11, 0},
  {  8, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, { 17, 2}, {  8, 4}, { 16, 5},  /* Row 146 */
  {  8, 4}, { 10, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 12, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, { 13, 3}, { 10, 4}, { 15, 5}, {  2, 4}, { 15, 5}, { 11, 4},
  { 14, 3}, { 51, 2}, {  2, 3}, {  7, 2}, { 10, 0},
  {  7, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, { 18, 2}, {  8, 4}, { 16, 5},  /* Row 147 */
  {  8, 4}, { 10, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 12, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, { 13, 3}, { 10, 4}, { 15, 5}, {  2, 4}, { 15, 5}, { 11, 4},
  { 14, 3}, { 52, 2}, {  2, 3}, {  7, 2}, {  9, 0},
  {  6, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, { 19, 2}, {  8, 4}, { 16, 5},  /* Row 148 */
  {  8, 4}, { 11, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 11, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, { 14, 3}, { 10, 4}, { 30, 5}, { 11, 4}, { 14, 3}, { 54, 2},
  {  2, 3}, {  7, 2}, {  8, 0},
  {  5, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, { 20, 2}, {  8, 4}, { 16, 5},  /* Row 149 */
  {  8, 4}, { 11, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 11, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, { 14, 3}, { 10, 4}, { 30, 5}, { 11, 4}, { 14, 3}, { 55, 2},
  {  2, 3}, {  7, 2}, {  7, 0},
  {  4, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, { 21, 2}, {  8, 4}, { 16, 5},  /* Row 150 */
  {  8, 4}, { 12, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 10, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, { 14, 3}, {  1, 2}, { 10, 4}, { 28, 5}, { 11, 4}, { 14, 3},
  { 57, 2}, {  2, 3}, {  7, 2}, {  6, 0},
  {  3, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, { 22, 2}, {  8, 4}, { 16, 5},  /* Row 151 */
  {  8, 4}, { 12, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, { 10, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, { 14, 3}, {  1, 2}, { 10, 4}, { 28, 5}, { 11, 4}, { 14, 3},
  { 58, 2}, {  2, 3}, {  7, 2}, {  5, 0},
  {  2, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, { 23, 2}, {  8, 4}, { 16, 5},  /* Row 152 */
  {  8, 4}, { 13, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  9, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, { 14, 3}, {  2, 2}, { 10, 4}, { 26, 5}, { 11, 4}, { 14, 3},
  { 60, 2}, {  2, 3}, {  7, 2}, {  4, 0},
  {  1, 0}, {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, { 24, 2}, {  8, 4}, { 16, 5},  /* Row 153 */
  {  8, 4}, { 13, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  9, 2}, {  8, 4},
  { 14, 5}, {  8, 4}, { 14, 3}, {  2, 2}, { 10, 4}, { 26, 5}, { 11, 4}, { 14, 3},
  { 61, 2}, {  2, 3}, {  7, 2}, {  3, 0},
  {  2, 1}, {  5, 2}, {  2, 3}, {  2, 1}, { 25, 2}, {  8, 4}, { 16, 5}, {  8, 4},  /* Row 154 */
  { 14, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  8, 2}, {  8, 4}, { 14, 5},
  {  8, 4}, { 14, 3}, {  3, 2}, { 10, 4}, { 24, 5}, { 11, 4}, { 14, 3}, { 63, 2},
  {  2, 3}, {  7, 2}, {  2, 0},
  {  1, 1}, {  5, 2}, {  2, 3}, {  2, 1}, { 26, 2}, {  8, 4}, { 16, 5}, {  8, 4},  /* Row 155 */
  { 14, 3}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  8, 2}, {  8, 4}, { 14, 5},
  {  8, 4}, { 14, 3}, {  3, 2}, { 10, 4}, { 24, 5}, { 11, 4}, { 14, 3}, { 64, 2},
  {  2, 3}, {  7, 2}, {  1, 0},
  {  5, 2}, {  2, 3}, {  2, 1}, { 27, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 156 */
  {  1, 2}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  7, 2}, {  8, 4}, { 14, 5},
  {  8, 4}, { 14, 3}, {  4, 2}, { 10, 4}, { 22, 5}, { 11, 4}, { 14, 3}, { 66, 2},
  {  2, 3}, {  7, 2},
  {  4, 2}, {  2, 3}, {  2, 1}, { 28, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 157 */
  {  1, 2}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  7, 2}, {  8, 4}, { 14, 5},
  {  8, 4}, { 14, 3}, {  4, 2}, { 10, 4}, { 22, 5}, { 11, 4}, { 14, 3}, { 67, 2},
  {  2, 3}, {  6, 2},
  {  3, 2}, {  2, 3}, {  2, 1}, { 29, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 158 */
  {  2, 2}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  6, 2}, {  8, 4}, { 14, 5},
  {  8, 4}, { 14, 3}, {  5, 2}, { 10, 4}, { 20, 5}, { 11, 4}, { 14, 3}, { 69, 2},
  {  2, 3}, {  5, 2},
  {  3, 2}, {  2, 3}, {  1, 1}, { 30, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 159 */
  {  2, 2}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  6, 2}, {  8, 4}, { 14, 5},
  {  8, 4}, { 14, 3}, {  5, 2}, { 10, 4}, { 20, 5}, { 11, 4}, { 14, 3}, { 70, 2},
  {  2, 3}, {  4, 2},
  {  4, 2}, {  2, 3}, { 30, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, {  3, 2},  /* Row 160 */
  { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  5, 2}, {  8, 4}, { 14, 5}, {  8, 4},
  { 14, 3}, {  5, 2}, { 11, 4}, { 19, 5}, { 10, 4}, { 14, 3}, { 72, 2}, {  3, 3},
  {  2, 2},
  {  5, 2}, {  2, 3}, { 29, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, {  3, 2},  /* Row 161 */
  { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  5, 2}, {  8, 4}, { 14, 5}, {  8, 4},
  { 14, 3}, {  5, 2}, { 11, 4}, { 18, 5}, { 11, 4}, { 14, 3}, { 72, 2}, {  3, 3},
  {  2, 2},
  {  6, 2}, {  2, 3}, { 28, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, {  4, 2},  /* Row 162 */
  { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  4, 2}, {  8, 4}, { 14, 5}, {  8, 4},
  { 14, 3}, {  4, 2}, { 11, 4}, { 19, 5}, { 10, 4}, { 14, 3}, { 72, 2}, {  2, 3},
  {  2, 1}, {  2, 2},
  {  7, 2}, {  2, 3}, { 27, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, {  4, 2},  /* Row 163 */
  { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  4, 2}, {  8, 4}, { 14, 5}, {  8, 4},
  { 14, 3}, {  4, 2}, { 11, 4}, { 18, 5}, { 11, 4}, { 14, 3}, { 71, 2}, {  2, 3},
  {  2, 1}, {  3, 2},
  {  8, 2}, {  2, 3}, { 26, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, {  5, 2},  /* Row 164 */
  { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  3, 2}, {  8, 4}, { 14, 5}, {  8, 4},
  { 14, 3}, {  3, 2}, { 11, 4}, { 20, 5}, { 11, 4}, { 13, 3}, { 70, 2}, {  2, 3},
  {  2, 1}, {  4, 2},
  {  9, 2}, {  2, 3}, { 25, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, {  5, 2},  /* Row 165 */
  { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  3, 2}, {  8, 4}, { 14, 5}, {  8, 4},
  { 14, 3}, {  3, 2}, { 11, 4}, { 20, 5}, { 11, 4}, { 13, 3}, { 69, 2}, {  2, 3},
  {  2, 1}, {  5, 2},
  {  1, 0}, {  9, 2}, {  2, 3}, { 24, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 166 */
  {  6, 2}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  2, 2}, {  8, 4}, { 14, 5},
  {  8, 4}, { 14, 3}, {  2, 2}, { 11, 4}, { 22, 5}, { 11, 4}, { 13, 3}, { 67, 2},
  {  2, 3}, {  2, 1}, {  5, 2}, {  1, 0},
  {  2, 0}, {  9, 2}, {  2, 3}, { 23, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 167 */
  {  6, 2}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  2, 2}, {  8, 4}, { 14, 5},
  {  8, 4}, { 14, 3}, {  2, 2}, { 11, 4}, { 22, 5}, { 11, 4}, { 13, 3}, { 66, 2},
  {  2, 3}, {  2, 1}, {  5, 2}, {  2, 0},
  {  3, 0}, {  9, 2}, {  2, 3}, { 22, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 168 */
  {  7, 2}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  1, 2}, {  8, 4}, { 14, 5},
  {  8, 4}, { 14, 3}, {  1, 2}, { 11, 4}, { 24, 5}, { 11, 4}, { 13, 3}, { 64, 2},
  {  2, 3}, {  2, 1}, {  5, 2}, {  3, 0},
  {  4, 0}, {  9, 2}, {  2, 3}, { 21, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 169 */
  {  7, 2}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  1, 2}, {  8, 4}, { 14, 5},
  {  8, 4}, { 14, 3}, {  1, 2}, { 11, 4}, { 24, 5}, { 11, 4}, { 13, 3}, { 63, 2},
  {  2, 3}, {  2, 1}, {  5, 2}, {  4, 0},
  {  5, 0}, {  9, 2}, {  2, 3}, { 20, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 170 */
  {  8, 2}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  { 14, 3}, { 11, 4}, { 26, 5}, { 11, 4}, { 13, 3}, { 61, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, {  5, 0},
  {  6, 0}, {  9, 2}, {  2, 3}, { 19, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 171 */
  {  8, 2}, { 11, 4}, { 14, 5}, { 13, 4}, { 12, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  { 14, 3}, { 11, 4}, { 26, 5}, { 11, 4}, { 13, 3}, { 60, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, {  6, 0},
  {  7, 0}, {  9, 2}, {  2, 3}, { 18, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 172 */
  {  9, 2}, { 11, 4}, { 14, 5}, { 13, 4}, { 11, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  { 13, 3}, { 11, 4}, { 28, 5}, { 11, 4}, { 13, 3}, { 58, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, {  7, 0},
  {  8, 0}, {  9, 2}, {  2, 3}, { 17, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 173 */
  {  9, 2}, { 11, 4}, { 14, 5}, { 13, 4}, { 11, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  { 13, 3}, { 11, 4}, { 28, 5}, { 11, 4}, { 13, 3}, { 57, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, {  8, 0},
  {  9, 0}, {  9, 2}, {  2, 3}, { 16, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 174 */
  { 10, 2}, { 11, 4}, { 14, 5}, { 13, 4}, { 10, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  { 12, 3}, { 11, 4}, { 30, 5}, { 11, 4}, { 13, 3}, { 55, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, {  9, 0},
  { 10, 0}, {  9, 2}, {  2, 3}, { 15, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 175 */
  { 10, 2}, { 11, 4}, { 14, 5}, { 13, 4}, { 10, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  { 12, 3}, { 11, 4}, { 30, 5}, { 11, 4}, { 13, 3}, { 54, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 10, 0},
  { 11, 0}, {  9, 2}, {  2, 3}, { 14, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 176 */
  { 11, 2}, { 11, 4}, { 14, 5}, { 13, 4}, {  9, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  { 11, 3}, { 11, 4}, { 32, 5}, { 11, 4}, { 13, 3}, { 52, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 11, 0},
  { 12, 0}, {  9, 2}, {  2, 3}, { 13, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 177 */
  { 11, 2}, { 11, 4}, { 14, 5}, { 13, 4}, {  9, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  { 11, 3}, { 11, 4}, { 32, 5}, { 11, 4}, { 13, 3}, { 51, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 12, 0},
  { 13, 0}, {  9, 2}, {  2, 3}, { 12, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 178 */
  { 12, 2}, { 11, 4}, { 14, 5}, { 13, 4}, {  8, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  { 10, 3}, { 11, 4}, { 16, 5}, {  3, 4}, { 15, 5}, { 11, 4}, { 13, 3}, { 49, 2},
  {  2, 3}, {  2, 1}, {  5, 2}, { 13, 0},
  { 14, 0}, {  9, 2}, {  2, 3}, { 11, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 179 */
  { 12, 2}, { 11, 4}, { 14, 5}, { 13, 4}, {  8, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  { 10, 3}, { 11, 4}, { 16, 5}, {  3, 4}, { 15, 5}, { 11, 4}, { 13, 3}, { 48, 2},
  {  2, 3}, {  2, 1}, {  5, 2}, { 14, 0},
  { 15, 0}, {  9, 2}, {  2, 3}, { 10, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 180 */
  { 13, 2}, { 11, 4}, { 14, 5}, { 13, 4}, {  7, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  {  9, 3}, { 11, 4}, { 16, 5}, {  5, 4}, { 15, 5}, { 11, 4}, { 13, 3}, { 46, 2},
  {  2, 3}, {  2, 1}, {  5, 2}, { 15, 0},
  { 16, 0}, {  9, 2}, {  2, 3}, {  9, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 181 */
  { 13, 2}, { 11, 4}, { 14, 5}, { 13, 4}, {  7, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  {  9, 3}, { 11, 4}, { 16, 5}, {  5, 4}, { 15, 5}, { 11, 4}, { 13, 3}, { 45, 2},
  {  2, 3}, {  2, 1}, {  5, 2}, { 16, 0},
  { 17, 0}, {  9, 2}, {  2, 3}, {  8, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 182 */
  { 14, 2}, { 11, 4}, { 14, 5}, { 13, 4}, {  6, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  {  8, 3}, { 11, 4}, { 16, 5}, {  7, 4}, { 15, 5}, { 11, 4}, { 13, 3}, { 43, 2},
  {  2, 3}, {  2, 1}, {  5, 2}, { 17, 0},
  { 18, 0}, {  9, 2}, {  2, 3}, {  7, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 183 */
  { 14, 2}, { 11, 4}, { 14, 5}, { 13, 4}, {  6, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  {  8, 3}, { 11, 4}, { 16, 5}, {  7, 4}, { 15, 5}, { 11, 4}, { 13, 3}, { 42, 2},
  {  2, 3}, {  2, 1}, {  5, 2}, { 18, 0},
  { 19, 0}, {  9, 2}, {  2, 3}, {  6, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 184 */
  { 15, 2}, { 11, 4}, { 14, 5}, { 13, 4}, {  5, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  {  7, 3}, { 11, 4}, { 16, 5}, {  9, 4}, { 15, 5}, { 11, 4}, { 13, 3}, { 40, 2},
  {  2, 3}, {  2, 1}, {  5, 2}, { 19, 0},
  { 20, 0}, {  9, 2}, {  2, 3}, {  5, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 185 */
  { 15, 2}, { 11, 4}, { 14, 5}, { 13, 4}, {  5, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  {  7, 3}, { 11, 4}, { 16, 5}, {  9, 4}, { 15, 5}, { 11, 4}, { 13, 3}, { 39, 2},
  {  2, 3}, {  2, 1}, {  5, 2}, { 20, 0},
  { 21, 0}, {  9, 2}, {  2, 3}, {  4, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 186 */
  { 16, 2}, { 11, 4}, { 14, 5}, { 13, 4}, {  4, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  {  6, 3}, { 11, 4}, { 16, 5}, { 11, 4}, { 15, 5}, { 11, 4}, { 13, 3}, { 37, 2},
  {  2, 3}, {  2, 1}, {  5, 2}, { 21, 0},
  { 22, 0}, {  9, 2}, {  2, 3}, {  3, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 187 */
  { 16, 2}, { 11, 4}, { 14, 5}, { 13, 4}, {  4, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  {  6, 3}, { 11, 4}, { 16, 5}, { 11, 4}, { 15, 5}, { 11, 4}, { 13, 3}, { 36, 2},
  {  2, 3}, {  2, 1}, {  5, 2}, { 22, 0},
  { 23, 0}, {  9, 2}, {  2, 3}, {  2, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 188 */
  { 17, 2}, { 11, 4}, { 14, 5}, { 13, 4}, {  3, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  {  5, 3}, { 11, 4}, { 16, 5}, { 13, 4}, { 15, 5}, { 11, 4}, { 13, 3}, { 34, 2},
  {  2, 3}, {  2, 1}, {  5, 2}, { 23, 0},
  { 24, 0}, {  9, 2}, {  2, 3}, {  1, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3},  /* Row 189 */
  { 17, 2}, { 11, 4}, { 14, 5}, { 13, 4}, {  3, 3}, {  8, 4}, { 14, 5}, {  8, 4},
  {  5, 3}, { 11, 4}, { 16, 5}, { 13, 4}, { 15, 5}, { 11, 4}, { 13, 3}, { 33, 2},
  {  2, 3}, {  2, 1}, {  5, 2}, { 24, 0},
  { 25, 0}, {  9, 2}, {  2, 3}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 18, 2},  /* Row 190 */
  { 11, 4}, { 14, 5}, { 13, 4}, {  2, 3}, {  8, 4}, { 14, 5}, {  8, 4}, {  4, 3},
  { 11, 4}, { 16, 5}, { 15, 4}, { 15, 5}, { 11, 4}, { 13, 3}, { 31, 2}, {  2, 3},
  {  2, 1}, {  5, 2}, { 25, 0},
  { 26, 0}, {  9, 2}, {  1, 3}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 18, 2},  /* Row 191 */
  { 11, 4}, { 14, 5}, { 13, 4}, {  2, 3}, {  8, 4}, { 14, 5}, {  8, 4}, {  4, 3},
  { 11, 4}, { 16, 5}, { 15, 4}, { 15, 5}, { 11, 4}, { 13, 3}, { 30, 2}, {  2, 3},
  {  2, 1}, {  5, 2}, { 26, 0},
  { 27, 0}, {  9, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 19, 2}, { 11, 4},  /* Row 192 */
  { 14, 5}, { 13, 4}, {  1, 3}, {  8, 4}, { 14, 5}, {  8, 4}, {  3, 3}, { 11, 4},
  { 16, 5}, { 17, 4}, { 15, 5}, { 11, 4}, { 13, 3}, { 28, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 27, 0},
  { 28, 0}, {  8, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 19, 2}, { 11, 4},  /* Row 193 */
  { 14, 5}, { 13, 4}, {  1, 3}, {  8, 4}, { 14, 5}, {  8, 4}, {  3, 3}, { 11, 4},
  { 16, 5}, { 17, 4}, { 15, 5}, { 11, 4}, { 13, 3}, { 27, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 28, 0},
  { 29, 0}, {  7, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 20, 2}, { 11, 4},  /* Row 194 */
  { 14, 5}, { 21, 4}, { 14, 5}, {  8, 4}, {  2, 3}, { 11, 4}, { 16, 5}, { 19, 4},
  { 15, 5}, { 11, 4}, { 13, 3}, { 25, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 29, 0},
  { 30, 0}, {  6, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 20, 2}, { 11, 4},  /* Row 195 */
  { 14, 5}, { 21, 4}, { 14, 5}, {  8, 4}, {  2, 3}, { 11, 4}, { 16, 5}, { 19, 4},
  { 15, 5}, { 11, 4}, { 13, 3}, { 24, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 30, 0},
  { 31, 0}, {  5, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 21, 2}, { 11, 4},  /* Row 196 */
  { 14, 5}, { 20, 4}, { 14, 5}, {  8, 4}, {  1, 3}, { 11, 4}, { 16, 5}, { 11, 4},
  {  1, 3}, {  9, 4}, { 15, 5}, { 11, 4}, { 13, 3}, { 22, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 31, 0},
  { 32, 0}, {  4, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 21, 2}, { 11, 4},  /* Row 197 */
  { 14, 5}, { 20, 4}, { 14, 5}, {  8, 4}, {  1, 3}, { 11, 4}, { 16, 5}, { 11, 4},
  {  1, 3}, {  9, 4}, { 15, 5}, { 11, 4}, { 13, 3}, { 21, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 32, 0},
  { 33, 0}, {  3, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 22, 2}, { 11, 4},  /* Row 198 */
  { 14, 5}, { 19, 4}, { 14, 5}, { 19, 4}, { 16, 5}, { 11, 4}, {  3, 3}, {  9, 4},
  { 15, 5}, { 11, 4}, { 13, 3}, { 19, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 33, 0},
  { 34, 0}, {  2, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 22, 2}, { 11, 4},  /* Row 199 */
  { 14, 5}, { 19, 4}, { 14, 5}, { 19, 4}, { 16, 5}, { 11, 4}, {  3, 3}, {  9, 4},
  { 15, 5}, { 11, 4}, { 13, 3}, { 18, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 34, 0},
  { 35, 0}, {  1, 2}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 23, 2}, { 11, 4},  /* Row 200 */
  { 14, 5}, { 18, 4}, { 14, 5}, { 18, 4}, { 16, 5}, { 11, 4}, {  5, 3}, {  9, 4},
  { 15, 5}, { 11, 4}, { 13, 3}, { 16, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 35, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 23, 2}, { 11, 4}, { 14, 5},  /* Row 201 */
  { 18, 4}, { 14, 5}, { 18, 4}, { 16, 5}, { 11, 4}, {  5, 3}, {  9, 4}, { 15, 5},
  { 11, 4}, { 13, 3}, { 15, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 36, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 24, 2}, { 11, 4}, { 14, 5},  /* Row 202 */
  { 17, 4}, { 14, 5}, { 17, 4}, { 16, 5}, { 11, 4}, {  7, 3}, {  9, 4}, { 15, 5},
  { 11, 4}, { 13, 3}, { 13, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 37, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 24, 2}, { 11, 4}, { 14, 5},  /* Row 203 */
  { 17, 4}, { 14, 5}, { 17, 4}, { 16, 5}, { 11, 4}, {  7, 3}, {  9, 4}, { 15, 5},
  { 11, 4}, { 13, 3}, { 12, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 38, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 25, 2}, { 11, 4}, { 14, 5},  /* Row 204 */
  { 16, 4}, { 14, 5}, { 16, 4}, { 16, 5}, { 11, 4}, {  9, 3}, {  9, 4}, { 15, 5},
  { 11, 4}, { 13, 3}, { 10, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 39, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 25, 2}, { 11, 4}, { 14, 5},  /* Row 205 */
  { 16, 4}, { 14, 5}, { 16, 4}, { 16, 5}, { 11, 4}, {  9, 3}, {  9, 4}, { 15, 5},
  { 11, 4}, { 13, 3}, {  9, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 40, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 26, 2}, { 11, 4}, { 14, 5},  /* Row 206 */
  { 15, 4}, { 14, 5}, { 15, 4}, { 16, 5}, { 11, 4}, { 11, 3}, {  9, 4}, { 15, 5},
  { 11, 4}, { 13, 3}, {  7, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 41, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 26, 2}, { 11, 4}, { 14, 5},  /* Row 207 */
  { 15, 4}, { 14, 5}, { 15, 4}, { 16, 5}, { 11, 4}, { 11, 3}, {  9, 4}, { 15, 5},
  { 11, 4}, { 13, 3}, {  6, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 42, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 27, 2}, { 11, 4}, { 14, 5},  /* Row 208 */
  { 14, 4}, { 14, 5}, { 14, 4}, { 16, 5}, { 11, 4}, { 13, 3}, {  9, 4}, { 15, 5},
  { 11, 4}, { 13, 3}, {  4, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 43, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 27, 2}, { 11, 4}, { 14, 5},  /* Row 209 */
  { 14, 4}, { 14, 5}, { 14, 4}, { 16, 5}, { 11, 4}, { 13, 3}, {  9, 4}, { 15, 5},
  { 11, 4}, { 13, 3}, {  3, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 44, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 28, 2}, { 11, 4}, { 14, 5},  /* Row 210 */
  { 13, 4}, { 14, 5}, { 13, 4}, { 16, 5}, { 11, 4}, { 15, 3}, {  9, 4}, { 15, 5},
  { 11, 4}, { 13, 3}, {  1, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 45, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 28, 2}, { 11, 4}, { 14, 5},  /* Row 211 */
  { 13, 4}, { 14, 5}, { 13, 4}, { 16, 5}, { 11, 4}, { 15, 3}, {  9, 4}, { 15, 5},
  { 11, 4}, { 15, 3}, {  2, 1}, {  5, 2}, { 46, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 29, 2}, { 11, 4}, { 14, 5},  /* Row 212 */
  { 12, 4}, { 14, 5}, { 12, 4}, { 16, 5}, { 11, 4}, { 15, 3}, {  2, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, { 13, 3}, {  2, 1}, {  5, 2}, { 47, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 29, 2}, { 11, 4}, { 14, 5},  /* Row 213 */
  { 12, 4}, { 14, 5}, { 12, 4}, { 16, 5}, { 11, 4}, { 15, 3}, {  2, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, { 14, 3}, {  5, 2}, { 48, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 30, 2}, { 11, 4}, { 14, 5},  /* Row 214 */
  { 11, 4}, { 14, 5}, { 11, 4}, { 16, 5}, { 11, 4}, { 15, 3}, {  4, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, { 14, 3}, {  3, 2}, { 49, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 30, 2}, { 11, 4}, { 14, 5},  /* Row 215 */
  { 11, 4}, { 14, 5}, { 11, 4}, { 16, 5}, { 11, 4}, { 15, 3}, {  4, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, { 14, 3}, {  2, 2}, { 50, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 31, 2}, { 11, 4}, { 14, 5},  /* Row 216 */
  { 10, 4}, { 14, 5}, { 10, 4}, { 16, 5}, { 11, 4}, { 15, 3}, {  6, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, { 12, 3}, {  1, 6}, {  1, 2}, { 51, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 31, 2}, { 11, 4}, { 14, 5},  /* Row 217 */
  { 10, 4}, { 14, 5}, { 10, 4}, { 16, 5}, { 11, 4}, { 15, 3}, {  6, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, { 11, 3}, {  3, 6}, { 51, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 32, 2}, { 11, 4}, { 14, 5},  /* Row 218 */
  {  9, 4}, { 14, 5}, {  9, 4}, { 16, 5}, { 11, 4}, { 15, 3}, {  8, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, {  9, 3}, {  5, 6}, { 50, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 32, 2}, { 11, 4}, { 14, 5},  /* Row 219 */
  {  9, 4}, { 14, 5}, {  9, 4}, { 16, 5}, { 11, 4}, { 15, 3}, {  8, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, {  8, 3}, {  6, 6}, { 50, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 33, 2}, { 11, 4}, { 14, 5},  /* Row 220 */
  {  8, 4}, { 14, 5}, {  8, 4}, { 16, 5}, { 11, 4}, { 15, 3}, { 10, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, {  6, 3}, {  8, 6}, { 49, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 33, 2}, { 11, 4}, { 14, 5},  /* Row 221 */
  {  8, 4}, { 14, 5}, {  8, 4}, { 16, 5}, { 11, 4}, { 15, 3}, { 10, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, {  5, 3}, {  9, 6}, { 49, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 34, 2}, { 11, 4}, { 14, 5},  /* Row 222 */
  {  7, 4}, { 14, 5}, {  7, 4}, { 16, 5}, { 11, 4}, { 15, 3}, { 12, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, {  3, 3}, { 11, 6}, { 48, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 34, 2}, { 11, 4}, { 14, 5},  /* Row 223 */
  {  7, 4}, { 14, 5}, {  7, 4}, { 16, 5}, { 11, 4}, { 15, 3}, { 12, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, {  2, 3}, { 12, 6}, { 48, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 35, 2}, { 11, 4}, { 14, 5},  /* Row 224 */
  {  6, 4}, { 14, 5}, {  6, 4}, { 16, 5}, { 11, 4}, { 15, 3}, { 14, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 47, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 35, 2}, { 11, 4}, { 14, 5},  /* Row 225 */
  {  6, 4}, { 14, 5}, {  6, 4}, { 16, 5}, { 11, 4}, { 15, 3}, { 14, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 47, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 36, 2}, { 11, 4}, { 14, 5},  /* Row 226 */
  {  5, 4}, { 14, 5}, {  5, 4}, { 16, 5}, { 11, 4}, { 15, 3}, { 16, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 46, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 36, 2}, { 11, 4}, { 14, 5},  /* Row 227 */
  {  5, 4}, { 14, 5}, {  5, 4}, { 16, 5}, { 11, 4}, { 15, 3}, { 16, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 46, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 37, 2}, { 11, 4}, { 14, 5},  /* Row 228 */
  {  4, 4}, { 14, 5}, {  4, 4}, { 16, 5}, { 11, 4}, { 15, 3}, { 18, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 45, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 37, 2}, { 11, 4}, { 14, 5},  /* Row 229 */
  {  4, 4}, { 14, 5}, {  4, 4}, { 16, 5}, { 11, 4}, { 15, 3}, { 18, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 45, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 38, 2}, { 11, 4}, { 14, 5},  /* Row 230 */
  {  3, 4}, { 14, 5}, {  3, 4}, { 16, 5}, { 11, 4}, { 15, 3}, { 20, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 44, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 38, 2}, { 11, 4}, { 14, 5},  /* Row 231 */
  {  3, 4}, { 14, 5}, {  3, 4}, { 16, 5}, { 11, 4}, { 15, 3}, { 20, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 44, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 39, 2}, { 11, 4}, { 14, 5},  /* Row 232 */
  {  2, 4}, { 14, 5}, {  2, 4}, { 16, 5}, { 11, 4}, { 15, 3}, { 22, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 43, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 3}, { 39, 2}, { 11, 4}, { 14, 5},  /* Row 233 */
  {  2, 4}, { 14, 5}, {  2, 4}, { 16, 5}, { 11, 4}, { 15, 3}, { 22, 2}, {  9, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 43, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, {  1, 6}, { 13, 3}, { 40, 2}, { 11, 4},  /* Row 234 */
  { 14, 5}, {  1, 4}, { 14, 5}, {  1, 4}, { 16, 5}, { 11, 4}, { 15, 3}, { 24, 2},
  {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 42, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, {  2, 6}, { 12, 3}, { 40, 2}, { 11, 4},  /* Row 235 */
  { 14, 5}, {  1, 4}, { 14, 5}, {  1, 4}, { 16, 5}, { 11, 4}, { 15, 3}, { 24, 2},
  {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 42, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, {  3, 6}, { 11, 3}, { 41, 2}, { 11, 4},  /* Row 236 */
  { 44, 5}, { 11, 4}, { 15, 3}, { 26, 2}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6},
  { 41, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, {  4, 6}, { 11, 3}, { 40, 2}, { 11, 4},  /* Row 237 */
  { 44, 5}, { 11, 4}, { 15, 3}, { 26, 2}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6},
  { 41, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, {  5, 6}, { 11, 3}, { 40, 2}, { 11, 4},  /* Row 238 */
  { 42, 5}, { 11, 4}, { 15, 3}, { 28, 2}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6},
  { 40, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, {  6, 6}, {  8, 3}, {  1, 2}, {  2, 3},  /* Row 239 */
  { 39, 2}, { 11, 4}, { 42, 5}, { 11, 4}, { 15, 3}, { 28, 2}, {  9, 4}, { 15, 5},
  { 11, 4}, { 14, 6}, { 40, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, {  7, 6}, {  7, 3}, {  2, 2}, {  2, 3},  /* Row 240 */
  { 39, 2}, { 11, 4}, { 40, 5}, { 11, 4}, { 15, 3}, { 30, 2}, {  9, 4}, { 15, 5},
  { 11, 4}, { 14, 6}, { 39, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, {  8, 6}, {  6, 3}, {  3, 2}, {  2, 3},  /* Row 241 */
  { 38, 2}, { 11, 4}, { 40, 5}, { 11, 4}, { 15, 3}, { 29, 2}, {  1, 3}, {  9, 4},
  { 15, 5}, { 11, 4}, { 14, 6}, { 39, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, {  9, 6}, {  5, 3}, {  4, 2}, {  2, 3},  /* Row 242 */
  { 38, 2}, { 11, 4}, { 38, 5}, { 11, 4}, { 15, 3}, { 29, 2}, {  2, 3}, {  1, 1},
  {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 38, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 10, 6}, {  4, 3}, {  5, 2}, {  2, 3},  /* Row 243 */
  { 37, 2}, { 11, 4}, { 38, 5}, { 11, 4}, { 15, 3}, { 28, 2}, {  2, 3}, {  2, 1},
  {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 38, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 11, 6}, {  3, 3}, {  6, 2}, {  2, 3},  /* Row 244 */
  { 37, 2}, { 11, 4}, { 36, 5}, { 11, 4}, { 15, 3}, { 28, 2}, {  2, 3}, {  2, 1},
  {  2, 2}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 37, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 12, 6}, {  2, 3}, {  7, 2}, {  2, 3},  /* Row 245 */
  { 36, 2}, { 11, 4}, { 36, 5}, { 11, 4}, { 15, 3}, { 27, 2}, {  2, 3}, {  2, 1},
  {  3, 2}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 37, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 13, 6}, {  1, 3}, {  8, 2}, {  2, 3},  /* Row 246 */
  { 36, 2}, { 11, 4}, { 34, 5}, { 11, 4}, { 15, 3}, { 27, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 36, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, {  9, 2}, {  2, 3}, { 35, 2},  /* Row 247 */
  { 11, 4}, { 34, 5}, { 11, 4}, { 15, 3}, { 26, 2}, {  2, 3}, {  2, 1}, {  5, 2},
  {  1, 0}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 36, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, {  1, 0}, {  9, 2}, {  2, 3},  /* Row 248 */
  { 35, 2}, { 11, 4}, { 32, 5}, { 11, 4}, { 15, 3}, { 26, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, {  2, 0}, { 10, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 35, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, {  2, 0}, {  9, 2}, {  2, 3},  /* Row 249 */
  { 34, 2}, { 11, 4}, { 32, 5}, { 11, 4}, { 15, 3}, { 25, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, {  4, 0}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 35, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, {  3, 0}, {  9, 2}, {  2, 3},  /* Row 250 */
  { 34, 2}, { 11, 4}, { 30, 5}, { 11, 4}, { 15, 3}, { 25, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, {  6, 0}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 34, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, {  4, 0}, {  9, 2}, {  2, 3},  /* Row 251 */
  { 33, 2}, { 11, 4}, { 30, 5}, { 11, 4}, { 15, 3}, { 24, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, {  7, 0}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 34, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, {  5, 0}, {  9, 2}, {  2, 3},  /* Row 252 */
  { 33, 2}, { 11, 4}, { 28, 5}, { 11, 4}, { 15, 3}, { 24, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, {  9, 0}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 33, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, {  6, 0}, {  9, 2}, {  2, 3},  /* Row 253 */
  { 32, 2}, { 11, 4}, { 28, 5}, { 11, 4}, { 15, 3}, { 23, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 10, 0}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 33, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, {  7, 0}, {  9, 2}, {  2, 3},  /* Row 254 */
  { 32, 2}, { 11, 4}, { 26, 5}, { 11, 4}, { 15, 3}, { 23, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 12, 0}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 32, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, {  8, 0}, {  9, 2}, {  2, 3},  /* Row 255 */
  { 31, 2}, { 11, 4}, { 26, 5}, { 11, 4}, { 15, 3}, { 22, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 13, 0}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 32, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, {  9, 0}, {  9, 2}, {  2, 3},  /* Row 256 */
  { 31, 2}, { 11, 4}, { 24, 5}, { 11, 4}, { 15, 3}, { 22, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 15, 0}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 31, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 10, 0}, {  9, 2}, {  2, 3},  /* Row 257 */
  { 30, 2}, { 11, 4}, { 24, 5}, { 11, 4}, { 15, 3}, { 21, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 16, 0}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 31, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 11, 0}, {  9, 2}, {  2, 3},  /* Row 258 */
  { 30, 2}, { 11, 4}, { 22, 5}, { 11, 4}, { 15, 3}, { 21, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 18, 0}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 30, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 12, 0}, {  9, 2}, {  2, 3},  /* Row 259 */
  { 29, 2}, { 11, 4}, { 22, 5}, { 11, 4}, { 15, 3}, { 20, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 19, 0}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 30, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 13, 0}, {  9, 2}, {  2, 3},  /* Row 260 */
  { 29, 2}, { 11, 4}, { 20, 5}, { 11, 4}, { 15, 3}, { 20, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 21, 0}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 29, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 14, 0}, {  9, 2}, {  2, 3},  /* Row 261 */
  { 28, 2}, { 11, 4}, { 20, 5}, { 11, 4}, { 15, 3}, { 19, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 22, 0}, {  9, 4}, { 15, 5}, { 11, 4}, { 14, 6}, { 29, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 15, 0}, {  9, 2}, {  2, 3},  /* Row 262 */
  { 28, 2}, { 11, 4}, { 18, 5}, { 11, 4}, { 15, 3}, { 19, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 24, 0}, {  9, 4}, { 15, 5}, { 10, 4}, { 14, 6}, { 29, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 16, 0}, {  9, 2}, {  2, 3},  /* Row 263 */
  { 27, 2}, { 11, 4}, { 18, 5}, { 11, 4}, { 15, 3}, { 18, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 25, 0}, {  9, 4}, { 15, 5}, { 10, 4}, { 14, 6}, { 29, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 17, 0}, {  9, 2}, {  2, 3},  /* Row 264 */
  { 27, 2}, { 11, 4}, { 16, 5}, { 11, 4}, { 15, 3}, { 18, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 27, 0}, {  9, 4}, { 14, 5}, { 10, 4}, { 15, 6}, { 28, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 18, 0}, {  9, 2}, {  2, 3},  /* Row 265 */
  { 26, 2}, { 11, 4}, { 16, 5}, { 11, 4}, { 15, 3}, { 17, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 28, 0}, {  9, 4}, { 14, 5}, { 10, 4}, { 15, 6}, { 28, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 19, 0}, {  9, 2}, {  2, 3},  /* Row 266 */
  { 26, 2}, { 11, 4}, { 14, 5}, { 11, 4}, { 15, 3}, { 17, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 30, 0}, {  9, 4}, { 13, 5}, { 10, 4}, { 16, 6}, { 27, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 20, 0}, {  9, 2}, {  2, 3},  /* Row 267 */
  { 25, 2}, { 11, 4}, { 14, 5}, { 11, 4}, { 15, 3}, { 16, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 31, 0}, {  9, 4}, { 13, 5}, {  9, 4}, { 17, 6}, { 27, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 21, 0}, {  9, 2}, {  2, 3},  /* Row 268 */
  { 25, 2}, { 11, 4}, { 12, 5}, { 11, 4}, { 15, 3}, { 16, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 33, 0}, {  9, 4}, { 11, 5}, { 10, 4}, { 17, 6}, { 27, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 22, 0}, {  9, 2}, {  2, 3},  /* Row 269 */
  { 24, 2}, { 12, 4}, { 10, 5}, { 12, 4}, { 15, 3}, { 15, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 34, 0}, {  9, 4}, { 11, 5}, { 10, 4}, { 17, 6}, { 27, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 23, 0}, {  9, 2}, {  2, 3},  /* Row 270 */
  { 24, 2}, { 12, 4}, {  8, 5}, { 12, 4}, { 15, 3}, { 15, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 36, 0}, {  9, 4}, {  8, 5}, { 11, 4}, { 18, 6}, { 27, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 24, 0}, {  9, 2}, {  2, 3},  /* Row 271 */
  { 23, 2}, { 14, 4}, {  4, 5}, { 14, 4}, { 15, 3}, { 14, 2}, {  2, 3}, {  2, 1},
  {  5, 2}, { 37, 0}, { 11, 4}, {  4, 5}, { 13, 4}, { 18, 6}, { 27, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 25, 0}, {  9, 2}, {  2, 3},  /* Row 272 */
  { 23, 2}, { 30, 4}, { 15, 3}, { 14, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 39, 0},
  { 26, 4}, { 19, 6}, { 27, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 26, 0}, {  9, 2}, {  2, 3},  /* Row 273 */
  { 22, 2}, { 30, 4}, { 15, 3}, { 13, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 40, 0},
  { 26, 4}, { 18, 6}, { 28, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 27, 0}, {  9, 2}, {  2, 3},  /* Row 274 */
  { 22, 2}, { 28, 4}, { 15, 3}, { 13, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 42, 0},
  { 24, 4}, { 19, 6}, { 28, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 28, 0}, {  9, 2}, {  2, 3},  /* Row 275 */
  { 21, 2}, { 28, 4}, { 15, 3}, { 12, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 43, 0},
  { 23, 4}, { 20, 6}, { 28, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 29, 0}, {  9, 2}, {  2, 3},  /* Row 276 */
  { 21, 2}, { 26, 4}, { 15, 3}, { 12, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 45, 0},
  { 21, 4}, { 20, 6}, { 29, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 30, 0}, {  9, 2}, {  2, 3},  /* Row 277 */
  { 20, 2}, { 26, 4}, { 15, 3}, { 11, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 46, 0},
  { 20, 4}, { 21, 6}, { 29, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 31, 0}, {  9, 2}, {  2, 3},  /* Row 278 */
  { 20, 2}, { 24, 4}, { 15, 3}, { 11, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 48, 0},
  { 18, 4}, { 21, 6}, { 30, 0},
  { 36, 0}, {  8, 4}, { 16, 5}, {  8, 4}, { 14, 6}, { 32, 0}, {  9, 2}, {  2, 3},  /* Row 279 */
  { 20, 2}, { 22, 4}, { 16, 3}, { 10, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 51, 0},
  { 14, 4}, { 23, 6}, { 30, 0},
  { 50, 0}, { 32, 6}, { 33, 0}, {  9, 2}, {  2, 3}, { 20, 2}, { 20, 4}, { 16, 3},  /* Row 280 */
  { 10, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 55, 0}, {  8, 4}, { 25, 6}, { 31, 0},
  { 50, 0}, { 32, 6}, { 34, 0}, {  9, 2}, {  2, 3}, { 21, 2}, { 16, 4}, { 18, 3},  /* Row 281 */
  {  9, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 61, 0}, { 28, 6}, { 31, 0},
  { 50, 0}, { 32, 6}, { 35, 0}, {  9, 2}, {  2, 3}, { 22, 2}, { 12, 4}, { 19, 3},  /* Row 282 */
  {  9, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 62, 0}, { 27, 6}, { 32, 0},
  { 50, 0}, { 32, 6}, { 36, 0}, {  9, 2}, {  2, 3}, { 23, 2}, {  8, 4}, { 20, 3},  /* Row 283 */
  {  9, 2}, {  2, 3}, {  2, 1}, {  5, 2}, { 64, 0}, { 26, 6}, { 32, 0},
  { 50, 0}, { 32, 6}, { 37, 0}, {  9, 2}, {  2, 3}, { 24, 2}, { 25, 3}, {  9, 2},  /* Row 284 */
  {  2, 3}, {  2, 1}, {  5, 2}, { 66, 0}, { 23, 6}, { 34, 0},
  { 50, 0}, { 32, 6}, { 38, 0}, {  9, 2}, {  2, 3}, { 25, 2}, { 21, 3}, { 10, 2},  /* Row 285 */
  {  2, 3}, {  2, 1}, {  5, 2}, { 67, 0}, { 23, 6}, { 34, 0},
  { 50, 0}, { 32, 6}, { 39, 0}, {  9, 2}, {  2, 3}, { 26, 2}, { 17, 3}, { 11, 2},  /* Row 286 */
  {  2, 3}, {  2, 1}, {  5, 2}, { 69, 0}, { 20, 6}, { 36, 0},
  { 50, 0}, { 32, 6}, { 40, 0}, {  9, 2}, {  2, 3}, { 27, 2}, { 13, 3}, { 12, 2},  /* Row 287 */
  {  2, 3}, {  2, 1}, {  5, 2}, { 70, 0}, { 20, 6}, { 36, 0},
  { 50, 0}, { 32, 6}, { 41, 0}, {  9, 2}, {  2, 3}, { 50, 2}, {  2, 3}, {  2, 1},  /* Row 288 */
  {  5, 2}, { 72, 0}, { 17, 6}, { 38, 0},
  { 50, 0}, { 32, 6}, { 42, 0}, {  9, 2}, {  2, 3}, { 48, 2}, {  2, 3}, {  2, 1},  /* Row 289 */
  {  5, 2}, { 74, 0}, { 14, 6}, { 40, 0},
  { 50, 0}, { 32, 6}, { 43, 0}, {  9, 2}, {  2, 3}, { 46, 2}, {  2, 3}, {  2, 1},  /* Row 290 */
  {  5, 2}, {129, 0},
  { 50, 0}, { 32, 6}, { 44, 0}, {  9, 2}, {  2, 3}, { 44, 2}, {  2, 3}, {  2, 1},  /* Row 291 */
  {  5, 2}, {130, 0},
  { 50, 0}, { 32, 6}, { 45, 0}, {  9, 2}, {  2, 3}, { 42, 2}, {  2, 3}, {  2, 1},  /* Row 292 */
  {  5, 2}, {131, 0},
  { 50, 0}, { 32, 6}, { 46, 0}, {  9, 2}, {  2, 3}, { 40, 2}, {  2, 3}, {  2, 1},  /* Row 293 */
  {  5, 2}, {132, 0},
  {129, 0}, {  9, 2}, {  2, 3}, { 38, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {133, 0},  /* Row 294 */
  {130, 0}, {  9, 2}, {  2, 3}, { 36, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {134, 0},  /* Row 295 */
  {131, 0}, {  9, 2}, {  2, 3}, { 34, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {135, 0},  /* Row 296 */
  {132, 0}, {  9, 2}, {  2, 3}, { 32, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {136, 0},  /* Row 297 */
  {133, 0}, {  9, 2}, {  2, 3}, { 30, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {137, 0},  /* Row 298 */
  {134, 0}, {  9, 2}, {  2, 3}, { 28, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {138, 0},  /* Row 299 */
  {135, 0}, {  9, 2}, {  2, 3}, { 26, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {139, 0},  /* Row 300 */
  {136, 0}, {  9, 2}, {  2, 3}, { 24, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {140, 0},  /* Row 301 */
  {137, 0}, {  9, 2}, {  2, 3}, { 22, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {141, 0},  /* Row 302 */
  {138, 0}, {  9, 2}, {  2, 3}, { 20, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {142, 0},  /* Row 303 */
  {139, 0}, {  9, 2}, {  2, 3}, { 18, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {143, 0},  /* Row 304 */
  {140, 0}, {  9, 2}, {  2, 3}, { 16, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {144, 0},  /* Row 305 */
  {141, 0}, {  9, 2}, {  2, 3}, { 14, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {145, 0},  /* Row 306 */
  {142, 0}, {  9, 2}, {  2, 3}, { 12, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {146, 0},  /* Row 307 */
  {143, 0}, {  9, 2}, {  2, 3}, { 10, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {147, 0},  /* Row 308 */
  {144, 0}, {  9, 2}, {  2, 3}, {  8, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {148, 0},  /* Row 309 */
  {145, 0}, {  9, 2}, {  2, 3}, {  6, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {149, 0},  /* Row 310 */
  {146, 0}, {  9, 2}, {  2, 3}, {  4, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {150, 0},  /* Row 311 */
  {147, 0}, {  9, 2}, {  2, 3}, {  2, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {151, 0},  /* Row 312 */
  {148, 0}, {  9, 2}, {  4, 3}, {  2, 1}, {  5, 2}, {152, 0},                    /* Row 313 */
  {149, 0}, {  9, 2}, {  2, 3}, {  2, 1}, {  5, 2}, {153, 0},                    /* Row 314 */
  {150, 0}, {  9, 2}, {  2, 1}, {  5, 2}, {154, 0},                              /* Row 315 */
  {151, 0}, {  7, 2}, {  2, 1}, {  5, 2}, {155, 0},                              /* Row 316 */
  {152, 0}, { 12, 2}, {156, 0},                                                  /* Row 317 */
  {153, 0}, { 10, 2}, {157, 0},                                                  /* Row 318 */
  {154, 0}, {  8, 2}, {158, 0},                                                  /* Row 319 */
};

/****************************************************************************
 * Public Bitmap Structure Definitions
 ****************************************************************************/

const struct SRlePaletteBitmap NXWidgets::g_nuttxBitmap320x320 =
{
  CONFIG_NXWIDGETS_BPP,  // bpp    - Bits per pixel
  CONFIG_NXWIDGETS_FMT,  // fmt    - Color format
  BITMAP_NLUTCODES,      // nlut   - Number of colors in the lLook-Up Table (LUT)
  BITMAP_NCOLUMNS,       // width  - Width in pixels
  BITMAP_NROWS,          // height - Height in rows
  {                      // lut    - Pointer to the beginning of the Look-Up Table (LUT)
    g_nuttxLut,          //          Index 0: Unselected LUT
    g_nuttxLut,          //          Index 1: Selected LUT
  },
  g_nuttxRleEntries      // data   - Pointer to the beginning of the RLE data
};
