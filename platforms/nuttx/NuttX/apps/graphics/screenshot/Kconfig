#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config GRAPHICS_SCREENSHOT
	tristate "TIFF screenshot utility"
	default n
	depends on TIFF && NX
	---help---
		Generate a NX screenshot utility based on the TIFF library.

if GRAP<PERSON><PERSON>S_SCREENSHOT

config SCREENSHOT_WIDTH
	int "Screenshot width (in pixels)"
	default 320
	---help---
		The width of the screenshot in pixels/columns.

config SCREENSHOT_HEIGHT
	int "Screenshot height (in lines)"
	default 240
	---help---
		The height of the screenshot in pixels/rows.

config SCREENSHOT_FORMAT
	int "Screenshot color format"
	default 9
	---help---
		See include/nuttx/video/fb.h for a list of color formats.  The default
		value of 9 corresponds to FB_FMT_RGB16_565

endif
