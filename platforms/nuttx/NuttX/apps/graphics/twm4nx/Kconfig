#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

menuconfig GRAPHICS_TWM4NX
	tristate "Minimal Tom's Window Manager (TWM) for NuttX (Twm4Nx)"
	default n
	select NX_RAMBACKED
	select NXWIDGETS
	depends on NX && HAVE_CXX
	---help---
		Enable Tom's Window Manager (TWM) for NuttX (Twm4Nx).

		Use of this window manager requires keyboard and mouse input.
		Output is through the NX server.

if GRAPHICS_TWM4NX

comment "Twm4Nx Core Configuration"

config TWM4NX_PROGNAME
	string "Twm4Nx program name"
	default "twm4nx"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config TWM4NX_PRIORITY
	int "Twm4Nx task priority"
	default 100

config TWM4NX_STACKSIZE
	int "Twm4Nx stack size"
	default DEFAULT_TASK_STACKSIZE

config TWM4NX_REVMAJOR
	string "Twm4Nx major version number"
	default "0"

config TWM4NX_REVMINOR
	string "Twm4Nx minor version number"
	default "0"

config TWM4NX_ARCHINIT
	bool "Have architecture-specific initialization"
	default n
	select BOARDCTL
	depends on !NSH_ARCHINIT
	---help---
		Set if your board provides architecture specific initialization
		via the board-interface function boardctl().  The boardctl()
		function will be called early in Twm4Nx initialization to allow
		board logic to do such things as configure MMC/SD slots.

config TWM4NX_NETINIT
	bool "Network initialization"
	default y
	depends on NET
	select NETUTILS_NETINIT
	depends on !NSH_NETINIT
	---help---
		This option enables/disables all network initialization in Twm4Nx.

config TWM4NX_VNCSERVER
	bool "Use VNC Server"
	default y
	depends on VNCSERVER
	select TWM4NX_NOKEYBOARD
	select TWM4NX_NOMOUSE
	---help---
		If selected, then keyboard and positional input will come from the
		VNC server.  In this case all other input settings are ignored.

config TWM4NX_NOKEYBOARD
	bool "Disable keyboard"
	default n
	---help---
		Normally you would never disable keyboard input.  That would make
		using many Twm4Nx applications impossible.  However, this setting
		is sometimes useful for debugging Twm4Nx bringup in a simpler
		environment (it is also used if VNC is selected)

config TWM4NX_NOMOUSE
	bool "Disable mouse/touchscreen"
	default n
	---help---
		Normally you would never disable mouse input.  That would make using
		Twm4Nx impossible.  However, this setting is sometimes useful for
		debugging Twm4Nx bring-up in a simpler environment (it is also used
		if VNC is selected)

choice
	prompt "Position device"
	default TWM4NX_MOUSE
	depends on !TWM4NX_NOMOUSE

config TWM4NX_MOUSE
	bool "Mouse"
	select NX_SWCURSOR

config TWM4NX_TOUCHSCREEN
	bool "Touchscreen"

endchoice # Position device

config TWM4NX_MOUSE_DEVPATH
	string "Path to input device"
	default "/dev/mouse0" if TWM4NX_MOUSE
	default "/dev/input0" if TWM4NX_TOUCHSCREEN

config TWM4NX_KEYBOARD_DEVPATH
	string "Patch to keyboard input device"
	default "/dev/kbd0"
	depends on !TWM4NX_NOKEYBOARD

choice
	prompt "Twm4Nx Theme"
	default TWM4NX_CLASSIC

config TWM4NX_CLASSIC
	bool "Classic"
	---help---
		Strong bordered windows with dark primary colors.  Reminiscent of
		Windows 98.

config TWM4NX_CONTEMPORARY
	bool "Contemporary"
	---help---
		Border-less windows in pastel shades for a more contemporary look.

endchoice # Twm4Nx Theme

config TWM4NX_ICONMGR_NCOLUMNS
	int "Icon Manager columns"
	default 4
	---help---
		The number of buttons in one row of the Icon Manager.

config TWM4NX_DEBUG
	bool "Force debug output"
	default n
	depends on DEBUG_FEATURES
	---help---
		This option will force graphics debug output for Twm4Nx even if
		graphics debug output is not enabled.  This is useful for very low
		level debug of the Twm4Nx logic without interfering output from
		other graphics components.

		NOTE: The level of this Twm4Nx debug is still controlled by the
		basic debug output settings CONFIG_DEBUG_INFO, CONFIG_DEBUG_WARN,
		and CONFIG_DEBUG_ERROR (but not the corresponding
		CONFIG_DEBUG_GRAPHICS_* settings)

source "$APPSDIR/graphics/twm4nx/apps/Kconfig"

endif # GRAPHICS_TWM4NX
