#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

comment "Twm4Nx Applications"

config TWM4NX_CALIBRATION
	bool "Touchscreen"
	default y
	depends on TWM4NX_TOUCHSCREEN
	---help---
		Enable support for the touchscreen calibration display.

if TWM4NX_CALIBRATION

config TW4NX_STARTUP_CALIB
	bool "Calibrate on startup"
	default n
	---help---
		Bring up the calibration display automatically on startup.  This
		option is necessary if no stored calibration data is provided and
		the window manager is not usable without calibration.  Otherwise,
		calibration can be performed later via the Twm4Nx main menu.

endif # TWM4NX_CALIBRATION

config TWM4NX_NXTERM
	bool "NxTerm Window"
	default y
	depends on NXTERM && NSH_LIBRARY
	---help---
		Enable support for the NxTerm window which provides a text window
		in which you can interact with NSH.

if TWM4NX_NXTERM
endif # TWM4NX_NXTERM

config TWM4NX_CLOCK
	bool "Retro segment LCD clock"
	default y
	select GRAPHICS_SLCD
	---help---
		Enable support for the LCD clock window based on the retro segment
		LCD driver of apps/graphics/slcd.

if TWM4NX_CLOCK

config TWM4NX_CLOCK_HEIGHT
	int "LCD clock height"
	default 64
	---help---
		Fixed height of the LCD clock.  The clock window is not re-sizeable.

endif # TWM4NX_CLOCK
