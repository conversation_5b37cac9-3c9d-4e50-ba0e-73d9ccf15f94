#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config PDCURSES_WIDE
	bool "Wide character support"
	default n
	depends on EXPERIMENTAL

config PDCURSES_FORCE_UTF8
	bool "Force UTF8 character set"
	default n
	depends on PDCURSES_WIDE

config PDCURSES_CHTYPE_LONG
	bool "32-bit chtype"
	default n

config PDCURSES_DEBUG
	bool "Enable pdcurses debug output"
	default n

config PDCURSES_PANEL_DEBUG
	bool "Enable special debug output for panels"
	default n

config PDCURSES_MULTITHREAD
	bool "Enable code for running pdcurses from multiple threads (removes global variables)."
	default n
	---help---
		The base pdcurses code uses global and function static variables,
		preventing multiple tasks from using pdcurses in a FLAT model.
		This option collects all those variables into a thread specific
		dynamically allocated context, enabling multiple tasks to use
		pdcurses concurrently at the cost of a slight execution speed
		penalty for performing per task context lookup.

config PDCURSES_MULTITHREAD_HASH
	bool "Use PID HASH lookup for thread specific pdcurses context data."
	depends on PDCURSES_MULTITHREAD
	default n
	---help---
		Without this option, the per task pdcurses logic uses a linked list
		of allocated contexts, keeping RAM usage to a minimum at the expense
		of execution time for semaphore locking / unlocking and list traversal.
		This option enables a PID hash lookup option which eliminates the
		need for this extra logic by using a statically allocated array of
		context pointers whose size is dictated by the MAX_TASKS config variable.

		Enabling this option allows simple hash calculation and array lookup at
		the expense of extra RAM usage for the static array of context pointers.

config PDCURSES_SHORT_ALTCHARSET
	bool "Allow alternate character set with short 16-bit chtype"
	depends on !PDCURSES_CHTYPE_LONG && !PDCURSES_FORCE_UTF8
	default y
	---help---
		The alternate character set includes graphic line drawing characters
		used for drawing box borders.  When using 16-bit chtype, the base
		pdcurses does not define a bit for A_ALTCHARSET.  This option uses
		the MSB of the 8-bit character code as A_ALTCHARSET to enable line
		drawing characters.

		Enabling this means limiting the usable normal character set to the
		base 7-bit ASCII characters, however for most VT-100 / ANSI emulators,
		the upper bits are either not defined or not critical.
