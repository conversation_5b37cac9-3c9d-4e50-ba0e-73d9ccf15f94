############################################################################
# apps/graphics/pdcurs34/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

CSRCS += pdc_addch.c pdc_addchstr.c pdc_addstr.c pdc_attr.c pdc_beep.c
CSRCS += pdc_bkgd.c pdc_border.c pdc_clear.c pdc_color.c pdc_debug.c
CSRCS += pdc_delch.c pdc_deleteln.c pdc_getch.c pdc_getstr.c pdc_getyx.c
CSRCS += pdc_inch.c pdc_inchstr.c pdc_initscr.c pdc_inopts.c pdc_insch.c
CSRCS += pdc_insstr.c pdc_instr.c pdc_kernel.c pdc_keyname.c pdc_mouse.c
CSRCS += pdc_move.c pdc_outopts.c pdc_overlay.c pdc_pad.c pdc_panel.c
CSRCS += pdc_printw.c pdc_refresh.c pdc_scanw.c pdc_scrdump.c pdc_scroll.c
CSRCS += pdc_slk.c pdc_termattr.c pdc_terminfo.c pdc_touch.c pdc_util.c
CSRCS += pdc_window.c

DEPPATH += --dep-path pdcurses
VPATH += :pdcurses
