#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

menuconfig GRAPHICS_PDCURSES
	bool "pdcurses Text User Interface (TUI)"
	default n
	select NXFONTS
	select NXFONTS_PACKEDMSFIRST
	select LCD_PACKEDMSFIRST if LCD
	---help---
		Enable support for the pdcurses Text User Interface (TUI) library.

if GRAPHICS_PDCURSES
source "$APPSDIR/graphics/pdcurs34/pdcurses/Kconfig"
source "$APPSDIR/graphics/pdcurs34/nuttx/Kconfig"
endif # GRAPHICS_PDCURSES
