############################################################################
# apps/graphics/ft80x/Makefile
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

include $(APPDIR)/Make.defs

# FTDI/BridgeTek FT80x library
CSRCS  = ft80x_dl.c ft80x_ramg.c ft80x_ramdl.c ft80x_ramcmd.c
CSRCS += ft80x_coproc.c ft80x_touch.c ft80x_audio.c ft80x_backlight.c
CSRCS += ft80x_gpio.c ft80x_regs.c

include $(APPDIR)/Application.mk
