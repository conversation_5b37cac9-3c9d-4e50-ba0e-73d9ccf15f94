#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

menu "NxWidgets"

config NXWIDGETS
	bool "Enable NxWidgets"
	default n
	depends on NX && HAVE_CXX
	---help---
		Enable support for NxWidgets

if NXWIDGETS

menu "NX Server/Device Configuration"

config NXWIDGETS_FLICKERFREE
	bool "Enable Flicker Reduction Logic"
	default y if NX_LCDDRIVER
	default n if !NX_LCDDRIVER
	---help---
		Because of their performance an in the manner in which they are
		updated, LCDs may be prone to "flicker" in the displays when Widgets
		are updated.  Often more complex (and slower) options are available
		to reduce the flicker.  Enabling this option will enabled those
		lower-performance flicker-reductions measures where-ever they may
		be available.

config NXWIDGET_SERVERINIT
	bool "Start server"
	default y
	---help---
		If this option is selected, then the NxWidgets::CNxServer class will
		bring up the NX server when it is initialized.  If this option is
		not selected then the NxWidgets::CNxServer constructor
		initialization logic will assume that the NX server has been started
		by other, external logic and will simply attempt to connect to the
		server.

config NXWIDGETS_CLIENTPRIO
	int "NX Client Priority"
	default 100
	---help---
		The thread that calls CNxServer::connect() will be re-prioritized to
		this priority. Default: 100

config NXWIDGETS_LISTENERPRIO
	int "NX Listener Priority"
	default 100
	---help---
		Priority of the NX event listener thread.  Default: 100

config NXWIDGETS_LISTENERSTACK
	int "NX Listener Stack Size"
	default DEFAULT_TASK_STACKSIZE
	---help---
		NX listener thread stack size (in multi-user mode).  Default 2048

config NXWIDGET_EVENTWAIT
	bool "Event Waiting"
	default n
	---help---
		Build in support for external window event, modal loop management
		logic.  This includes methods to wait for windows events to occur
		so that looping logic can sleep until something interesting happens
		with the window.

endmenu # NX Server/Device Configuration

menu "NXWidget Configuration"

config NXWIDGETS_BPP
	int "BPP"
	---help---
		Supported bits-per-pixel {8, 16, 24, 32}.  Default: The smallest
		BPP configuration supported by NX.

config NXWIDGETS_GREYSCALE
	bool "Greyscale (vs RGB)"
	default n
	---help---
		Select to use a greyscale vs RGB color image.  This option is ignored if
		CONFIG_NXWIDGETS_BPP > 8

config NXWIDGETS_SIZEOFCHAR
	int "Size of a character (1 or 2 bytes)"
	range 1 2
	---help---
		Size of character {1 or 2 bytes}.  Default Determined by
		NXWIDGETS_SIZEOFCHAR

comment "NXWidget Default Values"

config NXWIDGETS_SYSTEM_CUSTOM_FONTID
	bool "Use a Custom Default Font"
	default n
	---help---
		Set to override the system default font id (NXFONT_DEFAULT).

if NXWIDGETS_SYSTEM_CUSTOM_FONTID

config NXWIDGETS_DEFAULT_FONTID
	int "Default Font ID"
	default 0
	---help---
		Use this default NxWidgets font ID instead of the system font ID
		(NXFONT_DEFAULT).  Default: 0

endif # NXWIDGETS_SYSTEM_CUSTOM_FONTID

config NXWIDGETS_TNXARRAY_INITIALSIZE
	int "Initial Size of Dynamic Arrays"
	default 16
	---help---
		Default dynamic array size (in entries).  Default: 16

config NXWIDGETS_TNXARRAY_SIZEINCREMENT
	int "Dyanamic Array Reallocation Size Increment"
	default 8
	---help---
		Default dynamic array reallocation increment (in entries).  Default: 8

config NXWIDGETS_CUSTOM_FILLCOLORS
	bool "Custom Default Fill Colors"
	default n
	---help---
		Select custom default colors for the widget background.  If defined,
		the hexadecimal values for all filled colors must be provided
		(there are no default colors because the hexadecimal representation
		of the default colors depend on the pixel depth). Default: n

if NXWIDGETS_CUSTOM_FILLCOLORS

config NXWIDGETS_DEFAULT_BACKGROUNDCOLOR
	hex "Default Normal Background Color"
	---help---
		Normal background color.  Default: RGB(148,189,215)

config NXWIDGETS_DEFAULT_SELECTEDBACKGROUNDCOLOR
	hex "Default Selected Background Color"
	---help---
		Default selected background color. Default: RGB(206,227,241)

config NXWIDGETS_DEFAULT_HIGHLIGHTCOLOR
	hex "Default Highlight Color"
	---help---
		Highlight color.  Currently this color is only used in clist
		boxes, progress bars, and slider grips.  Default: RGB(192,192,192)

endif # NXWIDGETS_CUSTOM_FILLCOLORS

config NXWIDGETS_CUSTOM_EDGECOLORS
	bool "Custom Default Edge Colors"
	default n
	---help---
		Select custom default colors for the widget edges.  If defined,
		then hexadecimal values for all edge colors must be provided
		(there are no default colors because the hexadecimal representation
		of the default colors depend on the pixel depth). Default: n.

if NXWIDGETS_CUSTOM_EDGECOLORS

config NXWIDGETS_DEFAULT_SHINEEDGECOLOR
	hex "Default Shiny Edge Color"
	---help---
		Shiny side boarder color. Default: RGB(248,248,248)

config NXWIDGETS_DEFAULT_SHADOWEDGECOLOR
	hex "Default Shadow Edge Color"
	---help---
		Shadowed side border color. Default: RGB(35,58,73)

endif # NXWIDGETS_CUSTOM_EDGECOLORS

config NXWIDGETS_CUSTOM_TEXTCOLORS
	bool "Custom Default Text colors"
	default n
	---help---
		Select custom colors for the widget text.  If defined, then
		hexadecimal values for all text colors must be provided
		(there are no default colors because the hexadecimal representation
		of the default colors depend on the pixel depth). Default: n.

if NXWIDGETS_CUSTOM_TEXTCOLORS

config NXWIDGETS_DEFAULT_DISABLEDTEXTCOLOR
	hex "Default Disabled Text Color"
	---help---
		Text color on a disabled widget: Default: RGB(192,192,192)

config NXWIDGETS_DEFAULT_ENABLEDTEXTCOLOR
	hex "Default Enabled Text Color"
	---help---
		Text color on a enabled widget. Default: RGB(248,248,248)

config NXWIDGETS_DEFAULT_SELECTEDTEXTCOLOR
	hex "Default Selected Text Color"
	---help---
		Text color on a selected widget. Default: RGB(0,0,0)

config NXWIDGETS_DEFAULT_FONTCOLOR
	hex "Default Default Font Color"
	---help---
		Default font color. Default: RGB(255,255,255)

endif # NXWIDGETS_CUSTOM_TEXTCOLORS

config NXWIDGETS_TRANSPARENT_COLOR
	hex "Transparent Color"
	default 0x0
	---help---
		Transparent color. Default: RGB(0,0,0)

comment "Keypad behavior"

config NXWIDGETS_FIRST_REPEAT_TIME
	int "First Repeat Time"
	default 500
	---help---
		Time taken before a key starts repeating (in milliseconds).  Default: 500

config NXWIDGETS_CONTINUE_REPEAT_TIME
	int "Continue Repeat Time"
	default 200
	---help---
		Time taken before a repeating key repeats again (in milliseconds).
		Default: 200

config NXWIDGETS_DOUBLECLICK_TIME
	int "Double Click Time"
	default 350
	---help---
		Left button release-press time for double click (in milliseconds).
		Default: 350

config NXWIDGETS_KBDBUFFER_SIZE
	int "Keyboard Buffer Size"
	default 16
	---help---
		Size of incoming character buffer, i.e., the maximum number of
		characters that can be entered between NX polling cycles without
		losing data.

config NXWIDGETS_CURSORCONTROL_SIZE
	int "Cursor Control Buffer Size"
	default 4
	---help---
		Size of incoming cursor control buffer, i.e., the maximum number
		of cursor controls that can between entered by NX polling cycles
		without losing data.  Default: 4

endmenu # NxWidgets Configuration
endif # NxWidgets
endmenu # NxWidgets
