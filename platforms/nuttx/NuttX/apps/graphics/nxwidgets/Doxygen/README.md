# Graphics / `nxwidgets` NXWidgets / Doxygen

This directory contains the documentation automatically generated by Doxygen.

## Contents

- Installing the necessary packages in Ubuntu
- Generating documentation
- References

## Installing the Necessary Packages in Ubuntu

1. Install the following packages.

   ```bash
   $ sudo aptitude install doxygen doxygen-doc doxygen-gui dot2tex graphviz
   ```

2. (Optional) Install Doxygen from the latest sourcode.

   The Ubuntu package is outdated. The newer the version of Doxygen, the better
   the documentation looks.

   Place yourself in some temporary folder where you can download the source,
   and run [1]:

   ```bash
   $ svn co https://doxygen.svn.sourceforge.net/svnroot/doxygen/trunk doxygen-svn
   $ cd doxygen-svn
   $ ./configure
   $ make
   $ make install
   ```

## Generating Documentation

Two ways described here:

1. Use the provided `gendoc.sh` script.

   ```bash
   trunk/NXWidgets/Doxygen/gendoc.sh
   ```

   The script only needs the argument to the absolute path where to place the
   generated documentation. I.e.:

   ```bash
   $ cd /path/to/nuttx/trunk/NXWidgets/Doxygen/
   $ mkdir doc
   $ ./gendoc.sh $PWD/doc
   ```

2. Using the `Doxyfile` directly:

   The file `Doxyfile` contains the configuration of the Doxygen settings for
   the run, edit only if necessary.

   To generate the documentation type:

   ```bash
   $ cd /path/to/nuttx/trunk/NXWidgets/Doxygen/
   $ doxygen Doxyfile
   ```

## References

[1] http://www.stack.nl/~dimitri/doxygen/download.html
