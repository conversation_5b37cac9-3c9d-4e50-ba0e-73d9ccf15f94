#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config TESTING_NXFFS
	tristate "NXFFS file system example"
	default n
	---help---
		Enable the NXFFS file system example

if TESTING_NXFFS

config TESTING_NXFFS_ARCHINIT
	bool "Architecture-specific initialization"
	default n
	---help---
		The default is to use the RAM MTD device at drivers/mtd/rammtd.c.
		But an architecture-specific MTD driver can be used instead by
		defining TESTING_NXFFS_ARCHINIT.  In this case, the
		initialization logic will call mtdpart_archinitialize() to obtain
		the MTD driver instance.

config TESTING_NXFFS_NEBLOCKS
	int "Number of erase blocks (simulated)"
	default 32
	depends on !TESTING_NXFFS_ARCHINIT
	---help---
		When TESTING_NXFFS_ARCHINIT is not defined, this test will use
		the RAM MTD device at drivers/mtd/rammtd.c to simulate FLASH.  In
		this case, this value must be provided to give the number of erase
		blocks in MTD RAM device.

		The size of the allocated RAM drive will be:

			RAMMTD_ERASESIZE * TESTING_NXFFS_NEBLOCKS

config TESTING_NXFFS_MAXNAME
	int "Max name size"
	default 128
	range 1 255
	---help---
		Determines the maximum size of names used in the filesystem

	config TESTING_NXFFS_MAXFILE
	int "Max file size"
	default 8192
	---help---
		Determines the maximum size of a file

config TESTING_NXFFS_MAXIO
	int "Max I/O"
	default 347

config TESTING_NXFFS_MAXOPEN
	int "Max open files"
	default 512

config TESTING_NXFFS_MOUNTPT
	string "NXFFS mountpoint"
	default "/mnt/nxffs"

config TESTING_NXFFS_NLOOPS
	int "Number of test loops"
	default 100

config TESTING_NXFFS_VERBOSE
	bool "Verbose output"
	default n

endif
