#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config TESTING_SCANFTEST
	tristate "sscanf() test"
	default n
	---help---
		Enable sscanf() test

		For all tests to be successful, you need to enable
		CONFIG_LIBC_FLOATINGPOINT, CONFIG_LIBC_LONG_LONG and
		CONFIG_LIBC_SCANSET in addition.

if TESTING_SCANFTEST

config TESTING_SCANFTEST_PROGNAME
	string "Program name"
	default "scanftest"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config TESTING_SCANFTEST_PRIORITY
	int "Scanftest task priority"
	default 100

config TESTING_SCANFTEST_STACKSIZE
	int "Scanftest stack size"
	default DEFAULT_TASK_STACKSIZE

endif
