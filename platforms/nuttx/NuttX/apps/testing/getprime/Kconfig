#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config TESTING_GETPRIME
	tristate "getprime example"
	default n
	---help---
		Enable the getprime example. This program is used to check multi
		thread performance mainly for SMP (but not limited to)

if TESTING_GETPRIME

config TESTING_GETPRIME_PROGNAME
	string "Program name"
	default "getprime"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config TESTING_GETPRIME_PRIORITY
	int "getprime task priority"
	default 50

config TESTING_GETPRIME_STACKSIZE
	int "getprime stack size"
	default DEFAULT_TASK_STACKSIZE

config TESTING_GETPRIME_THREAD_PRIORITY
	int "getprime thread priority"
	default 10

endif
