#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config TESTING_FSTEST
	tristate "Generic file system test"
	default n
	---help---
		Enable the generic file system test

if TESTING_FSTEST

config TESTING_FSTEST_PROGNAME
	string "Program name"
	default "fstest"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config TESTING_FSTEST_PRIORITY
	int "FS test task priority"
	default 100

config TESTING_FSTEST_STACKSIZE
	int "FS test stack size"
	default DEFAULT_TASK_STACKSIZE

config TESTING_FSTEST_MAXNAME
	int "Max name size"
	default 32
	range 1 255
	---help---
		Determines the maximum size of names used in the filesystem

config TESTING_FSTEST_MAXFILE
	int "Max file size"
	default 8192
	---help---
		Determines the maximum size of a file

config TESTING_FSTEST_MAXIO
	int "Max I/O"
	default 347

config TESTING_FSTEST_MAXOPEN
	int "Max open files"
	default 512

config TESTING_FSTEST_MOUNTPT
	string "FSTEST mountpoint"

config TESTING_FSTEST_NLOOPS
	int "Number of test loops"
	default 100

config TESTING_FSTEST_SPIFFS
	bool "Enable SPIFFS testing"
	default y
	depends on FS_SPIFFS && EXPERIMENTAL
	---help---
		SPIFFS garbage collection and integrity checking will be performed
		after each pass through the test.

		If this option is not selected then the SPIFFS FLASH will be be
		reclaimed on-demand when there is no longer any available FLASH.

		Marked EXPERIMENTAL because it interferes with test performance.

config TESTING_FSTEST_VERBOSE
	bool "Verbose output"
	default n

endif
