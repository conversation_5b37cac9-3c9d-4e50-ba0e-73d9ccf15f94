From 1482d59fc2f5ed935e8201704305684cf129fbae Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 11 Apr 2021 00:12:06 -0700
Subject: [PATCH 2/2] Use #ifdef instead of #if for __linux__

---
 .../conformance/interfaces/sem_timedwait/3-1.c                  | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/testcases/open_posix_testsuite/conformance/interfaces/sem_timedwait/3-1.c b/testcases/open_posix_testsuite/conformance/interfaces/sem_timedwait/3-1.c
index fb6f2e6cb..739fe86a5 100644
--- a/testcases/open_posix_testsuite/conformance/interfaces/sem_timedwait/3-1.c
+++ b/testcases/open_posix_testsuite/conformance/interfaces/sem_timedwait/3-1.c
@@ -15,7 +15,7 @@
 #include <sys/stat.h>
 #include <sys/types.h>
 #include <fcntl.h>
-#if __linux__
+#ifdef __linux__
 #include <features.h>
 #endif
 #include <semaphore.h>
-- 
2.30.2

