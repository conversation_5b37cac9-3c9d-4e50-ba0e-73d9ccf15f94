#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config TESTING_FATUTF8
	tristate "FAT UTF8 test"
	default n
	---help---
		Enable FAT UTF8 test

		For the tests to be successful, you need to enable
		CONFIG_FAT_LFN, CONFIG_FAT_LFN_UTF8.

if TESTING_FATUTF8

config TESTING_FATUTF8_PROGNAME
	string "Program name"
	default "fatutf8"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config TESTING_FATUTF8_PRIORITY
	int "FAT UTF8 test task priority"
	default 100

config TESTING_FATUTF8_STACKSIZE
	int "FAT UTF8 test stack size"
	default DEFAULT_TASK_STACKSIZE

endif
