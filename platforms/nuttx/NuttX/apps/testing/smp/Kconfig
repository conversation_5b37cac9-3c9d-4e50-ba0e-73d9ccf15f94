#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config TESTING_SMP
	tristate "SMP example"
	default n
	depends on SMP
	---help---
		Enable the SMP example.  This example basically extracts the pthread
		barrier test from apps/testing/ostest and adds some instrumentation
		useful for debugging SMP implementations.

if TESTING_SMP

config TESTING_SMP_NBARRIER_THREADS
	int "Number of barrier threads"
	default 8
	---help---
		Specifies the number of threads to create in the SMP test.  The default
		is 8 but a smaller number may be needed on systems without sufficient memory
		to start so many threads.

config TESTING_SMP_PROGNAME
	string "Program name"
	default "smp"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config TESTING_SMP_PRIORITY
	int "SMP task priority"
	default 100

config TESTING_SMP_STACKSIZE
	int "SMP stack size"
	default DEFAULT_TASK_STACKSIZE

endif
