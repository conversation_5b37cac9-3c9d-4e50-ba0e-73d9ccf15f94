#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config TESTING_MM
	tristate "Memory management test"
	default n
	---help---
		Enable the memory management test

if TESTING_MM

config TESTING_MM_PROGNAME
	string "Program name"
	default "mm"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config TESTING_MM_PRIORITY
	int "Task priority"
	default 100

config TESTING_MM_STACKSIZE
	int "Stack size"
	default DEFAULT_TASK_STACKSIZE

endif
