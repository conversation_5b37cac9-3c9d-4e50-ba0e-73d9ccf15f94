#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config TESTING_OSTEST
	tristate "OS test example"
	default n
	---help---
		Enable the OS test example

if TESTING_OSTEST

config TESTING_OSTEST_LOOPS
	int "OS test loop"
	default 1
	---help---
		Used to control the number of executions of the test.  If undefined, the test
		executes one time.  If defined to be zero, the test runs forever.

config TESTING_OSTEST_STACKSIZE
	int "OS test stack size"
	default 8192
	---help---
		Size of the stack used to create the ostest task.  Default is 8192.

config TESTING_OSTEST_NBARRIER_THREADS
	int "Number of barrier threads"
	default 8
	---help---
		Specifies the number of threads to create in the barrier test.  The default
		is 8 but a smaller number may be needed on systems without sufficient memory
		to start so many threads.

config TESTING_OSTEST_AIO
	bool "Asynchronous I/O Tests"
	default n
	depends on FS_AIO
	---help---
		Enables test of asynchronous I/O.  You must have a mounted, writable
		directory in place to use this test.

if TESTING_OSTEST_AIO

config TESTING_OSTEST_AIOPATH
	string "Scratch file path"
	default "/tmp"
	---help---
		This is the location of a directory in a mounted file system that
		the AIO test can write into.

endif

config TESTING_OSTEST_RR_RANGE
	int "Round-robin test - end of search range"
	default 10000
	range 1 32767
	---help---
		During round-robin scheduling test two threads are created. Each of the threads
		searches for prime numbers in the configurable range, doing that configurable
		number of times.

		This value specifies the end of search range and together with number of runs
		allows to configure the length of this test - it should last at least a few
		tens of seconds. Allowed values [1; 32767], default 10000

config TESTING_OSTEST_RR_RUNS
	int "Round-robin test - number of runs"
	default 10
	range 1 32767
	---help---
		During round-robin scheduling test two threads are created. Each of the threads
		searches for prime numbers in the configurable range, doing that configurable
		number of times.

		This value specifies the number of times the thread searches the range for
		prime numbers and together with end of search range allows to configure the
		length of this test - it should last at least a few tens of seconds. Allowed
		values [1; 32767], default 10

if ARCH_FPU && SCHED_WAITPID

config TESTING_OSTEST_FPUTESTDISABLE
	bool "Disable FPU test"
	default n

if !TESTING_OSTEST_FPUTESTDISABLE

config TESTING_OSTEST_FPULOOPS
	int "Number of FPU test loops"
	default 16

config TESTING_OSTEST_FPUMSDELAY
	int "FPU test delay (MSec)"
	default 750

config TESTING_OSTEST_FPUPRIORITY
	int "FPU test thread priority"
	default 100

config TESTING_OSTEST_FPUSTACKSIZE
	int "FPU test thread stack size"
	default DEFAULT_TASK_STACKSIZE

endif # !TESTING_OSTEST_FPUTESTDISABLE
endif # ARCH_FPU && SCHED_WAITPID

config TESTING_OSTEST_WAITRESULT
	bool "Wait and return test result"
	default y
	depends on SCHED_WAITPID

config TESTING_OSTEST_POWEROFF
	bool "Terminate on test completion"
	default n
	depends on BOARDCTL_POWEROFF && TESTING_OSTEST_WAITRESULT

endif # TESTING_OSTEST
