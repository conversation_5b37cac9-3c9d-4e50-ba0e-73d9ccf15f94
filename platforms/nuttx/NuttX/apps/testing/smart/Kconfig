#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config TESTING_SMART
	tristate "SMART file system example"
	default n
	---help---
		Enable the SMART file system example

if TESTING_SMART

config TESTING_SMART_ARCHINIT
	bool "Architecture-specific initialization"
	default n
	---help---
		The default is to use the RAM MTD device at drivers/mtd/rammtd.c.
		But an architecture-specific MTD driver can be used instead by
		defining TESTING_SMART_ARCHINIT.  In this case, the
		initialization logic will call smart_archinitialize() to obtain
		the MTD driver instance.

config TESTING_SMART_NEBLOCKS
	int "Number of erase blocks (simulated)"
	default 32
	depends on !TESTING_SMART_ARCHINIT
	---help---
		When TESTING_SMART_ARCHINIT is not defined, this test will use
		the RAM MTD device at drivers/mtd/rammtd.c to simulate FLASH.  In
		this case, this value must be provided to give the number of erase
		blocks in MTD RAM device.

		The size of the allocated RAM drive will be:

			RAMMTD_ERASESIZE * TESTING_SMART_NEBLOCKS

config TESTING_SMART_MAXNAME
	int "Max name size"
	default 32
	range 1 255
	---help---
		Determines the maximum size of names used in the filesystem

config TESTING_SMART_MAXFILE
	int "Max file size"
	default 8192
	---help---
		Determines the maximum size of a file

config TESTING_SMART_MAXIO
	int "Max I/O"
	default 347

config TESTING_SMART_MAXOPEN
	int "Max open files"
	default 512

config TESTING_SMART_MOUNTPT
	string "SMART mountpoint"
	default "/mnt/nxffs"

config TESTING_SMART_NLOOPS
	int "Number of test loops"
	default 100

config TESTING_SMART_VERBOSE
	bool "Verbose output"
	default n

endif
