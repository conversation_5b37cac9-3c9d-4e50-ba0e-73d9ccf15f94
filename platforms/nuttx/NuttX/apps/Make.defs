############################################################################
# apps/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

TOPDIR ?= $(APPDIR)/import
include $(TOPDIR)/Make.defs

# The GNU make CURDIR will always be a POSIX-like path with forward slashes
# as path segment separators.  This is fine for the above inclusions but
# will cause problems later for the native build.  If we know that this is
# a native build, then we need to fix up the APPDIR path for subsequent
# use

ifeq ($(CONFIG_WINDOWS_NATIVE),y)
APPDIR := ${shell echo %CD%}
endif

# Application Directories

# BUILDIRS is the list of top-level directories containing Make.defs files
# CLEANDIRS is the list of all top-level directories containing Makefiles.
#   It is used only for cleaning.

BUILDIRS   := $(dir $(wildcard $(APPDIR)$(DELIM)*$(DELIM)Make.defs))
BUILDIRS   := $(filter-out $(APPDIR)$(DELIM)import$(DELIM),$(BUILDIRS))
CONFIGDIRS := $(filter-out $(APPDIR)$(DELIM)builtin$(DELIM),$(BUILDIRS))
CONFIGDIRS := $(filter-out $(dir $(wildcard $(APPDIR)$(DELIM)*$(DELIM)Kconfig)),$(CONFIGDIRS))
CLEANDIRS  := $(dir $(wildcard $(APPDIR)$(DELIM)*$(DELIM)Makefile))

# CONFIGURED_APPS is the application directories that should be built in
#   the current configuration.

CONFIGURED_APPS :=

define Add_Application
	include $(1)Make.defs
endef

$(foreach BDIR, $(BUILDIRS), $(eval $(call Add_Application,$(BDIR))))

# File extensions

CXXEXT ?= .cxx
RUSTEXT ?= .rs
ZIGEXT ?= .zig

# Library path

LIBPATH ?= $(TOPDIR)$(DELIM)staging

# The install path

BINDIR ?= $(APPDIR)$(DELIM)bin

# The final build target

BIN ?= $(APPDIR)$(DELIM)libapps$(LIBEXT)

# Tools

ifeq ($(CONFIG_WINDOWS_NATIVE),y)
  MKKCONFIG ?= $(APPDIR)$(DELIM)tools$(DELIM)mkkconfig.bat
else
  MKKCONFIG ?= $(APPDIR)$(DELIM)tools$(DELIM)mkkconfig.sh
endif

# Builtin Registration

BUILTIN_REGISTRY = $(APPDIR)$(DELIM)builtin$(DELIM)registry
DEPCONFIG = $(TOPDIR)$(DELIM).config

define REGISTER
	$(Q) echo Register: $1
	$(Q) echo { \"$1\", $2, $3, $4 }, > "$(BUILTIN_REGISTRY)$(DELIM)$1.bdat"
	$(Q) if [ ! -z $4 ]; then \
	        echo "int $4(int argc, char *argv[]);" > "$(BUILTIN_REGISTRY)$(DELIM)$1.pdat"; \
	     fi;
	$(Q) touch "$(BUILTIN_REGISTRY)$(DELIM).updated"
endef

# Standard include path

CFLAGS   += ${shell $(INCDIR) "$(CC)" "$(APPDIR)$(DELIM)include"}
CXXFLAGS += ${shell $(INCDIR) "$(CC)" "$(APPDIR)$(DELIM)include"}

NUTTXLIB ?= $(call CONVERT_PATH,$(TOPDIR)$(DELIM)staging)
