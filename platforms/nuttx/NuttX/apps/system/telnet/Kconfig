#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

menuconfig SYSTEM_TELNET_CHATD
	tristate "Telnet chat daemon"
	default n
	depends on NET && NET_TCP
	select NETUTILS_TELNETC
	---help---
		Enable the Telnet Chat daemon.

if SYSTEM_TELNET_CHATD

config SYSTEM_TELNET_CHATD_PROGNAME
	string "Chat daemon program name"
	default "chatd"

config SYSTEM_TELNET_CHATD_STACKSIZE
	int "Chat daemon stacksize"
	default DEFAULT_TASK_STACKSIZE

config SYSTEM_TELNET_CHATD_PRIORITY
	int "Chat daemon priority"
	default 100

endif # SYSTEM_TELNET_CHATD

menuconfig SYSTEM_TELNET_CLIENT
	tristate "Telnet client"
	default n
	depends on NET && NET_TCP
	select NETUTILS_TELNETC
	select SYSTEM_READLINE
	---help---
		Enable the Telnet client program.

if SYSTEM_TELNET_CLIENT

config SYSTEM_TELNET_CLIENT_PROGNAME
	string "Telnet client program name"
	default "telnet"

config SYSTEM_TELNET_CLIENT_STACKSIZE
	int "Telnet client stacksize"
	default DEFAULT_TASK_STACKSIZE

config SYSTEM_TELNET_CLIENT_PRIORITY
	int "Telnet client priority"
	default 100

endif # SYSTEM_TELNET_CLIENT
