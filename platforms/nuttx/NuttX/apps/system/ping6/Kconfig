#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config SYSTEM_PING6
	tristate "ICMPv6 'ping6' command"
	default n
	depends on NET_ICMPv6
	select NETUTILS_PING6
	---help---
		Enable support for the ICMP 'ping' command.

if SYSTEM_PING6
config SYSTEM_PING6_PROGNAME
	string "Ping program name"
	default "ping6"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config SYSTEM_PING6_PRIORITY
	int "Ping task priority"
	default 100

config SYSTEM_PING6_STACKSIZE
	int "Ping stack size"
	default DEFAULT_TASK_STACKSIZE

endif
