/****************************************************************************
 * apps/system/ubloxmodem/ubloxmodem.h
 *
 *   Copyright (C) 2016 <PERSON>. All rights reserved.
 *   Author: <PERSON> <<EMAIL>>
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 * 3. Neither the name NuttX nor the names of its contributors may be
 *    used to endorse or promote products derived from this software
 *    without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS
 * OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 * AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
 * ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 ****************************************************************************/

#ifndef __APPS_SYSTEM_UBLOXMODEM_UBLOXMODEM_H
#define __APPS_SYSTEM_UBLOXMODEM_UBLOXMODEM_H

/****************************************************************************
 * Public Types
 ****************************************************************************/

/* Command index */

enum ubloxmodem_cmd
{
  UBLOXMODEM_CMD_UNKNOWN = -1,
  UBLOXMODEM_CMD_HELP    = 0,
  UBLOXMODEM_CMD_ON,
  UBLOXMODEM_CMD_OFF,
  UBLOXMODEM_CMD_RESET,
  UBLOXMODEM_CMD_STATUS,
  UBLOXMODEM_CMD_AT,
};

/* App context */

struct ubloxmodem_cxt
{
  int argc;
  FAR char **argv;
  enum ubloxmodem_cmd cmd;
  int fd;
};

/* Type of function of the command state returning modified state */

typedef int (*ubloxmodem_fun)(FAR struct ubloxmodem_cxt* cxt);

/* Command information tuple */

struct cmdinfo
{
  ubloxmodem_fun  handler;    /* Function that handles the command */
  FAR const char* name;       /* Name of the command */
  FAR const char* desc;       /* Short description */
  FAR const char* args;       /* Description of command arguments */
};

#endif // __APPS_SYSTEM_UBLOXMODEM_UBLOXMODEM_H
