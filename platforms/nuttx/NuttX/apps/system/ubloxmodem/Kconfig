#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

menuconfig SYSTEM_UBLOXMODEM
	tristate "u-blox modem configuration tool"
	default n
	---help---
		Enable the u-blox modem configuration tool.

		This app provides a command-line interface to GPIO power controls of
		u-blox modem. The actual behaviour should be implemented in the lower-
		half modem driver. Additionally the app can send test AT commands to
		the modem via the TTY device node settable in the defconfig:

			CONFIG_SYSTEM_UBLOXMODEM_TTY_DEVNODE="/dev/ttyS1"

if SYSTEM_UBLOXMODEM

config SYSTEM_UBLOXMODEM_TTY_DEVNODE
	string "u-blox modem TTY device node"
	default "/dev/ttyS1"
	---help---
		TTY device node associated to the u-blox modem UART.

config SYSTEM_UBLOXMODEM_DEVNODE
	string "u-blox modem device node"
	default "/dev/ubxmdm"
	---help---
		Device node created by the u-blox modem driver.

endif
