#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config SYSTEM_POPEN
	bool "popen()/pclose() Functions"
	default n
	select SCHED_WAITPID
	depends on NSH_LIBRARY
	---help---
		Enable support for the popen() and pclose() interfaces.
		This will support execution of NSH commands from C code with
		pipe communications with the shell.

if SYSTEM_POPEN

config SYSTEM_POPEN_SHPATH
	string "Path to shell command"
	default "/bin/sh"
	depends on SYSTEM_NSH=m || BUILD_KERNEL
	---help---
		This is the full path to the program in a mounted file system that
		implements the system() command.  That is, a program that starts the
		NSH shell, executes one command (in argv[1]), then exits.

config SYSTEM_POPEN_STACKSIZE
	int "Shell stack size"
	default DEFAULT_TASK_STACKSIZE
	---help---
		The size of stack allocated for the shell.

		NOTE: I needed to set the stack size quite large to get this example
		working on the simulated target (perhaps because of the 64-bit
		stack?  Or perhaps that is a sneak call into the host libc that I
		have not caught).  I assume that a smaller stack would be okay on
		real hardware, but I have not yet verified that.

config SYSTEM_POPEN_PRIORITY
	int "Shell priority"
	default 100
	---help---
		The priority of the shell.

endif
