#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

menuconfig SYSTEM_CUTERM
	tristate "CU minimal serial terminal"
	default n
	---help---
		Enable the CU serial terminal.  This is a minimalistic
		implementation of the 'cu' terminal program (part of Taylor UUCP for
		ages). Using it, you can simply open a serial port and interact with
		it. Using '~.' you can leave the terminal program and drop back to nsh.

		This terminal might come in handy for other people that have e.g. GS
		modems, GPS receivers or other devices with text based serial
		communications attached to their NuttX systems.

if SYSTEM_CUTERM

config SYSTEM_CUTERM_DEFAULT_DEVICE
	string "Default serial device"
	default "/dev/ttyS0"
	---help---
		Normally, the serial device to be used is provided on the command line.
		If no device is provided then this is the default device that will be\
		used.

config SYSTEM_CUTERM_DEFAULT_BAUD
	int "Default serial baud"
	default 115200
	---help---
		Normally, the BAUD to be used is provided on the command line.  If no
		BAUD is provided then this is the default device that will be used.

config SYSTEM_CUTERM_STACKSIZE
	int "CU terminal stack size"
	default DEFAULT_TASK_STACKSIZE
	---help---
		This is the stack size that will be used when starting the CU terminal.

config SYSTEM_CUTERM_PRIORITY
	int "CU terminal priority"
	default 100
	---help---
		This is the task priority that will be used when starting the CU terminal.

endif # SYSTEM_CUTERM
