############################################################################
# apps/system/lzf/Makefile.host
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

############################################################################
# USAGE:
#
#   1. TOPDIR and APPDIR must be defined on the make command line:  TOPDIR
#      is the full path to the nuttx/ directory; APPDIR is the full path to
#      the apps/ directory.  For example:
#
#        make -f Makefile.host TOPDIR=/home/<USER>/projects/nuttx
#          APPDIR=/home/<USER>/projects/apps
#
#   2. Add CONFIG_DEBUG_FEATURES=y to the make command line to enable debug output
#   3. Make sure to clean old target .o files before making new host .o
#      files.
#
############################################################################

include $(APPDIR)/Make.defs

BIN      = lzf$(HOSTEXEEXT)
HCFLAGS := -I. -I $(TOPDIR)/libs/libc
HCFLAGS += -DFAR= -Dnoreturn_function= -Dset_errno=

SRCS    := $(TOPDIR)/libs/libc/lzf/lzf_d.c
SRCS    += $(TOPDIR)/libs/libc/lzf/lzf_c.c
SRCS    += $(APPDIR)/system/lzf/lzf_main.c

all: $(BIN)
.PHONY: clean

lzf.h:
	$(Q) ln -sf $(TOPDIR)/include/lzf.h

nuttx/config.h:
	$(Q) mkdir -p nuttx
	$(Q) ln -sf $(TOPDIR)/include/nuttx/config.h nuttx/

$(BIN): lzf.h nuttx/config.h $(SRCS)
	$(Q) $(HOSTCC) $(HCFLAGS) -o $@ $(filter-out lzf.h nuttx/config.h, $^)

clean:
	rm -rf $(BIN) lzf.h nuttx
