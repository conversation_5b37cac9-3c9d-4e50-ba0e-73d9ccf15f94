#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

menuconfig SYSTEM_COMPOSITE
	tristate "USB Composite Device Commands"
	default n
	depends on BOARDCTL && USBDEV_COMPOSITE && BUILD_FLAT
	select BOA<PERSON>CTL_USBDEVCTRL
	---help---
		Enable the USB composite class controls.  These controls include:

		conn:  Connect the mass storage device to the host
		disconn:  Disconnect the mass storage device to the host

if SYSTEM_COMPOSITE

config SYSTEM_COMPOSITE_DEFCONFIG
	int "Default composite configuration"
	default 0
	---help---
		Boards may support multiple composite configurations.  If so, then
		this is the default configuration that the conn command will use if
		no configuration ID is provided on the command line.

if USBDEV_TRACE || DEBUG_USB

config SYSTEM_COMPOSITE_TRACEINIT
	bool "USB Trace Initialization"
	default n
	---help---
		If USBDEV_TRACE is enabled (or CONFIG_DEBUG_FEATURES and CONFIG_DEBUG_USB),
		then the add-on code will also manage the USB trace output.  The
		amount of trace output can be controlled this configuration value:
		This setting will show USB initialization events

config SYSTEM_COMPOSITE_TRACECLASS
	bool "USB Trace Class"
	default n
	---help---
		If USBDEV_TRACE is enabled (or CONFIG_DEBUG_FEATURES and CONFIG_DEBUG_USB),
		then the add-on code will also manage the USB trace output.  The
		amount of trace output can be controlled this configuration value:
		This setting will show USB class driver events

config SYSTEM_COMPOSITE_TRACETRANSFERS
	bool "USB Trace Transfers"
	default n
	---help---
		If USBDEV_TRACE is enabled (or CONFIG_DEBUG_FEATURES and CONFIG_DEBUG_USB),
		then the add-on code will also manage the USB trace output.  The
		amount of trace output can be controlled this configuration value:
		This setting will show USB data transfer events

config SYSTEM_COMPOSITE_TRACECONTROLLER
	bool "USB Trace Device Controller Events"
	default n
	---help---
		If USBDEV_TRACE is enabled (or CONFIG_DEBUG_FEATURES and CONFIG_DEBUG_USB),
		then the add-on code will also manage the USB trace output.  The
		amount of trace output can be controlled this configuration value:
		This setting will show USB device controller events

config SYSTEM_COMPOSITE_TRACEINTERRUPTS
	bool "USB Trace Device Controller Interrupt Events"
	default n
	---help---
		If USBDEV_TRACE is enabled (or CONFIG_DEBUG_FEATURES and CONFIG_DEBUG_USB),
		then the add-on code will also manage the USB trace output.  The
		amount of trace output can be controlled this configuration value:
		This setting will show USB device controller interrupt-related events.

endif # USBDEV_TRACE || DEBUG_USB

config SYSTEM_COMPOSITE_DEBUGMM
	bool "Memory usage debug"
	default n
	---help---
		Enables some debug tests to check for memory usage and memory leaks.

endif
