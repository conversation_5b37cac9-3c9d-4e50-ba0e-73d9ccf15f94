#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

menuconfig SYSTEM_VI
	tristate "VI Work-Alike Text Editor"
	select SYSTEM_TERMCURSES
	default n
	---help---
		Enable support for NuttX tiny VI work-alike editor.

		Omitted features:
			- No keypad cursor control support
			- No word oriented operations.

		Assumptions and Limitations:
			- A VT100 host terminal is assumed.  At 115200 BAUD, screen
			  update times with a 24x64 edit region are noticeable but
			  acceptable.
			- A fixed width character set (like Courier) is assumed
			- Files are edited in memory so unless you have a lot of memory
			  to spare, this editor will only be useful for very small files.

		Size: As of 2013-01-30, it appears that the size will vary from
		about 8.5 to 10.5KB, depending upon the efficiency of the
		underlying architecture.

if SYSTEM_VI

config SYSTEM_VI_COLS
	int "Display width (columns)"
	default 64
	---help---
		The editor does not have the capability to query the display for
		its width or height.  This setting provides the default width of
		the display in columns.  The actually width can be overridden using
		command line options.

		The default column width for a VT100 terminal is 80 characters.  The
		default for this setting is smaller, 64, so that a smaller in-memory
		buffer is used.  You might have a better experience with a column
		width of 80 (if the terminal width is not controllable from your
		terminal application).

config SYSTEM_VI_ROWS
	int "Display height (rows)"
	default 16
	---help---
		The editor does not have the capability to query the display for
		its width or height.  This setting provides the default height of
		the display in rows.  The actually width can be overridden using
		command line options.

		The default height for a VT100 terminal is 64 rows.  The default
		for this setting is smaller, 16, so that a smaller in-memory
		buffer is used.  You might have a better experience with a height
		of 64 rows (if the terminal height is not controllable from your
		terminal application).

config SYSTEM_VI_DEBUGLEVEL
	int "Debug level"
	default 0
	range 0 2
	---help---
		0=Debug off; 1=Print errors on console; 2=Print debug information
		on the console.

		Debug output is generated with syslog.  The editor works on
		/dev/console.  In order to get both a usable display and also
		readable debug output, syslog'ing should sent to some device other
		than /dev/console (which is the default).

config SYSTEM_VI_STACKSIZE
	int "Builtin task stack size"
	default DEFAULT_TASK_STACKSIZE
	---help---
		Size of the task to configure when started VI from NSH

config SYSTEM_VI_PRIORITY
	int "Builtin task priority"
	default 100
	---help---
		Priority of the task to configure when started VI from NSH

config SYSTEM_VI_INCLUDE_COMMAND_REPEAT
	bool "Include support for '.' command repeat key"
	default y

# These are placeholders and will be implemented soon
#config SYSTEM_VI_INCLUDE_UNDO
#	bool "Include undo/redo support"
#	default y
#	---help---
#		Adds multi-level undo / redo support.
#
#config SYSTEM_VI_UNDO_LEVELS
#	int "Undo / Redo stack size"
#	depends on SYSTEM_VI_INCLUDE_UNDO
#	default 10
#	---help---
#		Number of undo / redo operations saved.

endif # SYSTEM_VI
