#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

menuconfig SYSTEM_CRITMONITOR
	tristate "Critcal Section Monitor"
	default n
	depends on FS_PROCFS && !FS_PROCFS_EXCLUDE_PROCESS && SCHED_CRITMONITOR
	---help---
		If the critical section monitor is enabled (CONFIGSCHED_CRITMONITOR)
		this option will enable a critical section monitor daemon.  This daemon
		that will periodically assess usage of critical sections by all tasks
		and threads in the system.

if SYSTEM_CRITMONITOR

config SYSTEM_CRITMONITOR_STACKSIZE
	int "Critical section monitor stop/stop stack size"
	default DEFAULT_TASK_STACKSIZE
	---help---
		The stack size to use the critmon_start/critmon_stop task.  Default: 2048

config SYSTEM_CRITMONITOR_PRIORITY
	int "Critical section monitor stop/stop priority"
	default 100
	---help---
		The priority to use the critmon_start/critmon_stop task.  Default: 100

config SYSTEM_CRITMONITOR_DAEMON_STACKSIZE
	int "Critical section monitor daemon stack size"
	default DEFAULT_TASK_STACKSIZE
	---help---
		The stack size to use the critical section monitor daemon.  Default: 2048

config SYSTEM_CRITMONITOR_DAEMON_PRIORITY
	int "Critical section monitor daemon priority"
	default 50
	---help---
		The priority to use the stack monitor daemon.  Default: 50

config SYSTEM_CRITMONITOR_INTERVAL
	int "Critical section monitor dump frequency"
	default 2
	---help---
		The rate in seconds that the Critical section monitor will wait before
		dumping the next set Critical section information.  Default:  2 seconds.

config SYSTEM_CRITMONITOR_MOUNTPOINT
	string "procfs mountpoint"
	default "/proc"

endif
