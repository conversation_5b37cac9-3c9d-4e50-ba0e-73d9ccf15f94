#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config SYSTEM_NSH
	tristate "NuttShell (NSH) example"
	default n
	select NSH_LIBRARY
	select SYSTEM_READLINE
	---help---
		Enable the NuttShell (NSH) example

if SYSTEM_NSH

config SYSTEM_NSH_PRIORITY
	int "NuttX shell thread priority"
	default 100

config SYSTEM_NSH_STACKSIZE
	int "NuttX shell stack size"
	default DEFAULT_TASK_STACKSIZE

config SYSTEM_NSH_SYMTAB
	bool "Register symbol table"
	default n
	depends on LIBC_EXECFUNCS && BOARDCTL && !EXECFUNCS_HAVE_SYMTAB
	select BOARDCTL_APP_SYMTAB
	---help---
		Enable logic to automatically register an application symbol table
		as part of NSH initialization.  If enabled, then application logic
		must provide the following:

			const struct symtab_s g_exports[];
			const int g_nexports;

		Where g_exports is the name of the exported application symbol table
		and g_nexports holds the number of entries in the application symbol
		table.

		This is done very early in the NSH initialization sequence.

		Why might you want to do this?  There is really only one reason:  You
		would like to have the symbol tables in place early so that programs
		started by NSH, perhaps via an initialization script, will have all
		of the necessary symbols in place.  Otherwise, you probably do *not*
		want this option!

if SYSTEM_NSH_SYMTAB

config SYSTEM_NSH_SYMTAB_ARRAYNAME
	string "Symbol table used by exec[l|v]"
	default "g_exports"
	---help---
		The exec[l|v] and posix_spawn() functions needs to have (1) a
		symbol table that provides the list of symbols exported by the base
		code, and (2) the number of symbols in that table.  This selection
		provides the name of that symbol table.

config SYSTEM_NSH_SYMTAB_COUNTNAME
	string "Variable holding the number of symbols"
	default "g_nexports"
	---help---
		The exec[l|v] and posix_spawn() functions needs to have (1) a
		symbol table that provides the list of symbols exported by the base
		code, and (2) the number of symbols in that table.  This selection
		provides the name of 'int' variable that holds the number of symbol
		in the table.

endif # SYSTEM_NSH_SYMTAB

config SYSTEM_NSH_PROGNAME
	string "Program name"
	default "nsh"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

endif
