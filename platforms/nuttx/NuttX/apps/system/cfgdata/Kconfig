#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config SYSTEM_CFGDATA
	tristate "Cfgdata Command"
	default n
	depends on MTD_CONFIG
	---help---
		Enable support for the CFGDATA tool.

if SYSTEM_CFGDATA

config SYSTEM_CFGDATA_STACKSIZE
	int "Builtin task stack size"
	default DEFAULT_TASK_STACKSIZE
	---help---
		Size of the task to configure when started cfgdata from NSH

config SYSTEM_CFGDATA_PRIORITY
	int "Builtin task priority"
	default 100
	---help---
		Priority of the task to configure when started cfgdata from NSH

endif
