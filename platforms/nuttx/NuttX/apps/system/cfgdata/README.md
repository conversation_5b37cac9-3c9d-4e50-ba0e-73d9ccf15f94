# System / `cfgdata`

```
Author: <PERSON>
  Date: 18 December 2018
```

This application provides a command line interface for managing platform
specific configdata within the `/dev/config` device.

**Usage**:

```shell
config <cmd> [arguments]
```

Where `<cmd>` is one of:

- `all` – show all config entries
- `print` – display a specific config entry
- `set` – set or change a config entry
- `unset` – delete a config entry
