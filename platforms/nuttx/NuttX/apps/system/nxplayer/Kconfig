#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config SYSTEM_NXPLAYER
	bool "NxPlayer Media Player"
	default n
	depends on AUDIO
	---help---
		Enable support for the NxPlayer media player library and optional
		command line interface.

if SY<PERSON><PERSON>_NXPLAYER

config NXPLAYER_MAINTHREAD_STACKSIZE
	int "NxPlayer main thread stack size"
	default DEFAULT_TASK_STACKSIZE
	---help---
		Stack size to use with the NxPlayer main thread.

config NXPLAYER_PLAYTHREAD_STACKSIZE
	int "NxPlayer thread stack size"
	default PTHREAD_STACK_DEFAULT
	---help---
		Stack size to use with the NxPlayer play thread.

config NXPLAYER_COMMAND_LINE
	tristate "Include nxplayer command line application"
	default y
	---help---
		Compiles in code for the nxplayer command line control.
		This is a text-based command line interface that uses
		the nxplayer library to play media files, control the
		volume, balance, bass, etc.

if NXPL<PERSON>YER_COMMAND_LINE

config NXPLAYER_INCLUDE_HELP
	bool "Include HELP command and text"
	default y
	---help---
		Compiles in the NxPlayer help text to provide online help
		for available commands with syntax.

endif

config NXPLAYER_INCLUDE_DEVICE_SEARCH
	bool "Include audio device search code"
	default y
	---help---
		Compiles in extra code to search the audio device directory
		for a suitable audio device to play the specified file.
		Disabling this feature saves some code space, but it will
		mean the calling application must specify the path of the
		audio device to use before performing any other operations.

config NXPLAYER_INCLUDE_PREFERRED_DEVICE
	bool "Include preferred audio device specification code"
	default y
	---help---
		Adds support for identifying a specific audio device  to use
		for audio operations.  If this feature is not enabled, then
		an audio device search will be performed.

config NXPLAYER_FMT_FROM_EXT
	bool "Include code to determine Audio format from extension"
	default y
	---help---
		Compiles in extra code to determine audio format based
		on the filename extension for known file types.
		This feature is used if the format is not manually
		specified, and will take priority over the more lengthy
		file content detection approach.

config NXPLAYER_FMT_FROM_HEADER
	bool "Include code to find Audio format from file content"
	default n
	---help---
		Compiles in extra code to determine audio format based
		on the header content of a file for known file types.
		This feature is used when the format type cannot be
		determined from the filename extension.

config NXPLAYER_INCLUDE_MEDIADIR
	bool "Include support for specifying a media directory"
	default y
	---help---
		Compiles in extra code to set a media directory which
		will be searched when a request is made to play a file
		which is not fully qualified.

if NXPLAYER_INCLUDE_MEDIADIR

config NXPLAYER_DEFAULT_MEDIADIR
	string "Default root directory to search for media files"
	default "/music"
	---help---
		Specifies a root directory to search for media files
		when an absolute path is not provided.  This can be
		changed at the nxplayer command line, but will default
		to this value each time nxplayer is launched.

config NXPLAYER_RECURSIVE_MEDIA_SEARCH
	bool "Perform recursive directory search for media files"
	default n
	---help---
		When enabled, this feature will add code to perform
		a complete recursive directory search within the
		MEDIADIR for any media files that do not have a
		qualified path (i.e. contain no '/' characters).

endif

config NXPLAYER_INCLUDE_SYSTEM_RESET
	bool "Include support for system / hardware reset"
	default n
	---help---
		When enabled, this feature will add code to enable issuing
		a HW reset via program call.  The system reset will perform
		a reset on all registered audio devices.

config NXPLAYER_HTTP_STREAMING_SUPPORT
	bool "Include support for http streaming"
	default n
	depends on NET_IPv4 && NET_TCP
	---help---
		When enabled, this feature will add code to enable HTTP
		audio streaming as well as local file playback.

endif

if NXPLAYER_HTTP_STREAMING_SUPPORT

config NXPLAYER_HTTP_MAXHOSTNAME
	int "Max host name in URL"
	default 40

config NXPLAYER_HTTP_MAXFILENAME
	int "Max file name in URL"
	default 100

endif
