#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

menu "NSH Library"

config NSH_LIBRARY
	bool "NSH Library"
	default n
	select NETUTILS_NETLIB if NET
	select LIBC_NETDB if NET
	select B<PERSON><PERSON><PERSON><PERSON> if (!NSH_DISABLE_MKRD && !DISABLE_MOUNTPOINT) || NSH_ARCHINIT || NSH_ROMFSETC
	select BOARDCTL_MKRD if !NSH_DISABLE_MKRD && !DISABLE_MOUNTPOINT
	select BOARDCTL_ROMDISK if NSH_ROMFSETC
	---help---
		Build the NSH support library.  This is used, for example, by
		system/nsh in order to implement the full NuttShell (NSH).

if NSH_LIBRARY

menuconfig NSH_MOTD
	bool "Message of the Day (MOTD)"
	default n
	---help---
		Support a user-provided Message of the Day (MOTD) that will be
		presented each time new NSH session is opened.

if NSH_MOTD

config NSH_PLATFORM_MOTD
	bool "Platform MOTD"
	default n
	---help---
		If this option is selected, the NSH will call into platform-specific
		logic in order to get the MOTD.  The function prototype for this
		call is:

			void platform_motd(FAR char *buffer, size_t buflen);

		Where buffer is the location to return the MOTD and buflen is the
		length of that buffer.  The maximum size of the buffer is determined
		by NSH_FILEIOSIZE.  An appropriate location for the
		implementation of platform_motd would be within apps/platform/<board>.

		One newline will be inserted after the platform-supplied message.

		platform_motd() is prototyped and described in apps/include/nshlib/nshlib.h
		which may be included like:

			#include "nshlib/nshlib.h"

config NSH_MOTD_STRING
	string "MOTD String"
	default "No MOTD string provided"
	depends on !NSH_PLATFORM_MOTD
	---help---
		If NSH_MOTD is selected, but NSH_PLATFORM_MOTD is not, then a fixed
		MOTD string will be used.  That string is provided by this selection.

		One newline will be inserted after supplied MOTD message.

endif # NSH_MOTD

menu "Command Line Configuration"

config NSH_PROMPT_STRING
	string "Prompt String"
	default "nsh> "
	---help---
		Provide the shell prompt string, default is "nsh> ".

choice
	prompt "Command Line Editor"
	default NSH_READLINE if DEFAULT_SMALL
	default NSH_CLE if !DEFAULT_SMALL

config NSH_READLINE
	bool "Minimal readline()"
	select SYSTEM_READLINE
	select READLINE_HAVE_EXTMATCH
	---help---
		Selects the minimal implementation of readline().  This minimal
		implementation provides on backspace for command line editing.

config NSH_CLE
	bool "Command Line Editor"
	select SYSTEM_CLE
	---help---
		Selects the more extensive, EMACS-like command line editor.
		Select this option only if (1) you don't mind a modest increase
		in the FLASH footprint, and (2) you work with a terminal that
		supports extensive VT100 editing commands.

		Selecting this option will add probably 1.5-2KB to the FLASH
		footprint.

endchoice

config NSH_LINELEN
	int "Max command line length"
	default 64 if DEFAULT_SMALL
	default 80 if !DEFAULT_SMALL
	---help---
		The maximum length of one command line and of one output line.
		Default: 64/80

config NSH_DISABLE_SEMICOLON
	bool "Disable multiple commands per line"
	default DEFAULT_SMALL
	---help---
		By default, you can enter multiple NSH commands on a line with each
		command separated by a semicolon.  You can disable this feature to
		save a little memory on FLASH challenged platforms.

config NSH_QUOTE
	bool "Enable back-slash quoting of characters"
	default !DEFAULT_SMALL && NSH_ARGCAT
	---help---
		Force special characters like back-quotes, quotation marks, and the
		back-slash character itself to be treat like normal text.

		This feature is only implemented properly for the case where
		CONFIG_NSH_ARGCAT is also selected.

config NSH_CMDPARMS
	bool "Enable commands as parameters"
	default !DEFAULT_SMALL
	depends on !DISABLE_MOUNTPOINT
	---help---
		If selected, then the output from commands, from file applications, and
		from NSH built-in commands can be used as arguments to other
		commands.  The entity to be executed is identified by enclosing the
		command line in back quotes.  For example,

			set FOO `myprogram $BAR`

		Will execute the program named myprogram passing it the value of the
		environment variable BAR.  The value of the environment variable FOO
		is then set output of myprogram on stdout.

		Because this feature commits significant resources, it is disabled by
		default.

config NSH_MAXARGUMENTS
	int "Maximum number of command arguments"
	default 7
	---help---
		The maximum number of NSH command arguments.
		Default: 7

config NSH_ARGCAT
	bool "Concatenation of argument strings"
	default !DEFAULT_SMALL
	---help---
		Support concatenation of strings with environment variables or command
		output.  For example:

			set FOO XYZ
			set BAR 123
			set FOOBAR ABC_${FOO}_${BAR}

		would set the environment variable FOO to XYZ, BAR to 123 and FOOBAR
		to ABC_XYZ_123.  If NSH_ARGCAT is not selected, then a slightly small
		FLASH footprint results but then also only simple environment
		variables like $FOO can be used on the command line.

config NSH_NESTDEPTH
	int "Maximum command nesting"
	default 3
	---help---
		The maximum number of nested if-then[-else]-fi sequences that
		are permissible.  Default: 3

config NSH_DISABLEBG
	bool "Disable background commands"
	default DEFAULT_SMALL
	---help---
		This can be set to 'y' to suppress support for background
		commands.  This setting disables the 'nice' command prefix and
		the '&' command suffix.  This would only be set on systems
		where a minimal footprint is a necessity and background command
		execution is not.

endmenu # Command Line Configuration

config NSH_BUILTIN_APPS
	bool "Enable built-in applications"
	default n
	depends on BUILTIN
	---help---
		Support external registered, "built-in" applications that can be
		executed from the NSH command line (see apps/README.txt for
		more information).  This options requires support for builtin
		applications (BUILTIN).

config NSH_FILE_APPS
	bool "Enable execution of program files"
	default n
	depends on LIBC_EXECFUNCS
	---help---
		Support execution of program files residing within a file
		system.  This options requires support for the posix_spawn()
		interface (LIBC_EXECFUNCS).

menu "Disable Individual commands"

config NSH_DISABLE_ADDROUTE
	bool "Disable addroute"
	default DEFAULT_SMALL
	depends on NET_ROUTE

config NSH_DISABLE_ARP
	bool "Disable arp"
	default DEFAULT_SMALL
	depends on NET_ARP

config NSH_DISABLE_BASE64DEC
	bool "Disable base64dec"
	default DEFAULT_SMALL
	depends on NETUTILS_CODECS && CODECS_BASE64

config NSH_DISABLE_BASE64ENC
	bool "Disable base64enc"
	default DEFAULT_SMALL
	depends on NETUTILS_CODECS && CODECS_BASE64

config NSH_DISABLE_BASENAME
	bool "Disable basename"
	default DEFAULT_SMALL || NSH_DISABLESCRIPT

config NSH_DISABLE_CAT
	bool "Disable cat"
	default DEFAULT_SMALL

config NSH_DISABLE_CD
	bool "Disable cd"
	default DEFAULT_SMALL

config NSH_DISABLE_CP
	bool "Disable cp"
	default DEFAULT_SMALL

config NSH_DISABLE_CMP
	bool "Disable cmp"
	default DEFAULT_SMALL

config NSH_DISABLE_TIMEDATECTL
	bool "Disable TIMEDATECTL"
	default DEFAULT_SMALL || !LIBC_LOCALTIME || !RTC

config NSH_DISABLE_DATE
	bool "Disable date"
	default DEFAULT_SMALL || !RTC

config NSH_DISABLE_DD
	bool "Disable dd"
	default DEFAULT_SMALL

config NSH_DISABLE_DF
	bool "Disable df"
	default DEFAULT_SMALL

config NSH_DISABLE_DELROUTE
	bool "Disable delroute"
	default DEFAULT_SMALL
	depends on NET_ROUTE

config NSH_DISABLE_DIRNAME
	bool "Disable dirname"
	default DEFAULT_SMALL || NSH_DISABLESCRIPT

config NSH_DISABLE_DMESG
	bool "Disable dmesg"
	default DEFAULT_SMALL
	depends on RAMLOG_SYSLOG

config NSH_DISABLE_ECHO
	bool "Disable echo"
	default DEFAULT_SMALL

config NSH_DISABLE_ENV
	bool "Disable env"
	default DEFAULT_SMALL

config NSH_DISABLE_EXEC
	bool "Disable exec"
	default DEFAULT_SMALL

config NSH_DISABLE_EXIT
	bool "Disable exit"
	default DEFAULT_SMALL && !NSH_TELNET

config NSH_DISABLE_EXPORT
	bool "Disable export"
	default DEFAULT_SMALL

config NSH_DISABLE_FREE
	bool "Disable free"
	default DEFAULT_SMALL

config NSH_DISABLE_GET
	bool "Disable get"
	default DEFAULT_SMALL

config NSH_DISABLE_HELP
	bool "Disable help"
	default DEFAULT_SMALL

config NSH_DISABLE_HEXDUMP
	bool "Disable hexdump"
	default DEFAULT_SMALL

config NSH_DISABLE_IFCONFIG
	bool "Disable ifconfig"
	default DEFAULT_SMALL || !FS_PROCFS || FS_PROCFS_EXCLUDE_NET

config NSH_DISABLE_IFUPDOWN
	bool "Disable ifup/down"
	default DEFAULT_SMALL || !FS_PROCFS || FS_PROCFS_EXCLUDE_NET

config NSH_DISABLE_KILL
	bool "Disable kill"
	default DEFAULT_SMALL

config NSH_DISABLE_LOSETUP
	bool "Disable losetup"
	default DEFAULT_SMALL

config NSH_DISABLE_LOSMART
	bool "Disable losmart"
	default DEFAULT_SMALL || !MTD_SMART

config NSH_DISABLE_LN
	bool "Disable ln"
	default DEFAULT_SMALL
	depends on PSEUDOFS_SOFTLINKS

config NSH_DISABLE_LS
	bool "Disable ls"
	default DEFAULT_SMALL

config NSH_DISABLE_MB
	bool "Disable mb"
	default y

config NSH_DISABLE_MD5
	bool "Disable md5"
	default DEFAULT_SMALL
	depends on NETUTILS_CODECS && CODECS_HASH_MD5

config NSH_DISABLE_MKDIR
	bool "Disable mkdir"
	default DEFAULT_SMALL

config NSH_DISABLE_MKFATFS
	bool "Disable mkfatfs"
	default DEFAULT_SMALL
	depends on FSUTILS_MKFATFS

config NSH_DISABLE_MKFIFO
	bool "Disable mkfifo"
	default DEFAULT_SMALL
	depends on PIPES

config NSH_DISABLE_MKRD
	bool "Disable mkrd"
	default DEFAULT_SMALL

config NSH_DISABLE_MKSMARTFS
	bool "Disable mksmartfs"
	default DEFAULT_SMALL
	depends on FS_SMARTFS && FSUTILS_MKSMARTFS

config NSH_DISABLE_MH
	bool "Disable mh"
	default y

config NSH_DISABLE_MODCMDS
	bool "Disable modules commands (insmod, rmmod, lsmod)"
	default DEFAULT_SMALL
	depends on MODULE

config NSH_DISABLE_MOUNT
	bool "Disable mount"
	default DEFAULT_SMALL

config NSH_DISABLE_MV
	bool "Disable mv"
	default DEFAULT_SMALL

config NSH_DISABLE_MW
	bool "Disable mw"
	default y

config NSH_DISABLE_NSFMOUNT
	bool "Disable nfsmount"
	default DEFAULT_SMALL
	depends on NFS

config NSH_DISABLE_NSLOOKUP
	bool "Disable nslookup"
	default DEFAULT_SMALL
	depends on LIBC_NETDB

config NSH_DISABLE_PASSWD
	bool "Disable passwd"
	default DEFAULT_SMALL
	depends on NSH_LOGIN_PASSWD && !FSUTILS_PASSWD_READONLY

config NSH_DISABLE_PMCONFIG
	bool "Disable pmconfig"
	default DEFAULT_SMALL
	depends on PM

config NSH_DISABLE_POWEROFF
	bool "Disable poweroff"
	default DEFAULT_SMALL
	depends on BOARDCTL_POWEROFF

config NSH_DISABLE_PRINTF
	bool "Disable printf"
	default DEFAULT_SMALL

config NSH_DISABLE_PS
	bool "Disable ps"
	default DEFAULT_SMALL || !FS_PROCFS || FS_PROCFS_EXCLUDE_PROCESS

config NSH_DISABLE_PSHEAPUSAGE
	bool "Disable ps heap usage"
	depends on DEBUG_MM && !NSH_DISABLE_PS
	default DEFAULT_SMALL

config NSH_DISABLE_PSSTACKUSAGE
	bool "Disable ps stack usage"
	depends on STACK_COLORATION && !NSH_DISABLE_PS
	default DEFAULT_SMALL
	---help---
		Disable to save space and not pull in floating point routines

config NSH_DISABLE_PUT
	bool "Disable put"
	default DEFAULT_SMALL

config NSH_DISABLE_PWD
	bool "Disable pwd"
	default DEFAULT_SMALL

config NSH_DISABLE_READLINK
	bool "Disable readlink"
	default DEFAULT_SMALL
	depends on PSEUDOFS_SOFTLINKS

config NSH_DISABLE_REBOOT
	bool "Disable reboot"
	default DEFAULT_SMALL
	depends on BOARDCTL_RESET

config NSH_DISABLE_RM
	bool "Disable rm"
	default DEFAULT_SMALL

config NSH_DISABLE_RMDIR
	bool "Disable rmdir"
	default DEFAULT_SMALL

config NSH_DISABLE_ROUTE
	bool "Disable delroute"
	depends on FS_PROCFS && NET_ROUTE && !FS_PROCFS_EXCLUDE_NET && !FS_PROCFS_EXCLUDE_ROUTE
	default DEFAULT_SMALL

config NSH_DISABLE_RPTUN
	bool "Disable rptun"
	default DEFAULT_SMALL
	depends on RPTUN

config NSH_DISABLE_SET
	bool "Disable set"
	default DEFAULT_SMALL

config NSH_DISABLE_SOURCE
	bool "Disable source"
	default DEFAULT_SMALL

config NSH_DISABLE_SHUTDOWN
	bool "Disable shutdown"
	default DEFAULT_SMALL || !BOARD_POWEROFF || !BOARD_RESET
	depends on BOARDCTL_POWEROFF || BOARDCTL_RESET

config NSH_DISABLE_SLEEP
	bool "Disable sleep"
	default DEFAULT_SMALL

config NSH_DISABLE_TIME
	bool "Disable time"
	default DEFAULT_SMALL

config NSH_DISABLE_TEST
	bool "Disable test"
	default DEFAULT_SMALL

config NSH_DISABLE_TELNETD
	bool "Disable telnetd"
	default DEFAULT_SMALL || NSH_NETLOCAL

config NSH_DISABLE_TRUNCATE
	bool "Disable truncate"
	default DEFAULT_SMALL

config NSH_DISABLE_UMOUNT
	bool "Disable umount"
	default DEFAULT_SMALL

config NSH_DISABLE_UNAME
	bool "Disable uname"
	default DEFAULT_SMALL

config NSH_DISABLE_UNSET
	bool "Disable unset"
	default DEFAULT_SMALL

config NSH_DISABLE_URLDECODE
	bool "Disable urldecode"
	default DEFAULT_SMALL
	depends on NETUTILS_CODECS && CODECS_URLCODE

config NSH_DISABLE_URLENCODE
	bool "Disable urlencode"
	default DEFAULT_SMALL
	depends on NETUTILS_CODECS && CODECS_URLCODE

config NSH_DISABLE_USERADD
	bool "Disable useradd"
	default DEFAULT_SMALL
	depends on NSH_LOGIN_PASSWD && !FSUTILS_PASSWD_READONLY

config NSH_DISABLE_USERDEL
	bool "Disable userdel"
	default DEFAULT_SMALL
	depends on NSH_LOGIN_PASSWD && !FSUTILS_PASSWD_READONLY

config NSH_DISABLE_USLEEP
	bool "Disable usleep"
	default DEFAULT_SMALL

config NSH_DISABLE_WGET
	bool "Disable wget"
	default DEFAULT_SMALL

config NSH_DISABLE_XD
	bool "Disable xd"
	default DEFAULT_SMALL

config NSH_DISABLE_RESET_CAUSE
	bool "Disable reset cause"
	default n if !DEFAULT_SMALL
	default y if DEFAULT_SMALL
	depends on BOARDCTL_RESET_CAUSE

endmenu

if MMCSD

config NSH_MMCSDMINOR
	int "MMC/SD minor device number"
	default 0
	---help---
		If the architecture supports an MMC/SD slot and if the NSH
		architecture specific logic is present, this option will provide
		the MMC/SD minor number, i.e., the MMC/SD block driver will
		be registered as /dev/mmcsdN where N is the minor number.
		Default is zero.

config NSH_MMCSDSLOTNO
	int "MMC/SD slot number"
	default 0
	---help---
		If board-specific NSH start-up supports more than one MMC/SD slot, then this setting
		should be provided to indicate which slot should be used.  Default: 0.

config NSH_MMCSDSPIPORTNO
	int "MMC/SD SPI device number"
	default 0
	depends on MMCSD_SPI
	---help---
		If board-specif NSH start-up logic will mount an SPI-based MMC/SD volume, then this
		setting may be needed to tell the board logic which SPI bus to use.  Default: 0
		(meaning is board-specific).

endif # MMCSD

menu "Configure Command Options"

config NSH_VARS
	bool "NSH variables"
	default n
	---help---
		By default, there are no internal NSH variables.  NSH will use OS
		environment variables for all variable storage.  If this option, NSH
		will also support local NSH variables.  These variables are, for the
		most part, transparent and work just like the OS environment
		variables.  The difference is that when you create new tasks, all of
		environment variables are inherited by the created tasks.  NSH local
		variables are not.

		If this option is enabled (and CONFIG_DISABLE_ENVIRON is not), then a
		new command called 'export' is enabled.  The export command works very
		must like the set command except that is operates on environment
		variables.  When CONFIG_NSH_VARS is enabled, there are changes in the
		behavior of certains commands

		============== =========================== ===========================
		CMD             w/o CONFIG_NSH_VARS        w/CONFIG_NSH_VARS
		============== =========================== ===========================
		set <a> <b>    Set environment var a to b  Set NSH var a to b
		unset <a>      Unsets environment var a    Unsets both environment var
		                                           and NSH var a
		export <a> <b> Causes an error             Unsets NSH var a.  Sets
		                                           environment var a to b.
		export <a>     Causes an error             Sets environment var a to
		                                           NSH var b (or "").  Unsets
		                                           local var a.
		env            Lists all environment       Lists all environment
		               variables                   variables
		============== =========================== ===========================

config NSH_CMDOPT_DD_STATS
	bool "dd: Support transfer statistics"
	default n
	depends on !NSH_DISABLE_DD

config NSH_CODECS_BUFSIZE
	int "File buffer size used by CODEC commands"
	default 128

config NSH_CMDOPT_HEXDUMP
	bool "hexdump: Enable 'skip' and 'count' parameters"
	default !DEFAULT_SMALL
	depends on !NSH_DISABLE_HEXDUMP

config NSH_PROC_MOUNTPOINT
	string "procfs mountpoint"
	default "/proc"
	depends on FS_PROCFS

endmenu

config NSH_FILEIOSIZE
	int "NSH I/O buffer size"
	default 512 if DEFAULT_SMALL
	default 1024 if !DEFAULT_SMALL
	---help---
		Size of a static I/O buffer used for file access (ignored if
		there is no filesystem). Default is 512/1024.

config NSH_STRERROR
	bool "Use strerror()"
	default n
	depends on LIBC_STRERROR
	---help---
		strerror(errno) makes more readable output but strerror() is
		very large and will not be used unless this setting is 'y'
		This setting depends upon the strerror() having been enabled
		with LIBC_STRERROR.

menu "Scripting Support"

config NSH_DISABLESCRIPT
	bool "Disable script support"
	default DEFAULT_SMALL
	---help---
		This can be set to 'y' to suppress support for scripting.  This
		setting disables the 'sh', 'test', and '[' commands and the
		if-then[-else]-fi construct.  This would only be set on systems
		where a minimal footprint is a necessity and scripting is not.

if !NSH_DISABLESCRIPT

config NSH_DISABLE_ITEF
	bool "Disable if-then-else-fi"
	default DEFAULT_SMALL
	---help---
		This can be set to 'y' to suppress support for if-then-else-fi
		sequences in scripts.  This would only be set on systems where
		some minimal scripting is required but if-then-else-fi is not.

config NSH_DISABLE_LOOPS
	bool "Disable loops"
	default DEFAULT_SMALL
	---help---
		This can be set to 'y' to suppress support for while-do-done and
		until-do-done sequences in scripts.  This would only be set on
		systems where some minimal scripting is required but looping
		is not.

endif # !NSH_DISABLESCRIPT

config NSH_ROMFSETC
	bool "Support ROMFS start-up script"
	default n
	depends on FS_ROMFS
	---help---
		Mount a ROMFS filesystem at /etc and provide a system init
		script at /etc/init.d/rc.sysinit and a startup script
		at /etc/init.d/rcS.  The default system init script will mount
		a FAT FS RAMDISK at /tmp but the logic is easily extensible.

if NSH_ROMFSETC

config NSH_CROMFSETC
	bool "Support CROMFS (compressed) start-up script"
	default n
	depends on FS_CROMFS
	---help---
		Mount a CROMFS filesystem at /etc and provide a compressed system
		init script at /etc/init.d/rc.sysinit and a startup script
		at /etc/init.d/rcS.

config NSH_ROMFSRC
	bool "Support ROMFS login script"
	default n
	---help---
		The ROMFS start-up script will be executed exactly once.  For
		simple, persistence consoles (like a serial console).  But with
		other other kinds of consoles, there may be multiple, transient
		sessions (such as Telnet and USB consoles).  In these cases, you
		may need another script that is executed at the beginning of each
		session.  Selecting this option enables support for such a login
		script

config NSH_ROMFSMOUNTPT
	string "ROMFS mount point"
	default "/etc"
	---help---
		The default mountpoint for the ROMFS volume is /etc, but that
		can be changed with this setting.  This must be a absolute path
		beginning with '/'.

config NSH_SYSINITSCRIPT
	string "Relative path to sysinit script"
	default "init.d/rc.sysinit"
	---help---
		This is the relative path to the sysinit script within the mountpoint.
		The default is init.d/rc.sysinit. This is a relative path and must not
		start with '/'.

config NSH_INITSCRIPT
	string "Relative path to startup script"
	default "init.d/rcS"
	---help---
		This is the relative path to the startup script within the mountpoint.
		The default is init.d/rcS.  This is a relative path and must not
		start with '/'.

config NSH_RCSCRIPT
	string "Relative path to login script"
	default ".nshrc"
	depends on NSH_ROMFSRC
	---help---
		This is the relative path to the login script within the mountpoint.
		The default is .nshrc.  This is a relative path and must not
		start with '/'.

config NSH_ROMFSDEVNO
	int "ROMFS block device minor number"
	default 0
	---help---
		This is the minor number of the ROMFS block device.  The default is
		'0' corresponding to /dev/ram0.

config NSH_ROMFSSECTSIZE
	int "ROMFS sector size"
	default 64
	---help---
		This is the sector size to use with the ROMFS volume.  Since the
		default volume is very small, this defaults to 64 but should be
		increased if the ROMFS volume were to be become large.  Any value
		selected must be a power of 2.

choice
	prompt "ROMFS header location"
	default NSH_DEFAULTROMFS

config NSH_DEFAULTROMFS
	bool "Default ROMFS header path"
	---help---
		Selects the default header located in the source directory of the
		NSH library.

config NSH_ARCHROMFS
	bool "Architecture-specific ROMFS path"
	---help---
		Enable this option to provide an architecture-specific ROMFS
		header at arch/<boardname>/nsh_romfsimg.h.  Note that this header
		will be linked (or copied) from nuttx/boards/<arch>/<chip>/<board>/include
		and should be stored at that location in the nuttx boards/
		sub-directory.

config NSH_CUSTOMROMFS
	bool "Custom ROMFS header path"
	---help---
		Enable this option to provide a custom ROMFS header. The path to
		the header file can be specified in the option "Custom ROMFS header
		file".

endchoice

if NSH_CUSTOMROMFS

config NSH_CUSTOMROMFS_HEADER
	string "Custom ROMFS header file path"
	default ""
	---help---
		Specifies the path to the custom ROMFS header file.  This must be
		either a full path or a path relative to one of the include file
		search paths provided in your CFLAGS.

endif #NSH_CUSTOMROMFS

config NSH_FATDEVNO
	int "FAT block device minor number"
	default 1
	depends on FS_FAT
	---help---
		When the default rcS file used when NSH_ROMFSETC is selected, it
		will mount a FAT FS under /tmp. This is the minor number of the FAT
		FS block device.  The default is '1' corresponding to /dev/ram1.

config NSH_FATSECTSIZE
	int "FAT sector size"
	default 512
	depends on FS_FAT
	---help---
		When the default rcS file used when NSH_ROMFSETC is selected, it
		will mount a FAT FS under /tmp. This is the sector size use with the
		FAT FS. Default is 512.

config NSH_FATNSECTORS
	int "FAT number of sectors"
	default 1024
	depends on FS_FAT
	---help---
		When the default rcS file used when NSH_ROMFSETC is selected, it
		will mount a FAT FS under /tmp. This is the number of sectors to use
		with the FAT FS.  Default is 1024.  The amount of memory used by the
		FAT FS will be NSH_FATSECTSIZE * NSH_FATNSECTORS bytes.

config NSH_FATMOUNTPT
	string "FAT mount point"
	default "/tmp"
	depends on FS_FAT
	---help---
		When the default rcS file used when NSH_ROMFSETC is selected, it
		will mount a FAT FS under /tmp. This is the location where the FAT
		FS will be mounted.  Default is "/tmp".

config NSH_SCRIPT_REDIRECT_PATH
	string "rcS redirect output"
	default ""
	---help---
		This option can redirect rcS output.such as /dev/log or other.

endif # NSH_ROMFSETC
endmenu # Scripting Support

menu "Console Configuration"

config NSH_CONSOLE
	bool "Use console"
	default y
	---help---
		If NSH_CONSOLE is set to 'y', then a character driver
		console front-end is selected (/dev/console).

		Normally, the serial console device is a UART and RS-232
		interface.  However, if USBDEV is defined, then a USB
		serial device may, instead, be used if the one of
		the following are defined:

		PL2303 and PL2303_CONSOLE - Set up the Prolifics PL2303
		emulation as a console device at /dev/console.

		CDCACM and CDCACM_CONSOLE - Set up the CDC/ACM serial
		device as a console device at dev/console.

		NSH_USBCONSOLE and NSH_USBCONDEV - Sets up some other USB
		serial device as the NSH console (not necessarily dev/console).

config NSH_USBCONSOLE
	bool "Use a USB serial console"
	default n
	depends on BOARDCTL && NSH_CONSOLE && USBDEV && (CDCACM || PL2303)
	select BOARDCTL_USBDEVCTRL
	---help---
		If defined, then the an arbitrary USB serial device may be used
		to as the NSH console.  In this case, NSH_USBCONDEV must be defined
		to indicate which USB serial device to use as the console.

if NSH_USBCONSOLE

config NSH_USBCONDEV
	string "USB serial console device"
	default "/dev/ttyACM0" if CDCACM
	default "/dev/ttyUSB0" if !CDCACM
	---help---
		If NSH_USBCONSOLE is set to 'y', then NSH_USBCONDEV must
		also be set to select the USB device used to support the
		NSH console.   This should be set to the quoted name of a
		read-/write-able USB driver.  Default: "/dev/ttyACM0".

config USBDEV_MINOR
	int "USB serial console device minor number"
	default 0
	---help---
		If there are more than one USB devices, then a USB device
		minor number may also need to be provided. Default: 0

endif # NSH_USBCONSOLE

config NSH_ALTCONDEV
	bool "Alternative console device"
	default n
	depends on NSH_CONSOLE && !NSH_USBCONSOLE && !NSH_TELNET
	---help---
		If CONFIG_NSH_CONSOLE is set to y, then CONFIG_NSH_ALTCONDEV may
		also be selected to enable use of an alternate character device
		to support the NSH console. If CONFIG_NSH_ALTCONDEV is selected,
		then NSH_ALTSTDIN, NSH_ALTSTDOUT and NSH_ALTSTDERR must be set
		to select the serial devices used to support the NSH console.
		This may be useful, for example, to separate the NSH command
		line from the system console when the system console is used to
		provide debug output.

		Default: stdin, stderr and stdout (probably "/dev/console")

		NOTE 1: When any other device other than /dev/console is used
		for a user interface, (1) linefeeds (\n) will not be expanded to
		carriage return / linefeeds (\r\n).  You will need to set
		your terminal program to account for this.  And (2) input is
		not automatically echoed so you will have to turn local echo on.

		NOTE 2:  This option forces the console of all sessions to use
		NSH_ALTSTD(IN/OUT/ERR).  Hence, this option only makes sense for
		a system that supports only a single session.  This option is,
		in particular, incompatible with Telnet sessions because each
		Telnet session must use a different console device.

if NSH_ALTCONDEV

config NSH_ALTSTDIN
	string "Alternative console \"stdin\" device name"
	default "/dev/console"
	---help---
		If CONFIG_NSH_CONSOLE is set to y, then CONFIG_NSH_ALTCONDEV may
		also be selected to enable use of an alternate character device
		to support the NSH console. If CONFIG_NSH_ALTCONDEV is selected,
		then NSH_ALTSTDIN must be set to select the "stdin" device to
		support the NSH console.   This should be set to the quoted name
		of a readable character driver such as:

			NSH_ALTSTDIN="/dev/ttyS1".

		This way the input will come from "/dev/ttyS1".

config NSH_ALTSTDOUT
	string "Alternative console \"stdout\" device name"
	default "/dev/console" if !SLCD_CONSOLE
	default "/dev/slcd0"   if SLCD_CONSOLE
	---help---
		If CONFIG_NSH_CONSOLE is set to y, then CONFIG_NSH_ALTCONDEV may
		also be selected to enable use of an alternate character device
		to support the NSH console. If CONFIG_NSH_ALTCONDEV is selected,
		then NSH_ALTSTDOUT must be set to select the "stdout" device to
		support the NSH console.   This should be set to the quoted name
		of a write-able character driver such as:

			NSH_ALTSTDIN="/dev/ttyS1".

		This way the standard output will go to "/dev/ttyS1".

config NSH_ALTSTDERR
	string "Alternative console \"stderr\" device name"
	default "/dev/console" if !SLCD_CONSOLE
	default "/dev/slcd0"   if SLCD_CONSOLE
	---help---
		If CONFIG_NSH_CONSOLE is set to y, then CONFIG_NSH_ALTCONDEV may
		also be selected to enable use of an alternate character device
		to support the NSH console. If CONFIG_NSH_ALTCONDEV is selected,
		then NSH_ALTSTDERR must be set to select the "stderr" device to
		support the NSH console.   This should be set to the quoted name
		of a write-able character driver such as:

			NSH_ALTSTDIN="/dev/ttyS1".

		This way the standard error output will go to "/dev/ttyS1".

endif # NSH_ALTCONDEV

config NSH_USBKBD
	bool "Use USB keyboard input"
	default n
	depends on NSH_CONSOLE && USBHOST_HIDKBD && !NSH_USBCONSOLE
	---help---
		Normally NSH uses the same device for stdin, stdout, and stderr.  By
		default, that device is /dev/console.  If this option is selected,
		then NSH will use a USB HID keyboard for stdin.  In this case, the
		keyboard is connected directly to the target (via a USB host
		interface) and the data from the keyboard will drive NSH.  NSH
		output (stdout and stderr) will still go to /dev/console.

if NSH_USBKBD

config NSH_USBKBD_DEVNAME
	string "USB keyboard device"
	default "/dev/kbda"
	---help---
		If NSH_USBKBD is set to 'y', then NSH_USBKBD_DEVNAME must also be
		set to select the USB keyboard device used to support the NSH
		console input.   This should be set to the quoted name of a read-
		able keyboard driver. Default: "/dev/kbda".

endif #NSH_USBKBD
endmenu # Console Configuration

menu "USB Device Trace Support"
	depends on USBDEV && (DEBUG_FEATURES || USBDEV_TRACE)

config NSH_USBDEV_TRACE
	bool "Enable Builtin USB Trace Support"
	default n
	---help---
		Enable builtin USB trace support in NSH.  If selected, buffered USB
		trace data will be presented each time a command is provided to NSH.
		The USB trace data will be sent to the console unless CONFIG_DEBUG_FEATURES
		set or unless you are using a USB console.  In those cases, the trace
		data will go to the SYSLOG device.

		If not enabled, the USB trace support can be provided by external
		logic such as nuttx/drivers/usbmonitor.

if NSH_USBDEV_TRACE

config NSH_USBDEV_TRACEINIT
	bool "Show initialization events"
	default n
	---help---
		Show initialization events

config NSH_USBDEV_TRACECLASS
	bool "Show class driver events"
	default n
	---help---
		Show class driver events

config NSH_USBDEV_TRACETRANSFERS
	bool "Show data transfer events"
	default n
	---help---
		Show data transfer events

config NSH_USBDEV_TRACECONTROLLER
	bool "Show controller events"
	default n
	---help---
		Show controller events

config NSH_USBDEV_TRACEINTERRUPTS
	bool "Show interrupt-related events"
	default n
	---help---
		Show interrupt-related events

endif # NSH_USBDEV_TRACE
endmenu # USB Device Trace Support

config NSH_ARCHINIT
	bool "Have architecture-specific initialization"
	default n
	select BOARDCTL
	---help---
		Set if your board provides architecture specific initialization
		via the board-interface function boardctl().  The boardctl()
		function will be called early in NSH initialization to allow
		board logic to do such things as configure MMC/SD slots.

menu "Networking Configuration"
	depends on NET

config NSH_NETINIT
	bool "Network initialization"
	default y
	depends on NET
	select NETUTILS_NETINIT
	---help---
		This option enables/disables all network initialization in NSH.

if !NSH_DISABLE_WGET
config NSH_WGET_BUFF_SIZE
	int "wget buffer size"
	default 512
	---help---
		Buffer size for wget command
endif # NSH_DISABLE_WGET

endmenu # Networking Configuration"

menu "Telnet Configuration"
	depends on NETUTILS_TELNETD

config NSH_TELNET
	bool "Use Telnet console"
	default !DEFAULT_SMALL
	depends on NETUTILS_TELNETD
	---help---
		If NSH_TELNET is set to 'y', then a TELENET
		server front-end is selected.  When this option is provided,
		you may log into NuttX remotely using telnet in order to
		access NSH.

if NSH_TELNET

config NSH_TELNETD_PORT
	int "Telnet port number"
	default 23
	---help---
		The telnet daemon will listen on this TCP port number for connections.
		Default: 23

config NSH_TELNETD_DAEMONPRIO
	int "Telnet daemon priority"
	default 100
	---help---
		Priority of the Telnet daemon. Default: 100

config NSH_TELNETD_DAEMONSTACKSIZE
	int "Telnet daemon stack size"
	default DEFAULT_TASK_STACKSIZE
	---help---
		Stack size allocated for the Telnet daemon. Default: 2048

config NSH_TELNETD_CLIENTPRIO
	int "Telnet client priority"
	default 100
	---help---
		Priority of the Telnet client. Default: 100

config NSH_TELNETD_CLIENTSTACKSIZE
	int "Telnet client stack size"
	default DEFAULT_TASK_STACKSIZE
	---help---
		Stack size allocated for the Telnet client. Default: 2048

config NSH_IOBUFFER_SIZE
	int "Telnet I/O buffer size"
	default 512
	---help---
		Determines the size of the I/O buffer to use for sending/
		receiving TELNET commands/responses.  Default: 512

endif # NSH_TELNET
endmenu # Telnet Configuration

config NSH_LOGIN
	bool
	default n

config NSH_CONSOLE_LOGIN
	bool "Console Login"
	default n
	select NSH_LOGIN
	---help---
		If defined, then the console user will be required to provide a
		username and password to start the NSH shell.

config NSH_TELNET_LOGIN
	bool "Telnet Login"
	default n
	depends on NSH_TELNET
	select NSH_LOGIN
	---help---
		If defined, then the Telnet user will be required to provide a
		username and password to start the NSH shell.

if NSH_LOGIN

choice
	prompt "Verification method"
	default NSH_LOGIN_PASSWD if FSUTILS_PASSWD
	default NSH_LOGIN_FIXED if !FSUTILS_PASSWD

config NSH_LOGIN_FIXED
	bool "Fixed username/password"
	---help---
		Verify user credentials by matching to fixed username and password
		strings

config NSH_LOGIN_PLATFORM
	bool "Platform username/password"
	---help---
		Call a platform-specific function to perform the verification of
		user credentials.  In this case, the platform-specific logic must
		provide a function with the following prototype:

			int platform_user_verify(FAR const char *username, FAR const char *password);

		which is prototyped an described in apps/include/nshlib/nshlib.h and
		which may be included like:

			#include "nshlib/nshlib.h"

		An appropriate place to implement this function might be in the
		directory apps/platform/<board>.

config NSH_LOGIN_PASSWD
	bool "Encrypted password file"
	depends on FSUTILS_PASSWD
	---help---
		Use the content of an encrypted password file to verify user
		credentials.  This option requires that you have selected
		CONFIG_FSUTILS_PASSWD to enable the access methods of
		apps/fsutils/passwd.

endchoice # Verification method

config NSH_LOGIN_USERNAME
	string "Login username"
	default "admin"
	depends on !NSH_LOGIN_PASSWD
	---help---
		Login user name.  Default: "admin"

config NSH_LOGIN_PASSWORD
	string "Login password"
	default "Administrator"
	depends on !NSH_LOGIN_PASSWD
	---help---
		Login password:  Default: "Administrator"

config NSH_LOGIN_FAILDELAY
	int "Login failure delay"
	default 0
	---help---
		Login failure delay in milliseconds.  The system will pause this
		amount of time after each failed login attempt in order to
		discourage people from cracking the password by brute force.  The
		value zero may be supplied to disable the delay.

config NSH_LOGIN_FAILCOUNT
	int "Login retry count"
	default 3
	---help---
		Number of login retry attempts.

endif # NSH_LOGIN
endif # NSH_LIBRARY
endmenu # NSH Library
