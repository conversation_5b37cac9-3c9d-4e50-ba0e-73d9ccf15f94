# Wireless / `wapi` WAPI

```
`_`_`_`_____`_____`__`
|`|`|`|``_``|``_``|``|
|`|`|`|`````|```__|``|
|_____|__|__|__|``|__|
```

WAPI (Wireless API) provides an easy-to-use function set to configure wireless
network interfaces on a GNU/Linux system. One can think WAPI as a lightweight C
API for `iwconfig`, `wlanconfig`, `ifconfig`, and `route` commands. (But it is
not a thin wrapper for these command line tools.) It is designed to be used in
wireless heteregenous network research projects and supported by BWRC (Berkeley
Wireless Research Center) and WISERLAB (Wireless Information Systems Engineering
Research Laboratory at Özyeğin University).

For source codes, see http://github.com/vy/wapi. The most recent version of the
documentation is (hopefully) always available at http://vy.github.com/wapi.

## License

Copyright (c) 2010, Volkan YAZICI <<EMAIL>> All rights reserved.

Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

- Redistributions of source code must retain the above copyright notice, this
  list of conditions and the following disclaimer.
- Redistributions in binary form must reproduce the above copyright notice, this
  list of conditions and the following disclaimer in the documentation and/or
  other materials provided with the distribution.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON
ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
