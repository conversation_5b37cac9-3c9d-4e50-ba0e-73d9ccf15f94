# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
name: Build

on:
  pull_request:
  push:
    branches:
      - master
      - 'releases/*'
    tags:

concurrency:
  group: build-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  Fetch-Source:
    runs-on: ubuntu-latest
    steps:
      - name: Determine Target Branches
        id: gittargets
        shell: bash
        run: |
          OS_REF=""
          APPS_REF=""

          REF=$GITHUB_REF

          # If a base ref is set this is a PR and we will want to use
          # the base ref instead of the ref that triggered the event
          if [ ${GITHUB_BASE_REF} ]; then
            REF=refs/heads/$GITHUB_BASE_REF
          fi

          echo "Working with ref: $REF"

          # We modify for all tags and release branches
          if [[ $REF =~ refs/heads/releases/*|refs/tags/* ]]; then
            if [[ $REF =~ refs/heads/* ]]
              then
                REF_NAME=${REF##refs/heads/}
                echo "Working with a branch: $REF_NAME"
              else
                REF_NAME=${REF##refs/tags/}
                echo "Working with a tag: $REF_NAME"
            fi

            # Determine the repo and leave that unset to use the normal checkout behavior
            # of using the merge commit instead of HEAD
            case $GITHUB_REPOSITORY in
              "apache/incubator-nuttx")
                # OS
                echo "Triggered by change in OS"
                APPS_REF=$REF_NAME
                ;;

              "apache/incubator-nuttx-apps" )
                # APPS
                OS_REF=$REF_NAME
                echo "Triggered by change in APPS"
                ;;

              *)
                echo "Trigger by change on $GITHUB_REPOSITORY. This is unexpected."
                ;;
            esac
          fi

          echo ::set-output name=os_ref::$OS_REF
          echo ::set-output name=apps_ref::$APPS_REF

      - name: Checkout nuttx repo
        uses: actions/checkout@v3
        with:
          repository: apache/incubator-nuttx
          ref: ${{ steps.gittargets.outputs.os_ref }}
          path: sources/nuttx
          fetch-depth: 1
      - name: Checkout nuttx repo tags
        run: git -C sources/nuttx fetch --tags

      - name: Checkout apps repo
        uses: actions/checkout@v3
        with:
          repository: apache/incubator-nuttx-apps
          ref: ${{ steps.gittargets.outputs.apps_ref }}
          path: sources/apps
          fetch-depth: 1

      - name: Tar sources
        run: tar zcf sources.tar.gz sources
      - name: Archive Source Bundle
        uses: actions/upload-artifact@v3
        with:
          name: source-bundle
          path: sources.tar.gz

  Linux:
    needs: Fetch-Source
    runs-on: ubuntu-latest
    env:
      DOCKER_BUILDKIT: 1

    strategy:
      matrix:
        boards: [arm-01, arm-02, arm-03, arm-04, arm-05, arm-06, arm-07, arm-08, arm-09, arm-10, arm-11, arm-12, arm-13, other, risc-v, sim-01, sim-02, xtensa]

    steps:
      - name: Download Source Artifact
        uses: actions/download-artifact@v3
        with:
          name: source-bundle
          path: .
      - name: Extract sources
        run: tar zxf sources.tar.gz
      - name: Docker Login
        uses: azure/docker-login@v1
        with:
          login-server: ghcr.io
          username: ${GITHUB_ACTOR}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Docker Pull
        run: docker pull ghcr.io/apache/incubator-nuttx/apache-nuttx-ci-linux
      - name: Export NuttX Repo SHA
        run: echo "nuttx_sha=`git -C sources/nuttx rev-parse HEAD`" >> $GITHUB_ENV
      - name: Run builds
        uses: ./sources/nuttx/.github/actions/ci-container
        env:
          BLOBDIR: /tools/blobs
        with:
          run: |
            echo "::add-matcher::sources/nuttx/.github/gcc.json"
            export CCACHE_DIR=`pwd`/ccache
            mkdir $CCACHE_DIR
            export ARTIFACTDIR=`pwd`/buildartifacts
            git config --global --add safe.directory /github/workspace/sources/nuttx
            git config --global --add safe.directory /github/workspace/sources/apps
            cd sources/nuttx/tools/ci
            ./cibuild.sh -A -c testlist/${{matrix.boards}}.dat
            ccache -s
      - uses: actions/upload-artifact@v3
        with:
          name: linux-builds
          path: buildartifacts/
        continue-on-error: true

  macOS:
    permissions:
      contents: none
    runs-on: macos-10.15
    needs: Fetch-Source
    strategy:
      matrix:
        boards: [macos, sim-01, sim-02]
    steps:
      - name: Download Source Artifact
        uses: actions/download-artifact@v3
        with:
          name: source-bundle
          path: .
      - name: Extract sources
        run: tar zxf sources.tar.gz
      - name: Restore Tools Cache
        id: cache-tools
        uses: actions/cache@v3
        env:
          cache-name: ${{ runner.os }}-cache-tools
        with:
          path: prebuilt
          key: ${{ runner.os }}-tools-${{ hashFiles('./sources/nuttx/tools/ci/cibuild.sh') }}

      - name: Export NuttX Repo SHA
        run: echo "nuttx_sha=`git -C sources/nuttx rev-parse HEAD`" >> $GITHUB_ENV
      - name: Run Builds
        run: |
          echo "::add-matcher::sources/nuttx/.github/gcc.json"
          export CCACHE_DIR=`pwd`/ccache
          mkdir $CCACHE_DIR
          export ARTIFACTDIR=`pwd`/buildartifacts
          cd sources/nuttx/tools/ci
          ./cibuild.sh -i -A -c testlist/${{matrix.boards}}.dat
          ccache -s
      - uses: actions/upload-artifact@v3
        with:
          name: macos-builds
          path: buildartifacts/
        continue-on-error: true
