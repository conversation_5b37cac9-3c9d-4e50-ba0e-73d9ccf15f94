# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
name: Check

on:
  pull_request:

concurrency:
  group: check-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  check:
    runs-on: ubuntu-18.04

    steps:
      - name: Checkout nuttx repo
        uses: actions/checkout@v3
        with:
          repository: apache/incubator-nuttx
          path: nuttx
          fetch-depth: 0

      - name: Checkout apps repo
        uses: actions/checkout@v3
        with:
          repository: apache/incubator-nuttx-apps
          path: apps
          fetch-depth: 0

      - name: Check Pull Request
        run: |
          echo "::add-matcher::nuttx/.github/nxstyle.json"
          cd apps
          commits="${{ github.event.pull_request.base.sha }}..HEAD"
          git log --oneline $commits
          echo "../nuttx/tools/checkpatch.sh -m -g $commits"
          ../nuttx/tools/checkpatch.sh -m -g $commits
