/****************************************************************************
 * apps/examples/elf/tests/task/parent.c
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <sys/types.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <sched.h>
#include <semaphore.h>
#include <errno.h>

/****************************************************************************
 * Private Data
 ****************************************************************************/

static char child_name[] = "child";
static char child_arg[] = "Hello from your parent!";
static sem_t g_sem;

#if CONFIG_TASK_NAME_SIZE == 0
static char no_name[] = "<noname>";
#endif

/****************************************************************************
 * Privite Functions
 ****************************************************************************/

/* NOTE: it is necessary for functions that are referred to by function pointers
 * pointer to be declared with global scope (at least for ARM).  Otherwise,
 * a relocation type that is not supported by ELF is generated by GCC.
 */

int child_task(int argc, char **argv)
{
  printf("Child: execv was successful!\n");
  printf("Child: argc=%d\n", argc);

  if (argc != 2)
    {
      printf("Child: expected argc to be 2\n");
      printf("Child: Exit-ting with status=2\n");
      exit(2);
    }
  printf("Child: argv[0]=\"%s\"\n", argv[0]);

#if CONFIG_TASK_NAME_SIZE == 0
  if (strcmp(argv[0], no_name) != 0)
    {
      printf("Child: expected argv[0] to be \"%s\"\n", no_name);
      printf("Child: Exit-ting with status=3\n");
      exit(3);
    }
#else
  if (strncmp(argv[0], child_name, CONFIG_TASK_NAME_SIZE) != 0)
    {
      printf("Child: expected argv[0] to be \"%s\"\n", child_name);
      printf("Child: Exit-ting with status=3\n");
      exit(3);
    }
#endif

  printf("Child: argv[1]=\"%s\"\n", argv[1]);

  if (strcmp(argv[1], child_arg) != 0)
    {
      printf("Child: expected argv[1] to be \"%s\"\n", child_arg);
      printf("Child: Exit-ting with status=4\n");
      exit(4);
    }

  printf("Child: Exit-ting with status=0\n");
  sem_post(&g_sem);
  return 0;
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/

int main(int argc, char **argv)
{
  pid_t parent_pid = getpid();
  char *child_argv[2];
  pid_t child_pid;

  printf("Parent: Started, pid=%d\n", parent_pid);

  sem_init(&g_sem, 0, 0);

  printf("Parent: Calling task_create()\n");

  child_argv[0] = child_arg;
  child_argv[1] = 0;
  child_pid = task_create(child_name, 50, 2048, child_task, (FAR char * const *)child_argv);
  if (child_pid < 0)
    {
      printf("Parent: task_create failed: %d\n", errno);
    }

  printf("Parent: Waiting for child (pid=%d)\n", child_pid);
  sem_wait(&g_sem);
  printf("Parent: Exit-ing\n");
  sem_destroy(&g_sem);
  return 0;
}
