#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_ELF
	tristate "ELF Loader Example"
	default n
	select LIBC_EXECFUNCS
	depends on ELF
	---help---
		Enable the ELF loader example

if EXAMPLES_ELF

choice
	prompt "ELF File System"
	default EXAMPLES_ELF_ROMFS  if   FS_ROMFS && BUILD_FLAT
	default EXAMPLES_ELF_CROMFS if  !FS_ROMFS && FS_CROMFS && BUILD_FLAT
	default EXAMPLES_ELF_EXTERN if (!FS_ROMFS && !FS_CROMFS) || !BUILD_FLAT

config EXAMPLES_ELF_ROMFS
	bool "ROMFS"
	depends on FS_ROMFS && BUILD_FLAT
	---help---
		Automatically generates and mounts an internal ROMFS file
		system

config EXAMPLES_ELF_CROMFS
	bool "CROMFS"
	depends on FS_CROMFS && BUILD_FLAT
	---help---
		Automatically generates and mounts an internal CROMFS file
		system

config EXAMPLES_ELF_EXTERN
	bool "External File system"
	---help---
		The file system is assumed to reside on some external media
		such as an SD card or a USB FLASH drive.  In this case, that
		external file system must be created manually by copying the
		files in apps/examples/elf/tests/romfs to the volume.

		The external volume can optionally be mounted by the test if
		the CONFIG_EXAMPLES_FSMOUNT option is also selected.

endchoice # ROM File System

config EXAMPLES_ELF_FSMOUNT
	bool "Mount external file system"
	default y
	depends on EXAMPLES_ELF_EXTERN
	---help---
		Automatically mount the external file system using the block
		device provided by CONFIG_EXAMPLES_ELF_DEVPATH and the file
		system type specified by CONFIG_EXAMPLES_ELF_FSTYPE.

config EXAMPLES_ELF_FSREMOVEABLE
	bool "Removable file system"
	default n
	depends on EXAMPLES_ELF_FSMOUNT
	---help---
		If the external file system is on removable media such as an
		SD card or a USB FLASH driver, then this option may be selected
		to build in logic to wait for the media to be installed and
		initialized before trying to mount it.

config EXAMPLES_ELF_DEVMINOR
	int "ROMFS Minor Device Number"
	default 0
	depends on EXAMPLES_ELF_ROMFS
	---help---
		The minor device number of the ROMFS block. For example, the N in
		/dev/ramN.  Used for registering the RAM block driver that will hold
		the ROMFS file system containing the ELF executables to be tested.
		Default: 0

config EXAMPLES_ELF_FSTYPE
	string "External file system type"
	default "vfat"
	depends on EXAMPLES_ELF_FSMOUNT
	---help---
		The type of the external file system as will be used in the mount()
		command.  Default: "vfat"

config EXAMPLES_ELF_DEVPATH
	string "Block driver device path"
	default "/dev/ram0" if EXAMPLES_ELF_ROMFS
	default "/dev/mmcsd0" if EXAMPLES_ELF_FSMOUNT
	depends on EXAMPLES_ELF_ROMFS || EXAMPLES_ELF_FSMOUNT
	---help---
		The path to the ROMFS/External block driver device.  This must match
		EXAMPLES_ELF_DEVMINOR for the case of ROMFS.  Used for mounting the
		file system containing the ELF executables to be tested.  Default:
		"/dev/ram0" for ROMFS, "/dev/mmcsd0" for the external file system.

config EXAMPLES_ELF_SYSCALL
	bool "Link with SYSCALL library"
	default n
	depends on LIB_SYSCALL
	---help---
		Link with the SYSCALL library.  By default, all undefined symbols
		must be provided via a symbol table.  But if this option is
		selected, then each ELF test program will link with the SYSCALL
		library and will interface with the OS system calls.  In this case,
		those symbols will not be undefined.  If the SYSCALL library is
		available then you probably will want to select this option.

config EXAMPLES_ELF_LIBC
	bool "Link with LIBC"
	default n
	---help---
		Link with the C library (and also math library if it was built).
		By default, all undefined symbols must be provided via a symbol
		table.  But if this option is selected, then each ELF test program
		will link with the C library and will not require symbol table
		entries.  You probably will NOT want this option, however,
		because it will substantially increase the size of code.  For
		example, a separate copy of printf() would be linked with every
		program greatly increasing the total code size.  This option is
		primarily intended only for testing.

config EXAMPLES_ELF_CXX
	bool "uClibc++/libcxx is installed"
	default n
	depends on UCLIBCXX || LIBCXX
	---help---
		By default, uClibc++/libcxx is not installed and configured. If the
		user installs and configures the C++ standard library, this
		example will compile the demos using it.

endif
