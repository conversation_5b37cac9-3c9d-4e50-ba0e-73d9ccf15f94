#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_TIMER_GPIO
	tristate "Timer GPIO example"
	default n
	depends on TIMER && DEV_GPIO
	---help---
		Enable the timer-gpio example. This example uses the timer interrupt
		to periodically change the state of a digital output. The digital
		output may be a relay, a led or anything else. This example can be
		very useful to validate timer drivers by using a logic analyzer
		connected to the digital output. This example differs from the timer
		example because it waits on a sigwaitinfo() instead of using a signal
		handler. This approach ensures a deterministic wake-up time when the
		signal occurs. 

if EXAMPLES_TIMER_GPIO

config EXAMPLES_TIMER_GPIO_TIM_DEVNAME
	string "Timer device name"
	default "/dev/timer0"
	---help---
		This is the name of the timer device that will be used.

config EXAMPLES_TIMER_GPIO_GPIO_DEVNAME
	string "GPIO device name"
	default "/dev/gpio0"
	---help---
		This is the name of the gpio device that will be used.

config EXAMPLES_TIMER_GPIO_INTERVAL
	int "Timer interval (microseconds)"
	default 1000000
	---help---
		This is the timer interval in microseconds.

config EXAMPLES_TIMER_GPIO_SIGNO
	int "Notification signal number"
	default 17
	---help---
		This is the signal number that is used to notify that a timer
		interrupt occurred.

config EXAMPLES_TIMER_GPIO_STACKSIZE
	int "Timer stack size"
	default DEFAULT_TASK_STACKSIZE
	---help---
		This is the stack size allocated when the timer task runs.

config EXAMPLES_TIMER_GPIO_PRIORITY
	int "Timer task priority"
	default 255
	---help---
		This is the priority of the timer task.

config EXAMPLES_TIMER_GPIO_PROGNAME
	string "Timer GPIO program name"
	default "timer_gpio"
	---help---
		This is the name of the program that will be used from the nsh.

endif
