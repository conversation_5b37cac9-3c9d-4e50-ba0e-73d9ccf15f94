/********************************************************************************************
 * apps/examples/nximage/nximage_bitmap.c
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ********************************************************************************************/

/********************************************************************************************
 * Included Files
 ********************************************************************************************/

#include <nuttx/config.h>

#include <stdint.h>
#include <assert.h>

#include <nuttx/nx/nx.h>
#include <nuttx/nx/nxglib.h>

#include "nximage.h"

/********************************************************************************************
 * Pre-processor Definitions
 ********************************************************************************************/

#if CONFIG_EXAMPLES_NXIMAGE_BPP == 24

#  ifdef CONFIG_EXAMPLES_NXIMAGE_GREYSCALE
#    error "24-bit greyscale not supported"
#  endif

#  define IMAGE_NLUTCODES    164  /* Number of unique RGB colors in the image */

#elif CONFIG_EXAMPLES_NXIMAGE_BPP == 16

#  ifdef CONFIG_EXAMPLES_NXIMAGE_GREYSCALE
#    error "16-bit greyscale not supported"
#  endif

#  define IMAGE_NLUTCODES    141  /* Number of unique RGB colors in the image */

#elif CONFIG_EXAMPLES_NXIMAGE_BPP == 8

#  ifdef CONFIG_EXAMPLES_NXIMAGE_GREYSCALE
#    define IMAGE_NLUTCODES  116  /* Number of unique greyscale levels in the image */
#  else
#    define IMAGE_NLUTCODES  27   /* Number of unique RGB colors in the image */
#  endif

#else
#  error "Unsupported pixel format"
#endif

/********************************************************************************************
 * Private Types
 ********************************************************************************************/

struct pix_run_s
{
  uint8_t npix;  /* Number of pixels */
  uint8_t code;  /* Pixel RGB code */
};

/********************************************************************************************
 * Private Data
 ********************************************************************************************/
/* RGB24 (8-8-8) Colors */

#if CONFIG_EXAMPLES_NXIMAGE_BPP == 24

static const nxgl_mxpixel_t g_lut[IMAGE_NLUTCODES] =
{
  0x000000, 0x0a0804, 0x382616, 0x390c0f, 0x390d0f, 0x0a0203, 0x390b0e, 0x3a0d0f,  /* Codes 0-7 */
  0x350d0e, 0x1d0709, 0x24090b, 0x230f0d, 0x3b2817, 0x341511, 0x230a0b, 0x3a2817,  /* Codes 8-15 */
  0x350c0e, 0x1e1c1a, 0x3c3934, 0x390a0d, 0x0b0a09, 0x3c3933, 0x3b2321, 0x3d3d37,  /* Codes 16-23 */
  0x3c3b36, 0x38090b, 0x312e2a, 0x3c312d, 0x391516, 0x3c3c36, 0x3d3c36, 0x241212,  /* Codes 24-31 */
  0x1f0a0b, 0x1f0a0a, 0x1e090a, 0x2d0b0d, 0x3c3833, 0x3d3a36, 0x322f2a, 0x261f16,  /* Codes 32-39 */
  0x2f2617, 0x18130b, 0x3f3e38, 0x1e0507, 0x210a0b, 0x250a0c, 0x3f3f3f, 0x3d3a35,  /* Codes 40-47 */
  0x2e2414, 0x080704, 0x3e3b36, 0x2f2517, 0x3b2423, 0x2f211f, 0x3c3832, 0x363026,  /* Codes 48-55 */
  0x3e3c3a, 0x362f25, 0x390c0e, 0x3b2f2c, 0x3f3e3d, 0x3e3c39, 0x3b0d0f, 0x0a0805,  /* Codes 56-63 */
  0x2f2516, 0x2b1e14, 0x2d2114, 0x20080a, 0x2d0a0d, 0x3d3934, 0x32291c, 0x360d0f,  /* Codes 64-71 */
  0x2c201e, 0x2e2415, 0x2d2115, 0x1e070a, 0x3b2917, 0x3f3d37, 0x1e0406, 0x200a0b,  /* Codes 72-79 */
  0x340b0e, 0x2e0b0d, 0x220607, 0x3c2322, 0x3c3831, 0x373126, 0x1d0306, 0x2c2014,  /* Codes 80-87 */
  0x20090a, 0x030001, 0x1d090a, 0x3d0e10, 0x3b090c, 0x270b0c, 0x2d211f, 0x0a0703,  /* Codes 88-95 */
  0x382414, 0x39080c, 0x3e3c36, 0x2d1e1d, 0x1c0204, 0x1b0002, 0x230609, 0x1f0708,  /* Codes 96-103 */
  0x24080b, 0x342614, 0x3c3935, 0x342716, 0x38100e, 0x391210, 0x3c3731, 0x3e3d37,  /* Codes 104-111 */
  0x3a090c, 0x2b0b0d, 0x2e1e12, 0x381210, 0x3a2120, 0x23090b, 0x2f0c0d, 0x1c0709,  /* Codes 112-119 */
  0x1d0405, 0x380a0d, 0x3b0b0d, 0x2d090b, 0x1e0708, 0x340a0c, 0x230709, 0x2d1e1c,  /* Codes 120-127 */
  0x0a0001, 0x3c3a35, 0x351f13, 0x1e080a, 0x2c2115, 0x170a08, 0x1f0608, 0x2c1f13,  /* Codes 128-135 */
  0x340c0e, 0x3c0d10, 0x32080a, 0x3d3c37, 0x23100d, 0x3b3526, 0x38070b, 0x2b2014,  /* Codes 136-143 */
  0x3a0c0f, 0x2a1e1c, 0x070001, 0x0d0c0b, 0x19140c, 0x16110b, 0x322a1d, 0x3f3e3e,  /* Codes 144-151 */
  0x3e3b37, 0x37302c, 0x2f2c28, 0x39352d, 0x201e1c, 0x2a261f, 0x3c3d37, 0x2b080a,  /* Codes 152-159 */
  0x1d0608, 0x260a0c, 0x2b110e, 0x381f14                                           /* Codes 160-163 */
};

/* RGB16 (565) Colors (four of the colors in this map are duplicates) */

#elif CONFIG_EXAMPLES_NXIMAGE_BPP == 16

static const nxgl_mxpixel_t g_lut[IMAGE_NLUTCODES] =
{
  0x0000, 0x1105, 0x5cdc, 0x399c, 0x41bc, 0x0845, 0x397c, 0x39bc, 0x41bd, 0x39ba,  /* Codes 0-9 */
  0x28ee, 0x3132, 0x31f2, 0x5d1d, 0x42ba, 0x3152, 0x399a, 0x6b8f, 0xd73e, 0x315c,  /* Codes 10-19 */
  0x2945, 0x8c7d, 0xe7be, 0xdf7e, 0x313c, 0xadd9, 0xb63e, 0x5abd, 0xdf9e, 0xdf9f,  /* Codes 20-29 */
  0x4a52, 0x2950, 0x292f, 0x3176, 0xcf1e, 0xdf5f, 0xadf9, 0x5bf3, 0x5cd8, 0x326c,  /* Codes 30-39 */
  0xe7df, 0x18af, 0x3153, 0xdf5e, 0xffff, 0x5497, 0x10e4, 0xdf7f, 0x5cb8, 0x8c9d,  /* Codes 40-49 */
  0x8437, 0x9e1b, 0xef9f, 0x9dfb, 0xb5fe, 0xffdf, 0x41be, 0x53d6, 0x5436, 0x2910,  /* Codes 50-59 */
  0x3156, 0x7539, 0x39bb, 0x7c16, 0x95fb, 0x28ef, 0x653d, 0xe7bf, 0x188f, 0x397a,  /* Codes 60-69 */
  0x3977, 0x20d1, 0x8c7e, 0xe79f, 0x9e3b, 0x186e, 0x5416, 0x2930, 0x0801, 0x41de,  /* Codes 70-79 */
  0x313d, 0x3173, 0x7c36, 0x08e5, 0x549c, 0xd75f, 0x311c, 0x73d6, 0x3150, 0x315d,  /* Codes 80-89 */
  0x104e, 0x080e, 0x20f0, 0x2912, 0x54da, 0xdf3e, 0x5cfa, 0x3a1c, 0x425c, 0xcefe,  /* Codes 90-99 */
  0x4bd7, 0x843d, 0x2932, 0x3997, 0xf7df, 0x397d, 0x2936, 0x20ef, 0x315a, 0x20f1,  /* Codes 100-109 */
  0x0005, 0xd75e, 0x53fa, 0x290f, 0x214b, 0x20cf, 0x4bf6, 0x2919, 0x3212, 0x9ebd,  /* Codes 110-119 */
  0x28fc, 0x5415, 0x399d, 0x73d5, 0x0804, 0x3187, 0x328d, 0x2a2b, 0x7559, 0xe77f,  /* Codes 120-129 */
  0xb61c, 0x3186, 0xa598, 0xbebd, 0x73d0, 0x84d5, 0x5cb7, 0x2916, 0x20ce, 0x3a35,  /* Codes 130-139 */
  0x53fc                                                                           /* Codes 140-140 */
};

/* 8-bit color lookups.  NOTE:  This is really dumb!  The lookup index is 8-bits and it used
 * to lookup an 8-bit value.  There is no savings in that!  It would be better to just put
 * the 8-bit color/greyscale value in the run-length encoded image and save the cost of these
 * pointless lookups.  But these p;ointless lookups do make the logic compatible with the
 * 16- and 24-bit types.
 */

#elif CONFIG_EXAMPLES_NXIMAGE_BPP == 8
#  ifdef CONFIG_EXAMPLES_NXIMAGE_GREYSCALE

/* 8-bit Greyscale */

static const uint8_t g_lut[IMAGE_NLUTCODES] =
{
  0x00, 0x19, 0x8b, 0x46, 0x4a, 0x0b, 0x8d, 0x41, 0x44, 0x27, 0x32, 0x92, 0x59, 0x34, 0x6d, 0xdb,  /* Codes 0-15 */
  0x3f, 0x26, 0x91, 0xec, 0xe5, 0x39, 0xb4, 0xc0, 0x62, 0xe6, 0x4c, 0x2f, 0x2e, 0x3c, 0xda, 0xe4,  /* Codes 16-31 */
  0x72, 0x86, 0xed, 0x1d, 0x35, 0xe2, 0xfc, 0xe3, 0x7d, 0x16, 0x97, 0x87, 0xee, 0xb2, 0x42, 0xbc,  /* Codes 32-47 */
  0xf6, 0x4b, 0x1c, 0x70, 0x90, 0x77, 0x2c, 0xdf, 0x96, 0x29, 0x45, 0x7f, 0x81, 0xb1, 0x28, 0xea,  /* Codes 48-63 */
  0x40, 0x3d, 0x23, 0x95, 0xe9, 0xd6, 0xb7, 0x2d, 0x02, 0x3a, 0x85, 0x17, 0x82, 0xe7, 0x7b, 0x33,  /* Codes 64-79 */
  0x0e, 0x25, 0x84, 0xe0, 0x8c, 0x4d, 0x56, 0x1e, 0x3b, 0x6e, 0x31, 0x3e, 0xf3, 0x1a, 0x20, 0x04,  /* Codes 80-95 */
  0xe1, 0x74, 0x2a, 0x79, 0x21, 0x71, 0xc4, 0x76, 0x48, 0x98, 0xf7, 0x94, 0xe8, 0xbe, 0xac, 0xc9,  /* Codes 96-111 */
  0x75, 0x8e, 0x49, 0x7a                                                                           /* Codes 112-115 */
};

#  else /* CONFIG_EXAMPLES_NXIMAGE_GREYSCALE */

/* RGB8 (332) Colors */

static const nxgl_mxpixel_t g_lut[IMAGE_NLUTCODES] =
{
  0x00, 0x25, 0x77, 0x4b, 0x01, 0x47, 0x26, 0x4a, 0x4f, 0x72, 0xdf, 0x93, 0xff, 0x27, 0xbb, 0xdb,  /* Codes 0-15 */
  0x6f, 0x29, 0x53, 0x97, 0x73, 0x22, 0x05, 0x02, 0x57, 0xbf, 0x4e                                 /* Codes 16-26 */
};

#  endif
#else
# error "Unsupported pixel format"
#endif

#if CONFIG_EXAMPLES_NXIMAGE_BPP == 24

static const struct pix_run_s g_nuttx[] =
{
  { 76,   0}, {  1,   1}, {  1,   2}, {  1,   3}, {  4,   4}, {  1,   5}, { 76,   0},              /* Row 0 */
  { 75,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  2,   7}, {  3,   4}, {  1,   5},  /* Row 1 */
  { 75,   0},
  { 74,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,   9}, {  1,  10},  /* Row 2 */
  {  1,   7}, {  3,   4}, {  1,   5}, { 74,   0},
  { 73,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  12},  /* Row 3 */
  {  1,  13}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 73,   0},
  { 72,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15},  /* Row 4 */
  {  1,   6}, {  1,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 72,   0},
  { 71,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15},  /* Row 5 */
  {  1,   6}, {  3,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 71,   0},
  { 70,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15},  /* Row 6 */
  {  1,   6}, {  5,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 70,   0},
  { 69,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15},  /* Row 7 */
  {  1,   6}, {  7,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 69,   0},
  { 68,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15},  /* Row 8 */
  {  1,   6}, {  9,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 68,   0},
  { 67,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15},  /* Row 9 */
  {  1,   6}, { 11,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 67,   0},
  { 66,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15},  /* Row 10 */
  {  1,   6}, { 13,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 66,   0},
  { 65,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15},  /* Row 11 */
  {  1,   6}, { 15,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 65,   0},
  { 64,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15},  /* Row 12 */
  {  1,   6}, { 17,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 64,   0},
  { 63,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15},  /* Row 13 */
  {  1,   6}, { 19,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 63,   0},
  { 62,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15},  /* Row 14 */
  {  1,   6}, { 21,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 62,   0},
  { 61,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15},  /* Row 15 */
  {  1,   6}, { 23,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 61,   0},
  { 60,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15},  /* Row 16 */
  {  1,   6}, { 25,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 60,   0},
  { 59,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15},  /* Row 17 */
  {  1,   6}, { 27,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 59,   0},
  { 58,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15},  /* Row 18 */
  {  1,   6}, { 29,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 26,   0},
  {  1,  17}, {  5,  18}, {  1,  17}, { 25,   0},
  { 57,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15},  /* Row 19 */
  {  1,   6}, { 10,   4}, {  5,  19}, { 16,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4},
  {  1,   5}, { 23,   0}, {  1,  20}, {  1,  18}, {  1,  21}, {  5,  18}, {  1,  21}, {  1,  18},
  {  1,  20}, { 23,   0},
  { 56,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15},  /* Row 20 */
  {  1,   6}, {  9,   4}, {  1,  19}, {  1,  22}, {  1,  23}, {  3,  24}, {  1,  23}, {  1,  22},
  {  1,  25}, { 15,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 22,   0},
  { 11,  18}, {  1,  20}, { 22,   0},
  { 23,   0}, {  1,  17}, {  5,  18}, {  1,  26}, { 25,   0}, {  1,   1}, {  1,   2}, {  1,   6},  /* Row 21 */
  {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  9,   4}, {  1,  19}, {  1,  27},
  {  1,  21}, {  5,  18}, {  1,  21}, {  1,  23}, {  1,  28}, { 15,   4}, {  1,  16}, {  1,  14},
  {  1,   7}, {  3,   4}, {  1,   5}, { 20,   0}, {  1,  17}, {  1,  21}, { 11,  18}, {  1,  20},
  { 21,   0},
  { 21,   0}, {  1,  20}, {  1,  18}, {  1,  21}, {  5,  18}, {  1,  21}, {  1,  18}, {  1,  20},  /* Row 22 */
  { 22,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15},
  {  1,   6}, {  9,   4}, {  1,  19}, {  1,  27}, {  1,  21}, {  8,  18}, {  1,  29}, {  1,  28},
  {  4,   7}, { 11,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 19,   0},
  { 14,  18}, { 21,   0},
  { 21,   0}, { 11,  18}, {  1,  20}, { 20,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4},  /* Row 23 */
  {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, { 10,   4}, {  1,  22}, {  1,  21}, { 10,  18},
  {  1,  30}, {  1,  31}, {  1,  32}, {  1,  33}, {  1,  34}, {  1,  35}, {  1,   7}, { 10,   4},
  {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 17,   0}, {  1,  17}, {  1,  21},
  {  4,  18}, {  1,  36}, {  2,  37}, {  1,  36}, {  4,  18}, {  1,  21}, {  1,  17}, { 20,   0},
  { 20,   0}, {  1,  38}, {  1,  21}, { 11,  18}, {  1,  39}, {  4,  40}, {  1,  41}, { 13,   0},  /* Row 24 */
  {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6},
  { 10,   4}, {  1,  25}, {  1,  23}, {  4,  18}, {  3,  36}, {  5,  18}, {  1,  42}, {  1,  43},
  {  3,  44}, {  1,  45}, {  1,   7}, { 10,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4},
  {  1,   5}, { 16,   0}, {  5,  18}, {  1,  37}, {  4,  46}, {  1,  37}, {  4,  18}, {  1,  47},
  {  1,  48}, {  1,  49}, { 18,   0},
  { 19,   0}, {  1,  17}, {  1,  21}, { 12,  18}, {  1,  50}, {  1,  48}, {  3,  40}, {  1,  51},  /* Row 25 */
  {  1,  40}, { 11,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11},
  {  1,  15}, {  1,   6}, { 11,   4}, {  1,  52}, {  1,  21}, {  3,  18}, {  1,  37}, {  3,  46},
  {  1,  37}, {  4,  18}, {  1,  21}, {  1,  53}, {  4,  44}, {  1,  45}, {  1,   7}, { 10,   4},
  {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 14,   0}, {  1,  17}, {  1,  21},
  {  3,  18}, {  1,  54}, {  6,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  55}, {  1,  40},
  {  1,  49}, { 17,   0},
  { 19,   0}, {  5,  18}, {  1,  54}, {  2,  56}, {  1,  37}, {  5,  18}, {  1,  21}, {  1,  57},  /* Row 26 */
  {  4,  40}, {  1,  51}, {  1,  41}, {  9,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4},
  {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, { 11,   4}, {  1,  58}, {  1,  59}, {  3,  18},
  {  1,  21}, {  1,  60}, {  4,  46}, {  1,  37}, {  4,  18}, {  1,  42}, {  1,  43}, {  4,  44},
  {  1,  45}, {  1,   7}, { 10,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5},
  { 13,   0}, {  5,  18}, {  1,  56}, {  6,  46}, {  1,  56}, {  4,  18}, {  1,  57}, {  2,  40},
  {  1,  49}, { 16,   0},
  { 18,   0}, {  1,  17}, {  1,  21}, {  3,  18}, {  1,  37}, {  4,  46}, {  1,  37}, {  5,  18},  /* Row 27 */
  {  1,  50}, {  1,  48}, {  5,  40}, {  8,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4},
  {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, { 12,   4}, {  1,  19}, {  1,  24}, {  3,  18},
  {  1,  37}, {  6,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35},
  { 11,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, { 11,   0}, {  1,  17},
  {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  57}, {  3,  40},
  { 16,   0},
  { 18,   0}, {  4,  18}, {  1,  54}, {  5,  46}, {  1,  60}, {  1,  36}, {  4,  18}, {  1,  21},  /* Row 28 */
  {  1,  57}, {  4,  40}, {  1,  51}, {  1,  41}, {  6,   0}, {  1,   1}, {  1,   2}, {  1,   6},
  {  1,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, { 13,   4}, {  1,  19}, {  1,  24},
  {  3,  18}, {  1,  61}, {  6,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  4,  44},
  {  1,  34}, {  1,  62}, { 11,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5},
  { 10,   0}, {  5,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  55},
  {  2,  40}, {  1,  51}, {  1,  41}, { 15,   0},
  { 18,   0}, {  4,  18}, {  1,  56}, {  6,  46}, {  1,  56}, {  5,  18}, {  1,  50}, {  1,  48},  /* Row 29 */
  {  5,  40}, {  5,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11},
  {  1,  15}, {  1,   6}, { 14,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46},
  {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, { 12,   4}, {  1,  16},
  {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, {  8,   0}, {  1,  17}, {  1,  21}, {  3,  18},
  {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  50}, {  1,  48}, {  4,  40}, { 15,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  54}, {  4,  18}, {  1,  21}, {  1,  57},  /* Row 30 */
  {  4,  40}, {  1,  51}, {  1,  41}, {  3,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4},
  {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, { 15,   4}, {  1,  19}, {  1,  24}, {  2,  18},
  {  1,  36}, {  8,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34},
  {  1,  62}, { 12,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, {  7,   0},
  {  5,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  57}, {  5,  40},
  {  1,  63}, { 14,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  56}, {  5,  18}, {  1,  50}, {  1,  48},  /* Row 31 */
  {  5,  40}, {  2,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11},
  {  1,  15}, {  1,   6}, { 16,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  9,  46},
  {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, { 13,   4}, {  1,  16},
  {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, {  5,   0}, {  1,  17}, {  1,  21}, {  3,  18},
  {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  50}, {  1,  48}, {  5,  40}, {  1,  41},
  { 14,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  9,  46}, {  1,  54}, {  4,  18}, {  1,  21}, {  1,  57},  /* Row 32 */
  {  4,  40}, {  1,  51}, {  1,  41}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   4}, {  1,   8},
  {  1,  11}, {  1,  15}, {  1,   6}, { 17,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36},
  {  9,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62},
  { 13,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, {  4,   0}, {  5,  18},
  {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  57}, {  6,  40}, {  1,  63},
  { 14,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  9,  46}, {  1,  56}, {  5,  18}, {  1,  50}, {  1,  48},  /* Row 33 */
  {  3,  40}, {  1,  64}, {  1,  65}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   8}, {  1,  11},
  {  1,  15}, {  1,   6}, { 18,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, { 10,  46},
  {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, { 14,   4}, {  1,  16},
  {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, {  2,   0}, {  1,  17}, {  1,  21}, {  3,  18},
  {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  50}, {  1,  48}, {  6,  40}, { 15,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, { 10,  46}, {  1,  54}, {  4,  18}, {  1,  21}, {  1,  57},  /* Row 34 */
  {  2,  40}, {  1,  64}, {  1,  66}, {  1,  67}, {  1,  68}, {  1,   4}, {  1,   8}, {  1,  11},
  {  1,  15}, {  1,   6}, { 19,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, { 10,  46},
  {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, { 14,   4},
  {  1,  16}, {  1,  14}, {  1,   7}, {  3,   4}, {  1,   5}, {  1,   0}, {  5,  18}, {  1,  56},
  {  7,  46}, {  1,  54}, {  3,  18}, {  1,  69}, {  1,  70}, {  5,  40}, {  1,  51}, {  1,  41},
  { 15,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, { 10,  46}, {  1,  56}, {  5,  18}, {  1,  50}, {  1,  48},  /* Row 35 */
  {  1,  64}, {  1,  66}, {  1,  67}, {  1,  44}, {  1,  34}, {  1,  71}, {  1,  11}, {  1,  15},
  {  1,   6}, { 20,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, { 11,  46}, {  1,  54},
  {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, { 15,   4}, {  1,  16}, {  1,  14},
  {  1,   7}, {  3,   4}, {  1,  72}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56},
  {  4,  18}, {  1,  50}, {  1,  73}, {  6,  40}, { 16,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, { 11,  46}, {  1,  54}, {  4,  18}, {  1,  21}, {  1,  57},  /* Row 36 */
  {  1,  74}, {  1,  67}, {  3,  44}, {  1,  75}, {  1,  76}, {  1,   6}, { 21,   4}, {  1,  19},
  {  1,  24}, {  2,  18}, {  1,  36}, { 11,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43},
  {  4,  44}, {  1,  34}, {  1,  62}, { 15,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  1,   4},
  {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21},
  {  1,  57}, {  5,  40}, {  1,  51}, {  1,  41}, { 16,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, { 11,  46}, {  1,  56}, {  5,  18}, {  1,  77}, {  1,  78},  /* Row 37 */
  {  4,  44}, {  1,  79}, {  1,  80}, { 22,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36},
  { 12,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, { 16,   4},
  {  1,  16}, {  1,  14}, {  1,   7}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46},
  {  1,  56}, {  4,  18}, {  1,  50}, {  1,  48}, {  6,  40}, { 17,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, { 12,  46}, {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53},  /* Row 38 */
  {  5,  44}, {  1,  81}, { 22,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, { 12,  46},
  {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, { 16,   4},
  {  1,  16}, {  1,  82}, {  1,  23}, {  4,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18},
  {  1,  21}, {  1,  57}, {  5,  40}, {  1,  51}, {  1,  41}, { 17,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, { 12,  46}, {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43},  /* Row 39 */
  {  4,  44}, {  1,  34}, {  1,  62}, { 21,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36},
  { 13,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, { 17,   4},
  {  1,  83}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  50},
  {  1,  48}, {  6,  40}, { 18,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, { 13,  46}, {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53},  /* Row 40 */
  {  5,  44}, {  1,  35}, { 21,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, { 13,  46},
  {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, { 15,   4},
  {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21},
  {  1,  57}, {  5,  40}, {  1,  51}, {  1,  41}, { 18,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, { 13,  46}, {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43},  /* Row 41 */
  {  4,  44}, {  1,  34}, {  1,  62}, { 20,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36},
  { 14,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, { 15,   4},
  {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  50},
  {  1,  48}, {  6,  40}, { 19,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, { 14,  46}, {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53},  /* Row 42 */
  {  5,  44}, {  1,  35}, { 20,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, { 14,  46},
  {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, { 13,   4},
  {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21},
  {  1,  57}, {  5,  40}, {  1,  51}, {  1,  41}, { 19,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, { 14,  46}, {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43},  /* Row 43 */
  {  4,  44}, {  1,  34}, {  1,  62}, { 19,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36},
  {  7,  46}, {  1,  61}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44},
  {  1,  35}, { 13,   4}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56},
  {  4,  18}, {  1,  50}, {  1,  48}, {  6,  40}, { 20,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, { 15,  46}, {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53},  /* Row 44 */
  {  5,  44}, {  1,  35}, { 19,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46},
  {  1,  84}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34},
  {  1,  62}, { 11,   4}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, {  7,  46}, {  1,  54},
  {  3,  18}, {  1,  21}, {  1,  57}, {  5,  40}, {  1,  51}, {  1,  41}, { 20,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  61}, {  6,  46}, {  1,  56}, {  5,  18},  /* Row 45 */
  {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, { 18,   4}, {  1,  19}, {  1,  24},
  {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18},
  {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, { 11,   4}, {  1,  22}, {  1,  21}, {  3,  18},
  {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  50}, {  1,  48}, {  6,  40}, { 21,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  84}, {  7,  46}, {  1,  54}, {  4,  18},  /* Row 46 */
  {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, { 18,   4}, {  1,  19}, {  1,  24}, {  2,  18},
  {  1,  36}, {  7,  46}, {  1,  36}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42},
  {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, {  9,   4}, {  1,  25}, {  1,  23}, {  4,  18},
  {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  57}, {  5,  40}, {  1,  51},
  {  1,  41}, { 21,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  1,  56}, {  6,  46}, {  1,  56},  /* Row 47 */
  {  5,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, { 17,   4}, {  1,  19},
  {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  1,  18}, {  1,  56}, {  7,  46},
  {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, {  9,   4}, {  1,  22},
  {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  50}, {  1,  48},
  {  6,  40}, { 22,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  1,  54}, {  7,  46}, {  1,  54},  /* Row 48 */
  {  4,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, { 17,   4}, {  1,  19}, {  1,  24},
  {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  1,  18}, {  1,  54}, {  7,  46}, {  1,  56},
  {  4,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, {  7,   4}, {  1,  25},
  {  1,  23}, {  4,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  85},
  {  1,  51}, {  4,  40}, {  1,  51}, {  1,  41}, { 22,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  1,  18}, {  1,  56}, {  6,  46},  /* Row 49 */
  {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, { 16,   4},
  {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  56},
  {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, {  7,   4},
  {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42},
  {  1,  86}, {  1,  66}, {  1,  64}, {  4,  40}, { 23,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  1,  18}, {  1,  54}, {  7,  46},  /* Row 50 */
  {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, { 16,   4}, {  1,  19},
  {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  54}, {  7,  46},
  {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, {  5,   4},
  {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21},
  {  1,  53}, {  1,  44}, {  1,  67}, {  1,  66}, {  1,  64}, {  1,  40}, {  1,  51}, {  1,  41},
  { 23,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  56}, {  6,  46},  /* Row 51 */
  {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, { 15,   4},
  {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  3,  18}, {  1,  56},
  {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, {  5,   4},
  {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42},
  {  1,  43}, {  2,  44}, {  1,  67}, {  1,  66}, {  1,  64}, {  1,  40}, { 24,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  54}, {  7,  46},  /* Row 52 */
  {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, { 15,   4}, {  1,  19},
  {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  3,  18}, {  1,  54}, {  7,  46},
  {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, {  3,   4},
  {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21},
  {  1,  53}, {  4,  44}, {  1,  67}, {  1,  87}, {  1,  41}, { 24,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  3,  18}, {  1,  56}, {  6,  46},  /* Row 53 */
  {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, { 14,   4},
  {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  4,  18}, {  1,  56},
  {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, {  3,   4},
  {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42},
  {  1,  43}, {  5,  44}, {  1,  88}, {  1,  89}, { 24,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  3,  18}, {  1,  54}, {  7,  46},  /* Row 54 */
  {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, { 14,   4}, {  1,  19},
  {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  4,  18}, {  1,  54}, {  7,  46},
  {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, {  1,   4},
  {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21},
  {  1,  53}, {  6,  44}, {  1,  90}, {  1,  62}, {  1,   5}, { 23,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  4,  18}, {  1,  56}, {  6,  46},  /* Row 55 */
  {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, { 13,   4},
  {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  5,  18}, {  1,  56},
  {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, {  1,   4},
  {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42},
  {  1,  43}, {  5,  44}, {  1,  33}, {  1,  91}, {  2,   4}, {  1,   5}, { 22,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  4,  18}, {  1,  54}, {  7,  46},  /* Row 56 */
  {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, { 13,   4}, {  1,  19},
  {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  5,  18}, {  1,  54}, {  7,  46},
  {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  92}, {  1,  23},
  {  4,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  6,  44},
  {  1,  93}, {  1,  14}, {  1,   7}, {  2,   4}, {  1,   5}, { 21,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  5,  18}, {  1,  56}, {  6,  46},  /* Row 57 */
  {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, { 12,   4},
  {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  6,  18}, {  1,  56},
  {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  94}, {  1,  21},
  {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  5,  44},
  {  1,  34}, {  1,  62}, {  1,  16}, {  1,  14}, {  1,   7}, {  2,   4}, {  1,   5}, { 20,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  5,  18}, {  1,  54}, {  7,  46},  /* Row 58 */
  {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, { 12,   4}, {  1,  19},
  {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  6,  18}, {  1,  54}, {  7,  46},
  {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  3,  44}, {  1,  43}, {  1,  42}, {  4,  18},
  {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  6,  44}, {  1,  35},
  {  2,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  2,   4}, {  1,   5}, { 19,   0},
  { 17,   0}, {  1,  95}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  6,  18}, {  1,  56},  /* Row 59 */
  {  6,  46}, {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62},
  { 11,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  7,  18},
  {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  3,  44}, {  1,  53},
  {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43},
  {  5,  44}, {  1,  34}, {  1,  62}, {  3,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  2,   4},
  {  1,   5}, { 18,   0},
  { 16,   0}, {  1,   1}, {  1,  96}, {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36},  /* Row 60 */
  {  6,  18}, {  1,  54}, {  7,  46}, {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53}, {  5,  44},
  {  1,  35}, { 11,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36},
  {  2,  18}, {  1,  21}, {  1,  47}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18},
  {  1,  42}, {  1,  43}, {  1,  44}, {  1,  43}, {  1,  42}, {  4,  18}, {  1,  56}, {  7,  46},
  {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  6,  44}, {  1,  35}, {  5,   4}, {  1,  16},
  {  1,  14}, {  1,   7}, {  2,   4}, {  1,   5}, { 17,   0},
  { 15,   0}, {  1,   1}, {  1,   2}, {  1,  97}, {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46},  /* Row 61 */
  {  1,  36}, {  7,  18}, {  1,  56}, {  6,  46}, {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43},
  {  4,  44}, {  1,  34}, {  1,  62}, { 10,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36},
  {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1,  99}, {  1,  21}, {  3,  18}, {  1,  56},
  {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  1,  44}, {  1,  53}, {  1,  21},
  {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  5,  44},
  {  1,  34}, {  1,  62}, {  6,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  2,   4}, {  1,   5},
  { 16,   0},
  { 14,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36},  /* Row 62 */
  {  8,  46}, {  1,  36}, {  7,  18}, {  1,  54}, {  7,  46}, {  1,  54}, {  4,  18}, {  1,  21},
  {  1,  53}, {  5,  44}, {  1,  35}, { 10,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36},
  {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 100}, {  1,  42}, {  3,  18}, {  1,  54},
  {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1, 101}, {  1,  42}, {  4,  18}, {  1,  56},
  {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  6,  44}, {  1,  35}, {  8,   4},
  {  1,  16}, {  1,  14}, {  1,   7}, {  2,   4}, {  1,   5}, { 15,   0},
  { 13,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1, 102}, {  1,  98}, {  2,  18},  /* Row 63 */
  {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  21}, {  1,  47}, {  4,  18}, {  1,  56},
  {  6,  46}, {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62},
  {  9,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18},
  {  1,  98}, {  1, 103}, {  1,  53}, {  1,  21}, {  3,  18}, {  1,  56}, {  7,  46}, {  1,  54},
  {  3,  18}, {  1,  21}, {  1,  98}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56},
  {  4,  18}, {  1,  42}, {  1,  43}, {  5,  44}, {  1,  34}, {  1,  62}, {  9,   4}, {  1,  16},
  {  1,  14}, {  1,   7}, {  2,   4}, {  1,   5}, { 14,   0},
  { 12,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1, 104}, {  1, 105}, {  1, 106},  /* Row 64 */
  {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1,  99}, {  1,  21},
  {  3,  18}, {  1,  54}, {  7,  46}, {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53}, {  5,  44},
  {  1,  35}, {  9,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36},
  {  2,  18}, {  1,  98}, {  1, 103}, {  1,  43}, {  1,  42}, {  3,  18}, {  1,  54}, {  7,  46},
  {  1,  56}, {  9,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53},
  {  6,  44}, {  1,  35}, { 11,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  2,   4}, {  1,   5},
  { 13,   0},
  { 11,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1, 104}, {  1, 107}, {  1, 108},  /* Row 65 */
  {  1,  29}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 100},
  {  1,  42}, {  4,  18}, {  1,  56}, {  6,  46}, {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43},
  {  4,  44}, {  1,  34}, {  1,  62}, {  8,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36},
  {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  1,  44}, {  1,  53}, {  1,  21},
  {  3,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  7,  18}, {  1,  54}, {  7,  46}, {  1,  56},
  {  4,  18}, {  1,  42}, {  1,  43}, {  5,  44}, {  1,  34}, {  1,  62}, { 12,   4}, {  1,  16},
  {  1,  14}, {  1,   7}, {  2,   4}, {  1,   5}, { 12,   0},
  { 10,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1, 104}, {  1, 107}, {  1, 109},  /* Row 66 */
  {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98},
  {  1, 103}, {  1,  53}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  54}, {  4,  18},
  {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, {  8,   4}, {  1,  19}, {  1,  24}, {  2,  18},
  {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  1,  44}, {  1,  43},
  {  1,  42}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  7,  18}, {  1,  56}, {  7,  46},
  {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  6,  44}, {  1,  35}, { 14,   4}, {  1,  16},
  {  1,  14}, {  1,   7}, {  2,   4}, {  1,   5}, { 11,   0},
  {  9,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1, 104}, {  1, 107}, {  1, 109},  /* Row 67 */
  {  1,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18},
  {  1,  98}, {  1, 103}, {  1,  43}, {  1,  42}, {  4,  18}, {  1,  56}, {  6,  46}, {  1,  56},
  {  5,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, {  7,   4}, {  1,  19},
  {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},
  {  2,  44}, {  1,  53}, {  1,  21}, {  3,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  5,  18},
  {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  5,  44}, {  1,  34},
  {  1,  62}, { 15,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  2,   4}, {  1,   5}, { 10,   0},
  {  8,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1, 104}, {  1, 107}, {  1, 109},  /* Row 68 */
  {  2,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18},
  {  1,  98}, {  1, 103}, {  1,  44}, {  1,  53}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46},
  {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, {  7,   4}, {  1,  19},
  {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},
  {  2,  44}, {  1,  43}, {  1,  42}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  5,  18},
  {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  6,  44}, {  1,  35},
  { 17,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  2,   4}, {  1,   5}, {  9,   0},
  {  7,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1, 104}, {  1, 107}, {  1, 109},  /* Row 69 */
  {  3,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18},
  {  1,  98}, {  1, 103}, {  1,  44}, {  1,  43}, {  1,  42}, {  4,  18}, {  1,  56}, {  6,  46},
  {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, {  6,   4},
  {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98},
  {  1, 103}, {  3,  44}, {  1,  53}, {  1,  21}, {  3,  18}, {  1,  56}, {  7,  46}, {  1,  54},
  {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  5,  44},
  {  1,  34}, {  1,  62}, { 18,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  2,   4}, {  1,   5},
  {  8,   0},
  {  6,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1, 104}, {  1, 107}, {  1, 109},  /* Row 70 */
  {  4,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18},
  {  1,  98}, {  1, 103}, {  2,  44}, {  1,  53}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46},
  {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, {  6,   4}, {  1,  19},
  {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},
  {  3,  44}, {  1,  43}, {  1,  42}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  3,  18},
  {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  6,  44}, {  1,  35},
  { 20,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  2,   4}, {  1,   5}, {  7,   0},
  {  5,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1, 104}, {  1, 107}, {  1, 109},  /* Row 71 */
  {  5,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18},
  {  1,  98}, {  1, 103}, {  2,  44}, {  1,  43}, {  1,  42}, {  4,  18}, {  1,  56}, {  6,  46},
  {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, {  5,   4},
  {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98},
  {  1, 103}, {  4,  44}, {  1,  53}, {  1,  21}, {  3,  18}, {  1,  56}, {  7,  46}, {  1,  54},
  {  1,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  5,  44},
  {  1,  34}, {  1,  62}, { 21,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  2,   4}, {  1,   5},
  {  6,   0},
  {  4,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1, 104}, {  1, 107}, {  1, 109},  /* Row 72 */
  {  6,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18},
  {  1,  98}, {  1, 103}, {  3,  44}, {  1,  53}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46},
  {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, {  5,   4}, {  1,  19},
  {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},
  {  4,  44}, {  1,  43}, {  1,  42}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  1,  18},
  {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  6,  44}, {  1,  35},
  { 23,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  2,   4}, {  1,   5}, {  5,   0},
  {  3,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1, 104}, {  1, 107}, {  1, 109},  /* Row 73 */
  {  7,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18},
  {  1,  98}, {  1, 103}, {  3,  44}, {  1,  43}, {  1,  42}, {  4,  18}, {  1,  56}, {  6,  46},
  {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, {  4,   4},
  {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98},
  {  1, 103}, {  5,  44}, {  1,  53}, {  1,  21}, {  3,  18}, {  1,  56}, {  7,  46}, {  1, 110},
  {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  5,  44}, {  1,  34}, {  1,  62},
  { 24,   4}, {  1,  16}, {  1,  14}, {  1,   7}, {  2,   4}, {  1,   5}, {  4,   0},
  {  2,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1, 104}, {  1, 107}, {  1, 109},  /* Row 74 */
  {  8,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18},
  {  1,  98}, {  1, 103}, {  4,  44}, {  1,  53}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46},
  {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, {  4,   4}, {  1,  19},
  {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},
  {  5,  44}, {  1,  43}, {  1, 111}, {  3,  18}, {  1,  54}, { 15,  46}, {  1,  54}, {  3,  18},
  {  1,  21}, {  1,  53}, {  6,  44}, {  1,  35}, { 26,   4}, {  1,  16}, {  1,  14}, {  1,   7},
  {  2,   4}, {  1,   5}, {  3,   0},
  {  1,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1, 104}, {  1, 107}, {  1, 109},  /* Row 75 */
  {  9,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18},
  {  1,  98}, {  1, 103}, {  4,  44}, {  1,  43}, {  1,  42}, {  4,  18}, {  1,  56}, {  6,  46},
  {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, {  3,   4},
  {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98},
  {  1, 103}, {  5,  44}, {  1,  32}, {  1,  83}, {  1,  21}, {  3,  18}, {  1,  56}, { 13,  46},
  {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  5,  44}, {  1,  34}, {  1,  62}, { 27,   4},
  {  1,  16}, {  1,  14}, {  1,   7}, {  2,   4}, {  1,   5}, {  2,   0},
  {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1, 104}, {  1, 107}, {  1, 109}, { 10,   4},  /* Row 76 */
  {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98},
  {  1, 103}, {  5,  44}, {  1,  53}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  54},
  {  4,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, {  3,   4}, {  1,  19}, {  1,  24},
  {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44},
  {  1,  33}, {  1, 112}, {  1,  23}, {  3,  18}, {  1,  54}, { 13,  46}, {  1,  54}, {  3,  18},
  {  1,  21}, {  1,  53}, {  6,  44}, {  1,  35}, { 29,   4}, {  1,  16}, {  1,  14}, {  1,   7},
  {  2,   4}, {  1,   5}, {  1,   0},
  {  1,   2}, {  1,   6}, {  1,   7}, {  1, 104}, {  1, 107}, {  1, 109}, { 11,   4}, {  1,  19},  /* Row 77 */
  {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},
  {  5,  44}, {  1,  43}, {  1, 111}, {  4,  18}, {  1,  56}, {  6,  46}, {  1,  56}, {  5,  18},
  {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, {  2,   4}, {  1,  19}, {  1,  24},
  {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44},
  {  1,  33}, {  1,   7}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  56}, { 11,  46}, {  1,  56},
  {  4,  18}, {  1,  42}, {  1,  43}, {  5,  44}, {  1,  34}, {  1,  62}, { 30,   4}, {  1,  16},
  {  1,  14}, {  1,   7}, {  2,   4}, {  1,   5},
  {  1,   3}, {  1,   7}, {  1, 104}, {  1, 107}, {  1, 109}, { 12,   4}, {  1,  19}, {  1,  24},  /* Row 78 */
  {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44},
  {  1,  32}, {  1,  83}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  54}, {  4,  18},
  {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, {  2,   4}, {  1,  19}, {  1,  24}, {  2,  18},
  {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44}, {  1,  33},
  {  1,   7}, {  1,  25}, {  1,  23}, {  3,  18}, {  1,  54}, { 11,  46}, {  1,  54}, {  3,  18},
  {  1,  21}, {  1,  53}, {  6,  44}, {  1,  35}, { 32,   4}, {  1,  16}, {  1,  14}, {  1,   7},
  {  2,   4},
  {  1,   4}, {  1, 113}, {  1, 114}, {  1, 115}, { 13,   4}, {  1,  19}, {  1,  24}, {  2,  18},  /* Row 79 */
  {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44}, {  1,  33},
  {  1, 112}, {  1,  23}, {  4,  18}, {  1,  56}, {  6,  46}, {  1,  56}, {  5,  18}, {  1,  42},
  {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, {  1,   4}, {  1,  19}, {  1,  24}, {  2,  18},
  {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44}, {  1,  33},
  {  1,   7}, {  1,   4}, {  1, 116}, {  4,  18}, {  1,  56}, {  9,  46}, {  1,  61}, {  4,  18},
  {  1,  42}, {  1,  43}, {  5,  44}, {  1,  34}, {  1,  62}, { 33,   4}, {  1,  16}, {  1,  14},
  {  1,  62}, {  1,   4},
  {  1,   4}, {  1,   7}, {  1, 117}, {  1,   8}, { 13,   4}, {  1,  19}, {  1,  24}, {  2,  18},  /* Row 80 */
  {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44}, {  1,  33},
  {  1,   7}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  54}, {  4,  18},
  {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, {  1,   4}, {  1,  19}, {  1,  24}, {  2,  18},
  {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44}, {  1,  33},
  {  1,   7}, {  1,   4}, {  1, 116}, {  4,  18}, {  1,  54}, {  9,  46}, {  1,  37}, {  3,  18},
  {  1,  21}, {  1,  53}, {  6,  44}, {  1,  35}, { 35,   4}, {  1, 118}, {  1, 119}, {  1,   7},
  {  2,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 12,   4}, {  1,  19}, {  1,  24}, {  2,  18},  /* Row 81 */
  {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44}, {  1,  33},
  {  1,   7}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, {  6,  46}, {  1,  56}, {  5,  18},
  {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1,  62}, {  1,  19}, {  1,  24}, {  2,  18},
  {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44}, {  1,  33},
  {  1,   7}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, {  8,  46}, {  1,  60}, {  1,  21},
  {  3,  18}, {  1,  98}, {  1, 120}, {  5,  44}, {  1,  33}, {  1,  62}, { 34,   4}, {  1,   8},
  {  1,  11}, {  1,  76}, {  1,   3},
  {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 11,   4}, {  1,  19}, {  1,  24}, {  2,  18},  /* Row 82 */
  {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44}, {  1,  33},
  {  1,   7}, {  1,   4}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  54},
  {  4,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  35}, {  1, 121}, {  1,  24}, {  2,  18},
  {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44}, {  1,  33},
  {  1,   7}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, { 10,  46}, {  1,  54}, {  3,  18},
  {  1,  21}, {  1,  53}, {  5,  44}, {  1,  33}, {  1,  62}, { 33,   4}, {  1,   8}, {  1,  11},
  {  1,  15}, {  1,   6}, {  1,   4},
  {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 10,   4}, {  1,  19}, {  1,  24},  /* Row 83 */
  {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44},
  {  1,  33}, {  1,   7}, {  1,   4}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, {  6,  46},
  {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  34}, {  1, 122}, {  1,  24},
  {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44},
  {  1,  33}, {  1, 112}, {  1,  23}, {  4,  18}, {  1,  56}, { 10,  46}, {  1,  56}, {  4,  18},
  {  1,  42}, {  1,  43}, {  5,  44}, {  1,  35}, { 32,   4}, {  1,   8}, {  1,  11}, {  1,  15},
  {  1,   6}, {  1,   4}, {  1,   5},
  {  1,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, {  9,   4}, {  1,  19},  /* Row 84 */
  {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},
  {  5,  44}, {  1,  33}, {  1,   7}, {  2,   4}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54},
  {  7,  46}, {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1, 123}, {  1,  24},
  {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44},
  {  1,  32}, {  1,  83}, {  1,  21}, {  3,  18}, {  1,  54}, { 12,  46}, {  1,  54}, {  3,  18},
  {  1,  21}, {  1,  53}, {  5,  44}, {  1,  34}, {  1,  62}, { 30,   4}, {  1,   8}, {  1,  11},
  {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, {  1,   0},
  {  2,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, {  8,   4}, {  1,  19},  /* Row 85 */
  {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},
  {  5,  44}, {  1,  33}, {  1,   7}, {  2,   4}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56},
  {  6,  46}, {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1, 124}, {  1,  98},
  {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44},
  {  1,  43}, {  1, 111}, {  4,  18}, {  1,  56}, { 12,  46}, {  1,  56}, {  4,  18}, {  1,  42},
  {  1,  43}, {  5,  44}, {  1,  35}, { 29,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6},
  {  1,   4}, {  1,   5}, {  2,   0},
  {  3,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, {  7,   4}, {  1,  19},  /* Row 86 */
  {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},
  {  5,  44}, {  1,  33}, {  1,   7}, {  3,   4}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54},
  {  7,  46}, {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53}, {  4,  44}, {  1, 103}, {  1,  98},
  {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44},
  {  1,  53}, {  1,  21}, {  3,  18}, {  1,  54}, { 14,  46}, {  1,  54}, {  3,  18}, {  1,  21},
  {  1,  53}, {  5,  44}, {  1,  34}, {  1,  62}, { 27,   4}, {  1,   8}, {  1,  11}, {  1,  15},
  {  1,   6}, {  1,   4}, {  1,   5}, {  3,   0},
  {  4,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, {  6,   4}, {  1,  19},  /* Row 87 */
  {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},
  {  5,  44}, {  1,  33}, {  1,   7}, {  3,   4}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56},
  {  6,  46}, {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43}, {  3,  44}, {  1, 103}, {  1,  98},
  {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  4,  44},
  {  1,  43}, {  1,  42}, {  4,  18}, {  1,  56}, { 14,  46}, {  1,  56}, {  4,  18}, {  1,  42},
  {  1,  43}, {  5,  44}, {  1,  35}, { 26,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6},
  {  1,   4}, {  1,   5}, {  4,   0},
  {  5,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, {  5,   4}, {  1,  19},  /* Row 88 */
  {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},
  {  5,  44}, {  1,  33}, {  1,   7}, {  4,   4}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54},
  {  7,  46}, {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53}, {  3,  44}, {  1, 103}, {  1,  98},
  {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  4,  44},
  {  1,  53}, {  1,  21}, {  3,  18}, {  1,  54}, { 16,  46}, {  1,  54}, {  3,  18}, {  1,  21},
  {  1,  53}, {  5,  44}, {  1,  34}, {  1,  62}, { 24,   4}, {  1,   8}, {  1,  11}, {  1,  15},
  {  1,   6}, {  1,   4}, {  1,   5}, {  5,   0},
  {  6,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, {  4,   4}, {  1,  19},  /* Row 89 */
  {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},
  {  5,  44}, {  1,  33}, {  1,   7}, {  4,   4}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56},
  {  6,  46}, {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43}, {  2,  44}, {  1, 103}, {  1,  98},
  {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  3,  44},
  {  1,  43}, {  1,  42}, {  4,  18}, {  1,  56}, {  7,  46}, {  1,  56}, {  1,  54}, {  7,  46},
  {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  5,  44}, {  1,  35}, { 23,   4}, {  1,   8},
  {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, {  6,   0},
  {  7,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, {  3,   4}, {  1,  19},  /* Row 90 */
  {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},
  {  5,  44}, {  1,  33}, {  1,   7}, {  5,   4}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54},
  {  7,  46}, {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53}, {  2,  44}, {  1, 103}, {  1,  98},
  {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  3,  44},
  {  1,  53}, {  1,  21}, {  3,  18}, {  1,  54}, {  8,  46}, {  1,  54}, {  1,  18}, {  1,  56},
  {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  34}, {  1,  62},
  { 21,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, {  7,   0},
  {  8,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, {  2,   4}, {  1,  19},  /* Row 91 */
  {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},
  {  5,  44}, {  1,  33}, {  1,   7}, {  5,   4}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56},
  {  6,  46}, {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43}, {  1,  44}, {  1, 103}, {  1,  98},
  {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  2,  44},
  {  1,  43}, {  1,  42}, {  4,  18}, {  1,  56}, {  7,  46}, {  1,  56}, {  2,  18}, {  1,  54},
  {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  5,  44}, {  1,  35}, { 20,   4},
  {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, {  8,   0},
  {  9,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, {  1,   4}, {  1,  19},  /* Row 92 */
  {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},
  {  5,  44}, {  1,  33}, {  1,   7}, {  6,   4}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54},
  {  7,  46}, {  1,  54}, {  4,  18}, {  1,  21}, {  1,  53}, {  1,  44}, {  1, 103}, {  1,  98},
  {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  2,  44},
  {  1,  53}, {  1,  21}, {  3,  18}, {  1,  54}, {  8,  46}, {  1,  54}, {  3,  18}, {  1,  56},
  {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  34}, {  1,  62},
  { 18,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, {  9,   0},
  { 10,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, {  1, 121}, {  1,  24},  /* Row 93 */
  {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44},
  {  1,  33}, {  1,   7}, {  6,   4}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, {  6,  46},
  {  1,  56}, {  5,  18}, {  1,  42}, {  1,  43}, {  1, 103}, {  1,  98}, {  2,  18}, {  1,  36},
  {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  1,  44}, {  1,  43}, {  1,  42},
  {  4,  18}, {  1,  56}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  54}, {  7,  46}, {  1,  56},
  {  4,  18}, {  1,  42}, {  1,  43}, {  5,  44}, {  1,  35}, { 17,   4}, {  1,   8}, {  1,  11},
  {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 10,   0},
  { 11,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1, 125}, {  1,  24}, {  2,  18},  /* Row 94 */
  {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44}, {  1,  33},
  {  1,   7}, {  7,   4}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  54},
  {  4,  18}, {  1,  21}, {  1,  53}, {  1, 103}, {  1,  98}, {  2,  18}, {  1,  36}, {  7,  46},
  {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  1,  44}, {  1,  53}, {  1,  21}, {  3,  18},
  {  1,  54}, {  8,  46}, {  1,  54}, {  5,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18},
  {  1,  21}, {  1,  53}, {  5,  44}, {  1,  34}, {  1,  62}, { 15,   4}, {  1,   8}, {  1,  11},
  {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 11,   0},
  { 12,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1, 126}, {  1,  98}, {  2,  18}, {  1,  36},  /* Row 95 */
  {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44}, {  1,  33}, {  1,   7},
  {  7,   4}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, {  6,  46}, {  1,  56}, {  5,  18},
  {  1,  42}, {  1, 100}, {  1,  98}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18},
  {  1,  98}, {  1, 103}, {  1,  43}, {  1,  42}, {  4,  18}, {  1,  56}, {  7,  46}, {  1,  56},
  {  6,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  5,  44},
  {  1,  35}, { 14,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5},
  { 12,   0},
  { 13,   0}, {  1,   5}, {  3,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46},  /* Row 96 */
  {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44}, {  1,  33}, {  1,   7}, {  8,   4},
  {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  54}, {  4,  18}, {  1,  21},
  {  1,  99}, {  1,  98}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98},
  {  1, 103}, {  1,  53}, {  1,  21}, {  3,  18}, {  1,  54}, {  8,  46}, {  1,  54}, {  7,  18},
  {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  34},
  {  1,  62}, { 12,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5},
  { 13,   0},
  { 14,   0}, {  1,   5}, {  2,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46},  /* Row 97 */
  {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44}, {  1,  33}, {  1,   7}, {  8,   4},
  {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, {  6,  46}, {  1,  56}, {  5,  18}, {  1,  47},
  {  1,  21}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 100},
  {  1,  42}, {  4,  18}, {  1,  56}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  47}, {  1,  21},
  {  2,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  5,  44},
  {  1,  35}, { 11,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5},
  { 14,   0},
  { 15,   0}, {  1,   5}, {  1,   4}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46},  /* Row 98 */
  {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44}, {  1,  33}, {  1,   7}, {  9,   4},
  {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  54}, {  8,  18}, {  1,  36},
  {  7,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1,  99}, {  1,  21}, {  3,  18}, {  1,  54},
  {  8,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1, 127}, {  1,  42}, {  3,  18}, {  1,  56},
  {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  34}, {  1,  62},
  {  9,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 15,   0},
  { 16,   0}, {  1,   5}, {  1,  19}, {  1,  24}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36},  /* Row 99 */
  {  2,  18}, {  1,  98}, {  1, 103}, {  5,  44}, {  1,  33}, {  1,   7}, {  9,   4}, {  1,  25},
  {  1,  23}, {  4,  18}, {  1,  56}, {  6,  46}, {  1,  56}, {  8,  18}, {  1,  36}, {  7,  46},
  {  1,  36}, {  2,  18}, {  1,  21}, {  1,  47}, {  4,  18}, {  1,  56}, {  7,  46}, {  1,  56},
  {  4,  18}, {  1,  42}, {  1,  43}, {  1,  53}, {  1,  21}, {  2,  18}, {  1,  54}, {  7,  46},
  {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  5,  44}, {  1,  35}, {  8,   4}, {  1,   8},
  {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 16,   0},
  { 17,   0}, {  1, 128}, {  1, 129}, {  2,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18},  /* Row 100 */
  {  1,  98}, {  1, 103}, {  5,  44}, {  1,  33}, {  1,   7}, { 10,   4}, {  1,  22}, {  1,  21},
  {  3,  18}, {  1,  54}, {  7,  46}, {  1,  54}, {  7,  18}, {  1,  36}, {  7,  46}, {  1,  36},
  {  7,  18}, {  1,  54}, {  8,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  1,  44},
  {  1,  43}, {  1,  42}, {  3,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21},
  {  1,  53}, {  5,  44}, {  1,  34}, {  1,  62}, {  6,   4}, {  1,   8}, {  1,  11}, {  1,  15},
  {  1,   6}, {  1,   4}, {  1,   5}, { 17,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},  /* Row 101 */
  {  5,  44}, {  1,  33}, {  1,   7}, { 10,   4}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56},
  {  6,  46}, {  1,  56}, {  7,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  7,  18}, {  1,  56},
  {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  2,  44}, {  1,  53}, {  1,  21},
  {  2,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  5,  44},
  {  1,  35}, {  5,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5},
  { 18,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},  /* Row 102 */
  {  5,  44}, {  1,  33}, {  1,   7}, { 11,   4}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54},
  {  7,  46}, {  1,  54}, {  6,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  6,  18}, {  1,  54},
  {  8,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  3,  44}, {  1,  43}, {  1,  42},
  {  3,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44},
  {  1,  34}, {  1,  62}, {  3,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4},
  {  1,   5}, { 19,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},  /* Row 103 */
  {  5,  44}, {  1,  33}, {  1,   7}, { 11,   4}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56},
  {  6,  46}, {  1,  56}, {  6,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  6,  18}, {  1,  56},
  {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  4,  44}, {  1,  53}, {  1,  21},
  {  2,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  5,  44},
  {  1,  35}, {  2,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5},
  { 20,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},  /* Row 104 */
  {  5,  44}, {  1,  33}, {  1,   7}, { 12,   4}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54},
  {  7,  46}, {  1,  54}, {  5,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  5,  18}, {  1,  54},
  {  8,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44}, {  1,  43}, {  1,  42},
  {  3,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  5,  44},
  {  1,  34}, {  1,  62}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5},
  { 21,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},  /* Row 105 */
  {  5,  44}, {  1,  33}, {  1,   7}, { 12,   4}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56},
  {  6,  46}, {  1,  56}, {  5,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  5,  18}, {  1,  56},
  {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  6,  44}, {  1,  94}, {  1,  21},
  {  2,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  5,  44},
  {  1,  93}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 22,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},  /* Row 106 */
  {  5,  44}, {  1,  33}, {  1,   7}, { 13,   4}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54},
  {  7,  46}, {  1,  54}, {  4,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  4,  18}, {  1,  54},
  {  8,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  6,  44}, {  1,  34}, {  1,  92},
  {  1,  23}, {  3,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53},
  {  5,  44}, {  1,  88}, {  1, 130}, {  1,   6}, {  1,   4}, {  1,   5}, { 23,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},  /* Row 107 */
  {  5,  44}, {  1,  33}, {  1,   7}, { 13,   4}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56},
  {  6,  46}, {  1,  56}, {  4,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  4,  18}, {  1,  56},
  {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  6,  44}, {  1,  35}, {  1,   4},
  {  1,  22}, {  1,  21}, {  2,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42},
  {  1,  43}, {  5,  44}, {  1, 131}, {  1,   7}, {  1,   5}, { 24,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},  /* Row 108 */
  {  5,  44}, {  1,  33}, {  1,   7}, { 14,   4}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54},
  {  7,  46}, {  1,  54}, {  3,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  3,  18}, {  1,  54},
  {  8,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  6,  44}, {  1,  34}, {  1,  62},
  {  1,   4}, {  1,  25}, {  1,  23}, {  3,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18},
  {  1,  21}, {  1,  53}, {  4,  44}, {  1,  67}, {  1, 132}, {  1, 133}, { 25,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},  /* Row 109 */
  {  5,  44}, {  1,  33}, {  1,   7}, { 14,   4}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56},
  {  6,  46}, {  1,  56}, {  3,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  3,  18}, {  1,  56},
  {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  6,  44}, {  1,  35}, {  3,   4},
  {  1,  22}, {  1,  21}, {  2,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42},
  {  1,  43}, {  2,  44}, {  1,  67}, {  1,  66}, {  1,  64}, {  1,  40}, { 25,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},  /* Row 110 */
  {  5,  44}, {  1,  33}, {  1,   7}, { 15,   4}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54},
  {  7,  46}, {  1,  54}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  54},
  {  8,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  6,  44}, {  1,  34}, {  1,  62},
  {  3,   4}, {  1,  25}, {  1,  23}, {  3,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18},
  {  1,  21}, {  1,  53}, {  1,  44}, {  1,  67}, {  1,  66}, {  1,  64}, {  1,  40}, {  1,  51},
  {  1,  41}, { 24,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},  /* Row 111 */
  {  5,  44}, {  1,  33}, {  1,   7}, { 15,   4}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56},
  {  6,  46}, {  1,  56}, {  2,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  2,  18}, {  1,  56},
  {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  6,  44}, {  1,  35}, {  5,   4},
  {  1,  22}, {  1,  21}, {  2,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42},
  {  1,  86}, {  1,  66}, {  1,  64}, {  4,  40}, { 24,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},  /* Row 112 */
  {  5,  44}, {  1,  33}, {  1,   7}, { 16,   4}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54},
  {  7,  46}, {  1,  54}, {  1,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  1,  18}, {  1,  54},
  {  8,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  6,  44}, {  1,  34}, {  1,  62},
  {  5,   4}, {  1,  25}, {  1,  23}, {  3,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18},
  {  1,  21}, {  1,  85}, {  1,  51}, {  4,  40}, {  1,  51}, {  1,  41}, { 23,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},  /* Row 113 */
  {  5,  44}, {  1,  33}, {  1,   7}, { 16,   4}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56},
  {  6,  46}, {  1,  56}, {  1,  18}, {  1,  36}, {  7,  46}, {  1,  36}, {  1,  18}, {  1,  56},
  {  7,  46}, {  1,  56}, {  4,  18}, {  1,  42}, {  1,  43}, {  6,  44}, {  1,  35}, {  7,   4},
  {  1,  22}, {  1,  21}, {  2,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  50},
  {  1,  48}, {  6,  40}, { 23,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},  /* Row 114 */
  {  5,  44}, {  1,  33}, {  1,   7}, { 17,   4}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54},
  {  7,  46}, {  1,  54}, {  1,  36}, {  7,  46}, {  1,  36}, {  1,  54}, {  8,  46}, {  1,  54},
  {  3,  18}, {  1,  21}, {  1,  53}, {  6,  44}, {  1,  34}, {  1,  62}, {  7,   4}, {  1,  25},
  {  1,  23}, {  3,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  57},
  {  5,  40}, {  1,  51}, {  1,  41}, { 22,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 103},  /* Row 115 */
  {  5,  44}, {  1,  33}, {  1,   7}, { 17,   4}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56},
  {  6,  46}, {  1,  56}, {  1,  36}, {  7,  46}, {  1,  36}, {  1,  56}, {  7,  46}, {  1,  56},
  {  4,  18}, {  1,  42}, {  1,  43}, {  6,  44}, {  1,  35}, {  9,   4}, {  1,  22}, {  1,  21},
  {  2,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  50}, {  1,  48}, {  6,  40},
  { 22,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  98}, {  1, 134},  /* Row 116 */
  {  5,  44}, {  1,  33}, {  1,   7}, { 18,   4}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54},
  {  7,  46}, {  1,  84}, {  7,  46}, {  1,  84}, {  8,  46}, {  1,  54}, {  3,  18}, {  1,  21},
  {  1,  53}, {  6,  44}, {  1,  34}, {  1,  62}, {  9,   4}, {  1,  25}, {  1,  23}, {  3,  18},
  {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  57}, {  5,  40}, {  1,  51},
  {  1,  41}, { 21,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1, 135},  /* Row 117 */
  {  1,  67}, {  4,  44}, {  1,  33}, {  1,   7}, { 18,   4}, {  1,  25}, {  1,  23}, {  4,  18},
  {  1,  56}, {  6,  46}, {  1,  61}, {  7,  46}, {  1,  61}, {  7,  46}, {  1,  56}, {  4,  18},
  {  1,  42}, {  1,  43}, {  6,  44}, {  1,  35}, { 11,   4}, {  1,  22}, {  1,  21}, {  2,  18},
  {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  50}, {  1,  48}, {  6,  40}, { 21,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 118 */
  {  1,  66}, {  1,  67}, {  3,  44}, {  1,  79}, {  1, 136}, { 19,   4}, {  1,  22}, {  1,  21},
  {  3,  18}, {  1,  54}, { 22,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  53}, {  6,  44},
  {  1,  34}, {  1,  62}, { 11,   4}, {  1,  25}, {  1,  23}, {  3,  18}, {  1,  56}, {  7,  46},
  {  1,  54}, {  3,  18}, {  1,  21}, {  1,  57}, {  5,  40}, {  1,  51}, {  1,  41}, { 20,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 119 */
  {  1,  64}, {  1,  66}, {  1,  67}, {  2,  44}, {  1,  79}, {  1,  45}, {  1,  16}, { 18,   4},
  {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, { 20,  46}, {  1,  56}, {  4,  18}, {  1,  42},
  {  1,  43}, {  6,  44}, {  1,  35}, { 13,   4}, {  1,  22}, {  1,  21}, {  2,  18}, {  1,  54},
  {  7,  46}, {  1,  56}, {  4,  18}, {  1,  50}, {  1,  48}, {  6,  40}, { 20,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 120 */
  {  1,  40}, {  1,  64}, {  1,  66}, {  1,  67}, {  1,  44}, {  1,  33}, {  1, 137}, {  1,  14},
  {  1,  16}, { 18,   4}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, { 20,  46}, {  1,  54},
  {  3,  18}, {  1,  21}, {  1,  53}, {  6,  44}, {  1,  34}, {  1,  62}, { 13,   4}, {  1, 138},
  {  1, 139}, {  3,  18}, {  1,  56}, {  7,  46}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  57},
  {  5,  40}, {  1,  51}, {  1,  41}, { 19,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 121 */
  {  2,  40}, {  1,  64}, {  1,  66}, {  1,  67}, {  1,  33}, {  2,   7}, {  1,  14}, {  1,  16},
  { 17,   4}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, { 18,  46}, {  1,  56}, {  4,  18},
  {  1,  42}, {  1,  43}, {  6,  44}, {  1,  35}, { 13,   4}, {  1,   8}, {  1, 140}, {  1, 141},
  {  1,  21}, {  2,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18}, {  1,  50}, {  1,  48},
  {  6,  40}, { 19,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 122 */
  {  3,  40}, {  1,  64}, {  1,  66}, {  1, 131}, {  1,   7}, {  1,   4}, {  1,   7}, {  1,  14},
  {  1,  16}, { 17,   4}, {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, { 18,  46}, {  1,  54},
  {  3,  18}, {  1,  21}, {  1,  53}, {  6,  44}, {  1,  34}, {  1,  62}, { 12,   4}, {  1,   8},
  {  1,  11}, {  1,  15}, {  1, 142}, {  1,  23}, {  3,  18}, {  1,  56}, {  7,  46}, {  1,  54},
  {  3,  18}, {  1,  21}, {  1,  57}, {  5,  40}, {  1,  51}, {  1,  41}, { 18,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 123 */
  {  4,  40}, {  1,  64}, {  1, 143}, {  1, 144}, {  2,   4}, {  1,   7}, {  1,  14}, {  1,  16},
  { 16,   4}, {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, { 16,  46}, {  1,  56}, {  4,  18},
  {  1,  42}, {  1,  43}, {  6,  44}, {  1,  35}, { 12,   4}, {  1,   8}, {  1,  11}, {  1,  15},
  {  1,   6}, {  1,   4}, {  1, 145}, {  3,  18}, {  1,  54}, {  7,  46}, {  1,  56}, {  4,  18},
  {  1,  50}, {  1,  48}, {  6,  40}, { 18,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 124 */
  {  5,  40}, {  1,  64}, {  1, 146}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 16,   4},
  {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, { 16,  46}, {  1,  54}, {  3,  18}, {  1,  21},
  {  1,  53}, {  6,  44}, {  1,  34}, {  1,  62}, { 11,   4}, {  1,   8}, {  1,  11}, {  1,  15},
  {  1,   6}, {  1,   4}, {  1,   5}, {  1, 147}, {  4,  18}, {  1,  56}, {  7,  46}, {  1,  54},
  {  3,  18}, {  1,  21}, {  1,  57}, {  5,  40}, {  1,  51}, {  1,  41}, { 17,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 125 */
  {  6,  40}, {  1,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 15,   4},
  {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, { 14,  46}, {  1,  56}, {  4,  18}, {  1,  42},
  {  1,  43}, {  6,  44}, {  1,  35}, { 11,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6},
  {  1,   4}, {  1,   5}, {  2,   0}, {  1,  17}, {  1,  21}, {  2,  18}, {  1,  54}, {  7,  46},
  {  1,  56}, {  4,  18}, {  1,  50}, {  1,  48}, {  6,  40}, { 17,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 126 */
  {  6,  40}, {  2,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 15,   4},
  {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, { 14,  46}, {  1,  54}, {  3,  18}, {  1,  21},
  {  1,  53}, {  6,  44}, {  1,  34}, {  1,  62}, { 10,   4}, {  1,   8}, {  1,  11}, {  1,  15},
  {  1,   6}, {  1,   4}, {  1,   5}, {  4,   0}, {  4,  18}, {  1,  56}, {  7,  46}, {  1,  54},
  {  3,  18}, {  1,  21}, {  1,  57}, {  5,  40}, {  1,  51}, {  1,  41}, { 16,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 127 */
  {  6,  40}, {  3,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 14,   4},
  {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, { 12,  46}, {  1,  56}, {  4,  18}, {  1,  42},
  {  1,  43}, {  6,  44}, {  1,  35}, { 10,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6},
  {  1,   4}, {  1,   5}, {  5,   0}, {  1,  17}, {  1,  21}, {  2,  18}, {  1,  54}, {  7,  46},
  {  1,  56}, {  4,  18}, {  1,  50}, {  1,  48}, {  6,  40}, { 16,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 128 */
  {  6,  40}, {  4,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 14,   4},
  {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, { 12,  46}, {  1,  54}, {  3,  18}, {  1,  21},
  {  1,  53}, {  6,  44}, {  1,  34}, {  1,  62}, {  9,   4}, {  1,   8}, {  1,  11}, {  1,  15},
  {  1,   6}, {  1,   4}, {  1,   5}, {  7,   0}, {  4,  18}, {  1,  56}, {  7,  46}, {  1,  54},
  {  3,  18}, {  1,  21}, {  1,  57}, {  5,  40}, {  1,  51}, {  1,  41}, { 15,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 129 */
  {  6,  40}, {  5,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 13,   4},
  {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, { 10,  46}, {  1,  56}, {  4,  18}, {  1,  42},
  {  1,  43}, {  6,  44}, {  1,  35}, {  9,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6},
  {  1,   4}, {  1,   5}, {  8,   0}, {  1,  17}, {  1,  21}, {  2,  18}, {  1,  54}, {  7,  46},
  {  1,  56}, {  4,  18}, {  1,  50}, {  1,  48}, {  6,  40}, { 15,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 130 */
  {  6,  40}, {  6,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 13,   4},
  {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, { 10,  46}, {  1,  54}, {  3,  18}, {  1,  21},
  {  1,  53}, {  6,  44}, {  1,  34}, {  1,  62}, {  8,   4}, {  1,   8}, {  1,  11}, {  1,  15},
  {  1,   6}, {  1,   4}, {  1,   5}, { 10,   0}, {  4,  18}, {  1,  56}, {  7,  46}, {  1,  54},
  {  3,  18}, {  1,  21}, {  1,  55}, {  5,  40}, {  1,  51}, {  1, 148}, { 14,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 131 */
  {  6,  40}, {  7,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 12,   4},
  {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, {  8,  46}, {  1,  56}, {  4,  18}, {  1,  42},
  {  1,  43}, {  6,  44}, {  1,  35}, {  8,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6},
  {  1,   4}, {  1,   5}, { 11,   0}, {  1,  17}, {  1,  21}, {  2,  18}, {  1,  54}, {  7,  46},
  {  1,  56}, {  4,  18}, {  1,  57}, {  6,  40}, {  1, 149}, { 14,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 132 */
  {  6,  40}, {  8,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 12,   4},
  {  1,  22}, {  1,  21}, {  3,  18}, {  1,  54}, {  8,  46}, {  1,  54}, {  3,  18}, {  1,  21},
  {  1,  53}, {  6,  44}, {  1,  34}, {  1,  62}, {  7,   4}, {  1,   8}, {  1,  11}, {  1,  15},
  {  1,   6}, {  1,   4}, {  1,   5}, { 13,   0}, {  4,  18}, {  1,  56}, {  6,  46}, {  1,  56},
  {  4,  18}, {  1,  57}, {  7,  40}, { 14,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 133 */
  {  6,  40}, {  9,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 11,   4},
  {  1,  25}, {  1,  23}, {  4,  18}, {  1,  56}, {  6,  46}, {  1,  56}, {  4,  18}, {  1,  42},
  {  1,  43}, {  6,  44}, {  1,  35}, {  7,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6},
  {  1,   4}, {  1,   5}, { 14,   0}, {  1,  17}, {  1,  21}, {  2,  18}, {  1,  54}, {  6,  46},
  {  1,  56}, {  3,  18}, {  1,  69}, {  1, 150}, {  6,  40}, {  1,  51}, {  1, 148}, { 13,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 134 */
  {  6,  40}, { 10,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 11,   4},
  {  1,  22}, {  1,  21}, {  3,  18}, {  1,  36}, {  1, 151}, {  4,  46}, {  1, 151}, {  1,  36},
  {  3,  18}, {  1,  21}, {  1,  53}, {  6,  44}, {  1,  34}, {  1,  62}, {  6,   4}, {  1,   8},
  {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 16,   0}, {  4,  18}, {  1,  56},
  {  5,  46}, {  1,  54}, {  3,  18}, {  1,  50}, {  1,  73}, {  7,  40}, {  1,  41}, { 13,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 135 */
  {  6,  40}, { 11,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 10,   4},
  {  1,  25}, {  1,  23}, {  4,  18}, {  1,  36}, {  1,  56}, {  2,  46}, {  1,  56}, {  1,  36},
  {  4,  18}, {  1,  42}, {  1,  43}, {  6,  44}, {  1,  35}, {  6,   4}, {  1,   8}, {  1,  11},
  {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 17,   0}, {  1,  17}, {  1,  21}, {  2,  18},
  {  1,  36}, {  1,  56}, {  2,  46}, {  1,  56}, {  1,  54}, {  3,  18}, {  1,  21}, {  1,  57},
  {  8,  40}, {  1,  41}, { 13,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 136 */
  {  6,  40}, { 12,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 10,   4},
  {  1,  22}, {  1,  21}, {  5,  18}, {  2,  36}, {  5,  18}, {  1,  21}, {  1,  53}, {  6,  44},
  {  1,  34}, {  1,  62}, {  5,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4},
  {  1,   5}, { 19,   0}, {  5,  18}, {  2,  36}, {  5,  18}, {  1,  50}, {  1,  48}, {  8,  40},
  {  1,  63}, { 13,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 137 */
  {  6,  40}, { 13,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, {  9,   4},
  {  1,  25}, {  1,  23}, { 12,  18}, {  1,  42}, {  1,  43}, {  6,  44}, {  1,  35}, {  5,   4},
  {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 20,   0}, {  1,  17},
  {  1,  21}, {  9,  18}, {  1,  47}, {  1,  70}, {  9,  40}, { 14,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  36}, {  2,  18}, {  1,  47}, {  1,  73},  /* Row 138 */
  {  6,  40}, { 14,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, {  9,   4},
  {  1,  22}, {  1,  21}, { 10,  18}, {  1,  21}, {  1,  53}, {  6,  44}, {  1,  34}, {  1,  62},
  {  4,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 22,   0},
  {  9,  18}, {  1,  47}, {  1,  70}, {  8,  40}, {  1,  51}, {  1,  41}, { 14,   0},
  { 18,   0}, {  3,  18}, {  1,  36}, {  8,  46}, {  1,  69}, {  2,  47}, {  1, 152}, {  1,  73},  /* Row 139 */
  {  6,  40}, { 15,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, {  8,   4},
  {  1,  19}, {  1,  27}, {  1,  21}, {  8,  18}, {  1,  21}, {  1, 153}, {  1, 103}, {  6,  44},
  {  1,  35}, {  4,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5},
  { 23,   0}, {  1, 147}, {  1, 154}, {  1,  18}, {  1,  21}, {  2,  18}, {  1,  21}, {  1,  47},
  {  1, 155}, {  1, 150}, { 10,  40}, { 15,   0},
  { 25,   0}, {  5,  48}, {  4,  73}, {  7,  40}, { 16,   0}, {  1,   5}, {  3,   4}, {  1,   7},  /* Row 140 */
  {  1,  14}, {  1,  16}, {  8,   4}, {  1,  19}, {  1,  22}, {  1,  23}, {  1,  21}, {  4,  18},
  {  1,  21}, {  1,  42}, {  1,  53}, {  1, 103}, {  6,  44}, {  1,  34}, {  1,  62}, {  3,   4},
  {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 27,   0}, {  1, 156},
  {  1,  17}, {  1, 157}, {  1,  55}, {  1,  48}, {  1,  64}, {  9,  40}, {  1,  51}, {  1,  41},
  { 15,   0},
  { 25,   0}, { 16,  40}, { 17,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16},  /* Row 141 */
  {  9,   4}, {  1,  25}, {  1,  22}, {  1, 158}, {  2,  98}, {  1,  77}, {  1,  53}, {  1,  43},
  {  7,  44}, {  1,  33}, {  1, 136}, {  3,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6},
  {  1,   4}, {  1,   5}, { 30,   0}, {  1,  63}, { 11,  40}, {  1,  51}, {  1,  40}, { 16,   0},
  { 25,   0}, { 16,  40}, { 18,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16},  /* Row 142 */
  { 10,   4}, {  1,  19}, {  1, 159}, {  1, 160}, {  1, 103}, {  7,  44}, {  1,  79}, {  1, 161},
  {  1, 136}, {  3,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5},
  { 32,   0}, {  1,  41}, {  1,  51}, {  8,  40}, {  1,  51}, {  1,  40}, { 17,   0},
  { 25,   0}, { 16,  40}, { 19,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16},  /* Row 143 */
  { 11,   4}, {  1,  62}, {  1,  35}, {  1,  34}, {  5,  33}, {  1, 161}, {  1, 136}, {  1,   7},
  {  3,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 34,   0},
  {  8,  40}, {  1,  51}, {  1,  40}, { 18,   0},
  { 25,   0}, { 16,  40}, { 20,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16},  /* Row 144 */
  { 12,   4}, {  7,   7}, {  4,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4},
  {  1,   5}, { 35,   0}, {  1,  49}, {  7,  40}, {  1,  41}, { 19,   0},
  { 25,   0}, { 16,  40}, { 21,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16},  /* Row 145 */
  { 21,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 64,   0},
  { 25,   0}, { 16,  40}, { 22,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16},  /* Row 146 */
  { 19,   4}, {  1,   8}, {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 65,   0},
  { 64,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 17,   4}, {  1,   8},  /* Row 147 */
  {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 66,   0},
  { 65,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 15,   4}, {  1,   8},  /* Row 148 */
  {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 67,   0},
  { 66,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 13,   4}, {  1,   8},  /* Row 149 */
  {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 68,   0},
  { 67,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, { 11,   4}, {  1,   8},  /* Row 150 */
  {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 69,   0},
  { 68,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, {  9,   4}, {  1,   8},  /* Row 151 */
  {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 70,   0},
  { 69,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, {  7,   4}, {  1,   8},  /* Row 152 */
  {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 71,   0},
  { 70,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, {  5,   4}, {  1,   8},  /* Row 153 */
  {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 72,   0},
  { 71,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, {  3,   4}, {  1,   8},  /* Row 154 */
  {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 73,   0},
  { 72,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1,  16}, {  1,   4}, {  1,   8},  /* Row 155 */
  {  1,  11}, {  1,  15}, {  1,   6}, {  1,   4}, {  1,   5}, { 74,   0},
  { 73,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1,  14}, {  1, 118}, {  1,  11}, {  1,  15},  /* Row 156 */
  {  1,   6}, {  1,   4}, {  1,   5}, { 75,   0},
  { 74,   0}, {  1,   5}, {  3,   4}, {  1,   7}, {  1, 162}, {  1,  15}, {  1,   6}, {  1,   4},  /* Row 157 */
  {  1,   5}, { 76,   0},
  { 75,   0}, {  1,   5}, {  2,   4}, {  1,   3}, {  1, 163}, {  1,   6}, {  1,   4}, {  1,   5},  /* Row 158 */
  { 77,   0},
  { 76,   0}, {  1,   5}, {  4,   4}, {  1,   5}, { 78,   0}                                       /* Row 159 */
};

#elif CONFIG_EXAMPLES_NXIMAGE_BPP == 16

static const struct pix_run_s g_nuttx[] =
{
  { 76,   0}, {  1,   1}, {  1,   2}, {  1,   3}, {  4,   4}, {  1,   5}, { 76,   0},              /* Row 0 */
  { 75,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  2,   8}, {  3,   4}, {  1,   5},  /* Row 1 */
  { 75,   0},
  { 74,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  10}, {  1,  11},  /* Row 2 */
  {  1,   8}, {  3,   4}, {  1,   5}, { 74,   0},
  { 73,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},  /* Row 3 */
  {  1,  14}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5}, { 73,   0},
  { 72,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},  /* Row 4 */
  {  1,   6}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5}, { 72,   0},
  { 71,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},  /* Row 5 */
  {  1,   6}, {  2,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5},
  { 71,   0},
  { 70,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},  /* Row 6 */
  {  1,   6}, {  4,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5},
  { 70,   0},
  { 69,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},  /* Row 7 */
  {  1,   6}, {  6,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5},
  { 69,   0},
  { 68,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},  /* Row 8 */
  {  1,   6}, {  8,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5},
  { 68,   0},
  { 67,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},  /* Row 9 */
  {  1,   6}, { 10,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5},
  { 67,   0},
  { 66,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},  /* Row 10 */
  {  1,   6}, { 12,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5},
  { 66,   0},
  { 65,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},  /* Row 11 */
  {  1,   6}, { 14,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5},
  { 65,   0},
  { 64,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},  /* Row 12 */
  {  1,   6}, { 16,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5},
  { 64,   0},
  { 63,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},  /* Row 13 */
  {  1,   6}, { 18,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5},
  { 63,   0},
  { 62,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},  /* Row 14 */
  {  1,   6}, { 20,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5},
  { 62,   0},
  { 61,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},  /* Row 15 */
  {  1,   6}, { 22,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5},
  { 61,   0},
  { 60,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},  /* Row 16 */
  {  1,   6}, { 24,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5},
  { 60,   0},
  { 59,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},  /* Row 17 */
  {  1,   6}, { 26,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5},
  { 59,   0},
  { 58,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},  /* Row 18 */
  {  1,   6}, { 28,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5},
  { 26,   0}, {  1,  17}, {  5,  18}, {  1,  17}, { 25,   0},
  { 57,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},  /* Row 19 */
  {  1,   6}, { 10,   4}, {  5,  19}, { 15,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8},
  {  3,   4}, {  1,   5}, { 23,   0}, {  1,  20}, {  9,  18}, {  1,  20}, { 23,   0},
  { 56,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},  /* Row 20 */
  {  1,   6}, {  9,   4}, {  1,  19}, {  1,  21}, {  1,  22}, {  3,  23}, {  1,  22}, {  1,  21},
  {  1,  24}, { 14,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5},
  { 22,   0}, { 11,  18}, {  1,  20}, { 22,   0},
  { 23,   0}, {  1,  17}, {  5,  18}, {  1,  25}, { 25,   0}, {  1,   1}, {  1,   2}, {  1,   6},  /* Row 21 */
  {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  9,   4}, {  1,  19}, {  1,  26},
  {  7,  18}, {  1,  22}, {  1,  27}, { 14,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8},
  {  3,   4}, {  1,   5}, { 20,   0}, {  1,  17}, { 12,  18}, {  1,  20}, { 21,   0},
  { 21,   0}, {  1,  20}, {  9,  18}, {  1,  20}, { 22,   0}, {  1,   1}, {  1,   2}, {  1,   6},  /* Row 22 */
  {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  9,   4}, {  1,  19}, {  1,  26},
  {  9,  18}, {  1,  28}, {  1,  27}, {  4,   8}, {  1,   7}, {  9,   4}, {  1,   7}, {  1,  16},
  {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5}, { 19,   0}, { 14,  18}, { 21,   0},
  { 21,   0}, { 11,  18}, {  1,  20}, { 20,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7},  /* Row 23 */
  {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, { 10,   4}, {  1,  21}, { 11,  18}, {  1,  29},
  {  1,  30}, {  2,  31}, {  1,  32}, {  1,  33}, {  1,   8}, {  9,   4}, {  1,   7}, {  1,  16},
  {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5}, { 17,   0}, {  1,  17}, {  5,  18}, {  1,  34},
  {  2,  35}, {  1,  34}, {  5,  18}, {  1,  17}, { 20,   0},
  { 20,   0}, {  1,  36}, { 12,  18}, {  1,  37}, {  4,  38}, {  1,  39}, { 13,   0}, {  1,   1},  /* Row 24 */
  {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, { 10,   4},
  {  1,  24}, {  1,  22}, {  4,  18}, {  3,  34}, {  5,  18}, {  1,  40}, {  1,  41}, {  3,  31},
  {  1,  42}, {  1,   8}, {  9,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4},
  {  1,   5}, { 16,   0}, {  5,  18}, {  1,  43}, {  4,  44}, {  1,  43}, {  4,  18}, {  1,  35},
  {  1,  45}, {  1,  46}, { 18,   0},
  { 19,   0}, {  1,  17}, { 13,  18}, {  1,  47}, {  1,  45}, {  3,  38}, {  1,  48}, {  1,  38},  /* Row 25 */
  { 11,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},
  {  1,   6}, { 11,   4}, {  1,  49}, {  4,  18}, {  1,  35}, {  3,  44}, {  1,  43}, {  5,  18},
  {  1,  50}, {  4,  31}, {  1,  42}, {  1,   8}, {  9,   4}, {  1,   7}, {  1,  16}, {  1,  15},
  {  1,   8}, {  3,   4}, {  1,   5}, { 14,   0}, {  1,  17}, {  4,  18}, {  1,  34}, {  6,  44},
  {  1,  34}, {  4,  18}, {  1,  51}, {  1,  38}, {  1,  46}, { 17,   0},
  { 19,   0}, {  5,  18}, {  1,  34}, {  2,  52}, {  1,  35}, {  6,  18}, {  1,  53}, {  4,  38},  /* Row 26 */
  {  1,  48}, {  1,  39}, {  9,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9},
  {  1,  12}, {  1,  13}, {  1,   6}, { 11,   4}, {  1,   3}, {  1,  54}, {  4,  18}, {  1,  55},
  {  4,  44}, {  1,  43}, {  4,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  42}, {  1,   8},
  {  9,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5}, { 13,   0},
  {  5,  18}, {  1,  52}, {  6,  44}, {  1,  52}, {  4,  18}, {  1,  53}, {  2,  38}, {  1,  46},
  { 16,   0},
  { 18,   0}, {  1,  17}, {  4,  18}, {  1,  43}, {  4,  44}, {  1,  35}, {  5,  18}, {  1,  47},  /* Row 27 */
  {  1,  45}, {  5,  38}, {  8,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9},
  {  1,  12}, {  1,  13}, {  1,   6}, { 12,   4}, {  1,  19}, {  1,  23}, {  3,  18}, {  1,  35},
  {  6,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, {  9,   4},
  {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5}, { 11,   0}, {  1,  17},
  {  4,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  53}, {  3,  38}, { 16,   0},
  { 18,   0}, {  4,  18}, {  1,  34}, {  5,  44}, {  1,  55}, {  1,  34}, {  5,  18}, {  1,  53},  /* Row 28 */
  {  4,  38}, {  1,  48}, {  1,  39}, {  6,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7},
  {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, { 13,   4}, {  1,  19}, {  1,  23}, {  3,  18},
  {  1,  52}, {  6,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32},
  {  1,  56}, { 10,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5},
  { 10,   0}, {  5,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  51}, {  2,  38},
  {  1,  48}, {  1,  39}, { 15,   0},
  { 18,   0}, {  4,  18}, {  1,  52}, {  6,  44}, {  1,  52}, {  5,  18}, {  1,  47}, {  1,  45},  /* Row 29 */
  {  5,  38}, {  5,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12},
  {  1,  13}, {  1,   6}, { 14,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44},
  {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, { 10,   4}, {  1,   7},
  {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5}, {  8,   0}, {  1,  17}, {  4,  18},
  {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  47}, {  1,  45}, {  4,  38}, { 15,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  5,  18}, {  1,  53}, {  4,  38},  /* Row 30 */
  {  1,  48}, {  1,  39}, {  3,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9},
  {  1,  12}, {  1,  13}, {  1,   6}, { 15,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34},
  {  8,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56},
  { 11,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5}, {  7,   0},
  {  5,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  53}, {  5,  38}, {  1,   1},
  { 14,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  52}, {  5,  18}, {  1,  47}, {  1,  45},  /* Row 31 */
  {  5,  38}, {  2,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12},
  {  1,  13}, {  1,   6}, { 16,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  9,  44},
  {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, { 11,   4}, {  1,   7},
  {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5}, {  5,   0}, {  1,  17}, {  4,  18},
  {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  47}, {  1,  45}, {  5,  38}, {  1,  39},
  { 14,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  9,  44}, {  1,  34}, {  5,  18}, {  1,  53}, {  4,  38},  /* Row 32 */
  {  1,  48}, {  1,  39}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12},
  {  1,  13}, {  1,   6}, { 17,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  9,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, { 12,   4},
  {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5}, {  4,   0}, {  5,  18},
  {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  53}, {  6,  38}, {  1,   1}, { 14,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  9,  44}, {  1,  52}, {  5,  18}, {  1,  47}, {  1,  45},  /* Row 33 */
  {  3,  38}, {  1,  48}, {  1,  57}, {  1,  13}, {  1,   6}, {  1,   7}, {  1,   9}, {  1,  12},
  {  1,  13}, {  1,   6}, { 18,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, { 10,  44},
  {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, { 12,   4}, {  1,   7},
  {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5}, {  2,   0}, {  1,  17}, {  4,  18},
  {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  47}, {  1,  45}, {  6,  38}, { 15,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, { 10,  44}, {  1,  34}, {  5,  18}, {  1,  53}, {  2,  38},  /* Row 34 */
  {  1,  48}, {  1,  58}, {  1,  59}, {  1,  60}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},
  {  1,   6}, { 19,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, { 10,  44}, {  1,  52},
  {  4,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, { 13,   4}, {  1,   7},
  {  1,  16}, {  1,  15}, {  1,   8}, {  3,   4}, {  1,   5}, {  1,   0}, {  5,  18}, {  1,  52},
  {  7,  44}, {  1,  34}, {  4,  18}, {  1,  61}, {  5,  38}, {  1,  48}, {  1,  39}, { 15,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, { 10,  44}, {  1,  52}, {  5,  18}, {  1,  47}, {  1,  45},  /* Row 35 */
  {  1,  48}, {  1,  58}, {  1,  59}, {  1,  31}, {  1,  32}, {  1,  62}, {  1,  12}, {  1,  13},
  {  1,   6}, { 20,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, { 11,  44}, {  1,  34},
  {  4,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, { 13,   4}, {  1,   7}, {  1,  16},
  {  1,  15}, {  1,   8}, {  3,   4}, {  1,  63}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  52},
  {  4,  18}, {  1,  47}, {  1,  45}, {  6,  38}, { 16,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, { 11,  44}, {  1,  34}, {  5,  18}, {  1,  64}, {  1,  58},  /* Row 36 */
  {  1,  59}, {  3,  31}, {  1,  65}, {  1,  66}, {  1,   6}, { 21,   4}, {  1,  19}, {  1,  23},
  {  2,  18}, {  1,  34}, { 11,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  4,  31},
  {  1,  32}, {  1,  56}, { 14,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  1,   4},
  {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  53},
  {  5,  38}, {  1,  48}, {  1,  39}, { 16,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, { 11,  44}, {  1,  52}, {  5,  18}, {  1,  67}, {  1,  68},  /* Row 37 */
  {  5,  31}, {  1,  69}, { 22,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, { 12,  44},
  {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, { 14,   4}, {  1,   7},
  {  1,  16}, {  1,  15}, {  1,   8}, {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  52},
  {  4,  18}, {  1,  47}, {  1,  45}, {  6,  38}, { 17,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, { 12,  44}, {  1,  34}, {  5,  18}, {  1,  50}, {  5,  31},  /* Row 38 */
  {  1,  70}, {  1,   7}, { 21,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, { 12,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, { 15,   4},
  {  1,   7}, {  1,  16}, {  1,  71}, {  1,  67}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  34},
  {  4,  18}, {  1,  53}, {  5,  38}, {  1,  48}, {  1,  39}, { 17,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, { 12,  44}, {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41},  /* Row 39 */
  {  4,  31}, {  1,  32}, {  1,  56}, { 21,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34},
  { 13,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, { 16,   4},
  {  1,  72}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  47}, {  1,  45},
  {  6,  38}, { 18,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, { 13,  44}, {  1,  34}, {  5,  18}, {  1,  50}, {  5,  31},  /* Row 40 */
  {  1,  33}, {  1,   7}, { 20,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, { 13,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, { 15,   4},
  {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  53},
  {  5,  38}, {  1,  48}, {  1,  39}, { 18,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, { 13,  44}, {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41},  /* Row 41 */
  {  4,  31}, {  1,  32}, {  1,  56}, { 20,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34},
  { 14,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, { 14,   4},
  {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  47}, {  1,  45},
  {  6,  38}, { 19,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, { 14,  44}, {  1,  34}, {  5,  18}, {  1,  50}, {  5,  31},  /* Row 42 */
  {  1,  33}, {  1,   7}, { 19,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, { 14,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, { 13,   4},
  {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  53},
  {  5,  38}, {  1,  48}, {  1,  39}, { 19,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, { 14,  44}, {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41},  /* Row 43 */
  {  4,  31}, {  1,  32}, {  1,  56}, { 19,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34},
  {  7,  44}, {  1,  73}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  33},
  {  1,   7}, { 12,   4}, {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18},
  {  1,  47}, {  1,  45}, {  6,  38}, { 20,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, { 15,  44}, {  1,  34}, {  5,  18}, {  1,  50}, {  5,  31},  /* Row 44 */
  {  1,  33}, {  1,   7}, { 18,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44},
  {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32},
  {  1,  56}, { 11,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  34},
  {  4,  18}, {  1,  53}, {  5,  38}, {  1,  48}, {  1,  39}, { 20,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  73}, {  6,  44}, {  1,  52}, {  5,  18},  /* Row 45 */
  {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, { 18,   4}, {  1,  19}, {  1,  23},
  {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18},
  {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, { 10,   4}, {  1,  21}, {  4,  18}, {  1,  34},
  {  7,  44}, {  1,  52}, {  4,  18}, {  1,  47}, {  1,  45}, {  6,  38}, { 21,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  7,  44}, {  1,  34}, {  5,  18},  /* Row 46 */
  {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, { 17,   4}, {  1,  19}, {  1,  23}, {  2,  18},
  {  1,  34}, {  7,  44}, {  2,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41},
  {  4,  31}, {  1,  32}, {  1,  56}, {  9,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52},
  {  7,  44}, {  1,  34}, {  4,  18}, {  1,  53}, {  5,  38}, {  1,  48}, {  1,  39}, { 21,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  1,  52}, {  6,  44}, {  1,  52},  /* Row 47 */
  {  5,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, { 17,   4}, {  1,  19},
  {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  1,  18}, {  1,  52}, {  7,  44},
  {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, {  8,   4}, {  1,  21},
  {  4,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  47}, {  1,  45}, {  6,  38},
  { 22,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  2,  34}, {  7,  44}, {  1,  34}, {  5,  18},  /* Row 48 */
  {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, { 16,   4}, {  1,  19}, {  1,  23}, {  2,  18},
  {  1,  34}, {  7,  44}, {  1,  34}, {  1,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18},
  {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, {  7,   4}, {  1,  24}, {  1,  22},
  {  4,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  74}, {  1,  48}, {  4,  38},
  {  1,  48}, {  1,  39}, { 22,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  1,  18}, {  1,  52}, {  6,  44},  /* Row 49 */
  {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, { 16,   4},
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  52},
  {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, {  6,   4},
  {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  75},
  {  1,  58}, {  1,  48}, {  4,  38}, { 23,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  1,  18}, {  1,  34}, {  7,  44},  /* Row 50 */
  {  1,  34}, {  5,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, { 15,   4}, {  1,  19},
  {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  34}, {  7,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, {  5,   4},
  {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50},
  {  1,  31}, {  1,  59}, {  1,  58}, {  1,  48}, {  1,  38}, {  1,  48}, {  1,  39}, { 23,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  52}, {  6,  44},  /* Row 51 */
  {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, { 15,   4},
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  3,  18}, {  1,  52},
  {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, {  4,   4},
  {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41},
  {  2,  31}, {  1,  59}, {  1,  58}, {  1,  48}, {  1,  38}, { 24,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  34}, {  7,  44},  /* Row 52 */
  {  1,  34}, {  5,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, { 14,   4}, {  1,  19},
  {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  3,  18}, {  1,  34}, {  7,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, {  3,   4},
  {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50},
  {  4,  31}, {  1,  59}, {  1,  76}, {  1,  39}, { 24,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  3,  18}, {  1,  52}, {  6,  44},  /* Row 53 */
  {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, { 14,   4},
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  52},
  {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, {  2,   4},
  {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41},
  {  5,  31}, {  1,  77}, {  1,  78}, { 24,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  3,  18}, {  1,  34}, {  7,  44},  /* Row 54 */
  {  1,  34}, {  5,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, { 13,   4}, {  1,  19},
  {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  34}, {  7,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, {  1,   4},
  {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50},
  {  6,  31}, {  1,  32}, {  1,  56}, {  1,   5}, { 23,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  4,  18}, {  1,  52}, {  6,  44},  /* Row 55 */
  {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, { 13,   4},
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  5,  18}, {  1,  52},
  {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   4}, {  1,  21},
  {  4,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  6,  31},
  {  1,  79}, {  1,   7}, {  1,   4}, {  1,   5}, { 22,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  4,  18}, {  1,  34}, {  7,  44},  /* Row 56 */
  {  1,  34}, {  5,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, { 12,   4}, {  1,  19},
  {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  5,  18}, {  1,  34}, {  7,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  80}, {  1,  22},
  {  4,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  6,  31}, {  1,  81},
  {  1,  15}, {  1,   8}, {  2,   4}, {  1,   5}, { 21,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  5,  18}, {  1,  52}, {  6,  44},  /* Row 57 */
  {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, { 12,   4},
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  6,  18}, {  1,  52},
  {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  82}, {  4,  18}, {  1,  34},
  {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  5,  31}, {  1,  32}, {  1,  56},
  {  1,  16}, {  1,  15}, {  1,   8}, {  2,   4}, {  1,   5}, { 20,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  5,  18}, {  1,  34}, {  7,  44},  /* Row 58 */
  {  1,  34}, {  5,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, { 11,   4}, {  1,  19},
  {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  6,  18}, {  1,  34}, {  7,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  3,  31}, {  1,  41}, {  1,  40}, {  4,  18},
  {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  6,  31}, {  1,  33}, {  2,   7},
  {  1,  16}, {  1,  15}, {  1,   8}, {  2,   4}, {  1,   5}, { 19,   0},
  { 17,   0}, {  1,  83}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  6,  18}, {  1,  52},  /* Row 59 */
  {  6,  44}, {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56},
  { 11,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  7,  18},
  {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  3,  31}, {  1,  50}, {  4,  18},
  {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  5,  31}, {  1,  32},
  {  1,  56}, {  2,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  2,   4}, {  1,   5},
  { 18,   0},
  { 16,   0}, {  1,   1}, {  1,  84}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34},  /* Row 60 */
  {  6,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  5,  18}, {  1,  50}, {  5,  31}, {  1,  33},
  {  1,   7}, { 10,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34},
  {  3,  18}, {  1,  85}, {  3,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40},
  {  1,  41}, {  1,  31}, {  1,  41}, {  1,  40}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  34},
  {  4,  18}, {  1,  50}, {  6,  31}, {  1,  33}, {  1,   7}, {  3,   4}, {  1,   7}, {  1,  16},
  {  1,  15}, {  1,   8}, {  2,   4}, {  1,   5}, { 17,   0},
  { 15,   0}, {  1,   1}, {  1,   2}, {  1,  86}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44},  /* Row 61 */
  {  1,  34}, {  7,  18}, {  1,  52}, {  6,  44}, {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41},
  {  4,  31}, {  1,  32}, {  1,  56}, { 10,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34},
  {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  87}, {  4,  18}, {  1,  52}, {  7,  44},
  {  1,  34}, {  4,  18}, {  1,  50}, {  1,  88}, {  1,  50}, {  4,  18}, {  1,  34}, {  7,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  5,  31}, {  1,  32}, {  1,  56}, {  5,   4},
  {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  2,   4}, {  1,   5}, { 16,   0},
  { 14,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,  89}, {  1,  23}, {  2,  18}, {  1,  34},  /* Row 62 */
  {  8,  44}, {  1,  34}, {  7,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  5,  18}, {  1,  50},
  {  5,  31}, {  1,  33}, {  1,   7}, {  9,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34},
  {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  90}, {  1,  40}, {  3,  18}, {  1,  34},
  {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  91}, {  1,  40}, {  4,  18}, {  1,  52},
  {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  6,  31}, {  1,  33}, {  1,   7}, {  6,   4},
  {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  2,   4}, {  1,   5}, { 15,   0},
  { 13,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   8}, {  1,  71}, {  1,  29}, {  2,  18},  /* Row 63 */
  {  1,  34}, {  8,  44}, {  1,  34}, {  3,  18}, {  1,  85}, {  4,  18}, {  1,  52}, {  6,  44},
  {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, {  9,   4},
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29},
  {  1,  92}, {  1,  50}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  29},
  {  4,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  5,  31},
  {  1,  32}, {  1,  56}, {  8,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  2,   4},
  {  1,   5}, { 14,   0},
  { 12,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   8}, {  1,  93}, {  1,  94}, {  1,  95},  /* Row 64 */
  {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  87}, {  4,  18},
  {  1,  34}, {  7,  44}, {  1,  34}, {  5,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7},
  {  8,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18},
  {  1,  29}, {  1,  92}, {  1,  41}, {  1,  40}, {  3,  18}, {  1,  34}, {  7,  44}, {  1,  52},
  {  9,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  6,  31}, {  1,  33},
  {  1,   7}, {  9,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  2,   4}, {  1,   5},
  { 13,   0},
  { 11,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   8}, {  1,  93}, {  1,  96}, {  1,  97},  /* Row 65 */
  {  1,  28}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  90},
  {  1,  40}, {  4,  18}, {  1,  52}, {  6,  44}, {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41},
  {  4,  31}, {  1,  32}, {  1,  56}, {  8,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34},
  {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  1,  31}, {  1,  50}, {  4,  18},
  {  1,  52}, {  7,  44}, {  1,  34}, {  7,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18},
  {  1,  40}, {  1,  41}, {  5,  31}, {  1,  32}, {  1,  56}, { 11,   4}, {  1,   7}, {  1,  16},
  {  1,  15}, {  1,   8}, {  2,   4}, {  1,   5}, { 12,   0},
  { 10,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   8}, {  1,  93}, {  1,  96}, {  1,  98},  /* Row 66 */
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29},
  {  1,  92}, {  1,  50}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  5,  18}, {  1,  50},
  {  5,  31}, {  1,  33}, {  1,   7}, {  7,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34},
  {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  1,  31}, {  1,  41}, {  1,  40},
  {  3,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  7,  18}, {  1,  52}, {  7,  44}, {  1,  34},
  {  4,  18}, {  1,  50}, {  6,  31}, {  1,  33}, {  1,   7}, { 12,   4}, {  1,   7}, {  1,  16},
  {  1,  15}, {  1,   8}, {  2,   4}, {  1,   5}, { 11,   0},
  {  9,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   8}, {  1,  93}, {  1,  96}, {  1,  98},  /* Row 67 */
  {  1,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18},
  {  1,  29}, {  1,  92}, {  1,  41}, {  1,  40}, {  4,  18}, {  1,  52}, {  6,  44}, {  1,  52},
  {  5,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, {  7,   4}, {  1,  19},
  {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92},
  {  2,  31}, {  1,  50}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  5,  18}, {  1,  34},
  {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  5,  31}, {  1,  32}, {  1,  56},
  { 14,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  2,   4}, {  1,   5}, { 10,   0},
  {  8,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   8}, {  1,  93}, {  1,  96}, {  1,  98},  /* Row 68 */
  {  2,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18},
  {  1,  29}, {  1,  92}, {  1,  31}, {  1,  50}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34},
  {  5,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, {  6,   4}, {  1,  19}, {  1,  23},
  {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  2,  31},
  {  1,  41}, {  1,  40}, {  3,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  5,  18}, {  1,  52},
  {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  6,  31}, {  1,  33}, {  1,   7}, { 15,   4},
  {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  2,   4}, {  1,   5}, {  9,   0},
  {  7,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   8}, {  1,  93}, {  1,  96}, {  1,  98},  /* Row 69 */
  {  3,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18},
  {  1,  29}, {  1,  92}, {  1,  31}, {  1,  41}, {  1,  40}, {  4,  18}, {  1,  52}, {  6,  44},
  {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, {  6,   4},
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29},
  {  1,  92}, {  3,  31}, {  1,  50}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  3,  18},
  {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  5,  31}, {  1,  32},
  {  1,  56}, { 17,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  2,   4}, {  1,   5},
  {  8,   0},
  {  6,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   8}, {  1,  93}, {  1,  96}, {  1,  98},  /* Row 70 */
  {  4,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18},
  {  1,  29}, {  1,  92}, {  2,  31}, {  1,  50}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34},
  {  5,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, {  5,   4}, {  1,  19}, {  1,  23},
  {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  3,  31},
  {  1,  41}, {  1,  40}, {  3,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  3,  18}, {  1,  52},
  {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  6,  31}, {  1,  33}, {  1,   7}, { 18,   4},
  {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  2,   4}, {  1,   5}, {  7,   0},
  {  5,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   8}, {  1,  93}, {  1,  96}, {  1,  98},  /* Row 71 */
  {  5,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18},
  {  1,  29}, {  1,  92}, {  2,  31}, {  1,  41}, {  1,  40}, {  4,  18}, {  1,  52}, {  6,  44},
  {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, {  5,   4},
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29},
  {  1,  92}, {  4,  31}, {  1,  50}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  1,  18},
  {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  5,  31}, {  1,  32},
  {  1,  56}, { 20,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  2,   4}, {  1,   5},
  {  6,   0},
  {  4,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   8}, {  1,  93}, {  1,  96}, {  1,  98},  /* Row 72 */
  {  6,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18},
  {  1,  29}, {  1,  92}, {  3,  31}, {  1,  50}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34},
  {  5,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, {  4,   4}, {  1,  19}, {  1,  23},
  {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  4,  31},
  {  1,  41}, {  1,  40}, {  3,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  1,  18}, {  1,  52},
  {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  6,  31}, {  1,  33}, {  1,   7}, { 21,   4},
  {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  2,   4}, {  1,   5}, {  5,   0},
  {  3,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   8}, {  1,  93}, {  1,  96}, {  1,  98},  /* Row 73 */
  {  7,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18},
  {  1,  29}, {  1,  92}, {  3,  31}, {  1,  41}, {  1,  40}, {  4,  18}, {  1,  52}, {  6,  44},
  {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, {  4,   4},
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29},
  {  1,  92}, {  5,  31}, {  1,  50}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  99}, {  7,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  5,  31}, {  1,  32}, {  1,  56}, { 23,   4},
  {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  2,   4}, {  1,   5}, {  4,   0},
  {  2,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   8}, {  1,  93}, {  1,  96}, {  1,  98},  /* Row 74 */
  {  8,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18},
  {  1,  29}, {  1,  92}, {  4,  31}, {  1,  50}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34},
  {  5,  18}, {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, {  3,   4}, {  1,  19}, {  1,  23},
  {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  5,  31},
  {  1,  41}, {  1,  67}, {  3,  18}, {  1,  34}, { 15,  44}, {  1,  34}, {  4,  18}, {  1,  50},
  {  6,  31}, {  1,  33}, {  1,   7}, { 24,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8},
  {  2,   4}, {  1,   5}, {  3,   0},
  {  1,   0}, {  1,   1}, {  1,   2}, {  1,   6}, {  1,   8}, {  1,  93}, {  1,  96}, {  1,  98},  /* Row 75 */
  {  9,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18},
  {  1,  29}, {  1,  92}, {  4,  31}, {  1,  41}, {  1,  40}, {  4,  18}, {  1,  52}, {  6,  44},
  {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, {  3,   4},
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29},
  {  1,  92}, {  6,  31}, {  1,  72}, {  4,  18}, {  1,  52}, { 13,  44}, {  1,  52}, {  4,  18},
  {  1,  40}, {  1,  41}, {  5,  31}, {  1,  32}, {  1,  56}, { 26,   4}, {  1,   7}, {  1,  16},
  {  1,  15}, {  1,   8}, {  2,   4}, {  1,   5}, {  2,   0},
  {  1,   1}, {  1,   2}, {  1,   6}, {  1,   8}, {  1,  93}, {  1,  96}, {  1,  98}, { 10,   4},  /* Row 76 */
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29},
  {  1,  92}, {  5,  31}, {  1,  50}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  5,  18},
  {  1,  50}, {  5,  31}, {  1,  33}, {  1,   7}, {  2,   4}, {  1,  19}, {  1,  23}, {  2,  18},
  {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31}, {  1,  80},
  {  1,  22}, {  3,  18}, {  1,  34}, { 13,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  6,  31},
  {  1,  33}, {  1,   7}, { 27,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  2,   4},
  {  1,   5}, {  1,   0},
  {  1,   2}, {  1,   6}, {  1,   8}, {  1,  93}, {  1,  96}, {  1,  98}, { 11,   4}, {  1,  19},  /* Row 77 */
  {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92},
  {  5,  31}, {  1,  41}, {  1,  67}, {  4,  18}, {  1,  52}, {  6,  44}, {  1,  52}, {  5,  18},
  {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, {  2,   4}, {  1,  19}, {  1,  23},
  {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31},
  {  1,   8}, {  1,  21}, {  4,  18}, {  1,  52}, { 11,  44}, {  1,  52}, {  4,  18}, {  1,  40},
  {  1,  41}, {  5,  31}, {  1,  32}, {  1,  56}, { 29,   4}, {  1,   7}, {  1,  16}, {  1,  15},
  {  1,   8}, {  2,   4}, {  1,   5},
  {  1,   3}, {  1,   8}, {  1,  93}, {  1,  96}, {  1,  98}, { 12,   4}, {  1,  19}, {  1,  23},  /* Row 78 */
  {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31},
  {  1,  72}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  5,  18}, {  1,  50}, {  5,  31},
  {  1,  33}, {  1,   7}, {  1,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44},
  {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31}, {  1,   8}, {  1,  24}, {  1,  22},
  {  3,  18}, {  1,  34}, { 11,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  6,  31}, {  1,  33},
  {  1,   7}, { 30,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,   8}, {  2,   4},
  {  1,   4}, {  1,  33}, {  1, 100}, {  1,  98}, { 13,   4}, {  1,  19}, {  1,  23}, {  2,  18},  /* Row 79 */
  {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31}, {  1,  80},
  {  1,  22}, {  4,  18}, {  1,  52}, {  6,  44}, {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41},
  {  4,  31}, {  1,  32}, {  1,  56}, {  1,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34},
  {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31}, {  1,   8}, {  1,   4},
  {  1, 101}, {  4,  18}, {  1,  52}, {  9,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41},
  {  5,  31}, {  1,  32}, {  1,  56}, { 32,   4}, {  1,   7}, {  1,  16}, {  1,  15}, {  1,  56},
  {  1,   7},
  {  1,   4}, {  1,   8}, {  1, 102}, {  1,   9}, {  1,   7}, { 12,   4}, {  1,  19}, {  1,  23},  /* Row 80 */
  {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31},
  {  1,   8}, {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  5,  18}, {  1,  50},
  {  5,  31}, {  1,  33}, {  1,   7}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44},
  {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31}, {  1,   8}, {  1,   4}, {  1, 101},
  {  4,  18}, {  1,  34}, {  9,  44}, {  1,  35}, {  4,  18}, {  1,  50}, {  6,  31}, {  1,  33},
  {  1,   7}, { 33,   4}, {  1,   7}, {  1, 103}, {  1,  10}, {  1,   8},
  {  2,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, { 11,   4}, {  1,  19}, {  1,  23},  /* Row 81 */
  {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31},
  {  1,   8}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  6,  44}, {  1,  52}, {  5,  18},
  {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1,  56}, {  1,  19}, {  1,  23}, {  2,  18},
  {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31}, {  1,   8},
  {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  8,  44}, {  1, 104}, {  4,  18}, {  1,  29},
  {  1,  68}, {  6,  31}, {  1,  56}, { 33,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  66},
  {  1,   3},
  {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, { 10,   4}, {  1,  19}, {  1,  23},  /* Row 82 */
  {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31},
  {  1,   8}, {  1,   4}, {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  5,  18},
  {  1,  50}, {  5,  31}, {  1,  33}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  7,  44},
  {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31}, {  1,   8}, {  1,  21}, {  4,  18},
  {  1,  34}, { 10,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  6,  31}, {  1,  56}, { 32,   4},
  {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4},
  {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, {  9,   4}, {  1,  19},  /* Row 83 */
  {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92},
  {  6,  31}, {  1,   8}, {  1,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  6,  44},
  {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  32}, {  1, 105}, {  1,  23},
  {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31},
  {  1,  80}, {  1,  22}, {  4,  18}, {  1,  52}, { 10,  44}, {  1,  52}, {  4,  18}, {  1,  40},
  {  1,  41}, {  5,  31}, {  1,  33}, {  1,   7}, { 30,   4}, {  1,   7}, {  1,   9}, {  1,  12},
  {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5},
  {  1,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, {  8,   4},  /* Row 84 */
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29},
  {  1,  92}, {  6,  31}, {  1,   8}, {  2,   4}, {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44},
  {  1,  34}, {  5,  18}, {  1,  50}, {  5,  31}, {  1, 106}, {  1,  23}, {  2,  18}, {  1,  34},
  {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31}, {  1,  72}, {  4,  18},
  {  1,  34}, { 12,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  32}, {  1,  56},
  { 29,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5},
  {  1,   0},
  {  2,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, {  7,   4},  /* Row 85 */
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29},
  {  1,  92}, {  6,  31}, {  1,   8}, {  2,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52},
  {  6,  44}, {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1, 107}, {  1,  29},
  {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  5,  31},
  {  1,  41}, {  1,  67}, {  4,  18}, {  1,  52}, { 12,  44}, {  1,  52}, {  4,  18}, {  1,  40},
  {  1,  41}, {  5,  31}, {  1,  33}, {  1,   7}, { 27,   4}, {  1,   7}, {  1,   9}, {  1,  12},
  {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, {  2,   0},
  {  3,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, {  6,   4},  /* Row 86 */
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29},
  {  1,  92}, {  6,  31}, {  1,   8}, {  3,   4}, {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44},
  {  1,  34}, {  5,  18}, {  1,  50}, {  4,  31}, {  1,  92}, {  1,  29}, {  2,  18}, {  1,  34},
  {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  5,  31}, {  1,  50}, {  4,  18},
  {  1,  34}, { 14,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  32}, {  1,  56},
  { 26,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5},
  {  3,   0},
  {  4,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, {  5,   4},  /* Row 87 */
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29},
  {  1,  92}, {  6,  31}, {  1,   8}, {  3,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52},
  {  6,  44}, {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41}, {  3,  31}, {  1,  92}, {  1,  29},
  {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  4,  31},
  {  1,  41}, {  1,  40}, {  4,  18}, {  1,  52}, { 14,  44}, {  1,  52}, {  4,  18}, {  1,  40},
  {  1,  41}, {  5,  31}, {  1,  33}, {  1,   7}, { 24,   4}, {  1,   7}, {  1,   9}, {  1,  12},
  {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, {  4,   0},
  {  5,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, {  4,   4},  /* Row 88 */
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29},
  {  1,  92}, {  6,  31}, {  1,   8}, {  4,   4}, {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44},
  {  1,  34}, {  5,  18}, {  1,  50}, {  3,  31}, {  1,  92}, {  1,  29}, {  2,  18}, {  1,  34},
  {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  4,  31}, {  1,  50}, {  4,  18},
  {  1,  34}, { 16,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  32}, {  1,  56},
  { 23,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5},
  {  5,   0},
  {  6,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, {  3,   4},  /* Row 89 */
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29},
  {  1,  92}, {  6,  31}, {  1,   8}, {  4,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52},
  {  6,  44}, {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41}, {  2,  31}, {  1,  92}, {  1,  29},
  {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  3,  31},
  {  1,  41}, {  1,  40}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  52}, {  1,  34}, {  7,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  5,  31}, {  1,  33}, {  1,   7}, { 21,   4},
  {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, {  6,   0},
  {  7,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, {  2,   4},  /* Row 90 */
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29},
  {  1,  92}, {  6,  31}, {  1,   8}, {  5,   4}, {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44},
  {  1,  34}, {  5,  18}, {  1,  50}, {  2,  31}, {  1,  92}, {  1,  29}, {  2,  18}, {  1,  34},
  {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  3,  31}, {  1,  50}, {  4,  18},
  {  1,  34}, {  8,  44}, {  1,  34}, {  1,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18},
  {  1,  50}, {  5,  31}, {  1,  32}, {  1,  56}, { 20,   4}, {  1,   7}, {  1,   9}, {  1,  12},
  {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, {  7,   0},
  {  8,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, {  1,   4},  /* Row 91 */
  {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29},
  {  1,  92}, {  6,  31}, {  1,   8}, {  5,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52},
  {  6,  44}, {  1,  52}, {  5,  18}, {  1,  40}, {  1,  41}, {  1,  31}, {  1,  92}, {  1,  29},
  {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  2,  31},
  {  1,  41}, {  1,  40}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  52}, {  2,  18}, {  1,  34},
  {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  5,  31}, {  1,  33}, {  1,   7},
  { 18,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5},
  {  8,   0},
  {  9,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, {  1,  19},  /* Row 92 */
  {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92},
  {  6,  31}, {  1,   8}, {  6,   4}, {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34},
  {  5,  18}, {  1,  50}, {  1,  31}, {  1,  92}, {  1,  29}, {  2,  18}, {  1,  34}, {  7,  44},
  {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  2,  31}, {  1,  50}, {  4,  18}, {  1,  34},
  {  8,  44}, {  1,  34}, {  3,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50},
  {  5,  31}, {  1,  32}, {  1,  56}, { 17,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},
  {  1,   6}, {  1,   4}, {  1,   5}, {  9,   0},
  { 10,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,  19}, {  1,  23},  /* Row 93 */
  {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31},
  {  1,   8}, {  6,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  6,  44}, {  1,  52},
  {  5,  18}, {  1,  40}, {  1,  41}, {  1,  92}, {  1,  29}, {  2,  18}, {  1,  34}, {  7,  44},
  {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  1,  31}, {  1,  41}, {  1,  40}, {  4,  18},
  {  1,  52}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18},
  {  1,  40}, {  1,  41}, {  5,  31}, {  1,  33}, {  1,   7}, { 15,   4}, {  1,   7}, {  1,   9},
  {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 10,   0},
  { 11,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1, 108}, {  1,  23}, {  2,  18},  /* Row 94 */
  {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31}, {  1,   8},
  {  7,   4}, {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  5,  18}, {  1,  50},
  {  1,  92}, {  1,  29}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29},
  {  1,  92}, {  1,  31}, {  1,  50}, {  4,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  5,  18},
  {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  32}, {  1,  56},
  { 14,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5},
  { 11,   0},
  { 12,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1, 109}, {  1,  29}, {  2,  18}, {  1,  34},  /* Row 95 */
  {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31}, {  1,   8}, {  7,   4},
  {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  6,  44}, {  1,  52}, {  5,  18}, {  1,  40},
  {  1,  90}, {  1,  29}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29},
  {  1,  92}, {  1,  41}, {  1,  40}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  52}, {  6,  18},
  {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  5,  31}, {  1,  33},
  {  1,   7}, { 12,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4},
  {  1,   5}, { 12,   0},
  { 13,   0}, {  1,   5}, {  3,   4}, {  1,  89}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44},  /* Row 96 */
  {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31}, {  1,   8}, {  8,   4}, {  1,  21},
  {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  5,  18}, {  1,  87}, {  1,  29}, {  2,  18},
  {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  1,  50}, {  4,  18},
  {  1,  34}, {  8,  44}, {  1,  34}, {  7,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18},
  {  1,  50}, {  5,  31}, {  1,  32}, {  1,  56}, { 11,   4}, {  1,   7}, {  1,   9}, {  1,  12},
  {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 13,   0},
  { 14,   0}, {  1,   5}, {  2,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44},  /* Row 97 */
  {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31}, {  1,   8}, {  8,   4}, {  1,  24},
  {  1,  22}, {  4,  18}, {  1,  52}, {  6,  44}, {  1,  52}, {  5,  18}, {  1,  85}, {  3,  18},
  {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  90}, {  1,  40}, {  4,  18},
  {  1,  52}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  85}, {  3,  18}, {  1,  34}, {  7,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  5,  31}, {  1,  33}, {  1,   7}, {  9,   4},
  {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 14,   0},
  { 15,   0}, {  1,   5}, {  1,   4}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44},  /* Row 98 */
  {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31}, {  1,   8}, {  9,   4}, {  1,  21},
  {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  8,  18}, {  1,  34}, {  7,  44}, {  1,  34},
  {  2,  18}, {  1,  29}, {  1,  87}, {  4,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  4,  18},
  {  1,  87}, {  1,  40}, {  3,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50},
  {  5,  31}, {  1,  32}, {  1,  56}, {  8,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},
  {  1,   6}, {  1,   4}, {  1,   5}, { 15,   0},
  { 16,   0}, {  1,   5}, {  1,  19}, {  1,  23}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34},  /* Row 99 */
  {  2,  18}, {  1,  29}, {  1,  92}, {  6,  31}, {  1,   8}, {  9,   4}, {  1,  24}, {  1,  22},
  {  4,  18}, {  1,  52}, {  6,  44}, {  1,  52}, {  8,  18}, {  1,  34}, {  7,  44}, {  1,  34},
  {  3,  18}, {  1,  85}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40},
  {  1,  41}, {  1,  50}, {  3,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40},
  {  1,  41}, {  5,  31}, {  1,  33}, {  1,   7}, {  6,   4}, {  1,   7}, {  1,   9}, {  1,  12},
  {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 16,   0},
  { 17,   0}, {  1, 110}, {  1, 111}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18},  /* Row 100 */
  {  1,  29}, {  1,  92}, {  6,  31}, {  1,   8}, { 10,   4}, {  1,  21}, {  4,  18}, {  1,  34},
  {  7,  44}, {  1,  34}, {  7,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  7,  18}, {  1,  34},
  {  8,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  1,  31}, {  1,  41}, {  1,  40}, {  3,  18},
  {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  32}, {  1,  56},
  {  5,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5},
  { 17,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92},  /* Row 101 */
  {  6,  31}, {  1,   8}, { 10,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  6,  44},
  {  1,  52}, {  7,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  7,  18}, {  1,  52}, {  7,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  2,  31}, {  1,  50}, {  3,  18}, {  1,  34},
  {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  5,  31}, {  1,  33}, {  1,   7},
  {  3,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5},
  { 18,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92},  /* Row 102 */
  {  6,  31}, {  1,   8}, { 11,   4}, {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34},
  {  6,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  6,  18}, {  1,  34}, {  8,  44}, {  1,  34},
  {  4,  18}, {  1,  50}, {  3,  31}, {  1,  41}, {  1,  40}, {  3,  18}, {  1,  52}, {  7,  44},
  {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  32}, {  1,  56}, {  2,   4}, {  1,   7},
  {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 19,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92},  /* Row 103 */
  {  6,  31}, {  1,   8}, { 11,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  6,  44},
  {  1,  52}, {  6,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  6,  18}, {  1,  52}, {  7,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  4,  31}, {  1,  50}, {  3,  18}, {  1,  34},
  {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  5,  31}, {  1,  33}, {  2,   7},
  {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 20,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92},  /* Row 104 */
  {  6,  31}, {  1,   8}, { 12,   4}, {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34},
  {  5,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  5,  18}, {  1,  34}, {  8,  44}, {  1,  34},
  {  4,  18}, {  1,  50}, {  5,  31}, {  1,  41}, {  1,  40}, {  3,  18}, {  1,  52}, {  7,  44},
  {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  32}, {  1,  56}, {  1,   9}, {  1,  12},
  {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 21,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92},  /* Row 105 */
  {  6,  31}, {  1,   8}, { 12,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  6,  44},
  {  1,  52}, {  5,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  5,  18}, {  1,  52}, {  7,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  6,  31}, {  1,  82}, {  3,  18}, {  1,  34},
  {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  5,  31}, {  1,  81}, {  1,  12},
  {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 22,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92},  /* Row 106 */
  {  6,  31}, {  1,   8}, { 13,   4}, {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34},
  {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  34}, {  8,  44}, {  1,  34},
  {  4,  18}, {  1,  50}, {  6,  31}, {  1,  32}, {  1,  80}, {  1,  22}, {  3,  18}, {  1,  52},
  {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  5,  31}, {  1,  77}, {  1, 112}, {  1,   6},
  {  1,   4}, {  1,   5}, { 23,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92},  /* Row 107 */
  {  6,  31}, {  1,   8}, { 13,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  6,  44},
  {  1,  52}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  52}, {  7,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  6,  31}, {  1,  33}, {  1,   4}, {  1,  21},
  {  3,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  5,  31},
  {  1, 113}, {  1,   8}, {  1,   5}, { 24,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92},  /* Row 108 */
  {  6,  31}, {  1,   8}, { 14,   4}, {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34},
  {  3,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34},
  {  4,  18}, {  1,  50}, {  6,  31}, {  1,  32}, {  1,  56}, {  1,   4}, {  1,  24}, {  1,  22},
  {  3,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  4,  31}, {  1,  59},
  {  1,  58}, {  1, 114}, { 25,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92},  /* Row 109 */
  {  6,  31}, {  1,   8}, { 14,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  6,  44},
  {  1,  52}, {  3,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  3,  18}, {  1,  52}, {  7,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  6,  31}, {  1,  33}, {  1,   7}, {  2,   4},
  {  1,  21}, {  3,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41},
  {  2,  31}, {  1,  59}, {  1,  58}, {  1,  48}, {  1,  38}, { 25,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92},  /* Row 110 */
  {  6,  31}, {  1,   8}, { 15,   4}, {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34},
  {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  34}, {  8,  44}, {  1,  34},
  {  4,  18}, {  1,  50}, {  6,  31}, {  1,  32}, {  1,  56}, {  3,   4}, {  1,  24}, {  1,  22},
  {  3,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  1,  31}, {  1,  59},
  {  1,  58}, {  1,  48}, {  1,  38}, {  1,  48}, {  1,  39}, { 24,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92},  /* Row 111 */
  {  6,  31}, {  1,   8}, { 15,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  6,  44},
  {  1,  52}, {  2,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  2,  18}, {  1,  52}, {  7,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  6,  31}, {  1,  33}, {  1,   7}, {  4,   4},
  {  1,  21}, {  3,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40}, {  1,  75},
  {  1,  58}, {  1,  48}, {  4,  38}, { 24,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92},  /* Row 112 */
  {  6,  31}, {  1,   8}, { 16,   4}, {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34},
  {  1,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  1,  18}, {  1,  34}, {  8,  44}, {  1,  34},
  {  4,  18}, {  1,  50}, {  6,  31}, {  1,  32}, {  1,  56}, {  5,   4}, {  1,  24}, {  1,  22},
  {  3,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  74}, {  1,  48}, {  4,  38},
  {  1,  48}, {  1,  39}, { 23,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92},  /* Row 113 */
  {  6,  31}, {  1,   8}, { 16,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  6,  44},
  {  1,  52}, {  1,  18}, {  1,  34}, {  7,  44}, {  1,  34}, {  1,  18}, {  1,  52}, {  7,  44},
  {  1,  52}, {  4,  18}, {  1,  40}, {  1,  41}, {  6,  31}, {  1,  33}, {  1,   7}, {  6,   4},
  {  1,  21}, {  3,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  47}, {  1,  45},
  {  6,  38}, { 23,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92},  /* Row 114 */
  {  6,  31}, {  1,   8}, { 17,   4}, {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44}, {  2,  34},
  {  7,  44}, {  2,  34}, {  8,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  6,  31}, {  1,  32},
  {  1,  56}, {  7,   4}, {  1,  24}, {  1,  22}, {  3,  18}, {  1,  52}, {  7,  44}, {  1,  34},
  {  4,  18}, {  1,  53}, {  5,  38}, {  1,  48}, {  1,  39}, { 22,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1,  92},  /* Row 115 */
  {  6,  31}, {  1,   8}, { 17,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  6,  44},
  {  1,  52}, {  1,  34}, {  7,  44}, {  1,  34}, {  1,  52}, {  7,  44}, {  1,  52}, {  4,  18},
  {  1,  40}, {  1,  41}, {  6,  31}, {  1,  33}, {  1,   7}, {  8,   4}, {  1,  21}, {  3,  18},
  {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  47}, {  1,  45}, {  6,  38}, { 22,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  29}, {  1, 115},  /* Row 116 */
  {  6,  31}, {  1,   8}, { 18,   4}, {  1,  21}, {  4,  18}, {  1,  34}, {  7,  44}, {  1,  34},
  {  7,  44}, {  1,  34}, {  8,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  6,  31}, {  1,  32},
  {  1,  56}, {  9,   4}, {  1,  24}, {  1,  22}, {  3,  18}, {  1,  52}, {  7,  44}, {  1,  34},
  {  4,  18}, {  1,  53}, {  5,  38}, {  1,  48}, {  1,  39}, { 21,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1, 116},  /* Row 117 */
  {  1,  59}, {  5,  31}, {  1,   8}, { 18,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52},
  {  6,  44}, {  1,  73}, {  7,  44}, {  1,  73}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  40},
  {  1,  41}, {  6,  31}, {  1,  33}, {  1,   7}, { 10,   4}, {  1,  21}, {  3,  18}, {  1,  34},
  {  7,  44}, {  1,  52}, {  4,  18}, {  1,  47}, {  1,  45}, {  6,  38}, { 21,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 118 */
  {  1,  58}, {  1,  59}, {  4,  31}, {  1,  16}, {  1,   7}, { 18,   4}, {  1,  21}, {  4,  18},
  {  1,  34}, { 22,  44}, {  1,  34}, {  4,  18}, {  1,  50}, {  6,  31}, {  1,  32}, {  1,  56},
  { 11,   4}, {  1,  24}, {  1,  22}, {  3,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18},
  {  1,  53}, {  5,  38}, {  1,  48}, {  1,  39}, { 20,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 119 */
  {  1,  48}, {  1,  58}, {  1,  59}, {  3,  31}, {  1,  42}, {  1,  16}, {  1,   7}, { 17,   4},
  {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, { 20,  44}, {  1,  52}, {  4,  18}, {  1,  40},
  {  1,  41}, {  6,  31}, {  1,  33}, {  1,   7}, { 12,   4}, {  1,  21}, {  3,  18}, {  1,  34},
  {  7,  44}, {  1,  52}, {  4,  18}, {  1,  47}, {  1,  45}, {  6,  38}, { 20,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 120 */
  {  1,  38}, {  1,  48}, {  1,  58}, {  1,  59}, {  2,  31}, {  1,  56}, {  1,  15}, {  1,  16},
  {  1,   7}, { 17,   4}, {  1,  21}, {  4,  18}, {  1,  34}, { 20,  44}, {  1,  34}, {  4,  18},
  {  1,  50}, {  6,  31}, {  1,  32}, {  1,  56}, { 12,   4}, {  1,   7}, {  1, 117}, {  1,  73},
  {  3,  18}, {  1,  52}, {  7,  44}, {  1,  34}, {  4,  18}, {  1,  53}, {  5,  38}, {  1,  48},
  {  1,  39}, { 19,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 121 */
  {  2,  38}, {  1,  48}, {  1,  58}, {  1,  59}, {  1,  31}, {  2,   8}, {  1,  15}, {  1,  16},
  {  1,   7}, { 16,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, { 18,  44}, {  1,  52},
  {  4,  18}, {  1,  40}, {  1,  41}, {  6,  31}, {  1,  33}, {  1,   7}, { 11,   4}, {  1,   7},
  {  1,   9}, {  1, 118}, {  1, 119}, {  3,  18}, {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18},
  {  1,  47}, {  1,  45}, {  6,  38}, { 19,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 122 */
  {  3,  38}, {  1,  48}, {  1,  58}, {  1, 113}, {  1,   8}, {  1,   4}, {  1,   8}, {  1,  15},
  {  1,  16}, {  1,   7}, { 16,   4}, {  1,  21}, {  4,  18}, {  1,  34}, { 18,  44}, {  1,  34},
  {  4,  18}, {  1,  50}, {  6,  31}, {  1,  32}, {  1,  56}, { 11,   4}, {  1,   7}, {  1,   9},
  {  1,  12}, {  1,  13}, {  1, 120}, {  1,  22}, {  3,  18}, {  1,  52}, {  7,  44}, {  1,  34},
  {  4,  18}, {  1,  53}, {  5,  38}, {  1,  48}, {  1,  39}, { 18,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 123 */
  {  4,  38}, {  1,  48}, {  1, 121}, {  1, 122}, {  2,   4}, {  1,   8}, {  1,  15}, {  1,  16},
  {  1,   7}, { 15,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, { 16,  44}, {  1,  52},
  {  4,  18}, {  1,  40}, {  1,  41}, {  6,  31}, {  1,  33}, {  1,   7}, { 10,   4}, {  1,   7},
  {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1, 123}, {  3,  18}, {  1,  34},
  {  7,  44}, {  1,  52}, {  4,  18}, {  1,  47}, {  1,  45}, {  6,  38}, { 18,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 124 */
  {  5,  38}, {  1,  48}, {  1, 124}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7},
  { 15,   4}, {  1,  21}, {  4,  18}, {  1,  34}, { 16,  44}, {  1,  34}, {  4,  18}, {  1,  50},
  {  6,  31}, {  1,  32}, {  1,  56}, { 10,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},
  {  1,   6}, {  1,   4}, {  1,   5}, {  1, 125}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  34},
  {  4,  18}, {  1,  53}, {  5,  38}, {  1,  48}, {  1,  39}, { 17,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 125 */
  {  6,  38}, {  1,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7},
  { 14,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, { 14,  44}, {  1,  52}, {  4,  18},
  {  1,  40}, {  1,  41}, {  6,  31}, {  1,  33}, {  1,   7}, {  9,   4}, {  1,   7}, {  1,   9},
  {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, {  2,   0}, {  1,  17}, {  3,  18},
  {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  47}, {  1,  45}, {  6,  38}, { 17,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 126 */
  {  6,  38}, {  2,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7},
  { 14,   4}, {  1,  21}, {  4,  18}, {  1,  34}, { 14,  44}, {  1,  34}, {  4,  18}, {  1,  50},
  {  6,  31}, {  1,  32}, {  1,  56}, {  9,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},
  {  1,   6}, {  1,   4}, {  1,   5}, {  4,   0}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  34},
  {  4,  18}, {  1,  53}, {  5,  38}, {  1,  48}, {  1,  39}, { 16,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 127 */
  {  6,  38}, {  3,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7},
  { 13,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, { 12,  44}, {  1,  52}, {  4,  18},
  {  1,  40}, {  1,  41}, {  6,  31}, {  1,  33}, {  1,   7}, {  8,   4}, {  1,   7}, {  1,   9},
  {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, {  5,   0}, {  1,  17}, {  3,  18},
  {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  47}, {  1,  45}, {  6,  38}, { 16,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 128 */
  {  6,  38}, {  4,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7},
  { 13,   4}, {  1,  21}, {  4,  18}, {  1,  34}, { 12,  44}, {  1,  34}, {  4,  18}, {  1,  50},
  {  6,  31}, {  1,  32}, {  1,  56}, {  8,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},
  {  1,   6}, {  1,   4}, {  1,   5}, {  7,   0}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  34},
  {  4,  18}, {  1,  53}, {  5,  38}, {  1,  48}, {  1,  39}, { 15,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 129 */
  {  6,  38}, {  5,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7},
  { 12,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, { 10,  44}, {  1,  52}, {  4,  18},
  {  1,  40}, {  1,  41}, {  6,  31}, {  1,  33}, {  1,   7}, {  7,   4}, {  1,   7}, {  1,   9},
  {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, {  8,   0}, {  1,  17}, {  3,  18},
  {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  47}, {  1,  45}, {  6,  38}, { 15,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 130 */
  {  6,  38}, {  6,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7},
  { 12,   4}, {  1,  21}, {  4,  18}, {  1,  34}, { 10,  44}, {  1,  34}, {  4,  18}, {  1,  50},
  {  6,  31}, {  1,  32}, {  1,  56}, {  7,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},
  {  1,   6}, {  1,   4}, {  1,   5}, { 10,   0}, {  4,  18}, {  1,  52}, {  7,  44}, {  1,  34},
  {  4,  18}, {  1,  51}, {  5,  38}, {  1,  48}, {  1, 126}, { 14,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 131 */
  {  6,  38}, {  7,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7},
  { 11,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  8,  44}, {  1,  52}, {  4,  18},
  {  1,  40}, {  1,  41}, {  6,  31}, {  1,  33}, {  1,   7}, {  6,   4}, {  1,   7}, {  1,   9},
  {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 11,   0}, {  1,  17}, {  3,  18},
  {  1,  34}, {  7,  44}, {  1,  52}, {  4,  18}, {  1,  53}, {  6,  38}, {  1, 127}, { 14,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 132 */
  {  6,  38}, {  8,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7},
  { 11,   4}, {  1,  21}, {  4,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  4,  18}, {  1,  50},
  {  6,  31}, {  1,  32}, {  1,  56}, {  6,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13},
  {  1,   6}, {  1,   4}, {  1,   5}, { 13,   0}, {  4,  18}, {  1,  52}, {  6,  44}, {  1,  52},
  {  4,  18}, {  1,  53}, {  7,  38}, { 14,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 133 */
  {  6,  38}, {  9,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7},
  { 10,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  52}, {  6,  44}, {  1,  52}, {  4,  18},
  {  1,  40}, {  1,  41}, {  6,  31}, {  1,  33}, {  1,   7}, {  5,   4}, {  1,   7}, {  1,   9},
  {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 14,   0}, {  1,  17}, {  3,  18},
  {  1,  34}, {  6,  44}, {  1,  52}, {  4,  18}, {  1, 128}, {  6,  38}, {  1,  48}, {  1, 126},
  { 13,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 134 */
  {  6,  38}, { 10,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7},
  { 10,   4}, {  1,  21}, {  4,  18}, {  1,  34}, {  1,  55}, {  4,  44}, {  1,  55}, {  1,  34},
  {  4,  18}, {  1,  50}, {  6,  31}, {  1,  32}, {  1,  56}, {  5,   4}, {  1,   7}, {  1,   9},
  {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 16,   0}, {  4,  18}, {  1,  52},
  {  5,  44}, {  1,  34}, {  3,  18}, {  1,  47}, {  1,  45}, {  7,  38}, {  1,  39}, { 13,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 135 */
  {  6,  38}, { 11,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7},
  {  9,   4}, {  1,  24}, {  1,  22}, {  4,  18}, {  1,  34}, {  1,  52}, {  2,  44}, {  1,  52},
  {  1,  34}, {  4,  18}, {  1,  40}, {  1,  41}, {  6,  31}, {  1,  33}, {  1,   7}, {  4,   4},
  {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 17,   0},
  {  1,  17}, {  3,  18}, {  1,  34}, {  1,  52}, {  2,  44}, {  1,  52}, {  1,  34}, {  4,  18},
  {  1,  53}, {  8,  38}, {  1,  39}, { 13,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 136 */
  {  6,  38}, { 12,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7},
  {  9,   4}, {  1,  21}, {  6,  18}, {  2,  34}, {  6,  18}, {  1,  50}, {  6,  31}, {  1,  32},
  {  1,  56}, {  4,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4},
  {  1,   5}, { 19,   0}, {  5,  18}, {  2,  34}, {  5,  18}, {  1,  47}, {  1,  45}, {  8,  38},
  {  1,   1}, { 13,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 137 */
  {  6,  38}, { 13,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7},
  {  8,   4}, {  1,  24}, {  1,  22}, { 12,  18}, {  1,  40}, {  1,  41}, {  6,  31}, {  1,  33},
  {  1,   7}, {  3,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4},
  {  1,   5}, { 20,   0}, {  1,  17}, { 10,  18}, {  1,  35}, {  1,  61}, {  9,  38}, { 14,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  34}, {  2,  18}, {  1,  35}, {  1,  45},  /* Row 138 */
  {  6,  38}, { 14,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7},
  {  8,   4}, {  1,  21}, { 12,  18}, {  1,  50}, {  6,  31}, {  1,  32}, {  1,  56}, {  3,   4},
  {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 22,   0},
  {  9,  18}, {  1,  35}, {  1,  61}, {  8,  38}, {  1,  48}, {  1,  39}, { 14,   0},
  { 18,   0}, {  3,  18}, {  1,  34}, {  8,  44}, {  1,  18}, {  2,  35}, {  1, 129}, {  1,  45},  /* Row 139 */
  {  6,  38}, { 15,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7},
  {  7,   4}, {  1,  19}, {  1,  26}, { 10,  18}, {  1, 130}, {  1, 107}, {  6,  31}, {  1,  33},
  {  1,   7}, {  2,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4},
  {  1,   5}, { 23,   0}, {  1, 131}, {  1, 132}, {  5,  18}, {  1,  35}, {  1, 133}, {  1, 128},
  { 10,  38}, { 15,   0},
  { 25,   0}, {  9,  45}, {  7,  38}, { 16,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15},  /* Row 140 */
  {  1,  16}, {  1,   7}, {  7,   4}, {  1,  19}, {  1,  21}, {  1,  22}, {  6,  18}, {  1,  40},
  {  1,  50}, {  1, 107}, {  6,  31}, {  1,  32}, {  1,  56}, {  2,   4}, {  1,   7}, {  1,   9},
  {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 27,   0}, {  1, 134}, {  1,  17},
  {  1, 135}, {  1,  51}, {  1,  45}, {  1, 136}, {  9,  38}, {  1,  48}, {  1,  39}, { 15,   0},
  { 25,   0}, { 16,  38}, { 17,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16},  /* Row 141 */
  {  1,   7}, {  8,   4}, {  1,  24}, {  1,  21}, {  1,  22}, {  2,  29}, {  1,  67}, {  1,  50},
  {  1,  41}, {  8,  31}, {  1,  16}, {  1,   7}, {  1,   4}, {  1,   7}, {  1,   9}, {  1,  12},
  {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 30,   0}, {  1,   1}, { 11,  38}, {  1,  48},
  {  1,  38}, { 16,   0},
  { 25,   0}, { 16,  38}, { 18,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16},  /* Row 142 */
  {  1,   7}, {  9,   4}, {  1,  89}, {  1, 137}, {  1, 138}, {  1,  92}, {  8,  31}, {  1,  42},
  {  1,  16}, {  1,   7}, {  1,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6},
  {  1,   4}, {  1,   5}, { 32,   0}, {  1,  39}, {  1,  48}, {  8,  38}, {  1,  48}, {  1,  38},
  { 17,   0},
  { 25,   0}, { 16,  38}, { 19,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16},  /* Row 143 */
  {  1,   7}, {  9,   4}, {  1,   7}, {  1,  56}, {  1,  33}, {  1,  32}, {  5,  31}, {  1,  42},
  {  1,  16}, {  1,   8}, {  2,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6},
  {  1,   4}, {  1,   5}, { 34,   0}, {  8,  38}, {  1,  48}, {  1,  38}, { 18,   0},
  { 25,   0}, { 16,  38}, { 20,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16},  /* Row 144 */
  {  1,   7}, { 10,   4}, {  1,   7}, {  7,   8}, {  3,   4}, {  1,   7}, {  1,   9}, {  1,  12},
  {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 35,   0}, {  1,  46}, {  7,  38}, {  1,  39},
  { 19,   0},
  { 25,   0}, { 16,  38}, { 21,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16},  /* Row 145 */
  {  1,   7}, { 19,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4},
  {  1,   5}, { 64,   0},
  { 25,   0}, { 16,  38}, { 22,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16},  /* Row 146 */
  {  1,   7}, { 17,   4}, {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4},
  {  1,   5}, { 65,   0},
  { 64,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, { 15,   4},  /* Row 147 */
  {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 66,   0},
  { 65,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, { 13,   4},  /* Row 148 */
  {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 67,   0},
  { 66,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, { 11,   4},  /* Row 149 */
  {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 68,   0},
  { 67,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, {  9,   4},  /* Row 150 */
  {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 69,   0},
  { 68,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, {  7,   4},  /* Row 151 */
  {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 70,   0},
  { 69,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, {  5,   4},  /* Row 152 */
  {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 71,   0},
  { 70,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, {  3,   4},  /* Row 153 */
  {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 72,   0},
  { 71,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, {  1,   4},  /* Row 154 */
  {  1,   7}, {  1,   9}, {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 73,   0},
  { 72,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1,  16}, {  1,   7}, {  1,   9},  /* Row 155 */
  {  1,  12}, {  1,  13}, {  1,   6}, {  1,   4}, {  1,   5}, { 74,   0},
  { 73,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1,  15}, {  1, 103}, {  1,  12}, {  1,  13},  /* Row 156 */
  {  1,   6}, {  1,   4}, {  1,   5}, { 75,   0},
  { 74,   0}, {  1,   5}, {  3,   4}, {  1,   8}, {  1, 139}, {  1,  13}, {  1,   6}, {  1,   4},  /* Row 157 */
  {  1,   5}, { 76,   0},
  { 75,   0}, {  1,   5}, {  2,   4}, {  1,   3}, {  1, 140}, {  1,   6}, {  1,   4}, {  1,   5},  /* Row 158 */
  { 77,   0},
  { 76,   0}, {  1,   5}, {  4,   4}, {  1,   5}, { 78,   0}                                       /* Row 159 */
};

#elif CONFIG_EXAMPLES_NXIMAGE_BPP == 8
#  ifdef CONFIG_EXAMPLES_NXIMAGE_GREYSCALE

  { 76,   0}, {  1,   1}, {  1,   2}, {  1,   3}, {  4,   4}, {  1,   5}, { 76,   0},              /* Row 0 */
  { 75,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  5,   4}, {  1,   5}, { 75,   0},  /* Row 1 */
  { 74,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   9}, {  1,  10},  /* Row 2 */
  {  4,   4}, {  1,   5}, { 74,   0},
  { 73,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},  /* Row 3 */
  {  1,  12}, {  1,  13}, {  4,   4}, {  1,   5}, { 73,   0},
  { 72,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},  /* Row 4 */
  {  1,   7}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 72,   0},
  { 71,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},  /* Row 5 */
  {  1,   7}, {  2,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 71,   0},
  { 70,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},  /* Row 6 */
  {  1,   7}, {  4,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 70,   0},
  { 69,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},  /* Row 7 */
  {  1,   7}, {  6,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 69,   0},
  { 68,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},  /* Row 8 */
  {  1,   7}, {  8,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 68,   0},
  { 67,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},  /* Row 9 */
  {  1,   7}, { 10,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 67,   0},
  { 66,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},  /* Row 10 */
  {  1,   7}, { 12,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 66,   0},
  { 65,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},  /* Row 11 */
  {  1,   7}, { 14,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 65,   0},
  { 64,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},  /* Row 12 */
  {  1,   7}, { 16,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 64,   0},
  { 63,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},  /* Row 13 */
  {  1,   7}, { 18,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 63,   0},
  { 62,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},  /* Row 14 */
  {  1,   7}, { 20,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 62,   0},
  { 61,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},  /* Row 15 */
  {  1,   7}, { 22,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 61,   0},
  { 60,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},  /* Row 16 */
  {  1,   7}, { 24,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 60,   0},
  { 59,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},  /* Row 17 */
  {  1,   7}, { 26,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 59,   0},
  { 58,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},  /* Row 18 */
  {  1,   7}, { 28,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 26,   0},
  {  1,  14}, {  5,  15}, {  1,  14}, { 25,   0},
  { 57,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},  /* Row 19 */
  {  1,   7}, { 10,   4}, {  5,  16}, { 15,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4},
  {  1,   5}, { 23,   0}, {  1,  17}, {  9,  15}, {  1,  17}, { 23,   0},
  { 56,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},  /* Row 20 */
  {  1,   7}, {  9,   4}, {  1,  16}, {  1,  18}, {  1,  19}, {  3,  20}, {  1,  19}, {  1,  18},
  {  1,  21}, { 14,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 22,   0},
  { 11,  15}, {  1,  17}, { 22,   0},
  { 23,   0}, {  1,  14}, {  5,  15}, {  1,  22}, { 25,   0}, {  1,   1}, {  1,   6}, {  1,   7},  /* Row 21 */
  {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  9,   4}, {  1,  16}, {  1,  23},
  {  7,  15}, {  1,  19}, {  1,  24}, { 14,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4},
  {  1,   5}, { 20,   0}, {  1,  14}, { 12,  15}, {  1,  17}, { 21,   0},
  { 21,   0}, {  1,  17}, {  9,  15}, {  1,  17}, { 22,   0}, {  1,   1}, {  1,   6}, {  1,   7},  /* Row 22 */
  {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  9,   4}, {  1,  16}, {  1,  23},
  {  9,  15}, {  1,  20}, {  1,  24}, {  4,   4}, {  1,   3}, {  9,   4}, {  1,   3}, {  1,   8},
  {  1,  13}, {  4,   4}, {  1,   5}, { 19,   0}, { 14,  15}, { 21,   0},
  { 21,   0}, { 11,  15}, {  1,  17}, { 20,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3},  /* Row 23 */
  {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, { 10,   4}, {  1,  18}, { 11,  15}, {  1,  25},
  {  1,  26}, {  2,  27}, {  1,  28}, {  1,  29}, { 10,   4}, {  1,   3}, {  1,   8}, {  1,  13},
  {  4,   4}, {  1,   5}, { 17,   0}, {  1,  14}, {  5,  15}, {  1,  30}, {  2,  31}, {  1,  30},
  {  5,  15}, {  1,  14}, { 20,   0},
  { 20,   0}, {  1,  22}, { 12,  15}, {  1,  32}, {  4,  33}, {  1,   7}, { 13,   0}, {  1,   1},  /* Row 24 */
  {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, { 10,   4},
  {  1,  21}, {  1,  19}, {  4,  15}, {  3,  30}, {  5,  15}, {  1,  34}, {  1,  35}, {  3,  27},
  {  1,  36}, { 10,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 16,   0},
  {  5,  15}, {  1,  37}, {  4,  38}, {  1,  37}, {  4,  15}, {  1,  39}, {  1,  40}, {  1,  41},
  { 18,   0},
  { 19,   0}, {  1,  14}, { 13,  15}, {  1,  31}, {  1,  40}, {  5,  33}, { 11,   0}, {  1,   1},  /* Row 25 */
  {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, { 11,   4},
  {  1,  42}, {  4,  15}, {  1,  31}, {  3,  38}, {  1,  37}, {  5,  15}, {  1,  43}, {  4,  27},
  {  1,  36}, { 10,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 14,   0},
  {  1,  14}, {  4,  15}, {  1,  30}, {  6,  38}, {  1,  30}, {  4,  15}, {  1,  22}, {  1,  33},
  {  1,  41}, { 17,   0},
  { 19,   0}, {  5,  15}, {  1,  30}, {  1,  44}, {  1,  34}, {  1,  31}, {  6,  15}, {  1,  45},  /* Row 26 */
  {  5,  33}, {  1,   7}, {  9,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8},
  {  1,   7}, {  1,  11}, {  1,   7}, { 11,   4}, {  1,  46}, {  1,  47}, {  4,  15}, {  1,  48},
  {  4,  38}, {  1,  37}, {  4,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  36}, { 10,   4},
  {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 13,   0}, {  5,  15}, {  1,  34},
  {  6,  38}, {  1,  44}, {  4,  15}, {  1,  45}, {  2,  33}, {  1,  41}, { 16,   0},
  { 18,   0}, {  1,  14}, {  4,  15}, {  1,  37}, {  4,  38}, {  1,  31}, {  5,  15}, {  1,  31},  /* Row 27 */
  {  1,  40}, {  5,  33}, {  8,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8},
  {  1,   7}, {  1,  11}, {  1,   7}, { 12,   4}, {  1,  16}, {  1,  20}, {  3,  15}, {  1,  31},
  {  6,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, {  9,   4},
  {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 11,   0}, {  1,  14}, {  4,  15},
  {  1,  30}, {  7,  38}, {  1,  44}, {  4,  15}, {  1,  45}, {  3,  33}, { 16,   0},
  { 18,   0}, {  4,  15}, {  1,  30}, {  5,  38}, {  1,  48}, {  1,  30}, {  5,  15}, {  1,  45},  /* Row 28 */
  {  5,  33}, {  1,   7}, {  6,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8},
  {  1,   7}, {  1,  11}, {  1,   7}, { 13,   4}, {  1,  16}, {  1,  20}, {  3,  15}, {  1,  19},
  {  6,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49},
  { 10,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, { 10,   0}, {  5,  15},
  {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  22}, {  3,  33}, {  1,   7}, { 15,   0},
  { 18,   0}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34}, {  5,  15}, {  1,  31}, {  1,  40},  /* Row 29 */
  {  5,  33}, {  5,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7},
  {  1,  11}, {  1,   7}, { 14,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38},
  {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, { 10,   4}, {  1,   3},
  {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, {  8,   0}, {  1,  14}, {  4,  15}, {  1,  30},
  {  7,  38}, {  1,  34}, {  4,  15}, {  1,  31}, {  1,  40}, {  4,  33}, { 15,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  5,  15}, {  1,  45}, {  5,  33},  /* Row 30 */
  {  1,   7}, {  3,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7},
  {  1,  11}, {  1,   7}, { 15,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38},
  {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, { 11,   4},
  {  1,   3}, {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, {  7,   0}, {  5,  15}, {  1,  34},
  {  7,  38}, {  1,  30}, {  4,  15}, {  1,  45}, {  5,  33}, {  1,  50}, { 14,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  34}, {  5,  15}, {  1,  31}, {  1,  40},  /* Row 31 */
  {  5,  33}, {  2,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7},
  {  1,  11}, {  1,   7}, { 16,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  9,  38},
  {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, { 11,   4}, {  1,   3},
  {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, {  5,   0}, {  1,  14}, {  4,  15}, {  1,  30},
  {  7,  38}, {  1,  34}, {  4,  15}, {  1,  31}, {  1,  40}, {  5,  33}, {  1,   7}, { 14,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  9,  38}, {  1,  30}, {  5,  15}, {  1,  45}, {  5,  33},  /* Row 32 */
  {  1,   7}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},
  {  1,   7}, { 17,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  9,  38}, {  1,  34},
  {  4,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, { 12,   4}, {  1,   3},
  {  1,   8}, {  1,  13}, {  4,   4}, {  1,   5}, {  4,   0}, {  5,  15}, {  1,  34}, {  7,  38},
  {  1,  30}, {  4,  15}, {  1,  45}, {  6,  33}, {  1,  50}, { 14,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  9,  38}, {  1,  34}, {  5,  15}, {  1,  31}, {  1,  40},  /* Row 33 */
  {  4,  33}, {  1,  51}, {  1,  52}, {  1,   7}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},
  {  1,   7}, { 18,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, { 10,  38}, {  1,  30},
  {  4,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, { 12,   4}, {  1,   3}, {  1,   8},
  {  1,  13}, {  4,   4}, {  1,   5}, {  2,   0}, {  1,  14}, {  4,  15}, {  1,  30}, {  7,  38},
  {  1,  34}, {  4,  15}, {  1,  31}, {  1,  40}, {  6,  33}, { 15,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, { 10,  38}, {  1,  30}, {  5,  15}, {  1,  45}, {  3,  33},  /* Row 34 */
  {  1,  53}, {  1,  54}, {  1,  21}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7},
  { 19,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, { 10,  38}, {  1,  34}, {  4,  15},
  {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, { 13,   4}, {  1,   3}, {  1,   8},
  {  1,  13}, {  4,   4}, {  1,   5}, {  1,   0}, {  5,  15}, {  1,  34}, {  7,  38}, {  1,  30},
  {  3,  15}, {  1,  55}, {  1,  56}, {  6,  33}, {  1,   7}, { 15,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, { 10,  38}, {  1,  34}, {  5,  15}, {  1,  31}, {  1,  40},  /* Row 35 */
  {  1,  33}, {  1,  53}, {  1,  57}, {  1,  27}, {  1,  28}, {  1,  58}, {  1,   7}, {  1,  11},
  {  1,   7}, { 20,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, { 11,  38}, {  1,  30},
  {  4,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, { 13,   4}, {  1,   3}, {  1,   8},
  {  1,  13}, {  4,   4}, {  1,  59}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15},
  {  1,  31}, {  1,  60}, {  6,  33}, { 16,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, { 11,  38}, {  1,  30}, {  5,  15}, {  1,  61}, {  1,  53},  /* Row 36 */
  {  1,  57}, {  3,  27}, {  1,  62}, {  1,  56}, {  1,   7}, { 21,   4}, {  1,  16}, {  1,  20},
  {  2,  15}, {  1,  30}, { 11,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  4,  27},
  {  1,  28}, {  1,  49}, { 14,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  2,   4}, {  1,  21},
  {  1,  19}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  45}, {  6,  33},
  {  1,   7}, { 16,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, { 11,  38}, {  1,  34}, {  5,  15}, {  1,  63}, {  1,  35},  /* Row 37 */
  {  5,  27}, {  1,  64}, { 22,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, { 12,  38},
  {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, { 14,   4}, {  1,   3},
  {  1,   8}, {  1,  13}, {  1,   4}, {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  34},
  {  4,  15}, {  1,  31}, {  1,  40}, {  6,  33}, { 17,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, { 12,  38}, {  1,  30}, {  5,  15}, {  1,  43}, {  5,  27},  /* Row 38 */
  {  1,  65}, {  1,   3}, { 21,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, { 12,  38},
  {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, { 15,   4},
  {  1,   3}, {  1,   8}, {  1,  66}, {  1,  19}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  30},
  {  4,  15}, {  1,  45}, {  6,  33}, {  1,   7}, { 17,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, { 12,  38}, {  1,  34}, {  5,  15}, {  1,  34}, {  1,  35},  /* Row 39 */
  {  4,  27}, {  1,  28}, {  1,  49}, { 21,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30},
  { 13,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, { 16,   4},
  {  1,  67}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  31}, {  1,  40},
  {  6,  33}, { 18,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, { 13,  38}, {  1,  30}, {  5,  15}, {  1,  43}, {  5,  27},  /* Row 40 */
  {  1,  29}, {  1,   3}, { 20,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, { 13,  38},
  {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, { 15,   4},
  {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  45},
  {  6,  33}, {  1,   7}, { 18,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, { 13,  38}, {  1,  34}, {  5,  15}, {  1,  34}, {  1,  35},  /* Row 41 */
  {  4,  27}, {  1,  28}, {  1,  49}, { 20,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30},
  { 14,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, { 14,   4},
  {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  31}, {  1,  40},
  {  6,  33}, { 19,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, { 14,  38}, {  1,  30}, {  5,  15}, {  1,  43}, {  5,  27},  /* Row 42 */
  {  1,  29}, {  1,   3}, { 19,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, { 14,  38},
  {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, { 13,   4},
  {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  45},
  {  6,  33}, {  1,   7}, { 19,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, { 14,  38}, {  1,  34}, {  5,  15}, {  1,  34}, {  1,  35},  /* Row 43 */
  {  4,  27}, {  1,  28}, {  1,  49}, { 19,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30},
  {  7,  38}, {  1,  68}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  29},
  {  1,   3}, { 12,   4}, {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15},
  {  1,  31}, {  1,  40}, {  6,  33}, { 20,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, { 15,  38}, {  1,  30}, {  5,  15}, {  1,  43}, {  5,  27},  /* Row 44 */
  {  1,  29}, {  1,   3}, { 18,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38},
  {  1,  69}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28},
  {  1,  49}, { 11,   4}, {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  30},
  {  4,  15}, {  1,  45}, {  6,  33}, {  1,   7}, { 20,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  68}, {  6,  38}, {  1,  34}, {  5,  15},  /* Row 45 */
  {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, { 18,   4}, {  1,  16}, {  1,  20},
  {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15},
  {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, { 10,   4}, {  1,  18}, {  4,  15}, {  1,  30},
  {  7,  38}, {  1,  34}, {  4,  15}, {  1,  31}, {  1,  40}, {  6,  33}, { 21,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  69}, {  7,  38}, {  1,  30}, {  5,  15},  /* Row 46 */
  {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, { 17,   4}, {  1,  16}, {  1,  20}, {  2,  15},
  {  1,  30}, {  7,  38}, {  2,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35},
  {  4,  27}, {  1,  28}, {  1,  49}, {  9,   4}, {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34},
  {  7,  38}, {  1,  30}, {  4,  15}, {  1,  45}, {  6,  33}, {  1,   7}, { 21,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  1,  34}, {  6,  38}, {  1,  34},  /* Row 47 */
  {  5,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, { 17,   4}, {  1,  16},
  {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  1,  15}, {  1,  34}, {  7,  38},
  {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, {  8,   4}, {  1,  18},
  {  4,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  31}, {  1,  40}, {  6,  33},
  { 22,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  2,  30}, {  7,  38}, {  1,  30}, {  5,  15},  /* Row 48 */
  {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, { 16,   4}, {  1,  16}, {  1,  20}, {  2,  15},
  {  1,  30}, {  7,  38}, {  1,  30}, {  1,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15},
  {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, {  7,   4}, {  1,  21}, {  1,  19},
  {  4,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  70}, {  6,  33}, {  1,   7},
  { 22,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  1,  15}, {  1,  34}, {  6,  38},  /* Row 49 */
  {  1,  34}, {  5,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, { 16,   4},
  {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15}, {  1,  34},
  {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, {  6,   4},
  {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,   1},
  {  1,  53}, {  5,  33}, { 23,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  1,  15}, {  1,  30}, {  7,  38},  /* Row 50 */
  {  1,  30}, {  5,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, { 15,   4}, {  1,  16},
  {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15}, {  1,  30}, {  7,  38},
  {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, {  5,   4},
  {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43},
  {  1,  27}, {  1,  57}, {  1,  53}, {  3,  33}, {  1,   7}, { 23,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  34}, {  6,  38},  /* Row 51 */
  {  1,  34}, {  5,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, { 15,   4},
  {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  3,  15}, {  1,  34},
  {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, {  4,   4},
  {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35},
  {  2,  27}, {  1,  57}, {  1,  53}, {  2,  33}, { 24,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  30}, {  7,  38},  /* Row 52 */
  {  1,  30}, {  5,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, { 14,   4}, {  1,  16},
  {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  3,  15}, {  1,  30}, {  7,  38},
  {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, {  3,   4},
  {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43},
  {  4,  27}, {  1,  57}, {  1,  53}, {  1,   7}, { 24,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  3,  15}, {  1,  34}, {  6,  38},  /* Row 53 */
  {  1,  34}, {  5,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, { 14,   4},
  {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  34},
  {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, {  2,   4},
  {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35},
  {  5,  27}, {  1,  71}, {  1,  72}, { 24,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  3,  15}, {  1,  30}, {  7,  38},  /* Row 54 */
  {  1,  30}, {  5,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, { 13,   4}, {  1,  16},
  {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  30}, {  7,  38},
  {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, {  1,   4},
  {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43},
  {  6,  27}, {  1,  28}, {  1,  49}, {  1,   5}, { 23,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  4,  15}, {  1,  34}, {  6,  38},  /* Row 55 */
  {  1,  34}, {  5,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, { 13,   4},
  {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15}, {  1,  34},
  {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   4}, {  1,  18},
  {  4,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  6,  27},
  {  1,  49}, {  1,   3}, {  1,   4}, {  1,   5}, { 22,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  4,  15}, {  1,  30}, {  7,  38},  /* Row 56 */
  {  1,  30}, {  5,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, { 12,   4}, {  1,  16},
  {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15}, {  1,  30}, {  7,  38},
  {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  73}, {  1,  19},
  {  4,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  6,  27}, {  1,  36},
  {  1,  13}, {  3,   4}, {  1,   5}, { 21,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  5,  15}, {  1,  34}, {  6,  38},  /* Row 57 */
  {  1,  34}, {  5,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, { 12,   4},
  {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  6,  15}, {  1,  34},
  {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  74}, {  4,  15}, {  1,  30},
  {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  5,  27}, {  1,  28}, {  1,  49},
  {  1,   8}, {  1,  13}, {  3,   4}, {  1,   5}, { 20,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  5,  15}, {  1,  30}, {  7,  38},  /* Row 58 */
  {  1,  30}, {  5,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, { 11,   4}, {  1,  16},
  {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  6,  15}, {  1,  30}, {  7,  38},
  {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  3,  27}, {  1,  35}, {  1,  34}, {  4,  15},
  {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  6,  27}, {  1,  29}, {  2,   3},
  {  1,   8}, {  1,  13}, {  3,   4}, {  1,   5}, { 19,   0},
  { 17,   0}, {  1,  75}, {  1,  55}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  6,  15},  /* Row 59 */
  {  1,  34}, {  6,  38}, {  1,  34}, {  5,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28},
  {  1,  49}, { 11,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30},
  {  7,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  3,  27}, {  1,  43},
  {  4,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  5,  27},
  {  1,  28}, {  1,  49}, {  2,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  3,   4}, {  1,   5},
  { 18,   0},
  { 16,   0}, {  1,   1}, {  1,  76}, {  1,  37}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30},  /* Row 60 */
  {  6,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15}, {  1,  43}, {  5,  27}, {  1,  29},
  {  1,   3}, { 10,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30},
  {  3,  15}, {  1,  37}, {  3,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34},
  {  1,  35}, {  1,  27}, {  1,  35}, {  1,  34}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  30},
  {  4,  15}, {  1,  43}, {  6,  27}, {  1,  29}, {  1,   3}, {  3,   4}, {  1,   3}, {  1,   8},
  {  1,  13}, {  3,   4}, {  1,   5}, { 17,   0},
  { 15,   0}, {  1,   1}, {  1,   6}, {  1,  73}, {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38},  /* Row 61 */
  {  1,  30}, {  7,  15}, {  1,  34}, {  6,  38}, {  1,  34}, {  5,  15}, {  1,  34}, {  1,  35},
  {  4,  27}, {  1,  28}, {  1,  49}, { 10,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30},
  {  7,  38}, {  1,  30}, {  2,  15}, {  1,  77}, {  1,  78}, {  4,  15}, {  1,  34}, {  7,  38},
  {  1,  30}, {  4,  15}, {  1,  43}, {  1,  79}, {  1,  43}, {  4,  15}, {  1,  30}, {  7,  38},
  {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  5,  27}, {  1,  28}, {  1,  49}, {  5,   4},
  {  1,   3}, {  1,   8}, {  1,  13}, {  3,   4}, {  1,   5}, { 16,   0},
  { 14,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30},  /* Row 62 */
  {  8,  38}, {  1,  30}, {  7,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15}, {  1,  43},
  {  5,  27}, {  1,  29}, {  1,   3}, {  9,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30},
  {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  41}, {  1,  34}, {  3,  15}, {  1,  30},
  {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  80}, {  1,  34}, {  4,  15}, {  1,  34},
  {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  6,  27}, {  1,  29}, {  1,   3}, {  6,   4},
  {  1,   3}, {  1,   8}, {  1,  13}, {  3,   4}, {  1,   5}, { 15,   0},
  { 13,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   4}, {  1,   9}, {  1,  77}, {  2,  15},  /* Row 63 */
  {  1,  30}, {  8,  38}, {  1,  30}, {  3,  15}, {  1,  37}, {  4,  15}, {  1,  34}, {  6,  38},
  {  1,  34}, {  5,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, {  9,   4},
  {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25},
  {  1,  81}, {  1,  43}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  68},
  {  4,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  5,  27},
  {  1,  28}, {  1,  49}, {  8,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  3,   4}, {  1,   5},
  { 14,   0},
  { 12,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   4}, {  1,  28}, {  1,  82}, {  1,  83},  /* Row 64 */
  {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  77}, {  1,  78}, {  4,  15},
  {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3},
  {  8,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15},
  {  1,  25}, {  1,  81}, {  1,  35}, {  1,  34}, {  3,  15}, {  1,  30}, {  7,  38}, {  1,  34},
  {  9,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  6,  27}, {  1,  29},
  {  1,   3}, {  9,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  3,   4}, {  1,   5}, { 13,   0},
  { 11,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   4}, {  1,  28}, {  1,  84}, {  1,  85},  /* Row 65 */
  {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  41},
  {  1,  34}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34}, {  5,  15}, {  1,  34}, {  1,  35},
  {  4,  27}, {  1,  28}, {  1,  49}, {  8,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30},
  {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  1,  27}, {  1,  43}, {  4,  15},
  {  1,  34}, {  7,  38}, {  1,  30}, {  7,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15},
  {  1,  34}, {  1,  35}, {  5,  27}, {  1,  28}, {  1,  49}, { 11,   4}, {  1,   3}, {  1,   8},
  {  1,  13}, {  3,   4}, {  1,   5}, { 12,   0},
  { 10,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   4}, {  1,  28}, {  1,  84}, {  1,  86},  /* Row 66 */
  {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25},
  {  1,  81}, {  1,  43}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15}, {  1,  43},
  {  5,  27}, {  1,  29}, {  1,   3}, {  7,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30},
  {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  1,  27}, {  1,  35}, {  1,  34},
  {  3,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  7,  15}, {  1,  34}, {  7,  38}, {  1,  30},
  {  4,  15}, {  1,  43}, {  6,  27}, {  1,  29}, {  1,   3}, { 12,   4}, {  1,   3}, {  1,   8},
  {  1,  13}, {  3,   4}, {  1,   5}, { 11,   0},
  {  9,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   4}, {  1,  28}, {  1,  84}, {  1,  86},  /* Row 67 */
  {  1,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15},
  {  1,  25}, {  1,  81}, {  1,  35}, {  1,  34}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34},
  {  5,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, {  7,   4}, {  1,  16},
  {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},
  {  2,  27}, {  1,  43}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  5,  15}, {  1,  30},
  {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  5,  27}, {  1,  28}, {  1,  49},
  { 14,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  3,   4}, {  1,   5}, { 10,   0},
  {  8,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   4}, {  1,  28}, {  1,  84}, {  1,  86},  /* Row 68 */
  {  2,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15},
  {  1,  25}, {  1,  81}, {  1,  27}, {  1,  43}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30},
  {  5,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, {  6,   4}, {  1,  16}, {  1,  20},
  {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  2,  27},
  {  1,  35}, {  1,  34}, {  3,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  5,  15}, {  1,  34},
  {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  6,  27}, {  1,  29}, {  1,   3}, { 15,   4},
  {  1,   3}, {  1,   8}, {  1,  13}, {  3,   4}, {  1,   5}, {  9,   0},
  {  7,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   4}, {  1,  28}, {  1,  84}, {  1,  86},  /* Row 69 */
  {  3,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15},
  {  1,  25}, {  1,  81}, {  1,  27}, {  1,  35}, {  1,  34}, {  4,  15}, {  1,  34}, {  6,  38},
  {  1,  34}, {  5,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, {  6,   4},
  {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25},
  {  1,  81}, {  3,  27}, {  1,  43}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  3,  15},
  {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  5,  27}, {  1,  28},
  {  1,  49}, { 17,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  3,   4}, {  1,   5}, {  8,   0},
  {  6,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   4}, {  1,  28}, {  1,  84}, {  1,  86},  /* Row 70 */
  {  4,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15},
  {  1,  25}, {  1,  81}, {  2,  27}, {  1,  43}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30},
  {  5,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, {  5,   4}, {  1,  16}, {  1,  20},
  {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  3,  27},
  {  1,  35}, {  1,  34}, {  3,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  3,  15}, {  1,  34},
  {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  6,  27}, {  1,  29}, {  1,   3}, { 18,   4},
  {  1,   3}, {  1,   8}, {  1,  13}, {  3,   4}, {  1,   5}, {  7,   0},
  {  5,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   4}, {  1,  28}, {  1,  84}, {  1,  86},  /* Row 71 */
  {  5,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15},
  {  1,  25}, {  1,  81}, {  2,  27}, {  1,  35}, {  1,  34}, {  4,  15}, {  1,  34}, {  6,  38},
  {  1,  34}, {  5,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, {  5,   4},
  {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25},
  {  1,  81}, {  4,  27}, {  1,  43}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  1,  15},
  {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  5,  27}, {  1,  28},
  {  1,  49}, { 20,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  3,   4}, {  1,   5}, {  6,   0},
  {  4,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   4}, {  1,  28}, {  1,  84}, {  1,  86},  /* Row 72 */
  {  6,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15},
  {  1,  25}, {  1,  81}, {  3,  27}, {  1,  43}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30},
  {  5,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, {  4,   4}, {  1,  16}, {  1,  20},
  {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  4,  27},
  {  1,  35}, {  1,  34}, {  3,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  1,  15}, {  1,  34},
  {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  6,  27}, {  1,  29}, {  1,   3}, { 21,   4},
  {  1,   3}, {  1,   8}, {  1,  13}, {  3,   4}, {  1,   5}, {  5,   0},
  {  3,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   4}, {  1,  28}, {  1,  84}, {  1,  86},  /* Row 73 */
  {  7,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15},
  {  1,  25}, {  1,  81}, {  3,  27}, {  1,  35}, {  1,  34}, {  4,  15}, {  1,  34}, {  6,  38},
  {  1,  34}, {  5,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, {  4,   4},
  {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25},
  {  1,  81}, {  5,  27}, {  1,  43}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  69}, {  7,  38},
  {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  5,  27}, {  1,  28}, {  1,  49}, { 23,   4},
  {  1,   3}, {  1,   8}, {  1,  13}, {  3,   4}, {  1,   5}, {  4,   0},
  {  2,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   4}, {  1,  28}, {  1,  84}, {  1,  86},  /* Row 74 */
  {  8,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15},
  {  1,  25}, {  1,  81}, {  4,  27}, {  1,  43}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30},
  {  5,  15}, {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, {  3,   4}, {  1,  16}, {  1,  20},
  {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  5,  27},
  {  1,  87}, {  1,  34}, {  3,  15}, {  1,  30}, { 15,  38}, {  1,  30}, {  4,  15}, {  1,  43},
  {  6,  27}, {  1,  29}, {  1,   3}, { 24,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  3,   4},
  {  1,   5}, {  3,   0},
  {  1,   0}, {  1,   1}, {  1,   6}, {  1,   7}, {  1,   4}, {  1,  28}, {  1,  84}, {  1,  86},  /* Row 75 */
  {  9,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15},
  {  1,  25}, {  1,  81}, {  4,  27}, {  1,  35}, {  1,  34}, {  4,  15}, {  1,  34}, {  6,  38},
  {  1,  34}, {  5,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, {  3,   4},
  {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25},
  {  1,  81}, {  6,  27}, {  1,  67}, {  4,  15}, {  1,  34}, { 13,  38}, {  1,  34}, {  4,  15},
  {  1,  34}, {  1,  35}, {  5,  27}, {  1,  28}, {  1,  49}, { 26,   4}, {  1,   3}, {  1,   8},
  {  1,  13}, {  3,   4}, {  1,   5}, {  2,   0},
  {  1,   1}, {  1,   6}, {  1,   7}, {  1,   4}, {  1,  28}, {  1,  84}, {  1,  86}, { 10,   4},  /* Row 76 */
  {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25},
  {  1,  81}, {  5,  27}, {  1,  43}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15},
  {  1,  43}, {  5,  27}, {  1,  29}, {  1,   3}, {  2,   4}, {  1,  16}, {  1,  20}, {  2,  15},
  {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27}, {  1,  21},
  {  1,  19}, {  3,  15}, {  1,  30}, { 13,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  6,  27},
  {  1,  29}, {  1,   3}, { 27,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  3,   4}, {  1,   5},
  {  1,   0},
  {  1,   2}, {  1,   7}, {  1,   4}, {  1,  28}, {  1,  84}, {  1,  86}, { 11,   4}, {  1,  16},  /* Row 77 */
  {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},
  {  5,  27}, {  1,  87}, {  1,  34}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34}, {  5,  15},
  {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, {  2,   4}, {  1,  16}, {  1,  20},
  {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27},
  {  1,   4}, {  1,  18}, {  4,  15}, {  1,  34}, { 11,  38}, {  1,  34}, {  4,  15}, {  1,  34},
  {  1,  35}, {  5,  27}, {  1,  28}, {  1,  49}, { 29,   4}, {  1,   3}, {  1,   8}, {  1,  13},
  {  3,   4}, {  1,   5},
  {  1,   3}, {  1,   4}, {  1,  28}, {  1,  84}, {  1,  86}, { 12,   4}, {  1,  16}, {  1,  20},  /* Row 78 */
  {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27},
  {  1,  67}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15}, {  1,  43}, {  5,  27},
  {  1,  29}, {  1,   3}, {  1,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38},
  {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27}, {  1,   4}, {  1,  21}, {  1,  19},
  {  3,  15}, {  1,  30}, { 11,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  6,  27}, {  1,  29},
  {  1,   3}, { 30,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  3,   4},
  {  1,   4}, {  1,  88}, {  1,  89}, {  1,  86}, { 13,   4}, {  1,  16}, {  1,  20}, {  2,  15},  /* Row 79 */
  {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27}, {  1,  21},
  {  1,  19}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34}, {  5,  15}, {  1,  34}, {  1,  35},
  {  4,  27}, {  1,  28}, {  1,  49}, {  1,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30},
  {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27}, {  2,   4}, {  1,   6},
  {  4,  15}, {  1,  34}, {  9,  38}, {  1,  19}, {  4,  15}, {  1,  34}, {  1,  35}, {  5,  27},
  {  1,  28}, {  1,  49}, { 32,   4}, {  1,   3}, {  1,   8}, {  1,  13}, {  1,  49}, {  1,   3},
  {  2,   4}, {  1,  90}, {  1,   8}, {  1,   3}, { 12,   4}, {  1,  16}, {  1,  20}, {  2,  15},  /* Row 80 */
  {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27}, {  1,   4},
  {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15}, {  1,  43}, {  5,  27},
  {  1,  29}, {  1,   3}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30},
  {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27}, {  2,   4}, {  1,   6}, {  4,  15}, {  1,  69},
  {  9,  38}, {  1,  31}, {  4,  15}, {  1,  43}, {  6,  27}, {  1,  29}, {  1,   3}, { 33,   4},
  {  1,   3}, {  1,  91}, {  1,  81}, {  1,   4},
  {  3,   4}, {  1,  13}, {  1,   8}, {  1,   3}, { 11,   4}, {  1,  16}, {  1,  20}, {  2,  15},  /* Row 81 */
  {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27}, {  1,   4},
  {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34}, {  5,  15}, {  1,  34},
  {  1,  35}, {  4,  27}, {  1,  28}, {  1,  49}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30},
  {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27}, {  1,   4}, {  1,  21},
  {  1,  19}, {  4,  15}, {  1,  34}, {  8,  38}, {  1,  92}, {  4,  15}, {  1,  68}, {  1,  93},
  {  6,  27}, {  1,  49}, { 33,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  56}, {  1,   8},
  {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, { 10,   4}, {  1,  16}, {  1,  20}, {  2,  15},  /* Row 82 */
  {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27}, {  2,   4},
  {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15}, {  1,  43}, {  5,  27},
  {  1,  29}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15},
  {  1,  25}, {  1,  81}, {  6,  27}, {  1,   4}, {  1,  18}, {  4,  15}, {  1,  30}, { 10,  38},
  {  1,  30}, {  4,  15}, {  1,  43}, {  6,  27}, {  1,  49}, { 32,   4}, {  1,   3}, {  1,   8},
  {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4},
  {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  9,   4}, {  1,  16}, {  1,  20},  /* Row 83 */
  {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27},
  {  2,   4}, {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34}, {  5,  15},
  {  1,  34}, {  1,  35}, {  4,  27}, {  1,  28}, {  1,   7}, {  1,  20}, {  2,  15}, {  1,  30},
  {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27}, {  1,  21}, {  1,  19},
  {  4,  15}, {  1,  34}, { 10,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  5,  27},
  {  1,  29}, {  1,   3}, { 30,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7},
  {  1,   4}, {  1,   5},
  {  1,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  8,   4}, {  1,  16},  /* Row 84 */
  {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},
  {  6,  27}, {  3,   4}, {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15},
  {  1,  43}, {  5,  27}, {  1,  10}, {  1,  20}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30},
  {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27}, {  1,  67}, {  4,  15}, {  1,  30}, { 12,  38},
  {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  28}, {  1,  49}, { 29,   4}, {  1,   3},
  {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, {  1,   0},
  {  2,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  7,   4}, {  1,  16},  /* Row 85 */
  {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},
  {  6,  27}, {  3,   4}, {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34},
  {  5,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  81}, {  1,  77}, {  2,  15}, {  1,  30},
  {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  5,  27}, {  1,  87}, {  1,  34},
  {  4,  15}, {  1,  34}, { 12,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  5,  27},
  {  1,  29}, {  1,   3}, { 27,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7},
  {  1,   4}, {  1,   5}, {  2,   0},
  {  3,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  6,   4}, {  1,  16},  /* Row 86 */
  {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},
  {  6,  27}, {  4,   4}, {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15},
  {  1,  43}, {  4,  27}, {  1,  81}, {  1,  25}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30},
  {  2,  15}, {  1,  25}, {  1,  81}, {  5,  27}, {  1,  43}, {  4,  15}, {  1,  30}, { 14,  38},
  {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  28}, {  1,  49}, { 26,   4}, {  1,   3},
  {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, {  3,   0},
  {  4,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  5,   4}, {  1,  16},  /* Row 87 */
  {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},
  {  6,  27}, {  4,   4}, {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34},
  {  5,  15}, {  1,  34}, {  1,  35}, {  3,  27}, {  1,  81}, {  1,  25}, {  2,  15}, {  1,  30},
  {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  4,  27}, {  1,  35}, {  1,  34},
  {  4,  15}, {  1,  34}, { 14,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  5,  27},
  {  1,  29}, {  1,   3}, { 24,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7},
  {  1,   4}, {  1,   5}, {  4,   0},
  {  5,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  4,   4}, {  1,  16},  /* Row 88 */
  {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},
  {  6,  27}, {  5,   4}, {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15},
  {  1,  43}, {  3,  27}, {  1,  81}, {  1,  25}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30},
  {  2,  15}, {  1,  25}, {  1,  81}, {  4,  27}, {  1,  43}, {  4,  15}, {  1,  30}, { 16,  38},
  {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  28}, {  1,  49}, { 23,   4}, {  1,   3},
  {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, {  5,   0},
  {  6,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  3,   4}, {  1,  16},  /* Row 89 */
  {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},
  {  6,  27}, {  5,   4}, {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34},
  {  5,  15}, {  1,  34}, {  1,  35}, {  2,  27}, {  1,  81}, {  1,  25}, {  2,  15}, {  1,  30},
  {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  3,  27}, {  1,  35}, {  1,  34},
  {  4,  15}, {  1,  34}, {  7,  38}, {  1,  34}, {  1,  69}, {  7,  38}, {  1,  34}, {  4,  15},
  {  1,  34}, {  1,  35}, {  5,  27}, {  1,  29}, {  1,   3}, { 21,   4}, {  1,   3}, {  1,   8},
  {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, {  6,   0},
  {  7,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  2,   4}, {  1,  16},  /* Row 90 */
  {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},
  {  6,  27}, {  6,   4}, {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15},
  {  1,  43}, {  2,  27}, {  1,  81}, {  1,  25}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30},
  {  2,  15}, {  1,  25}, {  1,  81}, {  3,  27}, {  1,  43}, {  4,  15}, {  1,  30}, {  8,  38},
  {  1,  30}, {  1,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27},
  {  1,  28}, {  1,  49}, { 20,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7},
  {  1,   4}, {  1,   5}, {  7,   0},
  {  8,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  1,   4}, {  1,  16},  /* Row 91 */
  {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},
  {  6,  27}, {  6,   4}, {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34},
  {  5,  15}, {  1,  34}, {  1,  35}, {  1,  27}, {  1,  81}, {  1,  25}, {  2,  15}, {  1,  30},
  {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  2,  27}, {  1,  35}, {  1,  34},
  {  4,  15}, {  1,  34}, {  7,  38}, {  1,  34}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  34},
  {  4,  15}, {  1,  34}, {  1,  35}, {  5,  27}, {  1,  29}, {  1,   3}, { 18,   4}, {  1,   3},
  {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, {  8,   0},
  {  9,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  1,  16}, {  1,  20},  /* Row 92 */
  {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27},
  {  7,   4}, {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15}, {  1,  43},
  {  1,  27}, {  1,  81}, {  1,  25}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15},
  {  1,  25}, {  1,  81}, {  2,  27}, {  1,  43}, {  4,  15}, {  1,  30}, {  8,  38}, {  1,  30},
  {  3,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  28},
  {  1,  49}, { 17,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4},
  {  1,   5}, {  9,   0},
  { 10,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,  16}, {  1,  20}, {  2,  15},  /* Row 93 */
  {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27}, {  7,   4},
  {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34}, {  5,  15}, {  1,  34},
  {  1,  35}, {  1,  81}, {  1,  25}, {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15},
  {  1,  25}, {  1,  81}, {  1,  27}, {  1,  35}, {  1,  34}, {  4,  15}, {  1,  34}, {  7,  38},
  {  1,  34}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35},
  {  5,  27}, {  1,  29}, {  1,   3}, { 15,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},
  {  1,   7}, {  1,   4}, {  1,   5}, { 10,   0},
  { 11,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,  73}, {  1,  20}, {  2,  15}, {  1,  30},  /* Row 94 */
  {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27}, {  8,   4}, {  1,  18},
  {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15}, {  1,  43}, {  1,  81}, {  1,  25},
  {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  1,  27},
  {  1,  43}, {  4,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  5,  15}, {  1,  34}, {  7,  38},
  {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  28}, {  1,  49}, { 14,   4}, {  1,   3},
  {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 11,   0},
  { 12,   0}, {  1,   5}, {  4,   4}, {  1,  57}, {  1,  77}, {  2,  15}, {  1,  30}, {  8,  38},  /* Row 95 */
  {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27}, {  8,   4}, {  1,  21}, {  1,  19},
  {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34}, {  5,  15}, {  1,  34}, {  1,  41}, {  1,  25},
  {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  1,  35},
  {  1,  34}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  34}, {  6,  15}, {  1,  30}, {  7,  38},
  {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  5,  27}, {  1,  29}, {  1,   3}, { 12,   4},
  {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 12,   0},
  { 13,   0}, {  1,   5}, {  3,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38},  /* Row 96 */
  {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27}, {  9,   4}, {  1,  18}, {  4,  15},
  {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15}, {  1,  78}, {  1,  77}, {  2,  15}, {  1,  30},
  {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  1,  43}, {  4,  15}, {  1,  30},
  {  8,  38}, {  1,  30}, {  7,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43},
  {  5,  27}, {  1,  28}, {  1,  49}, { 11,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},
  {  1,   7}, {  1,   4}, {  1,   5}, { 13,   0},
  { 14,   0}, {  1,   5}, {  2,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38},  /* Row 97 */
  {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27}, {  9,   4}, {  1,  21}, {  1,  19},
  {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34}, {  5,  15}, {  1,  37}, {  3,  15}, {  1,  30},
  {  7,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  41}, {  1,  34}, {  4,  15}, {  1,  34},
  {  7,  38}, {  1,  34}, {  4,  15}, {  1,  37}, {  3,  15}, {  1,  30}, {  7,  38}, {  1,  34},
  {  4,  15}, {  1,  34}, {  1,  35}, {  5,  27}, {  1,  29}, {  1,   3}, {  9,   4}, {  1,   3},
  {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 14,   0},
  { 15,   0}, {  1,   5}, {  1,   4}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38},  /* Row 98 */
  {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27}, { 10,   4}, {  1,  18}, {  4,  15},
  {  1,  30}, {  7,  38}, {  1,  30}, {  8,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15},
  {  1,  77}, {  1,  78}, {  4,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  4,  15}, {  1,  78},
  {  1,  34}, {  3,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27},
  {  1,  28}, {  1,  49}, {  8,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7},
  {  1,   4}, {  1,   5}, { 15,   0},
  { 16,   0}, {  1,   5}, {  1,  16}, {  1,  20}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30},  /* Row 99 */
  {  2,  15}, {  1,  25}, {  1,  81}, {  6,  27}, { 10,   4}, {  1,  21}, {  1,  19}, {  4,  15},
  {  1,  34}, {  6,  38}, {  1,  34}, {  8,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  3,  15},
  {  1,  37}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  94},
  {  1,  43}, {  3,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35},
  {  5,  27}, {  1,  29}, {  1,   3}, {  6,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11},
  {  1,   7}, {  1,   4}, {  1,   5}, { 16,   0},
  { 17,   0}, {  1,  95}, {  1,  96}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15},  /* Row 100 */
  {  1,  25}, {  1,  81}, {  6,  27}, { 11,   4}, {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38},
  {  1,  30}, {  7,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  7,  15}, {  1,  30}, {  8,  38},
  {  1,  30}, {  4,  15}, {  1,  43}, {  1,  27}, {  1,  35}, {  1,  34}, {  3,  15}, {  1,  34},
  {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  28}, {  1,  49}, {  5,   4},
  {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 17,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},  /* Row 101 */
  {  6,  27}, { 11,   4}, {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34},
  {  7,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  7,  15}, {  1,  34}, {  7,  38}, {  1,  34},
  {  4,  15}, {  1,  34}, {  1,  35}, {  2,  27}, {  1,  43}, {  3,  15}, {  1,  30}, {  7,  38},
  {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  5,  27}, {  1,  29}, {  1,   3}, {  3,   4},
  {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 18,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},  /* Row 102 */
  {  6,  27}, { 12,   4}, {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  6,  15},
  {  1,  30}, {  7,  38}, {  1,  30}, {  6,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  4,  15},
  {  1,  43}, {  3,  27}, {  1,  35}, {  1,  34}, {  3,  15}, {  1,  34}, {  7,  38}, {  1,  30},
  {  4,  15}, {  1,  43}, {  5,  27}, {  1,  28}, {  1,  49}, {  2,   4}, {  1,   3}, {  1,   8},
  {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 19,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},  /* Row 103 */
  {  6,  27}, { 12,   4}, {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34},
  {  6,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  6,  15}, {  1,  34}, {  7,  38}, {  1,  34},
  {  4,  15}, {  1,  34}, {  1,  35}, {  4,  27}, {  1,  43}, {  3,  15}, {  1,  30}, {  7,  38},
  {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  5,  27}, {  1,  29}, {  2,   3}, {  1,   8},
  {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 20,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},  /* Row 104 */
  {  6,  27}, { 13,   4}, {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15},
  {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  4,  15},
  {  1,  43}, {  5,  27}, {  1,  35}, {  1,  34}, {  3,  15}, {  1,  34}, {  7,  38}, {  1,  30},
  {  4,  15}, {  1,  43}, {  5,  27}, {  1,  28}, {  1,  49}, {  1,   8}, {  1,   7}, {  1,  11},
  {  1,   7}, {  1,   4}, {  1,   5}, { 21,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},  /* Row 105 */
  {  6,  27}, { 13,   4}, {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34},
  {  5,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  5,  15}, {  1,  34}, {  7,  38}, {  1,  34},
  {  4,  15}, {  1,  34}, {  1,  35}, {  6,  27}, {  1,  74}, {  3,  15}, {  1,  30}, {  7,  38},
  {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  5,  27}, {  1,  36}, {  1,   7}, {  1,  11},
  {  1,   7}, {  1,   4}, {  1,   5}, { 22,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},  /* Row 106 */
  {  6,  27}, { 14,   4}, {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  4,  15},
  {  1,  30}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  4,  15},
  {  1,  43}, {  6,  27}, {  1,  28}, {  1,  73}, {  1,  19}, {  3,  15}, {  1,  34}, {  7,  38},
  {  1,  30}, {  4,  15}, {  1,  43}, {  5,  27}, {  1,  54}, {  1,  97}, {  1,   7}, {  1,   4},
  {  1,   5}, { 23,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},  /* Row 107 */
  {  6,  27}, { 14,   4}, {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34},
  {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  34},
  {  4,  15}, {  1,  34}, {  1,  35}, {  6,  27}, {  1,  29}, {  1,   4}, {  1,  18}, {  3,  15},
  {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  5,  27}, {  1,  98},
  {  1,   4}, {  1,   5}, { 24,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},  /* Row 108 */
  {  6,  27}, { 15,   4}, {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  3,  15},
  {  1,  30}, {  7,  38}, {  1,  30}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  4,  15},
  {  1,  43}, {  6,  27}, {  1,  28}, {  1,  49}, {  1,   4}, {  1,  21}, {  1,  19}, {  3,  15},
  {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  4,  27}, {  1,  57}, {  1,  99},
  {  1,   9}, { 25,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},  /* Row 109 */
  {  6,  27}, { 15,   4}, {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34},
  {  3,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  3,  15}, {  1,  34}, {  7,  38}, {  1,  34},
  {  4,  15}, {  1,  34}, {  1,  35}, {  6,  27}, {  1,  29}, {  1,   3}, {  2,   4}, {  1,  18},
  {  3,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35}, {  2,  27},
  {  1,  57}, {  1,  53}, {  2,  33}, { 25,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},  /* Row 110 */
  {  6,  27}, { 16,   4}, {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15},
  {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  4,  15},
  {  1,  43}, {  6,  27}, {  1,  28}, {  1,  49}, {  3,   4}, {  1,  21}, {  1,  19}, {  3,  15},
  {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  1,  27}, {  1,  57}, {  1,  53},
  {  3,  33}, {  1,   7}, { 24,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},  /* Row 111 */
  {  6,  27}, { 16,   4}, {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34},
  {  2,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  2,  15}, {  1,  34}, {  7,  38}, {  1,  34},
  {  4,  15}, {  1,  34}, {  1,  35}, {  6,  27}, {  1,  29}, {  1,   3}, {  4,   4}, {  1,  18},
  {  3,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,   1}, {  1,  53},
  {  5,  33}, { 24,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},  /* Row 112 */
  {  6,  27}, { 17,   4}, {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  1,  15},
  {  1,  30}, {  7,  38}, {  1,  30}, {  1,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  4,  15},
  {  1,  43}, {  6,  27}, {  1,  28}, {  1,  49}, {  5,   4}, {  1,  21}, {  1,  19}, {  3,  15},
  {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  70}, {  6,  33}, {  1,   7}, { 23,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},  /* Row 113 */
  {  6,  27}, { 17,   4}, {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34},
  {  1,  15}, {  1,  30}, {  7,  38}, {  1,  30}, {  1,  15}, {  1,  34}, {  7,  38}, {  1,  34},
  {  4,  15}, {  1,  34}, {  1,  35}, {  6,  27}, {  1,  29}, {  1,   3}, {  6,   4}, {  1,  18},
  {  3,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  31}, {  1,  40}, {  6,  33},
  { 23,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},  /* Row 114 */
  {  6,  27}, { 18,   4}, {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  2,  30}, {  7,  38},
  {  2,  30}, {  8,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  6,  27}, {  1,  28}, {  1,  49},
  {  7,   4}, {  1,  21}, {  1,  19}, {  3,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15},
  {  1,  45}, {  6,  33}, {  1,   7}, { 22,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  25}, {  1,  81},  /* Row 115 */
  {  6,  27}, { 18,   4}, {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34},
  {  1,  30}, {  7,  38}, {  1,  30}, {  1,  34}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34},
  {  1,  35}, {  6,  27}, {  1,  29}, {  1,   3}, {  8,   4}, {  1,  18}, {  3,  15}, {  1,  30},
  {  7,  38}, {  1,  34}, {  4,  15}, {  1,  31}, {  1,  40}, {  6,  33}, { 22,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  77}, {  1, 100},  /* Row 116 */
  {  6,  27}, { 19,   4}, {  1,  18}, {  4,  15}, {  1,  30}, {  7,  38}, {  1,  69}, {  7,  38},
  {  1,  69}, {  8,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  6,  27}, {  1,  28}, {  1,  49},
  {  9,   4}, {  1,  21}, {  1,  19}, {  3,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15},
  {  1,  45}, {  6,  33}, {  1,   7}, { 21,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1, 101},  /* Row 117 */
  {  1,  57}, {  5,  27}, { 19,   4}, {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  6,  38},
  {  1,  68}, {  7,  38}, {  1,  68}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  34}, {  1,  35},
  {  6,  27}, {  1,  29}, {  1,   3}, { 10,   4}, {  1,  18}, {  3,  15}, {  1,  30}, {  7,  38},
  {  1,  34}, {  4,  15}, {  1,  31}, {  1,  40}, {  6,  33}, { 21,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 118 */
  {  1,  53}, {  1,  57}, {  4,  27}, {  1,   8}, {  1,   3}, { 18,   4}, {  1,  18}, {  4,  15},
  {  1,  30}, { 22,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  6,  27}, {  1,  28}, {  1,  49},
  { 11,   4}, {  1,  21}, {  1,  19}, {  3,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15},
  {  1,  45}, {  6,  33}, {  1,   7}, { 20,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 119 */
  {  1,  33}, {  1,  53}, {  1,  57}, {  3,  27}, {  1,  36}, {  1,   8}, {  1,   3}, { 17,   4},
  {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, { 20,  38}, {  1,  34}, {  4,  15}, {  1,  34},
  {  1,  35}, {  6,  27}, {  1,  29}, {  1,   3}, { 12,   4}, {  1,  18}, {  3,  15}, {  1,  30},
  {  7,  38}, {  1,  34}, {  4,  15}, {  1,  31}, {  1,  40}, {  6,  33}, { 20,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 120 */
  {  2,  33}, {  1,  53}, {  1,  57}, {  2,  27}, {  1,  49}, {  1,  13}, {  1,   8}, {  1,   3},
  { 17,   4}, {  1,  18}, {  4,  15}, {  1,  30}, { 20,  38}, {  1,  30}, {  4,  15}, {  1,  43},
  {  6,  27}, {  1,  28}, {  1,  49}, { 12,   4}, {  1,   3}, {  1,  79}, {  1,  63}, {  3,  15},
  {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  45}, {  6,  33}, {  1,   7}, { 19,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 121 */
  {  3,  33}, {  1,  53}, {  1,  57}, {  1,  27}, {  2,   4}, {  1,  13}, {  1,   8}, {  1,   3},
  { 16,   4}, {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, { 18,  38}, {  1,  34}, {  4,  15},
  {  1,  34}, {  1,  35}, {  6,  27}, {  1,  29}, {  1,   3}, { 11,   4}, {  1,   3}, {  1,   8},
  {  1,   7}, {  1, 102}, {  3,  15}, {  1,  30}, {  7,  38}, {  1,  34}, {  4,  15}, {  1,  31},
  {  1,  40}, {  6,  33}, { 19,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 122 */
  {  4,  33}, {  1,  53}, {  1,  62}, {  3,   4}, {  1,  13}, {  1,   8}, {  1,   3}, { 16,   4},
  {  1,  18}, {  4,  15}, {  1,  30}, { 18,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  6,  27},
  {  1,  28}, {  1,  49}, { 11,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,  79},
  {  1,  19}, {  3,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  45}, {  6,  33},
  {  1,   7}, { 18,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 123 */
  {  5,  33}, {  1, 103}, {  1,   3}, {  3,   4}, {  1,  13}, {  1,   8}, {  1,   3}, { 15,   4},
  {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, { 16,  38}, {  1,  34}, {  4,  15}, {  1,  34},
  {  1,  35}, {  6,  27}, {  1,  29}, {  1,   3}, { 10,   4}, {  1,   3}, {  1,   8}, {  1,   7},
  {  1,  11}, {  1,   7}, {  1,   4}, {  1,  53}, {  3,  15}, {  1,  30}, {  7,  38}, {  1,  34},
  {  4,  15}, {  1,  31}, {  1,  40}, {  6,  33}, { 18,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 124 */
  {  6,  33}, {  1,  95}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, { 15,   4}, {  1,  18},
  {  4,  15}, {  1,  30}, { 16,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  6,  27}, {  1,  28},
  {  1,  49}, { 10,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4},
  {  1,   5}, {  1,  27}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15}, {  1,  45},
  {  6,  33}, {  1,   7}, { 17,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 125 */
  {  6,  33}, {  1,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, { 14,   4},
  {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, { 14,  38}, {  1,  34}, {  4,  15}, {  1,  34},
  {  1,  35}, {  6,  27}, {  1,  29}, {  1,   3}, {  9,   4}, {  1,   3}, {  1,   8}, {  1,   7},
  {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, {  2,   0}, {  1,  14}, {  3,  15}, {  1,  30},
  {  7,  38}, {  1,  34}, {  4,  15}, {  1,  31}, {  1,  40}, {  6,  33}, { 17,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 126 */
  {  6,  33}, {  2,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, { 14,   4},
  {  1,  18}, {  4,  15}, {  1,  30}, { 14,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  6,  27},
  {  1,  28}, {  1,  49}, {  9,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7},
  {  1,   4}, {  1,   5}, {  4,   0}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15},
  {  1,  45}, {  6,  33}, {  1,   7}, { 16,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 127 */
  {  6,  33}, {  3,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, { 13,   4},
  {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, { 12,  38}, {  1,  34}, {  4,  15}, {  1,  34},
  {  1,  35}, {  6,  27}, {  1,  29}, {  1,   3}, {  8,   4}, {  1,   3}, {  1,   8}, {  1,   7},
  {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, {  5,   0}, {  1,  14}, {  3,  15}, {  1,  30},
  {  7,  38}, {  1,  34}, {  4,  15}, {  1,  31}, {  1,  40}, {  6,  33}, { 16,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 128 */
  {  6,  33}, {  4,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, { 13,   4},
  {  1,  18}, {  4,  15}, {  1,  30}, { 12,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  6,  27},
  {  1,  28}, {  1,  49}, {  8,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7},
  {  1,   4}, {  1,   5}, {  7,   0}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15},
  {  1,  45}, {  6,  33}, {  1,   7}, { 15,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 129 */
  {  6,  33}, {  5,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, { 12,   4},
  {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, { 10,  38}, {  1,  34}, {  4,  15}, {  1,  34},
  {  1,  35}, {  6,  27}, {  1,  29}, {  1,   3}, {  7,   4}, {  1,   3}, {  1,   8}, {  1,   7},
  {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, {  8,   0}, {  1,  14}, {  3,  15}, {  1,  30},
  {  7,  38}, {  1,  34}, {  4,  15}, {  1,  31}, {  1,  40}, {  6,  33}, { 15,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 130 */
  {  6,  33}, {  6,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, { 12,   4},
  {  1,  18}, {  4,  15}, {  1,  30}, { 10,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  6,  27},
  {  1,  28}, {  1,  49}, {  7,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7},
  {  1,   4}, {  1,   5}, { 10,   0}, {  4,  15}, {  1,  34}, {  7,  38}, {  1,  30}, {  4,  15},
  {  1,  22}, {  6,  33}, {  1, 104}, { 14,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 131 */
  {  6,  33}, {  7,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, { 11,   4},
  {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  8,  38}, {  1,  34}, {  4,  15}, {  1,  34},
  {  1,  35}, {  6,  27}, {  1,  29}, {  1,   3}, {  6,   4}, {  1,   3}, {  1,   8}, {  1,   7},
  {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 11,   0}, {  1,  14}, {  3,  15}, {  1,  30},
  {  7,  38}, {  1,  44}, {  4,  15}, {  1,  45}, {  6,  33}, {  1,  65}, { 14,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 132 */
  {  6,  33}, {  8,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, { 11,   4},
  {  1,  18}, {  4,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  4,  15}, {  1,  43}, {  6,  27},
  {  1,  28}, {  1,  49}, {  6,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7},
  {  1,   4}, {  1,   5}, { 13,   0}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34}, {  4,  15},
  {  1,  45}, {  7,  33}, { 14,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 133 */
  {  6,  33}, {  9,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, { 10,   4},
  {  1,  21}, {  1,  19}, {  4,  15}, {  1,  34}, {  6,  38}, {  1,  34}, {  4,  15}, {  1,  34},
  {  1,  35}, {  6,  27}, {  1,  29}, {  1,   3}, {  5,   4}, {  1,   3}, {  1,   8}, {  1,   7},
  {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 14,   0}, {  1,  14}, {  3,  15}, {  1,  30},
  {  6,  38}, {  1,  44}, {  3,  15}, {  1,  55}, {  1, 105}, {  7,  33}, {  1, 104}, { 13,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 134 */
  {  6,  33}, { 10,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, { 10,   4},
  {  1,  18}, {  4,  15}, {  1,  30}, {  1, 106}, {  4,  38}, {  1, 106}, {  1,  30}, {  4,  15},
  {  1,  43}, {  6,  27}, {  1,  28}, {  1,  49}, {  5,   4}, {  1,   3}, {  1,   8}, {  1,   7},
  {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 16,   0}, {  4,  15}, {  1,  34}, {  5,  38},
  {  1,  30}, {  3,  15}, {  1,  31}, {  1,  60}, {  7,  33}, {  1,   7}, { 13,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 135 */
  {  6,  33}, { 11,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  9,   4},
  {  1,  21}, {  1,  19}, {  4,  15}, {  1,  30}, {  1,  34}, {  2,  38}, {  1,  34}, {  1,  30},
  {  4,  15}, {  1,  34}, {  1,  35}, {  6,  27}, {  1,  29}, {  1,   3}, {  4,   4}, {  1,   3},
  {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 17,   0}, {  1,  14},
  {  3,  15}, {  1,  30}, {  1,  34}, {  2,  38}, {  1,  34}, {  1,  30}, {  4,  15}, {  1,  45},
  {  8,  33}, {  1,   7}, { 13,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 136 */
  {  6,  33}, { 12,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  9,   4},
  {  1,  18}, {  6,  15}, {  2,  30}, {  6,  15}, {  1,  43}, {  6,  27}, {  1,  28}, {  1,  49},
  {  4,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5},
  { 19,   0}, {  5,  15}, {  2,  30}, {  5,  15}, {  1,  31}, {  1,  40}, {  8,  33}, {  1,  50},
  { 13,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 137 */
  {  6,  33}, { 13,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  8,   4},
  {  1,  21}, {  1,  19}, { 12,  15}, {  1,  34}, {  1,  35}, {  6,  27}, {  1,  29}, {  1,   3},
  {  3,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5},
  { 20,   0}, {  1,  14}, { 10,  15}, {  1,  39}, {  1, 107}, {  9,  33}, { 14,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  30}, {  2,  15}, {  1,  96}, {  1,  60},  /* Row 138 */
  {  6,  33}, { 14,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  8,   4},
  {  1,  18}, { 12,  15}, {  1,  43}, {  6,  27}, {  1,  28}, {  1,  49}, {  3,   4}, {  1,   3},
  {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 22,   0}, {  9,  15},
  {  1,  39}, {  1, 107}, {  9,  33}, {  1,   7}, { 14,   0},
  { 18,   0}, {  3,  15}, {  1,  30}, {  8,  38}, {  1,  55}, {  2,  96}, {  1, 108}, {  1,  60},  /* Row 139 */
  {  6,  33}, { 15,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  7,   4},
  {  1,  16}, {  1,  23}, { 10,  15}, {  1, 109}, {  1,  81}, {  6,  27}, {  1,  29}, {  1,   3},
  {  2,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5},
  { 23,   0}, {  1,  28}, {  1, 110}, {  5,  15}, {  1,  39}, {  1, 111}, {  1, 105}, { 10,  33},
  { 15,   0},
  { 25,   0}, {  1,  59}, {  4,  40}, {  4,  60}, {  7,  33}, { 16,   0}, {  1,   5}, {  4,   4},  /* Row 140 */
  {  1,  13}, {  1,   8}, {  1,   3}, {  7,   4}, {  1,  16}, {  1,  18}, {  1,  19}, {  6,  15},
  {  1,  34}, {  1,  43}, {  1,  81}, {  6,  27}, {  1,  28}, {  1,  49}, {  2,   4}, {  1,   3},
  {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 27,   0}, {  1, 112},
  {  1,  14}, {  1, 113}, {  1,  22}, {  1,  40}, {  1,  76}, { 10,  33}, {  1,   7}, { 15,   0},
  { 25,   0}, { 16,  33}, { 17,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3},  /* Row 141 */
  {  8,   4}, {  1,  21}, {  1,  18}, {  1,  19}, {  1,  77}, {  1,  25}, {  1,  34}, {  1,  43},
  {  1,  35}, {  8,  27}, {  1,   8}, {  1,   3}, {  1,   4}, {  1,   3}, {  1,   8}, {  1,   7},
  {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 30,   0}, {  1,  50}, { 13,  33}, { 16,   0},
  { 25,   0}, { 16,  33}, { 18,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3},  /* Row 142 */
  {  9,   4}, {  1,  16}, {  1,  10}, {  1,  66}, {  1,  81}, {  8,  27}, {  1,  36}, {  1,   8},
  {  1,   3}, {  1,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4},
  {  1,   5}, { 32,   0}, {  1,   7}, { 11,  33}, { 17,   0},
  { 25,   0}, { 16,  33}, { 19,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3},  /* Row 143 */
  {  9,   4}, {  1,   3}, {  1,  49}, {  1,  29}, {  1,  28}, {  5,  27}, {  1,  36}, {  1,   8},
  {  3,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5},
  { 34,   0}, { 10,  33}, { 18,   0},
  { 25,   0}, { 16,  33}, { 20,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3},  /* Row 144 */
  { 10,   4}, {  1,   3}, { 10,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7},
  {  1,   4}, {  1,   5}, { 35,   0}, {  1,  41}, {  7,  33}, {  1,   7}, { 19,   0},
  { 25,   0}, { 16,  33}, { 21,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3},  /* Row 145 */
  { 19,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5},
  { 64,   0},
  { 25,   0}, { 16,  33}, { 22,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3},  /* Row 146 */
  { 17,   4}, {  1,   3}, {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5},
  { 65,   0},
  { 64,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, { 15,   4}, {  1,   3},  /* Row 147 */
  {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 66,   0},
  { 65,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, { 13,   4}, {  1,   3},  /* Row 148 */
  {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 67,   0},
  { 66,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, { 11,   4}, {  1,   3},  /* Row 149 */
  {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 68,   0},
  { 67,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  9,   4}, {  1,   3},  /* Row 150 */
  {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 69,   0},
  { 68,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  7,   4}, {  1,   3},  /* Row 151 */
  {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 70,   0},
  { 69,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  5,   4}, {  1,   3},  /* Row 152 */
  {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 71,   0},
  { 70,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  3,   4}, {  1,   3},  /* Row 153 */
  {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 72,   0},
  { 71,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  1,   4}, {  1,   3},  /* Row 154 */
  {  1,   8}, {  1,   7}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 73,   0},
  { 72,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,   8}, {  1,   3}, {  1,   8}, {  1,   7},  /* Row 155 */
  {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5}, { 74,   0},
  { 73,   0}, {  1,   5}, {  4,   4}, {  1,  13}, {  1,  91}, {  1,   7}, {  1,  11}, {  1,   7},  /* Row 156 */
  {  1,   4}, {  1,   5}, { 75,   0},
  { 74,   0}, {  1,   5}, {  4,   4}, {  1, 114}, {  1,  11}, {  1,   7}, {  1,   4}, {  1,   5},  /* Row 157 */
  { 76,   0},
  { 75,   0}, {  1,   5}, {  2,   4}, {  1,   8}, {  1, 115}, {  1,   7}, {  1,   4}, {  1,   5},  /* Row 158 */
  { 77,   0},
  { 76,   0}, {  1,   5}, {  4,   4}, {  1,   5}, { 78,   0}                                       /* Row 159 */

#  else /* CONFIG_EXAMPLES_NXIMAGE_GREYSCALE */

static const struct pix_run_s g_nuttx[] =
{
  { 76,   0}, {  1,   1}, {  1,   2}, {  5,   3}, {  1,   4}, { 76,   0},                          /* Row 0 */
  { 75,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  6,   3}, {  1,   4}, { 75,   0},              /* Row 1 */
  { 74,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  2,   6}, {  4,   3}, {  1,   4},  /* Row 2 */
  { 74,   0},
  { 73,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   8},  /* Row 3 */
  {  1,   6}, {  4,   3}, {  1,   4}, { 73,   0},
  { 72,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 4 */
  {  2,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 72,   0},
  { 71,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 5 */
  {  4,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 71,   0},
  { 70,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 6 */
  {  6,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 70,   0},
  { 69,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 7 */
  {  8,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 69,   0},
  { 68,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 8 */
  { 10,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 68,   0},
  { 67,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 9 */
  { 12,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 67,   0},
  { 66,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 10 */
  { 14,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 66,   0},
  { 65,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 11 */
  { 16,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 65,   0},
  { 64,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 12 */
  { 18,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 64,   0},
  { 63,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 13 */
  { 20,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 63,   0},
  { 62,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 14 */
  { 22,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 62,   0},
  { 61,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 15 */
  { 24,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 61,   0},
  { 60,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 16 */
  { 26,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 60,   0},
  { 59,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 17 */
  { 28,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 59,   0},
  { 58,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 18 */
  { 30,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 26,   0}, {  1,   9}, {  5,  10}, {  1,   9},
  { 25,   0},
  { 57,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 19 */
  { 10,   3}, {  5,   5}, { 17,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 23,   0}, {  1,   1},
  {  9,  10}, {  1,   1}, { 23,   0},
  { 56,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 20 */
  {  9,   3}, {  1,   5}, {  1,  11}, {  5,  12}, {  1,  11}, {  1,  13}, { 16,   3}, {  1,   6},
  {  4,   3}, {  1,   4}, { 22,   0}, { 11,  10}, {  1,   1}, { 22,   0},
  { 23,   0}, {  1,   9}, {  5,  10}, {  1,  14}, { 25,   0}, {  1,   1}, {  1,   2}, {  1,   5},  /* Row 21 */
  {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  9,   3}, {  1,   5}, {  1,  15}, {  7,  10},
  {  1,  12}, {  1,  16}, { 16,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 20,   0}, {  1,   9},
  { 12,  10}, {  1,   1}, { 21,   0},
  { 21,   0}, {  1,   1}, {  9,  10}, {  1,   1}, { 22,   0}, {  1,   1}, {  1,   2}, {  1,   5},  /* Row 22 */
  {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  9,   3}, {  1,   5}, {  1,  15}, {  9,  10},
  {  1,  12}, {  1,  16}, { 16,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 19,   0}, { 14,  10},
  { 21,   0},
  { 21,   0}, { 11,  10}, {  1,   1}, { 20,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3},  /* Row 23 */
  {  1,   7}, {  1,   2}, {  1,   5}, { 10,   3}, {  1,  11}, { 11,  10}, {  1,  12}, {  1,   7},
  {  3,   6}, {  1,   5}, { 12,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 17,   0}, {  1,   9},
  {  6,  10}, {  2,  12}, {  6,  10}, {  1,   9}, { 20,   0},
  { 20,   0}, {  1,  14}, { 12,  10}, {  1,   9}, {  4,   2}, {  1,  17}, { 13,   0}, {  1,   1},  /* Row 24 */
  {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5}, { 10,   3}, {  1,  13},
  {  1,  12}, { 12,  10}, {  1,  12}, {  5,   6}, { 12,   3}, {  1,   6}, {  4,   3}, {  1,   4},
  { 16,   0}, {  5,  10}, {  6,  12}, {  4,  10}, {  1,  12}, {  1,  18}, {  1,   1}, { 18,   0},
  { 19,   0}, {  1,   9}, { 13,  10}, {  1,  12}, {  1,  18}, {  5,   2}, { 11,   0}, {  1,   1},  /* Row 25 */
  {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5}, { 11,   3}, {  1,  19},
  {  4,  10}, {  5,  12}, {  5,  10}, {  1,  11}, {  5,   6}, { 12,   3}, {  1,   6}, {  4,   3},
  {  1,   4}, { 14,   0}, {  1,   9}, {  5,  10}, {  6,  12}, {  5,  10}, {  1,  14}, {  1,   2},
  {  1,   1}, { 17,   0},
  { 19,   0}, {  6,  10}, {  3,  12}, {  6,  10}, {  1,  14}, {  5,   2}, {  1,  17}, {  9,   0},  /* Row 26 */
  {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5}, { 11,   3},
  {  1,   5}, {  1,  14}, {  4,  10}, {  6,  12}, {  4,  10}, {  1,  12}, {  6,   6}, { 12,   3},
  {  1,   6}, {  4,   3}, {  1,   4}, { 13,   0}, {  5,  10}, {  8,  12}, {  4,  10}, {  1,  14},
  {  2,   2}, {  1,   1}, { 16,   0},
  { 18,   0}, {  1,   9}, {  4,  10}, {  6,  12}, {  5,  10}, {  1,  12}, {  1,  18}, {  5,   2},  /* Row 27 */
  {  8,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5},
  { 12,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  5,  10}, {  1,  11}, {  5,   6},
  {  1,   5}, { 12,   3}, {  1,   6}, {  4,   3}, {  1,   4}, { 11,   0}, {  1,   9}, {  5,  10},
  {  8,  12}, {  4,  10}, {  1,  14}, {  3,   2}, { 16,   0},
  { 18,   0}, {  5,  10}, {  6,  12}, {  6,  10}, {  1,  14}, {  5,   2}, {  1,  17}, {  6,   0},  /* Row 28 */
  {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5}, { 13,   3},
  {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  6,   6}, { 13,   3},
  {  1,   6}, {  4,   3}, {  1,   4}, { 10,   0}, {  5,  10}, {  8,  12}, {  5,  10}, {  1,  14},
  {  3,   2}, {  1,  17}, { 15,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  5,  10}, {  1,  12}, {  1,  18}, {  5,   2}, {  5,   0},  /* Row 29 */
  {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5}, { 14,   3},
  {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  5,  10}, {  1,  11}, {  5,   6}, {  1,   5},
  { 13,   3}, {  1,   6}, {  4,   3}, {  1,   4}, {  8,   0}, {  1,   9}, {  5,  10}, {  8,  12},
  {  4,  10}, {  1,  12}, {  1,  18}, {  4,   2}, { 15,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  6,  10}, {  1,  14}, {  5,   2}, {  1,  17}, {  3,   0},  /* Row 30 */
  {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5}, { 15,   3},
  {  1,   5}, {  1,  12}, {  3,  10}, {  9,  12}, {  4,  10}, {  1,  12}, {  6,   6}, { 14,   3},
  {  1,   6}, {  4,   3}, {  1,   4}, {  7,   0}, {  5,  10}, {  8,  12}, {  5,  10}, {  1,  14},
  {  5,   2}, {  1,   1}, { 14,   0},
  { 18,   0}, {  4,  10}, {  9,  12}, {  5,  10}, {  1,  12}, {  1,  18}, {  5,   2}, {  2,   0},  /* Row 31 */
  {  1,   1}, {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5}, { 16,   3},
  {  1,   5}, {  1,  12}, {  3,  10}, {  9,  12}, {  5,  10}, {  1,  11}, {  5,   6}, {  1,   5},
  { 14,   3}, {  1,   6}, {  4,   3}, {  1,   4}, {  5,   0}, {  1,   9}, {  5,  10}, {  8,  12},
  {  4,  10}, {  1,  12}, {  1,  18}, {  5,   2}, {  1,  17}, { 14,   0},
  { 18,   0}, {  4,  10}, {  9,  12}, {  6,  10}, {  1,  14}, {  5,   2}, {  1,  17}, {  1,   1},  /* Row 32 */
  {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5}, { 17,   3}, {  1,   5},
  {  1,  12}, {  3,  10}, { 10,  12}, {  4,  10}, {  1,  12}, {  6,   6}, { 15,   3}, {  1,   6},
  {  4,   3}, {  1,   4}, {  4,   0}, {  5,  10}, {  8,  12}, {  5,  10}, {  1,  14}, {  6,   2},
  {  1,   1}, { 14,   0},
  { 18,   0}, {  4,  10}, { 10,  12}, {  5,  10}, {  1,  12}, {  1,  18}, {  4,   2}, {  1,  18},  /* Row 33 */
  {  1,   2}, {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5}, { 18,   3}, {  1,   5},
  {  1,  12}, {  3,  10}, { 10,  12}, {  5,  10}, {  1,  11}, {  5,   6}, {  1,   5}, { 15,   3},
  {  1,   6}, {  4,   3}, {  1,   4}, {  2,   0}, {  1,   9}, {  5,  10}, {  8,  12}, {  4,  10},
  {  1,  12}, {  1,  18}, {  6,   2}, { 15,   0},
  { 18,   0}, {  4,  10}, { 10,  12}, {  6,  10}, {  1,  14}, {  3,   2}, {  1,  20}, {  1,   6},  /* Row 34 */
  {  1,   5}, {  2,   3}, {  1,   7}, {  1,   2}, {  1,   5}, { 19,   3}, {  1,   5}, {  1,  12},
  {  3,  10}, { 11,  12}, {  4,  10}, {  1,  12}, {  6,   6}, { 16,   3}, {  1,   6}, {  4,   3},
  {  1,   4}, {  1,   0}, {  5,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  7,   2}, {  1,  17},
  { 15,   0},
  { 18,   0}, {  4,  10}, { 11,  12}, {  5,  10}, {  1,  12}, {  1,  18}, {  1,   2}, {  1,  20},  /* Row 35 */
  {  3,   6}, {  1,   3}, {  1,   7}, {  1,   2}, {  1,   5}, { 20,   3}, {  1,   5}, {  1,  12},
  {  3,  10}, { 11,  12}, {  5,  10}, {  1,  11}, {  5,   6}, {  1,   5}, { 16,   3}, {  1,   6},
  {  4,   3}, {  1,  11}, {  5,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  7,   2}, { 16,   0},
  { 18,   0}, {  4,  10}, { 11,  12}, {  6,  10}, {  1,  14}, {  1,  20}, {  5,   6}, {  1,   2},  /* Row 36 */
  {  1,   5}, { 21,   3}, {  1,   5}, {  1,  12}, {  3,  10}, { 12,  12}, {  4,  10}, {  1,  12},
  {  6,   6}, { 17,   3}, {  1,   6}, {  2,   3}, {  1,  13}, {  1,  12}, {  4,  10}, {  8,  12},
  {  5,  10}, {  1,  14}, {  6,   2}, {  1,  17}, { 16,   0},
  { 18,   0}, {  4,  10}, { 12,  12}, {  5,  10}, {  1,  12}, {  6,   6}, {  1,   5}, { 22,   3},  /* Row 37 */
  {  1,   5}, {  1,  12}, {  3,  10}, { 12,  12}, {  5,  10}, {  1,  11}, {  5,   6}, {  1,   5},
  { 17,   3}, {  1,   6}, {  1,   3}, {  1,  11}, {  5,  10}, {  8,  12}, {  4,  10}, {  1,  12},
  {  1,  18}, {  6,   2}, { 17,   0},
  { 18,   0}, {  4,  10}, { 12,  12}, {  6,  10}, {  1,  11}, {  5,   6}, {  1,   5}, { 22,   3},  /* Row 38 */
  {  1,   5}, {  1,  12}, {  3,  10}, { 13,  12}, {  4,  10}, {  1,  12}, {  6,   6}, { 18,   3},
  {  1,   6}, {  1,  12}, {  4,  10}, {  8,  12}, {  5,  10}, {  1,  14}, {  6,   2}, {  1,  17},
  { 17,   0},
  { 18,   0}, {  4,  10}, { 13,  12}, {  5,  10}, {  1,  12}, {  6,   6}, { 22,   3}, {  1,   5},  /* Row 39 */
  {  1,  12}, {  3,  10}, { 13,  12}, {  5,  10}, {  1,  11}, {  5,   6}, {  1,   5}, { 17,   3},
  {  1,  11}, {  5,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  1,  18}, {  6,   2}, { 18,   0},
  { 18,   0}, {  4,  10}, { 13,  12}, {  6,  10}, {  1,  11}, {  5,   6}, {  1,   5}, { 21,   3},  /* Row 40 */
  {  1,   5}, {  1,  12}, {  3,  10}, { 14,  12}, {  4,  10}, {  1,  12}, {  6,   6}, { 16,   3},
  {  1,  13}, {  1,  12}, {  4,  10}, {  8,  12}, {  5,  10}, {  1,  14}, {  6,   2}, {  1,  17},
  { 18,   0},
  { 18,   0}, {  4,  10}, { 14,  12}, {  5,  10}, {  1,  12}, {  6,   6}, { 21,   3}, {  1,   5},  /* Row 41 */
  {  1,  12}, {  3,  10}, { 14,  12}, {  5,  10}, {  1,  11}, {  5,   6}, {  1,   5}, { 15,   3},
  {  1,  11}, {  5,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  1,  18}, {  6,   2}, { 19,   0},
  { 18,   0}, {  4,  10}, { 14,  12}, {  6,  10}, {  1,  11}, {  5,   6}, {  1,   5}, { 20,   3},  /* Row 42 */
  {  1,   5}, {  1,  12}, {  3,  10}, { 15,  12}, {  4,  10}, {  1,  12}, {  6,   6}, { 14,   3},
  {  1,  13}, {  1,  12}, {  4,  10}, {  8,  12}, {  5,  10}, {  1,  14}, {  6,   2}, {  1,  17},
  { 19,   0},
  { 18,   0}, {  4,  10}, { 15,  12}, {  5,  10}, {  1,  12}, {  6,   6}, { 20,   3}, {  1,   5},  /* Row 43 */
  {  1,  12}, {  3,  10}, { 15,  12}, {  5,  10}, {  1,  11}, {  5,   6}, {  1,   5}, { 13,   3},
  {  1,  11}, {  5,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  1,  18}, {  6,   2}, { 20,   0},
  { 18,   0}, {  4,  10}, { 15,  12}, {  6,  10}, {  1,  11}, {  5,   6}, {  1,   5}, { 19,   3},  /* Row 44 */
  {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  1,  10}, {  8,  12}, {  4,  10}, {  1,  12},
  {  6,   6}, { 12,   3}, {  1,  13}, {  1,  12}, {  4,  10}, {  8,  12}, {  5,  10}, {  1,  14},
  {  6,   2}, {  1,  17}, { 20,   0},
  { 18,   0}, {  4,  10}, { 16,  12}, {  5,  10}, {  1,  12}, {  6,   6}, { 19,   3}, {  1,   5},  /* Row 45 */
  {  1,  12}, {  3,  10}, {  7,  12}, {  1,  10}, {  8,  12}, {  5,  10}, {  1,  11}, {  5,   6},
  {  1,   5}, { 11,   3}, {  1,  11}, {  5,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  1,  18},
  {  6,   2}, { 21,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  1,  10}, {  7,  12}, {  6,  10}, {  1,  11}, {  5,   6},  /* Row 46 */
  {  1,   5}, { 18,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  2,  10}, {  8,  12},
  {  4,  10}, {  1,  12}, {  6,   6}, { 10,   3}, {  1,  13}, {  1,  12}, {  4,  10}, {  8,  12},
  {  5,  10}, {  1,  14}, {  6,   2}, {  1,  17}, { 21,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  1,  10}, {  8,  12}, {  5,  10}, {  1,  12}, {  6,   6},  /* Row 47 */
  { 18,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  2,  10}, {  8,  12}, {  5,  10},
  {  1,  11}, {  5,   6}, {  1,   5}, {  9,   3}, {  1,  11}, {  5,  10}, {  8,  12}, {  4,  10},
  {  1,  12}, {  1,  18}, {  6,   2}, { 22,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  2,  10}, {  7,  12}, {  6,  10}, {  1,  11}, {  5,   6},  /* Row 48 */
  {  1,   5}, { 17,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10}, {  8,  12},
  {  4,  10}, {  1,  12}, {  6,   6}, {  8,   3}, {  1,  13}, {  1,  12}, {  4,  10}, {  8,  12},
  {  5,  10}, {  1,  14}, {  6,   2}, {  1,  17}, { 22,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  2,  10}, {  8,  12}, {  5,  10}, {  1,  12}, {  6,   6},  /* Row 49 */
  { 17,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10}, {  8,  12}, {  5,  10},
  {  1,  11}, {  5,   6}, {  1,   5}, {  7,   3}, {  1,  11}, {  5,  10}, {  8,  12}, {  4,  10},
  {  1,  12}, {  1,  21}, {  1,  20}, {  5,   2}, { 23,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  7,  12}, {  6,  10}, {  1,  11}, {  5,   6},  /* Row 50 */
  {  1,   5}, { 16,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  4,  10}, {  8,  12},
  {  4,  10}, {  1,  12}, {  6,   6}, {  6,   3}, {  1,  13}, {  1,  12}, {  4,  10}, {  8,  12},
  {  5,  10}, {  1,  11}, {  2,   6}, {  1,  20}, {  3,   2}, {  1,  17}, { 23,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  8,  12}, {  5,  10}, {  1,  12}, {  6,   6},  /* Row 51 */
  { 16,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  4,  10}, {  8,  12}, {  5,  10},
  {  1,  11}, {  5,   6}, {  1,   5}, {  5,   3}, {  1,  11}, {  5,  10}, {  8,  12}, {  4,  10},
  {  1,  12}, {  4,   6}, {  1,  20}, {  2,   2}, { 24,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  4,  10}, {  7,  12}, {  6,  10}, {  1,  11}, {  5,   6},  /* Row 52 */
  {  1,   5}, { 15,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  5,  10}, {  8,  12},
  {  4,  10}, {  1,  12}, {  6,   6}, {  4,   3}, {  1,  13}, {  1,  12}, {  4,  10}, {  8,  12},
  {  5,  10}, {  1,  11}, {  5,   6}, {  1,  20}, {  1,  17}, { 24,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  4,  10}, {  8,  12}, {  5,  10}, {  1,  12}, {  6,   6},  /* Row 53 */
  { 15,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  5,  10}, {  8,  12}, {  5,  10},
  {  1,  11}, {  5,   6}, {  1,   5}, {  3,   3}, {  1,  11}, {  5,  10}, {  8,  12}, {  4,  10},
  {  1,  12}, {  7,   6}, { 25,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  5,  10}, {  7,  12}, {  6,  10}, {  1,  11}, {  5,   6},  /* Row 54 */
  {  1,   5}, { 14,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  6,  10}, {  8,  12},
  {  4,  10}, {  1,  12}, {  6,   6}, {  2,   3}, {  1,  13}, {  1,  12}, {  4,  10}, {  8,  12},
  {  5,  10}, {  1,  11}, {  7,   6}, {  1,   3}, {  1,   4}, { 23,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  5,  10}, {  8,  12}, {  5,  10}, {  1,  12}, {  6,   6},  /* Row 55 */
  { 14,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  6,  10}, {  8,  12}, {  5,  10},
  {  1,  11}, {  5,   6}, {  1,   5}, {  1,   3}, {  1,  11}, {  5,  10}, {  8,  12}, {  4,  10},
  {  1,  12}, {  7,   6}, {  3,   3}, {  1,   4}, { 22,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  6,  10}, {  7,  12}, {  6,  10}, {  1,  11}, {  5,   6},  /* Row 56 */
  {  1,   5}, { 13,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  7,  10}, {  8,  12},
  {  4,  10}, {  1,  12}, {  6,   6}, {  1,  13}, {  1,  12}, {  4,  10}, {  8,  12}, {  5,  10},
  {  1,  11}, {  8,   6}, {  3,   3}, {  1,   4}, { 21,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  6,  10}, {  8,  12}, {  5,  10}, {  1,  12}, {  6,   6},  /* Row 57 */
  { 13,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  7,  10}, {  8,  12}, {  5,  10},
  {  1,  11}, {  5,   6}, {  1,  11}, {  5,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  7,   6},
  {  2,   3}, {  1,   6}, {  3,   3}, {  1,   4}, { 20,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  7,  10}, {  7,  12}, {  6,  10}, {  1,  11}, {  5,   6},  /* Row 58 */
  {  1,   5}, { 12,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  8,  10}, {  8,  12},
  {  4,  10}, {  1,  12}, {  5,   6}, {  1,  12}, {  4,  10}, {  8,  12}, {  5,  10}, {  1,  11},
  {  6,   6}, {  1,   5}, {  3,   3}, {  1,   6}, {  3,   3}, {  1,   4}, { 19,   0},
  { 17,   0}, {  1,  22}, {  1,  12}, {  3,  10}, {  8,  12}, {  7,  10}, {  8,  12}, {  5,  10},  /* Row 59 */
  {  1,  12}, {  6,   6}, { 12,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  8,  10},
  {  8,  12}, {  5,  10}, {  1,  11}, {  3,   6}, {  1,  11}, {  5,  10}, {  8,  12}, {  4,  10},
  {  1,  12}, {  7,   6}, {  5,   3}, {  1,   6}, {  3,   3}, {  1,   4}, { 18,   0},
  { 16,   0}, {  1,   1}, {  1,  18}, {  1,  12}, {  3,  10}, {  8,  12}, {  8,  10}, {  7,  12},  /* Row 60 */
  {  6,  10}, {  1,  11}, {  5,   6}, {  1,   5}, { 11,   3}, {  1,   5}, {  1,  12}, {  3,  10},
  {  7,  12}, {  4,  10}, {  1,  12}, {  4,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  3,   6},
  {  1,  12}, {  4,  10}, {  8,  12}, {  5,  10}, {  1,  11}, {  6,   6}, {  1,   5}, {  6,   3},
  {  1,   6}, {  3,   3}, {  1,   4}, { 17,   0},
  { 15,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  8,  10},  /* Row 61 */
  {  8,  12}, {  5,  10}, {  1,  12}, {  6,   6}, { 11,   3}, {  1,   5}, {  1,  12}, {  3,  10},
  {  7,  12}, {  3,  10}, {  1,  12}, {  1,  11}, {  4,  10}, {  8,  12}, {  5,  10}, {  1,  11},
  {  1,   6}, {  1,  11}, {  5,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  7,   6}, {  8,   3},
  {  1,   6}, {  3,   3}, {  1,   4}, { 16,   0},
  { 14,   0}, {  1,   1}, {  1,   2}, {  2,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  9,  10},  /* Row 62 */
  {  7,  12}, {  6,  10}, {  1,  11}, {  5,   6}, {  1,   5}, { 10,   3}, {  1,   5}, {  1,  12},
  {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12}, {  1,  21}, {  1,  12}, {  4,  10}, {  8,  12},
  {  4,  10}, {  1,  12}, {  1,  23}, {  1,  12}, {  4,  10}, {  8,  12}, {  5,  10}, {  1,  11},
  {  6,   6}, {  1,   5}, {  9,   3}, {  1,   6}, {  3,   3}, {  1,   4}, { 15,   0},
  { 13,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   6}, {  1,  12}, {  3,  10},  /* Row 63 */
  {  8,  12}, {  4,  10}, {  1,  12}, {  4,  10}, {  8,  12}, {  5,  10}, {  1,  12}, {  6,   6},
  { 10,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12}, {  1,   6},
  {  1,  11}, {  4,  10}, {  8,  12}, {  5,  10}, {  1,  12}, {  5,  10}, {  8,  12}, {  4,  10},
  {  1,  12}, {  7,   6}, { 11,   3}, {  1,   6}, {  3,   3}, {  1,   4}, { 14,   0},
  { 12,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   6}, {  1,  24}, {  1,  12},  /* Row 64 */
  {  3,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  1,  11}, {  5,  10}, {  7,  12}, {  6,  10},
  {  1,  11}, {  5,   6}, {  1,   5}, {  9,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12},
  {  3,  10}, {  1,  12}, {  2,   6}, {  1,  12}, {  4,  10}, {  8,  12}, {  9,  10}, {  8,  12},
  {  5,  10}, {  1,  11}, {  6,   6}, {  1,   5}, { 12,   3}, {  1,   6}, {  3,   3}, {  1,   4},
  { 13,   0},
  { 11,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   6}, {  1,   2}, {  1,   3},  /* Row 65 */
  {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  1,  21}, {  1,  12}, {  4,  10},
  {  8,  12}, {  5,  10}, {  1,  12}, {  6,   6}, {  9,   3}, {  1,   5}, {  1,  12}, {  3,  10},
  {  7,  12}, {  3,  10}, {  1,  12}, {  2,   6}, {  1,  11}, {  4,  10}, {  8,  12}, {  9,  10},
  {  8,  12}, {  4,  10}, {  1,  12}, {  7,   6}, { 14,   3}, {  1,   6}, {  3,   3}, {  1,   4},
  { 12,   0},
  { 10,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   6}, {  1,   2}, {  1,   3},  /* Row 66 */
  {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  1,   6}, {  1,  11},
  {  5,  10}, {  7,  12}, {  6,  10}, {  1,  11}, {  5,   6}, {  1,   5}, {  8,   3}, {  1,   5},
  {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12}, {  3,   6}, {  1,  12}, {  4,  10},
  {  8,  12}, {  7,  10}, {  8,  12}, {  5,  10}, {  1,  11}, {  6,   6}, {  1,   5}, { 15,   3},
  {  1,   6}, {  3,   3}, {  1,   4}, { 11,   0},
  {  9,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   6}, {  1,   2}, {  2,   3},  /* Row 67 */
  {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  2,   6}, {  1,  12},
  {  4,  10}, {  8,  12}, {  5,  10}, {  1,  12}, {  6,   6}, {  8,   3}, {  1,   5}, {  1,  12},
  {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12}, {  3,   6}, {  1,  11}, {  4,  10}, {  8,  12},
  {  7,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  7,   6}, { 17,   3}, {  1,   6}, {  3,   3},
  {  1,   4}, { 10,   0},
  {  8,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   6}, {  1,   2}, {  3,   3},  /* Row 68 */
  {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  2,   6}, {  1,  11},
  {  5,  10}, {  7,  12}, {  6,  10}, {  1,  11}, {  5,   6}, {  1,   5}, {  7,   3}, {  1,   5},
  {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12}, {  4,   6}, {  1,  12}, {  4,  10},
  {  8,  12}, {  5,  10}, {  8,  12}, {  5,  10}, {  1,  11}, {  6,   6}, {  1,   5}, { 18,   3},
  {  1,   6}, {  3,   3}, {  1,   4}, {  9,   0},
  {  7,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   6}, {  1,   2}, {  4,   3},  /* Row 69 */
  {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  3,   6}, {  1,  12},
  {  4,  10}, {  8,  12}, {  5,  10}, {  1,  12}, {  6,   6}, {  7,   3}, {  1,   5}, {  1,  12},
  {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12}, {  4,   6}, {  1,  11}, {  4,  10}, {  8,  12},
  {  5,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  7,   6}, { 20,   3}, {  1,   6}, {  3,   3},
  {  1,   4}, {  8,   0},
  {  6,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   6}, {  1,   2}, {  5,   3},  /* Row 70 */
  {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  3,   6}, {  1,  11},
  {  5,  10}, {  7,  12}, {  6,  10}, {  1,  11}, {  5,   6}, {  1,   5}, {  6,   3}, {  1,   5},
  {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12}, {  5,   6}, {  1,  12}, {  4,  10},
  {  8,  12}, {  3,  10}, {  8,  12}, {  5,  10}, {  1,  11}, {  6,   6}, {  1,   5}, { 21,   3},
  {  1,   6}, {  3,   3}, {  1,   4}, {  7,   0},
  {  5,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   6}, {  1,   2}, {  6,   3},  /* Row 71 */
  {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  4,   6}, {  1,  12},
  {  4,  10}, {  8,  12}, {  5,  10}, {  1,  12}, {  6,   6}, {  6,   3}, {  1,   5}, {  1,  12},
  {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12}, {  5,   6}, {  1,  11}, {  4,  10}, {  8,  12},
  {  3,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  7,   6}, { 23,   3}, {  1,   6}, {  3,   3},
  {  1,   4}, {  6,   0},
  {  4,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   6}, {  1,   2}, {  7,   3},  /* Row 72 */
  {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  4,   6}, {  1,  11},
  {  5,  10}, {  7,  12}, {  6,  10}, {  1,  11}, {  5,   6}, {  1,   5}, {  5,   3}, {  1,   5},
  {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12}, {  6,   6}, {  1,  12}, {  4,  10},
  {  8,  12}, {  1,  10}, {  8,  12}, {  5,  10}, {  1,  11}, {  6,   6}, {  1,   5}, { 24,   3},
  {  1,   6}, {  3,   3}, {  1,   4}, {  5,   0},
  {  3,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   6}, {  1,   2}, {  8,   3},  /* Row 73 */
  {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  5,   6}, {  1,  12},
  {  4,  10}, {  8,  12}, {  5,  10}, {  1,  12}, {  6,   6}, {  5,   3}, {  1,   5}, {  1,  12},
  {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12}, {  6,   6}, {  1,  11}, {  4,  10}, {  8,  12},
  {  1,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  7,   6}, { 26,   3}, {  1,   6}, {  3,   3},
  {  1,   4}, {  4,   0},
  {  2,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   6}, {  1,   2}, {  9,   3},  /* Row 74 */
  {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  5,   6}, {  1,  11},
  {  5,  10}, {  7,  12}, {  6,  10}, {  1,  11}, {  5,   6}, {  1,   5}, {  4,   3}, {  1,   5},
  {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12}, {  7,   6}, {  1,  12}, {  4,  10},
  { 15,  12}, {  5,  10}, {  1,  11}, {  6,   6}, {  1,   5}, { 27,   3}, {  1,   6}, {  3,   3},
  {  1,   4}, {  3,   0},
  {  1,   0}, {  1,   1}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   6}, {  1,   2}, { 10,   3},  /* Row 75 */
  {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  6,   6}, {  1,  12},
  {  4,  10}, {  8,  12}, {  5,  10}, {  1,  12}, {  6,   6}, {  4,   3}, {  1,   5}, {  1,  12},
  {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12}, {  7,   6}, {  1,  11}, {  4,  10}, { 15,  12},
  {  4,  10}, {  1,  12}, {  7,   6}, { 29,   3}, {  1,   6}, {  3,   3}, {  1,   4}, {  2,   0},
  {  1,   1}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   6}, {  1,   2}, { 11,   3}, {  1,   5},  /* Row 76 */
  {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  6,   6}, {  1,  11}, {  5,  10},
  {  7,  12}, {  6,  10}, {  1,  11}, {  5,   6}, {  1,   5}, {  3,   3}, {  1,   5}, {  1,  12},
  {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12}, {  7,   6}, {  1,  13}, {  1,  12}, {  4,  10},
  { 13,  12}, {  5,  10}, {  1,  11}, {  6,   6}, {  1,   5}, { 30,   3}, {  1,   6}, {  3,   3},
  {  1,   4}, {  1,   0},
  {  1,   2}, {  1,   5}, {  1,   3}, {  1,   6}, {  1,   2}, { 12,   3}, {  1,   5}, {  1,  12},  /* Row 77 */
  {  3,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, {  1,  12}, {  4,  10}, {  8,  12},
  {  5,  10}, {  1,  12}, {  6,   6}, {  3,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12},
  {  3,  10}, {  1,  12}, {  7,   6}, {  1,   3}, {  1,  11}, {  4,  10}, { 13,  12}, {  4,  10},
  {  1,  12}, {  7,   6}, { 32,   3}, {  1,   6}, {  3,   3}, {  1,   4},
  {  2,   3}, {  1,   6}, {  1,   2}, { 13,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12},  /* Row 78 */
  {  3,  10}, {  1,  12}, {  7,   6}, {  1,  11}, {  5,  10}, {  7,  12}, {  6,  10}, {  1,  11},
  {  5,   6}, {  1,   5}, {  2,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10},
  {  1,  12}, {  7,   6}, {  1,   3}, {  1,  13}, {  1,  12}, {  4,  10}, { 11,  12}, {  5,  10},
  {  1,  11}, {  6,   6}, {  1,   5}, { 33,   3}, {  1,   6}, {  3,   3},
  {  1,   3}, {  1,   5}, {  1,  18}, { 14,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12},  /* Row 79 */
  {  3,  10}, {  1,  12}, {  7,   6}, {  1,  13}, {  1,  12}, {  4,  10}, {  8,  12}, {  5,  10},
  {  1,  12}, {  6,   6}, {  2,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10},
  {  1,  12}, {  7,   6}, {  2,   3}, {  1,  11}, {  4,  10}, { 11,  12}, {  4,  10}, {  1,  12},
  {  7,   6}, { 35,   3}, {  1,   6}, {  2,   3},
  {  2,   3}, {  1,   6}, { 14,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10},  /* Row 80 */
  {  1,  12}, {  7,   6}, {  1,   3}, {  1,  11}, {  5,  10}, {  7,  12}, {  6,  10}, {  1,  11},
  {  5,   6}, {  1,   5}, {  1,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10},
  {  1,  12}, {  7,   6}, {  2,   3}, {  1,  11}, {  5,  10}, { 10,  12}, {  4,  10}, {  1,  11},
  {  6,   6}, {  1,   5}, { 35,   3}, {  1,   5}, {  1,   6}, {  1,   3},
  {  3,   3}, {  1,   6}, { 13,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10},  /* Row 81 */
  {  1,  12}, {  7,   6}, {  1,   3}, {  1,  13}, {  1,  12}, {  4,  10}, {  8,  12}, {  5,  10},
  {  1,  12}, {  6,   6}, {  1,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10},
  {  1,  12}, {  7,   6}, {  1,   3}, {  1,  13}, {  1,  12}, {  4,  10}, { 10,  12}, {  4,  10},
  {  1,  12}, {  1,  21}, {  6,   6}, { 36,   3}, {  1,   7}, {  1,   2}, {  1,   5},
  {  4,   3}, {  1,   6}, { 12,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10},  /* Row 82 */
  {  1,  12}, {  7,   6}, {  2,   3}, {  1,  11}, {  5,  10}, {  7,  12}, {  6,  10}, {  1,  11},
  {  5,   6}, {  2,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12}, {  7,   6},
  {  1,   3}, {  1,  11}, {  5,  10}, { 10,  12}, {  5,  10}, {  1,  11}, {  6,   6}, { 35,   3},
  {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3},
  {  1,   4}, {  4,   3}, {  1,   6}, { 11,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12},  /* Row 83 */
  {  3,  10}, {  1,  12}, {  7,   6}, {  2,   3}, {  1,  13}, {  1,  12}, {  4,  10}, {  8,  12},
  {  5,  10}, {  1,  12}, {  6,   6}, {  1,   5}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10},
  {  1,  12}, {  7,   6}, {  1,  13}, {  1,  12}, {  4,  10}, { 12,  12}, {  4,  10}, {  1,  12},
  {  6,   6}, {  1,   5}, { 33,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4},
  {  1,   0}, {  1,   4}, {  4,   3}, {  1,   6}, { 10,   3}, {  1,   5}, {  1,  12}, {  3,  10},  /* Row 84 */
  {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, {  3,   3}, {  1,  11}, {  5,  10}, {  7,  12},
  {  6,  10}, {  1,  11}, {  5,   6}, {  1,  13}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10},
  {  1,  12}, {  7,   6}, {  1,  11}, {  5,  10}, { 12,  12}, {  5,  10}, {  1,  11}, {  6,   6},
  { 32,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, {  1,   0},
  {  2,   0}, {  1,   4}, {  4,   3}, {  1,   6}, {  9,   3}, {  1,   5}, {  1,  12}, {  3,  10},  /* Row 85 */
  {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, {  3,   3}, {  1,  13}, {  1,  12}, {  4,  10},
  {  8,  12}, {  5,  10}, {  1,  12}, {  6,   6}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10},
  {  1,  12}, {  7,   6}, {  1,  12}, {  4,  10}, { 14,  12}, {  4,  10}, {  1,  12}, {  6,   6},
  {  1,   5}, { 30,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, {  2,   0},
  {  3,   0}, {  1,   4}, {  4,   3}, {  1,   6}, {  8,   3}, {  1,   5}, {  1,  12}, {  3,  10},  /* Row 86 */
  {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, {  4,   3}, {  1,  11}, {  5,  10}, {  7,  12},
  {  6,  10}, {  1,  11}, {  5,   6}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12},
  {  6,   6}, {  1,  11}, {  5,  10}, { 14,  12}, {  5,  10}, {  1,  11}, {  6,   6}, { 29,   3},
  {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, {  3,   0},
  {  4,   0}, {  1,   4}, {  4,   3}, {  1,   6}, {  7,   3}, {  1,   5}, {  1,  12}, {  3,  10},  /* Row 87 */
  {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, {  4,   3}, {  1,  13}, {  1,  12}, {  4,  10},
  {  8,  12}, {  5,  10}, {  1,  12}, {  5,   6}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10},
  {  1,  12}, {  6,   6}, {  1,  12}, {  4,  10}, { 16,  12}, {  4,  10}, {  1,  12}, {  6,   6},
  {  1,   5}, { 27,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, {  4,   0},
  {  5,   0}, {  1,   4}, {  4,   3}, {  1,   6}, {  6,   3}, {  1,   5}, {  1,  12}, {  3,  10},  /* Row 88 */
  {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, {  5,   3}, {  1,  11}, {  5,  10}, {  7,  12},
  {  6,  10}, {  1,  11}, {  4,   6}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12},
  {  5,   6}, {  1,  11}, {  5,  10}, { 16,  12}, {  5,  10}, {  1,  11}, {  6,   6}, { 26,   3},
  {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, {  5,   0},
  {  6,   0}, {  1,   4}, {  4,   3}, {  1,   6}, {  5,   3}, {  1,   5}, {  1,  12}, {  3,  10},  /* Row 89 */
  {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, {  5,   3}, {  1,  13}, {  1,  12}, {  4,  10},
  {  8,  12}, {  5,  10}, {  1,  12}, {  4,   6}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10},
  {  1,  12}, {  5,   6}, {  1,  12}, {  4,  10}, {  9,  12}, {  1,  10}, {  8,  12}, {  4,  10},
  {  1,  12}, {  6,   6}, {  1,   5}, { 24,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3},
  {  1,   4}, {  6,   0},
  {  7,   0}, {  1,   4}, {  4,   3}, {  1,   6}, {  4,   3}, {  1,   5}, {  1,  12}, {  3,  10},  /* Row 90 */
  {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, {  6,   3}, {  1,  11}, {  5,  10}, {  7,  12},
  {  6,  10}, {  1,  11}, {  3,   6}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12},
  {  4,   6}, {  1,  11}, {  5,  10}, {  8,  12}, {  2,  10}, {  8,  12}, {  5,  10}, {  1,  11},
  {  6,   6}, { 23,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, {  7,   0},
  {  8,   0}, {  1,   4}, {  4,   3}, {  1,   6}, {  3,   3}, {  1,   5}, {  1,  12}, {  3,  10},  /* Row 91 */
  {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, {  6,   3}, {  1,  13}, {  1,  12}, {  4,  10},
  {  8,  12}, {  5,  10}, {  1,  12}, {  3,   6}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10},
  {  1,  12}, {  4,   6}, {  1,  12}, {  4,  10}, {  9,  12}, {  3,  10}, {  8,  12}, {  4,  10},
  {  1,  12}, {  6,   6}, {  1,   5}, { 21,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3},
  {  1,   4}, {  8,   0},
  {  9,   0}, {  1,   4}, {  4,   3}, {  1,   6}, {  2,   3}, {  1,   5}, {  1,  12}, {  3,  10},  /* Row 92 */
  {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, {  7,   3}, {  1,  11}, {  5,  10}, {  7,  12},
  {  6,  10}, {  1,  11}, {  2,   6}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12},
  {  3,   6}, {  1,  11}, {  5,  10}, {  8,  12}, {  4,  10}, {  8,  12}, {  5,  10}, {  1,  11},
  {  6,   6}, { 20,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, {  9,   0},
  { 10,   0}, {  1,   4}, {  4,   3}, {  1,   6}, {  1,   3}, {  1,   5}, {  1,  12}, {  3,  10},  /* Row 93 */
  {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, {  7,   3}, {  1,  13}, {  1,  12}, {  4,  10},
  {  8,  12}, {  5,  10}, {  1,  12}, {  2,   6}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10},
  {  1,  12}, {  3,   6}, {  1,  12}, {  4,  10}, {  9,  12}, {  5,  10}, {  8,  12}, {  4,  10},
  {  1,  12}, {  6,   6}, {  1,   5}, { 18,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3},
  {  1,   4}, { 10,   0},
  { 11,   0}, {  1,   4}, {  4,   3}, {  1,   6}, {  1,  13}, {  1,  12}, {  3,  10}, {  8,  12},  /* Row 94 */
  {  3,  10}, {  1,  12}, {  7,   6}, {  8,   3}, {  1,  11}, {  5,  10}, {  7,  12}, {  6,  10},
  {  1,  11}, {  1,   6}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12}, {  2,   6},
  {  1,  11}, {  5,  10}, {  8,  12}, {  6,  10}, {  8,  12}, {  5,  10}, {  1,  11}, {  6,   6},
  { 17,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 11,   0},
  { 12,   0}, {  1,   4}, {  4,   3}, {  1,   6}, {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10},  /* Row 95 */
  {  1,  12}, {  7,   6}, {  8,   3}, {  1,  13}, {  1,  12}, {  4,  10}, {  8,  12}, {  5,  10},
  {  1,  12}, {  1,  21}, {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12}, {  2,   6},
  {  1,  12}, {  4,  10}, {  9,  12}, {  7,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  6,   6},
  {  1,   5}, { 15,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 12,   0},
  { 13,   0}, {  1,   4}, {  3,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10},  /* Row 96 */
  {  1,  12}, {  7,   6}, {  9,   3}, {  1,  11}, {  5,  10}, {  7,  12}, {  6,  10}, {  1,  11},
  {  1,  12}, {  3,  10}, {  7,  12}, {  3,  10}, {  1,  12}, {  1,   6}, {  1,  11}, {  5,  10},
  {  8,  12}, {  8,  10}, {  8,  12}, {  5,  10}, {  1,  11}, {  6,   6}, { 14,   3}, {  1,   7},
  {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 13,   0},
  { 14,   0}, {  1,   4}, {  2,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10},  /* Row 97 */
  {  1,  12}, {  7,   6}, {  9,   3}, {  1,  13}, {  1,  12}, {  4,  10}, {  8,  12}, {  5,  10},
  {  1,  12}, {  4,  10}, {  7,  12}, {  3,  10}, {  1,  12}, {  1,  21}, {  1,  12}, {  4,  10},
  {  9,  12}, {  4,  10}, {  1,  12}, {  4,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  6,   6},
  {  1,   5}, { 12,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 14,   0},
  { 15,   0}, {  1,   4}, {  1,   3}, {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10},  /* Row 98 */
  {  1,  12}, {  7,   6}, { 10,   3}, {  1,  11}, {  5,  10}, {  7,  12}, { 10,  10}, {  7,  12},
  {  3,  10}, {  1,  12}, {  1,  11}, {  5,  10}, {  8,  12}, {  5,  10}, {  1,  11}, {  1,  12},
  {  3,  10}, {  8,  12}, {  5,  10}, {  1,  11}, {  6,   6}, { 11,   3}, {  1,   7}, {  1,   2},
  {  1,   5}, {  1,   3}, {  1,   4}, { 15,   0},
  { 16,   0}, {  1,   4}, {  1,   5}, {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10}, {  1,  12},  /* Row 99 */
  {  7,   6}, { 10,   3}, {  1,  13}, {  1,  12}, {  4,  10}, {  8,  12}, {  9,  10}, {  7,  12},
  {  4,  10}, {  1,  12}, {  4,  10}, {  9,  12}, {  4,  10}, {  1,  12}, {  1,   6}, {  1,  11},
  {  4,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  6,   6}, {  1,   5}, {  9,   3}, {  1,   7},
  {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 16,   0},
  { 17,   0}, {  1,   4}, {  1,  12}, {  3,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6},  /* Row 100 */
  { 11,   3}, {  1,  11}, {  5,  10}, {  7,  12}, {  9,  10}, {  7,  12}, {  9,  10}, {  8,  12},
  {  5,  10}, {  1,  11}, {  2,   6}, {  1,  12}, {  3,  10}, {  8,  12}, {  5,  10}, {  1,  11},
  {  6,   6}, {  8,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 17,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, { 11,   3}, {  1,  13},  /* Row 101 */
  {  1,  12}, {  4,  10}, {  8,  12}, {  8,  10}, {  7,  12}, {  8,  10}, {  9,  12}, {  4,  10},
  {  1,  12}, {  3,   6}, {  1,  11}, {  4,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  6,   6},
  {  1,   5}, {  6,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 18,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, { 12,   3}, {  1,  11},  /* Row 102 */
  {  5,  10}, {  7,  12}, {  8,  10}, {  7,  12}, {  8,  10}, {  8,  12}, {  5,  10}, {  1,  11},
  {  4,   6}, {  1,  12}, {  3,  10}, {  8,  12}, {  5,  10}, {  1,  11}, {  6,   6}, {  5,   3},
  {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 19,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, { 12,   3}, {  1,  13},  /* Row 103 */
  {  1,  12}, {  4,  10}, {  8,  12}, {  7,  10}, {  7,  12}, {  7,  10}, {  9,  12}, {  4,  10},
  {  1,  12}, {  5,   6}, {  1,  11}, {  4,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  6,   6},
  {  1,   5}, {  3,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 20,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, { 13,   3}, {  1,  11},  /* Row 104 */
  {  5,  10}, {  7,  12}, {  7,  10}, {  7,  12}, {  7,  10}, {  8,  12}, {  5,  10}, {  1,  11},
  {  6,   6}, {  1,  12}, {  3,  10}, {  8,  12}, {  5,  10}, {  1,  11}, {  6,   6}, {  2,   3},
  {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 21,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, { 13,   3}, {  1,  13},  /* Row 105 */
  {  1,  12}, {  4,  10}, {  8,  12}, {  6,  10}, {  7,  12}, {  6,  10}, {  9,  12}, {  4,  10},
  {  1,  12}, {  7,   6}, {  1,  11}, {  4,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  7,   6},
  {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 22,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, { 14,   3}, {  1,  11},  /* Row 106 */
  {  5,  10}, {  7,  12}, {  6,  10}, {  7,  12}, {  6,  10}, {  8,  12}, {  5,  10}, {  1,  11},
  {  7,   6}, {  1,  13}, {  1,  12}, {  3,  10}, {  8,  12}, {  5,  10}, {  1,  11}, {  6,   6},
  {  1,  18}, {  1,   5}, {  1,   3}, {  1,   4}, { 23,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, { 14,   3}, {  1,  13},  /* Row 107 */
  {  1,  12}, {  4,  10}, {  8,  12}, {  5,  10}, {  7,  12}, {  5,  10}, {  9,  12}, {  4,  10},
  {  1,  12}, {  7,   6}, {  1,   5}, {  1,   3}, {  1,  11}, {  4,  10}, {  8,  12}, {  4,  10},
  {  1,  12}, {  7,   6}, {  1,   3}, {  1,   4}, { 24,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, { 15,   3}, {  1,  11},  /* Row 108 */
  {  5,  10}, {  7,  12}, {  5,  10}, {  7,  12}, {  5,  10}, {  8,  12}, {  5,  10}, {  1,  11},
  {  7,   6}, {  2,   3}, {  1,  13}, {  1,  12}, {  3,  10}, {  8,  12}, {  5,  10}, {  1,  11},
  {  5,   6}, {  1,  20}, {  1,   1}, { 25,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, { 15,   3}, {  1,  13},  /* Row 109 */
  {  1,  12}, {  4,  10}, {  8,  12}, {  4,  10}, {  7,  12}, {  4,  10}, {  9,  12}, {  4,  10},
  {  1,  12}, {  7,   6}, {  1,   5}, {  3,   3}, {  1,  11}, {  4,  10}, {  8,  12}, {  4,  10},
  {  1,  12}, {  4,   6}, {  1,  20}, {  2,   2}, { 25,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, { 16,   3}, {  1,  11},  /* Row 110 */
  {  5,  10}, {  7,  12}, {  4,  10}, {  7,  12}, {  4,  10}, {  8,  12}, {  5,  10}, {  1,  11},
  {  7,   6}, {  4,   3}, {  1,  13}, {  1,  12}, {  3,  10}, {  8,  12}, {  5,  10}, {  1,  11},
  {  2,   6}, {  1,  20}, {  3,   2}, {  1,  17}, { 24,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, { 16,   3}, {  1,  13},  /* Row 111 */
  {  1,  12}, {  4,  10}, {  8,  12}, {  3,  10}, {  7,  12}, {  3,  10}, {  9,  12}, {  4,  10},
  {  1,  12}, {  7,   6}, {  1,   5}, {  5,   3}, {  1,  11}, {  4,  10}, {  8,  12}, {  4,  10},
  {  1,  12}, {  1,  21}, {  1,  20}, {  5,   2}, { 24,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, { 17,   3}, {  1,  11},  /* Row 112 */
  {  5,  10}, {  7,  12}, {  3,  10}, {  7,  12}, {  3,  10}, {  8,  12}, {  5,  10}, {  1,  11},
  {  7,   6}, {  6,   3}, {  1,  13}, {  1,  12}, {  3,  10}, {  8,  12}, {  5,  10}, {  1,  14},
  {  6,   2}, {  1,  17}, { 23,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, { 17,   3}, {  1,  13},  /* Row 113 */
  {  1,  12}, {  4,  10}, {  8,  12}, {  2,  10}, {  7,  12}, {  2,  10}, {  9,  12}, {  4,  10},
  {  1,  12}, {  7,   6}, {  1,   5}, {  7,   3}, {  1,  11}, {  4,  10}, {  8,  12}, {  4,  10},
  {  1,  12}, {  1,  18}, {  6,   2}, { 23,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, { 18,   3}, {  1,  11},  /* Row 114 */
  {  5,  10}, {  7,  12}, {  2,  10}, {  7,  12}, {  2,  10}, {  8,  12}, {  5,  10}, {  1,  11},
  {  7,   6}, {  8,   3}, {  1,  13}, {  1,  12}, {  3,  10}, {  8,  12}, {  5,  10}, {  1,  14},
  {  6,   2}, {  1,  17}, { 22,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, { 18,   3}, {  1,  13},  /* Row 115 */
  {  1,  12}, {  4,  10}, {  8,  12}, {  1,  10}, {  7,  12}, {  1,  10}, {  9,  12}, {  4,  10},
  {  1,  12}, {  7,   6}, {  1,   5}, {  9,   3}, {  1,  11}, {  4,  10}, {  8,  12}, {  4,  10},
  {  1,  12}, {  1,  18}, {  6,   2}, { 22,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   6}, { 19,   3}, {  1,  11},  /* Row 116 */
  {  5,  10}, {  7,  12}, {  1,  10}, {  7,  12}, {  1,  10}, {  8,  12}, {  5,  10}, {  1,  11},
  {  7,   6}, { 10,   3}, {  1,  13}, {  1,  12}, {  3,  10}, {  8,  12}, {  5,  10}, {  1,  14},
  {  6,   2}, {  1,  17}, { 21,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  1,  18}, {  6,   6}, { 19,   3},  /* Row 117 */
  {  1,  13}, {  1,  12}, {  4,  10}, { 24,  12}, {  4,  10}, {  1,  12}, {  7,   6}, {  1,   5},
  { 11,   3}, {  1,  11}, {  4,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  1,  18}, {  6,   2},
  { 21,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  1,   2}, {  1,  20}, {  5,   6},  /* Row 118 */
  { 20,   3}, {  1,  11}, {  5,  10}, { 22,  12}, {  5,  10}, {  1,  11}, {  7,   6}, { 12,   3},
  {  1,  13}, {  1,  12}, {  3,  10}, {  8,  12}, {  5,  10}, {  1,  14}, {  6,   2}, {  1,  17},
  { 20,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  2,   2}, {  1,  20}, {  5,   6},  /* Row 119 */
  { 19,   3}, {  1,  13}, {  1,  12}, {  4,  10}, { 22,  12}, {  4,  10}, {  1,  12}, {  7,   6},
  {  1,   5}, { 13,   3}, {  1,  11}, {  4,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  1,  18},
  {  6,   2}, { 20,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  3,   2}, {  1,  20}, {  3,   6},  /* Row 120 */
  {  1,   3}, {  1,   6}, { 19,   3}, {  1,  11}, {  5,  10}, { 20,  12}, {  5,  10}, {  1,  11},
  {  7,   6}, { 14,   3}, {  1,  13}, {  1,  12}, {  3,  10}, {  8,  12}, {  5,  10}, {  1,  14},
  {  6,   2}, {  1,  17}, { 19,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  4,   2}, {  1,  20}, {  2,   6},  /* Row 121 */
  {  2,   3}, {  1,   6}, { 18,   3}, {  1,  13}, {  1,  12}, {  4,  10}, { 20,  12}, {  4,  10},
  {  1,  12}, {  7,   6}, {  1,   5}, { 14,   3}, {  1,   7}, {  1,  25}, {  4,  10}, {  8,  12},
  {  4,  10}, {  1,  12}, {  1,  18}, {  6,   2}, { 19,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  5,   2}, {  1,  20}, {  1,   6},  /* Row 122 */
  {  3,   3}, {  1,   6}, { 18,   3}, {  1,  11}, {  5,  10}, { 18,  12}, {  5,  10}, {  1,  11},
  {  7,   6}, { 14,   3}, {  1,   7}, {  1,   2}, {  1,  13}, {  1,  12}, {  3,  10}, {  8,  12},
  {  5,  10}, {  1,  14}, {  6,   2}, {  1,  17}, { 18,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  6,   2}, {  1,  20}, {  4,   3},  /* Row 123 */
  {  1,   6}, { 17,   3}, {  1,  13}, {  1,  12}, {  4,  10}, { 18,  12}, {  4,  10}, {  1,  12},
  {  7,   6}, {  1,   5}, { 13,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,  20},
  {  4,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  1,  18}, {  6,   2}, { 18,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   2}, {  1,   0}, {  4,   3},  /* Row 124 */
  {  1,   6}, { 17,   3}, {  1,  11}, {  5,  10}, { 16,  12}, {  5,  10}, {  1,  11}, {  7,   6},
  { 13,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, {  1,  17}, {  4,  10},
  {  8,  12}, {  5,  10}, {  1,  14}, {  6,   2}, {  1,  17}, { 17,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   2}, {  1,   0}, {  1,   4},  /* Row 125 */
  {  4,   3}, {  1,   6}, { 16,   3}, {  1,  13}, {  1,  12}, {  4,  10}, { 16,  12}, {  4,  10},
  {  1,  12}, {  7,   6}, {  1,   5}, { 12,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3},
  {  1,   4}, {  2,   0}, {  1,   9}, {  4,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  1,  18},
  {  6,   2}, { 17,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   2}, {  2,   0}, {  1,   4},  /* Row 126 */
  {  4,   3}, {  1,   6}, { 16,   3}, {  1,  11}, {  5,  10}, { 14,  12}, {  5,  10}, {  1,  11},
  {  7,   6}, { 12,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, {  4,   0},
  {  4,  10}, {  8,  12}, {  5,  10}, {  1,  14}, {  6,   2}, {  1,  17}, { 16,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   2}, {  3,   0}, {  1,   4},  /* Row 127 */
  {  4,   3}, {  1,   6}, { 15,   3}, {  1,  13}, {  1,  12}, {  4,  10}, { 14,  12}, {  4,  10},
  {  1,  12}, {  7,   6}, {  1,   5}, { 11,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3},
  {  1,   4}, {  5,   0}, {  1,   9}, {  4,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  1,  18},
  {  6,   2}, { 16,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   2}, {  4,   0}, {  1,   4},  /* Row 128 */
  {  4,   3}, {  1,   6}, { 15,   3}, {  1,  11}, {  5,  10}, { 12,  12}, {  5,  10}, {  1,  11},
  {  7,   6}, { 11,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, {  7,   0},
  {  4,  10}, {  8,  12}, {  5,  10}, {  1,  14}, {  6,   2}, {  1,  17}, { 15,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   2}, {  5,   0}, {  1,   4},  /* Row 129 */
  {  4,   3}, {  1,   6}, { 14,   3}, {  1,  13}, {  1,  12}, {  4,  10}, { 12,  12}, {  4,  10},
  {  1,  12}, {  7,   6}, {  1,   5}, { 10,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3},
  {  1,   4}, {  8,   0}, {  1,   9}, {  4,  10}, {  8,  12}, {  4,  10}, {  1,  12}, {  1,  18},
  {  6,   2}, { 15,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   2}, {  6,   0}, {  1,   4},  /* Row 130 */
  {  4,   3}, {  1,   6}, { 14,   3}, {  1,  11}, {  5,  10}, { 10,  12}, {  5,  10}, {  1,  11},
  {  7,   6}, { 10,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 10,   0},
  {  4,  10}, {  8,  12}, {  5,  10}, {  1,  14}, {  6,   2}, {  1,  26}, { 14,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   2}, {  7,   0}, {  1,   4},  /* Row 131 */
  {  4,   3}, {  1,   6}, { 13,   3}, {  1,  13}, {  1,  12}, {  4,  10}, { 10,  12}, {  4,  10},
  {  1,  12}, {  7,   6}, {  1,   5}, {  9,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3},
  {  1,   4}, { 11,   0}, {  1,   9}, {  4,  10}, {  8,  12}, {  4,  10}, {  1,  14}, {  6,   2},
  {  1,  17}, { 14,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   2}, {  8,   0}, {  1,   4},  /* Row 132 */
  {  4,   3}, {  1,   6}, { 13,   3}, {  1,  11}, {  5,  10}, {  8,  12}, {  5,  10}, {  1,  11},
  {  7,   6}, {  9,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 13,   0},
  {  4,  10}, {  8,  12}, {  4,  10}, {  1,  14}, {  7,   2}, { 14,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   2}, {  9,   0}, {  1,   4},  /* Row 133 */
  {  4,   3}, {  1,   6}, { 12,   3}, {  1,  13}, {  1,  12}, {  4,  10}, {  8,  12}, {  4,  10},
  {  1,  12}, {  7,   6}, {  1,   5}, {  8,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3},
  {  1,   4}, { 14,   0}, {  1,   9}, {  4,  10}, {  7,  12}, {  3,  10}, {  1,  12}, {  1,  19},
  {  7,   2}, {  1,  26}, { 13,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   2}, { 10,   0}, {  1,   4},  /* Row 134 */
  {  4,   3}, {  1,   6}, { 12,   3}, {  1,  11}, {  5,  10}, {  6,  12}, {  5,  10}, {  1,  11},
  {  7,   6}, {  8,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 16,   0},
  {  4,  10}, {  6,  12}, {  4,  10}, {  1,  12}, {  8,   2}, {  1,  17}, { 13,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   2}, { 11,   0}, {  1,   4},  /* Row 135 */
  {  4,   3}, {  1,   6}, { 11,   3}, {  1,  13}, {  1,  12}, {  5,  10}, {  4,  12}, {  5,  10},
  {  1,  12}, {  7,   6}, {  1,   5}, {  7,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3},
  {  1,   4}, { 17,   0}, {  1,   9}, {  4,  10}, {  4,  12}, {  5,  10}, {  1,  14}, {  8,   2},
  {  1,  17}, { 13,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   2}, { 12,   0}, {  1,   4},  /* Row 136 */
  {  4,   3}, {  1,   6}, { 11,   3}, {  1,  11}, { 14,  10}, {  1,  11}, {  7,   6}, {  7,   3},
  {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 19,   0}, { 12,  10}, {  1,  12},
  {  1,  18}, {  8,   2}, {  1,   1}, { 13,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   2}, { 13,   0}, {  1,   4},  /* Row 137 */
  {  4,   3}, {  1,   6}, { 10,   3}, {  1,  13}, {  1,  12}, { 12,  10}, {  1,  12}, {  7,   6},
  {  1,   5}, {  6,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 20,   0},
  {  1,   9}, { 10,  10}, {  1,  12}, { 10,   2}, { 14,   0},
  { 18,   0}, {  4,  10}, {  8,  12}, {  3,  10}, {  1,  12}, {  7,   2}, { 14,   0}, {  1,   4},  /* Row 138 */
  {  4,   3}, {  1,   6}, { 10,   3}, {  1,  11}, { 12,  10}, {  1,  11}, {  7,   6}, {  6,   3},
  {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 22,   0}, {  9,  10}, {  1,  12},
  { 10,   2}, {  1,  17}, { 14,   0},
  { 18,   0}, {  4,  10}, { 12,  12}, {  7,   2}, { 15,   0}, {  1,   4}, {  4,   3}, {  1,   6},  /* Row 139 */
  {  9,   3}, {  1,   5}, {  1,  15}, { 10,  10}, {  1,  15}, {  7,   6}, {  1,   5}, {  5,   3},
  {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 23,   0}, {  1,  17}, {  1,  14},
  {  5,  10}, {  1,  12}, {  1,  10}, {  1,  19}, { 10,   2}, { 15,   0},
  { 25,   0}, {  1,  20}, {  4,  18}, { 11,   2}, { 16,   0}, {  1,   4}, {  4,   3}, {  1,   6},  /* Row 140 */
  {  9,   3}, {  1,   5}, {  1,  11}, {  1,  12}, {  6,  10}, {  1,  12}, {  1,  11}, {  8,   6},
  {  5,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 27,   0}, {  2,   9},
  {  1,  19}, {  1,  14}, {  1,  18}, { 11,   2}, {  1,  17}, { 15,   0},
  { 25,   0}, { 16,   2}, { 17,   0}, {  1,   4}, {  4,   3}, {  1,   6}, { 10,   3}, {  1,  13},  /* Row 141 */
  {  1,  11}, {  4,  12}, {  1,  11}, {  9,   6}, {  5,   3}, {  1,   7}, {  1,   2}, {  1,   5},
  {  1,   3}, {  1,   4}, { 30,   0}, {  1,   1}, { 13,   2}, { 16,   0},
  { 25,   0}, { 16,   2}, { 18,   0}, {  1,   4}, {  4,   3}, {  1,   6}, { 11,   3}, {  1,   5},  /* Row 142 */
  {  1,  13}, { 11,   6}, {  5,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4},
  { 32,   0}, {  1,  17}, { 11,   2}, { 17,   0},
  { 25,   0}, { 16,   2}, { 19,   0}, {  1,   4}, {  4,   3}, {  1,   6}, { 13,   3}, {  1,   5},  /* Row 143 */
  {  7,   6}, {  6,   3}, {  1,   7}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 34,   0},
  { 10,   2}, { 18,   0},
  { 25,   0}, { 16,   2}, { 20,   0}, {  1,   4}, {  4,   3}, {  1,   6}, { 25,   3}, {  1,   7},  /* Row 144 */
  {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 35,   0}, {  1,   1}, {  7,   2}, {  1,  17},
  { 19,   0},
  { 25,   0}, { 16,   2}, { 21,   0}, {  1,   4}, {  4,   3}, {  1,   6}, { 23,   3}, {  1,   7},  /* Row 145 */
  {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 64,   0},
  { 25,   0}, { 16,   2}, { 22,   0}, {  1,   4}, {  4,   3}, {  1,   6}, { 21,   3}, {  1,   7},  /* Row 146 */
  {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 65,   0},
  { 64,   0}, {  1,   4}, {  4,   3}, {  1,   6}, { 19,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 147 */
  {  1,   3}, {  1,   4}, { 66,   0},
  { 65,   0}, {  1,   4}, {  4,   3}, {  1,   6}, { 17,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 148 */
  {  1,   3}, {  1,   4}, { 67,   0},
  { 66,   0}, {  1,   4}, {  4,   3}, {  1,   6}, { 15,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 149 */
  {  1,   3}, {  1,   4}, { 68,   0},
  { 67,   0}, {  1,   4}, {  4,   3}, {  1,   6}, { 13,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 150 */
  {  1,   3}, {  1,   4}, { 69,   0},
  { 68,   0}, {  1,   4}, {  4,   3}, {  1,   6}, { 11,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 151 */
  {  1,   3}, {  1,   4}, { 70,   0},
  { 69,   0}, {  1,   4}, {  4,   3}, {  1,   6}, {  9,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 152 */
  {  1,   3}, {  1,   4}, { 71,   0},
  { 70,   0}, {  1,   4}, {  4,   3}, {  1,   6}, {  7,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 153 */
  {  1,   3}, {  1,   4}, { 72,   0},
  { 71,   0}, {  1,   4}, {  4,   3}, {  1,   6}, {  5,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 154 */
  {  1,   3}, {  1,   4}, { 73,   0},
  { 72,   0}, {  1,   4}, {  4,   3}, {  1,   6}, {  3,   3}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 155 */
  {  1,   3}, {  1,   4}, { 74,   0},
  { 73,   0}, {  1,   4}, {  4,   3}, {  1,   6}, {  1,   5}, {  1,   7}, {  1,   2}, {  1,   5},  /* Row 156 */
  {  1,   3}, {  1,   4}, { 75,   0},
  { 74,   0}, {  1,   4}, {  5,   3}, {  1,   2}, {  1,   5}, {  1,   3}, {  1,   4}, { 76,   0},  /* Row 157 */
  { 75,   0}, {  1,   4}, {  2,   3}, {  1,   5}, {  1,  20}, {  1,   5}, {  1,   3}, {  1,   4},  /* Row 158 */
  { 77,   0},
  { 76,   0}, {  1,   4}, {  4,   3}, {  1,   4}, { 78,   0}                                       /* Row 159 */
};

#  endif
#else
# error "Unsupported pixel format"
#endif

/********************************************************************************************
 * Public Data
 ********************************************************************************************/

/********************************************************************************************
 * Private Functions
 ********************************************************************************************/

/********************************************************************************************
 * Public Functions
 ********************************************************************************************/

/********************************************************************************************
 * Name:nximage_bgcolor
 *
 * Description:
 *   Return the color of the background.  In this case, we know that this is the first
 *   encoded color in the look-up table.
 *
 ********************************************************************************************/

nxgl_mxpixel_t nximage_bgcolor(void)
{
  return g_lut[0];
}

/********************************************************************************************
 * Name:nximage_avgcolor
 *
 * Description:
 *   Take the average of two pixel RGB values.
 *
 ********************************************************************************************/

nxgl_mxpixel_t nximage_avgcolor(nxgl_mxpixel_t color1, nxgl_mxpixel_t color2)
{
#ifdef CONFIG_EXAMPLES_NXIMAGE_GREYSCALE

  return (nxgl_mxpixel_t)(((unsigned int)color1 + (unsigned int)color2) >> 1);

#else /* CONFIG_EXAMPLES_NXIMAGE_GREYSCALE */
  unsigned int r1;
  unsigned int g1;
  unsigned int b1;
  unsigned int r2;
  unsigned int g2;
  unsigned int b2;

#if CONFIG_EXAMPLES_NXIMAGE_BPP == 24

  /* RGB24 (8-8-8) Colors */

  /* Demultiplex */

  r1 = (color1 >> 16) & 0xff; /* 8-bit */
  g1 = (color1 >>  8) & 0xff; /* 8-bit */
  b1 =  color1        & 0xff; /* 8-bit */

  r2 = (color2 >> 16) & 0xff; /* 8-bit */
  g2 = (color2 >>  8) & 0xff; /* 8-bit */
  b2 =  color2        & 0xff; /* 8-bit */

  /* Average */

  r1 = (r1 + r2 + 1) >> 1;    /* 8-bit */
  g1 = (g1 + g2 + 1) >> 1;    /* 8-bit */
  b1 = (b1 + b2 + 1) >> 1;    /* 8-bit */

  /* Clip */

  if (r1 > 0xff)
    {
      r1 = 0xff;
    }

  if (g1 > 0xff)
    {
      g1 = 0xff;
    }

  if (b1 > 0xff)
    {
      b1 = 0xff;
    }

  /* Multiplex */

  color1 = r1 << 16 | g1 << 8 | b1;

#elif CONFIG_EXAMPLES_NXIMAGE_BPP == 16

  /* RGB16 (565) Colors */

  /* Demultiplex */

  r1 = (color1 >> 11) & 0x1f; /* 5-bit */
  g1 = (color1 >>  5) & 0x3f; /* 6-bit */
  b1 =  color1        & 0x1f; /* 5-bit */

  r2 = (color2 >> 11) & 0x1f; /* 5-bit */
  g2 = (color2 >>  5) & 0x3f; /* 6-bit */
  b2 =  color2        & 0x1f; /* 5-bit */

  /* Average */

  r1 = (r1 + r2 + 1) >> 1;    /* 5-bit */
  g1 = (g1 + g2 + 1) >> 1;    /* 6-bit */
  b1 = (b1 + b2 + 1) >> 1;    /* 5-bit */

  /* Clip */

  if (r1 > 0x1f)
    {
      r1 = 0x1f;
    }

  if (g1 > 0x3f)
    {
      g1 = 0x3f;
    }

  if (b1 > 0x1f)
    {
      b1 = 0x1f;
    }

  /* Multiplex */

  color1 = r1 << 11 | g1 << 5 | b1;

#elif CONFIG_EXAMPLES_NXIMAGE_BPP == 8

  /* RGB8 (332) Colors */

  /* Demultiplex */

  r1 = (color1 >>  5) & 0x07; /* 3-bit */
  g1 = (color1 >>  2) & 0x07; /* 3-bit */
  b1 =  color1        & 0x03; /* 2-bit */

  r2 = (color2 >>  5) & 0x07; /* 3-bit */
  g2 = (color2 >>  2) & 0x07; /* 3-bit */
  b2 =  color2        & 0x03; /* 2-bit */

  /* Average */

  r1 = (r1 + r2 + 1) >> 1;    /* 3-bit */
  g1 = (g1 + g2 + 1) >> 1;    /* 3-bit */
  b1 = (b1 + b2 + 1) >> 1;    /* 2-bit */

  /* RGB24 (8-8-8) Colors */

  /* Clip */

  if (r1 > 0x07)
    {
      r1 = 0x07;
    }

  if (g1 > 0x07)
    {
      g1 = 0x07;
    }

  if (b1 > 0x03)
    {
      b1 = 0x03;
    }

  /* Multiplex */

  color1 = r1 << 5 | g1 << 2 | b1;

#else
# error "Unsupported pixel format"
#endif

  return color1;
#endif /* CONFIG_EXAMPLES_NXIMAGE_GREYSCALE */
}

/********************************************************************************************
 * Name: nximage_blitrow
 *
 * Description:
 *   Return the next properly scaled row from the image.
 *
 ********************************************************************************************/

void nximage_blitrow(FAR nxgl_mxpixel_t *run, FAR const void **state)
{
  FAR const struct pix_run_s *pos = *(FAR const struct pix_run_s **)state;
  unsigned int width;
#if defined(CONFIG_EXAMPLES_NXIMAGE_XSCALEp5) || defined(CONFIG_EXAMPLES_NXIMAGE_XSCALE1p5)
  unsigned int nhalfpixels;
  nxgl_mxpixel_t last;
#endif
  unsigned int   nrun;
  nxgl_mxpixel_t color;

  /* NULL positional data means to start over */

  if (!pos)
    {
      pos = g_nuttx;
    }

  /* Process each run-length encoded pixel in the image */

#if defined(CONFIG_EXAMPLES_NXIMAGE_XSCALEp5) || defined(CONFIG_EXAMPLES_NXIMAGE_XSCALE1p5)
  nhalfpixels = 0;
  last        = nximage_bgcolor();
#endif

  for (width = 0; width < SCALED_WIDTH; pos++)
    {
      nrun  = (unsigned int)pos->npix;
      color = g_lut[pos->code];

#if defined(CONFIG_EXAMPLES_NXIMAGE_XSCALEp5) || defined(CONFIG_EXAMPLES_NXIMAGE_XSCALE1p5)
      nhalfpixels += nrun & 1;
#ifdef CONFIG_EXAMPLES_NXIMAGE_XSCALEp5
      nrun >>= 1;
#else
      nrun   = nrun + (nrun >> 1);
#endif
      if (nhalfpixels > 1)
        {
          *run++       = nximage_avgcolor(color, last);
          nhalfpixels -= 2;
          width++;
        }
      last = color;
#elif defined(CONFIG_EXAMPLES_NXIMAGE_XSCALE2p0)
      nrun <<= 1;
#endif
      width += nrun;
      while (nrun-- > 0)
        {
          *run++ = color;
        }
    }
  DEBUGASSERT(width == SCALED_WIDTH);

  /* Save the start of the next row and return success */

  *state = (FAR const void *)pos;
}
