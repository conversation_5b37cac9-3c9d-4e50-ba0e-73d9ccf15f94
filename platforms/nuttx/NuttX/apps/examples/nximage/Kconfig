#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_NXIMAGE
	tristate "NX graphics image example"
	default n
	depends on NX
	select BOARDCTL
	---help---
		Enable the X graphics image example

if EXAMPLES_NXIMAGE

config EXAMPLES_NXIMAGE_PROGNAME
	string "NX Image Program name"
	default "nxhello"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config EXAMPLES_NXIMAGE_PRIORITY
	int "NX Image task priority"
	default 100

config EXAMPLES_NXIMAGE_STACKSIZE
	int "NX Image stack size"
	default DEFAULT_TASK_STACKSIZE

config EXAMPLES_NXIMAGE_BPP
	int "Bits-Per-Pixel"
	default 32
	---help---
		Pixels per pixel to use.  Valid options include 1, 2, 4, 8, 16, 24,
		and 32.  Default is 32.

config EXAMPLES_NXIMAGE_GREYSCALE
	bool "Greyscale (vs RGB)"
	default n
	---help---
		Select to use a greyscale vs RGB color image.  This option is ignored if
		CONFIG_EXAMPLES_NXIMAGE_BPP > 8

config EXAMPLES_NXIMAGE_XSCALEp5
	bool "Scale X by 50%"
	default n
	---help---
		Reduce image scale by 50% of its original size.

config EXAMPLES_NXIMAGE_XSCALE1p0
	bool "Don't scale image."
	default y
	---help---
		Keep original image size.

config EXAMPLES_NXIMAGE_XSCALE1p5
	bool "Scale X by 150%"
	default n
	---help---
		Increase image scale by 50% of its original size.

config EXAMPLES_NXIMAGE_XSCALE2p0
	bool "Scale X by 200%"
	default n
	---help---
		Increase image scale by 200% of its original size.

config EXAMPLES_NXIMAGE_YSCALEp5
	bool "Scale Y by 50%"
	default n
	---help---
		Reduce image scale by 50% of its original size.

config EXAMPLES_NXIMAGE_YSCALE1p0
	bool "Don't scale image."
	default y
	---help---
		Keep original image size.

config EXAMPLES_NXIMAGE_YSCALE1p5
	bool "Scale Y by 150%"
	default n
	---help---
		Increase image scale by 50% of its original size.

config EXAMPLES_NXIMAGE_YSCALE2p0
	bool "Scale Y by 200%"
	default n
	---help---
		Increase image scale by 200% of its original size.

comment "NX Server Options"

config EXAMPLES_NXIMAGE_LISTENER_STACKSIZE
	int "NX Server/Listener Stack Size"
	default DEFAULT_TASK_STACKSIZE
	---help---
		The stacksize to use when creating the NX server.  Default 2048

config EXAMPLES_NXIMAGE_CLIENTPRIO
	int "Client Priority"
	default 100
	---help---
		The client priority.  Default: 100

config EXAMPLES_NXIMAGE_SERVERPRIO
	int "Server Priority"
	default 120
	---help---
		The server priority.  Default: 120

config EXAMPLES_NXIMAGE_LISTENERPRIO
	int "Listener Priority"
	default 80
	---help---
		The priority of the event listener thread. Default 80.

config EXAMPLES_NXIMAGE_NOTIFYSIGNO
	int "Notify Signal Number"
	default 4
	---help---
		The signal number to use with nx_eventnotify().  Default: 4

endif
