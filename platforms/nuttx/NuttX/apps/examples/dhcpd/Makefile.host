############################################################################
# apps/examples/dhcpd/Makefile.host
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

# TOPDIR must be defined on the make command line

include $(APPDIR)/Make.defs

OBJS		= host.hobj dhcpd.hobj
BIN		= dhcpd

HOSTCFLAGS	+= -DCONFIG_NETUTILS_DHCPD_HOST=1
HOSTCFLAGS	+= -DHAVE_SO_REUSEADDR=1
HOSTCFLAGS	+= -DHAVE_SO_BROADCAST=1

VPATH		= $(TOPDIR)/netutils/dhcpd:.

all: $(BIN)
.PHONY: clean context clean_context distclean

$(OBJS): %.hobj: %.c
	$(HOSTCC) -c $(HOSTCFLAGS) $< -o $@

$(BIN): $(OBJS)
	$(HOSTCC) $(HOSTLDFLAGS) $^ -o $@

clean:
	@rm -f $(BIN).* *.hobj *~
