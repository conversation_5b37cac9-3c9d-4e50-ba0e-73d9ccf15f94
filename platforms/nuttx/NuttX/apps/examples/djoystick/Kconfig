#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_DJOYSTICK
	tristate "Discrete joystick example"
	default n
	depends on INPUT_DJOYSTICK
	---help---
		Enable the discrete joystick example

if E<PERSON>AM<PERSON><PERSON>_DJOYSTICK

config EXAMPLES_DJOYSTICK_DEVNAME
	string "Joystick device name"
	default "/dev/djoy0"

config EXAMPLES_DJOYSTICK_SIGNO
	int "Joystick signal"
	default 13

endif
