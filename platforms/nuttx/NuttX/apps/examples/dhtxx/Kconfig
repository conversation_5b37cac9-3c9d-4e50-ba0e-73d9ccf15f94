#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_DHTXX
	tristate "Dhtxx sensor example"
	default n
	---help---
		Enable the dhtxx sensor example

if EXAMPLES_DHTXX

config EXAMPLES_DHTXX_PROGNAME
	string "Program name"
	default "dhtxx"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config EXAMPLES_DHTXX_DEVPATH
	string "Device path"
	default "/dev/hum0"
	---help---
		The device path

config EXAMPLES_DHTXX_PRIORITY
	int "Dhtxx task priority"
	default 100

config EXAMPLES_DHTXX_STACKSIZE
	int "Dhtxx stack size"
	default DEFAULT_TASK_STACKSIZE

endif
