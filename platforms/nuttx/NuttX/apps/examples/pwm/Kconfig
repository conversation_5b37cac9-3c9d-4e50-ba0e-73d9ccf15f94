#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_PWM
	tristate "Pulse width modulation (PWM) example"
	default n
	depends on PWM
	---help---
		Enable the Pulse width modulation (PWM) example

if EXAMPLES_PWM

config EXAMPLES_PWM_DEVPATH
	string "PWM device path"
	default "/dev/pwm0"
	---help---
		The path to the PWM device.  Default: /dev/pwm0

config EXAMPLES_PWM_FREQUENCY
	int "Default PWM frequency"
	default 100
	---help---
		The default PWM frequency.  Default: 100 Hz

config EXAMPLES_PWM_DURATION
	int "Default PWM duration"
	default 5
	---help---
		The default PWM pulse train duration in seconds.  Used only if the current
		pulse count is zero (pulse count is only supported if PWM_PULSECOUNT
		is not defined).  Default: 5 seconds

if PWM_MULTICHAN

config EXAMPLES_PWM_DUTYPCT1
	int "First PWM duty percentage"
	default 50
	range 1 99
	---help---
		The first PWM duty as a percentage.  Default: 50%

config EXAMPLES_PWM_CHANNEL1
	int "First PWM channel number"
	default 1
	range 1 6
	---help---
		The first PWM channel number.  Default: 1

if PWM_NCHANNELS = 2 || PWM_NCHANNELS = 3 || PWM_NCHANNELS = 4 || PWM_NCHANNELS = 5 || PWM_NCHANNELS = 6

config EXAMPLES_PWM_DUTYPCT2
	int "Second PWM duty percentage"
	default 50
	range 1 99
	---help---
		The second PWM duty as a percentage.  Default: 50%

config EXAMPLES_PWM_CHANNEL2
	int "Second PWM channel number"
	default 2
	range 1 6
	---help---
		The second PWM channel number.  Default: 2

endif

if PWM_NCHANNELS = 3 || PWM_NCHANNELS = 4 || PWM_NCHANNELS = 5 || PWM_NCHANNELS = 6

config EXAMPLES_PWM_DUTYPCT3
	int "Third PWM duty percentage"
	default 50
	range 1 99
	---help---
		The third PWM duty as a percentage.  Default: 50%

config EXAMPLES_PWM_CHANNEL3
	int "Third PWM channel number"
	default 3
	range 1 6
	---help---
		The third PWM channel number.  Default: 3

endif

if PWM_NCHANNELS = 4 || PWM_NCHANNELS = 5 || PWM_NCHANNELS = 6

config EXAMPLES_PWM_DUTYPCT4
	int "Fourth PWM duty percentage"
	default 50
	range 1 99
	---help---
		The fourth PWM duty as a percentage.  Default: 50%

config EXAMPLES_PWM_CHANNEL4
	int "Fourth PWM channel number"
	default 4
	range 1 6
	---help---
		The fourth PWM channel number.  Default: 4

endif

if PWM_NCHANNELS = 5 || PWM_NCHANNELS = 6

config EXAMPLES_PWM_DUTYPCT5
	int "Fifth PWM duty percentage"
	default 50
	range 1 99
	---help---
		The fifth PWM duty as a percentage.  Default: 50%

config EXAMPLES_PWM_CHANNEL5
	int "Fifth PWM channel number"
	default 5
	range 1 6
	---help---
		The fifth PWM channel number.  Default: 5

endif

if PWM_NCHANNELS = 6

config EXAMPLES_PWM_DUTYPCT6
	int "Sixth PWM duty percentage"
	default 50
	range 1 99
	---help---
		The sixth PWM duty as a percentage.  Default: 50%

config EXAMPLES_PWM_CHANNEL5
	int "Sixth PWM channel number"
	default 6
	range 1 6
	---help---
		The sixth PWM channel number.  Default: 6

endif

endif

if !PWM_MULTICHAN

config EXAMPLES_PWM_DUTYPCT
	int "Default PWM duty percentage"
	default 50
	range 1 99
	---help---
		The default PWM duty as a percentage.  Default: 50%

config EXAMPLES_PWM_PULSECOUNT
	int "Default pulse count"
	default 0
	depends on PWM_PULSECOUNT
	---help---
		The initial PWM pulse count.  This option is only available if
		PWM_PULSECOUNT is defined.  Default: 0 (i.e., use the duration, not
		the count).

endif

endif
