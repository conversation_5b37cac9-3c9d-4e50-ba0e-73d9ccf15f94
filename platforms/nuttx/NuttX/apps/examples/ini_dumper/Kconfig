#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

if FSUTILS_INIH

config EXAMPLES_INI_DUMPER
	tristate "ini dumper example"
	default n
	---help---
		Enable ini dumper example program which uses inih

if EXAMPLES_INI_DUMPER

config EXAMPLES_INI_DUMPER_PROGNAME
	string "Program name"
	default "ini_dumper"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config EXAMPLES_INI_DUMPER_PRIORITY
	int "ini dumper example task priority"
	default 100

config EXAMPLES_INI_DUMPER_STACKSIZE
	int "ini dumper example stack size"
	default DEFAULT_TASK_STACKSIZE

endif #EXAMPLES_INI_DUMPER
endif #SYSTEM_INI_DUMPER
