#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_MTDPART
	tristate "MTD partition test"
	default n
	depends on MTD_PARTITION && BUILD_FLAT
	select BCH
	---help---
		Enable the MTD partition test example.

		NOTE: This example uses some internal NuttX interfaces and, hence,
		is not available in the kernel build.

if EXAMPLES_MTDPART

config EXAMPLES_MTDPART_ARCHINIT
	bool "Architecture-specific initialization"
	default n
	---help---
		The default is to use the RAM MTD device at drivers/mtd/rammtd.c.
		But an architecture-specific MTD driver can be used instead by
		defining EXAMPLES_MTDPART_ARCHINIT.  In this case, the
		initialization logic will call mtdpart_archinitialize() to obtain
		the MTD driver instance.

config EXAMPLES_MTDPART_NEBLOCKS
	int "Number of erase blocks (simulated)"
	default 32
	depends on !EXAMPLES_MTDPART_ARCHINIT
	---help---
		When EXAMPLES_MTDPART_ARCHINIT is not defined, this test will use
		the RAM MTD device at drivers/mtd/rammtd.c to simulate FLASH.  In
		this case, this value must be provided to give the number of erase
		blocks in MTD RAM device.

		The size of the allocated RAM drive will be:

			EXAMPLES_MTDPART_ERASESIZE * EXAMPLES_MTDPART_NEBLOCKS

config EXAMPLES_MTDPART_NPARTITIONS
	int "Number of partitions"
	default 3
	---help---
		This setting provides the number of partitions to test.  The
		test will divide the reported size of the MTD device into equal-
		sized sub-regions for each test partition.

endif
