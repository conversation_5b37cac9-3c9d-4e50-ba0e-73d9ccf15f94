#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_PTYTEST
	tristate "Pseudo Terminal test example"
	default n
	depends on PSEUDOTERM
	select PSEUDOTERM_SUSV1
	---help---
		Enable the PTY example

if EXAMPLES_PTYTEST

config EXAMPLES_PTYTEST_PROGNAME
	string "Program name"
	default "pts"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config EXAMPLES_PTYTEST_POLL
	bool "Test poll() with data transfers"
	default n

config EXAMPLES_PTYTEST_SERIALDEV
	string "Test serial device"
	default "/dev/ttyS1"

config EXAMPLES_PTYTEST_PRIORITY
	int "PTY_Test task priority"
	default 100

config EXAMPLES_PTYTEST_STACKSIZE
	int "PTYTest stack size"
	default DEFAULT_TASK_STACKSIZE

config EXAMPLES_PTYTEST_DAEMONPRIO
	int "PTY_Test daemon task priority"
	default 100

config EXAMPLES_PTYTEST_WAIT_CONNECTED
  bool "Keep retrying open serial device"
  ---help---
     For USB based serial devices, open will fail
     if the other end is not connected (USB cable unplugged).
     Enabling this option will retry the open() call every second
     until connected.

endif
