#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_POSIXSPAWN
	tristate "posix_spawn Unit Test"
	default n
	---help---
		Enable the posix_spawn() unit test

if EXAMPLES_POSIXSPAWN
config EXAMPLES_POSIXSPAWN_DEVMINOR
	int "ROMFS Minor Device Number"
	default 0
	---help---
		The minor device number of the ROMFS block. For example, the N in /dev/ramN.
		Used for registering the RAM block driver that will hold the ROMFS file system
		containing the ELF executables to be tested.  Default: 0

config EXAMPLES_POSIXSPAWN_DEVPATH
	string "ROMFS Devie Path"
	default "/dev/ram0"
	---help---
		The path to the ROMFS block driver device.  This must match EXAMPLES_POSIXSPAWN_DEVMINOR.
		Used for registering the RAM block driver that will hold the ROMFS file system
		containing the ELF executables to be tested.  Default: "/dev/ram0"

endif
