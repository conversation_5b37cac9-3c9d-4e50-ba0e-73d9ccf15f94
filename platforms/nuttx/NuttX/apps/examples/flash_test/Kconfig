#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_FLASH_TEST
	tristate "SMART FLASH block device test"
	default n
	depends on BUILD_FLAT && MTD_SMART
	---help---
		This logic performs a SMART flash block device test.  This test
		performs a sector allocate, read, write, free and garbage collection
		test on a SMART MTD block device.

		NOTE:  This test uses internal OS interfaces and so is not available
		in the NUTTX kernel build

if EXAMPLES_FLASH_TEST
endif
