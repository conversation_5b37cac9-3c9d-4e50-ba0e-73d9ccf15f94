#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_SENDMAIL
	tristate "Sendmail example"
	default n
	depends on NET_IPv4
	depends on NET_TCP
	select NETUTILS_SMTP
	---help---
		Enable the sendmail example

if EXAMPLES_SENDMAIL

config EXAMPLES_SENDMAIL_NOMAC
	bool "Use Canned MAC Address"
	default n
	---help---
		If the hardware has no MAC address of its own, define this =y to
		provide a bogus address for testing.

config EXAMPLES_SENDMAIL_IPADDR
	hex "Target IP address"
	default 0x0a000002
	---help---
		The target IP address.  Default ******** (0x0a000002)

config EXAMPLES_SENDMAIL_DRIPADDR
	hex "Default Router IP address (Gateway)"
	default 0x0a000001
	---help---
		The default router address. Default ******** (0x0a000001)

config EXAMPLES_SENDMAIL_SERVERADDR
	hex "SMTP Server address"
	default 0x0a000003
	---help---
		The SMTP Server address. Default ******** (0x0a000003)

config EXAMPLES_SENDMAIL_NETMASK
	hex "Network Mask"
	default 0xffffff00
	---help---
		The network mask.  Default: ************* (0xffffff00)

config EXAMPLES_SENDMAIL_PORT
	int "Server Port"
	default 25
	---help---
		The SMTP server's port.  Default 25.

config EXAMPLES_SENDMAIL_RECIPIENT
	string "Recipient email"
	default "Jane Doe <<EMAIL>>"

config EXAMPLES_SENDMAIL_SENDER
	string "Sender email"
	default "John Doe <<EMAIL>>"

config EXAMPLES_SENDMAIL_SUBJECT
	string "Subject"
	default "Test"

config EXAMPLES_SENDMAIL_BODY
	string "Mail Body"
	default "This is a test"

endif
