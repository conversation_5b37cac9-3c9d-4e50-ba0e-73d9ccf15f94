<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
  <head>
    <title>NuttX examples/thttpd</title>
    <link rel="stylesheet" type="text/css" href="style.css">
  </head>
  <body bgcolor="#fffeec" text="black">

  <div class="menu">
  <div class="menubox"><a href="index.html">Front page</a></div>
  <div class="menubox"><a href="cgi-bin/hello">Say Hello</a></div>
  <div class="menubox"><a href="cgi-bin/tasks">Tasks</a></div>
  <br>
  </div>

  <div class="contentblock">
  <p>
    These web pages are served by a port of <a href="http://acme.com/software/thttpd/">THTTPD</a>
    running on top of <a href="http://www.nuttx.org">NuttX</a>.
    NuttX includes an embedded TCP/IP state that derives from the <a href="http://www.sics.se/~adam/uip/">uIP</a>.
  </p>
  <p>
    Click on the links above to exercise THTTPD's CGI capability under NuttX.
    Depending on the configuration, clicking the links will execute the CGI program from either:
  </p>
  <ol>
    <li>An <a href="https://bitbucket.org/nuttx/documentation/src/master/NuttXNxFlat.html">NXFLAT</a> program residing
    in a NuttX ROMFS file system, or</li>
    <li>A built-in application managed by a NuttX BINFS file system.</li>
  </ol>
  </body>
</html>
