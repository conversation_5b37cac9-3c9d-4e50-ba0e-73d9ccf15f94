############################################################################
# apps/examples/thttpd/content/Makefile.nxflat
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

include $(APPDIR)/Make.defs

SUBDIRS = hello tasks
INSTALL_FILES = index.html style.css

THTTPD_DIR = $(APPDIR)/examples/thttpd
CONTENT_DIR = $(THTTPD_DIR)/content
ROMFS_DIR = $(CONTENT_DIR)/romfs
ROMFS_IMG = $(CONTENT_DIR)/romfs.img
ROMFS_SRC = $(CONTENT_DIR)/romfs.c
ROMFSCGI_DIR = $(ROMFS_DIR)/cgi-bin
SYMTAB_SRC = $(CONTENT_DIR)/symtab.c

define DIR_template
$(1)_$(2):
	+$(Q) $(MAKE) -C $(1) $(2) TOPDIR="$(TOPDIR)" APPDIR="$(APPDIR)" CROSSDEV=$(CROSSDEV) CGI_DIR="$(ROMFSCGI_DIR)"
endef

all: $(ROMFS_SRC) $(SYMTAB_SRC)
.PHONY: all clean distclean install context depend

$(foreach DIR, $(SUBDIRS), $(eval $(call DIR_template,$(DIR),clean)))
$(foreach DIR, $(SUBDIRS), $(eval $(call DIR_template,$(DIR),install)))

# Install each program in the romfs directory

install: $(foreach DIR, $(SUBDIRS), $(DIR)_install)
	$(Q) ( for file in $(INSTALL_FILES); do\
		install -m 0644 -D $${file} $(ROMFS_DIR)/$${file}; \
	done; )

# Create the romfs.img file from the populated romfs directory

$(ROMFS_IMG): install
	$(Q) genromfs -f $@.tmp -d $(ROMFS_DIR) -V "THTTPDTEST"
	$(Q) $(call TESTANDREPLACEFILE, $@.tmp, $@)

# Create the romfs.c file from the romfs.img file

$(ROMFS_SRC): $(ROMFS_IMG)
	$(Q) (cd $(CONTENT_DIR) && echo "#include <nuttx/compiler.h>" >$@ && \
	      xxd -i romfs.img | sed -e "s/^unsigned char/const unsigned char aligned_data(4)/g" >>$@)

# Create the exported symbol table list from the derived *-thunk.S files

$(SYMTAB_SRC): install
	$(Q) $(APPDIR)/tools/mksymtab.sh $(CONTENT_DIR) g_thttpd >$@.tmp
	$(Q) $(call TESTANDREPLACEFILE, $@.tmp, $@)

# Nothing special needs to be done during the context phase

context:

# Clean each subdirectory

clean: $(foreach DIR, $(SUBDIRS), $(DIR)_clean)
	$(call DELFILE, $(ROMFS_SRC))
	$(call DELFILE, $(ROMFS_IMG))
	$(call DELFILE, $(SYMTAB_SRC))
	$(Q) rm -rf $(ROMFS_DIR)

distclean: clean

# No dependencies

depend:
