#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_POLL
	tristate "Poll example"
	default n
	depends on PIPES
	---help---
		Enable the poll example

if EXAMPLES_POLL

config EXAMPLES_POLL_NOMAC
	bool "Use Canned MAC Address"
	default n

config EXAMPLES_POLL_IPADDR
	hex "Target IP address"
	default 0x0a000002

config EXAMPLES_POLL_DRIPADDR
	hex "Default Router IP address (Gateway)"
	default 0x0a000001

config EXAMPLES_POLL_NETMASK
	hex "Network Mask"
	default 0xffffff00

endif # EXAMPLES_POLL
