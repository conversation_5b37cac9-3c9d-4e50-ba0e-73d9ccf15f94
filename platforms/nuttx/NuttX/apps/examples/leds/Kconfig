#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_LEDS
	tristate "LED driver example"
	default n
	depends on USERLED
	---help---
		Enable the LED driversexample

if EXAMPLES_LEDS

config EXAMPLES_LEDS_PROGNAME
	string "Program name"
	default "leds"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config EXAMPLES_LEDS_PRIORITY
	int "LED task priority"
	default 100

config EXAMPLES_LEDS_STACKSIZE
	int "LED stack size"
	default DEFAULT_TASK_STACKSIZE

config EXAMPLES_LEDS_DEVPATH
	string "LED device path"
	default "/dev/userleds"

config EXAMPLES_LEDS_LEDSET
	hex "Subset of LEDs to use"
	default 0x0f

endif # EXAMPLES_LEDS
