#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_PWFB
	tristate "NX Per-Window Framebuffer Text Example"
	default n
	depends on NX
	select BOARDCTL
	select NX_RAMBACKED
	---help---
		Enable the NX per-window framebuffer example

		WARNING:  Verbose graphics debug output interferes with this
		test because it introduces some weird timing.  The test probably
		should use nx_synchronize() to keep syncrhonization even with the
		added delays.

if EXAMPLES_PWFB

config EXAMPLES_PWFB_NWINDOWS
	int "Number of Windows"
	default 3
	range 0 3 if NX_SWCURSOR
	range 1 3 if !NX_SWCURSOR

config EXAMPLES_PWFB_DEFAULT_COLORS
	bool "Use Default Colors"
	default y

if !EXAMPLES_PWFB_DEFAULT_COLORS

config EXAMPLES_PWFB_BGCOLOR
	hex "Background Color"
	---help---
		The color of the background.  Default depends on config EXAMPLES_PWFB_BPP.

config EXAMPLES_PWFB_COLOR1
	hex "Color of Window 1"
	---help---
		The color of window 1. Default depends on config EXAMPLES_PWFB_BPP.

config EXAMPLES_PWFB_COLOR2
	hex "Color of Window 2"
	---help---
		The color of window 2. Default depends on config EXAMPLES_PWFB_BPP.

config EXAMPLES_PWFB_COLOR3
	hex "Color of Window 3"
	---help---
		The color of window 3. Default depends on config EXAMPLES_PWFB_BPP.

config EXAMPLES_PWFB_TBCOLOR
	hex "Toolbar Color"
	---help---
		The color of the toolbar. Default depends on config EXAMPLES_PWFB_BPP.

config EXAMPLES_PWFB_FONTCOLOR
	hex "Font Color"
	---help---
		The color of the fonts. Default depends on config EXAMPLES_PWFB_BPP.

endif

config EXAMPLES_PWFB_DEFAULT_FONT
	bool "Use Default Font"
	default y

config EXAMPLES_PWFB_FONTID
	int "Font ID"
	depends on !EXAMPLES_PWFB_DEFAULT_FONT
	---help---
		Selects the font (see font ID numbers in include/nuttx/nx/nxfonts.h)

config EXAMPLES_PWFB_BPP
	int "Bits-Per-Pixel"
	default 32
	---help---
		Pixels per pixel to use.  Valid options include 2, 4, 8, 16, 24,
		and 32.  Default is 32.

config EXAMPLES_PWFB_RATECONTROL
	int "Frame rate control"
	default 100
	range 0 1000
	---help---
		This is the inter-frame period in milliseconds that is used to
		control the framerate.  A value of zero will disable frame controls
		and the rendering will occur as fast as is possible.

		If you run this example with high frame rates, it becomes unstable.
		This is probably a bug in the example:  It may not be accounting
		for for some asynchronous behaviors.

config EXAMPLES_PWFB_VERBOSE
	bool "Verbose output"
	default n

comment "Tasking options"

config EXAMPLES_PWFB_PROGNAME
	string "Program name"
	default "pwfb"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config EXAMPLES_PWFB_CLIENT_STACKSIZE
	int "Example Main Stack Size"
	default DEFAULT_TASK_STACKSIZE
	---help---
		The stacksize to use when starting the example main().
		Default 2048

config EXAMPLES_PWFB_CLIENT_PRIO
	int "Client Priority"
	default 90
	---help---
		The priority to use when staring the example main().  This priority
		should be lower than both the listener and server priorities (See
		CONFIG_NXSTART_SERVERPRIO).  Default: 90

config EXAMPLES_PWFB_LISTENER_STACKSIZE
	int "Listener Stack Size"
	default DEFAULT_TASK_STACKSIZE
	---help---
		The stacksize to use when creating the NX server.  Default 2048

config EXAMPLES_PWFB_LISTENER_PRIO
	int "Listener Priority"
	default 100
	---help---
		The priority of the event listener thread.  This priority should be
		above the client priority but below the server priority (See
		CONFIG_NXSTART_SERVERPRIO).  Default 100.

endif
