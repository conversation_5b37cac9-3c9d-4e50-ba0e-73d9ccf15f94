#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_HIDKBD
	tristate "USB HID keyboard example"
	default n
	---help---
		Enable the USB HID keyboard example

if EXAMPLES_HIDKBD

config EXAMPLES_HIDKBD_PROGNAME
	string "Program name"
	default "hidkbd"
	---help---
		This is the name of the program that will be used when the Nettest
		program is installed.

config EXAMPLES_HIDKBD_STACKSIZE
	int "Task stack size"
	default DEFAULT_TASK_STACKSIZE

config EXAMPLES_HIDKBD_DEFPRIO
	int "Task priority"
	default 100

config EXAMPLES_HIDKBD_DEVNAME
	string "Keyboard Device Name"
	default "/dev/kbda"
	---help---
		Name of keyboard device to be used. Default: "/dev/kbda"

config EXAMPLES_HIDKBD_ENCODED
	bool "Encode Special Keys"
	default y
	depends on HIDKBD_ENCODED && LIBC_KBDCODEC
	---help---
		Decode special key press events in the user buffer.  In this case,
		the example coded will use the interfaces defined in
		include/nuttx/input/kbd_codec.h to decode the returned keyboard
		data.  These special keys include such things as up/down arrows,
		home and end keys, etc.  If this not defined, only 7-bit print-able
		and control ASCII characters will be provided to the user.

endif
