#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_WGET
	tristate "wget example"
	default n
	depends on NET_TCP
	---help---
		Enable the wget example

if EXAMPLES_WGET

config EXAMPLES_WGET_STACKSIZE
	int "wget stack size"
	default DEFAULT_TASK_STACKSIZE
	---help---
		Stack size of "wget" command. If you use SSL connection,
		you should increase this value around 8096.

config EXAMPLES_WGET_URL
	string "File URL"
	default ""
	---help---
		The URL of the file to get

config EXAMPLES_WGET_NOMAC
	bool "Use Canned MAC Address"
	depends on !NSH_NETINIT
	default n

config EXAMPLES_WGET_IPADDR
	hex "Target IP address"
	default 0x0a000002

config EXAMPLES_WGET_DRIPADDR
	hex "Default Router IP address (Gateway)"
	default 0x0a000001

config EXAMPLES_WGET_NETMASK
	hex "Network Mask"
	default 0xffffff00

endif
