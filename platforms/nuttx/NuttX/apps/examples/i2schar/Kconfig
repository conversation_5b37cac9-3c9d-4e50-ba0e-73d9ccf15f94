#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_I2SCHAR
	tristate "I2S character driver test"
	default n
	depends on I2S && AUDIO && DRIVERS_AUDIO && AUDIO_I2SCHAR
	---help---
		Enable the I2S character driver test

if EXAMPLES_I2SCHAR

config EXAMPLES_I2SCHAR_DEVPATH
	string "I2S character device path"
	default "/dev/i2schar0"
	---help---
		The default path to the I2S character device. Default: /dev/i2schar0

config EXAMPLES_I2SCHAR_TX
	bool "Use I2S transmitter"
	default n
	---help---
		This should be set if the I2S device supports a transmitter.

if EXAMPLES_I2SCHAR_TX

config EXAMPLES_I2SCHAR_TXBUFFERS
	int "Number of TX buffers"
	default 4
	---help---
		This is the default number of audio buffers to send before the TX
		transfers terminate.  When both TX and RX transfers terminate, the
		task exits (and, if an NSH builtin, the i2schar command returns).
		This number can be changed from the NSH command line.

config EXAMPLES_I2SCHAR_TXSTACKSIZE
	int "Transmitter thread stack size"
	default DEFAULT_TASK_STACKSIZE
	---help---
		This is the stack size to use when starting the transmitter thread.

endif # EXAMPLES_I2SCHAR_TX

config EXAMPLES_I2SCHAR_RX
	bool "Use I2S receiver"
	default n
	---help---
		This should be set if the I2S device supports a receiver.

if EXAMPLES_I2SCHAR_RX

config EXAMPLES_I2SCHAR_RXBUFFERS
	int "Number of RX buffers"
	default 4
	---help---
		This is the default number of audio buffers to receive before the RX
		transfers terminate.  When both TX and RX transfers terminate, the
		task exits (and, if an NSH builtin, the i2schar command returns).
		This number can be changed from the NSH command line.

config EXAMPLES_I2SCHAR_RXSTACKSIZE
	int "Receiver thread stack size"
	default DEFAULT_TASK_STACKSIZE
	---help---
		This is the stack size to use when starting the receiver thread.

endif # EXAMPLES_I2SCHAR_RX

config EXAMPLES_I2SCHAR_BUFSIZE
	int "Audio payload size"
	default 256
	---help---
		The size of the data payload in one audio buffer in bytes.  Applies
		to both TX and RX audio buffers.

config EXAMPLES_I2SCHAR_DEVINIT
	bool "Architecture-specific device initialization"
	default n
	depends on BUILD_FLAT
	---help---
		Define if architecture-specific I2S device initialize is available.
		If defined, the platform specific code must provide a function
		i2schar_devinit() that will be called each time that this test
		executes.  Not available in the kernel build mode.

endif
