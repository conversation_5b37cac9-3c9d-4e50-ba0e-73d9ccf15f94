#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_BMP180
	tristate "BMP180/280 Barometer sensor example"
	default n
	depends on SENSORS_BMP180 || SENSORS_BMP280
	---help---
		Enable the BMP180/BMP280 example

if EXAMPLES_BMP180

config EXAMPLES_BMP180_PROGNAME
	string "Program name"
	default "bmp180"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config EXAMPLES_BMP180_PRIORITY
	int "BMP180 task priority"
	default 100

config EXAMPLES_BMP180_STACKSIZE
	int "BMP180 stack size"
	default DEFAULT_TASK_STACKSIZE

endif
