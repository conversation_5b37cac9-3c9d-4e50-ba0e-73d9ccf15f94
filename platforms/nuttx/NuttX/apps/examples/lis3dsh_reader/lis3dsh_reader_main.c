/****************************************************************************
 * apps/examples/lis3dsh_reader/lis3dsh_reader_main.c
 *
 *   Copyright (C) 2017 <PERSON><PERSON><PERSON>. All rights reserved.
 *   Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 * 3. Neither the name NuttX nor the names of its contributors may be
 *    used to endorse or promote products derived from this software
 *    without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS
 * OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 * AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
 * ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

#include <sys/ioctl.h>
#include <stdbool.h>
#include <stdlib.h>
#include <stdio.h>
#include <fcntl.h>
#include <sched.h>
#include <errno.h>

#include <debug.h>
#include <time.h>

#include <nuttx/sensors/lis3dsh.h>

/****************************************************************************
 * Public Functions
 ****************************************************************************/

/****************************************************************************
 * lis3dsh_reader_main
 ****************************************************************************/

int main(int argc, FAR char *argv[])
{
  FILE *acc;
  struct acc_data_t
  {
    int16_t x;
    int16_t y;
    int16_t z;
  } acc_data;

  acc = fopen("/dev/acc0", "r");

  for (; ; )
    {
      fread(&acc_data, 6, 1, acc);
      printf("x: %4d  y: %4d  z: %4d            \r",
             acc_data.x, acc_data.y, acc_data.z);
      usleep(300);
    }

  fclose(acc);
  return EXIT_SUCCESS;
}
