/****************************************************************************
 * apps/examples/lua_module/luamod_hello.c
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <lua.h>
#include <lauxlib.h>

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/

static int say_hello(lua_State *L);

/****************************************************************************
 * Private Data
 ****************************************************************************/

static const struct luaL_Reg g_hello[] =
{
  {"say_hello", say_hello},
  {NULL, NULL},
};

/****************************************************************************
 * Private Functions
 ****************************************************************************/

/****************************************************************************
 * Name: say_hello
 *
 *   Push a "Hello World!" string to the Lua interpreter.
 *
 ****************************************************************************/

static int say_hello(lua_State *L)
{
  lua_pushstring(L, "Hello World!");
  return 1;
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/

/****************************************************************************
 * Name: luaopen_hello
 *
 *   Open the "hello" Lua module.
 *
 ****************************************************************************/

int luaopen_hello(lua_State *L)
{
  luaL_newlib(L, g_hello);
  return 1;
}
