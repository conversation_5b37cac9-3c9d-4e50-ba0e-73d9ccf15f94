#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_VEML6070
	tristate "VEML6070 UltraViolet sensor example"
	default n
	depends on SENSORS_VEML6070
	---help---
		Enable the VEML6070 example

if EXAMPLES_VEML6070

config EXAMPLES_VEML6070_PROGNAME
	string "Program name"
	default "veml6070"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config EXAMPLES_VEML6070_PRIORITY
	int "VEML6070 task priority"
	default 100

config EXAMPLES_VEML6070_STACKSIZE
	int "VEML6070 stack size"
	default DEFAULT_TASK_STACKSIZE

endif
