#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

if SYSTEM_EMBEDLOG

config EXAMPLES_EMBEDLOG
	tristate "embedlog example"
	default n
	select EMBEDLOG_ENABLE_OUT_FIL<PERSON>
	select EMBEDLOG_ENABLE_OUT_STDERR
	select EMBEDLOG_ENABLE_OUT_TTY
	select EMBEDLOG_ENABLE_OUT_CUSTOM
	select EMBEDLOG_ENABLE_BINARY_LOGS
	select EMBEDLOG_ENABLE_TIMESTAMP
	select EMBEDLOG_ENABLE_FRACTIONS
	select EMBEDLOG_ENABLE_PREFIX
	select EMBEDLOG_ENABLE_FIN<PERSON><PERSON>
	select EMBEDLOG_ENABLE_COLORS
	---help---
		Enable the "embedlog" example.

if EXAMPLES_EMBEDLOG

config EXAMPLES_EMBEDLOG_PROGNAME
	string "Program name"
	default "embedlog"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config EXAMPLES_EMBEDLOG_PRIORITY
	int "embedlog example task priority"
	default 100

config EXAMPLES_EMBEDLOG_STACKSIZE
	int "embedlog example stack size"
	default DEFAULT_TASK_STACKSIZE

endif #EXAMPLES_EMBEDLOG
endif #SYSTEM_EMBEDLOG
