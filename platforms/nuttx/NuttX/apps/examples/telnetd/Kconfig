#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_TELNETD
	tristate "Telnet daemon example"
	default n
	depends on NETUTILS_TELNETD
	---help---
		Enable the Telnet daemon example

if EXAMPLES_TELNETD

config EXAMPLES_TELNETD_NOMAC
	bool "Use Canned MAC Address"
	default n
	---help---
		If the hardware has no MAC address of its own, define this =y to
		provide a bogus address for testing.

config EXAMPLES_TELNETD_IPADDR
	hex "Target IP address"
	default 0x0a000002
	---help---
		The target IP address.  Default ******** (0x0a000002)

config EXAMPLES_TELNETD_DRIPADDR
	hex "Default Router IP address (Gateway)"
	default 0x0a000001
	---help---
		The default router address. Default ******** (0x0a000001)

config EXAMPLES_TELNETD_NETMASK
	hex "Network Mask"
	default 0xffffff00
	---help---
		The network mask.  Default: ************* (0xffffff00)

config EXAMPLES_TELNETD_DAEMONPRIO
	int "Telnet daemon priority"
	default 100
	---help---
		Priority of the Telnetd server daemon.  Default: 100

config EXAMPLES_TELNETD_DAEMONSTACKSIZE
	int "Telnet daemon stack size"
	default DEFAULT_TASK_STACKSIZE
	---help---
		Stack size allocated for the Telnet daemon. Default: 2048

config EXAMPLES_TELNETD_CLIENTPRIO
	int "Telnet client priority"
	default 100
	---help---
		Priority of the Telnet client.  Default: 100

config EXAMPLES_TELNETD_CLIENTSTACKSIZE
	int "Telnet client stack size"
	default DEFAULT_TASK_STACKSIZE
	---help---
		Stack size allocated for the Telnet client. Default: 2048

endif
