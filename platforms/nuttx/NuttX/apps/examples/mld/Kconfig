#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_MLD
	tristate "MLD example"
	default n
	depends on NET_UDP && NET_MLD
	---help---
		Enable the MLD example

if EXAM<PERSON><PERSON>_MLD

config EXAMPLES_MLD_PROGNAME
	string "Program name"
	default "mld"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config EXAMPLES_MLD_PRIORITY
	int "MLD example task priority"
	default 100

config EXAMPLES_MLD_STACKSIZE
	int "MLD example stack size"
	default DEFAULT_TASK_STACKSIZE

config EXAMPLES_MLD_INIT
	bool "Initialize network"
	default n if NSH_NETINIT
	default y if !NSH_NETINIT
	---help---
		Include logic to initialize the network.  This should not be done if
		the network is already initialized when nettest runs.  This is
		usually the case, for example, when nettest is run as an NSH built-
		in task.

if EXAM<PERSON><PERSON>_MLD_INIT

config EXAMPLES_MLD_IPADDR
	hex "Target Link-Local address"
	default 0x00000002
	range 0x0 0xffff
	---help---
		Last 16-bits of the target link-local, unicast in host order.  Given

			CONFIG_EXAMPLES_MLD_IPADDR=0xXXXX

		the full IPv6 link local address will be fe80::00ff:fe00:XXXX (host order)

config EXAMPLES_MLD_DRIPADDR
	hex "Default Router IP address (Gateway)"
	default 0x00000001
	range 0x0 0xffff
	---help---
		Last 16-bits of the default link-local, unicast in host order.  Given

			CONFIG_EXAMPLES_MLD_DRIPADDR=0xXXXX

		the full IPv6 link local address will be fe80::00ff:fe00:XXXX (host order)

config EXAMPLES_MLD_NOMAC
	bool "Use Canned MAC Address"
	default n

endif # EXAMPLES_MLD_INIT

config EXAMPLES_MLD_GRPADDR
	hex "Group address"
	default 0xE0000181
	range 0x0 0xffff
	---help---
		Last 16-bits of the group link-local, multicast address in host order.  Given

			CONFIG_EXAMPLES_MLD_GRPADDR=0xXXXX

		the full IPv6 link local address will be ff02::0000:0000:XXXX (host order)

endif
