#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_SX127X
	tristate "SX127x example"
	default n
	depends on LPWAN_SX127X

if EXAMPLES_SX127X

config EXAMPLES_SX127X_RFFREQ
	int "SX127x RF frequency"
	default *********

config EXAMPLES_SX127X_TXPOWER
	int "SX127x TX power"
	default 14

config EXAMPLES_SX127X_TXDATA
	int "SX127x TX data length"
	default 5

config EXAMPLES_SX127X_TIME
	int "SX127x demo time, 0 for infinity"
	default 0

config EXAMPLES_SX127X_INTERVAL
	int "SX127x time interval for modem operations"
	default 5

endif
