#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_ALARM
	tristate "RTC alarm driver example"
	default n
	depends on RTC_DRIVER && RTC_ALARM
	---help---
		Enable the RTC driver alarm test

if EXAMPLES_ALARM

config EXAMPLES_ALARM_PROGNAME
	string "Program name"
	default "alarm"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config EXAMPLES_ALARM_PRIORITY
	int "Alarm task priority"
	default 100

config EXAMPLES_ALARM_STACKSIZE
	int "Alarm stack size"
	default DEFAULT_TASK_STACKSIZE

config EXAMPLES_ALARM_DEVPATH
	string "RTC device path"
	default "/dev/rtc0"

config EXAMPLES_ALARM_SIGNO
	int "Alarm signal"
	default 1

endif # EXAMPLES_ALARM
