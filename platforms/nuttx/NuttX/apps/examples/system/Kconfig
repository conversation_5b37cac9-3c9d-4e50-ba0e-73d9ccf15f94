#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_SYSTEM
	tristate "System() example"
	default n
	depends on SYSTEM_SYSTEM
	---help---
		Enable the system() example

if EXAMPL<PERSON>_SYSTEM

config EXAMPLES_SYSTEM_PROGNAME
	string "Program name"
	default "system"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config EXAMPLES_SYSTEM_PRIORITY
	int "System task priority"
	default 100

config EXAMPLES_SYSTEM_STACKSIZE
	int "System stack size"
	default DEFAULT_TASK_STACKSIZE

endif
