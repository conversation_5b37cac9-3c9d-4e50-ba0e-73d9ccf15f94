############################################################################
# apps/examples/usbserial/Makefile.host
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

# TOPDIR must be defined on the make command line

include $(APPDIR)/Make.defs

SRC = host.c
BIN = host$(HOSTEXEEXT)

ifeq ($(CONFIG_EXAMPLES_USBSERIAL_INONLY),y)
DEFINES += -DCONFIG_EXAMPLES_USBSERIAL_INONLY=1
endif
ifeq ($(CONFIG_EXAMPLES_USBSERIAL_OUTONLY),y)
DEFINES += -DCONFIG_EXAMPLES_USBSERIAL_OUTONLY=1
endif
ifeq ($(CONFIG_EXAMPLES_USBSERIAL_ONLYSMALL),y)
DEFINES += -DCONFIG_EXAMPLES_USBSERIAL_ONLYSMALL=1
endif
ifeq ($(CONFIG_EXAMPLES_USBSERIAL_ONLYBIG),y)
DEFINES += -DCONFIG_EXAMPLES_USBSERIAL_ONLYBIG=1
endif
ifeq ($(CONFIG_CDCACM),y)
DEFINES += -DCONFIG_CDCACM=1
endif

all: $(BIN)
.PHONY: clean

$(BIN): $(SRC)
	@$(HOSTCC) $(HOSTCFLAGS) $(DEFINES) $^ -o $@

clean:
	$(call DELFILE,  $(BIN))
	$(call CLEAN)
