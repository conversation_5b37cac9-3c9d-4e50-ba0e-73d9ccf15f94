#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_CAMERA
	tristate "Camera example"
	default n
	---help---
		Enable the camera example

if EXAMPLES_CAMERA

config EXAMPLES_CAMERA_PROGNAME
	string "Program name"
	default "camera"
	---help---
		This is the name of the program that will be use when the NSH ELF
		program is installed.

config EXAMPLES_CAMERA_PRIORITY
	int "Camera task priority"
	default 100

config EXAMPLES_CAMERA_STACKSIZE
	int "Camera stack size"
	default 2048

config EXAMPLES_CAMERA_OUTPUT_LCD
	bool "Output to LCD"
	default n
	---help---
		This enables to output the captured RGB565 image to LCD.

endif
