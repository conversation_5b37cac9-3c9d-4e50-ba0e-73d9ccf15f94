#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_DISCOVER
	tristate "UDP Discovery Example"
	default n
	depends on NET_UDP
	select NETUTILS_DISCOVER
	---help---
		Enable the netutils/discover utility.  This example initializes and
		starts the UDP discover daemon. This daemon is useful for
		discovering devices in local networks, especially with DHCP
		configured devices.  It listens for UDP broadcasts which also can
		include a device class so that groups of devices can be discovered.
		It is also possible to address all classes with a kind of broadcast
		discover.

config EXAMPLES_DISCOVER_DHCPC
	bool "DHCP Client"
	default n
	depends on EXAMPLES_DISCOVER && !NSH_NETINIT
	select NETUTILS_DHCPC
	select NETDB_DNSCLIENT

config EXAMPLES_DISCOVER_NOMAC
	bool "Use Canned MAC Address"
	default n
	depends on EXAMPLES_DISCOVER && !NSH_NETINIT

config EXAMPLES_DISCOVER_IPADDR
	hex "Target IP address"
	default 0x0a000002
	depends on EXAMPLES_DISCOVER && !NSH_NETINIT && !EXAMPLES_DISCOVER_DHCPC

config EXAMPLES_DISCOVER_DRIPADDR
	hex "Default Router IP address (Gateway)"
	default 0x0a000001
	depends on EXAMPLES_DISCOVER && !NSH_NETINIT

config EXAMPLES_DISCOVER_NETMASK
	hex "Network Mask"
	default 0xffffff00
	depends on EXAMPLES_DISCOVER && !NSH_NETINIT
