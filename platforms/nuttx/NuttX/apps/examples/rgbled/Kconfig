#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_RGBLED
	tristate "RGB LED Test"
	default n
	---help---
		Enable the RGB LED example

if EXAMPLES_RGBLED

config EXAMPLES_RGBLED_DEVNAME
	string "RGB LED device name"
	default "/dev/rgbled0"

config EXAMPLES_RGBLED_PROGNAME
	string "Program name"
	default "rgbled"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config EXAMPLES_RGBLED_PRIORITY
	int "RGBLED task priority"
	default 100

config EXAMPLES_RGBLED_STACKSIZE
	int "RGBLED stack size"
	default DEFAULT_TASK_STACKSIZE

endif # EXAMPLES_RGBLED
