#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_ADXL372_TEST
	tristate "ADXL372 test program"
	default n
	---help---
		Enable the ADXL372 sensor SPI test program.

if EXAMPLES_ADXL372_TEST

config EXAMPLES_ADXL372_TEST_PROGNAME
	string "Program name"
	default "adxl372_test"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config EXAMPLES_ADXL372_TEST_PRIORITY
	int "adxl372_test task priority"
	default 100

config EXAMPLES_ADXL372_TEST_STACKSIZE
	int "adxl372_test stack size"
	default DEFAULT_TASK_STACKSIZE

endif
