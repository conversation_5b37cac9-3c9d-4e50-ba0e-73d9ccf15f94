#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_LIBCANARDV1
	tristate "libcandard v1 example"
	default n
	depends on CANUTILS_LIBCANARDV1
	---help---
		Enable the LIBCANARDV1 example

if EXAMPLES_LIBCANARDV1

config EXAMPLES_LIBCANARDV1_DEV
	string "Device"
	default "can0"
	---help---
		The device

config EXAMPLES_LIBCANARDV1_NODE_ID
	int "Node ID"
	default 1
	range 1 127
	---help---
		Specifies the node's ID

config EXAMPLES_LIBCANARDV1_APP_NODE_NAME
	string "Node name"
	default "org.uavcan.libcanardv1.nuttx.demo"
	---help---
		app node name

config EXAMPLES_LIBCANARDV1_NODE_MEM_POOL_SIZE
	int "Node Memory Pool Size"
	default 1024
	---help---
		Specifies the node's memory pool size

config EXAMPLES_LIBCANARDV1_DAEMON_PRIORITY
	int "daemon task priority"
	default 100

config EXAMPLES_LIBCANARDV1_DAEMON_STACK_SIZE
	int "canard stack size"
	default DEFAULT_TASK_STACKSIZE

endif
