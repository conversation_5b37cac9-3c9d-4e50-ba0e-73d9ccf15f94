#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config EXAMPLES_TOUCHSCREEN
	tristate "Touchscreen example"
	default n
	---help---
		Enable the touchscreen example

if EXAMPLES_TOUCHSCREEN

config EXAMPLES_TOUCHSCREEN_MINOR
	int "Touchscreen minor device number"
	default 0
	---help---
		The minor device number.  Minor=N corresponds to touchscreen device
		/dev/inputN.  Note this value must with EXAMPLES_TOUCHSCREEN_DEVPATH.
		Default 0.

config EXAMPLES_TOUCHSCREEN_DEVPATH
	string "Touchscreen device path"
	default "/dev/input0"
	---help---
		The path to the touchscreen device.  This must be consistent with
		EXAM<PERSON>ES_TOUCHSCREEN_MINOR. Default: "/dev/input0"

config EXAMPLES_TOUCHSCREEN_NSAMPLES
	int "Number of samples"
	default 0
	---help---
		This number of samples is collected and the program.
		terminates.  Default:  Zero (Samples are collected indefinitely).

config EXAMPLES_TOUCHSCREEN_MOUSE
	bool "Mouse interface"
	default n
	---help---
		The touchscreen test can also be configured to work with a mouse
		driver by setting this option.

endif
