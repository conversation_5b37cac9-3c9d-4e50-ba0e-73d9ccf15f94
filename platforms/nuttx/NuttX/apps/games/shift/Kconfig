#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config GAMES_SHIFT
	bool "Shift Game"
	default n
	---help---
		Enable Shift games. Shift is a brick game, like a mix
		between Tetris and Crush Candy. The inspiration came
		from a Shift game that was available for Android in the
		F-Droid store. The original game source code still here:
		https://github.com/boombuler/Shift/ but the game only works
		for old Android versions (4.x).
		NOTE: The source code here is not based on that code from
		above github.

if GAMES_SHIFT

config GAMES_SHIFT_PROGNAME
	string "Program name"
	default "shift"
	---help---
		This is the name of the program that will be used when the NSH ELF
		program is installed.

config GAMES_SHIFT_PRIORITY
	int "Shift Game task priority"
	default 100

config GAMES_SHIFT_STACKSIZE
	int "Shift Game stack size"
	default DEFAULT_TASK_STACKSIZE

#
# Input Device Selection
#

choice
        prompt "Input Device (Joystick, Gesture Sensor, etc)"
	default GAMES_SHIFT_USE_CONSOLEKEY

config GAMES_SHIFT_USE_CONSOLEKEY
	bool "Serial Console as Input"
	depends on !INPUT_DJOYSTICK && !SENSORS_APDS9960

config GAMES_SHIFT_USE_DJOYSTICK
	bool "Discrete Joystick as Input"
	depends on INPUT_DJOYSTICK

config GAMES_SHIFT_USE_GESTURE
	bool "Gesture Sensor APDS-9960 as Input"
	depends on SENSORS_APDS9960

endchoice

endif
