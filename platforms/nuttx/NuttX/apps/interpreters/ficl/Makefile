############################################################################
# apps/interpreters/ficl/Makefile
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

BUILDDIR := ${shell echo $(CURDIR) | sed -e 's/ /\\ /g'}

include $(APPDIR)/Make.defs

# Tools

# Include the generated Make.srcs

-include Make.srcs

# Include paths

CFLAGS += ${shell $(INCDIR) "$(CC)" $(BUILDDIR)/$(FICL_SUBDIR) $(BUILDDIR)/src}

# Source Files

CSRCS = nuttx.c

ASRCS += $(FICL_ASRCS)
CXXSRCS += $(FICL_CXXSRCS)
CSRCS += $(FICL_CSRCS)

DEPPATH += --dep-path src

VPATH += :src:$(FICL_SUBDIR)

include $(APPDIR)/Application.mk
