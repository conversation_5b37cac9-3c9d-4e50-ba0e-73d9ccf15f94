/***************************************************************************
 * apps/interpreters/ficl/src/nuttx.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ***************************************************************************/

#ifndef __APPS_INTERPRETERS_FICL_SRC_NUTTX_H
#define __APPS_INTERPRETERS_FICL_SRC_NUTTX_H

/***************************************************************************
 * Included Files
 ***************************************************************************/

#include <stdint.h>

typedef int8_t ficlInteger8;
typedef uint8_t ficlUnsigned8;
typedef int16_t ficlInteger16;
typedef uint16_t ficlUnsigned16;
typedef int32_t ficlInteger32;
typedef uint32_t ficlUnsigned32;

typedef intptr_t ficlInteger;
typedef uintptr_t ficlUnsigned;
typedef float ficlFloat;

#define FICL_PLATFORM_BASIC_TYPES   (1)
#define FICL_PLATFORM_HAS_2INTEGER  (0)
#define FICL_PLATFORM_HAS_FTRUNCATE (0)

#define FICL_PLATFORM_OS            "ansi"
#define FICL_PLATFORM_ARCHITECTURE  "unknown"

#endif /* __APPS_INTERPRETERS_FICL_SRC_NUTTX_H */
