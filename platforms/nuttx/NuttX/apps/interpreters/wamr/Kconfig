#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

config INTERPRETERS_WAMR
	tristate "Webassembly Micro Runtime"
	default n

if INTERPRETERS_WAMR

config INTERPRETERS_WAMR_VERSION
	string "WAMR Version"
	default "WAMR-04-15-2021"
	---help---
	Version WAMR-09-29-2020 and later (include main) supported.

config INTERPRETERS_IWASM_TASK
	bool "Webassembly iwasm task"
	default y

if INTERPRETERS_IWASM_TASK

config INTERPRETERS_WAMR_PRIORITY
	int "Webassembly Micro Runtime priority"
	default 100

config INTERPRETERS_WAMR_STACKSIZE
	int "Webassembly Micro Runtime stack size"
	default 8192

endif

config INTERPRETERS_WAMR_AOT
	bool "Enable AOT"
	select ARCH_USE_TEXT_HEAP if ARCH_HAVE_TEXT_HEAP
	default n

choice
	prompt "Enable interpreter"
	default INTERPRETERS_NONE

config INTERPRETERS_WAMR_FAST
	bool "Enable fast interpreter"

config INTERPRETERS_WAMR_CLASSIC
	bool "Enable classic interpreter"

config INTERPRETERS_NONE
	bool "Disable interpreter"

endchoice

config INTERPRETERS_WAMR_LOG
	bool "Enable log"
	default n

config INTERPRETERS_WAMR_LIBC_BUILTIN
	bool "Enable built-in libc"
	default n

config INTERPRETERS_WAMR_LIBC_WASI
	bool "Enable WASI libc"
	default n
	---help---
	Note: As of writing this, this works only with main branch of
	wasm-micro-runtime.
	I.e. INTERPRETERS_WAMR_VERSION="main"

	Note: As of writing this, most of the filesystem operations are
	not implemented. (Mainly because of lack of openat family of
	the API in NuttX.)

config INTERPRETERS_WAMR_MULTI_MODULE
	bool "Enable multi module support"
	default n

config INTERPRETERS_WAMR_MINILOADER
	bool "Enable mini-loader"
	default n
	---help---
	Mini-loader don't check the integrity of wasm module

config INTERPRETERS_WAMR_THREAD_MGR
	bool "Enable thread manager"
	default n

config INTERPRETERS_WAMR_LIB_PTHREAD
	bool "Enable lib pthread"
	default n

config INTERPRETERS_WAMR_SHARED_MEMORY
	bool "Enable shared memory"
	default n

config INTERPRETERS_WAMR_BULK_MEMORY
	bool "Enable bluk memory"
	default n

config INTERPRETERS_WAMR_PERF_PROFILING
	bool "Enable performance profiling"
	default n

config INTERPRETERS_WAMR_MEMORY_PROFILING
	bool "Enable memory profiling"
	default n

config INTERPRETERS_WAMR_MEMORY_TRACING
	bool "Enable memory tracing"
	default n

config INTERPRETERS_WAMR_DISABLE_HW_BOUND_CHECK
	bool "Disable hardware bound check"
	default n

config INTERPRETERS_WAMR_CUSTOM_NAME_SECTIONS
	bool "Enable custom name section support"
	default n

config INTERPRETERS_WAMR_GLOBAL_HEAP_POOL
	bool "Enable global heap pool"
	default n

if INTERPRETERS_WAMR_GLOBAL_HEAP_POOL

config INTERPRETERS_WAMR_GLOBAL_HEAP_POOL_SIZE
	int "Global heap pool size (in KB)"
	default 128

endif

endif
