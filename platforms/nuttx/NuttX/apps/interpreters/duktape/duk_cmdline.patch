diff --color -ur duktape/examples/cmdline/duk_cmdline.c duktape-2.5.0-modifed/examples/cmdline/duk_cmdline.c
--- duktape/examples/cmdline/duk_cmdline.c	2019-11-25 06:04:27.000000000 +0800
+++ duktape-2.5.0-modifed/examples/cmdline/duk_cmdline.c	2020-08-07 23:20:27.320000000 +0800
@@ -103,7 +103,7 @@
 #define  MEM_LIMIT_NORMAL   (128*1024*1024)   /* 128 MB */
 #define  MEM_LIMIT_HIGH     (2047*1024*1024)  /* ~2 GB */
-#define  LINEBUF_SIZE       65536
+#define  LINEBUF_SIZE       256
 
 static int main_argc = 0;
 static char **main_argv = NULL;

@@ -810,10 +810,12 @@
 
 		for (;;) {
 			int c = fgetc(stdin);
+			fputc(c, stdout);
+			fflush(stdout);
 			if (c == EOF) {
 				got_eof = 1;
 				break;
-			} else if (c == '\n') {
+			} else if (c == '\n' || c == '\r') {
 				break;
 			} else if (idx >= LINEBUF_SIZE) {
 				fprintf(stderr, "line too long\n");

diff --color -ur duktape/src-noline/duk_config.h duktape-2.5.0-modifed/src-noline/duk_config.h
--- duktape/src-noline/duk_config.h	2019-11-25 06:04:27.000000000 +0800
+++ duktape-2.5.0-modifed/src-noline/duk_config.h	2020-08-07 23:14:55.880000000 +0800
@@ -402,7 +402,7 @@
 /* --- Mac OSX, iPhone, Darwin --- */
 #define DUK_USE_DATE_NOW_GETTIMEOFDAY
 #define DUK_USE_DATE_TZO_GMTIME_R
-#define DUK_USE_DATE_PRS_STRPTIME
+#undef DUK_USE_DATE_PRS_STRPTIME
 #define DUK_USE_DATE_FMT_STRFTIME
 #include <TargetConditionals.h>
 #include <architecture/byte_order.h>
@@ -746,7 +746,7 @@
 
 #define DUK_USE_DATE_NOW_GETTIMEOFDAY
 #define DUK_USE_DATE_TZO_GMTIME_R
-#define DUK_USE_DATE_PRS_STRPTIME
+#undef DUK_USE_DATE_PRS_STRPTIME
 #define DUK_USE_DATE_FMT_STRFTIME
