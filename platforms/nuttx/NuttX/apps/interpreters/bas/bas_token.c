
#line 3 "<stdout>"

#define  YY_INT_ALIGNED short int

/* A lexical scanner generated by flex */

#define FLEX_SCANNER
#define YY_FLEX_MAJOR_VERSION 2
#define YY_FLEX_MINOR_VERSION 5
#define YY_FLEX_SUBMINOR_VERSION 39
#if YY_FLEX_SUBMINOR_VERSION > 0
#define FLEX_BETA
#endif

/* First, we deal with  platform-specific or compiler-specific issues. */

/* begin standard C headers. */
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <stdlib.h>

/* end standard C headers. */

/* flex integer type definitions */

#ifndef FLEXINT_H
#define FLEXINT_H

/* C99 systems have <inttypes.h>. Non-C99 systems may or may not. */

#if defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L

/* C9<PERSON> says to define __STDC_LIMIT_MACROS before including stdint.h,
 * if you want the limit (max/min) macros for int types.
 */
#ifndef __STDC_LIMIT_MACROS
#define __STDC_LIMIT_MACROS 1
#endif

#include <inttypes.h>
typedef int8_t flex_int8_t;
typedef uint8_t flex_uint8_t;
typedef int16_t flex_int16_t;
typedef uint16_t flex_uint16_t;
typedef int32_t flex_int32_t;
typedef uint32_t flex_uint32_t;
#else
typedef signed char flex_int8_t;
typedef short int flex_int16_t;
typedef int flex_int32_t;
typedef unsigned char flex_uint8_t;
typedef unsigned short int flex_uint16_t;
typedef unsigned int flex_uint32_t;

/* Limits of integral types. */
#ifndef INT8_MIN
#define INT8_MIN               (-128)
#endif
#ifndef INT16_MIN
#define INT16_MIN              (-32767-1)
#endif
#ifndef INT32_MIN
#define INT32_MIN              (-2147483647-1)
#endif
#ifndef INT8_MAX
#define INT8_MAX               (127)
#endif
#ifndef INT16_MAX
#define INT16_MAX              (32767)
#endif
#ifndef INT32_MAX
#define INT32_MAX              (2147483647)
#endif
#ifndef UINT8_MAX
#define UINT8_MAX              (255U)
#endif
#ifndef UINT16_MAX
#define UINT16_MAX             (65535U)
#endif
#ifndef UINT32_MAX
#define UINT32_MAX             (4294967295U)
#endif

#endif /* ! C99 */

#endif /* ! FLEXINT_H */

#ifdef __cplusplus

/* The "const" storage-class-modifier is valid. */
#define YY_USE_CONST

#else  /* ! __cplusplus */

/* C99 requires __STDC__ to be defined as 1. */
#if defined (__STDC__)

#define YY_USE_CONST

#endif /* defined (__STDC__) */
#endif /* ! __cplusplus */

#ifdef YY_USE_CONST
#define yyconst const
#else
#define yyconst
#endif

/* Returned upon end-of-file. */
#define YY_NULL 0

/* Promotes a possibly negative, possibly signed char to an unsigned
 * integer for use as an array index.  If the signed char is negative,
 * we want to instead treat it as an 8-bit unsigned char, hence the
 * double cast.
 */
#define YY_SC_TO_UI(c) ((unsigned int) (unsigned char) c)

/* Enter a start condition.  This macro really ought to take a parameter,
 * but we do it the disgusting crufty way forced on us by the ()-less
 * definition of BEGIN.
 */
#define BEGIN (yy_start) = 1 + 2 *

/* Translate the current start state into a value that can be later handed
 * to BEGIN to return to the state.  The YYSTATE alias is for lex
 * compatibility.
 */
#define YY_START (((yy_start) - 1) / 2)
#define YYSTATE YY_START

/* Action number for EOF rule of a given start state. */
#define YY_STATE_EOF(state) (YY_END_OF_BUFFER + state + 1)

/* Special action meaning "start processing a new file". */
#define YY_NEW_FILE yyrestart(yyin)

#define YY_END_OF_BUFFER_CHAR 0

/* Size of default input buffer. */
#ifndef YY_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k.
 * Moreover, YY_BUF_SIZE is 2*YY_READ_BUF_SIZE in the general case.
 * Ditto for the __ia64__ case accordingly.
 */
#define YY_BUF_SIZE 32768
#else
#define YY_BUF_SIZE 16384
#endif /* __ia64__ */
#endif

/* The state buf must be large enough to hold one state per character in the main buffer.
 */
#define YY_STATE_BUF_SIZE   ((YY_BUF_SIZE + 2) * sizeof(yy_state_type))

#ifndef YY_TYPEDEF_YY_BUFFER_STATE
#define YY_TYPEDEF_YY_BUFFER_STATE
typedef struct yy_buffer_state *YY_BUFFER_STATE;
#endif

#ifndef YY_TYPEDEF_YY_SIZE_T
#define YY_TYPEDEF_YY_SIZE_T
typedef size_t yy_size_t;
#endif

extern yy_size_t yyleng;

extern FILE *yyin, *yyout;

#define EOB_ACT_CONTINUE_SCAN 0
#define EOB_ACT_END_OF_FILE 1
#define EOB_ACT_LAST_MATCH 2

    #define YY_LESS_LINENO(n)
    #define YY_LINENO_REWIND_TO(ptr)

/* Return all but the first "n" matched characters back to the input stream. */
#define yyless(n) \
  do \
    { \
    /* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
    *yy_cp = (yy_hold_char); \
    YY_RESTORE_YY_MORE_OFFSET \
    (yy_c_buf_p) = yy_cp = yy_bp + yyless_macro_arg - YY_MORE_ADJ; \
    YY_DO_BEFORE_ACTION; /* set up yytext again */ \
    } \
  while (0)

#define unput(c) yyunput(c, (yytext_ptr))

#ifndef YY_STRUCT_YY_BUFFER_STATE
#define YY_STRUCT_YY_BUFFER_STATE
struct yy_buffer_state
  {
  FILE *yy_input_file;

  char *yy_ch_buf;    /* input buffer */
  char *yy_buf_pos;    /* current position in input buffer */

  /* Size of input buffer in bytes, not including room for EOB
   * characters.
   */
  yy_size_t yy_buf_size;

  /* Number of characters read into yy_ch_buf, not including EOB
   * characters.
   */
  yy_size_t yy_n_chars;

  /* Whether we "own" the buffer - i.e., we know we created it,
   * and can realloc() it to grow it, and should free() it to
   * delete it.
   */
  int yy_is_our_buffer;

  /* Whether this is an "interactive" input source; if so, and
   * if we're using stdio for input, then we want to use getc()
   * instead of fread(), to make sure we stop fetching input after
   * each newline.
   */
  int yy_is_interactive;

  /* Whether we're considered to be at the beginning of a line.
   * If so, '^' rules will be active on the next match, otherwise
   * not.
   */
  int yy_at_bol;

    int yy_bs_lineno; /**< The line count. */
    int yy_bs_column; /**< The column count. */

  /* Whether to try to fill the input buffer when we reach the
   * end of it.
   */
  int yy_fill_buffer;

  int yy_buffer_status;

#define YY_BUFFER_NEW 0
#define YY_BUFFER_NORMAL 1
  /* When an EOF's been seen but there's still some text to process
   * then we mark the buffer as YY_EOF_PENDING, to indicate that we
   * shouldn't try reading from the input source any more.  We might
   * still have a bunch of tokens to match, though, because of
   * possible backing-up.
   *
   * When we actually see the EOF, we change the status to "new"
   * (via yyrestart()), so that the user can continue scanning by
   * just pointing yyin at a new input file.
   */
#define YY_BUFFER_EOF_PENDING 2

  };
#endif /* !YY_STRUCT_YY_BUFFER_STATE */

/* Stack of input buffers. */
static size_t yy_buffer_stack_top = 0; /**< index of top of stack. */
static size_t yy_buffer_stack_max = 0; /**< capacity of stack. */
static YY_BUFFER_STATE * yy_buffer_stack = 0; /**< Stack as an array. */

/* We provide macros for accessing buffer states in case in the
 * future we want to put the buffer states in a more general
 * "scanner state".
 *
 * Returns the top of the stack, or NULL.
 */
#define YY_CURRENT_BUFFER ((yy_buffer_stack) \
                          ? (yy_buffer_stack)[(yy_buffer_stack_top)] \
                          : NULL)

/* Same as previous macro, but useful when we know that the buffer stack is not
 * NULL or when we need an lvalue. For internal use only.
 */
#define YY_CURRENT_BUFFER_LVALUE (yy_buffer_stack)[(yy_buffer_stack_top)]

/* yy_hold_char holds the character lost when yytext is formed. */
static char yy_hold_char;
static yy_size_t yy_n_chars;    /* number of characters read into yy_ch_buf */
yy_size_t yyleng;

/* Points to current character in buffer. */
static char *yy_c_buf_p = (char *) 0;
static int yy_init = 0;    /* whether we need to initialize */
static int yy_start = 0;  /* start state number */

/* Flag which is used to allow yywrap()'s to do buffer switches
 * instead of setting up a fresh yyin.  A bit of a hack ...
 */
static int yy_did_buffer_switch_on_eof;

void yyrestart (FILE *input_file);
void yy_switch_to_buffer (YY_BUFFER_STATE new_buffer);
YY_BUFFER_STATE yy_create_buffer (FILE *file,int size);
void yy_delete_buffer (YY_BUFFER_STATE b);
void yy_flush_buffer (YY_BUFFER_STATE b);
void yypush_buffer_state (YY_BUFFER_STATE new_buffer);
void yypop_buffer_state (void);

static void yyensure_buffer_stack (void);
static void yy_load_buffer_state (void);
static void yy_init_buffer (YY_BUFFER_STATE b,FILE *file);

#define YY_FLUSH_BUFFER yy_flush_buffer(YY_CURRENT_BUFFER)

YY_BUFFER_STATE yy_scan_buffer (char *base,yy_size_t size);
YY_BUFFER_STATE yy_scan_string (yyconst char *yy_str);
YY_BUFFER_STATE yy_scan_bytes (yyconst char *bytes,yy_size_t len);

void *yyalloc (yy_size_t);
void *yyrealloc (void *,yy_size_t);
void yyfree (void *);

#define yy_new_buffer yy_create_buffer

#define yy_set_interactive(is_interactive) \
  { \
  if (! YY_CURRENT_BUFFER){ \
        yyensure_buffer_stack (); \
    YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer(yyin,YY_BUF_SIZE); \
  } \
  YY_CURRENT_BUFFER_LVALUE->yy_is_interactive = is_interactive; \
  }

#define yy_set_bol(at_bol) \
  { \
  if (! YY_CURRENT_BUFFER){\
        yyensure_buffer_stack (); \
    YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer(yyin,YY_BUF_SIZE); \
  } \
  YY_CURRENT_BUFFER_LVALUE->yy_at_bol = at_bol; \
  }

#define YY_AT_BOL() (YY_CURRENT_BUFFER_LVALUE->yy_at_bol)

/* Begin user sect3 */

#define yywrap() 1
#define YY_SKIP_YYWRAP

typedef unsigned char YY_CHAR;

FILE *yyin = (FILE *) 0, *yyout = (FILE *) 0;

typedef int yy_state_type;

extern int yylineno;

int yylineno = 1;

extern char *yytext;
#define yytext_ptr yytext

static yy_state_type yy_get_previous_state (void);
static yy_state_type yy_try_NUL_trans (yy_state_type current_state);
static int yy_get_next_buffer (void);
static void yy_fatal_error (yyconst char msg[]);

/* Done after the current pattern has been matched and before the
 * corresponding action - sets up yytext.
 */
#define YY_DO_BEFORE_ACTION \
  (yytext_ptr) = yy_bp; \
  yyleng = (size_t) (yy_cp - yy_bp); \
  (yy_hold_char) = *yy_cp; \
  *yy_cp = '\0'; \
  (yy_c_buf_p) = yy_cp;

#define YY_NUM_RULES 198
#define YY_END_OF_BUFFER 199
/* This struct is not used in this scanner,
   but its presence is necessary. */
struct yy_trans_info
  {
  flex_int32_t yy_verify;
  flex_int32_t yy_nxt;
  };
static yyconst flex_int16_t yy_accept[701] =
    {   0,
        0,    0,    0,    0,    0,    0,    0,    0,  199,  197,
      196,  196,  193,  197,    1,  197,    8,    9,   10,   11,
       13,   12,  197,   14,    3,   16,   17,   18,   22,   23,
      142,  195,  195,  195,  195,  195,  195,  195,  195,  195,
      195,  195,  195,  195,  195,  195,  195,  195,  195,  195,
      195,  195,  195,  195,   15,   26,   47,   48,   49,   47,
       46,   50,  198,  198,  198,   98,  196,  193,    0,    7,
        6,    0,    0,    2,    2,    3,    2,    3,    0,   19,
       21,   20,   25,   24,  143,  195,  195,  195,  195,   31,
      195,  195,  195,  195,  195,   43,  195,  195,  195,   60,

      195,  195,  195,  195,  195,  195,  195,  195,  195,  195,
      195,  195,  195,  195,   96,  195,  195,  105,  195,  195,
      195,  195,  195,  195,  195,  195,  195,  195,  195,  195,
      195,  135,  195,  140,  195,  142,  195,  195,  195,  153,
      195,  195,  195,  195,  195,  195,  195,  195,  195,  195,
      195,  195,  195,  195,  195,  171,  195,  195,  195,  195,
      195,  195,  195,  195,  195,  195,  195,  195,   47,   48,
       47,   45,   44,    0,   66,    0,   98,    4,    5,    2,
        0,    2,    2,    0,    0,    2,  195,   30,  168,  195,
      195,  195,  195,  195,   39,  195,   41,  195,  195,   51,

      195,  195,   58,  195,    0,  195,   64,  195,   72,  195,
       75,  195,  195,  195,    0,  195,  195,   84,  195,   91,
        0,  195,  195,  195,   95,  195,  100,  101,  195,  104,
      195,  107,  195,  195,  195,  195,  195,  195,  195,  195,
      125,  195,  127,  195,  128,  195,  131,    0,  195,  195,
      141,  143,  195,  195,  145,  195,  195,  191,  195,  195,
      195,  195,  195,  155,  195,  195,  195,  195,  195,  161,
      195,  195,  166,  195,  195,  170,  169,  195,  172,  195,
      195,  195,  195,  195,  195,  195,  195,  195,  195,  195,
      187,  195,  189,  195,   44,    0,    2,    2,    0,    0,

        2,    2,  195,   32,   34,  195,  195,  195,  195,   42,
        0,  195,  195,  195,  195,  195,    0,    0,   63,   64,
        0,  195,  195,  195,  195,  195,  195,  195,  195,  195,
      195,    0,  195,   92,    0,    0,  195,   94,   39,  195,
      195,  106,  195,  195,  108,  195,  110,  195,  113,  116,
      195,  119,    0,  195,  129,  130,    0,  136,  195,  144,
      195,  146,  195,  148,  191,  191,  149,  195,  195,  150,
      195,  151,  195,  195,  195,  154,  156,  195,  195,  195,
      195,  162,  163,    0,  195,  167,  195,  195,  174,  195,
      195,  195,  195,  195,  180,  181,  195,  195,  195,  188,

      190,    0,    2,  195,    0,   35,   36,   37,   40,    0,
        0,  195,  195,  195,  195,  195,    0,    0,  195,    0,
        0,    0,    0,   68,  195,  195,  195,  195,   74,    0,
       80,   82,  195,    0,    0,    0,    0,    0,  195,    0,
       94,   93,   99,  102,    0,  195,  109,  111,  195,    0,
        0,  195,    0,    0,    0,    0,  126,    0,  195,  195,
      191,  195,  195,  195,  195,  195,  195,  195,  159,  160,
        0,  195,  195,  195,  173,  195,  195,  177,  178,  179,
      182,  183,  185,  195,    0,   38,    0,    0,   52,   53,
       54,   57,  195,    0,    0,   65,    0,   68,    0,    0,

        0,  195,  195,   71,  195,    0,    0,    0,   81,  195,
        0,    0,    0,    0,    0,  195,   93,   97,    0,   97,
       97,  103,    0,  194,  112,    0,    0,    0,  118,    0,
        0,    0,    0,    0,  195,  195,  192,  195,  152,  195,
      158,    0,    0,  164,  195,  168,  195,  176,  184,  186,
        0,    0,    0,   55,    0,   59,    0,    0,    0,    0,
        0,   71,   69,  195,   73,   76,    0,    0,    0,  195,
        0,    0,    0,    0,    0,  195,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,  195,    0,  164,
        0,  165,  195,    0,    0,    0,    0,   61,   62,    0,

       69,    0,  195,   77,    0,   79,   83,    0,    0,    0,
        0,    0,   90,    0,    0,    0,    0,    0,    0,  122,
        0,    0,  134,    0,    0,    0,  195,    0,  165,  175,
        0,    0,   33,   56,    0,    0,   70,    0,    0,    0,
       85,    0,    0,    0,  114,    0,    0,  120,  121,  123,
      124,    0,    0,    0,    0,  147,    0,    0,    0,    0,
       70,    0,   87,   89,   86,   88,  194,  115,  117,    0,
        0,    0,  138,    0,    0,   27,    0,    0,    0,    0,
        0,  137,  139,  157,    0,   29,   67,    0,    0,  132,
        0,   78,    0,    0,    0,    0,  133,    0,   28,    0

    } ;

static yyconst flex_int32_t yy_ec[256] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    2,    3,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    2,    4,    5,    6,    7,    8,    9,   10,   11,
       12,   13,   14,   15,   16,   17,   18,   19,   20,   20,
       20,   20,   20,   20,   20,   21,   21,   22,   23,   24,
       25,   26,   27,    1,   28,   29,   30,   31,   32,   33,
       34,   35,   36,   37,   38,   39,   40,   41,   42,   43,
       44,   45,   46,   47,   48,   49,   50,   51,   52,   53,
       54,   55,   56,   57,   58,    1,   59,   60,   61,   62,

       63,   64,   65,   66,   67,   37,   68,   69,   70,   71,
       72,   73,   74,   75,   76,   77,   78,   79,   80,   81,
       82,   83,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,

        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1
    } ;

static yyconst flex_int32_t yy_meta[84] =
    {   0,
        1,    2,    3,    1,    4,    5,    5,    5,    1,    1,
        1,    1,    1,    1,    6,    1,    7,    1,    8,    8,
        8,    6,    1,    1,    1,    1,    1,    9,    9,    9,
        9,    9,    9,   10,   10,   10,   10,   10,   10,   10,
       10,   10,   10,   10,   10,   10,   10,   10,   10,   10,
       10,   10,   10,    1,    1,    1,    1,    7,    9,    9,
        9,    9,    9,    9,   10,   10,   10,   10,   10,   10,
       10,   10,   10,   10,   10,   10,   10,   10,   10,   10,
       10,   10,   10
    } ;

static yyconst flex_int16_t yy_base[718] =
    {   0,
        0,    0,   82,   86,   50,   54,  292,  268,  260, 3041,
       90,   92,    0,   93, 3041,   64, 3041, 3041, 3041, 3041,
     3041, 3041,   90, 3041,   99, 3041, 3041,   87,   76,   90,
      242,  117,  120,  126,  138,  205,  211,  144,  131,  284,
      134,  218,  355,  235,  294,  302,  334,  397,  470,  413,
      258,  547,  409,  427, 3041, 3041,    0,  235, 3041,  172,
     3041, 3041, 3041,   89,  232, 3041,  146,    0,  154,  164,
      218,    0,  162,  506, 3041, 3041,  538,  557,  213, 3041,
     3041, 3041, 3041, 3041, 3041, 3041,  183,  232,  290,  214,
      305,  455,  364,  565,  578,  262,  312,  596,  592,  325,

      377,  588,  617,  458,  646,  636,  663,  674,  485,  677,
      680,  692,  430,  683,  489,  701,  704,  508,  707,  714,
      725,  744,  756,  734,  781,  786,  796,  799,  812,  815,
      828,  560,  839,  818,  831,  834,  842,  847,  862,  866,
      881,  947,  885,  891,  912,  905,  917,  934,  920,  950,
      971,  957,  923,  996, 1002,  927, 1020, 1030,  965, 1036,
     1033, 1039, 1047, 1043, 1051, 1076, 1079, 1082,    0,  213,
      385,  201,  209,  225, 3041,  205, 3041,    0,  164, 3041,
     1097, 3041, 1108, 1116,  917, 1127, 1087, 1114, 1135, 1149,
     1152, 1155, 1158, 1161, 1166, 1169, 1172, 1175, 1186, 1189,

     1197, 1202, 1194, 1213,  695, 1223, 1217, 1240, 1247, 1232,
     1244, 1255, 1268, 1271,  198, 1281, 1274, 1289, 1305, 1301,
      406, 1310, 1318, 1321, 1326, 1335, 1340, 1343, 1348, 1355,
     1364, 1367, 1372, 1375, 1379, 1385, 1391, 1399, 1402, 1406,
     1454, 1457, 1424, 1460, 1433, 1467, 1470,  243, 1474, 1480,
     1483, 3041, 1488, 1491, 1494, 1497, 1502, 1564, 1533, 1577,
     1550, 1547, 1594, 1505, 1597, 1600, 1606, 1609, 1627, 1511,
     1630, 1636, 1648, 1639, 1658, 1519, 1523, 1616, 1528, 1661,
     1669, 1677, 1685, 1690, 1693, 1700, 1707, 1714, 1721, 1735,
     1710, 1738, 1744, 1748,  194, 1739, 1766, 3041, 1774, 1746,

     1785, 3041, 1767, 1793, 1801, 1808, 1811, 1814, 1817, 1820,
     1808, 1825, 1828, 1831, 1841, 1851,  138,   94, 1857, 1860,
     1858, 1899, 1867, 1903, 1907, 1922, 1931, 1937, 1940, 1949,
     1953, 1947, 1963, 3041,  183,  209, 1966, 1971, 1980, 1995,
     1990, 2009, 2026, 2017, 2035, 2038, 2041, 2044, 2053, 2062,
     2065, 2068, 2051, 2071, 2074, 2082,  212, 2091, 2096, 2101,
     2104, 3041, 2121, 2126,    0,    0, 2133, 2136, 2142, 2146,
     2149, 2152, 2158, 2163, 2166, 2172, 2177, 2183, 2188, 2180,
     2191, 2208, 2215,  333, 2218, 2221, 2229, 2232, 2239, 2246,
     2249, 2260, 2264, 2267, 2274, 2277, 2280, 2287, 2307, 2298,

     2310, 2300, 2305, 2321,  367, 2328, 2334, 2338, 2341,  217,
      239, 2344, 2348, 2351, 2354, 2357,  281,  356, 2365,  296,
      378,  311,  471, 2368, 2372, 2381, 2386, 2393, 2396, 2375,
     2403, 2416, 2424,  359,  404,  409,  409,  476, 2434,  527,
     3041, 2441, 2502, 2446,  579, 2453, 2449, 2456, 2460, 2055,
      734, 2472,  470,  476,  576,  543, 2467,  586, 2480, 2491,
        0, 2475, 2487, 2529, 2533, 2536, 2539, 2547, 2483, 2560,
     1377, 2563, 2574, 2577, 2581, 2584, 2591, 2594, 2607, 2612,
     2615, 2620, 2624, 2631,  594, 3041,  194,  603, 2628, 2638,
     2642, 2645, 2648,  573,  654, 2654,  684, 3041,  686,  728,

      598, 2657, 2660, 2663, 2666,  728, 1164,  730, 3041, 2669,
      756,  791,  808,  813,  823, 2673, 3041, 3041,  169, 3041,
     2676, 3041,  835, 2682, 2687,  604,  832,  844, 2690,  878,
      637, 1392,  723,  917, 2697, 2702, 2705, 2714, 2718, 2725,
     2728,  754,  931, 2733, 2741, 2744, 2747, 2750, 3041, 3041,
     2715,  938,  162, 3041,  991, 2755,  966,  997, 1003, 1036,
     1153, 3041, 2760, 2766, 2772, 3041, 1025, 1074, 1184, 2775,
     1203, 1239, 1290, 1316, 1240, 2778, 1321, 1277, 1470, 1318,
     1379, 1408, 1476, 1485, 1516, 1551, 2762, 2791, 1628, 3041,
     1584, 2794, 2797, 1607, 1629, 1698,    0, 3041, 3041, 1701,

     3041, 1701, 2803, 3041, 1800, 3041, 2806, 1799, 1805, 1805,
     1839, 1847, 2824, 1845, 1814, 1917, 1835, 1943, 1944, 3041,
     1965, 1863,  154, 1908, 1960, 1962, 2827, 1925, 3041, 2833,
     1934, 1995, 3041, 3041, 2050, 1989, 2845, 2007, 2061, 2011,
     3041, 2090, 2105, 2115, 3041, 2169, 2179, 3041, 3041, 3041,
     3041, 2813, 2214, 2237, 2296, 2850, 2336, 2200, 2381, 2480,
     3041, 2369, 3041, 3041, 3041, 3041, 3041, 3041, 3041, 2490,
     2374, 2413, 3041, 2490, 2516,  141, 2540, 2534, 2534, 2639,
     2560, 3041, 3041, 3041, 2726, 3041, 3041, 2688, 2745, 3041,
     2748, 3041,  133, 2575,  253, 2789, 3041, 2757, 3041, 3041,

     2890, 2900, 2910, 2920, 2930, 2936, 2946, 2956, 2966, 2969,
     2978, 2987, 2997, 3007, 3016, 3026, 3030
    } ;

static yyconst flex_int16_t yy_def[718] =
    {   0,
      700,    1,  701,  701,  702,  702,  703,  703,  700,  700,
      700,  700,  704,  705,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  706,  706,  706,  706,  706,  706,  706,  706,  706,
      706,  706,  706,  706,  706,  706,  706,  706,  706,  706,
      706,  706,  706,  706,  700,  700,  707,  700,  700,  708,
      700,  700,  700,  700,  709,  700,  700,  704,  705,  705,
      700,  710,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  706,  706,  706,  706,
      706,  706,  706,  706,  706,  706,  706,  706,  706,  706,

      706,  706,  706,  706,  706,  706,  706,  706,  706,  706,
      706,  706,  706,  706,  706,  706,  706,  706,  706,  706,
      706,  706,  706,  706,  706,  706,  706,  706,  706,  706,
      706,  706,  706,  706,  706,  706,  706,  706,  706,  706,
      706,  706,  706,  706,  706,  706,  706,  706,  706,  706,
      706,  706,  706,  706,  706,  706,  706,  706,  706,  706,
      706,  706,  706,  706,  706,  706,  706,  706,  707,  700,
      708,  711,  707,  711,  700,  709,  700,  710,  700,  700,
      700,  700,  700,  700,  700,  700,  706,  706,  706,  706,
      706,  706,  706,  706,  706,  706,  706,  706,  706,  706,

      706,  706,  706,  706,  700,  706,  706,  706,  706,  706,
      706,  706,  706,  706,  712,  706,  706,  706,  706,  706,
      700,  706,  706,  706,  706,  706,  706,  706,  706,  706,
      706,  706,  706,  706,  706,  706,  706,  706,  706,  706,
      706,  706,  706,  706,  706,  706,  706,  700,  706,  706,
      706,  700,  706,  706,  706,  706,  706,  706,  706,  706,
      706,  706,  706,  706,  706,  706,  706,  706,  706,  706,
      706,  706,  706,  706,  706,  706,  706,  706,  706,  706,
      706,  706,  706,  706,  706,  706,  706,  706,  706,  706,
      706,  706,  706,  706,  700,  700,  700,  700,  700,  700,

      700,  700,  706,  706,  706,  706,  706,  706,  706,  706,
      700,  706,  706,  706,  706,  706,  700,  700,  706,  706,
      700,  706,  706,  706,  706,  706,  706,  706,  706,  706,
      706,  700,  706,  700,  700,  700,  706,  706,  706,  706,
      706,  706,  706,  706,  706,  706,  706,  706,  706,  706,
      706,  706,  700,  706,  706,  706,  700,  706,  706,  706,
      706,  700,  706,  706,  713,  713,  706,  706,  706,  706,
      706,  706,  706,  706,  706,  706,  706,  706,  706,  706,
      706,  706,  706,  700,  706,  706,  706,  706,  706,  706,
      706,  706,  706,  706,  706,  706,  706,  706,  706,  706,

      706,  700,  700,  706,  700,  706,  706,  706,  706,  700,
      700,  706,  706,  706,  706,  706,  700,  700,  706,  700,
      700,  700,  700,  706,  706,  706,  706,  706,  706,  700,
      706,  706,  706,  700,  700,  700,  700,  700,  706,  700,
      700,  706,  714,  706,  700,  706,  706,  706,  706,  700,
      700,  706,  700,  700,  700,  700,  706,  700,  706,  706,
      713,  706,  706,  706,  706,  706,  706,  706,  706,  706,
      700,  706,  706,  706,  706,  706,  706,  706,  706,  706,
      706,  706,  706,  706,  700,  700,  715,  700,  706,  706,
      706,  706,  706,  700,  700,  706,  700,  700,  700,  700,

      700,  706,  706,  706,  706,  700,  700,  700,  700,  706,
      700,  700,  700,  700,  700,  706,  700,  700,  716,  700,
      706,  700,  700,  706,  706,  700,  700,  700,  706,  700,
      700,  700,  700,  700,  706,  706,  706,  706,  706,  706,
      706,  700,  700,  706,  706,  706,  706,  706,  700,  700,
      700,  700,  715,  700,  700,  706,  700,  700,  700,  700,
      700,  700,  706,  706,  706,  700,  700,  700,  700,  706,
      700,  700,  700,  700,  700,  706,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700,  706,  700,  700,
      700,  706,  706,  700,  700,  700,  717,  700,  700,  700,

      700,  700,  706,  700,  700,  700,  706,  700,  700,  700,
      700,  700,  706,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  706,  700,  700,  706,
      700,  700,  700,  700,  700,  700,  706,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  706,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700,  700,  700,    0,

      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700
    } ;

static yyconst flex_int16_t yy_nxt[3125] =
    {   0,
       10,   11,   12,   13,   14,   15,   10,   10,   16,   13,
       17,   18,   19,   20,   21,   22,   23,   24,   25,   25,
       25,   26,   27,   28,   29,   30,   31,   32,   33,   34,
       35,   36,   37,   38,   39,   40,   41,   42,   43,   44,
       45,   46,   47,   41,   48,   49,   50,   51,   41,   52,
       53,   41,   54,   17,   55,   18,   56,   10,   32,   33,
       34,   35,   36,   37,   38,   39,   40,   42,   43,   44,
       45,   46,   47,   41,   48,   49,   50,   51,   41,   52,
       53,   41,   54,   58,   59,   64,   60,   58,   59,   64,
       60,   67,   67,   67,   67,   70,   61,   71,   72,   82,

       61,   83,   75,   62,   75,   73,   76,   62,   74,   74,
       74,   80,   81,   81,   84,   77,   64,   78,   78,   78,
       64,  175,   86,   86,   86,   86,   86,   86,  418,   72,
       79,   86,   86,   86,  695,   73,   86,   86,   86,   86,
       86,   86,  685,   86,   86,   86,   88,   67,   67,   86,
       86,   86,  175,   92,   96,  652,   70,   89,   71,  418,
       93,   79,   90,  553,   94,   97,   70,   95,   71,   98,
      519,   91,  113,   99,  172,  111,  173,   88,  417,  100,
      179,  179,  179,  179,   92,  112,  174,   89,   86,   86,
       86,   93,   90,  174,   94,  553,   97,   95,  174,  215,

       98,   91,  113,  172,   99,  295,  111,  177,  417,  100,
       86,   86,   86,  171,  170,  112,   86,   86,   86,   86,
       86,   86,   69,   86,   86,   86,  185,  172,  185,  295,
      440,  186,  186,  186,  177,  101,  170,   86,   86,   86,
       86,   86,   86,  102,  248,  103,  107,   85,  104,  105,
      441,  108,  109,  119,  695,  106,  458,  487,  110,  700,
      440,  187,  126,   86,   86,   86,  101,   86,   86,   86,
       66,  697,  127,  102,  357,  103,  128,  107,  104,  105,
      441,  108,  109,  488,  119,  106,  458,  487,  110,   86,
       86,   86,  187,  126,   66,   86,   86,   86,  158,   86,

       86,   86,  127,  159,  700,  357,  128,   86,   86,   86,
       86,   86,   86,  488,  114,  700,  115,   86,   86,   86,
      188,  129,  700,  116,  117,  130,  205,  494,  158,  118,
       86,   86,   86,  159,  384,  131,  189,  700,  700,   86,
       86,   86,  132,  497,  133,  114,  134,  115,  700,  135,
      136,  188,  129,  116,  117,  499,  130,  494,  199,  118,
       86,   86,   86,  700,  471,  131,  700,  189,  405,   86,
       86,   86,  132,  497,  133,  137,  134,  700,  138,  135,
      700,  139,   86,   86,   86,  499,  120,  172,  199,  173,
      121,  495,  700,  122,  192,  471,  123,  124,  485,  174,

      125,  511,   86,   86,   86,  137,  174,  221,  138,  700,
      498,  139,  206,  140,   86,   86,   86,  120,   86,   86,
       86,  121,  495,  122,  141,  192,  123,  124,  142,  485,
      125,  511,   86,   86,   86,   86,   86,   86,  700,  512,
      154,  498,  143,  206,  144,  700,  700,  155,  700,  513,
      165,  335,  336,  166,  156,  141,  514,  157,  167,  142,
       86,   86,   86,   86,   86,   86,  700,  700,  168,  224,
      512,  154,  143,  700,  144,   86,   86,   86,  155,  513,
      165,  335,  336,  166,  156,  700,  514,  157,  700,  167,
       86,   86,   86,  190,   86,   86,   86,  145,  168,  224,

      191,  146,  500,  515,  147,  700,  211,  700,  148,  180,
      530,  180,  149,   86,   86,   86,  150,  151,  501,  152,
      531,  153,  700,  190,   74,   74,   74,  700,  145,  218,
      191,  700,  146,  500,  515,  147,  211,  181,  148,  700,
      530,  182,  149,  182,  700,  700,  150,  151,  501,  152,
      531,  153,   86,   86,   86,  517,  183,  183,  183,  218,
       75,  248,   75,  700,   76,   86,   86,   86,  181,  184,
       86,   86,   86,   77,  160,   78,   78,   78,  161,  700,
      445,  162,  163,   86,   86,   86,  517,  533,   79,  700,
      700,  164,  700,   86,   86,   86,  193,   86,   86,   86,

      184,   86,   86,   86,  207,  160,  194,  532,  557,  161,
      195,  700,  162,  163,  523,  700,  196,  533,  197,   79,
      198,  164,   86,   86,   86,  200,  562,  193,  201,  700,
      534,  203,  552,  208,  202,  578,  194,  204,  532,  557,
      195,   86,   86,   86,  555,  523,  196,  209,  197,  700,
      198,   86,   86,   86,  700,  700,  200,  562,  700,  201,
      534,  203,  552,  208,  202,  210,  578,  204,   86,   86,
       86,  213,  582,  212,  555,  215,  700,  700,  209,   86,
       86,   86,   86,   86,   86,   86,   86,   86,   86,   86,
       86,  700,  558,  221,  214,  210,  205,   86,   86,   86,

      700,  700,  213,  582,  212,  216,   86,   86,   86,   86,
       86,   86,   86,   86,   86,  700,  700,  219,  217,   86,
       86,   86,  558,  225,  559,  214,  220,  560,  226,  700,
       86,   86,   86,  228,  700,  451,  216,  222,  223,   86,
       86,   86,  317,  227,  318,  231,  229,  219,  217,   86,
       86,   86,  230,  225,  559,  700,  220,  560,  585,  226,
      232,   86,   86,   86,  228,  233,  561,  222,  223,  566,
      234,  700,  317,  227,  318,  231,  229,  569,  239,  235,
      700,  528,  230,  236,  590,  237,   86,   86,   86,  585,
      232,   86,   86,   86,  700,  233,  561,  238,  571,  566,

      234,   86,   86,   86,   86,   86,   86,  569,  239,  700,
      235,  528,  240,  700,  236,  590,  237,   86,   86,   86,
       86,   86,   86,   86,   86,   86,  242,  238,  571,  243,
      700,  572,  241,   86,   86,   86,   86,   86,   86,  252,
       86,   86,  700,  240,   86,   86,   86,   86,   86,   86,
      573,  244,   86,   86,   86,  700,  700,  242,  700,  574,
      243,  572,  241,  575,  245,  246,  700,   86,   86,   86,
      249,   86,   86,   86,  247,  577,  579,  251,  700,  253,
      573,  244,  254,  700,  580,  250,   86,   86,   86,  574,
       86,   86,   86,  575,  245,  246,   86,   86,   86,  700,

      700,  249,  700,  700,  247,  577,  579,  251,  255,  253,
       86,   86,   86,  254,  580,  250,  263,   86,   86,   86,
      581,  256,   86,   86,   86,   86,   86,   86,   86,   86,
       86,  264,   86,   86,   86,  186,  186,  186,  255,   86,
       86,   86,  700,  266,  267,  700,  700,  263,  268,  270,
      581,  256,   86,   86,   86,   86,   86,   86,  586,  700,
      265,  264,   86,   86,   86,  269,  591,  700,  275,  700,
       86,   86,   86,  266,  257,  267,   86,   86,   86,  268,
      270,  271,  700,  596,  274,  700,  258,  259,  586,  260,
      265,  272,  261,  262,  700,  700,  269,  591,  275,  273,

      285,   86,   86,   86,  598,  257,  700,   86,   86,   86,
      700,  700,  271,  596,  700,  274,  258,  259,  277,  260,
      597,  272,  261,  262,  276,   86,   86,   86,  599,  700,
      273,  285,  600,  278,  598,   86,   86,   86,   86,   86,
       86,   86,   86,   86,   86,   86,   86,  700,   86,   86,
       86,  597,   86,   86,   86,  276,   86,   86,   86,  599,
      279,  280,  700,  600,  278,  601,  700,  281,  282,  604,
      283,  286,  700,  287,  288,  700,  284,  289,  290,  700,
      700,   86,   86,   86,   86,   86,   86,   86,   86,   86,
      279,  280,   86,   86,   86,  291,  601,  281,  282,  604,

      283,  700,  286,  287,  700,  288,  284,  292,  289,  290,
      296,  298,  296,  298,  605,  297,  297,  297,  303,   86,
       86,   86,  294,  293,  700,  291,  183,  183,  183,  300,
      302,  300,  302,  700,  301,  301,  301,  700,  292,  299,
       86,   86,   86,  700,  605,  186,  186,  186,  700,  303,
      700,  700,  294,  293,   86,   86,   86,   86,   86,   86,
       86,   86,   86,   86,   86,   86,   86,   86,   86,  700,
      299,   86,   86,   86,   86,   86,   86,   86,   86,   86,
       86,   86,   86,  305,  602,  307,  700,  304,  700,  700,
      306,   86,   86,   86,   86,   86,   86,  700,  311,   86,

       86,   86,   86,   86,   86,  567,  308,   86,   86,   86,
      309,  568,  606,   96,  305,  602,  307,  304,   86,   86,
       86,  306,   86,   86,   86,  700,  310,  312,   86,   86,
       86,  700,  313,  315,  608,  567,  308,   86,   86,   86,
      309,  568,  314,  606,   96,   86,   86,   86,  321,   86,
       86,   86,   86,   86,   86,  316,  310,  700,  312,  700,
       86,   86,   86,  313,  315,  608,  609,  325,  700,  319,
      612,  320,  314,   86,   86,   86,   86,   86,   86,   86,
       86,   86,  322,  700,  700,  316,   86,   86,   86,  323,
      332,  700,  324,  700,   86,   86,   86,  609,  325,  319,

      326,  612,  320,  700,  615,  331,  334,   86,   86,  328,
       86,   86,   86,  322,  327,   86,   86,   86,  700,  323,
      700,  330,  324,   86,   86,   86,   86,   86,   86,  700,
      326,   86,   86,   86,  333,  615,  331,  610,  700,  328,
       86,   86,   86,  700,  327,   86,   86,   86,   86,   86,
       86,  330,  339,   86,   86,   86,  700,  337,  611,  338,
       86,   86,   86,  614,  617,  333,  700,  610,  340,   86,
       86,   86,   86,   86,   86,  700,  700,   86,   86,   86,
       86,   86,   86,  339,   86,   86,   86,  337,  611,  338,
       86,   86,   86,  614,  617,  341,   86,   86,   86,  340,

      700,  700,  342,  343,   86,   86,   86,   86,   86,   86,
      700,   86,   86,   86,  344,  347,  700,  542,  348,  583,
      700,  345,  584,  700,  346,  341,  618,  543,  349,   86,
       86,   86,  342,  700,  343,  700,  700,  351,   86,   86,
       86,  350,  700,  700,  344,  700,  347,  542,  619,  348,
      583,  345,  352,  584,  346,  353,  618,  543,  349,   86,
       86,   86,   86,   86,   86,   86,   86,   86,  351,  700,
      700,  350,   86,   86,   86,   86,   86,   86,  619,   86,
       86,   86,  352,  700,  700,   86,   86,   86,   86,   86,
       86,  355,  354,   86,   86,   86,   86,   86,   86,  362,

       86,   86,   86,   86,   86,  616,  620,   86,   86,   86,
       86,   86,   86,  356,  358,  359,   86,   86,   86,  360,
      621,  700,  355,  354,   86,   86,   86,  363,   86,   86,
       86,  361,  364,   86,   86,   86,  616,  620,   86,   86,
       86,  700,  700,  356,  358,  700,  359,  700,  700,  367,
      360,  621,   86,   86,   86,   86,   86,   86,  363,  700,
      368,  361,  622,  364,  365,  365,  372,  365,  365,  366,
      366,  366,  365,  365,  365,  365,  365,  365,  365,  365,
      369,  365,   86,   86,   86,  365,  365,  365,  365,  365,
      365,  368,  622,  370,  375,  623,  373,  374,  700,   86,

       86,   86,   86,   86,   86,   86,   86,   86,  371,  700,
      369,   86,   86,   86,   86,   86,   86,  365,  365,  365,
      365,   86,   86,   86,  375,  623,  373,  374,  377,  589,
      629,  378,   86,   86,   86,   86,   86,   86,  631,  371,
      376,   86,   86,   86,   86,   86,   86,  380,  700,  384,
      379,  700,  700,   86,   86,   86,  277,  628,  381,  377,
      629,  700,  378,   86,   86,   86,   86,   86,   86,  631,
      376,  700,  382,  632,   86,   86,   86,  380,  383,  385,
      379,  386,   86,   86,   86,  700,  277,  700,  628,  381,
       86,   86,   86,  388,  700,   86,   86,   86,   86,   86,

       86,  389,  382,  632,  387,   86,   86,   86,  383,  390,
      385,  386,   86,   86,   86,   86,   86,   86,  391,   86,
       86,   86,  700,  700,  388,  393,   86,   86,   86,  633,
      636,  389,  392,  394,  387,  700,  700,  396,  700,  390,
       86,   86,   86,   86,   86,   86,  395,  635,  391,   86,
       86,   86,  397,   86,   86,   86,  393,  297,  297,  297,
      633,  636,  392,  394,  301,  301,  301,  398,  396,  180,
      400,  180,   86,   86,   86,  700,  395,  635,  700,  401,
      700,  399,  397,  700,  297,  297,  297,  402,  182,  402,
      182,  700,  403,  403,  403,  700,  700,  398,   86,   86,

       86,  400,  405,  301,  301,  301,   86,   86,   86,  311,
      401,  399,  404,   86,   86,   86,   86,   86,   86,   86,
       86,   86,   86,   86,   86,   86,   86,   86,  700,  638,
       86,   86,   86,   86,   86,   86,   86,   86,   86,  639,
      410,  700,  404,  700,  645,  408,   86,   86,   86,  640,
      411,  641,  406,  412,  700,  407,   86,   86,   86,  321,
      638,  409,   86,   86,   86,   86,   86,   86,  413,  639,
      647,  410,   86,   86,   86,  645,  408,  414,  700,  640,
      411,  641,  406,  700,  412,  407,  642,  415,  643,  416,
      420,  409,  644,  421,  651,  419,  700,  700,  413,  700,

      422,  647,  700,  423,   86,   86,   86,  414,   86,   86,
       86,  425,   86,   86,   86,  700,  642,  415,  643,  416,
      700,  420,  644,  700,  421,  651,  419,   86,   86,   86,
      422,  424,  430,  423,  426,  653,   86,   86,   86,  700,
      700,  425,   86,   86,   86,   86,   86,   86,  332,  700,
      427,  428,  657,  429,   86,   86,   86,  700,   86,   86,
       86,  658,  424,  646,  700,  426,  653,  431,   86,   86,
       86,   86,   86,   86,  434,  435,   86,   86,   86,  432,
      427,  428,  436,  657,  429,   86,   86,   86,  437,  648,
      649,  438,  658,  646,  442,   86,   86,   86,  431,  433,

       86,   86,   86,  700,  650,  434,  435,  654,  655,  439,
      432,  700,  700,  436,   86,   86,   86,  700,  437,  648,
      649,  438,   86,   86,   86,  442,  443,  445,  700,  433,
      659,   86,   86,   86,  650,  661,  444,  654,  655,  439,
       86,   86,   86,   86,   86,   86,   86,   86,   86,   86,
       86,   86,  353,  662,  450,  700,  450,  443,   86,   86,
       86,  659,  664,  451,  446,  661,  444,   86,   86,   86,
       86,   86,   86,   86,   86,   86,   86,   86,   86,   86,
       86,   86,  448,  662,  447,  660,  453,   86,   86,   86,
      449,  663,  664,  454,  446,  455,   86,   86,   86,  526,

      456,   86,   86,   86,  527,  452,   86,   86,   86,   86,
       86,   86,  448,  700,  447,  457,  660,  453,  700,  700,
      449,  700,  663,  454,  700,  455,   86,   86,   86,  526,
      456,   86,   86,   86,  527,  452,  665,  459,   86,   86,
       86,   86,   86,   86,  666,  457,  700,   86,   86,   86,
      136,   86,   86,   86,   86,   86,   86,   86,   86,   86,
      700,  667,  460,   86,   86,   86,  665,  459,   86,   86,
       86,   86,   86,   86,  666,  462,  463,   86,   86,   86,
      136,  367,   86,   86,   86,   86,   86,   86,   86,   86,
       86,  667,  460,   86,   86,   86,   86,   86,   86,  464,

      668,  700,  465,  700,  700,  462,  700,  463,  700,  700,
      466,  367,  467,   86,   86,   86,  700,  669,  469,  468,
       86,   86,   86,   86,   86,   86,   86,   86,   86,  464,
      676,  668,  465,  470,   86,   86,   86,   86,   86,   86,
      466,  700,  700,  467,   86,   86,   86,  669,  469,  700,
      468,   86,   86,   86,   86,   86,   86,  700,  472,  672,
      474,  676,  700,  470,  475,   86,   86,   86,  473,   86,
       86,   86,   86,   86,   86,  476,  700,  673,  477,   86,
       86,   86,   86,   86,   86,   86,   86,   86,  472,  672,
      700,  474,   86,   86,   86,  475,  700,  700,  473,  478,

      480,  700,  479,   86,   86,   86,  476,  673,  298,  477,
      298,  481,   86,   86,   86,   86,   86,   86,  403,  403,
      403,  482,  700,  403,  403,  403,   86,   86,   86,  478,
      700,  480,  479,   86,   86,   86,  700,  674,  483,   86,
       86,   86,  481,  486,   86,   86,   86,   86,   86,   86,
       86,   86,  482,   86,   86,   86,   86,   86,   86,   86,
       86,   86,   86,   86,   86,  700,  484,  674,  700,  483,
       86,   86,   86,   86,   86,   86,  430,   86,   86,   86,
      700,  675,  489,  700,  493,  492,   86,   86,   86,  700,
      700,   86,   86,   86,  490,  491,  484,  496,   86,   86,

       86,   86,   86,   86,  679,  506,  681,  507,  509,   86,
       86,  675,  489,  502,  504,  493,  492,  700,  700,  503,
      508,   86,   86,   86,  490,  491,  700,  677,  496,   86,
       86,   86,  700,  700,  505,  679,  506,  681,  507,   86,
       86,   86,  700,  502,  682,  504,   86,   86,   86,  503,
      508,  522,   86,   86,   86,   86,   86,  677,   86,   86,
       86,   86,   86,   86,  505,   86,   86,   86,  700,  516,
      700,  510,   86,   86,   86,  682,  700,   86,   86,   86,
       86,   86,   86,  700,  700,   86,   86,   86,   86,   86,
       86,  525,   86,   86,   86,  700,   86,   86,   86,  524,

      516,  510,  518,  519,  700,  518,  537,  520,  520,  520,
      518,  518,  518,  518,  518,  518,  518,  518,  529,  518,
      535,  678,  525,  518,  518,  518,  518,  518,  518,  524,
      536,  680,  683,  370,   86,   86,   86,  537,   86,   86,
       86,   86,   86,   86,   86,   86,   86,  684,  529,  700,
      535,  678,   86,   86,   86,  518,  518,  518,  518,  700,
      536,  680,  683,  370,  539,   86,   86,   86,   86,   86,
       86,  686,  700,  538,  687,  688,  140,  541,  684,   86,
       86,   86,   86,   86,   86,  540,   86,   86,   86,   86,
       86,   86,  690,  544,  700,  539,   86,   86,   86,   86,

       86,   86,  686,  538,  687,  688,  140,  700,  541,  545,
      696,  547,   86,   86,   86,  540,  546,   86,   86,   86,
       86,   86,   86,  690,  544,  549,   86,   86,  548,  550,
       86,   86,  551,   86,   86,   86,   86,   86,   86,  700,
      545,  696,  547,   86,   86,   86,  546,   86,   86,   86,
       86,   86,   86,   86,   86,   86,  700,  700,  548,   86,
       86,   86,   86,   86,   86,   86,   86,   86,   86,   86,
       86,   86,   86,   86,   86,   86,   86,  700,   86,   86,
       86,   86,   86,   86,  700,  689,  563,   86,   86,   86,
      700,  564,   86,   86,   86,   86,   86,   86,  587,  556,

      700,  700,   86,   86,   86,  700,  565,   86,   86,   86,
       86,   86,   86,  570,  576,  689,  551,  563,  700,   86,
       86,   86,  564,   86,   86,   86,  589,  685,  692,  556,
       86,   86,   86,   86,   86,   86,  565,  588,   86,   86,
       86,  700,  700,  570,  576,  372,   86,   86,   86,   86,
       86,   86,   86,   86,   86,   86,   86,   86,  692,  594,
       86,   86,   86,  587,  595,   86,   86,   86,  588,  700,
      700,   86,   86,   86,  700,  691,  372,   86,   86,   86,
       86,   86,   86,   86,   86,   86,  693,  592,  699,  594,
      624,  700,  694,  593,  595,  603,   86,   86,   86,   86,

       86,   86,   86,   86,   86,  691,  625,  626,   86,   86,
       86,   86,   86,   86,  652,  607,  693,  592,  613,  699,
      700,  624,  694,  593,  700,  700,  603,  700,  630,   86,
       86,   86,   86,   86,   86,  698,  625,  626,   86,   86,
       86,  700,  700,  627,  700,  607,  670,  700,  613,  637,
       86,   86,   86,  700,  671,   86,   86,   86,  656,  630,
      700,  700,  700,  700,  700,  698,  700,  700,  700,  700,
      700,  700,  700,  627,  700,  700,  700,  670,  700,  637,
      700,  700,  700,  700,  671,  700,  700,  700,  700,  656,
       57,   57,   57,   57,   57,   57,   57,   57,   57,   57,

       63,   63,   63,   63,   63,   63,   63,   63,   63,   63,
       65,   65,   65,   65,   65,   65,   65,   65,   65,   65,
       68,   68,  700,   68,   68,   68,   68,   68,   68,   68,
       69,   69,   69,   69,   69,   69,   69,   69,   69,   69,
       87,  700,   87,   87,   87,   87,  169,  169,  700,  169,
      169,  700,  169,  169,  169,  169,  171,  171,  171,  171,
      171,  171,  171,  171,  171,  171,  176,  176,  176,  176,
      176,  176,  176,  176,  176,  176,  178,  178,  174,  174,
      174,  174,  174,  174,  174,  174,  174,  174,  329,  700,
      700,  700,  700,  700,  700,  329,  329,  461,  461,  700,

      461,  461,  461,  461,  461,  461,  461,  521,  521,  700,
      700,  521,  521,  521,  521,  521,  521,  554,  700,  700,
      700,  700,  554,  554,  554,  554,  518,  518,  700,  700,
      518,  518,  518,  518,  518,  518,  634,  634,  634,  634,
        9,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,

      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700
    } ;

static yyconst flex_int16_t yy_chk[3125] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    3,    3,    5,    3,    4,    4,    6,
        4,   11,   11,   12,   12,   14,    3,   14,   16,   29,

        4,   29,   25,    3,   25,   16,   25,    4,   23,   23,
       23,   28,   28,   30,   30,   25,    5,   25,   25,   25,
        6,   64,   32,   32,   32,   33,   33,   33,  318,   16,
       25,   34,   34,   34,  693,   16,   39,   39,   39,   41,
       41,   41,  676,   35,   35,   35,   32,   67,   67,   38,
       38,   38,   64,   34,   35,  623,   69,   32,   69,  318,
       34,   25,   32,  553,   34,   35,   70,   34,   70,   35,
      519,   33,   39,   35,   60,   38,   60,   32,  317,   35,
       73,   73,  179,  179,   34,   38,   60,   32,   87,   87,
       87,   34,   32,   60,   34,  487,   35,   34,  295,  215,

       35,   33,   39,  172,   35,  172,   38,  176,  317,   35,
       36,   36,   36,  173,  170,   38,   37,   37,   37,   90,
       90,   90,   71,   42,   42,   42,   79,  174,   79,  174,
      335,   79,   79,   79,   65,   36,   58,   88,   88,   88,
       44,   44,   44,   36,  248,   36,   37,   31,   36,   36,
      336,   37,   37,   42,  695,   36,  357,  410,   37,    9,
      335,   88,   44,   51,   51,   51,   36,   96,   96,   96,
        8,  695,   44,   36,  248,   36,   44,   37,   36,   36,
      336,   37,   37,  411,   42,   36,  357,  410,   37,   40,
       40,   40,   88,   44,    7,   89,   89,   89,   51,   45,

       45,   45,   44,   51,    0,  248,   44,   46,   46,   46,
       91,   91,   91,  411,   40,    0,   40,   97,   97,   97,
       89,   45,    0,   40,   40,   45,  100,  417,   51,   40,
      100,  100,  100,   51,  384,   45,   91,    0,    0,   47,
       47,   47,   46,  420,   46,   40,   46,   40,    0,   46,
       47,   89,   45,   40,   40,  422,   45,  417,   97,   40,
       43,   43,   43,    0,  384,   45,    0,   91,  405,   93,
       93,   93,   46,  420,   46,   47,   46,    0,   47,   46,
        0,   47,  101,  101,  101,  422,   43,  171,   97,  171,
       43,  418,    0,   43,   93,  384,   43,   43,  405,  171,

       43,  434,   48,   48,   48,   47,  171,  221,   47,    0,
      421,   47,  101,   48,   53,   53,   53,   43,   50,   50,
       50,   43,  418,   43,   48,   93,   43,   43,   48,  405,
       43,  434,   54,   54,   54,  113,  113,  113,    0,  435,
       50,  421,   48,  101,   48,    0,    0,   50,    0,  436,
       53,  221,  221,   53,   50,   48,  437,   50,   54,   48,
       92,   92,   92,  104,  104,  104,    0,    0,   54,  113,
      435,   50,   48,    0,   48,   49,   49,   49,   50,  436,
       53,  221,  221,   53,   50,    0,  437,   50,    0,   54,
      109,  109,  109,   92,  115,  115,  115,   49,   54,  113,

       92,   49,  423,  438,   49,    0,  104,    0,   49,   74,
      453,   74,   49,  118,  118,  118,   49,   49,  423,   49,
      454,   49,    0,   92,   74,   74,   74,    0,   49,  109,
       92,    0,   49,  423,  438,   49,  104,   74,   49,    0,
      453,   77,   49,   77,    0,    0,   49,   49,  423,   49,
      454,   49,   52,   52,   52,  440,   77,   77,   77,  109,
       78,  132,   78,    0,   78,  132,  132,  132,   74,   77,
       94,   94,   94,   78,   52,   78,   78,   78,   52,    0,
      445,   52,   52,   95,   95,   95,  440,  456,   78,    0,
        0,   52,    0,  102,  102,  102,   94,   99,   99,   99,

       77,   98,   98,   98,  102,   52,   94,  455,  494,   52,
       94,    0,   52,   52,  445,    0,   95,  456,   95,   78,
       95,   52,  103,  103,  103,   98,  501,   94,   98,    0,
      458,   99,  485,  102,   98,  526,   94,   99,  455,  494,
       94,  106,  106,  106,  488,  445,   95,  103,   95,    0,
       95,  105,  105,  105,    0,    0,   98,  501,    0,   98,
      458,   99,  485,  102,   98,  103,  526,   99,  107,  107,
      107,  106,  531,  105,  488,  108,    0,    0,  103,  108,
      108,  108,  110,  110,  110,  111,  111,  111,  114,  114,
      114,    0,  495,  112,  107,  103,  205,  112,  112,  112,

        0,    0,  106,  531,  105,  108,  116,  116,  116,  117,
      117,  117,  119,  119,  119,    0,    0,  110,  108,  120,
      120,  120,  495,  114,  497,  107,  111,  499,  116,    0,
      121,  121,  121,  117,    0,  451,  108,  112,  112,  124,
      124,  124,  205,  116,  205,  119,  117,  110,  108,  122,
      122,  122,  117,  114,  497,    0,  111,  499,  533,  116,
      120,  123,  123,  123,  117,  121,  500,  112,  112,  506,
      121,    0,  205,  116,  205,  119,  117,  508,  124,  122,
        0,  451,  117,  123,  542,  123,  125,  125,  125,  533,
      120,  126,  126,  126,    0,  121,  500,  123,  511,  506,

      121,  127,  127,  127,  128,  128,  128,  508,  124,    0,
      122,  451,  125,    0,  123,  542,  123,  129,  129,  129,
      130,  130,  130,  134,  134,  134,  127,  123,  511,  128,
        0,  512,  126,  131,  131,  131,  135,  135,  135,  136,
      136,  136,    0,  125,  133,  133,  133,  137,  137,  137,
      513,  129,  138,  138,  138,    0,    0,  127,    0,  514,
      128,  512,  126,  515,  130,  130,    0,  139,  139,  139,
      133,  140,  140,  140,  131,  523,  527,  135,    0,  137,
      513,  129,  138,    0,  528,  133,  141,  141,  141,  514,
      143,  143,  143,  515,  130,  130,  144,  144,  144,    0,

        0,  133,    0,    0,  131,  523,  527,  135,  139,  137,
      146,  146,  146,  138,  528,  133,  143,  145,  145,  145,
      530,  141,  147,  147,  147,  149,  149,  149,  153,  153,
      153,  144,  156,  156,  156,  185,  185,  185,  139,  148,
      148,  148,    0,  146,  147,    0,    0,  143,  147,  149,
      530,  141,  142,  142,  142,  150,  150,  150,  534,    0,
      145,  144,  152,  152,  152,  148,  543,    0,  153,    0,
      159,  159,  159,  146,  142,  147,  151,  151,  151,  147,
      149,  150,    0,  552,  152,    0,  142,  142,  534,  142,
      145,  150,  142,  142,    0,    0,  148,  543,  153,  151,

      159,  154,  154,  154,  557,  142,    0,  155,  155,  155,
        0,    0,  150,  552,    0,  152,  142,  142,  155,  142,
      555,  150,  142,  142,  154,  157,  157,  157,  558,    0,
      151,  159,  559,  155,  557,  158,  158,  158,  161,  161,
      161,  160,  160,  160,  162,  162,  162,    0,  164,  164,
      164,  555,  163,  163,  163,  154,  165,  165,  165,  558,
      157,  157,    0,  559,  155,  560,    0,  157,  158,  567,
      158,  160,    0,  161,  162,    0,  158,  163,  164,    0,
        0,  166,  166,  166,  167,  167,  167,  168,  168,  168,
      157,  157,  187,  187,  187,  165,  560,  157,  158,  567,

      158,    0,  160,  161,    0,  162,  158,  166,  163,  164,
      181,  183,  181,  183,  568,  181,  181,  181,  187,  188,
      188,  188,  168,  167,    0,  165,  183,  183,  183,  184,
      186,  184,  186,    0,  184,  184,  184,    0,  166,  183,
      189,  189,  189,    0,  568,  186,  186,  186,    0,  187,
        0,    0,  168,  167,  190,  190,  190,  191,  191,  191,
      192,  192,  192,  193,  193,  193,  194,  194,  194,    0,
      183,  195,  195,  195,  196,  196,  196,  197,  197,  197,
      198,  198,  198,  191,  561,  193,    0,  190,    0,    0,
      192,  199,  199,  199,  200,  200,  200,    0,  201,  203,

      203,  203,  201,  201,  201,  507,  194,  202,  202,  202,
      196,  507,  569,  199,  191,  561,  193,  190,  204,  204,
      204,  192,  207,  207,  207,    0,  198,  201,  206,  206,
      206,    0,  201,  202,  571,  507,  194,  210,  210,  210,
      196,  507,  201,  569,  199,  208,  208,  208,  209,  211,
      211,  211,  209,  209,  209,  204,  198,    0,  201,    0,
      212,  212,  212,  201,  202,  571,  572,  210,    0,  206,
      575,  208,  201,  213,  213,  213,  214,  214,  214,  217,
      217,  217,  209,    0,    0,  204,  216,  216,  216,  209,
      218,    0,  209,    0,  218,  218,  218,  572,  210,  206,

      212,  575,  208,    0,  578,  217,  220,  220,  220,  214,
      219,  219,  219,  209,  213,  222,  222,  222,    0,  209,
        0,  216,  209,  223,  223,  223,  224,  224,  224,    0,
      212,  225,  225,  225,  219,  578,  217,  573,    0,  214,
      226,  226,  226,    0,  213,  227,  227,  227,  228,  228,
      228,  216,  224,  229,  229,  229,    0,  222,  574,  223,
      230,  230,  230,  577,  580,  219,    0,  573,  226,  231,
      231,  231,  232,  232,  232,    0,    0,  233,  233,  233,
      234,  234,  234,  224,  235,  235,  235,  222,  574,  223,
      236,  236,  236,  577,  580,  229,  237,  237,  237,  226,

        0,    0,  231,  233,  238,  238,  238,  239,  239,  239,
        0,  240,  240,  240,  233,  236,    0,  471,  237,  532,
        0,  234,  532,    0,  235,  229,  581,  471,  237,  243,
      243,  243,  231,    0,  233,    0,    0,  239,  245,  245,
      245,  238,    0,    0,  233,    0,  236,  471,  582,  237,
      532,  234,  240,  532,  235,  241,  581,  471,  237,  241,
      241,  241,  242,  242,  242,  244,  244,  244,  239,    0,
        0,  238,  246,  246,  246,  247,  247,  247,  582,  249,
      249,  249,  240,    0,    0,  250,  250,  250,  251,  251,
      251,  244,  242,  253,  253,  253,  254,  254,  254,  255,

      255,  255,  256,  256,  256,  579,  583,  257,  257,  257,
      264,  264,  264,  246,  249,  250,  270,  270,  270,  253,
      584,    0,  244,  242,  276,  276,  276,  256,  277,  277,
      277,  254,  257,  279,  279,  279,  579,  583,  259,  259,
      259,    0,    0,  246,  249,    0,  250,    0,    0,  259,
      253,  584,  262,  262,  262,  261,  261,  261,  256,    0,
      259,  254,  585,  257,  258,  258,  261,  258,  258,  258,
      258,  258,  258,  258,  258,  258,  258,  258,  258,  258,
      259,  258,  260,  260,  260,  258,  258,  258,  258,  258,
      258,  259,  585,  260,  262,  586,  261,  261,    0,  263,

      263,  263,  265,  265,  265,  266,  266,  266,  260,    0,
      259,  267,  267,  267,  268,  268,  268,  258,  258,  258,
      258,  278,  278,  278,  262,  586,  261,  261,  265,  589,
      591,  266,  269,  269,  269,  271,  271,  271,  594,  260,
      263,  272,  272,  272,  274,  274,  274,  268,    0,  273,
      267,    0,    0,  273,  273,  273,  278,  589,  269,  265,
      591,    0,  266,  275,  275,  275,  280,  280,  280,  594,
      263,    0,  271,  595,  281,  281,  281,  268,  272,  273,
      267,  274,  282,  282,  282,    0,  278,    0,  589,  269,
      283,  283,  283,  280,    0,  284,  284,  284,  285,  285,

      285,  280,  271,  595,  275,  286,  286,  286,  272,  281,
      273,  274,  287,  287,  287,  291,  291,  291,  282,  288,
      288,  288,    0,    0,  280,  284,  289,  289,  289,  596,
      602,  280,  283,  285,  275,    0,    0,  287,    0,  281,
      290,  290,  290,  292,  292,  292,  286,  600,  282,  293,
      293,  293,  288,  294,  294,  294,  284,  296,  296,  296,
      596,  602,  283,  285,  300,  300,  300,  289,  287,  297,
      292,  297,  303,  303,  303,    0,  286,  600,    0,  294,
        0,  290,  288,    0,  297,  297,  297,  299,  301,  299,
      301,    0,  299,  299,  299,    0,    0,  289,  304,  304,

      304,  292,  305,  301,  301,  301,  305,  305,  305,  311,
      294,  290,  303,  306,  306,  306,  307,  307,  307,  308,
      308,  308,  309,  309,  309,  310,  310,  310,    0,  605,
      312,  312,  312,  313,  313,  313,  314,  314,  314,  608,
      311,    0,  303,    0,  615,  308,  315,  315,  315,  609,
      311,  610,  306,  312,    0,  307,  316,  316,  316,  321,
      605,  309,  319,  319,  319,  320,  320,  320,  313,  608,
      617,  311,  323,  323,  323,  615,  308,  314,    0,  609,
      311,  610,  306,    0,  312,  307,  611,  315,  612,  316,
      321,  309,  614,  321,  622,  320,    0,    0,  313,    0,

      321,  617,    0,  321,  322,  322,  322,  314,  324,  324,
      324,  323,  325,  325,  325,    0,  611,  315,  612,  316,
        0,  321,  614,    0,  321,  622,  320,  326,  326,  326,
      321,  322,  327,  321,  324,  624,  327,  327,  327,    0,
        0,  323,  328,  328,  328,  329,  329,  329,  332,    0,
      324,  325,  628,  326,  330,  330,  330,    0,  331,  331,
      331,  631,  322,  616,    0,  324,  624,  328,  333,  333,
      333,  337,  337,  337,  332,  332,  338,  338,  338,  330,
      324,  325,  332,  628,  326,  339,  339,  339,  332,  618,
      619,  332,  631,  616,  337,  341,  341,  341,  328,  331,

      340,  340,  340,    0,  621,  332,  332,  625,  626,  333,
      330,    0,    0,  332,  342,  342,  342,    0,  332,  618,
      619,  332,  344,  344,  344,  337,  340,  343,    0,  331,
      632,  343,  343,  343,  621,  636,  341,  625,  626,  333,
      345,  345,  345,  346,  346,  346,  347,  347,  347,  348,
      348,  348,  353,  638,  349,    0,  450,  340,  349,  349,
      349,  632,  640,  350,  344,  636,  341,  350,  350,  350,
      351,  351,  351,  352,  352,  352,  354,  354,  354,  355,
      355,  355,  348,  638,  346,  635,  353,  356,  356,  356,
      348,  639,  640,  353,  344,  353,  358,  358,  358,  450,

      353,  359,  359,  359,  450,  351,  360,  360,  360,  361,
      361,  361,  348,    0,  346,  354,  635,  353,    0,    0,
      348,    0,  639,  353,    0,  353,  363,  363,  363,  450,
      353,  364,  364,  364,  450,  351,  642,  359,  367,  367,
      367,  368,  368,  368,  643,  354,    0,  369,  369,  369,
      361,  370,  370,  370,  371,  371,  371,  372,  372,  372,
        0,  644,  363,  373,  373,  373,  642,  359,  374,  374,
      374,  375,  375,  375,  643,  368,  371,  376,  376,  376,
      361,  369,  377,  377,  377,  380,  380,  380,  378,  378,
      378,  644,  363,  379,  379,  379,  381,  381,  381,  373,

      646,    0,  374,    0,    0,  368,    0,  371,    0,    0,
      375,  369,  378,  382,  382,  382,    0,  647,  380,  379,
      383,  383,  383,  385,  385,  385,  386,  386,  386,  373,
      658,  646,  374,  381,  387,  387,  387,  388,  388,  388,
      375,    0,    0,  378,  389,  389,  389,  647,  380,    0,
      379,  390,  390,  390,  391,  391,  391,    0,  385,  653,
      387,  658,    0,  381,  388,  392,  392,  392,  385,  393,
      393,  393,  394,  394,  394,  390,    0,  654,  391,  395,
      395,  395,  396,  396,  396,  397,  397,  397,  385,  653,
        0,  387,  398,  398,  398,  388,    0,    0,  385,  392,

      394,    0,  393,  400,  400,  400,  390,  654,  403,  391,
      403,  397,  399,  399,  399,  401,  401,  401,  402,  402,
      402,  398,    0,  403,  403,  403,  404,  404,  404,  392,
        0,  394,  393,  406,  406,  406,    0,  655,  399,  407,
      407,  407,  397,  408,  408,  408,  409,  409,  409,  412,
      412,  412,  398,  413,  413,  413,  414,  414,  414,  415,
      415,  415,  416,  416,  416,    0,  404,  655,    0,  399,
      419,  419,  419,  424,  424,  424,  430,  425,  425,  425,
        0,  657,  412,    0,  416,  415,  426,  426,  426,    0,
        0,  427,  427,  427,  413,  414,  404,  419,  428,  428,

      428,  429,  429,  429,  662,  430,  671,  430,  431,  431,
      431,  657,  412,  425,  427,  416,  415,    0,    0,  426,
      430,  432,  432,  432,  413,  414,    0,  659,  419,  433,
      433,  433,    0,    0,  428,  662,  430,  671,  430,  439,
      439,  439,    0,  425,  672,  427,  442,  442,  442,  426,
      430,  444,  444,  444,  447,  447,  447,  659,  446,  446,
      446,  448,  448,  448,  428,  449,  449,  449,    0,  439,
        0,  433,  457,  457,  457,  672,    0,  452,  452,  452,
      462,  462,  462,    0,    0,  459,  459,  459,  469,  469,
      469,  449,  463,  463,  463,    0,  460,  460,  460,  446,

      439,  433,  443,  443,    0,  443,  462,  443,  443,  443,
      443,  443,  443,  443,  443,  443,  443,  443,  452,  443,
      459,  660,  449,  443,  443,  443,  443,  443,  443,  446,
      460,  670,  674,  463,  464,  464,  464,  462,  465,  465,
      465,  466,  466,  466,  467,  467,  467,  675,  452,    0,
      459,  660,  468,  468,  468,  443,  443,  443,  443,    0,
      460,  670,  674,  463,  465,  470,  470,  470,  472,  472,
      472,  677,    0,  464,  678,  679,  466,  468,  675,  473,
      473,  473,  474,  474,  474,  467,  475,  475,  475,  476,
      476,  476,  681,  472,    0,  465,  477,  477,  477,  478,

      478,  478,  677,  464,  678,  679,  466,    0,  468,  473,
      694,  476,  479,  479,  479,  467,  474,  480,  480,  480,
      481,  481,  481,  681,  472,  482,  482,  482,  477,  483,
      483,  483,  484,  489,  489,  489,  484,  484,  484,    0,
      473,  694,  476,  490,  490,  490,  474,  491,  491,  491,
      492,  492,  492,  493,  493,  493,    0,    0,  477,  496,
      496,  496,  502,  502,  502,  503,  503,  503,  504,  504,
      504,  505,  505,  505,  510,  510,  510,    0,  516,  516,
      516,  521,  521,  521,    0,  680,  502,  524,  524,  524,
        0,  503,  525,  525,  525,  529,  529,  529,  535,  493,

        0,    0,  535,  535,  535,    0,  505,  536,  536,  536,
      537,  537,  537,  510,  516,  680,  551,  502,    0,  538,
      538,  538,  503,  539,  539,  539,  540,  685,  688,  493,
      540,  540,  540,  541,  541,  541,  505,  536,  544,  544,
      544,    0,    0,  510,  516,  538,  545,  545,  545,  546,
      546,  546,  547,  547,  547,  548,  548,  548,  688,  551,
      556,  556,  556,  587,  551,  563,  563,  563,  536,    0,
        0,  564,  564,  564,    0,  685,  538,  565,  565,  565,
      570,  570,  570,  576,  576,  576,  689,  545,  698,  551,
      587,    0,  691,  547,  551,  564,  588,  588,  588,  592,

      592,  592,  593,  593,  593,  685,  587,  587,  603,  603,
      603,  607,  607,  607,  652,  570,  689,  545,  576,  698,
        0,  587,  691,  547,    0,    0,  564,    0,  593,  613,
      613,  613,  627,  627,  627,  696,  587,  587,  630,  630,
      630,    0,    0,  588,    0,  570,  652,    0,  576,  603,
      637,  637,  637,    0,  652,  656,  656,  656,  627,  593,
        0,    0,    0,    0,    0,  696,    0,    0,    0,    0,
        0,    0,    0,  588,    0,    0,    0,  652,    0,  603,
        0,    0,    0,    0,  652,    0,    0,    0,    0,  627,
      701,  701,  701,  701,  701,  701,  701,  701,  701,  701,

      702,  702,  702,  702,  702,  702,  702,  702,  702,  702,
      703,  703,  703,  703,  703,  703,  703,  703,  703,  703,
      704,  704,    0,  704,  704,  704,  704,  704,  704,  704,
      705,  705,  705,  705,  705,  705,  705,  705,  705,  705,
      706,    0,  706,  706,  706,  706,  707,  707,    0,  707,
      707,    0,  707,  707,  707,  707,  708,  708,  708,  708,
      708,  708,  708,  708,  708,  708,  709,  709,  709,  709,
      709,  709,  709,  709,  709,  709,  710,  710,  711,  711,
      711,  711,  711,  711,  711,  711,  711,  711,  712,    0,
        0,    0,    0,    0,    0,  712,  712,  713,  713,    0,

      713,  713,  713,  713,  713,  713,  713,  714,  714,    0,
        0,  714,  714,  714,  714,  714,  714,  715,    0,    0,
        0,    0,  715,  715,  715,  715,  716,  716,    0,    0,
      716,  716,  716,  716,  716,  716,  717,  717,  717,  717,
      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,

      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700,  700,  700,  700,  700,  700,  700,
      700,  700,  700,  700
    } ;

static yy_state_type yy_last_accepting_state;
static char *yy_last_accepting_cpos;

extern int yy_flex_debug;
int yy_flex_debug = 0;

/* The intent behind this definition is that it'll catch
 * any uses of REJECT which flex missed.
 */
#define REJECT reject_used_but_not_detected
#define yymore() yymore_used_but_not_detected
#define YY_MORE_ADJ 0
#define YY_RESTORE_YY_MORE_OFFSET
char *yytext;
#line 1 "bas_token.l"
/* Tokens and token sequence arrays. */
#line 3 "bas_token.l"
/* #includes */ /*{{{C}}}*//*{{{*/
#include <nuttx/config.h>

#include <assert.h>
#include <ctype.h>
#include <float.h>
#include <limits.h>
#include <math.h>
#include <stddef.h>
#include <stdlib.h>
#include <string.h>
#include <termios.h>

#include "bas_auto.h"
#include "bas_token.h"
#include "bas_statement.h"

static int g_matchdata;
static int g_backslash_colon;
static int g_uppercase;
static struct Token *cur;
int yylex(void);

static void string(const char *text) /*{{{*/
{
  if (cur)
  {
    const char *t;
    char *q;
    size_t l;

    for (t=text+1,l=0; *(t+1); ++t,++l)
    {
      if (*t=='"') ++t;
    }
    cur->u.string=malloc(sizeof(struct String));
    String_size(String_new(cur->u.string),l);
    for (t=text+1,q=cur->u.string->character; *(t+1); ++t,++q)
    {
      *q=*t;
      if (*t=='"') ++t;
    }
  }
}
/*}}}*/
static void string2(void) /*{{{*/
{
  if (cur)
  {
    char *t,*q;
    size_t l;

    for (t=yytext+1,l=0; *t; ++t,++l)
    {
      if (*t=='"') ++t;
    }
    cur->u.string=malloc(sizeof(struct String));
    String_size(String_new(cur->u.string),l);
    for (t=yytext+1,q=cur->u.string->character; *t; ++t,++q)
    {
      *q=*t;
      if (*t=='"') ++t;
    }
  }
}
/*}}}*/
/* flex options and definitions */ /*{{{*/

/*}}}*/
#line 1463 "<stdout>"

#define INITIAL 0
#define DATAINPUT 1
#define ELSEIF 2
#define IMAGEFMT 3

#ifndef YY_NO_UNISTD_H
/* Special case for "unistd.h", since it is non-ANSI. We include it way
 * down here because we want the user's section 1 to have been scanned first.
 * The user has a chance to override it with an option.
 */
#include <unistd.h>
#endif

#ifndef YY_EXTRA_TYPE
#define YY_EXTRA_TYPE void *
#endif

static int yy_init_globals (void);

/* Accessor methods to globals.
   These are made visible to non-reentrant scanners for convenience. */

int yylex_destroy (void);

int yyget_debug (void);

void yyset_debug (int debug_flag);

YY_EXTRA_TYPE yyget_extra (void);

void yyset_extra (YY_EXTRA_TYPE user_defined);

FILE *yyget_in (void);

void yyset_in  (FILE * in_str);

FILE *yyget_out (void);

void yyset_out  (FILE * out_str);

yy_size_t yyget_leng (void);

char *yyget_text (void);

int yyget_lineno (void);

void yyset_lineno (int line_number);

/* Macros after this point can all be overridden by user definitions in
 * section 1.
 */

#ifndef YY_SKIP_YYWRAP
#ifdef __cplusplus
extern "C" int yywrap (void);
#else
extern int yywrap (void);
#endif
#endif

#ifndef yytext_ptr
static void yy_flex_strncpy (char *,yyconst char *,int);
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen (yyconst char *);
#endif

/* Amount of stuff to slurp up with each read. */
#ifndef YY_READ_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k */
#define YY_READ_BUF_SIZE 16384
#else
#define YY_READ_BUF_SIZE 8192
#endif /* __ia64__ */
#endif

/* Copy whatever the last rule matched to the standard output. */
#ifndef ECHO
/* This used to be an fputs(), but since the string might contain NUL's,
 * we now use fwrite().
 */
#define ECHO do { if (fwrite(yytext, yyleng, 1, yyout)) {} } while (0)
#endif

/* Gets input and stuffs it into "buf".  number of characters read, or YY_NULL,
 * is returned in "result".
 */
#ifndef YY_INPUT
#define YY_INPUT(buf,result,max_size) \
  if (YY_CURRENT_BUFFER_LVALUE->yy_is_interactive) \
    { \
    int c = '*'; \
    size_t n; \
    for (n = 0; n < max_size && \
           (c = getc(yyin)) != EOF && c != '\n'; ++n) \
      buf[n] = (char) c; \
    if (c == '\n') \
      buf[n++] = (char) c; \
    if (c == EOF && ferror(yyin)) \
      YY_FATAL_ERROR("input in flex scanner failed"); \
    result = n; \
    } \
  else \
    { \
    errno=0; \
    while ((result = fread(buf, 1, max_size, yyin))==0 && ferror(yyin)) \
      { \
      if(errno != EINTR) \
        { \
        YY_FATAL_ERROR("input in flex scanner failed"); \
        break; \
        } \
      errno=0; \
      clearerr(yyin); \
      } \
    }\
\

#endif

/* No semi-colon after return; correct usage is to write "yyterminate();" -
 * we don't want an extra ';' after the "return" because that will cause
 * some compilers to complain about unreachable statements.
 */
#ifndef yyterminate
#define yyterminate() return YY_NULL
#endif

/* Number of entries by which start-condition stack grows. */
#ifndef YY_START_STACK_INCR
#define YY_START_STACK_INCR 25
#endif

/* Report a fatal error. */
#ifndef YY_FATAL_ERROR
#define YY_FATAL_ERROR(msg) yy_fatal_error(msg)
#endif

/* end tables serialization structures and prototypes */

/* Default declaration of generated scanner - a define so the user can
 * easily add parameters.
 */
#ifndef YY_DECL
#define YY_DECL_IS_OURS 1

extern int yylex (void);

#define YY_DECL int yylex (void)
#endif /* !YY_DECL */

/* Code executed at the beginning of each rule, after yytext and yyleng
 * have been set up.
 */
#ifndef YY_USER_ACTION
#define YY_USER_ACTION
#endif

/* Code executed at the end of each rule. */
#ifndef YY_BREAK
#define YY_BREAK break;
#endif

#define YY_RULE_SETUP \
  YY_USER_ACTION

/** The main scanner function which does all the work.
 */
YY_DECL
{
  register yy_state_type yy_current_state;
  register char *yy_cp, *yy_bp;
  register int yy_act;

  if (!(yy_init))
    {
    (yy_init) = 1;

#ifdef YY_USER_INIT
    YY_USER_INIT;
#endif

    if (! (yy_start))
      (yy_start) = 1;  /* first start state */

    if (! yyin)
      yyin = stdin;

    if (! yyout)
      yyout = stdout;

    if (! YY_CURRENT_BUFFER) {
      yyensure_buffer_stack ();
      YY_CURRENT_BUFFER_LVALUE =
        yy_create_buffer(yyin,YY_BUF_SIZE);
    }

    yy_load_buffer_state();
    }

  {
#line 102 "bas_token.l"

 /* flex rules */ /*{{{*/
  if (g_matchdata) BEGIN(DATAINPUT);

#line 1683 "<stdout>"

  while (1)    /* loops until end-of-file is reached */
    {
    yy_cp = (yy_c_buf_p);

    /* Support of yytext. */
    *yy_cp = (yy_hold_char);

    /* yy_bp points to the position in yy_ch_buf of the start of
     * the current run.
     */
    yy_bp = yy_cp;

    yy_current_state = (yy_start);
yy_match:
    do
      {
      register YY_CHAR yy_c = yy_ec[YY_SC_TO_UI(*yy_cp)] ;
      if (yy_accept[yy_current_state])
        {
        (yy_last_accepting_state) = yy_current_state;
        (yy_last_accepting_cpos) = yy_cp;
        }
      while (yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state)
        {
        yy_current_state = (int) yy_def[yy_current_state];
        if (yy_current_state >= 701)
          yy_c = yy_meta[(unsigned int) yy_c];
        }
      yy_current_state = yy_nxt[yy_base[yy_current_state] + (unsigned int) yy_c];
      ++yy_cp;
      }
    while (yy_base[yy_current_state] != 3041);

yy_find_action:
    yy_act = yy_accept[yy_current_state];
    if (yy_act == 0)
      { /* have to back up */
      yy_cp = (yy_last_accepting_cpos);
      yy_current_state = (yy_last_accepting_state);
      yy_act = yy_accept[yy_current_state];
      }

    YY_DO_BEFORE_ACTION;

do_action:  /* This label is used only to access EOF actions. */

    switch (yy_act)
  { /* beginning of action switch */
      case 0: /* must back up */
      /* undo the effects of YY_DO_BEFORE_ACTION */
      *yy_cp = (yy_hold_char);
      yy_cp = (yy_last_accepting_cpos);
      yy_current_state = (yy_last_accepting_state);
      goto yy_find_action;

case 1:
YY_RULE_SETUP
#line 106 "bas_token.l"
return T_CHANNEL;
  YY_BREAK
case 2:
YY_RULE_SETUP
#line 107 "bas_token.l"
{
        int overflow;
        double d;

                    d=Value_vald(yytext,(char**)0,&overflow);
                    if (overflow)
        {
          if (cur) cur->u.junk=yytext[0];
          yyless(1);
          return T_JUNK;
        }
        if (cur) cur->u.real=d;
                    return T_REAL;
                  }
  YY_BREAK
case 3:
YY_RULE_SETUP
#line 121 "bas_token.l"
{
        int overflow;
        long int n;

                    n=Value_vali(yytext,(char**)0,&overflow);
        if (overflow)
        {
          double d;

          d=Value_vald(yytext,(char**)0,&overflow);
          if (overflow)
          {
            if (cur) cur->u.junk=yytext[0];
            yyless(1);
            return T_JUNK;
          }
          if (cur) cur->u.real=d;
          return T_REAL;
        }
                    if (cur) cur->u.integer=n;
                      return T_INTEGER;
                  }
  YY_BREAK
case 4:
YY_RULE_SETUP
#line 143 "bas_token.l"
{
        int overflow;
        long int n;

        n=Value_vali(yytext,(char**)0,&overflow);
                    if (overflow)
                    {
                      if (cur) cur->u.junk=yytext[0];
                      yyless(1);
                            return T_JUNK;
                    }
                    if (cur) cur->u.hexinteger=n;
                    return T_HEXINTEGER;
                  }
  YY_BREAK
case 5:
YY_RULE_SETUP
#line 157 "bas_token.l"
{
        int overflow;
        long int n;

                    n=Value_vali(yytext,(char**)0,&overflow);
                    if (overflow)
                    {
                      if (cur) cur->u.junk=yytext[0];
                      yyless(1);
                            return T_JUNK;
                    }
                    if (cur) cur->u.octinteger=n;
                    return T_OCTINTEGER;
                  }
  YY_BREAK
case 6:
/* rule 6 can match eol */
YY_RULE_SETUP
#line 171 "bas_token.l"
string(yytext); return T_STRING;
  YY_BREAK
case 7:
/* rule 7 can match eol */
*yy_cp = (yy_hold_char); /* undo effects of setting up yytext */
YY_LINENO_REWIND_TO(yy_cp - 1);
(yy_c_buf_p) = yy_cp -= 1;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
#line 172 "bas_token.l"
string2(); return T_STRING;
  YY_BREAK
case 8:
YY_RULE_SETUP
#line 173 "bas_token.l"
return T_OP;
  YY_BREAK
case 9:
YY_RULE_SETUP
#line 174 "bas_token.l"
return T_CP;
  YY_BREAK
case 10:
YY_RULE_SETUP
#line 175 "bas_token.l"
return T_MULT;
  YY_BREAK
case 11:
YY_RULE_SETUP
#line 176 "bas_token.l"
return T_PLUS;
  YY_BREAK
case 12:
YY_RULE_SETUP
#line 177 "bas_token.l"
return T_MINUS;
  YY_BREAK
case 13:
YY_RULE_SETUP
#line 178 "bas_token.l"
return T_COMMA;
  YY_BREAK
case 14:
YY_RULE_SETUP
#line 179 "bas_token.l"
return T_DIV;
  YY_BREAK
case 15:
YY_RULE_SETUP
#line 180 "bas_token.l"
{
                          if (g_backslash_colon)
                          {
                            if (cur) cur->statement=stmt_COLON_EOL;
                            return T_COLON;
                          }
                          return T_IDIV;
                        }
  YY_BREAK
case 16:
YY_RULE_SETUP
#line 188 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_COLON_EOL;
                          }
                          return T_COLON;
                        }
  YY_BREAK
case 17:
YY_RULE_SETUP
#line 195 "bas_token.l"
return T_SEMICOLON;
  YY_BREAK
case 18:
YY_RULE_SETUP
#line 196 "bas_token.l"
return T_LT;
  YY_BREAK
case 19:
YY_RULE_SETUP
#line 197 "bas_token.l"
return T_LE;
  YY_BREAK
case 20:
YY_RULE_SETUP
#line 198 "bas_token.l"
return T_LE;
  YY_BREAK
case 21:
YY_RULE_SETUP
#line 199 "bas_token.l"
return T_NE;
  YY_BREAK
case 22:
YY_RULE_SETUP
#line 200 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_EQ_FNRETURN_FNEND;
                          }
                          return T_EQ;
                        }
  YY_BREAK
case 23:
YY_RULE_SETUP
#line 207 "bas_token.l"
return T_GT;
  YY_BREAK
case 24:
YY_RULE_SETUP
#line 208 "bas_token.l"
return T_GE;
  YY_BREAK
case 25:
YY_RULE_SETUP
#line 209 "bas_token.l"
return T_GE;
  YY_BREAK
case 26:
YY_RULE_SETUP
#line 210 "bas_token.l"
return T_POW;
  YY_BREAK
case 27:
YY_RULE_SETUP
#line 211 "bas_token.l"
return T_ACCESS_READ;
  YY_BREAK
case 28:
YY_RULE_SETUP
#line 212 "bas_token.l"
return T_ACCESS_READ_WRITE;
  YY_BREAK
case 29:
YY_RULE_SETUP
#line 213 "bas_token.l"
return T_ACCESS_WRITE;
  YY_BREAK
case 30:
YY_RULE_SETUP
#line 214 "bas_token.l"
return T_AND;
  YY_BREAK
case 31:
YY_RULE_SETUP
#line 215 "bas_token.l"
return T_AS;
  YY_BREAK
case 32:
YY_RULE_SETUP
#line 216 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_CALL;
                          }
                          return T_CALL;
                        }
  YY_BREAK
case 33:
YY_RULE_SETUP
#line 223 "bas_token.l"
{
        if (cur)
        {
          cur->statement=stmt_CASE;
          cur->u.casevalue=malloc(sizeof(struct Casevalue));
        }
        return T_CASEELSE;
      }
  YY_BREAK
case 34:
YY_RULE_SETUP
#line 231 "bas_token.l"
{
        if (cur)
        {
          cur->statement=stmt_CASE;
          cur->u.casevalue=malloc(sizeof(struct Casevalue));
        }
        return T_CASEVALUE;
      }
  YY_BREAK
case 35:
YY_RULE_SETUP
#line 239 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_CHDIR_MKDIR;
                          }
                          return T_CHDIR;
                        }
  YY_BREAK
case 36:
YY_RULE_SETUP
#line 246 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_CLEAR;
                          }
                          return T_CLEAR;
                        }
  YY_BREAK
case 37:
YY_RULE_SETUP
#line 253 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_CLOSE;
                          }
                          return T_CLOSE;
                        }
  YY_BREAK
case 38:
*yy_cp = (yy_hold_char); /* undo effects of setting up yytext */
(yy_c_buf_p) = yy_cp = yy_bp + 5;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
#line 260 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_CLOSE;
                          }
                          return T_CLOSE;
                        }
  YY_BREAK
case 39:
YY_RULE_SETUP
#line 267 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_CLS;
                          }
                          return T_CLS;
                        }
  YY_BREAK
case 40:
YY_RULE_SETUP
#line 274 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_COLOR;
                          }
                          return T_COLOR;
                        }
  YY_BREAK
case 41:
YY_RULE_SETUP
#line 281 "bas_token.l"
return T_CON;
  YY_BREAK
case 42:
YY_RULE_SETUP
#line 282 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_COPY_RENAME;
                          }
                          return T_COPY;
                        }
  YY_BREAK
case 43:
YY_RULE_SETUP
#line 289 "bas_token.l"
{
                          BEGIN(DATAINPUT);
                          if (cur)
                          {
                            cur->statement=stmt_DATA;
                          }
                          return T_DATA;
                        }
  YY_BREAK
case 44:
/* rule 44 can match eol */
YY_RULE_SETUP
#line 297 "bas_token.l"
string(yytext); return T_STRING;
  YY_BREAK
case 45:
/* rule 45 can match eol */
*yy_cp = (yy_hold_char); /* undo effects of setting up yytext */
YY_LINENO_REWIND_TO(yy_cp - 1);
(yy_c_buf_p) = yy_cp -= 1;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
#line 298 "bas_token.l"
string2(); return T_STRING;
  YY_BREAK
case 46:
YY_RULE_SETUP
#line 299 "bas_token.l"
return T_COMMA;
  YY_BREAK
case 47:
YY_RULE_SETUP
#line 300 "bas_token.l"
{
                    if (cur) cur->u.datainput=strcpy(malloc(strlen(yytext)+1),yytext);
        return T_DATAINPUT;
      }
  YY_BREAK
case 48:
YY_RULE_SETUP
#line 304 "bas_token.l"

  YY_BREAK
case 49:
/* rule 49 can match eol */
YY_RULE_SETUP
#line 305 "bas_token.l"
BEGIN(INITIAL);
  YY_BREAK
case 50:
YY_RULE_SETUP
#line 306 "bas_token.l"
BEGIN(INITIAL); return T_COLON;
  YY_BREAK
case 51:
YY_RULE_SETUP
#line 307 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_DEC_INC;
                          }
                          return T_DEC;
                        }
  YY_BREAK
case 52:
YY_RULE_SETUP
#line 314 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_DEFINT_DEFDBL_DEFSTR;
                          }
                          return T_DEFDBL;
                        }
  YY_BREAK
case 53:
YY_RULE_SETUP
#line 321 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_DEFINT_DEFDBL_DEFSTR;
                          }
                          return T_DEFINT;
                        }
  YY_BREAK
case 54:
YY_RULE_SETUP
#line 328 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_DEFINT_DEFDBL_DEFSTR;
                          }
                          return T_DEFSTR;
                        }
  YY_BREAK
case 55:
*yy_cp = (yy_hold_char); /* undo effects of setting up yytext */
(yy_c_buf_p) = yy_cp = yy_bp + 3;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
#line 335 "bas_token.l"
{
                    if (cur)
                    {
                      cur->statement=stmt_DEFFN_DEFPROC_FUNCTION_SUB;
                      cur->u.localSyms=(struct Symbol*)0;
                    }
                    return T_DEFFN;
                  }
  YY_BREAK
case 56:
*yy_cp = (yy_hold_char); /* undo effects of setting up yytext */
(yy_c_buf_p) = yy_cp = yy_bp + 3;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
#line 343 "bas_token.l"
{
                    if (cur)
                    {
                      cur->statement=stmt_DEFFN_DEFPROC_FUNCTION_SUB;
                      cur->u.localSyms=(struct Symbol*)0;
                    }
                    return T_DEFPROC;
                  }
  YY_BREAK
case 57:
YY_RULE_SETUP
#line 351 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_DELETE;
                          }
                          return T_DELETE;
                        }
  YY_BREAK
case 58:
YY_RULE_SETUP
#line 358 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_DIM;
                          }
                          return T_DIM;
                        }
  YY_BREAK
case 59:
YY_RULE_SETUP
#line 365 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_DISPLAY;
                          }
                          return T_DISPLAY;
                        }
  YY_BREAK
case 60:
YY_RULE_SETUP
#line 372 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_DO;
                          }
                          return T_DO;
                        }
  YY_BREAK
case 61:
YY_RULE_SETUP
#line 379 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_DOcondition;
                          }
                          return T_DOUNTIL;
                        }
  YY_BREAK
case 62:
YY_RULE_SETUP
#line 386 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_DOcondition;
                          }
                          return T_DOWHILE;
                        }
  YY_BREAK
case 63:
YY_RULE_SETUP
#line 393 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_EDIT;
                          }
                          return T_EDIT;
                        }
  YY_BREAK
case 64:
YY_RULE_SETUP
#line 400 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_ELSE_ELSEIFELSE;
                          }
                          return T_ELSE;
                        }
  YY_BREAK
case 65:
*yy_cp = (yy_hold_char); /* undo effects of setting up yytext */
(yy_c_buf_p) = yy_cp = yy_bp + 4;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
#line 407 "bas_token.l"
{
                          BEGIN(ELSEIF);
                          if (cur)
                          {
                            cur->statement=stmt_ELSE_ELSEIFELSE;
                          }
                          return T_ELSEIFELSE;
                        }
  YY_BREAK
case 66:
YY_RULE_SETUP
#line 415 "bas_token.l"
{
                          BEGIN(INITIAL);
                          if (cur)
                          {
                            cur->statement=stmt_IF_ELSEIFIF;
                          }
                          return T_ELSEIFIF;
                        }
  YY_BREAK
case 67:
YY_RULE_SETUP
#line 423 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_ENDFN;
                          }
                          return T_ENDFN;
                        }
  YY_BREAK
case 68:
YY_RULE_SETUP
#line 430 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_ENDIF;
                          }
                          return T_ENDIF;
                        }
  YY_BREAK
case 69:
YY_RULE_SETUP
#line 437 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_ENDPROC_SUBEND;
                          }
                          return T_ENDPROC;
                        }
  YY_BREAK
case 70:
YY_RULE_SETUP
#line 444 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_ENDSELECT;
                          }
                          return T_ENDSELECT;
                        }
  YY_BREAK
case 71:
YY_RULE_SETUP
#line 451 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_ENDPROC_SUBEND;
                          }
                          return T_SUBEND;
                        }
  YY_BREAK
case 72:
YY_RULE_SETUP
#line 458 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_END;
                          }
                          return T_END;
                        }
  YY_BREAK
case 73:
YY_RULE_SETUP
#line 465 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_ENVIRON;
                          }
                          return T_ENVIRON;
                        }
  YY_BREAK
case 74:
YY_RULE_SETUP
#line 472 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_ERASE;
                          }
                          return T_ERASE;
                        }
  YY_BREAK
case 75:
YY_RULE_SETUP
#line 479 "bas_token.l"
return T_EQV;
  YY_BREAK
case 76:
YY_RULE_SETUP
#line 480 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_EXITDO;
                          }
                          return T_EXITDO;
                        }
  YY_BREAK
case 77:
YY_RULE_SETUP
#line 487 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_EXITFOR;
                          }
                          return T_EXITFOR;
                        }
  YY_BREAK
case 78:
YY_RULE_SETUP
#line 494 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_FNEXIT;
                          }
                          return T_FNEXIT;
                        }
  YY_BREAK
case 79:
YY_RULE_SETUP
#line 501 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_SUBEXIT;
                          }
                          return T_SUBEXIT;
                        }
  YY_BREAK
case 80:
YY_RULE_SETUP
#line 508 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_FIELD;
                          }
                          return T_FIELD;
                        }
  YY_BREAK
case 81:
*yy_cp = (yy_hold_char); /* undo effects of setting up yytext */
(yy_c_buf_p) = yy_cp = yy_bp + 5;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
#line 515 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_FIELD;
                          }
                          return T_FIELD;
                        }
  YY_BREAK
case 82:
YY_RULE_SETUP
#line 522 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_EQ_FNRETURN_FNEND;
                          }
                          return T_FNEND;
                        }
  YY_BREAK
case 83:
YY_RULE_SETUP
#line 529 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_EQ_FNRETURN_FNEND;
                          }
                          return T_FNRETURN;
                        }
  YY_BREAK
case 84:
YY_RULE_SETUP
#line 536 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_FOR;
                          }
                          return T_FOR;
                        }
  YY_BREAK
case 85:
YY_RULE_SETUP
#line 543 "bas_token.l"
return T_FOR_INPUT;
  YY_BREAK
case 86:
YY_RULE_SETUP
#line 544 "bas_token.l"
return T_FOR_OUTPUT;
  YY_BREAK
case 87:
YY_RULE_SETUP
#line 545 "bas_token.l"
return T_FOR_APPEND;
  YY_BREAK
case 88:
YY_RULE_SETUP
#line 546 "bas_token.l"
return T_FOR_RANDOM;
  YY_BREAK
case 89:
YY_RULE_SETUP
#line 547 "bas_token.l"
return T_FOR_BINARY;
  YY_BREAK
case 90:
YY_RULE_SETUP
#line 548 "bas_token.l"
{
                    if (cur)
                    {
                      cur->statement=stmt_DEFFN_DEFPROC_FUNCTION_SUB;
                      cur->u.localSyms=(struct Symbol*)0;
                    }
                    return T_FUNCTION;
                  }
  YY_BREAK
case 91:
YY_RULE_SETUP
#line 556 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_GET_PUT;
                          }
                          return T_GET;
                        }
  YY_BREAK
case 92:
*yy_cp = (yy_hold_char); /* undo effects of setting up yytext */
(yy_c_buf_p) = yy_cp = yy_bp + 3;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
#line 563 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_GET_PUT;
                          }
                          return T_GET;
                        }
  YY_BREAK
case 93:
YY_RULE_SETUP
#line 570 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_GOSUB;
                          }
                          return T_GOSUB;
                        }
  YY_BREAK
case 94:
YY_RULE_SETUP
#line 577 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_RESUME_GOTO;
                          }
                          return T_GOTO;
                        }
  YY_BREAK
case 95:
YY_RULE_SETUP
#line 584 "bas_token.l"
return T_IDN;
  YY_BREAK
case 96:
YY_RULE_SETUP
#line 585 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_IF_ELSEIFIF;
                          }
                          return T_IF;
                        }
  YY_BREAK
case 97:
*yy_cp = (yy_hold_char); /* undo effects of setting up yytext */
(yy_c_buf_p) = yy_cp -= 1;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
#line 592 "bas_token.l"
{
        BEGIN(IMAGEFMT);
                    if (cur)
                    {
                      cur->statement=stmt_IMAGE;
                    }
                    return T_IMAGE;
      }
  YY_BREAK
case 98:
*yy_cp = (yy_hold_char); /* undo effects of setting up yytext */
(yy_c_buf_p) = yy_cp -= 1;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
#line 600 "bas_token.l"
{
        BEGIN(INITIAL);
                    if (cur)
                    {
                            size_t l;

                      l=strlen(yytext);
                            cur->u.string=malloc(sizeof(struct String));
                            String_size(String_new(cur->u.string),l);
                            memcpy(cur->u.string->character,yytext,l);
                    }
                    return T_STRING;
                  }
  YY_BREAK
case 99:
YY_RULE_SETUP
#line 613 "bas_token.l"
{
                    if (cur)
                    {
                      cur->statement=stmt_IMAGE;
                    }
                    return T_IMAGE;
                  }
  YY_BREAK
case 100:
YY_RULE_SETUP
#line 620 "bas_token.l"
return T_IMP;
  YY_BREAK
case 101:
YY_RULE_SETUP
#line 621 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_DEC_INC;
                          }
                          return T_INC;
                        }
  YY_BREAK
case 102:
YY_RULE_SETUP
#line 628 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_INPUT;
                          }
                          return T_INPUT;
                        }
  YY_BREAK
case 103:
*yy_cp = (yy_hold_char); /* undo effects of setting up yytext */
(yy_c_buf_p) = yy_cp = yy_bp + 5;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
#line 635 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_INPUT;
                          }
                          return T_INPUT;
                        }
  YY_BREAK
case 104:
YY_RULE_SETUP
#line 642 "bas_token.l"
return T_INV;
  YY_BREAK
case 105:
YY_RULE_SETUP
#line 643 "bas_token.l"
return T_IS;
  YY_BREAK
case 106:
YY_RULE_SETUP
#line 644 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_KILL;
                          }
                          return T_KILL;
                        }
  YY_BREAK
case 107:
YY_RULE_SETUP
#line 651 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_LET;
                          }
                          return T_LET;
                        }
  YY_BREAK
case 108:
YY_RULE_SETUP
#line 658 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_LIST_LLIST;
                          }
                          return T_LIST;
                        }
  YY_BREAK
case 109:
YY_RULE_SETUP
#line 665 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_LIST_LLIST;
                          }
                          return T_LLIST;
                        }
  YY_BREAK
case 110:
YY_RULE_SETUP
#line 672 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_LOAD;
                          }
                          return T_LOAD;
                        }
  YY_BREAK
case 111:
YY_RULE_SETUP
#line 679 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_LOCAL;
                          }
                          return T_LOCAL;
                        }
  YY_BREAK
case 112:
YY_RULE_SETUP
#line 686 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_LOCATE;
                          }
                          return T_LOCATE;
                        }
  YY_BREAK
case 113:
YY_RULE_SETUP
#line 693 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_LOCK_UNLOCK;
                          }
                          return T_LOCK;
                        }
  YY_BREAK
case 114:
YY_RULE_SETUP
#line 700 "bas_token.l"
return T_LOCK_READ;
  YY_BREAK
case 115:
YY_RULE_SETUP
#line 701 "bas_token.l"
return T_LOCK_WRITE;
  YY_BREAK
case 116:
YY_RULE_SETUP
#line 702 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_LOOP;
                          }
                          return T_LOOP;
                        }
  YY_BREAK
case 117:
YY_RULE_SETUP
#line 709 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_LOOPUNTIL;
                          }
                          return T_LOOPUNTIL;
                        }
  YY_BREAK
case 118:
YY_RULE_SETUP
#line 716 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_PRINT_LPRINT;
                          }
                          return T_LPRINT;
                        }
  YY_BREAK
case 119:
YY_RULE_SETUP
#line 723 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_LSET_RSET;
                          }
                          return T_LSET;
                        }
  YY_BREAK
case 120:
YY_RULE_SETUP
#line 730 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_MATINPUT;
                          }
                          return T_MATINPUT;
                        }
  YY_BREAK
case 121:
YY_RULE_SETUP
#line 737 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_MATPRINT;
                          }
                          return T_MATPRINT;
                        }
  YY_BREAK
case 122:
YY_RULE_SETUP
#line 744 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_MATREAD;
                          }
                          return T_MATREAD;
                        }
  YY_BREAK
case 123:
YY_RULE_SETUP
#line 751 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_MATREDIM;
                          }
                          return T_MATREDIM;
                        }
  YY_BREAK
case 124:
YY_RULE_SETUP
#line 758 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_MATWRITE;
                          }
                          return T_MATWRITE;
                        }
  YY_BREAK
case 125:
YY_RULE_SETUP
#line 765 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_MAT;
                          }
                          return T_MAT;
                        }
  YY_BREAK
case 126:
YY_RULE_SETUP
#line 772 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_CHDIR_MKDIR;
                          }
                          return T_MKDIR;
                        }
  YY_BREAK
case 127:
YY_RULE_SETUP
#line 779 "bas_token.l"
return T_MOD;
  YY_BREAK
case 128:
YY_RULE_SETUP
#line 780 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_NEW;
                          }
                          return T_NEW;
                        }
  YY_BREAK
case 129:
YY_RULE_SETUP
#line 787 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_NAME;
                          }
                          return T_NAME;
                        }
  YY_BREAK
case 130:
YY_RULE_SETUP
#line 794 "bas_token.l"
{
                      if (cur)
                      {
                            cur->statement=stmt_NEXT;
                        cur->u.next=malloc(sizeof(struct Next));
                      }
        return T_NEXT;
                  }
  YY_BREAK
case 131:
YY_RULE_SETUP
#line 802 "bas_token.l"
return T_NOT;
  YY_BREAK
case 132:
YY_RULE_SETUP
#line 803 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_ONERROROFF;
                          }
                          return T_ONERROROFF;
                        }
  YY_BREAK
case 133:
YY_RULE_SETUP
#line 810 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_ONERRORGOTO0;
                          }
                          return T_ONERRORGOTO0;
                        }
  YY_BREAK
case 134:
YY_RULE_SETUP
#line 817 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_ONERROR;
                          }
                          return T_ONERROR;
                        }
  YY_BREAK
case 135:
YY_RULE_SETUP
#line 824 "bas_token.l"
{
        if (cur)
        {
          cur->statement=stmt_ON;
          cur->u.on.pcLength=1;
          cur->u.on.pc=(struct Pc*)0;
        }
        return T_ON;
      }
  YY_BREAK
case 136:
YY_RULE_SETUP
#line 833 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_OPEN;
                          }
                          return T_OPEN;
                        }
  YY_BREAK
case 137:
YY_RULE_SETUP
#line 840 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_OPTIONBASE;
                          }
                          return T_OPTIONBASE;
                        }
  YY_BREAK
case 138:
YY_RULE_SETUP
#line 847 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_OPTIONRUN;
                          }
                          return T_OPTIONRUN;
                        }
  YY_BREAK
case 139:
YY_RULE_SETUP
#line 854 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_OPTIONSTOP;
                          }
                          return T_OPTIONSTOP;
                        }
  YY_BREAK
case 140:
YY_RULE_SETUP
#line 861 "bas_token.l"
return T_OR;
  YY_BREAK
case 141:
YY_RULE_SETUP
#line 862 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_OUT_POKE;
                          }
                          return T_OUT;
                        }
  YY_BREAK
case 142:
YY_RULE_SETUP
#line 869 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_PRINT_LPRINT;
                          }
                          return T_PRINT;
                        }
  YY_BREAK
case 143:
*yy_cp = (yy_hold_char); /* undo effects of setting up yytext */
(yy_c_buf_p) = yy_cp -= 1;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
#line 876 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_PRINT_LPRINT;
                          }
                          return T_PRINT;
                        }
  YY_BREAK
case 144:
YY_RULE_SETUP
#line 883 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_OUT_POKE;
                          }
                          return T_POKE;
                        }
  YY_BREAK
case 145:
YY_RULE_SETUP
#line 890 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_GET_PUT;
                          }
                          return T_PUT;
                        }
  YY_BREAK
case 146:
*yy_cp = (yy_hold_char); /* undo effects of setting up yytext */
(yy_c_buf_p) = yy_cp = yy_bp + 3;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
#line 897 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_GET_PUT;
                          }
                          return T_PUT;
                        }
  YY_BREAK
case 147:
YY_RULE_SETUP
#line 904 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_RANDOMIZE;
                          }
                          return T_RANDOMIZE;
                        }
  YY_BREAK
case 148:
YY_RULE_SETUP
#line 911 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_READ;
                          }
                          return T_READ;
                        }
  YY_BREAK
case 149:
YY_RULE_SETUP
#line 918 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_RENUM;
                          }
                          return T_RENUM;
                        }
  YY_BREAK
case 150:
YY_RULE_SETUP
#line 925 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_REPEAT;
                          }
                          return T_REPEAT;
                        }
  YY_BREAK
case 151:
YY_RULE_SETUP
#line 932 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_RESTORE;
                          }
                          return T_RESTORE;
                        }
  YY_BREAK
case 152:
YY_RULE_SETUP
#line 939 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_RESUME_GOTO;
                          }
                          return T_RESUME;
                        }
  YY_BREAK
case 153:
YY_RULE_SETUP
#line 946 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_RETURN;
                          }
                          return T_RETURN;
                        }
  YY_BREAK
case 154:
YY_RULE_SETUP
#line 953 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_LSET_RSET;
                          }
                          return T_RSET;
                        }
  YY_BREAK
case 155:
YY_RULE_SETUP
#line 960 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_RUN;
                          }
                          return T_RUN;
                        }
  YY_BREAK
case 156:
YY_RULE_SETUP
#line 967 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_SAVE;
                          }
                          return T_SAVE;
                        }
  YY_BREAK
case 157:
YY_RULE_SETUP
#line 974 "bas_token.l"
{
        if (cur)
        {
          cur->statement=stmt_SELECTCASE;
          cur->u.selectcase=malloc(sizeof(struct Selectcase));
        }
        return T_SELECTCASE;
      }
  YY_BREAK
case 158:
YY_RULE_SETUP
#line 982 "bas_token.l"
return T_SHARED;
  YY_BREAK
case 159:
YY_RULE_SETUP
#line 983 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_SHELL;
                          }
                          return T_SHELL;
                        }
  YY_BREAK
case 160:
YY_RULE_SETUP
#line 990 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_SLEEP;
                          }
                          return T_SLEEP;
                        }
  YY_BREAK
case 161:
YY_RULE_SETUP
#line 997 "bas_token.l"
return T_SPC;
  YY_BREAK
case 162:
YY_RULE_SETUP
#line 998 "bas_token.l"
return T_STEP;
  YY_BREAK
case 163:
YY_RULE_SETUP
#line 999 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_STOP;
                          }
                          return T_STOP;
                        }
  YY_BREAK
case 164:
YY_RULE_SETUP
#line 1006 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_ENDPROC_SUBEND;
                          }
                          return T_SUBEND;
                        }
  YY_BREAK
case 165:
YY_RULE_SETUP
#line 1013 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_SUBEXIT;
                          }
                          return T_SUBEXIT;
                        }
  YY_BREAK
case 166:
YY_RULE_SETUP
#line 1020 "bas_token.l"
{
                    if (cur)
                    {
                      cur->statement=stmt_DEFFN_DEFPROC_FUNCTION_SUB;
                      cur->u.localSyms=(struct Symbol*)0;
                    }
                    return T_SUB;
                  }
  YY_BREAK
case 167:
YY_RULE_SETUP
#line 1028 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_SWAP;
                          }
                          return T_SWAP;
                        }
  YY_BREAK
case 168:
YY_RULE_SETUP
#line 1035 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_SYSTEM;
                          }
                          return T_SYSTEM;
                        }
  YY_BREAK
case 169:
YY_RULE_SETUP
#line 1042 "bas_token.l"
return T_THEN;
  YY_BREAK
case 170:
YY_RULE_SETUP
#line 1043 "bas_token.l"
return T_TAB;
  YY_BREAK
case 171:
YY_RULE_SETUP
#line 1044 "bas_token.l"
return T_TO;
  YY_BREAK
case 172:
YY_RULE_SETUP
#line 1045 "bas_token.l"
return T_TRN;
  YY_BREAK
case 173:
YY_RULE_SETUP
#line 1046 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_TROFF;
                          }
                          return T_TROFF;
                        }
  YY_BREAK
case 174:
YY_RULE_SETUP
#line 1053 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_TRON;
                          }
                          return T_TRON;
                        }
  YY_BREAK
case 175:
YY_RULE_SETUP
#line 1060 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_TRUNCATE;
                          }
                          return T_TRUNCATE;
                        }
  YY_BREAK
case 176:
YY_RULE_SETUP
#line 1067 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_LOCK_UNLOCK;
                          }
                          return T_UNLOCK;
                        }
  YY_BREAK
case 177:
YY_RULE_SETUP
#line 1074 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_UNNUM;
                          }
                          return T_UNNUM;
                        }
  YY_BREAK
case 178:
YY_RULE_SETUP
#line 1081 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_UNTIL;
                          }
                          return T_UNTIL;
                        }
  YY_BREAK
case 179:
YY_RULE_SETUP
#line 1088 "bas_token.l"
return T_USING;
  YY_BREAK
case 180:
YY_RULE_SETUP
#line 1089 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_WAIT;
                          }
                          return T_WAIT;
                        }
  YY_BREAK
case 181:
YY_RULE_SETUP
#line 1096 "bas_token.l"
{
                    if (cur)
                    {
                      cur->statement=stmt_WEND;
                      cur->u.whilepc=malloc(sizeof(struct Pc));
                    }
                    return T_WEND;
                  }
  YY_BREAK
case 182:
YY_RULE_SETUP
#line 1104 "bas_token.l"
{
                    if (cur)
                    {
                      cur->statement=stmt_WHILE;
                      cur->u.afterwend=malloc(sizeof(struct Pc));
                    }
                    return T_WHILE;
                  }
  YY_BREAK
case 183:
YY_RULE_SETUP
#line 1112 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_WIDTH;
                          }
                          return T_WIDTH;
                        }
  YY_BREAK
case 184:
*yy_cp = (yy_hold_char); /* undo effects of setting up yytext */
(yy_c_buf_p) = yy_cp = yy_bp + 5;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
#line 1119 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_WIDTH;
                          }
                          return T_WIDTH;
                        }
  YY_BREAK
case 185:
YY_RULE_SETUP
#line 1126 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_WRITE;
                          }
                          return T_WRITE;
                        }
  YY_BREAK
case 186:
*yy_cp = (yy_hold_char); /* undo effects of setting up yytext */
(yy_c_buf_p) = yy_cp = yy_bp + 5;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
#line 1133 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_WRITE;
                          }
                          return T_WRITE;
                        }
  YY_BREAK
case 187:
YY_RULE_SETUP
#line 1140 "bas_token.l"
return T_XOR;
  YY_BREAK
case 188:
YY_RULE_SETUP
#line 1141 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_XREF;
                          }
                          return T_XREF;
                        }
  YY_BREAK
case 189:
YY_RULE_SETUP
#line 1148 "bas_token.l"
return T_ZER;
  YY_BREAK
case 190:
YY_RULE_SETUP
#line 1149 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_ZONE;
                          }
                          return T_ZONE;
                        }
  YY_BREAK
case 191:
YY_RULE_SETUP
#line 1156 "bas_token.l"
{
                    if (cur)
                    {
                      cur->statement=stmt_QUOTE_REM;
                      cur->u.rem=strcpy(malloc(strlen(yytext+3)+1),yytext+3);
                    }
                    return T_REM;
                  }
  YY_BREAK
case 192:
YY_RULE_SETUP
#line 1164 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_COPY_RENAME;
                          }
                          return T_RENAME;
                        }
  YY_BREAK
case 193:
YY_RULE_SETUP
#line 1171 "bas_token.l"
{
                    if (cur)
                      {
                        cur->statement=stmt_QUOTE_REM;
                        strcpy(cur->u.rem=malloc(strlen(yytext+1)+1),yytext+1);
                      }

                    return T_QUOTE;
                  }
  YY_BREAK
case 194:
YY_RULE_SETUP
#line 1179 "bas_token.l"
{
                          if (cur)
                          {
                            cur->statement=stmt_LINEINPUT;
                          }
                          return T_LINEINPUT;
                        }
  YY_BREAK
case 195:
YY_RULE_SETUP
#line 1186 "bas_token.l"
{
        if (cur)
        {
                            size_t len;
                            char *s;
                            int fn;

                            cur->statement=stmt_IDENTIFIER;
                            if (tolower(yytext[0])=='f' && tolower(yytext[1])=='n')
                            {
                              for (len=2,s=&yytext[2]; *s==' ' || *s=='\t'; ++s);
                              fn=1;
                            }
                            else
                            {
                              len=0;
                              s=yytext;
                              fn=0;
                            }
                            len+=strlen(s);
          cur->u.identifier=malloc(offsetof(struct Identifier,name)+len+1);
                            if (fn)
                            {
                              memcpy(cur->u.identifier->name,yytext,2);
                              strcpy(cur->u.identifier->name+2,s);
                            }
                            else
                            {
                              strcpy(cur->u.identifier->name,s);
                            }
          switch (yytext[yyleng-1])
          {
            case '$': cur->u.identifier->defaultType=V_STRING; break;
            case '%': cur->u.identifier->defaultType=V_INTEGER; break;
            default: cur->u.identifier->defaultType=V_REAL; break;
          }
        }
        return T_IDENTIFIER;
      }
  YY_BREAK
case 196:
/* rule 196 can match eol */
YY_RULE_SETUP
#line 1225 "bas_token.l"

  YY_BREAK
case 197:
YY_RULE_SETUP
#line 1226 "bas_token.l"
{
        if (cur) cur->u.junk=yytext[0];
        return T_JUNK;
      }
  YY_BREAK
/*}}}*/
case 198:
YY_RULE_SETUP
#line 1231 "bas_token.l"
ECHO;
  YY_BREAK
#line 3711 "<stdout>"
case YY_STATE_EOF(INITIAL):
case YY_STATE_EOF(DATAINPUT):
case YY_STATE_EOF(ELSEIF):
case YY_STATE_EOF(IMAGEFMT):
  yyterminate();

  case YY_END_OF_BUFFER:
    {
    /* Amount of text matched not including the EOB char. */
    int yy_amount_of_matched_text = (int) (yy_cp - (yytext_ptr)) - 1;

    /* Undo the effects of YY_DO_BEFORE_ACTION. */
    *yy_cp = (yy_hold_char);
    YY_RESTORE_YY_MORE_OFFSET

    if (YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_NEW)
      {
      /* We're scanning a new file or input source.  It's
       * possible that this happened because the user
       * just pointed yyin at a new source and called
       * yylex().  If so, then we have to assure
       * consistency between YY_CURRENT_BUFFER and our
       * globals.  Here is the right place to do so, because
       * this is the first action (other than possibly a
       * back-up) that will match for the new input source.
       */
      (yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
      YY_CURRENT_BUFFER_LVALUE->yy_input_file = yyin;
      YY_CURRENT_BUFFER_LVALUE->yy_buffer_status = YY_BUFFER_NORMAL;
      }

    /* Note that here we test for yy_c_buf_p "<=" to the position
     * of the first EOB in the buffer, since yy_c_buf_p will
     * already have been incremented past the NUL character
     * (since all states make transitions on EOB to the
     * end-of-buffer state).  Contrast this with the test
     * in input().
     */
    if ((yy_c_buf_p) <= &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)])
      { /* This was really a NUL. */
      yy_state_type yy_next_state;

      (yy_c_buf_p) = (yytext_ptr) + yy_amount_of_matched_text;

      yy_current_state = yy_get_previous_state();

      /* Okay, we're now positioned to make the NUL
       * transition.  We couldn't have
       * yy_get_previous_state() go ahead and do it
       * for us because it doesn't know how to deal
       * with the possibility of jamming (and we don't
       * want to build jamming into it because then it
       * will run more slowly).
       */

      yy_next_state = yy_try_NUL_trans(yy_current_state);

      yy_bp = (yytext_ptr) + YY_MORE_ADJ;

      if (yy_next_state)
        {
        /* Consume the NUL. */
        yy_cp = ++(yy_c_buf_p);
        yy_current_state = yy_next_state;
        goto yy_match;
        }

      else
        {
        yy_cp = (yy_c_buf_p);
        goto yy_find_action;
        }
      }

    else switch (yy_get_next_buffer())
      {
      case EOB_ACT_END_OF_FILE:
        {
        (yy_did_buffer_switch_on_eof) = 0;

        if (yywrap())
          {
          /* Note: because we've taken care in
           * yy_get_next_buffer() to have set up
           * yytext, we can now set up
           * yy_c_buf_p so that if some total
           * hoser (like flex itself) wants to
           * call the scanner after we return the
           * YY_NULL, it'll still work - another
           * YY_NULL will get returned.
           */
          (yy_c_buf_p) = (yytext_ptr) + YY_MORE_ADJ;

          yy_act = YY_STATE_EOF(YY_START);
          goto do_action;
          }

        else
          {
          if (! (yy_did_buffer_switch_on_eof))
            YY_NEW_FILE;
          }
        break;
        }

      case EOB_ACT_CONTINUE_SCAN:
        (yy_c_buf_p) =
          (yytext_ptr) + yy_amount_of_matched_text;

        yy_current_state = yy_get_previous_state();

        yy_cp = (yy_c_buf_p);
        yy_bp = (yytext_ptr) + YY_MORE_ADJ;
        goto yy_match;

      case EOB_ACT_LAST_MATCH:
        (yy_c_buf_p) =
        &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)];

        yy_current_state = yy_get_previous_state();

        yy_cp = (yy_c_buf_p);
        yy_bp = (yytext_ptr) + YY_MORE_ADJ;
        goto yy_find_action;
      }
    break;
    }

  default:
    YY_FATAL_ERROR(
      "fatal flex scanner internal error--no action found");
  } /* end of action switch */
    } /* end of scanning one token */
  } /* end of user's declarations */
} /* end of yylex */

/* yy_get_next_buffer - try to read in a new buffer
 *
 * Returns a code representing an action:
 *  EOB_ACT_LAST_MATCH -
 *  EOB_ACT_CONTINUE_SCAN - continue scanning from current position
 *  EOB_ACT_END_OF_FILE - end of file
 */
static int yy_get_next_buffer (void)
{
  register char *dest = YY_CURRENT_BUFFER_LVALUE->yy_ch_buf;
  register char *source = (yytext_ptr);
  register int number_to_move;
  register int i;
  int ret_val;

  if ((yy_c_buf_p) > &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1])
    YY_FATAL_ERROR(
    "fatal flex scanner internal error--end of buffer missed");

  if (YY_CURRENT_BUFFER_LVALUE->yy_fill_buffer == 0)
    { /* Don't try to fill the buffer, so this is an EOF. */
    if ((yy_c_buf_p) - (yytext_ptr) - YY_MORE_ADJ == 1)
      {
      /* We matched a single character, the EOB, so
       * treat this as a final EOF.
       */
      return EOB_ACT_END_OF_FILE;
      }

    else
      {
      /* We matched some text prior to the EOB, first
       * process it.
       */
      return EOB_ACT_LAST_MATCH;
      }
    }

  /* Try to read more data. */

  /* First move last chars to start of buffer. */
  number_to_move = (int) ((yy_c_buf_p) - (yytext_ptr)) - 1;

  for (i = 0; i < number_to_move; ++i)
    *(dest++) = *(source++);

  if (YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_EOF_PENDING)
    /* don't do the read, it's not guaranteed to return an EOF,
     * just force an EOF
     */
    YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars) = 0;

  else
    {
      yy_size_t num_to_read =
      YY_CURRENT_BUFFER_LVALUE->yy_buf_size - number_to_move - 1;

    while (num_to_read < 1)
      { /* Not enough room in the buffer - grow it. */

      /* just a shorter name for the current buffer */
      YY_BUFFER_STATE b = YY_CURRENT_BUFFER_LVALUE;

      int yy_c_buf_p_offset =
        (int) ((yy_c_buf_p) - b->yy_ch_buf);

      if (b->yy_is_our_buffer)
        {
        yy_size_t new_size = b->yy_buf_size * 2;

        if (new_size < 1)
          b->yy_buf_size += b->yy_buf_size / 8;
        else
          b->yy_buf_size *= 2;

        b->yy_ch_buf = (char *)
          /* Include room in for 2 EOB chars. */
          yyrealloc((void *) b->yy_ch_buf,b->yy_buf_size + 2);
        }
      else
        /* Can't grow it, we don't own it. */
        b->yy_ch_buf = 0;

      if (! b->yy_ch_buf)
        YY_FATAL_ERROR(
        "fatal error - scanner input buffer overflow");

      (yy_c_buf_p) = &b->yy_ch_buf[yy_c_buf_p_offset];

      num_to_read = YY_CURRENT_BUFFER_LVALUE->yy_buf_size -
            number_to_move - 1;

      }

    if (num_to_read > YY_READ_BUF_SIZE)
      num_to_read = YY_READ_BUF_SIZE;

    /* Read in more data. */
    YY_INPUT((&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[number_to_move]),
      (yy_n_chars), num_to_read);

    YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
    }

  if ((yy_n_chars) == 0)
    {
    if (number_to_move == YY_MORE_ADJ)
      {
      ret_val = EOB_ACT_END_OF_FILE;
      yyrestart(yyin);
      }

    else
      {
      ret_val = EOB_ACT_LAST_MATCH;
      YY_CURRENT_BUFFER_LVALUE->yy_buffer_status =
        YY_BUFFER_EOF_PENDING;
      }
    }

  else
    ret_val = EOB_ACT_CONTINUE_SCAN;

  if ((yy_size_t) ((yy_n_chars) + number_to_move) > YY_CURRENT_BUFFER_LVALUE->yy_buf_size) {
    /* Extend the array by 50%, plus the number we really need. */
    yy_size_t new_size = (yy_n_chars) + number_to_move + ((yy_n_chars) >> 1);
    YY_CURRENT_BUFFER_LVALUE->yy_ch_buf = (char *) yyrealloc((void *) YY_CURRENT_BUFFER_LVALUE->yy_ch_buf, new_size);
    if (! YY_CURRENT_BUFFER_LVALUE->yy_ch_buf)
      YY_FATAL_ERROR("out of dynamic memory in yy_get_next_buffer()");
  }

  (yy_n_chars) += number_to_move;
  YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] = YY_END_OF_BUFFER_CHAR;
  YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] = YY_END_OF_BUFFER_CHAR;

  (yytext_ptr) = &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[0];

  return ret_val;
}

/* yy_get_previous_state - get the state just before the EOB char was reached */

    static yy_state_type yy_get_previous_state (void)
{
  register yy_state_type yy_current_state;
  register char *yy_cp;

  yy_current_state = (yy_start);

  for (yy_cp = (yytext_ptr) + YY_MORE_ADJ; yy_cp < (yy_c_buf_p); ++yy_cp)
    {
    register YY_CHAR yy_c = (*yy_cp ? yy_ec[YY_SC_TO_UI(*yy_cp)] : 1);
    if (yy_accept[yy_current_state])
      {
      (yy_last_accepting_state) = yy_current_state;
      (yy_last_accepting_cpos) = yy_cp;
      }
    while (yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state)
      {
      yy_current_state = (int) yy_def[yy_current_state];
      if (yy_current_state >= 701)
        yy_c = yy_meta[(unsigned int) yy_c];
      }
    yy_current_state = yy_nxt[yy_base[yy_current_state] + (unsigned int) yy_c];
    }

  return yy_current_state;
}

/* yy_try_NUL_trans - try to make a transition on the NUL character
 *
 * synopsis
 *  next_state = yy_try_NUL_trans(current_state);
 */
    static yy_state_type yy_try_NUL_trans  (yy_state_type yy_current_state)
{
  register int yy_is_jam;
      register char *yy_cp = (yy_c_buf_p);

  register YY_CHAR yy_c = 1;
  if (yy_accept[yy_current_state])
    {
    (yy_last_accepting_state) = yy_current_state;
    (yy_last_accepting_cpos) = yy_cp;
    }
  while (yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state)
    {
    yy_current_state = (int) yy_def[yy_current_state];
    if (yy_current_state >= 701)
      yy_c = yy_meta[(unsigned int) yy_c];
    }
  yy_current_state = yy_nxt[yy_base[yy_current_state] + (unsigned int) yy_c];
  yy_is_jam = (yy_current_state == 700);

    return yy_is_jam ? 0 : yy_current_state;
}

/** Immediately switch to a different input stream.
 * @param input_file A readable stream.
 *
 * @note This function does not reset the start condition to @c INITIAL .
 */
    void yyrestart  (FILE * input_file)
{

  if (! YY_CURRENT_BUFFER){
        yyensure_buffer_stack ();
    YY_CURRENT_BUFFER_LVALUE =
            yy_create_buffer(yyin,YY_BUF_SIZE);
  }

  yy_init_buffer(YY_CURRENT_BUFFER,input_file);
  yy_load_buffer_state();
}

/** Switch to a different input buffer.
 * @param new_buffer The new input buffer.
 *
 */
    void yy_switch_to_buffer  (YY_BUFFER_STATE  new_buffer)
{

  /* TODO. We should be able to replace this entire function body
   * with
   *    yypop_buffer_state();
   *    yypush_buffer_state(new_buffer);
     */
  yyensure_buffer_stack ();
  if (YY_CURRENT_BUFFER == new_buffer)
    return;

  if (YY_CURRENT_BUFFER)
    {
    /* Flush out information for old buffer. */
    *(yy_c_buf_p) = (yy_hold_char);
    YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
    YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
    }

  YY_CURRENT_BUFFER_LVALUE = new_buffer;
  yy_load_buffer_state();

  /* We don't actually know whether we did this switch during
   * EOF (yywrap()) processing, but the only time this flag
   * is looked at is after yywrap() is called, so it's safe
   * to go ahead and always set it.
   */
  (yy_did_buffer_switch_on_eof) = 1;
}

static void yy_load_buffer_state  (void)
{
      (yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
  (yytext_ptr) = (yy_c_buf_p) = YY_CURRENT_BUFFER_LVALUE->yy_buf_pos;
  yyin = YY_CURRENT_BUFFER_LVALUE->yy_input_file;
  (yy_hold_char) = *(yy_c_buf_p);
}

/** Allocate and initialize an input buffer state.
 * @param file A readable stream.
 * @param size The character buffer size in bytes. When in doubt, use @c YY_BUF_SIZE.
 *
 * @return the allocated buffer state.
 */
    YY_BUFFER_STATE yy_create_buffer  (FILE * file, int  size)
{
  YY_BUFFER_STATE b;

  b = (YY_BUFFER_STATE) yyalloc(sizeof(struct yy_buffer_state));
  if (! b)
    YY_FATAL_ERROR("out of dynamic memory in yy_create_buffer()");

  b->yy_buf_size = size;

  /* yy_ch_buf has to be 2 characters longer than the size given because
   * we need to put in 2 end-of-buffer characters.
   */
  b->yy_ch_buf = (char *) yyalloc(b->yy_buf_size + 2);
  if (! b->yy_ch_buf)
    YY_FATAL_ERROR("out of dynamic memory in yy_create_buffer()");

  b->yy_is_our_buffer = 1;

  yy_init_buffer(b,file);

  return b;
}

/** Destroy the buffer.
 * @param b a buffer created with yy_create_buffer()
 *
 */
    void yy_delete_buffer (YY_BUFFER_STATE  b)
{

  if (! b)
    return;

  if (b == YY_CURRENT_BUFFER) /* Not sure if we should pop here. */
    YY_CURRENT_BUFFER_LVALUE = (YY_BUFFER_STATE) 0;

  if (b->yy_is_our_buffer)
    yyfree((void *) b->yy_ch_buf);

  yyfree((void *) b);
}

/* Initializes or reinitializes a buffer.
 * This function is sometimes called more than once on the same buffer,
 * such as during a yyrestart() or at EOF.
 */

static void yy_init_buffer  (YY_BUFFER_STATE  b, FILE * file)
{
  int oerrno = errno;

  yy_flush_buffer(b);

  b->yy_input_file = file;
  b->yy_fill_buffer = 1;

  /* If b is the current buffer, then yy_init_buffer was _probably_
   * called from yyrestart() or through yy_get_next_buffer.
   * In that case, we don't want to reset the lineno or column.
   */

  if (b != YY_CURRENT_BUFFER)
    {
      b->yy_bs_lineno = 1;
      b->yy_bs_column = 0;
    }

#ifdef CONFIG_SERIAL_TERMIOS
  b->yy_is_interactive = file ? (isatty(fileno(file)) > 0) : 0;
#else
  b->yy_is_interactive = 1;
#endif

  errno = oerrno;
}

/** Discard all buffered characters. On the next scan, YY_INPUT will be called.
 * @param b the buffer state to be flushed, usually @c YY_CURRENT_BUFFER.
 *
 */
    void yy_flush_buffer (YY_BUFFER_STATE  b)
{
      if (! b)
    return;

  b->yy_n_chars = 0;

  /* We always need two end-of-buffer characters.  The first causes
   * a transition to the end-of-buffer state.  The second causes
   * a jam in that state.
   */
  b->yy_ch_buf[0] = YY_END_OF_BUFFER_CHAR;
  b->yy_ch_buf[1] = YY_END_OF_BUFFER_CHAR;

  b->yy_buf_pos = &b->yy_ch_buf[0];

  b->yy_at_bol = 1;
  b->yy_buffer_status = YY_BUFFER_NEW;

  if (b == YY_CURRENT_BUFFER)
    yy_load_buffer_state();
}

/** Pushes the new state onto the stack. The new state becomes
 *  the current state. This function will allocate the stack
 *  if necessary.
 *  @param new_buffer The new state.
 *
 */
void yypush_buffer_state (YY_BUFFER_STATE new_buffer)
{
      if (new_buffer == NULL)
    return;

  yyensure_buffer_stack();

  /* This block is copied from yy_switch_to_buffer. */
  if (YY_CURRENT_BUFFER)
    {
    /* Flush out information for old buffer. */
    *(yy_c_buf_p) = (yy_hold_char);
    YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
    YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
    }

  /* Only push if top exists. Otherwise, replace top. */
  if (YY_CURRENT_BUFFER)
    (yy_buffer_stack_top)++;
  YY_CURRENT_BUFFER_LVALUE = new_buffer;

  /* copied from yy_switch_to_buffer. */
  yy_load_buffer_state();
  (yy_did_buffer_switch_on_eof) = 1;
}

/** Removes and deletes the top of the stack, if present.
 *  The next element becomes the new top.
 *
 */
void yypop_buffer_state (void)
{
      if (!YY_CURRENT_BUFFER)
    return;

  yy_delete_buffer(YY_CURRENT_BUFFER);
  YY_CURRENT_BUFFER_LVALUE = NULL;
  if ((yy_buffer_stack_top) > 0)
    --(yy_buffer_stack_top);

  if (YY_CURRENT_BUFFER) {
    yy_load_buffer_state();
    (yy_did_buffer_switch_on_eof) = 1;
  }
}

/* Allocates the stack if it does not exist.
 *  Guarantees space for at least one push.
 */
static void yyensure_buffer_stack (void)
{
  yy_size_t num_to_alloc;

  if (!(yy_buffer_stack)) {

    /* First allocation is just for 2 elements, since we don't know if this
     * scanner will even need a stack. We use 2 instead of 1 to avoid an
     * immediate realloc on the next call.
         */
    num_to_alloc = 1;
    (yy_buffer_stack) = (struct yy_buffer_state**)yyalloc
                (num_to_alloc * sizeof(struct yy_buffer_state*));
    if (! (yy_buffer_stack))
      YY_FATAL_ERROR("out of dynamic memory in yyensure_buffer_stack()");

    memset((yy_buffer_stack), 0, num_to_alloc * sizeof(struct yy_buffer_state*));

    (yy_buffer_stack_max) = num_to_alloc;
    (yy_buffer_stack_top) = 0;
    return;
  }

  if ((yy_buffer_stack_top) >= ((yy_buffer_stack_max)) - 1){

    /* Increase the buffer to prepare for a possible push. */
    int grow_size = 8 /* arbitrary grow size */;

    num_to_alloc = (yy_buffer_stack_max) + grow_size;
    (yy_buffer_stack) = (struct yy_buffer_state**)yyrealloc
                ((yy_buffer_stack),
                num_to_alloc * sizeof(struct yy_buffer_state*));
    if (! (yy_buffer_stack))
      YY_FATAL_ERROR("out of dynamic memory in yyensure_buffer_stack()");

    /* zero only the new slots.*/
    memset((yy_buffer_stack) + (yy_buffer_stack_max), 0, grow_size * sizeof(struct yy_buffer_state*));
    (yy_buffer_stack_max) = num_to_alloc;
  }
}

/** Setup the input buffer state to scan directly from a user-specified character buffer.
 * @param base the character buffer
 * @param size the size in bytes of the character buffer
 *
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_buffer  (char * base, yy_size_t  size)
{
  YY_BUFFER_STATE b;

  if (size < 2 ||
       base[size-2] != YY_END_OF_BUFFER_CHAR ||
       base[size-1] != YY_END_OF_BUFFER_CHAR)
    /* They forgot to leave room for the EOB's. */
    return 0;

  b = (YY_BUFFER_STATE) yyalloc(sizeof(struct yy_buffer_state));
  if (! b)
    YY_FATAL_ERROR("out of dynamic memory in yy_scan_buffer()");

  b->yy_buf_size = size - 2;  /* "- 2" to take care of EOB's */
  b->yy_buf_pos = b->yy_ch_buf = base;
  b->yy_is_our_buffer = 0;
  b->yy_input_file = 0;
  b->yy_n_chars = b->yy_buf_size;
  b->yy_is_interactive = 0;
  b->yy_at_bol = 1;
  b->yy_fill_buffer = 0;
  b->yy_buffer_status = YY_BUFFER_NEW;

  yy_switch_to_buffer(b);

  return b;
}

/** Setup the input buffer state to scan a string. The next call to yylex() will
 * scan from a @e copy of @a str.
 * @param yystr a NUL-terminated string to scan
 *
 * @return the newly allocated buffer state object.
 * @note If you want to scan bytes that may contain NUL values, then use
 *       yy_scan_bytes() instead.
 */
YY_BUFFER_STATE yy_scan_string (yyconst char * yystr)
{

  return yy_scan_bytes(yystr,strlen(yystr));
}

/** Setup the input buffer state to scan the given bytes. The next call to yylex() will
 * scan from a @e copy of @a bytes.
 * @param yybytes the byte buffer to scan
 * @param _yybytes_len the number of bytes in the buffer pointed to by @a bytes.
 *
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_bytes  (yyconst char * yybytes, yy_size_t  _yybytes_len)
{
  YY_BUFFER_STATE b;
  char *buf;
  yy_size_t n;
  yy_size_t i;

  /* Get memory for full buffer, including space for trailing EOB's. */
  n = _yybytes_len + 2;
  buf = (char *) yyalloc(n);
  if (! buf)
    YY_FATAL_ERROR("out of dynamic memory in yy_scan_bytes()");

  for (i = 0; i < _yybytes_len; ++i)
    buf[i] = yybytes[i];

  buf[_yybytes_len] = buf[_yybytes_len+1] = YY_END_OF_BUFFER_CHAR;

  b = yy_scan_buffer(buf,n);
  if (! b)
    YY_FATAL_ERROR("bad buffer in yy_scan_bytes()");

  /* It's okay to grow etc. this buffer, and we should throw it
   * away when we're done.
   */
  b->yy_is_our_buffer = 1;

  return b;
}

#ifndef YY_EXIT_FAILURE
#define YY_EXIT_FAILURE 2
#endif

static void yy_fatal_error (yyconst char* msg)
{
  fprintf(stderr, "%s\n", msg);
  exit(YY_EXIT_FAILURE);
}

/* Redefine yyless() so it works in section 3 code. */

#undef yyless
#define yyless(n) \
  do \
    { \
    /* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
    yytext[yyleng] = (yy_hold_char); \
    (yy_c_buf_p) = yytext + yyless_macro_arg; \
    (yy_hold_char) = *(yy_c_buf_p); \
    *(yy_c_buf_p) = '\0'; \
    yyleng = yyless_macro_arg; \
    } \
  while (0)

/* Accessor  methods (get/set functions) to struct members. */

/** Get the current line number.
 *
 */
int yyget_lineno  (void)
{

    return yylineno;
}

/** Get the input stream.
 *
 */
FILE *yyget_in  (void)
{
        return yyin;
}

/** Get the output stream.
 *
 */
FILE *yyget_out  (void)
{
        return yyout;
}

/** Get the length of the current token.
 *
 */
yy_size_t yyget_leng  (void)
{
        return yyleng;
}

/** Get the current token.
 *
 */

char *yyget_text  (void)
{
        return yytext;
}

/** Set the current line number.
 * @param line_number
 *
 */
void yyset_lineno (int  line_number)
{

    yylineno = line_number;
}

/** Set the input stream. This does not discard the current
 * input buffer.
 * @param in_str A readable stream.
 *
 * @see yy_switch_to_buffer
 */
void yyset_in (FILE *  in_str)
{
        yyin = in_str ;
}

void yyset_out (FILE *  out_str)
{
        yyout = out_str ;
}

int yyget_debug  (void)
{
        return yy_flex_debug;
}

void yyset_debug (int  bdebug)
{
        yy_flex_debug = bdebug ;
}

static int yy_init_globals (void)
{
    /* Initialization is the same as for the non-reentrant scanner.
     * This function is called from yylex_destroy(), so don't allocate here.
     */

    (yy_buffer_stack) = 0;
    (yy_buffer_stack_top) = 0;
    (yy_buffer_stack_max) = 0;
    (yy_c_buf_p) = (char *) 0;
    (yy_init) = 0;
    (yy_start) = 0;

    /* Defined in bas_main.c */
#ifdef YY_STDINIT
    yyin = stdin;
    yyout = stdout;
#else
    yyin = (FILE *) 0;
    yyout = (FILE *) 0;
#endif

    /* For future reference: Set errno on error, since we are called by
     * yylex_init()
     */
    return 0;
}

/* yylex_destroy is for both reentrant and non-reentrant scanners. */
int yylex_destroy  (void)
{

    /* Pop the buffer stack, destroying each element. */
  while(YY_CURRENT_BUFFER){
    yy_delete_buffer(YY_CURRENT_BUFFER);
    YY_CURRENT_BUFFER_LVALUE = NULL;
    yypop_buffer_state();
  }

  /* Destroy the stack itself. */
  yyfree((yy_buffer_stack));
  (yy_buffer_stack) = NULL;

    /* Reset the globals. This is important in a non-reentrant scanner so the next time
     * yylex() is called, initialization will occur. */
    yy_init_globals();

    return 0;
}

/*
 * Internal utility routines.
 */

#ifndef yytext_ptr
static void yy_flex_strncpy (char* s1, yyconst char * s2, int n)
{
  register int i;
  for (i = 0; i < n; ++i)
    s1[i] = s2[i];
}
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen (yyconst char * s)
{
  register int n;
  for (n = 0; s[n]; ++n)
    ;

  return n;
}
#endif

void *yyalloc (yy_size_t  size)
{
  return (void *) malloc(size);
}

void *yyrealloc  (void * ptr, yy_size_t  size)
{
  /* The cast to (char *) in the following accommodates both
   * implementations that use char* generic pointers, and those
   * that use void* generic pointers.  It works with the latter
   * because both ANSI C and C++ allow castless assignment from
   * any pointer type to void*, and deal with argument conversions
   * as though doing an assignment.
   */
  return (void *) realloc((char *) ptr, size);
}

void yyfree (void * ptr)
{
  free((char *) ptr);  /* see yyrealloc() for (char *) cast */
}

#define YYTABLES_NAME "yytables"

#line 1230 "bas_token.l"



int g_token_property[T_LASTTOKEN];

struct Token *Token_newCode(const char *ln) /*{{{*/
{
  int l,lasttok,thistok,addNumber=0,sawif;
  struct Token *result;
  YY_BUFFER_STATE buf;

  cur=(struct Token*)0;
  buf=yy_scan_string(ln);
  /* determine number of tokens */ /*{{{*/
  g_matchdata=sawif=0;
  for (lasttok=T_EOL,l=1; (thistok=yylex()); ++l)
  {
    if (l==1 && thistok!=T_INTEGER) { addNumber=1; ++l; }
    if ((lasttok==T_THEN || lasttok==T_ELSE) && thistok==T_INTEGER) ++l;
    if (thistok==T_IF) sawif=1;
    if (thistok==T_THEN) sawif=0;
    if (thistok==T_GOTO && sawif) ++l;
    lasttok=thistok;
  }
  if (l==1) { addNumber=1; ++l; }
  /*}}}*/
  yy_delete_buffer(buf);
  cur=result=malloc(sizeof(struct Token)*l);
  if (addNumber)
  {
    cur->type=T_UNNUMBERED;
    ++cur;
  }
  buf=yy_scan_string(ln);
  lasttok=T_EOL;
  g_matchdata=sawif=0;
  while (cur->statement=NULL,(cur->type=yylex()))
  {
    if (cur->type==T_IF) sawif=1;
    if (cur->type==T_THEN) sawif=0;
    if (cur->type==T_GOTO && sawif)
    {
      sawif=0;
      *(cur+1)=*cur;
      cur->type=T_THEN;
      lasttok=T_GOTO;
      cur+=2;
    }
    else if ((lasttok==T_THEN || lasttok==T_ELSE) && cur->type==T_INTEGER)
    {
      *(cur+1)=*cur;
      cur->type=T_GOTO;
      cur->statement=stmt_RESUME_GOTO;
      lasttok=T_INTEGER;
      cur+=2;
    }
    else
    {
      lasttok=cur->type;
      ++cur;
    }
  }
  cur->type=T_EOL;
  cur->statement=stmt_COLON_EOL;
  yy_delete_buffer(buf);
  return result;
}
/*}}}*/
struct Token *Token_newData(const char *ln) /*{{{*/
{
  int l;
  struct Token *result;
  YY_BUFFER_STATE buf;

  cur=(struct Token*)0;
  buf=yy_scan_string(ln);
  g_matchdata=1;
  for (l=1; yylex(); ++l);
  yy_delete_buffer(buf);
  cur=result=malloc(sizeof(struct Token)*l);
  buf=yy_scan_string(ln);
  g_matchdata=1;
  while (cur->statement=NULL,(cur->type=yylex())) ++cur;
  cur->type=T_EOL;
  cur->statement=stmt_COLON_EOL;
  yy_delete_buffer(buf);
  return result;
}
/*}}}*/
void Token_destroy(struct Token *token) /*{{{*/
{
  struct Token *r=token;

  do
  {
    switch (r->type)
    {
      case T_ACCESS_READ:       break;
      case T_ACCESS_WRITE:      break;
      case T_AND:               break;
      case T_AS:                break;
      case T_CALL:              break;
      case T_CASEELSE:
      case T_CASEVALUE:         free(r->u.casevalue); break;
      case T_CHANNEL:           break;
      case T_CHDIR:             break;
      case T_CLEAR:             break;
      case T_CLOSE:             break;
      case T_CLS:               break;
      case T_COLON:             break;
      case T_COLOR:             break;
      case T_COMMA:             break;
      case T_CON:               break;
      case T_COPY:              break;
      case T_CP:                break;
      case T_DATA:              break;
      case T_DATAINPUT:         free(r->u.datainput); break;
      case T_DEC:               break;
      case T_DEFFN:             break;
      case T_DEFDBL:            break;
      case T_DEFINT:            break;
      case T_DEFPROC:           break;
      case T_DEFSTR:            break;
      case T_DELETE:            break;
      case T_DIM:               break;
      case T_DISPLAY:           break;
      case T_DIV:               break;
      case T_DO:                break;
      case T_DOUNTIL:           break;
      case T_DOWHILE:           break;
      case T_EDIT:              break;
      case T_ELSE:              break;
      case T_ELSEIFELSE:        break;
      case T_ELSEIFIF:          break;
      case T_END:               break;
      case T_ENDFN:             break;
      case T_ENDIF:             break;
      case T_ENDPROC:           break;
      case T_ENDSELECT:         break;
      case T_ENVIRON:           break;
      case T_EOL:               break;
      case T_EQ:                break;
      case T_EQV:               break;
      case T_ERASE:             break;
      case T_EXITDO:            break;
      case T_EXITFOR:           break;
      case T_FIELD:             break;
      case T_FNEND:             break;
      case T_FNEXIT:            break;
      case T_FNRETURN:          break;
      case T_FOR:               break;
      case T_FOR_INPUT:         break;
      case T_FOR_OUTPUT:        break;
      case T_FOR_APPEND:        break;
      case T_FOR_RANDOM:        break;
      case T_FOR_BINARY:        break;
      case T_FUNCTION:          break;
      case T_GE:                break;
      case T_GET:               break;
      case T_GOSUB:             break;
      case T_GOTO:              break;
      case T_GT:                break;
      case T_HEXINTEGER:        break;
      case T_OCTINTEGER:        break;
      case T_IDENTIFIER:        free(r->u.identifier); break;
      case T_IDIV:              break;
      case T_IDN:               break;
      case T_IF:                break;
      case T_IMAGE:             break;
      case T_IMP:               break;
      case T_INC:               break;
      case T_INPUT:             break;
      case T_INTEGER:           break;
      case T_INV:               break;
      case T_IS:                break;
      case T_JUNK:              break;
      case T_KILL:              break;
      case T_LE:                break;
      case T_LET:               break;
      case T_LINEINPUT:         break;
      case T_LIST:              break;
      case T_LLIST:             break;
      case T_LOAD:              break;
      case T_LOCAL:             break;
      case T_LOCATE:            break;
      case T_LOCK:              break;
      case T_LOCK_READ:         break;
      case T_LOCK_WRITE:        break;
      case T_LOOP:              break;
      case T_LOOPUNTIL:         break;
      case T_LPRINT:            break;
      case T_LSET:              break;
      case T_LT:                break;
      case T_MAT:               break;
      case T_MATINPUT:          break;
      case T_MATPRINT:          break;
      case T_MATREAD:           break;
      case T_MATREDIM:          break;
      case T_MATWRITE:          break;
      case T_MINUS:             break;
      case T_MKDIR:             break;
      case T_MOD:               break;
      case T_MULT:              break;
      case T_NAME:              break;
      case T_NE:                break;
      case T_NEW:               break;
      case T_NEXT:              free(r->u.next); break;
      case T_NOT:               break;
      case T_ON:                if (r->u.on.pc) free(r->u.on.pc); break;
      case T_ONERROR:           break;
      case T_ONERRORGOTO0:      break;
      case T_ONERROROFF:        break;
      case T_OP:                break;
      case T_OPEN:              break;
      case T_OPTIONBASE:        break;
      case T_OPTIONRUN:         break;
      case T_OPTIONSTOP:        break;
      case T_OR:                break;
      case T_OUT:    break;
      case T_PLUS:              break;
      case T_POKE:    break;
      case T_POW:               break;
      case T_PRINT:             break;
      case T_PUT:               break;
      case T_QUOTE:             free(r->u.rem); break;
      case T_RANDOMIZE:         break;
      case T_READ:              break;
      case T_REAL:              break;
      case T_REM:               free(r->u.rem); break;
      case T_RENAME:            break;
      case T_RENUM:             break;
      case T_REPEAT:            break;
      case T_RESTORE:           break;
      case T_RESUME:            break;
      case T_RETURN:            break;
      case T_RSET:              break;
      case T_RUN:               break;
      case T_SAVE:              break;
      case T_SELECTCASE:        free(r->u.selectcase); break;
      case T_SEMICOLON:         break;
      case T_SHARED:            break;
      case T_SHELL:             break;
      case T_SLEEP:             break;
      case T_SPC:               break;
      case T_STEP:              break;
      case T_STOP:              break;
      case T_STRING:            String_destroy(r->u.string); free(r->u.string); break;
      case T_SUB:               break;
      case T_SUBEND:            break;
      case T_SUBEXIT:           break;
      case T_SWAP:              break;
      case T_SYSTEM:            break;
      case T_TAB:               break;
      case T_THEN:              break;
      case T_TO:                break;
      case T_TRN:               break;
      case T_TROFF:             break;
      case T_TRON:              break;
      case T_TRUNCATE:          break;
      case T_UNLOCK:            break;
      case T_UNNUM:             break;
      case T_UNNUMBERED:        break;
      case T_UNTIL:             break;
      case T_USING:             break;
      case T_WAIT:              break;
      case T_WEND:              free(r->u.whilepc); break;
      case T_WHILE:             free(r->u.afterwend); break;
      case T_WIDTH:             break;
      case T_WRITE:             break;
      case T_XOR:               break;
      case T_XREF:              break;
      case T_ZER:               break;
      case T_ZONE:              break;
      default:                  assert(0);
    }
  } while ((r++)->type!=T_EOL);
  free(token);
}
/*}}}*/
struct String *Token_toString(struct Token *token, struct Token *spaceto, struct String *s, int *indent, int width) /*{{{*/
{
  int ns=0,infn=0;
  int thisindent=0,thisnotindent=0,nextindent=0;
  size_t oldlength=s->length;
  struct Token *t;
  static struct
  {
    const char *text;
    char space;
  } table[]=
  {
    /* 0                    */ {(const char*)0,-1},
    /* T_ACCESS_READ        */ {"access read",1},
    /* T_ACCESS_READ_WRITE  */ {"access read write",1},
    /* T_ACCESS_WRITE       */ {"access write",1},
    /* T_AND                */ {"and",1},
    /* T_AS                 */ {"as",1},
    /* T_CALL               */ {"call",1},
    /* T_CASEELSE           */ {"case else",1},
    /* T_CASEVALUE          */ {"case",1},
    /* T_CHANNEL            */ {"#",0},
    /* T_CHDIR              */ {"chdir",1},
    /* T_CLEAR              */ {"clear",1},
    /* T_CLOSE              */ {"close",1},
    /* T_CLS                */ {"cls",1},
    /* T_COLON              */ {":",1},
    /* T_COLOR              */ {"color",1},
    /* T_COMMA              */ {",",0},
    /* T_CON                */ {"con",0},
    /* T_COPY               */ {"copy",1},
    /* T_CP                 */ {")",0},
    /* T_DATA               */ {"data",1},
    /* T_DATAINPUT          */ {(const char*)0,0},
    /* T_DEC                */ {"dec",1},
    /* T_DEFDBL             */ {"defdbl",1},
    /* T_DEFFN              */ {"def",1},
    /* T_DEFINT             */ {"defint",1},
    /* T_DEFPROC            */ {"def",1},
    /* T_DEFSTR             */ {"defstr",1},
    /* T_DELETE             */ {"delete",1},
    /* T_DIM                */ {"dim",1},
    /* T_DISPLAY            */ {"display",1},
    /* T_DIV                */ {"/",0},
    /* T_DO                 */ {"do",1},
    /* T_DOUNTIL            */ {"do until",1},
    /* T_DOWHILE            */ {"do while",1},
    /* T_EDIT               */ {"edit",1},
    /* T_ELSE               */ {"else",1},
    /* T_ELSEIFELSE         */ {"elseif",1},
    /* T_ELSEIFIF           */ {(const char*)0,0},
    /* T_END                */ {"end",1},
    /* T_ENDFN              */ {"end function",1},
    /* T_ENDIF              */ {"end if",1},
    /* T_ENDPROC            */ {"end proc",1},
    /* T_ENDSELECT          */ {"end select",1},
    /* T_ENVIRON            */ {"environ",1},
    /* T_EOL                */ {"\n",0},
    /* T_EQ                 */ {"=",0},
    /* T_EQV                */ {"eqv",0},
    /* T_ERASE              */ {"erase",1},
    /* T_EXITDO             */ {"exit do",1},
    /* T_EXITFOR            */ {"exit for",1},
    /* T_FIELD              */ {"field",1},
    /* T_FNEND              */ {"fnend",1},
    /* T_FNEXIT             */ {"exit function",1},
    /* T_FNRETURN           */ {"fnreturn",1},
    /* T_FOR                */ {"for",1},
    /* T_FOR_INPUT          */ {"for input",1},
    /* T_FOR_OUTPUT         */ {"for output",1},
    /* T_FOR_APPEND         */ {"for append",1},
    /* T_FOR_RANDOM         */ {"for random",1},
    /* T_FOR_BINARY         */ {"for binary",1},
    /* T_FUNCTION           */ {"function",1},
    /* T_GE                 */ {">=",0},
    /* T_GET                */ {"get",1},
    /* T_GOSUB              */ {"gosub",1},
    /* T_GOTO               */ {"goto",1},
    /* T_GT                 */ {">",0},
    /* T_HEXINTEGER         */ {(const char*)0,0},
    /* T_OCTINTEGER         */ {(const char*)0,0},
    /* T_IDENTIFIER         */ {(const char*)0,0},
    /* T_IDIV               */ {"\\",0},
    /* T_IDN                */ {"idn",0},
    /* T_IF                 */ {"if",1},
    /* T_IMAGE              */ {"image",1},
    /* T_IMP                */ {"imp",0},
    /* T_INC                */ {"inc",1},
    /* T_INPUT              */ {"input",1},
    /* T_INTEGER            */ {(const char*)0,0},
    /* T_INV                */ {"inv",0},
    /* T_IS                 */ {"is",1},
    /* T_JUNK               */ {(const char*)0,0},
    /* T_KILL               */ {"kill",1},
    /* T_LE                 */ {"<=",0},
    /* T_LET                */ {"let",1},
    /* T_LINEINPUT          */ {"line input",1},
    /* T_LIST               */ {"list",1},
    /* T_LLIST              */ {"llist",1},
    /* T_LOAD               */ {"load",1},
    /* T_LOCAL              */ {"local",1},
    /* T_LOCATE             */ {"locate",1},
    /* T_LOCK               */ {"lock",1},
    /* T_LOCK_READ          */ {"lock read",1},
    /* T_LOCK_WRITE         */ {"lock write",1},
    /* T_LOOP               */ {"loop",1},
    /* T_LOOPUNTIL          */ {"loop until",1},
    /* T_LPRINT             */ {"lprint",1},
    /* T_LSET               */ {"lset",1},
    /* T_LT                 */ {"<",0},
    /* T_MAT                */ {"mat",1},
    /* T_MATINPUT           */ {"mat input",1},
    /* T_MATPRINT           */ {"mat print",1},
    /* T_MATREAD            */ {"mat read",1},
    /* T_MATREDIM           */ {"mat redim",1},
    /* T_MATWRITE           */ {"mat write",1},
    /* T_MINUS              */ {"-",0},
    /* T_MKDIR              */ {"mkdir",1},
    /* T_MOD                */ {"mod",0},
    /* T_MULT               */ {"*",0},
    /* T_NAME               */ {"name",1},
    /* T_NE                 */ {"<>",0},
    /* T_NEW                */ {"new",1},
    /* T_NEXT               */ {"next",1},
    /* T_NOT                */ {"not",0},
    /* T_ON                 */ {"on",1},
    /* T_ONERROR            */ {"on error",1},
    /* T_ONERRORGOTO0       */ {"on error goto 0",1},
    /* T_ONERROROFF         */ {"on error off",1},
    /* T_OP                 */ {"(",0},
    /* T_OPEN               */ {"open",1},
    /* T_OPTIONBASE         */ {"option base",1},
    /* T_OPTIONRUN          */ {"option run",1},
    /* T_OPTIONSTOP         */ {"option stop",1},
    /* T_OR                 */ {"or",1},
    /* T_OUT                */ {"out",1},
    /* T_PLUS               */ {"+",0},
    /* T_POKE               */ {"poke",1},
    /* T_POW                */ {"^",0},
    /* T_PRINT              */ {"print",1},
    /* T_PUT                */ {"put",1},
    /* T_QUOTE              */ {(const char*)0,1},
    /* T_RANDOMIZE          */ {"randomize",1},
    /* T_READ               */ {"read",1},
    /* T_REAL               */ {(const char*)0,0},
    /* T_REM                */ {(const char*)0,1},
    /* T_RENAME             */ {"rename",1},
    /* T_RENUM              */ {"renum",1},
    /* T_REPEAT             */ {"repeat",1},
    /* T_RESTORE            */ {"restore",1},
    /* T_RESUME             */ {"resume",1},
    /* T_RETURN             */ {"return",1},
    /* T_RSET               */ {"rset",1},
    /* T_RUN                */ {"run",1},
    /* T_SAVE               */ {"save",1},
    /* T_SELECTCASE         */ {"select case",1},
    /* T_SEMICOLON          */ {";",0},
    /* T_SHARED             */ {"shared",1},
    /* T_SHELL              */ {"shell",1},
    /* T_SLEEP              */ {"sleep",1},
    /* T_SPC                */ {"spc",0},
    /* T_STEP               */ {"step",1},
    /* T_STOP               */ {"stop",1},
    /* T_STRING             */ {(const char*)0,0},
    /* T_SUB                */ {"sub",1},
    /* T_SUBEND             */ {"subend",1},
    /* T_SUBEXIT            */ {"subexit",1},
    /* T_SWAP               */ {"swap",1},
    /* T_SYSTEM             */ {"system",1},
    /* T_TAB                */ {"tab",0},
    /* T_THEN               */ {"then",1},
    /* T_TO                 */ {"to",1},
    /* T_TRN                */ {"trn",0},
    /* T_TROFF              */ {"troff",1},
    /* T_TRON               */ {"tron",1},
    /* T_TRUNCATE           */ {"truncate",1},
    /* T_UNLOCK             */ {"unlock",1},
    /* T_UNNUM              */ {"unnum",1},
    /* T_UNNUMBERED         */ {"",0},
    /* T_UNTIL              */ {"until",1},
    /* T_USING              */ {"using",0},
    /* T_WAIT               */ {"wait",1},
    /* T_WEND               */ {"wend",1},
    /* T_WHILE              */ {"while",1},
    /* T_WIDTH              */ {"width",1},
    /* T_WRITE              */ {"write",1},
    /* T_XOR                */ {"xor",0},
    /* T_XREF               */ {"xref",0},
    /* T_ZER                */ {"zer",0},
    /* T_ZONE               */ {"zone",1},
  };

  /* precompute indentation */ /*{{{*/
  if (indent) thisindent=nextindent=*indent;
  t=token;
  do
  {
    switch (t->type)
    {
      case T_CASEELSE:
      case T_CASEVALUE:
      {
        if (thisnotindent) --thisnotindent; else if (thisindent) --thisindent;
        break;
      }
      case T_DEFFN:
      case T_FUNCTION:
      {
        struct Token *cp;

        for (cp=t; cp->type!=T_EOL && cp->type!=T_CP; ++cp);
        if ((cp+1)->type!=T_EQ)
        {
          ++thisnotindent;
          ++nextindent;
        }
        infn=1;
        break;
      }
      case T_COLON: infn=0; break;
      case T_DEFPROC:
      case T_DO:
      case T_DOUNTIL:
      case T_DOWHILE:
      case T_REPEAT:
      case T_SUB:
      case T_WHILE: ++thisnotindent; ++nextindent; break;
      case T_FOR:
      {
        if ((t>token && ((t-1)->type==T_COLON || (t-1)->type==T_INTEGER || (t-1)->type==T_UNNUMBERED)))
        {
          ++thisnotindent; ++nextindent;
        }
        break;
      }
      case T_SELECTCASE: thisnotindent+=2; nextindent+=2; break;
      case T_EQ:
      {
        if (infn || (t>token && ((t-1)->type==T_COLON || (t-1)->type==T_INTEGER || (t-1)->type==T_UNNUMBERED)))
        {
          if (thisnotindent) --thisnotindent; else if (thisindent) --thisindent;
          if (nextindent) --nextindent;
        }
        infn=0;
        break;
      }
      case T_ENDFN:
      case T_FNEND:
      case T_ENDIF:
      case T_ENDPROC:
      case T_SUBEND:
      case T_LOOP:
      case T_LOOPUNTIL:
      case T_UNTIL:
      case T_WEND:
      {
        if (thisnotindent) --thisnotindent; else if (thisindent) --thisindent;
        if (nextindent) --nextindent;
        break;
      }
      case T_ENDSELECT:
      {
        if (thisnotindent) --thisnotindent; else if (thisindent) --thisindent;
        if (thisnotindent) --thisnotindent; else if (thisindent) --thisindent;
        if (nextindent) --nextindent;
        if (nextindent) --nextindent;
        break;
      }
      case T_NEXT:
      {
        ++t;
        while (1)
        {
          if (thisnotindent) --thisnotindent; else if (thisindent) --thisindent;
          if (nextindent) --nextindent;
          if (t->type==T_IDENTIFIER)
          {
            ++t;
            if (t->type==T_OP)
            {
              int par=0;

              do
              {
                if (t->type==T_OP) ++par;
                else if (t->type==T_CP) --par;
                if (t->type!=T_EOL) ++t;
                else break;
              } while (par);
            }
            if (t->type==T_COMMA) ++t;
            else break;
          }
          else break;
        }
        break;
      }
      case T_THEN: if ((t+1)->type==T_EOL) { ++thisnotindent; ++nextindent; } break;
      case T_ELSE:
      {
        if (t==token+1)
        {
          if (thisnotindent) --thisnotindent; else if (thisindent) --thisindent;
        }
        break;
      }
      case T_ELSEIFELSE:
      {
        if (t==token+1)
        {
          if (thisnotindent) --thisnotindent; else if (thisindent) --thisindent;
        }
        if (nextindent) --nextindent;
        break;
      }
      default: break;
    }
  } while (t++->type!=T_EOL);
  /*}}}*/
  if (width>=0) /* whole line */
  {
    if (width) /* nicely formatted listing */
    {
      assert (token->type==T_UNNUMBERED || token->type==T_INTEGER);
      if (token->type==T_INTEGER) String_appendPrintf(s,"%*ld ",width,token->u.integer);
      else String_appendPrintf(s,"%*s ",width,"");
    }
    else assert (token->type==T_UNNUMBERED);
    ++token;
  }
  while (thisindent--) String_appendPrintf(s,"  ");
  do
  {
    if (s->length>oldlength && token->type!=T_EOL)
    {
      const char *keyword;

      if ((keyword=table[token->type].text)==(const char*)0) keyword="X";
      if (ns && s->character[s->length-1]!=' ')
      {
        String_appendPrintf(s," ");
      }
      else if (isalnum((int)(s->character[s->length-1])) && isalnum((int)*keyword))
      {
        String_appendPrintf(s," ");
      }
      else if (s->character[s->length-1]!=' ' && table[token->type].space)
      {
        String_appendChar(s,' ');
      }
    }
    if (spaceto && token==spaceto) break;
    switch (token->type)
    {
      case T_DATAINPUT: String_appendChars(s,token->u.datainput); break;
      case T_ELSEIFIF: break;
      case T_IDENTIFIER: String_appendChars(s,token->u.identifier->name); break;
      case T_INTEGER: String_appendPrintf(s,"%ld",token->u.integer); break;
      case T_HEXINTEGER: String_appendPrintf(s,"&h%lx",token->u.hexinteger); break;
      case T_OCTINTEGER: String_appendPrintf(s,"&o%lo",token->u.octinteger); break;
      case T_JUNK: String_appendChar(s,token->u.junk); break;
      case T_REAL:
      {
        String_appendPrintf(s,"%.*g",DBL_DIG,token->u.real);
        if ((token->u.real<((double)LONG_MIN)) || (token->u.real>((double)LONG_MAX))) String_appendChar(s,'!');
        break;
      }
      case T_REM: String_appendPrintf(s,"%s%s",g_uppercase?"REM":"rem",token->u.rem); break;
      case T_QUOTE: String_appendPrintf(s,"'%s",token->u.rem); break;
      case T_STRING: /*{{{*/
      {
        size_t l=token->u.string->length;
        char *data=token->u.string->character;

        String_appendPrintf(s,"\"");
        while (l--)
        {
          if (*data=='"') String_appendPrintf(s,"\"");
          String_appendPrintf(s,"%c",*data);
          ++data;
        }
        String_appendPrintf(s,"\"");
        break;
      }
      /*}}}*/
      default:
      {
        if (g_uppercase)
        {
          struct String u;

          String_new(&u);
          String_appendChars(&u,table[token->type].text);
          String_ucase(&u);
          String_appendString(s,&u);
          String_destroy(&u);
        }
        else String_appendChars(s,table[token->type].text);
      }
    }
    ns=table[token->type].space;
  } while (token++->type!=T_EOL);
  if (indent) *indent=nextindent;
  if (spaceto && s->length>oldlength) memset(s->character+oldlength,' ',s->length-oldlength);
  return s;
}
/*}}}*/
void Token_init(int b_c, int uc) /*{{{*/
{
#define PROPERTY(t,assoc,unary_priority,binary_priority,is_unary,is_binary) \
  g_token_property[t]=(assoc<<8)|(unary_priority<<5)|(binary_priority<<2)|(is_unary<<1)|is_binary

  g_backslash_colon=b_c;
  g_uppercase=uc;
  PROPERTY(T_POW,  1,0,7,0,1);
  PROPERTY(T_MULT, 0,0,5,0,1);
  PROPERTY(T_DIV,  0,0,5,0,1);
  PROPERTY(T_IDIV, 0,0,5,0,1);
  PROPERTY(T_MOD,  0,0,5,0,1);
  PROPERTY(T_PLUS, 0,6,4,1,1);
  PROPERTY(T_MINUS,0,6,4,1,1);
  PROPERTY(T_LT,   0,0,3,0,1);
  PROPERTY(T_LE,   0,0,3,0,1);
  PROPERTY(T_EQ,   0,0,3,0,1);
  PROPERTY(T_GE,   0,0,3,0,1);
  PROPERTY(T_GT,   0,0,3,0,1);
  PROPERTY(T_NE,   0,0,3,0,1);
  PROPERTY(T_NOT,  0,2,0,1,0);
  PROPERTY(T_AND,  0,0,1,0,1);
  PROPERTY(T_OR,   0,0,0,0,1);
  PROPERTY(T_XOR,  0,0,0,0,1);
  PROPERTY(T_EQV,  0,0,0,0,1);
  PROPERTY(T_IMP,  0,0,0,0,1);
}
/*}}}*/
