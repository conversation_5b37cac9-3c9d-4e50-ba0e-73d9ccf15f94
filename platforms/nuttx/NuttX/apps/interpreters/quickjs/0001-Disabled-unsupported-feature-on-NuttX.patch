From a3374db7eca1141ec8a3909ff001f52f3e79d905 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Wed, 10 Mar 2021 16:30:13 +0800
Subject: [PATCH] Disabled unsupported feature on NuttX.

N/A

Signed-off-by: <PERSON> <<EMAIL>>
Change-Id: I4ff1793609b752320d64406deec3c0ed9b4da965
---
 qjs.c          |  2 ++
 quickjs-libc.c | 14 +++++++-------
 2 files changed, 9 insertions(+), 7 deletions(-)

diff --git a/qjs.c b/qjs.c
index 4dd11f8..a35a500 100644
--- a/qjs.c
+++ b/qjs.c
@@ -448,8 +448,10 @@ int main(int argc, char **argv)
         }
     }
 
+#ifdef CONFIG_BIGNUM
     if (load_jscalc)
         bignum_ext = 1;
+#endif
 
     if (trace_memory) {
         js_trace_malloc_init(&trace_data);
diff --git a/quickjs-libc.c b/quickjs-libc.c
index e8b81e9..858703c 100644
--- a/quickjs-libc.c
+++ b/quickjs-libc.c
@@ -3558,10 +3558,10 @@ static const JSCFunctionListEntry js_os_funcs[] = {
     JS_CFUNC_MAGIC_DEF("setWriteHandler", 2, js_os_setReadHandler, 1 ),
     JS_CFUNC_DEF("signal", 2, js_os_signal ),
     OS_FLAG(SIGINT),
-    OS_FLAG(SIGABRT),
-    OS_FLAG(SIGFPE),
-    OS_FLAG(SIGILL),
-    OS_FLAG(SIGSEGV),
+    // OS_FLAG(SIGABRT),
+    // OS_FLAG(SIGFPE),
+    // OS_FLAG(SIGILL),
+    // OS_FLAG(SIGSEGV),
     OS_FLAG(SIGTERM),
 #if !defined(_WIN32)
     OS_FLAG(SIGQUIT),
@@ -3573,8 +3573,8 @@ static const JSCFunctionListEntry js_os_funcs[] = {
     OS_FLAG(SIGCONT),
     OS_FLAG(SIGSTOP),
     OS_FLAG(SIGTSTP),
-    OS_FLAG(SIGTTIN),
-    OS_FLAG(SIGTTOU),
+    // OS_FLAG(SIGTTIN),
+    // OS_FLAG(SIGTTOU),
 #endif
     JS_CFUNC_DEF("setTimeout", 2, js_os_setTimeout ),
     JS_CFUNC_DEF("clearTimeout", 1, js_os_clearTimeout ),
@@ -3603,7 +3603,7 @@ static const JSCFunctionListEntry js_os_funcs[] = {
     JS_CFUNC_DEF("realpath", 1, js_os_realpath ),
     JS_CFUNC_DEF("symlink", 2, js_os_symlink ),
     JS_CFUNC_DEF("readlink", 1, js_os_readlink ),
-    JS_CFUNC_DEF("exec", 1, js_os_exec ),
+    // JS_CFUNC_DEF("exec", 1, js_os_exec ),
     JS_CFUNC_DEF("waitpid", 2, js_os_waitpid ),
     OS_FLAG(WNOHANG),
     JS_CFUNC_DEF("pipe", 0, js_os_pipe ),
-- 
2.25.1

