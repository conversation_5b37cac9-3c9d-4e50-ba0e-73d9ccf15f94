name: Lint

on: [pull_request]

concurrency:
  group: lint-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  lint:
    permissions:
      contents: read  # for actions/checkout to fetch code
      statuses: write  # for github/super-linter to mark status of each linter run
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - run: mkdir super-linter.report
      - name: <PERSON><PERSON>
        uses: github/super-linter@v4
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          VALIDATE_ALL_CODEBASE: false
          VALIDATE_PYTHON_BLACK: true
          VALIDATE_PYTHON_FLAKE8: true
          PYTHON_FLAKE8_CONFIG_FILE: setup.cfg
          VALIDATE_PYTHON_ISORT: true
          PYTHON_ISORT_CONFIG_FILE: setup.cfg
          VALIDATE_YAML: true
