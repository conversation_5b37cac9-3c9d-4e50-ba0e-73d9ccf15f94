# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
name: Docker-Linux

on:
  push:
    # Publish `master` as Docker `latest` image.
    branches:
      - master
    paths:
      - 'tools/ci/docker/linux/**'
      - '.github/workflows/docker_linux.yml'

  # Run builds for any PRs.
  pull_request:
    paths:
      - 'tools/ci/docker/linux/**'
      - '.github/workflows/docker_linux.yml'

env:
  IMAGE_NAME: apache-nuttx-ci-linux

concurrency:
  group: docker-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

permissions:
  packages: write
  contents: read

jobs:
  # Push image to GitHub Packages.
  push:
    runs-on: ubuntu-latest
    env:
      IMAGE_TAG: ghcr.io/${{ github.repository }}/apache-nuttx-ci-linux
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Log into registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build Linux image
        uses: docker/build-push-action@v3
        with:
          context: tools/ci/docker/linux
          load: true
          tags: ${{ env.IMAGE_TAG }}

      - name: Push Linux image
        uses: docker/build-push-action@v3
        with:
          context: tools/ci/docker/linux
          platforms: linux/amd64
          push: ${{ github.ref == 'refs/heads/master' }}
          tags: ${{ env.IMAGE_TAG }}
