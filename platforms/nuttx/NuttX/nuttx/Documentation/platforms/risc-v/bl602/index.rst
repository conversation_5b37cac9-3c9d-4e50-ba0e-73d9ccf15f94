==================
Bouffalo Lab BL602
==================

BL602/BL604 is Wi-Fi + BLE combo chipset for ultra-low-cost and
low-power application. Wireless subsystem contains 2.4G radio, Wi-Fi
802.11b/g/n and BLE 5.0 baseband/MAC designs. Microcontroller subsystem
contains a low-power 32-bit RISC CPU, high-speed cache and memories.
Power Management Unit controls low-power modes. Moreover, variety of
security features are supported.

-  32-bit RISC CPU with FPU (floating point unit)

-  276KB RAM

-  128KB ROM

-  1Kb eFuse

-  Embedded Flash (Optional)

-  Four DMA channels

BL602 Toolchain
===============

Toolchain can be obtained from
`bl_iot_sdk/toolchain/riscv <https://github.com/bouffalolab/bl_iot_sdk/tree/master/toolchain/riscv/Linux>`__

In addition, SiFive’s toolchain can be downloaded from:

https://static.dev.sifive.com/dev-tools/riscv64-unknown-elf-gcc-8.3.0-2019.08.0-x86_64-linux-ubuntu14.tar.gz

Building
========

nsh
---

First make sure the ``riscv-gnu-toolchain`` has been installed
correctly.

Configure the NuttX project: ``./tools/configure.sh bl602evb:nsh``. Run
``make`` to build the project.

.. code:: shell

   root@pc:~/nuttx$ tools/configure.sh bl602evb:nsh

   root@pc:~/nuttx$ make

wifi
----

Configure the NuttX project: ``./tools/configure.sh bl602evb:wifi``. Run
``make`` to build the project.

.. code:: shell

   root@pc:~/nuttx$ tools/configure.sh bl602evb:wifi

   root@pc:~/nuttx$ make

Flashing
========

Dev Cube is a chip integrated development tool provided by Bouffalolab,
which includes three functions: IOT program download, MCU program
download and RF performance test.

Dev Cube provides users with the function of downloading programs, and
supports the configuration of parameters such as clock and flash. Users
can decide whether to encrypt the program, add signatures, and replace
the information file, user resource file, partition table and other
functional configurations according to their own needs when the program
is started.

Users can get the latest version of Dev Cube through `Bouffalo Lab
Dev <https://dev.bouffalolab.com/download>`__.

Config download
---------------

**Configuration parameters include:**

-  **Interface**: Select the communication interface for flashing, here
   select Uart to download

-  **COM Port**: When select UART for downloading, select the COM port
   number connected to the chip, and you can click the Refresh button to
   refresh the COM port

-  **Uart Rate**: When select UART for downloading, fill in the baud
   rate. The recommended download baud rate is 2MHz,

-  **Xtal**: Select the crystal oscillator frequency when downloading.
   If the board does not have a soldered crystal oscillator, the
   internal RC32M clock source should be selected here,default is 40M

-  **Chip Erase**: default setting is False

**Configuration parameters include:**

-  **Factory Params**: Use the dts file in the device_tree folder of the
   corresponding chip model in the Dev Cube directory. The default
   selection is 40M.

-  **Partition Table**: Use the partition table in the partition folder
   of the corresponding chip model in the Dev Cube directory, and 2M
   files are selected by default for BL602

   **For the nuttx, BL602 has a dedicated partition file. It is placed
   in** ``nuttx/tools/bl602``

-  **Boot2 Bin**: It is the first Flash program that runs after the
   system is started. It is responsible for establishing the BLSP
   security environment and guiding the main program to run. It uses the
   Boot2 file in the builtin_imgs folder of the corresponding chip model
   in the Dev Cube directory.

-  **Firmware Bin**: Bin file path generated by user compilation

-  **Media/Romfs**: Choose one of Media and Romfs. If you check media,
   you should provide a file. If you check Romfs, a folder should be
   provided.

-  **MFG Bin**: Select MFG file

-  **AES-Encrypt**: If you use the encryption function, you need to
   select the AES-Encrypt option, and enter the Key and IV used for
   encryption. Hexadecimal characters should be entered, and a Byte is
   composed of two characters, so the Key and IV require 32 characters
   to be input respectively. It should be noted that the last 8
   characters of the IV (ie 4Bytes) must be all 0

-  **Single Download Config**: you can download a single file. Fill in
   the starting address of the download, starting with 0x

For details, please refer to `BLFlashEnv — BL602 IoT
SDK <https://bouffalolab.github.io/bl_iot_sdk/Developer_Environment/BLFlashEnv/BLFlashEnv.html#iot>`__

Debugging with OpenOCD
======================

Download OpenOCD `RISC‑V
OpenOCD <https://static.dev.sifive.com/dev-tools/freedom-tools/v2020.12/riscv-openocd-0.10.0-2020.12.1-x86_64-linux-ubuntu14.tar.gz>`__.

Start OpenOCD:

.. code:: shell

   root@pc:~$ openocd -f ~/bl_iot_sdk/tools/debug/if_bflb_link.cfg -f ~/bl_iot_sdk/tools/debug/tgt_602_xip.cfg

Start GDB:

.. code:: shell

   root@pc:~$ riscv64-unknown-elf-gdb ~/nuttx/nuttx -x ~/bl_iot_sdk/tools/debug/602.init

Connect OpenOCD:

.. code:: shell

   (gdb) target remote :3333

   (gdb) c

BL602 Peripheral Support
========================

-  One SDIO 2.0 slave

-  One SPI master/slave

-  Two UART

-  One I2C master

-  Five PWM channels

-  10-bit general DAC

-  12-bit general ADC

-  Two general analog comparators (ACOMP)

-  PIR (Passive Infra-Red) detection

-  IR remote HW accelerator

-  16 or 23 GPIOs

========== ======= =====
Peripheral Support NOTES
========== ======= =====
GPIO       Yes     
UART       Yes     
SPI        Yes     
I2C        Yes     
DMA        Yes     
Wifi       Yes     
SPI FLASH  Yes     
Timers     Yes     
Watchdog   Yes     
RTC        Yes     
RNG        No      
AES        No      
eFuse      Yes     
ADC        No      
Bluetooth  No      
LED_PWM    No      
SHA        No      
RSA        No      
========== ======= =====
