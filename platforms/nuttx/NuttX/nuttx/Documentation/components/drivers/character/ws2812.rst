===========
ws2812 Drivers
===========

This driver allows control of any smart pixels that use the ws2812
protocol.  It supports pixel chains that use normal RGB pixels or
newer RGBW pixels.

The current driver does not support mixed RGB and RGBW pixels in a
single chain.

The NuttX ws2812 driver is split into two parts:

#. An "upper half", generic driver that provides the common
   interface to application level code, and
#. A "lower half", platform-specific driver that implements the
   low-level formatting and output.

Files supporting ws2812 can be found in the following locations:

-  **Interface Definition**. The header file for the NuttX ws2812
   driver reside at ``include/nuttx/leds/ws2812.h``. This header
   file includes both the application level interface to the ws2812
   driver as well as the interface between the "upper half" and
   "lower half" drivers. The ws2812 module uses a standard character
   driver framework.
-  **"Upper Half" Driver**. The generic, "upper half" ws2812 driver
   resides at ``drivers/leds/ws2812.c``.
-  **"Lower Half" Drivers**. Platform-specific ws2812 drivers reside
   in ``arch/``\ *<architecture>*\ ``/src/``\ *<hardware>*
   directory for the specific processor *<architecture>* and for
   the specific *<chip>* ws2812 peripheral devices.
