OS Components
=============

NuttX is very feature-rich RTOS and is thus composed of various different subsystems. The following sections explain how each of these main RTOS components work and can be used. For detailed documentation on the specific API used in this case, you can head to the :doc:`reference <../reference/index>`.

.. toctree::
   :maxdepth: 2
   :caption: Contents:
   
   power.rst
   binfmt.rst
   drivers/index.rst
   filesystem.rst
   nxflat.rst
   nxgraphics/index.rst
   nxwidgets.rst
   paging.rst
