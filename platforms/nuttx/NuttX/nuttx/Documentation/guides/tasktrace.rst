==========
Task Trace
==========

Task Trace is the tool to collect the various events in the NuttX kernel and display the result graphically.

It can collect the following events.

  - Task execution, termination, switching
  - System call enter/leave
  - Interrupt handler enter/leave

.. toctree::
  :maxdepth: 1
  :caption: Contents:

  tasktraceuser.rst
  tasktraceinternal.rst

.. image:: image/task-trace-overview.png
