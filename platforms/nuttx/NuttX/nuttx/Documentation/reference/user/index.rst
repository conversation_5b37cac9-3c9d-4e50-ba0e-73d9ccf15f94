=============
Userspace API
=============

.. note:: Migration in progress

This manual provides general usage information for the NuttX RTOS from
the perspective of the firmware developer.

The intended audience for this document are firmware developers who are
implementing applications on NuttX. Specifically, this documented is
limited to addressing only NuttX RTOS APIs that are available to the
application developer. As such, this document does not focus on any
technical details of the organization or implementation of NuttX. Those
technical details are provided in the NuttX Porting
Guide.

Information about configuring and building NuttX is also needed by the
application developer. That information can also be found in the NuttX
Porting Guide.

.. toctree::
  :maxdepth: 1
  :caption: Contents
  :glob:

  *_*
  structures.rst
