==========
Trademarks
==========

-  NuttX is a registered trademark of Gregory Nutt.
-  ARM, ARM7 ARM7TDMI, ARM9, ARM920T, ARM926EJS, Cortex-M3 are
   trademarks of Advanced RISC Machines, Limited.
-  Beaglebone is a trademark of GHI.
-  BSD is a trademark of the University of California, Berkeley, USA.
-  Cygwin is a trademark of Red Hat, Incorporated.
-  Linux is a registered trademark of Linus Torvalds.
-  Eagle-100 is a trademark of `Micromint USA,
   LLC <http://www.micromint.com/>`__.
-  EnergyLite is a trademark of STMicroelectronics.
-  EFM32 is a trademark of Silicon Laboratories, Inc.
-  LPC2148 is a trademark of NXP Semiconductors.
-  POSIX is a trademark of the Institute of Electrical and Electronic
   Engineers, Inc.
-  RISC-V is a registration pending trade mark of the RISC-V Foundation.
-  Sitara is a trademark of Texas Instruments Incorporated.
-  TI is a tradename of Texas Instruments Incorporated.
-  Tiva is a trademark of Texas Instruments Incorporated.
-  UNIX is a registered trademark of The Open Group.
-  VxWorks is a registered trademark of Wind River Systems,
   Incorporated.
-  ZDS, ZNEO, Z16F, Z80, and Zilog are a registered trademark of Zilog,
   Inc.

NOTE: NuttX is *not* licensed to use the POSIX trademark. NuttX uses the
POSIX standard as a development guideline only.
