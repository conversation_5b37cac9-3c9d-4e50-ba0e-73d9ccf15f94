=========
Resources
=========

.. note:: this should be revised

Here's a list of Apache NuttX resources that you might find helpful:

 * Apache NuttX

   * `Apache NuttX website <https://nuttx.apache.org>`_
   * `Apache NuttX online documentation <https://cwiki.apache.org/confluence/display/NUTTX/Nuttx>`_
   * `Apache NuttX mailing list <https://nuttx.apache.org/community/>`_ – a very active mailing list, the place to get help with your application or any questions you have about NuttX.
   * `Apache NuttX YouTube channel <https://www.youtube.com/channel/UC0QciIlcUnjJkL5yJJBmluw/videos>`_ – <PERSON> de <PERSON>'s YouTube channel on NuttX. It's a source of a lot of great practical information.
   * `Apache NuttX Coding Standard <https://cwiki.apache.org/confluence/display/NUTTX/Coding+Standard>`_ — How code should look when you submit new files or modify existing ones.
   * `Apache NuttX Code Contribution Guidelines <https://cwiki.apache.org/confluence/display/NUTTX/Code+Contribution+Workflow>`_ — The full workflow to follow for submitting code with all the details.

 * Git

   * `Git Cheat Sheet (by GitHub) <https://github.github.com/training-kit/downloads/github-git-cheat-sheet.pdf>`_
   * `Git Book (online) <https://git-scm.com/book/en/v2>`_

