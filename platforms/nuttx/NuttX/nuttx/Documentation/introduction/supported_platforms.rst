.. include:: /substitutions.rst

===================
Supported Platforms
===================

**Supported Platforms by CPU core**. The number of ports to this
CPU follow in parentheses. The state of the various ports vary
from board-to-board. Follow the links for the details:

.. list-table::
   :class: valign-top

   * -

       - :ref:`introduction/detailed_support:Linux User Mode Simulation` (1)

       - ARM

         - :ref:`introduction/detailed_support:ARM7TDMI` (4)
         - :ref:`introduction/detailed_support:ARM920T` (1)
         - :ref:`introduction/detailed_support:ARM926EJS` (4)
         - :ref:`introduction/detailed_support:Other ARMv4` (1)
         - :ref:`introduction/detailed_support:ARM1176JZ` (1)
         - :ref:`introduction/detailed_support:ARM Cortex-A5` (3)
         - :ref:`introduction/detailed_support:ARM Cortex-A8` (2)
         - :ref:`introduction/detailed_support:ARM Cortex-A9` (1)
         - :ref:`introduction/detailed_support:ARM Cortex-R4` (2)
         - :ref:`introduction/detailed_support:ARM Cortex-M0/M0+` (13)
         - :ref:`introduction/detailed_support:ARM Cortex-M3` (39)
         - :ref:`introduction/detailed_support:ARM Cortex-M4` (59)
         - :ref:`introduction/detailed_support:ARM Cortex-M7` (15)

       - Atmel AVR

         - :ref:`introduction/detailed_support:Microchip AVR` (8-bit) (5)
         - :ref:`introduction/detailed_support:Microchip AVR32` (1)

       - Freescale

         - :ref:`introduction/detailed_support:Freescale M68HCS12` (2)
     -

       - Intel

         - :ref:`introduction/detailed_support:Intel 80x86` (2)

       - Microchip

         - :ref:`introduction/detailed_support:Microchip PIC32MX` (MIPS M4K) (4)
         - :ref:`introduction/detailed_support:Microchip PIC32MZEF` (MIPS M5150) (1)

       - Misoc

         - :ref:`introduction/detailed_support:Misoc` (1)

       - OpenRISC

         - :ref:`introduction/detailed_support:OpenRISC mor1kx` (1)

       - Renesas/Hitachi:

         - :ref:`introduction/detailed_support:Renesas/Hitachi SuperH` (1/2)
         - :ref:`introduction/detailed_support:Renesas M16C/26` (1/2)
         - :ref:`introduction/detailed_support:Renesas RX65N` (2)

     -
       - :ref:`introduction/detailed_support:RISC-V` (2)

         - :ref:`introduction/detailed_support:LiteX on Arty A7` (1)

       - Xtensa LX6:

         - :ref:`introduction/detailed_support:ESP32 (Dual Xtensa LX6)` (1)

       - ZiLOG

         - :ref:`introduction/detailed_support:ZiLOG ZNEO Z16F` (2)
         - :ref:`introduction/detailed_support:ZiLOG eZ80 Acclaim!` (4)
         - :ref:`introduction/detailed_support:ZiLOG Z8Encore!` (2)
         - :ref:`introduction/detailed_support:ZiLOG Z180` (1)
         - :ref:`introduction/detailed_support:ZiLOG Z80` (2)

**Supported Platforms by Manufacturer/MCU Family**. CPU core type
follows in parentheses. The state of the various ports vary from MCU to
MCU. Follow the links for the details:

.. list-table::
   :class: valign-top

   * -

       - :ref:`introduction/detailed_support:Linux User Mode Simulation` (1)

       - Allwinner

         - :ref:`introduction/detailed_support:Allwinner A10` (Cortex-A8)

       - Broadcom

         - :ref:`introduction/detailed_support:Broadcom BCM2708` (ARM1176JZ)

       - Espressif

         - :ref:`introduction/detailed_support:Xtensa LX6 ESP32` (Dual Xtensa LX6)

       - Host PC based simulations

         - :ref:`introduction/detailed_support:Linux User Mode Simulation`

       - Infineon

         - :ref:`introduction/detailed_support:Infineon XMC45xx`

       - Intel

         - :ref:`introduction/detailed_support:Intel 80x86`

       - Maxim Integrated

         - :ref:`introduction/detailed_support:Maxim Integrated MAX32660` (ARM Cortex-M3)

       - Microchip

         - :ref:`introduction/detailed_support:Microchip PIC32MX2xx` (MIPS32 M4K)
         - :ref:`introduction/detailed_support:Microchip PIC32MX4xx` (MIPS32 M4K)
         - :ref:`introduction/detailed_support:Microchip PIC32MX7xx` (MIPS32 M4K)
         - :ref:`introduction/detailed_support:Microchip PIC32MZEC` (MIPS32 microAptiv)
         - :ref:`introduction/detailed_support:Microchip PIC32MZEF` (MIPS32 M5150)

       - Microchip (Formerly Atmel)

         - :ref:`introduction/detailed_support:AVR ATMega128` (8-bit AVR)
         - :ref:`introduction/detailed_support:AVR ATMega1284p` (8-bit AVR)
         - :ref:`introduction/detailed_support:AVR ATMega2560` (8-bit AVR)
         - :ref:`introduction/detailed_support:AVR AT90USB64x and AT90USB6128x` (8-bit AVR)
         - :ref:`introduction/detailed_support:Microchip AVR32` (AT32UC3BXXX, 32-bit AVR32)
         - :ref:`introduction/detailed_support:Microchip SAMD20` (ARM Cortex-M0+)
         - :ref:`introduction/detailed_support:Microchip SAMD21` (ARM Cortex-M0+)
         - :ref:`introduction/detailed_support:Microchip SAML21` (ARM Cortex-M0+)
         - :ref:`introduction/detailed_support:Microchip SAM3U` (ARM Cortex-M3)
         - :ref:`introduction/detailed_support:Microchip SAM3X` (ARM Cortex-M3)
         - :ref:`introduction/detailed_support:Microchip SAM4CM` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:Microchip SAM4E` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:Microchip SAM4L` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:Microchip SAM4S` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:Microchip SAMD5x/E5x` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:Microchip SAME70` (ARM Cortex-M7)
         - :ref:`introduction/detailed_support:Microchip SAMV71` (ARM Cortex-M7)
         - :ref:`introduction/detailed_support:Microchip SAMA5D2` (ARM Cortex-A5)
         - :ref:`introduction/detailed_support:Microchip SAMA5D3` (ARM Cortex-A5)
         - :ref:`introduction/detailed_support:Microchip SAMA5D4` (ARM Cortex-A5)

       - Moxa

         - :ref:`introduction/detailed_support:Moxa NP51x0` (ARMv4)

       - nuvoTon

         - :ref:`introduction/detailed_support:nuvoTon NUC120` (ARM Cortex-M0)

       - Nordic Semiconductor

         - :ref:`introduction/detailed_support:Nordic Semiconductor NRF52xxx` (ARM Cortex-M4)

       - NXP/Freescale

         - :ref:`introduction/detailed_support:Freescale M68HCS12`
         - :ref:`introduction/detailed_support:NXP/Freescale i.MX1` (ARM920-T)
         - :ref:`introduction/detailed_support:NXP/Freescale i.MX6` (ARM Cortex-A9)
         - :ref:`introduction/detailed_support:NXP/Freescale i.MX RT` (ARM Cortex-M7)
         - :ref:`introduction/detailed_support:NXP/FreeScale KL25Z` (ARM Cortex-M0+)
         - :ref:`introduction/detailed_support:NXP/FreeScale KL26Z` (ARM Cortex-M0+)
         - :ref:`introduction/detailed_support:NXP/FreeScale Kinetis K20` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:NXP/FreeScale Kinetis K28F` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:NXP/FreeScale Kinetis K40` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:NXP/FreeScale Kinetis K60` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:NXP/FreeScale Kinetis K64` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:NXP/FreeScale Kinetis K66` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:NXP LPC11xx` (Cortex-M0)
         - :ref:`introduction/detailed_support:NXP LPC214x` (ARM7TDMI)
         - :ref:`introduction/detailed_support:NXP LPC2378` (ARM7TDMI)
         - :ref:`introduction/detailed_support:NXP LPC3131` (ARM9E6JS)
         - :ref:`introduction/detailed_support:NXP LPC315x` (ARM9E6JS)
         - :ref:`introduction/detailed_support:NXP LPC176x` (ARM Cortex-M3)
         - :ref:`introduction/detailed_support:NXP LPC178x` (ARM Cortex-M3)
         - :ref:`introduction/detailed_support:NXP LPC40xx` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:NXP LPC43xx` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:NXP LPC54xx` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:NXP S32K11x` (Cortex-M0+)
         - :ref:`introduction/detailed_support:NXP S32K14x` (Cortex-M4F)

     -

       - ON Semiconductor:

         - :ref:`introduction/detailed_support:On Semiconductor LC823450` (Dual core ARM Cortex-M3)

       - Renesas/Hitachi:

         - :ref:`introduction/detailed_support:Renesas/Hitachi SuperH`
         - :ref:`introduction/detailed_support:Renesas M16C/26`
         - :ref:`introduction/detailed_support:Renesas RX65N`

       - Silicon Laboratories, Inc.

         - :ref:`introduction/detailed_support:SiLabs EFM32 Gecko` (ARM Cortex-M3)
         - :ref:`introduction/detailed_support:SiLabs EFM32 Giant Gecko` (ARM Cortex-M3)

       - Sony.

         - :ref:`introduction/detailed_support:Sony CXD56xx` (6 x ARM Cortex-M4)

       - STMicroelectronics

         - :ref:`introduction/detailed_support:STMicro STR71x` (ARM7TDMI)
         - :ref:`introduction/detailed_support:STMicro STM32 F0xx` (STM32 F0, ARM Cortex-M0)
         - :ref:`introduction/detailed_support:STMicro STM32 L0xx` (STM32 L0, ARM Cortex-M0)
         - :ref:`introduction/detailed_support:STMicro STM32 G0xx` (STM32 G0, ARM Cortex-M0+)
         - :ref:`introduction/detailed_support:STMicro STM32 L152` (STM32 L1 "EnergyLite" Line, ARM Cortex-M3)
         - :ref:`introduction/detailed_support:STMicro STM32 L15x/16x` (STM32 L1 "EnergyLite" Medium+ Density, ARM Cortex-M3)
         - :ref:`introduction/detailed_support:STMicro STM32 F100x` (STM32 F1 "Value Line" Family, ARM Cortex-M3)
         - :ref:`introduction/detailed_support:STMicro STM32 F102x` (STM32 F1 family, ARM Cortex-M3)
         - :ref:`introduction/detailed_support:STMicro STM32 F103C4/C8` (STM32 F1 "Low- and Medium-Density Line" Family, ARM Cortex-M3)
         - :ref:`introduction/detailed_support:STMicro STM32 F103x` (STM32 F1 family, ARM Cortex-M3)
         - :ref:`introduction/detailed_support:STMicro STM32 F105x` (ARM Cortex-M3)
         - :ref:`introduction/detailed_support:STMicro STM32 F107x` (STM32 F1 family, "Connectivity Line" ARM Cortex-M3)
         - :ref:`introduction/detailed_support:STMicro STM32 F205x` (STM32 F2 family, ARM Cortex-M3)
         - :ref:`introduction/detailed_support:STMicro STM32 F207x` (STM32 F2 family, ARM Cortex-M3)
         - :ref:`introduction/detailed_support:STMicro STM32 F302x` (STM32 F3 family, ARM Cortex-M4)
         - :ref:`introduction/detailed_support:STMicro STM32 F303x` (STM32 F3 family, ARM Cortex-M4)
         - :ref:`introduction/detailed_support:STMicro STM32 F334` (STM32 F3 family, ARM Cortex-M4)
         - :ref:`introduction/detailed_support:STMicro STM32 F372/F373` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:STMicro STM32 F4x1` (STM32 F4 family, ARM Cortex-M4)
         - :ref:`introduction/detailed_support:STMicro STM32 F405x/407x` (STM32 F4 family, ARM Cortex-M4)
         - :ref:`introduction/detailed_support:STMicro STM32 F427/F437` (STM32 F4 family, ARM Cortex-M4)
         - :ref:`introduction/detailed_support:STMicro STM32 F429` (STM32 FB family, ARM Cortex-M4)
         - :ref:`introduction/detailed_support:STMicro STM32 F433` (STM32 F4 family, ARM Cortex-M4)
         - :ref:`introduction/detailed_support:STMicro STM32 F446` (STM32 F4 family, ARM Cortex-M4)
         - :ref:`introduction/detailed_support:STMicro STM32 F46xx` (STM32 F4 family, ARM Cortex-M4)
         - :ref:`introduction/detailed_support:STMicro STM32 G474x` (STM32 G4 family, ARM Cortex-M4)
         - :ref:`introduction/detailed_support:STMicro STM32 G431x` (STM32 G4 family, ARM Cortex-M4)
         - :ref:`introduction/detailed_support:STMicro STM32 L4x2` (STM32 L4 family, ARM Cortex-M4)
         - :ref:`introduction/detailed_support:STMicro STM32 L475` (STM32 L4 family, ARM Cortex-M4)
         - :ref:`introduction/detailed_support:STMicro STM32 L476` (STM32 L4 family, ARM Cortex-M4)
         - :ref:`introduction/detailed_support:STMicro STM32 L496` (STM32 L4 family, ARM Cortex-M4)
         - :ref:`introduction/detailed_support:STMicro STM32 L4Rx` (STM32 LB family, ARM Cortex-M4)
         - :ref:`introduction/detailed_support:STMicro STM32 F72x/F73x` (STM32 F7 family, ARM Cortex-M7)
         - :ref:`introduction/detailed_support:STMicro STM32 F745/F746` (STM32 F7 family, ARM Cortex-M7)
         - :ref:`introduction/detailed_support:STMicro STM32 F756` (STM32 F7 family, ARM Cortex-M7)
         - :ref:`introduction/detailed_support:STMicro STM32 F76xx/F77xx` (STM32 F7 family, ARM Cortex-M7)
         - :ref:`introduction/detailed_support:STMicro STM32 H7x3` (STM32 H7 family, ARM Cortex-M7)

     -

       - Texas Instruments (some formerly Luminary)

         - :ref:`introduction/detailed_support:TI TMS320-C5471` (ARM7TDMI)
         - :ref:`introduction/detailed_support:TI TMS320-DM320` (ARM9E6JS)
         - :ref:`introduction/detailed_support:TI/Stellaris LM3S6432` (ARM Cortex-M3)
         - :ref:`introduction/detailed_support:TI/Stellaris LM3S6432S2E` (ARM Cortex-M3)
         - :ref:`introduction/detailed_support:TI/Stellaris LM3S6918` (ARM Cortex-M3)
         - :ref:`introduction/detailed_support:TI/Stellaris LM3S6965` (ARM Cortex-M3)
         - :ref:`introduction/detailed_support:TI/Stellaris LM3S8962` (ARM Cortex-M3)
         - :ref:`introduction/detailed_support:TI/Stellaris LM3S9B92` (ARM Cortex-M3)
         - :ref:`introduction/detailed_support:TI/Stellaris LM3S9B96` (ARM Cortex-M3)
         - :ref:`introduction/detailed_support:TI/SimpleLink CC13x0` (ARM Cortex-M3)
         - :ref:`introduction/detailed_support:TI/Stellaris LM4F120x` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:TI/Tiva TM4C123G` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:TI/Tiva TM4C1294` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:TI/Tiva TM4C129X` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:TI/SimpleLink CC13x2` (ARM Cortex-M4)
         - :ref:`introduction/detailed_support:TI/Hercules TMS570LS04xx` (ARM Cortex-R4)
         - :ref:`introduction/detailed_support:TI/Hercules TMS570LS31xx` (ARM Cortex-R4)
         - :ref:`introduction/detailed_support:TI/Sitara AM335x` (Cortex-A8)

       - ZiLOG

         - :ref:`introduction/detailed_support:ZiLOG ZNEO Z16F`
         - :ref:`introduction/detailed_support:ZiLOG eZ80 Acclaim!`
         - :ref:`introduction/detailed_support:ZiLOG Z8Encore!`
         - :ref:`introduction/detailed_support:ZiLOG Z180`
         - :ref:`introduction/detailed_support:ZiLOG Z80`

