.. note::
  The present documentation is a recent addition to NuttX and was migrated from previous
  documentation content. For this reason, it is possible you may find broken links or
  formatting errors. You can help contribute fixes or improvements to this documentation
  by following these :doc:`instructions <contributing/documentation>`.

.. tip::
  You can find the old documentation `here <https://cwiki.apache.org/confluence/display/NUTTX/Documentation>`_.

===================
NuttX Documentation
===================

NuttX is a real-time operating system (RTOS) with an emphasis on standards compliance and small footprint. Scalable from 8-bit to 64-bit microcontroller environments, the primary governing standards in NuttX are POSIX and ANSI standards. Additional standard APIs from Unix and other common RTOS’s (such as VxWorks) are adopted for functionality not available under these standards, or for functionality that is not appropriate for deeply-embedded environments (such as fork()).

Last Updated: |today|

.. toctree::
   :caption: Table of Contents
   :maxdepth: 2

   Home <self>
   introduction/index.rst
   quickstart/index.rst
   contributing/index.rst
   introduction/inviolables.rst
   platforms/index.rst
   components/index.rst
   applications/index.rst
   reference/index.rst
   faq/index.rst
   guides/index.rst
   glossary.rst

.. include:: substitutions.rst
