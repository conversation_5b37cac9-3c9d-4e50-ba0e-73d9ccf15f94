############################################################################
# arch/mips/src/mips32/Toolchain.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

# Setup for the selected toolchain

#
# Handle old-style chip-specific toolchain names in the absence of
# a new-style toolchain specification, force the selection of a single
# toolchain and allow the selected toolchain to be overridden by a
# command-line selection.
#

ifeq ($(filter y, \
      $(CONFIG_MIPS32_TOOLCHAIN_GNU_ELF) \
    ),y)
  CONFIG_MIPS32_TOOLCHAIN ?= GNU_ELF
endif

ifeq ($(filter y, \
      $(CONFIG_MIPS32_TOOLCHAIN_PINGUINOL) \
    ),y)
  CONFIG_MIPS32_TOOLCHAIN ?= PINGUINOL
endif

ifeq ($(filter y, \
      $(CONFIG_MIPS32_TOOLCHAIN_SOURCERY_CODEBENCH_LITE) \
    ),y)
  CONFIG_MIPS32_TOOLCHAIN ?= SOURCERY_CODEBENCH_LITE
endif

ifeq ($(filter y, \
      $(CONFIG_MIPS32_TOOLCHAIN_MICROCHIPL_XC32) \
    ),y)
  CONFIG_MIPS32_TOOLCHAIN ?= MICROCHIPL_XC32
endif

ifeq ($(filter y, \
      $(CONFIG_MIPS32_TOOLCHAIN_MICROCHIPL) \
    ),y)
  CONFIG_MIPS32_TOOLCHAIN ?= MICROCHIPL
endif

ifeq ($(filter y, \
      $(CONFIG_MIPS32_TOOLCHAIN_MICROCHIPL_LITE) \
    ),y)
  CONFIG_MIPS32_TOOLCHAIN ?= MICROCHIPL_LITE
endif

ifeq ($(filter y, \
      $(CONFIG_MIPS32_TOOLCHAIN_MICROCHIPW_XC32) \
    ),y)
  CONFIG_MIPS32_TOOLCHAIN ?= MICROCHIPW_XC32
endif

ifeq ($(filter y, \
      $(CONFIG_MIPS32_TOOLCHAIN_MICROCHIPW) \
    ),y)
  CONFIG_MIPS32_TOOLCHAIN ?= MICROCHIPW
endif

ifeq ($(filter y, \
      $(CONFIG_MIPS32_TOOLCHAIN_MICROCHIPW_LITE) \
    ),y)
  CONFIG_MIPS32_TOOLCHAIN ?= MICROCHIPW_LITE
endif

ifeq ($(filter y, \
      $(CONFIG_MIPS32_TOOLCHAIN_MICROCHIPOPENL) \
    ),y)
  CONFIG_MIPS32_TOOLCHAIN ?= MICROCHIPOPENL
endif

ifeq ($(filter y, \
      $(CONFIG_MIPS32_TOOLCHAIN_PINGUINOW) \
    ),y)
  CONFIG_MIPS32_TOOLCHAIN ?= PINGUINOW
endif

#
# Supported toolchains
#
# Each toolchain definition should set:
#
#  CROSSDEV         The GNU toolchain triple (command prefix)
#  ARCHCPUFLAGS     CPU-specific flags selecting the instruction set
#                   FPU options, etc.
#  ARCHOPTIMIZATION The optimization level that results in
#                   reliable code generation.
#

ifeq ($(CONFIG_DEBUG_CUSTOMOPT),y)
  ARCHOPTIMIZATION += $(CONFIG_DEBUG_OPTLEVEL)
else ifeq ($(CONFIG_DEBUG_FULLOPT),y)
  ARCHOPTIMIZATION += -O2
endif

ifneq ($(CONFIG_DEBUG_NOOPT),y)
  ARCHOPTIMIZATION += -fno-strict-aliasing
endif

# Pick correct MIPS architecture selection

ifeq ($(CONFIG_ARCH_MIPS_M5150),y)
  MIPS_MARCH = mips32r2
  MIPS_MPROCESSOR = elf32pic32mz
else ifeq ($(CONFIG_ARCH_MIPS_MICROAPTIV), y)
  MIPS_MARCH = mips32r2
  MIPS_MPROCESSOR = elf32pic32mz
else
  MIPS_MARCH = m4k
  MIPS_MPROCESSOR = elf32pic32mx
endif

# Handle builds for the microMIPS ISA.  Interlinking may be
# necessary to integrate with MIPS32 ISA libraries.

ifeq ($(CONFIG_MIPS_MICROMIPS),y)
# MIPS_MICROMIPS = -mmicromips
# MIPS_MICROMIPS = -mmicromips -minterlink-mips16 -mno-jals
  MIPS_MICROMIPS = -mmicromips -minterlink-compressed
else
  MIPS_MICROMIPS =
endif

# Generic GNU mip32 toolchain on macOS or Linux

ifeq ($(CONFIG_MIPS32_TOOLCHAIN),GNU_ELF)
  CROSSDEV ?= mips-elf-
  ARCHCPUFLAGS = -mlong32 -membedded-data -msoft-float -march=$(MIPS_MARCH) $(MIPS_MICROMIPS) -EL
  ARCHPICFLAGS = -fpic -membedded-pic
  LDSCRIPT = mips-elf-debug.ld
endif

# Pinguino toolchain under Linux

ifeq ($(CONFIG_MIPS32_TOOLCHAIN),PINGUINOL)
  CROSSDEV ?= p32-
  ARCHCPUFLAGS = -mlong32 -membedded-data -msoft-float -march=$(MIPS_MARCH) $(MIPS_MICROMIPS) -EL
  ARCHPICFLAGS = -fpic -membedded-pic
  LDSCRIPT = pinguino-debug.ld
endif

# Sourcery CodeBench Lite toolchain under Linux

ifeq ($(CONFIG_MIPS32_TOOLCHAIN),SOURCERY_CODEBENCH_LITE)
  CROSSDEV ?= mips-sde-elf-
  ARCHCPUFLAGS = -mlong32 -membedded-data -msoft-float -march=$(MIPS_MARCH) $(MIPS_MICROMIPS) -EL
  ARCHPICFLAGS = -fpic -membedded-pic
  LDSCRIPT = sourcery-debug.ld
endif

# Microchip XC32 toolchain under Linux

ifeq ($(CONFIG_MIPS32_TOOLCHAIN),MICROCHIPL_XC32)
  CROSSDEV ?= xc32-
  ARCHCPUFLAGS = -mprocessor=$(MIPS_MPROCESSOR) -march=$(MIPS_MARCH) -EL $(MIPS_MICROMIPS) -msmart-io=0
  LDSCRIPT = xc32-debug.ld
endif

# Microchip C32 toolchain under Linux

ifeq ($(CONFIG_MIPS32_TOOLCHAIN),MICROCHIPL)
  CROSSDEV ?= pic32-
  ARCHCPUFLAGS = -mprocessor=$(MIPS_MPROCESSOR) $(MIPS_MICROMIPS) -mno-float -mlong32 -membedded-data
  ARCHPICFLAGS = -fpic -membedded-pic
  LDSCRIPT = xc32-debug.ld
endif

# Microchip XC32 toolchain under Windows

ifeq ($(CONFIG_MIPS32_TOOLCHAIN),MICROCHIPW_XC32)
  CROSSDEV ?= xc32-
  ARCHCPUFLAGS = -mprocessor=$(MIPS_MPROCESSOR) -march=$(MIPS_MARCH) -EL $(MIPS_MICROMIPS) -msmart-io=0
  LDSCRIPT = xc32-debug.ld
endif

# Microchip C32 toolchain under Windows

ifeq ($(CONFIG_MIPS32_TOOLCHAIN),MICROCHIPW)
  CROSSDEV ?= pic32-
  ARCHCPUFLAGS = -mprocessor=$(MIPS_MPROCESSOR) $(MIPS_MICROMIPS) -mno-float -mlong32 -membedded-data
  ARCHPICFLAGS = -fpic -membedded-pic
  LDSCRIPT = c32-debug.ld
endif

# Microchip C32 toolchain under Linux

ifeq ($(CONFIG_MIPS32_TOOLCHAIN),MICROCHIPL_LITE)
  CROSSDEV ?= pic32-
  ARCHCPUFLAGS = -mprocessor=$(MIPS_MPROCESSOR) $(MIPS_MICROMIPS) -mno-float -mlong32 -membedded-data
  ARCHPICFLAGS = -fpic -membedded-pic
  LDSCRIPT = xc32-debug.ld
endif

# Microchip C32 toolchain under Windows

ifeq ($(CONFIG_MIPS32_TOOLCHAIN),MICROCHIPW_LITE)
  CROSSDEV ?= pic32-
  ARCHCPUFLAGS = -mprocessor=$(MIPS_MPROCESSOR) $(MIPS_MICROMIPS) -mno-float -mlong32 -membedded-data
  ARCHPICFLAGS = -fpic -membedded-pic
  LDSCRIPT = c32-debug.ld
endif

# microchipOpen toolchain under Linux

ifeq ($(CONFIG_MIPS32_TOOLCHAIN),MICROCHIPOPENL)
  CROSSDEV ?= mypic32-
  ARCHCPUFLAGS = -mprocessor=$(MIPS_MPROCESSOR) $(MIPS_MICROMIPS) -mno-float -mlong32 -membedded-data
  ARCHPICFLAGS = -fpic -membedded-pic
  LDSCRIPT = c32-debug.ld
endif

# Pinguino mips-elf toolchain under Windows

ifeq ($(CONFIG_MIPS32_TOOLCHAIN),PINGUINOW)
  CROSSDEV ?= p32-
  ARCHCPUFLAGS = -mlong32 -membedded-data -msoft-float -march=$(MIPS_MARCH) $(MIPS_MICROMIPS) -EL
  ARCHPICFLAGS = -fpic -membedded-pic
  LDSCRIPT = mips-elf-debug.ld
endif

ifeq ($(CONFIG_FRAME_POINTER),y)
  ARCHOPTIMIZATION += -fno-omit-frame-pointer -fno-optimize-sibling-calls
else
  ARCHOPTIMIZATION += -fomit-frame-pointer
endif

# Optimization of unused sections

ifeq ($(CONFIG_DEBUG_OPT_UNUSED_SECTIONS),y)
  LDFLAGS          += --gc-sections
  ARCHOPTIMIZATION += -ffunction-sections -fdata-sections
endif

# Debug link map

ifeq ($(CONFIG_DEBUG_LINK_MAP),y)
  LDFLAGS += --cref -Map=$(call CONVERT_PATH,$(TOPDIR)$(DELIM)nuttx.map)
endif

ifeq ($(CONFIG_DEBUG_SYMBOLS),y)
  ARCHOPTIMIZATION += -g
endif

ARCHCFLAGS += -fno-common
ARCHCXXFLAGS += -fno-common -nostdinc++

ARCHCFLAGS += -Wall -Wstrict-prototypes -Wshadow -Wundef
ARCHCXXFLAGS += -Wall -Wshadow -Wundef

ifneq ($(CONFIG_CXX_EXCEPTION),y)
  ARCHCXXFLAGS += -fno-exceptions -fcheck-new
endif

ifneq ($(CONFIG_CXX_RTTI),y)
  ARCHCXXFLAGS += -fno-rtti
endif

LDFLAGS += -nostdlib

# Default toolchain

CC = $(CROSSDEV)gcc
CXX = $(CROSSDEV)g++
CPP = $(CROSSDEV)gcc -E -P -x c
LD = $(CROSSDEV)ld
STRIP = $(CROSSDEV)strip --strip-unneeded
AR = $(CROSSDEV)ar rcs
NM = $(CROSSDEV)nm
OBJCOPY = $(CROSSDEV)objcopy
OBJDUMP = $(CROSSDEV)objdump

# Add the builtin library

EXTRA_LIBS += $(wildcard $(shell $(CC) $(ARCHCPUFLAGS) --print-libgcc-file-name))

ifneq ($(CONFIG_LIBM),y)
  EXTRA_LIBS += $(wildcard $(shell $(CC) $(ARCHCPUFLAGS) --print-file-name=libm.a))
endif

ifeq ($(CONFIG_LIBSUPCXX),y)
  EXTRA_LIBS += $(wildcard $(shell $(CC) $(ARCHCPUFLAGS) --print-file-name=libsupc++.a))
endif
