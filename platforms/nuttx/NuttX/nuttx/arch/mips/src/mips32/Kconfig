#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

if ARCH_MIPS32
comment "MIPS32 Configuration Options"

choice
	prompt "Toolchain Selection"
	default MIPS32_TOOLCHAIN_MICROCHIPW_LITE if TOOLCHAIN_WINDOWS
	default MIPS32_TOOLCHAIN_GNU_ELF if !TOOLCHAIN_WINDOWS

config MIPS32_TOOLCHAIN_GNU_ELF
	bool "Generic GNU ELF toolchain"
	select ARCH_TOOLCHAIN_GNU
	---help---
		This option should work for any modern GNU toolchain (GCC 4.5 or newer)
		configured for mips32-elf.

config MIPS32_TOOLCHAIN_MICROCHIPL_XC32
	bool "Microchip XC32 toolchain under Linux"
	depends on HOST_LINUX
	select ARCH_TOOLCHAIN_GNU

config MIPS32_TOOLCHAIN_MICROCHIPL
	bool "Microchip C32 toolchain under Linux"
	depends on HOST_LINUX
	select ARCH_TOOLCHAIN_GNU

config MIPS32_TOOLCHAIN_MICROCHIPL_LITE
	bool "Microchip C32 toolchain under Linux (Lite edition)"
	depends on HOST_LINUX
	select ARCH_TOOLCHAIN_GNU

config MIPS32_TOOLCHAIN_MICROCHIPW_XC32
	bool "Microchip XC32 toolchain under Windows"
	depends on TOOLCHAIN_WINDOWS
	select CYGWIN_WINTOOL if WINDOWS_CYGWIN
	select ARCH_TOOLCHAIN_GNU

config MIPS32_TOOLCHAIN_MICROCHIPW
	bool "Microchip C32 toolchain under Windows"
	depends on TOOLCHAIN_WINDOWS
	select CYGWIN_WINTOOL if WINDOWS_CYGWIN
	select ARCH_TOOLCHAIN_GNU

config MIPS32_TOOLCHAIN_MICROCHIPW_LITE
	bool "Microchip C32 toolchain under Windows (Lite edition)"
	depends on TOOLCHAIN_WINDOWS
	select CYGWIN_WINTOOL if WINDOWS_CYGWIN
	select ARCH_TOOLCHAIN_GNU

config MIPS32_TOOLCHAIN_MICROCHIPOPENL
	bool "microchipOpen toolchain under Linux"
	depends on HOST_LINUX
	select ARCH_TOOLCHAIN_GNU

config MIPS32_TOOLCHAIN_PINGUINOW
	bool "Pinguino mips-elf toolchain under Windows"
	depends on TOOLCHAIN_WINDOWS
	select CYGWIN_WINTOOL if WINDOWS_CYGWIN
	select ARCH_TOOLCHAIN_GNU

config MIPS32_TOOLCHAIN_PINGUINOL
	bool "Pinguino mips-elf toolchain under macOS or Linux"
	depends on HOST_LINUX || HOST_MACOS
	select ARCH_TOOLCHAIN_GNU

config MIPS32_TOOLCHAIN_SOURCERY_CODEBENCH_LITE
	bool "Sourcery CodeBench Lite for MIPS ELF toolchain under Linux"
	depends on HOST_LINUX
	select ARCH_TOOLCHAIN_GNU

endchoice

config MIPS32_TOOLCHAIN_MICROCHIP_XC32_LICENSED
	bool "Licensed Microchip XC32 toolchain"
	default n
	depends on MIPS32_TOOLCHAIN_MICROCHIPL_XC32 || MIPS32_TOOLCHAIN_MICROCHIPW_XC32
	---help---
		The free, unlicensed XC32 compiler will not support either
		optimization or the microMIPs ISA.  If you are using a licensed,
		XC32 compiler then select this option so that the build system will
		support higher levels of optimization.

config MIPS32_FRAMEPOINTER
	bool "ABI Uses Frame Pointer"
	default n
	depends on ARCH_HAVE_VFORK
	---help---
		Register r30 may be a frame pointer in some ABIs.  Or may just be
		saved register s8.  It makes a difference for vfork handling.

config MIPS32_HAVE_ICACHE
	bool
	default n

config MIPS32_HAVE_DCACHE
	bool
	default n

config MIPS32_ICACHE
	bool "Use I-Cache"
	default n
	depends on MIPS32_HAVE_ICACHE
	select ARCH_ICACHE
	---help---
		Enable K0 I-Cache

config MIPS32_ICACHE_SIZE
	int "I-Cache Size"
	default 16384
	depends on MIPS32_ICACHE && !MIPS32_CACHE_AUTOINFO
	---help---
		Instruction cache size in bytes.

config MIPS32_ILINE_SIZE
	int "I-Cache Line Size"
	default 16
	depends on MIPS32_ICACHE && !MIPS32_CACHE_AUTOINFO
	---help---
		Instruction cache line size.

config MIPS32_KSEG0_IBASE
	hex "Instruction base address"
	default 0x9d000000
	depends on MIPS32_ICACHE
	---help---
		Instruction base address in KSEG0

config MIPS32_DCACHE
	bool "Use D-Cache"
	default n
	depends on MIPS32_HAVE_DCACHE
	select ARCH_DCACHE
	---help---
		Enable K0 D-Cache

config MIPS32_DCACHE_SIZE
	int "D-Cache Size"
	default 4096
	depends on MIPS32_DCACHE && !MIPS32_CACHE_AUTOINFO
	---help---
		Data cache size in bytes.

config MIPS32_DLINE_SIZE
	int "D-Cache Line Size"
	default 16
	depends on MIPS32_DCACHE && !MIPS32_CACHE_AUTOINFO
	---help---
		Data cache line size.

config MIPS32_KSEG0_DBASE
	hex "Data base address"
	default 0x80000000
	depends on MIPS32_DCACHE
	---help---
		Data base address in KSEG0

config MIPS32_CACHE_AUTOINFO
	bool "Auto detect cache size"
	depends on MIPS32_ICACHE || MIPS32_DCACHE
	default n

endif
