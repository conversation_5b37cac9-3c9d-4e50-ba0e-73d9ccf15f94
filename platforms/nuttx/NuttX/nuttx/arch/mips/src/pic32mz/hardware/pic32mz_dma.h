/****************************************************************************
 * arch/mips/src/pic32mz/hardware/pic32mz_dma.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_MIPS_SRC_PIC32MZ_HARDWARE_PIC32MZ_DMA_H
#define __ARCH_MIPS_SRC_PIC32MZ_HARDWARE_PIC32MZ_DMA_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include <arch/pic32mz/chip.h>

#include "hardware/pic32mz_memorymap.h"

#if CHIP_NDMACH > 0

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* DMA Channel Offsets ******************************************************/

#define PIC32MZ_DMACHn_OFFSET(n)      (0x0060 + 0xc0 *(n))
#  define PIC32MZ_DMACH0_OFFSET       0x0060
#  define PIC32MZ_DMACH1_OFFSET       0x0120
#  define PIC32MZ_DMACH2_OFFSET       0x01e0
#  define PIC32MZ_DMACH3_OFFSET       0x02a0
#  define PIC32MZ_DMACH4_OFFSET       0x0360
#  define PIC32MZ_DMACH5_OFFSET       0x0420
#  define PIC32MZ_DMACH6_OFFSET       0x04f0
#  define PIC32MZ_DMACH7_OFFSET       0x05b0

/* DMA Register Offsets *****************************************************/

/* Global DMA Registers (relative the DMA K1BASE) */

#define PIC32MZ_DMA_CON_OFFSET        0x0000  /* DMA Controller Control Register */
#define PIC32MZ_DMA_CONCLR_OFFSET     0x0004  /* DMA Controller Control Clear Register */
#define PIC32MZ_DMA_CONSET_OFFSET     0x0008  /* DMA Controller Control Set Register */
#define PIC32MZ_DMA_CONINV_OFFSET     0x000c  /* DMA Controller Control Invert Register */

#define PIC32MZ_DMA_STAT_OFFSET       0x0010  /* DMA Status Register */

#define PIC32MZ_DMA_ADDR_OFFSET       0x0020  /* DMA Address Register */

#define PIC32MZ_DMA_CRCCON_OFFSET     0x0030  /* DMA CRC Control Register */
#define PIC32MZ_DMA_CRCCONCLR_OFFSET  0x0034  /* DMA CRC Control Clear Register */
#define PIC32MZ_DMA_CRCCONSET_OFFSET  0x0038  /* DMA CRC Control Set Register */
#define PIC32MZ_DMA_CRCCONINV_OFFSET  0x003c  /* DMA CRC Control Invert Register */

#define PIC32MZ_DMA_CRCDATA_OFFSET    0x0040  /* DMA CRC Data Register */
#define PIC32MZ_DMA_CRCDATACLR_OFFSET 0x0044  /* DMA CRC Data Clear Register */
#define PIC32MZ_DMA_CRCDATASET_OFFSET 0x0048  /* DMA CRC Data Set Register */
#define PIC32MZ_DMA_CRCDATAINV_OFFSET 0x004c  /* DMA CRC Data Invert Register */

#define PIC32MZ_DMA_CRCXOR_OFFSET     0x0050  /* DMA CRCXOR Enable Register */
#define PIC32MZ_DMA_CRCXORCLR_OFFSET  0x0054  /* DMA CRCXOR Enable Clear Register */
#define PIC32MZ_DMA_CRCXORSET_OFFSET  0x0058  /* DMA CRCXOR Enable Set Register */
#define PIC32MZ_DMA_CRCXORINV_OFFSET  0x005c  /* DMA CRCXOR Enable Invert Register */

/* Per-Channel DMA Registers (relative to DMA channel base) */

#define PIC32MZ_DMACH_CON_OFFSET      0x0000  /* DMA Channel Control Register */
#define PIC32MZ_DMACH_CONCLR_OFFSET   0x0004  /* DMA Channel Control Clear Register */
#define PIC32MZ_DMACH_CONSET_OFFSET   0x0008  /* DMA Channel Control Set Register */
#define PIC32MZ_DMACH_CONINV_OFFSET   0x000c  /* DMA Channel Control Invert Register */

#define PIC32MZ_DMACH_ECON_OFFSET     0x0010  /* DMA Channel Event Control Register */
#define PIC32MZ_DMACH_ECONCLR_OFFSET  0x0014  /* DMA Channel Event Control Clear Register */
#define PIC32MZ_DMACH_ECONSET_OFFSET  0x0018  /* DMA Channel Event Control Set Register */
#define PIC32MZ_DMACH_ECONINV_OFFSET  0x001c  /* DMA Channel Event Control Invert Register */

#define PIC32MZ_DMACH_INT_OFFSET      0x0020  /* DMA Channel Interrupt Control Register */
#define PIC32MZ_DMACH_INTCLR_OFFSET   0x0024  /* DMA Channel Interrupt Control Clear Register */
#define PIC32MZ_DMACH_INTSET_OFFSET   0x0028  /* DMA Channel Interrupt Control Set Register */
#define PIC32MZ_DMACH_INTINV_OFFSET   0x002c  /* DMA Channel Interrupt Control Invert Register */

#define PIC32MZ_DMACH_SSA_OFFSET      0x0030  /* DMA Channel Source Start Address Register */
#define PIC32MZ_DMACH_SSACLR_OFFSET   0x0034  /* DMA Channel Source Start Address Clear Register */
#define PIC32MZ_DMACH_SSASET_OFFSET   0x0038  /* DMA Channel Source Start Address Set Register */
#define PIC32MZ_DMACH_SSAINV_OFFSET   0x003c  /* DMA Channel Source Start Address Invert Register */

#define PIC32MZ_DMACH_DSA_OFFSET      0x0040  /* DMA Channel Destination Start Address Register */
#define PIC32MZ_DMACH_DSACLR_OFFSET   0x0044  /* DMA Channel Destination Start Address Clear Register */
#define PIC32MZ_DMACH_DSASET_OFFSET   0x0048  /* DMA Channel Destination Start Address Set Register */
#define PIC32MZ_DMACH_DSAINV_OFFSET   0x004c  /* DMA Channel Destination Start Address Invert Register */

#define PIC32MZ_DMACH_SSIZ_OFFSET     0x0050  /* DMA Channel Source Size Register */
#define PIC32MZ_DMACH_SSIZCLR_OFFSET  0x0054  /* DMA Channel Source Size Clear Register */
#define PIC32MZ_DMACH_SSIZSET_OFFSET  0x0058  /* DMA Channel Source Size Set Register */
#define PIC32MZ_DMACH_SSIZINV_OFFSET  0x005c  /* DMA Channel Source Size Invert Register */

#define PIC32MZ_DMACH_DSIZ_OFFSET     0x0060  /* DMA Channel Destination Size Register */
#define PIC32MZ_DMACH_DSIZCLR_OFFSET  0x0064  /* DMA Channel Destination Size Clear Register */
#define PIC32MZ_DMACH_DSIZSET_OFFSET  0x0068  /* DMA Channel Destination Size Set Register */
#define PIC32MZ_DMACH_DSIZINV_OFFSET  0x006c  /* DMA Channel Destination Size Invert Register */

#define PIC32MZ_DMACH_SPTR_OFFSET     0x0070  /* DMA Channel Source Pointer Register */
#define PIC32MZ_DMACH_DPTR_OFFSET     0x0080  /* DMA Channel Destination Pointer Register */

#define PIC32MZ_DMACH_CSIZ_OFFSET     0x0090  /* DMA Channel Cell-Size Register */
#define PIC32MZ_DMACH_CSIZCLR_OFFSET  0x0094  /* DMA Channel Cell-Size Clear Register */
#define PIC32MZ_DMACH_CSIZSET_OFFSET  0x0098  /* DMA Channel Cell-Size Set Register */
#define PIC32MZ_DMACH_CSIZINV_OFFSET  0x009c  /* DMA Channel Cell-Size Invert Register */

#define PIC32MZ_DMACH_CPTR_OFFSET     0x00a0  /* DMA Channel Cell Pointer Register */

#define PIC32MZ_DMACH_DAT_OFFSET      0x00b0  /* DMA Channel Pattern Data Register */
#define PIC32MZ_DMACH_DATCLR_OFFSET   0x00b4  /* DMA Channel Pattern Data Clear Register */
#define PIC32MZ_DMACH_DATSET_OFFSET   0x00b8  /* DMA Channel Pattern Data Set Register */
#define PIC32MZ_DMACH_DATINV_OFFSET   0x00bc  /* DMA Channel Pattern Data Invert Register */

/* DMA Channel Addresses ****************************************************/

#define PIC32MZ_DMACHn_K1BASE(n)      (PIC32MZ_DMA_K1BASE+PIC32MZ_DMACHn_OFFSET(n))
#  define PIC32MZ_DMACH0_K1BASE       (PIC32MZ_DMA_K1BASE+PIC32MZ_DMACH0_OFFSET)
#  define PIC32MZ_DMACH1_K1BASE       (PIC32MZ_DMA_K1BASE+PIC32MZ_DMACH1_OFFSET)
#  define PIC32MZ_DMACH2_K1BASE       (PIC32MZ_DMA_K1BASE+PIC32MZ_DMACH2_OFFSET)
#  define PIC32MZ_DMACH3_K1BASE       (PIC32MZ_DMA_K1BASE+PIC32MZ_DMACH3_OFFSET)
#  define PIC32MZ_DMACH4_K1BASE       (PIC32MZ_DMA_K1BASE+PIC32MZ_DMACH4_OFFSET)
#  define PIC32MZ_DMACH5_K1BASE       (PIC32MZ_DMA_K1BASE+PIC32MZ_DMACH5_OFFSET)
#  define PIC32MZ_DMACH6_K1BASE       (PIC32MZ_DMA_K1BASE+PIC32MZ_DMACH6_OFFSET)
#  define PIC32MZ_DMACH7_K1BASE       (PIC32MZ_DMA_K1BASE+PIC32MZ_DMACH7_OFFSET)

/* DMA Register Addresses ***************************************************/

/* Global DMA Registers */

#define PIC32MZ_DMA_CON               (PIC32MZ_DMA_K1BASE+PIC32MZ_DMA_CON_OFFSET)
#define PIC32MZ_DMA_CONCLR            (PIC32MZ_DMA_K1BASE+PIC32MZ_DMA_CONCLR_OFFSET)
#define PIC32MZ_DMA_CONSET            (PIC32MZ_DMA_K1BASE+PIC32MZ_DMA_CONSET_OFFSET)
#define PIC32MZ_DMA_CONINV            (PIC32MZ_DMA_K1BASE+PIC32MZ_DMA_CONINV_OFFSET)

#define PIC32MZ_DMA_STAT              (PIC32MZ_DMA_K1BASE+PIC32MZ_DMA_STAT_OFFSET)

#define PIC32MZ_DMA_ADDR              (PIC32MZ_DMA_K1BASE+PIC32MZ_DMA_ADDR_OFFSET)
#define PIC32MZ_DMA_CRCCON            (PIC32MZ_DMA_K1BASE+PIC32MZ_DMA_CRCCON_OFFSET)
#define PIC32MZ_DMA_CRCCONCLR         (PIC32MZ_DMA_K1BASE+PIC32MZ_DMA_CRCCONCLR_OFFSET)
#define PIC32MZ_DMA_CRCCONSET         (PIC32MZ_DMA_K1BASE+PIC32MZ_DMA_CRCCONSET_OFFSET)
#define PIC32MZ_DMA_CRCCONINV         (PIC32MZ_DMA_K1BASE+PIC32MZ_DMA_CRCCONINV_OFFSET)

#define PIC32MZ_DMA_CRCDATA           (PIC32MZ_DMA_K1BASE+PIC32MZ_DMA_CRCDATA_OFFSET)
#define PIC32MZ_DMA_CRCDATACLR        (PIC32MZ_DMA_K1BASE+PIC32MZ_DMA_CRCDATACLR_OFFSET)
#define PIC32MZ_DMA_CRCDATASET        (PIC32MZ_DMA_K1BASE+PIC32MZ_DMA_CRCDATASET_OFFSET)
#define PIC32MZ_DMA_CRCDATAINV        (PIC32MZ_DMA_K1BASE+PIC32MZ_DMA_CRCDATAINV_OFFSET)

#define PIC32MZ_DMA_CRCXOR            (PIC32MZ_DMA_K1BASE+PIC32MZ_DMA_CRCXOR_OFFSET)
#define PIC32MZ_DMA_CRCXORCLR         (PIC32MZ_DMA_K1BASE+PIC32MZ_DMA_CRCXORCLR_OFFSET)
#define PIC32MZ_DMA_CRCXORSET         (PIC32MZ_DMA_K1BASE+PIC32MZ_DMA_CRCXORSET_OFFSET)
#define PIC32MZ_DMA_CRCXORINV         (PIC32MZ_DMA_K1BASE+PIC32MZ_DMA_CRCXORINV_OFFSET)

/* Per-Channel DMA Registers */

#define PIC32MZ_DMACH_CON(n)          (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_CON_OFFSET)
#define PIC32MZ_DMACH_CONCLR(n)       (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_CONCLR_OFFSET)
#define PIC32MZ_DMACH_CONSET(n)       (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_CONSET_OFFSET)
#define PIC32MZ_DMACH_CONINV(n)       (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_CONINV_OFFSET)

#define PIC32MZ_DMACH_ECON(n)         (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_ECON_OFFSET)
#define PIC32MZ_DMACH_ECONCLR(n)      (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_ECONCLR_OFFSET)
#define PIC32MZ_DMACH_ECONSET(n)      (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_ECONSET_OFFSET)
#define PIC32MZ_DMACH_ECONINV(n)      (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_ECONINV_OFFSET)

#define PIC32MZ_DMACH_INT(n)          (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_INT_OFFSET)
#define PIC32MZ_DMACH_INTCLR(n)       (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_INTCLR_OFFSET)
#define PIC32MZ_DMACH_INTSET(n)       (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_INTSET_OFFSET)
#define PIC32MZ_DMACH_INTINV(n)       (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_INTINV_OFFSET)

#define PIC32MZ_DMACH_SSA(n)          (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_SSA_OFFSET)
#define PIC32MZ_DMACH_SSACLR(n)       (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_SSACLR_OFFSET)
#define PIC32MZ_DMACH_SSASET(n)       (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_SSASET_OFFSET)
#define PIC32MZ_DMACH_SSAINV(n)       (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_SSAINV_OFFSET)

#define PIC32MZ_DMACH_DSA(n)          (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_DSA_OFFSET)
#define PIC32MZ_DMACH_DSACLR(n)       (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_DSACLR_OFFSET)
#define PIC32MZ_DMACH_DSASET(n)       (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_DSASET_OFFSET)
#define PIC32MZ_DMACH_DSAINV(n)       (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_DSAINV_OFFSET)

#define PIC32MZ_DMACH_SSIZ(n)         (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_SSIZ_OFFSET)
#define PIC32MZ_DMACH_SSIZCLR(n)      (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_SSIZCLR_OFFSET)
#define PIC32MZ_DMACH_SSIZSET(n)      (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_SSIZSET_OFFSET)
#define PIC32MZ_DMACH_SSIZINV(n)      (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_SSIZINV_OFFSET)

#define PIC32MZ_DMACH_DSIZ(n)         (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_DSIZ_OFFSET)
#define PIC32MZ_DMACH_DSIZCLR(n)      (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_DSIZCLR_OFFSET)
#define PIC32MZ_DMACH_DSIZSET(n)      (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_DSIZSET_OFFSET)
#define PIC32MZ_DMACH_DSIZINV(n)      (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_DSIZINV_OFFSET)

#define PIC32MZ_DMACH_SPTR(n)         (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_SPTR_OFFSET)

#define PIC32MZ_DMACH_DPTR(n)         (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_DPTR_OFFSET)

#define PIC32MZ_DMACH_CSIZ(n)         (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_CSIZ_OFFSET)
#define PIC32MZ_DMACH_CSIZCLR(n)      (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_CSIZCLR_OFFSET)
#define PIC32MZ_DMACH_CSIZSET(n)      (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_CSIZSET_OFFSET)
#define PIC32MZ_DMACH_CSIZINV(n)      (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_CSIZINV_OFFSET)

#define PIC32MZ_DMACH_CPTR(n)         (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_CPTR_OFFSET)

#define PIC32MZ_DMACH_DAT(n)          (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_DAT_OFFSET)
#define PIC32MZ_DMACH_DATCLR(n)       (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_DATCLR_OFFSET)
#define PIC32MZ_DMACH_DATSET(n)       (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_DATSET_OFFSET)
#define PIC32MZ_DMACH_DATINV(n)       (PIC32MZ_DMACHn_K1BASE(n)+PIC32MZ_DMACH_DATINV_OFFSET)

#if CHIP_NDMACH > 0
#  define PIC32MZ_DMACH0_CON          (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_CON_OFFSET)
#  define PIC32MZ_DMACH0_CONCLR       (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_CONCLR_OFFSET)
#  define PIC32MZ_DMACH0_CONSET       (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_CONSET_OFFSET)
#  define PIC32MZ_DMACH0_CONINV       (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_CONINV_OFFSET)

#  define PIC32MZ_DMACH0_ECON         (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_ECON_OFFSET)
#  define PIC32MZ_DMACH0_ECONCLR      (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_ECONCLR_OFFSET)
#  define PIC32MZ_DMACH0_ECONSET      (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_ECONSET_OFFSET)
#  define PIC32MZ_DMACH0_ECONINV      (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_ECONINV_OFFSET)

#  define PIC32MZ_DMACH0_INT          (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_INT_OFFSET)
#  define PIC32MZ_DMACH0_INTCLR       (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_INTCLR_OFFSET)
#  define PIC32MZ_DMACH0_INTSET       (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_INTSET_OFFSET)
#  define PIC32MZ_DMACH0_INTINV       (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_INTINV_OFFSET)

#  define PIC32MZ_DMACH0_SSA          (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_SSA_OFFSET)
#  define PIC32MZ_DMACH0_SSACLR       (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_SSACLR_OFFSET)
#  define PIC32MZ_DMACH0_SSASET       (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_SSASET_OFFSET)
#  define PIC32MZ_DMACH0_SSAINV       (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_SSAINV_OFFSET)

#  define PIC32MZ_DMACH0_DSA          (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_DSA_OFFSET)
#  define PIC32MZ_DMACH0_DSACLR       (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_DSACLR_OFFSET)
#  define PIC32MZ_DMACH0_DSASET       (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_DSASET_OFFSET)
#  define PIC32MZ_DMACH0_DSAINV       (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_DSAINV_OFFSET)

#  define PIC32MZ_DMACH0_SSIZ         (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_SSIZ_OFFSET)
#  define PIC32MZ_DMACH0_SSIZCLR      (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_SSIZCLR_OFFSET)
#  define PIC32MZ_DMACH0_SSIZSET      (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_SSIZSET_OFFSET)
#  define PIC32MZ_DMACH0_SSIZINV      (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_SSIZINV_OFFSET)

#  define PIC32MZ_DMACH0_DSIZ         (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_DSIZ_OFFSET)
#  define PIC32MZ_DMACH0_DSIZCLR      (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_DSIZCLR_OFFSET)
#  define PIC32MZ_DMACH0_DSIZSET      (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_DSIZSET_OFFSET)
#  define PIC32MZ_DMACH0_DSIZINV      (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_DSIZINV_OFFSET)

#  define PIC32MZ_DMACH0_SPTR         (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_SPTR_OFFSET)
#  define PIC32MZ_DMACH0_DPTR         (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_DPTR_OFFSET)

#  define PIC32MZ_DMACH0_CSIZ         (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_CSIZ_OFFSET)
#  define PIC32MZ_DMACH0_CSIZCLR      (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_CSIZCLR_OFFSET)
#  define PIC32MZ_DMACH0_CSIZSET      (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_CSIZSET_OFFSET)
#  define PIC32MZ_DMACH0_CSIZINV      (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_CSIZINV_OFFSET)

#  define PIC32MZ_DMACH0_CPTR         (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_CPTR_OFFSET)

#  define PIC32MZ_DMACH0_DAT          (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_DAT_OFFSET)
#  define PIC32MZ_DMACH0_DATCLR       (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_DATCLR_OFFSET)
#  define PIC32MZ_DMACH0_DATSET       (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_DATSET_OFFSET)
#  define PIC32MZ_DMACH0_DATINV       (PIC32MZ_DMACH0_K1BASE+PIC32MZ_DMACH_DATINV_OFFSET)
#endif

#if CHIP_NDMACH > 1
#  define PIC32MZ_DMACH1_CON          (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_CON_OFFSET)
#  define PIC32MZ_DMACH1_CONCLR       (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_CONCLR_OFFSET)
#  define PIC32MZ_DMACH1_CONSET       (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_CONSET_OFFSET)
#  define PIC32MZ_DMACH1_CONINV       (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_CONINV_OFFSET)

#  define PIC32MZ_DMACH1_ECON         (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_ECON_OFFSET)
#  define PIC32MZ_DMACH1_ECONCLR      (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_ECONCLR_OFFSET)
#  define PIC32MZ_DMACH1_ECONSET      (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_ECONSET_OFFSET)
#  define PIC32MZ_DMACH1_ECONINV      (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_ECONINV_OFFSET)

#  define PIC32MZ_DMACH1_INT          (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_INT_OFFSET)
#  define PIC32MZ_DMACH1_INTCLR       (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_INTCLR_OFFSET)
#  define PIC32MZ_DMACH1_INTSET       (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_INTSET_OFFSET)
#  define PIC32MZ_DMACH1_INTINV       (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_INTINV_OFFSET)

#  define PIC32MZ_DMACH1_SSA          (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_SSA_OFFSET)
#  define PIC32MZ_DMACH1_SSACLR       (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_SSACLR_OFFSET)
#  define PIC32MZ_DMACH1_SSASET       (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_SSASET_OFFSET)
#  define PIC32MZ_DMACH1_SSAINV       (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_SSAINV_OFFSET)

#  define PIC32MZ_DMACH1_DSA          (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_DSA_OFFSET)
#  define PIC32MZ_DMACH1_DSACLR       (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_DSACLR_OFFSET)
#  define PIC32MZ_DMACH1_DSASET       (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_DSASET_OFFSET)
#  define PIC32MZ_DMACH1_DSAINV       (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_DSAINV_OFFSET)

#  define PIC32MZ_DMACH1_SSIZ         (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_SSIZ_OFFSET)
#  define PIC32MZ_DMACH1_SSIZCLR      (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_SSIZCLR_OFFSET)
#  define PIC32MZ_DMACH1_SSIZSET      (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_SSIZSET_OFFSET)
#  define PIC32MZ_DMACH1_SSIZINV      (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_SSIZINV_OFFSET)

#  define PIC32MZ_DMACH1_DSIZ         (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_DSIZ_OFFSET)
#  define PIC32MZ_DMACH1_DSIZCLR      (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_DSIZCLR_OFFSET)
#  define PIC32MZ_DMACH1_DSIZSET      (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_DSIZSET_OFFSET)
#  define PIC32MZ_DMACH1_DSIZINV      (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_DSIZINV_OFFSET)

#  define PIC32MZ_DMACH1_SPTR         (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_SPTR_OFFSET)
#  define PIC32MZ_DMACH1_DPTR         (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_DPTR_OFFSET)

#  define PIC32MZ_DMACH1_CSIZ         (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_CSIZ_OFFSET)
#  define PIC32MZ_DMACH1_CSIZCLR      (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_CSIZCLR_OFFSET)
#  define PIC32MZ_DMACH1_CSIZSET      (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_CSIZSET_OFFSET)
#  define PIC32MZ_DMACH1_CSIZINV      (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_CSIZINV_OFFSET)

#  define PIC32MZ_DMACH1_CPTR         (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_CPTR_OFFSET)

#  define PIC32MZ_DMACH1_DAT          (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_DAT_OFFSET)
#  define PIC32MZ_DMACH1_DATCLR       (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_DATCLR_OFFSET)
#  define PIC32MZ_DMACH1_DATSET       (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_DATSET_OFFSET)
#  define PIC32MZ_DMACH1_DATINV       (PIC32MZ_DMACH1_K1BASE+PIC32MZ_DMACH_DATINV_OFFSET)
#endif

#if CHIP_NDMACH > 2
#  define PIC32MZ_DMACH2_CON          (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_CON_OFFSET)
#  define PIC32MZ_DMACH2_CONCLR       (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_CONCLR_OFFSET)
#  define PIC32MZ_DMACH2_CONSET       (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_CONSET_OFFSET)
#  define PIC32MZ_DMACH2_CONINV       (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_CONINV_OFFSET)

#  define PIC32MZ_DMACH2_ECON         (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_ECON_OFFSET)
#  define PIC32MZ_DMACH2_ECONCLR      (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_ECONCLR_OFFSET)
#  define PIC32MZ_DMACH2_ECONSET      (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_ECONSET_OFFSET)
#  define PIC32MZ_DMACH2_ECONINV      (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_ECONINV_OFFSET)

#  define PIC32MZ_DMACH2_INT          (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_INT_OFFSET)
#  define PIC32MZ_DMACH2_INTCLR       (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_INTCLR_OFFSET)
#  define PIC32MZ_DMACH2_INTSET       (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_INTSET_OFFSET)
#  define PIC32MZ_DMACH2_INTINV       (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_INTINV_OFFSET)

#  define PIC32MZ_DMACH2_SSA          (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_SSA_OFFSET)
#  define PIC32MZ_DMACH2_SSACLR       (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_SSACLR_OFFSET)
#  define PIC32MZ_DMACH2_SSASET       (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_SSASET_OFFSET)
#  define PIC32MZ_DMACH2_SSAINV       (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_SSAINV_OFFSET)

#  define PIC32MZ_DMACH2_DSA          (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_DSA_OFFSET)
#  define PIC32MZ_DMACH2_DSACLR       (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_DSACLR_OFFSET)
#  define PIC32MZ_DMACH2_DSASET       (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_DSASET_OFFSET)
#  define PIC32MZ_DMACH2_DSAINV       (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_DSAINV_OFFSET)

#  define PIC32MZ_DMACH2_SSIZ         (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_SSIZ_OFFSET)
#  define PIC32MZ_DMACH2_SSIZCLR      (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_SSIZCLR_OFFSET)
#  define PIC32MZ_DMACH2_SSIZSET      (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_SSIZSET_OFFSET)
#  define PIC32MZ_DMACH2_SSIZINV      (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_SSIZINV_OFFSET)

#  define PIC32MZ_DMACH2_DSIZ         (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_DSIZ_OFFSET)
#  define PIC32MZ_DMACH2_DSIZCLR      (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_DSIZCLR_OFFSET)
#  define PIC32MZ_DMACH2_DSIZSET      (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_DSIZSET_OFFSET)
#  define PIC32MZ_DMACH2_DSIZINV      (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_DSIZINV_OFFSET)

#  define PIC32MZ_DMACH2_SPTR         (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_SPTR_OFFSET)
#  define PIC32MZ_DMACH2_DPTR         (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_DPTR_OFFSET)

#  define PIC32MZ_DMACH2_CSIZ         (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_CSIZ_OFFSET)
#  define PIC32MZ_DMACH2_CSIZCLR      (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_CSIZCLR_OFFSET)
#  define PIC32MZ_DMACH2_CSIZSET      (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_CSIZSET_OFFSET)
#  define PIC32MZ_DMACH2_CSIZINV      (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_CSIZINV_OFFSET)

#  define PIC32MZ_DMACH2_CPTR         (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_CPTR_OFFSET)

#  define PIC32MZ_DMACH2_DAT          (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_DAT_OFFSET)
#  define PIC32MZ_DMACH2_DATCLR       (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_DATCLR_OFFSET)
#  define PIC32MZ_DMACH2_DATSET       (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_DATSET_OFFSET)
#  define PIC32MZ_DMACH2_DATINV       (PIC32MZ_DMACH2_K1BASE+PIC32MZ_DMACH_DATINV_OFFSET)
#endif

#if CHIP_NDMACH > 3
#  define PIC32MZ_DMACH3_CON          (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_CON_OFFSET)
#  define PIC32MZ_DMACH3_CONCLR       (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_CONCLR_OFFSET)
#  define PIC32MZ_DMACH3_CONSET       (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_CONSET_OFFSET)
#  define PIC32MZ_DMACH3_CONINV       (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_CONINV_OFFSET)

#  define PIC32MZ_DMACH3_ECON         (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_ECON_OFFSET)
#  define PIC32MZ_DMACH3_ECONCLR      (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_ECONCLR_OFFSET)
#  define PIC32MZ_DMACH3_ECONSET      (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_ECONSET_OFFSET)
#  define PIC32MZ_DMACH3_ECONINV      (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_ECONINV_OFFSET)

#  define PIC32MZ_DMACH3_INT          (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_INT_OFFSET)
#  define PIC32MZ_DMACH3_INTCLR       (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_INTCLR_OFFSET)
#  define PIC32MZ_DMACH3_INTSET       (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_INTSET_OFFSET)
#  define PIC32MZ_DMACH3_INTINV       (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_INTINV_OFFSET)

#  define PIC32MZ_DMACH3_SSA          (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_SSA_OFFSET)
#  define PIC32MZ_DMACH3_SSACLR       (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_SSACLR_OFFSET)
#  define PIC32MZ_DMACH3_SSASET       (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_SSASET_OFFSET)
#  define PIC32MZ_DMACH3_SSAINV       (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_SSAINV_OFFSET)

#  define PIC32MZ_DMACH3_DSA          (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_DSA_OFFSET)
#  define PIC32MZ_DMACH3_DSACLR       (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_DSACLR_OFFSET)
#  define PIC32MZ_DMACH3_DSASET       (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_DSASET_OFFSET)
#  define PIC32MZ_DMACH3_DSAINV       (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_DSAINV_OFFSET)

#  define PIC32MZ_DMACH3_SSIZ         (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_SSIZ_OFFSET)
#  define PIC32MZ_DMACH3_SSIZCLR      (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_SSIZCLR_OFFSET)
#  define PIC32MZ_DMACH3_SSIZSET      (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_SSIZSET_OFFSET)
#  define PIC32MZ_DMACH3_SSIZINV      (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_SSIZINV_OFFSET)

#  define PIC32MZ_DMACH3_DSIZ         (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_DSIZ_OFFSET)
#  define PIC32MZ_DMACH3_DSIZCLR      (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_DSIZCLR_OFFSET)
#  define PIC32MZ_DMACH3_DSIZSET      (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_DSIZSET_OFFSET)
#  define PIC32MZ_DMACH3_DSIZINV      (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_DSIZINV_OFFSET)

#  define PIC32MZ_DMACH3_SPTR         (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_SPTR_OFFSET)
#  define PIC32MZ_DMACH3_DPTR         (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_DPTR_OFFSET)

#  define PIC32MZ_DMACH3_CSIZ         (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_CSIZ_OFFSET)
#  define PIC32MZ_DMACH3_CSIZCLR      (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_CSIZCLR_OFFSET)
#  define PIC32MZ_DMACH3_CSIZSET      (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_CSIZSET_OFFSET)
#  define PIC32MZ_DMACH3_CSIZINV      (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_CSIZINV_OFFSET)

#  define PIC32MZ_DMACH3_CPTR         (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_CPTR_OFFSET)

#  define PIC32MZ_DMACH3_DAT          (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_DAT_OFFSET)
#  define PIC32MZ_DMACH3_DATCLR       (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_DATCLR_OFFSET)
#  define PIC32MZ_DMACH3_DATSET       (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_DATSET_OFFSET)
#  define PIC32MZ_DMACH3_DATINV       (PIC32MZ_DMACH3_K1BASE+PIC32MZ_DMACH_DATINV_OFFSET)
#endif

#if CHIP_NDMACH > 4
#  define PIC32MZ_DMACH4_CON          (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_CON_OFFSET)
#  define PIC32MZ_DMACH4_CONCLR       (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_CONCLR_OFFSET)
#  define PIC32MZ_DMACH4_CONSET       (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_CONSET_OFFSET)
#  define PIC32MZ_DMACH4_CONINV       (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_CONINV_OFFSET)

#  define PIC32MZ_DMACH4_ECON         (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_ECON_OFFSET)
#  define PIC32MZ_DMACH4_ECONCLR      (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_ECONCLR_OFFSET)
#  define PIC32MZ_DMACH4_ECONSET      (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_ECONSET_OFFSET)
#  define PIC32MZ_DMACH4_ECONINV      (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_ECONINV_OFFSET)

#  define PIC32MZ_DMACH4_INT          (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_INT_OFFSET)
#  define PIC32MZ_DMACH4_INTCLR       (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_INTCLR_OFFSET)
#  define PIC32MZ_DMACH4_INTSET       (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_INTSET_OFFSET)
#  define PIC32MZ_DMACH4_INTINV       (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_INTINV_OFFSET)

#  define PIC32MZ_DMACH4_SSA          (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_SSA_OFFSET)
#  define PIC32MZ_DMACH4_SSACLR       (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_SSACLR_OFFSET)
#  define PIC32MZ_DMACH4_SSASET       (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_SSASET_OFFSET)
#  define PIC32MZ_DMACH4_SSAINV       (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_SSAINV_OFFSET)

#  define PIC32MZ_DMACH4_DSA          (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_DSA_OFFSET)
#  define PIC32MZ_DMACH4_DSACLR       (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_DSACLR_OFFSET)
#  define PIC32MZ_DMACH4_DSASET       (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_DSASET_OFFSET)
#  define PIC32MZ_DMACH4_DSAINV       (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_DSAINV_OFFSET)

#  define PIC32MZ_DMACH4_SSIZ         (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_SSIZ_OFFSET)
#  define PIC32MZ_DMACH4_SSIZCLR      (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_SSIZCLR_OFFSET)
#  define PIC32MZ_DMACH4_SSIZSET      (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_SSIZSET_OFFSET)
#  define PIC32MZ_DMACH4_SSIZINV      (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_SSIZINV_OFFSET)

#  define PIC32MZ_DMACH4_DSIZ         (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_DSIZ_OFFSET)
#  define PIC32MZ_DMACH4_DSIZCLR      (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_DSIZCLR_OFFSET)
#  define PIC32MZ_DMACH4_DSIZSET      (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_DSIZSET_OFFSET)
#  define PIC32MZ_DMACH4_DSIZINV      (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_DSIZINV_OFFSET)

#  define PIC32MZ_DMACH4_SPTR         (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_SPTR_OFFSET)
#  define PIC32MZ_DMACH4_DPTR         (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_DPTR_OFFSET)

#  define PIC32MZ_DMACH4_CSIZ         (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_CSIZ_OFFSET)
#  define PIC32MZ_DMACH4_CSIZCLR      (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_CSIZCLR_OFFSET)
#  define PIC32MZ_DMACH4_CSIZSET      (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_CSIZSET_OFFSET)
#  define PIC32MZ_DMACH4_CSIZINV      (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_CSIZINV_OFFSET)

#  define PIC32MZ_DMACH4_CPTR         (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_CPTR_OFFSET)

#  define PIC32MZ_DMACH4_DAT          (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_DAT_OFFSET)
#  define PIC32MZ_DMACH4_DATCLR       (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_DATCLR_OFFSET)
#  define PIC32MZ_DMACH4_DATSET       (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_DATSET_OFFSET)
#  define PIC32MZ_DMACH4_DATINV       (PIC32MZ_DMACH4_K1BASE+PIC32MZ_DMACH_DATINV_OFFSET)
#endif

#if CHIP_NDMACH > 5
#  define PIC32MZ_DMACH5_CON          (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_CON_OFFSET)
#  define PIC32MZ_DMACH5_CONCLR       (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_CONCLR_OFFSET)
#  define PIC32MZ_DMACH5_CONSET       (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_CONSET_OFFSET)
#  define PIC32MZ_DMACH5_CONINV       (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_CONINV_OFFSET)

#  define PIC32MZ_DMACH5_ECON         (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_ECON_OFFSET)
#  define PIC32MZ_DMACH5_ECONCLR      (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_ECONCLR_OFFSET)
#  define PIC32MZ_DMACH5_ECONSET      (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_ECONSET_OFFSET)
#  define PIC32MZ_DMACH5_ECONINV      (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_ECONINV_OFFSET)

#  define PIC32MZ_DMACH5_INT          (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_INT_OFFSET)
#  define PIC32MZ_DMACH5_INTCLR       (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_INTCLR_OFFSET)
#  define PIC32MZ_DMACH5_INTSET       (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_INTSET_OFFSET)
#  define PIC32MZ_DMACH5_INTINV       (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_INTINV_OFFSET)

#  define PIC32MZ_DMACH5_SSA          (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_SSA_OFFSET)
#  define PIC32MZ_DMACH5_SSACLR       (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_SSACLR_OFFSET)
#  define PIC32MZ_DMACH5_SSASET       (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_SSASET_OFFSET)
#  define PIC32MZ_DMACH5_SSAINV       (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_SSAINV_OFFSET)

#  define PIC32MZ_DMACH5_DSA          (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_DSA_OFFSET)
#  define PIC32MZ_DMACH5_DSACLR       (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_DSACLR_OFFSET)
#  define PIC32MZ_DMACH5_DSASET       (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_DSASET_OFFSET)
#  define PIC32MZ_DMACH5_DSAINV       (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_DSAINV_OFFSET)

#  define PIC32MZ_DMACH5_SSIZ         (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_SSIZ_OFFSET)
#  define PIC32MZ_DMACH5_SSIZCLR      (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_SSIZCLR_OFFSET)
#  define PIC32MZ_DMACH5_SSIZSET      (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_SSIZSET_OFFSET)
#  define PIC32MZ_DMACH5_SSIZINV      (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_SSIZINV_OFFSET)

#  define PIC32MZ_DMACH5_DSIZ         (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_DSIZ_OFFSET)
#  define PIC32MZ_DMACH5_DSIZCLR      (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_DSIZCLR_OFFSET)
#  define PIC32MZ_DMACH5_DSIZSET      (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_DSIZSET_OFFSET)
#  define PIC32MZ_DMACH5_DSIZINV      (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_DSIZINV_OFFSET)

#  define PIC32MZ_DMACH5_SPTR         (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_SPTR_OFFSET)
#  define PIC32MZ_DMACH5_DPTR         (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_DPTR_OFFSET)

#  define PIC32MZ_DMACH5_CSIZ         (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_CSIZ_OFFSET)
#  define PIC32MZ_DMACH5_CSIZCLR      (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_CSIZCLR_OFFSET)
#  define PIC32MZ_DMACH5_CSIZSET      (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_CSIZSET_OFFSET)
#  define PIC32MZ_DMACH5_CSIZINV      (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_CSIZINV_OFFSET)

#  define PIC32MZ_DMACH5_CPTR         (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_CPTR_OFFSET)

#  define PIC32MZ_DMACH5_DAT          (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_DAT_OFFSET)
#  define PIC32MZ_DMACH5_DATCLR       (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_DATCLR_OFFSET)
#  define PIC32MZ_DMACH5_DATSET       (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_DATSET_OFFSET)
#  define PIC32MZ_DMACH5_DATINV       (PIC32MZ_DMACH5_K1BASE+PIC32MZ_DMACH_DATINV_OFFSET)
#endif

#if CHIP_NDMACH > 6
#  define PIC32MZ_DMACH6_CON          (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_CON_OFFSET)
#  define PIC32MZ_DMACH6_CONCLR       (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_CONCLR_OFFSET)
#  define PIC32MZ_DMACH6_CONSET       (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_CONSET_OFFSET)
#  define PIC32MZ_DMACH6_CONINV       (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_CONINV_OFFSET)

#  define PIC32MZ_DMACH6_ECON         (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_ECON_OFFSET)
#  define PIC32MZ_DMACH6_ECONCLR      (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_ECONCLR_OFFSET)
#  define PIC32MZ_DMACH6_ECONSET      (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_ECONSET_OFFSET)
#  define PIC32MZ_DMACH6_ECONINV      (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_ECONINV_OFFSET)

#  define PIC32MZ_DMACH6_INT          (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_INT_OFFSET)
#  define PIC32MZ_DMACH6_INTCLR       (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_INTCLR_OFFSET)
#  define PIC32MZ_DMACH6_INTSET       (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_INTSET_OFFSET)
#  define PIC32MZ_DMACH6_INTINV       (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_INTINV_OFFSET)

#  define PIC32MZ_DMACH6_SSA          (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_SSA_OFFSET)
#  define PIC32MZ_DMACH6_SSACLR       (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_SSACLR_OFFSET)
#  define PIC32MZ_DMACH6_SSASET       (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_SSASET_OFFSET)
#  define PIC32MZ_DMACH6_SSAINV       (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_SSAINV_OFFSET)

#  define PIC32MZ_DMACH6_DSA          (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_DSA_OFFSET)
#  define PIC32MZ_DMACH6_DSACLR       (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_DSACLR_OFFSET)
#  define PIC32MZ_DMACH6_DSASET       (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_DSASET_OFFSET)
#  define PIC32MZ_DMACH6_DSAINV       (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_DSAINV_OFFSET)

#  define PIC32MZ_DMACH6_SSIZ         (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_SSIZ_OFFSET)
#  define PIC32MZ_DMACH6_SSIZCLR      (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_SSIZCLR_OFFSET)
#  define PIC32MZ_DMACH6_SSIZSET      (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_SSIZSET_OFFSET)
#  define PIC32MZ_DMACH6_SSIZINV      (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_SSIZINV_OFFSET)

#  define PIC32MZ_DMACH6_DSIZ         (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_DSIZ_OFFSET)
#  define PIC32MZ_DMACH6_DSIZCLR      (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_DSIZCLR_OFFSET)
#  define PIC32MZ_DMACH6_DSIZSET      (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_DSIZSET_OFFSET)
#  define PIC32MZ_DMACH6_DSIZINV      (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_DSIZINV_OFFSET)

#  define PIC32MZ_DMACH6_SPTR         (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_SPTR_OFFSET)
#  define PIC32MZ_DMACH6_DPTR         (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_DPTR_OFFSET)

#  define PIC32MZ_DMACH6_CSIZ         (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_CSIZ_OFFSET)
#  define PIC32MZ_DMACH6_CSIZCLR      (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_CSIZCLR_OFFSET)
#  define PIC32MZ_DMACH6_CSIZSET      (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_CSIZSET_OFFSET)
#  define PIC32MZ_DMACH6_CSIZINV      (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_CSIZINV_OFFSET)

#  define PIC32MZ_DMACH6_CPTR         (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_CPTR_OFFSET)

#  define PIC32MZ_DMACH6_DAT          (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_DAT_OFFSET)
#  define PIC32MZ_DMACH6_DATCLR       (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_DATCLR_OFFSET)
#  define PIC32MZ_DMACH6_DATSET       (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_DATSET_OFFSET)
#  define PIC32MZ_DMACH6_DATINV       (PIC32MZ_DMACH6_K1BASE+PIC32MZ_DMACH_DATINV_OFFSET)
#endif

#if CHIP_NDMACH > 7
#  define PIC32MZ_DMACH7_CON          (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_CON_OFFSET)
#  define PIC32MZ_DMACH7_CONCLR       (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_CONCLR_OFFSET)
#  define PIC32MZ_DMACH7_CONSET       (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_CONSET_OFFSET)
#  define PIC32MZ_DMACH7_CONINV       (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_CONINV_OFFSET)

#  define PIC32MZ_DMACH7_ECON         (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_ECON_OFFSET)
#  define PIC32MZ_DMACH7_ECONCLR      (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_ECONCLR_OFFSET)
#  define PIC32MZ_DMACH7_ECONSET      (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_ECONSET_OFFSET)
#  define PIC32MZ_DMACH7_ECONINV      (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_ECONINV_OFFSET)

#  define PIC32MZ_DMACH7_INT          (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_INT_OFFSET)
#  define PIC32MZ_DMACH7_INTCLR       (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_INTCLR_OFFSET)
#  define PIC32MZ_DMACH7_INTSET       (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_INTSET_OFFSET)
#  define PIC32MZ_DMACH7_INTINV       (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_INTINV_OFFSET)

#  define PIC32MZ_DMACH7_SSA          (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_SSA_OFFSET)
#  define PIC32MZ_DMACH7_SSACLR       (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_SSACLR_OFFSET)
#  define PIC32MZ_DMACH7_SSASET       (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_SSASET_OFFSET)
#  define PIC32MZ_DMACH7_SSAINV       (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_SSAINV_OFFSET)

#  define PIC32MZ_DMACH7_DSA          (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_DSA_OFFSET)
#  define PIC32MZ_DMACH7_DSACLR       (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_DSACLR_OFFSET)
#  define PIC32MZ_DMACH7_DSASET       (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_DSASET_OFFSET)
#  define PIC32MZ_DMACH7_DSAINV       (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_DSAINV_OFFSET)

#  define PIC32MZ_DMACH7_SSIZ         (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_SSIZ_OFFSET)
#  define PIC32MZ_DMACH7_SSIZCLR      (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_SSIZCLR_OFFSET)
#  define PIC32MZ_DMACH7_SSIZSET      (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_SSIZSET_OFFSET)
#  define PIC32MZ_DMACH7_SSIZINV      (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_SSIZINV_OFFSET)

#  define PIC32MZ_DMACH7_DSIZ         (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_DSIZ_OFFSET)
#  define PIC32MZ_DMACH7_DSIZCLR      (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_DSIZCLR_OFFSET)
#  define PIC32MZ_DMACH7_DSIZSET      (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_DSIZSET_OFFSET)
#  define PIC32MZ_DMACH7_DSIZINV      (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_DSIZINV_OFFSET)

#  define PIC32MZ_DMACH7_SPTR         (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_SPTR_OFFSET)
#  define PIC32MZ_DMACH7_DPTR         (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_DPTR_OFFSET)

#  define PIC32MZ_DMACH7_CSIZ         (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_CSIZ_OFFSET)
#  define PIC32MZ_DMACH7_CSIZCLR      (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_CSIZCLR_OFFSET)
#  define PIC32MZ_DMACH7_CSIZSET      (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_CSIZSET_OFFSET)
#  define PIC32MZ_DMACH7_CSIZINV      (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_CSIZINV_OFFSET)

#  define PIC32MZ_DMACH7_CPTR         (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_CPTR_OFFSET)

#  define PIC32MZ_DMACH7_DAT          (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_DAT_OFFSET)
#  define PIC32MZ_DMACH7_DATCLR       (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_DATCLR_OFFSET)
#  define PIC32MZ_DMACH7_DATSET       (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_DATSET_OFFSET)
#  define PIC32MZ_DMACH7_DATINV       (PIC32MZ_DMACH7_K1BASE+PIC32MZ_DMACH_DATINV_OFFSET)
#endif

/* Register Bit-Field Definitions *******************************************/

/* Global DMA Registers */

/* DMA Controller Control Register */

#define DMA_CON_DMABUSY               (1 << 11) /* Bit 15: DMA module busy */
#define DMA_CON_SUSPEND               (1 << 12) /* Bit 12: DMA suspend */
#define DMA_CON_ON                    (1 << 15) /* Bit 15: DMA on */

/* DMA Status Register */

#define DMA_STAT_DMACH_SHIFT          (0)       /* Bits 0-1: DMA channel */
#define DMA_STAT_DMACH_MASK           (7 << DMA_STAT_DMACH_SHIFT)
#define DMA_STAT_RDWR                 (1 << 31) /* Bit 31:  Read/write status */

/* DMA Address Register -- This register contains a 32-bit address value */

/* DMA CRC Control Register */

#define DMA_CRCCON_CRCCH_SHIFT        (0)       /* Bits 0-2: CRC channel select */
#define DMA_CRCCON_CRCCH_MASK         (7 << DMA_CRCCON_CRCCH_SHIFT)
#  define DMA_CRCCON_CRCCH(n)         ((uint32_t)(n) << DMA_CRCCON_CRCCH_SHIFT)
#define DMA_CRCCON_CRCTYP             (1 << 5)  /* Bit 5:  CRC type selection */
#define DMA_CRCCON_CRCAPP             (1 << 6)  /* Bit 6:  CRC append mode */
#define DMA_CRCCON_CRCEN              (1 << 7)  /* Bit 7:  CRC enable */
#define DMA_CRCCON_PLEN_SHIFT         (8)       /* Bits 8-12: Polynomial length */
#define DMA_CRCCON_PLEN_MASK          (31 << DMA_CRCCON_PLEN_SHIFT)
#  define DMA_CRCCON_PLEN(n)          ((uint32_t)(n) << DMA_CRCCON_PLEN_SHIFT)
#define DMA_CRCCON_BITO               (1 << 24) /* Bit 24: CRC bit order selection */
#define DMA_CRCCON_WBO                (1 << 27) /* Bit 27: CRC write byte order selection */
#define DMA_CRCCON_BYTO_SHIFT         (28)      /* Bits 28-29: CRC byte order selection */
#define DMA_CRCCON_BYTO_MASK          (3 << DMA_CRCCON_BYTO_SHIFT)
#  define DMA_CRCCON_BYTO_SRCORDER    (0 << DMA_CRCCON_BYTO_SHIFT) /* No swapping (i.e., source byte order) */
#  define DMA_CRCCON_BYTO_SWAP32      (1 << DMA_CRCCON_BYTO_SHIFT) /* Endian byte swap on word boundaries */
#  define DMA_CRCCON_BYTO_SWAP32H     (2 << DMA_CRCCON_BYTO_SHIFT) /* Swap half-words on word boundaries */
#  define DMA_CRCCON_BYTO_SWAP16      (3 << DMA_CRCCON_BYTO_SHIFT) /* Endian byte swap on half-word boundaries */

/* DMA CRC Data Register -- 16 or 32-bits of data */

/* DMA CRCXOR Enable Register -- 16 or 32-bits of data */

/* Per-Channel DMA Registers */

/* DMA Channel Control Register */

#define DMACH_CON_CHPRI_SHIFT         (0)       /* Bits 0-1: Channel priority */
#define DMACH_CON_CHPRI_MASK          (3 << DMACH_CON_CHPRI_SHIFT)
#  define DMACH_CON_CHPRI(n)          ((n) << DMACH_CON_CHPRI_SHIFT)
#define DMACH_CON_CHEDET              (1 << 2)  /* Bit 2:  Channel event detected */
#define DMACH_CON_CHAEN               (1 << 4)  /* Bit 4:  Channel automatic enable */
#define DMACH_CON_CHCHN               (1 << 5)  /* Bit 5:  Channel chain enable */
#define DMACH_CON_CHAED               (1 << 6)  /* Bit 6:  Channel allow events if disabled */
#define DMACH_CON_CHEN                (1 << 7)  /* Bit 7:  Channel enable */
#define DMACH_CON_CHCHNS              (1 << 8)  /* Bit 8:  Chain channel selection */
#define DMACH_CON_CHPATLEN            (1 << 11) /* Bit 11: Pattern Length bit */
#define DMACH_CON_CHPIGNEN            (1 << 13) /* Bit 13: Enable Pattern Ignore Byte bit */
#define DMACH_CON_CHBUSY              (1 << 15) /* Bit 15: Channel busy */
#define DMACH_CON_CHPIGN_SHIFT        (24)      /* Bits 24-31:  Channel Register Data bits */
#define DMACH_CON_CHPIGN_MASK         (0xff << DMACH_CON_CHPIGN_SHIFT)
#  define DMACH_CON_CHPIGN(n)         ((uint32_t)(n) << DMACH_CON_CHPIGN_SHIFT)

/* DMA Channel Event Control Register */

#define DMACH_ECON_AIRQEN             (1 << 3)  /* Bit 3:  Channel abort IRQ enable */
#define DMACH_ECON_SIRQEN             (1 << 4)  /* Bit 4:  Channel start IRQ enable */
#define DMACH_ECON_PATEN              (1 << 5)  /* Bit 5:  Channel pattern match abort enable */
#define DMACH_ECON_CABORT             (1 << 6)  /* Bit 6:  DMA abort transfer */
#define DMACH_ECON_CFORCE             (1 << 7)  /* Bit 7:  DMA forced transfer */
#define DMACH_ECON_CHSIRQ_SHIFT       (8)       /* Bits 8-15: Channel Transfer Start IRQ */
#define DMACH_ECON_CHSIRQ_MASK        (0xff << DMACH_ECON_CHSIRQ_SHIFT)
#  define DMACH_ECON_CHSIRQ(n)        ((uint32_t)(n) << DMACH_ECON_CHSIRQ_SHIFT)
#define DMACH_ECON_CHAIRQ_SHIFT       (16)      /* Bits 16-23: Channel transfer abort irq */
#define DMACH_ECON_CHAIRQ_MASK        (0xff << DMACH_ECON_CHAIRQ_SHIFT)
#  define DMACH_ECON_CHAIRQ(n)        ((uint32_t)(n) << DMACH_ECON_CHAIRQ_SHIFT)

/* DMA Channel Interrupt Control Register */

#define DMACH_INT_CHERIF              (1 << 0)  /* Bit 0:  Channel address error interrupt flag */
#define DMACH_INT_CHTAIF              (1 << 1)  /* Bit 1:  Channel transfer abort interrupt flag */
#define DMACH_INT_CHCCIF              (1 << 2)  /* Bit 2:  Channel cell transfer complete interrupt flag */
#define DMACH_INT_CHBCIF              (1 << 3)  /* Bit 3:  Channel block transfer complete interrupt flag */
#define DMACH_INT_CHDHIF              (1 << 4)  /* Bit 4:  Channel destination half full interrupt flag */
#define DMACH_INT_CHDDIF              (1 << 5)  /* Bit 5:  Channel destination done interrupt flag */
#define DMACH_INT_CHSHIF              (1 << 6)  /* Bit 6:  Channel source half empty interrupt flag */
#define DMACH_INT_CHSDIF              (1 << 7)  /* Bit 7:  Channel source done interrupt flag */
#define DMACH_INT_CHERIE              (1 << 16) /* Bit 16: Channel address error interrupt enable */
#define DMACH_INT_CHTAIE              (1 << 17) /* Bit 17: Channel transfer abort interrupt enable */
#define DMACH_INT_CHCCIE              (1 << 18) /* Bit 18: Channel cell transfer complete interrupt enable */
#define DMACH_INT_CHBCIE              (1 << 19) /* Bit 19: Channel block transfer complete interrupt enable */
#define DMACH_INT_CHDHIE              (1 << 20) /* Bit 20: Channel destination half full interrupt enable */
#define DMACH_INT_CHDDIE              (1 << 21) /* Bit 21: Channel destination done interrupt enable */
#define DMACH_INT_CHSHIE              (1 << 22) /* Bit 22: Channel source half empty interrupt enable */
#define DMACH_INT_CHSDIE              (1 << 23) /* Bit 23: Channel source done interrupt enable */
#define DMACH_INT_FLAGS_SHIFT         (0)       /* Bits 0-7: Channel Interrupt flags */
#define DMACH_INT_FLAGS_MASK          (0xff << DMACH_INT_FLAGS_SHIFT)
#define DMACH_INT_EN_SHIFT            (16)      /* Bits 16-23: Channel Interrupt Enable events */
#define DMACH_INT_EN_MASK             (0xff << DMACH_INT_EN_SHIFT)

/* DMA Channel Source Start Address Register --
 * This register contains a 32-bit address value
 */

/* DMA Channel Destination Start Address Register --
 * This register contains a 32-bit address value
 */

/* DMA Channel Source Size Register -- 16 bits of byte size data */

#define DMACH_SSIZ_MASK                0x0000ffff

/* DMA Channel Destination Size Register -- 16 bits of byte size data */

#define DMACH_DSIZ_MASK                0x0000ffff

/* DMA Channel Source Pointer Register -- 16 bits of byte index data */

#define DMACH_SPTR_MASK                0x0000ffff

/* DMA Channel Destination Pointer Register -- 16 bits of byte index data */

#define DMACH_DPTR_MASK                0x0000ffff

/* DMA Channel Cell-Size Register -- 16 bits of byte transferred data */

#define DMACH_CSIZ_MASK                0x0000ffff

/* DMA Channel Cell Pointer Register -- 16 bits of byte transferred data */

#define DMACH_CPTR_MASK                0x0000ffff

/* DMA Channel Pattern Data Register -- 16 bits of pattern data */

#define DMACH_DAT_MASK                0x0000ffff

/****************************************************************************
 * Public Types
 ****************************************************************************/

#ifndef __ASSEMBLY__

/****************************************************************************
 * Inline Functions
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/

#ifdef __cplusplus
#define EXTERN extern "C"
extern "C"
{
#else
#define EXTERN extern
#endif

#undef EXTERN
#ifdef __cplusplus
}
#endif

#endif /* __ASSEMBLY__ */
#endif /* CHIP_NDMACH > 0 */
#endif /* __ARCH_MIPS_SRC_PIC32MZ_HARDWARE_PIC32MZ_DMA_H */
