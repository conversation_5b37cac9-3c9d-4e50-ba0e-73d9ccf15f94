/****************************************************************************
 * arch/mips/src/pic32mz/hardware/pic32mz_ioport.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_MIPS_SRC_PIC32MZ_HARDWARE_PIC32MZ_IOPORT_H
#define __ARCH_MIPS_SRC_PIC32MZ_HARDWARE_PIC32MZ_IOPORT_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include <arch/pic32mz/chip.h>
#include "pic32mz_memorymap.h"

#if CHIP_NPORTS > 0

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* IOPort Peripheral Offsets ************************************************/

#define PIC32MZ_IOPORTA                0
#define PIC32MZ_IOPORTB                1
#define PIC32MZ_IOPORTC                2
#define PIC32MZ_IOPORTD                3
#define PIC32MZ_IOPORTE                4
#define PIC32MZ_IOPORTF                5
#define PIC32MZ_IOPORTG                6
#define PIC32MZ_IOPORTH                7
#define PIC32MZ_IOPORTJ                8
#define PIC32MZ_IOPORTK                9

#define PIC32MZ_IOPORTn_OFFSET(n)      ((n)<<8)
#  define PIC32MZ_IOPORTA_OFFSET       0x0000
#  define PIC32MZ_IOPORTB_OFFSET       0x0100
#  define PIC32MZ_IOPORTC_OFFSET       0x0200
#  define PIC32MZ_IOPORTD_OFFSET       0x0300
#  define PIC32MZ_IOPORTE_OFFSET       0x0400
#  define PIC32MZ_IOPORTF_OFFSET       0x0500
#  define PIC32MZ_IOPORTG_OFFSET       0x0600
#  define PIC32MZ_IOPORTH_OFFSET       0x0700
#  define PIC32MZ_IOPORTJ_OFFSET       0x0800
#  define PIC32MZ_IOPORTK_OFFSET       0x0900

/* Register Offsets *********************************************************/

#define PIC32MZ_IOPORT_ANSEL_OFFSET     0x0000 /* Analog select register */
#define PIC32MZ_IOPORT_ANSELCLR_OFFSET  0x0004 /* Analog select clear register */
#define PIC32MZ_IOPORT_ANSELSET_OFFSET  0x0008 /* Analog select set register */
#define PIC32MZ_IOPORT_ANSELINV_OFFSET  0x000c /* Analog select invert register */

#define PIC32MZ_IOPORT_TRIS_OFFSET      0x0010 /* Tri-state register */
#define PIC32MZ_IOPORT_TRISCLR_OFFSET   0x0014 /* Tri-state clear register */
#define PIC32MZ_IOPORT_TRISSET_OFFSET   0x0018 /* Tri-state set register */
#define PIC32MZ_IOPORT_TRISINV_OFFSET   0x001c /* Tri-state invert register */

#define PIC32MZ_IOPORT_PORT_OFFSET      0x0020 /* Port register */
#define PIC32MZ_IOPORT_PORTCLR_OFFSET   0x0024 /* Port clear register */
#define PIC32MZ_IOPORT_PORTSET_OFFSET   0x0028 /* Port set register */
#define PIC32MZ_IOPORT_PORTINV_OFFSET   0x002c /* Port invert register */

#define PIC32MZ_IOPORT_LAT_OFFSET       0x0030 /* Port data latch register */
#define PIC32MZ_IOPORT_LATCLR_OFFSET    0x0034 /* Port data latch clear register */
#define PIC32MZ_IOPORT_LATSET_OFFSET    0x0038 /* Port data latch set register */
#define PIC32MZ_IOPORT_LATINV_OFFSET    0x003c /* Port data latch invert register */

#define PIC32MZ_IOPORT_ODC_OFFSET       0x0040 /* Open drain control register */
#define PIC32MZ_IOPORT_ODCCLR_OFFSET    0x0044 /* Open drain control clear register */
#define PIC32MZ_IOPORT_ODCSET_OFFSET    0x0048 /* Open drain control set register */
#define PIC32MZ_IOPORT_ODCINV_OFFSET    0x004c /* Open drain control invert register */

#define PIC32MZ_IOPORT_CNPU_OFFSET      0x0050 /* Change Notice Pull-up register */
#define PIC32MZ_IOPORT_CNPUCLR_OFFSET   0x0054 /* Change Notice Pull-up clear register */
#define PIC32MZ_IOPORT_CNPUSET_OFFSET   0x0058 /* Change Notice Pull-up set register */
#define PIC32MZ_IOPORT_CNPUINV_OFFSET   0x005c /* Change Notice Pull-up invert register */

#define PIC32MZ_IOPORT_CNPD_OFFSET      0x0060 /* Change Notice Pull-down register */
#define PIC32MZ_IOPORT_CNPDCLR_OFFSET   0x0064 /* Change Notice Pull-down clear register */
#define PIC32MZ_IOPORT_CNPDSET_OFFSET   0x0068 /* Change Notice Pull-down set register */
#define PIC32MZ_IOPORT_CNPDINV_OFFSET   0x006c /* Change Notice Pull-down invert register */

#define PIC32MZ_IOPORT_CNCON_OFFSET     0x0070 /* Change Notice Control register */
#define PIC32MZ_IOPORT_CNCONCLR_OFFSET  0x0074 /* Change Notice Control clear register */
#define PIC32MZ_IOPORT_CNCONSET_OFFSET  0x0078 /* Change Notice Control set register */
#define PIC32MZ_IOPORT_CNCONINV_OFFSET  0x007c /* Change Notice Control invert register */

#define PIC32MZ_IOPORT_CNEN_OFFSET      0x0080 /* Change Notice Interrupt Enable register */
#define PIC32MZ_IOPORT_CNENCLR_OFFSET   0x0084 /* Change Notice Interrupt Enable clear register */
#define PIC32MZ_IOPORT_CNENSET_OFFSET   0x0088 /* Change Notice Interrupt Enable set register */
#define PIC32MZ_IOPORT_CNENINV_OFFSET   0x008c /* Change Notice Interrupt Enable invert register */

#define PIC32MZ_IOPORT_CNSTAT_OFFSET    0x0090 /* Change Notice Status register */
#define PIC32MZ_IOPORT_CNSTATCLR_OFFSET 0x0094 /* Change Notice Status clear register*/
#define PIC32MZ_IOPORT_CNSTATSET_OFFSET 0x0098 /* Change Notice Status set register */
#define PIC32MZ_IOPORT_CNSTATINV_OFFSET 0x009c /* Change Notice Status invert register */

#define PIC32MZ_IOPORT_CNNE_OFFSET      0x00a0 /* Change Notice Interrupt Enable register */
#define PIC32MZ_IOPORT_CNNECLR_OFFSET   0x00a4 /* Change Notice Interrupt Enable clear register */
#define PIC32MZ_IOPORT_CNNESET_OFFSET   0x00a8 /* Change Notice Interrupt Enable set register */
#define PIC32MZ_IOPORT_CNNEINV_OFFSET   0x00ac /* Change Notice Interrupt Enable invert register */

#define PIC32MZ_IOPORT_CNF_OFFSET       0x00b0 /* Change Notice Status register */
#define PIC32MZ_IOPORT_CNFCLR_OFFSET    0x00b4 /* Change Notice Status clear register */
#define PIC32MZ_IOPORT_CNFSET_OFFSET    0x00b8 /* Change Notice Status set register */
#define PIC32MZ_IOPORT_CNFINV_OFFSET    0x00bc /* Change Notice Status invert register */

#define PIC32MZ_IOPORT_SRCON0_OFFSET    0x00c0 /* Slew Rate Control0 register */
#define PIC32MZ_IOPORT_SRCON0CLR_OFFSET 0x00c4 /* Slew Rate Control0 clear register */
#define PIC32MZ_IOPORT_SRCON0SET_OFFSET 0x00c8 /* Slew Rate Control0 set register */
#define PIC32MZ_IOPORT_SRCON0INV_OFFSET 0x00cc /* Slew Rate Control0 invert register */

#define PIC32MZ_IOPORT_SRCON1_OFFSET    0x00d0 /* Slew Rate Control1 register */
#define PIC32MZ_IOPORT_SRCON1CLR_OFFSET 0x00d4 /* Slew Rate Control1 clear register */
#define PIC32MZ_IOPORT_SRCON1SET_OFFSET 0x00d8 /* Slew Rate Control1 set register */
#define PIC32MZ_IOPORT_SRCON1INV_OFFSET 0x00dc /* Slew Rate Control1 invert register */

/* IOPort Peripheral Addresses **********************************************/

#define PIC32MZ_IOPORTn_K1BASE(n)      (PIC32MZ_IOPORT_K1BASE+PIC32MZ_IOPORTn_OFFSET(n))
#  define PIC32MZ_IOPORTA_K1BASE       (PIC32MZ_IOPORT_K1BASE+PIC32MZ_IOPORTA_OFFSET)
#  define PIC32MZ_IOPORTB_K1BASE       (PIC32MZ_IOPORT_K1BASE+PIC32MZ_IOPORTB_OFFSET)
#  define PIC32MZ_IOPORTC_K1BASE       (PIC32MZ_IOPORT_K1BASE+PIC32MZ_IOPORTC_OFFSET)
#  define PIC32MZ_IOPORTD_K1BASE       (PIC32MZ_IOPORT_K1BASE+PIC32MZ_IOPORTD_OFFSET)
#  define PIC32MZ_IOPORTE_K1BASE       (PIC32MZ_IOPORT_K1BASE+PIC32MZ_IOPORTE_OFFSET)
#  define PIC32MZ_IOPORTF_K1BASE       (PIC32MZ_IOPORT_K1BASE+PIC32MZ_IOPORTF_OFFSET)
#  define PIC32MZ_IOPORTG_K1BASE       (PIC32MZ_IOPORT_K1BASE+PIC32MZ_IOPORTG_OFFSET)
#  define PIC32MZ_IOPORTH_K1BASE       (PIC32MZ_IOPORT_K1BASE+PIC32MZ_IOPORTH_OFFSET)
#  define PIC32MZ_IOPORTJ_K1BASE       (PIC32MZ_IOPORT_K1BASE+PIC32MZ_IOPORTJ_OFFSET)
#  define PIC32MZ_IOPORTK_K1BASE       (PIC32MZ_IOPORT_K1BASE+PIC32MZ_IOPORTK_OFFSET)

/* Register Addresses *******************************************************/

#define PIC32MZ_IOPORT_ANSEL(n)         (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_ANSEL_OFFSET)
#define PIC32MZ_IOPORT_ANSELCLR(n)      (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_ANSELCLR_OFFSET)
#define PIC32MZ_IOPORT_ANSELSET(n)      (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_ANSELSET_OFFSET)
#define PIC32MZ_IOPORT_ANSELINV(n)      (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_ANSELINV_OFFSET)

#define PIC32MZ_IOPORT_TRIS(n)          (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_TRIS_OFFSET)
#define PIC32MZ_IOPORT_TRISCLR(n)       (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_TRISCLR_OFFSET)
#define PIC32MZ_IOPORT_TRISSET(n)       (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_TRISSET_OFFSET)
#define PIC32MZ_IOPORT_TRISINV(n)       (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_TRISINV_OFFSET)

#define PIC32MZ_IOPORT_PORT(n)          (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_PORT_OFFSET)
#define PIC32MZ_IOPORT_PORTCLR(n)       (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_PORTCLR_OFFSET)
#define PIC32MZ_IOPORT_PORTSET(n)       (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_PORTSET_OFFSET)
#define PIC32MZ_IOPORT_PORTINV(n)       (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_PORTINV_OFFSET)

#define PIC32MZ_IOPORT_LAT(n)           (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_LAT_OFFSET)
#define PIC32MZ_IOPORT_LATCLR(n)        (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_LATCLR_OFFSET)
#define PIC32MZ_IOPORT_LATSET(n)        (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_LATSET_OFFSET)
#define PIC32MZ_IOPORT_LATINV(n)        (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_LATINV_OFFSET)

#define PIC32MZ_IOPORT_ODC(n)           (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_ODC_OFFSET)
#define PIC32MZ_IOPORT_ODCCLR(n)        (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_ODCCLR_OFFSET)
#define PIC32MZ_IOPORT_ODCSET(n)        (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_ODCSET_OFFSET)
#define PIC32MZ_IOPORT_ODCINV(n)        (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_ODCINV_OFFSET)

#define PIC32MZ_IOPORT_CNPU(n)          (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNPU_OFFSET)
#define PIC32MZ_IOPORT_CNPUCLR(n)       (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNPUCLR_OFFSET)
#define PIC32MZ_IOPORT_CNPUSET(n)       (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNPUSET_OFFSET)
#define PIC32MZ_IOPORT_CNPUINV(n)       (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNPUINV_OFFSET)

#define PIC32MZ_IOPORT_CNPD(n)          (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNPD_OFFSET)
#define PIC32MZ_IOPORT_CNPDCLR(n)       (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNPDCLR_OFFSET)
#define PIC32MZ_IOPORT_CNPDSET(n)       (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNPDSET_OFFSET)
#define PIC32MZ_IOPORT_CNPDINV(n)       (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNPDINV_OFFSET)

#define PIC32MZ_IOPORT_CNCON(n)         (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNCON_OFFSET)
#define PIC32MZ_IOPORT_CNCONCLR(n)      (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNCONCLR_OFFSET)
#define PIC32MZ_IOPORT_CNCONSET(n)      (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNCONSET_OFFSET)
#define PIC32MZ_IOPORT_CNCONINV(n)      (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNCONINV_OFFSET)

#define PIC32MZ_IOPORT_CNEN(n)          (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNEN_OFFSET)
#define PIC32MZ_IOPORT_CNENCLR(n)       (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNENCLR_OFFSET)
#define PIC32MZ_IOPORT_CNENSET(n)       (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNENSET_OFFSET)
#define PIC32MZ_IOPORT_CNENINV(n)       (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNENINV_OFFSET)

#define PIC32MZ_IOPORT_CNSTAT(n)        (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNSTAT_OFFSET)
#define PIC32MZ_IOPORT_CNSTATCLR(n)     (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNSTATCLR_OFFSET)
#define PIC32MZ_IOPORT_CNSTATSET(n)     (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNSTATSET_OFFSET)
#define PIC32MZ_IOPORT_CNSTATINV(n)     (PIC32MZ_IOPORTn_K1BASE(n)+PIC32MZ_IOPORT_CNSTATINV_OFFSET)

/* Port A addresses */

#define PIC32MZ_IOPORTA_ANSEL           (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_ANSEL_OFFSET)
#define PIC32MZ_IOPORTA_ANSELCLR        (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_ANSELCLR_OFFSET)
#define PIC32MZ_IOPORTA_ANSELSET        (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_ANSELSET_OFFSET)
#define PIC32MZ_IOPORTA_ANSELINV        (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_ANSELINV_OFFSET)

#define PIC32MZ_IOPORTA_TRIS            (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_TRIS_OFFSET)
#define PIC32MZ_IOPORTA_TRISCLR         (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_TRISCLR_OFFSET)
#define PIC32MZ_IOPORTA_TRISSET         (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_TRISSET_OFFSET)
#define PIC32MZ_IOPORTA_TRISINV         (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_TRISINV_OFFSET)

#define PIC32MZ_IOPORTA_PORT            (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_PORT_OFFSET)
#define PIC32MZ_IOPORTA_PORTCLR         (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_PORTCLR_OFFSET)
#define PIC32MZ_IOPORTA_PORTSET         (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_PORTSET_OFFSET)
#define PIC32MZ_IOPORTA_PORTINV         (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_PORTINV_OFFSET)

#define PIC32MZ_IOPORTA_LAT             (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_LAT_OFFSET)
#define PIC32MZ_IOPORTA_LATCLR          (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_LATCLR_OFFSET)
#define PIC32MZ_IOPORTA_LATSET          (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_LATSET_OFFSET)
#define PIC32MZ_IOPORTA_LATINV          (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_LATINV_OFFSET)

#define PIC32MZ_IOPORTA_ODC             (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_ODC_OFFSET)
#define PIC32MZ_IOPORTA_ODCCLR          (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_ODCCLR_OFFSET)
#define PIC32MZ_IOPORTA_ODCSET          (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_ODCSET_OFFSET)
#define PIC32MZ_IOPORTA_ODCINV          (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_ODCINV_OFFSET)

#define PIC32MZ_IOPORTA_CNPU            (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNPU_OFFSET)
#define PIC32MZ_IOPORTA_CNPUCLR         (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNPUCLR_OFFSET)
#define PIC32MZ_IOPORTA_CNPUSET         (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNPUSET_OFFSET)
#define PIC32MZ_IOPORTA_CNPUINV         (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNPUINV_OFFSET)

#define PIC32MZ_IOPORTA_CNPD            (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNPD_OFFSET)
#define PIC32MZ_IOPORTA_CNPDCLR         (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNPDCLR_OFFSET)
#define PIC32MZ_IOPORTA_CNPDSET         (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNPDSET_OFFSET)
#define PIC32MZ_IOPORTA_CNPDINV         (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNPDINV_OFFSET)

#define PIC32MZ_IOPORTA_CNCON           (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNCON_OFFSET)
#define PIC32MZ_IOPORTA_CNCONCLR        (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNCONCLR_OFFSET)
#define PIC32MZ_IOPORTA_CNCONSET        (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNCONSET_OFFSET)
#define PIC32MZ_IOPORTA_CNCONINV        (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNCONINV_OFFSET)

#define PIC32MZ_IOPORTA_CNEN            (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNEN_OFFSET)
#define PIC32MZ_IOPORTA_CNENCLR         (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNENCLR_OFFSET)
#define PIC32MZ_IOPORTA_CNENSET         (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNENSET_OFFSET)
#define PIC32MZ_IOPORTA_CNENINV         (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNENINV_OFFSET)

#define PIC32MZ_IOPORTA_CNSTAT          (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNSTAT_OFFSET)
#define PIC32MZ_IOPORTA_CNSTATCLR       (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNSTATCLR_OFFSET)
#define PIC32MZ_IOPORTA_CNSTATSET       (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNSTATSET_OFFSET)
#define PIC32MZ_IOPORTA_CNSTATINV       (PIC32MZ_IOPORTA_K1BASE+PIC32MZ_IOPORT_CNSTATINV_OFFSET)

#if CHIP_NPORTS > 0
/* Port B addresses */

#  define PIC32MZ_IOPORTB_ANSEL         (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_ANSEL_OFFSET)
#  define PIC32MZ_IOPORTB_ANSELCLR      (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_ANSELCLR_OFFSET)
#  define PIC32MZ_IOPORTB_ANSELSET      (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_ANSELSET_OFFSET)
#  define PIC32MZ_IOPORTB_ANSELINV      (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_ANSELINV_OFFSET)

#  define PIC32MZ_IOPORTB_TRIS          (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_TRIS_OFFSET)
#  define PIC32MZ_IOPORTB_TRISCLR       (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_TRISCLR_OFFSET)
#  define PIC32MZ_IOPORTB_TRISSET       (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_TRISSET_OFFSET)
#  define PIC32MZ_IOPORTB_TRISINV       (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_TRISINV_OFFSET)

#  define PIC32MZ_IOPORTB_PORT          (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_PORT_OFFSET)
#  define PIC32MZ_IOPORTB_PORTCLR       (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_PORTCLR_OFFSET)
#  define PIC32MZ_IOPORTB_PORTSET       (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_PORTSET_OFFSET)
#  define PIC32MZ_IOPORTB_PORTINV       (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_PORTINV_OFFSET)

#  define PIC32MZ_IOPORTB_LAT           (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_LAT_OFFSET)
#  define PIC32MZ_IOPORTB_LATCLR        (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_LATCLR_OFFSET)
#  define PIC32MZ_IOPORTB_LATSET        (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_LATSET_OFFSET)
#  define PIC32MZ_IOPORTB_LATINV        (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_LATINV_OFFSET)

#  define PIC32MZ_IOPORTB_ODC           (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_ODC_OFFSET)
#  define PIC32MZ_IOPORTB_ODCCLR        (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_ODCCLR_OFFSET)
#  define PIC32MZ_IOPORTB_ODCSET        (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_ODCSET_OFFSET)
#  define PIC32MZ_IOPORTB_ODCINV        (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_ODCINV_OFFSET)

#  define PIC32MZ_IOPORTB_CNPU          (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNPU_OFFSET)
#  define PIC32MZ_IOPORTB_CNPUCLR       (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNPUCLR_OFFSET)
#  define PIC32MZ_IOPORTB_CNPUSET       (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNPUSET_OFFSET)
#  define PIC32MZ_IOPORTB_CNPUINV       (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNPUINV_OFFSET)

#  define PIC32MZ_IOPORTB_CNPD          (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNPD_OFFSET)
#  define PIC32MZ_IOPORTB_CNPDCLR       (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNPDCLR_OFFSET)
#  define PIC32MZ_IOPORTB_CNPDSET       (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNPDSET_OFFSET)
#  define PIC32MZ_IOPORTB_CNPDINV       (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNPDINV_OFFSET)

#  define PIC32MZ_IOPORTB_CNCON         (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNCON_OFFSET)
#  define PIC32MZ_IOPORTB_CNCONCLR      (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNCONCLR_OFFSET)
#  define PIC32MZ_IOPORTB_CNCONSET      (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNCONSET_OFFSET)
#  define PIC32MZ_IOPORTB_CNCONINV      (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNCONINV_OFFSET)

#  define PIC32MZ_IOPORTB_CNEN          (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNEN_OFFSET)
#  define PIC32MZ_IOPORTB_CNENCLR       (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNENCLR_OFFSET)
#  define PIC32MZ_IOPORTB_CNENSET       (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNENSET_OFFSET)
#  define PIC32MZ_IOPORTB_CNENINV       (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNENINV_OFFSET)

#  define PIC32MZ_IOPORTB_CNSTAT        (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNSTAT_OFFSET)
#  define PIC32MZ_IOPORTB_CNSTATCLR     (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNSTATCLR_OFFSET)
#  define PIC32MZ_IOPORTB_CNSTATSET     (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNSTATSET_OFFSET)
#  define PIC32MZ_IOPORTB_CNSTATINV     (PIC32MZ_IOPORTB_K1BASE+PIC32MZ_IOPORT_CNSTATINV_OFFSET)
#endif

#if CHIP_NPORTS > 1
/* Port C addresses */

#  define PIC32MZ_IOPORTC_ANSEL         (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_ANSEL_OFFSET)
#  define PIC32MZ_IOPORTC_ANSELCLR      (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_ANSELCLR_OFFSET)
#  define PIC32MZ_IOPORTC_ANSELSET      (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_ANSELSET_OFFSET)
#  define PIC32MZ_IOPORTC_ANSELINV      (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_ANSELINV_OFFSET)

#  define PIC32MZ_IOPORTC_TRIS          (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_TRIS_OFFSET)
#  define PIC32MZ_IOPORTC_TRISCLR       (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_TRISCLR_OFFSET)
#  define PIC32MZ_IOPORTC_TRISSET       (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_TRISSET_OFFSET)
#  define PIC32MZ_IOPORTC_TRISINV       (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_TRISINV_OFFSET)

#  define PIC32MZ_IOPORTC_PORT          (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_PORT_OFFSET)
#  define PIC32MZ_IOPORTC_PORTCLR       (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_PORTCLR_OFFSET)
#  define PIC32MZ_IOPORTC_PORTSET       (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_PORTSET_OFFSET)
#  define PIC32MZ_IOPORTC_PORTINV       (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_PORTINV_OFFSET)

#  define PIC32MZ_IOPORTC_LAT           (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_LAT_OFFSET)
#  define PIC32MZ_IOPORTC_LATCLR        (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_LATCLR_OFFSET)
#  define PIC32MZ_IOPORTC_LATSET        (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_LATSET_OFFSET)
#  define PIC32MZ_IOPORTC_LATINV        (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_LATINV_OFFSET)

#  define PIC32MZ_IOPORTC_ODC           (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_ODC_OFFSET)
#  define PIC32MZ_IOPORTC_ODCCLR        (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_ODCCLR_OFFSET)
#  define PIC32MZ_IOPORTC_ODCSET        (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_ODCSET_OFFSET)
#  define PIC32MZ_IOPORTC_ODCINV        (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_ODCINV_OFFSET)

#  define PIC32MZ_IOPORTC_CNPU          (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNPU_OFFSET)
#  define PIC32MZ_IOPORTC_CNPUCLR       (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNPUCLR_OFFSET)
#  define PIC32MZ_IOPORTC_CNPUSET       (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNPUSET_OFFSET)
#  define PIC32MZ_IOPORTC_CNPUINV       (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNPUINV_OFFSET)

#  define PIC32MZ_IOPORTC_CNPD          (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNPD_OFFSET)
#  define PIC32MZ_IOPORTC_CNPDCLR       (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNPDCLR_OFFSET)
#  define PIC32MZ_IOPORTC_CNPDSET       (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNPDSET_OFFSET)
#  define PIC32MZ_IOPORTC_CNPDINV       (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNPDINV_OFFSET)

#  define PIC32MZ_IOPORTC_CNCON         (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNCON_OFFSET)
#  define PIC32MZ_IOPORTC_CNCONCLR      (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNCONCLR_OFFSET)
#  define PIC32MZ_IOPORTC_CNCONSET      (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNCONSET_OFFSET)
#  define PIC32MZ_IOPORTC_CNCONINV      (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNCONINV_OFFSET)

#  define PIC32MZ_IOPORTC_CNEN          (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNEN_OFFSET)
#  define PIC32MZ_IOPORTC_CNENCLR       (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNENCLR_OFFSET)
#  define PIC32MZ_IOPORTC_CNENSET       (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNENSET_OFFSET)
#  define PIC32MZ_IOPORTC_CNENINV       (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNENINV_OFFSET)

#  define PIC32MZ_IOPORTC_CNSTAT        (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNSTAT_OFFSET)
#  define PIC32MZ_IOPORTC_CNSTATCLR     (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNSTATCLR_OFFSET)
#  define PIC32MZ_IOPORTC_CNSTATSET     (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNSTATSET_OFFSET)
#  define PIC32MZ_IOPORTC_CNSTATINV     (PIC32MZ_IOPORTC_K1BASE+PIC32MZ_IOPORT_CNSTATINV_OFFSET)
#endif

#if CHIP_NPORTS > 2
/* Port D addresses */

#  define PIC32MZ_IOPORTD_ANSEL         (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_ANSEL_OFFSET)
#  define PIC32MZ_IOPORTD_ANSELCLR      (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_ANSELCLR_OFFSET)
#  define PIC32MZ_IOPORTD_ANSELSET      (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_ANSELSET_OFFSET)
#  define PIC32MZ_IOPORTD_ANSELINV      (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_ANSELINV_OFFSET)

#  define PIC32MZ_IOPORTD_TRIS          (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_TRIS_OFFSET)
#  define PIC32MZ_IOPORTD_TRISCLR       (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_TRISCLR_OFFSET)
#  define PIC32MZ_IOPORTD_TRISSET       (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_TRISSET_OFFSET)
#  define PIC32MZ_IOPORTD_TRISINV       (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_TRISINV_OFFSET)

#  define PIC32MZ_IOPORTD_PORT          (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_PORT_OFFSET)
#  define PIC32MZ_IOPORTD_PORTCLR       (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_PORTCLR_OFFSET)
#  define PIC32MZ_IOPORTD_PORTSET       (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_PORTSET_OFFSET)
#  define PIC32MZ_IOPORTD_PORTINV       (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_PORTINV_OFFSET)

#  define PIC32MZ_IOPORTD_LAT           (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_LAT_OFFSET)
#  define PIC32MZ_IOPORTD_LATCLR        (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_LATCLR_OFFSET)
#  define PIC32MZ_IOPORTD_LATSET        (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_LATSET_OFFSET)
#  define PIC32MZ_IOPORTD_LATINV        (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_LATINV_OFFSET)

#  define PIC32MZ_IOPORTD_ODC           (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_ODC_OFFSET)
#  define PIC32MZ_IOPORTD_ODCCLR        (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_ODCCLR_OFFSET)
#  define PIC32MZ_IOPORTD_ODCSET        (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_ODCSET_OFFSET)
#  define PIC32MZ_IOPORTD_ODCINV        (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_ODCINV_OFFSET)

#  define PIC32MZ_IOPORTD_CNPU          (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNPU_OFFSET)
#  define PIC32MZ_IOPORTD_CNPUCLR       (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNPUCLR_OFFSET)
#  define PIC32MZ_IOPORTD_CNPUSET       (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNPUSET_OFFSET)
#  define PIC32MZ_IOPORTD_CNPUINV       (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNPUINV_OFFSET)

#  define PIC32MZ_IOPORTD_CNPD          (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNPD_OFFSET)
#  define PIC32MZ_IOPORTD_CNPDCLR       (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNPDCLR_OFFSET)
#  define PIC32MZ_IOPORTD_CNPDSET       (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNPDSET_OFFSET)
#  define PIC32MZ_IOPORTD_CNPDINV       (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNPDINV_OFFSET)

#  define PIC32MZ_IOPORTD_CNCON         (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNCON_OFFSET)
#  define PIC32MZ_IOPORTD_CNCONCLR      (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNCONCLR_OFFSET)
#  define PIC32MZ_IOPORTD_CNCONSET      (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNCONSET_OFFSET)
#  define PIC32MZ_IOPORTD_CNCONINV      (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNCONINV_OFFSET)

#  define PIC32MZ_IOPORTD_CNEN          (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNEN_OFFSET)
#  define PIC32MZ_IOPORTD_CNENCLR       (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNENCLR_OFFSET)
#  define PIC32MZ_IOPORTD_CNENSET       (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNENSET_OFFSET)
#  define PIC32MZ_IOPORTD_CNENINV       (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNENINV_OFFSET)

#  define PIC32MZ_IOPORTD_CNSTAT        (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNSTAT_OFFSET)
#  define PIC32MZ_IOPORTD_CNSTATCLR     (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNSTATCLR_OFFSET)
#  define PIC32MZ_IOPORTD_CNSTATSET     (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNSTATSET_OFFSET)
#  define PIC32MZ_IOPORTD_CNSTATINV     (PIC32MZ_IOPORTD_K1BASE+PIC32MZ_IOPORT_CNSTATINV_OFFSET)
#endif

#if CHIP_NPORTS > 3
/* Port E addresses */

#  define PIC32MZ_IOPORTE_ANSEL         (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_ANSEL_OFFSET)
#  define PIC32MZ_IOPORTE_ANSELCLR      (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_ANSELCLR_OFFSET)
#  define PIC32MZ_IOPORTE_ANSELSET      (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_ANSELSET_OFFSET)
#  define PIC32MZ_IOPORTE_ANSELINV      (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_ANSELINV_OFFSET)

#  define PIC32MZ_IOPORTE_TRIS          (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_TRIS_OFFSET)
#  define PIC32MZ_IOPORTE_TRISCLR       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_TRISCLR_OFFSET)
#  define PIC32MZ_IOPORTE_TRISSET       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_TRISSET_OFFSET)
#  define PIC32MZ_IOPORTE_TRISINV       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_TRISINV_OFFSET)

#  define PIC32MZ_IOPORTE_PORT          (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_PORT_OFFSET)
#  define PIC32MZ_IOPORTE_PORTCLR       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_PORTCLR_OFFSET)
#  define PIC32MZ_IOPORTE_PORTSET       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_PORTSET_OFFSET)
#  define PIC32MZ_IOPORTE_PORTINV       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_PORTINV_OFFSET)

#  define PIC32MZ_IOPORTE_LAT           (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_LAT_OFFSET)
#  define PIC32MZ_IOPORTE_LATCLR        (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_LATCLR_OFFSET)
#  define PIC32MZ_IOPORTE_LATSET        (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_LATSET_OFFSET)
#  define PIC32MZ_IOPORTE_LATINV        (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_LATINV_OFFSET)

#  define PIC32MZ_IOPORTE_ODC           (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_ODC_OFFSET)
#  define PIC32MZ_IOPORTE_ODCCLR        (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_ODCCLR_OFFSET)
#  define PIC32MZ_IOPORTE_ODCSET        (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_ODCSET_OFFSET)
#  define PIC32MZ_IOPORTE_ODCINV        (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_ODCINV_OFFSET)

#  define PIC32MZ_IOPORTE_CNPU          (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNPU_OFFSET)
#  define PIC32MZ_IOPORTE_CNPUCLR       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNPUCLR_OFFSET)
#  define PIC32MZ_IOPORTE_CNPUSET       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNPUSET_OFFSET)
#  define PIC32MZ_IOPORTE_CNPUINV       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNPUINV_OFFSET)

#  define PIC32MZ_IOPORTE_CNPD          (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNPD_OFFSET)
#  define PIC32MZ_IOPORTE_CNPDCLR       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNPDCLR_OFFSET)
#  define PIC32MZ_IOPORTE_CNPDSET       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNPDSET_OFFSET)
#  define PIC32MZ_IOPORTE_CNPDINV       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNPDINV_OFFSET)

#  define PIC32MZ_IOPORTE_CNCON         (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNCON_OFFSET)
#  define PIC32MZ_IOPORTE_CNCONCLR      (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNCONCLR_OFFSET)
#  define PIC32MZ_IOPORTE_CNCONSET      (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNCONSET_OFFSET)
#  define PIC32MZ_IOPORTE_CNCONINV      (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNCONINV_OFFSET)

#  define PIC32MZ_IOPORTE_CNEN          (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNEN_OFFSET)
#  define PIC32MZ_IOPORTE_CNENCLR       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNENCLR_OFFSET)
#  define PIC32MZ_IOPORTE_CNENSET       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNENSET_OFFSET)
#  define PIC32MZ_IOPORTE_CNENINV       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNENINV_OFFSET)

#  define PIC32MZ_IOPORTE_CNSTAT        (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNSTAT_OFFSET)
#  define PIC32MZ_IOPORTE_CNSTATCLR     (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNSTATCLR_OFFSET)
#  define PIC32MZ_IOPORTE_CNSTATSET     (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNSTATSET_OFFSET)
#  define PIC32MZ_IOPORTE_CNSTATINV     (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNSTATINV_OFFSET)
#endif

#if CHIP_NPORTS > 4
/* Port F addresses */

#  define PIC32MZ_IOPORTE_ANSEL         (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_ANSEL_OFFSET)
#  define PIC32MZ_IOPORTE_ANSELCLR      (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_ANSELCLR_OFFSET)
#  define PIC32MZ_IOPORTE_ANSELSET      (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_ANSELSET_OFFSET)
#  define PIC32MZ_IOPORTE_ANSELINV      (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_ANSELINV_OFFSET)

#  define PIC32MZ_IOPORTE_TRIS          (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_TRIS_OFFSET)
#  define PIC32MZ_IOPORTE_TRISCLR       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_TRISCLR_OFFSET)
#  define PIC32MZ_IOPORTE_TRISSET       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_TRISSET_OFFSET)
#  define PIC32MZ_IOPORTE_TRISINV       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_TRISINV_OFFSET)

#  define PIC32MZ_IOPORTE_PORT          (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_PORT_OFFSET)
#  define PIC32MZ_IOPORTE_PORTCLR       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_PORTCLR_OFFSET)
#  define PIC32MZ_IOPORTE_PORTSET       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_PORTSET_OFFSET)
#  define PIC32MZ_IOPORTE_PORTINV       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_PORTINV_OFFSET)

#  define PIC32MZ_IOPORTE_LAT           (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_LAT_OFFSET)
#  define PIC32MZ_IOPORTE_LATCLR        (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_LATCLR_OFFSET)
#  define PIC32MZ_IOPORTE_LATSET        (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_LATSET_OFFSET)
#  define PIC32MZ_IOPORTE_LATINV        (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_LATINV_OFFSET)

#  define PIC32MZ_IOPORTE_ODC           (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_ODC_OFFSET)
#  define PIC32MZ_IOPORTE_ODCCLR        (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_ODCCLR_OFFSET)
#  define PIC32MZ_IOPORTE_ODCSET        (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_ODCSET_OFFSET)
#  define PIC32MZ_IOPORTE_ODCINV        (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_ODCINV_OFFSET)

#  define PIC32MZ_IOPORTE_CNPU          (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNPU_OFFSET)
#  define PIC32MZ_IOPORTE_CNPUCLR       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNPUCLR_OFFSET)
#  define PIC32MZ_IOPORTE_CNPUSET       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNPUSET_OFFSET)
#  define PIC32MZ_IOPORTE_CNPUINV       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNPUINV_OFFSET)

#  define PIC32MZ_IOPORTE_CNPD          (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNPD_OFFSET)
#  define PIC32MZ_IOPORTE_CNPDCLR       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNPDCLR_OFFSET)
#  define PIC32MZ_IOPORTE_CNPDSET       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNPDSET_OFFSET)
#  define PIC32MZ_IOPORTE_CNPDINV       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNPDINV_OFFSET)

#  define PIC32MZ_IOPORTE_CNCON         (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNCON_OFFSET)
#  define PIC32MZ_IOPORTE_CNCONCLR      (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNCONCLR_OFFSET)
#  define PIC32MZ_IOPORTE_CNCONSET      (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNCONSET_OFFSET)
#  define PIC32MZ_IOPORTE_CNCONINV      (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNCONINV_OFFSET)

#  define PIC32MZ_IOPORTE_CNEN          (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNEN_OFFSET)
#  define PIC32MZ_IOPORTE_CNENCLR       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNENCLR_OFFSET)
#  define PIC32MZ_IOPORTE_CNENSET       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNENSET_OFFSET)
#  define PIC32MZ_IOPORTE_CNENINV       (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNENINV_OFFSET)

#  define PIC32MZ_IOPORTE_CNSTAT        (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNSTAT_OFFSET)
#  define PIC32MZ_IOPORTE_CNSTATCLR     (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNSTATCLR_OFFSET)
#  define PIC32MZ_IOPORTE_CNSTATSET     (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNSTATSET_OFFSET)
#  define PIC32MZ_IOPORTE_CNSTATINV     (PIC32MZ_IOPORTE_K1BASE+PIC32MZ_IOPORT_CNSTATINV_OFFSET)
#endif

#if CHIP_NPORTS > 5
/* Port F addresses */

#  define PIC32MZ_IOPORTF_ANSEL         (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_ANSEL_OFFSET)
#  define PIC32MZ_IOPORTF_ANSELCLR      (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_ANSELCLR_OFFSET)
#  define PIC32MZ_IOPORTF_ANSELSET      (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_ANSELSET_OFFSET)
#  define PIC32MZ_IOPORTF_ANSELINV      (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_ANSELINV_OFFSET)

#  define PIC32MZ_IOPORTF_TRIS          (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_TRIS_OFFSET)
#  define PIC32MZ_IOPORTF_TRISCLR       (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_TRISCLR_OFFSET)
#  define PIC32MZ_IOPORTF_TRISSET       (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_TRISSET_OFFSET)
#  define PIC32MZ_IOPORTF_TRISINV       (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_TRISINV_OFFSET)

#  define PIC32MZ_IOPORTF_PORT          (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_PORT_OFFSET)
#  define PIC32MZ_IOPORTF_PORTCLR       (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_PORTCLR_OFFSET)
#  define PIC32MZ_IOPORTF_PORTSET       (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_PORTSET_OFFSET)
#  define PIC32MZ_IOPORTF_PORTINV       (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_PORTINV_OFFSET)

#  define PIC32MZ_IOPORTF_LAT           (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_LAT_OFFSET)
#  define PIC32MZ_IOPORTF_LATCLR        (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_LATCLR_OFFSET)
#  define PIC32MZ_IOPORTF_LATSET        (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_LATSET_OFFSET)
#  define PIC32MZ_IOPORTF_LATINV        (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_LATINV_OFFSET)

#  define PIC32MZ_IOPORTF_ODC           (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_ODC_OFFSET)
#  define PIC32MZ_IOPORTF_ODCCLR        (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_ODCCLR_OFFSET)
#  define PIC32MZ_IOPORTF_ODCSET        (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_ODCSET_OFFSET)
#  define PIC32MZ_IOPORTF_ODCINV        (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_ODCINV_OFFSET)

#  define PIC32MZ_IOPORTF_CNPU          (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNPU_OFFSET)
#  define PIC32MZ_IOPORTF_CNPUCLR       (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNPUCLR_OFFSET)
#  define PIC32MZ_IOPORTF_CNPUSET       (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNPUSET_OFFSET)
#  define PIC32MZ_IOPORTF_CNPUINV       (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNPUINV_OFFSET)

#  define PIC32MZ_IOPORTF_CNPD          (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNPD_OFFSET)
#  define PIC32MZ_IOPORTF_CNPDCLR       (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNPDCLR_OFFSET)
#  define PIC32MZ_IOPORTF_CNPDSET       (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNPDSET_OFFSET)
#  define PIC32MZ_IOPORTF_CNPDINV       (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNPDINV_OFFSET)

#  define PIC32MZ_IOPORTF_CNCON         (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNCON_OFFSET)
#  define PIC32MZ_IOPORTF_CNCONCLR      (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNCONCLR_OFFSET)
#  define PIC32MZ_IOPORTF_CNCONSET      (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNCONSET_OFFSET)
#  define PIC32MZ_IOPORTF_CNCONINV      (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNCONINV_OFFSET)

#  define PIC32MZ_IOPORTF_CNEN          (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNEN_OFFSET)
#  define PIC32MZ_IOPORTF_CNENCLR       (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNENCLR_OFFSET)
#  define PIC32MZ_IOPORTF_CNENSET       (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNENSET_OFFSET)
#  define PIC32MZ_IOPORTF_CNENINV       (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNENINV_OFFSET)

#  define PIC32MZ_IOPORTF_CNSTAT        (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNSTAT_OFFSET)
#  define PIC32MZ_IOPORTF_CNSTATCLR     (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNSTATCLR_OFFSET)
#  define PIC32MZ_IOPORTF_CNSTATSET     (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNSTATSET_OFFSET)
#  define PIC32MZ_IOPORTF_CNSTATINV     (PIC32MZ_IOPORTF_K1BASE+PIC32MZ_IOPORT_CNSTATINV_OFFSET)
#endif

#if CHIP_NPORTS > 6
/* Port G addresses */

#  define PIC32MZ_IOPORTG_ANSEL         (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_ANSEL_OFFSET)
#  define PIC32MZ_IOPORTG_ANSELCLR      (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_ANSELCLR_OFFSET)
#  define PIC32MZ_IOPORTG_ANSELSET      (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_ANSELSET_OFFSET)
#  define PIC32MZ_IOPORTG_ANSELINV      (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_ANSELINV_OFFSET)

#  define PIC32MZ_IOPORTG_TRIS          (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_TRIS_OFFSET)
#  define PIC32MZ_IOPORTG_TRISCLR       (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_TRISCLR_OFFSET)
#  define PIC32MZ_IOPORTG_TRISSET       (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_TRISSET_OFFSET)
#  define PIC32MZ_IOPORTG_TRISINV       (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_TRISINV_OFFSET)

#  define PIC32MZ_IOPORTG_PORT          (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_PORT_OFFSET)
#  define PIC32MZ_IOPORTG_PORTCLR       (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_PORTCLR_OFFSET)
#  define PIC32MZ_IOPORTG_PORTSET       (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_PORTSET_OFFSET)
#  define PIC32MZ_IOPORTG_PORTINV       (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_PORTINV_OFFSET)

#  define PIC32MZ_IOPORTG_LAT           (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_LAT_OFFSET)
#  define PIC32MZ_IOPORTG_LATCLR        (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_LATCLR_OFFSET)
#  define PIC32MZ_IOPORTG_LATSET        (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_LATSET_OFFSET)
#  define PIC32MZ_IOPORTG_LATINV        (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_LATINV_OFFSET)

#  define PIC32MZ_IOPORTG_ODC           (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_ODC_OFFSET)
#  define PIC32MZ_IOPORTG_ODCCLR        (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_ODCCLR_OFFSET)
#  define PIC32MZ_IOPORTG_ODCSET        (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_ODCSET_OFFSET)
#  define PIC32MZ_IOPORTG_ODCINV        (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_ODCINV_OFFSET)

#  define PIC32MZ_IOPORTG_CNPU          (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNPU_OFFSET)
#  define PIC32MZ_IOPORTG_CNPUCLR       (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNPUCLR_OFFSET)
#  define PIC32MZ_IOPORTG_CNPUSET       (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNPUSET_OFFSET)
#  define PIC32MZ_IOPORTG_CNPUINV       (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNPUINV_OFFSET)

#  define PIC32MZ_IOPORTG_CNPD          (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNPD_OFFSET)
#  define PIC32MZ_IOPORTG_CNPDCLR       (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNPDCLR_OFFSET)
#  define PIC32MZ_IOPORTG_CNPDSET       (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNPDSET_OFFSET)
#  define PIC32MZ_IOPORTG_CNPDINV       (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNPDINV_OFFSET)

#  define PIC32MZ_IOPORTG_CNCON         (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNCON_OFFSET)
#  define PIC32MZ_IOPORTG_CNCONCLR      (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNCONCLR_OFFSET)
#  define PIC32MZ_IOPORTG_CNCONSET      (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNCONSET_OFFSET)
#  define PIC32MZ_IOPORTG_CNCONINV      (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNCONINV_OFFSET)

#  define PIC32MZ_IOPORTG_CNEN          (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNEN_OFFSET)
#  define PIC32MZ_IOPORTG_CNENCLR       (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNENCLR_OFFSET)
#  define PIC32MZ_IOPORTG_CNENSET       (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNENSET_OFFSET)
#  define PIC32MZ_IOPORTG_CNENINV       (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNENINV_OFFSET)

#  define PIC32MZ_IOPORTG_CNSTAT        (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNSTAT_OFFSET)
#  define PIC32MZ_IOPORTG_CNSTATCLR     (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNSTATCLR_OFFSET)
#  define PIC32MZ_IOPORTG_CNSTATSET     (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNSTATSET_OFFSET)
#  define PIC32MZ_IOPORTG_CNSTATINV     (PIC32MZ_IOPORTG_K1BASE+PIC32MZ_IOPORT_CNSTATINV_OFFSET)
#endif

#if CHIP_NPORTS > 7
/* Port H addresses */

#  define PIC32MZ_IOPORTH_ANSEL         (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_ANSEL_OFFSET)
#  define PIC32MZ_IOPORTH_ANSELCLR      (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_ANSELCLR_OFFSET)
#  define PIC32MZ_IOPORTH_ANSELSET      (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_ANSELSET_OFFSET)
#  define PIC32MZ_IOPORTH_ANSELINV      (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_ANSELINV_OFFSET)

#  define PIC32MZ_IOPORTH_TRIS          (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_TRIS_OFFSET)
#  define PIC32MZ_IOPORTH_TRISCLR       (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_TRISCLR_OFFSET)
#  define PIC32MZ_IOPORTH_TRISSET       (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_TRISSET_OFFSET)
#  define PIC32MZ_IOPORTH_TRISINV       (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_TRISINV_OFFSET)

#  define PIC32MZ_IOPORTH_PORT          (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_PORT_OFFSET)
#  define PIC32MZ_IOPORTH_PORTCLR       (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_PORTCLR_OFFSET)
#  define PIC32MZ_IOPORTH_PORTSET       (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_PORTSET_OFFSET)
#  define PIC32MZ_IOPORTH_PORTINV       (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_PORTINV_OFFSET)

#  define PIC32MZ_IOPORTH_LAT           (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_LAT_OFFSET)
#  define PIC32MZ_IOPORTH_LATCLR        (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_LATCLR_OFFSET)
#  define PIC32MZ_IOPORTH_LATSET        (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_LATSET_OFFSET)
#  define PIC32MZ_IOPORTH_LATINV        (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_LATINV_OFFSET)

#  define PIC32MZ_IOPORTH_ODC           (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_ODC_OFFSET)
#  define PIC32MZ_IOPORTH_ODCCLR        (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_ODCCLR_OFFSET)
#  define PIC32MZ_IOPORTH_ODCSET        (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_ODCSET_OFFSET)
#  define PIC32MZ_IOPORTH_ODCINV        (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_ODCINV_OFFSET)

#  define PIC32MZ_IOPORTH_CNPU          (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNPU_OFFSET)
#  define PIC32MZ_IOPORTH_CNPUCLR       (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNPUCLR_OFFSET)
#  define PIC32MZ_IOPORTH_CNPUSET       (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNPUSET_OFFSET)
#  define PIC32MZ_IOPORTH_CNPUINV       (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNPUINV_OFFSET)

#  define PIC32MZ_IOPORTH_CNPD          (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNPD_OFFSET)
#  define PIC32MZ_IOPORTH_CNPDCLR       (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNPDCLR_OFFSET)
#  define PIC32MZ_IOPORTH_CNPDSET       (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNPDSET_OFFSET)
#  define PIC32MZ_IOPORTH_CNPDINV       (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNPDINV_OFFSET)

#  define PIC32MZ_IOPORTH_CNCON         (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNCON_OFFSET)
#  define PIC32MZ_IOPORTH_CNCONCLR      (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNCONCLR_OFFSET)
#  define PIC32MZ_IOPORTH_CNCONSET      (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNCONSET_OFFSET)
#  define PIC32MZ_IOPORTH_CNCONINV      (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNCONINV_OFFSET)

#  define PIC32MZ_IOPORTH_CNEN          (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNEN_OFFSET)
#  define PIC32MZ_IOPORTH_CNENCLR       (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNENCLR_OFFSET)
#  define PIC32MZ_IOPORTH_CNENSET       (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNENSET_OFFSET)
#  define PIC32MZ_IOPORTH_CNENINV       (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNENINV_OFFSET)

#  define PIC32MZ_IOPORTH_CNSTAT        (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNSTAT_OFFSET)
#  define PIC32MZ_IOPORTH_CNSTATCLR     (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNSTATCLR_OFFSET)
#  define PIC32MZ_IOPORTH_CNSTATSET     (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNSTATSET_OFFSET)
#  define PIC32MZ_IOPORTH_CNSTATINV     (PIC32MZ_IOPORTH_K1BASE+PIC32MZ_IOPORT_CNSTATINV_OFFSET)
#endif

#if CHIP_NPORTS > 8
/* Port J addresses */

#  define PIC32MZ_IOPORTJ_ANSEL         (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_ANSEL_OFFSET)
#  define PIC32MZ_IOPORTJ_ANSELCLR      (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_ANSELCLR_OFFSET)
#  define PIC32MZ_IOPORTJ_ANSELSET      (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_ANSELSET_OFFSET)
#  define PIC32MZ_IOPORTJ_ANSELINV      (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_ANSELINV_OFFSET)

#  define PIC32MZ_IOPORTJ_TRIS          (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_TRIS_OFFSET)
#  define PIC32MZ_IOPORTJ_TRISCLR       (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_TRISCLR_OFFSET)
#  define PIC32MZ_IOPORTJ_TRISSET       (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_TRISSET_OFFSET)
#  define PIC32MZ_IOPORTJ_TRISINV       (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_TRISINV_OFFSET)

#  define PIC32MZ_IOPORTJ_PORT          (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_PORT_OFFSET)
#  define PIC32MZ_IOPORTJ_PORTCLR       (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_PORTCLR_OFFSET)
#  define PIC32MZ_IOPORTJ_PORTSET       (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_PORTSET_OFFSET)
#  define PIC32MZ_IOPORTJ_PORTINV       (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_PORTINV_OFFSET)

#  define PIC32MZ_IOPORTJ_LAT           (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_LAT_OFFSET)
#  define PIC32MZ_IOPORTJ_LATCLR        (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_LATCLR_OFFSET)
#  define PIC32MZ_IOPORTJ_LATSET        (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_LATSET_OFFSET)
#  define PIC32MZ_IOPORTJ_LATINV        (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_LATINV_OFFSET)

#  define PIC32MZ_IOPORTJ_ODC           (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_ODC_OFFSET)
#  define PIC32MZ_IOPORTJ_ODCCLR        (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_ODCCLR_OFFSET)
#  define PIC32MZ_IOPORTJ_ODCSET        (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_ODCSET_OFFSET)
#  define PIC32MZ_IOPORTJ_ODCINV        (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_ODCINV_OFFSET)

#  define PIC32MZ_IOPORTJ_CNPU          (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNPU_OFFSET)
#  define PIC32MZ_IOPORTJ_CNPUCLR       (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNPUCLR_OFFSET)
#  define PIC32MZ_IOPORTJ_CNPUSET       (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNPUSET_OFFSET)
#  define PIC32MZ_IOPORTJ_CNPUINV       (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNPUINV_OFFSET)

#  define PIC32MZ_IOPORTJ_CNPD          (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNPD_OFFSET)
#  define PIC32MZ_IOPORTJ_CNPDCLR       (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNPDCLR_OFFSET)
#  define PIC32MZ_IOPORTJ_CNPDSET       (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNPDSET_OFFSET)
#  define PIC32MZ_IOPORTJ_CNPDINV       (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNPDINV_OFFSET)

#  define PIC32MZ_IOPORTJ_CNCON         (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNCON_OFFSET)
#  define PIC32MZ_IOPORTJ_CNCONCLR      (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNCONCLR_OFFSET)
#  define PIC32MZ_IOPORTJ_CNCONSET      (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNCONSET_OFFSET)
#  define PIC32MZ_IOPORTJ_CNCONINV      (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNCONINV_OFFSET)

#  define PIC32MZ_IOPORTJ_CNEN          (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNEN_OFFSET)
#  define PIC32MZ_IOPORTJ_CNENCLR       (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNENCLR_OFFSET)
#  define PIC32MZ_IOPORTJ_CNENSET       (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNENSET_OFFSET)
#  define PIC32MZ_IOPORTJ_CNENINV       (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNENINV_OFFSET)

#  define PIC32MZ_IOPORTJ_CNSTAT        (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNSTAT_OFFSET)
#  define PIC32MZ_IOPORTJ_CNSTATCLR     (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNSTATCLR_OFFSET)
#  define PIC32MZ_IOPORTJ_CNSTATSET     (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNSTATSET_OFFSET)
#  define PIC32MZ_IOPORTJ_CNSTATINV     (PIC32MZ_IOPORTJ_K1BASE+PIC32MZ_IOPORT_CNSTATINV_OFFSET)
#endif

#if CHIP_NPORTS > 9
/* Port K addresses */

#  define PIC32MZ_IOPORTK_ANSEL         (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_ANSEL_OFFSET)
#  define PIC32MZ_IOPORTK_ANSELCLR      (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_ANSELCLR_OFFSET)
#  define PIC32MZ_IOPORTK_ANSELSET      (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_ANSELSET_OFFSET)
#  define PIC32MZ_IOPORTK_ANSELINV      (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_ANSELINV_OFFSET)

#  define PIC32MZ_IOPORTK_TRIS          (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_TRIS_OFFSET)
#  define PIC32MZ_IOPORTK_TRISCLR       (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_TRISCLR_OFFSET)
#  define PIC32MZ_IOPORTK_TRISSET       (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_TRISSET_OFFSET)
#  define PIC32MZ_IOPORTK_TRISINV       (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_TRISINV_OFFSET)

#  define PIC32MZ_IOPORTK_PORT          (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_PORT_OFFSET)
#  define PIC32MZ_IOPORTK_PORTCLR       (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_PORTCLR_OFFSET)
#  define PIC32MZ_IOPORTK_PORTSET       (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_PORTSET_OFFSET)
#  define PIC32MZ_IOPORTK_PORTINV       (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_PORTINV_OFFSET)

#  define PIC32MZ_IOPORTK_LAT           (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_LAT_OFFSET)
#  define PIC32MZ_IOPORTK_LATCLR        (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_LATCLR_OFFSET)
#  define PIC32MZ_IOPORTK_LATSET        (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_LATSET_OFFSET)
#  define PIC32MZ_IOPORTK_LATINV        (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_LATINV_OFFSET)

#  define PIC32MZ_IOPORTK_ODC           (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_ODC_OFFSET)
#  define PIC32MZ_IOPORTK_ODCCLR        (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_ODCCLR_OFFSET)
#  define PIC32MZ_IOPORTK_ODCSET        (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_ODCSET_OFFSET)
#  define PIC32MZ_IOPORTK_ODCINV        (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_ODCINV_OFFSET)

#  define PIC32MZ_IOPORTK_CNPU          (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNPU_OFFSET)
#  define PIC32MZ_IOPORTK_CNPUCLR       (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNPUCLR_OFFSET)
#  define PIC32MZ_IOPORTK_CNPUSET       (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNPUSET_OFFSET)
#  define PIC32MZ_IOPORTK_CNPUINV       (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNPUINV_OFFSET)

#  define PIC32MZ_IOPORTK_CNPD          (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNPD_OFFSET)
#  define PIC32MZ_IOPORTK_CNPDCLR       (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNPDCLR_OFFSET)
#  define PIC32MZ_IOPORTK_CNPDSET       (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNPDSET_OFFSET)
#  define PIC32MZ_IOPORTK_CNPDINV       (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNPDINV_OFFSET)

#  define PIC32MZ_IOPORTK_CNCON         (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNCON_OFFSET)
#  define PIC32MZ_IOPORTK_CNCONCLR      (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNCONCLR_OFFSET)
#  define PIC32MZ_IOPORTK_CNCONSET      (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNCONSET_OFFSET)
#  define PIC32MZ_IOPORTK_CNCONINV      (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNCONINV_OFFSET)

#  define PIC32MZ_IOPORTK_CNEN          (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNEN_OFFSET)
#  define PIC32MZ_IOPORTK_CNENCLR       (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNENCLR_OFFSET)
#  define PIC32MZ_IOPORTK_CNENSET       (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNENSET_OFFSET)
#  define PIC32MZ_IOPORTK_CNENINV       (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNENINV_OFFSET)

#  define PIC32MZ_IOPORTK_CNSTAT        (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNSTAT_OFFSET)
#  define PIC32MZ_IOPORTK_CNSTATCLR     (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNSTATCLR_OFFSET)
#  define PIC32MZ_IOPORTK_CNSTATSET     (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNSTATSET_OFFSET)
#  define PIC32MZ_IOPORTK_CNSTATINV     (PIC32MZ_IOPORTK_K1BASE+PIC32MZ_IOPORT_CNSTATINV_OFFSET)
#endif

/* Register Bit-Field Definitions *******************************************/

/* Analog select register */

#define IOPORT_ANSEL(n)                (1 << (n)) /* Bits 0-15: Analog select */
#define IOPORT_ANSEL_ALL               0x0000ffff

/* Tri-state register */

#define IOPORT_TRIS(n)                 (1 << (n)) /* Bits 0-15: 1: Input 0: Output */
#define IOPORT_TRIS_ALL                0x0000ffff

/* Port register */

#define IOPORT_PORT(n)                 (1 << (n)) /* Bits 0-15: Pin value */
#define IOPORT_PORT_ALL                0x0000ffff

/* Port data latch register */

#define IOPORT_LAT(n)                  (1 << (n)) /* Bits 0-15: Port latch value */
#define IOPORT_LAT_ALL                 0x0000ffff

/* Open drain control register */

#define IOPORT_ODC(n)                  (1 << (n)) /* Bits 0-15: 1: OD output enabled, 0: Disabled */
#define IOPORT_ODC_ALL                 0x0000ffff

/* Change Notice Pull-up register */

#define IOPORT_CNPU(n)                 (1 << (n)) /* Bits 0:15: 1=Pull-up enabled */
#define IOPORT_CNPU_ALL                0x0000ffff

/* Change Notice Pull-down register */

#define IOPORT_CNPD(n)                 (1 << (n)) /* Bits 0:15: 1=Pull-down enabled */
#define IOPORT_CNPD_ALL                0x0000ffff

/* Change Notice Control register */

#define IOPORT_CNCON_EDGEDETECT        (1 << 11) /* Bit 11: Change Notification Style bit */
#define IOPORT_CNCON_SIDL              (1 << 13) /* Bit 13: Stop in Idle Control bit */
#define IOPORT_CNCON_ON                (1 << 15) /* Bit 15: Change notice module enable */

/* Change Notice Interrupt Enable register */

#define IOPORT_CNEN(n)                 (1 << (n)) /* Bits 0-15: 1=Interrupt enabled */
#define IOPORT_CNEN_ALL                0x0000ffff

/* Change Notice Status register */

#define IOPORT_CNSTAT(n)               (1 << (n)) /* Bits 0-15: Change notice control pin n */
#define IOPORT_CNSTAT_ALL              0x0000ffff

/* Change Notice Interrupt Enable register (Negative edge) */

#define IOPORT_CNNE(n)                 (1 << (n)) /* Bits 0-15: 1=Interrupt enabled */
#define IOPORT_CNNE_ALL                0x0000ffff

/* Change Notice Status register */

#define IOPORT_CNF(n)                  (1 << (n)) /* Bits 0-15: Change notice control pin n */
#define IOPORT_CNF_ALL                 0x0000ffff

/* Slew Rate Control0 register */

#define IOPORT_SRCON0(n)               (1 << (n)) /* Bits 0-15: Slew Rate control pin n */
#define IOPORT_SRCON0_ALL              0x0000ffff

/* Slew Rate Control1 register */

#define IOPORT_SRCON1(n)               (1 << (n)) /* Bits 0-15: Slew Rate control pin n */
#define IOPORT_SRCON1_ALL              0x0000ffff

/****************************************************************************
 * Public Types
 ****************************************************************************/

#ifndef __ASSEMBLY__

/****************************************************************************
 * Public Data
 ****************************************************************************/

#ifdef __cplusplus
#define EXTERN extern "C"
extern "C"
{
#else
#define EXTERN extern
#endif

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/

#undef EXTERN
#ifdef __cplusplus
}
#endif

#endif /* __ASSEMBLY__ */
#endif /* CHIP_NPORTS > 0 */
#endif /* __ARCH_MIPS_SRC_PIC32MZ_HARDWARE_PIC32MZ_IOPORT_H */
