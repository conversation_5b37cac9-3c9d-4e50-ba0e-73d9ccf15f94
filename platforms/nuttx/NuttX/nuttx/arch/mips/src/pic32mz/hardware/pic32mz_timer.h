/****************************************************************************
 * arch/mips/src/pic32mz/hardware/pic32mz_timer.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_MIPS_SRC_PIC32MZ_HARDWARE_PIC32MZ_TIMER_H
#define __ARCH_MIPS_SRC_PIC32MZ_HARDWARE_PIC32MZ_TIMER_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

#include <arch/pic32mz/chip.h>
#include "pic32mz_memorymap.h"

#if CHIP_NTIMERS > 0

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Timer Peripheral Offsets *************************************************/

#define PIC32MZ_TIMERN_OFFSET(n)   ((n) << 9)
#  define PIC32MZ_TIMER1_OFFSET    0x0000
#  define PIC32MZ_TIMER2_OFFSET    0x0200
#  define PIC32MZ_TIMER3_OFFSET    0x0400
#  define PIC32MZ_TIMER4_OFFSET    0x0600
#  define PIC32MZ_TIMER5_OFFSET    0x0800
#  define PIC32MZ_TIMER6_OFFSET    0x0a00
#  define PIC32MZ_TIMER7_OFFSET    0x0c00
#  define PIC32MZ_TIMER8_OFFSET    0x0e00
#  define PIC32MZ_TIMER9_OFFSET    0x1000

/* Register Offsets *********************************************************/

#define PIC32MZ_TIMER_CON_OFFSET    0x0000 /* Timer control register */
#define PIC32MZ_TIMER_CONCLR_OFFSET 0x0004 /* Timer control clear register */
#define PIC32MZ_TIMER_CONSET_OFFSET 0x0008 /* Timer control set register */
#define PIC32MZ_TIMER_CONINV_OFFSET 0x000c /* Timer control invert register */

#define PIC32MZ_TIMER_CNT_OFFSET    0x0010 /* Timer count register */
#define PIC32MZ_TIMER_CNTCLR_OFFSET 0x0014 /* Timer count clear register */
#define PIC32MZ_TIMER_CNTSET_OFFSET 0x0018 /* Timer count set register */
#define PIC32MZ_TIMER_CNTINV_OFFSET 0x001c /* Timer count invert register */

#define PIC32MZ_TIMER_PR_OFFSET     0x0020 /* Timer period register */
#define PIC32MZ_TIMER_PRCLR_OFFSET  0x0024 /* Timer period clear register */
#define PIC32MZ_TIMER_PRSET_OFFSET  0x0028 /* Timer period set register */
#define PIC32MZ_TIMER_PRINV_OFFSET  0x002c /* Timer period invert register */

/* Timer Peripheral Addresses ***********************************************/

#define PIC32MZ_TIMERN_K1BASE(n)   (PIC32MZ_TIMER_K1BASE+PIC32MZ_TIMERN_OFFSET(n))
#  define PIC32MZ_TIMER1_K1BASE    (PIC32MZ_TIMER_K1BASE+PIC32MZ_TIMER1_OFFSET)
#  define PIC32MZ_TIMER2_K1BASE    (PIC32MZ_TIMER_K1BASE+PIC32MZ_TIMER2_OFFSET)
#  define PIC32MZ_TIMER3_K1BASE    (PIC32MZ_TIMER_K1BASE+PIC32MZ_TIMER3_OFFSET)
#  define PIC32MZ_TIMER4_K1BASE    (PIC32MZ_TIMER_K1BASE+PIC32MZ_TIMER4_OFFSET)
#  define PIC32MZ_TIMER5_K1BASE    (PIC32MZ_TIMER_K1BASE+PIC32MZ_TIMER5_OFFSET)
#  define PIC32MZ_TIMER6_K1BASE    (PIC32MZ_TIMER_K1BASE+PIC32MZ_TIMER6_OFFSET)
#  define PIC32MZ_TIMER7_K1BASE    (PIC32MZ_TIMER_K1BASE+PIC32MZ_TIMER7_OFFSET)
#  define PIC32MZ_TIMER8_K1BASE    (PIC32MZ_TIMER_K1BASE+PIC32MZ_TIMER8_OFFSET)
#  define PIC32MZ_TIMER9_K1BASE    (PIC32MZ_TIMER_K1BASE+PIC32MZ_TIMER9_OFFSET)

/* Register Addresses *******************************************************/

#define PIC32MZ_TIMER_CON(n)        (PIC32MZ_TIMERN_K1BASE(n)+PIC32MZ_TIMER_CON_OFFSET)
#define PIC32MZ_TIMER_CONCLR(n)     (PIC32MZ_TIMERN_K1BASE(n)+PIC32MZ_TIMER_CONCLR_OFFSET)
#define PIC32MZ_TIMER_CONSET(n)     (PIC32MZ_TIMERN_K1BASE(n)+PIC32MZ_TIMER_CONSET_OFFSET)
#define PIC32MZ_TIMER_CONINV(n)     (PIC32MZ_TIMERN_K1BASE(n)+PIC32MZ_TIMER_CONINV_OFFSET)
#define PIC32MZ_TIMER_CNT(n)        (PIC32MZ_TIMERN_K1BASE(n)+PIC32MZ_TIMER_CNT_OFFSET)
#define PIC32MZ_TIMER_CNTCLR(n)     (PIC32MZ_TIMERN_K1BASE(n)+PIC32MZ_TIMER_CNTCLR_OFFSET)
#define PIC32MZ_TIMER_CNTSET(n)     (PIC32MZ_TIMERN_K1BASE(n)+PIC32MZ_TIMER_CNTSET_OFFSET)
#define PIC32MZ_TIMER_CNTINV(n)     (PIC32MZ_TIMERN_K1BASE(n)+PIC32MZ_TIMER_CNTINV_OFFSET)
#define PIC32MZ_TIMER_PR(n)         (PIC32MZ_TIMERN_K1BASE(n)+PIC32MZ_TIMER_PR_OFFSET)
#define PIC32MZ_TIMER_PRCLR(n)      (PIC32MZ_TIMERN_K1BASE(n)+PIC32MZ_TIMER_PRCLR_OFFSET)
#define PIC32MZ_TIMER_PRSET(n)      (PIC32MZ_TIMERN_K1BASE(n)+PIC32MZ_TIMER_PRSET_OFFSET)
#define PIC32MZ_TIMER_PRINV(n)      (PIC32MZ_TIMERN_K1BASE(n)+PIC32MZ_TIMER_PRINV_OFFSET)

#define PIC32MZ_TIMER1_CON          (PIC32MZ_TIMER1_K1BASE+PIC32MZ_TIMER_CON_OFFSET)
#define PIC32MZ_TIMER1_CONCLR       (PIC32MZ_TIMER1_K1BASE+PIC32MZ_TIMER_CONCLR_OFFSET)
#define PIC32MZ_TIMER1_CONSET       (PIC32MZ_TIMER1_K1BASE+PIC32MZ_TIMER_CONSET_OFFSET)
#define PIC32MZ_TIMER1_CONINV       (PIC32MZ_TIMER1_K1BASE+PIC32MZ_TIMER_CONINV_OFFSET)
#define PIC32MZ_TIMER1_CNT          (PIC32MZ_TIMER1_K1BASE+PIC32MZ_TIMER_CNT_OFFSET)
#define PIC32MZ_TIMER1_CNTCLR       (PIC32MZ_TIMER1_K1BASE+PIC32MZ_TIMER_CNTCLR_OFFSET)
#define PIC32MZ_TIMER1_CNTSET       (PIC32MZ_TIMER1_K1BASE+PIC32MZ_TIMER_CNTSET_OFFSET)
#define PIC32MZ_TIMER1_CNTINV       (PIC32MZ_TIMER1_K1BASE+PIC32MZ_TIMER_CNTINV_OFFSET)
#define PIC32MZ_TIMER1_PR           (PIC32MZ_TIMER1_K1BASE+PIC32MZ_TIMER_PR_OFFSET)
#define PIC32MZ_TIMER1_PRCLR        (PIC32MZ_TIMER1_K1BASE+PIC32MZ_TIMER_PRCLR_OFFSET)
#define PIC32MZ_TIMER1_PRSET        (PIC32MZ_TIMER1_K1BASE+PIC32MZ_TIMER_PRSET_OFFSET)
#define PIC32MZ_TIMER1_PRINV        (PIC32MZ_TIMER1_K1BASE+PIC32MZ_TIMER_PRINV_OFFSET)

#if CHIP_NTIMERS > 1
#  define PIC32MZ_TIMER2_CON        (PIC32MZ_TIMER2_K1BASE+PIC32MZ_TIMER_CON_OFFSET)
#  define PIC32MZ_TIMER2_CONCLR     (PIC32MZ_TIMER2_K1BASE+PIC32MZ_TIMER_CONCLR_OFFSET)
#  define PIC32MZ_TIMER2_CONSET     (PIC32MZ_TIMER2_K1BASE+PIC32MZ_TIMER_CONSET_OFFSET)
#  define PIC32MZ_TIMER2_CONINV     (PIC32MZ_TIMER2_K1BASE+PIC32MZ_TIMER_CONINV_OFFSET)
#  define PIC32MZ_TIMER2_CNT        (PIC32MZ_TIMER2_K1BASE+PIC32MZ_TIMER_CNT_OFFSET)
#  define PIC32MZ_TIMER2_CNTCLR     (PIC32MZ_TIMER2_K1BASE+PIC32MZ_TIMER_CNTCLR_OFFSET)
#  define PIC32MZ_TIMER2_CNTSET     (PIC32MZ_TIMER2_K1BASE+PIC32MZ_TIMER_CNTSET_OFFSET)
#  define PIC32MZ_TIMER2_CNTINV     (PIC32MZ_TIMER2_K1BASE+PIC32MZ_TIMER_CNTINV_OFFSET)
#  define PIC32MZ_TIMER2_PR         (PIC32MZ_TIMER2_K1BASE+PIC32MZ_TIMER_PR_OFFSET)
#  define PIC32MZ_TIMER2_PRCLR      (PIC32MZ_TIMER2_K1BASE+PIC32MZ_TIMER_PRCLR_OFFSET)
#  define PIC32MZ_TIMER2_PRSET      (PIC32MZ_TIMER2_K1BASE+PIC32MZ_TIMER_PRSET_OFFSET)
#  define PIC32MZ_TIMER2_PRINV      (PIC32MZ_TIMER2_K1BASE+PIC32MZ_TIMER_PRINV_OFFSET)
#endif

#if CHIP_NTIMERS > 2
#  define PIC32MZ_TIMER3_CON        (PIC32MZ_TIMER3_K1BASE+PIC32MZ_TIMER_CON_OFFSET)
#  define PIC32MZ_TIMER3_CONCLR     (PIC32MZ_TIMER3_K1BASE+PIC32MZ_TIMER_CONCLR_OFFSET)
#  define PIC32MZ_TIMER3_CONSET     (PIC32MZ_TIMER3_K1BASE+PIC32MZ_TIMER_CONSET_OFFSET)
#  define PIC32MZ_TIMER3_CONINV     (PIC32MZ_TIMER3_K1BASE+PIC32MZ_TIMER_CONINV_OFFSET)
#  define PIC32MZ_TIMER3_CNT        (PIC32MZ_TIMER3_K1BASE+PIC32MZ_TIMER_CNT_OFFSET)
#  define PIC32MZ_TIMER3_CNTCLR     (PIC32MZ_TIMER3_K1BASE+PIC32MZ_TIMER_CNTCLR_OFFSET)
#  define PIC32MZ_TIMER3_CNTSET     (PIC32MZ_TIMER3_K1BASE+PIC32MZ_TIMER_CNTSET_OFFSET)
#  define PIC32MZ_TIMER3_CNTINV     (PIC32MZ_TIMER3_K1BASE+PIC32MZ_TIMER_CNTINV_OFFSET)
#  define PIC32MZ_TIMER3_PR         (PIC32MZ_TIMER3_K1BASE+PIC32MZ_TIMER_PR_OFFSET)
#  define PIC32MZ_TIMER3_PRCLR      (PIC32MZ_TIMER3_K1BASE+PIC32MZ_TIMER_PRCLR_OFFSET)
#  define PIC32MZ_TIMER3_PRSET      (PIC32MZ_TIMER3_K1BASE+PIC32MZ_TIMER_PRSET_OFFSET)
#  define PIC32MZ_TIMER3_PRINV      (PIC32MZ_TIMER3_K1BASE+PIC32MZ_TIMER_PRINV_OFFSET)
#endif

#if CHIP_NTIMERS > 3
#  define PIC32MZ_TIMER4_CON        (PIC32MZ_TIMER4_K1BASE+PIC32MZ_TIMER_CON_OFFSET)
#  define PIC32MZ_TIMER4_CONCLR     (PIC32MZ_TIMER4_K1BASE+PIC32MZ_TIMER_CONCLR_OFFSET)
#  define PIC32MZ_TIMER4_CONSET     (PIC32MZ_TIMER4_K1BASE+PIC32MZ_TIMER_CONSET_OFFSET)
#  define PIC32MZ_TIMER4_CONINV     (PIC32MZ_TIMER4_K1BASE+PIC32MZ_TIMER_CONINV_OFFSET)
#  define PIC32MZ_TIMER4_CNT        (PIC32MZ_TIMER4_K1BASE+PIC32MZ_TIMER_CNT_OFFSET)
#  define PIC32MZ_TIMER4_CNTCLR     (PIC32MZ_TIMER4_K1BASE+PIC32MZ_TIMER_CNTCLR_OFFSET)
#  define PIC32MZ_TIMER4_CNTSET     (PIC32MZ_TIMER4_K1BASE+PIC32MZ_TIMER_CNTSET_OFFSET)
#  define PIC32MZ_TIMER4_CNTINV     (PIC32MZ_TIMER4_K1BASE+PIC32MZ_TIMER_CNTINV_OFFSET)
#  define PIC32MZ_TIMER4_PR         (PIC32MZ_TIMER4_K1BASE+PIC32MZ_TIMER_PR_OFFSET)
#  define PIC32MZ_TIMER4_PRCLR      (PIC32MZ_TIMER4_K1BASE+PIC32MZ_TIMER_PRCLR_OFFSET)
#  define PIC32MZ_TIMER4_PRSET      (PIC32MZ_TIMER4_K1BASE+PIC32MZ_TIMER_PRSET_OFFSET)
#  define PIC32MZ_TIMER4_PRINV      (PIC32MZ_TIMER4_K1BASE+PIC32MZ_TIMER_PRINV_OFFSET)
#endif

#if CHIP_NTIMERS > 4
#  define PIC32MZ_TIMER5_CON        (PIC32MZ_TIMER5_K1BASE+PIC32MZ_TIMER_CON_OFFSET)
#  define PIC32MZ_TIMER5_CONCLR     (PIC32MZ_TIMER5_K1BASE+PIC32MZ_TIMER_CONCLR_OFFSET)
#  define PIC32MZ_TIMER5_CONSET     (PIC32MZ_TIMER5_K1BASE+PIC32MZ_TIMER_CONSET_OFFSET)
#  define PIC32MZ_TIMER5_CONINV     (PIC32MZ_TIMER5_K1BASE+PIC32MZ_TIMER_CONINV_OFFSET)
#  define PIC32MZ_TIMER5_CNT        (PIC32MZ_TIMER5_K1BASE+PIC32MZ_TIMER_CNT_OFFSET)
#  define PIC32MZ_TIMER5_CNTCLR     (PIC32MZ_TIMER5_K1BASE+PIC32MZ_TIMER_CNTCLR_OFFSET)
#  define PIC32MZ_TIMER5_CNTSET     (PIC32MZ_TIMER5_K1BASE+PIC32MZ_TIMER_CNTSET_OFFSET)
#  define PIC32MZ_TIMER5_CNTINV     (PIC32MZ_TIMER5_K1BASE+PIC32MZ_TIMER_CNTINV_OFFSET)
#  define PIC32MZ_TIMER5_PR         (PIC32MZ_TIMER5_K1BASE+PIC32MZ_TIMER_PR_OFFSET)
#  define PIC32MZ_TIMER5_PRCLR      (PIC32MZ_TIMER5_K1BASE+PIC32MZ_TIMER_PRCLR_OFFSET)
#  define PIC32MZ_TIMER5_PRSET      (PIC32MZ_TIMER5_K1BASE+PIC32MZ_TIMER_PRSET_OFFSET)
#  define PIC32MZ_TIMER5_PRINV      (PIC32MZ_TIMER5_K1BASE+PIC32MZ_TIMER_PRINV_OFFSET)
#endif

#if CHIP_NTIMERS > 5
#  define PIC32MZ_TIMER6_CON        (PIC32MZ_TIMER6_K1BASE+PIC32MZ_TIMER_CON_OFFSET)
#  define PIC32MZ_TIMER6_CONCLR     (PIC32MZ_TIMER6_K1BASE+PIC32MZ_TIMER_CONCLR_OFFSET)
#  define PIC32MZ_TIMER6_CONSET     (PIC32MZ_TIMER6_K1BASE+PIC32MZ_TIMER_CONSET_OFFSET)
#  define PIC32MZ_TIMER6_CONINV     (PIC32MZ_TIMER6_K1BASE+PIC32MZ_TIMER_CONINV_OFFSET)
#  define PIC32MZ_TIMER6_CNT        (PIC32MZ_TIMER6_K1BASE+PIC32MZ_TIMER_CNT_OFFSET)
#  define PIC32MZ_TIMER6_CNTCLR     (PIC32MZ_TIMER6_K1BASE+PIC32MZ_TIMER_CNTCLR_OFFSET)
#  define PIC32MZ_TIMER6_CNTSET     (PIC32MZ_TIMER6_K1BASE+PIC32MZ_TIMER_CNTSET_OFFSET)
#  define PIC32MZ_TIMER6_CNTINV     (PIC32MZ_TIMER6_K1BASE+PIC32MZ_TIMER_CNTINV_OFFSET)
#  define PIC32MZ_TIMER6_PR         (PIC32MZ_TIMER6_K1BASE+PIC32MZ_TIMER_PR_OFFSET)
#  define PIC32MZ_TIMER6_PRCLR      (PIC32MZ_TIMER6_K1BASE+PIC32MZ_TIMER_PRCLR_OFFSET)
#  define PIC32MZ_TIMER6_PRSET      (PIC32MZ_TIMER6_K1BASE+PIC32MZ_TIMER_PRSET_OFFSET)
#  define PIC32MZ_TIMER6_PRINV      (PIC32MZ_TIMER6_K1BASE+PIC32MZ_TIMER_PRINV_OFFSET)
#endif

#if CHIP_NTIMERS > 6
#  define PIC32MZ_TIMER7_CON        (PIC32MZ_TIMER7_K1BASE+PIC32MZ_TIMER_CON_OFFSET)
#  define PIC32MZ_TIMER7_CONCLR     (PIC32MZ_TIMER7_K1BASE+PIC32MZ_TIMER_CONCLR_OFFSET)
#  define PIC32MZ_TIMER7_CONSET     (PIC32MZ_TIMER7_K1BASE+PIC32MZ_TIMER_CONSET_OFFSET)
#  define PIC32MZ_TIMER7_CONINV     (PIC32MZ_TIMER7_K1BASE+PIC32MZ_TIMER_CONINV_OFFSET)
#  define PIC32MZ_TIMER7_CNT        (PIC32MZ_TIMER7_K1BASE+PIC32MZ_TIMER_CNT_OFFSET)
#  define PIC32MZ_TIMER7_CNTCLR     (PIC32MZ_TIMER7_K1BASE+PIC32MZ_TIMER_CNTCLR_OFFSET)
#  define PIC32MZ_TIMER7_CNTSET     (PIC32MZ_TIMER7_K1BASE+PIC32MZ_TIMER_CNTSET_OFFSET)
#  define PIC32MZ_TIMER7_CNTINV     (PIC32MZ_TIMER7_K1BASE+PIC32MZ_TIMER_CNTINV_OFFSET)
#  define PIC32MZ_TIMER7_PR         (PIC32MZ_TIMER7_K1BASE+PIC32MZ_TIMER_PR_OFFSET)
#  define PIC32MZ_TIMER7_PRCLR      (PIC32MZ_TIMER7_K1BASE+PIC32MZ_TIMER_PRCLR_OFFSET)
#  define PIC32MZ_TIMER7_PRSET      (PIC32MZ_TIMER7_K1BASE+PIC32MZ_TIMER_PRSET_OFFSET)
#  define PIC32MZ_TIMER7_PRINV      (PIC32MZ_TIMER7_K1BASE+PIC32MZ_TIMER_PRINV_OFFSET)
#endif

#if CHIP_NTIMERS > 7
#  define PIC32MZ_TIMER8_CON        (PIC32MZ_TIMER8_K1BASE+PIC32MZ_TIMER_CON_OFFSET)
#  define PIC32MZ_TIMER8_CONCLR     (PIC32MZ_TIMER8_K1BASE+PIC32MZ_TIMER_CONCLR_OFFSET)
#  define PIC32MZ_TIMER8_CONSET     (PIC32MZ_TIMER8_K1BASE+PIC32MZ_TIMER_CONSET_OFFSET)
#  define PIC32MZ_TIMER8_CONINV     (PIC32MZ_TIMER8_K1BASE+PIC32MZ_TIMER_CONINV_OFFSET)
#  define PIC32MZ_TIMER8_CNT        (PIC32MZ_TIMER8_K1BASE+PIC32MZ_TIMER_CNT_OFFSET)
#  define PIC32MZ_TIMER8_CNTCLR     (PIC32MZ_TIMER8_K1BASE+PIC32MZ_TIMER_CNTCLR_OFFSET)
#  define PIC32MZ_TIMER8_CNTSET     (PIC32MZ_TIMER8_K1BASE+PIC32MZ_TIMER_CNTSET_OFFSET)
#  define PIC32MZ_TIMER8_CNTINV     (PIC32MZ_TIMER8_K1BASE+PIC32MZ_TIMER_CNTINV_OFFSET)
#  define PIC32MZ_TIMER8_PR         (PIC32MZ_TIMER8_K1BASE+PIC32MZ_TIMER_PR_OFFSET)
#  define PIC32MZ_TIMER8_PRCLR      (PIC32MZ_TIMER8_K1BASE+PIC32MZ_TIMER_PRCLR_OFFSET)
#  define PIC32MZ_TIMER8_PRSET      (PIC32MZ_TIMER8_K1BASE+PIC32MZ_TIMER_PRSET_OFFSET)
#  define PIC32MZ_TIMER8_PRINV      (PIC32MZ_TIMER8_K1BASE+PIC32MZ_TIMER_PRINV_OFFSET)
#endif

#if CHIP_NTIMERS > 8
#  define PIC32MZ_TIMER9_CON        (PIC32MZ_TIMER9_K1BASE+PIC32MZ_TIMER_CON_OFFSET)
#  define PIC32MZ_TIMER9_CONCLR     (PIC32MZ_TIMER9_K1BASE+PIC32MZ_TIMER_CONCLR_OFFSET)
#  define PIC32MZ_TIMER9_CONSET     (PIC32MZ_TIMER9_K1BASE+PIC32MZ_TIMER_CONSET_OFFSET)
#  define PIC32MZ_TIMER9_CONINV     (PIC32MZ_TIMER9_K1BASE+PIC32MZ_TIMER_CONINV_OFFSET)
#  define PIC32MZ_TIMER9_CNT        (PIC32MZ_TIMER9_K1BASE+PIC32MZ_TIMER_CNT_OFFSET)
#  define PIC32MZ_TIMER9_CNTCLR     (PIC32MZ_TIMER9_K1BASE+PIC32MZ_TIMER_CNTCLR_OFFSET)
#  define PIC32MZ_TIMER9_CNTSET     (PIC32MZ_TIMER9_K1BASE+PIC32MZ_TIMER_CNTSET_OFFSET)
#  define PIC32MZ_TIMER9_CNTINV     (PIC32MZ_TIMER9_K1BASE+PIC32MZ_TIMER_CNTINV_OFFSET)
#  define PIC32MZ_TIMER9_PR         (PIC32MZ_TIMER9_K1BASE+PIC32MZ_TIMER_PR_OFFSET)
#  define PIC32MZ_TIMER9_PRCLR      (PIC32MZ_TIMER9_K1BASE+PIC32MZ_TIMER_PRCLR_OFFSET)
#  define PIC32MZ_TIMER9_PRSET      (PIC32MZ_TIMER9_K1BASE+PIC32MZ_TIMER_PRSET_OFFSET)
#  define PIC32MZ_TIMER9_PRINV      (PIC32MZ_TIMER9_K1BASE+PIC32MZ_TIMER_PRINV_OFFSET)
#endif

/* Register Bit-Field Definitions *******************************************/

/* Timer control register */

#define TIMER_CON_TCS               (1 << 1)  /* Bit 1: Timer clock source select (all) */
#define TIMER1_CON_TSYNC            (1 << 2)  /* Bit 2: Timer external clock input synchronization selection (timer 1 only) */
#define TIMER_CON_T32               (1 << 3)  /* Bit 3: 32-bit timer mode select (even timers only) */
#define TIMER_CON_TCKPS_SHIFT       (4)       /* Bits 4-6:  Timer input clock prescale select (all except timer 1) */
#define TIMER_CON_TCKPS_MASK        (7 << TIMER_CON_TCKPS_SHIFT)
#  define TIMER_CON_TCKPS_1         (0 << TIMER_CON_TCKPS_SHIFT) /* 1:1 prescale value */
#  define TIMER_CON_TCKPS_2         (1 << TIMER_CON_TCKPS_SHIFT) /* 1:2 prescale value */
#  define TIMER_CON_TCKPS_4         (2 << TIMER_CON_TCKPS_SHIFT) /* 1:4 prescale value */
#  define TIMER_CON_TCKPS_8         (3 << TIMER_CON_TCKPS_SHIFT) /* 1:8 prescale value */
#  define TIMER_CON_TCKPS_16        (4 << TIMER_CON_TCKPS_SHIFT) /* 1:16 prescale value */
#  define TIMER_CON_TCKPS_32        (5 << TIMER_CON_TCKPS_SHIFT) /* 1:32 prescale value */
#  define TIMER_CON_TCKPS_64        (6 << TIMER_CON_TCKPS_SHIFT) /* 1:64 prescale value */
#  define TIMER_CON_TCKPS_256       (7 << TIMER_CON_TCKPS_SHIFT) /* 1:256 prescale value */

#define TIMER1_CON_TCKPS_SHIFT      (4)       /* Bits 4-5:  Timer input clock prescale select (timer 1 only) */
#define TIMER1_CON_TCKPS_MASK       (3 << TIMER1_CON_TCKPS_SHIFT)
#  define TIMER1_CON_TCKPS_1        (0 << TIMER1_CON_TCKPS_SHIFT) /* 1:1 prescale value */
#  define TIMER1_CON_TCKPS_8        (1 << TIMER1_CON_TCKPS_SHIFT) /* 1:8 prescale value */
#  define TIMER1_CON_TCKPS_64       (2 << TIMER1_CON_TCKPS_SHIFT) /* 1:64 prescale value */
#  define TIMER1_CON_TCKPS_256      (3 << TIMER1_CON_TCKPS_SHIFT) /* 1:256 prescale value */

#define TIMER_CON_TGATE             (1 << 7)  /* Bit 7: Timer gated time accumulation enable (all) */
#define TIMER1_CON_TWIP             (1 << 11) /* Bit 11: Asynchronous timer write in progress (timer 1 only) */
#define TIMER1_CON_TWDIS            (1 << 12) /* Bit 12: Asynchronous timer write disable (timer 1 only) */
#define TIMER_CON_SIDL              (1 << 13) /* Bit 13: Stop in idle mode (all) */
#define TIMER_CON_ON                (1 << 15) /* Bit 15: Timer on (all) */

/* Timer count register */

#define TIMER_CNT_MASK  0xffff /* 16-bit timer counter value */

/* Timer period register */

#define TIMER_PR_MASK    0xffff /* 16-bit timer period value */

/****************************************************************************
 * Public Types
 ****************************************************************************/

#ifndef __ASSEMBLY__

/****************************************************************************
 * Inline Functions
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/

#ifdef __cplusplus
#define EXTERN extern "C"
extern "C"
{
#else
#define EXTERN extern
#endif

#undef EXTERN
#ifdef __cplusplus
}
#endif

#endif /* __ASSEMBLY__ */
#endif /* CHIP_NTIMERS > 0 */
#endif /* __ARCH_MIPS_SRC_PIC32MZ_HARDWARE_PIC32MZ_TIMER_H */
