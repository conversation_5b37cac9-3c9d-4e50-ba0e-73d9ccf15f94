############################################################################
# arch/mips/src/pic32mx/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

# The start-up, "head", file

HEAD_ASRC = pic32mx_head.S

# Common MIPS files

CMN_ASRCS  = mips_syscall0.S vfork.S
CMN_CSRCS  = mips_allocateheap.c mips_assert.c mips_blocktask.c mips_copystate.c
CMN_CSRCS += mips_createstack.c mips_doirq.c mips_exit.c mips_initialize.c
CMN_CSRCS += mips_initialstate.c mips_irq.c mips_lowputs.c
CMN_CSRCS += mips_mdelay.c mips_modifyreg8.c mips_modifyreg16.c mips_modifyreg32.c
CMN_CSRCS += mips_nputs.c mips_releasepending.c mips_releasestack.c
CMN_CSRCS += mips_reprioritizertr.c mips_schedulesigaction.c mips_sigdeliver.c
CMN_CSRCS += mips_stackframe.c mips_swint0.c mips_udelay.c mips_unblocktask.c
CMN_CSRCS += mips_usestack.c mips_vfork.c

# Configuration dependent common files

ifneq ($(CONFIG_ARCH_IDLE_CUSTOM),y)
CMN_CSRCS += mips_idle.c
endif

# Use of common/mips_etherstub.c is deprecated.  The preferred mechanism is to
# use CONFIG_NETDEV_LATEINIT=y to suppress the call to up_netinitialize() in
# up_initialize().  Then this stub would not be needed.

ifneq ($(CONFIG_PIC32MX_ETHERNET),y)
ifeq ($(CONFIG_NET),y)
CMN_CSRCS += mips_etherstub.c
endif
endif

ifeq ($(CONFIG_ARCH_STACKDUMP),y)
CMN_CSRCS += mips_dumpstate.c
endif

# Required PIC32MX files

CHIP_CSRCS  = pic32mx_irq.c pic32mx_decodeirq.c pic32mx_exception.c
CHIP_CSRCS += pic32mx_gpio.c pic32mx_lowconsole.c pic32mx_lowinit.c
CHIP_CSRCS += pic32mx_serial.c pic32mx_spi.c

# Configuration-dependent PIC32MX files

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += pic32mx_timerisr.c
endif

ifeq ($(CONFIG_PIC32MX_GPIOIRQ),y)
CHIP_CSRCS += pic32mx_gpioirq.c
endif

ifeq ($(CONFIG_PIC32MX_USBDEV),y)
CHIP_CSRCS += pic32mx_usbdev.c
endif

ifeq ($(CONFIG_PIC32MX_ETHERNET),y)
CHIP_CSRCS += pic32mx_ethernet.c
endif
