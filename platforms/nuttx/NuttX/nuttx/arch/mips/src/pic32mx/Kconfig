#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

if ARCH_CHIP_PIC32MX
comment "PIC32MX Configuration Options"

choice
	prompt "PIC32MX chip selection"
	default ARCH_CHIP_PIC32MX460F512L

config ARCH_CHIP_PIC32MX110F016B
	bool "PIC32MX110F016B"
	select ARCH_CHIP_PIC32MX1
	---help---
		Microchip PIC32MX110F016B (MPS32 M4K)
		ARCH_CHIP_PIC32MX1

config ARCH_CHIP_PIC32MX110F016C
	bool "PIC32MX110F016C"
	select ARCH_CHIP_PIC32MX1
	---help---
		<PERSON>chip PIC32MX110F016C (MPS32 M4K)

config ARCH_CHIP_PIC32MX110F016D
	bool "PIC32MX110F016D"
	select ARCH_CHIP_PIC32MX1
	---help---
		Microchip PIC32MX110F016D (MPS32 M4K)

config ARCH_CHIP_PIC32MX120F032B
	bool "PIC32MX120F032B"
	select ARCH_CHIP_PIC32MX1
	---help---
		<PERSON>chip PIC32MX120F032B (MPS32 M4K)

config ARCH_CHIP_PIC32MX120F032C
	bool "PIC32MX120F032C"
	select ARCH_CHIP_PIC32MX1
	---help---
		Microchip PIC32MX120F032C (MPS32 M4K)

config ARCH_CHIP_PIC32MX120F032D
	bool "PIC32MX120F032D"
	select ARCH_CHIP_PIC32MX1
	---help---
		Microchip PIC32MX120F032D (MPS32 M4K)

config ARCH_CHIP_PIC32MX130F064B
	bool "PIC32MX130F064B"
	select ARCH_CHIP_PIC32MX1
	---help---
		Microchip PIC32MX130F064B (MPS32 M4K)

config ARCH_CHIP_PIC32MX130F064C
	bool "PIC32MX130F064C"
	select ARCH_CHIP_PIC32MX1
	---help---
		Microchip PIC32MX130F064C (MPS32 M4K)

config ARCH_CHIP_PIC32MX130F064D
	bool "PIC32MX130F064D"
	select ARCH_CHIP_PIC32MX1
	---help---
		Microchip PIC32MX130F064D (MPS32 M4K)

config ARCH_CHIP_PIC32MX150F128B
	bool "PIC32MX150F128B"
	select ARCH_CHIP_PIC32MX1
	---help---
		Microchip PIC32MX150F128B (MPS32 M4K)

config ARCH_CHIP_PIC32MX150F128C
	bool "PIC32MX150F128C"
	select ARCH_CHIP_PIC32MX1
	---help---
		Microchip PIC32MX150F128C (MPS32 M4K)

config ARCH_CHIP_PIC32MX150F128D
	bool "PIC32MX150F128D"
	select ARCH_CHIP_PIC32MX1
	---help---
		Microchip PIC32MX150F128D (MPS32 M4K)

config ARCH_CHIP_PIC32MX210F016B
	bool "PIC32MX210F016B"
	select ARCH_CHIP_PIC32MX2
	---help---
		Microchip PIC32MX210F016B (MPS32 M4K)

config ARCH_CHIP_PIC32MX210F016C
	bool "PIC32MX210F016C"
	select ARCH_CHIP_PIC32MX2
	---help---
		Microchip PIC32MX210F016C (MPS32 M4K)

config ARCH_CHIP_PIC32MX210F016D
	bool "PIC32MX210F016D"
	select ARCH_CHIP_PIC32MX2
	---help---
		Microchip PIC32MX210F016D (MPS32 M4K)

config ARCH_CHIP_PIC32MX220F032B
	bool "PIC32MX220F032B"
	select ARCH_CHIP_PIC32MX2
	---help---
		Microchip PIC32MX220F032B (MPS32 M4K)

config ARCH_CHIP_PIC32MX220F032C
	bool "PIC32MX220F032C"
	select ARCH_CHIP_PIC32MX2
	---help---
		Microchip PIC32MX220F032C (MPS32 M4K)

config ARCH_CHIP_PIC32MX220F032D
	bool "PIC32MX220F032D"
	select ARCH_CHIP_PIC32MX2
	---help---
		Microchip PIC32MX220F032D (MPS32 M4K)

config ARCH_CHIP_PIC32MX230F064B
	bool "PIC32MX230F064B"
	select ARCH_CHIP_PIC32MX2
	---help---
		Microchip PIC32MX230F064B (MPS32 M4K)

config ARCH_CHIP_PIC32MX230F064C
	bool "PIC32MX230F064C"
	select ARCH_CHIP_PIC32MX2
	---help---
		Microchip PIC32MX230F064C (MPS32 M4K)

config ARCH_CHIP_PIC32MX230F064D
	bool "PIC32MX230F064D"
	select ARCH_CHIP_PIC32MX2
	---help---
		Microchip PIC32MX230F064D (MPS32 M4K)

config ARCH_CHIP_PIC32MX250F128B
	bool "PIC32MX250F128B"
	select ARCH_CHIP_PIC32MX2
	---help---
		Microchip PIC32MX250F128B (MPS32 M4K)

config ARCH_CHIP_PIC32MX250F128C
	bool "PIC32MX250F128C"
	select ARCH_CHIP_PIC32MX2
	---help---
		Microchip PIC32MX250F128C (MPS32 M4K)

config ARCH_CHIP_PIC32MX250F128D
	bool "PIC32MX250F128D"
	select ARCH_CHIP_PIC32MX2
	---help---
		Microchip PIC32MX250F128D (MPS32 M4K)

config ARCH_CHIP_PIC32MX320F032H
	bool "PIC32MX320F032H"
	select ARCH_CHIP_PIC32MX3
	---help---
		Microchip PIC32MX320F032H (MPS32 M4K)

config ARCH_CHIP_PIC32MX320F064H
	bool "PIC32MX320F064H"
	select ARCH_CHIP_PIC32MX3
	---help---
		Microchip PIC32MX320F064H (MPS32 M4K)

config ARCH_CHIP_PIC32MX320F128H
	bool "PIC32MX320F128H"
	select ARCH_CHIP_PIC32MX3
	---help---
		Microchip PIC32MX320F128H (MPS32 M4K)

config ARCH_CHIP_PIC32MX320F128L
	bool "PIC32MX320F128L"
	select ARCH_CHIP_PIC32MX3
	---help---
		Microchip PIC32MX320F128L (MPS32 M4K)

config ARCH_CHIP_PIC32MX340F128H
	bool "PIC32MX340F128H"
	select ARCH_CHIP_PIC32MX3
	---help---
		Microchip PIC32MX340F128H (MPS32 M4K)

config ARCH_CHIP_PIC32MX340F256H
	bool "PIC32MX340F256H"
	select ARCH_CHIP_PIC32MX3
	---help---
		Microchip PIC32MX340F256H (MPS32 M4K)

config ARCH_CHIP_PIC32MX340F512H
	bool "PIC32MX340F512H"
	select ARCH_CHIP_PIC32MX3
	---help---
		Microchip PIC32MX340F512H (MPS32 M4K)

config ARCH_CHIP_PIC32MX340F128L
	bool "PIC32MX340F128L"
	select ARCH_CHIP_PIC32MX3
	---help---
		Microchip PIC32MX340F128L (MPS32 M4K)

config ARCH_CHIP_PIC32MX360F256L
	bool "PIC32MX360F256L"
	select ARCH_CHIP_PIC32MX3
	---help---
		Microchip PIC32MX360F256L (MPS32 M4K)

config ARCH_CHIP_PIC32MX360F512L
	bool "PIC32MX360F512L"
	select ARCH_CHIP_PIC32MX3
	---help---
		Microchip PIC32MX360F512L (MPS32 M4K)

config ARCH_CHIP_PIC32MX420F032H
	bool "PIC32MX420F032H"
	select ARCH_CHIP_PIC32MX4
	---help---
		Microchip PIC32MX420F032H (MPS32 M4K)

config ARCH_CHIP_PIC32MX440F128H
	bool "PIC32MX440F128H"
	select ARCH_CHIP_PIC32MX4
	---help---
		Microchip PIC32MX440F128H (MPS32 M4K)

config ARCH_CHIP_PIC32MX440F128L
	bool "PIC32MX440F128L"
	select ARCH_CHIP_PIC32MX4
	---help---
		Microchip PIC32MX440F128L (MPS32 M4K)

config ARCH_CHIP_PIC32MX440F256H
	bool "PIC32MX440F256H"
	select ARCH_CHIP_PIC32MX4
	---help---
		Microchip PIC32MX440F256H (MPS32 M4K)

config ARCH_CHIP_PIC32MX440F512H
	bool "PIC32MX440F512H"
	select ARCH_CHIP_PIC32MX4
	---help---
		Microchip PIC32MX440F512H (MPS32 M4K)

config ARCH_CHIP_PIC32MX460F256L
	bool "PIC32MX460F256L"
	select ARCH_CHIP_PIC32MX4
	---help---
		Microchip PIC32MX460F256L (MPS32 M4K)

config ARCH_CHIP_PIC32MX460F512L
	bool "PIC32MX460F512L"
	select ARCH_CHIP_PIC32MX4
	---help---
		Microchip PIC32MX460F512L (MPS32 M4K)

config ARCH_CHIP_PIC32MX534F064H
	bool "PIC32MX534F064H"
	select ARCH_CHIP_PIC32MX5
	---help---
		Microchip PIC32MX534F064H (MPS32 M4K)

config ARCH_CHIP_PIC32MX534F064L
	bool "PIC32MX534F064L"
	select ARCH_CHIP_PIC32MX5
	---help---
		Microchip PIC32MX534F064L (MPS32 M4K)

config ARCH_CHIP_PIC32MX564F064H
	bool "PIC32MX564F064H"
	select ARCH_CHIP_PIC32MX5
	---help---
		Microchip PIC32MX564F064H (MPS32 M4K)

config ARCH_CHIP_PIC32MX564F064L
	bool "PIC32MX564F064L"
	select ARCH_CHIP_PIC32MX5
	---help---
		Microchip PIC32MX564F064L (MPS32 M4K)

config ARCH_CHIP_PIC32MX564F128H
	bool "PIC32MX564F128H"
	select ARCH_CHIP_PIC32MX5
	---help---
		Microchip PIC32MX564F128H (MPS32 M4K)

config ARCH_CHIP_PIC32MX564F128L
	bool "PIC32MX564F128L"
	select ARCH_CHIP_PIC32MX5
	---help---
		Microchip PIC32MX564F128L (MPS32 M4K)

config ARCH_CHIP_PIC32MX575F256H
	bool "PIC32MX575F256H"
	select ARCH_CHIP_PIC32MX5
	---help---
		Microchip PIC32MX575F256H (MPS32 M4K)

config ARCH_CHIP_PIC32MX575F256L
	bool "PIC32MX575F256L"
	select ARCH_CHIP_PIC32MX5
	---help---
		Microchip PIC32MX575F256L (MPS32 M4K)

config ARCH_CHIP_PIC32MX575F512H
	bool "PIC32MX575F512H"
	select ARCH_CHIP_PIC32MX5
	---help---
		Microchip PIC32MX575F512H (MPS32 M4K)

config ARCH_CHIP_PIC32MX575F512L
	bool "PIC32MX575F512L"
	select ARCH_CHIP_PIC32MX5
	---help---
		Microchip PIC32MX575F512L (MPS32 M4K)

config ARCH_CHIP_PIC32MX664F064H
	bool "PIC32MX664F064H"
	select ARCH_CHIP_PIC32MX6
	---help---
		Microchip PIC32MX664F064H (MPS32 M4K)

config ARCH_CHIP_PIC32MX664F064L
	bool "PIC32MX664F064L"
	select ARCH_CHIP_PIC32MX6
	---help---
		Microchip PIC32MX664F064L (MPS32 M4K)

config ARCH_CHIP_PIC32MX664F128H
	bool "PIC32MX664F128H"
	select ARCH_CHIP_PIC32MX6
	---help---
		Microchip PIC32MX664F128H (MPS32 M4K)

config ARCH_CHIP_PIC32MX664F128L
	bool "PIC32MX664F128L"
	select ARCH_CHIP_PIC32MX6
	---help---
		Microchip PIC32MX664F128L (MPS32 M4K)

config ARCH_CHIP_PIC32MX675F256H
	bool "PIC32MX675F256H"
	select ARCH_CHIP_PIC32MX6
	---help---
		Microchip PIC32MX675F256H (MPS32 M4K)

config ARCH_CHIP_PIC32MX675F256L
	bool "PIC32MX675F256L"
	select ARCH_CHIP_PIC32MX6
	---help---
		Microchip PIC32MX675F256L (MPS32 M4K)

config ARCH_CHIP_PIC32MX675F512H
	bool "PIC32MX675F512H"
	select ARCH_CHIP_PIC32MX6
	---help---
		Microchip PIC32MX675F512H (MPS32 M4K)

config ARCH_CHIP_PIC32MX675F512L
	bool "PIC32MX675F512L"
	select ARCH_CHIP_PIC32MX6
	---help---
		Microchip PIC32MX675F512L (MPS32 M4K)

config ARCH_CHIP_PIC32MX695F512H
	bool "PIC32MX695F512H"
	select ARCH_CHIP_PIC32MX6
	---help---
		Microchip PIC32MX695F512H (MPS32 M4K)

config ARCH_CHIP_PIC32MX695F512L
	bool "PIC32MX695F512L"
	select ARCH_CHIP_PIC32MX6
	---help---
		Microchip PIC32MX695F512L (MPS32 M4K)

config ARCH_CHIP_PIC32MX764F128H
	bool "PIC32MX764F128H"
	select ARCH_CHIP_PIC32MX7
	---help---
		Microchip PIC32MX764F128H (MPS32 M4K)

config ARCH_CHIP_PIC32MX764F128L
	bool "PIC32MX764F128L"
	select ARCH_CHIP_PIC32MX7
	---help---
		Microchip PIC32MX764F128L (MPS32 M4K)

config ARCH_CHIP_PIC32MX775F256H
	bool "PIC32MX775F256H"
	select ARCH_CHIP_PIC32MX7
	---help---
		Microchip PIC32MX775F256H (MPS32 M4K)

config ARCH_CHIP_PIC32MX775F256L
	bool "PIC32MX775F256L"
	select ARCH_CHIP_PIC32MX7
	---help---
		Microchip PIC32MX775F256L (MPS32 M4K)

config ARCH_CHIP_PIC32MX775F512H
	bool "PIC32MX775F512H"
	select ARCH_CHIP_PIC32MX7
	---help---
		Microchip PIC32MX775F512H (MPS32 M4K)

config ARCH_CHIP_PIC32MX775F512L
	bool "PIC32MX775F512L"
	select ARCH_CHIP_PIC32MX7
	---help---
		Microchip PIC32MX775F512L (MPS32 M4K)

config ARCH_CHIP_PIC32MX795F512H
	bool "PIC32MX795F512H"
	select ARCH_CHIP_PIC32MX7
	---help---
		Microchip PIC32MX795F512H (MPS32 M4K)

config ARCH_CHIP_PIC32MX795F512L
	bool "PIC32MX795F512L"
	select ARCH_CHIP_PIC32MX7
	---help---
		Microchip PIC32MX795F512L (MPS32 M4K)

endchoice

config ARCH_CHIP_PIC32MX1
	bool
	default n
	select ARCH_MIPS_M4K

config ARCH_CHIP_PIC32MX2
	bool
	default n
	select ARCH_MIPS_M4K

config ARCH_CHIP_PIC32MX3
	bool
	default n
	select ARCH_MIPS_M4K

config ARCH_CHIP_PIC32MX4
	bool
	default n
	select ARCH_MIPS_M4K

config ARCH_CHIP_PIC32MX5
	bool
	default n
	select ARCH_MIPS_M4K

config ARCH_CHIP_PIC32MX6
	bool
	default n
	select ARCH_MIPS_M4K

config ARCH_CHIP_PIC32MX7
	bool
	default n
	select ARCH_MIPS_M4K

config PIC32MX_MVEC
	bool
	default n

config PIC32MX_SPI
	bool
	default n

config PIC32MX_T1
	bool
	default y

menu "PIC32MX Peripheral Support"

config PIC32MX_WDT
	bool "Watchdog timer (WDT)"
	default n

config PIC32MX_T2
	bool "Timer 2 (T2)"
	default n

config PIC32MX_T3
	bool "Timer 3 (T3)"
	default n

config PIC32MX_T4
	bool "Timer 4 (T4)"
	default n

config PIC32MX_T5
	bool "Timer 5 (T5)"
	default n

config PIC32MX_IC1
	bool "Input Capture 1 (IC1)"
	default n

config PIC32MX_IC2
	bool "Input Capture 2 (IC2)"
	default n

config PIC32MX_IC3
	bool "Input Capture 3 (IC3)"
	default n

config PIC32MX_IC4
	bool "Input Capture 4 (IC4)"
	default n

config PIC32MX_IC5
	bool "Input Capture 5 (IC5)"
	default n

config PIC32MX_OC1
	bool "Output Compare 1 (OC1)"
	default n

config PIC32MX_OC2
	bool "Output Compare 2 (OC2)"
	default n

config PIC32MX_OC3
	bool "Output Compare 3 (OC3)"
	default n

config PIC32MX_OC4
	bool "Output Compare 4 (OC4)"
	default n

config PIC32MX_OC5
	bool "Output Compare 5 (OC5)"
	default n

config PIC32MX_I2C1
	bool "I2C1"
	default n

config PIC32MX_I2C2
	bool "I2C2"
	default n

config PIC32MX_I2C3
	bool "I2C3"
	default n

config PIC32MX_I2C4
	bool "I2C4"
	default n

config PIC32MX_I2C5
	bool "I2C5"
	default n

config PIC32MX_SPI1
	bool "SPI1"
	default n
	select PIC32MX_SPI

config PIC32MX_SPI2
	bool "SPI2"
	default n
	select PIC32MX_SPI

config PIC32MX_SPI3
	bool "SPI3"
	default n
	select PIC32MX_SPI

config PIC32MX_SPI4
	bool "SPI4"
	default n
	select PIC32MX_SPI

config PIC32MX_UART1
	bool "UART1"
	default n
	select UART1_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config PIC32MX_UART2
	bool "UART2"
	default n
	select UART2_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config PIC32MX_UART3
	bool "UART3"
	default n
	select UART3_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config PIC32MX_UART4
	bool "UART4"
	default n
	select UART4_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config PIC32MX_UART5
	bool "UART5"
	default n
	select UART5_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config PIC32MX_UART6
	bool "UART6"
	default n
	select UART6_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config PIC32MX_ADC
	bool "ADC1"
	default n

config PIC32MX_PMP
	bool "Parallel Master Port (PMP)"
	default n

config PIC32MX_CM1
	bool "Comparator 1 (CM1)"
	default n

config PIC32MX_CM2
	bool "Comparator 2 (CM2)"
	default n

config PIC32MX_CM3
	bool "Comparator 3 (CM3)"
	default n

config PIC32MX_RTCC
	bool "Real-Time Clock and Calendar (RTCC)"
	default n

config PIC32MX_DMA
	bool "DMA"
	default n
	select ARCH_DMA

config PIC32MX_FLASH
	bool "FLASH"
	default n

config PIC32MX_USBDEV
	bool "USB device"
	default n

config PIC32MX_USBHOST
	bool "USB host"
	default n

config PIC32MX_CAN1
	bool "Controller area network 1 (CAN1)"
	default n

config PIC32MX_CAN2
	bool "Controller area network 2 (CAN2)"
	default n

config PIC32MX_ETHERNET
	bool "Ethernet"
	default n
	select NETDEVICES
	select ARCH_HAVE_PHY
	select ARCH_HAVE_NETDEV_STATISTICS

config PIC32MX_CTMU
	bool "Charge Time Measurement Unit (CMTU)"
	default n

endmenu

menu "PIC32MX Peripheral Interrupt Priorities"

config PIC32MX_CTPRIO
	int "Core Timer Interrupt (CT)"
	default 16
	---help---
		Core Timer Interrupt.  Range 4-31, Default 16.

config PIC32MX_CS0PRIO
	int "Core Software Interrupt 0 (CS0)"
	default 16
	---help---
		Core Software Interrupt 0.  Range 4-31, Default 16.

config PIC32MX_CS1PRIO
	int "Core Software Interrupt 1 (CS1)"
	default 16
	---help---
		Core Software Interrupt 1.  Range 4-31, Default 16.

config PIC32MX_INT0PRIO
	int "External Interrupt 0 (INT0)"
	default 16
	---help---
		External Interrupt 0.  Range 4-31, Default 16.

config PIC32MX_INT1PRIO
	int "External Interrupt 1 (INT1)"
	default 16
	---help---
		External Interrupt 1.  Range 4-31, Default 16.

config PIC32MX_INT2PRIO
	int "External Interrupt 2 (INT2)"
	default 16
	---help---
		External Interrupt 2.  Range 4-31, Default 16.

config PIC32MX_INT3PRIO
	int "External Interrupt 3 (INT3)"
	default 16
	---help---
		External Interrupt 3.  Range 4-31, Default 16.

config PIC32MX_INT4PRIO
	int "External Interrupt 4 (INT4)"
	default 16
	---help---
		External Interrupt 4.  Range 4-31, Default 16.

config PIC32MX_FSCMPRIO
	int "Fail-Safe Clock Monitor (FSCM)"
	default 16
	depends on PIC32MX_
	---help---
		Fail-Safe Clock Monitor.  Range 4-31, Default 16.

config PIC32MX_T1PRIO
	int "Timer 1 (T1)"
	default 16
	---help---
		Timer 1 (System timer) priority.  Range 4-31, Default 16.

config PIC32MX_T2PRIO
	int "Timer 2 (T2)"
	default 16
	depends on PIC32MX_T2
	---help---
		Timer 2 priority.  Range 4-31, Default 16.

config PIC32MX_T3PRIO
	int "Timer 3 (T3)"
	default 16
	depends on PIC32MX_T3
	---help---
		Timer 3 priority.  Range 4-31, Default 16.

config PIC32MX_T4PRIO
	int "Timer 4 (T4)"
	default 16
	depends on PIC32MX_T4
	---help---
		Timer 4 priority.  Range 4-31, Default 16.

config PIC32MX_T5PRIO
	int "Timer 5 (T5)"
	default 16
	depends on PIC32MX_
	---help---
		Timer 5 priority.  Range 4-31, Default 16.

config PIC32MX_IC1PRIO
	int "Input Capture 1 (IC1)"
	default 16
	depends on PIC32MX_IC1
	---help---
		Input Capture 1.  Range 4-31, Default 16.

config PIC32MX_IC2PRIO
	int "Input Capture 2 (IC2)"
	default 16
	depends on PIC32MX_IC2
	---help---
		Input Capture 2.  Range 4-31, Default 16.

config PIC32MX_IC3PRIO
	int "Input Capture 3 (IC3)"
	default 16
	depends on PIC32MX_IC3
	---help---
		Input Capture 3.  Range 4-31, Default 16.

config PIC32MX_IC4PRIO
	int "Input Capture 4 (IC4)"
	default 16
	depends on PIC32MX_IC4
	---help---
		Input Capture 4.  Range 4-31, Default 16.

config PIC32MX_IC5PRIO
	int "Input Capture 5 (IC5)"
	default 16
	depends on PIC32MX_IC5
	---help---
		Input Capture 5.  Range 4-31, Default 16.

config PIC32MX_OC1PRIO
	int "Output Compare 1 (OC1)"
	default 16
	depends on PIC32MX_OC1
	---help---
		Output Compare 1.  Range 4-31, Default 16.

config PIC32MX_OC2PRIO
	int "Output Compare 2 (OC2)"
	default 16
	depends on PIC32MX_OC2
	---help---
		Output Compare 2.  Range 4-31, Default 16.

config PIC32MX_OC3PRIO
	int "Output Compare 3 (OC3)"
	default 16
	depends on PIC32MX_OC3
	---help---
		Output Compare 3.  Range 4-31, Default 16.

config PIC32MX_OC4PRIO
	int "Output Compare 4 (OC4)"
	default 16
	depends on PIC32MX_OC4
	---help---
		Output Compare 4.  Range 4-31, Default 16.

config PIC32MX_OC5PRIO
	int "Output Compare 5 (OC5)"
	default 16
	depends on PIC32MX_OC5
	---help---
		Output Compare 5.  Range 4-31, Default 16.

config PIC32MX_I2C1PRIO
	int "I2C1"
	default 16
	depends on PIC32MX_I2C1
	---help---
		I2C 1.  Range 4-31, Default 16.

config PIC32MX_I2C2PRIO
	int "I2C2"
	default 16
	depends on PIC32MX_I2C3
	---help---
		I2C 2.  Range 4-31, Default 16.

config PIC32MX_I2C3PRIO
	int "I2C3"
	default 16
	depends on PIC32MX_I2C3
	---help---
		I2C 3.  Range 4-31, Default 16.

config PIC32MX_I2C4PRIO
	int "I2C4"
	default 16
	depends on PIC32MX_I2C4
	---help---
		I2C 4.  Range 4-31, Default 16.

config PIC32MX_I2C5PRIO
	int "I2C5"
	default 16
	depends on PIC32MX_I2C5
	---help---
		I2C 5.  Range 4-31, Default 16.

config PIC32MX_SPI1PRIO
	int "SPI1"
	default 16
	depends on PIC32MX_SPI1
	---help---
		SPI 2

config PIC32MX_SPI2PRIO
	int "SPI2"
	default 16
	depends on PIC32MX_SPI2
	---help---
		SPI 2

config PIC32MX_UART1PRIO
	int "UART1"
	default 16
	depends on PIC32MX_UART1
	---help---
		UART 1.  Range 4-31, Default 16.

config PIC32MX_UART2PRIO
	int "UART2"
	default 16
	depends on PIC32MX_UART2
	---help---
		UART 2.  Range 4-31, Default 16.

config PIC32MX_CNPRIO
	int "CN"
	default 16
	depends on PIC32MX_CN
	---help---
		Input Change Interrupt.  Range 4-31, Default 16.

config PIC32MX_ADCPRIO
	int "ADC1"
	default 16
	depends on PIC32MX_ADC1
	---help---
		ADC1 Convert Done.  Range 4-31, Default 16.

config PIC32MX_PMPPRIO
	int "Parallel Master Port (PMP)"
	default 16
	depends on PIC32MX_PMP
	---help---
		Parallel Master Port.  Range 4-31, Default 16.

config PIC32MX_CM1PRIO
	int "Comparator 1 (CM1)"
	default 16
	depends on PIC32MX_CM1
	---help---
		Comparator 1.  Range 4-31, Default 16.

config PIC32MX_CM2PRIO
	int "Comparator 2 (CM2)"
	default 16
	depends on PIC32MX_CM2
	---help---
		Comparator 2.  Range 4-31, Default 16.

config PIC32MX_RTCCPRIO
	int "Real-Time Clock and Calendar (RTCC)"
	default 16
	depends on PIC32MX_RTCC
	---help---
		Real-Time Clock and Calendar.  Range 4-31, Default 16.

config PIC32MX_DMA0PRIO
	int "DMA0"
	default 16
	depends on PIC32MX_DMA
	---help---
		DMA Channel 0.  Range 4-31, Default 16.

config PIC32MX_DMA1PRIO
	int "DMA1"
	default 16
	depends on PIC32MX_DMA
	---help---
		DMA Channel 1.  Range 4-31, Default 16.

config PIC32MX_DMA2PRIO
	int "DMA2"
	default 16
	depends on PIC32MX_DMA
	---help---
		DMA Channel 2.  Range 4-31, Default 16.

config PIC32MX_DMA3PRIO
	int "DMA3"
	default 16
	depends on PIC32MX_DMA
	---help---
		DMA Channel 3.  Range 4-31, Default 16.

config PIC32MX_DMA4PRIO
	int "DMA4"
	default 16
	depends on PIC32MX_DMA
	---help---
		DMA Channel 4.  Range 4-31, Default 16.

config PIC32MX_DMA5PRIO
	int "DMA5"
	default 16
	depends on PIC32MX_DMA
	---help---
		DMA Channel 5.  Range 4-31, Default 16.

config PIC32MX_DMA6PRIO
	int "DMA6"
	default 16
	depends on PIC32MX_DMA
	---help---
		DMA Channel 6.  Range 4-31, Default 16.

config PIC32MX_DMA7PRIO
	int "DMA7"
	default 16
	depends on PIC32MX_DMA
	---help---
		DMA Channel 7.  Range 4-31, Default 16.

config PIC32MX_FCEPRIO
	int "FCE"
	default 16
	depends on PIC32MX_FLASH
	---help---
		Flash Control Event.  Range 4-31, Default 16.

config PIC32MX_USBPRIO
	int "USB"
	default 16
	depends on PIC32MX_USBDEV || PIC32MX_USBHOST
	---help---
		USB.  Range 4-31, Default 16.

endmenu

config PIC32MX_GPIOIRQ
	bool "GPIO Interrupt"
	default n
	depends on EXPERIMENTAL
	---help---
		Build in support for interrupts based on GPIO inputs from IOPorts

menu "SPI Driver Configuration"
	depends on PIC32MX_SPI

config PIC32MX_SPI_INTERRUPTS
	bool "SPI Interrupt Driven"
	default n
	depends on EXPERIMENTAL

config PIC32MX_SPI_ENHBUF
	bool "SPI Enhanced Buffer Mode"
	default n
	depends on EXPERIMENTAL

config PIC32MX_SPI_REGDEBUG
	bool "SPI Register level debug"
	depends on DEBUG_INFO
	default n
	---help---
		Output detailed register-level SPI device debug information.
		Requires also CONFIG_DEBUG_FEATURES.

endmenu # SPI Driver Configuration

menu "PIC32MX PHY/Ethernet device driver settings"
	depends on PIC32MX_ETHERNET

config PIC32MX_PHY_AUTONEG
	bool "Auto-negotiation"
	default y
	depends on PIC32MX_ETHERNET
	---help---
		Enable auto-negotiation

config PIC32MX_PHY_SPEED100
	bool "100Mbps spped"
	default n
	depends on PIC32MX_ETHERNET && !PIC32MX_PHY_AUTONEG
	---help---
		Select 100Mbit vs. 10Mbit speed.

config PIC32MX_PHY_FDUPLEX
	bool "Full duplex"
	default n
	depends on PIC32MX_ETHERNET && !PIC32MX_PHY_AUTONEG
	---help---
		Select full (vs. half) duplex

config PIC32MX_ETH_NTXDESC
	int "Number Tx descriptors"
	default 2
	depends on PIC32MX_ETHERNET
	---help---
		Configured number of Tx descriptors. Default: 2

config PIC32MX_ETH_NRXDESC
	int "Number Rx descriptors"
	default 4
	depends on PIC32MX_ETHERNET
	---help---
		Configured number of Rx descriptors. Default: 4

config PIC32MX_ETH_PRIORITY
	int ""
	default 28
	depends on PIC32MX_ETHERNET
	---help---
		Ethernet interrupt priority.  The is default is the highest priority.

config PIC32MX_MULTICAST
	bool "Multicast"
	default y if NET_MCASTGROUP
	depends on PIC32MX_ETHERNET
	---help---
		Enable receipt of multicast (and unicast) frames. Automatically set if
		NET_MCASTGROUP is selected.

config NET_REGDEBUG
	bool "Register level debug"
	default n
	depends on PIC32MX_ETHERNET && DEBUG_NET_INFO
	---help---
		Enabled low level register debug.  Also needs CONFIG_DEBUG_FEATURES.

endmenu

menu "Device Configuration 0 (DEVCFG0)"

config PIC32MX_DEBUGGER
	int "Debugger"
	default 3
	---help---
		Background Debugger Enable. Default 3 (disabled). The value 2 enables.

config PIC32MX_ICESEL
	int "ICE channel"
	default 1
	---help---
		In-Circuit Emulator/Debugger Communication Channel Select. Default 1 (PG2)

config PIC32MX_PROGFLASHWP
	hex "Program FLASH write protect"
	default 0x3ff if ARCH_CHIP_PIC32MX1 || ARCH_CHIP_PIC32MX2
	default 0xff if !ARCH_CHIP_PIC32MX1 && !ARCH_CHIP_PIC32MX2
	---help---
		Program FLASH write protect.  Default 0xff (disabled)

config PIC32MX_BOOTFLASHWP
	int "Boot FLASH write protect"
	default 1
	---help---
		Default 1 (disabled)

config PIC32MX_CODEWP
	int "Code write protect"
	default 1
	---help---
		Default 1 (disabled)

endmenu

menu "Device Configuration 1 (DEVCFG1)"

config PIC32MX_OSCOUT
	int "USB ID"
	default 0
	depends on ARCH_CHIP_PIC32MX1 || ARCH_CHIP_PIC32MX2
	---help---
		USB USBID Selection.  Default 1 if USB enabled (USBID pin is controlled by the USB
		module), but 0 (GPIO) otherwise.

endmenu

menu "Device Configuration 3 (DEVCFG3)"

config PIC32MX_USBIDO
	int "USB ID"
	default 1 if PIC32MX_USB
	default 0 if !PIC32MX_USB
	---help---
		USB USBID Selection.  Default 1 if USB enabled (USBID pin is controlled by the USB
		module), but 0 (GPIO) otherwise.

config PIC32MX_VBUSIO
	int "USB VBUSON"
	default 1 if PIC32MX_USB
	default 0 if !PIC32MX_USB
	---help---
		USB VBUSON Selection (Default 1 if USB enabled (VBUSON pin is controlled by the USB
		module, but 0 (GPIO) otherwise.

config PIC32MX_WDENABLE
	bool "Watchdog enable"
	default 0
	---help---
		Enabled watchdog on power up.  Default 0 (watchdog can be enabled later by software).

config PIC32MX_FETHIO
	int "Ethernet I/O pins"
	default 1
	---help---
		Ethernet I/O Pin Selection bit:

		1 = Default Ethernet I/O Pins
		0 = Alternate Ethernet I/O Pins

config PIC32MX_FMIIEN
	int "Ethernet MII"
	default 1
	---help---
		Ethernet MII Enable bit

		1 = MII enabled
		0 = RMII enabled

endmenu

endif
