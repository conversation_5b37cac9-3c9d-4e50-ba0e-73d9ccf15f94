#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

if ARCH_HC

choice
	prompt "HC chip selection"
	default ARCH_CHIP_MCS92S12NEC64

config ARCH_CHIP_MCS92S12NEC64
	bool "MCS92S12NEC64"
	select <PERSON><PERSON>_HSC12
	select MM_SMALL
	---help---
		Motorola/Freescale MCS92S12NEC64 (M9S12)

config ARCH_CHIP_HC_CUSTOM
	bool "Custom HC chip"
	select ARCH_CHIP_CUSTOM
	---help---
		Select this option if there is no directory for the chip under arch/hc/src/.

endchoice

config ARCH_HSC12
	bool
	default n

config ARCH_CHIP
	string
	default "m9s12"		if ARCH_CHIP_MCS92S12NEC64

source "arch/hc/src/m9s12/Kconfig"

endif # ARCH_HC
