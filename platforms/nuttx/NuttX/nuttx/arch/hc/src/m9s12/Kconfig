#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

if <PERSON><PERSON>_HSC12
comment "M9S12 Configuration Options"

menu "M9S12 Peripheral Selection"

config HCS12_SCI0
	bool "SCI0"
	default n
	select SCI0_SERIALDRIVER

config HCS12_SCI1
	bool "SCI1"
	default n
	select SCI1_SERIALDRIVER

endmenu # M9S12 Peripheral Selection

menu "HSC12 Build Options"

config HCS12_SERIALMON
	bool "Freescale serial bootloader"
	default n
	---help---
		Indicates that the target systems uses the Freescale serial
		bootloader.

config HCS12_NONBANKED
	bool "Non-banked"
	default n
	---help---
		Indicates that the target systems does not support banking.
		Only short calls are made; one fixed page is presented in the
		paging window.  Only 48Kb of FLASH is usable in this configuration:
		pages 3e, 3d, then 3f will appear as a contiguous address space
		in memory.

endmenu # HSC12 Build Options

config HCS12_GPIOIRQ
	bool "GPIO interrupt support"
	default n
	depends on EXPERIMENTAL
	---help---
		Enable support for GPIO interrupts (not implemented)

endif # ARCH_HSC12
