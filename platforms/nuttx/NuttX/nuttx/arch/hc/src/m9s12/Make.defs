############################################################################
# arch/hc/src/m9s12/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

HEAD_ASRC = m9s12_vectors.S

CMN_CSRCS  = up_allocateheap.c up_blocktask.c up_copystate.c up_createstack.c
CMN_CSRCS += up_doirq.c up_exit.c up_idle.c up_initialize.c
CMN_CSRCS += up_mdelay.c up_modifyreg16.c up_modifyreg32.c up_modifyreg8.c
CMN_CSRCS += up_nputs.c up_releasepending.c up_releasestack.c up_reprioritizertr.c
CMN_CSRCS += up_stackframe.c up_udelay.c up_unblocktask.c up_usestack.c

CHIP_ASRCS  = m9s12_start.S m9s12_lowputc.S m9s12_saveusercontext.S
CHIP_CSRCS  = m9s12_assert.c m9s12_gpio.c m9s12_gpioirq.c m9s12_initialstate.c
CHIP_CSRCS += m9s12_irq.c m9s12_serial.c

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += m9s12_timerisr.c
endif

ifeq ($(CONFIG_DEBUG_GPIO_INFO),y)
CHIP_CSRCS+= m9s12_dumpgpio.c
endif
