############################################################################
# arch/arm/src/rp2040/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

include armv6-m/Make.defs

CHIP_CSRCS += rp2040_idle.c
CHIP_CSRCS += rp2040_irq.c
CHIP_CSRCS += rp2040_uart.c
CHIP_CSRCS += rp2040_serial.c
CHIP_CSRCS += rp2040_start.c
CHIP_CSRCS += rp2040_timerisr.c
CHIP_CSRCS += rp2040_gpio.c
CHIP_CSRCS += rp2040_pio.c
CHIP_CSRCS += rp2040_clock.c
CHIP_CSRCS += rp2040_xosc.c
CHIP_CSRCS += rp2040_pll.c

ifeq ($(CONFIG_SMP),y)
CHIP_CSRCS += rp2040_cpuindex.c
CHIP_CSRCS += rp2040_cpustart.c
CHIP_CSRCS += rp2040_cpupause.c
CHIP_CSRCS += rp2040_cpuidlestack.c
CHIP_CSRCS += rp2040_testset.c
CMN_ASRCS  := $(filter-out arm_testset.S,$(CMN_ASRCS))
endif

ifeq ($(CONFIG_RP2040_DMAC),y)
CHIP_CSRCS += rp2040_dmac.c
endif

ifeq ($(CONFIG_RP2040_SPI),y)
CHIP_CSRCS += rp2040_spi.c
endif

ifeq ($(CONFIG_RP2040_PWM),y)
CHIP_CSRCS += rp2040_pwm.c
endif

ifeq ($(CONFIG_RP2040_I2C),y)
CHIP_CSRCS += rp2040_i2c.c
endif

ifeq ($(CONFIG_RP2040_I2C_SLAVE),y)
CHIP_CSRCS += rp2040_i2c_slave.c
endif

ifeq ($(CONFIG_RP2040_I2S),y)
CHIP_CSRCS += rp2040_i2s.c
CHIP_CSRCS += rp2040_i2s_pio.c
endif

ifeq ($(CONFIG_USBDEV),y)
CHIP_CSRCS += rp2040_usbdev.c
endif

ifeq ($(CONFIG_WS2812),y)
CHIP_CSRCS += rp2040_ws2812.c
endif

ifeq ($(CONFIG_ADC),y)
CHIP_CSRCS += rp2040_adc.c
endif

ifeq ($(CONFIG_RP2040_FLASH_BOOT),y)
ifneq ($(PICO_SDK_PATH),)
include chip/boot2/Make.defs
endif
endif
