#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

if ARCH_CHIP_STM32L4

comment "STM32L4 Configuration Options"

choice
	prompt "STM32 L4 Chip Selection"
	default ARCH_CHIP_STM32L476RG
	depends on ARCH_CHIP_STM32L4

config ARCH_CHIP_STM32L412KB
	bool "STM32L412KB"
	select STM32L4_STM32L412XX
	select STM32L4_FLASH_CONFIG_B
	select STM32L4_IO_CONFIG_K
	---help---
		STM32 L4 Cortex M4, 128 Kb FLASH, 40 Kb SRAM

config ARCH_CHIP_STM32L432KB
	bool "STM32L432KB"
	select STM32L4_STM32L432XX
	select STM32L4_FLASH_CONFIG_B
	select STM32L4_IO_CONFIG_K
	---help---
		STM32 L4 Cortex M4, 128 Kb FLASH, 64 Kb SRAM

config ARCH_CHIP_STM32L432KC
	bool "STM32L432KC"
	select STM32L4_STM32L432X<PERSON>
	select STM32L4_FLASH_CONFIG_C
	select STM32L4_IO_CONFIG_K
	---help---
		STM32 L4 Cortex M4, 256 Kb FLASH, 64 Kb SRAM

config ARCH_CHIP_STM32L433CB
	bool "STM32L433CB"
	select STM32L4_STM32L433XX
	select STM32L4_FLASH_CONFIG_B
	select STM32L4_IO_CONFIG_C
	---help---
		STM32 L4 Cortex M4, 128 Kb FLASH, 64 Kb SRAM

config ARCH_CHIP_STM32L433CC
	bool "STM32L433CC"
	select STM32L4_STM32L433XX
	select STM32L4_FLASH_CONFIG_C
	select STM32L4_IO_CONFIG_C
	---help---
		STM32 L4 Cortex M4, 256 Kb FLASH, 64 Kb SRAM

config ARCH_CHIP_STM32L433RB
	bool "STM32L433RB"
	select STM32L4_STM32L433XX
	select STM32L4_FLASH_CONFIG_B
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, 128 Kb FLASH, 64 Kb SRAM

config ARCH_CHIP_STM32L433RC
	bool "STM32L433RC"
	select STM32L4_STM32L433XX
	select STM32L4_FLASH_CONFIG_C
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, 256 Kb FLASH, 64 Kb SRAM

config ARCH_CHIP_STM32L433VC
	bool "STM32L433VC"
	select STM32L4_STM32L433XX
	select STM32L4_FLASH_CONFIG_C
	select STM32L4_IO_CONFIG_V
	---help---
		STM32 L4 Cortex M4, 256 Kb FLASH, 64 Kb SRAM

config ARCH_CHIP_STM32L442KC
	bool "STM32L442KC"
	select STM32L4_STM32L442XX
	select STM32L4_FLASH_CONFIG_C
	select STM32L4_IO_CONFIG_K
	---help---
		STM32 L4 Cortex M4, AES, 256 Kb FLASH, 64 Kb SRAM

config ARCH_CHIP_STM32L443CC
	bool "STM32L443CC"
	select STM32L4_STM32L443XX
	select STM32L4_FLASH_CONFIG_C
	select STM32L4_IO_CONFIG_C
	---help---
		STM32 L4 Cortex M4, AES, 256 Kb FLASH, 64 Kb SRAM

config ARCH_CHIP_STM32L443RC
	bool "STM32L443RC"
	select STM32L4_STM32L443XX
	select STM32L4_FLASH_CONFIG_C
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, AES, 256 Kb FLASH, 64 Kb SRAM

config ARCH_CHIP_STM32L443VC
	bool "STM32L443VC"
	select STM32L4_STM32L443XX
	select STM32L4_FLASH_CONFIG_C
	select STM32L4_IO_CONFIG_V
	---help---
		STM32 L4 Cortex M4, AES, 256 Kb FLASH, 64 Kb SRAM

config ARCH_CHIP_STM32L451CC
	bool "STM32L451CC"
	select STM32L4_STM32L451XX
	select STM32L4_FLASH_CONFIG_C
	select STM32L4_IO_CONFIG_C
	---help---
		STM32 L4 Cortex M4, 256 Kb FLASH, 128+32 Kb SRAM

config ARCH_CHIP_STM32L451CE
	bool "STM32L451CE"
	select STM32L4_STM32L451XX
	select STM32L4_FLASH_CONFIG_E
	select STM32L4_IO_CONFIG_C
	---help---
		STM32 L4 Cortex M4, 512 Kb FLASH, 128+32 Kb SRAM

config ARCH_CHIP_STM32L451RC
	bool "STM32L451RC"
	select STM32L4_STM32L451XX
	select STM32L4_FLASH_CONFIG_C
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, 256 Kb FLASH, 128+32 Kb SRAM

config ARCH_CHIP_STM32L451RE
	bool "STM32L451RE"
	select STM32L4_STM32L451XX
	select STM32L4_FLASH_CONFIG_E
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, 512 Kb FLASH, 128+32 Kb SRAM

config ARCH_CHIP_STM32L451VC
	bool "STM32L451VC"
	select STM32L4_STM32L451XX
	select STM32L4_FLASH_CONFIG_C
	select STM32L4_IO_CONFIG_V
	---help---
		STM32 L4 Cortex M4, 256 Kb FLASH, 128+32 Kb SRAM

config ARCH_CHIP_STM32L451VE
	bool "STM32L451VE"
	select STM32L4_STM32L451XX
	select STM32L4_FLASH_CONFIG_E
	select STM32L4_IO_CONFIG_V
	---help---
		STM32 L4 Cortex M4, 512 Kb FLASH, 128+32 Kb SRAM

config ARCH_CHIP_STM32L452CC
	bool "STM32L452CC"
	select STM32L4_STM32L452XX
	select STM32L4_FLASH_CONFIG_C
	select STM32L4_IO_CONFIG_C
	---help---
		STM32 L4 Cortex M4, 256 Kb FLASH, 128+32 Kb SRAM

config ARCH_CHIP_STM32L452CE
	bool "STM32L452CE"
	select STM32L4_STM32L452XX
	select STM32L4_FLASH_CONFIG_E
	select STM32L4_IO_CONFIG_C
	---help---
		STM32 L4 Cortex M4, 512 Kb FLASH, 128+32 Kb SRAM

config ARCH_CHIP_STM32L452RC
	bool "STM32L452RC"
	select STM32L4_STM32L452XX
	select STM32L4_FLASH_CONFIG_C
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, 256 Kb FLASH, 128+32 Kb SRAM

config ARCH_CHIP_STM32L452RE
	bool "STM32L452RE"
	select STM32L4_STM32L452XX
	select STM32L4_FLASH_CONFIG_E
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, 512 Kb FLASH, 128+32 Kb SRAM

config ARCH_CHIP_STM32L452VC
	bool "STM32L452VC"
	select STM32L4_STM32L452XX
	select STM32L4_FLASH_CONFIG_C
	select STM32L4_IO_CONFIG_V
	---help---
		STM32 L4 Cortex M4, 256 Kb FLASH, 128+32 Kb SRAM

config ARCH_CHIP_STM32L452VE
	bool "STM32L452VE"
	select STM32L4_STM32L452XX
	select STM32L4_FLASH_CONFIG_E
	select STM32L4_IO_CONFIG_V
	---help---
		STM32 L4 Cortex M4, 512 Kb FLASH, 128+32 Kb SRAM

config ARCH_CHIP_STM32L462CE
	bool "STM32L462CE"
	select STM32L4_STM32L462XX
	select STM32L4_FLASH_CONFIG_E
	select STM32L4_IO_CONFIG_C
	---help---
		STM32 L4 Cortex M4, USB FS, AES, 512 Kb FLASH, 128+32 Kb SRAM

config ARCH_CHIP_STM32L462RE
	bool "STM32L462RE"
	select STM32L4_STM32L462XX
	select STM32L4_FLASH_CONFIG_E
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, USB FS, AES, 512 Kb FLASH, 128+32 Kb SRAM

config ARCH_CHIP_STM32L462VE
	bool "STM32L462VE"
	select STM32L4_STM32L462XX
	select STM32L4_FLASH_CONFIG_E
	select STM32L4_IO_CONFIG_V
	---help---
		STM32 L4 Cortex M4, USB FS, AES, 512 Kb FLASH, 128+32 Kb SRAM

config ARCH_CHIP_STM32L475RG
	bool "STM32L475RG"
	select STM32L4_STM32L475XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, 1024Kb FLASH, 96+32 Kb SRAM, LQFP100

config ARCH_CHIP_STM32L475RE
	bool "STM32L475RE"
	select STM32L4_STM32L475XX
	select STM32L4_FLASH_CONFIG_E
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, 512Kb FLASH, 96+32 Kb SRAM, LQFP100

config ARCH_CHIP_STM32L475RC
	bool "STM32L475RC"
	select STM32L4_STM32L475XX
	select STM32L4_FLASH_CONFIG_E
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, 256Kb FLASH, 96+32 Kb SRAM, LQFP100

config ARCH_CHIP_STM32L475VG
	bool "STM32L475VG"
	select STM32L4_STM32L475XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, 1024Kb FLASH, 96+32 Kb SRAM, LQFP64

config ARCH_CHIP_STM32L475VE
	bool "STM32L475VE"
	select STM32L4_STM32L475XX
	select STM32L4_FLASH_CONFIG_E
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, 512Kb FLASH, 96+32 Kb SRAM, LQFP64

config ARCH_CHIP_STM32L475VC
	bool "STM32L475VC"
	select STM32L4_STM32L475XX
	select STM32L4_FLASH_CONFIG_E
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, 256Kb FLASH, 96+32 Kb SRAM, LQFP64

config ARCH_CHIP_STM32L476JG
	bool "STM32L476JG"
	select STM32L4_STM32L476XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_J
	---help---
		STM32 L4 Cortex M4, 1024Kb FLASH, 96+32 Kb SRAM

config ARCH_CHIP_STM32L476JE
	bool "STM32L476JE"
	select STM32L4_STM32L476XX
	select STM32L4_FLASH_CONFIG_E
	select STM32L4_IO_CONFIG_J
	---help---
		STM32 L4 Cortex M4, 512Kb FLASH, 96+32 Kb SRAM

config ARCH_CHIP_STM32L476RG
	bool "STM32L476RG"
	select STM32L4_STM32L476XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, 1024Kb FLASH, 96+32 Kb SRAM

config ARCH_CHIP_STM32L476RE
	bool "STM32L476RE"
	select STM32L4_STM32L476XX
	select STM32L4_FLASH_CONFIG_E
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, 512Kb FLASH, 96+32 Kb SRAM

config ARCH_CHIP_STM32L486RG
	bool "STM32L486RG"
	select STM32L4_STM32L486XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, AES, 1024Kb FLASH, 96+32 Kb SRAM

config ARCH_CHIP_STM32L486JG
	bool "STM32L486JG"
	select STM32L4_STM32L486XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_J
	---help---
		STM32 L4 Cortex M4, AES, 1024Kb FLASH, 96+32 Kb SRAM

config ARCH_CHIP_STM32L486VG
	bool "STM32L486VG"
	select STM32L4_STM32L486XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_V
	---help---
		STM32 L4 Cortex M4, AES, 1024Kb FLASH, 96+32 Kb SRAM

config ARCH_CHIP_STM32L486QG
	bool "STM32L486QG"
	select STM32L4_STM32L486XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_Q
	---help---
		STM32 L4 Cortex M4, AES, 1024Kb FLASH, 96+32 Kb SRAM

config ARCH_CHIP_STM32L486ZG
	bool "STM32L486ZG"
	select STM32L4_STM32L486XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_Z
	---help---
		STM32 L4 Cortex M4, AES, 1024Kb FLASH, 96+32 Kb SRAM

config ARCH_CHIP_STM32L496RE
	bool "STM32L496RE"
	select STM32L4_STM32L496XX
	select STM32L4_FLASH_CONFIG_E
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, 512Kb FLASH, 320 Kb SRAM

config ARCH_CHIP_STM32L496RG
	bool "STM32L496RG"
	select STM32L4_STM32L496XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, 1024Kb FLASH, 320 Kb SRAM

config ARCH_CHIP_STM32L496VE
	bool "STM32L496VE"
	select STM32L4_STM32L496XX
	select STM32L4_FLASH_CONFIG_E
	select STM32L4_IO_CONFIG_V
	---help---
		STM32 L4 Cortex M4, 512Kb FLASH, 320 Kb SRAM

config ARCH_CHIP_STM32L496VG
	bool "STM32L496VG"
	select STM32L4_STM32L496XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_V
	---help---
		STM32 L4 Cortex M4, 1024Kb FLASH, 320 Kb SRAM

config ARCH_CHIP_STM32L496ZE
	bool "STM32L496ZE"
	select STM32L4_STM32L496XX
	select STM32L4_FLASH_CONFIG_E
	select STM32L4_IO_CONFIG_Z
	---help---
		STM32 L4 Cortex M4, 512Kb FLASH, 320 Kb SRAM

config ARCH_CHIP_STM32L496ZG
	bool "STM32L496ZG"
	select STM32L4_STM32L496XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_Z
	---help---
		STM32 L4 Cortex M4, 1024Kb FLASH, 320 Kb SRAM

config ARCH_CHIP_STM32L496AG
	bool "STM32L496AG"
	select STM32L4_STM32L496XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_A
	---help---
		STM32 L4 Cortex M4, 1024Kb FLASH, 320 Kb SRAM

config ARCH_CHIP_STM32L4A6RG
	bool "STM32L4A6RG"
	select STM32L4_STM32L4A6XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_R
	---help---
		STM32 L4 Cortex M4, AES, HASH, 1024Kb FLASH, 320 Kb SRAM

config ARCH_CHIP_STM32L4A6VG
	bool "STM32L4A6VG"
	select STM32L4_STM32L4A6XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_V
	---help---
		STM32 L4 Cortex M4, AES, HASH, 1024Kb FLASH, 320 Kb SRAM

config ARCH_CHIP_STM32L4A6QG
	bool "STM32L4A6QG"
	select STM32L4_STM32L4A6XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_Q
	---help---
		STM32 L4 Cortex M4, AES, HASH, 1024Kb FLASH, 320 Kb SRAM

config ARCH_CHIP_STM32L4A6ZG
	bool "STM32L4A6ZG"
	select STM32L4_STM32L4A6XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_Z
	---help---
		STM32 L4 Cortex M4, AES, HASH, 1024Kb FLASH, 320 Kb SRAM

config ARCH_CHIP_STM32L4A6AG
	bool "STM32L4A6AG"
	select STM32L4_STM32L4A6XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_A
	---help---
		STM32 L4 Cortex M4, AES, HASH, 1024Kb FLASH, 320 Kb SRAM

config ARCH_CHIP_STM32L4R5VG
	bool "STM32L4R5VG"
	select STM32L4_STM32L4R5XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_V
	---help---
		STM32 L4+ Cortex M4, 1024Kb FLASH, 640 Kb SRAM

config ARCH_CHIP_STM32L4R5QG
	bool "STM32L4R5QG"
	select STM32L4_STM32L4R5XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_Q
	---help---
		STM32 L4+ Cortex M4, 1024Kb FLASH, 640 Kb SRAM

config ARCH_CHIP_STM32L4R5ZG
	bool "STM32L4R5ZG"
	select STM32L4_STM32L4R5XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_Z
	---help---
		STM32 L4+ Cortex M4, 1024Kb FLASH, 640 Kb SRAM

config ARCH_CHIP_STM32L4R5AG
	bool "STM32L4R5AG"
	select STM32L4_STM32L4R5XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_A
	---help---
		STM32 L4+ Cortex M4, 1024Kb FLASH, 640 Kb SRAM

config ARCH_CHIP_STM32L4R5VI
	bool "STM32L4R5VI"
	select STM32L4_STM32L4R5XX
	select STM32L4_FLASH_CONFIG_I
	select STM32L4_IO_CONFIG_V
	---help---
		STM32 L4+ Cortex M4, 2048Kb FLASH, 640 Kb SRAM

config ARCH_CHIP_STM32L4R5QI
	bool "STM32L4R5QI"
	select STM32L4_STM32L4R5XX
	select STM32L4_FLASH_CONFIG_I
	select STM32L4_IO_CONFIG_Q
	---help---
		STM32 L4+ Cortex M4, 2048Kb FLASH, 640 Kb SRAM

config ARCH_CHIP_STM32L4R5ZI
	bool "STM32L4R5ZI"
	select STM32L4_STM32L4R5XX
	select STM32L4_FLASH_CONFIG_I
	select STM32L4_IO_CONFIG_Z
	---help---
		STM32 L4+ Cortex M4, 2048Kb FLASH, 640 Kb SRAM

config ARCH_CHIP_STM32L4R5AI
	bool "STM32L4R5AI"
	select STM32L4_STM32L4R5XX
	select STM32L4_FLASH_CONFIG_I
	select STM32L4_IO_CONFIG_A
	---help---
		STM32 L4+ Cortex M4, 2048Kb FLASH, 640 Kb SRAM

config ARCH_CHIP_STM32L4R9VG
	bool "STM32L4R9VG"
	select STM32L4_STM32L4R9XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_V
	---help---
		STM32 L4+ Cortex M4, 1024Kb FLASH, 640 Kb SRAM

config ARCH_CHIP_STM32L4R9ZG
	bool "STM32L4R9ZG"
	select STM32L4_STM32L4R9XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_Z
	---help---
		STM32 L4+ Cortex M4, 1024Kb FLASH, 640 Kb SRAM

config ARCH_CHIP_STM32L4R9AG
	bool "STM32L4R9AG"
	select STM32L4_STM32L4R9XX
	select STM32L4_FLASH_CONFIG_G
	select STM32L4_IO_CONFIG_A
	---help---
		STM32 L4+ Cortex M4, 1024Kb FLASH, 640 Kb SRAM

config ARCH_CHIP_STM32L4R9VI
	bool "STM32L4R9VI"
	select STM32L4_STM32L4R9XX
	select STM32L4_FLASH_CONFIG_I
	select STM32L4_IO_CONFIG_V
	---help---
		STM32 L4+ Cortex M4, 2048Kb FLASH, 640 Kb SRAM

config ARCH_CHIP_STM32L4R9ZI
	bool "STM32L4R9ZI"
	select STM32L4_STM32L4R9XX
	select STM32L4_FLASH_CONFIG_I
	select STM32L4_IO_CONFIG_Z
	---help---
		STM32 L4+ Cortex M4, 2048Kb FLASH, 640 Kb SRAM

config ARCH_CHIP_STM32L4R9AI
	bool "STM32L4R9AI"
	select STM32L4_STM32L4R9XX
	select STM32L4_FLASH_CONFIG_I
	select STM32L4_IO_CONFIG_A
	---help---
		STM32 L4+ Cortex M4, 2048Kb FLASH, 640 Kb SRAM

endchoice # STM32 L4 Chip Selection

# Chip product lines

config STM32L4_STM32L4X1
	# STM32L4x1 Access Lines
	#
	# Avoid using this config as it is basically same subfamily
	# as STM32L4_STM32L4X3 (documented in RM0394).
	#
	# Note: This is _not_ for STM32L471xx (documented in RM0392).
	bool
	default n
	select STM32L4_STM32L4X3

config STM32L4_STM32L4X2
	# STM32L4x2 USB Device Lines
	#
	# Avoid using this config as it is basically same subfamily
	# as STM32L4_STM32L4X3 (documented in RM0394).
	bool
	default n
	select STM32L4_STM32L4X3
	select STM32L4_HAVE_USBFS

config STM32L4_STM32L4X3
	# STM32L4 devices documented in RM0394, regardless of what ST's
	# marketing calls them.
	bool
	default n
	select ARCH_HAVE_FPU
	select STM32L4_HAVE_LPUART1
	select STM32L4_HAVE_USART1
	select STM32L4_HAVE_USART2
	select STM32L4_HAVE_USART3 if !(STM32L4_STM32L432XX || STM32L4_STM32L442XX)
	select STM32L4_HAVE_LPTIM1
	select STM32L4_HAVE_LPTIM2
	select STM32L4_HAVE_COMP
	select STM32L4_HAVE_SAI1
	select STM32L4_HAVE_LCD if !(STM32L4_STM32L4X1 || STM32L4_STM32L4X2)
	select STM32L4_HAVE_HSI48

config STM32L4_STM32L4X5
	# STM32L4 USB OTG Lines (documented in RM0351)
	bool
	default n
	select ARCH_HAVE_FPU
	select STM32L4_HAVE_LPUART1
	select STM32L4_HAVE_USART1
	select STM32L4_HAVE_USART2
	select STM32L4_HAVE_USART3
	select STM32L4_HAVE_UART4
	select STM32L4_HAVE_UART5
	select STM32L4_HAVE_ADC2
	select STM32L4_HAVE_ADC3
	select STM32L4_HAVE_DAC2
	select STM32L4_HAVE_FSMC
	select STM32L4_HAVE_TIM3
	select STM32L4_HAVE_TIM4
	select STM32L4_HAVE_TIM5
	select STM32L4_HAVE_TIM7
	select STM32L4_HAVE_TIM8
	select STM32L4_HAVE_TIM17
	select STM32L4_HAVE_LPTIM1
	select STM32L4_HAVE_LPTIM2
	select STM32L4_HAVE_COMP
	select STM32L4_HAVE_SAI1
	select STM32L4_HAVE_SAI2
	select STM32L4_HAVE_SDMMC1
	select STM32L4_HAVE_OTGFS
	select STM32L4_HAVE_DFSDM1
	select STM32L4_HAVE_QSPI

config STM32L4_STM32L4X6
	# STM32L4x6 (documented in RM0351)
	bool
	default n
	select ARCH_HAVE_FPU
	select STM32L4_HAVE_LPUART1
	select STM32L4_HAVE_USART1
	select STM32L4_HAVE_USART2
	select STM32L4_HAVE_USART3
	select STM32L4_HAVE_UART4
	select STM32L4_HAVE_UART5
	select STM32L4_HAVE_ADC2
	select STM32L4_HAVE_ADC3
	select STM32L4_HAVE_DAC2
	select STM32L4_HAVE_FSMC
	select STM32L4_HAVE_TIM3
	select STM32L4_HAVE_TIM4
	select STM32L4_HAVE_TIM5
	select STM32L4_HAVE_TIM7
	select STM32L4_HAVE_TIM8
	select STM32L4_HAVE_TIM17
	select STM32L4_HAVE_LPTIM1
	select STM32L4_HAVE_LPTIM2
	select STM32L4_HAVE_COMP
	select STM32L4_HAVE_SAI1
	select STM32L4_HAVE_SAI2
	select STM32L4_HAVE_SDMMC1
	select STM32L4_HAVE_OTGFS
	select STM32L4_HAVE_LCD
	select STM32L4_HAVE_QSPI

config STM32L4_STM32L4XR
	# STM32L4+ (documented in RM0432)
	bool
	default n
	select ARCH_HAVE_FPU
	select STM32L4_HAVE_LPUART1
	select STM32L4_HAVE_USART1
	select STM32L4_HAVE_USART2
	select STM32L4_HAVE_USART3
	select STM32L4_HAVE_UART4
	select STM32L4_HAVE_UART5
	select STM32L4_HAVE_DAC2
	select STM32L4_HAVE_FSMC
	select STM32L4_HAVE_TIM3
	select STM32L4_HAVE_TIM4
	select STM32L4_HAVE_TIM5
	select STM32L4_HAVE_TIM7
	select STM32L4_HAVE_TIM8
	select STM32L4_HAVE_TIM17
	select STM32L4_HAVE_LPTIM1
	select STM32L4_HAVE_LPTIM2
	select STM32L4_HAVE_COMP
	select STM32L4_HAVE_SAI1
	select STM32L4_HAVE_SAI2
	select STM32L4_HAVE_SDMMC1
	select STM32L4_HAVE_OTGFS
	select STM32L4_HAVE_I2C4
	select STM32L4_HAVE_DCMI
	select STM32L4_HAVE_DFSDM1
	select STM32L4_HAVE_HSI48
	select STM32L4_HAVE_DMAMUX

# Chip subfamilies:

config STM32L4_STM32L412XX
	bool
	default n
	select STM32L4_STM32L4X2
	select STM32L4_HAVE_ADC2

config STM32L4_STM32L422XX
	bool
	default n
	select STM32L4_STM32L4X2
	select STM32L4_HAVE_ADC2
	select STM32L4_HAVE_AES

config STM32L4_STM32L431XX
	bool
	default n
	select STM32L4_STM32L4X1
	select STM32L4_HAVE_DAC2
	select STM32L4_HAVE_TIM7
	select STM32L4_HAVE_SDMMC1 if (STM32L4_IO_CONFIG_V || STM32L4_IO_CONFIG_R)

config STM32L4_STM32L432XX
	bool
	default n
	select STM32L4_STM32L4X2
	select STM32L4_HAVE_DAC2
	select STM32L4_HAVE_TIM7

config STM32L4_STM32L433XX
	bool
	default n
	select STM32L4_STM32L4X3
	select STM32L4_HAVE_DAC2
	select STM32L4_HAVE_TIM7

config STM32L4_STM32L442XX
	bool
	default n
	select STM32L4_STM32L4X2
	select STM32L4_HAVE_DAC2
	select STM32L4_HAVE_TIM7
	select STM32L4_HAVE_AES

config STM32L4_STM32L443XX
	bool
	default n
	select STM32L4_STM32L4X3
	select STM32L4_HAVE_DAC2
	select STM32L4_HAVE_TIM7
	select STM32L4_HAVE_SDMMC1
	select STM32L4_HAVE_AES

config STM32L4_STM32L451XX
	bool
	default n
	select STM32L4_STM32L4X1
	select STM32L4_HAVE_UART4
	select STM32L4_HAVE_TIM3
	select STM32L4_HAVE_I2C4
	select STM32L4_HAVE_SDMMC1 if !STM32L4_IO_CONFIG_C
	select STM32L4_HAVE_DFSDM1

config STM32L4_STM32L452XX
	bool
	default n
	select STM32L4_STM32L4X2
	select STM32L4_HAVE_UART4
	select STM32L4_HAVE_TIM3
	select STM32L4_HAVE_I2C4
	select STM32L4_HAVE_SDMMC1
	select STM32L4_HAVE_DFSDM1

config STM32L4_STM32L462XX
	bool
	default n
	select STM32L4_STM32L4X2
	select STM32L4_HAVE_UART4
	select STM32L4_HAVE_TIM3
	select STM32L4_HAVE_I2C4
	select STM32L4_HAVE_SDMMC1
	select STM32L4_HAVE_DFSDM1
	select STM32L4_HAVE_AES

config STM32L4_STM32L471XX
	bool
	default n
	# TODO

config STM32L4_STM32L475XX
	bool
	default n
	select STM32L4_STM32L4X5

config STM32L4_STM32L476XX
	bool
	default n
	select STM32L4_STM32L4X6

config STM32L4_STM32L486XX
	bool
	default n
	select STM32L4_STM32L4X6
	select STM32L4_HAVE_AES

config STM32L4_STM32L496XX
	bool
	default n
	select STM32L4_STM32L4X6
	select STM32L4_HAVE_I2C4
	select STM32L4_HAVE_CAN2
	select STM32L4_HAVE_DCMI
	select STM32L4_HAVE_DMA2D
	select STM32L4_HAVE_DFSDM1
	select STM32L4_HAVE_HSI48

config STM32L4_STM32L4A6XX
	bool
	default n
	select STM32L4_STM32L496XX
	select STM32L4_HAVE_AES
	select STM32L4_HAVE_HASH

config STM32L4_STM32L4R5XX
	bool
	default n
	select STM32L4_STM32L4XR

config STM32L4_STM32L4S5XX
	bool
	default n
	select STM32L4_STM32L4XR
	select STM32L4_HAVE_AES
	select STM32L4_HAVE_HASH

config STM32L4_STM32L4R7XX
	bool
	default n
	select STM32L4_STM32L4XR
	select STM32L4_HAVE_DMA2D

config STM32L4_STM32L4S7XX
	bool
	default n
	select STM32L4_STM32L4XR
	select STM32L4_HAVE_DMA2D
	select STM32L4_HAVE_AES
	select STM32L4_HAVE_HASH

config STM32L4_STM32L4R9XX
	bool
	default n
	select STM32L4_STM32L4XR
	select STM32L4_HAVE_DMA2D
	select STM32L4_HAVE_LTDC

config STM32L4_STM32L4S9XX
	bool
	default n
	select STM32L4_STM32L4XR
	select STM32L4_HAVE_DMA2D
	select STM32L4_HAVE_LTDC
	select STM32L4_HAVE_AES
	select STM32L4_HAVE_HASH

choice
	prompt "Override Flash Size Designator"
	depends on ARCH_CHIP_STM32L4
	default STM32L4_FLASH_OVERRIDE_DEFAULT
	---help---
		STM32L4 series parts numbering (sans the package type) ends with a letter
		that designates the FLASH size.

			Designator  Size in KiB
			     8          64
			     B         128
			     C         256
			     E         512
			     G        1024
			     I        2048

		This configuration option defaults to using the configuration based on that designator
		or the default smaller size if there is no last character designator is present in the
		STM32 Chip Selection.

		Examples:
		   If the STM32L476VE is chosen, the Flash configuration would be 'E', if a variant of
		   the part with a 1024 KiB Flash is released in the future one could simply select
		   the 'G' designator here.

		   If an STM32L4xxx Series parts is chosen the default Flash configuration will be set
		   herein and can be changed.

config STM32L4_FLASH_OVERRIDE_DEFAULT
	bool "Default"

config STM32L4_FLASH_OVERRIDE_8
	bool "8 64 KB"

config STM32L4_FLASH_OVERRIDE_B
	bool "B 128 KB"

config STM32L4_FLASH_OVERRIDE_C
	bool "C 256 KB"

config STM32L4_FLASH_OVERRIDE_E
	bool "E 512 KB"

config STM32L4_FLASH_OVERRIDE_G
	bool "G 1024 KB"

config STM32L4_FLASH_OVERRIDE_I
	bool "I 2048 KB"

endchoice # "Override Flash Size Designator"

# Flash configurations

config STM32L4_FLASH_CONFIG_8
	bool
	default n
	depends on STM32L4_STM32L412XX

config STM32L4_FLASH_CONFIG_B
	bool
	default n
	depends on STM32L4_STM32L4X1 || STM32L4_STM32L4X3

config STM32L4_FLASH_CONFIG_C
	bool
	default n
	depends on !STM32L4_STM32L496XX

config STM32L4_FLASH_CONFIG_E
	bool
	default n

config STM32L4_FLASH_CONFIG_G
	bool
	default n
	depends on STM32L4_STM32L4X5 || STM32L4_STM32L4X6

config STM32L4_FLASH_CONFIG_I
	bool
	default n
	depends on STM32L4_STM32L4XR

# Pin/package configurations

config STM32L4_IO_CONFIG_K
	bool
	default n

config STM32L4_IO_CONFIG_T
	bool
	default n

config STM32L4_IO_CONFIG_C
	bool
	default n

config STM32L4_IO_CONFIG_R
	bool
	default n

config STM32L4_IO_CONFIG_J
	bool
	default n

config STM32L4_IO_CONFIG_M
	bool
	default n

config STM32L4_IO_CONFIG_V
	bool
	default n

config STM32L4_IO_CONFIG_Q
	bool
	default n

config STM32L4_IO_CONFIG_Z
	bool
	default n

config STM32L4_IO_CONFIG_A
	bool
	default n

comment "STM32L4 SRAM2 and SRAM3 Options"

config STM32L4_SRAM2_HEAP
	bool "SRAM2 is used for heap"
	default n
	select STM32L4_SRAM2_INIT
	---help---
		The STM32L4 SRAM2 region has special properties (power, protection, parity)
		which may be used by the application for special purposes.  But if these
		special properties are not needed, it may be instead added to the heap for
		use by malloc().
		NOTE: you must also select an appropriate number of memory regions in the
		'Memory Management' section.

config STM32L4_SRAM2_INIT
	bool "SRAM2 is initialized to zero"
	default n
	---help---
		The STM32L4 SRAM2 region has parity checking.  However, when the system
		powers on, the memory is in an unknown state, and reads from uninitialized
		memory can trigger parity faults from the random data.  This can be
		avoided by first writing to all locations to force the parity into a valid
		state.
		However, if the SRAM2 is being used for it's battery-backed capability,
		this may be undesirable (because it will destroy the contents).  In that
		case, the board should handle the initialization itself at the appropriate
		time.

config STM32L4_SRAM3_HEAP
	bool "SRAM3 is used for heap"
	depends on STM32L4_STM32L4XR
	default y
	---help---
		Add the STM32L4 SRAM3 to the heap for use by malloc().
		NOTE: you must also select an appropriate number of memory regions in the
		'Memory Management' section.

config STM32L4_USE_LEGACY_PINMAP
	bool "Use the legacy pinmap with GPIO_SPEED_xxx included."
	default y
	---help---
		In the past, pinmap files included GPIO_SPEED_xxxMhz. These speed
		settings should have come from the board.h as it describes the wiring
		of the SoC to the board. The speed is really slew rate control and
		therefore is related to the layout and can only be properly set
		in board.h.

		STM32L4_USE_LEGACY_PINMAP is provided, to allow lazy migration to
		using pinmaps without speeds. The work required to do this can be aided
		by running tools/stm32_pinmap_tool.py. The tools will take a board.h
		file and a legacy pinmap and output the required changes that one needs
		to make to a board.h file.

		Eventually, STM32L4_USE_LEGACY_PINMAP will be deprecated and the legacy
		pinmaps removed from NuttX. Any new boards added should set
		STM32L4_USE_LEGACY_PINMAP=n and fully define the pins in board.h

comment "STM32L4 Peripherals"

menu "STM32L4 Peripheral Support"

# These "hidden" settings determine whether a peripheral option is available
# for the selected MCU

config STM32L4_HAVE_ADC2
	bool
	default n

config STM32L4_HAVE_ADC3
	bool
	default n

config STM32L4_HAVE_AES
	bool
	default n

config STM32L4_HAVE_CAN2
	bool
	default n

config STM32L4_HAVE_COMP
	bool
	default n

config STM32L4_HAVE_DAC2
	bool
	default n

config STM32L4_HAVE_DCMI
	bool
	default n

config STM32L4_HAVE_DFSDM1
	bool
	default n

config STM32L4_HAVE_DMA2D
	bool
	default n

config STM32L4_HAVE_DMAMUX
	bool
	default n

config STM32L4_HAVE_FSMC
	bool
	default n

config STM32L4_HAVE_HASH
	bool
	default n

config STM32L4_HAVE_HSI48
	bool
	default n

config STM32L4_HAVE_I2C4
	bool
	default n

config STM32L4_HAVE_LCD
	bool
	default n

config STM32L4_HAVE_LTDC
	bool
	default n

config STM32L4_HAVE_LPTIM1
	bool
	default n

config STM32L4_HAVE_LPTIM2
	bool
	default n

config STM32L4_HAVE_OTGFS
	bool
	default n

config STM32L4_HAVE_USBFS
	bool
	default n

config STM32L4_HAVE_SAI1
	bool
	default n

config STM32L4_HAVE_SAI2
	bool
	default n

config STM32L4_RTC
	bool "RTC"
	default n
	select RTC

config STM32L4_HAVE_SDMMC1
	bool
	default n

config STM32L4_HAVE_TIM3
	bool
	default n

config STM32L4_HAVE_TIM4
	bool
	default n

config STM32L4_HAVE_TIM5
	bool
	default n

config STM32L4_HAVE_TIM7
	bool
	default n

config STM32L4_HAVE_TIM8
	bool
	default n

config STM32L4_HAVE_TIM17
	bool
	default n

config STM32L4_HAVE_LPUART1
	bool
	default n

config STM32L4_HAVE_USART1
	bool
	default n

config STM32L4_HAVE_USART2
	bool
	default n

config STM32L4_HAVE_USART3
	bool
	default n

config STM32L4_HAVE_UART4
	bool
	default n

config STM32L4_HAVE_UART5
	bool
	default n

config STM32L4_HAVE_QSPI
	bool
	default n

# These "hidden" settings are the OR of individual peripheral selections
# indicating that the general capability is required.

config STM32L4_ADC
	bool
	default n

config STM32L4_CAN
	bool
	default n

config STM32L4_DAC
	bool
	default n

config STM32L4_DFSDM
	bool
	default n

config STM32L4_DMAMUX
	bool
	default n
	depends on STM32L4_HAVE_DMAMUX

config STM32L4_DMA
	bool
	default n
	select STM32L4_DMAMUX if STM32L4_HAVE_DMAMUX

config STM32L4_I2C
	bool
	default n

config STM32L4_SAI
	bool
	default n

config STM32L4_SPI
	bool
	default n

config STM32L4_PWM
	bool
	default n
	select ARCH_HAVE_PWM_PULSECOUNT

config STM32L4_USART
	bool
	default n

config STM32L4_LPTIM
	bool
	default n

config STM32L4_SDMMC
	bool
	default n

# These are the peripheral selections proper

comment "AHB1 Peripherals"

config STM32L4_DMAMUX1
	bool "DMAMUX1"
	default n
	depends on STM32L4_HAVE_DMAMUX
	select STM32L4_DMAMUX

config STM32L4_DMA1
	bool "DMA1"
	default n
	select STM32L4_DMA
	select ARCH_DMA
	select STM32L4_DMAMUX1 if STM32L4_HAVE_DMAMUX

config STM32L4_DMA2
	bool "DMA2"
	default n
	select STM32L4_DMA
	select ARCH_DMA
	select STM32L4_DMAMUX1 if STM32L4_HAVE_DMAMUX

config STM32L4_CRC
	bool "CRC"
	default n

config STM32L4_TSC
	bool "TSC"
	default n

comment "AHB2 Peripherals"

config STM32L4_OTGFS
	bool "OTG FS"
	default n
	select USBHOST_HAVE_ASYNCH if USBHOST
	depends on STM32L4_HAVE_OTGFS

config STM32L4_ADC1
	bool "ADC1"
	default n
	select STM32L4_ADC

config STM32L4_ADC2
	bool "ADC2"
	default n
	select STM32L4_ADC
	depends on STM32L4_HAVE_ADC2

config STM32L4_ADC3
	bool "ADC3"
	default n
	select STM32L4_ADC
	depends on STM32L4_HAVE_ADC3

config STM32L4_AES
	bool "AES"
	default n
	depends on STM32L4_HAVE_AES

config STM32L4_DCMI
	bool "DCMI"
	default n
	depends on STM32L4_HAVE_DCMI

config STM32L4_DMA2D
	bool "DMA2D"
	default n
	depends on STM32L4_HAVE_DMA2D

config STM32L4_HASH
	bool "HASH"
	default n
	depends on STM32L4_HAVE_HASH

config STM32L4_RNG
	bool "RNG"
	default n
	select ARCH_HAVE_RNG

comment "AHB3 Peripherals"

config STM32L4_FSMC
	bool "FSMC"
	default n
	depends on STM32L4_HAVE_FSMC

config STM32L4_QSPI
	bool "QuadSPI"
	default n
	depends on STM32L4_HAVE_QSPI
	---help---
		The STM32L4 QSPI block is intended to support one serial NOR flash device

if STM32L4_QSPI

config STM32L4_QSPI_FLASH_SIZE
	int "Size of attached serial flash, bytes"
	default 16777216
	range 1 2147483647
	---help---
		The STM32L4 QSPI peripheral requires the size of the Flash be specified

config STM32L4_QSPI_FIFO_THESHOLD
	int "Number of bytes before asserting FIFO threshold flag"
	default 4
	range 1 16
	---help---
		The STM32L4 QSPI peripheral requires that the FIFO threshold be specified
		I would leave it at the default value of 4 unless you know what you are doing.

config STM32L4_QSPI_CSHT
	int "Number of cycles Chip Select must be inactive between transactions"
	default 1
	range 1 8
	---help---
		The STM32L4 QSPI peripheral requires that it be specified the minimum number
		of AHB cycles that Chip Select be held inactive between transactions.

choice
	prompt "Transfer technique"
	default STM32L4_QSPI_DMA
	---help---
		You can choose between using polling, interrupts, or DMA to transfer data
		over the QSPI interface.

config STM32L4_QSPI_POLLING
	bool "Polling"
	---help---
		Use conventional register I/O with status polling to transfer data.

config STM32L4_QSPI_INTERRUPTS
	bool "Interrupts"
	---help---
		User interrupt driven I/O transfers.

config STM32L4_QSPI_DMA
	bool "DMA"
	depends on STM32L4_DMA
	---help---
		Use DMA to improve QSPI transfer performance.

endchoice

choice
	prompt "DMA Channel"
	default STM32L4_QSPI_DMA_CHAN_1_5
	depends on STM32L4_DMA
	---help---
		You can choose between two DMA channels for use with QSPI:
		either DMA1 channel 5, or DMA2 channel 7.
		If you only see one choice here, it is probably because
		you have not also enabled the associated DMA controller.

config STM32L4_QSPI_DMA_CHAN_1_5
	bool "DMA1 Channel 5"
	depends on STM32L4_DMA1 && !STM32L4_DMAMUX
	---help---
		Use DMA1 channel 5 for QSPI.

config STM32L4_QSPI_DMA_CHAN_2_7
	bool "DMA2 Channel 7"
	depends on STM32L4_DMA2 && !STM32L4_DMAMUX
	---help---
		Use DMA2 channel 7 for QSPI.

endchoice

choice
	prompt "DMA Priority"
	default STM32L4_QSPI_DMAPRIORITY_MEDIUM
	depends on STM32L4_DMA
	---help---
		The DMA controller supports priority levels.  You are probably fine
		with the default of 'medium' except for special cases.  In the event
		of contention between to channels at the same priority, the lower
		numbered channel has hardware priority over the higher numbered one.

config STM32L4_QSPI_DMAPRIORITY_VERYHIGH
	bool "Very High priority"
	depends on STM32L4_DMA
	---help---
		'Highest' priority.

config STM32L4_QSPI_DMAPRIORITY_HIGH
	bool "High priority"
	depends on STM32L4_DMA
	---help---
		'High' priority.

config STM32L4_QSPI_DMAPRIORITY_MEDIUM
	bool "Medium priority"
	depends on STM32L4_DMA
	---help---
		'Medium' priority.

config STM32L4_QSPI_DMAPRIORITY_LOW
	bool "Low priority"
	depends on STM32L4_DMA
	---help---
		'Low' priority.

endchoice

config STM32L4_QSPI_DMATHRESHOLD
	int "QSPI DMA threshold"
	default 4
	depends on STM32L4_QSPI_DMA
	---help---
		When QSPI DMA is enabled, small DMA transfers will still be performed
		by polling logic.  This value is the threshold below which transfers
		will still be performed by conventional register status polling.

config STM32L4_QSPI_DMADEBUG
	bool "QSPI DMA transfer debug"
	depends on STM32L4_QSPI_DMA && DEBUG_SPI && DEBUG_DMA
	default n
	---help---
		Enable special debug instrumentation to analyze QSPI DMA data transfers.
		This logic is as non-invasive as possible:  It samples DMA
		registers at key points in the data transfer and then dumps all of
		the registers at the end of the transfer.

config STM32L4_QSPI_REGDEBUG
	bool "QSPI Register level debug"
	depends on DEBUG_SPI_INFO
	default n
	---help---
		Output detailed register-level QSPI device debug information.
		Requires also CONFIG_DEBUG_SPI_INFO.

endif

comment "APB1 Peripherals"

config STM32L4_PWR
	bool "PWR"
	default n

config STM32L4_TIM2
	bool "TIM2"
	default n

config STM32L4_TIM3
	bool "TIM3"
	default n
	depends on STM32L4_HAVE_TIM3

config STM32L4_TIM4
	bool "TIM4"
	default n
	depends on STM32L4_HAVE_TIM4

config STM32L4_TIM5
	bool "TIM5"
	default n
	depends on STM32L4_HAVE_TIM5

config STM32L4_TIM6
	bool "TIM6"
	default n

config STM32L4_TIM7
	bool "TIM7"
	default n
	depends on STM32L4_HAVE_TIM7

config STM32L4_LCD
	bool "LCD"
	default n
	depends on STM32L4_HAVE_LCD

config STM32L4_SPI2
	bool "SPI2"
	default n
	depends on !(STM32L4_STM32L432XX || STM32L4_STM32L442XX)
	select SPI
	select STM32L4_SPI

config STM32L4_SPI3
	bool "SPI3"
	default n
	select SPI
	select STM32L4_SPI

config STM32L4_LPUART1
	bool "LPUART1"
	default n
	depends on STM32L4_HAVE_LPUART1
	select ARCH_HAVE_SERIAL_TERMIOS
	select STM32L4_USART

config STM32L4_USART2
	bool "USART2"
	default n
	depends on STM32L4_HAVE_USART2
	select ARCH_HAVE_SERIAL_TERMIOS
	select STM32L4_USART

config STM32L4_USART3
	bool "USART3"
	default n
	depends on STM32L4_HAVE_USART3
	select ARCH_HAVE_SERIAL_TERMIOS
	select STM32L4_USART

config STM32L4_UART4
	bool "UART4"
	default n
	depends on STM32L4_HAVE_UART4
	select ARCH_HAVE_SERIAL_TERMIOS
	select STM32L4_USART

config STM32L4_UART5
	bool "UART5"
	default n
	depends on STM32L4_HAVE_UART5
	select ARCH_HAVE_SERIAL_TERMIOS
	select STM32L4_USART

config STM32L4_I2C1
	bool "I2C1"
	default n
	select STM32L4_I2C

config STM32L4_I2C2
	bool "I2C2"
	default n
	depends on !(STM32L4_STM32L432XX || STM32L4_STM32L442XX)
	select STM32L4_I2C

config STM32L4_I2C3
	bool "I2C3"
	default n
	select STM32L4_I2C

config STM32L4_I2C4
	bool "I2C4"
	default n
	select STM32L4_I2C
	depends on STM32L4_HAVE_I2C4

config STM32L4_CAN1
	bool "CAN1"
	default n
	select CAN
	select STM32L4_CAN

config STM32L4_CAN2
	bool "CAN2"
	default n
	select CAN
	select STM32L4_CAN
	depends on STM32L4_HAVE_CAN2

config STM32L4_DAC1
	bool "DAC1"
	default n
	select STM32L4_DAC

config STM32L4_DAC2
	bool "DAC2"
	default n
	select STM32L4_DAC
	depends on STM32L4_HAVE_DAC2

config STM32L4_OPAMP
	bool "OPAMP"
	default n

config STM32L4_LPTIM1
	bool "LPTIM1"
	default n
	select STM32L4_LPTIM
	depends on STM32L4_HAVE_LPTIM1

config STM32L4_LPUART1
	bool "LPUART1"
	default n
	select LPUART1_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS
	select ARCH_HAVE_LPUART1

config STM32L4_SWPMI
	bool "SWPMI"
	default n

config STM32L4_LPTIM2
	bool "LPTIM2"
	default n
	select STM32L4_LPTIM
	depends on STM32L4_HAVE_LPTIM2

config STM32L4_USBFS
	bool "USB FS"
	default n
	depends on STM32L4_HAVE_USBFS
	select USBDEV

comment "APB2 Peripherals"

config STM32L4_SYSCFG
	bool "SYSCFG"
	default y

config STM32L4_FIREWALL
	bool "FIREWALL"
	default y
	depends on STM32L4_SYSCFG

config STM32L4_SDMMC1
	bool "SDMMC1"
	default n
	select ARCH_HAVE_SDIO
	select SCHED_HPWORK
	select STM32L4_SAI1PLL
	select STM32L4_SDMMC
	select ARCH_HAVE_SDIOWAIT_WRCOMPLETE
	select ARCH_HAVE_SDIO_PREFLIGHT
	depends on STM32L4_HAVE_SDMMC1

config STM32L4_TIM1
	bool "TIM1"
	default n

config STM32L4_SPI1
	bool "SPI1"
	default n
	select SPI
	select STM32L4_SPI

config STM32L4_TIM8
	bool "TIM8"
	default n
	depends on STM32L4_HAVE_TIM8

config STM32L4_USART1
	bool "USART1"
	default n
	depends on STM32L4_HAVE_USART1
	select ARCH_HAVE_SERIAL_TERMIOS
	select STM32L4_USART

config STM32L4_TIM15
	bool "TIM15"
	default n

config STM32L4_TIM16
	bool "TIM16"
	default n

config STM32L4_TIM17
	bool "TIM17"
	default n
	depends on STM32L4_HAVE_TIM17

config STM32L4_COMP
	bool "COMP"
	default n
	select COMP
	depends on STM32L4_HAVE_COMP

config STM32L4_SAI1
	bool "SAI1"
	default n
	depends on STM32L4_HAVE_SAI1

config STM32L4_SAI1_A
	bool "SAI1 Block A"
	default n
	select AUDIO
	select I2S
	select SCHED_HPWORK
	select STM32L4_SAI
	depends on STM32L4_SAI1

config STM32L4_SAI1_B
	bool "SAI1 Block B"
	default n
	select AUDIO
	select I2S
	select SCHED_HPWORK
	select STM32L4_SAI
	depends on STM32L4_SAI1

config STM32L4_SAI2
	bool "SAI2"
	default n
	depends on STM32L4_HAVE_SAI2

config STM32L4_SAI2_A
	bool "SAI2 Block A"
	default n
	select AUDIO
	select I2S
	select SCHED_HPWORK
	select STM32L4_SAI
	depends on STM32L4_SAI2

config STM32L4_SAI2_B
	bool "SAI2 Block B"
	default n
	select AUDIO
	select I2S
	select SCHED_HPWORK
	select STM32L4_SAI
	depends on STM32L4_SAI2

config STM32L4_DFSDM1
	bool "DFSDM1"
	default n
	depends on STM32L4_HAVE_DFSDM1

comment "Other Peripherals"

config STM32L4_BKPSRAM
	bool "Enable BKP RAM Domain"
	default n

config STM32L4_IWDG
	bool "IWDG"
	default n
	select WATCHDOG

config STM32L4_WWDG
	bool "WWDG"
	default n
	select WATCHDOG

endmenu

config STM32L4_SAI1PLL
	bool "SAI1PLL"
	default n
	---help---
		The STM32L4 has a separate PLL for the SAI1 block.
		Set this true and provide configuration parameters in
		board.h to use this PLL.

config STM32L4_SAI2PLL
	bool "SAI2PLL"
	default n
	depends on STM32L4_HAVE_SAI2
	---help---
		The STM32L4 has a separate PLL for the SAI2 block.
		Set this true and provide configuration parameters in
		board.h to use this PLL.

config STM32L4_FLASH_PREFETCH
	bool "Enable FLASH Pre-fetch"
	default y
	---help---
	Enable FLASH prefetch

config STM32L4_FLASH_WORKAROUND_DATA_CACHE_CORRUPTION_ON_RWW
	bool "Workaround for FLASH data cache corruption"
	default n
	depends on STM32L4_STM32L4X5 || STM32L4_STM32L4X6 || STM32L4_STM32L4XR
	---help---
		Enable the workaround to fix flash data cache corruption when reading
		from one flash bank while writing on other flash bank.  See your STM32
		errata to check if your STM32 is affected by this problem.

choice
	prompt "JTAG Configuration"
	default STM32L4_JTAG_DISABLE
	---help---
		JTAG Enable settings (by default JTAG-DP and SW-DP are disabled)

config STM32L4_JTAG_DISABLE
	bool "Disable all JTAG clocking"

config STM32L4_JTAG_FULL_ENABLE
	bool "Enable full SWJ (JTAG-DP + SW-DP)"

config STM32L4_JTAG_NOJNTRST_ENABLE
	bool "Enable full SWJ (JTAG-DP + SW-DP) but without JNTRST"

config STM32L4_JTAG_SW_ENABLE
	bool "Set JTAG-DP disabled and SW-DP enabled"

endchoice

config STM32L4_DISABLE_IDLE_SLEEP_DURING_DEBUG
	bool "Disable IDLE Sleep (WFI) in debug mode"
	default n
	---help---
		In debug configuration, disables the WFI instruction in the IDLE loop
		to prevent the JTAG from disconnecting.  With some JTAG debuggers, such
		as the ST-LINK2 with OpenOCD, if the ARM is put to sleep via the WFI
		instruction, the debugger will disconnect, terminating the debug session.

config ARCH_BOARD_STM32L4_CUSTOM_CLOCKCONFIG
	bool "Custom clock configuration"
	default n
	---help---
		Enables special, board-specific STM32 clock configuration.

config STM32L4_HAVE_RTC_SUBSECONDS
	bool
	select ARCH_HAVE_RTC_SUBSECONDS
	default y

menu "RTC Configuration"
	depends on STM32L4_RTC

config STM32L4_RTC_MAGIC_REG
	int "BKP register"
	default 0
	range  0 31
	---help---
		The BKP register used to store/check the Magic value to determine if
		RTC is already setup

config STM32L4_RTC_MAGIC
	hex "RTC Magic 1"
	default 0xfacefeed
	---help---
		Value used as Magic to determine if the RTC is already setup

config STM32L4_RTC_MAGIC_TIME_SET
	hex "RTC Magic 2"
	default 0xf00dface
	---help---
		Value used as Magic to determine if the RTC has been setup and has
		time set

choice
	prompt "RTC clock source"
	default STM32L4_RTC_LSECLOCK
	depends on STM32L4_RTC

config STM32L4_RTC_LSECLOCK
	bool "LSE clock"
	---help---
		Drive the RTC with the LSE clock

config STM32L4_RTC_LSICLOCK
	bool "LSI clock"
	---help---
		Drive the RTC with the LSI clock

config STM32L4_RTC_HSECLOCK
	bool "HSE clock"
	---help---
		Drive the RTC with the HSE clock, divided down to 1MHz.

endchoice

if STM32L4_RTC_LSECLOCK

config STM32L4_RTC_LSECLOCK_START_DRV_CAPABILITY
	int "LSE oscillator drive capability level at LSE start-up"
	default 0
	range 0 3
	---help---
		0 = Low drive capability (default)
		1 = Medium low drive capability
		2 = Medium high drive capability
		3 = High drive capability

config STM32L4_RTC_LSECLOCK_RUN_DRV_CAPABILITY
	int "LSE oscillator drive capability level after LSE start-up"
	default 0
	range 0 3
	---help---
		0 = Low drive capability (default)
		1 = Medium low drive capability
		2 = Medium high drive capability
		3 = High drive capability

endif # STM32L4_RTC_LSECLOCK

endmenu # RTC Configuration

menu "Timer Configuration"

if SCHED_TICKLESS

config STM32L4_ONESHOT
	bool
	default y

config STM32L4_FREERUN
	bool
	default y

config STM32L4_TICKLESS_ONESHOT
	int "Tickless one-shot timer channel"
	default 2
	range 1 8
	depends on STM32L4_ONESHOT
	---help---
		If the Tickless OS feature is enabled, then one clock must be
		assigned to provide the one-shot timer needed by the OS.

config STM32L4_TICKLESS_FREERUN
	int "Tickless free-running timer channel"
	default 5
	range 1 8
	depends on STM32L4_FREERUN
	---help---
		If the Tickless OS feature is enabled, then one clock must be
		assigned to provide the free-running timer needed by the OS.

endif # SCHED_TICKLESS

if !SCHED_TICKLESS

config STM32L4_ONESHOT
	bool "TIM one-shot wrapper"
	default n
	---help---
		Enable a wrapper around the low level timer/counter functions to
		support one-shot timer.

config STM32L4_FREERUN
	bool "TIM free-running wrapper"
	default n
	---help---
		Enable a wrapper around the low level timer/counter functions to
		support a free-running timer.

endif # !SCHED_TICKLESS

config STM32L4_ONESHOT_MAXTIMERS
	int "Maximum number of oneshot timers"
	default 1
	range 1 8
	depends on STM32L4_ONESHOT
	---help---
		Determines the maximum number of oneshot timers that can be
		supported.  This setting pre-allocates some minimal support for each
		of the timers and places an upper limit on the number of oneshot
		timers that you can use.

config STM32L4_LPTIM1_PWM
	bool "LPTIM1 PWM"
	default n
	depends on STM32L4_LPTIM1
	select PWM
	---help---
		Reserve low-power timer 1 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32L4_LPTIM1
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32L4_LPTIM1_PWM

choice
	prompt "LPTIM1 clock source"
	default STM32L4_LPTIM1_CLK_APB1

config STM32L4_LPTIM1_CLK_APB1
	bool "Clock LPTIM1 from APB1"

config STM32L4_LPTIM1_CLK_LSE
	bool "Clock LPTIM1 from LSE"

config STM32L4_LPTIM1_CLK_LSI
	bool "Clock LPTIM1 from LSI"

config STM32L4_LPTIM1_CLK_HSI
	bool "Clock LPTIM1 from HSI"
endchoice

endif # STM32L4_LPTIM1_PWM

config STM32L4_LPTIM2_PWM
	bool "LPTIM2 PWM"
	default n
	depends on STM32L4_LPTIM2
	select PWM
	---help---
		Reserve low-power timer 2 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32L4_LPTIM2
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32L4_LPTIM2_PWM

choice
	prompt "LPTIM2 clock source"
	default STM32L4_LPTIM2_CLK_APB1

config STM32L4_LPTIM2_CLK_APB1
	bool "Clock LPTIM2 from APB1"

config STM32L4_LPTIM2_CLK_LSE
	bool "Clock LPTIM2 from LSE"

config STM32L4_LPTIM2_CLK_LSI
	bool "Clock LPTIM2 from LSI"

config STM32L4_LPTIM2_CLK_HSI
	bool "Clock LPTIM2 from HSI"
endchoice

endif # STM32L4_LPTIM2_PWM

config STM32L4_PWM_LL_OPS
	bool "PWM low-level operations"
	default n
	---help---
		Enable low-level PWM ops.

config STM32L4_TIM1_PWM
	bool "TIM1 PWM"
	default n
	depends on STM32L4_TIM1
	select STM32L4_PWM
	---help---
		Reserve timer 1 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32L4_TIM1
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32L4_TIM1_PWM

config STM32L4_TIM1_MODE
	int "TIM1 Mode"
	default 0
	range 0 4
	---help---
		Specifies the timer mode.

config STM32L4_TIM1_LOCK
	int "TIM1 Lock Level Configuration"
	default 0
	range 0 3
	---help---
		Timer 1 lock level configuration

config STM32L4_TIM1_TDTS
	int "TIM1 t_DTS Division"
	default 0
	range 0 2
	---help---
		Timer 1 dead-time and sampling clock (t_DTS) division

config STM32L4_TIM1_DEADTIME
	int "TIM1 Initial Dead-time"
	default 0
	range 0 255
	---help---
		Timer 1 initial dead-time

if STM32L4_PWM_MULTICHAN

config STM32L4_TIM1_CHANNEL1
	bool "TIM1 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32L4_TIM1_CHANNEL1

config STM32L4_TIM1_CH1MODE
	int "TIM1 Channel 1 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM1_CH1OUT
	bool "TIM1 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32L4_TIM1_CH1NOUT
	bool "TIM1 Channel 1 Complementary Output"
	default n
	depends on STM32L4_TIM1_CH1OUT
	---help---
		Enables channel 1 complementary output.

endif # STM32L4_TIM1_CHANNEL1

config STM32L4_TIM1_CHANNEL2
	bool "TIM1 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32L4_TIM1_CHANNEL2

config STM32L4_TIM1_CH2MODE
	int "TIM1 Channel 2 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM1_CH2OUT
	bool "TIM1 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

config STM32L4_TIM1_CH2NOUT
	bool "TIM1 Channel 2 Complemenrary Output"
	default n
	depends on STM32L4_TIM1_CH2OUT
	---help---
		Enables channel 2 complementary output.

endif # STM32L4_TIM1_CHANNEL2

config STM32L4_TIM1_CHANNEL3
	bool "TIM1 Channel 3"
	default n
	---help---
		Enables channel 3.

if STM32L4_TIM1_CHANNEL3

config STM32L4_TIM1_CH3MODE
	int "TIM1 Channel 3 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM1_CH3OUT
	bool "TIM1 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

config STM32L4_TIM1_CH3NOUT
	bool "TIM1 Channel 3 Complementary Output"
	default n
	depends on STM32L4_TIM1_CH3OUT
	---help---
		Enables channel 3 complementary output.

endif # STM32L4_TIM1_CHANNEL3

config STM32L4_TIM1_CHANNEL4
	bool "TIM1 Channel 4"
	default n
	---help---
		Enables channel 4.

if STM32L4_TIM1_CHANNEL4

config STM32L4_TIM1_CH4MODE
	int "TIM1 Channel 4 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM1_CH4OUT
	bool "TIM1 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32L4_TIM1_CHANNEL4

endif # STM32L4_PWM_MULTICHAN

if !STM32L4_PWM_MULTICHAN

config STM32L4_TIM1_CHANNEL
	int "TIM1 PWM Output Channel"
	default 1
	range 1 4
	---help---
		If TIM1 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

if STM32L4_TIM1_CHANNEL = 1

config STM32L4_TIM1_CH1OUT
	bool "TIM1 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32L4_TIM1_CH1NOUT
	bool "TIM1 Channel 1 Complementary Output"
	default n
	---help---
		Enables channel 1 Complementary Output.

endif # STM32L4_TIM1_CHANNEL = 1

if STM32L4_TIM1_CHANNEL = 2

config STM32L4_TIM1_CH2OUT
	bool "TIM1 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

config STM32L4_TIM1_CH2NOUT
	bool "TIM1 Channel 2 Complementary Output"
	default n
	---help---
		Enables channel 2 Complementary Output.

endif # STM32L4_TIM1_CHANNEL = 2

if STM32L4_TIM1_CHANNEL = 3

config STM32L4_TIM1_CH3OUT
	bool "TIM1 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

config STM32L4_TIM1_CH3NOUT
	bool "TIM1 Channel 3 Complementary Output"
	default n
	---help---
		Enables channel 3 Complementary Output.

endif # STM32L4_TIM1_CHANNEL = 3

if STM32L4_TIM1_CHANNEL = 4

config STM32L4_TIM1_CH4OUT
	bool "TIM1 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32L4_TIM1_CHANNEL = 4

config STM32L4_TIM1_CHMODE
	int "TIM1 Channel Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

endif # !STM32L4_PWM_MULTICHAN

endif # STM32L4_TIM1_PWM

config STM32L4_TIM2_PWM
	bool "TIM2 PWM"
	default n
	depends on STM32L4_TIM2
	select STM32L4_PWM
	---help---
		Reserve timer 2 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32L4_TIM2
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32L4_TIM2_PWM

config STM32L4_TIM2_MODE
	int "TIM2 Mode"
	default 0
	range 0 4
	---help---
		Specifies the timer mode.

if STM32L4_PWM_MULTICHAN

config STM32L4_TIM2_CHANNEL1
	bool "TIM2 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32L4_TIM2_CHANNEL1

config STM32L4_TIM2_CH1MODE
	int "TIM2 Channel 1 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM2_CH1OUT
	bool "TIM2 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32L4_TIM2_CHANNEL1

config STM32L4_TIM2_CHANNEL2
	bool "TIM2 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32L4_TIM2_CHANNEL2

config STM32L4_TIM2_CH2MODE
	int "TIM2 Channel 2 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM2_CH2OUT
	bool "TIM2 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32L4_TIM2_CHANNEL2

config STM32L4_TIM2_CHANNEL3
	bool "TIM2 Channel 3"
	default n
	---help---
		Enables channel 3.

if STM32L4_TIM2_CHANNEL3

config STM32L4_TIM2_CH3MODE
	int "TIM2 Channel 3 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM2_CH3OUT
	bool "TIM2 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32L4_TIM2_CHANNEL3

config STM32L4_TIM2_CHANNEL4
	bool "TIM2 Channel 4"
	default n
	---help---
		Enables channel 4.

if STM32L4_TIM2_CHANNEL4

config STM32L4_TIM2_CH4MODE
	int "TIM2 Channel 4 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM2_CH4OUT
	bool "TIM2 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32L4_TIM2_CHANNEL4

endif # STM32L4_PWM_MULTICHAN

if !STM32L4_PWM_MULTICHAN

config STM32L4_TIM2_CHANNEL
	int "TIM2 PWM Output Channel"
	default 1
	range 1 4
	---help---
		If TIM2 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

if STM32L4_TIM2_CHANNEL = 1

config STM32L4_TIM2_CH1OUT
	bool "TIM2 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32L4_TIM2_CHANNEL = 1

if STM32L4_TIM2_CHANNEL = 2

config STM32L4_TIM2_CH2OUT
	bool "TIM2 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32L4_TIM2_CHANNEL = 2

if STM32L4_TIM2_CHANNEL = 3

config STM32L4_TIM2_CH3OUT
	bool "TIM2 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32L4_TIM2_CHANNEL = 3

if STM32L4_TIM2_CHANNEL = 4

config STM32L4_TIM2_CH4OUT
	bool "TIM2 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32L4_TIM2_CHANNEL = 4

config STM32L4_TIM2_CHMODE
	int "TIM2 Channel Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

endif # !STM32L4_PWM_MULTICHAN

endif # STM32L4_TIM2_PWM

config STM32L4_TIM3_PWM
	bool "TIM3 PWM"
	default n
	depends on STM32L4_TIM3
	select STM32L4_PWM
	---help---
		Reserve timer 3 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32L4_TIM3
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32L4_TIM3_PWM

config STM32L4_TIM3_MODE
	int "TIM3 Mode"
	default 0
	range 0 4
	---help---
		Specifies the timer mode.

if STM32L4_PWM_MULTICHAN

config STM32L4_TIM3_CHANNEL1
	bool "TIM3 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32L4_TIM3_CHANNEL1

config STM32L4_TIM3_CH1MODE
	int "TIM3 Channel 1 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM3_CH1OUT
	bool "TIM3 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32L4_TIM3_CHANNEL1

config STM32L4_TIM3_CHANNEL2
	bool "TIM3 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32L4_TIM3_CHANNEL2

config STM32L4_TIM3_CH2MODE
	int "TIM3 Channel 2 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM3_CH2OUT
	bool "TIM3 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32L4_TIM3_CHANNEL2

config STM32L4_TIM3_CHANNEL3
	bool "TIM3 Channel 3"
	default n
	---help---
		Enables channel 3.

if STM32L4_TIM3_CHANNEL3

config STM32L4_TIM3_CH3MODE
	int "TIM3 Channel 3 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM3_CH3OUT
	bool "TIM3 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32L4_TIM3_CHANNEL3

config STM32L4_TIM3_CHANNEL4
	bool "TIM3 Channel 4"
	default n
	---help---
		Enables channel 4.

if STM32L4_TIM3_CHANNEL4

config STM32L4_TIM3_CH4MODE
	int "TIM3 Channel 4 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM3_CH4OUT
	bool "TIM3 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32L4_TIM3_CHANNEL4

endif # STM32L4_PWM_MULTICHAN

if !STM32L4_PWM_MULTICHAN

config STM32L4_TIM3_CHANNEL
	int "TIM3 PWM Output Channel"
	default 1
	range 1 4
	---help---
		If TIM3 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

if STM32L4_TIM3_CHANNEL = 1

config STM32L4_TIM3_CH1OUT
	bool "TIM3 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32L4_TIM3_CHANNEL = 1

if STM32L4_TIM3_CHANNEL = 2

config STM32L4_TIM3_CH2OUT
	bool "TIM3 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32L4_TIM3_CHANNEL = 2

if STM32L4_TIM3_CHANNEL = 3

config STM32L4_TIM3_CH3OUT
	bool "TIM3 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32L4_TIM3_CHANNEL = 3

if STM32L4_TIM3_CHANNEL = 4

config STM32L4_TIM3_CH4OUT
	bool "TIM3 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32L4_TIM3_CHANNEL = 4

config STM32L4_TIM3_CHMODE
	int "TIM3 Channel Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

endif # !STM32L4_PWM_MULTICHAN

endif # STM32L4_TIM3_PWM

config STM32L4_TIM4_PWM
	bool "TIM4 PWM"
	default n
	depends on STM32L4_TIM4
	select STM32L4_PWM
	---help---
		Reserve timer 4 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32L4_TIM4
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32L4_TIM4_PWM

config STM32L4_TIM4_MODE
	int "TIM4 Mode"
	default 0
	range 0 4
	---help---
		Specifies the timer mode.

if STM32L4_PWM_MULTICHAN

config STM32L4_TIM4_CHANNEL1
	bool "TIM4 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32L4_TIM4_CHANNEL1

config STM32L4_TIM4_CH1MODE
	int "TIM4 Channel 1 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM4_CH1OUT
	bool "TIM4 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32L4_TIM4_CHANNEL1

config STM32L4_TIM4_CHANNEL2
	bool "TIM4 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32L4_TIM4_CHANNEL2

config STM32L4_TIM4_CH2MODE
	int "TIM4 Channel 2 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM4_CH2OUT
	bool "TIM4 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32L4_TIM4_CHANNEL2

config STM32L4_TIM4_CHANNEL3
	bool "TIM4 Channel 3"
	default n
	---help---
		Enables channel 3.

if STM32L4_TIM4_CHANNEL3

config STM32L4_TIM4_CH3MODE
	int "TIM4 Channel 3 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM4_CH3OUT
	bool "TIM4 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32L4_TIM4_CHANNEL3

config STM32L4_TIM4_CHANNEL4
	bool "TIM4 Channel 4"
	default n
	---help---
		Enables channel 4.

if STM32L4_TIM4_CHANNEL4

config STM32L4_TIM4_CH4MODE
	int "TIM4 Channel 4 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM4_CH4OUT
	bool "TIM4 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32L4_TIM4_CHANNEL4

endif # STM32L4_PWM_MULTICHAN

if !STM32L4_PWM_MULTICHAN

config STM32L4_TIM4_CHANNEL
	int "TIM4 PWM Output Channel"
	default 1
	range 1 4
	---help---
		If TIM4 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

if STM32L4_TIM4_CHANNEL = 1

config STM32L4_TIM4_CH1OUT
	bool "TIM4 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32L4_TIM4_CHANNEL = 1

if STM32L4_TIM4_CHANNEL = 2

config STM32L4_TIM4_CH2OUT
	bool "TIM4 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32L4_TIM4_CHANNEL = 2

if STM32L4_TIM4_CHANNEL = 3

config STM32L4_TIM4_CH3OUT
	bool "TIM4 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32L4_TIM4_CHANNEL = 3

if STM32L4_TIM4_CHANNEL = 4

config STM32L4_TIM4_CH4OUT
	bool "TIM4 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32L4_TIM4_CHANNEL = 4

config STM32L4_TIM4_CHMODE
	int "TIM4 Channel Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

endif # !STM32L4_PWM_MULTICHAN

endif # STM32L4_TIM4_PWM

config STM32L4_TIM5_PWM
	bool "TIM5 PWM"
	default n
	depends on STM32L4_TIM5
	select STM32L4_PWM
	---help---
		Reserve timer 5 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32L4_TIM5
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32L4_TIM5_PWM

config STM32L4_TIM5_MODE
	int "TIM5 Mode"
	default 0
	range 0 4
	---help---
		Specifies the timer mode.

if STM32L4_PWM_MULTICHAN

config STM32L4_TIM5_CHANNEL1
	bool "TIM5 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32L4_TIM5_CHANNEL1

config STM32L4_TIM5_CH1MODE
	int "TIM5 Channel 1 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM5_CH1OUT
	bool "TIM5 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32L4_TIM5_CHANNEL1

config STM32L4_TIM5_CHANNEL2
	bool "TIM5 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32L4_TIM5_CHANNEL2

config STM32L4_TIM5_CH2MODE
	int "TIM5 Channel 2 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM5_CH2OUT
	bool "TIM5 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32L4_TIM5_CHANNEL2

config STM32L4_TIM5_CHANNEL3
	bool "TIM5 Channel 3"
	default n
	---help---
		Enables channel 3.

if STM32L4_TIM5_CHANNEL3

config STM32L4_TIM5_CH3MODE
	int "TIM5 Channel 3 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM5_CH3OUT
	bool "TIM5 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32L4_TIM5_CHANNEL3

config STM32L4_TIM5_CHANNEL4
	bool "TIM5 Channel 4"
	default n
	---help---
		Enables channel 4.

if STM32L4_TIM5_CHANNEL4

config STM32L4_TIM5_CH4MODE
	int "TIM5 Channel 4 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM5_CH4OUT
	bool "TIM5 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32L4_TIM5_CHANNEL4

endif # STM32L4_PWM_MULTICHAN

if !STM32L4_PWM_MULTICHAN

config STM32L4_TIM5_CHANNEL
	int "TIM5 PWM Output Channel"
	default 1
	range 1 4
	---help---
		If TIM5 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

if STM32L4_TIM5_CHANNEL = 1

config STM32L4_TIM5_CH1OUT
	bool "TIM5 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32L4_TIM5_CHANNEL = 1

if STM32L4_TIM5_CHANNEL = 2

config STM32L4_TIM5_CH2OUT
	bool "TIM5 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32L4_TIM5_CHANNEL = 2

if STM32L4_TIM5_CHANNEL = 3

config STM32L4_TIM5_CH3OUT
	bool "TIM5 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32L4_TIM5_CHANNEL = 3

if STM32L4_TIM5_CHANNEL = 4

config STM32L4_TIM5_CH4OUT
	bool "TIM5 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32L4_TIM5_CHANNEL = 4

config STM32L4_TIM5_CHMODE
	int "TIM5 Channel Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

endif # !STM32L4_PWM_MULTICHAN

endif # STM32L4_TIM5_PWM

config STM32L4_TIM8_PWM
	bool "TIM8 PWM"
	default n
	depends on STM32L4_TIM8
	select STM32L4_PWM
	---help---
		Reserve timer 8 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32L4_TIM8
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32L4_TIM8_PWM

config STM32L4_TIM8_MODE
	int "TIM8 Mode"
	default 0
	range 0 4
	---help---
		Specifies the timer mode.

config STM32L4_TIM8_LOCK
	int "TIM1 Lock Level Configuration"
	default 0
	range 0 3
	---help---
		Timer 8 lock level configuration

config STM32L4_TIM8_TDTS
	int "TIM8 t_DTS Division"
	default 0
	range 0 2
	---help---
		Timer 8 dead-time and sampling clock (t_DTS) division

config STM32L4_TIM8_DEADTIME
	int "TIM8 Initial Dead-time"
	default 0
	range 0 255
	---help---
		Timer 8 initial dead-time

if STM32L4_PWM_MULTICHAN

config STM32L4_TIM8_CHANNEL1
	bool "TIM8 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32L4_TIM8_CHANNEL1

config STM32L4_TIM8_CH1MODE
	int "TIM8 Channel 1 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM8_CH1OUT
	bool "TIM8 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32L4_TIM8_CH1NOUT
	bool "TIM8 Channel 1 Complementary Output"
	default n
	depends on STM32L4_TIM8_CH1OUT
	---help---
		Enables channel 1 complementary output.

endif # STM32L4_TIM8_CHANNEL1

config STM32L4_TIM8_CHANNEL2
	bool "TIM8 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32L4_TIM8_CHANNEL2

config STM32L4_TIM8_CH2MODE
	int "TIM8 Channel 2 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM8_CH2OUT
	bool "TIM8 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

config STM32L4_TIM8_CH2NOUT
	bool "TIM8 Channel 2 Complementary Output"
	default n
	depends on STM32L4_TIM8_CH2OUT
	---help---
		Enables channel 2 complementary output.

endif # STM32L4_TIM8_CHANNEL2

config STM32L4_TIM8_CHANNEL3
	bool "TIM8 Channel 3"
	default n
	---help---
		Enables channel 3.

if STM32L4_TIM8_CHANNEL3

config STM32L4_TIM8_CH3MODE
	int "TIM8 Channel 3 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM8_CH3OUT
	bool "TIM8 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

config STM32L4_TIM8_CH3NOUT
	bool "TIM8 Channel 3 Complementary Output"
	default n
	depends on STM32L4_TIM8_CH3OUT
	---help---
		Enables channel 3 complementary output.

endif # STM32L4_TIM8_CHANNEL3

config STM32L4_TIM8_CHANNEL4
	bool "TIM8 Channel 4"
	default n
	---help---
		Enables channel 4.

if STM32L4_TIM8_CHANNEL4

config STM32L4_TIM8_CH4MODE
	int "TIM8 Channel 4 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM8_CH4OUT
	bool "TIM8 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32L4_TIM8_CHANNEL4

endif # STM32L4_PWM_MULTICHAN

if !STM32L4_PWM_MULTICHAN

config STM32L4_TIM8_CHANNEL
	int "TIM8 PWM Output Channel"
	default 1
	range 1 4
	---help---
		If TIM8 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

if STM32L4_TIM8_CHANNEL = 1

config STM32L4_TIM8_CH1OUT
	bool "TIM8 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32L4_TIM8_CH1NOUT
	bool "TIM8 Channel 1 Complementary Output"
	default n
	---help---
		Enables channel 1 Complementary Output.

endif # STM32L4_TIM8_CHANNEL = 1

if STM32L4_TIM8_CHANNEL = 2

config STM32L4_TIM8_CH2OUT
	bool "TIM8 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

config STM32L4_TIM8_CH2NOUT
	bool "TIM8 Channel 2 Complementary Output"
	default n
	---help---
		Enables channel 2 Complementary Output.

endif # STM32L4_TIM8_CHANNEL = 2

if STM32L4_TIM8_CHANNEL = 3

config STM32L4_TIM8_CH3OUT
	bool "TIM8 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

config STM32L4_TIM8_CH3NOUT
	bool "TIM8 Channel 3 Complementary Output"
	default n
	---help---
		Enables channel 3 Complementary Output.

endif # STM32L4_TIM8_CHANNEL = 3

if STM32L4_TIM8_CHANNEL = 4

config STM32L4_TIM8_CH4OUT
	bool "TIM8 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32L4_TIM8_CHANNEL = 4

config STM32L4_TIM8_CHMODE
	int "TIM8 Channel Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

endif # !STM32L4_PWM_MULTICHAN

endif # STM32L4_TIM8_PWM

config STM32L4_TIM15_PWM
	bool "TIM15 PWM"
	default n
	depends on STM32L4_TIM15
	select STM32L4_PWM
	---help---
		Reserve timer 15 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32L4_TIM15
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32L4_TIM15_PWM

config STM32L4_TIM15_LOCK
	int "TIM15 Lock Level Configuration"
	default 0
	range 0 3
	---help---
		Timer 15 lock level configuration

config STM32L4_TIM15_TDTS
	int "TIM15 t_DTS Division"
	default 0
	range 0 2
	---help---
		Timer 15 dead-time and sampling clock (t_DTS) division

config STM32L4_TIM15_DEADTIME
	int "TIM15 Initial Dead-time"
	default 0
	range 0 255
	---help---
		Timer 15 initial dead-time

if STM32L4_PWM_MULTICHAN

config STM32L4_TIM15_CHANNEL1
	bool "TIM15 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32L4_TIM15_CHANNEL1

config STM32L4_TIM15_CH1MODE
	int "TIM15 Channel 1 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM15_CH1OUT
	bool "TIM15 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32L4_TIM15_CH1NOUT
	bool "TIM15 Channel 1 Complementary Output"
	default n
	depends on STM32L4_TIM15_CH1OUT
	---help---
		Enables channel 1 complementary output.

endif # STM32L4_TIM15_CHANNEL1

config STM32L4_TIM15_CHANNEL2
	bool "TIM15 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32L4_TIM15_CHANNEL2

config STM32L4_TIM15_CH2MODE
	int "TIM15 Channel 2 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM15_CH2OUT
	bool "TIM15 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32L4_TIM15_CHANNEL2

endif # STM32L4_PWM_MULTICHAN

if !STM32L4_PWM_MULTICHAN

config STM32L4_TIM15_CHANNEL
	int "TIM15 PWM Output Channel"
	default 1
	range 1 2
	---help---
		If TIM15 is enabled for PWM usage, you also need specifies the timer output
		channel {1,2}

if STM32L4_TIM15_CHANNEL = 1

config STM32L4_TIM15_CH1OUT
	bool "TIM15 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32L4_TIM15_CH1NOUT
	bool "TIM15 Channel 1 Complementary Output"
	default n
	---help---
		Enables channel 1 Complementary Output.

endif # STM32L4_TIM15_CHANNEL = 1

if STM32L4_TIM15_CHANNEL = 2

config STM32L4_TIM15_CH2OUT
	bool "TIM15 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

config STM32L4_TIM15_CH2NOUT
	bool "TIM15 Channel 2 Complementary Output"
	default n
	---help---
		Enables channel 2 Complementary Output.

endif # STM32L4_TIM15_CHANNEL = 2

config STM32L4_TIM15_CHMODE
	int "TIM15 Channel Mode"
	default 6
	range 0 9
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

endif # !STM32L4_PWM_MULTICHAN

endif # STM32L4_TIM15_PWM

config STM32L4_TIM16_PWM
	bool "TIM16 PWM"
	default n
	depends on STM32L4_TIM16
	select STM32L4_PWM
	---help---
		Reserve timer 16 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32L4_TIM16
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32L4_TIM16_PWM

config STM32L4_TIM16_LOCK
	int "TIM16 Lock Level Configuration"
	default 0
	range 0 3
	---help---
		Timer 16 lock level configuration

config STM32L4_TIM16_TDTS
	int "TIM16 t_DTS Division"
	default 0
	range 0 2
	---help---
		Timer 16 dead-time and sampling clock (t_DTS) division

config STM32L4_TIM16_DEADTIME
	int "TIM16 Initial Dead-time"
	default 0
	range 0 255
	---help---
		Timer 16 initial dead-time

if STM32L4_PWM_MULTICHAN

config STM32L4_TIM16_CHANNEL1
	bool "TIM16 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32L4_TIM16_CHANNEL1

config STM32L4_TIM16_CH1MODE
	int "TIM16 Channel 1 Mode"
	default 6
	range 0 7
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM16_CH1OUT
	bool "TIM16 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32L4_TIM16_CH1NOUT
	bool "TIM16 Channel 1 Complementary Output"
	default n
	depends on STM32L4_TIM16_CH1OUT
	---help---
		Enables channel 1 complementary output.

endif # STM32L4_TIM16_CHANNEL1

endif # STM32L4_PWM_MULTICHAN

if !STM32L4_PWM_MULTICHAN

config STM32L4_TIM16_CHANNEL
	int "TIM16 PWM Output Channel"
	default 1
	range 1 1
	---help---
		If TIM16 is enabled for PWM usage, you also need specifies the timer output
		channel {1}

if STM32L4_TIM16_CHANNEL = 1

config STM32L4_TIM16_CH1OUT
	bool "TIM16 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32L4_TIM16_CHANNEL = 1

config STM32L4_TIM16_CHMODE
	int "TIM16 Channel Mode"
	default 6
	range 0 7
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

endif # !STM32L4_PWM_MULTICHAN

endif # STM32L4_TIM16_PWM

config STM32L4_TIM17_PWM
	bool "TIM17 PWM"
	default n
	depends on STM32L4_TIM17
	select STM32L4_PWM
	---help---
		Reserve timer 17 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32L4_TIM17
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32L4_TIM17_PWM

config STM32L4_TIM17_LOCK
	int "TIM17 Lock Level Configuration"
	default 0
	range 0 3
	---help---
		Timer 17 lock level configuration

config STM32L4_TIM17_TDTS
	int "TIM17 t_DTS Division"
	default 0
	range 0 2
	---help---
		Timer 17 dead-time and sampling clock (t_DTS) division

config STM32L4_TIM17_DEADTIME
	int "TIM17 Initial Dead-time"
	default 0
	range 0 255
	---help---
		Timer 17 initial dead-time

if STM32L4_PWM_MULTICHAN

config STM32L4_TIM17_CHANNEL1
	bool "TIM17 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32L4_TIM17_CHANNEL1

config STM32L4_TIM17_CH1MODE
	int "TIM17 Channel 1 Mode"
	default 6
	range 0 7
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

config STM32L4_TIM17_CH1OUT
	bool "TIM17 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32L4_TIM17_CH1NOUT
	bool "TIM17 Channel 1 Complementary Output"
	default n
	depends on STM32L4_TIM17_CH1OUT
	---help---
		Enables channel 1 complementary output.

endif # STM32L4_TIM17_CHANNEL1

endif # STM32L4_PWM_MULTICHAN

if !STM32L4_PWM_MULTICHAN

config STM32L4_TIM17_CHANNEL
	int "TIM17 PWM Output Channel"
	default 1
	range 1 1
	---help---
		If TIM17 is enabled for PWM usage, you also need specifies the timer output
		channel {1}

if STM32_TIM17_CHANNEL = 1

config STM32L4_TIM17_CH1OUT
	bool "TIM17 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32L4_TIM17_CHANNEL = 1

config STM32L4_TIM17_CHMODE
	int "TIM17 Channel Mode"
	default 6
	range 0 7
	---help---
		Specifies the channel mode. See enum stm32l4_pwm_chanmode_e in stm32l4_pwm.h.

endif # !STM32L4_PWM_MULTICHAN

endif # STM32L4_TIM17_PWM

if STM32L4_LPTIM1_PWM

if STM32L4_PWM_MULTICHAN

config STM32L4_LPTIM1_CHANNEL1
	bool "LPTIM1 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32L4_LPTIM1_CHANNEL1

config STM32L4_LPTIM1_CH1OUT
	bool "LPTIM1 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32L4_LPTIM1_CH1NOUT
	bool "LPTIM1 Channel 1 Complementary Output"
	default n
	depends on STM32L4_LPTIM1_CH1OUT
	---help---
		Enables channel 1 complementary output.

endif # STM32L4_LPTIM1_CHANNEL1

endif # STM32L4_PWM_MULTICHAN

if !STM32L4_PWM_MULTICHAN

config STM32L4_LPTIM1_CHANNEL
	int "LPTIM1 PWM Output Channel"
	default 1
	range 1 1
	---help---
		If LPTIM1 is enabled for PWM usage, you also need specifies the timer output
		channel {1}

if STM32L4_LPTIM1_CHANNEL = 1

config STM32L4_LPTIM1_CH1OUT
	bool "LPTIM1 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32L4_LPTIM1_CH1NOUT
	bool "LPTIM1 Channel 1 Complementary Output"
	default n
	---help---
		Enables channel 1 Complementary Output.

endif # STM32L4_LPTIM1_CHANNEL = 1

endif # !STM32L4_PWM_MULTICHAN

endif # STM32L4_LPTIM1_PWM

if STM32L4_LPTIM2_PWM

if STM32L4_PWM_MULTICHAN

config STM32L4_LPTIM2_CHANNEL1
	bool "LPTIM2 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32L4_LPTIM2_CHANNEL1

config STM32L4_LPTIM2_CH1OUT
	bool "LPTIM2 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32L4_LPTIM2_CH1NOUT
	bool "LPTIM2 Channel 1 Complementary Output"
	default n
	depends on STM32L4_LPTIM2_CH1OUT
	---help---
		Enables channel 1 complementary output.

endif # STM32L4_LPTIM2_CHANNEL1

endif # STM32L4_PWM_MULTICHAN

if !STM32L4_PWM_MULTICHAN

config STM32L4_LPTIM2_CHANNEL
	int "LPTIM2 PWM Output Channel"
	default 1
	range 1 1
	---help---
		If LPTIM2 is enabled for PWM usage, you also need specifies the timer output
		channel {1}

if STM32L4_LPTIM2_CHANNEL = 1

config STM32L4_LPTIM2_CH1OUT
	bool "LPTIM2 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32L4_LPTIM2_CH1NOUT
	bool "LPTIM2 Channel 1 Complementary Output"
	default n
	---help---
		Enables channel 1 Complementary Output.

endif # STM32L4_LPTIM2_CHANNEL = 1

endif # !STM32L4_PWM_MULTICHAN

endif # STM32L4_LPTIM2_PWM

config STM32L4_PWM_MULTICHAN
	bool "PWM Multiple Output Channels"
	default n
	depends on STM32L4_PWM
	select ARCH_HAVE_PWM_MULTICHAN
	---help---
		Specifies that the PWM driver supports multiple output
		channels per timer.

config STM32L4_TIM1_ADC
	bool "TIM1 ADC"
	default n
	depends on STM32L4_TIM1 && STM32L4_ADC
	---help---
		Reserve timer 1 for use by ADC

		Timer devices may be used for different purposes.  If STM32L4_TIM1 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for ADC conversion. Note that ADC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the ADC, but then you also have to configure which ADC
		channel it is assigned to.

choice
	prompt "Select ADC to trigger"
	default STM32L4_TIM1_ADC1
	depends on STM32L4_TIM1_ADC

config STM32L4_TIM1_ADC1
	bool "TIM1 trigger ADC1"
	depends on STM32L4_ADC1
	select STM32L4_HAVE_ADC1_TIMER
	---help---
		Reserve TIM1 to trigger ADC1

config STM32L4_TIM1_ADC2
	bool "TIM1 trigger ADC2"
	depends on STM32L4_ADC2
	select STM32L4_HAVE_ADC2_TIMER
	---help---
		Reserve TIM1 to trigger ADC2

config STM32L4_TIM1_ADC3
	bool "TIM1 trigger ADC3"
	depends on STM32L4_ADC3
	select STM32L4_HAVE_ADC3_TIMER
	---help---
		Reserve TIM1 to trigger ADC3

endchoice

config STM32L4_TIM1_ADC_CHAN
	int "TIM1 channel"
	default 1
	range 1 4
	depends on STM32L4_TIM1_ADC
	---help---
		Values 1:CC1 2:CC2 3:CC3 4:CC4

config STM32L4_TIM2_ADC
	bool "TIM2 ADC"
	default n
	depends on STM32L4_TIM2 && STM32L4_ADC
	---help---
		Reserve timer 2 for use by ADC

		Timer devices may be used for different purposes.  If STM32L4_TIM2 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for ADC conversion. Note that ADC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the ADC, but then you also have to configure which ADC
		channel it is assigned to.

choice
	prompt "Select ADC to trigger"
	default STM32L4_TIM2_ADC1
	depends on STM32L4_TIM2_ADC

config STM32L4_TIM2_ADC1
	bool "TIM2 trigger ADC1"
	depends on STM32L4_ADC1
	select STM32L4_HAVE_ADC1_TIMER
	---help---
		Reserve TIM2 to trigger ADC1

config STM32L4_TIM2_ADC2
	bool "TIM2 trigger ADC2"
	depends on STM32L4_ADC2
	select STM32L4_HAVE_ADC2_TIMER
	---help---
		Reserve TIM2 to trigger ADC2

config STM32L4_TIM2_ADC3
	bool "TIM2 trigger ADC3"
	depends on STM32L4_ADC3
	select STM32L4_HAVE_ADC3_TIMER
	---help---
		Reserve TIM2 to trigger ADC3

endchoice

config STM32L4_TIM2_ADC_CHAN
	int "TIM2 channel"
	default 1
	range 1 4
	depends on STM32L4_TIM2_ADC
	---help---
		Values 1:CC1 2:CC2 3:CC3 4:CC4

config STM32L4_TIM3_ADC
	bool "TIM3 ADC"
	default n
	depends on STM32L4_TIM3 && STM32L4_ADC
	---help---
		Reserve timer 3 for use by ADC

		Timer devices may be used for different purposes.  If STM32L4_TIM3 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for ADC conversion. Note that ADC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the ADC, but then you also have to configure which ADC
		channel it is assigned to.

choice
	prompt "Select ADC to trigger"
	default STM32L4_TIM3_ADC1
	depends on STM32L4_TIM3_ADC

config STM32L4_TIM3_ADC1
	bool "TIM3 trigger ADC1"
	depends on STM32L4_ADC1
	select STM32L4_HAVE_ADC1_TIMER
	---help---
		Reserve TIM3 to trigger ADC1

config STM32L4_TIM3_ADC2
	bool "TIM3 trigger ADC2"
	depends on STM32L4_ADC2
	select STM32L4_HAVE_ADC2_TIMER
	---help---
		Reserve TIM3 to trigger ADC2

config STM32L4_TIM3_ADC3
	bool "TIM3 trigger ADC3"
	depends on STM32L4_ADC3
	select STM32L4_HAVE_ADC3_TIMER
	---help---
		Reserve TIM3 to trigger ADC3

endchoice

config STM32L4_TIM3_ADC_CHAN
	int "TIM3 channel"
	default 1
	range 1 4
	depends on STM32L4_TIM3_ADC
	---help---
		Values 1:CC2 2:CC2 3:CC3 4:CC4

config STM32L4_TIM4_ADC
	bool "TIM4 ADC"
	default n
	depends on STM32L4_TIM4 && STM32L4_ADC
	---help---
		Reserve timer 4 for use by ADC

		Timer devices may be used for different purposes.  If STM32L4_TIM4 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for ADC conversion. Note that ADC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the ADC, but then you also have to configure which ADC
		channel it is assigned to.

choice
	prompt "Select ADC to trigger"
	default STM32L4_TIM4_ADC1
	depends on STM32L4_TIM4_ADC

config STM32L4_TIM4_ADC1
	bool "TIM4 trigger ADC1"
	depends on STM32L4_ADC1
	select STM32L4_HAVE_ADC1_TIMER
	---help---
		Reserve TIM4 to trigger ADC1

config STM32L4_TIM4_ADC2
	bool "TIM4 trigger ADC2"
	depends on STM32L4_ADC2
	select STM32L4_HAVE_ADC2_TIMER
	---help---
		Reserve TIM4 to trigger ADC2

config STM32L4_TIM4_ADC3
	bool "TIM4 trigger ADC3"
	depends on STM32L4_ADC3
	select STM32L4_HAVE_ADC3_TIMER
	---help---
		Reserve TIM4 to trigger ADC3

endchoice

config STM32L4_TIM4_ADC_CHAN
	int "TIM4 channel"
	default 1
	range 1 4
	depends on STM32L4_TIM4_ADC
	---help---
		Values 1:CC2 2:CC2 3:CC3 4:CC4

config STM32L4_TIM6_ADC
	bool "TIM6 ADC"
	default n
	depends on STM32L4_TIM6 && STM32L4_ADC
	---help---
		Reserve timer 6 for use by ADC

		Timer devices may be used for different purposes.  If STM32L4_TIM6 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for ADC conversion. Note that ADC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the ADC, but then you also have to configure which ADC
		channel it is assigned to.

choice
	prompt "Select ADC to trigger"
	default STM32L4_TIM6_ADC1
	depends on STM32L4_TIM6_ADC

config STM32L4_TIM6_ADC1
	bool "TIM6 trigger ADC1"
	depends on STM32L4_ADC1
	select STM32L4_HAVE_ADC1_TIMER
	---help---
		Reserve TIM6 to trigger ADC1

config STM32L4_TIM6_ADC2
	bool "TIM6 trigger ADC2"
	depends on STM32L4_ADC2
	select STM32L4_HAVE_ADC2_TIMER
	---help---
		Reserve TIM6 to trigger ADC2

config STM32L4_TIM6_ADC3
	bool "TIM6 trigger ADC3"
	depends on STM32L4_ADC3
	select STM32L4_HAVE_ADC3_TIMER
	---help---
		Reserve TIM6 to trigger ADC3

endchoice

config STM32L4_TIM6_ADC_CHAN
	int "TIM6 channel"
	default 1
	range 1 4
	depends on STM32L4_TIM6_ADC
	---help---
		Values 1:CC2 2:CC2 3:CC3 4:CC4

config STM32L4_TIM8_ADC
	bool "TIM8 ADC"
	default n
	depends on STM32L4_TIM8 && STM32L4_ADC
	---help---
		Reserve timer 8 for use by ADC

		Timer devices may be used for different purposes.  If STM32L4_TIM8 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for ADC conversion. Note that ADC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the ADC, but then you also have to configure which ADC
		channel it is assigned to.

choice
	prompt "Select ADC to trigger"
	default STM32L4_TIM8_ADC1
	depends on STM32L4_TIM8_ADC

config STM32L4_TIM8_ADC1
	bool "TIM8 trigger ADC1"
	depends on STM32L4_ADC1
	select STM32L4_HAVE_ADC1_TIMER
	---help---
		Reserve TIM8 to trigger ADC1

config STM32L4_TIM8_ADC2
	bool "TIM8 trigger ADC2"
	depends on STM32L4_ADC2
	select STM32L4_HAVE_ADC2_TIMER
	---help---
		Reserve TIM8 to trigger ADC2

config STM32L4_TIM8_ADC3
	bool "TIM8 trigger ADC3"
	depends on STM32L4_ADC3
	select STM32L4_HAVE_ADC3_TIMER
	---help---
		Reserve TIM8 to trigger ADC3

endchoice

config STM32L4_TIM8_ADC_CHAN
	int "TIM8 channel"
	default 1
	range 1 4
	depends on STM32L4_TIM8_ADC
	---help---
		Values 1:CC2 2:CC2 3:CC3 4:CC4

config STM32L4_TIM15_ADC
	bool "TIM15 ADC"
	default n
	depends on STM32L4_TIM15 && STM32L4_ADC
	---help---
		Reserve timer 15 for use by ADC

		Timer devices may be used for different purposes.  If STM32L4_TIM15 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for ADC conversion. Note that ADC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the ADC, but then you also have to configure which ADC
		channel it is assigned to.

choice
	prompt "Select ADC to trigger"
	default STM32L4_TIM15_ADC1
	depends on STM32L4_TIM15_ADC

config STM32L4_TIM15_ADC1
	bool "TIM15 trigger ADC1"
	depends on STM32L4_ADC1
	select STM32L4_HAVE_ADC1_TIMER
	---help---
		Reserve TIM15 to trigger ADC1

config STM32L4_TIM15_ADC2
	bool "TIM15 trigger ADC2"
	depends on STM32L4_ADC2
	select STM32L4_HAVE_ADC2_TIMER
	---help---
		Reserve TIM15 to trigger ADC2

config STM32L4_TIM15_ADC3
	bool "TIM15 trigger ADC3"
	depends on STM32L4_ADC3
	select STM32L4_HAVE_ADC3_TIMER
	---help---
		Reserve TIM15 to trigger ADC3

endchoice

config STM32L4_TIM15_ADC_CHAN
	int "TIM15 channel"
	default 1
	range 1 4
	depends on STM32L4_TIM15_ADC
	---help---
		Values 1:CC2 2:CC2 3:CC3 4:CC4

config STM32L4_HAVE_ADC1_TIMER
	bool

config STM32L4_HAVE_ADC2_TIMER
	bool

config STM32L4_HAVE_ADC3_TIMER
	bool

config STM32L4_ADC1_SAMPLE_FREQUENCY
	int "ADC1 Sampling Frequency"
	default 100
	depends on STM32L4_HAVE_ADC1_TIMER
	---help---
		ADC1 sampling frequency.  Default:  100Hz

config STM32L4_ADC2_SAMPLE_FREQUENCY
	int "ADC2 Sampling Frequency"
	default 100
	depends on STM32L4_HAVE_ADC2_TIMER
	---help---
		ADC2 sampling frequency.  Default:  100Hz

config STM32L4_ADC3_SAMPLE_FREQUENCY
	int "ADC3 Sampling Frequency"
	default 100
	depends on STM32L4_HAVE_ADC3_TIMER
	---help---
		ADC3 sampling frequency.  Default:  100Hz

config STM32L4_TIM1_DAC
	bool "TIM1 DAC"
	default n
	depends on STM32L4_TIM1 && STM32L4_DAC
	---help---
		Reserve timer 1 for use by DAC

		Timer devices may be used for different purposes.  If STM32L4_TIM1 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM1 DAC channel"
	default STM32L4_TIM1_DAC1
	depends on STM32L4_TIM1_DAC

config STM32L4_TIM1_DAC1
	bool "TIM1 DAC channel 1"
	---help---
		Reserve TIM1 to trigger DAC1

config STM32L4_TIM1_DAC2
	bool "TIM1 DAC channel 2"
	---help---
		Reserve TIM1 to trigger DAC2

endchoice

config STM32L4_TIM2_DAC
	bool "TIM2 DAC"
	default n
	depends on STM32L4_TIM2 && STM32L4_DAC
	---help---
		Reserve timer 2 for use by DAC

		Timer devices may be used for different purposes.  If STM32L4_TIM2 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM2 DAC channel"
	default STM32L4_TIM2_DAC1
	depends on STM32L4_TIM2_DAC

config STM32L4_TIM2_DAC1
	bool "TIM2 DAC channel 1"
	---help---
		Reserve TIM2 to trigger DAC1

config STM32L4_TIM2_DAC2
	bool "TIM2 DAC channel 2"
	---help---
		Reserve TIM2 to trigger DAC2

endchoice

config STM32L4_TIM3_DAC
	bool "TIM3 DAC"
	default n
	depends on STM32L4_TIM3 && STM32L4_DAC
	---help---
		Reserve timer 3 for use by DAC

		Timer devices may be used for different purposes.  If STM32L4_TIM3 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM3 DAC channel"
	default STM32L4_TIM3_DAC1
	depends on STM32L4_TIM3_DAC

config STM32L4_TIM3_DAC1
	bool "TIM3 DAC channel 1"
	---help---
		Reserve TIM3 to trigger DAC1

config STM32L4_TIM3_DAC2
	bool "TIM3 DAC channel 2"
	---help---
		Reserve TIM3 to trigger DAC2

endchoice

config STM32L4_TIM4_DAC
	bool "TIM4 DAC"
	default n
	depends on STM32L4_TIM4 && STM32L4_DAC
	---help---
		Reserve timer 4 for use by DAC

		Timer devices may be used for different purposes.  If STM32L4_TIM4 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM4 DAC channel"
	default STM32L4_TIM4_DAC1
	depends on STM32L4_TIM4_DAC

config STM32L4_TIM4_DAC1
	bool "TIM4 DAC channel 1"
	---help---
		Reserve TIM4 to trigger DAC1

config STM32L4_TIM4_DAC2
	bool "TIM4 DAC channel 2"
	---help---
		Reserve TIM4 to trigger DAC2

endchoice

config STM32L4_TIM5_DAC
	bool "TIM5 DAC"
	default n
	depends on STM32L4_TIM5 && STM32L4_DAC
	---help---
		Reserve timer 5 for use by DAC

		Timer devices may be used for different purposes.  If STM32L4_TIM5 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM5 DAC channel"
	default STM32L4_TIM5_DAC1
	depends on STM32L4_TIM5_DAC

config STM32L4_TIM5_DAC1
	bool "TIM5 DAC channel 1"
	---help---
		Reserve TIM5 to trigger DAC1

config STM32L4_TIM5_DAC2
	bool "TIM5 DAC channel 2"
	---help---
		Reserve TIM5 to trigger DAC2

endchoice

config STM32L4_TIM6_DAC
	bool "TIM6 DAC"
	default n
	depends on STM32L4_TIM6 && STM32L4_DAC
	---help---
		Reserve timer 6 for use by DAC

		Timer devices may be used for different purposes.  If STM32L4_TIM6 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM6 DAC channel"
	default STM32L4_TIM6_DAC1
	depends on STM32L4_TIM6_DAC

config STM32L4_TIM6_DAC1
	bool "TIM6 DAC channel 1"
	---help---
		Reserve TIM6 to trigger DAC1

config STM32L4_TIM6_DAC2
	bool "TIM6 DAC channel 2"
	---help---
		Reserve TIM6 to trigger DAC2

endchoice

config STM32L4_TIM7_DAC
	bool "TIM7 DAC"
	default n
	depends on STM32L4_TIM7 && STM32L4_DAC
	---help---
		Reserve timer 7 for use by DAC

		Timer devices may be used for different purposes.  If STM32L4_TIM7 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM7 DAC channel"
	default STM32L4_TIM7_DAC1
	depends on STM32L4_TIM7_DAC

config STM32L4_TIM7_DAC1
	bool "TIM7 DAC channel 1"
	---help---
		Reserve TIM7 to trigger DAC1

config STM32L4_TIM7_DAC2
	bool "TIM7 DAC channel 2"
	---help---
		Reserve TIM7 to trigger DAC2

endchoice

config STM32L4_TIM8_DAC
	bool "TIM8 DAC"
	default n
	depends on STM32L4_TIM8 && STM32L4_DAC
	---help---
		Reserve timer 8 for use by DAC

		Timer devices may be used for different purposes.  If STM32L4_TIM8 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM8 DAC channel"
	default STM32L4_TIM8_DAC1
	depends on STM32L4_TIM8_DAC

config STM32L4_TIM8_DAC1
	bool "TIM8 DAC channel 1"
	---help---
		Reserve TIM8 to trigger DAC1

config STM32L4_TIM8_DAC2
	bool "TIM8 DAC channel 2"
	---help---
		Reserve TIM8 to trigger DAC2

endchoice

config STM32L4_TIM1_CAP
	bool "TIM1 Capture"
	default n
	depends on STM32L4_HAVE_TIM1
	---help---
		Reserve timer 1 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

config STM32L4_TIM2_CAP
	bool "TIM2 Capture"
	default n
	depends on STM32L4_HAVE_TIM2
	---help---
		Reserve timer 2 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

config STM32L4_TIM3_CAP
	bool "TIM3 Capture"
	default n
	depends on STM32L4_HAVE_TIM3
	---help---
		Reserve timer 3 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

config STM32L4_TIM4_CAP
	bool "TIM4 Capture"
	default n
	depends on STM32L4_HAVE_TIM4
	---help---
		Reserve timer 4 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

config STM32L4_TIM5_CAP
	bool "TIM5 Capture"
	default n
	depends on STM32L4_HAVE_TIM5
	---help---
		Reserve timer 5 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

config STM32L4_TIM8_CAP
	bool "TIM8 Capture"
	default n
	depends on STM32L4_HAVE_TIM8
	---help---
		Reserve timer 8 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

menu "STM32L4 TIMx Outputs Configuration"

config STM32L4_TIM1_CH1POL
	int "TIM1 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM1_CH1OUT
	---help---
		TIM1 Channel 1 output polarity

config STM32L4_TIM1_CH1IDLE
	int "TIM1 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM1_CH1OUT
	---help---
		TIM1 Channel 1 output IDLE

config STM32L4_TIM1_CH1NPOL
	int "TIM1 Channel 1 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM1_CH1NOUT
	---help---
		TIM1 Channel 1 Complementary Output polarity

config STM32L4_TIM1_CH1NIDLE
	int "TIM1 Channel 1 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM1_CH1NOUT
	---help---
		TIM1 Channel 1 Complementary Output IDLE

config STM32L4_TIM1_CH2POL
	int "TIM1 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM1_CH2OUT
	---help---
		TIM1 Channel 2 output polarity

config STM32L4_TIM1_CH2IDLE
	int "TIM1 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM1_CH2OUT
	---help---
		TIM1 Channel 2 output IDLE

config STM32L4_TIM1_CH2NPOL
	int "TIM1 Channel 2 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM1_CH2NOUT
	---help---
		TIM1 Channel 2 Complementary Output polarity

config STM32L4_TIM1_CH2NIDLE
	int "TIM1 Channel 2 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM1_CH2NOUT
	---help---
		TIM1 Channel 2 Complementary Output IDLE

config STM32L4_TIM1_CH3POL
	int "TIM1 Channel 3 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM1_CH3OUT
	---help---
		TIM1 Channel 3 output polarity

config STM32L4_TIM1_CH3IDLE
	int "TIM1 Channel 3 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM1_CH3OUT
	---help---
		TIM1 Channel 3 output IDLE

config STM32L4_TIM1_CH3NPOL
	int "TIM1 Channel 3 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM1_CH3NOUT
	---help---
		TIM1 Channel 3 Complementary Output polarity

config STM32L4_TIM1_CH3NIDLE
	int "TIM1 Channel 3 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM1_CH3NOUT
	---help---
		TIM1 Channel 3 Complementary Output IDLE

config STM32L4_TIM1_CH4POL
	int "TIM1 Channel 4 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM1_CH4OUT
	---help---
		TIM1 Channel 4 output polarity

config STM32L4_TIM1_CH4IDLE
	int "TIM1 Channel 4 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM1_CH4OUT
	---help---
		TIM1 Channel 4 output IDLE

config STM32L4_TIM1_CH5POL
	int "TIM1 Channel 5 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM1_CH5OUT
	---help---
		TIM1 Channel 5 output polarity

config STM32L4_TIM1_CH5IDLE
	int "TIM1 Channel 5 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM1_CH5OUT
	---help---
		TIM1 Channel 5 output IDLE

config STM32L4_TIM1_CH6POL
	int "TIM1 Channel 6 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM1_CH6OUT
	---help---
		TIM1 Channel 6 output polarity

config STM32L4_TIM1_CH6IDLE
	int "TIM1 Channel 6 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM1_CH6OUT
	---help---
		TIM1 Channel 6 output IDLE

config STM32L4_TIM2_CH1POL
	int "TIM2 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM2_CH1OUT
	---help---
		TIM2 Channel 1 output polarity

config STM32L4_TIM2_CH1IDLE
	int "TIM2 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM2_CH1OUT
	---help---
		TIM2 Channel 1 output IDLE

config STM32L4_TIM2_CH2POL
	int "TIM2 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM2_CH2OUT
	---help---
		TIM2 Channel 2 output polarity

config STM32L4_TIM2_CH2IDLE
	int "TIM2 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM2_CH2OUT
	---help---
		TIM2 Channel 2 output IDLE

config STM32L4_TIM2_CH3POL
	int "TIM2 Channel 3 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM2_CH3OUT
	---help---
		TIM2 Channel 3 output polarity

config STM32L4_TIM2_CH3IDLE
	int "TIM2 Channel 3 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM2_CH3OUT
	---help---
		TIM2 Channel 3 output IDLE

config STM32L4_TIM2_CH4POL
	int "TIM2 Channel 4 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM2_CH4OUT
	---help---
		TIM2 Channel 4 output polarity

config STM32L4_TIM2_CH4IDLE
	int "TIM2 Channel 4 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM2_CH4OUT
	---help---
		TIM2 Channel 4 output IDLE

config STM32L4_TIM3_CH1POL
	int "TIM3 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM3_CH1OUT
	---help---
		TIM3 Channel 1 output polarity

config STM32L4_TIM3_CH1IDLE
	int "TIM3 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM3_CH1OUT
	---help---
		TIM3 Channel 1 output IDLE

config STM32L4_TIM3_CH2POL
	int "TIM3 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM3_CH2OUT
	---help---
		TIM3 Channel 2 output polarity

config STM32L4_TIM3_CH2IDLE
	int "TIM3 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM3_CH2OUT
	---help---
		TIM3 Channel 2 output IDLE

config STM32L4_TIM3_CH3POL
	int "TIM3 Channel 3 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM3_CH3OUT
	---help---
		TIM3 Channel 3 output polarity

config STM32L4_TIM3_CH3IDLE
	int "TIM3 Channel 3 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM3_CH3OUT
	---help---
		TIM3 Channel 3 output IDLE

config STM32L4_TIM3_CH4POL
	int "TIM3 Channel 4 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM3_CH4OUT
	---help---
		TIM3 Channel 4 output polarity

config STM32L4_TIM3_CH4IDLE
	int "TIM3 Channel 4 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM3_CH4OUT
	---help---
		TIM3 Channel 4 output IDLE

config STM32L4_TIM4_CH1POL
	int "TIM4 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM4_CH1OUT
	---help---
		TIM4 Channel 1 output polarity

config STM32L4_TIM4_CH1IDLE
	int "TIM4 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM4_CH1OUT
	---help---
		TIM4 Channel 1 output IDLE

config STM32L4_TIM4_CH2POL
	int "TIM4 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM4_CH2OUT
	---help---
		TIM4 Channel 2 output polarity

config STM32L4_TIM4_CH2IDLE
	int "TIM4 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM4_CH2OUT
	---help---
		TIM4 Channel 2 output IDLE

config STM32L4_TIM4_CH3POL
	int "TIM4 Channel 3 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM4_CH3OUT
	---help---
		TIM4 Channel 3 output polarity

config STM32L4_TIM4_CH3IDLE
	int "TIM4 Channel 3 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM4_CH3OUT
	---help---
		TIM4 Channel 3 output IDLE

config STM32L4_TIM4_CH4POL
	int "TIM4 Channel 4 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM4_CH4OUT
	---help---
		TIM4 Channel 4 output polarity

config STM32L4_TIM4_CH4IDLE
	int "TIM4 Channel 4 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM4_CH4OUT
	---help---
		TIM4 Channel 4 output IDLE

config STM32L4_TIM5_CH1POL
	int "TIM5 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM5_CH1OUT
	---help---
		TIM5 Channel 1 output polarity

config STM32L4_TIM5_CH1IDLE
	int "TIM5 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM5_CH1OUT
	---help---
		TIM5 Channel 1 output IDLE

config STM32L4_TIM5_CH2POL
	int "TIM5 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM5_CH2OUT
	---help---
		TIM5 Channel 2 output polarity

config STM32L4_TIM5_CH2IDLE
	int "TIM5 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM5_CH2OUT
	---help---
		TIM5 Channel 2 output IDLE

config STM32L4_TIM5_CH3POL
	int "TIM5 Channel 3 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM5_CH3OUT
	---help---
		TIM5 Channel 3 output polarity

config STM32L4_TIM5_CH3IDLE
	int "TIM5 Channel 3 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM5_CH3OUT
	---help---
		TIM5 Channel 3 output IDLE

config STM32L4_TIM5_CH4POL
	int "TIM5 Channel 4 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM5_CH4OUT
	---help---
		TIM5 Channel 4 output polarity

config STM32L4_TIM5_CH4IDLE
	int "TIM5 Channel 4 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM5_CH4OUT
	---help---
		TIM5 Channel 4 output IDLE

config STM32L4_TIM8_CH1POL
	int "TIM8 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM8_CH1OUT
	---help---
		TIM8 Channel 1 output polarity

config STM32L4_TIM8_CH1IDLE
	int "TIM8 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM8_CH1OUT
	---help---
		TIM8 Channel 1 output IDLE

config STM32L4_TIM8_CH1NPOL
	int "TIM8 Channel 1 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM8_CH1NOUT
	---help---
		TIM8 Channel 1 Complementary Output polarity

config STM32L4_TIM8_CH1NIDLE
	int "TIM8 Channel 1 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM8_CH1NOUT
	---help---
		TIM8 Channel 1 Complementary Output IDLE

config STM32L4_TIM8_CH2POL
	int "TIM8 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM8_CH2OUT
	---help---
		TIM8 Channel 2 output polarity

config STM32L4_TIM8_CH2IDLE
	int "TIM8 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM8_CH2OUT
	---help---
		TIM8 Channel 2 output IDLE

config STM32L4_TIM8_CH2NPOL
	int "TIM8 Channel 2 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM8_CH2NOUT
	---help---
		TIM8 Channel 2 Complementary Output polarity

config STM32L4_TIM8_CH2NIDLE
	int "TIM8 Channel 2 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM8_CH2NOUT
	---help---
		TIM8 Channel 2 Complementary Output IDLE

config STM32L4_TIM8_CH3POL
	int "TIM8 Channel 3 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM8_CH3OUT
	---help---
		TIM8 Channel 3 output polarity

config STM32L4_TIM8_CH3IDLE
	int "TIM8 Channel 3 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM8_CH3OUT
	---help---
		TIM8 Channel 3 output IDLE

config STM32L4_TIM8_CH3NPOL
	int "TIM8 Channel 3 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM8_CH3NOUT
	---help---
		TIM8 Channel 3 Complementary Output polarity

config STM32L4_TIM8_CH3NIDLE
	int "TIM8 Channel 3 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM8_CH3NOUT
	---help---
		TIM8 Channel 3 Complementary Output IDLE

config STM32L4_TIM8_CH4POL
	int "TIM8 Channel 4 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM8_CH4OUT
	---help---
		TIM8 Channel 4 output polarity

config STM32L4_TIM8_CH4IDLE
	int "TIM8 Channel 4 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM8_CH4OUT
	---help---
		TIM8 Channel 4 output IDLE

config STM32L4_TIM8_CH5POL
	int "TIM8 Channel 5 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM8_CH5OUT
	---help---
		TIM8 Channel 5 output polarity

config STM32L4_TIM8_CH5IDLE
	int "TIM8 Channel 5 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM8_CH5OUT
	---help---
		TIM8 Channel 5 output IDLE

config STM32L4_TIM8_CH6POL
	int "TIM8 Channel 6 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM8_CH6OUT
	---help---
		TIM8 Channel 6 output polarity

config STM32L4_TIM8_CH6IDLE
	int "TIM8 Channel 6 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM8_CH6OUT
	---help---
		TIM8 Channel 6 output IDLE

config STM32L4_TIM9_CH1POL
	int "TIM9 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM9_CH1OUT
	---help---
		TIM9 Channel 1 output polarity

config STM32L4_TIM9_CH1IDLE
	int "TIM9 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM9_CH1OUT
	---help---
		TIM9 Channel 1 output IDLE

config STM32L4_TIM9_CH2POL
	int "TIM9 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM9_CH2OUT
	---help---
		TIM9 Channel 2 output polarity

config STM32L4_TIM9_CH2IDLE
	int "TIM9 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM9_CH2OUT
	---help---
		TIM9 Channel 2 output IDLE

config STM32L4_TIM10_CH1POL
	int "TIM10 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM10_CH1OUT
	---help---
		TIM10 Channel 1 output polarity

config STM32L4_TIM10_CH1IDLE
	int "TIM10 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM10_CH1OUT
	---help---
		TIM10 Channel 1 output IDLE

config STM32L4_TIM11_CH1POL
	int "TIM11 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM11_CH1OUT
	---help---
		TIM11 Channel 1 output polarity

config STM32L4_TIM11_CH1IDLE
	int "TIM11 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM11_CH1OUT
	---help---
		TIM11 Channel 1 output IDLE

config STM32L4_TIM12_CH1POL
	int "TIM12 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM12_CH1OUT
	---help---
		TIM12 Channel 1 output polarity

config STM32L4_TIM12_CH1IDLE
	int "TIM12 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM12_CH1OUT
	---help---
		TIM12 Channel 1 output IDLE

config STM32L4_TIM12_CH2POL
	int "TIM12 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM12_CH2OUT
	---help---
		TIM12 Channel 2 output polarity

config STM32L4_TIM12_CH2IDLE
	int "TIM12 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM12_CH2OUT
	---help---
		TIM12 Channel 2 output IDLE

config STM32L4_TIM13_CH1POL
	int "TIM13 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM13_CH1OUT
	---help---
		TIM13 Channel 1 output polarity

config STM32L4_TIM13_CH1IDLE
	int "TIM13 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM13_CH1OUT
	---help---
		TIM13 Channel 1 output IDLE

config STM32L4_TIM14_CH1POL
	int "TIM14 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM14_CH1OUT
	---help---
		TIM14 Channel 1 output polarity

config STM32L4_TIM14_CH1IDLE
	int "TIM14 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM14_CH1OUT
	---help---
		TIM14 Channel 1 output IDLE

config STM32L4_TIM15_CH1POL
	int "TIM15 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM15_CH1OUT
	---help---
		TIM15 Channel 1 output polarity

config STM32L4_TIM15_CH1IDLE
	int "TIM15 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM15_CH1OUT
	---help---
		TIM15 Channel 1 output IDLE

config STM32L4_TIM15_CH1NPOL
	int "TIM15 Channel 1 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM15_CH1NOUT
	---help---
		TIM15 Channel 1 Complementary Output polarity

config STM32L4_TIM15_CH1NIDLE
	int "TIM15 Channel 1 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM15_CH1NOUT
	---help---
		TIM15 Channel 1 Complementary Output IDLE

config STM32L4_TIM15_CH2POL
	int "TIM15 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM15_CH2OUT
	---help---
		TIM15 Channel 2 output polarity

config STM32L4_TIM15_CH2IDLE
	int "TIM15 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM15_CH2OUT
	---help---
		TIM15 Channel 2 output IDLE

config STM32L4_TIM15_CH2NPOL
	int "TIM15 Channel 2 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM15_CH2NOUT
	---help---
		TIM15 Channel 2 Complementary Output polarity

config STM32L4_TIM15_CH2NIDLE
	int "TIM15 Channel 2 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM15_CH2NOUT
	---help---
		TIM15 Channel 2 Complementary Output IDLE

config STM32L4_TIM16_CH1POL
	int "TIM16 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM16_CH1OUT
	---help---
		TIM16 Channel 1 output polarity

config STM32L4_TIM16_CH1IDLE
	int "TIM16 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM16_CH1OUT
	---help---
		TIM16 Channel 1 output IDLE

config STM32L4_TIM17_CH1POL
	int "TIM17 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32L4_TIM17_CH1OUT
	---help---
		TIM17 Channel 1 output polarity

config STM32L4_TIM17_CH1IDLE
	int "TIM17 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32L4_TIM17_CH1OUT
	---help---
		TIM17 Channel 1 output IDLE

endmenu #STM32L4 TIMx Outputs Configuration

endmenu # Timer Configuration

menu "ADC Configuration"
	depends on STM32L4_ADC

config STM32L4_ADC_NO_STARTUP_CONV
	bool "Do not start conversion when opening ADC device"
	default n
	---help---
		Do not start conversion when opening ADC device.

config STM32L4_ADC_NOIRQ
	bool "Do not use default ADC interrupts"
	default n
	---help---
		Do not use default ADC interrupts handlers.

config STM32L4_ADC_SMPR
	int "ADC sample time"
	default 0
	range 0 7
	---help---
		ADC sample time
		  0 -   2.5 ADC clock cycles
			1 -   6.5 ADC clock cycles
			2 -  12.5 ADC clock cycles
			3 -  24.5 ADC clock cycles
			4 -  47.5 ADC clock cycles
			5 -  92.5 ADC clock cycles
			6 - 247.5 ADC clock cycles
			7 - 640.5 ADC clock cycles

config STM32L4_ADC_LL_OPS
	bool "ADC low-level operations"
	default n
	---help---
		Enable low-level ADC ops.

config STM32L4_ADC1_RESOLUTION
	int "ADC1 resolution"
	depends on STM32L4_ADC1
	default 0
	range 0 3
	---help---
		ADC1 resolution. 0 - 12 bit, 1 - 10 bit, 2 - 8 bit, 3 - 6 bit

config STM32L4_ADC2_RESOLUTION
	int "ADC2 resolution"
	depends on STM32L4_ADC2
	default 0
	range 0 3
	---help---
		ADC2 resolution. 0 - 12 bit, 1 - 10 bit, 2 - 8 bit, 3 - 6 bit

config STM32L4_ADC3_RESOLUTION
	int "ADC3 resolution"
	depends on STM32L4_ADC3
	default 0
	range 0 3
	---help---
		ADC3 resolution. 0 - 12 bit, 1 - 10 bit, 2 - 8 bit, 3 - 6 bit

config STM32L4_ADC1_DMA
	bool "ADC1 DMA"
	depends on STM32L4_ADC1
	default n
	---help---
		If DMA is selected, then the ADC may be configured to support
		DMA transfer, which is necessary if multiple channels are read
		or if very high trigger frequencies are used.

config STM32L4_ADC1_DMA_CFG
	int "ADC1 DMA configuration"
	depends on STM32L4_ADC1_DMA
	range 0 1
	default 1
	---help---
		0 - ADC1 DMA in One Shot Mode, 1 - ADC1 DMA in Circular Mode

config STM32L4_ADC2_DMA
	bool "ADC2 DMA"
	depends on STM32L4_ADC2
	default n
	---help---
		If DMA is selected, then the ADC may be configured to support
		DMA transfer, which is necessary if multiple channels are read
		or if very high trigger frequencies are used.

config STM32L4_ADC2_DMA_CFG
	int "ADC2 DMA configuration"
	depends on STM32L4_ADC2_DMA
	range 0 1
	default 1
	---help---
		0 - ADC2 DMA in One Shot Mode, 1 - ADC2 DMA in Circular Mode

config STM32L4_ADC3_DMA
	bool "ADC3 DMA"
	depends on STM32L4_ADC3
	default n
	---help---
		If DMA is selected, then the ADC may be configured to support
		DMA transfer, which is necessary if multiple channels are read
		or if very high trigger frequencies are used.

config STM32L4_ADC3_DMA_CFG
	int "ADC3 DMA configuration"
	depends on STM32L4_ADC3_DMA
	range 0 1
	default 1
	---help---
		0 - ADC3 DMA in One Shot Mode, 1 - ADC3 DMA in Circular Mode

config STM32L4_ADC1_OUTPUT_DFSDM
	bool "ADC1 output to DFSDM"
	depends on STM32L4_ADC1 && STM32L4_DFSDM1 && (STM32L4_STM32L496XX || STM32L4_STM32L4XR)
	default n
	---help---
		Route ADC1 output directly to DFSDM parallel inputs.

config STM32L4_ADC2_OUTPUT_DFSDM
	bool "ADC2 output to DFSDM"
	depends on STM32L4_ADC2 && STM32L4_DFSDM1 && STM32L4_STM32L496XX
	default n
	---help---
		Route ADC2 output directly to DFSDM parallel inputs.

config STM32L4_ADC3_OUTPUT_DFSDM
	bool "ADC3 output to DFSDM"
	depends on STM32L4_ADC3 && STM32L4_DFSDM1 && STM32L4_STM32L496XX
	default n
	---help---
		Route ADC3 output directly to DFSDM parallel inputs.

menu "STM32L4 ADCx triggering Configuration"

config STM32L4_ADC1_TIMTRIG
	int "ADC1 regular channel trigger"
	default 0
	range 0 4
	depends on STM32L4_HAVE_ADC1_TIMER
	---help---
		Values 0:CC1 1:CC2 2:CC3 3:CC4 4:TRGO

config STM32L4_ADC2_TIMTRIG
	int "ADC2 Timer Trigger"
	default 0
	range 0 4
	depends on STM32L4_HAVE_ADC2_TIMER
	---help---
		Values 0:CC1 1:CC2 2:CC3 3:CC4 4:TRGO

config STM32L4_ADC3_TIMTRIG
	int "ADC3 Timer Trigger"
	default 0
	range 0 4
	depends on STM32L4_HAVE_ADC3_TIMER
	---help---
		Values 0:CC1 1:CC2 2:CC3 3:CC4 4:TRGO

config STM32L4_ADC1_INJ_CHAN
	int "ADC1 configured injected channels"
	depends on STM32L4_ADC1
	range 0 4
	default 0
	---help---
		Number of configured ADC1 injected channels.

config STM32L4_ADC2_INJ_CHAN
	int "ADC2 configured injected channels"
	depends on STM32L4_ADC2
	range 0 4
	default 0
	---help---
		Number of configured ADC2 injected channels.

config STM32L4_ADC3_INJ_CHAN
	int "ADC3 configured injected channels"
	depends on STM32L4_ADC3
	range 0 4
	default 0
	---help---
		Number of configured ADC3 injected channels.

if STM32L4_ADC1_INJ_CHAN > 0

config STM32L4_ADC1_JTIMTRIG
	int "ADC1 external trigger for injected channels"
	default 0
	range 0 5
	depends on STM32L4_HAVE_ADC1_TIMER
	---help---
		Values 0:CC1 1:CC2 2:CC3 3:CC4 4:TRGO 5:TRGO2

endif

if STM32L4_ADC2_INJ_CHAN > 0

config STM32L4_ADC2_JTIMTRIG
	int "ADC2 external trigger for injected channels"
	default 0
	range 0 5
	depends on STM32L4_HAVE_ADC2_TIMER
	---help---
		Values 0:CC1 1:CC2 2:CC3 3:CC4 4:TRGO 5:TRGO2

endif

if STM32L4_ADC3_INJ_CHAN > 0

config STM32L4_ADC3_JTIMTRIG
	int "ADC3 external trigger for injected channels"
	default 0
	range 0 5
	depends on STM32L4_HAVE_ADC3_TIMER
	---help---
		Values 0:CC1 1:CC2 2:CC3 3:CC4 4:TRGO 5:TRGO2

endif

endmenu #STM32L4 ADCx triggering Configuration

endmenu

menu "DAC Configuration"
	depends on STM32L4_DAC

config STM32L4_DAC1_DMA
	bool "DAC1 DMA"
	depends on STM32L4_DAC1
	default n
	---help---
		If DMA is selected, then a timer and output frequency must also be
		provided to support the DMA transfer.  The DMA transfer could be
		supported by an EXTI trigger, but this feature is not currently
		supported by the driver.

if STM32L4_DAC1_DMA

config STM32L4_DAC1_TIMER
	int "DAC1 timer"
	range 2 8

config STM32L4_DAC1_TIMER_FREQUENCY
	int "DAC1 timer frequency"
	default 100
	---help---
		DAC1 output frequency.  Default:  100Hz

config STM32L4_DAC1_DMA_BUFFER_SIZE
	int "DAC1 DMA buffer size"
	default 1

endif

config STM32L4_DAC1_OUTPUT_ADC
	bool "DAC1 output to ADC"
	depends on STM32L4_DAC1
	default n
	---help---
		Route DAC1 output to ADC input instead of external pin.

config STM32L4_DAC2_DMA
	bool "DAC2 DMA"
	depends on STM32L4_DAC2
	default n
	---help---
		If DMA is selected, then a timer and output frequency must also be
		provided to support the DMA transfer.  The DMA transfer could be
		supported by an EXTI trigger, but this feature is not currently
		supported by the driver.

if STM32L4_DAC2_DMA

config STM32L4_DAC2_TIMER
	int "DAC2 timer"
	default 0
	range 2 8

config STM32L4_DAC2_TIMER_FREQUENCY
	int "DAC2 timer frequency"
	default 100
	---help---
		DAC2 output frequency.  Default:  100Hz

config STM32L4_DAC2_DMA_BUFFER_SIZE
	int "DAC2 DMA buffer size"
	default 1

endif

config STM32L4_DAC2_OUTPUT_ADC
	bool "DAC2 output to ADC"
	depends on STM32L4_DAC2
	default n
	---help---
		Route DAC2 output to ADC input instead of external pin.

config STM32L4_DAC_LL_OPS
	bool "DAC low-level operations"
	default n
	---help---
		Enable low-level DAC ops.

endmenu

menu "DFSDM Configuration"
	depends on STM32L4_DFSDM1

config STM32L4_DFSDM1_FLT0
	bool "DFSDM1 Filter 0"
	default n
	select STM32L4_DFSDM

config STM32L4_DFSDM1_FLT1
	bool "DFSDM1 Filter 1"
	default n
	select STM32L4_DFSDM

config STM32L4_DFSDM1_FLT2
	bool "DFSDM1 Filter 2"
	default n
	depends on !STM32L4_STM32L4X3
	select STM32L4_DFSDM

config STM32L4_DFSDM1_FLT3
	bool "DFSDM1 Filter 3"
	default n
	depends on !STM32L4_STM32L4X3
	select STM32L4_DFSDM

config STM32L4_DFSDM1_DMA
	bool "DFSDM1 DMA"
	depends on STM32L4_DFSDM
	default n
	---help---
		If DMA is selected, then the DFSDM may be configured to support
		DMA transfer, which is necessary if multiple channels are read
		or if very high trigger frequencies are used.

endmenu

config STM32L4_SERIALDRIVER
	bool

config STM32L4_1WIREDRIVER
	bool

menu "[LP]U[S]ART Configuration"
	depends on STM32L4_USART

choice
	prompt "LPUART1 Driver Configuration"
	default STM32L4_LPUART1_SERIALDRIVER
	depends on STM32L4_LPUART1

config STM32L4_LPUART1_SERIALDRIVER
	bool "Standard serial driver"
	select LPUART1_SERIALDRIVER
	select STM32L4_SERIALDRIVER

config STM32L4_LPUART1_1WIREDRIVER
	bool "1-Wire driver"
	select STM32L4_1WIREDRIVER

endchoice # LPUART1 Driver Configuration

if LPUART1_SERIALDRIVER

config LPUART1_RS485
	bool "RS-485 on LPUART1"
	default n
	depends on STM32L4_LPUART1
	---help---
		Enable RS-485 interface on LPUART1. Your board config will have to
		provide GPIO_LPUART1_RS485_DIR pin definition. Currently it cannot be
		used with LPUART1_RXDMA.

config LPUART1_RS485_DIR_POLARITY
	int "LPUART1 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on LPUART1_RS485
	---help---
		Polarity of DIR pin for RS-485 on LPUART1. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config LPUART1_RXDMA
	bool "LPUART1 Rx DMA"
	default n
	depends on STM32L4_LPUART1 && (STM32L4_DMA1 || STM32L4_DMA2 || STM32L4_DMAMUX)
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

endif # LPUART1_SERIALDRIVER

choice
	prompt "USART1 Driver Configuration"
	default STM32L4_USART1_SERIALDRIVER
	depends on STM32L4_USART1

config STM32L4_USART1_SERIALDRIVER
	bool "Standard serial driver"
	select USART1_SERIALDRIVER
	select STM32L4_SERIALDRIVER

config STM32L4_USART1_1WIREDRIVER
	bool "1-Wire driver"
	select STM32L4_1WIREDRIVER

endchoice # USART1 Driver Configuration

if USART1_SERIALDRIVER

config USART1_RS485
	bool "RS-485 on USART1"
	default n
	depends on STM32L4_USART1
	---help---
		Enable RS-485 interface on USART1. Your board config will have to
		provide GPIO_USART1_RS485_DIR pin definition. Currently it cannot be
		used with USART1_RXDMA.

config USART1_RS485_DIR_POLARITY
	int "USART1 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on USART1_RS485
	---help---
		Polarity of DIR pin for RS-485 on USART1. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config USART1_RXDMA
	bool "USART1 Rx DMA"
	default n
	depends on STM32L4_USART1 && (STM32L4_DMA1 || STM32L4_DMA2 || STM32L4_DMAMUX)
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

endif # USART1_SERIALDRIVER

choice
	prompt "USART2 Driver Configuration"
	default STM32L4_USART2_SERIALDRIVER
	depends on STM32L4_USART2

config STM32L4_USART2_SERIALDRIVER
	bool "Standard serial driver"
	select USART2_SERIALDRIVER
	select STM32L4_SERIALDRIVER

config STM32L4_USART2_1WIREDRIVER
	bool "1-Wire driver"
	select STM32L4_1WIREDRIVER

endchoice # USART2 Driver Configuration

if USART2_SERIALDRIVER

config USART2_RS485
	bool "RS-485 on USART2"
	default n
	depends on STM32L4_USART2
	---help---
		Enable RS-485 interface on USART2. Your board config will have to
		provide GPIO_USART2_RS485_DIR pin definition. Currently it cannot be
		used with USART2_RXDMA.

config USART2_RS485_DIR_POLARITY
	int "USART2 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on USART2_RS485
	---help---
		Polarity of DIR pin for RS-485 on USART2. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config USART2_RXDMA
	bool "USART2 Rx DMA"
	default n
	depends on STM32L4_USART2 && (STM32L4_DMA1 || STM32L4_DMAMUX)
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

endif # USART2_SERIALDRIVER

choice
	prompt "USART3 Driver Configuration"
	default STM32L4_USART3_SERIALDRIVER
	depends on STM32L4_USART3

config STM32L4_USART3_SERIALDRIVER
	bool "Standard serial driver"
	select USART3_SERIALDRIVER
	select STM32L4_SERIALDRIVER

config STM32L4_USART3_1WIREDRIVER
	bool "1-Wire driver"
	select STM32L4_1WIREDRIVER

endchoice # USART3 Driver Configuration

if USART3_SERIALDRIVER

config USART3_RS485
	bool "RS-485 on USART3"
	default n
	depends on STM32L4_USART3
	---help---
		Enable RS-485 interface on USART3. Your board config will have to
		provide GPIO_USART3_RS485_DIR pin definition. Currently it cannot be
		used with USART3_RXDMA.

config USART3_RS485_DIR_POLARITY
	int "USART3 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on USART3_RS485
	---help---
		Polarity of DIR pin for RS-485 on USART3. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config USART3_RXDMA
	bool "USART3 Rx DMA"
	default n
	depends on STM32L4_USART3 && (STM32L4_DMA1 || STM32L4_DMAMUX)
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

endif # USART3_SERIALDRIVER

choice
	prompt "UART4 Driver Configuration"
	default STM32L4_UART4_SERIALDRIVER
	depends on STM32L4_UART4

config STM32L4_UART4_SERIALDRIVER
	bool "Standard serial driver"
	select UART4_SERIALDRIVER
	select STM32L4_SERIALDRIVER

config STM32L4_UART4_1WIREDRIVER
	bool "1-Wire driver"
	select STM32L4_1WIREDRIVER

endchoice # UART4 Driver Configuration

if UART4_SERIALDRIVER

config UART4_RS485
	bool "RS-485 on UART4"
	default n
	depends on STM32L4_UART4
	---help---
		Enable RS-485 interface on UART4. Your board config will have to
		provide GPIO_UART4_RS485_DIR pin definition. Currently it cannot be
		used with UART4_RXDMA.

config UART4_RS485_DIR_POLARITY
	int "UART4 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on UART4_RS485
	---help---
		Polarity of DIR pin for RS-485 on UART4. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config UART4_RXDMA
	bool "UART4 Rx DMA"
	default n
	depends on STM32L4_UART4 && (STM32L4_DMA2 || STM32L4_DMAMUX)
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

endif # UART4_SERIALDRIVER

choice
	prompt "UART5 Driver Configuration"
	default STM32L4_UART5_SERIALDRIVER
	depends on STM32L4_UART5

config STM32L4_UART5_SERIALDRIVER
	bool "Standard serial driver"
	select UART5_SERIALDRIVER
	select STM32L4_SERIALDRIVER

config STM32L4_UART5_1WIREDRIVER
	bool "1-Wire driver"
	select STM32L4_1WIREDRIVER

endchoice # UART5 Driver Configuration

if UART5_SERIALDRIVER

config UART5_RS485
	bool "RS-485 on UART5"
	default n
	depends on STM32L4_UART5
	---help---
		Enable RS-485 interface on UART5. Your board config will have to
		provide GPIO_UART5_RS485_DIR pin definition. Currently it cannot be
		used with UART5_RXDMA.

config UART5_RS485_DIR_POLARITY
	int "UART5 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on UART5_RS485
	---help---
		Polarity of DIR pin for RS-485 on UART5. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config UART5_RXDMA
	bool "UART5 Rx DMA"
	default n
	depends on STM32L4_UART5 && (STM32L4_DMA2 || STM32L4_DMAMUX)
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

endif # UART5_SERIALDRIVER

if STM32L4_SERIALDRIVER

comment "Serial Driver Configuration"

config STM32L4_SERIAL_RXDMA_BUFFER_SIZE
	int "Rx DMA buffer size"
	default 32
	depends on USART1_RXDMA || USART2_RXDMA || USART3_RXDMA || UART4_RXDMA || UART5_RXDMA
	---help---
		The DMA buffer size when using RX DMA to emulate a FIFO.

		When streaming data, the generic serial layer will be called
		every time the FIFO receives half this number of bytes.

		Value given here will be rounded up to next multiple of 32 bytes.

config STM32L4_SERIAL_DISABLE_REORDERING
	bool "Disable reordering of ttySx devices."
	depends on STM32L4_USART1 || STM32L4_USART2 || STM32L4_USART3 || STM32L4_UART4 || STM32L4_UART5
	default n
	---help---
		NuttX per default reorders the serial ports (/dev/ttySx) so that the
		console is always on /dev/ttyS0. If more than one UART is in use this
		can, however, have the side-effect that all port mappings
		(hardware USART1 -> /dev/ttyS0) change if the console is moved to another
		UART. This is in particular relevant if a project uses the USB console
		in some boards and a serial console in other boards, but does not
		want the side effect of having all serial port names change when just
		the console is moved from serial to USB.

config STM32L4_FLOWCONTROL_BROKEN
	bool "Use Software UART RTS flow control"
	depends on STM32L4_USART
	default n
	---help---
		Enable UART RTS flow control using Software. Because STM
		Current STM32 have broken HW based RTS behavior (they assert
		nRTS after every byte received)  Enable this setting workaround
		this issue by using software based management of RTS

config STM32L4_USART_BREAKS
	bool "Add TIOxSBRK to support sending Breaks"
	depends on STM32L4_USART
	default n
	---help---
		Add TIOCxBRK routines to send a line break per the STM32 manual, the
		break will be a pulse based on the value M. This is not a BSD compatible
		break.

config STM32L4_SERIALBRK_BSDCOMPAT
	bool "Use GPIO To send Break"
	depends on STM32L4_USART && STM32L4_USART_BREAKS
	default n
	---help---
		Enable using GPIO on the TX pin to send a BSD compatible break:
		TIOCSBRK will start the break and TIOCCBRK will end the break.
		The current STM32 U[S]ARTS have no way to leave the break (TX=LOW)
		on because the SW starts the break and then the HW automatically clears
		the break. This makes it is difficult to sent a long break.

config STM32L4_USART_SINGLEWIRE
	bool "Single Wire Support"
	default n
	depends on STM32L4_USART
	---help---
		Enable single wire UART support.  The option enables support for the
		TIOCSSINGLEWIRE ioctl in the STM32L4 serial driver.

config STM32L4_USART_INVERT
	bool "Signal Invert Support"
	default n
	depends on STM32L4_USART
	---help---
		Enable signal inversion UART support. The option enables support for the
		TIOCSINVERT ioctl in the STM32L4 serial driver.

config STM32L4_USART_SWAP
	bool "Swap RX/TX pins support"
	default n
	depends on STM32L4_USART
	---help---
		Enable RX/TX pin swapping support. The option enables support for the
		TIOCSSWAP ioctl in the STM32L4 serial driver.

if PM

config STM32L4_PM_SERIAL_ACTIVITY
	int "PM serial activity"
	default 10
	---help---
		PM activity reported to power management logic on every serial
		interrupt.

endif
endif # STM32L4_SERIALDRIVER

endmenu # U[S]ART Configuration

menu "SPI Configuration"
	depends on STM32L4_SPI

config STM32L4_SPI_INTERRUPTS
	bool "Interrupt driver SPI"
	default n
	---help---
		Select to enable interrupt driven SPI support. Non-interrupt-driven,
		poll-waiting is recommended if the interrupt rate would be to high in
		the interrupt driven case.

config STM32L4_SPI_DMA
	bool "SPI DMA"
	default n
	---help---
		Use DMA to improve SPI transfer performance.  Cannot be used with STM32L4_SPI_INTERRUPT.

endmenu

menu "I2C Configuration"
	depends on STM32L4_I2C

config STM32L4_I2C_DYNTIMEO
	bool "Use dynamic timeouts"
	default n
	depends on STM32L4_I2C

config STM32L4_I2C_DYNTIMEO_USECPERBYTE
	int "Timeout Microseconds per Byte"
	default 500
	depends on STM32L4_I2C_DYNTIMEO

config STM32L4_I2C_DYNTIMEO_STARTSTOP
	int "Timeout for Start/Stop (Milliseconds)"
	default 1000
	depends on STM32L4_I2C_DYNTIMEO

config STM32L4_I2CTIMEOSEC
	int "Timeout seconds"
	default 0
	depends on STM32L4_I2C

config STM32L4_I2CTIMEOMS
	int "Timeout Milliseconds"
	default 500
	depends on STM32L4_I2C && !STM32L4_I2C_DYNTIMEO

config STM32L4_I2CTIMEOTICKS
	int "Timeout for Done and Stop (ticks)"
	default 500
	depends on STM32L4_I2C && !STM32L4_I2C_DYNTIMEO

endmenu

menu "SD/MMC Configuration"
	depends on STM32L4_SDMMC

config STM32L4_SDMMC_XFRDEBUG
	bool "SDMMC transfer debug"
	depends on DEBUG_FS_INFO
	default n
	---help---
		Enable special debug instrumentation analyze SDMMC data transfers.
		This logic is as non-invasive as possible:  It samples SDMMC
		registers at key points in the data transfer and then dumps all of
		the registers at the end of the transfer.  If DEBUG_DMA is also
		enabled, then DMA register will be collected as well.  Requires also
		DEBUG_FS and CONFIG_DEBUG_INFO.

config STM32L4_SDMMC_DMA
	bool "Support DMA data transfers"
	default n
	select SDIO_DMA
	depends on STM32L4_DMA
	---help---
		Support DMA data transfers.

menu "SDMMC1 Configuration"
	depends on STM32L4_SDMMC1

config STM32L4_SDMMC1_DMAPRIO
	hex "SDMMC1 DMA priority"
	default 0x00001000
	---help---
		Select SDMMC1 DMA priority.

		Options are: 0x00000000 low, 0x00001000 medium,
		0x00002000 high, 0x00003000 very high.  Default: medium.

config SDMMC1_WIDTH_D1_ONLY
	bool "Use D1 only on SDMMC1"
	default n
	---help---
		Select 1-bit transfer mode.  Default: 4-bit transfer mode.

endmenu # SDMMC1 Configuration
endmenu # SD/MMC Configuration

menu "CAN driver configuration"
	depends on STM32L4_CAN1 || STM32L4_CAN2

config STM32L4_CAN1_BAUD
	int "CAN1 BAUD"
	default 250000
	depends on STM32L4_CAN1
	---help---
		CAN1 BAUD rate.  Required if CONFIG_STM32L4_CAN1 is defined.

config STM32L4_CAN2_BAUD
	int "CAN2 BAUD"
	default 250000
	depends on STM32L4_CAN2
	---help---
		CAN2 BAUD rate.  Required if CONFIG_STM32L4_CAN2 is defined.

config STM32L4_CAN_TSEG1
	int "TSEG1 quanta"
	default 6
	---help---
		The number of CAN time quanta in segment 1. Default: 6

config STM32L4_CAN_TSEG2
	int "TSEG2 quanta"
	default 7
	---help---
		The number of CAN time quanta in segment 2. Default: 7

config STM32L4_CAN_REGDEBUG
	bool "CAN Register level debug"
	depends on DEBUG_CAN_INFO
	default n
	---help---
		Output detailed register-level CAN device debug information.
		Requires also CONFIG_DEBUG_CAN_INFO.

endmenu

menu "QEncoder Driver"
	depends on SENSORS_QENCODER
	depends on STM32L4_TIM1 || STM32L4_TIM2 || STM32L4_TIM3 || STM32L4_TIM4 || STM32L4_TIM5 || STM32L4_TIM8

config STM32L4_TIM1_QE
	bool "TIM1"
	default n
	depends on STM32L4_TIM1
	---help---
		Reserve TIM1 for use by QEncoder.

if STM32L4_TIM1_QE

config STM32L4_TIM1_QEPSC
	int "TIM1 pulse prescaler"
	default 1
	---help---
		This prescaler divides the number of recorded encoder pulses, limiting the count rate at the expense of resolution.
		Replaces the obscure "output clock of TIM1." (CONFIG_TIM1_QECLKOUT).

endif

config STM32L4_TIM2_QE
	bool "TIM2"
	default n
	depends on STM32L4_TIM2
	---help---
		Reserve TIM2 for use by QEncoder.

if STM32L4_TIM2_QE

config STM32L4_TIM2_QEPSC
	int "TIM2 pulse prescaler"
	default 1
	---help---
		This prescaler divides the number of recorded encoder pulses, limiting the count rate at the expense of resolution.
		Replaces the obscure "output clock of TIM2." (CONFIG_TIM2_QECLKOUT).

endif

config STM32L4_TIM3_QE
	bool "TIM3"
	default n
	depends on STM32L4_TIM3
	---help---
		Reserve TIM3 for use by QEncoder.

if STM32L4_TIM3_QE

config STM32L4_TIM3_QEPSC
	int "TIM3 pulse prescaler"
	default 1
	---help---
		This prescaler divides the number of recorded encoder pulses, limiting the count rate at the expense of resolution.
		Replaces the obscure "output clock of TIM3." (CONFIG_TIM3_QECLKOUT).

endif

config STM32L4_TIM4_QE
	bool "TIM4"
	default n
	depends on STM32L4_TIM4
	---help---
		Reserve TIM4 for use by QEncoder.

if STM32L4_TIM4_QE

config STM32L4_TIM4_QEPSC
	int "TIM4 pulse prescaler"
	default 1
	---help---
		This prescaler divides the number of recorded encoder pulses, limiting the count rate at the expense of resolution.
		Replaces the obscure "output clock of TIM4." (CONFIG_TIM4_QECLKOUT).

endif

config STM32L4_TIM5_QE
	bool "TIM5"
	default n
	depends on STM32L4_TIM5
	---help---
		Reserve TIM5 for use by QEncoder.

if STM32L4_TIM5_QE

config STM32L4_TIM5_QEPSC
	int "TIM5 pulse prescaler"
	default 1
	---help---
		This prescaler divides the number of recorded encoder pulses, limiting the count rate at the expense of resolution.
		Replaces the obscure "output clock of TIM5." (CONFIG_TIM5_QECLKOUT).

endif

config STM32L4_TIM8_QE
	bool "TIM8"
	default n
	depends on STM32L4_TIM8
	---help---
		Reserve TIM8 for use by QEncoder.

if STM32L4_TIM8_QE

config STM32L4_TIM8_QEPSC
	int "TIM8 pulse prescaler"
	default 1
	---help---
		This prescaler divides the number of recorded encoder pulses, limiting the count rate at the expense of resolution.
		Replaces the obscure "output clock of TIM8." (CONFIG_TIM8_QECLKOUT).

endif

config STM32L4_QENCODER_FILTER
	bool "Enable filtering on STM32 QEncoder input"
	default y

choice
	depends on STM32L4_QENCODER_FILTER
	prompt "Input channel sampling frequency"
	default STM32L4_QENCODER_SAMPLE_FDTS_4

config STM32L4_QENCODER_SAMPLE_FDTS
	bool "fDTS"

config STM32L4_QENCODER_SAMPLE_CKINT
	bool "fCK_INT"

config STM32L4_QENCODER_SAMPLE_FDTS_2
	bool "fDTS/2"

config STM32L4_QENCODER_SAMPLE_FDTS_4
	bool "fDTS/4"

config STM32L4_QENCODER_SAMPLE_FDTS_8
	bool "fDTS/8"

config STM32L4_QENCODER_SAMPLE_FDTS_16
	bool "fDTS/16"

config STM32L4_QENCODER_SAMPLE_FDTS_32
	bool "fDTS/32"

endchoice

choice
	depends on STM32L4_QENCODER_FILTER
	prompt "Input channel event count"
	default STM32L4_QENCODER_SAMPLE_EVENT_6

config STM32L4_QENCODER_SAMPLE_EVENT_1
	depends on STM32L4_QENCODER_SAMPLE_FDTS
	bool "1"

config STM32L4_QENCODER_SAMPLE_EVENT_2
	depends on STM32L4_QENCODER_SAMPLE_CKINT
	bool "2"

config STM32L4_QENCODER_SAMPLE_EVENT_4
	depends on STM32L4_QENCODER_SAMPLE_CKINT
	bool "4"

config STM32L4_QENCODER_SAMPLE_EVENT_5
	depends on STM32L4_QENCODER_SAMPLE_FDTS_16 || STM32L4_QENCODER_SAMPLE_FDTS_32
	bool "5"

config STM32L4_QENCODER_SAMPLE_EVENT_6
	depends on !STM32L4_QENCODER_SAMPLE_FDTS && !STM32L4_QENCODER_SAMPLE_CKINT
	bool "6"

config STM32L4_QENCODER_SAMPLE_EVENT_8
	depends on !STM32L4_QENCODER_SAMPLE_FDTS
	bool "8"

endchoice

endmenu

menu "SAI Configuration"
	depends on STM32L4_SAI

choice
	prompt "Operation mode"
	default STM32L4_SAI_DMA
	---help---
		Select the operation mode the SAI driver should use.

config STM32L4_SAI_POLLING
	bool "Polling"
	---help---
		The SAI registers are polled for events.

config STM32L4_SAI_INTERRUPTS
	bool "Interrupt"
	---help---
		Select to enable interrupt driven SAI support.

config STM32L4_SAI_DMA
	bool "DMA"
	---help---
		Use DMA to improve SAI transfer performance.

endchoice # Operation mode

choice
	prompt "SAI1 synchronization enable"
	default STM32L4_SAI1_BOTH_ASYNC
	depends on STM32L4_SAI1_A && STM32L4_SAI1_B
	---help---
		Select the synchronization mode of the SAI sub-blocks

config STM32L4_SAI1_BOTH_ASYNC
	bool "Both asynchronous"

config STM32L4_SAI1_A_SYNC_WITH_B
	bool "Block A is synchronous with Block B"

config STM32L4_SAI1_B_SYNC_WITH_A
	bool "Block B is synchronous with Block A"

endchoice # SAI1 synchronization enable

choice
	prompt "SAI2 synchronization enable"
	default STM32L4_SAI2_BOTH_ASYNC
	depends on STM32L4_SAI2_A && STM32L4_SAI2_B
	---help---
		Select the synchronization mode of the SAI sub-blocks

config STM32L4_SAI2_BOTH_ASYNC
	bool "Both asynchronous"

config STM32L4_SAI2_A_SYNC_WITH_B
	bool "Block A is synchronous with Block B"

config STM32L4_SAI2_B_SYNC_WITH_A
	bool "Block B is synchronous with Block A"

endchoice # SAI2 synchronization enable

endmenu

endif # ARCH_CHIP_STM32L4
