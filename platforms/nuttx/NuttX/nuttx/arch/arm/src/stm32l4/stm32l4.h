/****************************************************************************
 * arch/arm/src/stm32l4/stm32l4.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_STM32L4_STM32L4_H
#define __ARCH_ARM_SRC_STM32L4_STM32L4_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include <sys/types.h>
#include <stdint.h>
#include <stdbool.h>

#include "arm_internal.h"

/* Peripherals **************************************************************/

#include "chip.h"
#include "stm32l4_adc.h"
#include "stm32l4_can.h"
#include "stm32l4_comp.h"
#include "stm32l4_dac.h"
#include "stm32l4_dbgmcu.h"
#include "stm32l4_dma.h"
#include "stm32l4_exti.h"
#include "stm32l4_flash.h"
#include "stm32l4_gpio.h"
#include "stm32l4_i2c.h"
#include "stm32l4_pwr.h"
#include "stm32l4_rcc.h"
#include "stm32l4_rtc.h"
#include "stm32l4_sdmmc.h"
#include "stm32l4_spi.h"
#include "stm32l4_tim.h"
#include "stm32l4_uart.h"
#include "stm32l4_usbdev.h"
#include "stm32l4_wdg.h"
#include "stm32l4_lowputc.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

#endif /* __ARCH_ARM_SRC_STM32L4_STM32L4_H */
