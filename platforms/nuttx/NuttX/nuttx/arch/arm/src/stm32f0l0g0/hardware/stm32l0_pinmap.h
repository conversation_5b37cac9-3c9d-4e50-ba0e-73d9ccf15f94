/****************************************************************************
 * arch/arm/src/stm32f0l0g0/hardware/stm32l0_pinmap.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_STM32F0L0G0_HARDWARE_STM32L0_PINMAP_H
#define __ARCH_ARM_SRC_STM32F0L0G0_HARDWARE_STM32L0_PINMAP_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

#include "stm32_gpio.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Alternate Pin Functions.
 *
 * Alternative pin selections are provided with a numeric suffix like _1, _2,
 * etc. Drivers, however, will use the pin selection without the numeric
 * suffix. Additional definitions are required in the board.h file.  For
 * example, if CAN1_RX connects via PA11 on some board, then the following
 * definitions should appear in the board.h header file for that board:
 *
 * #define GPIO_I2C1_SCL GPIO_I2C1_SCL_1
 *
 * The driver will then automatically configure PB6 as the I2C1 SCL pin.
 */

/* WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific GPIO options such as
 * frequency, open-drain/push-pull, and pull-up/down!  Just the basics are
 * defined for most pins in this file.
 */

#define GPIO_BOOT1_0       (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN2)

/* ADC
 *
 * ADC_IN16 is internal temperature sensor
 * ADC_IN17 is internal Vrefint
 */

#define GPIO_ADC1_IN0_0     (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN0)
#define GPIO_ADC1_IN1_0     (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN1)
#define GPIO_ADC1_IN2_0     (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN2)
#define GPIO_ADC1_IN3_0     (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN3)
#define GPIO_ADC1_IN4_0     (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN4)
#define GPIO_ADC1_IN5_0     (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN5)
#define GPIO_ADC1_IN6_0     (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN6)
#define GPIO_ADC1_IN7_0     (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN7)
#define GPIO_ADC1_IN8_0     (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN0)
#define GPIO_ADC1_IN9_0     (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN1)
#define GPIO_ADC1_IN10_0    (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN0)
#define GPIO_ADC1_IN11_0    (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN1)
#define GPIO_ADC1_IN12_0    (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN2)
#define GPIO_ADC1_IN13_0    (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN3)
#define GPIO_ADC1_IN14_0    (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN4)
#define GPIO_ADC1_IN15_0    (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN5)

/* DAC */

#define GPIO_DAC1_OUT1_0    (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN4)

/* I2C */

#define GPIO_I2C1_SCL_1     (GPIO_ALT | GPIO_AF1 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN6)
#define GPIO_I2C1_SCL_2     (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN8)
#define GPIO_I2C1_SDA_1     (GPIO_ALT | GPIO_AF1 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN7)
#define GPIO_I2C1_SDA_2     (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN9)
#define GPIO_I2C1_SMBA_0    (GPIO_ALT | GPIO_AF3 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN5)
#define GPIO_I2C2_SCL_1     (GPIO_ALT | GPIO_AF6 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN10)
#define GPIO_I2C2_SCL_2     (GPIO_ALT | GPIO_AF5 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN13)
#define GPIO_I2C2_SDA_1     (GPIO_ALT | GPIO_AF6 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN11)
#define GPIO_I2C2_SDA_2     (GPIO_ALT | GPIO_AF5 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN14)
#define GPIO_I2C2_SMBA_0    (GPIO_ALT | GPIO_AF5 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN12)
#define GPIO_I2C3_SCL_0     (GPIO_ALT | GPIO_AF7 | GPIO_OPENDRAIN | GPIO_PORTC | GPIO_PIN0)
#define GPIO_I2C3_SDA_1     (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTC | GPIO_PIN1)
#define GPIO_I2C3_SDA_2     (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTC | GPIO_PIN9)

/* LCD */

#define GPIO_LCD_COM0_0     (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_LCD_COM1_0     (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN9)
#define GPIO_LCD_COM2_0     (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_LCD_COM3_0     (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_LCD_COM4_0     (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN10)
#define GPIO_LCD_COM5_0     (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN11)
#define GPIO_LCD_COM6_0     (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN12)
#define GPIO_LCD_COM7_0     (GPIO_ALT | GPIO_AF1 | GPIO_PORTD | GPIO_PIN2)
#define GPIO_LCD_SEG0_0     (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_LCD_SEG1_0     (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_LCD_SEG2_0     (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_LCD_SEG3_0     (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_LCD_SEG4_0     (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_LCD_SEG5_0     (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN0)
#define GPIO_LCD_SEG6_0     (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN1)
#define GPIO_LCD_SEG7_0     (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_LCD_SEG8_0     (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_LCD_SEG9_0     (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_LCD_SEG10_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_LCD_SEG11_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN11)
#define GPIO_LCD_SEG12_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_LCD_SEG13_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_LCD_SEG14_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_LCD_SEG15_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN15)
#define GPIO_LCD_SEG16_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_LCD_SEG17_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_LCD_SEG18_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN0)
#define GPIO_LCD_SEG19_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN1)
#define GPIO_LCD_SEG20_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN2)
#define GPIO_LCD_SEG21_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN3)
#define GPIO_LCD_SEG22_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN4)
#define GPIO_LCD_SEG23_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN5)
#define GPIO_LCD_SEG24_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN6)
#define GPIO_LCD_SEG25_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN7)
#define GPIO_LCD_SEG26_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN8)
#define GPIO_LCD_SEG27_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN9)
#define GPIO_LCD_SEG28_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN10)
#define GPIO_LCD_SEG29_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN11)
#define GPIO_LCD_SEG30_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN12)
#define GPIO_LCD_SEG31_0    (GPIO_ALT | GPIO_AF1 | GPIO_PORTD | GPIO_PIN2)

/* Clocking */

#define GPIO_MCO_1          (GPIO_ALT | GPIO_AF0 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN8)
#define GPIO_MCO_2          (GPIO_ALT | GPIO_AF0 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN9)
#define GPIO_OSC32_IN_0     (GPIO_ALT | GPIO_AF0 | GPIO_PORTC | GPIO_PIN14)
#define GPIO_OSC32_OUT_0    (GPIO_ALT | GPIO_AF0 | GPIO_PORTC | GPIO_PIN15)
#define GPIO_OSC_IN_0       (GPIO_ALT | GPIO_AF0 | GPIO_PORTH | GPIO_PIN0)
#define GPIO_OSC_OUT_0      (GPIO_ALT | GPIO_AF0 | GPIO_PORTH | GPIO_PIN1)

/* Event outputs */

#define GPIO_PA1_EVENT_OUT_0(GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_PA6_EVENT_OUT_0(GPIO_ALT | GPIO_AF6 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_PA7_EVENT_OUT_0(GPIO_ALT | GPIO_AF6 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_PA8_EVENT_OUT_0(GPIO_ALT | GPIO_AF3 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_PA11_EVENT_OUT_0GPIO_ALT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_PA12_EVENT_OUT_0GPIO_ALT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN12)
#define GPIO_PA15_EVENT_OUT_0GPIO_ALT | GPIO_AF3 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_PB0_EVENT_OUT_0(GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN0)
#define GPIO_PB3_EVENT_OUT_0(GPIO_ALT | GPIO_AF4 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_PB9_EVENT_OUT_0(GPIO_ALT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_PB11_EVENT_OUT_0GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN11)
#define GPIO_PB12_EVENT_OUT_0GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_PC0_EVENT_OUT_0(GPIO_ALT | GPIO_AF2 | GPIO_PORTC | GPIO_PIN0)
#define GPIO_PC1_EVENT_OUT_0(GPIO_ALT | GPIO_AF2 | GPIO_PORTC | GPIO_PIN1)
#define GPIO_PC4_EVENT_OUT_0(GPIO_ALT | GPIO_AF0 | GPIO_PORTC | GPIO_PIN4)
#define GPIO_PE0_EVENT_OUT_0(GPIO_ALT | GPIO_AF2 | GPIO_PORTE | GPIO_PIN0)
#define GPIO_PE1_EVENT_OUT_0(GPIO_ALT | GPIO_AF2 | GPIO_PORTE | GPIO_PIN1)

/* RTC */

#define GPIO_RTC_OUT_0      (GPIO_ALT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_RTC_REFIN_0    (GPIO_ALT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN15)
#define GPIO_RTC_TAMP1_0    (GPIO_ALT | GPIO_AF0 | GPIO_PORTC | GPIO_PIN13)
#define GPIO_RTC_TS_0       (GPIO_ALT | GPIO_AF0 | GPIO_PORTC | GPIO_PIN13)

/* SPI */

#define GPIO_SPI1_MISO_1    (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_SPI1_MISO_2    (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_SPI1_MISO_3    (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_SPI1_MISO_4    (GPIO_ALT | GPIO_AF2 | GPIO_PORTE | GPIO_PIN14)
#define GPIO_SPI1_MOSI_1    (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN12)
#define GPIO_SPI1_MOSI_2    (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_SPI1_MOSI_3    (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_SPI1_MOSI_4    (GPIO_ALT | GPIO_AF2 | GPIO_PORTE | GPIO_PIN15)
#define GPIO_SPI1_NSS_1     (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_SPI1_NSS_2     (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_SPI1_NSS_3     (GPIO_ALT | GPIO_AF2 | GPIO_PORTE | GPIO_PIN12)
#define GPIO_SPI1_SCK_1     (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN5)
#define GPIO_SPI1_SCK_2     (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_SPI1_SCK_3     (GPIO_ALT | GPIO_AF2 | GPIO_PORTE | GPIO_PIN13)
#define GPIO_SPI2_MISO_1    (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_SPI2_MISO_2    (GPIO_ALT | GPIO_AF2 | GPIO_PORTD | GPIO_PIN3)
#define GPIO_SPI2_MISO_3    (GPIO_ALT | GPIO_AF2 | GPIO_PORTC | GPIO_PIN2)
#define GPIO_SPI2_MOSI_1    (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN15)
#define GPIO_SPI2_MOSI_2    (GPIO_ALT | GPIO_AF1 | GPIO_PORTD | GPIO_PIN4)
#define GPIO_SPI2_MOSI_3    (GPIO_ALT | GPIO_AF2 | GPIO_PORTC | GPIO_PIN3)
#define GPIO_SPI2_NSS_1     (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_SPI2_NSS_2     (GPIO_ALT | GPIO_AF1 | GPIO_PORTD | GPIO_PIN0)
#define GPIO_SPI2_NSS_3     (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_SPI2_SCK_1     (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_SPI2_SCK_2     (GPIO_ALT | GPIO_AF1 | GPIO_PORTD | GPIO_PIN1)
#define GPIO_SPI2_SCK_3     (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN13)

/* Timers */

#define GPIO_TIM2_CH1_ETR_1 (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_TIM2_CH1_ETR_2 (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_TIM2_CH1_ETR_3 (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN9)
#define GPIO_TIM2_CH1_ETR_4 (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN5)
#define GPIO_TIM2_CH2IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_TIM2_CH2IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_TIM2_CH2IN_3   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN10)
#define GPIO_TIM2_CH2IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_TIM2_CH2IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_TIM2_CH2IN_3   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN10)
#define GPIO_TIM2_CH2OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_TIM2_CH2OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_TIM2_CH2OUT_3  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTE | GPIO_PIN10)
#define GPIO_TIM2_CH2OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_TIM2_CH2OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_TIM2_CH2OUT_3  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTE | GPIO_PIN10)
#define GPIO_TIM2_CH3IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM2_CH3IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_TIM2_CH3IN_3   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN11)
#define GPIO_TIM2_CH3IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM2_CH3IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_TIM2_CH3IN_3   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN11)
#define GPIO_TIM2_CH3OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM2_CH3OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_TIM2_CH3OUT_3  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTE | GPIO_PIN11)
#define GPIO_TIM2_CH3OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM2_CH3OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_TIM2_CH3OUT_3  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTE | GPIO_PIN11)
#define GPIO_TIM2_CH4IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_TIM2_CH4IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN11)
#define GPIO_TIM2_CH4IN_3   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN12)
#define GPIO_TIM2_CH4OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_TIM2_CH4OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTB | GPIO_PIN11)
#define GPIO_TIM2_CH4OUT_3  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTE | GPIO_PIN12)

#define GPIO_TIM3_CH1IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM3_CH1IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_TIM3_CH1IN_3   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTC | GPIO_PIN6)
#define GPIO_TIM3_CH1IN_4   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTE | GPIO_PIN3)
#define GPIO_TIM3_CH1OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM3_CH1OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_TIM3_CH1OUT_3  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTC | GPIO_PIN6)
#define GPIO_TIM3_CH1OUT_4  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTE | GPIO_PIN3)
#define GPIO_TIM3_CH2IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM3_CH2IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_TIM3_CH2IN_3   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTC | GPIO_PIN7)
#define GPIO_TIM3_CH2IN_4   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTE | GPIO_PIN4)
#define GPIO_TIM3_CH2OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM3_CH2OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_TIM3_CH2OUT_3  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTC | GPIO_PIN7)
#define GPIO_TIM3_CH2OUT_4  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTE | GPIO_PIN4)
#define GPIO_TIM3_CH3IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN0)
#define GPIO_TIM3_CH3IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTC | GPIO_PIN8)
#define GPIO_TIM3_CH3OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTB | GPIO_PIN0)
#define GPIO_TIM3_CH3OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTC | GPIO_PIN8)
#define GPIO_TIM3_CH4IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN1)
#define GPIO_TIM3_CH4IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTC | GPIO_PIN9)
#define GPIO_TIM3_CH4OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTB | GPIO_PIN1)
#define GPIO_TIM3_CH4OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTC | GPIO_PIN9)
#define GPIO_TIM3_ETR_1     (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTD | GPIO_PIN2)
#define GPIO_TIM3_ETR_2     (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTE | GPIO_PIN2)

/* TODO: TIM21, TIM22 */

/* USART */

#define GPIO_USART1_CK_1    (GPIO_ALT | GPIO_AF4 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_USART1_CK_2    (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_USART1_CTS_1   (GPIO_ALT | GPIO_AF4 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_USART1_CTS_2   (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_USART1_RTS_1   (GPIO_ALT | GPIO_AF4 | GPIO_PORTA | GPIO_PIN12)
#define GPIO_USART1_RTS_2   (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_USART1_RX_1    (GPIO_ALT | GPIO_PULLUP | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN10)
#define GPIO_USART1_RX_2    (GPIO_ALT | GPIO_PULLUP | GPIO_AF0 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN7)
#define GPIO_USART1_TX_1    (GPIO_ALT | GPIO_PULLUP | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN9)
#define GPIO_USART1_TX_2    (GPIO_ALT | GPIO_PULLUP | GPIO_AF0 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN6)

#define GPIO_USART2_CK_1    (GPIO_ALT | GPIO_AF4 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_USART2_CK_2    (GPIO_ALT | GPIO_AF0 | GPIO_PORTD | GPIO_PIN7)
#define GPIO_USART2_CTS_1   (GPIO_ALT | GPIO_AF4 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_USART2_CTS_2   (GPIO_ALT | GPIO_AF0 | GPIO_PORTD | GPIO_PIN3)
#define GPIO_USART2_RTS_1   (GPIO_ALT | GPIO_AF4 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_USART2_RTS_2   (GPIO_ALT | GPIO_AF0 | GPIO_PORTD | GPIO_PIN4)
#define GPIO_USART2_RX_1    (GPIO_ALT | GPIO_PULLUP | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN3)
#define GPIO_USART2_RX_2    (GPIO_ALT | GPIO_PULLUP | GPIO_AF0 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN6)
#define GPIO_USART2_RX_3    (GPIO_ALT | GPIO_PULLUP | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN15)
#define GPIO_USART2_TX_1    (GPIO_ALT | GPIO_PULLUP | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN2)
#define GPIO_USART2_TX_2    (GPIO_ALT | GPIO_PULLUP | GPIO_AF0 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN5)
#define GPIO_USART2_TX_3    (GPIO_ALT | GPIO_PULLUP | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN14)

#define GPIO_USART4_CK_0    (GPIO_ALT | GPIO_AF6 | GPIO_PORTC | GPIO_PIN12)
#define GPIO_USART4_RTS_0   (GPIO_ALT | GPIO_AF6 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_USART4_CTS_0   (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN7)
#define GPIO_USART4_RX_1    (GPIO_ALT | GPIO_PULLUP | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN11)
#define GPIO_USART4_RX_2    (GPIO_ALT | GPIO_PULLUP | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN9)
#define GPIO_USART4_RX_3    (GPIO_ALT | GPIO_PULLUP | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN1)
#define GPIO_USART4_TX_1    (GPIO_ALT | GPIO_PULLUP | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN10)
#define GPIO_USART4_TX_2    (GPIO_ALT | GPIO_PULLUP | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN8)
#define GPIO_USART4_TX_3    (GPIO_ALT | GPIO_PULLUP | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN0)

#define GPIO_USART5_CK_1    (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_USART5_CK_2    (GPIO_ALT | GPIO_AF6 | GPIO_PORTE | GPIO_PIN7)
#define GPIO_USART5_RTS_1   (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_USART5_RTS_2   (GPIO_ALT | GPIO_AF6 | GPIO_PORTE | GPIO_PIN7)
#define GPIO_USART5_RX_1    (GPIO_ALT | GPIO_PULLUP | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN2)
#define GPIO_USART5_RX_2    (GPIO_ALT | GPIO_PULLUP | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN11)
#define GPIO_USART5_RX_3    (GPIO_ALT | GPIO_PULLUP | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN3)
#define GPIO_USART5_TX_1    (GPIO_ALT | GPIO_PULLUP | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN12)
#define GPIO_USART5_TX_2    (GPIO_ALT | GPIO_PULLUP | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN10)
#define GPIO_USART5_TX_3    (GPIO_ALT | GPIO_PULLUP | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN4)

/* TODO: USB */

/* TODO: LPTIM */

/* TODO: LPUART */

#endif /* __ARCH_ARM_SRC_STM32F0L0G0_HARDWARE_STM32L0_PINMAP_H */
