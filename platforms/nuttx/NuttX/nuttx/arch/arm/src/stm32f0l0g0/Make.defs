############################################################################
# arch/arm/src/stm32f0l0g0/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

include armv6-m/Make.defs

CHIP_CSRCS  = stm32_start.c stm32_gpio.c stm32_exti_gpio.c stm32_irq.c
CHIP_CSRCS += stm32_lse.c stm32_lowputc.c stm32_serial.c stm32_rcc.c

ifeq ($(CONFIG_STM32F0L0G0_DMA),y)
CHIP_CSRCS += stm32_dma_v1.c
endif

ifeq ($(CONFIG_STM32F0L0G0_PWR),y)
CHIP_CSRCS += stm32_pwr.c
endif

ifneq ($(CONFIG_ARCH_IDLE_CUSTOM),y)
CHIP_CSRCS += stm32_idle.c
endif

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += stm32_timerisr.c
endif

ifeq ($(CONFIG_BUILD_PROTECTED),y)
CHIP_CSRCS += stm32_userspace.c
endif

ifeq ($(CONFIG_STM32F0L0G0_GPIOIRQ),y)
CHIP_CSRCS += stm32_gpioint.c
endif

ifeq ($(CONFIG_ARCH_IRQPRIO),y)
CHIP_CSRCS += stm32_irqprio.c
endif

ifeq ($(CONFIG_STM32F0L0G0_HAVE_HSI48),y)
CHIP_CSRCS += stm32_hsi48.c
endif

ifeq ($(CONFIG_STM32F0L0G0_USB),y)
CHIP_CSRCS += stm32_usbdev.c
endif

ifeq ($(CONFIG_STM32F0L0G0_I2C),y)
CHIP_CSRCS += stm32_i2c.c
endif

ifeq ($(CONFIG_STM32F0L0G0_SPI),y)
CHIP_CSRCS += stm32_spi.c
endif

ifeq ($(CONFIG_STM32F0L0G0_PWM),y)
CHIP_CSRCS += stm32_pwm.c
endif

ifeq ($(CONFIG_STM32F0L0G0_ADC),y)
CHIP_CSRCS += stm32_adc.c
endif

ifeq ($(CONFIG_STM32F0L0G0_AES),y)
CHIP_CSRCS += stm32_aes.c
endif

ifeq ($(CONFIG_STM32F0L0G0_RNG),y)
CHIP_CSRCS += stm32_rng.c
endif

ifeq ($(CONFIG_STM32F0L0G0_TIM),y)
CHIP_CSRCS += stm32_tim.c stm32_tim_lowerhalf.c
endif
