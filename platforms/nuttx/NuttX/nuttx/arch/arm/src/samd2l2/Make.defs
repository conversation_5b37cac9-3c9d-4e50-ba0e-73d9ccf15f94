############################################################################
# arch/arm/src/samd2l2/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

include armv6-m/Make.defs

CHIP_CSRCS  = sam_irq.c sam_lowputc.c sam_port.c sam_sercom.c sam_serial.c
CHIP_CSRCS += sam_start.c sam_usart.c

# Configuration-dependent SAM D/L files

ifeq ($(CONFIG_ARCH_FAMILY_SAMD20),y)
CHIP_CSRCS += samd_clockconfig.c samd_gclk.c
else ifeq ($(CONFIG_ARCH_FAMILY_SAMD21),y)
CHIP_CSRCS += samd_clockconfig.c samd_gclk.c
else ifeq ($(CONFIG_ARCH_FAMILY_SAML21),y)
CHIP_CSRCS += saml_clockconfig.c saml_gclk.c
endif

ifneq ($(CONFIG_ARCH_IDLE_CUSTOM),y)
CHIP_CSRCS += sam_idle.c
endif

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += sam_timerisr.c
endif

ifeq ($(CONFIG_BUILD_PROTECTED),y)
CHIP_CSRCS += sam_userspace.c
endif

ifeq ($(CONFIG_ARCH_IRQPRIO),y)
CHIP_CSRCS += sam_irqprio.c
endif

ifeq ($(CONFIG_SAMD2L2_ADC),y)
CHIP_CSRCS += sam_adc.c
endif

ifeq ($(CONFIG_SAMD2L2_DMAC),y)
CHIP_CSRCS += sam_dmac.c
endif

ifeq ($(CONFIG_SAMD2L2_HAVE_SPI),y)
CHIP_CSRCS += sam_spi.c
endif

ifeq ($(CONFIG_SAMD2L2_HAVE_I2C),y)
CHIP_CSRCS += sam_i2c_master.c
endif

ifeq ($(CONFIG_SAMD2L2_USB),y)
CHIP_CSRCS += sam_usb.c
endif

ifeq ($(CONFIG_SAMD2L2_EIC),y)
CHIP_CSRCS += sam_eic.c
endif

ifeq ($(CONFIG_SAMD2L2_AC),y)
CHIP_CSRCS += sam_ac.c
endif
