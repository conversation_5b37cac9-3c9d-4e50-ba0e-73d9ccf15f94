#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

comment "Atmel SAMD/L Configuration Options"

choice
	prompt "Atmel SAMD/L Chip Selection"
	default ARCH_CHIP_SAMD20J18 if ARCH_CHIP_SAMD2X
	default ARCH_CHIP_SAMD21J18A if ARCH_CHIP_SAML2X
	depends on ARCH_CHIP_SAMD2X || ARCH_CHIP_SAML2X

config ARCH_CHIP_SAMD20E14
	bool "SAMD20E14"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD20
	select ARCH_FAMILY_SAMD20E
	---help---
		Flash 16KB SRAM 2KB

config ARCH_CHIP_SAMD20E15
	bool "SAMD20E15"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD20
	select ARCH_FAMILY_SAMD20E
	---help---
		Flash 32KB SRAM 4KB

config ARCH_CHIP_SAMD20E16
	bool "SAMD20E16"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD20
	select ARCH_FAMILY_SAMD20E
	---help---
		<PERSON> 64KB SRAM 8KB

config ARCH_CHIP_SAMD20E17
	bool "SAMD20E17"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD20
	select ARCH_FAMILY_SAMD20E
	---help---
		Flash 128KB SRAM 16KB

config ARCH_CHIP_SAMD20E18
	bool "SAMD20E18"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD20
	select ARCH_FAMILY_SAMD20E
	---help---
		Flash 256KB SRAM 32KB

config ARCH_CHIP_SAMD20G14
	bool "SAMD20G14"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD20
	select ARCH_FAMILY_SAMD20G
	---help---
		Flash 16KB SRAM 2KB

config ARCH_CHIP_SAMD20G15
	bool "SAMD20G15"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD20
	select ARCH_FAMILY_SAMD20G
	---help---
		Flash 32KB SRAM 4KB

config ARCH_CHIP_SAMD20G16
	bool "SAMD20G16"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD20
	select ARCH_FAMILY_SAMD20G
	---help---
		Flash 64KB SRAM 8KB

config ARCH_CHIP_SAMD20G17
	bool "SAMD20G17"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD20
	select ARCH_FAMILY_SAMD20G
	---help---
		Flash 128KB SRAM 16KB

config ARCH_CHIP_SAMD20G18
	bool "SAMD20G18"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD20
	select ARCH_FAMILY_SAMD20G
	---help---
		Flash 256KB SRAM 32KB

config ARCH_CHIP_SAMD20J14
	bool "SAMD20J14"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD20
	select ARCH_FAMILY_SAMD20J
	---help---
		Flash 16KB SRAM 2KB

config ARCH_CHIP_SAMD20J15
	bool "SAMD20J15"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD20
	select ARCH_FAMILY_SAMD20J
	---help---
		Flash 32KB SRAM 4KB

config ARCH_CHIP_SAMD20J16
	bool "SAMD20J16"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD20
	select ARCH_FAMILY_SAMD20J
	---help---
		Flash 64KB SRAM 8KB

config ARCH_CHIP_SAMD20J17
	bool "SAMD20J17"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD20
	select ARCH_FAMILY_SAMD20J
	---help---
		Flash 128KB SRAM 16KB

config ARCH_CHIP_SAMD20J18
	bool "SAMD20J18"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD20
	select ARCH_FAMILY_SAMD20J
	---help---
		Flash 256KB SRAM 32KB

config ARCH_CHIP_SAMD21E15A
	bool "SAMD21E15A"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD21
	select ARCH_FAMILY_SAMD21E
	---help---
		Flash 32KB SRAM 4KB

config ARCH_CHIP_SAMD21E15B
	bool "SAMD21E15B"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD21
	select ARCH_FAMILY_SAMD21E
	---help---
		Flash 32KB SRAM 4KB RWW FLASH 1KB

config ARCH_CHIP_SAMD21E16A
	bool "SAMD21E16A"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD21
	select ARCH_FAMILY_SAMD21E
	---help---
		Flash 64KB SRAM 8KB

config ARCH_CHIP_SAMD21E16B
	bool "SAMD21E16B"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD21
	select ARCH_FAMILY_SAMD21E
	---help---
		Flash 64KB SRAM 8KB RWW FLASH 2KB

config ARCH_CHIP_SAMD21E17A
	bool "SAMD21E17A"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD21
	select ARCH_FAMILY_SAMD21E
	---help---
		Flash 128KB SRAM 16KB

config ARCH_CHIP_SAMD21E18A
	bool "SAMD21E18A"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD21
	select ARCH_FAMILY_SAMD21E
	---help---
		Flash 256KB SRAM 32KB

config ARCH_CHIP_SAMD21G15A
	bool "SAMD21G15A"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD21
	select ARCH_FAMILY_SAMD21G
	---help---
		Flash 32KB SRAM 4KB

config ARCH_CHIP_SAMD21G15B
	bool "SAMD21G15B"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD21
	select ARCH_FAMILY_SAMD21G
	---help---
		Flash 32KB SRAM 4KB RWW FLASH 1KB

config ARCH_CHIP_SAMD21G16A
	bool "SAMD21G16A"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD21
	select ARCH_FAMILY_SAMD21G
	---help---
		Flash 64KB SRAM 8KB

config ARCH_CHIP_SAMD21G16B
	bool "SAMD21G16B"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD21
	select ARCH_FAMILY_SAMD21E
	---help---
		Flash 64KB SRAM 8KB RWW FLASH 2KB

config ARCH_CHIP_SAMD21G17A
	bool "SAMD21G17A"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD21
	select ARCH_FAMILY_SAMD21G
	---help---
		Flash 128KB SRAM 16KB

config ARCH_CHIP_SAMD21G18A
	bool "SAMD21G18A"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD21
	select ARCH_FAMILY_SAMD21G
	---help---
		Flash 256KB SRAM 32KB

config ARCH_CHIP_SAMD21J15A
	bool "SAMD21J15A"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD21
	select ARCH_FAMILY_SAMD21J
	---help---
		Flash 32KB SRAM 4KB

config ARCH_CHIP_SAMD21J15B
	bool "SAMD21J15B"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD21
	select ARCH_FAMILY_SAMD21J
	---help---
		Flash 32KB SRAM 4KB RWW FLASH 1KB

config ARCH_CHIP_SAMD21J16A
	bool "SAMD21J16A"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD21
	select ARCH_FAMILY_SAMD21J
	---help---
		Flash 64KB SRAM 8KB

config ARCH_CHIP_SAMD21J16B
	bool "SAMD21J16B"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD21
	select ARCH_FAMILY_SAMD21J
	---help---
		Flash 64KB SRAM 8KB RWW FLASH 2KB

config ARCH_CHIP_SAMD21J17A
	bool "SAMD21J17A"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD21
	select ARCH_FAMILY_SAMD21E
	---help---
		Flash 128KB SRAM 16KB

config ARCH_CHIP_SAMD21J18A
	bool "SAMD21J18A"
	depends on ARCH_CHIP_SAMD2X
	select ARCH_FAMILY_SAMD21
	select ARCH_FAMILY_SAMD21J
	---help---
		Flash 256KB SRAM 32KB

config ARCH_CHIP_SAML21E15
	bool "SAML21E15"
	depends on ARCH_CHIP_SAML2X
	select ARCH_FAMILY_SAML21
	select ARCH_FAMILY_SAML21E
	---help---
		Flash 32KB SRAM 4KB

config ARCH_CHIP_SAML21E16
	bool "SAML21E16"
	depends on ARCH_CHIP_SAML2X
	select ARCH_FAMILY_SAML21
	select ARCH_FAMILY_SAML21E
	---help---
		Flash 64KB SRAM 8KB

config ARCH_CHIP_SAML21E17
	bool "SAML21E17"
	depends on ARCH_CHIP_SAML2X
	select ARCH_FAMILY_SAML21
	select ARCH_FAMILY_SAML21E
	---help---
		Flash 128KB SRAM 16KB

config ARCH_CHIP_SAML21E18
	bool "SAML21E18"
	depends on ARCH_CHIP_SAML2X
	select ARCH_FAMILY_SAML21
	select ARCH_FAMILY_SAML21E
	---help---
		Flash 256KB SRAM 32KB

config ARCH_CHIP_SAML21G16
	bool "SAML21G16"
	depends on ARCH_CHIP_SAML2X
	select ARCH_FAMILY_SAML21
	select ARCH_FAMILY_SAML21G
	---help---
		Flash 64KB SRAM 4KB

config ARCH_CHIP_SAML21G17
	bool "SAML21G17"
	depends on ARCH_CHIP_SAML2X
	select ARCH_FAMILY_SAML21
	select ARCH_FAMILY_SAML21G
	---help---
		Flash 128KB SRAM 16KB

config ARCH_CHIP_SAML21G18
	bool "SAML21G18"
	depends on ARCH_CHIP_SAML2X
	select ARCH_FAMILY_SAML21
	select ARCH_FAMILY_SAML21G
	---help---
		Flash 256KB SRAM 32KB

config ARCH_CHIP_SAML21J16
	bool "SAML21J16"
	depends on ARCH_CHIP_SAML2X
	select ARCH_FAMILY_SAML21
	select ARCH_FAMILY_SAML21J
	---help---
		Flash 64KB SRAM 4KB

config ARCH_CHIP_SAML21J17
	bool "SAML21J17"
	depends on ARCH_CHIP_SAML2X
	select ARCH_FAMILY_SAML21
	select ARCH_FAMILY_SAML21J
	---help---
		Flash 128KB SRAM 16KB

config ARCH_CHIP_SAML21J18
	bool "SAML21J18"
	depends on ARCH_CHIP_SAML2X
	select ARCH_FAMILY_SAML21
	select ARCH_FAMILY_SAML21J
	---help---
		Flash 256KB SRAM 32KB

endchoice

config ARCH_FAMILY_SAMD20
	bool
	default n
	select SAMD2L2_HAVE_TC2
	select SAMD2L2_HAVE_TC3
	select SAMD2L2_HAVE_TC5

config ARCH_FAMILY_SAMD20E
	bool
	default n

config ARCH_FAMILY_SAMD20G
	bool
	default n
	select SAMD2L2_HAVE_SERCOM4
	select SAMD2L2_HAVE_SERCOM5

config ARCH_FAMILY_SAMD20J
	bool
	default n
	select SAMD2L2_HAVE_SERCOM4
	select SAMD2L2_HAVE_SERCOM5
	select SAMD2L2_HAVE_TC6
	select SAMD2L2_HAVE_TC7

config ARCH_FAMILY_SAMD21
	bool
	default n
	select SAMD2L2_HAVE_DMAC
	select SAMD2L2_HAVE_USB

config ARCH_FAMILY_SAMD21E
	bool
	default n

config ARCH_FAMILY_SAMD21G
	bool
	default n
	select SAMD2L2_HAVE_SERCOM4
	select SAMD2L2_HAVE_SERCOM5

config ARCH_FAMILY_SAMD21J
	bool
	default n
	select SAMD2L2_HAVE_SERCOM4
	select SAMD2L2_HAVE_SERCOM5
	select SAMD2L2_HAVE_TC2
	select SAMD2L2_HAVE_TC3
	select SAMD2L2_HAVE_TC5

config ARCH_FAMILY_SAML21
	bool
	default n
	select SAMD2L2_HAVE_DMAC
	select SAMD2L2_HAVE_USB

config ARCH_FAMILY_SAML21E
	bool
	default n

config ARCH_FAMILY_SAML21G
	bool
	default n
	select SAMD2L2_HAVE_SERCOM4
	select SAMD2L2_HAVE_SERCOM5

config ARCH_FAMILY_SAML21J
	bool
	default n
	select SAMD2L2_HAVE_SERCOM4
	select SAMD2L2_HAVE_SERCOM5
	select SAMD2L2_HAVE_TC2
	select SAMD2L2_HAVE_TC3
	select SAMD2L2_HAVE_TC5

menu "SAMD/L Peripheral Support"

config SAMD2L2_HAVE_DMAC
	bool
	default n

config SAMD2L2_HAVE_SERCOM4
	bool
	default n

config SAMD2L2_HAVE_SERCOM5
	bool
	default n

config SAMD2L2_HAVE_TC5
	bool
	default n

config SAMD2L2_HAVE_TC2
	bool
	default n

config SAMD2L2_HAVE_TC3
	bool
	default n

config SAMD2L2_HAVE_TC6
	bool
	default n

config SAMD2L2_HAVE_TC7
	bool
	default n

config SAMD2L2_HAVE_USB
	bool
	default n

config SAMD2L2_AC
	bool "Analog Comparator"
	default n

config SAMD2L2_ADC
	bool "Analog-to-Digital Converter"
	default n

config SAMD2L2_DAC
	bool "Digital-to-Analog Converter"
	default n

config SAMD2L2_DMAC
	bool "DMA Controller"
	default n
	select ARCH_DMA
	depends on SAMD2L2_HAVE_DMAC && EXPERIMENTAL

config SAMD2L2_EVSYS
	bool "Event System"
	default n

config SAMD2L2_NVMCTRL
	bool "Non-Volatile Memory Controller"
	default n

config SAMD2L2_PTC
	bool "Peripheral Touch Controller"
	default n

config SAMD2L2_RTC
	bool "Real Time Counter"
	default n

config SAMD2L2_SERCOM0
	bool "Serial Communication Interface 0"
	default n

config SAMD2L2_SERCOM1
	bool "Serial Communication Interface 1"
	default n

config SAMD2L2_SERCOM2
	bool "Serial Communication Interface 2"
	default n

config SAMD2L2_SERCOM3
	bool "Serial Communication Interface 3"
	default n

config SAMD2L2_SERCOM4
	bool "Serial Communication Interface 4"
	default n
	depends on SAMD2L2_HAVE_SERCOM4

config SAMD2L2_SERCOM5
	bool "Serial Communication Interface 5"
	default n
	depends on SAMD2L2_HAVE_SERCOM5

config SAMD2L2_TC0
	bool "Timer/Counter 0"
	default n

config SAMD2L2_TC1
	bool "Timer/Counter 1"
	default n

config SAMD2L2_TC2
	bool "Timer/Counter 2"
	default n
	depends on SAMD2L2_HAVE_TC2

config SAMD2L2_TC3
	bool "Timer/Counter 3"
	default n
	depends on SAMD2L2_HAVE_TC3

config SAMD2L2_TC4
	bool "Timer/Counter 4"
	default n

config SAMD2L2_TC5
	bool "Timer/Counter 5"
	default n
	depends on SAMD2L2_HAVE_TC5

config SAMD2L2_TC6
	bool "Timer/Counter 6"
	default n
	depends on SAMD2L2_HAVE_TC6

config SAMD2L2_TC7
	bool "Timer/Counter 7"
	default n
	depends on SAMD2L2_HAVE_TC7

config SAMD2L2_USB
	bool "USB"
	default n
	depends on SAMD2L2_HAVE_USB

config SAMD2L2_EIC
	bool "External Interrupt Controller"
	default n

config SAMD2L2_WDT
	bool "Watchdog Timer"
	default n

endmenu

config SAMD2L2_DMAC_NDESC
	int "Number of additional DMA Descriptors"
	default 0
	depends on SAMD2L2_DMAC
	---help---
		This provides the number of additional DMA descriptors that can be
		use to support multi-linked DMA transfers.  A minimum of 16
		descriptors will always be allocated (16 for the base descriptor which
		overlap the writeback descriptors).  If this value is set to zero,
		then only single block DMA transfers can be supported.

		Each additional DMA descriptor will require 16-bytes for LPRAM
		memory.

choice
	prompt "SERCOM0 mode"
	default SAMD2L2_SERCOM0_ISUSART
	depends on SAMD2L2_SERCOM0

config SAMD2L2_SERCOM0_ISI2C
	bool "I2C"
	select I2C
	select SAMD2L2_HAVE_I2C

config SAMD2L2_SERCOM0_ISSPI
	bool "SPI"
	select SAMD2L2_HAVE_SPI

config SAMD2L2_SERCOM0_ISUSART
	bool "USART"
	select USART0_SERIALDRIVER

endchoice

if USART0_SERIALDRIVER

config USART0_RS485MODE
	bool "RS-485 on USART0"
	default n
	---help---
		Enable RS-485 interface on USART0. Your board config will have to
		provide GPIO_USART0_RS485_DIR pin definition. Currently it cannot be
		used with USART0_RXDMA.

config USART0_RS485_DIR_POLARITY
	int "USART0 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on USART0_RS485MODE
	---help---
		Polarity of DIR pin for RS-485 on USART0. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

endif # USART0_SERIALDRIVER

choice
	prompt "SERCOM1 mode"
	default SAMD2L2_SERCOM1_ISUSART
	depends on SAMD2L2_SERCOM1

config SAMD2L2_SERCOM1_ISI2C
	bool "I2C"
	select I2C
	select SAMD2L2_HAVE_I2C

config SAMD2L2_SERCOM1_ISSPI
	bool "SPI"
	select SAMD2L2_HAVE_SPI

config SAMD2L2_SERCOM1_ISUSART
	bool "USART"
	select USART1_SERIALDRIVER

endchoice

if USART1_SERIALDRIVER

config USART1_RS485MODE
	bool "RS-485 on USART1"
	default n
	---help---
		Enable RS-485 interface on USART1. Your board config will have to
		provide GPIO_USART1_RS485_DIR pin definition. Currently it cannot be
		used with USART1_RXDMA.

config USART1_RS485_DIR_POLARITY
	int "USART1 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on USART1_RS485MODE
	---help---
		Polarity of DIR pin for RS-485 on USART1. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

endif # USART1_SERIALDRIVER

choice
	prompt "SERCOM2 mode"
	default SAMD2L2_SERCOM2_ISUSART
	depends on SAMD2L2_SERCOM2

config SAMD2L2_SERCOM2_ISI2C
	bool "I2C"
	select I2C
	select SAMD2L2_HAVE_I2C

config SAMD2L2_SERCOM2_ISSPI
	bool "SPI"
	select SAMD2L2_HAVE_SPI

config SAMD2L2_SERCOM2_ISUSART
	bool "USART"
	select USART2_SERIALDRIVER

endchoice

if USART2_SERIALDRIVER

config USART2_RS485MODE
	bool "RS-485 on USART2"
	default n
	---help---
		Enable RS-485 interface on USART2. Your board config will have to
		provide GPIO_USART2_RS485_DIR pin definition. Currently it cannot be
		used with USART2_RXDMA.

config USART2_RS485_DIR_POLARITY
	int "USART2 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on USART2_RS485MODE
	---help---
		Polarity of DIR pin for RS-485 on USART2. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

endif # USART2_SERIALDRIVER

choice
	prompt "SERCOM3 mode"
	default SAMD2L2_SERCOM3_ISUSART
	depends on SAMD2L2_SERCOM3

config SAMD2L2_SERCOM3_ISI2C
	bool "I2C"
	select I2C
	select SAMD2L2_HAVE_I2C

config SAMD2L2_SERCOM3_ISSPI
	bool "SPI"
	select SAMD2L2_HAVE_SPI

config SAMD2L2_SERCOM3_ISUSART
	bool "USART"
	select USART3_SERIALDRIVER

endchoice

if USART3_SERIALDRIVER

config USART3_RS485MODE
	bool "RS-485 on USART3"
	default n
	---help---
		Enable RS-485 interface on USART3. Your board config will have to
		provide GPIO_USART3_RS485_DIR pin definition. Currently it cannot be
		used with USART3_RXDMA.

config USART3_RS485_DIR_POLARITY
	int "USART3 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on USART3_RS485MODE
	---help---
		Polarity of DIR pin for RS-485 on USART3. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

endif # USART3_SERIALDRIVER

choice
	prompt "SERCOM4 mode"
	default SAMD2L2_SERCOM4_ISUSART
	depends on SAMD2L2_SERCOM4

config SAMD2L2_SERCOM4_ISI2C
	bool "I2C"
	select I2C
	select SAMD2L2_HAVE_I2C

config SAMD2L2_SERCOM4_ISSPI
	bool "SPI"
	select SAMD2L2_HAVE_SPI

config SAMD2L2_SERCOM4_ISUSART
	bool "USART"
	select USART4_SERIALDRIVER

endchoice

if USART4_SERIALDRIVER

config USART4_RS485MODE
	bool "RS-485 on USART4"
	default n
	---help---
		Enable RS-485 interface on USART4. Your board config will have to
		provide GPIO_USART4_RS485_DIR pin definition. Currently it cannot be
		used with USART4_RXDMA.

config USART4_RS485_DIR_POLARITY
	int "USART4 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on USART4_RS485MODE
	---help---
		Polarity of DIR pin for RS-485 on USART4. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

endif # USART4_SERIALDRIVER

choice
	prompt "SERCOM5 mode"
	default SAMD2L2_SERCOM5_ISUSART
	depends on SAMD2L2_SERCOM5

config SAMD2L2_SERCOM5_ISI2C
	bool "I2C"
	select I2C
	select SAMD2L2_HAVE_I2C

config SAMD2L2_SERCOM5_ISSPI
	bool "SPI"
	select SAMD2L2_HAVE_SPI

config SAMD2L2_SERCOM5_ISUSART
	bool "USART"
	select USART5_SERIALDRIVER

endchoice

if USART5_SERIALDRIVER

config USART5_RS485MODE
	bool "RS-485 on USART5"
	default n
	---help---
		Enable RS-485 interface on USART5. Your board config will have to
		provide GPIO_USART5_RS485_DIR pin definition. Currently it cannot be
		used with USART5_RXDMA.

config USART5_RS485_DIR_POLARITY
	int "USART5 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on USART5_RS485MODE
	---help---
		Polarity of DIR pin for RS-485 on USART5. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

endif # USART5_SERIALDRIVER

config SAMD2L2_HAVE_SPI
	bool
	select SPI

menu "SPI options"
	depends on SAMD2L2_HAVE_SPI

config SAMD2L2_SPI_DMA
	bool "SPI DMA"
	default n
	depends on SAMD2L2_DMAC && EXPERIMENTAL
	---help---
		Use DMA for SPI SERCOM peripherals.

config SAMD2L2_SPI_REGDEBUG
	bool "SPI register-Level Debug"
	default n
	depends on DEBUG_SPI_INFO
	---help---
		Enable very low-level register access debug.  Depends on DEBUG_SPI.

endmenu # SPI options

config SAMD2L2_HAVE_I2C
	bool
	select I2C

menu "I2C options"
	depends on SAMD2L2_HAVE_I2C

config SAMD2L2_I2C_REGDEBUG
	bool "I2C register-Level Debug"
	default n
	depends on DEBUG_I2C_INFO
	---help---
		Enable very low-level register access debug.  Depends on DEBUG_I2C.

endmenu # I2C options

menu "USB options"
	depends on SAMD2L2_HAVE_USB

config SAMD2L2_USB_ENABLE_PPEP
	bool "Enable Ping-Pong Endpoints"
	default n
	---help---
		To maximize throughput, an endpoint can be configured for ping-pong
		operation.  When this is done the input and output endpoint with the same
		address are used in the same direction.  The CPU or DMA Controller can
		then read/write one data buffer while the USB module writes/reads from
		the other buffer.  This gives double buffered communication.

config SAMD2L2_USB_REGDEBUG
	bool "USB register-Level Debug"
	default n
	depends on DEBUG_USB_INFO
	---help---
		Enable very low-level register access debug.  Depends on
		CONFIG_DEBUG_USB_INFO.

endmenu # USB options
