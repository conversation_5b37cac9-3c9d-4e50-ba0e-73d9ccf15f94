/*********************************************************************************
 * arch/arm/src/imxrt/hardware/rt117x/imxrt117x_gpc.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 *********************************************************************************/

/* Copyright 2022 NXP */

#ifndef __ARCH_ARM_SRC_IMXRT_HARDWARE_RT117X_IMXRT117X_GPC_H
#define __ARCH_ARM_SRC_IMXRT_HARDWARE_RT117X_IMXRT117X_GPC_H

/*********************************************************************************
 * Included Files
 *********************************************************************************/

#include <nuttx/config.h>

/*********************************************************************************
 * Preprocessor Definitions
 *********************************************************************************/

/* GPC_CPU_MODE_CTRL Register Offsets ********************************************/
#define IMXRT_GPC_CPU_MODE_CTRL_CM_AUTHEN_CTRL_OFFSET           (0x0004)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_INT_CTRL_OFFSET              (0x0008)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_MISC_OFFSET                  (0x000c)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_MODE_CTRL_OFFSET             (0x0010)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_MODE_STAT_OFFSET             (0x0014)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_0_OFFSET     (0x0100)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_1_OFFSET     (0x0104)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_2_OFFSET     (0x0108)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_3_OFFSET     (0x010c)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_4_OFFSET     (0x0110)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_5_OFFSET     (0x0114)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_6_OFFSET     (0x0118)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_7_OFFSET     (0x011c)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_NON_IRQ_WAKEUP_MASK_OFFSET   (0x0140)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_0_OFFSET     (0x0150)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_1_OFFSET     (0x0154)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_2_OFFSET     (0x0158)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_3_OFFSET     (0x015c)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_4_OFFSET     (0x0160)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_5_OFFSET     (0x0164)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_6_OFFSET     (0x0168)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_7_OFFSET     (0x016c)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_NON_IRQ_WAKEUP_STAT_OFFSET   (0x0190)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SLEEP_SSAR_CTRL_OFFSET       (0x0200)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SLEEP_LPCG_CTRL_OFFSET       (0x0208)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SLEEP_PLL_CTRL_OFFSET        (0x0210)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SLEEP_ISO_CTRL_OFFSET        (0x0218)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SLEEP_RESET_CTRL_OFFSET      (0x0220)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SLEEP_POWER_CTRL_OFFSET      (0x0228)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_WAKEUP_POWER_CTRL_OFFSET     (0x0290)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_WAKEUP_RESET_CTRL_OFFSET     (0x0298)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_WAKEUP_ISO_CTRL_OFFSET       (0x02a0)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_WAKEUP_PLL_CTRL_OFFSET       (0x02a8)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_WAKEUP_LPCG_CTRL_OFFSET      (0x02b0)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_WAKEUP_SSAR_CTRL_OFFSET      (0x02b8)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SP_CTRL_OFFSET               (0x0300)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SP_STAT_OFFSET               (0x0304)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_RUN_MODE_MAPPING_OFFSET      (0x0310)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_WAIT_MODE_MAPPING_OFFSET     (0x0314)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_STOP_MODE_MAPPING_OFFSET     (0x0318)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SUSPEND_MODE_MAPPING_OFFSET  (0x031c)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SP0_MAPPING_OFFSET           (0x0320)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SP1_MAPPING_OFFSET           (0x0324)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SP2_MAPPING_OFFSET           (0x0328)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SP3_MAPPING_OFFSET           (0x032c)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SP4_MAPPING_OFFSET           (0x0330)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SP5_MAPPING_OFFSET           (0x0334)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SP6_MAPPING_OFFSET           (0x0338)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SP7_MAPPING_OFFSET           (0x033c)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SP8_MAPPING_OFFSET           (0x0340)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SP9_MAPPING_OFFSET           (0x0344)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SP10_MAPPING_OFFSET          (0x0348)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SP11_MAPPING_OFFSET          (0x034c)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SP12_MAPPING_OFFSET          (0x0350)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SP13_MAPPING_OFFSET          (0x0354)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SP14_MAPPING_OFFSET          (0x0358)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_SP15_MAPPING_OFFSET          (0x035c)
#define IMXRT_GPC_CPU_MODE_CTRL_CM_STBY_CTRL_OFFSET             (0x0380)
#define IMXRT_GPC_CPU_MODE_CTRL_0_OFFSET                        (0x0)
#define IMXRT_GPC_CPU_MODE_CTRL_1_OFFSET                        (0x800)
#define IMXRT_GPC_CPU_MODE_CTRL_0_BASE                          (IMXRT_GPC_BASE + IMXRT_GPC_CPU_MODE_CTRL_0_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_BASE                          (IMXRT_GPC_BASE + IMXRT_GPC_CPU_MODE_CTRL_1_OFFSET)

/* GPC_CPU_MODE_CTRL_0 Register Addresses ****************************************/
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_AUTHEN_CTRL           (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_AUTHEN_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_INT_CTRL              (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_INT_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_MISC                  (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_MISC_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_MODE_CTRL             (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_MODE_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_MODE_STAT             (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_MODE_STAT_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_IRQ_WAKEUP_MASK_0     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_0_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_IRQ_WAKEUP_MASK_1     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_1_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_IRQ_WAKEUP_MASK_2     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_2_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_IRQ_WAKEUP_MASK_3     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_3_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_IRQ_WAKEUP_MASK_4     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_4_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_IRQ_WAKEUP_MASK_5     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_5_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_IRQ_WAKEUP_MASK_6     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_6_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_IRQ_WAKEUP_MASK_7     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_7_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_NON_IRQ_WAKEUP_MASK   (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_NON_IRQ_WAKEUP_MASK_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_IRQ_WAKEUP_STAT_0     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_0_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_IRQ_WAKEUP_STAT_1     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_1_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_IRQ_WAKEUP_STAT_2     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_2_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_IRQ_WAKEUP_STAT_3     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_3_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_IRQ_WAKEUP_STAT_4     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_4_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_IRQ_WAKEUP_STAT_5     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_5_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_IRQ_WAKEUP_STAT_6     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_6_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_IRQ_WAKEUP_STAT_7     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_7_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_NON_IRQ_WAKEUP_STAT   (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_NON_IRQ_WAKEUP_STAT_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SLEEP_SSAR_CTRL       (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SLEEP_SSAR_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SLEEP_LPCG_CTRL       (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SLEEP_LPCG_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SLEEP_PLL_CTRL        (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SLEEP_PLL_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SLEEP_ISO_CTRL        (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SLEEP_ISO_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SLEEP_RESET_CTRL      (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SLEEP_RESET_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SLEEP_POWER_CTRL      (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SLEEP_POWER_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_WAKEUP_POWER_CTRL     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_WAKEUP_POWER_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_WAKEUP_RESET_CTRL     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_WAKEUP_RESET_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_WAKEUP_ISO_CTRL       (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_WAKEUP_ISO_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_WAKEUP_PLL_CTRL       (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_WAKEUP_PLL_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_WAKEUP_LPCG_CTRL      (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_WAKEUP_LPCG_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_WAKEUP_SSAR_CTRL      (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_WAKEUP_SSAR_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SP_CTRL               (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SP_STAT               (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP_STAT_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_RUN_MODE_MAPPING      (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_RUN_MODE_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_WAIT_MODE_MAPPING     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_WAIT_MODE_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_STOP_MODE_MAPPING     (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_STOP_MODE_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SUSPEND_MODE_MAPPING  (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SUSPEND_MODE_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SP0_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP0_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SP1_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP1_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SP2_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP2_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SP3_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP3_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SP4_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP4_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SP5_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP5_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SP6_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP6_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SP7_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP7_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SP8_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP8_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SP9_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP9_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SP10_MAPPING          (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP10_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SP11_MAPPING          (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP11_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SP12_MAPPING          (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP12_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SP13_MAPPING          (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP13_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SP14_MAPPING          (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP14_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_SP15_MAPPING          (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP15_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_0_CM_STBY_CTRL             (IMXRT_GPC_CPU_MODE_CTRL_0_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_STBY_CTRL_OFFSET)

/* GPC_CPU_MODE_CTRL_1 Register Addresses ****************************************/
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_AUTHEN_CTRL           (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_AUTHEN_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_INT_CTRL              (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_INT_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_MISC                  (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_MISC_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_MODE_CTRL             (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_MODE_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_MODE_STAT             (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_MODE_STAT_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_IRQ_WAKEUP_MASK_0     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_0_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_IRQ_WAKEUP_MASK_1     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_1_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_IRQ_WAKEUP_MASK_2     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_2_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_IRQ_WAKEUP_MASK_3     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_3_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_IRQ_WAKEUP_MASK_4     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_4_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_IRQ_WAKEUP_MASK_5     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_5_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_IRQ_WAKEUP_MASK_6     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_6_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_IRQ_WAKEUP_MASK_7     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_7_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_NON_IRQ_WAKEUP_MASK   (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_NON_IRQ_WAKEUP_MASK_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_IRQ_WAKEUP_STAT_0     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_0_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_IRQ_WAKEUP_STAT_1     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_1_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_IRQ_WAKEUP_STAT_2     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_2_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_IRQ_WAKEUP_STAT_3     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_3_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_IRQ_WAKEUP_STAT_4     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_4_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_IRQ_WAKEUP_STAT_5     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_5_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_IRQ_WAKEUP_STAT_6     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_6_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_IRQ_WAKEUP_STAT_7     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_7_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_NON_IRQ_WAKEUP_STAT   (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_NON_IRQ_WAKEUP_STAT_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SLEEP_SSAR_CTRL       (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SLEEP_SSAR_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SLEEP_LPCG_CTRL       (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SLEEP_LPCG_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SLEEP_PLL_CTRL        (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SLEEP_PLL_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SLEEP_ISO_CTRL        (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SLEEP_ISO_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SLEEP_RESET_CTRL      (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SLEEP_RESET_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SLEEP_POWER_CTRL      (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SLEEP_POWER_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_WAKEUP_POWER_CTRL     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_WAKEUP_POWER_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_WAKEUP_RESET_CTRL     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_WAKEUP_RESET_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_WAKEUP_ISO_CTRL       (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_WAKEUP_ISO_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_WAKEUP_PLL_CTRL       (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_WAKEUP_PLL_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_WAKEUP_LPCG_CTRL      (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_WAKEUP_LPCG_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_WAKEUP_SSAR_CTRL      (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_WAKEUP_SSAR_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SP_CTRL               (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP_CTRL_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SP_STAT               (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP_STAT_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_RUN_MODE_MAPPING      (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_RUN_MODE_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_WAIT_MODE_MAPPING     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_WAIT_MODE_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_STOP_MODE_MAPPING     (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_STOP_MODE_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SUSPEND_MODE_MAPPING  (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SUSPEND_MODE_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SP0_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP0_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SP1_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP1_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SP2_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP2_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SP3_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP3_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SP4_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP4_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SP5_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP5_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SP6_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP6_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SP7_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP7_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SP8_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP8_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SP9_MAPPING           (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP9_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SP10_MAPPING          (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP10_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SP11_MAPPING          (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP11_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SP12_MAPPING          (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP12_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SP13_MAPPING          (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP13_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SP14_MAPPING          (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP14_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_SP15_MAPPING          (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_SP15_MAPPING_OFFSET)
#define IMXRT_GPC_CPU_MODE_CTRL_1_CM_STBY_CTRL             (IMXRT_GPC_CPU_MODE_CTRL_1_BASE + IMXRT_GPC_CPU_MODE_CTRL_CM_STBY_CTRL_OFFSET)

/* CM Authentication Control (CM_AUTHEN_CTRL) */
#define GPC_CPU_MODE_CTRL_CM_AUTHEN_CTRL_USER              (1 << 0)   /* Bit 0: Allow user mode access */
#define GPC_CPU_MODE_CTRL_CM_AUTHEN_CTRL_NONSECURE         (1 << 1)   /* Bit 1: Allow non-secure mode access */
#define GPC_CPU_MODE_CTRL_CM_AUTHEN_CTRL_LOCK_SETTING      (1 << 4)   /* Bit 4: Lock NONSECURE and USER */
#define GPC_CPU_MODE_CTRL_CM_AUTHEN_CTRL_WHITE_LIST_SHIFT  (8)        /* Bits 8-12: Domain ID white list */
#define GPC_CPU_MODE_CTRL_CM_AUTHEN_CTRL_WHITE_LIST_MASK   (0xF << GPC_CPU_MODE_CTRL_CM_AUTHEN_CTRL_WHITE_LIST_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_AUTHEN_CTRL_WHITE_LIST(n)     (((n) << GPC_CPU_MODE_CTRL_CM_AUTHEN_CTRL_WHITE_LIST_SHIFT) & GPC_CPU_MODE_CTRL_CM_AUTHEN_CTRL_WHITE_LIST_MASK)
#define GPC_CPU_MODE_CTRL_CM_AUTHEN_CTRL_LOCK_LIST         (1 << 12)  /* Bit 12: White list lock */
#define GPC_CPU_MODE_CTRL_CM_AUTHEN_CTRL_LOCK_CFG          (1 << 20)  /* Bit 20: Configuration lock */

/* CM Interrupt Control (CM_INT_CTRL) */
#define GPC_CPU_MODE_CTRL_CM_INT_CTRL_SP_REQ_NOT_ALLOWED_SLEEP_INT_EN   (1 << 0)   /* Bit 0: sp_req_not_allowed_for_sleep interrupt enable */
#define GPC_CPU_MODE_CTRL_CM_INT_CTRL_SP_REQ_NOT_ALLOWED_WAKEUP_INT_EN  (1 << 1)   /* Bit 1: sp_req_not_allowed_for_wakeup interrupt enable */
#define GPC_CPU_MODE_CTRL_CM_INT_CTRL_SP_REQ_NOT_ALLOWED_SOFT_INT_EN    (1 << 2)   /* Bit 2: sp_req_not_allowed_for_soft interrupt enable */
#define GPC_CPU_MODE_CTRL_CM_INT_CTRL_SP_REQ_NOT_ALLOWED_SLEEP_INT      (1 << 16)  /* Bit 16: sp_req_not_allowed_for_sleep interrupt status and clear register */
#define GPC_CPU_MODE_CTRL_CM_INT_CTRL_SP_REQ_NOT_ALLOWED_WAKEUP_INT     (1 << 17)  /* Bit 17: sp_req_not_allowed_for_wakeup interrupt status and clear register */
#define GPC_CPU_MODE_CTRL_CM_INT_CTRL_SP_REQ_NOT_ALLOWED_SOFT_INT       (1 << 18)  /* Bit 18: sp_req_not_allowed_for_soft interrupt status and clear register */

/* Miscellaneous (CM_MISC) */
#define GPC_CPU_MODE_CTRL_CM_MISC_NMI_STAT         (1 << 0)  /* Bit 0: Non-masked interrupt status */
#define GPC_CPU_MODE_CTRL_CM_MISC_SLEEP_HOLD_EN    (1 << 1)  /* Bit 1: Allow cpu_sleep_hold_req assert during CPU low power status */
#define GPC_CPU_MODE_CTRL_CM_MISC_SLEEP_HOLD_STAT  (1 << 2)  /* Bit 2: Status of cpu_sleep_hold_ack_b */
#define GPC_CPU_MODE_CTRL_CM_MISC_MASTER_CPU       (1 << 4)  /* Bit 4: Master CPU */

#define GPC_CPU_MODE_RUN_MODE                      (0x0)
#define GPC_CPU_MODE_WAIT_MODE                     (0x1)
#define GPC_CPU_MODE_STOP_MODE                     (0x2)
#define GPC_CPU_MODE_SUSPEND_MODE                  (0x3)

/* CPU mode control (CM_MODE_CTRL) */
#define GPC_CPU_MODE_CTRL_CM_MODE_CTRL_CPU_MODE_TARGET_SHIFT  (0)       /* Bits 0-2: The CPU mode the CPU platform should transit to on next sleep event */
#define GPC_CPU_MODE_CTRL_CM_MODE_CTRL_CPU_MODE_TARGET_MASK   (0x3 << GPC_CPU_MODE_CTRL_CM_MODE_CTRL_CPU_MODE_TARGET_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_MODE_CTRL_CPU_MODE_TARGET(n)     (((n) << GPC_CPU_MODE_CTRL_CM_MODE_CTRL_CPU_MODE_TARGET_SHIFT) & GPC_CPU_MODE_CTRL_CM_MODE_CTRL_CPU_MODE_TARGET_MASK)
#define GPC_CPU_MODE_CTRL_CM_MODE_CTRL_WFE_EN                 (1 << 4)  /* Bit 4: WFE assertion can be sleep event */

/* CM CPU mode Status (CM_MODE_STAT) */
#define GPC_CPU_MODE_CTRL_CM_MODE_STAT_CPU_MODE_CURRENT_SHIFT   (0)  /* Bits 0-2: Current CPU mode */
#define GPC_CPU_MODE_CTRL_CM_MODE_STAT_CPU_MODE_CURRENT_MASK    (0x3 << GPC_CPU_MODE_CTRL_CM_MODE_STAT_CPU_MODE_CURRENT_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_MODE_STAT_CPU_MODE_CURRENT(n)      (((n) << GPC_CPU_MODE_CTRL_CM_MODE_STAT_CPU_MODE_CURRENT_SHIFT) & GPC_CPU_MODE_CTRL_CM_MODE_STAT_CPU_MODE_CURRENT_MASK)
#define GPC_CPU_MODE_CTRL_CM_MODE_STAT_CPU_MODE_PREVIOUS_SHIFT  (2)  /* Bits 2-4: Previous CPU mode */
#define GPC_CPU_MODE_CTRL_CM_MODE_STAT_CPU_MODE_PREVIOUS_MASK   (0x3 << GPC_CPU_MODE_CTRL_CM_MODE_STAT_CPU_MODE_PREVIOUS_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_MODE_STAT_CPU_MODE_PREVIOUS(n)     (((n) << GPC_CPU_MODE_CTRL_CM_MODE_STAT_CPU_MODE_PREVIOUS_SHIFT) & GPC_CPU_MODE_CTRL_CM_MODE_STAT_CPU_MODE_PREVIOUS_MASK)

/* CM IRQ0~31 wakeup mask (CM_IRQ_WAKEUP_MASK_0) */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_0_IRQ_WAKEUP_MASK_0_31_SHIFT  (0)  /* Bits 0-32: "1" means the IRQ cannot wakeup CPU platform */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_0_IRQ_WAKEUP_MASK_0_31_MASK   (0xFFFFFFFF << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_0_IRQ_WAKEUP_MASK_0_31_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_0_IRQ_WAKEUP_MASK_0_31(n)     (((n) << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_0_IRQ_WAKEUP_MASK_0_31_SHIFT) & GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_0_IRQ_WAKEUP_MASK_0_31_MASK)

/* CM IRQ32~63 wakeup mask (CM_IRQ_WAKEUP_MASK_1) */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_1_IRQ_WAKEUP_MASK_32_63_SHIFT  (0)  /* Bits 0-32: "1" means the IRQ cannot wakeup CPU platform */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_1_IRQ_WAKEUP_MASK_32_63_MASK   (0xFFFFFFFF << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_1_IRQ_WAKEUP_MASK_32_63_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_1_IRQ_WAKEUP_MASK_32_63(n)     (((n) << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_1_IRQ_WAKEUP_MASK_32_63_SHIFT) & GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_1_IRQ_WAKEUP_MASK_32_63_MASK)

/* CM IRQ64~95 wakeup mask (CM_IRQ_WAKEUP_MASK_2) */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_2_IRQ_WAKEUP_MASK_64_95_SHIFT  (0)  /* Bits 0-32: "1" means the IRQ cannot wakeup CPU platform */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_2_IRQ_WAKEUP_MASK_64_95_MASK   (0xFFFFFFFF << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_2_IRQ_WAKEUP_MASK_64_95_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_2_IRQ_WAKEUP_MASK_64_95(n)     (((n) << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_2_IRQ_WAKEUP_MASK_64_95_SHIFT) & GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_2_IRQ_WAKEUP_MASK_64_95_MASK)

/* CM IRQ96~127 wakeup mask (CM_IRQ_WAKEUP_MASK_3) */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_3_IRQ_WAKEUP_MASK_96_127_SHIFT  (0)  /* Bits 0-32: "1" means the IRQ cannot wakeup CPU platform */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_3_IRQ_WAKEUP_MASK_96_127_MASK   (0xFFFFFFFF << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_3_IRQ_WAKEUP_MASK_96_127_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_3_IRQ_WAKEUP_MASK_96_127(n)     (((n) << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_3_IRQ_WAKEUP_MASK_96_127_SHIFT) & GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_3_IRQ_WAKEUP_MASK_96_127_MASK)

/* CM IRQ128~159 wakeup mask (CM_IRQ_WAKEUP_MASK_4) */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_4_IRQ_WAKEUP_MASK_128_159_SHIFT  (0)  /* Bits 0-32: "1" means the IRQ cannot wakeup CPU platform */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_4_IRQ_WAKEUP_MASK_128_159_MASK   (0xFFFFFFFF << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_4_IRQ_WAKEUP_MASK_128_159_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_4_IRQ_WAKEUP_MASK_128_159(n)     (((n) << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_4_IRQ_WAKEUP_MASK_128_159_SHIFT) & GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_4_IRQ_WAKEUP_MASK_128_159_MASK)

/* CM IRQ160~191 wakeup mask (CM_IRQ_WAKEUP_MASK_5) */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_5_IRQ_WAKEUP_MASK_160_191_SHIFT  (0)  /* Bits 0-32: "1" means the IRQ cannot wakeup CPU platform */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_5_IRQ_WAKEUP_MASK_160_191_MASK   (0xFFFFFFFF << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_5_IRQ_WAKEUP_MASK_160_191_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_5_IRQ_WAKEUP_MASK_160_191(n)     (((n) << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_5_IRQ_WAKEUP_MASK_160_191_SHIFT) & GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_5_IRQ_WAKEUP_MASK_160_191_MASK)

/* CM IRQ192~223 wakeup mask (CM_IRQ_WAKEUP_MASK_6) */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_6_IRQ_WAKEUP_MASK_192_223_SHIFT  (0)  /* Bits 0-32: "1" means the IRQ cannot wakeup CPU platform */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_6_IRQ_WAKEUP_MASK_192_223_MASK   (0xFFFFFFFF << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_6_IRQ_WAKEUP_MASK_192_223_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_6_IRQ_WAKEUP_MASK_192_223(n)     (((n) << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_6_IRQ_WAKEUP_MASK_192_223_SHIFT) & GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_6_IRQ_WAKEUP_MASK_192_223_MASK)

/* CM IRQ224~255 wakeup mask (CM_IRQ_WAKEUP_MASK_7) */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_7_IRQ_WAKEUP_MASK_224_255_SHIFT  (0)  /* Bits 0-32: "1" means the IRQ cannot wakeup CPU platform */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_7_IRQ_WAKEUP_MASK_224_255_MASK   (0xFFFFFFFF << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_7_IRQ_WAKEUP_MASK_224_255_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_7_IRQ_WAKEUP_MASK_224_255(n)     (((n) << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_7_IRQ_WAKEUP_MASK_224_255_SHIFT) & GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_MASK_7_IRQ_WAKEUP_MASK_224_255_MASK)

/* CM non-irq wakeup mask (CM_NON_IRQ_WAKEUP_MASK) */
#define GPC_CPU_MODE_CTRL_CM_NON_IRQ_WAKEUP_MASK_EVENT_WAKEUP_MASK  (1 << 0)  /* Bit 0: There are 256 interrupts and 1 event as a wakeup source for GPC. This field masks the 1 event wakeup source. */
#define GPC_CPU_MODE_CTRL_CM_NON_IRQ_WAKEUP_MASK_DEBUG_WAKEUP_MASK  (1 << 1)  /* Bit 1: "1" means the debug_wakeup_request cannot wakeup CPU platform */

/* CM IRQ0~31 wakeup status (CM_IRQ_WAKEUP_STAT_0) */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_0_IRQ_WAKEUP_STAT_0_31_SHIFT  (0)  /* Bits 0-32: IRQ status */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_0_IRQ_WAKEUP_STAT_0_31_MASK   (0xFFFFFFFF << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_0_IRQ_WAKEUP_STAT_0_31_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_0_IRQ_WAKEUP_STAT_0_31(n)     (((n) << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_0_IRQ_WAKEUP_STAT_0_31_SHIFT) & GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_0_IRQ_WAKEUP_STAT_0_31_MASK)

/* CM IRQ32~63 wakeup status (CM_IRQ_WAKEUP_STAT_1) */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_1_IRQ_WAKEUP_STAT_32_63_SHIFT  (0)  /* Bits 0-32: IRQ status */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_1_IRQ_WAKEUP_STAT_32_63_MASK   (0xFFFFFFFF << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_1_IRQ_WAKEUP_STAT_32_63_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_1_IRQ_WAKEUP_STAT_32_63(n)     (((n) << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_1_IRQ_WAKEUP_STAT_32_63_SHIFT) & GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_1_IRQ_WAKEUP_STAT_32_63_MASK)

/* CM IRQ64~95 wakeup status (CM_IRQ_WAKEUP_STAT_2) */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_2_IRQ_WAKEUP_STAT_64_95_SHIFT  (0)  /* Bits 0-32: IRQ status */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_2_IRQ_WAKEUP_STAT_64_95_MASK   (0xFFFFFFFF << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_2_IRQ_WAKEUP_STAT_64_95_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_2_IRQ_WAKEUP_STAT_64_95(n)     (((n) << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_2_IRQ_WAKEUP_STAT_64_95_SHIFT) & GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_2_IRQ_WAKEUP_STAT_64_95_MASK)

/* CM IRQ96~127 wakeup status (CM_IRQ_WAKEUP_STAT_3) */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_3_IRQ_WAKEUP_STAT_96_127_SHIFT  (0)  /* Bits 0-32: IRQ status */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_3_IRQ_WAKEUP_STAT_96_127_MASK   (0xFFFFFFFF << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_3_IRQ_WAKEUP_STAT_96_127_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_3_IRQ_WAKEUP_STAT_96_127(n)     (((n) << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_3_IRQ_WAKEUP_STAT_96_127_SHIFT) & GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_3_IRQ_WAKEUP_STAT_96_127_MASK)

/* CM IRQ128~159 wakeup status (CM_IRQ_WAKEUP_STAT_4) */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_4_IRQ_WAKEUP_STAT_128_159_SHIFT  (0)  /* Bits 0-32: IRQ status */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_4_IRQ_WAKEUP_STAT_128_159_MASK   (0xFFFFFFFF << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_4_IRQ_WAKEUP_STAT_128_159_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_4_IRQ_WAKEUP_STAT_128_159(n)     (((n) << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_4_IRQ_WAKEUP_STAT_128_159_SHIFT) & GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_4_IRQ_WAKEUP_STAT_128_159_MASK)

/* CM IRQ160~191 wakeup status (CM_IRQ_WAKEUP_STAT_5) */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_5_IRQ_WAKEUP_STAT_160_191_SHIFT  (0)  /* Bits 0-32: IRQ status */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_5_IRQ_WAKEUP_STAT_160_191_MASK   (0xFFFFFFFF << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_5_IRQ_WAKEUP_STAT_160_191_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_5_IRQ_WAKEUP_STAT_160_191(n)     (((n) << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_5_IRQ_WAKEUP_STAT_160_191_SHIFT) & GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_5_IRQ_WAKEUP_STAT_160_191_MASK)

/* CM IRQ192~223 wakeup status (CM_IRQ_WAKEUP_STAT_6) */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_6_IRQ_WAKEUP_STAT_192_223_SHIFT  (0)  /* Bits 0-32: IRQ status */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_6_IRQ_WAKEUP_STAT_192_223_MASK   (0xFFFFFFFF << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_6_IRQ_WAKEUP_STAT_192_223_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_6_IRQ_WAKEUP_STAT_192_223(n)     (((n) << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_6_IRQ_WAKEUP_STAT_192_223_SHIFT) & GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_6_IRQ_WAKEUP_STAT_192_223_MASK)

/* CM IRQ224~255 wakeup status (CM_IRQ_WAKEUP_STAT_7) */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_7_IRQ_WAKEUP_MASK_224_255_SHIFT  (0)  /* Bits 0-32: IRQ status */
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_7_IRQ_WAKEUP_MASK_224_255_MASK   (0xFFFFFFFF << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_7_IRQ_WAKEUP_MASK_224_255_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_7_IRQ_WAKEUP_MASK_224_255(n)     (((n) << GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_7_IRQ_WAKEUP_MASK_224_255_SHIFT) & GPC_CPU_MODE_CTRL_CM_IRQ_WAKEUP_STAT_7_IRQ_WAKEUP_MASK_224_255_MASK)

/* CM non-irq wakeup status (CM_NON_IRQ_WAKEUP_STAT) */
#define GPC_CPU_MODE_CTRL_CM_NON_IRQ_WAKEUP_STAT_EVENT_WAKEUP_STAT  (1 << 0)  /* Bit 0: Event wakeup status */
#define GPC_CPU_MODE_CTRL_CM_NON_IRQ_WAKEUP_STAT_DEBUG_WAKEUP_STAT  (1 << 1)  /* Bit 1: Debug wakeup status */

/* CM sleep SSAR control (CM_SLEEP_SSAR_CTRL) */
#define GPC_CPU_MODE_CTRL_CM_SLEEP_SSAR_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE. */
#define GPC_CPU_MODE_CTRL_CM_SLEEP_SSAR_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SLEEP_SSAR_CTRL_STEP_CNT_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_SSAR_CTRL_STEP_CNT(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SLEEP_SSAR_CTRL_STEP_CNT_SHIFT) & GPC_CPU_MODE_CTRL_CM_SLEEP_SSAR_CTRL_STEP_CNT_MASK)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_SSAR_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_CPU_MODE_CTRL_CM_SLEEP_SSAR_CTRL_CNT_MODE_MASK   (0x3 << GPC_CPU_MODE_CTRL_CM_SLEEP_SSAR_CTRL_CNT_MODE_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_SSAR_CTRL_CNT_MODE(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SLEEP_SSAR_CTRL_CNT_MODE_SHIFT) & GPC_CPU_MODE_CTRL_CM_SLEEP_SSAR_CTRL_CNT_MODE_MASK)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_SSAR_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* CM sleep LPCG control (CM_SLEEP_LPCG_CTRL) */
#define GPC_CPU_MODE_CTRL_CM_SLEEP_LPCG_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_CPU_MODE_CTRL_CM_SLEEP_LPCG_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SLEEP_LPCG_CTRL_STEP_CNT_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_LPCG_CTRL_STEP_CNT(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SLEEP_LPCG_CTRL_STEP_CNT_SHIFT) & GPC_CPU_MODE_CTRL_CM_SLEEP_LPCG_CTRL_STEP_CNT_MASK)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_LPCG_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_CPU_MODE_CTRL_CM_SLEEP_LPCG_CTRL_CNT_MODE_MASK   (0x3 << GPC_CPU_MODE_CTRL_CM_SLEEP_LPCG_CTRL_CNT_MODE_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_LPCG_CTRL_CNT_MODE(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SLEEP_LPCG_CTRL_CNT_MODE_SHIFT) & GPC_CPU_MODE_CTRL_CM_SLEEP_LPCG_CTRL_CNT_MODE_MASK)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_LPCG_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* CM sleep PLL control (CM_SLEEP_PLL_CTRL) */
#define GPC_CPU_MODE_CTRL_CM_SLEEP_PLL_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_CPU_MODE_CTRL_CM_SLEEP_PLL_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SLEEP_PLL_CTRL_STEP_CNT_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_PLL_CTRL_STEP_CNT(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SLEEP_PLL_CTRL_STEP_CNT_SHIFT) & GPC_CPU_MODE_CTRL_CM_SLEEP_PLL_CTRL_STEP_CNT_MASK)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_PLL_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_CPU_MODE_CTRL_CM_SLEEP_PLL_CTRL_CNT_MODE_MASK   (0x3 << GPC_CPU_MODE_CTRL_CM_SLEEP_PLL_CTRL_CNT_MODE_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_PLL_CTRL_CNT_MODE(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SLEEP_PLL_CTRL_CNT_MODE_SHIFT) & GPC_CPU_MODE_CTRL_CM_SLEEP_PLL_CTRL_CNT_MODE_MASK)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_PLL_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* CM sleep isolation control (CM_SLEEP_ISO_CTRL) */
#define GPC_CPU_MODE_CTRL_CM_SLEEP_ISO_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_CPU_MODE_CTRL_CM_SLEEP_ISO_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SLEEP_ISO_CTRL_STEP_CNT_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_ISO_CTRL_STEP_CNT(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SLEEP_ISO_CTRL_STEP_CNT_SHIFT) & GPC_CPU_MODE_CTRL_CM_SLEEP_ISO_CTRL_STEP_CNT_MASK)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_ISO_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_CPU_MODE_CTRL_CM_SLEEP_ISO_CTRL_CNT_MODE_MASK   (0x3 << GPC_CPU_MODE_CTRL_CM_SLEEP_ISO_CTRL_CNT_MODE_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_ISO_CTRL_CNT_MODE(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SLEEP_ISO_CTRL_CNT_MODE_SHIFT) & GPC_CPU_MODE_CTRL_CM_SLEEP_ISO_CTRL_CNT_MODE_MASK)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_ISO_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* CM sleep reset control (CM_SLEEP_RESET_CTRL) */
#define GPC_CPU_MODE_CTRL_CM_SLEEP_RESET_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_CPU_MODE_CTRL_CM_SLEEP_RESET_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SLEEP_RESET_CTRL_STEP_CNT_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_RESET_CTRL_STEP_CNT(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SLEEP_RESET_CTRL_STEP_CNT_SHIFT) & GPC_CPU_MODE_CTRL_CM_SLEEP_RESET_CTRL_STEP_CNT_MASK)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_RESET_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_CPU_MODE_CTRL_CM_SLEEP_RESET_CTRL_CNT_MODE_MASK   (0x3 << GPC_CPU_MODE_CTRL_CM_SLEEP_RESET_CTRL_CNT_MODE_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_RESET_CTRL_CNT_MODE(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SLEEP_RESET_CTRL_CNT_MODE_SHIFT) & GPC_CPU_MODE_CTRL_CM_SLEEP_RESET_CTRL_CNT_MODE_MASK)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_RESET_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* CM sleep power control (CM_SLEEP_POWER_CTRL) */
#define GPC_CPU_MODE_CTRL_CM_SLEEP_POWER_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_CPU_MODE_CTRL_CM_SLEEP_POWER_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SLEEP_POWER_CTRL_STEP_CNT_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_POWER_CTRL_STEP_CNT(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SLEEP_POWER_CTRL_STEP_CNT_SHIFT) & GPC_CPU_MODE_CTRL_CM_SLEEP_POWER_CTRL_STEP_CNT_MASK)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_POWER_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_CPU_MODE_CTRL_CM_SLEEP_POWER_CTRL_CNT_MODE_MASK   (0x3 << GPC_CPU_MODE_CTRL_CM_SLEEP_POWER_CTRL_CNT_MODE_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_POWER_CTRL_CNT_MODE(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SLEEP_POWER_CTRL_CNT_MODE_SHIFT) & GPC_CPU_MODE_CTRL_CM_SLEEP_POWER_CTRL_CNT_MODE_MASK)
#define GPC_CPU_MODE_CTRL_CM_SLEEP_POWER_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* CM wakeup power control (CM_WAKEUP_POWER_CTRL) */
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_POWER_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_POWER_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_WAKEUP_POWER_CTRL_STEP_CNT_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_POWER_CTRL_STEP_CNT(n)     (((n) << GPC_CPU_MODE_CTRL_CM_WAKEUP_POWER_CTRL_STEP_CNT_SHIFT) & GPC_CPU_MODE_CTRL_CM_WAKEUP_POWER_CTRL_STEP_CNT_MASK)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_POWER_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_POWER_CTRL_CNT_MODE_MASK   (0x3 << GPC_CPU_MODE_CTRL_CM_WAKEUP_POWER_CTRL_CNT_MODE_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_POWER_CTRL_CNT_MODE(n)     (((n) << GPC_CPU_MODE_CTRL_CM_WAKEUP_POWER_CTRL_CNT_MODE_SHIFT) & GPC_CPU_MODE_CTRL_CM_WAKEUP_POWER_CTRL_CNT_MODE_MASK)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_POWER_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* CM wakeup reset control (CM_WAKEUP_RESET_CTRL) */
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_RESET_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_RESET_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_WAKEUP_RESET_CTRL_STEP_CNT_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_RESET_CTRL_STEP_CNT(n)     (((n) << GPC_CPU_MODE_CTRL_CM_WAKEUP_RESET_CTRL_STEP_CNT_SHIFT) & GPC_CPU_MODE_CTRL_CM_WAKEUP_RESET_CTRL_STEP_CNT_MASK)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_RESET_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_RESET_CTRL_CNT_MODE_MASK   (0x3 << GPC_CPU_MODE_CTRL_CM_WAKEUP_RESET_CTRL_CNT_MODE_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_RESET_CTRL_CNT_MODE(n)     (((n) << GPC_CPU_MODE_CTRL_CM_WAKEUP_RESET_CTRL_CNT_MODE_SHIFT) & GPC_CPU_MODE_CTRL_CM_WAKEUP_RESET_CTRL_CNT_MODE_MASK)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_RESET_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* CM wakeup isolation control (CM_WAKEUP_ISO_CTRL) */
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_ISO_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_ISO_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_WAKEUP_ISO_CTRL_STEP_CNT_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_ISO_CTRL_STEP_CNT(n)     (((n) << GPC_CPU_MODE_CTRL_CM_WAKEUP_ISO_CTRL_STEP_CNT_SHIFT) & GPC_CPU_MODE_CTRL_CM_WAKEUP_ISO_CTRL_STEP_CNT_MASK)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_ISO_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_ISO_CTRL_CNT_MODE_MASK   (0x3 << GPC_CPU_MODE_CTRL_CM_WAKEUP_ISO_CTRL_CNT_MODE_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_ISO_CTRL_CNT_MODE(n)     (((n) << GPC_CPU_MODE_CTRL_CM_WAKEUP_ISO_CTRL_CNT_MODE_SHIFT) & GPC_CPU_MODE_CTRL_CM_WAKEUP_ISO_CTRL_CNT_MODE_MASK)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_ISO_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* CM wakeup PLL control (CM_WAKEUP_PLL_CTRL) */
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_PLL_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_PLL_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_WAKEUP_PLL_CTRL_STEP_CNT_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_PLL_CTRL_STEP_CNT(n)     (((n) << GPC_CPU_MODE_CTRL_CM_WAKEUP_PLL_CTRL_STEP_CNT_SHIFT) & GPC_CPU_MODE_CTRL_CM_WAKEUP_PLL_CTRL_STEP_CNT_MASK)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_PLL_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_PLL_CTRL_CNT_MODE_MASK   (0x3 << GPC_CPU_MODE_CTRL_CM_WAKEUP_PLL_CTRL_CNT_MODE_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_PLL_CTRL_CNT_MODE(n)     (((n) << GPC_CPU_MODE_CTRL_CM_WAKEUP_PLL_CTRL_CNT_MODE_SHIFT) & GPC_CPU_MODE_CTRL_CM_WAKEUP_PLL_CTRL_CNT_MODE_MASK)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_PLL_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* CM wakeup LPCG control (CM_WAKEUP_LPCG_CTRL) */
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_LPCG_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_LPCG_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_WAKEUP_LPCG_CTRL_STEP_CNT_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_LPCG_CTRL_STEP_CNT(n)     (((n) << GPC_CPU_MODE_CTRL_CM_WAKEUP_LPCG_CTRL_STEP_CNT_SHIFT) & GPC_CPU_MODE_CTRL_CM_WAKEUP_LPCG_CTRL_STEP_CNT_MASK)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_LPCG_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_LPCG_CTRL_CNT_MODE_MASK   (0x3 << GPC_CPU_MODE_CTRL_CM_WAKEUP_LPCG_CTRL_CNT_MODE_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_LPCG_CTRL_CNT_MODE(n)     (((n) << GPC_CPU_MODE_CTRL_CM_WAKEUP_LPCG_CTRL_CNT_MODE_SHIFT) & GPC_CPU_MODE_CTRL_CM_WAKEUP_LPCG_CTRL_CNT_MODE_MASK)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_LPCG_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* CM wakeup SSAR control (CM_WAKEUP_SSAR_CTRL) */
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_SSAR_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_SSAR_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_WAKEUP_SSAR_CTRL_STEP_CNT_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_SSAR_CTRL_STEP_CNT(n)     (((n) << GPC_CPU_MODE_CTRL_CM_WAKEUP_SSAR_CTRL_STEP_CNT_SHIFT) & GPC_CPU_MODE_CTRL_CM_WAKEUP_SSAR_CTRL_STEP_CNT_MASK)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_SSAR_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_SSAR_CTRL_CNT_MODE_MASK   (0x3 << GPC_CPU_MODE_CTRL_CM_WAKEUP_SSAR_CTRL_CNT_MODE_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_SSAR_CTRL_CNT_MODE(n)     (((n) << GPC_CPU_MODE_CTRL_CM_WAKEUP_SSAR_CTRL_CNT_MODE_SHIFT) & GPC_CPU_MODE_CTRL_CM_WAKEUP_SSAR_CTRL_CNT_MODE_MASK)
#define GPC_CPU_MODE_CTRL_CM_WAKEUP_SSAR_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* CM Setpoint Control (CM_SP_CTRL) */
#define GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_RUN_EN        (1 << 0)   /* Bit 0: Request a Setpoint transition when this bit is set */
#define GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_RUN_SHIFT     (1)        /* Bits 1-5: The Setpoint that CPU want the system to transit to when CPU_SP_RUN_EN is set */
#define GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_RUN_MASK      (0xF << GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_RUN_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_RUN(n)        (((n) << GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_RUN_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_RUN_MASK)
#define GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_SLEEP_EN      (1 << 5)   /* Bit 5: 1 means enable Setpoint transition on next CPU platform sleep sequence */
#define GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_SLEEP_SHIFT   (6)        /* Bits 6-10: The Setpoint that CPU want the system to transit to on next CPU platform sleep sequence */
#define GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_SLEEP_MASK    (0xF << GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_SLEEP_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_SLEEP(n)      (((n) << GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_SLEEP_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_SLEEP_MASK)
#define GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_WAKEUP_EN     (1 << 10)  /* Bit 10: 1 means enable Setpoint transition on next CPU platform wakeup sequence */
#define GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_WAKEUP_SHIFT  (11)       /* Bits 11-15: The Setpoint that CPU want the system to transit to on next CPU platform wakeup sequence */
#define GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_WAKEUP_MASK   (0xF << GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_WAKEUP_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_WAKEUP(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_WAKEUP_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_WAKEUP_MASK)
#define GPC_CPU_MODE_CTRL_CM_SP_CTRL_CPU_SP_WAKEUP_SEL    (1 << 15)  /* Bit 15: Select the Setpoint transiton on the next CPU platform wakeup sequence */

/* CM Setpoint Status (CM_SP_STAT) */
#define GPC_CPU_MODE_CTRL_CM_SP_STAT_CPU_SP_CURRENT_SHIFT   (0)  /* Bits 0-4: The current Setpoint of the system */
#define GPC_CPU_MODE_CTRL_CM_SP_STAT_CPU_SP_CURRENT_MASK    (0xF << GPC_CPU_MODE_CTRL_CM_SP_STAT_CPU_SP_CURRENT_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP_STAT_CPU_SP_CURRENT(n)      (((n) << GPC_CPU_MODE_CTRL_CM_SP_STAT_CPU_SP_CURRENT_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP_STAT_CPU_SP_CURRENT_MASK)
#define GPC_CPU_MODE_CTRL_CM_SP_STAT_CPU_SP_PREVIOUS_SHIFT  (4)  /* Bits 4-8: The previous Setpoint of the system */
#define GPC_CPU_MODE_CTRL_CM_SP_STAT_CPU_SP_PREVIOUS_MASK   (0xF << GPC_CPU_MODE_CTRL_CM_SP_STAT_CPU_SP_PREVIOUS_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP_STAT_CPU_SP_PREVIOUS(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SP_STAT_CPU_SP_PREVIOUS_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP_STAT_CPU_SP_PREVIOUS_MASK)
#define GPC_CPU_MODE_CTRL_CM_SP_STAT_CPU_SP_TARGET_SHIFT    (8)  /* Bits 8-12: The requested Setpoint from the CPU platform */
#define GPC_CPU_MODE_CTRL_CM_SP_STAT_CPU_SP_TARGET_MASK     (0xF << GPC_CPU_MODE_CTRL_CM_SP_STAT_CPU_SP_TARGET_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP_STAT_CPU_SP_TARGET(n)       (((n) << GPC_CPU_MODE_CTRL_CM_SP_STAT_CPU_SP_TARGET_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP_STAT_CPU_SP_TARGET_MASK)

/* CM Run Mode Setpoint Allowed (CM_RUN_MODE_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_RUN_MODE_MAPPING_CPU_RUN_MODE_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines which Setpoint is allowed when CPU enters RUN mode. Each bit stands for 1 Setpoint, locked by LOCK_CFG field */
#define GPC_CPU_MODE_CTRL_CM_RUN_MODE_MAPPING_CPU_RUN_MODE_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_RUN_MODE_MAPPING_CPU_RUN_MODE_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_RUN_MODE_MAPPING_CPU_RUN_MODE_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_RUN_MODE_MAPPING_CPU_RUN_MODE_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_RUN_MODE_MAPPING_CPU_RUN_MODE_MAPPING_MASK)

/* CM Wait Mode Setpoint Allowed (CM_WAIT_MODE_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_WAIT_MODE_MAPPING_CPU_WAIT_MODE_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines which Setpoint is allowed when CPU enters WAIT mode. Each bit stands for 1 Setpoint, locked by LOCK_CFG */
#define GPC_CPU_MODE_CTRL_CM_WAIT_MODE_MAPPING_CPU_WAIT_MODE_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_WAIT_MODE_MAPPING_CPU_WAIT_MODE_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_WAIT_MODE_MAPPING_CPU_WAIT_MODE_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_WAIT_MODE_MAPPING_CPU_WAIT_MODE_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_WAIT_MODE_MAPPING_CPU_WAIT_MODE_MAPPING_MASK)

/* CM Stop Mode Setpoint Allowed (CM_STOP_MODE_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_STOP_MODE_MAPPING_CPU_STOP_MODE_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines which Setpoint is allowed when CPU enters STOP mode. Each bit stands for 1 Setpoint, locked by LOCK_CFG */
#define GPC_CPU_MODE_CTRL_CM_STOP_MODE_MAPPING_CPU_STOP_MODE_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_STOP_MODE_MAPPING_CPU_STOP_MODE_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_STOP_MODE_MAPPING_CPU_STOP_MODE_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_STOP_MODE_MAPPING_CPU_STOP_MODE_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_STOP_MODE_MAPPING_CPU_STOP_MODE_MAPPING_MASK)

/* CM Suspend Mode Setpoint Allowed (CM_SUSPEND_MODE_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_SUSPEND_MODE_MAPPING_CPU_SUSPEND_MODE_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines which Setpoint is allowed when CPU enters SUSPEND mode. Each bit stands for 1 Setpoint, locked by LOCK_CFG */
#define GPC_CPU_MODE_CTRL_CM_SUSPEND_MODE_MAPPING_CPU_SUSPEND_MODE_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SUSPEND_MODE_MAPPING_CPU_SUSPEND_MODE_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SUSPEND_MODE_MAPPING_CPU_SUSPEND_MODE_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SUSPEND_MODE_MAPPING_CPU_SUSPEND_MODE_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_SUSPEND_MODE_MAPPING_CPU_SUSPEND_MODE_MAPPING_MASK)

/* CM Setpoint 0 Mapping (CM_SP0_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_SP0_MAPPING_CPU_SP0_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines when SP0 is set as the CPU_SP_TARGET, which SP is allowed, locked by LOCK_CFG field */
#define GPC_CPU_MODE_CTRL_CM_SP0_MAPPING_CPU_SP0_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SP0_MAPPING_CPU_SP0_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP0_MAPPING_CPU_SP0_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SP0_MAPPING_CPU_SP0_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP0_MAPPING_CPU_SP0_MAPPING_MASK)

/* CM Setpoint 1 Mapping (CM_SP1_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_SP1_MAPPING_CPU_SP1_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines when SP1 is set as the CPU_SP_TARGET, which SP is allowed, locked by LOCK_CFG field */
#define GPC_CPU_MODE_CTRL_CM_SP1_MAPPING_CPU_SP1_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SP1_MAPPING_CPU_SP1_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP1_MAPPING_CPU_SP1_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SP1_MAPPING_CPU_SP1_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP1_MAPPING_CPU_SP1_MAPPING_MASK)

/* CM Setpoint 2 Mapping (CM_SP2_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_SP2_MAPPING_CPU_SP2_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines when SP2 is set as the CPU_SP_TARGET, which SP is allowed, locked by LOCK_CFG field */
#define GPC_CPU_MODE_CTRL_CM_SP2_MAPPING_CPU_SP2_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SP2_MAPPING_CPU_SP2_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP2_MAPPING_CPU_SP2_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SP2_MAPPING_CPU_SP2_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP2_MAPPING_CPU_SP2_MAPPING_MASK)

/* CM Setpoint 3 Mapping (CM_SP3_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_SP3_MAPPING_CPU_SP3_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines when SP3 is set as the CPU_SP_TARGET, which SP is allowed, locked by LOCK_CFG field */
#define GPC_CPU_MODE_CTRL_CM_SP3_MAPPING_CPU_SP3_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SP3_MAPPING_CPU_SP3_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP3_MAPPING_CPU_SP3_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SP3_MAPPING_CPU_SP3_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP3_MAPPING_CPU_SP3_MAPPING_MASK)

/* CM Setpoint 4 Mapping (CM_SP4_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_SP4_MAPPING_CPU_SP4_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines when SP4 is set as the CPU_SP_TARGET, which SP is allowed, locked by LOCK_CFG field */
#define GPC_CPU_MODE_CTRL_CM_SP4_MAPPING_CPU_SP4_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SP4_MAPPING_CPU_SP4_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP4_MAPPING_CPU_SP4_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SP4_MAPPING_CPU_SP4_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP4_MAPPING_CPU_SP4_MAPPING_MASK)

/* CM Setpoint 5 Mapping (CM_SP5_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_SP5_MAPPING_CPU_SP5_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines when SP5 is set as the CPU_SP_TARGET, which SP is allowed, locked by LOCK_CFG field */
#define GPC_CPU_MODE_CTRL_CM_SP5_MAPPING_CPU_SP5_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SP5_MAPPING_CPU_SP5_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP5_MAPPING_CPU_SP5_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SP5_MAPPING_CPU_SP5_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP5_MAPPING_CPU_SP5_MAPPING_MASK)

/* CM Setpoint 6 Mapping (CM_SP6_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_SP6_MAPPING_CPU_SP6_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines when SP6 is set as the CPU_SP_TARGET, which SP is allowed, locked by LOCK_CFG field */
#define GPC_CPU_MODE_CTRL_CM_SP6_MAPPING_CPU_SP6_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SP6_MAPPING_CPU_SP6_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP6_MAPPING_CPU_SP6_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SP6_MAPPING_CPU_SP6_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP6_MAPPING_CPU_SP6_MAPPING_MASK)

/* CM Setpoint 7 Mapping (CM_SP7_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_SP7_MAPPING_CPU_SP7_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines when SP7 is set as the CPU_SP_TARGET, which SP is allowed, locked by LOCK_CFG field */
#define GPC_CPU_MODE_CTRL_CM_SP7_MAPPING_CPU_SP7_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SP7_MAPPING_CPU_SP7_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP7_MAPPING_CPU_SP7_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SP7_MAPPING_CPU_SP7_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP7_MAPPING_CPU_SP7_MAPPING_MASK)

/* CM Setpoint 8 Mapping (CM_SP8_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_SP8_MAPPING_CPU_SP8_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines when SP8 is set as the CPU_SP_TARGET, which SP is allowed, locked by LOCK_CFG field */
#define GPC_CPU_MODE_CTRL_CM_SP8_MAPPING_CPU_SP8_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SP8_MAPPING_CPU_SP8_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP8_MAPPING_CPU_SP8_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SP8_MAPPING_CPU_SP8_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP8_MAPPING_CPU_SP8_MAPPING_MASK)

/* CM Setpoint 9 Mapping (CM_SP9_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_SP9_MAPPING_CPU_SP9_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines when SP9 is set as the CPU_SP_TARGET, which SP is allowed, locked by LOCK_CFG field */
#define GPC_CPU_MODE_CTRL_CM_SP9_MAPPING_CPU_SP9_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SP9_MAPPING_CPU_SP9_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP9_MAPPING_CPU_SP9_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SP9_MAPPING_CPU_SP9_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP9_MAPPING_CPU_SP9_MAPPING_MASK)

/* CM Setpoint 10 Mapping (CM_SP10_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_SP10_MAPPING_CPU_SP10_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines when SP10 is set as the CPU_SP_TARGET, which SP is allowed, locked by LOCK_CFG field */
#define GPC_CPU_MODE_CTRL_CM_SP10_MAPPING_CPU_SP10_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SP10_MAPPING_CPU_SP10_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP10_MAPPING_CPU_SP10_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SP10_MAPPING_CPU_SP10_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP10_MAPPING_CPU_SP10_MAPPING_MASK)

/* CM Setpoint 11 Mapping (CM_SP11_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_SP11_MAPPING_CPU_SP11_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines when SP11 is set as the CPU_SP_TARGET, which SP is allowed, locked by LOCK_CFG field */
#define GPC_CPU_MODE_CTRL_CM_SP11_MAPPING_CPU_SP11_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SP11_MAPPING_CPU_SP11_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP11_MAPPING_CPU_SP11_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SP11_MAPPING_CPU_SP11_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP11_MAPPING_CPU_SP11_MAPPING_MASK)

/* CM Setpoint 12 Mapping (CM_SP12_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_SP12_MAPPING_CPU_SP12_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines when SP12 is set as the CPU_SP_TARGET, which SP is allowed, locked by LOCK_CFG field */
#define GPC_CPU_MODE_CTRL_CM_SP12_MAPPING_CPU_SP12_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SP12_MAPPING_CPU_SP12_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP12_MAPPING_CPU_SP12_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SP12_MAPPING_CPU_SP12_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP12_MAPPING_CPU_SP12_MAPPING_MASK)

/* CM Setpoint 13 Mapping (CM_SP13_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_SP13_MAPPING_CPU_SP13_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines when SP13 is set as the CPU_SP_TARGET, which SP is allowed, locked by LOCK_CFG field */
#define GPC_CPU_MODE_CTRL_CM_SP13_MAPPING_CPU_SP13_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SP13_MAPPING_CPU_SP13_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP13_MAPPING_CPU_SP13_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SP13_MAPPING_CPU_SP13_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP13_MAPPING_CPU_SP13_MAPPING_MASK)

/* CM Setpoint 14 Mapping (CM_SP14_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_SP14_MAPPING_CPU_SP14_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines when SP14 is set as the CPU_SP_TARGET, which SP is allowed, locked by LOCK_CFG field */
#define GPC_CPU_MODE_CTRL_CM_SP14_MAPPING_CPU_SP14_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SP14_MAPPING_CPU_SP14_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP14_MAPPING_CPU_SP14_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SP14_MAPPING_CPU_SP14_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP14_MAPPING_CPU_SP14_MAPPING_MASK)

/* CM Setpoint 15 Mapping (CM_SP15_MAPPING) */
#define GPC_CPU_MODE_CTRL_CM_SP15_MAPPING_CPU_SP15_MAPPING_SHIFT  (0)  /* Bits 0-16: Defines when SP15 is set as the CPU_SP_TARGET, which SP is allowed, locked by LOCK_CFG field */
#define GPC_CPU_MODE_CTRL_CM_SP15_MAPPING_CPU_SP15_MAPPING_MASK   (0xFFFF << GPC_CPU_MODE_CTRL_CM_SP15_MAPPING_CPU_SP15_MAPPING_SHIFT)
#define GPC_CPU_MODE_CTRL_CM_SP15_MAPPING_CPU_SP15_MAPPING(n)     (((n) << GPC_CPU_MODE_CTRL_CM_SP15_MAPPING_CPU_SP15_MAPPING_SHIFT) & GPC_CPU_MODE_CTRL_CM_SP15_MAPPING_CPU_SP15_MAPPING_MASK)

/* CM standby control (CM_STBY_CTRL) */
#define GPC_CPU_MODE_CTRL_CM_STBY_CTRL_STBY_WAIT         (1 << 0)   /* Bit 0: 0x1: Request the chip into standby mode when CPU entering WAIT mode, locked by LOCK_CFG field. */
#define GPC_CPU_MODE_CTRL_CM_STBY_CTRL_STBY_STOP         (1 << 1)   /* Bit 1: 0x1: Request the chip into standby mode when CPU entering STOP mode, locked by LOCK_CFG field. */
#define GPC_CPU_MODE_CTRL_CM_STBY_CTRL_STBY_SUSPEND      (1 << 2)   /* Bit 2: 0x1: Request the chip into standby mode when CPU entering SUSPEND mode, locked by LOCK_CFG field. */
#define GPC_CPU_MODE_CTRL_CM_STBY_CTRL_STBY_SLEEP_BUSY   (1 << 16)  /* Bit 16: Indicate the CPU is busy entering standby mode. */
#define GPC_CPU_MODE_CTRL_CM_STBY_CTRL_STBY_WAKEUP_BUSY  (1 << 17)  /* Bit 17: Indicate the CPU is busy exiting standby mode. */

/* GPC_SET_POINT_CTRL Register Offsets *******************************************/
#define IMXRT_GPC_SET_POINT_CTRL_OFFSET                       (0x2000)
#define IMXRT_GPC_SET_POINT_CTRL_SP_AUTHEN_CTRL_OFFSET        (0x0004)
#define IMXRT_GPC_SET_POINT_CTRL_SP_INT_CTRL_OFFSET           (0x0008)
#define IMXRT_GPC_SET_POINT_CTRL_SP_CPU_REQ_OFFSET            (0x0010)
#define IMXRT_GPC_SET_POINT_CTRL_SP_SYS_STAT_OFFSET           (0x0014)
#define IMXRT_GPC_SET_POINT_CTRL_SP_ROSC_CTRL_OFFSET          (0x001c)
#define IMXRT_GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_OFFSET       (0x0040)
#define IMXRT_GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_OFFSET      (0x0044)
#define IMXRT_GPC_SET_POINT_CTRL_SP_SSAR_SAVE_CTRL_OFFSET     (0x0100)
#define IMXRT_GPC_SET_POINT_CTRL_SP_LPCG_OFF_CTRL_OFFSET      (0x0110)
#define IMXRT_GPC_SET_POINT_CTRL_SP_GROUP_DOWN_CTRL_OFFSET    (0x0120)
#define IMXRT_GPC_SET_POINT_CTRL_SP_ROOT_DOWN_CTRL_OFFSET     (0x0130)
#define IMXRT_GPC_SET_POINT_CTRL_SP_PLL_OFF_CTRL_OFFSET       (0x0140)
#define IMXRT_GPC_SET_POINT_CTRL_SP_ISO_ON_CTRL_OFFSET        (0x0150)
#define IMXRT_GPC_SET_POINT_CTRL_SP_RESET_EARLY_CTRL_OFFSET   (0x0160)
#define IMXRT_GPC_SET_POINT_CTRL_SP_POWER_OFF_CTRL_OFFSET     (0x0170)
#define IMXRT_GPC_SET_POINT_CTRL_SP_BIAS_OFF_CTRL_OFFSET      (0x0180)
#define IMXRT_GPC_SET_POINT_CTRL_SP_BG_PLDO_OFF_CTRL_OFFSET   (0x0190)
#define IMXRT_GPC_SET_POINT_CTRL_SP_LDO_PRE_CTRL_OFFSET       (0x01a0)
#define IMXRT_GPC_SET_POINT_CTRL_SP_DCDC_DOWN_CTRL_OFFSET     (0x01b0)
#define IMXRT_GPC_SET_POINT_CTRL_SP_DCDC_UP_CTRL_OFFSET       (0x0200)
#define IMXRT_GPC_SET_POINT_CTRL_SP_LDO_POST_CTRL_OFFSET      (0x0210)
#define IMXRT_GPC_SET_POINT_CTRL_SP_BG_PLDO_ON_CTRL_OFFSET    (0x0220)
#define IMXRT_GPC_SET_POINT_CTRL_SP_BIAS_ON_CTRL_OFFSET       (0x0230)
#define IMXRT_GPC_SET_POINT_CTRL_SP_POWER_ON_CTRL_OFFSET      (0x0240)
#define IMXRT_GPC_SET_POINT_CTRL_SP_RESET_LATE_CTRL_OFFSET    (0x0250)
#define IMXRT_GPC_SET_POINT_CTRL_SP_ISO_OFF_CTRL_OFFSET       (0x0260)
#define IMXRT_GPC_SET_POINT_CTRL_SP_PLL_ON_CTRL_OFFSET        (0x0270)
#define IMXRT_GPC_SET_POINT_CTRL_SP_ROOT_UP_CTRL_OFFSET       (0x0280)
#define IMXRT_GPC_SET_POINT_CTRL_SP_GROUP_UP_CTRL_OFFSET      (0x0290)
#define IMXRT_GPC_SET_POINT_CTRL_SP_LPCG_ON_CTRL_OFFSET       (0x02a0)
#define IMXRT_GPC_SET_POINT_CTRL_SP_SSAR_RESTORE_CTRL_OFFSET  (0x02b0)

/* GPC_SET_POINT_CTRL Register Addresses *****************************************/
#define IMXRT_GPC_SET_POINT_CTRL_BASE                  (IMXRT_GPC_BASE + IMXRT_GPC_SET_POINT_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_AUTHEN_CTRL        (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_AUTHEN_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_INT_CTRL           (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_INT_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_CPU_REQ            (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_CPU_REQ_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_SYS_STAT           (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_SYS_STAT_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_ROSC_CTRL          (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_ROSC_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_PRIORITY_0_7       (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_PRIORITY_8_15      (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_SSAR_SAVE_CTRL     (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_SSAR_SAVE_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_LPCG_OFF_CTRL      (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_LPCG_OFF_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_GROUP_DOWN_CTRL    (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_GROUP_DOWN_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_ROOT_DOWN_CTRL     (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_ROOT_DOWN_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_PLL_OFF_CTRL       (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_PLL_OFF_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_ISO_ON_CTRL        (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_ISO_ON_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_RESET_EARLY_CTRL   (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_RESET_EARLY_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_POWER_OFF_CTRL     (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_POWER_OFF_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_BIAS_OFF_CTRL      (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_BIAS_OFF_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_BG_PLDO_OFF_CTRL   (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_BG_PLDO_OFF_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_LDO_PRE_CTRL       (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_LDO_PRE_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_DCDC_DOWN_CTRL     (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_DCDC_DOWN_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_DCDC_UP_CTRL       (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_DCDC_UP_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_LDO_POST_CTRL      (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_LDO_POST_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_BG_PLDO_ON_CTRL    (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_BG_PLDO_ON_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_BIAS_ON_CTRL       (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_BIAS_ON_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_POWER_ON_CTRL      (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_POWER_ON_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_RESET_LATE_CTRL    (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_RESET_LATE_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_ISO_OFF_CTRL       (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_ISO_OFF_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_PLL_ON_CTRL        (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_PLL_ON_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_ROOT_UP_CTRL       (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_ROOT_UP_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_GROUP_UP_CTRL      (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_GROUP_UP_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_LPCG_ON_CTRL       (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_LPCG_ON_CTRL_OFFSET)
#define IMXRT_GPC_SET_POINT_CTRL_SP_SSAR_RESTORE_CTRL  (IMXRT_GPC_SET_POINT_CTRL_BASE + IMXRT_GPC_SET_POINT_CTRL_SP_SSAR_RESTORE_CTRL_OFFSET)

/* SP Authentication Control (SP_AUTHEN_CTRL) */
#define GPC_SET_POINT_CTRL_SP_AUTHEN_CTRL_USER              (1 << 0)   /* Bit 0: Allow user mode access */
#define GPC_SET_POINT_CTRL_SP_AUTHEN_CTRL_NONSECURE         (1 << 1)   /* Bit 1: Allow non-secure mode access */
#define GPC_SET_POINT_CTRL_SP_AUTHEN_CTRL_LOCK_SETTING      (1 << 4)   /* Bit 4: Lock NONSECURE and USER */
#define GPC_SET_POINT_CTRL_SP_AUTHEN_CTRL_WHITE_LIST_SHIFT  (8)        /* Bits 8-12: Domain ID white list */
#define GPC_SET_POINT_CTRL_SP_AUTHEN_CTRL_WHITE_LIST_MASK   (0xF << GPC_SET_POINT_CTRL_SP_AUTHEN_CTRL_WHITE_LIST_SHIFT)
#define GPC_SET_POINT_CTRL_SP_AUTHEN_CTRL_WHITE_LIST(n)     (((n) << GPC_SET_POINT_CTRL_SP_AUTHEN_CTRL_WHITE_LIST_SHIFT) & GPC_SET_POINT_CTRL_SP_AUTHEN_CTRL_WHITE_LIST_MASK)
#define GPC_SET_POINT_CTRL_SP_AUTHEN_CTRL_LOCK_LIST         (1 << 12)  /* Bit 12: White list lock */
#define GPC_SET_POINT_CTRL_SP_AUTHEN_CTRL_LOCK_CFG          (1 << 20)  /* Bit 20: Configuration lock */

/* SP Interrupt Control (SP_INT_CTRL) */
#define GPC_SET_POINT_CTRL_SP_INT_CTRL_NO_ALLOWED_SP_INT_EN  (1 << 0)  /* Bit 0: no_allowed_set_point interrupt enable */
#define GPC_SET_POINT_CTRL_SP_INT_CTRL_NO_ALLOWED_SP_INT     (1 << 1)  /* Bit 1: no_allowed_set_point interrupt */

/* CPU SP Request (SP_CPU_REQ) */
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU0_SHIFT       (0)   /* Bits 0-4: Setpoint requested by CPU0 */
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU0_MASK        (0xF << GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU0_SHIFT)
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU0(n)          (((n) << GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU0_SHIFT) & GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU0_MASK)
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU1_SHIFT       (4)   /* Bits 4-8: Setpoint requested by CPU1 */
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU1_MASK        (0xF << GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU1_SHIFT)
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU1(n)          (((n) << GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU1_SHIFT) & GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU1_MASK)
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU2_SHIFT       (8)   /* Bits 8-12: Setpoint requested by CPU2 */
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU2_MASK        (0xF << GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU2_SHIFT)
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU2(n)          (((n) << GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU2_SHIFT) & GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU2_MASK)
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU3_SHIFT       (12)  /* Bits 12-16: Setpoint requested by CPU3 */
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU3_MASK        (0xF << GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU3_SHIFT)
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU3(n)          (((n) << GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU3_SHIFT) & GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_REQ_CPU3_MASK)
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU0_SHIFT  (16)  /* Bits 16-20: CPU0 Setpoint accepted by SP controller */
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU0_MASK   (0xF << GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU0_SHIFT)
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU0(n)     (((n) << GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU0_SHIFT) & GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU0_MASK)
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU1_SHIFT  (20)  /* Bits 20-24: CPU1 Setpoint accepted by SP controller */
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU1_MASK   (0xF << GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU1_SHIFT)
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU1(n)     (((n) << GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU1_SHIFT) & GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU1_MASK)
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU2_SHIFT  (24)  /* Bits 24-28: CPU2 Setpoint accepted by SP controller */
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU2_MASK   (0xF << GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU2_SHIFT)
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU2(n)     (((n) << GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU2_SHIFT) & GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU2_MASK)
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU3_SHIFT  (28)  /* Bits 28-32: CPU3 Setpoint accepted by SP controller */
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU3_MASK   (0xF << GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU3_SHIFT)
#define GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU3(n)     (((n) << GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU3_SHIFT) & GPC_SET_POINT_CTRL_SP_CPU_REQ_SP_ACCEPTED_CPU3_MASK)

/* SP System Status (SP_SYS_STAT) */
#define GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_ALLOWED_SHIFT   (0)   /* Bits 0-16: Allowed Setpoints by all current CPU Setpoint requests */
#define GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_ALLOWED_MASK    (0xFFFF << GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_ALLOWED_SHIFT)
#define GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_ALLOWED(n)      (((n) << GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_ALLOWED_SHIFT) & GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_ALLOWED_MASK)
#define GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_TARGET_SHIFT    (16)  /* Bits 16-20: The Setpoint chosen as the target setpoint */
#define GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_TARGET_MASK     (0xF << GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_TARGET_SHIFT)
#define GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_TARGET(n)       (((n) << GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_TARGET_SHIFT) & GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_TARGET_MASK)
#define GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_CURRENT_SHIFT   (20)  /* Bits 20-24: Current Setpoint, only valid when not SP trans busy */
#define GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_CURRENT_MASK    (0xF << GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_CURRENT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_CURRENT(n)      (((n) << GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_CURRENT_SHIFT) & GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_CURRENT_MASK)
#define GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_PREVIOUS_SHIFT  (24)  /* Bits 24-28: Previous Setpoint, only valid when not SP trans busy */
#define GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_PREVIOUS_MASK   (0xF << GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_PREVIOUS_SHIFT)
#define GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_PREVIOUS(n)     (((n) << GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_PREVIOUS_SHIFT) & GPC_SET_POINT_CTRL_SP_SYS_STAT_SYS_SP_PREVIOUS_MASK)

/* SP ROSC Control (SP_ROSC_CTRL) */
#define GPC_SET_POINT_CTRL_SP_ROSC_CTRL_SP_ALLOW_ROSC_OFF_SHIFT  (0)  /* Bits 0-16: Allow shutting off the ROSC */
#define GPC_SET_POINT_CTRL_SP_ROSC_CTRL_SP_ALLOW_ROSC_OFF_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_ROSC_CTRL_SP_ALLOW_ROSC_OFF_SHIFT)
#define GPC_SET_POINT_CTRL_SP_ROSC_CTRL_SP_ALLOW_ROSC_OFF(n)     (((n) << GPC_SET_POINT_CTRL_SP_ROSC_CTRL_SP_ALLOW_ROSC_OFF_SHIFT) & GPC_SET_POINT_CTRL_SP_ROSC_CTRL_SP_ALLOW_ROSC_OFF_MASK)

/* SP0~7 Priority (SP_PRIORITY_0_7) */
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP0_PRIORITY_SHIFT  (0)   /* Bits 0-4: priority of Setpoint 0 */
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP0_PRIORITY_MASK   (0xF << GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP0_PRIORITY_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP0_PRIORITY(n)     (((n) << GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP0_PRIORITY_SHIFT) & GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP0_PRIORITY_MASK)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP1_PRIORITY_SHIFT  (4)   /* Bits 4-8: priority of Setpoint 1 */
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP1_PRIORITY_MASK   (0xF << GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP1_PRIORITY_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP1_PRIORITY(n)     (((n) << GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP1_PRIORITY_SHIFT) & GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP1_PRIORITY_MASK)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP2_PRIORITY_SHIFT  (8)   /* Bits 8-12: priority of Setpoint 2 */
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP2_PRIORITY_MASK   (0xF << GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP2_PRIORITY_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP2_PRIORITY(n)     (((n) << GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP2_PRIORITY_SHIFT) & GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP2_PRIORITY_MASK)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP3_PRIORITY_SHIFT  (12)  /* Bits 12-16: priority of Setpoint 3 */
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP3_PRIORITY_MASK   (0xF << GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP3_PRIORITY_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP3_PRIORITY(n)     (((n) << GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP3_PRIORITY_SHIFT) & GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP3_PRIORITY_MASK)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP4_PRIORITY_SHIFT  (16)  /* Bits 16-20: priority of Setpoint 4 */
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP4_PRIORITY_MASK   (0xF << GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP4_PRIORITY_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP4_PRIORITY(n)     (((n) << GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP4_PRIORITY_SHIFT) & GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP4_PRIORITY_MASK)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP5_PRIORITY_SHIFT  (20)  /* Bits 20-24: priority of Setpoint 5 */
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP5_PRIORITY_MASK   (0xF << GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP5_PRIORITY_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP5_PRIORITY(n)     (((n) << GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP5_PRIORITY_SHIFT) & GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP5_PRIORITY_MASK)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP6_PRIORITY_SHIFT  (24)  /* Bits 24-28: priority of Setpoint 6 */
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP6_PRIORITY_MASK   (0xF << GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP6_PRIORITY_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP6_PRIORITY(n)     (((n) << GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP6_PRIORITY_SHIFT) & GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP6_PRIORITY_MASK)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP7_PRIORITY_SHIFT  (28)  /* Bits 28-32: priority of Setpoint 7 */
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP7_PRIORITY_MASK   (0xF << GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP7_PRIORITY_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP7_PRIORITY(n)     (((n) << GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP7_PRIORITY_SHIFT) & GPC_SET_POINT_CTRL_SP_PRIORITY_0_7_SYS_SP7_PRIORITY_MASK)

/* SP8~15 Priority (SP_PRIORITY_8_15) */
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP8_PRIORITY_SHIFT   (0)   /* Bits 0-4: priority of Setpoint 8 */
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP8_PRIORITY_MASK    (0xF << GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP8_PRIORITY_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP8_PRIORITY(n)      (((n) << GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP8_PRIORITY_SHIFT) & GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP8_PRIORITY_MASK)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP9_PRIORITY_SHIFT   (4)   /* Bits 4-8: priority of Setpoint 9 */
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP9_PRIORITY_MASK    (0xF << GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP9_PRIORITY_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP9_PRIORITY(n)      (((n) << GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP9_PRIORITY_SHIFT) & GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP9_PRIORITY_MASK)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP10_PRIORITY_SHIFT  (8)   /* Bits 8-12: priority of Setpoint 10 */
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP10_PRIORITY_MASK   (0xF << GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP10_PRIORITY_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP10_PRIORITY(n)     (((n) << GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP10_PRIORITY_SHIFT) & GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP10_PRIORITY_MASK)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP11_PRIORITY_SHIFT  (12)  /* Bits 12-16: priority of Setpoint 11 */
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP11_PRIORITY_MASK   (0xF << GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP11_PRIORITY_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP11_PRIORITY(n)     (((n) << GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP11_PRIORITY_SHIFT) & GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP11_PRIORITY_MASK)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP12_PRIORITY_SHIFT  (16)  /* Bits 16-20: priority of Setpoint 12 */
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP12_PRIORITY_MASK   (0xF << GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP12_PRIORITY_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP12_PRIORITY(n)     (((n) << GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP12_PRIORITY_SHIFT) & GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP12_PRIORITY_MASK)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP13_PRIORITY_SHIFT  (20)  /* Bits 20-24: priority of Setpoint 13 */
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP13_PRIORITY_MASK   (0xF << GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP13_PRIORITY_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP13_PRIORITY(n)     (((n) << GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP13_PRIORITY_SHIFT) & GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP13_PRIORITY_MASK)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP14_PRIORITY_SHIFT  (24)  /* Bits 24-28: priority of Setpoint 14 */
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP14_PRIORITY_MASK   (0xF << GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP14_PRIORITY_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP14_PRIORITY(n)     (((n) << GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP14_PRIORITY_SHIFT) & GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP14_PRIORITY_MASK)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP15_PRIORITY_SHIFT  (28)  /* Bits 28-32: priority of Setpoint 15 */
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP15_PRIORITY_MASK   (0xF << GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP15_PRIORITY_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP15_PRIORITY(n)     (((n) << GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP15_PRIORITY_SHIFT) & GPC_SET_POINT_CTRL_SP_PRIORITY_8_15_SYS_SP15_PRIORITY_MASK)

/* SP SSAR save control (SP_SSAR_SAVE_CTRL) */
#define GPC_SET_POINT_CTRL_SP_SSAR_SAVE_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_SSAR_SAVE_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_SSAR_SAVE_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_SSAR_SAVE_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_SSAR_SAVE_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_SSAR_SAVE_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_SSAR_SAVE_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_SSAR_SAVE_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_SSAR_SAVE_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_SSAR_SAVE_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_SSAR_SAVE_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_SSAR_SAVE_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_SSAR_SAVE_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP LPCG off control (SP_LPCG_OFF_CTRL) */
#define GPC_SET_POINT_CTRL_SP_LPCG_OFF_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_LPCG_OFF_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_LPCG_OFF_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_LPCG_OFF_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_LPCG_OFF_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_LPCG_OFF_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_LPCG_OFF_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_LPCG_OFF_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_LPCG_OFF_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_LPCG_OFF_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_LPCG_OFF_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_LPCG_OFF_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_LPCG_OFF_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP group down control (SP_GROUP_DOWN_CTRL) */
#define GPC_SET_POINT_CTRL_SP_GROUP_DOWN_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_GROUP_DOWN_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_GROUP_DOWN_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_GROUP_DOWN_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_GROUP_DOWN_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_GROUP_DOWN_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_GROUP_DOWN_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_GROUP_DOWN_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_GROUP_DOWN_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_GROUP_DOWN_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_GROUP_DOWN_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_GROUP_DOWN_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_GROUP_DOWN_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP root down control (SP_ROOT_DOWN_CTRL) */
#define GPC_SET_POINT_CTRL_SP_ROOT_DOWN_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_ROOT_DOWN_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_ROOT_DOWN_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_ROOT_DOWN_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_ROOT_DOWN_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_ROOT_DOWN_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_ROOT_DOWN_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_ROOT_DOWN_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_ROOT_DOWN_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_ROOT_DOWN_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_ROOT_DOWN_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_ROOT_DOWN_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_ROOT_DOWN_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP PLL off control (SP_PLL_OFF_CTRL) */
#define GPC_SET_POINT_CTRL_SP_PLL_OFF_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_PLL_OFF_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_PLL_OFF_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PLL_OFF_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_PLL_OFF_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_PLL_OFF_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_PLL_OFF_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_PLL_OFF_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_PLL_OFF_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PLL_OFF_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_PLL_OFF_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_PLL_OFF_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_PLL_OFF_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP ISO on control (SP_ISO_ON_CTRL) */
#define GPC_SET_POINT_CTRL_SP_ISO_ON_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_ISO_ON_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_ISO_ON_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_ISO_ON_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_ISO_ON_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_ISO_ON_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_ISO_ON_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_ISO_ON_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_ISO_ON_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_ISO_ON_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_ISO_ON_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_ISO_ON_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_ISO_ON_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP reset early control (SP_RESET_EARLY_CTRL) */
#define GPC_SET_POINT_CTRL_SP_RESET_EARLY_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_RESET_EARLY_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_RESET_EARLY_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_RESET_EARLY_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_RESET_EARLY_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_RESET_EARLY_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_RESET_EARLY_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_RESET_EARLY_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_RESET_EARLY_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_RESET_EARLY_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_RESET_EARLY_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_RESET_EARLY_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_RESET_EARLY_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP power off control (SP_POWER_OFF_CTRL) */
#define GPC_SET_POINT_CTRL_SP_POWER_OFF_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_POWER_OFF_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_POWER_OFF_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_POWER_OFF_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_POWER_OFF_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_POWER_OFF_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_POWER_OFF_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_POWER_OFF_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_POWER_OFF_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_POWER_OFF_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_POWER_OFF_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_POWER_OFF_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_POWER_OFF_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP bias off control (SP_BIAS_OFF_CTRL) */
#define GPC_SET_POINT_CTRL_SP_BIAS_OFF_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_BIAS_OFF_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_BIAS_OFF_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_BIAS_OFF_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_BIAS_OFF_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_BIAS_OFF_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_BIAS_OFF_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_BIAS_OFF_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_BIAS_OFF_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_BIAS_OFF_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_BIAS_OFF_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_BIAS_OFF_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_BIAS_OFF_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP bandgap and PLL_LDO off control (SP_BG_PLDO_OFF_CTRL) */
#define GPC_SET_POINT_CTRL_SP_BG_PLDO_OFF_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_BG_PLDO_OFF_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_BG_PLDO_OFF_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_BG_PLDO_OFF_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_BG_PLDO_OFF_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_BG_PLDO_OFF_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_BG_PLDO_OFF_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_BG_PLDO_OFF_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_BG_PLDO_OFF_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_BG_PLDO_OFF_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_BG_PLDO_OFF_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_BG_PLDO_OFF_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_BG_PLDO_OFF_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP LDO pre control (SP_LDO_PRE_CTRL) */
#define GPC_SET_POINT_CTRL_SP_LDO_PRE_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_LDO_PRE_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_LDO_PRE_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_LDO_PRE_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_LDO_PRE_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_LDO_PRE_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_LDO_PRE_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_LDO_PRE_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_LDO_PRE_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_LDO_PRE_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_LDO_PRE_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_LDO_PRE_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_LDO_PRE_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP DCDC down control (SP_DCDC_DOWN_CTRL) */
#define GPC_SET_POINT_CTRL_SP_DCDC_DOWN_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_DCDC_DOWN_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_DCDC_DOWN_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_DCDC_DOWN_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_DCDC_DOWN_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_DCDC_DOWN_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_DCDC_DOWN_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_DCDC_DOWN_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_DCDC_DOWN_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_DCDC_DOWN_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_DCDC_DOWN_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_DCDC_DOWN_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_DCDC_DOWN_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP DCDC up control (SP_DCDC_UP_CTRL) */
#define GPC_SET_POINT_CTRL_SP_DCDC_UP_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_DCDC_UP_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_DCDC_UP_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_DCDC_UP_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_DCDC_UP_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_DCDC_UP_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_DCDC_UP_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_DCDC_UP_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_DCDC_UP_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_DCDC_UP_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_DCDC_UP_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_DCDC_UP_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_DCDC_UP_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP LDO post control (SP_LDO_POST_CTRL) */
#define GPC_SET_POINT_CTRL_SP_LDO_POST_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_LDO_POST_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_LDO_POST_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_LDO_POST_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_LDO_POST_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_LDO_POST_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_LDO_POST_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_LDO_POST_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_LDO_POST_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_LDO_POST_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_LDO_POST_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_LDO_POST_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_LDO_POST_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP bandgap and PLL_LDO on control (SP_BG_PLDO_ON_CTRL) */
#define GPC_SET_POINT_CTRL_SP_BG_PLDO_ON_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_BG_PLDO_ON_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_BG_PLDO_ON_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_BG_PLDO_ON_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_BG_PLDO_ON_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_BG_PLDO_ON_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_BG_PLDO_ON_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_BG_PLDO_ON_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_BG_PLDO_ON_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_BG_PLDO_ON_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_BG_PLDO_ON_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_BG_PLDO_ON_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_BG_PLDO_ON_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP bias on control (SP_BIAS_ON_CTRL) */
#define GPC_SET_POINT_CTRL_SP_BIAS_ON_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_BIAS_ON_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_BIAS_ON_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_BIAS_ON_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_BIAS_ON_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_BIAS_ON_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_BIAS_ON_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_BIAS_ON_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_BIAS_ON_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_BIAS_ON_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_BIAS_ON_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_BIAS_ON_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_BIAS_ON_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP power on control (SP_POWER_ON_CTRL) */
#define GPC_SET_POINT_CTRL_SP_POWER_ON_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_POWER_ON_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_POWER_ON_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_POWER_ON_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_POWER_ON_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_POWER_ON_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_POWER_ON_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_POWER_ON_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_POWER_ON_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_POWER_ON_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_POWER_ON_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_POWER_ON_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_POWER_ON_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP reset late control (SP_RESET_LATE_CTRL) */
#define GPC_SET_POINT_CTRL_SP_RESET_LATE_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_RESET_LATE_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_RESET_LATE_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_RESET_LATE_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_RESET_LATE_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_RESET_LATE_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_RESET_LATE_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_RESET_LATE_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_RESET_LATE_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_RESET_LATE_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_RESET_LATE_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_RESET_LATE_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_RESET_LATE_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP ISO off control (SP_ISO_OFF_CTRL) */
#define GPC_SET_POINT_CTRL_SP_ISO_OFF_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_ISO_OFF_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_ISO_OFF_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_ISO_OFF_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_ISO_OFF_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_ISO_OFF_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_ISO_OFF_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_ISO_OFF_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_ISO_OFF_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_ISO_OFF_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_ISO_OFF_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_ISO_OFF_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_ISO_OFF_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP PLL on control (SP_PLL_ON_CTRL) */
#define GPC_SET_POINT_CTRL_SP_PLL_ON_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_PLL_ON_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_PLL_ON_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PLL_ON_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_PLL_ON_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_PLL_ON_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_PLL_ON_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_PLL_ON_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_PLL_ON_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_PLL_ON_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_PLL_ON_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_PLL_ON_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_PLL_ON_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP root up control (SP_ROOT_UP_CTRL) */
#define GPC_SET_POINT_CTRL_SP_ROOT_UP_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_ROOT_UP_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_ROOT_UP_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_ROOT_UP_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_ROOT_UP_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_ROOT_UP_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_ROOT_UP_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_ROOT_UP_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_ROOT_UP_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_ROOT_UP_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_ROOT_UP_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_ROOT_UP_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_ROOT_UP_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP group up control (SP_GROUP_UP_CTRL) */
#define GPC_SET_POINT_CTRL_SP_GROUP_UP_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_GROUP_UP_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_GROUP_UP_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_GROUP_UP_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_GROUP_UP_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_GROUP_UP_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_GROUP_UP_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_GROUP_UP_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_GROUP_UP_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_GROUP_UP_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_GROUP_UP_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_GROUP_UP_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_GROUP_UP_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP LPCG on control (SP_LPCG_ON_CTRL) */
#define GPC_SET_POINT_CTRL_SP_LPCG_ON_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_LPCG_ON_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_LPCG_ON_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_LPCG_ON_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_LPCG_ON_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_LPCG_ON_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_LPCG_ON_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_LPCG_ON_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_LPCG_ON_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_LPCG_ON_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_LPCG_ON_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_LPCG_ON_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_LPCG_ON_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* SP SSAR restore control (SP_SSAR_RESTORE_CTRL) */
#define GPC_SET_POINT_CTRL_SP_SSAR_RESTORE_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_SET_POINT_CTRL_SP_SSAR_RESTORE_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_SET_POINT_CTRL_SP_SSAR_RESTORE_CTRL_STEP_CNT_SHIFT)
#define GPC_SET_POINT_CTRL_SP_SSAR_RESTORE_CTRL_STEP_CNT(n)     (((n) << GPC_SET_POINT_CTRL_SP_SSAR_RESTORE_CTRL_STEP_CNT_SHIFT) & GPC_SET_POINT_CTRL_SP_SSAR_RESTORE_CTRL_STEP_CNT_MASK)
#define GPC_SET_POINT_CTRL_SP_SSAR_RESTORE_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_SET_POINT_CTRL_SP_SSAR_RESTORE_CTRL_CNT_MODE_MASK   (0x3 << GPC_SET_POINT_CTRL_SP_SSAR_RESTORE_CTRL_CNT_MODE_SHIFT)
#define GPC_SET_POINT_CTRL_SP_SSAR_RESTORE_CTRL_CNT_MODE(n)     (((n) << GPC_SET_POINT_CTRL_SP_SSAR_RESTORE_CTRL_CNT_MODE_SHIFT) & GPC_SET_POINT_CTRL_SP_SSAR_RESTORE_CTRL_CNT_MODE_MASK)
#define GPC_SET_POINT_CTRL_SP_SSAR_RESTORE_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* GPC_STBY_CTRL Register Offsets ************************************************/
#define IMXRT_GPC_STBY_CTRL_OFFSET                        (0x2800)
#define IMXRT_GPC_STBY_CTRL_STBY_AUTHEN_CTRL_OFFSET       (0x0004)
#define IMXRT_GPC_STBY_CTRL_STBY_MISC_OFFSET              (0x000c)
#define IMXRT_GPC_STBY_CTRL_STBY_LPCG_IN_CTRL_OFFSET      (0x00f0)
#define IMXRT_GPC_STBY_CTRL_STBY_PLL_IN_CTRL_OFFSET       (0x0100)
#define IMXRT_GPC_STBY_CTRL_STBY_BIAS_IN_CTRL_OFFSET      (0x0110)
#define IMXRT_GPC_STBY_CTRL_STBY_PLDO_IN_CTRL_OFFSET      (0x0120)
#define IMXRT_GPC_STBY_CTRL_STBY_BANDGAP_IN_CTRL_OFFSET   (0x0128)
#define IMXRT_GPC_STBY_CTRL_STBY_LDO_IN_CTRL_OFFSET       (0x0130)
#define IMXRT_GPC_STBY_CTRL_STBY_DCDC_IN_CTRL_OFFSET      (0x0140)
#define IMXRT_GPC_STBY_CTRL_STBY_PMIC_IN_CTRL_OFFSET      (0x0150)
#define IMXRT_GPC_STBY_CTRL_STBY_PMIC_OUT_CTRL_OFFSET     (0x0200)
#define IMXRT_GPC_STBY_CTRL_STBY_DCDC_OUT_CTRL_OFFSET     (0x0210)
#define IMXRT_GPC_STBY_CTRL_STBY_LDO_OUT_CTRL_OFFSET      (0x0220)
#define IMXRT_GPC_STBY_CTRL_STBY_BANDGAP_OUT_CTRL_OFFSET  (0x0230)
#define IMXRT_GPC_STBY_CTRL_STBY_PLDO_OUT_CTRL_OFFSET     (0x0238)
#define IMXRT_GPC_STBY_CTRL_STBY_BIAS_OUT_CTRL_OFFSET     (0x0240)
#define IMXRT_GPC_STBY_CTRL_STBY_PLL_OUT_CTRL_OFFSET      (0x0250)
#define IMXRT_GPC_STBY_CTRL_STBY_LPCG_OUT_CTRL_OFFSET     (0x0260)

/* GPC_STBY_CTRL Register Addresses **********************************************/
#define IMXRT_GPC_STBY_CTRL_BASE                   (IMXRT_GPC_BASE + IMXRT_GPC_STBY_CTRL_OFFSET)
#define IMXRT_GPC_STBY_CTRL_STBY_AUTHEN_CTRL       (IMXRT_GPC_STBY_CTRL_BASE + IMXRT_GPC_STBY_CTRL_STBY_AUTHEN_CTRL_OFFSET)
#define IMXRT_GPC_STBY_CTRL_STBY_MISC              (IMXRT_GPC_STBY_CTRL_BASE + IMXRT_GPC_STBY_CTRL_STBY_MISC_OFFSET)
#define IMXRT_GPC_STBY_CTRL_STBY_LPCG_IN_CTRL      (IMXRT_GPC_STBY_CTRL_BASE + IMXRT_GPC_STBY_CTRL_STBY_LPCG_IN_CTRL_OFFSET)
#define IMXRT_GPC_STBY_CTRL_STBY_PLL_IN_CTRL       (IMXRT_GPC_STBY_CTRL_BASE + IMXRT_GPC_STBY_CTRL_STBY_PLL_IN_CTRL_OFFSET)
#define IMXRT_GPC_STBY_CTRL_STBY_BIAS_IN_CTRL      (IMXRT_GPC_STBY_CTRL_BASE + IMXRT_GPC_STBY_CTRL_STBY_BIAS_IN_CTRL_OFFSET)
#define IMXRT_GPC_STBY_CTRL_STBY_PLDO_IN_CTRL      (IMXRT_GPC_STBY_CTRL_BASE + IMXRT_GPC_STBY_CTRL_STBY_PLDO_IN_CTRL_OFFSET)
#define IMXRT_GPC_STBY_CTRL_STBY_BANDGAP_IN_CTRL   (IMXRT_GPC_STBY_CTRL_BASE + IMXRT_GPC_STBY_CTRL_STBY_BANDGAP_IN_CTRL_OFFSET)
#define IMXRT_GPC_STBY_CTRL_STBY_LDO_IN_CTRL       (IMXRT_GPC_STBY_CTRL_BASE + IMXRT_GPC_STBY_CTRL_STBY_LDO_IN_CTRL_OFFSET)
#define IMXRT_GPC_STBY_CTRL_STBY_DCDC_IN_CTRL      (IMXRT_GPC_STBY_CTRL_BASE + IMXRT_GPC_STBY_CTRL_STBY_DCDC_IN_CTRL_OFFSET)
#define IMXRT_GPC_STBY_CTRL_STBY_PMIC_IN_CTRL      (IMXRT_GPC_STBY_CTRL_BASE + IMXRT_GPC_STBY_CTRL_STBY_PMIC_IN_CTRL_OFFSET)
#define IMXRT_GPC_STBY_CTRL_STBY_PMIC_OUT_CTRL     (IMXRT_GPC_STBY_CTRL_BASE + IMXRT_GPC_STBY_CTRL_STBY_PMIC_OUT_CTRL_OFFSET)
#define IMXRT_GPC_STBY_CTRL_STBY_DCDC_OUT_CTRL     (IMXRT_GPC_STBY_CTRL_BASE + IMXRT_GPC_STBY_CTRL_STBY_DCDC_OUT_CTRL_OFFSET)
#define IMXRT_GPC_STBY_CTRL_STBY_LDO_OUT_CTRL      (IMXRT_GPC_STBY_CTRL_BASE + IMXRT_GPC_STBY_CTRL_STBY_LDO_OUT_CTRL_OFFSET)
#define IMXRT_GPC_STBY_CTRL_STBY_BANDGAP_OUT_CTRL  (IMXRT_GPC_STBY_CTRL_BASE + IMXRT_GPC_STBY_CTRL_STBY_BANDGAP_OUT_CTRL_OFFSET)
#define IMXRT_GPC_STBY_CTRL_STBY_PLDO_OUT_CTRL     (IMXRT_GPC_STBY_CTRL_BASE + IMXRT_GPC_STBY_CTRL_STBY_PLDO_OUT_CTRL_OFFSET)
#define IMXRT_GPC_STBY_CTRL_STBY_BIAS_OUT_CTRL     (IMXRT_GPC_STBY_CTRL_BASE + IMXRT_GPC_STBY_CTRL_STBY_BIAS_OUT_CTRL_OFFSET)
#define IMXRT_GPC_STBY_CTRL_STBY_PLL_OUT_CTRL      (IMXRT_GPC_STBY_CTRL_BASE + IMXRT_GPC_STBY_CTRL_STBY_PLL_OUT_CTRL_OFFSET)
#define IMXRT_GPC_STBY_CTRL_STBY_LPCG_OUT_CTRL     (IMXRT_GPC_STBY_CTRL_BASE + IMXRT_GPC_STBY_CTRL_STBY_LPCG_OUT_CTRL_OFFSET)

/* Standby Authentication Control (STBY_AUTHEN_CTRL) */
#define GPC_STBY_CTRL_STBY_AUTHEN_CTRL_LOCK_CFG  (1 << 20)  /* Bit 20: Configuration lock */

/* STBY Misc (STBY_MISC) */
#define GPC_STBY_CTRL_STBY_MISC_FORCE_CPU0_STBY  (1 << 0)  /* Bit 0: Force CPU0 requesting standby mode */
#define GPC_STBY_CTRL_STBY_MISC_FORCE_CPU1_STBY  (1 << 1)  /* Bit 1: Force CPU0 requesting standby mode */
#define GPC_STBY_CTRL_STBY_MISC_FORCE_CPU2_STBY  (1 << 2)  /* Bit 2: Force CPU2 requesting standby mode */
#define GPC_STBY_CTRL_STBY_MISC_FORCE_CPU3_STBY  (1 << 3)  /* Bit 3: Force CPU3 requesting standby mode */

/* STBY lpcg_in control (STBY_LPCG_IN_CTRL) */
#define GPC_STBY_CTRL_STBY_LPCG_IN_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_STBY_CTRL_STBY_LPCG_IN_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_STBY_CTRL_STBY_LPCG_IN_CTRL_STEP_CNT_SHIFT)
#define GPC_STBY_CTRL_STBY_LPCG_IN_CTRL_STEP_CNT(n)     (((n) << GPC_STBY_CTRL_STBY_LPCG_IN_CTRL_STEP_CNT_SHIFT) & GPC_STBY_CTRL_STBY_LPCG_IN_CTRL_STEP_CNT_MASK)
#define GPC_STBY_CTRL_STBY_LPCG_IN_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_STBY_CTRL_STBY_LPCG_IN_CTRL_CNT_MODE_MASK   (0x3 << GPC_STBY_CTRL_STBY_LPCG_IN_CTRL_CNT_MODE_SHIFT)
#define GPC_STBY_CTRL_STBY_LPCG_IN_CTRL_CNT_MODE(n)     (((n) << GPC_STBY_CTRL_STBY_LPCG_IN_CTRL_CNT_MODE_SHIFT) & GPC_STBY_CTRL_STBY_LPCG_IN_CTRL_CNT_MODE_MASK)
#define GPC_STBY_CTRL_STBY_LPCG_IN_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* STBY pll_in control (STBY_PLL_IN_CTRL) */
#define GPC_STBY_CTRL_STBY_PLL_IN_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_STBY_CTRL_STBY_PLL_IN_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_STBY_CTRL_STBY_PLL_IN_CTRL_STEP_CNT_SHIFT)
#define GPC_STBY_CTRL_STBY_PLL_IN_CTRL_STEP_CNT(n)     (((n) << GPC_STBY_CTRL_STBY_PLL_IN_CTRL_STEP_CNT_SHIFT) & GPC_STBY_CTRL_STBY_PLL_IN_CTRL_STEP_CNT_MASK)
#define GPC_STBY_CTRL_STBY_PLL_IN_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_STBY_CTRL_STBY_PLL_IN_CTRL_CNT_MODE_MASK   (0x3 << GPC_STBY_CTRL_STBY_PLL_IN_CTRL_CNT_MODE_SHIFT)
#define GPC_STBY_CTRL_STBY_PLL_IN_CTRL_CNT_MODE(n)     (((n) << GPC_STBY_CTRL_STBY_PLL_IN_CTRL_CNT_MODE_SHIFT) & GPC_STBY_CTRL_STBY_PLL_IN_CTRL_CNT_MODE_MASK)
#define GPC_STBY_CTRL_STBY_PLL_IN_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* STBY bias_in control (STBY_BIAS_IN_CTRL) */
#define GPC_STBY_CTRL_STBY_BIAS_IN_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_STBY_CTRL_STBY_BIAS_IN_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_STBY_CTRL_STBY_BIAS_IN_CTRL_STEP_CNT_SHIFT)
#define GPC_STBY_CTRL_STBY_BIAS_IN_CTRL_STEP_CNT(n)     (((n) << GPC_STBY_CTRL_STBY_BIAS_IN_CTRL_STEP_CNT_SHIFT) & GPC_STBY_CTRL_STBY_BIAS_IN_CTRL_STEP_CNT_MASK)
#define GPC_STBY_CTRL_STBY_BIAS_IN_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_STBY_CTRL_STBY_BIAS_IN_CTRL_CNT_MODE_MASK   (0x3 << GPC_STBY_CTRL_STBY_BIAS_IN_CTRL_CNT_MODE_SHIFT)
#define GPC_STBY_CTRL_STBY_BIAS_IN_CTRL_CNT_MODE(n)     (((n) << GPC_STBY_CTRL_STBY_BIAS_IN_CTRL_CNT_MODE_SHIFT) & GPC_STBY_CTRL_STBY_BIAS_IN_CTRL_CNT_MODE_MASK)
#define GPC_STBY_CTRL_STBY_BIAS_IN_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* STBY pldo_in control (STBY_PLDO_IN_CTRL) */
#define GPC_STBY_CTRL_STBY_PLDO_IN_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_STBY_CTRL_STBY_PLDO_IN_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_STBY_CTRL_STBY_PLDO_IN_CTRL_STEP_CNT_SHIFT)
#define GPC_STBY_CTRL_STBY_PLDO_IN_CTRL_STEP_CNT(n)     (((n) << GPC_STBY_CTRL_STBY_PLDO_IN_CTRL_STEP_CNT_SHIFT) & GPC_STBY_CTRL_STBY_PLDO_IN_CTRL_STEP_CNT_MASK)
#define GPC_STBY_CTRL_STBY_PLDO_IN_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_STBY_CTRL_STBY_PLDO_IN_CTRL_CNT_MODE_MASK   (0x3 << GPC_STBY_CTRL_STBY_PLDO_IN_CTRL_CNT_MODE_SHIFT)
#define GPC_STBY_CTRL_STBY_PLDO_IN_CTRL_CNT_MODE(n)     (((n) << GPC_STBY_CTRL_STBY_PLDO_IN_CTRL_CNT_MODE_SHIFT) & GPC_STBY_CTRL_STBY_PLDO_IN_CTRL_CNT_MODE_MASK)
#define GPC_STBY_CTRL_STBY_PLDO_IN_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* STBY bandgap_in control (STBY_BANDGAP_IN_CTRL) */
#define GPC_STBY_CTRL_STBY_BANDGAP_IN_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_STBY_CTRL_STBY_BANDGAP_IN_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_STBY_CTRL_STBY_BANDGAP_IN_CTRL_STEP_CNT_SHIFT)
#define GPC_STBY_CTRL_STBY_BANDGAP_IN_CTRL_STEP_CNT(n)     (((n) << GPC_STBY_CTRL_STBY_BANDGAP_IN_CTRL_STEP_CNT_SHIFT) & GPC_STBY_CTRL_STBY_BANDGAP_IN_CTRL_STEP_CNT_MASK)
#define GPC_STBY_CTRL_STBY_BANDGAP_IN_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_STBY_CTRL_STBY_BANDGAP_IN_CTRL_CNT_MODE_MASK   (0x3 << GPC_STBY_CTRL_STBY_BANDGAP_IN_CTRL_CNT_MODE_SHIFT)
#define GPC_STBY_CTRL_STBY_BANDGAP_IN_CTRL_CNT_MODE(n)     (((n) << GPC_STBY_CTRL_STBY_BANDGAP_IN_CTRL_CNT_MODE_SHIFT) & GPC_STBY_CTRL_STBY_BANDGAP_IN_CTRL_CNT_MODE_MASK)
#define GPC_STBY_CTRL_STBY_BANDGAP_IN_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* STBY ldo_in control (STBY_LDO_IN_CTRL) */
#define GPC_STBY_CTRL_STBY_LDO_IN_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_STBY_CTRL_STBY_LDO_IN_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_STBY_CTRL_STBY_LDO_IN_CTRL_STEP_CNT_SHIFT)
#define GPC_STBY_CTRL_STBY_LDO_IN_CTRL_STEP_CNT(n)     (((n) << GPC_STBY_CTRL_STBY_LDO_IN_CTRL_STEP_CNT_SHIFT) & GPC_STBY_CTRL_STBY_LDO_IN_CTRL_STEP_CNT_MASK)
#define GPC_STBY_CTRL_STBY_LDO_IN_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_STBY_CTRL_STBY_LDO_IN_CTRL_CNT_MODE_MASK   (0x3 << GPC_STBY_CTRL_STBY_LDO_IN_CTRL_CNT_MODE_SHIFT)
#define GPC_STBY_CTRL_STBY_LDO_IN_CTRL_CNT_MODE(n)     (((n) << GPC_STBY_CTRL_STBY_LDO_IN_CTRL_CNT_MODE_SHIFT) & GPC_STBY_CTRL_STBY_LDO_IN_CTRL_CNT_MODE_MASK)
#define GPC_STBY_CTRL_STBY_LDO_IN_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* STBY dcdc_in control (STBY_DCDC_IN_CTRL) */
#define GPC_STBY_CTRL_STBY_DCDC_IN_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_STBY_CTRL_STBY_DCDC_IN_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_STBY_CTRL_STBY_DCDC_IN_CTRL_STEP_CNT_SHIFT)
#define GPC_STBY_CTRL_STBY_DCDC_IN_CTRL_STEP_CNT(n)     (((n) << GPC_STBY_CTRL_STBY_DCDC_IN_CTRL_STEP_CNT_SHIFT) & GPC_STBY_CTRL_STBY_DCDC_IN_CTRL_STEP_CNT_MASK)
#define GPC_STBY_CTRL_STBY_DCDC_IN_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_STBY_CTRL_STBY_DCDC_IN_CTRL_CNT_MODE_MASK   (0x3 << GPC_STBY_CTRL_STBY_DCDC_IN_CTRL_CNT_MODE_SHIFT)
#define GPC_STBY_CTRL_STBY_DCDC_IN_CTRL_CNT_MODE(n)     (((n) << GPC_STBY_CTRL_STBY_DCDC_IN_CTRL_CNT_MODE_SHIFT) & GPC_STBY_CTRL_STBY_DCDC_IN_CTRL_CNT_MODE_MASK)
#define GPC_STBY_CTRL_STBY_DCDC_IN_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* STBY PMIC in control (STBY_PMIC_IN_CTRL) */
#define GPC_STBY_CTRL_STBY_PMIC_IN_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_STBY_CTRL_STBY_PMIC_IN_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_STBY_CTRL_STBY_PMIC_IN_CTRL_STEP_CNT_SHIFT)
#define GPC_STBY_CTRL_STBY_PMIC_IN_CTRL_STEP_CNT(n)     (((n) << GPC_STBY_CTRL_STBY_PMIC_IN_CTRL_STEP_CNT_SHIFT) & GPC_STBY_CTRL_STBY_PMIC_IN_CTRL_STEP_CNT_MASK)
#define GPC_STBY_CTRL_STBY_PMIC_IN_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_STBY_CTRL_STBY_PMIC_IN_CTRL_CNT_MODE_MASK   (0x3 << GPC_STBY_CTRL_STBY_PMIC_IN_CTRL_CNT_MODE_SHIFT)
#define GPC_STBY_CTRL_STBY_PMIC_IN_CTRL_CNT_MODE(n)     (((n) << GPC_STBY_CTRL_STBY_PMIC_IN_CTRL_CNT_MODE_SHIFT) & GPC_STBY_CTRL_STBY_PMIC_IN_CTRL_CNT_MODE_MASK)
#define GPC_STBY_CTRL_STBY_PMIC_IN_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* STBY PMIC out control (STBY_PMIC_OUT_CTRL) */
#define GPC_STBY_CTRL_STBY_PMIC_OUT_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_STBY_CTRL_STBY_PMIC_OUT_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_STBY_CTRL_STBY_PMIC_OUT_CTRL_STEP_CNT_SHIFT)
#define GPC_STBY_CTRL_STBY_PMIC_OUT_CTRL_STEP_CNT(n)     (((n) << GPC_STBY_CTRL_STBY_PMIC_OUT_CTRL_STEP_CNT_SHIFT) & GPC_STBY_CTRL_STBY_PMIC_OUT_CTRL_STEP_CNT_MASK)
#define GPC_STBY_CTRL_STBY_PMIC_OUT_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_STBY_CTRL_STBY_PMIC_OUT_CTRL_CNT_MODE_MASK   (0x3 << GPC_STBY_CTRL_STBY_PMIC_OUT_CTRL_CNT_MODE_SHIFT)
#define GPC_STBY_CTRL_STBY_PMIC_OUT_CTRL_CNT_MODE(n)     (((n) << GPC_STBY_CTRL_STBY_PMIC_OUT_CTRL_CNT_MODE_SHIFT) & GPC_STBY_CTRL_STBY_PMIC_OUT_CTRL_CNT_MODE_MASK)
#define GPC_STBY_CTRL_STBY_PMIC_OUT_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* STBY DCDC out control (STBY_DCDC_OUT_CTRL) */
#define GPC_STBY_CTRL_STBY_DCDC_OUT_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_STBY_CTRL_STBY_DCDC_OUT_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_STBY_CTRL_STBY_DCDC_OUT_CTRL_STEP_CNT_SHIFT)
#define GPC_STBY_CTRL_STBY_DCDC_OUT_CTRL_STEP_CNT(n)     (((n) << GPC_STBY_CTRL_STBY_DCDC_OUT_CTRL_STEP_CNT_SHIFT) & GPC_STBY_CTRL_STBY_DCDC_OUT_CTRL_STEP_CNT_MASK)
#define GPC_STBY_CTRL_STBY_DCDC_OUT_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_STBY_CTRL_STBY_DCDC_OUT_CTRL_CNT_MODE_MASK   (0x3 << GPC_STBY_CTRL_STBY_DCDC_OUT_CTRL_CNT_MODE_SHIFT)
#define GPC_STBY_CTRL_STBY_DCDC_OUT_CTRL_CNT_MODE(n)     (((n) << GPC_STBY_CTRL_STBY_DCDC_OUT_CTRL_CNT_MODE_SHIFT) & GPC_STBY_CTRL_STBY_DCDC_OUT_CTRL_CNT_MODE_MASK)
#define GPC_STBY_CTRL_STBY_DCDC_OUT_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* STBY LDO out control (STBY_LDO_OUT_CTRL) */
#define GPC_STBY_CTRL_STBY_LDO_OUT_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_STBY_CTRL_STBY_LDO_OUT_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_STBY_CTRL_STBY_LDO_OUT_CTRL_STEP_CNT_SHIFT)
#define GPC_STBY_CTRL_STBY_LDO_OUT_CTRL_STEP_CNT(n)     (((n) << GPC_STBY_CTRL_STBY_LDO_OUT_CTRL_STEP_CNT_SHIFT) & GPC_STBY_CTRL_STBY_LDO_OUT_CTRL_STEP_CNT_MASK)
#define GPC_STBY_CTRL_STBY_LDO_OUT_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_STBY_CTRL_STBY_LDO_OUT_CTRL_CNT_MODE_MASK   (0x3 << GPC_STBY_CTRL_STBY_LDO_OUT_CTRL_CNT_MODE_SHIFT)
#define GPC_STBY_CTRL_STBY_LDO_OUT_CTRL_CNT_MODE(n)     (((n) << GPC_STBY_CTRL_STBY_LDO_OUT_CTRL_CNT_MODE_SHIFT) & GPC_STBY_CTRL_STBY_LDO_OUT_CTRL_CNT_MODE_MASK)
#define GPC_STBY_CTRL_STBY_LDO_OUT_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* STBY bandgap out control (STBY_BANDGAP_OUT_CTRL) */
#define GPC_STBY_CTRL_STBY_BANDGAP_OUT_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_STBY_CTRL_STBY_BANDGAP_OUT_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_STBY_CTRL_STBY_BANDGAP_OUT_CTRL_STEP_CNT_SHIFT)
#define GPC_STBY_CTRL_STBY_BANDGAP_OUT_CTRL_STEP_CNT(n)     (((n) << GPC_STBY_CTRL_STBY_BANDGAP_OUT_CTRL_STEP_CNT_SHIFT) & GPC_STBY_CTRL_STBY_BANDGAP_OUT_CTRL_STEP_CNT_MASK)
#define GPC_STBY_CTRL_STBY_BANDGAP_OUT_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_STBY_CTRL_STBY_BANDGAP_OUT_CTRL_CNT_MODE_MASK   (0x3 << GPC_STBY_CTRL_STBY_BANDGAP_OUT_CTRL_CNT_MODE_SHIFT)
#define GPC_STBY_CTRL_STBY_BANDGAP_OUT_CTRL_CNT_MODE(n)     (((n) << GPC_STBY_CTRL_STBY_BANDGAP_OUT_CTRL_CNT_MODE_SHIFT) & GPC_STBY_CTRL_STBY_BANDGAP_OUT_CTRL_CNT_MODE_MASK)
#define GPC_STBY_CTRL_STBY_BANDGAP_OUT_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* STBY pldo out control (STBY_PLDO_OUT_CTRL) */
#define GPC_STBY_CTRL_STBY_PLDO_OUT_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_STBY_CTRL_STBY_PLDO_OUT_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_STBY_CTRL_STBY_PLDO_OUT_CTRL_STEP_CNT_SHIFT)
#define GPC_STBY_CTRL_STBY_PLDO_OUT_CTRL_STEP_CNT(n)     (((n) << GPC_STBY_CTRL_STBY_PLDO_OUT_CTRL_STEP_CNT_SHIFT) & GPC_STBY_CTRL_STBY_PLDO_OUT_CTRL_STEP_CNT_MASK)
#define GPC_STBY_CTRL_STBY_PLDO_OUT_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_STBY_CTRL_STBY_PLDO_OUT_CTRL_CNT_MODE_MASK   (0x3 << GPC_STBY_CTRL_STBY_PLDO_OUT_CTRL_CNT_MODE_SHIFT)
#define GPC_STBY_CTRL_STBY_PLDO_OUT_CTRL_CNT_MODE(n)     (((n) << GPC_STBY_CTRL_STBY_PLDO_OUT_CTRL_CNT_MODE_SHIFT) & GPC_STBY_CTRL_STBY_PLDO_OUT_CTRL_CNT_MODE_MASK)
#define GPC_STBY_CTRL_STBY_PLDO_OUT_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* STBY bias out control (STBY_BIAS_OUT_CTRL) */
#define GPC_STBY_CTRL_STBY_BIAS_OUT_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_STBY_CTRL_STBY_BIAS_OUT_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_STBY_CTRL_STBY_BIAS_OUT_CTRL_STEP_CNT_SHIFT)
#define GPC_STBY_CTRL_STBY_BIAS_OUT_CTRL_STEP_CNT(n)     (((n) << GPC_STBY_CTRL_STBY_BIAS_OUT_CTRL_STEP_CNT_SHIFT) & GPC_STBY_CTRL_STBY_BIAS_OUT_CTRL_STEP_CNT_MASK)
#define GPC_STBY_CTRL_STBY_BIAS_OUT_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_STBY_CTRL_STBY_BIAS_OUT_CTRL_CNT_MODE_MASK   (0x3 << GPC_STBY_CTRL_STBY_BIAS_OUT_CTRL_CNT_MODE_SHIFT)
#define GPC_STBY_CTRL_STBY_BIAS_OUT_CTRL_CNT_MODE(n)     (((n) << GPC_STBY_CTRL_STBY_BIAS_OUT_CTRL_CNT_MODE_SHIFT) & GPC_STBY_CTRL_STBY_BIAS_OUT_CTRL_CNT_MODE_MASK)
#define GPC_STBY_CTRL_STBY_BIAS_OUT_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* STBY PLL out control (STBY_PLL_OUT_CTRL) */
#define GPC_STBY_CTRL_STBY_PLL_OUT_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_STBY_CTRL_STBY_PLL_OUT_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_STBY_CTRL_STBY_PLL_OUT_CTRL_STEP_CNT_SHIFT)
#define GPC_STBY_CTRL_STBY_PLL_OUT_CTRL_STEP_CNT(n)     (((n) << GPC_STBY_CTRL_STBY_PLL_OUT_CTRL_STEP_CNT_SHIFT) & GPC_STBY_CTRL_STBY_PLL_OUT_CTRL_STEP_CNT_MASK)
#define GPC_STBY_CTRL_STBY_PLL_OUT_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_STBY_CTRL_STBY_PLL_OUT_CTRL_CNT_MODE_MASK   (0x3 << GPC_STBY_CTRL_STBY_PLL_OUT_CTRL_CNT_MODE_SHIFT)
#define GPC_STBY_CTRL_STBY_PLL_OUT_CTRL_CNT_MODE(n)     (((n) << GPC_STBY_CTRL_STBY_PLL_OUT_CTRL_CNT_MODE_SHIFT) & GPC_STBY_CTRL_STBY_PLL_OUT_CTRL_CNT_MODE_MASK)
#define GPC_STBY_CTRL_STBY_PLL_OUT_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

/* STBY LPCG out control (STBY_LPCG_OUT_CTRL) */
#define GPC_STBY_CTRL_STBY_LPCG_OUT_CTRL_STEP_CNT_SHIFT  (0)        /* Bits 0-16: Step count, useage is depending on CNT_MODE */
#define GPC_STBY_CTRL_STBY_LPCG_OUT_CTRL_STEP_CNT_MASK   (0xFFFF << GPC_STBY_CTRL_STBY_LPCG_OUT_CTRL_STEP_CNT_SHIFT)
#define GPC_STBY_CTRL_STBY_LPCG_OUT_CTRL_STEP_CNT(n)     (((n) << GPC_STBY_CTRL_STBY_LPCG_OUT_CTRL_STEP_CNT_SHIFT) & GPC_STBY_CTRL_STBY_LPCG_OUT_CTRL_STEP_CNT_MASK)
#define GPC_STBY_CTRL_STBY_LPCG_OUT_CTRL_CNT_MODE_SHIFT  (28)       /* Bits 28-30: Count mode */
#define GPC_STBY_CTRL_STBY_LPCG_OUT_CTRL_CNT_MODE_MASK   (0x3 << GPC_STBY_CTRL_STBY_LPCG_OUT_CTRL_CNT_MODE_SHIFT)
#define GPC_STBY_CTRL_STBY_LPCG_OUT_CTRL_CNT_MODE(n)     (((n) << GPC_STBY_CTRL_STBY_LPCG_OUT_CTRL_CNT_MODE_SHIFT) & GPC_STBY_CTRL_STBY_LPCG_OUT_CTRL_CNT_MODE_MASK)
#define GPC_STBY_CTRL_STBY_LPCG_OUT_CTRL_DISABLE         (1 << 31)  /* Bit 31: Disable this step */

#endif /* __ARCH_ARM_SRC_IMXRT_HARDWARE_RT117X_IMXRT117X_GPC_H */
