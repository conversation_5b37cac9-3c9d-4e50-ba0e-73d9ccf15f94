/****************************************************************************
 * arch/arm/src/imxrt/hardware/rt106x/imxrt106x_xbar.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/* XBAR Defines for IMXRT1060 */

#define IMXRT_XBAR_SEL_MASKS {0x7f, 0x3f, 0x3f}  /* The select masks A, B2, B3 */

/* XBARA1 Mux inputs (I values) *********************************************/

#define IMXRT_XBARA1_IN_LOGIC_LOW                        IMXRT_XBARA1(XBAR_INPUT, 0)    /* LOGIC_LOW output assigned to XBARA1_IN0 input. */
#define IMXRT_XBARA1_IN_LOGIC_HIGH                       IMXRT_XBARA1(XBAR_INPUT, 1)    /* LOGIC_HIGH output assigned to XBARA1_IN1 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IN02                  IMXRT_XBARA1(XBAR_INPUT, 2)    /* IOMUX_XBAR_IN02 output assigned to XBARA1_IN2 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IN03                  IMXRT_XBARA1(XBAR_INPUT, 3)    /* IOMUX_XBAR_IN03 output assigned to XBARA1_IN3 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IO04                  IMXRT_XBARA1(XBAR_INPUT, 4)    /* IOMUX_XBAR_INOUT04 output assigned to XBARA1_IN4 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IO05                  IMXRT_XBARA1(XBAR_INPUT, 5)    /* IOMUX_XBAR_INOUT05 output assigned to XBARA1_IN5 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IO06                  IMXRT_XBARA1(XBAR_INPUT, 6)    /* IOMUX_XBAR_INOUT06 output assigned to XBARA1_IN6 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IO07                  IMXRT_XBARA1(XBAR_INPUT, 7)    /* IOMUX_XBAR_INOUT07 output assigned to XBARA1_IN7 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IO08                  IMXRT_XBARA1(XBAR_INPUT, 8)    /* IOMUX_XBAR_INOUT08 output assigned to XBARA1_IN8 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IO09                  IMXRT_XBARA1(XBAR_INPUT, 9)    /* IOMUX_XBAR_INOUT09 output assigned to XBARA1_IN9 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IO10                  IMXRT_XBARA1(XBAR_INPUT, 10)   /* IOMUX_XBAR_INOUT10 output assigned to XBARA1_IN10 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IO11                  IMXRT_XBARA1(XBAR_INPUT, 11)   /* IOMUX_XBAR_INOUT11 output assigned to XBARA1_IN11 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IO12                  IMXRT_XBARA1(XBAR_INPUT, 12)   /* IOMUX_XBAR_INOUT12 output assigned to XBARA1_IN12 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IO13                  IMXRT_XBARA1(XBAR_INPUT, 13)   /* IOMUX_XBAR_INOUT13 output assigned to XBARA1_IN13 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IO14                  IMXRT_XBARA1(XBAR_INPUT, 14)   /* IOMUX_XBAR_INOUT14 output assigned to XBARA1_IN14 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IO15                  IMXRT_XBARA1(XBAR_INPUT, 15)   /* IOMUX_XBAR_INOUT15 output assigned to XBARA1_IN15 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IO16                  IMXRT_XBARA1(XBAR_INPUT, 16)   /* IOMUX_XBAR_INOUT16 output assigned to XBARA1_IN16 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IO17                  IMXRT_XBARA1(XBAR_INPUT, 17)   /* IOMUX_XBAR_INOUT17 output assigned to XBARA1_IN17 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IO18                  IMXRT_XBARA1(XBAR_INPUT, 18)   /* IOMUX_XBAR_INOUT18 output assigned to XBARA1_IN18 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IO19                  IMXRT_XBARA1(XBAR_INPUT, 19)   /* IOMUX_XBAR_INOUT19 output assigned to XBARA1_IN19 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IN20                  IMXRT_XBARA1(XBAR_INPUT, 20)   /* IOMUX_XBAR_IN20 output assigned to XBARA1_IN20 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IN21                  IMXRT_XBARA1(XBAR_INPUT, 21)   /* IOMUX_XBAR_IN21 output assigned to XBARA1_IN21 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IN22                  IMXRT_XBARA1(XBAR_INPUT, 22)   /* IOMUX_XBAR_IN22 output assigned to XBARA1_IN22 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IN23                  IMXRT_XBARA1(XBAR_INPUT, 23)   /* IOMUX_XBAR_IN23 output assigned to XBARA1_IN23 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IN24                  IMXRT_XBARA1(XBAR_INPUT, 24)   /* IOMUX_XBAR_IN24 output assigned to XBARA1_IN24 input. */
#define IMXRT_XBARA1_IN_IOMUX_XBAR_IN25                  IMXRT_XBARA1(XBAR_INPUT, 25)   /* IOMUX_XBAR_IN25 output assigned to XBARA1_IN25 input. */
#define IMXRT_XBARA1_IN_ACMP1_OUT                        IMXRT_XBARA1(XBAR_INPUT, 26)   /* ACMP1_OUT output assigned to XBARA1_IN26 input. */
#define IMXRT_XBARA1_IN_ACMP2_OUT                        IMXRT_XBARA1(XBAR_INPUT, 27)   /* ACMP2_OUT output assigned to XBARA1_IN27 input. */
#define IMXRT_XBARA1_IN_ACMP3_OUT                        IMXRT_XBARA1(XBAR_INPUT, 28)   /* ACMP3_OUT output assigned to XBARA1_IN28 input. */
#define IMXRT_XBARA1_IN_ACMP4_OUT                        IMXRT_XBARA1(XBAR_INPUT, 29)   /* ACMP4_OUT output assigned to XBARA1_IN29 input. */
#define IMXRT_XBARA1_IN_RESERVED30                       IMXRT_XBARA1(XBAR_INPUT, 30)   /* XBARA1_IN30 input is reserved. */
#define IMXRT_XBARA1_IN_RESERVED31                       IMXRT_XBARA1(XBAR_INPUT, 31)   /* XBARA1_IN31 input is reserved. */
#define IMXRT_XBARA1_IN_QTIMER3_TMR0_OUT                 IMXRT_XBARA1(XBAR_INPUT, 32)   /* QTIMER3_TMR0_OUTPUT output assigned to XBARA1_IN32 input. */
#define IMXRT_XBARA1_IN_QTIMER3_TMR1_OUT                 IMXRT_XBARA1(XBAR_INPUT, 33)   /* QTIMER3_TMR1_OUTPUT output assigned to XBARA1_IN33 input. */
#define IMXRT_XBARA1_IN_QTIMER3_TMR2_OUT                 IMXRT_XBARA1(XBAR_INPUT, 34)   /* QTIMER3_TMR2_OUTPUT output assigned to XBARA1_IN34 input. */
#define IMXRT_XBARA1_IN_QTIMER3_TMR3_OUT                 IMXRT_XBARA1(XBAR_INPUT, 35)   /* QTIMER3_TMR3_OUTPUT output assigned to XBARA1_IN35 input. */
#define IMXRT_XBARA1_IN_QTIMER4_TMR0_OUT                 IMXRT_XBARA1(XBAR_INPUT, 36)   /* QTIMER4_TMR0_OUTPUT output assigned to XBARA1_IN36 input. */
#define IMXRT_XBARA1_IN_QTIMER4_TMR1_OUT                 IMXRT_XBARA1(XBAR_INPUT, 37)   /* QTIMER4_TMR1_OUTPUT output assigned to XBARA1_IN37 input. */
#define IMXRT_XBARA1_IN_QTIMER4_TMR2_OUT                 IMXRT_XBARA1(XBAR_INPUT, 38)   /* QTIMER4_TMR2_OUTPUT output assigned to XBARA1_IN38 input. */
#define IMXRT_XBARA1_IN_QTIMER4_TMR3_OUT                 IMXRT_XBARA1(XBAR_INPUT, 39)   /* QTIMER4_TMR3_OUTPUT output assigned to XBARA1_IN39 input. */
#define IMXRT_XBARA1_IN_FLEXPWM1_PWM1_OUT_TRIG01         IMXRT_XBARA1(XBAR_INPUT, 40)   /* FLEXPWM1_PWM1_OUT_TRIG0_1 output assigned to XBARA1_IN40 input. */
#define IMXRT_XBARA1_IN_FLEXPWM1_PWM2_OUT_TRIG01         IMXRT_XBARA1(XBAR_INPUT, 41)   /* FLEXPWM1_PWM2_OUT_TRIG0_1 output assigned to XBARA1_IN41 input. */
#define IMXRT_XBARA1_IN_FLEXPWM1_PWM3_OUT_TRIG01         IMXRT_XBARA1(XBAR_INPUT, 42)   /* FLEXPWM1_PWM3_OUT_TRIG0_1 output assigned to XBARA1_IN42 input. */
#define IMXRT_XBARA1_IN_FLEXPWM1_PWM4_OUT_TRIG01         IMXRT_XBARA1(XBAR_INPUT, 43)   /* FLEXPWM1_PWM4_OUT_TRIG0_1 output assigned to XBARA1_IN43 input. */
#define IMXRT_XBARA1_IN_FLEXPWM2_PWM1_OUT_TRIG01         IMXRT_XBARA1(XBAR_INPUT, 44)   /* FLEXPWM2_PWM1_OUT_TRIG0_1 output assigned to XBARA1_IN44 input. */
#define IMXRT_XBARA1_IN_FLEXPWM2_PWM2_OUT_TRIG01         IMXRT_XBARA1(XBAR_INPUT, 45)   /* FLEXPWM2_PWM2_OUT_TRIG0_1 output assigned to XBARA1_IN45 input. */
#define IMXRT_XBARA1_IN_FLEXPWM2_PWM3_OUT_TRIG01         IMXRT_XBARA1(XBAR_INPUT, 46)   /* FLEXPWM2_PWM3_OUT_TRIG0_1 output assigned to XBARA1_IN46 input. */
#define IMXRT_XBARA1_IN_FLEXPWM2_PWM4_OUT_TRIG01         IMXRT_XBARA1(XBAR_INPUT, 47)   /* FLEXPWM2_PWM4_OUT_TRIG0_1 output assigned to XBARA1_IN47 input. */
#define IMXRT_XBARA1_IN_FLEXPWM3_PWM1_OUT_TRIG01         IMXRT_XBARA1(XBAR_INPUT, 48)   /* FLEXPWM3_PWM1_OUT_TRIG0_1 output assigned to XBARA1_IN48 input. */
#define IMXRT_XBARA1_IN_FLEXPWM3_PWM2_OUT_TRIG01         IMXRT_XBARA1(XBAR_INPUT, 49)   /* FLEXPWM3_PWM2_OUT_TRIG0_1 output assigned to XBARA1_IN49 input. */
#define IMXRT_XBARA1_IN_FLEXPWM3_PWM3_OUT_TRIG01         IMXRT_XBARA1(XBAR_INPUT, 50)   /* FLEXPWM3_PWM3_OUT_TRIG0_1 output assigned to XBARA1_IN50 input. */
#define IMXRT_XBARA1_IN_FLEXPWM3_PWM4_OUT_TRIG01         IMXRT_XBARA1(XBAR_INPUT, 51)   /* FLEXPWM3_PWM4_OUT_TRIG0_1 output assigned to XBARA1_IN51 input. */
#define IMXRT_XBARA1_IN_FLEXPWM4_PWM1_OUT_TRIG01         IMXRT_XBARA1(XBAR_INPUT, 52)   /* FLEXPWM4_PWM1_OUT_TRIG0_1 output assigned to XBARA1_IN52 input. */
#define IMXRT_XBARA1_IN_FLEXPWM4_PWM2_OUT_TRIG01         IMXRT_XBARA1(XBAR_INPUT, 53)   /* FLEXPWM4_PWM2_OUT_TRIG0_1 output assigned to XBARA1_IN53 input. */
#define IMXRT_XBARA1_IN_FLEXPWM4_PWM3_OUT_TRIG01         IMXRT_XBARA1(XBAR_INPUT, 54)   /* FLEXPWM4_PWM3_OUT_TRIG0_1 output assigned to XBARA1_IN54 input. */
#define IMXRT_XBARA1_IN_FLEXPWM4_PWM4_OUT_TRIG01         IMXRT_XBARA1(XBAR_INPUT, 55)   /* FLEXPWM4_PWM4_OUT_TRIG0_1 output assigned to XBARA1_IN55 input. */
#define IMXRT_XBARA1_IN_PIT_TRIGGER0                     IMXRT_XBARA1(XBAR_INPUT, 56)   /* PIT_TRIGGER0 output assigned to XBARA1_IN56 input. */
#define IMXRT_XBARA1_IN_PIT_TRIGGER1                     IMXRT_XBARA1(XBAR_INPUT, 57)   /* PIT_TRIGGER1 output assigned to XBARA1_IN57 input. */
#define IMXRT_XBARA1_IN_PIT_TRIGGER2                     IMXRT_XBARA1(XBAR_INPUT, 58)   /* PIT_TRIGGER2 output assigned to XBARA1_IN58 input. */
#define IMXRT_XBARA1_IN_PIT_TRIGGER3                     IMXRT_XBARA1(XBAR_INPUT, 59)   /* PIT_TRIGGER3 output assigned to XBARA1_IN59 input. */
#define IMXRT_XBARA1_IN_ENC1_POS_MATCH                   IMXRT_XBARA1(XBAR_INPUT, 60)   /* ENC1_POS_MATCH output assigned to XBARA1_IN60 input. */
#define IMXRT_XBARA1_IN_ENC2_POS_MATCH                   IMXRT_XBARA1(XBAR_INPUT, 61)   /* ENC2_POS_MATCH output assigned to XBARA1_IN61 input. */
#define IMXRT_XBARA1_IN_ENC3_POS_MATCH                   IMXRT_XBARA1(XBAR_INPUT, 62)   /* ENC3_POS_MATCH output assigned to XBARA1_IN62 input. */
#define IMXRT_XBARA1_IN_ENC4_POS_MATCH                   IMXRT_XBARA1(XBAR_INPUT, 63)   /* ENC4_POS_MATCH output assigned to XBARA1_IN63 input. */
#define IMXRT_XBARA1_IN_DMA_DONE0                        IMXRT_XBARA1(XBAR_INPUT, 64)   /* DMA_DONE0 output assigned to XBARA1_IN64 input. */
#define IMXRT_XBARA1_IN_DMA_DONE1                        IMXRT_XBARA1(XBAR_INPUT, 65)   /* DMA_DONE1 output assigned to XBARA1_IN65 input. */
#define IMXRT_XBARA1_IN_DMA_DONE2                        IMXRT_XBARA1(XBAR_INPUT, 66)   /* DMA_DONE2 output assigned to XBARA1_IN66 input. */
#define IMXRT_XBARA1_IN_DMA_DONE3                        IMXRT_XBARA1(XBAR_INPUT, 67)   /* DMA_DONE3 output assigned to XBARA1_IN67 input. */
#define IMXRT_XBARA1_IN_DMA_DONE4                        IMXRT_XBARA1(XBAR_INPUT, 68)   /* DMA_DONE4 output assigned to XBARA1_IN68 input. */
#define IMXRT_XBARA1_IN_DMA_DONE5                        IMXRT_XBARA1(XBAR_INPUT, 69)   /* DMA_DONE5 output assigned to XBARA1_IN69 input. */
#define IMXRT_XBARA1_IN_DMA_DONE6                        IMXRT_XBARA1(XBAR_INPUT, 70)   /* DMA_DONE6 output assigned to XBARA1_IN70 input. */
#define IMXRT_XBARA1_IN_DMA_DONE7                        IMXRT_XBARA1(XBAR_INPUT, 71)   /* DMA_DONE7 output assigned to XBARA1_IN71 input. */
#define IMXRT_XBARA1_IN_AOI1_OUT0                        IMXRT_XBARA1(XBAR_INPUT, 72)   /* AOI1_OUT0 output assigned to XBARA1_IN72 input. */
#define IMXRT_XBARA1_IN_AOI1_OUT1                        IMXRT_XBARA1(XBAR_INPUT, 73)   /* AOI1_OUT1 output assigned to XBARA1_IN73 input. */
#define IMXRT_XBARA1_IN_AOI1_OUT2                        IMXRT_XBARA1(XBAR_INPUT, 74)   /* AOI1_OUT2 output assigned to XBARA1_IN74 input. */
#define IMXRT_XBARA1_IN_AOI1_OUT3                        IMXRT_XBARA1(XBAR_INPUT, 75)   /* AOI1_OUT3 output assigned to XBARA1_IN75 input. */
#define IMXRT_XBARA1_IN_AOI2_OUT0                        IMXRT_XBARA1(XBAR_INPUT, 76)   /* AOI2_OUT0 output assigned to XBARA1_IN76 input. */
#define IMXRT_XBARA1_IN_AOI2_OUT1                        IMXRT_XBARA1(XBAR_INPUT, 77)   /* AOI2_OUT1 output assigned to XBARA1_IN77 input. */
#define IMXRT_XBARA1_IN_AOI2_OUT2                        IMXRT_XBARA1(XBAR_INPUT, 78)   /* AOI2_OUT2 output assigned to XBARA1_IN78 input. */
#define IMXRT_XBARA1_IN_AOI2_OUT3                        IMXRT_XBARA1(XBAR_INPUT, 79)   /* AOI2_OUT3 output assigned to XBARA1_IN79 input. */
#define IMXRT_XBARA1_IN_ADC_ETC_XBAR0_COCO0              IMXRT_XBARA1(XBAR_INPUT, 80)   /* ADC_ETC_XBAR0_COCO0 output assigned to XBARA1_IN80 input. */
#define IMXRT_XBARA1_IN_ADC_ETC_XBAR0_COCO1              IMXRT_XBARA1(XBAR_INPUT, 81)   /* ADC_ETC_XBAR0_COCO1 output assigned to XBARA1_IN81 input. */
#define IMXRT_XBARA1_IN_ADC_ETC_XBAR0_COCO2              IMXRT_XBARA1(XBAR_INPUT, 82)   /* ADC_ETC_XBAR0_COCO2 output assigned to XBARA1_IN82 input. */
#define IMXRT_XBARA1_IN_ADC_ETC_XBAR0_COCO3              IMXRT_XBARA1(XBAR_INPUT, 83)   /* ADC_ETC_XBAR0_COCO3 output assigned to XBARA1_IN83 input. */
#define IMXRT_XBARA1_IN_ADC_ETC_XBAR1_COCO0              IMXRT_XBARA1(XBAR_INPUT, 84)   /* ADC_ETC_XBAR1_COCO0 output assigned to XBARA1_IN84 input. */
#define IMXRT_XBARA1_IN_ADC_ETC_XBAR1_COCO1              IMXRT_XBARA1(XBAR_INPUT, 85)   /* ADC_ETC_XBAR1_COCO1 output assigned to XBARA1_IN85 input. */
#define IMXRT_XBARA1_IN_ADC_ETC_XBAR1_COCO2              IMXRT_XBARA1(XBAR_INPUT, 86)   /* ADC_ETC_XBAR1_COCO2 output assigned to XBARA1_IN86 input. */
#define IMXRT_XBARA1_IN_ADC_ETC_XBAR1_COCO3              IMXRT_XBARA1(XBAR_INPUT, 87)   /* ADC_ETC_XBAR1_COCO3 output assigned to XBARA1_IN87 input. */

/* XBARA1 Mux Output  (M Muxes) *********************************************/

#define IMXRT_XBARA1_OUT_DMA_CH_MUX_REQ30_SEL_OFFSET     IMXRT_XBARA1(XBAR_OUTPUT, 0)    /* XBARA1_OUT0 output assigned to DMA_CH_MUX_REQ30 */
#define IMXRT_XBARA1_OUT_DMA_CH_MUX_REQ31_SEL_OFFSET     IMXRT_XBARA1(XBAR_OUTPUT, 1)    /* XBARA1_OUT1 output assigned to DMA_CH_MUX_REQ31 */
#define IMXRT_XBARA1_OUT_DMA_CH_MUX_REQ94_SEL_OFFSET     IMXRT_XBARA1(XBAR_OUTPUT, 2)    /* XBARA1_OUT2 output assigned to DMA_CH_MUX_REQ94 */
#define IMXRT_XBARA1_OUT_DMA_CH_MUX_REQ95_SEL_OFFSET     IMXRT_XBARA1(XBAR_OUTPUT, 3)    /* XBARA1_OUT3 output assigned to DMA_CH_MUX_REQ95 */
#define IMXRT_XBARA1_OUT_IOMUX_XBAR_IO04_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 4)    /* XBARA1_OUT4 output assigned to IOMUX_XBAR_INOUT04 */
#define IMXRT_XBARA1_OUT_IOMUX_XBAR_IO05_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 5)    /* XBARA1_OUT5 output assigned to IOMUX_XBAR_INOUT05 */
#define IMXRT_XBARA1_OUT_IOMUX_XBAR_IO06_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 6)    /* XBARA1_OUT6 output assigned to IOMUX_XBAR_INOUT06 */
#define IMXRT_XBARA1_OUT_IOMUX_XBAR_IO07_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 7)    /* XBARA1_OUT7 output assigned to IOMUX_XBAR_INOUT07 */
#define IMXRT_XBARA1_OUT_IOMUX_XBAR_IO08_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 8)    /* XBARA1_OUT8 output assigned to IOMUX_XBAR_INOUT08 */
#define IMXRT_XBARA1_OUT_IOMUX_XBAR_IO09_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 9)    /* XBARA1_OUT9 output assigned to IOMUX_XBAR_INOUT09 */
#define IMXRT_XBARA1_OUT_IOMUX_XBAR_IO10_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 10)   /* XBARA1_OUT10 output assigned to IOMUX_XBAR_INOUT10 */
#define IMXRT_XBARA1_OUT_IOMUX_XBAR_IO11_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 11)   /* XBARA1_OUT11 output assigned to IOMUX_XBAR_INOUT11 */
#define IMXRT_XBARA1_OUT_IOMUX_XBAR_IO12_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 12)   /* XBARA1_OUT12 output assigned to IOMUX_XBAR_INOUT12 */
#define IMXRT_XBARA1_OUT_IOMUX_XBAR_IO13_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 13)   /* XBARA1_OUT13 output assigned to IOMUX_XBAR_INOUT13 */
#define IMXRT_XBARA1_OUT_IOMUX_XBAR_IO14_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 14)   /* XBARA1_OUT14 output assigned to IOMUX_XBAR_INOUT14 */
#define IMXRT_XBARA1_OUT_IOMUX_XBAR_IO15_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 15)   /* XBARA1_OUT15 output assigned to IOMUX_XBAR_INOUT15 */
#define IMXRT_XBARA1_OUT_IOMUX_XBAR_IO16_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 16)   /* XBARA1_OUT16 output assigned to IOMUX_XBAR_INOUT16 */
#define IMXRT_XBARA1_OUT_IOMUX_XBAR_IO17_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 17)   /* XBARA1_OUT17 output assigned to IOMUX_XBAR_INOUT17 */
#define IMXRT_XBARA1_OUT_IOMUX_XBAR_IO18_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 18)   /* XBARA1_OUT18 output assigned to IOMUX_XBAR_INOUT18 */
#define IMXRT_XBARA1_OUT_IOMUX_XBAR_IO19_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 19)   /* XBARA1_OUT19 output assigned to IOMUX_XBAR_INOUT19 */
#define IMXRT_XBARA1_OUT_ACMP1_SAMPLE_SEL_OFFSET         IMXRT_XBARA1(XBAR_OUTPUT, 20)   /* XBARA1_OUT20 output assigned to ACMP1_SAMPLE */
#define IMXRT_XBARA1_OUT_ACMP2_SAMPLE_SEL_OFFSET         IMXRT_XBARA1(XBAR_OUTPUT, 21)   /* XBARA1_OUT21 output assigned to ACMP2_SAMPLE */
#define IMXRT_XBARA1_OUT_ACMP3_SAMPLE_SEL_OFFSET         IMXRT_XBARA1(XBAR_OUTPUT, 22)   /* XBARA1_OUT22 output assigned to ACMP3_SAMPLE */
#define IMXRT_XBARA1_OUT_ACMP4_SAMPLE_SEL_OFFSET         IMXRT_XBARA1(XBAR_OUTPUT, 23)   /* XBARA1_OUT23 output assigned to ACMP4_SAMPLE */
#define IMXRT_XBARA1_OUT_RESERVED24_SEL_OFFSET           IMXRT_XBARA1(XBAR_OUTPUT, 24)   /* XBARA1_OUT24 output is reserved. */
#define IMXRT_XBARA1_OUT_RESERVED25_SEL_OFFSET           IMXRT_XBARA1(XBAR_OUTPUT, 25)   /* XBARA1_OUT25 output is reserved. */
#define IMXRT_XBARA1_OUT_FLEXPWM1_EXTA0_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 26)   /* XBARA1_OUT26 output assigned to FLEXPWM1_EXTA0 */
#define IMXRT_XBARA1_OUT_FLEXPWM1_EXTA1_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 27)   /* XBARA1_OUT27 output assigned to FLEXPWM1_EXTA1 */
#define IMXRT_XBARA1_OUT_FLEXPWM1_EXTA2_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 28)   /* XBARA1_OUT28 output assigned to FLEXPWM1_EXTA2 */
#define IMXRT_XBARA1_OUT_FLEXPWM1_EXTA3_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 29)   /* XBARA1_OUT29 output assigned to FLEXPWM1_EXTA3 */
#define IMXRT_XBARA1_OUT_FLEXPWM1_EXT_SYNC0_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 30)   /* XBARA1_OUT30 output assigned to FLEXPWM1_EXT_SYNC0 */
#define IMXRT_XBARA1_OUT_FLEXPWM1_EXT_SYNC1_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 31)   /* XBARA1_OUT31 output assigned to FLEXPWM1_EXT_SYNC1 */
#define IMXRT_XBARA1_OUT_FLEXPWM1_EXT_SYNC2_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 32)   /* XBARA1_OUT32 output assigned to FLEXPWM1_EXT_SYNC2 */
#define IMXRT_XBARA1_OUT_FLEXPWM1_EXT_SYNC3_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 33)   /* XBARA1_OUT33 output assigned to FLEXPWM1_EXT_SYNC3 */
#define IMXRT_XBARA1_OUT_FLEXPWM1_EXT_CLK_SEL_OFFSET     IMXRT_XBARA1(XBAR_OUTPUT, 34)   /* XBARA1_OUT34 output assigned to FLEXPWM1_EXT_CLK */
#define IMXRT_XBARA1_OUT_FLEXPWM1_FAULT0_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 35)   /* XBARA1_OUT35 output assigned to FLEXPWM1_FAULT0 */
#define IMXRT_XBARA1_OUT_FLEXPWM1_FAULT1_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 36)   /* XBARA1_OUT36 output assigned to FLEXPWM1_FAULT1 */
#define IMXRT_XBARA1_OUT_FLEXPWM1234_FAULT2_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 37)   /* XBARA1_OUT37 output assigned to FLEXPWM1_2_3_4_FAULT2 */
#define IMXRT_XBARA1_OUT_FLEXPWM1234_FAULT3_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 38)   /* XBARA1_OUT38 output assigned to FLEXPWM1_2_3_4_FAULT3 */
#define IMXRT_XBARA1_OUT_FLEXPWM1_EXT_FORCE_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 39)   /* XBARA1_OUT39 output assigned to FLEXPWM1_EXT_FORCE */
#define IMXRT_XBARA1_OUT_FLEXPWM234_EXTA0_SEL_OFFSET     IMXRT_XBARA1(XBAR_OUTPUT, 40)   /* XBARA1_OUT40 output assigned to FLEXPWM2_3_4_EXTA0 */
#define IMXRT_XBARA1_OUT_FLEXPWM234_EXTA1_SEL_OFFSET     IMXRT_XBARA1(XBAR_OUTPUT, 41)   /* XBARA1_OUT41 output assigned to FLEXPWM2_3_4_EXTA1 */
#define IMXRT_XBARA1_OUT_FLEXPWM234_EXTA2_SEL_OFFSET     IMXRT_XBARA1(XBAR_OUTPUT, 42)   /* XBARA1_OUT42 output assigned to FLEXPWM2_3_4_EXTA2 */
#define IMXRT_XBARA1_OUT_FLEXPWM234_EXTA3_SEL_OFFSET     IMXRT_XBARA1(XBAR_OUTPUT, 43)   /* XBARA1_OUT43 output assigned to FLEXPWM2_3_4_EXTA3 */
#define IMXRT_XBARA1_OUT_FLEXPWM2_EXT_SYNC0_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 44)   /* XBARA1_OUT44 output assigned to FLEXPWM2_EXT_SYNC0 */
#define IMXRT_XBARA1_OUT_FLEXPWM2_EXT_SYNC1_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 45)   /* XBARA1_OUT45 output assigned to FLEXPWM2_EXT_SYNC1 */
#define IMXRT_XBARA1_OUT_FLEXPWM2_EXT_SYNC2_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 46)   /* XBARA1_OUT46 output assigned to FLEXPWM2_EXT_SYNC2 */
#define IMXRT_XBARA1_OUT_FLEXPWM2_EXT_SYNC3_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 47)   /* XBARA1_OUT47 output assigned to FLEXPWM2_EXT_SYNC3 */
#define IMXRT_XBARA1_OUT_FLEXPWM234_EXT_CLK_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 48)   /* XBARA1_OUT48 output assigned to FLEXPWM2_3_4_EXT_CLK */
#define IMXRT_XBARA1_OUT_FLEXPWM2_FAULT0_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 49)   /* XBARA1_OUT49 output assigned to FLEXPWM2_FAULT0 */
#define IMXRT_XBARA1_OUT_FLEXPWM2_FAULT1_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 50)   /* XBARA1_OUT50 output assigned to FLEXPWM2_FAULT1 */
#define IMXRT_XBARA1_OUT_FLEXPWM2_EXT_FORCE_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 51)   /* XBARA1_OUT51 output assigned to FLEXPWM2_EXT_FORCE */
#define IMXRT_XBARA1_OUT_FLEXPWM3_EXT_SYNC0_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 52)   /* XBARA1_OUT52 output assigned to FLEXPWM3_EXT_SYNC0 */
#define IMXRT_XBARA1_OUT_FLEXPWM3_EXT_SYNC1_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 53)   /* XBARA1_OUT53 output assigned to FLEXPWM3_EXT_SYNC1 */
#define IMXRT_XBARA1_OUT_FLEXPWM3_EXT_SYNC2_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 54)   /* XBARA1_OUT54 output assigned to FLEXPWM3_EXT_SYNC2 */
#define IMXRT_XBARA1_OUT_FLEXPWM3_EXT_SYNC3_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 55)   /* XBARA1_OUT55 output assigned to FLEXPWM3_EXT_SYNC3 */
#define IMXRT_XBARA1_OUT_FLEXPWM3_FAULT0_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 56)   /* XBARA1_OUT56 output assigned to FLEXPWM3_FAULT0 */
#define IMXRT_XBARA1_OUT_FLEXPWM3_FAULT1_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 57)   /* XBARA1_OUT57 output assigned to FLEXPWM3_FAULT1 */
#define IMXRT_XBARA1_OUT_FLEXPWM3_EXT_FORCE_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 58)   /* XBARA1_OUT58 output assigned to FLEXPWM3_EXT_FORCE */
#define IMXRT_XBARA1_OUT_FLEXPWM4_EXT_SYNC0_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 59)   /* XBARA1_OUT59 output assigned to FLEXPWM4_EXT_SYNC0 */
#define IMXRT_XBARA1_OUT_FLEXPWM4_EXT_SYNC1_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 60)   /* XBARA1_OUT60 output assigned to FLEXPWM4_EXT_SYNC1 */
#define IMXRT_XBARA1_OUT_FLEXPWM4_EXT_SYNC2_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 61)   /* XBARA1_OUT61 output assigned to FLEXPWM4_EXT_SYNC2 */
#define IMXRT_XBARA1_OUT_FLEXPWM4_EXT_SYNC3_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 62)   /* XBARA1_OUT62 output assigned to FLEXPWM4_EXT_SYNC3 */
#define IMXRT_XBARA1_OUT_FLEXPWM4_FAULT0_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 63)   /* XBARA1_OUT63 output assigned to FLEXPWM4_FAULT0 */
#define IMXRT_XBARA1_OUT_FLEXPWM4_FAULT1_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 64)   /* XBARA1_OUT64 output assigned to FLEXPWM4_FAULT1 */
#define IMXRT_XBARA1_OUT_FLEXPWM4_EXT_FORCE_SEL_OFFSET   IMXRT_XBARA1(XBAR_OUTPUT, 65)   /* XBARA1_OUT65 output assigned to FLEXPWM4_EXT_FORCE */
#define IMXRT_XBARA1_OUT_ENC1_PHASE_AIN_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 66)   /* XBARA1_OUT66 output assigned to ENC1_PHASE_A_IN_ */
#define IMXRT_XBARA1_OUT_ENC1_PHASE_BIN_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 67)   /* XBARA1_OUT67 output assigned to ENC1_PHASE_B_IN_ */
#define IMXRT_XBARA1_OUT_ENC1_INDEX_SEL_OFFSET           IMXRT_XBARA1(XBAR_OUTPUT, 68)   /* XBARA1_OUT68 output assigned to ENC1_INDEX */
#define IMXRT_XBARA1_OUT_ENC1_HOME_SEL_OFFSET            IMXRT_XBARA1(XBAR_OUTPUT, 69)   /* XBARA1_OUT69 output assigned to ENC1_HOME */
#define IMXRT_XBARA1_OUT_ENC1_TRIGGER_SEL_OFFSET         IMXRT_XBARA1(XBAR_OUTPUT, 70)   /* XBARA1_OUT70 output assigned to ENC1_TRIGGER */
#define IMXRT_XBARA1_OUT_ENC2_PHASE_AIN_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 71)   /* XBARA1_OUT71 output assigned to ENC2_PHASE_A_IN_ */
#define IMXRT_XBARA1_OUT_ENC2_PHASE_BIN_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 72)   /* XBARA1_OUT72 output assigned to ENC2_PHASE_B_IN_ */
#define IMXRT_XBARA1_OUT_ENC2_INDEX_SEL_OFFSET           IMXRT_XBARA1(XBAR_OUTPUT, 73)   /* XBARA1_OUT73 output assigned to ENC2_INDEX */
#define IMXRT_XBARA1_OUT_ENC2_HOME_SEL_OFFSET            IMXRT_XBARA1(XBAR_OUTPUT, 74)   /* XBARA1_OUT74 output assigned to ENC2_HOME */
#define IMXRT_XBARA1_OUT_ENC2_TRIGGER_SEL_OFFSET         IMXRT_XBARA1(XBAR_OUTPUT, 75)   /* XBARA1_OUT75 output assigned to ENC2_TRIGGER */
#define IMXRT_XBARA1_OUT_ENC3_PHASE_AIN_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 76)   /* XBARA1_OUT76 output assigned to ENC3_PHASE_A_IN_ */
#define IMXRT_XBARA1_OUT_ENC3_PHASE_BIN_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 77)   /* XBARA1_OUT77 output assigned to ENC3_PHASE_B_IN_ */
#define IMXRT_XBARA1_OUT_ENC3_INDEX_SEL_OFFSET           IMXRT_XBARA1(XBAR_OUTPUT, 78)   /* XBARA1_OUT78 output assigned to ENC3_INDEX */
#define IMXRT_XBARA1_OUT_ENC3_HOME_SEL_OFFSET            IMXRT_XBARA1(XBAR_OUTPUT, 79)   /* XBARA1_OUT79 output assigned to ENC3_HOME */
#define IMXRT_XBARA1_OUT_ENC3_TRIGGER_SEL_OFFSET         IMXRT_XBARA1(XBAR_OUTPUT, 80)   /* XBARA1_OUT80 output assigned to ENC3_TRIGGER */
#define IMXRT_XBARA1_OUT_ENC4_PHASE_AIN_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 81)   /* XBARA1_OUT81 output assigned to ENC4_PHASE_A_IN_ */
#define IMXRT_XBARA1_OUT_ENC4_PHASE_BIN_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 82)   /* XBARA1_OUT82 output assigned to ENC4_PHASE_B_IN_ */
#define IMXRT_XBARA1_OUT_ENC4_INDEX_SEL_OFFSET           IMXRT_XBARA1(XBAR_OUTPUT, 83)   /* XBARA1_OUT83 output assigned to ENC4_INDEX */
#define IMXRT_XBARA1_OUT_ENC4_HOME_SEL_OFFSET            IMXRT_XBARA1(XBAR_OUTPUT, 84)   /* XBARA1_OUT84 output assigned to ENC4_HOME */
#define IMXRT_XBARA1_OUT_ENC4_TRIGGER_SEL_OFFSET         IMXRT_XBARA1(XBAR_OUTPUT, 85)   /* XBARA1_OUT85 output assigned to ENC4_TRIGGER */
#define IMXRT_XBARA1_OUT_QTIMER1_TMR0_IN_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 86)   /* XBARA1_OUT86 output assigned to QTIMER1_TMR0_IN_ */
#define IMXRT_XBARA1_OUT_QTIMER1_TMR1_IN_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 87)   /* XBARA1_OUT87 output assigned to QTIMER1_TMR1_IN_ */
#define IMXRT_XBARA1_OUT_QTIMER1_TMR2_IN_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 88)   /* XBARA1_OUT88 output assigned to QTIMER1_TMR2_IN_ */
#define IMXRT_XBARA1_OUT_QTIMER1_TMR3_IN_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 89)   /* XBARA1_OUT89 output assigned to QTIMER1_TMR3_IN_ */
#define IMXRT_XBARA1_OUT_QTIMER2_TMR0_IN_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 90)   /* XBARA1_OUT90 output assigned to QTIMER2_TMR0_IN_ */
#define IMXRT_XBARA1_OUT_QTIMER2_TMR1_IN_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 91)   /* XBARA1_OUT91 output assigned to QTIMER2_TMR1_IN_ */
#define IMXRT_XBARA1_OUT_QTIMER2_TMR2_IN_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 92)   /* XBARA1_OUT92 output assigned to QTIMER2_TMR2_IN_ */
#define IMXRT_XBARA1_OUT_QTIMER2_TMR3_IN_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 93)   /* XBARA1_OUT93 output assigned to QTIMER2_TMR3_IN_ */
#define IMXRT_XBARA1_OUT_QTIMER3_TMR0_IN_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 94)   /* XBARA1_OUT94 output assigned to QTIMER3_TMR0_IN_ */
#define IMXRT_XBARA1_OUT_QTIMER3_TMR1_IN_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 95)   /* XBARA1_OUT95 output assigned to QTIMER3_TMR1_IN_ */
#define IMXRT_XBARA1_OUT_QTIMER3_TMR2_IN_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 96)   /* XBARA1_OUT96 output assigned to QTIMER3_TMR2_IN_ */
#define IMXRT_XBARA1_OUT_QTIMER3_TMR3_IN_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 97)   /* XBARA1_OUT97 output assigned to QTIMER3_TMR3_IN_ */
#define IMXRT_XBARA1_OUT_QTIMER4_TMR0_IN_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 98)   /* XBARA1_OUT98 output assigned to QTIMER4_TMR0_IN_ */
#define IMXRT_XBARA1_OUT_QTIMER4_TMR1_IN_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 99)   /* XBARA1_OUT99 output assigned to QTIMER4_TMR1_IN_ */
#define IMXRT_XBARA1_OUT_QTIMER4_TMR2_IN_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 100)  /* XBARA1_OUT100 output assigned to QTIMER4_TMR2_IN_ */
#define IMXRT_XBARA1_OUT_QTIMER4_TMR3_IN_SEL_OFFSET      IMXRT_XBARA1(XBAR_OUTPUT, 101)  /* XBARA1_OUT101 output assigned to QTIMER4_TMR3_IN_ */
#define IMXRT_XBARA1_OUT_EWM_EWM_IN_SEL_OFFSET           IMXRT_XBARA1(XBAR_OUTPUT, 102)  /* XBARA1_OUT102 output assigned to EWM_EWM_IN */
#define IMXRT_XBARA1_OUT_ADC_ETC_XBAR0_TRIG0_SEL_OFFSET  IMXRT_XBARA1(XBAR_OUTPUT, 103)  /* XBARA1_OUT103 output assigned to ADC_ETC_XBAR0_TRIG0 */
#define IMXRT_XBARA1_OUT_ADC_ETC_XBAR0_TRIG1_SEL_OFFSET  IMXRT_XBARA1(XBAR_OUTPUT, 104)  /* XBARA1_OUT104 output assigned to ADC_ETC_XBAR0_TRIG1 */
#define IMXRT_XBARA1_OUT_ADC_ETC_XBAR0_TRIG2_SEL_OFFSET  IMXRT_XBARA1(XBAR_OUTPUT, 105)  /* XBARA1_OUT105 output assigned to ADC_ETC_XBAR0_TRIG2 */
#define IMXRT_XBARA1_OUT_ADC_ETC_XBAR0_TRIG3_SEL_OFFSET  IMXRT_XBARA1(XBAR_OUTPUT, 106)  /* XBARA1_OUT106 output assigned to ADC_ETC_XBAR0_TRIG3 */
#define IMXRT_XBARA1_OUT_ADC_ETC_XBAR1_TRIG0_SEL_OFFSET  IMXRT_XBARA1(XBAR_OUTPUT, 107)  /* XBARA1_OUT107 output assigned to ADC_ETC_XBAR1_TRIG0 */
#define IMXRT_XBARA1_OUT_ADC_ETC_XBAR1_TRIG1_SEL_OFFSET  IMXRT_XBARA1(XBAR_OUTPUT, 108)  /* XBARA1_OUT108 output assigned to ADC_ETC_XBAR1_TRIG1 */
#define IMXRT_XBARA1_OUT_ADC_ETC_XBAR1_TRIG2_SEL_OFFSET  IMXRT_XBARA1(XBAR_OUTPUT, 109)  /* XBARA1_OUT109 output assigned to ADC_ETC_XBAR1_TRIG2 */
#define IMXRT_XBARA1_OUT_ADC_ETC_XBAR1_TRIG3_SEL_OFFSET  IMXRT_XBARA1(XBAR_OUTPUT, 110)  /* XBARA1_OUT110 output assigned to ADC_ETC_XBAR1_TRIG3 */
#define IMXRT_XBARA1_OUT_LPI2C1_TRG_IN_SEL_OFFSET        IMXRT_XBARA1(XBAR_OUTPUT, 111)  /* XBARA1_OUT111 output assigned to LPI2C1_TRG_IN_ */
#define IMXRT_XBARA1_OUT_LPI2C2_TRG_IN_SEL_OFFSET        IMXRT_XBARA1(XBAR_OUTPUT, 112)  /* XBARA1_OUT112 output assigned to LPI2C2_TRG_IN_ */
#define IMXRT_XBARA1_OUT_LPI2C3_TRG_IN_SEL_OFFSET        IMXRT_XBARA1(XBAR_OUTPUT, 113)  /* XBARA1_OUT113 output assigned to LPI2C3_TRG_IN_ */
#define IMXRT_XBARA1_OUT_LPI2C4_TRG_IN_SEL_OFFSET        IMXRT_XBARA1(XBAR_OUTPUT, 114)  /* XBARA1_OUT114 output assigned to LPI2C4_TRG_IN_ */
#define IMXRT_XBARA1_OUT_LPSPI1_TRG_IN_SEL_OFFSET        IMXRT_XBARA1(XBAR_OUTPUT, 115)  /* XBARA1_OUT115 output assigned to LPSPI1_TRG_IN_ */
#define IMXRT_XBARA1_OUT_LPSPI2_TRG_IN_SEL_OFFSET        IMXRT_XBARA1(XBAR_OUTPUT, 116)  /* XBARA1_OUT116 output assigned to LPSPI2_TRG_IN_ */
#define IMXRT_XBARA1_OUT_LPSPI3_TRG_IN_SEL_OFFSET        IMXRT_XBARA1(XBAR_OUTPUT, 117)  /* XBARA1_OUT117 output assigned to LPSPI3_TRG_IN_ */
#define IMXRT_XBARA1_OUT_LPSPI4_TRG_IN_SEL_OFFSET        IMXRT_XBARA1(XBAR_OUTPUT, 118)  /* XBARA1_OUT118 output assigned to LPSPI4_TRG_IN_ */
#define IMXRT_XBARA1_OUT_LPUART1_TRG_IN_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 119)  /* XBARA1_OUT119 output assigned to LPUART1_TRG_IN_ */
#define IMXRT_XBARA1_OUT_LPUART2_TRG_IN_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 120)  /* XBARA1_OUT120 output assigned to LPUART2_TRG_IN_ */
#define IMXRT_XBARA1_OUT_LPUART3_TRG_IN_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 121)  /* XBARA1_OUT121 output assigned to LPUART3_TRG_IN_ */
#define IMXRT_XBARA1_OUT_LPUART4_TRG_IN_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 122)  /* XBARA1_OUT122 output assigned to LPUART4_TRG_IN_ */
#define IMXRT_XBARA1_OUT_LPUART5_TRG_IN_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 123)  /* XBARA1_OUT123 output assigned to LPUART5_TRG_IN_ */
#define IMXRT_XBARA1_OUT_LPUART6_TRG_IN_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 124)  /* XBARA1_OUT124 output assigned to LPUART6_TRG_IN_ */
#define IMXRT_XBARA1_OUT_LPUART7_TRG_IN_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 125)  /* XBARA1_OUT125 output assigned to LPUART7_TRG_IN_ */
#define IMXRT_XBARA1_OUT_LPUART8_TRG_IN_SEL_OFFSET       IMXRT_XBARA1(XBAR_OUTPUT, 126)  /* XBARA1_OUT126 output assigned to LPUART8_TRG_IN_ */
#define IMXRT_XBARA1_OUT_FLEXIO1_TRIGGER_IN0_SEL_OFFSET  IMXRT_XBARA1(XBAR_OUTPUT, 127)  /* XBARA1_OUT127 output assigned to FLEXIO1_TRIGGER_IN0 */
#define IMXRT_XBARA1_OUT_FLEXIO1_TRIGGER_IN1_SEL_OFFSET  IMXRT_XBARA1(XBAR_OUTPUT, 128)  /* XBARA1_OUT128 output assigned to FLEXIO1_TRIGGER_IN1 */
#define IMXRT_XBARA1_OUT_FLEXIO2_TRIGGER_IN0_SEL_OFFSET  IMXRT_XBARA1(XBAR_OUTPUT, 129)  /* XBARA1_OUT129 output assigned to FLEXIO2_TRIGGER_IN0 */
#define IMXRT_XBARA1_OUT_FLEXIO2_TRIGGER_IN1_SEL_OFFSET  IMXRT_XBARA1(XBAR_OUTPUT, 130)  /* XBARA1_OUT130 output assigned to FLEXIO2_TRIGGER_IN1 */

/* XBARB2 Mux inputs (I values) *********************************************/

#define IMXRT_XBARB2_IN_LOGIC_LOW                        IMXRT_XBARB2(XBAR_INPUT, 0)    /* LOGIC_LOW output assigned to XBARB2_IN0 input. */
#define IMXRT_XBARB2_IN_LOGIC_HIGH                       IMXRT_XBARB2(XBAR_INPUT, 1)    /* LOGIC_HIGH output assigned to XBARB2_IN1 input. */
#define IMXRT_XBARB2_IN_RESERVED2                        IMXRT_XBARB2(XBAR_INPUT, 2)    /* XBARB2_IN2 input is reserved. */
#define IMXRT_XBARB2_IN_RESERVED3                        IMXRT_XBARB2(XBAR_INPUT, 3)    /* XBARB2_IN3 input is reserved. */
#define IMXRT_XBARB2_IN_RESERVED4                        IMXRT_XBARB2(XBAR_INPUT, 4)    /* XBARB2_IN4 input is reserved. */
#define IMXRT_XBARB2_IN_RESERVED5                        IMXRT_XBARB2(XBAR_INPUT, 5)    /* XBARB2_IN5 input is reserved. */
#define IMXRT_XBARB2_IN_ACMP1_OUT                        IMXRT_XBARB2(XBAR_INPUT, 6)    /* ACMP1_OUT output assigned to XBARB2_IN6 input. */
#define IMXRT_XBARB2_IN_ACMP2_OUT                        IMXRT_XBARB2(XBAR_INPUT, 7)    /* ACMP2_OUT output assigned to XBARB2_IN7 input. */
#define IMXRT_XBARB2_IN_ACMP3_OUT                        IMXRT_XBARB2(XBAR_INPUT, 8)    /* ACMP3_OUT output assigned to XBARB2_IN8 input. */
#define IMXRT_XBARB2_IN_ACMP4_OUT                        IMXRT_XBARB2(XBAR_INPUT, 9)    /* ACMP4_OUT output assigned to XBARB2_IN9 input. */
#define IMXRT_XBARB2_IN_RESERVED10                       IMXRT_XBARB2(XBAR_INPUT, 10)   /* XBARB2_IN10 input is reserved. */
#define IMXRT_XBARB2_IN_RESERVED11                       IMXRT_XBARB2(XBAR_INPUT, 11)   /* XBARB2_IN11 input is reserved. */
#define IMXRT_XBARB2_IN_QTIMER3_TMR0_OUT                 IMXRT_XBARB2(XBAR_INPUT, 12)   /* QTIMER3_TMR0_OUTPUT output assigned to XBARB2_IN12 input. */
#define IMXRT_XBARB2_IN_QTIMER3_TMR1_OUT                 IMXRT_XBARB2(XBAR_INPUT, 13)   /* QTIMER3_TMR1_OUTPUT output assigned to XBARB2_IN13 input. */
#define IMXRT_XBARB2_IN_QTIMER3_TMR2_OUT                 IMXRT_XBARB2(XBAR_INPUT, 14)   /* QTIMER3_TMR2_OUTPUT output assigned to XBARB2_IN14 input. */
#define IMXRT_XBARB2_IN_QTIMER3_TMR3_OUT                 IMXRT_XBARB2(XBAR_INPUT, 15)   /* QTIMER3_TMR3_OUTPUT output assigned to XBARB2_IN15 input. */
#define IMXRT_XBARB2_IN_QTIMER4_TMR0_OUT                 IMXRT_XBARB2(XBAR_INPUT, 16)   /* QTIMER4_TMR0_OUTPUT output assigned to XBARB2_IN16 input. */
#define IMXRT_XBARB2_IN_QTIMER4_TMR1_OUT                 IMXRT_XBARB2(XBAR_INPUT, 17)   /* QTIMER4_TMR1_OUTPUT output assigned to XBARB2_IN17 input. */
#define IMXRT_XBARB2_IN_QTIMER4_TMR2_OUT                 IMXRT_XBARB2(XBAR_INPUT, 18)   /* QTIMER4_TMR2_OUTPUT output assigned to XBARB2_IN18 input. */
#define IMXRT_XBARB2_IN_QTIMER4_TMR3_OUT                 IMXRT_XBARB2(XBAR_INPUT, 19)   /* QTIMER4_TMR3_OUTPUT output assigned to XBARB2_IN19 input. */
#define IMXRT_XBARB2_IN_FLEXPWM1_PWM1_OUT_TRIG01         IMXRT_XBARB2(XBAR_INPUT, 20)   /* FLEXPWM1_PWM1_OUT_TRIG0_1 output assigned to XBARB2_IN20 input. */
#define IMXRT_XBARB2_IN_FLEXPWM1_PWM2_OUT_TRIG01         IMXRT_XBARB2(XBAR_INPUT, 21)   /* FLEXPWM1_PWM2_OUT_TRIG0_1 output assigned to XBARB2_IN21 input. */
#define IMXRT_XBARB2_IN_FLEXPWM1_PWM3_OUT_TRIG01         IMXRT_XBARB2(XBAR_INPUT, 22)   /* FLEXPWM1_PWM3_OUT_TRIG0_1 output assigned to XBARB2_IN22 input. */
#define IMXRT_XBARB2_IN_FLEXPWM1_PWM4_OUT_TRIG01         IMXRT_XBARB2(XBAR_INPUT, 23)   /* FLEXPWM1_PWM4_OUT_TRIG0_1 output assigned to XBARB2_IN23 input. */
#define IMXRT_XBARB2_IN_FLEXPWM2_PWM1_OUT_TRIG01         IMXRT_XBARB2(XBAR_INPUT, 24)   /* FLEXPWM2_PWM1_OUT_TRIG0_1 output assigned to XBARB2_IN24 input. */
#define IMXRT_XBARB2_IN_FLEXPWM2_PWM2_OUT_TRIG01         IMXRT_XBARB2(XBAR_INPUT, 25)   /* FLEXPWM2_PWM2_OUT_TRIG0_1 output assigned to XBARB2_IN25 input. */
#define IMXRT_XBARB2_IN_FLEXPWM2_PWM3_OUT_TRIG01         IMXRT_XBARB2(XBAR_INPUT, 26)   /* FLEXPWM2_PWM3_OUT_TRIG0_1 output assigned to XBARB2_IN26 input. */
#define IMXRT_XBARB2_IN_FLEXPWM2_PWM4_OUT_TRIG01         IMXRT_XBARB2(XBAR_INPUT, 27)   /* FLEXPWM2_PWM4_OUT_TRIG0_1 output assigned to XBARB2_IN27 input. */
#define IMXRT_XBARB2_IN_FLEXPWM3_PWM1_OUT_TRIG01         IMXRT_XBARB2(XBAR_INPUT, 28)   /* FLEXPWM3_PWM1_OUT_TRIG0_1 output assigned to XBARB2_IN28 input. */
#define IMXRT_XBARB2_IN_FLEXPWM3_PWM2_OUT_TRIG01         IMXRT_XBARB2(XBAR_INPUT, 29)   /* FLEXPWM3_PWM2_OUT_TRIG0_1 output assigned to XBARB2_IN29 input. */
#define IMXRT_XBARB2_IN_FLEXPWM3_PWM3_OUT_TRIG01         IMXRT_XBARB2(XBAR_INPUT, 30)   /* FLEXPWM3_PWM3_OUT_TRIG0_1 output assigned to XBARB2_IN30 input. */
#define IMXRT_XBARB2_IN_FLEXPWM3_PWM4_OUT_TRIG01         IMXRT_XBARB2(XBAR_INPUT, 31)   /* FLEXPWM3_PWM4_OUT_TRIG0_1 output assigned to XBARB2_IN31 input. */
#define IMXRT_XBARB2_IN_FLEXPWM4_PWM1_OUT_TRIG01         IMXRT_XBARB2(XBAR_INPUT, 32)   /* FLEXPWM4_PWM1_OUT_TRIG0_1 output assigned to XBARB2_IN32 input. */
#define IMXRT_XBARB2_IN_FLEXPWM4_PWM2_OUT_TRIG01         IMXRT_XBARB2(XBAR_INPUT, 33)   /* FLEXPWM4_PWM2_OUT_TRIG0_1 output assigned to XBARB2_IN33 input. */
#define IMXRT_XBARB2_IN_FLEXPWM4_PWM3_OUT_TRIG01         IMXRT_XBARB2(XBAR_INPUT, 34)   /* FLEXPWM4_PWM3_OUT_TRIG0_1 output assigned to XBARB2_IN34 input. */
#define IMXRT_XBARB2_IN_FLEXPWM4_PWM4_OUT_TRIG01         IMXRT_XBARB2(XBAR_INPUT, 35)   /* FLEXPWM4_PWM4_OUT_TRIG0_1 output assigned to XBARB2_IN35 input. */
#define IMXRT_XBARB2_IN_PIT_TRIGGER0                     IMXRT_XBARB2(XBAR_INPUT, 36)   /* PIT_TRIGGER0 output assigned to XBARB2_IN36 input. */
#define IMXRT_XBARB2_IN_PIT_TRIGGER1                     IMXRT_XBARB2(XBAR_INPUT, 37)   /* PIT_TRIGGER1 output assigned to XBARB2_IN37 input. */
#define IMXRT_XBARB2_IN_ADC_ETC_XBAR0_COCO0              IMXRT_XBARB2(XBAR_INPUT, 38)   /* ADC_ETC_XBAR0_COCO0 output assigned to XBARB2_IN38 input. */
#define IMXRT_XBARB2_IN_ADC_ETC_XBAR0_COCO1              IMXRT_XBARB2(XBAR_INPUT, 39)   /* ADC_ETC_XBAR0_COCO1 output assigned to XBARB2_IN39 input. */
#define IMXRT_XBARB2_IN_ADC_ETC_XBAR0_COCO2              IMXRT_XBARB2(XBAR_INPUT, 40)   /* ADC_ETC_XBAR0_COCO2 output assigned to XBARB2_IN40 input. */
#define IMXRT_XBARB2_IN_ADC_ETC_XBAR0_COCO3              IMXRT_XBARB2(XBAR_INPUT, 41)   /* ADC_ETC_XBAR0_COCO3 output assigned to XBARB2_IN41 input. */
#define IMXRT_XBARB2_IN_ADC_ETC_XBAR1_COCO0              IMXRT_XBARB2(XBAR_INPUT, 42)   /* ADC_ETC_XBAR1_COCO0 output assigned to XBARB2_IN42 input. */
#define IMXRT_XBARB2_IN_ADC_ETC_XBAR1_COCO1              IMXRT_XBARB2(XBAR_INPUT, 43)   /* ADC_ETC_XBAR1_COCO1 output assigned to XBARB2_IN43 input. */
#define IMXRT_XBARB2_IN_ADC_ETC_XBAR1_COCO2              IMXRT_XBARB2(XBAR_INPUT, 44)   /* ADC_ETC_XBAR1_COCO2 output assigned to XBARB2_IN44 input. */
#define IMXRT_XBARB2_IN_ADC_ETC_XBAR1_COCO3              IMXRT_XBARB2(XBAR_INPUT, 45)   /* ADC_ETC_XBAR1_COCO3 output assigned to XBARB2_IN45 input. */
#define IMXRT_XBARB2_IN_ENC1_POS_MATCH                   IMXRT_XBARB2(XBAR_INPUT, 46)   /* ENC1_POS_MATCH output assigned to XBARB2_IN46 input. */
#define IMXRT_XBARB2_IN_ENC2_POS_MATCH                   IMXRT_XBARB2(XBAR_INPUT, 47)   /* ENC2_POS_MATCH output assigned to XBARB2_IN47 input. */
#define IMXRT_XBARB2_IN_ENC3_POS_MATCH                   IMXRT_XBARB2(XBAR_INPUT, 48)   /* ENC3_POS_MATCH output assigned to XBARB2_IN48 input. */
#define IMXRT_XBARB2_IN_ENC4_POS_MATCH                   IMXRT_XBARB2(XBAR_INPUT, 49)   /* ENC4_POS_MATCH output assigned to XBARB2_IN49 input. */
#define IMXRT_XBARB2_IN_DMA_DONE0                        IMXRT_XBARB2(XBAR_INPUT, 50)   /* DMA_DONE0 output assigned to XBARB2_IN50 input. */
#define IMXRT_XBARB2_IN_DMA_DONE1                        IMXRT_XBARB2(XBAR_INPUT, 51)   /* DMA_DONE1 output assigned to XBARB2_IN51 input. */
#define IMXRT_XBARB2_IN_DMA_DONE2                        IMXRT_XBARB2(XBAR_INPUT, 52)   /* DMA_DONE2 output assigned to XBARB2_IN52 input. */
#define IMXRT_XBARB2_IN_DMA_DONE3                        IMXRT_XBARB2(XBAR_INPUT, 53)   /* DMA_DONE3 output assigned to XBARB2_IN53 input. */
#define IMXRT_XBARB2_IN_DMA_DONE4                        IMXRT_XBARB2(XBAR_INPUT, 54)   /* DMA_DONE4 output assigned to XBARB2_IN54 input. */
#define IMXRT_XBARB2_IN_DMA_DONE5                        IMXRT_XBARB2(XBAR_INPUT, 55)   /* DMA_DONE5 output assigned to XBARB2_IN55 input. */
#define IMXRT_XBARB2_IN_DMA_DONE6                        IMXRT_XBARB2(XBAR_INPUT, 56)   /* DMA_DONE6 output assigned to XBARB2_IN56 input. */
#define IMXRT_XBARB2_IN_DMA_DONE7                        IMXRT_XBARB2(XBAR_INPUT, 57)   /* DMA_DONE7 output assigned to XBARB2_IN57 input. */

/* XBARB2 Mux Output  (M Muxes) *********************************************/

#define IMXRT_XBARB2_OUT_AOI1_IN00_SEL_OFFSET            IMXRT_XBARB2(XBAR_OUTPUT, 0)    /* XBARB2_OUT0 output assigned to AOI1_IN00 */
#define IMXRT_XBARB2_OUT_AOI1_IN01_SEL_OFFSET            IMXRT_XBARB2(XBAR_OUTPUT, 1)    /* XBARB2_OUT1 output assigned to AOI1_IN01 */
#define IMXRT_XBARB2_OUT_AOI1_IN02_SEL_OFFSET            IMXRT_XBARB2(XBAR_OUTPUT, 2)    /* XBARB2_OUT2 output assigned to AOI1_IN02 */
#define IMXRT_XBARB2_OUT_AOI1_IN03_SEL_OFFSET            IMXRT_XBARB2(XBAR_OUTPUT, 3)    /* XBARB2_OUT3 output assigned to AOI1_IN03 */
#define IMXRT_XBARB2_OUT_AOI1_IN04_SEL_OFFSET            IMXRT_XBARB2(XBAR_OUTPUT, 4)    /* XBARB2_OUT4 output assigned to AOI1_IN04 */
#define IMXRT_XBARB2_OUT_AOI1_IN05_SEL_OFFSET            IMXRT_XBARB2(XBAR_OUTPUT, 5)    /* XBARB2_OUT5 output assigned to AOI1_IN05 */
#define IMXRT_XBARB2_OUT_AOI1_IN06_SEL_OFFSET            IMXRT_XBARB2(XBAR_OUTPUT, 6)    /* XBARB2_OUT6 output assigned to AOI1_IN06 */
#define IMXRT_XBARB2_OUT_AOI1_IN07_SEL_OFFSET            IMXRT_XBARB2(XBAR_OUTPUT, 7)    /* XBARB2_OUT7 output assigned to AOI1_IN07 */
#define IMXRT_XBARB2_OUT_AOI1_IN08_SEL_OFFSET            IMXRT_XBARB2(XBAR_OUTPUT, 8)    /* XBARB2_OUT8 output assigned to AOI1_IN08 */
#define IMXRT_XBARB2_OUT_AOI1_IN09_SEL_OFFSET            IMXRT_XBARB2(XBAR_OUTPUT, 9)    /* XBARB2_OUT9 output assigned to AOI1_IN09 */
#define IMXRT_XBARB2_OUT_AOI1_IN10_SEL_OFFSET            IMXRT_XBARB2(XBAR_OUTPUT, 10)   /* XBARB2_OUT10 output assigned to AOI1_IN10 */
#define IMXRT_XBARB2_OUT_AOI1_IN11_SEL_OFFSET            IMXRT_XBARB2(XBAR_OUTPUT, 11)   /* XBARB2_OUT11 output assigned to AOI1_IN11 */
#define IMXRT_XBARB2_OUT_AOI1_IN12_SEL_OFFSET            IMXRT_XBARB2(XBAR_OUTPUT, 12)   /* XBARB2_OUT12 output assigned to AOI1_IN12 */
#define IMXRT_XBARB2_OUT_AOI1_IN13_SEL_OFFSET            IMXRT_XBARB2(XBAR_OUTPUT, 13)   /* XBARB2_OUT13 output assigned to AOI1_IN13 */
#define IMXRT_XBARB2_OUT_AOI1_IN14_SEL_OFFSET            IMXRT_XBARB2(XBAR_OUTPUT, 14)   /* XBARB2_OUT14 output assigned to AOI1_IN14 */
#define IMXRT_XBARB2_OUT_AOI1_IN15_SEL_OFFSET            IMXRT_XBARB2(XBAR_OUTPUT, 15)   /* XBARB2_OUT15 output assigned to AOI1_IN15 */

/* XBARB3 Mux inputs (I values) *********************************************/

#define IMXRT_XBARB3_IN_LOGIC_LOW                        IMXRT_XBARB3(XBAR_INPUT, 0)    /* LOGIC_LOW output assigned to XBARB3_IN0 input. */
#define IMXRT_XBARB3_IN_LOGIC_HIGH                       IMXRT_XBARB3(XBAR_INPUT, 1)    /* LOGIC_HIGH output assigned to XBARB3_IN1 input. */
#define IMXRT_XBARB3_IN_RESERVED2                        IMXRT_XBARB3(XBAR_INPUT, 2)    /* XBARB3_IN2 input is reserved. */
#define IMXRT_XBARB3_IN_RESERVED3                        IMXRT_XBARB3(XBAR_INPUT, 3)    /* XBARB3_IN3 input is reserved. */
#define IMXRT_XBARB3_IN_RESERVED4                        IMXRT_XBARB3(XBAR_INPUT, 4)    /* XBARB3_IN4 input is reserved. */
#define IMXRT_XBARB3_IN_RESERVED5                        IMXRT_XBARB3(XBAR_INPUT, 5)    /* XBARB3_IN5 input is reserved. */
#define IMXRT_XBARB3_IN_ACMP1_OUT                        IMXRT_XBARB3(XBAR_INPUT, 6)    /* ACMP1_OUT output assigned to XBARB3_IN6 input. */
#define IMXRT_XBARB3_IN_ACMP2_OUT                        IMXRT_XBARB3(XBAR_INPUT, 7)    /* ACMP2_OUT output assigned to XBARB3_IN7 input. */
#define IMXRT_XBARB3_IN_ACMP3_OUT                        IMXRT_XBARB3(XBAR_INPUT, 8)    /* ACMP3_OUT output assigned to XBARB3_IN8 input. */
#define IMXRT_XBARB3_IN_ACMP4_OUT                        IMXRT_XBARB3(XBAR_INPUT, 9)    /* ACMP4_OUT output assigned to XBARB3_IN9 input. */
#define IMXRT_XBARB3_IN_RESERVED10                       IMXRT_XBARB3(XBAR_INPUT, 10)   /* XBARB3_IN10 input is reserved. */
#define IMXRT_XBARB3_IN_RESERVED11                       IMXRT_XBARB3(XBAR_INPUT, 11)   /* XBARB3_IN11 input is reserved. */
#define IMXRT_XBARB3_IN_QTIMER3_TMR0_OUT                 IMXRT_XBARB3(XBAR_INPUT, 12)   /* QTIMER3_TMR0_OUTPUT output assigned to XBARB3_IN12 input. */
#define IMXRT_XBARB3_IN_QTIMER3_TMR1_OUT                 IMXRT_XBARB3(XBAR_INPUT, 13)   /* QTIMER3_TMR1_OUTPUT output assigned to XBARB3_IN13 input. */
#define IMXRT_XBARB3_IN_QTIMER3_TMR2_OUT                 IMXRT_XBARB3(XBAR_INPUT, 14)   /* QTIMER3_TMR2_OUTPUT output assigned to XBARB3_IN14 input. */
#define IMXRT_XBARB3_IN_QTIMER3_TMR3_OUT                 IMXRT_XBARB3(XBAR_INPUT, 15)   /* QTIMER3_TMR3_OUTPUT output assigned to XBARB3_IN15 input. */
#define IMXRT_XBARB3_IN_QTIMER4_TMR0_OUT                 IMXRT_XBARB3(XBAR_INPUT, 16)   /* QTIMER4_TMR0_OUTPUT output assigned to XBARB3_IN16 input. */
#define IMXRT_XBARB3_IN_QTIMER4_TMR1_OUT                 IMXRT_XBARB3(XBAR_INPUT, 17)   /* QTIMER4_TMR1_OUTPUT output assigned to XBARB3_IN17 input. */
#define IMXRT_XBARB3_IN_QTIMER4_TMR2_OUT                 IMXRT_XBARB3(XBAR_INPUT, 18)   /* QTIMER4_TMR2_OUTPUT output assigned to XBARB3_IN18 input. */
#define IMXRT_XBARB3_IN_QTIMER4_TMR3_OUT                 IMXRT_XBARB3(XBAR_INPUT, 19)   /* QTIMER4_TMR3_OUTPUT output assigned to XBARB3_IN19 input. */
#define IMXRT_XBARB3_IN_FLEXPWM1_PWM1_OUT_TRIG01         IMXRT_XBARB3(XBAR_INPUT, 20)   /* FLEXPWM1_PWM1_OUT_TRIG0_1 output assigned to XBARB3_IN20 input. */
#define IMXRT_XBARB3_IN_FLEXPWM1_PWM2_OUT_TRIG01         IMXRT_XBARB3(XBAR_INPUT, 21)   /* FLEXPWM1_PWM2_OUT_TRIG0_1 output assigned to XBARB3_IN21 input. */
#define IMXRT_XBARB3_IN_FLEXPWM1_PWM3_OUT_TRIG01         IMXRT_XBARB3(XBAR_INPUT, 22)   /* FLEXPWM1_PWM3_OUT_TRIG0_1 output assigned to XBARB3_IN22 input. */
#define IMXRT_XBARB3_IN_FLEXPWM1_PWM4_OUT_TRIG01         IMXRT_XBARB3(XBAR_INPUT, 23)   /* FLEXPWM1_PWM4_OUT_TRIG0_1 output assigned to XBARB3_IN23 input. */
#define IMXRT_XBARB3_IN_FLEXPWM2_PWM1_OUT_TRIG01         IMXRT_XBARB3(XBAR_INPUT, 24)   /* FLEXPWM2_PWM1_OUT_TRIG0_1 output assigned to XBARB3_IN24 input. */
#define IMXRT_XBARB3_IN_FLEXPWM2_PWM2_OUT_TRIG01         IMXRT_XBARB3(XBAR_INPUT, 25)   /* FLEXPWM2_PWM2_OUT_TRIG0_1 output assigned to XBARB3_IN25 input. */
#define IMXRT_XBARB3_IN_FLEXPWM2_PWM3_OUT_TRIG01         IMXRT_XBARB3(XBAR_INPUT, 26)   /* FLEXPWM2_PWM3_OUT_TRIG0_1 output assigned to XBARB3_IN26 input. */
#define IMXRT_XBARB3_IN_FLEXPWM2_PWM4_OUT_TRIG01         IMXRT_XBARB3(XBAR_INPUT, 27)   /* FLEXPWM2_PWM4_OUT_TRIG0_1 output assigned to XBARB3_IN27 input. */
#define IMXRT_XBARB3_IN_FLEXPWM3_PWM1_OUT_TRIG01         IMXRT_XBARB3(XBAR_INPUT, 28)   /* FLEXPWM3_PWM1_OUT_TRIG0_1 output assigned to XBARB3_IN28 input. */
#define IMXRT_XBARB3_IN_FLEXPWM3_PWM2_OUT_TRIG01         IMXRT_XBARB3(XBAR_INPUT, 29)   /* FLEXPWM3_PWM2_OUT_TRIG0_1 output assigned to XBARB3_IN29 input. */
#define IMXRT_XBARB3_IN_FLEXPWM3_PWM3_OUT_TRIG01         IMXRT_XBARB3(XBAR_INPUT, 30)   /* FLEXPWM3_PWM3_OUT_TRIG0_1 output assigned to XBARB3_IN30 input. */
#define IMXRT_XBARB3_IN_FLEXPWM3_PWM4_OUT_TRIG01         IMXRT_XBARB3(XBAR_INPUT, 31)   /* FLEXPWM3_PWM4_OUT_TRIG0_1 output assigned to XBARB3_IN31 input. */
#define IMXRT_XBARB3_IN_FLEXPWM4_PWM1_OUT_TRIG01         IMXRT_XBARB3(XBAR_INPUT, 32)   /* FLEXPWM4_PWM1_OUT_TRIG0_1 output assigned to XBARB3_IN32 input. */
#define IMXRT_XBARB3_IN_FLEXPWM4_PWM2_OUT_TRIG01         IMXRT_XBARB3(XBAR_INPUT, 33)   /* FLEXPWM4_PWM2_OUT_TRIG0_1 output assigned to XBARB3_IN33 input. */
#define IMXRT_XBARB3_IN_FLEXPWM4_PWM3_OUT_TRIG01         IMXRT_XBARB3(XBAR_INPUT, 34)   /* FLEXPWM4_PWM3_OUT_TRIG0_1 output assigned to XBARB3_IN34 input. */
#define IMXRT_XBARB3_IN_FLEXPWM4_PWM4_OUT_TRIG01         IMXRT_XBARB3(XBAR_INPUT, 35)   /* FLEXPWM4_PWM4_OUT_TRIG0_1 output assigned to XBARB3_IN35 input. */
#define IMXRT_XBARB3_IN_PIT_TRIGGER0                     IMXRT_XBARB3(XBAR_INPUT, 36)   /* PIT_TRIGGER0 output assigned to XBARB3_IN36 input. */
#define IMXRT_XBARB3_IN_PIT_TRIGGER1                     IMXRT_XBARB3(XBAR_INPUT, 37)   /* PIT_TRIGGER1 output assigned to XBARB3_IN37 input. */
#define IMXRT_XBARB3_IN_ADC_ETC_XBAR0_COCO0              IMXRT_XBARB3(XBAR_INPUT, 38)   /* ADC_ETC_XBAR0_COCO0 output assigned to XBARB3_IN38 input. */
#define IMXRT_XBARB3_IN_ADC_ETC_XBAR0_COCO1              IMXRT_XBARB3(XBAR_INPUT, 39)   /* ADC_ETC_XBAR0_COCO1 output assigned to XBARB3_IN39 input. */
#define IMXRT_XBARB3_IN_ADC_ETC_XBAR0_COCO2              IMXRT_XBARB3(XBAR_INPUT, 40)   /* ADC_ETC_XBAR0_COCO2 output assigned to XBARB3_IN40 input. */
#define IMXRT_XBARB3_IN_ADC_ETC_XBAR0_COCO3              IMXRT_XBARB3(XBAR_INPUT, 41)   /* ADC_ETC_XBAR0_COCO3 output assigned to XBARB3_IN41 input. */
#define IMXRT_XBARB3_IN_ADC_ETC_XBAR1_COCO0              IMXRT_XBARB3(XBAR_INPUT, 42)   /* ADC_ETC_XBAR1_COCO0 output assigned to XBARB3_IN42 input. */
#define IMXRT_XBARB3_IN_ADC_ETC_XBAR1_COCO1              IMXRT_XBARB3(XBAR_INPUT, 43)   /* ADC_ETC_XBAR1_COCO1 output assigned to XBARB3_IN43 input. */
#define IMXRT_XBARB3_IN_ADC_ETC_XBAR1_COCO2              IMXRT_XBARB3(XBAR_INPUT, 44)   /* ADC_ETC_XBAR1_COCO2 output assigned to XBARB3_IN44 input. */
#define IMXRT_XBARB3_IN_ADC_ETC_XBAR1_COCO3              IMXRT_XBARB3(XBAR_INPUT, 45)   /* ADC_ETC_XBAR1_COCO3 output assigned to XBARB3_IN45 input. */
#define IMXRT_XBARB3_IN_ENC1_POS_MATCH                   IMXRT_XBARB3(XBAR_INPUT, 46)   /* ENC1_POS_MATCH output assigned to XBARB3_IN46 input. */
#define IMXRT_XBARB3_IN_ENC2_POS_MATCH                   IMXRT_XBARB3(XBAR_INPUT, 47)   /* ENC2_POS_MATCH output assigned to XBARB3_IN47 input. */
#define IMXRT_XBARB3_IN_ENC3_POS_MATCH                   IMXRT_XBARB3(XBAR_INPUT, 48)   /* ENC3_POS_MATCH output assigned to XBARB3_IN48 input. */
#define IMXRT_XBARB3_IN_ENC4_POS_MATCH                   IMXRT_XBARB3(XBAR_INPUT, 49)   /* ENC4_POS_MATCH output assigned to XBARB3_IN49 input. */
#define IMXRT_XBARB3_IN_DMA_DONE0                        IMXRT_XBARB3(XBAR_INPUT, 50)   /* DMA_DONE0 output assigned to XBARB3_IN50 input. */
#define IMXRT_XBARB3_IN_DMA_DONE1                        IMXRT_XBARB3(XBAR_INPUT, 51)   /* DMA_DONE1 output assigned to XBARB3_IN51 input. */
#define IMXRT_XBARB3_IN_DMA_DONE2                        IMXRT_XBARB3(XBAR_INPUT, 52)   /* DMA_DONE2 output assigned to XBARB3_IN52 input. */
#define IMXRT_XBARB3_IN_DMA_DONE3                        IMXRT_XBARB3(XBAR_INPUT, 53)   /* DMA_DONE3 output assigned to XBARB3_IN53 input. */
#define IMXRT_XBARB3_IN_DMA_DONE4                        IMXRT_XBARB3(XBAR_INPUT, 54)   /* DMA_DONE4 output assigned to XBARB3_IN54 input. */
#define IMXRT_XBARB3_IN_DMA_DONE5                        IMXRT_XBARB3(XBAR_INPUT, 55)   /* DMA_DONE5 output assigned to XBARB3_IN55 input. */
#define IMXRT_XBARB3_IN_DMA_DONE6                        IMXRT_XBARB3(XBAR_INPUT, 56)   /* DMA_DONE6 output assigned to XBARB3_IN56 input. */
#define IMXRT_XBARB3_IN_DMA_DONE7                        IMXRT_XBARB3(XBAR_INPUT, 57)   /* DMA_DONE7 output assigned to XBARB3_IN57 input. */

/* XBARB3 Mux Output  (M Muxes) *********************************************/

#define IMXRT_XBARB3_OUT_AOI2_IN00_SEL_OFFSET            IMXRT_XBARB3(XBAR_OUTPUT, 0)    /* XBARB3_OUT0 output assigned to AOI2_IN00 */
#define IMXRT_XBARB3_OUT_AOI2_IN01_SEL_OFFSET            IMXRT_XBARB3(XBAR_OUTPUT, 1)    /* XBARB3_OUT1 output assigned to AOI2_IN01 */
#define IMXRT_XBARB3_OUT_AOI2_IN02_SEL_OFFSET            IMXRT_XBARB3(XBAR_OUTPUT, 2)    /* XBARB3_OUT2 output assigned to AOI2_IN02 */
#define IMXRT_XBARB3_OUT_AOI2_IN03_SEL_OFFSET            IMXRT_XBARB3(XBAR_OUTPUT, 3)    /* XBARB3_OUT3 output assigned to AOI2_IN03 */
#define IMXRT_XBARB3_OUT_AOI2_IN04_SEL_OFFSET            IMXRT_XBARB3(XBAR_OUTPUT, 4)    /* XBARB3_OUT4 output assigned to AOI2_IN04 */
#define IMXRT_XBARB3_OUT_AOI2_IN05_SEL_OFFSET            IMXRT_XBARB3(XBAR_OUTPUT, 5)    /* XBARB3_OUT5 output assigned to AOI2_IN05 */
#define IMXRT_XBARB3_OUT_AOI2_IN06_SEL_OFFSET            IMXRT_XBARB3(XBAR_OUTPUT, 6)    /* XBARB3_OUT6 output assigned to AOI2_IN06 */
#define IMXRT_XBARB3_OUT_AOI2_IN07_SEL_OFFSET            IMXRT_XBARB3(XBAR_OUTPUT, 7)    /* XBARB3_OUT7 output assigned to AOI2_IN07 */
#define IMXRT_XBARB3_OUT_AOI2_IN08_SEL_OFFSET            IMXRT_XBARB3(XBAR_OUTPUT, 8)    /* XBARB3_OUT8 output assigned to AOI2_IN08 */
#define IMXRT_XBARB3_OUT_AOI2_IN09_SEL_OFFSET            IMXRT_XBARB3(XBAR_OUTPUT, 9)    /* XBARB3_OUT9 output assigned to AOI2_IN09 */
#define IMXRT_XBARB3_OUT_AOI2_IN10_SEL_OFFSET            IMXRT_XBARB3(XBAR_OUTPUT, 10)   /* XBARB3_OUT10 output assigned to AOI2_IN10 */
#define IMXRT_XBARB3_OUT_AOI2_IN11_SEL_OFFSET            IMXRT_XBARB3(XBAR_OUTPUT, 11)   /* XBARB3_OUT11 output assigned to AOI2_IN11 */
#define IMXRT_XBARB3_OUT_AOI2_IN12_SEL_OFFSET            IMXRT_XBARB3(XBAR_OUTPUT, 12)   /* XBARB3_OUT12 output assigned to AOI2_IN12 */
#define IMXRT_XBARB3_OUT_AOI2_IN13_SEL_OFFSET            IMXRT_XBARB3(XBAR_OUTPUT, 13)   /* XBARB3_OUT13 output assigned to AOI2_IN13 */
#define IMXRT_XBARB3_OUT_AOI2_IN14_SEL_OFFSET            IMXRT_XBARB3(XBAR_OUTPUT, 14)   /* XBARB3_OUT14 output assigned to AOI2_IN14 */
#define IMXRT_XBARB3_OUT_AOI2_IN15_SEL_OFFSET            IMXRT_XBARB3(XBAR_OUTPUT, 15)   /* XBARB3_OUT15 output assigned to AOI2_IN15 */
