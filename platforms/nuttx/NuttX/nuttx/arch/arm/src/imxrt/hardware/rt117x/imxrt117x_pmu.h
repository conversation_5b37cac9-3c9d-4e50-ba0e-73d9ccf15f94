/*********************************************************************************
 * arch/arm/src/imxrt/hardware/rt117x/imxrt117x_pmu.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 *********************************************************************************/

/* Copyright 2022 NXP */

#ifndef __ARCH_ARM_SRC_IMXRT_HARDWARE_RT117X_IMXRT117X_PMU_H
#define __ARCH_ARM_SRC_IMXRT_HARDWARE_RT117X_IMXRT117X_PMU_H

/*********************************************************************************
 * Included Files
 *********************************************************************************/

#include <nuttx/config.h>
#include "hardware/imxrt_memorymap.h"

/*********************************************************************************
 * Pre-processor Definitions
 *********************************************************************************/

/* ANADIG_PMU Register Offsets ***************************************************/
#define IMXRT_ANADIG_PMU_PMU_LDO_PLL_OFFSET                  (0x0500)
#define IMXRT_ANADIG_PMU_PMU_BIAS_CTRL_OFFSET                (0x0550)
#define IMXRT_ANADIG_PMU_PMU_BIAS_CTRL2_OFFSET               (0x0560)
#define IMXRT_ANADIG_PMU_PMU_REF_CTRL_OFFSET                 (0x0570)
#define IMXRT_ANADIG_PMU_PMU_POWER_DETECT_CTRL_OFFSET        (0x0580)
#define IMXRT_ANADIG_PMU_LDO_PLL_ENABLE_SP_OFFSET            (0x0600)
#define IMXRT_ANADIG_PMU_LDO_LPSR_ANA_ENABLE_SP_OFFSET       (0x0610)
#define IMXRT_ANADIG_PMU_LDO_LPSR_ANA_LP_MODE_SP_OFFSET      (0x0620)
#define IMXRT_ANADIG_PMU_LDO_LPSR_ANA_TRACKING_EN_SP_OFFSET  (0x0630)
#define IMXRT_ANADIG_PMU_LDO_LPSR_ANA_BYPASS_EN_SP_OFFSET    (0x0640)
#define IMXRT_ANADIG_PMU_LDO_LPSR_ANA_STBY_EN_SP_OFFSET      (0x0650)
#define IMXRT_ANADIG_PMU_LDO_LPSR_DIG_ENABLE_SP_OFFSET       (0x0660)
#define IMXRT_ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_OFFSET         (0x0670)
#define IMXRT_ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_OFFSET         (0x0680)
#define IMXRT_ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_OFFSET         (0x0690)
#define IMXRT_ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_OFFSET         (0x06a0)
#define IMXRT_ANADIG_PMU_LDO_LPSR_DIG_LP_MODE_SP_OFFSET      (0x06b0)
#define IMXRT_ANADIG_PMU_LDO_LPSR_DIG_TRACKING_EN_SP_OFFSET  (0x06c0)
#define IMXRT_ANADIG_PMU_LDO_LPSR_DIG_BYPASS_EN_SP_OFFSET    (0x06d0)
#define IMXRT_ANADIG_PMU_LDO_LPSR_DIG_STBY_EN_SP_OFFSET      (0x06e0)
#define IMXRT_ANADIG_PMU_BANDGAP_ENABLE_SP_OFFSET            (0x06f0)
#define IMXRT_ANADIG_PMU_FBB_M7_ENABLE_SP_OFFSET             (0x0700)
#define IMXRT_ANADIG_PMU_RBB_SOC_ENABLE_SP_OFFSET            (0x0710)
#define IMXRT_ANADIG_PMU_RBB_LPSR_ENABLE_SP_OFFSET           (0x0720)
#define IMXRT_ANADIG_PMU_BANDGAP_STBY_EN_SP_OFFSET           (0x0730)
#define IMXRT_ANADIG_PMU_PLL_LDO_STBY_EN_SP_OFFSET           (0x0740)
#define IMXRT_ANADIG_PMU_FBB_M7_STBY_EN_SP_OFFSET            (0x0750)
#define IMXRT_ANADIG_PMU_RBB_SOC_STBY_EN_SP_OFFSET           (0x0760)
#define IMXRT_ANADIG_PMU_RBB_LPSR_STBY_EN_SP_OFFSET          (0x0770)
#define IMXRT_ANADIG_PMU_FBB_M7_CONFIGURE_OFFSET             (0x0780)
#define IMXRT_ANADIG_PMU_RBB_LPSR_CONFIGURE_OFFSET           (0x0790)
#define IMXRT_ANADIG_PMU_RBB_SOC_CONFIGURE_OFFSET            (0x07a0)
#define IMXRT_ANADIG_PMU_REFTOP_OTP_TRIM_VALUE_OFFSET        (0x07b0)
#define IMXRT_ANADIG_PMU_LPSR_1P8_LDO_OTP_TRIM_VALUE_OFFSET  (0x07d0)

/* ANADIG_PMU Register Addresses *************************************************/
#define IMXRT_ANADIG_PMU_PMU_LDO_PLL                  (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_PMU_LDO_PLL_OFFSET)
#define IMXRT_ANADIG_PMU_PMU_BIAS_CTRL                (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_PMU_BIAS_CTRL_OFFSET)
#define IMXRT_ANADIG_PMU_PMU_BIAS_CTRL2               (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_PMU_BIAS_CTRL2_OFFSET)
#define IMXRT_ANADIG_PMU_PMU_REF_CTRL                 (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_PMU_REF_CTRL_OFFSET)
#define IMXRT_ANADIG_PMU_PMU_POWER_DETECT_CTRL        (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_PMU_POWER_DETECT_CTRL_OFFSET)
#define IMXRT_ANADIG_PMU_LDO_PLL_ENABLE_SP            (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_LDO_PLL_ENABLE_SP_OFFSET)
#define IMXRT_ANADIG_PMU_LDO_LPSR_ANA_ENABLE_SP       (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_LDO_LPSR_ANA_ENABLE_SP_OFFSET)
#define IMXRT_ANADIG_PMU_LDO_LPSR_ANA_LP_MODE_SP      (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_LDO_LPSR_ANA_LP_MODE_SP_OFFSET)
#define IMXRT_ANADIG_PMU_LDO_LPSR_ANA_TRACKING_EN_SP  (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_LDO_LPSR_ANA_TRACKING_EN_SP_OFFSET)
#define IMXRT_ANADIG_PMU_LDO_LPSR_ANA_BYPASS_EN_SP    (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_LDO_LPSR_ANA_BYPASS_EN_SP_OFFSET)
#define IMXRT_ANADIG_PMU_LDO_LPSR_ANA_STBY_EN_SP      (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_LDO_LPSR_ANA_STBY_EN_SP_OFFSET)
#define IMXRT_ANADIG_PMU_LDO_LPSR_DIG_ENABLE_SP       (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_LDO_LPSR_DIG_ENABLE_SP_OFFSET)
#define IMXRT_ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0         (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_OFFSET)
#define IMXRT_ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1         (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_OFFSET)
#define IMXRT_ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2         (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_OFFSET)
#define IMXRT_ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3         (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_OFFSET)
#define IMXRT_ANADIG_PMU_LDO_LPSR_DIG_LP_MODE_SP      (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_LDO_LPSR_DIG_LP_MODE_SP_OFFSET)
#define IMXRT_ANADIG_PMU_LDO_LPSR_DIG_TRACKING_EN_SP  (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_LDO_LPSR_DIG_TRACKING_EN_SP_OFFSET)
#define IMXRT_ANADIG_PMU_LDO_LPSR_DIG_BYPASS_EN_SP    (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_LDO_LPSR_DIG_BYPASS_EN_SP_OFFSET)
#define IMXRT_ANADIG_PMU_LDO_LPSR_DIG_STBY_EN_SP      (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_LDO_LPSR_DIG_STBY_EN_SP_OFFSET)
#define IMXRT_ANADIG_PMU_BANDGAP_ENABLE_SP            (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_BANDGAP_ENABLE_SP_OFFSET)
#define IMXRT_ANADIG_PMU_FBB_M7_ENABLE_SP             (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_FBB_M7_ENABLE_SP_OFFSET)
#define IMXRT_ANADIG_PMU_RBB_SOC_ENABLE_SP            (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_RBB_SOC_ENABLE_SP_OFFSET)
#define IMXRT_ANADIG_PMU_RBB_LPSR_ENABLE_SP           (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_RBB_LPSR_ENABLE_SP_OFFSET)
#define IMXRT_ANADIG_PMU_BANDGAP_STBY_EN_SP           (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_BANDGAP_STBY_EN_SP_OFFSET)
#define IMXRT_ANADIG_PMU_PLL_LDO_STBY_EN_SP           (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_PLL_LDO_STBY_EN_SP_OFFSET)
#define IMXRT_ANADIG_PMU_FBB_M7_STBY_EN_SP            (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_FBB_M7_STBY_EN_SP_OFFSET)
#define IMXRT_ANADIG_PMU_RBB_SOC_STBY_EN_SP           (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_RBB_SOC_STBY_EN_SP_OFFSET)
#define IMXRT_ANADIG_PMU_RBB_LPSR_STBY_EN_SP          (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_RBB_LPSR_STBY_EN_SP_OFFSET)
#define IMXRT_ANADIG_PMU_FBB_M7_CONFIGURE             (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_FBB_M7_CONFIGURE_OFFSET)
#define IMXRT_ANADIG_PMU_RBB_LPSR_CONFIGURE           (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_RBB_LPSR_CONFIGURE_OFFSET)
#define IMXRT_ANADIG_PMU_RBB_SOC_CONFIGURE            (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_RBB_SOC_CONFIGURE_OFFSET)
#define IMXRT_ANADIG_PMU_REFTOP_OTP_TRIM_VALUE        (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_REFTOP_OTP_TRIM_VALUE_OFFSET)
#define IMXRT_ANADIG_PMU_LPSR_1P8_LDO_OTP_TRIM_VALUE  (IMXRT_ANADIG_BASE + IMXRT_ANADIG_PMU_LPSR_1P8_LDO_OTP_TRIM_VALUE_OFFSET)

/* PMU_LDO_PLL_REGISTER (PMU_LDO_PLL) */
#define ANADIG_PMU_PMU_LDO_PLL_LDO_PLL_ENABLE        (1 << 0)   /* Bit 0: LDO_PLL_ENABLE */
#define ANADIG_PMU_PMU_LDO_PLL_LDO_PLL_CONTROL_MODE  (1 << 1)   /* Bit 1: LDO_PLL_CONTROL_MODE */
#define ANADIG_PMU_PMU_LDO_PLL_LDO_PLL_AI_TOGGLE     (1 << 16)  /* Bit 16: ldo_pll_ai_toggle */
#define ANADIG_PMU_PMU_LDO_PLL_LDO_PLL_AI_BUSY       (1 << 30)  /* Bit 30: ldo_pll_busy */

/* PMU_BIAS_CTRL_REGISTER (PMU_BIAS_CTRL) */
#define ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_SHIFT  (0)        /* Bits 0-13: wb_cfg_1p8 */
#define ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_MASK   (0x1FFF << ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_SHIFT)
#define ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8(n)     (((n) << ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_SHIFT) & ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_MASK)
#define ANADIG_PMU_PMU_BIAS_CTRL_WB_VDD_SEL_1P8    (1 << 14)  /* Bit 14: wb_vdd_sel_1p8 */

#define ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_WELL_SELECT (0x1) /* Bit 0: WELL Select */ 

#define ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_VOLTAGE_THRESHOLD_SHIFT (1)
#define ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_VOLTAGE_THRESHOLD_MASK  (0x1 << ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_VOLTAGE_THRESHOLD_SHIFT)
#define ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_VOLTAGE_THRESHOLD(n)    ((n) << ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_VOLTAGE_THRESHOLD_SHIFT) & ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_VOLTAGE_THRESHOLD_MASK)

#define ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_DRIVE_STRENGTH_SHIFT  (2) /* Bits 2-4: Select size of bias area */
#define ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_DRIVE_STRENGTH_MASK   (0x7 << ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_DRIVE_STRENGTH_SHIFT)
#define ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_DRIVE_STRENGTH(n)     ((n) << ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_DRIVE_STRENGTH_SHIFT) & ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_DRIVE_STRENGTH_MASK)

#define ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_OSCILLATOR_FREQ_SHIFT (5) /* Bits 5:8: Oscillator settings */ 
#define ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_OSCILLATOR_FREQ_MASK  (0xf << ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_OSCILLATOR_FREQ_SHIFT)
#define ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_OSCILLATOR_FREQ(n)    ((n) << ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_OSCILLATOR_FREQ_SHIFT) & ANADIG_PMU_PMU_BIAS_CTRL_WB_CFG_1P8_OSCILLATOR_FREQ_MASK)

/* PMU_BIAS_CTRL2_REGISTER (PMU_BIAS_CTRL2) */
#define ANADIG_PMU_PMU_BIAS_CTRL2_WB_TST_MD_SHIFT         (1)        /* Bits 1-10: TMOD_wb_tst_md_1p8 */
#define ANADIG_PMU_PMU_BIAS_CTRL2_WB_TST_MD_MASK          (0x1FF << ANADIG_PMU_PMU_BIAS_CTRL2_WB_TST_MD_SHIFT)
#define ANADIG_PMU_PMU_BIAS_CTRL2_WB_TST_MD(n)            (((n) << ANADIG_PMU_PMU_BIAS_CTRL2_WB_TST_MD_SHIFT) & ANADIG_PMU_PMU_BIAS_CTRL2_WB_TST_MD_MASK)
#define ANADIG_PMU_PMU_BIAS_CTRL2_WB_PWR_SW_EN_1P8_SHIFT  (10)       /* Bits 10-12: MODSEL_wb_tst_md_1p8 */
#define ANADIG_PMU_PMU_BIAS_CTRL2_WB_PWR_SW_EN_1P8_MASK   (0x7 << ANADIG_PMU_PMU_BIAS_CTRL2_WB_PWR_SW_EN_1P8_SHIFT)
#define ANADIG_PMU_PMU_BIAS_CTRL2_WB_PWR_SW_EN_1P8(n)     (((n) << ANADIG_PMU_PMU_BIAS_CTRL2_WB_PWR_SW_EN_1P8_SHIFT) & ANADIG_PMU_PMU_BIAS_CTRL2_WB_PWR_SW_EN_1P8_MASK)
#define ANADIG_PMU_PMU_BIAS_CTRL2_WB_ADJ_1P8_SHIFT        (13)       /* Bits 13-20: wb_adj_1p8 */
#define ANADIG_PMU_PMU_BIAS_CTRL2_WB_ADJ_1P8_MASK         (0xFF << ANADIG_PMU_PMU_BIAS_CTRL2_WB_ADJ_1P8_SHIFT)
#define ANADIG_PMU_PMU_BIAS_CTRL2_WB_ADJ_1P8(n)           (((n) << ANADIG_PMU_PMU_BIAS_CTRL2_WB_ADJ_1P8_SHIFT) & ANADIG_PMU_PMU_BIAS_CTRL2_WB_ADJ_1P8_MASK)
#define ANADIG_PMU_PMU_BIAS_CTRL2_FBB_M7_CONTROL_MODE     (1 << 21)  /* Bit 21: FBB_M7_CONTROL_MODE */
#define ANADIG_PMU_PMU_BIAS_CTRL2_RBB_SOC_CONTROL_MODE    (1 << 22)  /* Bit 22: RBB_SOC_CONTROL_MODE */
#define ANADIG_PMU_PMU_BIAS_CTRL2_RBB_LPSR_CONTROL_MODE   (1 << 23)  /* Bit 23: RBB_LPSR_CONTROL_MODE */
#define ANADIG_PMU_PMU_BIAS_CTRL2_WB_EN                   (1 << 24)  /* Bit 24: wb_en */
#define ANADIG_PMU_PMU_BIAS_CTRL2_WB_TST_DIG_OUT          (1 << 25)  /* Bit 25: Digital output */
#define ANADIG_PMU_PMU_BIAS_CTRL2_WB_OK                   (1 << 26)  /* Bit 26: Digital Output pin. */

/* PMU_REF_CTRL_REGISTER (PMU_REF_CTRL) */
#define ANADIG_PMU_PMU_REF_CTRL_REF_AI_TOGGLE          (1 << 0)  /* Bit 0: ref_ai_toggle */
#define ANADIG_PMU_PMU_REF_CTRL_REF_AI_BUSY            (1 << 1)  /* Bit 1: ref_ai_busy */
#define ANADIG_PMU_PMU_REF_CTRL_REF_ENABLE             (1 << 2)  /* Bit 2: REF_ENABLE */
#define ANADIG_PMU_PMU_REF_CTRL_REF_CONTROL_MODE       (1 << 3)  /* Bit 3: REF_CONTROL_MODE */
#define ANADIG_PMU_PMU_REF_CTRL_EN_PLL_VOL_REF_BUFFER  (1 << 4)  /* Bit 4: en_pll_vol_ref_buffer */

/* PMU_POWER_DETECT_CTRL_REGISTER (PMU_POWER_DETECT_CTRL) */
#define ANADIG_PMU_PMU_POWER_DETECT_CTRL_CKGB_LPSR1P0  (1 << 8)  /* Bit 8: ckgb_lpsr1p0 */

/* LDO_PLL_ENABLE_SP_REGISTER (LDO_PLL_ENABLE_SP) */
#define ANADIG_PMU_LDO_PLL_ENABLE_SP_ON_OFF_SETPOINT0   (1 << 0)   /* Bit 0: ON_OFF_SETPOINT0 */
#define ANADIG_PMU_LDO_PLL_ENABLE_SP_ON_OFF_SETPOINT1   (1 << 1)   /* Bit 1: ON_OFF_SETPOINT1 */
#define ANADIG_PMU_LDO_PLL_ENABLE_SP_ON_OFF_SETPOINT2   (1 << 2)   /* Bit 2: ON_OFF_SETPOINT2 */
#define ANADIG_PMU_LDO_PLL_ENABLE_SP_ON_OFF_SETPOINT3   (1 << 3)   /* Bit 3: ON_OFF_SETPOINT3 */
#define ANADIG_PMU_LDO_PLL_ENABLE_SP_ON_OFF_SETPOINT4   (1 << 4)   /* Bit 4: ON_OFF_SETPOINT4 */
#define ANADIG_PMU_LDO_PLL_ENABLE_SP_ON_OFF_SETPOINT5   (1 << 5)   /* Bit 5: ON_OFF_SETPOINT5 */
#define ANADIG_PMU_LDO_PLL_ENABLE_SP_ON_OFF_SETPOINT6   (1 << 6)   /* Bit 6: ON_OFF_SETPOINT6 */
#define ANADIG_PMU_LDO_PLL_ENABLE_SP_ON_OFF_SETPOINT7   (1 << 7)   /* Bit 7: ON_OFF_SETPOINT7 */
#define ANADIG_PMU_LDO_PLL_ENABLE_SP_ON_OFF_SETPOINT8   (1 << 8)   /* Bit 8: ON_OFF_SETPOINT8 */
#define ANADIG_PMU_LDO_PLL_ENABLE_SP_ON_OFF_SETPOINT9   (1 << 9)   /* Bit 9: ON_OFF_SETPOINT9 */
#define ANADIG_PMU_LDO_PLL_ENABLE_SP_ON_OFF_SETPOINT10  (1 << 10)  /* Bit 10: ON_OFF_SETPOINT10 */
#define ANADIG_PMU_LDO_PLL_ENABLE_SP_ON_OFF_SETPOINT11  (1 << 11)  /* Bit 11: ON_OFF_SETPOINT11 */
#define ANADIG_PMU_LDO_PLL_ENABLE_SP_ON_OFF_SETPOINT12  (1 << 12)  /* Bit 12: ON_OFF_SETPOINT12 */
#define ANADIG_PMU_LDO_PLL_ENABLE_SP_ON_OFF_SETPOINT13  (1 << 13)  /* Bit 13: ON_OFF_SETPOINT13 */
#define ANADIG_PMU_LDO_PLL_ENABLE_SP_ON_OFF_SETPOINT14  (1 << 14)  /* Bit 14: ON_OFF_SETPOINT14 */
#define ANADIG_PMU_LDO_PLL_ENABLE_SP_ON_OFF_SETPOINT15  (1 << 15)  /* Bit 15: ON_OFF_SETPOINT15 */

/* LDO_LPSR_ANA_ENABLE_SP_REGISTER (LDO_LPSR_ANA_ENABLE_SP) */
#define ANADIG_PMU_LDO_LPSR_ANA_ENABLE_SP_ON_OFF_SETPOINT0   (1 << 0)   /* Bit 0: ON_OFF_SETPOINT0 */
#define ANADIG_PMU_LDO_LPSR_ANA_ENABLE_SP_ON_OFF_SETPOINT1   (1 << 1)   /* Bit 1: ON_OFF_SETPOINT1 */
#define ANADIG_PMU_LDO_LPSR_ANA_ENABLE_SP_ON_OFF_SETPOINT2   (1 << 2)   /* Bit 2: ON_OFF_SETPOINT2 */
#define ANADIG_PMU_LDO_LPSR_ANA_ENABLE_SP_ON_OFF_SETPOINT3   (1 << 3)   /* Bit 3: ON_OFF_SETPOINT3 */
#define ANADIG_PMU_LDO_LPSR_ANA_ENABLE_SP_ON_OFF_SETPOINT4   (1 << 4)   /* Bit 4: ON_OFF_SETPOINT4 */
#define ANADIG_PMU_LDO_LPSR_ANA_ENABLE_SP_ON_OFF_SETPOINT5   (1 << 5)   /* Bit 5: ON_OFF_SETPOINT5 */
#define ANADIG_PMU_LDO_LPSR_ANA_ENABLE_SP_ON_OFF_SETPOINT6   (1 << 6)   /* Bit 6: ON_OFF_SETPOINT6 */
#define ANADIG_PMU_LDO_LPSR_ANA_ENABLE_SP_ON_OFF_SETPOINT7   (1 << 7)   /* Bit 7: ON_OFF_SETPOINT7 */
#define ANADIG_PMU_LDO_LPSR_ANA_ENABLE_SP_ON_OFF_SETPOINT8   (1 << 8)   /* Bit 8: ON_OFF_SETPOINT8 */
#define ANADIG_PMU_LDO_LPSR_ANA_ENABLE_SP_ON_OFF_SETPOINT9   (1 << 9)   /* Bit 9: ON_OFF_SETPOINT9 */
#define ANADIG_PMU_LDO_LPSR_ANA_ENABLE_SP_ON_OFF_SETPOINT10  (1 << 10)  /* Bit 10: ON_OFF_SETPOINT10 */
#define ANADIG_PMU_LDO_LPSR_ANA_ENABLE_SP_ON_OFF_SETPOINT11  (1 << 11)  /* Bit 11: ON_OFF_SETPOINT11 */
#define ANADIG_PMU_LDO_LPSR_ANA_ENABLE_SP_ON_OFF_SETPOINT12  (1 << 12)  /* Bit 12: ON_OFF_SETPOINT12 */
#define ANADIG_PMU_LDO_LPSR_ANA_ENABLE_SP_ON_OFF_SETPOINT13  (1 << 13)  /* Bit 13: ON_OFF_SETPOINT13 */
#define ANADIG_PMU_LDO_LPSR_ANA_ENABLE_SP_ON_OFF_SETPOINT14  (1 << 14)  /* Bit 14: ON_OFF_SETPOINT14 */
#define ANADIG_PMU_LDO_LPSR_ANA_ENABLE_SP_ON_OFF_SETPOINT15  (1 << 15)  /* Bit 15: ON_OFF_SETPOINT15 */

/* LDO_LPSR_ANA_LP_MODE_SP_REGISTER (LDO_LPSR_ANA_LP_MODE_SP) */
#define ANADIG_PMU_LDO_LPSR_ANA_LP_MODE_SP_LP_MODE_SETPOINT0   (1 << 0)   /* Bit 0: LP_MODE_SETPOINT0 */
#define ANADIG_PMU_LDO_LPSR_ANA_LP_MODE_SP_LP_MODE_SETPOINT1   (1 << 1)   /* Bit 1: LP_MODE_SETPOINT1 */
#define ANADIG_PMU_LDO_LPSR_ANA_LP_MODE_SP_LP_MODE_SETPONIT2   (1 << 2)   /* Bit 2: LP_MODE_SETPOINT2 */
#define ANADIG_PMU_LDO_LPSR_ANA_LP_MODE_SP_LP_MODE_SETPONIT3   (1 << 3)   /* Bit 3: LP_MODE_SETPOINT3 */
#define ANADIG_PMU_LDO_LPSR_ANA_LP_MODE_SP_LP_MODE_SETPONIT4   (1 << 4)   /* Bit 4: LP_MODE_SETPOINT4 */
#define ANADIG_PMU_LDO_LPSR_ANA_LP_MODE_SP_LP_MODE_SETPONIT5   (1 << 5)   /* Bit 5: LP_MODE_SETPOINT5 */
#define ANADIG_PMU_LDO_LPSR_ANA_LP_MODE_SP_LP_MODE_SETPONIT6   (1 << 6)   /* Bit 6: LP_MODE_SETPOINT6 */
#define ANADIG_PMU_LDO_LPSR_ANA_LP_MODE_SP_LP_MODE_SETPONIT7   (1 << 7)   /* Bit 7: LP_MODE_SETPOINT7 */
#define ANADIG_PMU_LDO_LPSR_ANA_LP_MODE_SP_LP_MODE_SETPONIT8   (1 << 8)   /* Bit 8: LP_MODE_SETPOINT8 */
#define ANADIG_PMU_LDO_LPSR_ANA_LP_MODE_SP_LP_MODE_SETPONIT9   (1 << 9)   /* Bit 9: LP_MODE_SETPOINT9 */
#define ANADIG_PMU_LDO_LPSR_ANA_LP_MODE_SP_LP_MODE_SETPONIT10  (1 << 10)  /* Bit 10: LP_MODE_SETPOINT10 */
#define ANADIG_PMU_LDO_LPSR_ANA_LP_MODE_SP_LP_MODE_SETPONIT11  (1 << 11)  /* Bit 11: LP_MODE_SETPOINT11 */
#define ANADIG_PMU_LDO_LPSR_ANA_LP_MODE_SP_LP_MODE_SETPONIT12  (1 << 12)  /* Bit 12: LP_MODE_SETPOINT12 */
#define ANADIG_PMU_LDO_LPSR_ANA_LP_MODE_SP_LP_MODE_SETPONIT13  (1 << 13)  /* Bit 13: LP_MODE_SETPOINT13 */
#define ANADIG_PMU_LDO_LPSR_ANA_LP_MODE_SP_LP_MODE_SETPONIT14  (1 << 14)  /* Bit 14: LP_MODE_SETPOINT14 */
#define ANADIG_PMU_LDO_LPSR_ANA_LP_MODE_SP_LP_MODE_SETPONIT15  (1 << 15)  /* Bit 15: LP_MODE_SETPOINT15 */

/* LDO_LPSR_ANA_TRACKING_EN_SP_REGISTER (LDO_LPSR_ANA_TRACKING_EN_SP) */
#define ANADIG_PMU_LDO_LPSR_ANA_TRACKING_EN_SP_TRACKING_EN_SETPOINT0   (1 << 0)   /* Bit 0: TRACKING_EN_SETPOINT0 */
#define ANADIG_PMU_LDO_LPSR_ANA_TRACKING_EN_SP_TRACKING_EN_SETPOINT1   (1 << 1)   /* Bit 1: TRACKING_EN_SETPOINT1 */
#define ANADIG_PMU_LDO_LPSR_ANA_TRACKING_EN_SP_TRACKING_EN_SETPOINT2   (1 << 2)   /* Bit 2: TRACKING_EN_SETPOINT2 */
#define ANADIG_PMU_LDO_LPSR_ANA_TRACKING_EN_SP_TRACKING_EN_SETPOINT3   (1 << 3)   /* Bit 3: TRACKING_EN_SETPOINT3 */
#define ANADIG_PMU_LDO_LPSR_ANA_TRACKING_EN_SP_TRACKING_EN_SETPOINT4   (1 << 4)   /* Bit 4: TRACKING_EN_SETPOINT4 */
#define ANADIG_PMU_LDO_LPSR_ANA_TRACKING_EN_SP_TRACKING_EN_SETPOINT5   (1 << 5)   /* Bit 5: TRACKING_EN_SETPOINT5 */
#define ANADIG_PMU_LDO_LPSR_ANA_TRACKING_EN_SP_TRACKING_EN_SETPOINT6   (1 << 6)   /* Bit 6: TRACKING_EN_SETPOINT6 */
#define ANADIG_PMU_LDO_LPSR_ANA_TRACKING_EN_SP_TRACKING_EN_SETPOINT7   (1 << 7)   /* Bit 7: TRACKING_EN_SETPOINT7 */
#define ANADIG_PMU_LDO_LPSR_ANA_TRACKING_EN_SP_TRACKING_EN_SETPOINT8   (1 << 8)   /* Bit 8: TRACKING_EN_SETPOINT8 */
#define ANADIG_PMU_LDO_LPSR_ANA_TRACKING_EN_SP_TRACKING_EN_SETPOINT9   (1 << 9)   /* Bit 9: TRACKING_EN_SETPOINT9 */
#define ANADIG_PMU_LDO_LPSR_ANA_TRACKING_EN_SP_TRACKING_EN_SETPOINT10  (1 << 10)  /* Bit 10: TRACKING_EN_SETPOINT10 */
#define ANADIG_PMU_LDO_LPSR_ANA_TRACKING_EN_SP_TRACKING_EN_SETPOINT11  (1 << 11)  /* Bit 11: TRACKING_EN_SETPOINT11 */
#define ANADIG_PMU_LDO_LPSR_ANA_TRACKING_EN_SP_TRACKING_EN_SETPOINT12  (1 << 12)  /* Bit 12: TRACKING_EN_SETPOINT12 */
#define ANADIG_PMU_LDO_LPSR_ANA_TRACKING_EN_SP_TRACKING_EN_SETPOINT13  (1 << 13)  /* Bit 13: TRACKING_EN_SETPOINT13 */
#define ANADIG_PMU_LDO_LPSR_ANA_TRACKING_EN_SP_TRACKING_EN_SETPOINT14  (1 << 14)  /* Bit 14: TRACKING_EN_SETPOINT14 */
#define ANADIG_PMU_LDO_LPSR_ANA_TRACKING_EN_SP_TRACKING_EN_SETPOINT15  (1 << 15)  /* Bit 15: TRACKING_EN_SETPOINT15 */

/* LDO_LPSR_ANA_BYPASS_EN_SP_REGISTER (LDO_LPSR_ANA_BYPASS_EN_SP) */
#define ANADIG_PMU_LDO_LPSR_ANA_BYPASS_EN_SP_BYPASS_EN_SETPOINT0   (1 << 0)   /* Bit 0: BYPASS_EN_SETPOINT0 */
#define ANADIG_PMU_LDO_LPSR_ANA_BYPASS_EN_SP_BYPASS_EN_SETPOINT1   (1 << 1)   /* Bit 1: BYPASS_EN_SETPOINT1 */
#define ANADIG_PMU_LDO_LPSR_ANA_BYPASS_EN_SP_BYPASS_EN_SETPOINT2   (1 << 2)   /* Bit 2: BYPASS_EN_SETPOINT2 */
#define ANADIG_PMU_LDO_LPSR_ANA_BYPASS_EN_SP_BYPASS_EN_SETPOINT3   (1 << 3)   /* Bit 3: BYPASS_EN_SETPOINT3 */
#define ANADIG_PMU_LDO_LPSR_ANA_BYPASS_EN_SP_BYPASS_EN_SETPOINT4   (1 << 4)   /* Bit 4: BYPASS_EN_SETPOINT4 */
#define ANADIG_PMU_LDO_LPSR_ANA_BYPASS_EN_SP_BYPASS_EN_SETPOINT5   (1 << 5)   /* Bit 5: BYPASS_EN_SETPOINT5 */
#define ANADIG_PMU_LDO_LPSR_ANA_BYPASS_EN_SP_BYPASS_EN_SETPOINT6   (1 << 6)   /* Bit 6: BYPASS_EN_SETPOINT6 */
#define ANADIG_PMU_LDO_LPSR_ANA_BYPASS_EN_SP_BYPASS_EN_SETPOINT7   (1 << 7)   /* Bit 7: BYPASS_EN_SETPOINT7 */
#define ANADIG_PMU_LDO_LPSR_ANA_BYPASS_EN_SP_BYPASS_EN_SETPOINT8   (1 << 8)   /* Bit 8: BYPASS_EN_SETPOINT */
#define ANADIG_PMU_LDO_LPSR_ANA_BYPASS_EN_SP_BYPASS_EN_SETPOINT9   (1 << 9)   /* Bit 9: BYPASS_EN_SETPOINT9 */
#define ANADIG_PMU_LDO_LPSR_ANA_BYPASS_EN_SP_BYPASS_EN_SETPOINT10  (1 << 10)  /* Bit 10: BYPASS_EN_SETPOINT10 */
#define ANADIG_PMU_LDO_LPSR_ANA_BYPASS_EN_SP_BYPASS_EN_SETPOINT11  (1 << 11)  /* Bit 11: BYPASS_EN_SETPOINT11 */
#define ANADIG_PMU_LDO_LPSR_ANA_BYPASS_EN_SP_BYPASS_EN_SETPOINT12  (1 << 12)  /* Bit 12: BYPASS_EN_SETPOINT12 */
#define ANADIG_PMU_LDO_LPSR_ANA_BYPASS_EN_SP_BYPASS_EN_SETPOINT13  (1 << 13)  /* Bit 13: BYPASS_EN_SETPOINT13 */
#define ANADIG_PMU_LDO_LPSR_ANA_BYPASS_EN_SP_BYPASS_EN_SETPOINT14  (1 << 14)  /* Bit 14: BYPASS_EN_SETPOINT14 */
#define ANADIG_PMU_LDO_LPSR_ANA_BYPASS_EN_SP_BYPASS_EN_SETPOINT15  (1 << 15)  /* Bit 15: BYPASS_EN_SETPOINT15 */

/* LDO_LPSR_ANA_STBY_EN_SP_REGISTER (LDO_LPSR_ANA_STBY_EN_SP) */
#define ANADIG_PMU_LDO_LPSR_ANA_STBY_EN_SP_STBY_EN_SETPOINT0   (1 << 0)   /* Bit 0: STBY_EN_SETPOINT0 */
#define ANADIG_PMU_LDO_LPSR_ANA_STBY_EN_SP_STBY_EN_SETPOINT1   (1 << 1)   /* Bit 1: STBY_EN_SETPOINT1 */
#define ANADIG_PMU_LDO_LPSR_ANA_STBY_EN_SP_STBY_EN_SETPOINT2   (1 << 2)   /* Bit 2: STBY_EN_SETPOINT2 */
#define ANADIG_PMU_LDO_LPSR_ANA_STBY_EN_SP_STBY_EN_SETPOINT3   (1 << 3)   /* Bit 3: STBY_EN_SETPOINT3 */
#define ANADIG_PMU_LDO_LPSR_ANA_STBY_EN_SP_STBY_EN_SETPOINT4   (1 << 4)   /* Bit 4: STBY_EN_SETPOINT4 */
#define ANADIG_PMU_LDO_LPSR_ANA_STBY_EN_SP_STBY_EN_SETPOINT5   (1 << 5)   /* Bit 5: STBY_EN_SETPOINT5 */
#define ANADIG_PMU_LDO_LPSR_ANA_STBY_EN_SP_STBY_EN_SETPOINT6   (1 << 6)   /* Bit 6: STBY_EN_SETPOINT6 */
#define ANADIG_PMU_LDO_LPSR_ANA_STBY_EN_SP_STBY_EN_SETPOINT7   (1 << 7)   /* Bit 7: STBY_EN_SETPOINT7 */
#define ANADIG_PMU_LDO_LPSR_ANA_STBY_EN_SP_STBY_EN_SETPOINT8   (1 << 8)   /* Bit 8: STBY_EN_SETPOINT8 */
#define ANADIG_PMU_LDO_LPSR_ANA_STBY_EN_SP_STBY_EN_SETPOINT9   (1 << 9)   /* Bit 9: STBY_EN_SETPOINT9 */
#define ANADIG_PMU_LDO_LPSR_ANA_STBY_EN_SP_STBY_EN_SETPOINT10  (1 << 10)  /* Bit 10: STBY_EN_SETPOINT10 */
#define ANADIG_PMU_LDO_LPSR_ANA_STBY_EN_SP_STBY_EN_SETPOINT11  (1 << 11)  /* Bit 11: STBY_EN_SETPOINT11 */
#define ANADIG_PMU_LDO_LPSR_ANA_STBY_EN_SP_STBY_EN_SETPOINT12  (1 << 12)  /* Bit 12: STBY_EN_SETPOINT12 */
#define ANADIG_PMU_LDO_LPSR_ANA_STBY_EN_SP_STBY_EN_SETPOINT13  (1 << 13)  /* Bit 13: STBY_EN_SETPOINT13 */
#define ANADIG_PMU_LDO_LPSR_ANA_STBY_EN_SP_STBY_EN_SETPOINT14  (1 << 14)  /* Bit 14: STBY_EN_SETPOINT14 */
#define ANADIG_PMU_LDO_LPSR_ANA_STBY_EN_SP_STBY_EN_SETPOINT15  (1 << 15)  /* Bit 15: STBY_EN_SETPOINT15 */

/* LDO_LPSR_DIG_ENABLE_SP_REGISTER (LDO_LPSR_DIG_ENABLE_SP) */
#define ANADIG_PMU_LDO_LPSR_DIG_ENABLE_SP_ON_OFF_SETPOINT0   (1 << 0)   /* Bit 0: ON_OFF_SETPOINT0 */
#define ANADIG_PMU_LDO_LPSR_DIG_ENABLE_SP_ON_OFF_SETPOINT1   (1 << 1)   /* Bit 1: ON_OFF_SETPOINT1 */
#define ANADIG_PMU_LDO_LPSR_DIG_ENABLE_SP_ON_OFF_SETPOINT2   (1 << 2)   /* Bit 2: ON_OFF_SETPOINT2 */
#define ANADIG_PMU_LDO_LPSR_DIG_ENABLE_SP_ON_OFF_SETPOINT3   (1 << 3)   /* Bit 3: ON_OFF_SETPOINT3 */
#define ANADIG_PMU_LDO_LPSR_DIG_ENABLE_SP_ON_OFF_SETPOINT4   (1 << 4)   /* Bit 4: ON_OFF_SETPOINT4 */
#define ANADIG_PMU_LDO_LPSR_DIG_ENABLE_SP_ON_OFF_SETPOINT5   (1 << 5)   /* Bit 5: ON_OFF_SETPOINT5 */
#define ANADIG_PMU_LDO_LPSR_DIG_ENABLE_SP_ON_OFF_SETPOINT6   (1 << 6)   /* Bit 6: ON_OFF_SETPOINT6 */
#define ANADIG_PMU_LDO_LPSR_DIG_ENABLE_SP_ON_OFF_SETPOINT7   (1 << 7)   /* Bit 7: ON_OFF_SETPOINT7 */
#define ANADIG_PMU_LDO_LPSR_DIG_ENABLE_SP_ON_OFF_SETPOINT8   (1 << 8)   /* Bit 8: ON_OFF_SETPOINT8 */
#define ANADIG_PMU_LDO_LPSR_DIG_ENABLE_SP_ON_OFF_SETPOINT9   (1 << 9)   /* Bit 9: ON_OFF_SETPOINT9 */
#define ANADIG_PMU_LDO_LPSR_DIG_ENABLE_SP_ON_OFF_SETPOINT10  (1 << 10)  /* Bit 10: ON_OFF_SETPOINT10 */
#define ANADIG_PMU_LDO_LPSR_DIG_ENABLE_SP_ON_OFF_SETPOINT11  (1 << 11)  /* Bit 11: ON_OFF_SETPOINT11 */
#define ANADIG_PMU_LDO_LPSR_DIG_ENABLE_SP_ON_OFF_SETPOINT12  (1 << 12)  /* Bit 12: ON_OFF_SETPOINT12 */
#define ANADIG_PMU_LDO_LPSR_DIG_ENABLE_SP_ON_OFF_SETPOINT13  (1 << 13)  /* Bit 13: ON_OFF_SETPOINT13 */
#define ANADIG_PMU_LDO_LPSR_DIG_ENABLE_SP_ON_OFF_SETPOINT14  (1 << 14)  /* Bit 14: ON_OFF_SETPOINT14 */
#define ANADIG_PMU_LDO_LPSR_DIG_ENABLE_SP_ON_OFF_SETPOINT15  (1 << 15)  /* Bit 15: ON_OFF_SETPOINT15 */

/* LDO_LPSR_DIG_TRG_SP0_REGISTER (LDO_LPSR_DIG_TRG_SP0) */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT0_SHIFT  (0)   /* Bits 0-8: VOLTAGE_SETPOINT0 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT0_MASK   (0xFF << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT0_SHIFT)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT0(n)     (((n) << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT0_SHIFT) & ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT0_MASK)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT1_SHIFT  (8)   /* Bits 8-16: VOLTAGE_SETPOINT1 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT1_MASK   (0xFF << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT1_SHIFT)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT1(n)     (((n) << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT1_SHIFT) & ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT1_MASK)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT2_SHIFT  (16)  /* Bits 16-24: VOLTAGE_SETPOINT2 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT2_MASK   (0xFF << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT2_SHIFT)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT2(n)     (((n) << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT2_SHIFT) & ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT2_MASK)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT3_SHIFT  (24)  /* Bits 24-32: VOLTAGE_SETPOINT3 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT3_MASK   (0xFF << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT3_SHIFT)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT3(n)     (((n) << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT3_SHIFT) & ANADIG_PMU_LDO_LPSR_DIG_TRG_SP0_VOLTAGE_SETPOINT3_MASK)

/* LDO_LPSR_DIG_TRG_SP1_REGISTER (LDO_LPSR_DIG_TRG_SP1) */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT4_SHIFT  (0)   /* Bits 0-8: VOLTAGE_SETPOINT4 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT4_MASK   (0xFF << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT4_SHIFT)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT4(n)     (((n) << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT4_SHIFT) & ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT4_MASK)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT5_SHIFT  (8)   /* Bits 8-16: VOLTAGE_SETPOINT5 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT5_MASK   (0xFF << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT5_SHIFT)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT5(n)     (((n) << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT5_SHIFT) & ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT5_MASK)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT6_SHIFT  (16)  /* Bits 16-24: VOLTAGE_SETPOINT6 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT6_MASK   (0xFF << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT6_SHIFT)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT6(n)     (((n) << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT6_SHIFT) & ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT6_MASK)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT7_SHIFT  (24)  /* Bits 24-32: VOLTAGE_SETPOINT7 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT7_MASK   (0xFF << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT7_SHIFT)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT7(n)     (((n) << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT7_SHIFT) & ANADIG_PMU_LDO_LPSR_DIG_TRG_SP1_VOLTAGE_SETPOINT7_MASK)

/* LDO_LPSR_DIG_TRG_SP2_REGISTER (LDO_LPSR_DIG_TRG_SP2) */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT8_SHIFT   (0)   /* Bits 0-8: VOLTAGE_SETPOINT8 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT8_MASK    (0xFF << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT8_SHIFT)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT8(n)      (((n) << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT8_SHIFT) & ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT8_MASK)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT9_SHIFT   (8)   /* Bits 8-16: VOLTAGE_SETPOINT9 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT9_MASK    (0xFF << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT9_SHIFT)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT9(n)      (((n) << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT9_SHIFT) & ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT9_MASK)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT10_SHIFT  (16)  /* Bits 16-24: VOLTAGE_SETPOINT10 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT10_MASK   (0xFF << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT10_SHIFT)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT10(n)     (((n) << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT10_SHIFT) & ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT10_MASK)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT11_SHIFT  (24)  /* Bits 24-32: VOLTAGE_SETPOINT11 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT11_MASK   (0xFF << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT11_SHIFT)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT11(n)     (((n) << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT11_SHIFT) & ANADIG_PMU_LDO_LPSR_DIG_TRG_SP2_VOLTAGE_SETPOINT11_MASK)

/* LDO_LPSR_DIG_TRG_SP3_REGISTER (LDO_LPSR_DIG_TRG_SP3) */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT12_SHIFT  (0)   /* Bits 0-8: VOLTAGE_SETPOINT12 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT12_MASK   (0xFF << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT12_SHIFT)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT12(n)     (((n) << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT12_SHIFT) & ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT12_MASK)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT13_SHIFT  (8)   /* Bits 8-16: VOLTAGE_SETPOINT13 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT13_MASK   (0xFF << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT13_SHIFT)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT13(n)     (((n) << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT13_SHIFT) & ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT13_MASK)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT14_SHIFT  (16)  /* Bits 16-24: VOLTAGE_SETPOINT14 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT14_MASK   (0xFF << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT14_SHIFT)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT14(n)     (((n) << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT14_SHIFT) & ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT14_MASK)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT15_SHIFT  (24)  /* Bits 24-32: VOLTAGE_SETPOINT15 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT15_MASK   (0xFF << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT15_SHIFT)
#define ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT15(n)     (((n) << ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT15_SHIFT) & ANADIG_PMU_LDO_LPSR_DIG_TRG_SP3_VOLTAGE_SETPOINT15_MASK)

/* LDO_LPSR_DIG_LP_MODE_SP_REGISTER (LDO_LPSR_DIG_LP_MODE_SP) */
#define ANADIG_PMU_LDO_LPSR_DIG_LP_MODE_SP_LP_MODE_SETPOINT0   (1 << 0)   /* Bit 0: LP_MODE_SETPOINT0 */
#define ANADIG_PMU_LDO_LPSR_DIG_LP_MODE_SP_LP_MODE_SETPOINT1   (1 << 1)   /* Bit 1: LP_MODE_SETPOINT1 */
#define ANADIG_PMU_LDO_LPSR_DIG_LP_MODE_SP_LP_MODE_SETPOINT2   (1 << 2)   /* Bit 2: LP_MODE_SETPOINT2 */
#define ANADIG_PMU_LDO_LPSR_DIG_LP_MODE_SP_LP_MODE_SETPOINT3   (1 << 3)   /* Bit 3: LP_MODE_SETPOINT3 */
#define ANADIG_PMU_LDO_LPSR_DIG_LP_MODE_SP_LP_MODE_SETPOINT4   (1 << 4)   /* Bit 4: LP_MODE_SETPOINT4 */
#define ANADIG_PMU_LDO_LPSR_DIG_LP_MODE_SP_LP_MODE_SETPOINT5   (1 << 5)   /* Bit 5: LP_MODE_SETPOINT5 */
#define ANADIG_PMU_LDO_LPSR_DIG_LP_MODE_SP_LP_MODE_SETPOINT6   (1 << 6)   /* Bit 6: LP_MODE_SETPOINT6 */
#define ANADIG_PMU_LDO_LPSR_DIG_LP_MODE_SP_LP_MODE_SETPOINT7   (1 << 7)   /* Bit 7: LP_MODE_SETPOINT7 */
#define ANADIG_PMU_LDO_LPSR_DIG_LP_MODE_SP_LP_MODE_SETPOINT8   (1 << 8)   /* Bit 8: LP_MODE_SETPOINT8 */
#define ANADIG_PMU_LDO_LPSR_DIG_LP_MODE_SP_LP_MODE_SETPOINT9   (1 << 9)   /* Bit 9: LP_MODE_SETPOINT9 */
#define ANADIG_PMU_LDO_LPSR_DIG_LP_MODE_SP_LP_MODE_SETPOINT10  (1 << 10)  /* Bit 10: LP_MODE_SETPOINT10 */
#define ANADIG_PMU_LDO_LPSR_DIG_LP_MODE_SP_LP_MODE_SETPOINT11  (1 << 11)  /* Bit 11: LP_MODE_SETPOINT11 */
#define ANADIG_PMU_LDO_LPSR_DIG_LP_MODE_SP_LP_MODE_SETPOINT12  (1 << 12)  /* Bit 12: LP_MODE_SETPOINT12 */
#define ANADIG_PMU_LDO_LPSR_DIG_LP_MODE_SP_LP_MODE_SETPOINT13  (1 << 13)  /* Bit 13: LP_MODE_SETPOINT13 */
#define ANADIG_PMU_LDO_LPSR_DIG_LP_MODE_SP_LP_MODE_SETPOINT14  (1 << 14)  /* Bit 14: LP_MODE_SETPOINT14 */
#define ANADIG_PMU_LDO_LPSR_DIG_LP_MODE_SP_LP_MODE_SETPOINT15  (1 << 15)  /* Bit 15: LP_MODE_SETPOINT15 */

/* LDO_LPSR_DIG_TRACKING_EN_SP_REGISTER (LDO_LPSR_DIG_TRACKING_EN_SP) */
#define ANADIG_PMU_LDO_LPSR_DIG_TRACKING_EN_SP_TRACKING_EN_SETPOINT0   (1 << 0)   /* Bit 0: TRACKING_EN_SETPOINT0 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRACKING_EN_SP_TRACKING_EN_SETPOINT1   (1 << 1)   /* Bit 1: TRACKING_EN_SETPOINT1 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRACKING_EN_SP_TRACKING_EN_SETPOINT2   (1 << 2)   /* Bit 2: TRACKING_EN_SETPOINT2 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRACKING_EN_SP_TRACKING_EN_SETPOINT3   (1 << 3)   /* Bit 3: TRACKING_EN_SETPOINT3 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRACKING_EN_SP_TRACKING_EN_SETPOINT4   (1 << 4)   /* Bit 4: TRACKING_EN_SETPOINT4 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRACKING_EN_SP_TRACKING_EN_SETPOINT5   (1 << 5)   /* Bit 5: TRACKING_EN_SETPOINT5 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRACKING_EN_SP_TRACKING_EN_SETPOINT6   (1 << 6)   /* Bit 6: TRACKING_EN_SETPOINT6 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRACKING_EN_SP_TRACKING_EN_SETPOINT7   (1 << 7)   /* Bit 7: TRACKING_EN_SETPOINT7 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRACKING_EN_SP_TRACKING_EN_SETPOINT8   (1 << 8)   /* Bit 8: TRACKING_EN_SETPOINT8 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRACKING_EN_SP_TRACKING_EN_SETPOINT9   (1 << 9)   /* Bit 9: TRACKING_EN_SETPOINT9 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRACKING_EN_SP_TRACKING_EN_SETPOINT10  (1 << 10)  /* Bit 10: TRACKING_EN_SETPOINT10 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRACKING_EN_SP_TRACKING_EN_SETPOINT11  (1 << 11)  /* Bit 11: TRACKING_EN_SETPOINT11 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRACKING_EN_SP_TRACKING_EN_SETPOINT12  (1 << 12)  /* Bit 12: TRACKING_EN_SETPOINT12 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRACKING_EN_SP_TRACKING_EN_SETPOINT13  (1 << 13)  /* Bit 13: TRACKING_EN_SETPOINT13 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRACKING_EN_SP_TRACKING_EN_SETPOINT14  (1 << 14)  /* Bit 14: TRACKING_EN_SETPOINT14 */
#define ANADIG_PMU_LDO_LPSR_DIG_TRACKING_EN_SP_TRACKING_EN_SETPOINT15  (1 << 15)  /* Bit 15: TRACKING_EN_SETPOINT15 */

/* LDO_LPSR_DIG_BYPASS_EN_SP_REGISTER (LDO_LPSR_DIG_BYPASS_EN_SP) */
#define ANADIG_PMU_LDO_LPSR_DIG_BYPASS_EN_SP_BYPASS_EN_SETPOINT0   (1 << 0)   /* Bit 0: BYPASS_EN_SETPOINT0 */
#define ANADIG_PMU_LDO_LPSR_DIG_BYPASS_EN_SP_BYPASS_EN_SETPOINT1   (1 << 1)   /* Bit 1: BYPASS_EN_SETPOINT1 */
#define ANADIG_PMU_LDO_LPSR_DIG_BYPASS_EN_SP_BYPASS_EN_SETPOINT2   (1 << 2)   /* Bit 2: BYPASS_EN_SETPOINT2 */
#define ANADIG_PMU_LDO_LPSR_DIG_BYPASS_EN_SP_BYPASS_EN_SETPOINT3   (1 << 3)   /* Bit 3: BYPASS_EN_SETPOINT3 */
#define ANADIG_PMU_LDO_LPSR_DIG_BYPASS_EN_SP_BYPASS_EN_SETPOINT4   (1 << 4)   /* Bit 4: BYPASS_EN_SETPOINT4 */
#define ANADIG_PMU_LDO_LPSR_DIG_BYPASS_EN_SP_BYPASS_EN_SETPOINT5   (1 << 5)   /* Bit 5: BYPASS_EN_SETPOINT5 */
#define ANADIG_PMU_LDO_LPSR_DIG_BYPASS_EN_SP_BYPASS_EN_SETPOINT6   (1 << 6)   /* Bit 6: BYPASS_EN_SETPOINT6 */
#define ANADIG_PMU_LDO_LPSR_DIG_BYPASS_EN_SP_BYPASS_EN_SETPOINT7   (1 << 7)   /* Bit 7: BYPASS_EN_SETPOINT7 */
#define ANADIG_PMU_LDO_LPSR_DIG_BYPASS_EN_SP_BYPASS_EN_SETPOINT8   (1 << 8)   /* Bit 8: BYPASS_EN_SETPOINT8 */
#define ANADIG_PMU_LDO_LPSR_DIG_BYPASS_EN_SP_BYPASS_EN_SETPOINT9   (1 << 9)   /* Bit 9: BYPASS_EN_SETPOINT9 */
#define ANADIG_PMU_LDO_LPSR_DIG_BYPASS_EN_SP_BYPASS_EN_SETPOINT10  (1 << 10)  /* Bit 10: BYPASS_EN_SETPOINT10 */
#define ANADIG_PMU_LDO_LPSR_DIG_BYPASS_EN_SP_BYPASS_EN_SETPOINT11  (1 << 11)  /* Bit 11: BYPASS_EN_SETPOINT11 */
#define ANADIG_PMU_LDO_LPSR_DIG_BYPASS_EN_SP_BYPASS_EN_SETPOINT12  (1 << 12)  /* Bit 12: BYPASS_EN_SETPOINT12 */
#define ANADIG_PMU_LDO_LPSR_DIG_BYPASS_EN_SP_BYPASS_EN_SETPOINT13  (1 << 13)  /* Bit 13: BYPASS_EN_SETPOINT13 */
#define ANADIG_PMU_LDO_LPSR_DIG_BYPASS_EN_SP_BYPASS_EN_SETPOINT14  (1 << 14)  /* Bit 14: BYPASS_EN_SETPOINT14 */
#define ANADIG_PMU_LDO_LPSR_DIG_BYPASS_EN_SP_BYPASS_EN_SETPOINT15  (1 << 15)  /* Bit 15: BYPASS_EN_SETPOINT15 */

/* LDO_LPSR_DIG_STBY_EN_SP_REGISTER (LDO_LPSR_DIG_STBY_EN_SP) */
#define ANADIG_PMU_LDO_LPSR_DIG_STBY_EN_SP_STBY_EN_SETPOINT0   (1 << 0)   /* Bit 0: STBY_EN_SETPOINT0 */
#define ANADIG_PMU_LDO_LPSR_DIG_STBY_EN_SP_STBY_EN_SETPOINT1   (1 << 1)   /* Bit 1: STBY_EN_SETPOINT1 */
#define ANADIG_PMU_LDO_LPSR_DIG_STBY_EN_SP_STBY_EN_SETPOINT2   (1 << 2)   /* Bit 2: STBY_EN_SETPOINT2 */
#define ANADIG_PMU_LDO_LPSR_DIG_STBY_EN_SP_STBY_EN_SETPOINT3   (1 << 3)   /* Bit 3: STBY_EN_SETPOINT3 */
#define ANADIG_PMU_LDO_LPSR_DIG_STBY_EN_SP_STBY_EN_SETPOINT4   (1 << 4)   /* Bit 4: STBY_EN_SETPOINT4 */
#define ANADIG_PMU_LDO_LPSR_DIG_STBY_EN_SP_STBY_EN_SETPOINT5   (1 << 5)   /* Bit 5: STBY_EN_SETPOINT5 */
#define ANADIG_PMU_LDO_LPSR_DIG_STBY_EN_SP_STBY_EN_SETPOINT6   (1 << 6)   /* Bit 6: STBY_EN_SETPOINT6 */
#define ANADIG_PMU_LDO_LPSR_DIG_STBY_EN_SP_STBY_EN_SETPOINT7   (1 << 7)   /* Bit 7: STBY_EN_SETPOINT7 */
#define ANADIG_PMU_LDO_LPSR_DIG_STBY_EN_SP_STBY_EN_SETPOINT8   (1 << 8)   /* Bit 8: STBY_EN_SETPOINT8 */
#define ANADIG_PMU_LDO_LPSR_DIG_STBY_EN_SP_STBY_EN_SETPOINT9   (1 << 9)   /* Bit 9: STBY_EN_SETPOINT9 */
#define ANADIG_PMU_LDO_LPSR_DIG_STBY_EN_SP_STBY_EN_SETPOINT10  (1 << 10)  /* Bit 10: STBY_EN_SETPOINT10 */
#define ANADIG_PMU_LDO_LPSR_DIG_STBY_EN_SP_STBY_EN_SETPOINT11  (1 << 11)  /* Bit 11: STBY_EN_SETPOINT11 */
#define ANADIG_PMU_LDO_LPSR_DIG_STBY_EN_SP_STBY_EN_SETPOINT12  (1 << 12)  /* Bit 12: STBY_EN_SETPOINT12 */
#define ANADIG_PMU_LDO_LPSR_DIG_STBY_EN_SP_STBY_EN_SETPOINT13  (1 << 13)  /* Bit 13: STBY_EN_SETPOINT13 */
#define ANADIG_PMU_LDO_LPSR_DIG_STBY_EN_SP_STBY_EN_SETPOINT14  (1 << 14)  /* Bit 14: STBY_EN_SETPOINT14 */
#define ANADIG_PMU_LDO_LPSR_DIG_STBY_EN_SP_STBY_EN_SETPOINT15  (1 << 15)  /* Bit 15: STBY_EN_SETPOINT15 */

/* BANDGAP_ENABLE_SP_REGISTER (BANDGAP_ENABLE_SP) */
#define ANADIG_PMU_BANDGAP_ENABLE_SP_ON_OFF_SETPOINT0   (1 << 0)   /* Bit 0: ON_OFF_SETPOINT0 */
#define ANADIG_PMU_BANDGAP_ENABLE_SP_ON_OFF_SETPOINT1   (1 << 1)   /* Bit 1: ON_OFF_SETPOINT1 */
#define ANADIG_PMU_BANDGAP_ENABLE_SP_ON_OFF_SETPOINT2   (1 << 2)   /* Bit 2: ON_OFF_SETPOINT2 */
#define ANADIG_PMU_BANDGAP_ENABLE_SP_ON_OFF_SETPOINT3   (1 << 3)   /* Bit 3: ON_OFF_SETPOINT3 */
#define ANADIG_PMU_BANDGAP_ENABLE_SP_ON_OFF_SETPOINT4   (1 << 4)   /* Bit 4: ON_OFF_SETPOINT4 */
#define ANADIG_PMU_BANDGAP_ENABLE_SP_ON_OFF_SETPOINT5   (1 << 5)   /* Bit 5: ON_OFF_SETPOINT5 */
#define ANADIG_PMU_BANDGAP_ENABLE_SP_ON_OFF_SETPOINT6   (1 << 6)   /* Bit 6: ON_OFF_SETPOINT5 */
#define ANADIG_PMU_BANDGAP_ENABLE_SP_ON_OFF_SETPOINT7   (1 << 7)   /* Bit 7: ON_OFF_SETPOINT7 */
#define ANADIG_PMU_BANDGAP_ENABLE_SP_ON_OFF_SETPOINT8   (1 << 8)   /* Bit 8: ON_OFF_SETPOINT8 */
#define ANADIG_PMU_BANDGAP_ENABLE_SP_ON_OFF_SETPOINT9   (1 << 9)   /* Bit 9: ON_OFF_SETPOINT9 */
#define ANADIG_PMU_BANDGAP_ENABLE_SP_ON_OFF_SETPOINT10  (1 << 10)  /* Bit 10: ON_OFF_SETPOINT10 */
#define ANADIG_PMU_BANDGAP_ENABLE_SP_ON_OFF_SETPOINT11  (1 << 11)  /* Bit 11: ON_OFF_SETPOINT11 */
#define ANADIG_PMU_BANDGAP_ENABLE_SP_ON_OFF_SETPOINT12  (1 << 12)  /* Bit 12: ON_OFF_SETPOINT12 */
#define ANADIG_PMU_BANDGAP_ENABLE_SP_ON_OFF_SETPOINT13  (1 << 13)  /* Bit 13: ON_OFF_SETPOINT13 */
#define ANADIG_PMU_BANDGAP_ENABLE_SP_ON_OFF_SETPOINT14  (1 << 14)  /* Bit 14: ON_OFF_SETPOINT14 */
#define ANADIG_PMU_BANDGAP_ENABLE_SP_ON_OFF_SETPOINT15  (1 << 15)  /* Bit 15: ON_OFF_SETPOINT15 */

/* FBB_M7_ENABLE_SP_REGISTER (FBB_M7_ENABLE_SP) */
#define ANADIG_PMU_FBB_M7_ENABLE_SP_ON_OFF_SETPOINT0   (1 << 0)   /* Bit 0: ON_OFF_SETPOINT0 */
#define ANADIG_PMU_FBB_M7_ENABLE_SP_ON_OFF_SETPOINT1   (1 << 1)   /* Bit 1: ON_OFF_SETPOINT1 */
#define ANADIG_PMU_FBB_M7_ENABLE_SP_ON_OFF_SETPOINT2   (1 << 2)   /* Bit 2: ON_OFF_SETPOINT2 */
#define ANADIG_PMU_FBB_M7_ENABLE_SP_ON_OFF_SETPOINT3   (1 << 3)   /* Bit 3: ON_OFF_SETPOINT3 */
#define ANADIG_PMU_FBB_M7_ENABLE_SP_ON_OFF_SETPOINT4   (1 << 4)   /* Bit 4: ON_OFF_SETPOINT4 */
#define ANADIG_PMU_FBB_M7_ENABLE_SP_ON_OFF_SETPOINT5   (1 << 5)   /* Bit 5: ON_OFF_SETPOINT5 */
#define ANADIG_PMU_FBB_M7_ENABLE_SP_ON_OFF_SETPOINT6   (1 << 6)   /* Bit 6: ON_OFF_SETPOINT6 */
#define ANADIG_PMU_FBB_M7_ENABLE_SP_ON_OFF_SETPOINT7   (1 << 7)   /* Bit 7: ON_OFF_SETPOINT7 */
#define ANADIG_PMU_FBB_M7_ENABLE_SP_ON_OFF_SETPOINT8   (1 << 8)   /* Bit 8: ON_OFF_SETPOINT8 */
#define ANADIG_PMU_FBB_M7_ENABLE_SP_ON_OFF_SETPOINT9   (1 << 9)   /* Bit 9: ON_OFF_SETPOINT9 */
#define ANADIG_PMU_FBB_M7_ENABLE_SP_ON_OFF_SETPOINT10  (1 << 10)  /* Bit 10: ON_OFF_SETPOINT10 */
#define ANADIG_PMU_FBB_M7_ENABLE_SP_ON_OFF_SETPOINT11  (1 << 11)  /* Bit 11: ON_OFF_SETPOINT11 */
#define ANADIG_PMU_FBB_M7_ENABLE_SP_ON_OFF_SETPOINT12  (1 << 12)  /* Bit 12: ON_OFF_SETPOINT12 */
#define ANADIG_PMU_FBB_M7_ENABLE_SP_ON_OFF_SETPOINT13  (1 << 13)  /* Bit 13: ON_OFF_SETPOINT13 */
#define ANADIG_PMU_FBB_M7_ENABLE_SP_ON_OFF_SETPOINT14  (1 << 14)  /* Bit 14: ON_OFF_SETPOINT14 */
#define ANADIG_PMU_FBB_M7_ENABLE_SP_ON_OFF_SETPOINT15  (1 << 15)  /* Bit 15: ON_OFF_SETPOINT15 */

/* RBB_SOC_ENABLE_SP_REGISTER (RBB_SOC_ENABLE_SP) */
#define ANADIG_PMU_RBB_SOC_ENABLE_SP_ON_OFF_SETPOINT0   (1 << 0)   /* Bit 0: ON_OFF_SETPOINT0 */
#define ANADIG_PMU_RBB_SOC_ENABLE_SP_ON_OFF_SETPOINT1   (1 << 1)   /* Bit 1: ON_OFF_SETPOINT1 */
#define ANADIG_PMU_RBB_SOC_ENABLE_SP_ON_OFF_SETPOINT2   (1 << 2)   /* Bit 2: ON_OFF_SETPOINT2 */
#define ANADIG_PMU_RBB_SOC_ENABLE_SP_ON_OFF_SETPOINT3   (1 << 3)   /* Bit 3: ON_OFF_SETPOINT3 */
#define ANADIG_PMU_RBB_SOC_ENABLE_SP_ON_OFF_SETPOINT4   (1 << 4)   /* Bit 4: ON_OFF_SETPOINT4 */
#define ANADIG_PMU_RBB_SOC_ENABLE_SP_ON_OFF_SETPOINT5   (1 << 5)   /* Bit 5: ON_OFF_SETPOINT5 */
#define ANADIG_PMU_RBB_SOC_ENABLE_SP_ON_OFF_SETPOINT6   (1 << 6)   /* Bit 6: ON_OFF_SETPOINT6 */
#define ANADIG_PMU_RBB_SOC_ENABLE_SP_ON_OFF_SETPOINT7   (1 << 7)   /* Bit 7: ON_OFF_SETPOINT7 */
#define ANADIG_PMU_RBB_SOC_ENABLE_SP_ON_OFF_SETPOINT8   (1 << 8)   /* Bit 8: ON_OFF_SETPOINT8 */
#define ANADIG_PMU_RBB_SOC_ENABLE_SP_ON_OFF_SETPOINT9   (1 << 9)   /* Bit 9: ON_OFF_SETPOINT9 */
#define ANADIG_PMU_RBB_SOC_ENABLE_SP_ON_OFF_SETPOINT10  (1 << 10)  /* Bit 10: ON_OFF_SETPOINT10 */
#define ANADIG_PMU_RBB_SOC_ENABLE_SP_ON_OFF_SETPOINT11  (1 << 11)  /* Bit 11: ON_OFF_SETPOINT11 */
#define ANADIG_PMU_RBB_SOC_ENABLE_SP_ON_OFF_SETPOINT12  (1 << 12)  /* Bit 12: ON_OFF_SETPOINT12 */
#define ANADIG_PMU_RBB_SOC_ENABLE_SP_ON_OFF_SETPOINT13  (1 << 13)  /* Bit 13: ON_OFF_SETPOINT13 */
#define ANADIG_PMU_RBB_SOC_ENABLE_SP_ON_OFF_SETPOINT14  (1 << 14)  /* Bit 14: ON_OFF_SETPOINT14 */
#define ANADIG_PMU_RBB_SOC_ENABLE_SP_ON_OFF_SETPOINT15  (1 << 15)  /* Bit 15: ON_OFF_SETPOINT15 */

/* RBB_LPSR_ENABLE_SP_REGISTER (RBB_LPSR_ENABLE_SP) */
#define ANADIG_PMU_RBB_LPSR_ENABLE_SP_ON_OFF_SETPOINT0   (1 << 0)   /* Bit 0: ON_OFF_SETPOINT0 */
#define ANADIG_PMU_RBB_LPSR_ENABLE_SP_ON_OFF_SETPOINT1   (1 << 1)   /* Bit 1: ON_OFF_SETPOINT1 */
#define ANADIG_PMU_RBB_LPSR_ENABLE_SP_ON_OFF_SETPOINT2   (1 << 2)   /* Bit 2: ON_OFF_SETPOINT2 */
#define ANADIG_PMU_RBB_LPSR_ENABLE_SP_ON_OFF_SETPOINT3   (1 << 3)   /* Bit 3: ON_OFF_SETPOINT3 */
#define ANADIG_PMU_RBB_LPSR_ENABLE_SP_ON_OFF_SETPOINT4   (1 << 4)   /* Bit 4: ON_OFF_SETPOINT4 */
#define ANADIG_PMU_RBB_LPSR_ENABLE_SP_ON_OFF_SETPOINT5   (1 << 5)   /* Bit 5: ON_OFF_SETPOINT5 */
#define ANADIG_PMU_RBB_LPSR_ENABLE_SP_ON_OFF_SETPOINT6   (1 << 6)   /* Bit 6: ON_OFF_SETPOINT6 */
#define ANADIG_PMU_RBB_LPSR_ENABLE_SP_ON_OFF_SETPOINT7   (1 << 7)   /* Bit 7: ON_OFF_SETPOINT7 */
#define ANADIG_PMU_RBB_LPSR_ENABLE_SP_ON_OFF_SETPOINT8   (1 << 8)   /* Bit 8: ON_OFF_SETPOINT8 */
#define ANADIG_PMU_RBB_LPSR_ENABLE_SP_ON_OFF_SETPOINT9   (1 << 9)   /* Bit 9: ON_OFF_SETPOINT9 */
#define ANADIG_PMU_RBB_LPSR_ENABLE_SP_ON_OFF_SETPOINT10  (1 << 10)  /* Bit 10: ON_OFF_SETPOINT10 */
#define ANADIG_PMU_RBB_LPSR_ENABLE_SP_ON_OFF_SETPOINT11  (1 << 11)  /* Bit 11: ON_OFF_SETPOINT11 */
#define ANADIG_PMU_RBB_LPSR_ENABLE_SP_ON_OFF_SETPOINT12  (1 << 12)  /* Bit 12: ON_OFF_SETPOINT12 */
#define ANADIG_PMU_RBB_LPSR_ENABLE_SP_ON_OFF_SETPOINT13  (1 << 13)  /* Bit 13: ON_OFF_SETPOINT13 */
#define ANADIG_PMU_RBB_LPSR_ENABLE_SP_ON_OFF_SETPOINT14  (1 << 14)  /* Bit 14: ON_OFF_SETPOINT14 */
#define ANADIG_PMU_RBB_LPSR_ENABLE_SP_ON_OFF_SETPOINT15  (1 << 15)  /* Bit 15: ON_OFF_SETPOINT15 */

/* BANDGAP_STBY_EN_SP_REGISTER (BANDGAP_STBY_EN_SP) */
#define ANADIG_PMU_BANDGAP_STBY_EN_SP_STBY_EN_SETPOINT0   (1 << 0)   /* Bit 0: STBY_EN_SETPOINT */
#define ANADIG_PMU_BANDGAP_STBY_EN_SP_STBY_EN_SETPOINT1   (1 << 1)   /* Bit 1: STBY_EN_SETPOINT */
#define ANADIG_PMU_BANDGAP_STBY_EN_SP_STBY_EN_SETPOINT2   (1 << 2)   /* Bit 2: STBY_EN_SETPOINT */
#define ANADIG_PMU_BANDGAP_STBY_EN_SP_STBY_EN_SETPOINT3   (1 << 3)   /* Bit 3: STBY_EN_SETPOINT */
#define ANADIG_PMU_BANDGAP_STBY_EN_SP_STBY_EN_SETPOINT4   (1 << 4)   /* Bit 4: STBY_EN_SETPOINT */
#define ANADIG_PMU_BANDGAP_STBY_EN_SP_STBY_EN_SETPOINT5   (1 << 5)   /* Bit 5: STBY_EN_SETPOINT */
#define ANADIG_PMU_BANDGAP_STBY_EN_SP_STBY_EN_SETPOINT6   (1 << 6)   /* Bit 6: STBY_EN_SETPOINT */
#define ANADIG_PMU_BANDGAP_STBY_EN_SP_STBY_EN_SETPOINT7   (1 << 7)   /* Bit 7: STBY_EN_SETPOINT */
#define ANADIG_PMU_BANDGAP_STBY_EN_SP_STBY_EN_SETPOINT8   (1 << 8)   /* Bit 8: STBY_EN_SETPOINT */
#define ANADIG_PMU_BANDGAP_STBY_EN_SP_STBY_EN_SETPOINT9   (1 << 9)   /* Bit 9: STBY_EN_SETPOINT */
#define ANADIG_PMU_BANDGAP_STBY_EN_SP_STBY_EN_SETPOINT10  (1 << 10)  /* Bit 10: STBY_EN_SETPOINT */
#define ANADIG_PMU_BANDGAP_STBY_EN_SP_STBY_EN_SETPOINT11  (1 << 11)  /* Bit 11: STBY_EN_SETPOINT */
#define ANADIG_PMU_BANDGAP_STBY_EN_SP_STBY_EN_SETPOINT12  (1 << 12)  /* Bit 12: STBY_EN_SETPOINT */
#define ANADIG_PMU_BANDGAP_STBY_EN_SP_STBY_EN_SETPOINT13  (1 << 13)  /* Bit 13: STBY_EN_SETPOINT */
#define ANADIG_PMU_BANDGAP_STBY_EN_SP_STBY_EN_SETPOINT14  (1 << 14)  /* Bit 14: STBY_EN_SETPOINT */
#define ANADIG_PMU_BANDGAP_STBY_EN_SP_STBY_EN_SETPOINT15  (1 << 15)  /* Bit 15: STBY_EN_SETPOINT */

/* PLL_LDO_STBY_EN_SP_REGISTER (PLL_LDO_STBY_EN_SP) */
#define ANADIG_PMU_PLL_LDO_STBY_EN_SP_STBY_EN_SETPOINT0   (1 << 0)   /* Bit 0: Standby mode */
#define ANADIG_PMU_PLL_LDO_STBY_EN_SP_STBY_EN_SETPOINT1   (1 << 1)   /* Bit 1: Standby mode */
#define ANADIG_PMU_PLL_LDO_STBY_EN_SP_STBY_EN_SETPOINT2   (1 << 2)   /* Bit 2: Standby mode */
#define ANADIG_PMU_PLL_LDO_STBY_EN_SP_STBY_EN_SETPOINT3   (1 << 3)   /* Bit 3: Standby mode */
#define ANADIG_PMU_PLL_LDO_STBY_EN_SP_STBY_EN_SETPOINT4   (1 << 4)   /* Bit 4: Standby mode */
#define ANADIG_PMU_PLL_LDO_STBY_EN_SP_STBY_EN_SETPOINT5   (1 << 5)   /* Bit 5: Standby mode */
#define ANADIG_PMU_PLL_LDO_STBY_EN_SP_STBY_EN_SETPOINT6   (1 << 6)   /* Bit 6: Standby mode */
#define ANADIG_PMU_PLL_LDO_STBY_EN_SP_STBY_EN_SETPOINT7   (1 << 7)   /* Bit 7: Standby mode */
#define ANADIG_PMU_PLL_LDO_STBY_EN_SP_STBY_EN_SETPOINT8   (1 << 8)   /* Bit 8: Standby mode */
#define ANADIG_PMU_PLL_LDO_STBY_EN_SP_STBY_EN_SETPOINT9   (1 << 9)   /* Bit 9: Standby mode */
#define ANADIG_PMU_PLL_LDO_STBY_EN_SP_STBY_EN_SETPOINT10  (1 << 10)  /* Bit 10: Standby mode */
#define ANADIG_PMU_PLL_LDO_STBY_EN_SP_STBY_EN_SETPOINT11  (1 << 11)  /* Bit 11: Standby mode */
#define ANADIG_PMU_PLL_LDO_STBY_EN_SP_STBY_EN_SETPOINT12  (1 << 12)  /* Bit 12: Standby mode */
#define ANADIG_PMU_PLL_LDO_STBY_EN_SP_STBY_EN_SETPOINT13  (1 << 13)  /* Bit 13: Standby mode */
#define ANADIG_PMU_PLL_LDO_STBY_EN_SP_STBY_EN_SETPOINT14  (1 << 14)  /* Bit 14: Standby mode */
#define ANADIG_PMU_PLL_LDO_STBY_EN_SP_STBY_EN_SETPOINT15  (1 << 15)  /* Bit 15: Standby mode */

/* FBB_M7_STBY_EN_SP_REGISTER (FBB_M7_STBY_EN_SP) */
#define ANADIG_PMU_FBB_M7_STBY_EN_SP_STBY_EN_SETPOINT0   (1 << 0)   /* Bit 0: Standby mode */
#define ANADIG_PMU_FBB_M7_STBY_EN_SP_STBY_EN_SETPOINT1   (1 << 1)   /* Bit 1: Standby mode */
#define ANADIG_PMU_FBB_M7_STBY_EN_SP_STBY_EN_SETPOINT2   (1 << 2)   /* Bit 2: Standby mode */
#define ANADIG_PMU_FBB_M7_STBY_EN_SP_STBY_EN_SETPOINT3   (1 << 3)   /* Bit 3: Standby mode */
#define ANADIG_PMU_FBB_M7_STBY_EN_SP_STBY_EN_SETPOINT4   (1 << 4)   /* Bit 4: Standby mode */
#define ANADIG_PMU_FBB_M7_STBY_EN_SP_STBY_EN_SETPOINT5   (1 << 5)   /* Bit 5: Standby mode */
#define ANADIG_PMU_FBB_M7_STBY_EN_SP_STBY_EN_SETPOINT6   (1 << 6)   /* Bit 6: Standby mode */
#define ANADIG_PMU_FBB_M7_STBY_EN_SP_STBY_EN_SETPOINT7   (1 << 7)   /* Bit 7: Standby mode */
#define ANADIG_PMU_FBB_M7_STBY_EN_SP_STBY_EN_SETPOINT8   (1 << 8)   /* Bit 8: Standby mode */
#define ANADIG_PMU_FBB_M7_STBY_EN_SP_STBY_EN_SETPOINT9   (1 << 9)   /* Bit 9: Standby mode */
#define ANADIG_PMU_FBB_M7_STBY_EN_SP_STBY_EN_SETPOINT10  (1 << 10)  /* Bit 10: Standby mode */
#define ANADIG_PMU_FBB_M7_STBY_EN_SP_STBY_EN_SETPOINT11  (1 << 11)  /* Bit 11: Standby mode */
#define ANADIG_PMU_FBB_M7_STBY_EN_SP_STBY_EN_SETPOINT12  (1 << 12)  /* Bit 12: Standby mode */
#define ANADIG_PMU_FBB_M7_STBY_EN_SP_STBY_EN_SETPOINT13  (1 << 13)  /* Bit 13: Standby mode */
#define ANADIG_PMU_FBB_M7_STBY_EN_SP_STBY_EN_SETPOINT14  (1 << 14)  /* Bit 14: Standby mode */
#define ANADIG_PMU_FBB_M7_STBY_EN_SP_STBY_EN_SETPOINT15  (1 << 15)  /* Bit 15: Standby mode */

/* RBB_SOC_STBY_EN_SP_REGISTER (RBB_SOC_STBY_EN_SP) */
#define ANADIG_PMU_RBB_SOC_STBY_EN_SP_STBY_EN_SETPOINT0   (1 << 0)   /* Bit 0: Standby mode */
#define ANADIG_PMU_RBB_SOC_STBY_EN_SP_STBY_EN_SETPOINT1   (1 << 1)   /* Bit 1: Standby mode */
#define ANADIG_PMU_RBB_SOC_STBY_EN_SP_STBY_EN_SETPOINT2   (1 << 2)   /* Bit 2: Standby mode */
#define ANADIG_PMU_RBB_SOC_STBY_EN_SP_STBY_EN_SETPOINT3   (1 << 3)   /* Bit 3: Standby mode */
#define ANADIG_PMU_RBB_SOC_STBY_EN_SP_STBY_EN_SETPOINT4   (1 << 4)   /* Bit 4: Standby mode */
#define ANADIG_PMU_RBB_SOC_STBY_EN_SP_STBY_EN_SETPOINT5   (1 << 5)   /* Bit 5: Standby mode */
#define ANADIG_PMU_RBB_SOC_STBY_EN_SP_STBY_EN_SETPOINT6   (1 << 6)   /* Bit 6: Standby mode */
#define ANADIG_PMU_RBB_SOC_STBY_EN_SP_STBY_EN_SETPOINT7   (1 << 7)   /* Bit 7: Standby mode */
#define ANADIG_PMU_RBB_SOC_STBY_EN_SP_STBY_EN_SETPOINT8   (1 << 8)   /* Bit 8: Standby mode */
#define ANADIG_PMU_RBB_SOC_STBY_EN_SP_STBY_EN_SETPOINT9   (1 << 9)   /* Bit 9: Standby mode */
#define ANADIG_PMU_RBB_SOC_STBY_EN_SP_STBY_EN_SETPOINT10  (1 << 10)  /* Bit 10: Standby mode */
#define ANADIG_PMU_RBB_SOC_STBY_EN_SP_STBY_EN_SETPOINT11  (1 << 11)  /* Bit 11: Standby mode */
#define ANADIG_PMU_RBB_SOC_STBY_EN_SP_STBY_EN_SETPOINT12  (1 << 12)  /* Bit 12: Standby mode */
#define ANADIG_PMU_RBB_SOC_STBY_EN_SP_STBY_EN_SETPOINT13  (1 << 13)  /* Bit 13: Standby mode */
#define ANADIG_PMU_RBB_SOC_STBY_EN_SP_STBY_EN_SETPOINT14  (1 << 14)  /* Bit 14: Standby mode */
#define ANADIG_PMU_RBB_SOC_STBY_EN_SP_STBY_EN_SETPOINT15  (1 << 15)  /* Bit 15: Standby mode */

/* RBB_LPSR_STBY_EN_SP_REGISTER (RBB_LPSR_STBY_EN_SP) */
#define ANADIG_PMU_RBB_LPSR_STBY_EN_SP_STBY_EN_SETPOINT0   (1 << 0)   /* Bit 0: Standby mode */
#define ANADIG_PMU_RBB_LPSR_STBY_EN_SP_STBY_EN_SETPOINT1   (1 << 1)   /* Bit 1: Standby mode */
#define ANADIG_PMU_RBB_LPSR_STBY_EN_SP_STBY_EN_SETPOINT2   (1 << 2)   /* Bit 2: Standby mode */
#define ANADIG_PMU_RBB_LPSR_STBY_EN_SP_STBY_EN_SETPOINT3   (1 << 3)   /* Bit 3: Standby mode */
#define ANADIG_PMU_RBB_LPSR_STBY_EN_SP_STBY_EN_SETPOINT4   (1 << 4)   /* Bit 4: Standby mode */
#define ANADIG_PMU_RBB_LPSR_STBY_EN_SP_STBY_EN_SETPOINT5   (1 << 5)   /* Bit 5: Standby mode */
#define ANADIG_PMU_RBB_LPSR_STBY_EN_SP_STBY_EN_SETPOINT6   (1 << 6)   /* Bit 6: Standby mode */
#define ANADIG_PMU_RBB_LPSR_STBY_EN_SP_STBY_EN_SETPOINT7   (1 << 7)   /* Bit 7: Standby mode */
#define ANADIG_PMU_RBB_LPSR_STBY_EN_SP_STBY_EN_SETPOINT8   (1 << 8)   /* Bit 8: Standby mode */
#define ANADIG_PMU_RBB_LPSR_STBY_EN_SP_STBY_EN_SETPOINT9   (1 << 9)   /* Bit 9: Standby mode */
#define ANADIG_PMU_RBB_LPSR_STBY_EN_SP_STBY_EN_SETPOINT10  (1 << 10)  /* Bit 10: Standby mode */
#define ANADIG_PMU_RBB_LPSR_STBY_EN_SP_STBY_EN_SETPOINT11  (1 << 11)  /* Bit 11: Standby mode */
#define ANADIG_PMU_RBB_LPSR_STBY_EN_SP_STBY_EN_SETPOINT12  (1 << 12)  /* Bit 12: Standby mode */
#define ANADIG_PMU_RBB_LPSR_STBY_EN_SP_STBY_EN_SETPOINT13  (1 << 13)  /* Bit 13: Standby mode */
#define ANADIG_PMU_RBB_LPSR_STBY_EN_SP_STBY_EN_SETPOINT14  (1 << 14)  /* Bit 14: Standby mode */
#define ANADIG_PMU_RBB_LPSR_STBY_EN_SP_STBY_EN_SETPOINT15  (1 << 15)  /* Bit 15: Standby mode */

/* FBB_M7_CONFIGURE_REGISTER (FBB_M7_CONFIGURE) */
#define ANADIG_PMU_FBB_M7_CONFIGURE_WB_CFG_PW_SHIFT           (0)   /* Bits 0-4: wb_cfg_pw */
#define ANADIG_PMU_FBB_M7_CONFIGURE_WB_CFG_PW_MASK            (0xF << ANADIG_PMU_FBB_M7_CONFIGURE_WB_CFG_PW_SHIFT)
#define ANADIG_PMU_FBB_M7_CONFIGURE_WB_CFG_PW(n)              (((n) << ANADIG_PMU_FBB_M7_CONFIGURE_WB_CFG_PW_SHIFT) & ANADIG_PMU_FBB_M7_CONFIGURE_WB_CFG_PW_MASK)
#define ANADIG_PMU_FBB_M7_CONFIGURE_WB_CFG_NW_SHIFT           (4)   /* Bits 4-8: wb_cfg_nw */
#define ANADIG_PMU_FBB_M7_CONFIGURE_WB_CFG_NW_MASK            (0xF << ANADIG_PMU_FBB_M7_CONFIGURE_WB_CFG_NW_SHIFT)
#define ANADIG_PMU_FBB_M7_CONFIGURE_WB_CFG_NW(n)              (((n) << ANADIG_PMU_FBB_M7_CONFIGURE_WB_CFG_NW_SHIFT) & ANADIG_PMU_FBB_M7_CONFIGURE_WB_CFG_NW_MASK)
#define ANADIG_PMU_FBB_M7_CONFIGURE_OSCILLATOR_BITS_SHIFT     (8)   /* Bits 8-11: oscillator_bits */
#define ANADIG_PMU_FBB_M7_CONFIGURE_OSCILLATOR_BITS_MASK      (0x7 << ANADIG_PMU_FBB_M7_CONFIGURE_OSCILLATOR_BITS_SHIFT)
#define ANADIG_PMU_FBB_M7_CONFIGURE_OSCILLATOR_BITS(n)        (((n) << ANADIG_PMU_FBB_M7_CONFIGURE_OSCILLATOR_BITS_SHIFT) & ANADIG_PMU_FBB_M7_CONFIGURE_OSCILLATOR_BITS_MASK)
#define ANADIG_PMU_FBB_M7_CONFIGURE_REGULATOR_STRENGTH_SHIFT  (11)  /* Bits 11-14: regulator_strength */
#define ANADIG_PMU_FBB_M7_CONFIGURE_REGULATOR_STRENGTH_MASK   (0x7 << ANADIG_PMU_FBB_M7_CONFIGURE_REGULATOR_STRENGTH_SHIFT)
#define ANADIG_PMU_FBB_M7_CONFIGURE_REGULATOR_STRENGTH(n)     (((n) << ANADIG_PMU_FBB_M7_CONFIGURE_REGULATOR_STRENGTH_SHIFT) & ANADIG_PMU_FBB_M7_CONFIGURE_REGULATOR_STRENGTH_MASK)

/* RBB_LPSR_CONFIGURE_REGISTER (RBB_LPSR_CONFIGURE) */
#define ANADIG_PMU_RBB_LPSR_CONFIGURE_WB_CFG_PW_SHIFT           (0)   /* Bits 0-4: wb_cfg_pw */
#define ANADIG_PMU_RBB_LPSR_CONFIGURE_WB_CFG_PW_MASK            (0xF << ANADIG_PMU_RBB_LPSR_CONFIGURE_WB_CFG_PW_SHIFT)
#define ANADIG_PMU_RBB_LPSR_CONFIGURE_WB_CFG_PW(n)              (((n) << ANADIG_PMU_RBB_LPSR_CONFIGURE_WB_CFG_PW_SHIFT) & ANADIG_PMU_RBB_LPSR_CONFIGURE_WB_CFG_PW_MASK)
#define ANADIG_PMU_RBB_LPSR_CONFIGURE_WB_CFG_NW_SHIFT           (4)   /* Bits 4-8: wb_cfg_nw */
#define ANADIG_PMU_RBB_LPSR_CONFIGURE_WB_CFG_NW_MASK            (0xF << ANADIG_PMU_RBB_LPSR_CONFIGURE_WB_CFG_NW_SHIFT)
#define ANADIG_PMU_RBB_LPSR_CONFIGURE_WB_CFG_NW(n)              (((n) << ANADIG_PMU_RBB_LPSR_CONFIGURE_WB_CFG_NW_SHIFT) & ANADIG_PMU_RBB_LPSR_CONFIGURE_WB_CFG_NW_MASK)
#define ANADIG_PMU_RBB_LPSR_CONFIGURE_OSCILLATOR_BITS_SHIFT     (8)   /* Bits 8-11: oscillator_bits */
#define ANADIG_PMU_RBB_LPSR_CONFIGURE_OSCILLATOR_BITS_MASK      (0x7 << ANADIG_PMU_RBB_LPSR_CONFIGURE_OSCILLATOR_BITS_SHIFT)
#define ANADIG_PMU_RBB_LPSR_CONFIGURE_OSCILLATOR_BITS(n)        (((n) << ANADIG_PMU_RBB_LPSR_CONFIGURE_OSCILLATOR_BITS_SHIFT) & ANADIG_PMU_RBB_LPSR_CONFIGURE_OSCILLATOR_BITS_MASK)
#define ANADIG_PMU_RBB_LPSR_CONFIGURE_REGULATOR_STRENGTH_SHIFT  (11)  /* Bits 11-14: regulator_strength */
#define ANADIG_PMU_RBB_LPSR_CONFIGURE_REGULATOR_STRENGTH_MASK   (0x7 << ANADIG_PMU_RBB_LPSR_CONFIGURE_REGULATOR_STRENGTH_SHIFT)
#define ANADIG_PMU_RBB_LPSR_CONFIGURE_REGULATOR_STRENGTH(n)     (((n) << ANADIG_PMU_RBB_LPSR_CONFIGURE_REGULATOR_STRENGTH_SHIFT) & ANADIG_PMU_RBB_LPSR_CONFIGURE_REGULATOR_STRENGTH_MASK)

/* RBB_SOC_CONFIGURE_REGISTER (RBB_SOC_CONFIGURE) */
#define ANADIG_PMU_RBB_SOC_CONFIGURE_WB_CFG_PW_SHIFT           (0)   /* Bits 0-4: wb_cfg_pw */
#define ANADIG_PMU_RBB_SOC_CONFIGURE_WB_CFG_PW_MASK            (0xF << ANADIG_PMU_RBB_SOC_CONFIGURE_WB_CFG_PW_SHIFT)
#define ANADIG_PMU_RBB_SOC_CONFIGURE_WB_CFG_PW(n)              (((n) << ANADIG_PMU_RBB_SOC_CONFIGURE_WB_CFG_PW_SHIFT) & ANADIG_PMU_RBB_SOC_CONFIGURE_WB_CFG_PW_MASK)
#define ANADIG_PMU_RBB_SOC_CONFIGURE_WB_CFG_NW_SHIFT           (4)   /* Bits 4-8: wb_cfg_nw */
#define ANADIG_PMU_RBB_SOC_CONFIGURE_WB_CFG_NW_MASK            (0xF << ANADIG_PMU_RBB_SOC_CONFIGURE_WB_CFG_NW_SHIFT)
#define ANADIG_PMU_RBB_SOC_CONFIGURE_WB_CFG_NW(n)              (((n) << ANADIG_PMU_RBB_SOC_CONFIGURE_WB_CFG_NW_SHIFT) & ANADIG_PMU_RBB_SOC_CONFIGURE_WB_CFG_NW_MASK)
#define ANADIG_PMU_RBB_SOC_CONFIGURE_OSCILLATOR_BITS_SHIFT     (8)   /* Bits 8-11: oscillator_bits */
#define ANADIG_PMU_RBB_SOC_CONFIGURE_OSCILLATOR_BITS_MASK      (0x7 << ANADIG_PMU_RBB_SOC_CONFIGURE_OSCILLATOR_BITS_SHIFT)
#define ANADIG_PMU_RBB_SOC_CONFIGURE_OSCILLATOR_BITS(n)        (((n) << ANADIG_PMU_RBB_SOC_CONFIGURE_OSCILLATOR_BITS_SHIFT) & ANADIG_PMU_RBB_SOC_CONFIGURE_OSCILLATOR_BITS_MASK)
#define ANADIG_PMU_RBB_SOC_CONFIGURE_REGULATOR_STRENGTH_SHIFT  (11)  /* Bits 11-14: regulator_strength */
#define ANADIG_PMU_RBB_SOC_CONFIGURE_REGULATOR_STRENGTH_MASK   (0x7 << ANADIG_PMU_RBB_SOC_CONFIGURE_REGULATOR_STRENGTH_SHIFT)
#define ANADIG_PMU_RBB_SOC_CONFIGURE_REGULATOR_STRENGTH(n)     (((n) << ANADIG_PMU_RBB_SOC_CONFIGURE_REGULATOR_STRENGTH_SHIFT) & ANADIG_PMU_RBB_SOC_CONFIGURE_REGULATOR_STRENGTH_MASK)

/* REFTOP_OTP_TRIM_VALUE_REGISTER (REFTOP_OTP_TRIM_VALUE) */
#define ANADIG_PMU_REFTOP_OTP_TRIM_VALUE_REFTOP_IBZTCADJ_SHIFT  (0)       /* Bits 0-3: REFTOP_IBZTCADJ */
#define ANADIG_PMU_REFTOP_OTP_TRIM_VALUE_REFTOP_IBZTCADJ_MASK   (0x7 << ANADIG_PMU_REFTOP_OTP_TRIM_VALUE_REFTOP_IBZTCADJ_SHIFT)
#define ANADIG_PMU_REFTOP_OTP_TRIM_VALUE_REFTOP_IBZTCADJ(n)     (((n) << ANADIG_PMU_REFTOP_OTP_TRIM_VALUE_REFTOP_IBZTCADJ_SHIFT) & ANADIG_PMU_REFTOP_OTP_TRIM_VALUE_REFTOP_IBZTCADJ_MASK)
#define ANADIG_PMU_REFTOP_OTP_TRIM_VALUE_REFTOP_VBGADJ_SHIFT    (3)       /* Bits 3-6: REFTOP_VBGADJ */
#define ANADIG_PMU_REFTOP_OTP_TRIM_VALUE_REFTOP_VBGADJ_MASK     (0x7 << ANADIG_PMU_REFTOP_OTP_TRIM_VALUE_REFTOP_VBGADJ_SHIFT)
#define ANADIG_PMU_REFTOP_OTP_TRIM_VALUE_REFTOP_VBGADJ(n)       (((n) << ANADIG_PMU_REFTOP_OTP_TRIM_VALUE_REFTOP_VBGADJ_SHIFT) & ANADIG_PMU_REFTOP_OTP_TRIM_VALUE_REFTOP_VBGADJ_MASK)
#define ANADIG_PMU_REFTOP_OTP_TRIM_VALUE_REFTOP_TRIM_EN         (1 << 6)  /* Bit 6: REFTOP_TRIM_EN */

/* LPSR_1P8_LDO_OTP_TRIM_VALUE_REGISTER (LPSR_1P8_LDO_OTP_TRIM_VALUE) */
#define ANADIG_PMU_LPSR_1P8_LDO_OTP_TRIM_VALUE_LPSR_LDO_1P8_TRIM_SHIFT  (0)       /* Bits 0-2: LPSR_LDO_1P8_TRIM */
#define ANADIG_PMU_LPSR_1P8_LDO_OTP_TRIM_VALUE_LPSR_LDO_1P8_TRIM_MASK   (0x3 << ANADIG_PMU_LPSR_1P8_LDO_OTP_TRIM_VALUE_LPSR_LDO_1P8_TRIM_SHIFT)
#define ANADIG_PMU_LPSR_1P8_LDO_OTP_TRIM_VALUE_LPSR_LDO_1P8_TRIM(n)     (((n) << ANADIG_PMU_LPSR_1P8_LDO_OTP_TRIM_VALUE_LPSR_LDO_1P8_TRIM_SHIFT) & ANADIG_PMU_LPSR_1P8_LDO_OTP_TRIM_VALUE_LPSR_LDO_1P8_TRIM_MASK)
#define ANADIG_PMU_LPSR_1P8_LDO_OTP_TRIM_VALUE_LPSR_LDO_1P8_TRIM_EN     (1 << 2)  /* Bit 2: LPSR_LDO_1P8_TRIM_EN */

#endif /* __ARCH_ARM_SRC_IMXRT_HARDWARE_RT117X_IMXRT117X_PMU_H */
