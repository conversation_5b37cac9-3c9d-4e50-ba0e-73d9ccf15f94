############################################################################
# arch/arm/src/imxrt/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

# Common ARM and Cortex-M7 files

include armv7-m/Make.defs

# Required i.MX RT files

CHIP_CSRCS  = imxrt_allocateheap.c imxrt_start.c imxrt_periphclks.c
CHIP_CSRCS += imxrt_irq.c imxrt_clrpend.c imxrt_gpio.c
CHIP_CSRCS += imxrt_daisy.c imxrt_wdog.c imxrt_serial.c
CHIP_CSRCS += imxrt_xbar.c imxrt_ocotp.c imxrt_lowputc.c

# Configuration-dependent i.MX RT files

ifeq ($(CONFIG_IMXRT_CLOCKCONFIG_VER2),y)
CHIP_CSRCS += imxrt_clockconfig_ver2.c imxrt_pmu.c
else
CHIP_CSRCS += imxrt_clockconfig_ver1.c
endif

ifeq ($(CONFIG_IMXRT_IOMUX_VER2),y)
CHIP_CSRCS += imxrt_iomuxc_ver2.c
else
CHIP_CSRCS += imxrt_iomuxc_ver1.c
endif

ifneq ($(CONFIG_ARCH_IDLE_CUSTOM),y)
CHIP_CSRCS += imxrt_idle.c
endif

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += imxrt_timerisr.c
else
CHIP_CSRCS += imxrt_tickless.c
endif

ifeq ($(CONFIG_IMXRT_GPIO_IRQ),y)
CHIP_CSRCS += imxrt_gpioirq.c
endif

ifeq ($(CONFIG_ARM_MPU),y)
CHIP_CSRCS += imxrt_mpuinit.c
endif

ifeq ($(CONFIG_BUILD_PROTECTED),y)
CHIP_CSRCS += imxrt_userspace.c
endif

ifeq ($(CONFIG_IMXRT_EDMA),y)
CHIP_CSRCS += imxrt_edma.c
endif

ifdef CONFIG_IMXRT_USDHC
CHIP_CSRCS += imxrt_usdhc.c
endif

ifeq ($(CONFIG_IMXRT_LCD),y)
CHIP_CSRCS += imxrt_lcd.c
endif

ifeq ($(CONFIG_IMXRT_FLEXCAN),y)
CHIP_CSRCS += imxrt_flexcan.c
endif

ifeq ($(CONFIG_IMXRT_FLEXIO),y)
CHIP_CSRCS += imxrt_flexio.c
endif

ifeq ($(CONFIG_IMXRT_FLEXPWM),y)
CHIP_CSRCS += imxrt_flexpwm.c
endif

ifeq ($(CONFIG_IMXRT_SNVS_LPSRTC),y)
CHIP_CSRCS += imxrt_lpsrtc.c
CHIP_CSRCS += imxrt_hprtc.c
else ifeq ($(CONFIG_IMXRT_SNVS_HPRTC),y)
CHIP_CSRCS += imxrt_hprtc.c
endif

ifeq ($(CONFIG_RTC_DRIVER),y)
CHIP_CSRCS += imxrt_rtc_lowerhalf.c
endif

ifeq ($(CONFIG_IMXRT_ENET),y)
CHIP_CSRCS += imxrt_enet.c
endif

ifeq ($(CONFIG_IMXRT_LPI2C),y)
CHIP_CSRCS += imxrt_lpi2c.c
endif

ifeq ($(CONFIG_IMXRT_LPSPI),y)
CHIP_CSRCS += imxrt_lpspi.c
endif

ifeq ($(CONFIG_IMXRT_FLEXSPI),y)
CHIP_CSRCS += imxrt_flexspi.c
endif

ifeq ($(CONFIG_IMXRT_ENC),y)
CHIP_CSRCS += imxrt_enc.c
endif

ifeq ($(CONFIG_IMXRT_USBOTG),y)
CHIP_CSRCS += imxrt_ehci.c
endif

ifeq ($(CONFIG_IMXRT_USBDEV),y)
CHIP_CSRCS += imxrt_usbdev.c
endif

ifeq ($(CONFIG_IMXRT_ADC),y)
ifeq ($(CONFIG_IMXRT_ADC_VER2),y)
CHIP_CSRCS += imxrt_adc_ver2.c
else
CHIP_CSRCS += imxrt_adc_ver1.c
endif
endif
