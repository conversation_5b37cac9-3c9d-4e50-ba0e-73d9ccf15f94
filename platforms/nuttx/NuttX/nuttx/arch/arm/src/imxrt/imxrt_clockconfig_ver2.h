/****************************************************************************
 * arch/arm/src/imxrt/imxrt_clockconfig_ver2.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_IMXRT_IMXRT_CLOCKCONFIG_VER2_H
#define __ARCH_ARM_SRC_IMXRT_IMXRT_CLOCKCONFIG_VER2_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include "hardware/imxrt_ccm.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

#define CCM_CLOCK_ROOT_OFFSET(n,m) ((void*)&m - (void*)&n) \
                                 / sizeof(struct ccm_clock_root)

#define IMXRT_CCM_CLOCK_ROOT_REG(n,m) IMXRT_CCM_CR_CTRL( \
                                    CCM_CLOCK_ROOT_OFFSET(n,m))

#define CLOCK_NAME_SHIFT     (3)
#define CLOCK_NAME_MASK      (0x1f << CLOCK_NAME_SHIFT)
#define CLOCK_NAME(n)        (((n) << CLOCK_NAME_SHIFT) & CLOCK_NAME_MASK)

#define ROOT_MUX_SHIFT       (0)
#define ROOT_MUX_MASK        (0x07 << ROOT_MUX_SHIFT)
#define ROOT_MUX(n)          (((n) << ROOT_MUX_SHIFT) & ROOT_MUX_MASK)

#define SYS_PLL1_FREQ        1000000000
#define SYS_PLL2_FREQ         528000000
#define SYS_PLL3_FREQ         480000000

/* M7 */
#define M7_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define M7_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define M7_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define M7_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define M7_CLK_ROOT_PLL_ARM_CLK     ROOT_MUX(4U) | CLOCK_NAME(PLL_ARM_CLK)
#define M7_CLK_ROOT_SYS_PLL1_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1)
#define M7_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL3)
#define M7_CLK_ROOT_VIDEO_PLL_CLK   ROOT_MUX(7U) | CLOCK_NAME(VIDEO_PLL_CLK)

/* M4 */
#define M4_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define M4_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define M4_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define M4_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define M4_CLK_ROOT_SYS_PLL3_PFD3   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_PFD3)
#define M4_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3)
#define M4_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define M4_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL1_DIV5)

/* BUS */
#define BUS_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define BUS_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define BUS_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define BUS_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define BUS_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3)
#define BUS_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define BUS_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define BUS_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* BUS_LPSR */
#define BUS_LPSR_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define BUS_LPSR_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define BUS_LPSR_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define BUS_LPSR_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define BUS_LPSR_CLK_ROOT_SYS_PLL3_PFD3   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_PFD3)
#define BUS_LPSR_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3)
#define BUS_LPSR_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define BUS_LPSR_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL1_DIV5)

/* SEMC */
#define SEMC_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define SEMC_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define SEMC_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define SEMC_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define SEMC_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define SEMC_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL2)
#define SEMC_CLK_ROOT_SYS_PLL2_PFD1   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2_PFD1)
#define SEMC_CLK_ROOT_SYS_PLL3_PFD0   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL3_PFD0)

/* CSSYS */
#define CSSYS_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define CSSYS_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define CSSYS_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define CSSYS_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define CSSYS_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define CSSYS_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define CSSYS_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define CSSYS_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* CSTRACE */
#define CSTRACE_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define CSTRACE_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define CSTRACE_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define CSTRACE_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define CSTRACE_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define CSTRACE_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define CSTRACE_CLK_ROOT_SYS_PLL2_PFD1   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2_PFD1)
#define CSTRACE_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2)

/* M4_SYSTICK */
#define M4_SYSTICK_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define M4_SYSTICK_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define M4_SYSTICK_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define M4_SYSTICK_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define M4_SYSTICK_CLK_ROOT_SYS_PLL3_PFD3   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_PFD3)
#define M4_SYSTICK_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3)
#define M4_SYSTICK_CLK_ROOT_SYS_PLL2_PFD0   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2_PFD0)
#define M4_SYSTICK_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL1_DIV5)

/* M7_SYSTICK */
#define M7_SYSTICK_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define M7_SYSTICK_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define M7_SYSTICK_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define M7_SYSTICK_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define M7_SYSTICK_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL2)
#define M7_SYSTICK_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define M7_SYSTICK_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define M7_SYSTICK_CLK_ROOT_SYS_PLL2_PFD0   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD0)

/* ADC1 */
#define ADC1_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define ADC1_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define ADC1_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define ADC1_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define ADC1_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define ADC1_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define ADC1_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define ADC1_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* ADC2 */
#define ADC2_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define ADC2_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define ADC2_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define ADC2_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define ADC2_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define ADC2_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define ADC2_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define ADC2_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* ACMP */
#define ACMP_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define ACMP_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define ACMP_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define ACMP_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define ACMP_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3)
#define ACMP_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define ACMP_CLK_ROOT_AUDIO_PLL_CLK   ROOT_MUX(6U) | CLOCK_NAME(AUDIO_PLL_CLK)
#define ACMP_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* FLEXIO1 */
#define FLEXIO1_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define FLEXIO1_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define FLEXIO1_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define FLEXIO1_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define FLEXIO1_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define FLEXIO1_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define FLEXIO1_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define FLEXIO1_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* FLEXIO2 */
#define FLEXIO2_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define FLEXIO2_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define FLEXIO2_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define FLEXIO2_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define FLEXIO2_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define FLEXIO2_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define FLEXIO2_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define FLEXIO2_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* GPT1 */
#define GPT1_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define GPT1_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define GPT1_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define GPT1_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define GPT1_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define GPT1_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define GPT1_CLK_ROOT_SYS_PLL3_PFD2   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL3_PFD2)
#define GPT1_CLK_ROOT_SYS_PLL3_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL3_PFD3)

/* GPT2 */
#define GPT2_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define GPT2_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define GPT2_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define GPT2_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define GPT2_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define GPT2_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define GPT2_CLK_ROOT_AUDIO_PLL_CLK   ROOT_MUX(6U) | CLOCK_NAME(AUDIO_PLL_CLK)
#define GPT2_CLK_ROOT_VIDEO_PLL_CLK   ROOT_MUX(7U) | CLOCK_NAME(VIDEO_PLL_CLK)

/* GPT3 */
#define GPT3_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define GPT3_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define GPT3_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define GPT3_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define GPT3_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define GPT3_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define GPT3_CLK_ROOT_AUDIO_PLL_CLK   ROOT_MUX(6U) | CLOCK_NAME(AUDIO_PLL_CLK)
#define GPT3_CLK_ROOT_VIDEO_PLL_CLK   ROOT_MUX(7U) | CLOCK_NAME(VIDEO_PLL_CLK)

/* GPT4 */
#define GPT4_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define GPT4_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define GPT4_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define GPT4_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define GPT4_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define GPT4_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define GPT4_CLK_ROOT_SYS_PLL3_PFD2   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL3_PFD2)
#define GPT4_CLK_ROOT_SYS_PLL3_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL3_PFD3)

/* GPT5 */
#define GPT5_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define GPT5_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define GPT5_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define GPT5_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define GPT5_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define GPT5_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define GPT5_CLK_ROOT_SYS_PLL3_PFD2   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL3_PFD2)
#define GPT5_CLK_ROOT_SYS_PLL3_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL3_PFD3)

/* GPT6 */
#define GPT6_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define GPT6_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define GPT6_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define GPT6_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define GPT6_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define GPT6_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define GPT6_CLK_ROOT_SYS_PLL3_PFD2   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL3_PFD2)
#define GPT6_CLK_ROOT_SYS_PLL3_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL3_PFD3)

/* FLEXSPI1 */
#define FLEXSPI1_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define FLEXSPI1_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define FLEXSPI1_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define FLEXSPI1_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define FLEXSPI1_CLK_ROOT_SYS_PLL3_PFD0   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_PFD0)
#define FLEXSPI1_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL2)
#define FLEXSPI1_CLK_ROOT_SYS_PLL2_PFD2   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2_PFD2)
#define FLEXSPI1_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL3)

/* FLEXSPI2 */
#define FLEXSPI2_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define FLEXSPI2_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define FLEXSPI2_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define FLEXSPI2_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define FLEXSPI2_CLK_ROOT_SYS_PLL3_PFD0   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_PFD0)
#define FLEXSPI2_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL2)
#define FLEXSPI2_CLK_ROOT_SYS_PLL2_PFD2   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2_PFD2)
#define FLEXSPI2_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL3)

/* CAN1 */
#define CAN1_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define CAN1_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define CAN1_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define CAN1_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define CAN1_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define CAN1_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define CAN1_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define CAN1_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* CAN2 */
#define CAN2_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define CAN2_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define CAN2_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define CAN2_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define CAN2_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define CAN2_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define CAN2_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define CAN2_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* CAN3 */
#define CAN3_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define CAN3_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define CAN3_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define CAN3_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define CAN3_CLK_ROOT_SYS_PLL3_PFD3   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_PFD3)
#define CAN3_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3)
#define CAN3_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2_PFD3)
#define CAN3_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL1_DIV5)

/* LPUART1 */
#define LPUART1_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPUART1_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPUART1_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPUART1_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPUART1_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define LPUART1_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define LPUART1_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define LPUART1_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* LPUART2 */
#define LPUART2_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPUART2_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPUART2_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPUART2_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPUART2_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define LPUART2_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define LPUART2_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define LPUART2_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* LPUART3 */
#define LPUART3_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPUART3_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPUART3_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPUART3_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPUART3_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define LPUART3_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define LPUART3_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define LPUART3_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* LPUART4 */
#define LPUART4_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPUART4_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPUART4_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPUART4_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPUART4_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define LPUART4_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define LPUART4_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define LPUART4_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* LPUART5 */
#define LPUART5_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPUART5_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPUART5_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPUART5_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPUART5_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define LPUART5_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define LPUART5_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define LPUART5_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* LPUART6 */
#define LPUART6_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPUART6_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPUART6_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPUART6_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPUART6_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define LPUART6_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define LPUART6_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define LPUART6_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* LPUART7 */
#define LPUART7_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPUART7_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPUART7_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPUART7_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPUART7_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define LPUART7_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define LPUART7_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define LPUART7_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* LPUART8 */
#define LPUART8_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPUART8_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPUART8_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPUART8_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPUART8_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define LPUART8_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define LPUART8_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define LPUART8_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* LPUART9 */
#define LPUART9_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPUART9_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPUART9_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPUART9_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPUART9_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define LPUART9_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define LPUART9_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define LPUART9_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* LPUART10 */
#define LPUART10_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPUART10_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPUART10_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPUART10_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPUART10_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define LPUART10_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define LPUART10_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define LPUART10_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* LPUART11 */
#define LPUART11_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPUART11_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPUART11_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPUART11_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPUART11_CLK_ROOT_SYS_PLL3_PFD3   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_PFD3)
#define LPUART11_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3)
#define LPUART11_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2_PFD3)
#define LPUART11_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL1_DIV5)

/* LPUART12 */
#define LPUART12_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPUART12_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPUART12_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPUART12_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPUART12_CLK_ROOT_SYS_PLL3_PFD3   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_PFD3)
#define LPUART12_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3)
#define LPUART12_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2_PFD3)
#define LPUART12_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL1_DIV5)

/* LPI2C1 */
#define LPI2C1_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPI2C1_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPI2C1_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPI2C1_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPI2C1_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define LPI2C1_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define LPI2C1_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define LPI2C1_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* LPI2C2 */
#define LPI2C2_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPI2C2_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPI2C2_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPI2C2_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPI2C2_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define LPI2C2_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define LPI2C2_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define LPI2C2_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* LPI2C3 */
#define LPI2C3_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPI2C3_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPI2C3_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPI2C3_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPI2C3_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define LPI2C3_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define LPI2C3_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define LPI2C3_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* LPI2C4 */
#define LPI2C4_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPI2C4_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPI2C4_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPI2C4_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPI2C4_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define LPI2C4_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define LPI2C4_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define LPI2C4_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* LPI2C5 */
#define LPI2C5_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPI2C5_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPI2C5_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPI2C5_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPI2C5_CLK_ROOT_SYS_PLL3_PFD3   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_PFD3)
#define LPI2C5_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3)
#define LPI2C5_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2_PFD3)
#define LPI2C5_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL1_DIV5)

/* LPI2C6 */
#define LPI2C6_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPI2C6_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPI2C6_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPI2C6_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPI2C6_CLK_ROOT_SYS_PLL3_PFD3   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_PFD3)
#define LPI2C6_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3)
#define LPI2C6_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2_PFD3)
#define LPI2C6_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL1_DIV5)

/* LPSPI1 */
#define LPSPI1_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPSPI1_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPSPI1_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPSPI1_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPSPI1_CLK_ROOT_SYS_PLL3_PFD2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_PFD2)
#define LPSPI1_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define LPSPI1_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define LPSPI1_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* LPSPI2 */
#define LPSPI2_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPSPI2_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPSPI2_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPSPI2_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPSPI2_CLK_ROOT_SYS_PLL3_PFD2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_PFD2)
#define LPSPI2_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define LPSPI2_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define LPSPI2_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* LPSPI3 */
#define LPSPI3_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPSPI3_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPSPI3_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPSPI3_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPSPI3_CLK_ROOT_SYS_PLL3_PFD2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_PFD2)
#define LPSPI3_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define LPSPI3_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define LPSPI3_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* LPSPI4 */
#define LPSPI4_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPSPI4_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPSPI4_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPSPI4_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPSPI4_CLK_ROOT_SYS_PLL3_PFD2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_PFD2)
#define LPSPI4_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define LPSPI4_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define LPSPI4_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* LPSPI5 */
#define LPSPI5_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPSPI5_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPSPI5_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPSPI5_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPSPI5_CLK_ROOT_SYS_PLL3_PFD3   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_PFD3)
#define LPSPI5_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3)
#define LPSPI5_CLK_ROOT_SYS_PLL3_PFD2   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL3_PFD2)
#define LPSPI5_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL1_DIV5)

/* LPSPI6 */
#define LPSPI6_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LPSPI6_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LPSPI6_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LPSPI6_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LPSPI6_CLK_ROOT_SYS_PLL3_PFD3   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_PFD3)
#define LPSPI6_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3)
#define LPSPI6_CLK_ROOT_SYS_PLL3_PFD2   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL3_PFD2)
#define LPSPI6_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL1_DIV5)

/* EMV1 */
#define EMV1_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define EMV1_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define EMV1_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define EMV1_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define EMV1_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define EMV1_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define EMV1_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define EMV1_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* EMV2 */
#define EMV2_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define EMV2_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define EMV2_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define EMV2_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define EMV2_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define EMV2_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define EMV2_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2)
#define EMV2_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* ENET1 */
#define ENET1_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define ENET1_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define ENET1_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define ENET1_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define ENET1_CLK_ROOT_SYS_PLL1_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL1_DIV2)
#define ENET1_CLK_ROOT_AUDIO_PLL_CLK   ROOT_MUX(5U) | CLOCK_NAME(AUDIO_PLL_CLK)
#define ENET1_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define ENET1_CLK_ROOT_SYS_PLL2_PFD1   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD1)

/* ENET2 */
#define ENET2_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define ENET2_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define ENET2_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define ENET2_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define ENET2_CLK_ROOT_SYS_PLL1_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL1_DIV2)
#define ENET2_CLK_ROOT_AUDIO_PLL_CLK   ROOT_MUX(5U) | CLOCK_NAME(AUDIO_PLL_CLK)
#define ENET2_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define ENET2_CLK_ROOT_SYS_PLL2_PFD1   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD1)

/* ENET_QOS */
#define ENET_QOS_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define ENET_QOS_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define ENET_QOS_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define ENET_QOS_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define ENET_QOS_CLK_ROOT_SYS_PLL1_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL1_DIV2)
#define ENET_QOS_CLK_ROOT_AUDIO_PLL_CLK   ROOT_MUX(5U) | CLOCK_NAME(AUDIO_PLL_CLK)
#define ENET_QOS_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define ENET_QOS_CLK_ROOT_SYS_PLL2_PFD1   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD1)

/* ENET_25M */
#define ENET_25M_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define ENET_25M_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define ENET_25M_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define ENET_25M_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define ENET_25M_CLK_ROOT_SYS_PLL1_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL1_DIV2)
#define ENET_25M_CLK_ROOT_AUDIO_PLL_CLK   ROOT_MUX(5U) | CLOCK_NAME(AUDIO_PLL_CLK)
#define ENET_25M_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define ENET_25M_CLK_ROOT_SYS_PLL2_PFD1   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD1)

/* ENET_TIMER1 */
#define ENET_TIMER1_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define ENET_TIMER1_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define ENET_TIMER1_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define ENET_TIMER1_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define ENET_TIMER1_CLK_ROOT_SYS_PLL1_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL1_DIV2)
#define ENET_TIMER1_CLK_ROOT_AUDIO_PLL_CLK   ROOT_MUX(5U) | CLOCK_NAME(AUDIO_PLL_CLK)
#define ENET_TIMER1_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define ENET_TIMER1_CLK_ROOT_SYS_PLL2_PFD1   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD1)

/* ENET_TIMER2 */
#define ENET_TIMER2_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define ENET_TIMER2_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define ENET_TIMER2_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define ENET_TIMER2_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define ENET_TIMER2_CLK_ROOT_SYS_PLL1_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL1_DIV2)
#define ENET_TIMER2_CLK_ROOT_AUDIO_PLL_CLK   ROOT_MUX(5U) | CLOCK_NAME(AUDIO_PLL_CLK)
#define ENET_TIMER2_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define ENET_TIMER2_CLK_ROOT_SYS_PLL2_PFD1   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD1)

/* ENET_TIMER3 */
#define ENET_TIMER3_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define ENET_TIMER3_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define ENET_TIMER3_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define ENET_TIMER3_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define ENET_TIMER3_CLK_ROOT_SYS_PLL1_DIV2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL1_DIV2)
#define ENET_TIMER3_CLK_ROOT_AUDIO_PLL_CLK   ROOT_MUX(5U) | CLOCK_NAME(AUDIO_PLL_CLK)
#define ENET_TIMER3_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define ENET_TIMER3_CLK_ROOT_SYS_PLL2_PFD1   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD1)

/* USDHC1 */
#define USDHC1_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define USDHC1_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define USDHC1_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define USDHC1_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define USDHC1_CLK_ROOT_SYS_PLL2_PFD2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL2_PFD2)
#define USDHC1_CLK_ROOT_SYS_PLL2_PFD0   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL2_PFD0)
#define USDHC1_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define USDHC1_CLK_ROOT_PLL_ARM_CLK     ROOT_MUX(7U) | CLOCK_NAME(PLL_ARM_CLK)

/* USDHC2 */
#define USDHC2_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define USDHC2_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define USDHC2_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define USDHC2_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define USDHC2_CLK_ROOT_SYS_PLL2_PFD2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL2_PFD2)
#define USDHC2_CLK_ROOT_SYS_PLL2_PFD0   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL2_PFD0)
#define USDHC2_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define USDHC2_CLK_ROOT_PLL_ARM_CLK     ROOT_MUX(7U) | CLOCK_NAME(PLL_ARM_CLK)

/* ASRC */
#define ASRC_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define ASRC_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define ASRC_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define ASRC_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define ASRC_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define ASRC_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define ASRC_CLK_ROOT_AUDIO_PLL_CLK   ROOT_MUX(6U) | CLOCK_NAME(AUDIO_PLL_CLK)
#define ASRC_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* MQS */
#define MQS_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define MQS_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define MQS_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define MQS_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define MQS_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define MQS_CLK_ROOT_SYS_PLL3_DIV2   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3_DIV2)
#define MQS_CLK_ROOT_AUDIO_PLL_CLK   ROOT_MUX(6U) | CLOCK_NAME(AUDIO_PLL_CLK)
#define MQS_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* MIC */
#define MIC_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define MIC_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define MIC_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define MIC_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define MIC_CLK_ROOT_SYS_PLL3_PFD3   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_PFD3)
#define MIC_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3)
#define MIC_CLK_ROOT_AUDIO_PLL_CLK   ROOT_MUX(6U) | CLOCK_NAME(AUDIO_PLL_CLK)
#define MIC_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL1_DIV5)

/* SPDIF */
#define SPDIF_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define SPDIF_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define SPDIF_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define SPDIF_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define SPDIF_CLK_ROOT_AUDIO_PLL_CLK   ROOT_MUX(4U) | CLOCK_NAME(AUDIO_PLL_CLK)
#define SPDIF_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3)
#define SPDIF_CLK_ROOT_SYS_PLL3_PFD2   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL3_PFD2)
#define SPDIF_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* SAI1 */
#define SAI1_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define SAI1_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define SAI1_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define SAI1_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define SAI1_CLK_ROOT_AUDIO_PLL_CLK   ROOT_MUX(4U) | CLOCK_NAME(AUDIO_PLL_CLK)
#define SAI1_CLK_ROOT_SYS_PLL3_PFD2   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3_PFD2)
#define SAI1_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define SAI1_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* SAI2 */
#define SAI2_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define SAI2_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define SAI2_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define SAI2_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define SAI2_CLK_ROOT_AUDIO_PLL_CLK   ROOT_MUX(4U) | CLOCK_NAME(AUDIO_PLL_CLK)
#define SAI2_CLK_ROOT_SYS_PLL3_PFD2   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3_PFD2)
#define SAI2_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define SAI2_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* SAI3 */
#define SAI3_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define SAI3_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define SAI3_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define SAI3_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define SAI3_CLK_ROOT_AUDIO_PLL_CLK   ROOT_MUX(4U) | CLOCK_NAME(AUDIO_PLL_CLK)
#define SAI3_CLK_ROOT_SYS_PLL3_PFD2   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3_PFD2)
#define SAI3_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL1_DIV5)
#define SAI3_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL2_PFD3)

/* SAI4 */
#define SAI4_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define SAI4_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define SAI4_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define SAI4_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define SAI4_CLK_ROOT_SYS_PLL3_PFD3   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL3_PFD3)
#define SAI4_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3)
#define SAI4_CLK_ROOT_AUDIO_PLL_CLK   ROOT_MUX(6U) | CLOCK_NAME(AUDIO_PLL_CLK)
#define SAI4_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL1_DIV5)

/* GC355 */
#define GC355_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define GC355_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define GC355_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define GC355_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define GC355_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL2)
#define GC355_CLK_ROOT_SYS_PLL2_PFD1   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL2_PFD1)
#define GC355_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL3)
#define GC355_CLK_ROOT_VIDEO_PLL_CLK   ROOT_MUX(7U) | CLOCK_NAME(VIDEO_PLL_CLK)

/* LCDIF */
#define LCDIF_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LCDIF_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LCDIF_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LCDIF_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LCDIF_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL2)
#define LCDIF_CLK_ROOT_SYS_PLL2_PFD2   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL2_PFD2)
#define LCDIF_CLK_ROOT_SYS_PLL3_PFD0   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL3_PFD0)
#define LCDIF_CLK_ROOT_VIDEO_PLL_CLK   ROOT_MUX(7U) | CLOCK_NAME(VIDEO_PLL_CLK)

/* LCDIFV2 */
#define LCDIFV2_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define LCDIFV2_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define LCDIFV2_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define LCDIFV2_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define LCDIFV2_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL2)
#define LCDIFV2_CLK_ROOT_SYS_PLL2_PFD2   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL2_PFD2)
#define LCDIFV2_CLK_ROOT_SYS_PLL3_PFD0   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL3_PFD0)
#define LCDIFV2_CLK_ROOT_VIDEO_PLL_CLK   ROOT_MUX(7U) | CLOCK_NAME(VIDEO_PLL_CLK)

/* MIPI_REF */
#define MIPI_REF_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define MIPI_REF_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define MIPI_REF_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define MIPI_REF_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define MIPI_REF_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL2)
#define MIPI_REF_CLK_ROOT_SYS_PLL2_PFD0   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL2_PFD0)
#define MIPI_REF_CLK_ROOT_SYS_PLL3_PFD0   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL3_PFD0)
#define MIPI_REF_CLK_ROOT_VIDEO_PLL_CLK   ROOT_MUX(7U) | CLOCK_NAME(VIDEO_PLL_CLK)

/* MIPI_ESC */
#define MIPI_ESC_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define MIPI_ESC_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define MIPI_ESC_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define MIPI_ESC_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define MIPI_ESC_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL2)
#define MIPI_ESC_CLK_ROOT_SYS_PLL2_PFD0   ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL2_PFD0)
#define MIPI_ESC_CLK_ROOT_SYS_PLL3_PFD0   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL3_PFD0)
#define MIPI_ESC_CLK_ROOT_VIDEO_PLL_CLK   ROOT_MUX(7U) | CLOCK_NAME(VIDEO_PLL_CLK)

/* CSI2 */
#define CSI2_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define CSI2_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define CSI2_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define CSI2_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define CSI2_CLK_ROOT_SYS_PLL2_PFD2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL2_PFD2)
#define CSI2_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3)
#define CSI2_CLK_ROOT_SYS_PLL2_PFD0   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2_PFD0)
#define CSI2_CLK_ROOT_VIDEO_PLL_CLK   ROOT_MUX(7U) | CLOCK_NAME(VIDEO_PLL_CLK)

/* CSI2_ESC */
#define CSI2_ESC_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define CSI2_ESC_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define CSI2_ESC_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define CSI2_ESC_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define CSI2_ESC_CLK_ROOT_SYS_PLL2_PFD2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL2_PFD2)
#define CSI2_ESC_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3)
#define CSI2_ESC_CLK_ROOT_SYS_PLL2_PFD0   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2_PFD0)
#define CSI2_ESC_CLK_ROOT_VIDEO_PLL_CLK   ROOT_MUX(7U) | CLOCK_NAME(VIDEO_PLL_CLK)

/* CSI2_UI */
#define CSI2_UI_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define CSI2_UI_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define CSI2_UI_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define CSI2_UI_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define CSI2_UI_CLK_ROOT_SYS_PLL2_PFD2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL2_PFD2)
#define CSI2_UI_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3)
#define CSI2_UI_CLK_ROOT_SYS_PLL2_PFD0   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL2_PFD0)
#define CSI2_UI_CLK_ROOT_VIDEO_PLL_CLK   ROOT_MUX(7U) | CLOCK_NAME(VIDEO_PLL_CLK)

/* CSI */
#define CSI_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define CSI_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define CSI_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define CSI_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define CSI_CLK_ROOT_SYS_PLL2_PFD2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL2_PFD2)
#define CSI_CLK_ROOT_SYS_PLL3_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL3)
#define CSI_CLK_ROOT_SYS_PLL3_PFD1   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL3_PFD1)
#define CSI_CLK_ROOT_VIDEO_PLL_CLK   ROOT_MUX(7U) | CLOCK_NAME(VIDEO_PLL_CLK)

/* CKO1 */
#define CKO1_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define CKO1_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define CKO1_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define CKO1_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define CKO1_CLK_ROOT_SYS_PLL2_PFD2   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL2_PFD2)
#define CKO1_CLK_ROOT_SYS_PLL2_CLK    ROOT_MUX(5U) | CLOCK_NAME(SYS_PLL2)
#define CKO1_CLK_ROOT_SYS_PLL3_PFD1   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL3_PFD1)
#define CKO1_CLK_ROOT_SYS_PLL1_DIV5   ROOT_MUX(7U) | CLOCK_NAME(SYS_PLL1_DIV5)

/* CKO2 */
#define CKO2_CLK_ROOT_OSC_RC_48M_DIV2 ROOT_MUX(0U) | CLOCK_NAME(OSC_RC_48M_DIV2)
#define CKO2_CLK_ROOT_OSC_24M         ROOT_MUX(1U) | CLOCK_NAME(OSC_24M)
#define CKO2_CLK_ROOT_OSC_RC_400M     ROOT_MUX(2U) | CLOCK_NAME(OSC_RC_400M)
#define CKO2_CLK_ROOT_OSC_RC_16M      ROOT_MUX(3U) | CLOCK_NAME(OSC_RC_16M)
#define CKO2_CLK_ROOT_SYS_PLL2_PFD3   ROOT_MUX(4U) | CLOCK_NAME(SYS_PLL2_PFD3)
#define CKO2_CLK_ROOT_OSC_RC_48M      ROOT_MUX(5U) | CLOCK_NAME(OSC_RC_48M)
#define CKO2_CLK_ROOT_SYS_PLL3_PFD1   ROOT_MUX(6U) | CLOCK_NAME(SYS_PLL3_PFD1)
#define CKO2_CLK_ROOT_AUDIO_PLL_CLK   ROOT_MUX(7U) | CLOCK_NAME(AUDIO_PLL_CLK)

/****************************************************************************
 * Public Types
 ****************************************************************************/

typedef enum ccm_clock_name
{
    OSC_RC_16M      = 0,  /* 16MHZ RC OSCILLATOR. */
    OSC_RC_48M      = 1,  /* 48MHZ RC OSCILLATOR. */
    OSC_RC_48M_DIV2 = 2,  /* 48MHZ RC OSCILLATOR DIV2. */
    OSC_RC_400M     = 3,  /* 400MHZ RC OSCILLATOR. */
    OSC_24M         = 4,  /* 24MHZ OSCILLATOR. */
    PLL_ARM_CLK     = 6,  /* ARM PLL. */
    SYS_PLL2        = 8,  /* SYS PLL2. */
    SYS_PLL2_PFD0   = 10, /* SYS PLL2 PFD0. */
    SYS_PLL2_PFD1   = 11, /* SYS PLL2 PFD1. */
    SYS_PLL2_PFD2   = 12, /* SYS PLL2 PFD2. */
    SYS_PLL2_PFD3   = 13, /* SYS PLL2 PFD3. */
    SYS_PLL3        = 14, /* SYS PLL3. */
    SYS_PLL3_DIV2   = 16, /* SYS PLL3 DIV2 */
    SYS_PLL3_PFD0   = 17, /* SYS PLL3 PFD0. */
    SYS_PLL3_PFD1   = 18, /* SYS PLL3 PFD1 */
    SYS_PLL3_PFD2   = 19, /* SYS PLL3 PFD2 */
    SYS_PLL3_PFD3   = 20, /* SYS PLL3 PFD3 */
    SYS_PLL1        = 21, /* SYS PLL1. */
    SYS_PLL1_DIV2   = 23, /* SYS PLL1 DIV2. */
    SYS_PLL1_DIV5   = 24, /* SYS PLL1 DIV5. */
    AUDIO_PLL_CLK   = 25, /* SYS AUDIO PLL. */
    VIDEO_PLL_CLK   = 27, /* SYS VIDEO PLL. */
} ccm_clock_name;

/* Clock Configuration ******************************************************/

struct ccm_clock_root
{
    uint8_t                 enable;
    uint8_t                 div;
    uint8_t                 mux;
};

struct ccm_clock_root_config_s
{
  union
  {
    struct
    {
      struct ccm_clock_root m7_clk_root;
      struct ccm_clock_root m4_clk_root;
      struct ccm_clock_root bus_clk_root;
      struct ccm_clock_root bus_lpsr_clk_root;
      struct ccm_clock_root semc_clk_root;
      struct ccm_clock_root cssys_clk_root;
      struct ccm_clock_root cstrace_clk_root;
      struct ccm_clock_root m4_systick_clk_root;
      struct ccm_clock_root m7_systick_clk_root;
      struct ccm_clock_root adc1_clk_root;
      struct ccm_clock_root adc2_clk_root;
      struct ccm_clock_root acmp_clk_root;
      struct ccm_clock_root flexio1_clk_root;
      struct ccm_clock_root flexio2_clk_root;
      struct ccm_clock_root gpt1_clk_root;
      struct ccm_clock_root gpt2_clk_root;
      struct ccm_clock_root gpt3_clk_root;
      struct ccm_clock_root gpt4_clk_root;
      struct ccm_clock_root gpt5_clk_root;
      struct ccm_clock_root gpt6_clk_root;
      struct ccm_clock_root flexspi1_clk_root;
      struct ccm_clock_root flexspi2_clk_root;
      struct ccm_clock_root can1_clk_root;
      struct ccm_clock_root can2_clk_root;
      struct ccm_clock_root can3_clk_root;
      struct ccm_clock_root lpuart1_clk_root;
      struct ccm_clock_root lpuart2_clk_root;
      struct ccm_clock_root lpuart3_clk_root;
      struct ccm_clock_root lpuart4_clk_root;
      struct ccm_clock_root lpuart5_clk_root;
      struct ccm_clock_root lpuart6_clk_root;
      struct ccm_clock_root lpuart7_clk_root;
      struct ccm_clock_root lpuart8_clk_root;
      struct ccm_clock_root lpuart9_clk_root;
      struct ccm_clock_root lpuart10_clk_root;
      struct ccm_clock_root lpuart11_clk_root;
      struct ccm_clock_root lpuart12_clk_root;
      struct ccm_clock_root lpi2c1_clk_root;
      struct ccm_clock_root lpi2c2_clk_root;
      struct ccm_clock_root lpi2c3_clk_root;
      struct ccm_clock_root lpi2c4_clk_root;
      struct ccm_clock_root lpi2c5_clk_root;
      struct ccm_clock_root lpi2c6_clk_root;
      struct ccm_clock_root lpspi1_clk_root;
      struct ccm_clock_root lpspi2_clk_root;
      struct ccm_clock_root lpspi3_clk_root;
      struct ccm_clock_root lpspi4_clk_root;
      struct ccm_clock_root lpspi5_clk_root;
      struct ccm_clock_root lpspi6_clk_root;
      struct ccm_clock_root emv1_clk_root;
      struct ccm_clock_root emv2_clk_root;
      struct ccm_clock_root enet1_clk_root;
      struct ccm_clock_root enet2_clk_root;
      struct ccm_clock_root enet_qos_clk_root;
      struct ccm_clock_root enet_25m_clk_root;
      struct ccm_clock_root enet_timer1_clk_root;
      struct ccm_clock_root enet_timer2_clk_root;
      struct ccm_clock_root enet_timer3_clk_root;
      struct ccm_clock_root usdhc1_clk_root;
      struct ccm_clock_root usdhc2_clk_root;
      struct ccm_clock_root asrc_clk_root;
      struct ccm_clock_root mqs_clk_root;
      struct ccm_clock_root mic_clk_root;
      struct ccm_clock_root spdif_clk_root;
      struct ccm_clock_root sai1_clk_root;
      struct ccm_clock_root sai2_clk_root;
      struct ccm_clock_root sai3_clk_root;
      struct ccm_clock_root sai4_clk_root;
      struct ccm_clock_root gc355_clk_root;
      struct ccm_clock_root lcdif_clk_root;
      struct ccm_clock_root lcdifv2_clk_root;
      struct ccm_clock_root mipi_ref_clk_root;
      struct ccm_clock_root mipi_esc_clk_root;
      struct ccm_clock_root csi2_clk_root;
      struct ccm_clock_root csi2_esc_clk_root;
      struct ccm_clock_root csi2_ui_clk_root;
      struct ccm_clock_root csi_clk_root;
      struct ccm_clock_root cko1_clk_root;
      struct ccm_clock_root cko2_clk_root;
    };
    struct ccm_clock_root clock_root[IMXRT_CCM_CR_COUNT];
  };
};

struct ccm_arm_pll
{
    uint8_t  post_div;
    uint8_t  loop_div;
};

struct ccm_sys_pll1
{
    uint32_t  enable;
    uint8_t   div;
    uint32_t  num;
    uint32_t  denom;
};

struct ccm_sys_pll2
{
    uint32_t mfd;
    uint32_t ss;
    uint8_t  ss_enable;
    uint16_t ss_stop;
    uint16_t ss_step;
    uint32_t pfd0;
    uint32_t pfd1;
    uint32_t pfd2;
    uint32_t pfd3;
};

struct ccm_sys_pll3
{
    uint32_t pfd0;
    uint32_t pfd1;
    uint32_t pfd2;
    uint32_t pfd3;
};

struct clock_configuration_s
{
  struct ccm_clock_root_config_s ccm;  /* CCM Root Clock configuration */
  struct ccm_arm_pll             arm_pll;
  struct ccm_sys_pll1            sys_pll1;
  struct ccm_sys_pll2            sys_pll2;
  struct ccm_sys_pll3            sys_pll3;
};

/****************************************************************************
 * Public Data
 ****************************************************************************/

/* Each IMXRT117X board must provide the following initialized structure.
 *  This is needed to establish the initial board clocking.
 */

extern const struct clock_configuration_s g_initial_clkconfig;

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/

/****************************************************************************
 * Name: imxrt_clockconfig
 *
 * Description:
 *   Called to initialize the i.MXRT.  This does whatever setup is needed to
 *   put the SoC in a usable state.  This includes the initialization of
 *   clocking using the settings in board.h.
 *
 ****************************************************************************/

void imxrt_clockconfig(void);

/****************************************************************************
 * Name: imxrt_get_clock
 *
 * Description:
 *   This function returns the clock frequency of the specified functional
 *   clock.
 *
 * Input Parameters:
 *   clkname   - Identifies the clock of interest
 *   frequency - The location where the peripheral clock frequency will be
 *              returned
 *
 * Returned Value:
 *   Zero (OK) is returned on success; a negated errno value is returned on
 *   any failure.  -ENODEV is returned if the clock is not enabled or is not
 *   being clocked.
 *
 ****************************************************************************/

int imxrt_get_clock(enum ccm_clock_name clkname, uint32_t *frequency);

/****************************************************************************
 * Name: imxrt_get_rootclock
 *
 * Description:
 *   This function returns the clock frequency of the specified root
 *   functional clock.
 *
 * Input Parameters:
 *   clkroot   - Identifies the peripheral clock of interest
 *   frequency - The location where the peripheral clock frequency will be
 *              returned
 *
 * Returned Value:
 *   Zero (OK) is returned on success; a negated errno value is returned on
 *   any failure.  -ENODEV is returned if the clock is not enabled or is not
 *   being clocked.
 *
 ****************************************************************************/

int imxrt_get_rootclock(uint32_t clkroot, uint32_t *frequency);

#endif /* __ARCH_ARM_SRC_IMXRT_IMXRT_CLOCKCONFIG_VER2_H */
