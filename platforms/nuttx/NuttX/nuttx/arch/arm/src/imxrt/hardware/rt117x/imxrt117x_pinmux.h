/****************************************************************************
 * arch/arm/src/imxrt/hardware/rt117x/imxrt117x_pinmux.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/* Copyright 2022 NXP */

#ifndef __ARCH_ARM_SRC_IMXRT_HARDWARE_RT106X_IMXRT106X_PINMUX_H
#define __ARCH_ARM_SRC_IMXRT_HARDWARE_RT106X_IMXRT106X_PINMUX_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include "imxrt_iomuxc_ver2.h"
#include "imxrt_gpio.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Alternate Pin Functions.
 *
 * Alternative pin selections are provided with a numeric suffix like _1, _2,
 * etc.  Drivers, however, will use the pin selection without the numeric
 * suffix.  Definitions to select the option to be used are required in the
 * board.h file.  For example, if LPUART1 CTS connects via the AD_B1_04 pin,
 * then the following definition should appear in the board.h header file
 * for that board:
 *
 *   #define GPIO_LPUART3_CTS GPIO_LPUART3_CTS_1
 *
 * The driver will then automatically configure to use the AD_B1_04 pin
 * for LPUART1 CTS.
 *
 * Note that a suffix is *still* given even if there's only one option
 * for a pin. This means it must be explicitly configured with other
 * decorations like the IOMUX options. Default IOMUX options can be found
 * in the imxrt_iomuxc.h file.
 *
 * WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific IOMUX options such
 * as frequency, open-drain, push-pull, and pull-up/down!  Just the basic
 * configuration is defined in this file.
 */

/* Clock Controller Module (CCM) */

#define GPIO_CCM_CLKO1_1               (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_40_INDEX))
#define GPIO_CCM_CLKO2_1               (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_41_INDEX))
#define GPIO_CCM_ENET_REF_CLK_25M_1    (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_00_INDEX))
#define GPIO_CCM_ENET_REF_CLK_25M_2    (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_14_INDEX))
#define GPIO_CCM_ENET_REF_CLK_25M_3    (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_01_INDEX))

/* 10/100 Mbps Ethernet MAC (ENET) */

#define GPIO_ENET_1588_EVENT0_IN_1     (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_34_INDEX))
#define GPIO_ENET_1588_EVENT0_OUT_1    (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_35_INDEX))
#define GPIO_ENET_1588_EVENT1_IN_1     (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_06_INDEX))
#define GPIO_ENET_1588_EVENT1_OUT_1    (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_07_INDEX))
#define GPIO_ENET_1588_EVENT2_IN_1     (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_08_INDEX))
#define GPIO_ENET_1588_EVENT2_OUT_1    (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_09_INDEX))
#define GPIO_ENET_1588_EVENT3_IN_1     (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_10_INDEX))
#define GPIO_ENET_1588_EVENT3_OUT_1    (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_11_INDEX))
#define GPIO_ENET_COL_1                (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_19_INDEX))
#define GPIO_ENET_CRS_1                (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_18_INDEX))
#define GPIO_ENET_MDC_1                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_19_INDEX))
#define GPIO_ENET_MDC_2                (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_32_INDEX))
#define GPIO_ENET_MDIO_1               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_20_INDEX))
#define GPIO_ENET_MDIO_2               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_33_INDEX))
#define GPIO_ENET_RX_DATA0_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_06_INDEX))
#define GPIO_ENET_RX_DATA0_2           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_26_INDEX))
#define GPIO_ENET_RX_DATA1_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_07_INDEX))
#define GPIO_ENET_RX_DATA1_2           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_27_INDEX))
#define GPIO_ENET_RX_DATA2_1           (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_17_INDEX))
#define GPIO_ENET_RX_DATA3_1           (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_16_INDEX))
#define GPIO_ENET_REF_CLK_1            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_29_INDEX))
#define GPIO_ENET_REF_CLK_2            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_05_INDEX))
#define GPIO_ENET_REF_CLK_3            (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_13_INDEX))
#define GPIO_ENET_RX_CLK_1             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_14_INDEX))
#define GPIO_ENET_RX_EN_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_08_INDEX))
#define GPIO_ENET_RX_EN_2              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_24_INDEX))
#define GPIO_ENET_RX_ER_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_09_INDEX))
#define GPIO_ENET_RX_ER_2              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_25_INDEX))
#define GPIO_ENET_TX_DATA0_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_02_INDEX))
#define GPIO_ENET_TX_DATA0_2           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_30_INDEX))
#define GPIO_ENET_TX_DATA1_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_03_INDEX))
#define GPIO_ENET_TX_DATA1_2           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_31_INDEX))
#define GPIO_ENET_TX_DATA2_1           (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_13_INDEX))
#define GPIO_ENET_TX_DATA3_1           (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_12_INDEX))
#define GPIO_ENET_TX_CLK_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_05_INDEX))
#define GPIO_ENET_TX_CLK_2             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_29_INDEX))
#define GPIO_ENET_TX_EN_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_04_INDEX))
#define GPIO_ENET_TX_EN_2              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_28_INDEX))
#define GPIO_ENET_TX_ER_1              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_15_INDEX))
#define GPIO_ENET_TX_ER_2              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_07_INDEX))

/* 10/100/1000 Mbps Ethernet MAC (ENET_1G) */

#define GPIO_ENET_1G_1588_EVENT0_IN_1  (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_34_INDEX))
#define GPIO_ENET_1G_1588_EVENT0_OUT_1 (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_35_INDEX))
#define GPIO_ENET_1G_1588_EVENT1_IN_1  (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_00_INDEX))
#define GPIO_ENET_1G_1588_EVENT1_OUT_1 (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_01_INDEX))
#define GPIO_ENET_1G_1588_EVENT2_IN_1  (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_02_INDEX))
#define GPIO_ENET_1G_1588_EVENT2_OUT_1 (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_03_INDEX))
#define GPIO_ENET_1G_1588_EVENT3_IN_1  (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_04_INDEX))
#define GPIO_ENET_1G_1588_EVENT3_O_1   (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_UTGPIO_AD_05_INDEX))
#define GPIO_ENET_1G_COL_1             (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_10_INDEX))
#define GPIO_ENET_1G_CRS_1             (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_09_INDEX))
#define GPIO_ENET_1G_MDC_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_19_INDEX))
#define GPIO_ENET_1G_MDC_2             (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_40_INDEX))
#define GPIO_ENET_1G_MDC_3             (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_16_INDEX))
#define GPIO_ENET_1G_MDC_4             (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_32_INDEX))
#define GPIO_ENET_1G_MDIO_1            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_20_INDEX))
#define GPIO_ENET_1G_MDIO_2            (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_41_INDEX))
#define GPIO_ENET_1G_MDIO_3            (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_17_INDEX))
#define GPIO_ENET_1G_MDIO_4            (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_33_INDEX))
#define GPIO_ENET_1G_RX_DATA0_1        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_02_INDEX))
#define GPIO_ENET_1G_RX_DATA0_2        (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_15_INDEX))
#define GPIO_ENET_1G_RX_DATA0_3        (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_02_INDEX))
#define GPIO_ENET_1G_RX_DATA1_1        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_03_INDEX))
#define GPIO_ENET_1G_RX_DATA1_2        (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_16_INDEX))
#define GPIO_ENET_1G_RX_DATA1_3        (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_03_INDEX))
#define GPIO_ENET_1G_RX_DATA2_1        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_04_INDEX))
#define GPIO_ENET_1G_RX_DATA2_2        (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_04_INDEX))
#define GPIO_ENET_1G_RX_DATA2_3        (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_08_INDEX))
#define GPIO_ENET_1G_RX_DATA3_1        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_05_INDEX))
#define GPIO_ENET_1G_RX_DATA3_2        (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_05_INDEX))
#define GPIO_ENET_1G_RX_DATA3_3        (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_07_INDEX))
#define GPIO_ENET_1G_REF_CLK_1         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_11_INDEX))
#define GPIO_ENET_1G_REF_CLK_2         (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_19_INDEX))
#define GPIO_ENET_1G_REF_CLK_3         (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_11_INDEX))
#define GPIO_ENET_1G_REF_CLK_4         (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_14_INDEX))
#define GPIO_ENET_1G_RX_CLK_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_01_INDEX))
#define GPIO_ENET_1G_RX_CLK_2          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_01_INDEX))
#define GPIO_ENET_1G_RX_CLK_3          (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_05_INDEX))
#define GPIO_ENET_1G_RX_EN_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_00_INDEX))
#define GPIO_ENET_1G_RX_EN_2           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_17_INDEX))
#define GPIO_ENET_1G_RX_EN_3           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_00_INDEX))
#define GPIO_ENET_1G_RX_ER_1           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_18_INDEX))
#define GPIO_ENET_1G_RX_ER_2           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_01_INDEX))
#define GPIO_ENET_1G_TX_DATA0_1        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_09_INDEX))
#define GPIO_ENET_1G_TX_DATA0_2        (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_11_INDEX))
#define GPIO_ENET_1G_TX_DATA0_3        (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_09_INDEX))
#define GPIO_ENET_1G_TX_DATA1_1        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_08_INDEX))
#define GPIO_ENET_1G_TX_DATA1_2        (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_12_INDEX))
#define GPIO_ENET_1G_TX_DATA1_3        (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_08_INDEX))
#define GPIO_ENET_1G_TX_DATA2_1        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_07_INDEX))
#define GPIO_ENET_1G_TX_DATA2_2        (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_07_INDEX))
#define GPIO_ENET_1G_TX_DATA2_3        (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_04_INDEX))
#define GPIO_ENET_1G_TX_DATA3_1        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_06_INDEX))
#define GPIO_ENET_1G_TX_DATA3_2        (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_06_INDEX))
#define GPIO_ENET_1G_TX_DATA3_3        (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_03_INDEX))
#define GPIO_ENET_1G_TX_CLK_IO_1       (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_11_INDEX))
#define GPIO_ENET_1G_TX_CLK_IO_2       (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_14_INDEX))
#define GPIO_ENET_1G_TX_CLK_IO_3       (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_11_INDEX))
#define GPIO_ENET_1G_TX_EN_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_10_INDEX))
#define GPIO_ENET_1G_TX_EN_2           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_13_INDEX))
#define GPIO_ENET_1G_TX_EN_3           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_10_INDEX))
#define GPIO_ENET_1G_TX_ER_1           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_00_INDEX))
#define GPIO_ENET_1G_TX_ER_2           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_06_INDEX))

/* Flexible Controller Area Network (FLEXCAN) */

#define GPIO_FLEXCAN1_RX_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_07_INDEX))
#define GPIO_FLEXCAN1_RX_2             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_13_INDEX))
#define GPIO_FLEXCAN1_RX_3             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_15_INDEX))
#define GPIO_FLEXCAN1_TX_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_06_INDEX))
#define GPIO_FLEXCAN1_TX_2             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_12_INDEX))
#define GPIO_FLEXCAN1_TX_3             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_14_INDEX))

#define GPIO_FLEXCAN2_RX_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_01_INDEX))
#define GPIO_FLEXCAN2_RX_2             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_31_INDEX))
#define GPIO_FLEXCAN2_TX_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_00_INDEX))
#define GPIO_FLEXCAN2_TX_2             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_30_INDEX))

#define GPIO_FLEXCAN3_RX_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_01_INDEX))
#define GPIO_FLEXCAN3_RX_2             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_09_INDEX))
#define GPIO_FLEXCAN3_RX_3             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_07_INDEX))
#define GPIO_FLEXCAN3_TX_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_00_INDEX))
#define GPIO_FLEXCAN3_TX_2             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_08_INDEX))
#define GPIO_FLEXCAN3_TX_3             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_06_INDEX))

/* Flexible I/O (FlexIO) */

#define GPIO_FLEXIO1_FLEXIO00_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_00_INDEX))
#define GPIO_FLEXIO1_FLEXIO01_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_01_INDEX))
#define GPIO_FLEXIO1_FLEXIO02_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_02_INDEX))
#define GPIO_FLEXIO1_FLEXIO03_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_03_INDEX))
#define GPIO_FLEXIO1_FLEXIO04_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_04_INDEX))
#define GPIO_FLEXIO1_FLEXIO05_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_05_INDEX))
#define GPIO_FLEXIO1_FLEXIO06_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_06_INDEX))
#define GPIO_FLEXIO1_FLEXIO07_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_07_INDEX))
#define GPIO_FLEXIO1_FLEXIO08_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_08_INDEX))
#define GPIO_FLEXIO1_FLEXIO09_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_09_INDEX))
#define GPIO_FLEXIO1_FLEXIO10_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_10_INDEX))
#define GPIO_FLEXIO1_FLEXIO11_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_11_INDEX))
#define GPIO_FLEXIO1_FLEXIO12_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_12_INDEX))
#define GPIO_FLEXIO1_FLEXIO13_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_13_INDEX))
#define GPIO_FLEXIO1_FLEXIO14_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_14_INDEX))
#define GPIO_FLEXIO1_FLEXIO15_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_15_INDEX))
#define GPIO_FLEXIO1_FLEXIO16_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_16_INDEX))
#define GPIO_FLEXIO1_FLEXIO17_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_17_INDEX))
#define GPIO_FLEXIO1_FLEXIO18_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_18_INDEX))
#define GPIO_FLEXIO1_FLEXIO19_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_19_INDEX))
#define GPIO_FLEXIO1_FLEXIO20_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_20_INDEX))
#define GPIO_FLEXIO1_FLEXIO21_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_21_INDEX))
#define GPIO_FLEXIO1_FLEXIO22_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_22_INDEX))
#define GPIO_FLEXIO1_FLEXIO23_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_23_INDEX))
#define GPIO_FLEXIO1_FLEXIO24_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_24_INDEX))
#define GPIO_FLEXIO1_FLEXIO25_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_25_INDEX))
#define GPIO_FLEXIO1_FLEXIO26_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_26_INDEX))
#define GPIO_FLEXIO1_FLEXIO27_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_27_INDEX))
#define GPIO_FLEXIO1_FLEXIO28_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_28_INDEX))
#define GPIO_FLEXIO1_FLEXIO29_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_29_INDEX))
#define GPIO_FLEXIO1_FLEXIO30_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_30_INDEX))
#define GPIO_FLEXIO1_FLEXIO31_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_31_INDEX))

#define GPIO_FLEXIO2_FLEXIO00_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_00_INDEX))
#define GPIO_FLEXIO2_FLEXIO01_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_01_INDEX))
#define GPIO_FLEXIO2_FLEXIO02_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_02_INDEX))
#define GPIO_FLEXIO2_FLEXIO03_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_03_INDEX))
#define GPIO_FLEXIO2_FLEXIO04_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_04_INDEX))
#define GPIO_FLEXIO2_FLEXIO05_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_05_INDEX))
#define GPIO_FLEXIO2_FLEXIO06_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_06_INDEX))
#define GPIO_FLEXIO2_FLEXIO07_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_07_INDEX))
#define GPIO_FLEXIO2_FLEXIO08_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_08_INDEX))
#define GPIO_FLEXIO2_FLEXIO09_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_09_INDEX))
#define GPIO_FLEXIO2_FLEXIO10_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_10_INDEX))
#define GPIO_FLEXIO2_FLEXIO11_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_11_INDEX))
#define GPIO_FLEXIO2_FLEXIO12_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_12_INDEX))
#define GPIO_FLEXIO2_FLEXIO13_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_13_INDEX))
#define GPIO_FLEXIO2_FLEXIO14_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_14_INDEX))
#define GPIO_FLEXIO2_FLEXIO15_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_15_INDEX))
#define GPIO_FLEXIO2_FLEXIO16_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_16_INDEX))
#define GPIO_FLEXIO2_FLEXIO17_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_17_INDEX))
#define GPIO_FLEXIO2_FLEXIO18_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_18_INDEX))
#define GPIO_FLEXIO2_FLEXIO19_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_19_INDEX))
#define GPIO_FLEXIO2_FLEXIO20_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_20_INDEX))
#define GPIO_FLEXIO2_FLEXIO21_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_21_INDEX))
#define GPIO_FLEXIO2_FLEXIO22_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_22_INDEX))
#define GPIO_FLEXIO2_FLEXIO23_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_23_INDEX))
#define GPIO_FLEXIO2_FLEXIO24_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_24_INDEX))
#define GPIO_FLEXIO2_FLEXIO25_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_25_INDEX))
#define GPIO_FLEXIO2_FLEXIO26_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_26_INDEX))
#define GPIO_FLEXIO2_FLEXIO27_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_27_INDEX))
#define GPIO_FLEXIO2_FLEXIO28_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_28_INDEX))
#define GPIO_FLEXIO2_FLEXIO29_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_29_INDEX))
#define GPIO_FLEXIO2_FLEXIO30_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_30_INDEX))
#define GPIO_FLEXIO2_FLEXIO31_1        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_31_INDEX))

/* Enhanced Flex Pulse Width Modulator (eFlexPWM) */

#define GPIO_FLEXPWM1_PWM0_A_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_23_INDEX))
#define GPIO_FLEXPWM1_PWM0_A_2           (GPIO_PERIPH | GPIO_ALT4  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_00_INDEX))
#define GPIO_FLEXPWM1_PWM1_A_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_25_INDEX))
#define GPIO_FLEXPWM1_PWM1_A_2           (GPIO_PERIPH | GPIO_ALT4  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_02_INDEX))
#define GPIO_FLEXPWM1_PWM2_A_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_27_INDEX))
#define GPIO_FLEXPWM1_PWM2_A_2           (GPIO_PERIPH | GPIO_ALT4  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_04_INDEX))
#define GPIO_FLEXPWM1_PWM3_A_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_38_INDEX))
#define GPIO_FLEXPWM1_PWM0_B_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_24_INDEX))
#define GPIO_FLEXPWM1_PWM0_B_2           (GPIO_PERIPH | GPIO_ALT4  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_01_INDEX))
#define GPIO_FLEXPWM1_PWM1_B_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_26_INDEX))
#define GPIO_FLEXPWM1_PWM1_B_2           (GPIO_PERIPH | GPIO_ALT4  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_03_INDEX))
#define GPIO_FLEXPWM1_PWM2_B_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_28_INDEX))
#define GPIO_FLEXPWM1_PWM2_B_2           (GPIO_PERIPH | GPIO_ALT4  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_05_INDEX))
#define GPIO_FLEXPWM1_PWM3_B_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_39_INDEX))
#define GPIO_FLEXPWM1_PWM0_X_1           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_06_INDEX))
#define GPIO_FLEXPWM1_PWM1_X_1           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_07_INDEX))
#define GPIO_FLEXPWM1_PWM2_X_1           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_08_INDEX))
#define GPIO_FLEXPWM1_PWM3_X_1           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_09_INDEX))
#define GPIO_FLEXPWM2_PWM0_A_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_06_INDEX))
#define GPIO_FLEXPWM2_PWM0_A_2           (GPIO_PERIPH | GPIO_ALT4  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_24_INDEX))
#define GPIO_FLEXPWM2_PWM1_A_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_08_INDEX))
#define GPIO_FLEXPWM2_PWM1_A_2           (GPIO_PERIPH | GPIO_ALT4  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_26_INDEX))
#define GPIO_FLEXPWM2_PWM2_A_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_10_INDEX))
#define GPIO_FLEXPWM2_PWM2_A_2           (GPIO_PERIPH | GPIO_ALT4  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_28_INDEX))
#define GPIO_FLEXPWM2_PWM3_A_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_19_INDEX))
#define GPIO_FLEXPWM2_PWM0_B_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_07_INDEX))
#define GPIO_FLEXPWM2_PWM0_B_2           (GPIO_PERIPH | GPIO_ALT4  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_25_INDEX))
#define GPIO_FLEXPWM2_PWM1_B_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_09_INDEX))
#define GPIO_FLEXPWM2_PWM1_B_2           (GPIO_PERIPH | GPIO_ALT4  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_27_INDEX))
#define GPIO_FLEXPWM2_PWM2_B_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_11_INDEX))
#define GPIO_FLEXPWM2_PWM2_B_2           (GPIO_PERIPH | GPIO_ALT4  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_29_INDEX))
#define GPIO_FLEXPWM2_PWM3_B_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_20_INDEX))
#define GPIO_FLEXPWM2_PWM0_X_1           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_10_INDEX))
#define GPIO_FLEXPWM2_PWM1_X_1           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_11_INDEX))
#define GPIO_FLEXPWM2_PWM2_X_1           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_12_INDEX))
#define GPIO_FLEXPWM2_PWM3_X_1           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_13_INDEX))
#define GPIO_FLEXPWM3_PWM0_A_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_29_INDEX))
#define GPIO_FLEXPWM3_PWM0_A_2           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_00_INDEX))
#define GPIO_FLEXPWM3_PWM1_A_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_31_INDEX))
#define GPIO_FLEXPWM3_PWM1_A_2           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_02_INDEX))
#define GPIO_FLEXPWM3_PWM2_A_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_33_INDEX))
#define GPIO_FLEXPWM3_PWM2_A_2           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_04_INDEX))
#define GPIO_FLEXPWM3_PWM3_A_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_21_INDEX))
#define GPIO_FLEXPWM3_PWM3_A_2           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_06_INDEX))
#define GPIO_FLEXPWM3_PWM0_B_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_30_INDEX))
#define GPIO_FLEXPWM3_PWM0_B_2           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_01_INDEX))
#define GPIO_FLEXPWM3_PWM1_B_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_32_INDEX))
#define GPIO_FLEXPWM3_PWM1_B_2           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_03_INDEX))
#define GPIO_FLEXPWM3_PWM2_B_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_34_INDEX))
#define GPIO_FLEXPWM3_PWM2_B_2           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_05_INDEX))
#define GPIO_FLEXPWM3_PWM3_B_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_22_INDEX))
#define GPIO_FLEXPWM3_PWM3_B_2           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_07_INDEX))
#define GPIO_FLEXPWM3_PWM0_X_1           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_14_INDEX))
#define GPIO_FLEXPWM3_PWM1_X_1           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_15_INDEX))
#define GPIO_FLEXPWM3_PWM2_X_1           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_16_INDEX))
#define GPIO_FLEXPWM3_PWM3_X_1           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_17_INDEX))
#define GPIO_FLEXPWM4_PWM0_A_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_00_INDEX))
#define GPIO_FLEXPWM4_PWM1_A_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_02_INDEX))
#define GPIO_FLEXPWM4_PWM2_A_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_04_INDEX))
#define GPIO_FLEXPWM4_PWM3_A_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_17_INDEX))
#define GPIO_FLEXPWM4_PWM0_B_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_01_INDEX))
#define GPIO_FLEXPWM4_PWM1_B_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_03_INDEX))
#define GPIO_FLEXPWM4_PWM2_B_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_05_INDEX))
#define GPIO_FLEXPWM4_PWM3_B_1           (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_18_INDEX))
#define GPIO_FLEXPWM4_PWM0_X_1           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_18_INDEX))
#define GPIO_FLEXPWM4_PWM1_X_1           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_19_INDEX))
#define GPIO_FLEXPWM4_PWM2_X_1           (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_20_INDEX))

/* Flexible SPI (FlexSPI) */

#define GPIO_FLEXSPI1_A_DATA0_1        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_08_INDEX))
#define GPIO_FLEXSPI1_A_DATA0_2        (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_20_INDEX))
#define GPIO_FLEXSPI1_A_DATA1_1        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_09_INDEX))
#define GPIO_FLEXSPI1_A_DATA1_2        (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_21_INDEX))
#define GPIO_FLEXSPI1_A_DATA2_1        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_10_INDEX))
#define GPIO_FLEXSPI1_A_DATA2_2        (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_22_INDEX))
#define GPIO_FLEXSPI1_A_DATA3_1        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_11_INDEX))
#define GPIO_FLEXSPI1_A_DATA3_2        (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_23_INDEX))
#define GPIO_FLEXSPI1_A_DQS_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_05_INDEX))
#define GPIO_FLEXSPI1_A_DQS_2          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_17_INDEX))
#define GPIO_FLEXSPI1_A_DQS_3          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_18_INDEX))
#define GPIO_FLEXSPI1_A_SCLK_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_07_INDEX))
#define GPIO_FLEXSPI1_A_SCLK_2         (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_19_INDEX))
#define GPIO_FLEXSPI1_A_SS0_B_1        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_06_INDEX))
#define GPIO_FLEXSPI1_A_SS0_B_2        (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_18_INDEX))
#define GPIO_FLEXSPI1_A_SS1_B_1        (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_04_INDEX))
#define GPIO_FLEXSPI1_A_SS1_B_2        (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_02_INDEX))

#define GPIO_FLEXSPI1_B_DATA0_1        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_03_INDEX))
#define GPIO_FLEXSPI1_B_DATA0_2        (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_15_INDEX))
#define GPIO_FLEXSPI1_B_DATA1_1        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_02_INDEX))
#define GPIO_FLEXSPI1_B_DATA1_2        (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_14_INDEX))
#define GPIO_FLEXSPI1_B_DATA2_1        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_01_INDEX))
#define GPIO_FLEXSPI1_B_DATA2_2        (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_13_INDEX))
#define GPIO_FLEXSPI1_B_DATA3_1        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_00_INDEX))
#define GPIO_FLEXSPI1_B_DATA3_2        (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_12_INDEX))
#define GPIO_FLEXSPI1_B_DQS_1          (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_05_INDEX))
#define GPIO_FLEXSPI1_B_SCLK_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_04_INDEX))
#define GPIO_FLEXSPI1_B_SCLK_2         (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_16_INDEX))
#define GPIO_FLEXSPI1_B_SS0_B_1        (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_05_INDEX))
#define GPIO_FLEXSPI1_B_SS0_B_2        (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_04_INDEX))
#define GPIO_FLEXSPI1_B_SS1_B_1        (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_35_INDEX))
#define GPIO_FLEXSPI1_B_SS1_B_2        (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_03_INDEX))

#define GPIO_FLEXSPI2_A_DATA0_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_13_INDEX))
#define GPIO_FLEXSPI2_A_DATA0_2        (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_02_INDEX))
#define GPIO_FLEXSPI2_A_DATA1_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_14_INDEX))
#define GPIO_FLEXSPI2_A_DATA1_2        (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_03_INDEX))
#define GPIO_FLEXSPI2_A_DATA2_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_15_INDEX))
#define GPIO_FLEXSPI2_A_DATA2_2        (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_04_INDEX))
#define GPIO_FLEXSPI2_A_DATA3_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_16_INDEX))
#define GPIO_FLEXSPI2_A_DATA3_2        (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_05_INDEX))
#define GPIO_FLEXSPI2_A_DATA4_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_17_INDEX))
#define GPIO_FLEXSPI2_A_DATA5_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_18_INDEX))
#define GPIO_FLEXSPI2_A_DATA6_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_19_INDEX))
#define GPIO_FLEXSPI2_A_DATA7_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_20_INDEX))
#define GPIO_FLEXSPI2_A_DQS_1          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_12_INDEX))
#define GPIO_FLEXSPI2_A_SCLK_1         (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_10_INDEX))
#define GPIO_FLEXSPI2_A_SCLK_2         (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_01_INDEX))
#define GPIO_FLEXSPI2_A_SS0_B_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_11_INDEX))
#define GPIO_FLEXSPI2_A_SS0_B_2        (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_00_INDEX))
#define GPIO_FLEXSPI2_A_SS1_B_1        (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_01_INDEX))

#define GPIO_FLEXSPI2_B_DATA0_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_06_INDEX))
#define GPIO_FLEXSPI2_B_DATA1_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_05_INDEX))
#define GPIO_FLEXSPI2_B_DATA2_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_04_INDEX))
#define GPIO_FLEXSPI2_B_DATA3_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_03_INDEX))
#define GPIO_FLEXSPI2_B_DATA4_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_02_INDEX))
#define GPIO_FLEXSPI2_B_DATA5_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_01_INDEX))
#define GPIO_FLEXSPI2_B_DATA6_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_00_INDEX))
#define GPIO_FLEXSPI2_B_DATA7_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_41_INDEX))
#define GPIO_FLEXSPI2_B_DQS_1          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_07_INDEX))
#define GPIO_FLEXSPI2_B_SCLK_1         (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_09_INDEX))
#define GPIO_FLEXSPI2_B_SS0_B_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_08_INDEX))
#define GPIO_FLEXSPI2_B_SS1_B_1        (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_00_INDEX))

/* GPIO */

#define GPIO_GPIO1_IO00_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_00_INDEX))
#define GPIO_GPIO1_IO01_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_01_INDEX))
#define GPIO_GPIO1_IO02_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_02_INDEX))
#define GPIO_GPIO1_IO03_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_03_INDEX))
#define GPIO_GPIO1_IO04_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_04_INDEX))
#define GPIO_GPIO1_IO05_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_05_INDEX))
#define GPIO_GPIO1_IO06_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_06_INDEX))
#define GPIO_GPIO1_IO07_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_07_INDEX))
#define GPIO_GPIO1_IO08_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_08_INDEX))
#define GPIO_GPIO1_IO09_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_09_INDEX))
#define GPIO_GPIO1_IO10_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_10_INDEX))
#define GPIO_GPIO1_IO11_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_11_INDEX))
#define GPIO_GPIO1_IO12_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_12_INDEX))
#define GPIO_GPIO1_IO13_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_13_INDEX))
#define GPIO_GPIO1_IO14_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_14_INDEX))
#define GPIO_GPIO1_IO15_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_15_INDEX))
#define GPIO_GPIO1_IO16_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_16_INDEX))
#define GPIO_GPIO1_IO17_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_17_INDEX))
#define GPIO_GPIO1_IO18_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_18_INDEX))
#define GPIO_GPIO1_IO19_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_19_INDEX))
#define GPIO_GPIO1_IO20_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_20_INDEX))
#define GPIO_GPIO1_IO21_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_21_INDEX))
#define GPIO_GPIO1_IO22_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_22_INDEX))
#define GPIO_GPIO1_IO23_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_23_INDEX))
#define GPIO_GPIO1_IO24_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_24_INDEX))
#define GPIO_GPIO1_IO25_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_25_INDEX))
#define GPIO_GPIO1_IO26_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_26_INDEX))
#define GPIO_GPIO1_IO27_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_27_INDEX))
#define GPIO_GPIO1_IO28_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_28_INDEX))
#define GPIO_GPIO1_IO29_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_29_INDEX))
#define GPIO_GPIO1_IO30_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_30_INDEX))
#define GPIO_GPIO1_IO31_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_31_INDEX))

#define GPIO_GPIO2_IO00_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_32_INDEX))
#define GPIO_GPIO2_IO01_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_33_INDEX))
#define GPIO_GPIO2_IO02_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_34_INDEX))
#define GPIO_GPIO2_IO03_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_35_INDEX))
#define GPIO_GPIO2_IO04_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_36_INDEX))
#define GPIO_GPIO2_IO05_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_37_INDEX))
#define GPIO_GPIO2_IO06_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_38_INDEX))
#define GPIO_GPIO2_IO07_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_39_INDEX))
#define GPIO_GPIO2_IO08_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_40_INDEX))
#define GPIO_GPIO2_IO09_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_41_INDEX))
#define GPIO_GPIO2_IO10_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_00_INDEX))
#define GPIO_GPIO2_IO11_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_01_INDEX))
#define GPIO_GPIO2_IO12_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_02_INDEX))
#define GPIO_GPIO2_IO13_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_03_INDEX))
#define GPIO_GPIO2_IO14_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_04_INDEX))
#define GPIO_GPIO2_IO15_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_05_INDEX))
#define GPIO_GPIO2_IO16_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_06_INDEX))
#define GPIO_GPIO2_IO17_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_07_INDEX))
#define GPIO_GPIO2_IO18_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_08_INDEX))
#define GPIO_GPIO2_IO19_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_09_INDEX))
#define GPIO_GPIO2_IO20_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_10_INDEX))
#define GPIO_GPIO2_IO21_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_11_INDEX))
#define GPIO_GPIO2_IO22_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_12_INDEX))
#define GPIO_GPIO2_IO23_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_13_INDEX))
#define GPIO_GPIO2_IO24_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_14_INDEX))
#define GPIO_GPIO2_IO25_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_15_INDEX))
#define GPIO_GPIO2_IO26_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_16_INDEX))
#define GPIO_GPIO2_IO27_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_17_INDEX))
#define GPIO_GPIO2_IO28_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_18_INDEX))
#define GPIO_GPIO2_IO29_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_19_INDEX))
#define GPIO_GPIO2_IO30_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_20_INDEX))
#define GPIO_GPIO2_IO31_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_00_INDEX))

#define GPIO_GPIO3_IO00_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_01_INDEX))
#define GPIO_GPIO3_IO01_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_02_INDEX))
#define GPIO_GPIO3_IO02_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_03_INDEX))
#define GPIO_GPIO3_IO03_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_04_INDEX))
#define GPIO_GPIO3_IO04_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_05_INDEX))
#define GPIO_GPIO3_IO05_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_06_INDEX))
#define GPIO_GPIO3_IO06_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_07_INDEX))
#define GPIO_GPIO3_IO07_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_08_INDEX))
#define GPIO_GPIO3_IO08_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_09_INDEX))
#define GPIO_GPIO3_IO09_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_10_INDEX))
#define GPIO_GPIO3_IO10_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_11_INDEX))
#define GPIO_GPIO3_IO11_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_12_INDEX))
#define GPIO_GPIO3_IO12_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_13_INDEX))
#define GPIO_GPIO3_IO13_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_14_INDEX))
#define GPIO_GPIO3_IO14_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_15_INDEX))
#define GPIO_GPIO3_IO15_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_16_INDEX))
#define GPIO_GPIO3_IO16_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_17_INDEX))
#define GPIO_GPIO3_IO17_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_18_INDEX))
#define GPIO_GPIO3_IO18_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_19_INDEX))
#define GPIO_GPIO3_IO19_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_20_INDEX))
#define GPIO_GPIO3_IO20_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_21_INDEX))
#define GPIO_GPIO3_IO21_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_22_INDEX))
#define GPIO_GPIO3_IO22_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_23_INDEX))
#define GPIO_GPIO3_IO23_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_24_INDEX))
#define GPIO_GPIO3_IO24_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_25_INDEX))
#define GPIO_GPIO3_IO25_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_26_INDEX))
#define GPIO_GPIO3_IO26_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_27_INDEX))
#define GPIO_GPIO3_IO27_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_28_INDEX))
#define GPIO_GPIO3_IO28_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_29_INDEX))
#define GPIO_GPIO3_IO29_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_30_INDEX))
#define GPIO_GPIO3_IO30_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_31_INDEX))
#define GPIO_GPIO3_IO31_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_32_INDEX))

#define GPIO_GPIO4_IO00_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_33_INDEX))
#define GPIO_GPIO4_IO01_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_34_INDEX))
#define GPIO_GPIO4_IO02_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_35_INDEX))
#define GPIO_GPIO4_IO03_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_00_INDEX))
#define GPIO_GPIO4_IO04_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_01_INDEX))
#define GPIO_GPIO4_IO05_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_02_INDEX))
#define GPIO_GPIO4_IO06_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_03_INDEX))
#define GPIO_GPIO4_IO07_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_04_INDEX))
#define GPIO_GPIO4_IO08_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_05_INDEX))
#define GPIO_GPIO4_IO09_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_00_INDEX))
#define GPIO_GPIO4_IO10_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_01_INDEX))
#define GPIO_GPIO4_IO11_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_02_INDEX))
#define GPIO_GPIO4_IO12_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_03_INDEX))
#define GPIO_GPIO4_IO13_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_04_INDEX))
#define GPIO_GPIO4_IO14_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_05_INDEX))
#define GPIO_GPIO4_IO15_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_06_INDEX))
#define GPIO_GPIO4_IO16_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_07_INDEX))
#define GPIO_GPIO4_IO17_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_08_INDEX))
#define GPIO_GPIO4_IO18_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_09_INDEX))
#define GPIO_GPIO4_IO19_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_10_INDEX))
#define GPIO_GPIO4_IO20_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_11_INDEX))
#define GPIO_GPIO4_IO21_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_00_INDEX))
#define GPIO_GPIO4_IO22_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_01_INDEX))
#define GPIO_GPIO4_IO23_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_02_INDEX))
#define GPIO_GPIO4_IO24_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_03_INDEX))
#define GPIO_GPIO4_IO25_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_04_INDEX))
#define GPIO_GPIO4_IO26_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_05_INDEX))
#define GPIO_GPIO4_IO27_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_06_INDEX))
#define GPIO_GPIO4_IO28_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_07_INDEX))
#define GPIO_GPIO4_IO29_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_08_INDEX))
#define GPIO_GPIO4_IO30_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_09_INDEX))
#define GPIO_GPIO4_IO31_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_10_INDEX))

#define GPIO_GPIO5_IO00_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_11_INDEX))
#define GPIO_GPIO5_IO01_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_00_INDEX))
#define GPIO_GPIO5_IO02_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_01_INDEX))
#define GPIO_GPIO5_IO03_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_02_INDEX))
#define GPIO_GPIO5_IO04_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_03_INDEX))
#define GPIO_GPIO5_IO05_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_04_INDEX))
#define GPIO_GPIO5_IO06_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_05_INDEX))
#define GPIO_GPIO5_IO07_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_06_INDEX))
#define GPIO_GPIO5_IO08_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_07_INDEX))
#define GPIO_GPIO5_IO09_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_08_INDEX))
#define GPIO_GPIO5_IO10_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_09_INDEX))
#define GPIO_GPIO5_IO11_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_10_INDEX))
#define GPIO_GPIO5_IO12_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_11_INDEX))
#define GPIO_GPIO5_IO13_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_12_INDEX))
#define GPIO_GPIO5_IO14_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_13_INDEX))
#define GPIO_GPIO5_IO15_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_14_INDEX))
#define GPIO_GPIO5_IO16_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_15_INDEX))

#define GPIO_GPIO6_IO00_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_00_INDEX))
#define GPIO_GPIO6_IO01_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_01_INDEX))
#define GPIO_GPIO6_IO02_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_02_INDEX))
#define GPIO_GPIO6_IO03_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_03_INDEX))
#define GPIO_GPIO6_IO04_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_04_INDEX))
#define GPIO_GPIO6_IO05_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_05_INDEX))
#define GPIO_GPIO6_IO06_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_06_INDEX))
#define GPIO_GPIO6_IO07_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_07_INDEX))
#define GPIO_GPIO6_IO08_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_08_INDEX))
#define GPIO_GPIO6_IO09_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_09_INDEX))
#define GPIO_GPIO6_IO10_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_10_INDEX))
#define GPIO_GPIO6_IO11_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_11_INDEX))
#define GPIO_GPIO6_IO12_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_12_INDEX))
#define GPIO_GPIO6_IO13_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_13_INDEX))
#define GPIO_GPIO6_IO14_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_14_INDEX))
#define GPIO_GPIO6_IO15_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_15_INDEX))

#define GPIO_GPIO7_IO00_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_00_INDEX))
#define GPIO_GPIO7_IO01_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_01_INDEX))
#define GPIO_GPIO7_IO02_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_02_INDEX))
#define GPIO_GPIO7_IO03_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_03_INDEX))
#define GPIO_GPIO7_IO04_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_04_INDEX))
#define GPIO_GPIO7_IO05_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_05_INDEX))
#define GPIO_GPIO7_IO06_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_06_INDEX))
#define GPIO_GPIO7_IO07_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_07_INDEX))
#define GPIO_GPIO7_IO08_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_08_INDEX))
#define GPIO_GPIO7_IO09_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_09_INDEX))
#define GPIO_GPIO7_IO10_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_10_INDEX))
#define GPIO_GPIO7_IO11_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_11_INDEX))
#define GPIO_GPIO7_IO12_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_12_INDEX))
#define GPIO_GPIO7_IO13_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_13_INDEX))
#define GPIO_GPIO7_IO14_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_14_INDEX))
#define GPIO_GPIO7_IO15_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_15_INDEX))
#define GPIO_GPIO7_IO16_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_16_INDEX))
#define GPIO_GPIO7_IO17_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_17_INDEX))
#define GPIO_GPIO7_IO18_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_18_INDEX))
#define GPIO_GPIO7_IO19_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_19_INDEX))
#define GPIO_GPIO7_IO20_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_20_INDEX))
#define GPIO_GPIO7_IO21_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_21_INDEX))
#define GPIO_GPIO7_IO22_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_22_INDEX))
#define GPIO_GPIO7_IO23_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_23_INDEX))
#define GPIO_GPIO7_IO24_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_24_INDEX))
#define GPIO_GPIO7_IO25_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_25_INDEX))
#define GPIO_GPIO7_IO26_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_26_INDEX))
#define GPIO_GPIO7_IO27_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_27_INDEX))
#define GPIO_GPIO7_IO28_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_28_INDEX))
#define GPIO_GPIO7_IO29_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_29_INDEX))
#define GPIO_GPIO7_IO30_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_30_INDEX))
#define GPIO_GPIO7_IO31_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_31_INDEX))

#define GPIO_GPIO8_IO00_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_32_INDEX))
#define GPIO_GPIO8_IO01_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_33_INDEX))
#define GPIO_GPIO8_IO02_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_34_INDEX))
#define GPIO_GPIO8_IO03_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_35_INDEX))
#define GPIO_GPIO8_IO04_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_36_INDEX))
#define GPIO_GPIO8_IO05_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_37_INDEX))
#define GPIO_GPIO8_IO06_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_38_INDEX))
#define GPIO_GPIO8_IO07_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_39_INDEX))
#define GPIO_GPIO8_IO08_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_40_INDEX))
#define GPIO_GPIO8_IO09_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_41_INDEX))
#define GPIO_GPIO8_IO10_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_00_INDEX))
#define GPIO_GPIO8_IO11_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_01_INDEX))
#define GPIO_GPIO8_IO12_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_02_INDEX))
#define GPIO_GPIO8_IO13_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_03_INDEX))
#define GPIO_GPIO8_IO14_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_04_INDEX))
#define GPIO_GPIO8_IO15_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_05_INDEX))
#define GPIO_GPIO8_IO16_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_06_INDEX))
#define GPIO_GPIO8_IO17_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_07_INDEX))
#define GPIO_GPIO8_IO18_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_08_INDEX))
#define GPIO_GPIO8_IO19_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_09_INDEX))
#define GPIO_GPIO8_IO20_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_10_INDEX))
#define GPIO_GPIO8_IO21_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_11_INDEX))
#define GPIO_GPIO8_IO22_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_12_INDEX))
#define GPIO_GPIO8_IO23_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_13_INDEX))
#define GPIO_GPIO8_IO24_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_14_INDEX))
#define GPIO_GPIO8_IO25_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_15_INDEX))
#define GPIO_GPIO8_IO26_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_16_INDEX))
#define GPIO_GPIO8_IO27_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_17_INDEX))
#define GPIO_GPIO8_IO28_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_18_INDEX))
#define GPIO_GPIO8_IO29_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_19_INDEX))
#define GPIO_GPIO8_IO30_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_20_INDEX))
#define GPIO_GPIO8_IO31_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_00_INDEX))

#define GPIO_GPIO9_IO00_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_01_INDEX))
#define GPIO_GPIO9_IO01_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_02_INDEX))
#define GPIO_GPIO9_IO02_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_03_INDEX))
#define GPIO_GPIO9_IO03_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_04_INDEX))
#define GPIO_GPIO9_IO04_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_05_INDEX))
#define GPIO_GPIO9_IO05_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_06_INDEX))
#define GPIO_GPIO9_IO06_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_07_INDEX))
#define GPIO_GPIO9_IO07_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_08_INDEX))
#define GPIO_GPIO9_IO08_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_09_INDEX))
#define GPIO_GPIO9_IO09_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_10_INDEX))
#define GPIO_GPIO9_IO10_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_11_INDEX))
#define GPIO_GPIO9_IO11_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_12_INDEX))
#define GPIO_GPIO9_IO12_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_13_INDEX))
#define GPIO_GPIO9_IO13_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_14_INDEX))
#define GPIO_GPIO9_IO14_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_15_INDEX))
#define GPIO_GPIO9_IO15_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_16_INDEX))
#define GPIO_GPIO9_IO16_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_17_INDEX))
#define GPIO_GPIO9_IO17_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_18_INDEX))
#define GPIO_GPIO9_IO18_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_19_INDEX))
#define GPIO_GPIO9_IO19_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_20_INDEX))
#define GPIO_GPIO9_IO20_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_21_INDEX))
#define GPIO_GPIO9_IO21_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_22_INDEX))
#define GPIO_GPIO9_IO22_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_23_INDEX))
#define GPIO_GPIO9_IO23_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_24_INDEX))
#define GPIO_GPIO9_IO24_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_25_INDEX))
#define GPIO_GPIO9_IO25_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_26_INDEX))
#define GPIO_GPIO9_IO26_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_27_INDEX))
#define GPIO_GPIO9_IO27_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_28_INDEX))
#define GPIO_GPIO9_IO28_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_29_INDEX))
#define GPIO_GPIO9_IO29_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_30_INDEX))
#define GPIO_GPIO9_IO30_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_31_INDEX))
#define GPIO_GPIO9_IO31_1              (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_32_INDEX))

#define GPIO_GPIO10_IO00_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_33_INDEX))
#define GPIO_GPIO10_IO01_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_34_INDEX))
#define GPIO_GPIO10_IO02_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_35_INDEX))
#define GPIO_GPIO10_IO03_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_00_INDEX))
#define GPIO_GPIO10_IO04_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_01_INDEX))
#define GPIO_GPIO10_IO05_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_02_INDEX))
#define GPIO_GPIO10_IO06_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_03_INDEX))
#define GPIO_GPIO10_IO07_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_04_INDEX))
#define GPIO_GPIO10_IO08_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_05_INDEX))
#define GPIO_GPIO10_IO09_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_00_INDEX))
#define GPIO_GPIO10_IO10_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_01_INDEX))
#define GPIO_GPIO10_IO11_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_02_INDEX))
#define GPIO_GPIO10_IO12_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_03_INDEX))
#define GPIO_GPIO10_IO13_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_04_INDEX))
#define GPIO_GPIO10_IO14_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_05_INDEX))
#define GPIO_GPIO10_IO15_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_06_INDEX))
#define GPIO_GPIO10_IO16_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_07_INDEX))
#define GPIO_GPIO10_IO17_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_08_INDEX))
#define GPIO_GPIO10_IO18_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_09_INDEX))
#define GPIO_GPIO10_IO19_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_10_INDEX))
#define GPIO_GPIO10_IO20_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_11_INDEX))
#define GPIO_GPIO10_IO21_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_00_INDEX))
#define GPIO_GPIO10_IO22_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_01_INDEX))
#define GPIO_GPIO10_IO23_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_02_INDEX))
#define GPIO_GPIO10_IO24_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_03_INDEX))
#define GPIO_GPIO10_IO25_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_04_INDEX))
#define GPIO_GPIO10_IO26_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_05_INDEX))
#define GPIO_GPIO10_IO27_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_06_INDEX))
#define GPIO_GPIO10_IO28_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_07_INDEX))
#define GPIO_GPIO10_IO29_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_08_INDEX))
#define GPIO_GPIO10_IO30_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_09_INDEX))
#define GPIO_GPIO10_IO31_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_10_INDEX))

#define GPIO_GPIO11_IO00_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_11_INDEX))
#define GPIO_GPIO11_IO01_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_00_INDEX))
#define GPIO_GPIO11_IO02_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_01_INDEX))
#define GPIO_GPIO11_IO03_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_02_INDEX))
#define GPIO_GPIO11_IO04_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_03_INDEX))
#define GPIO_GPIO11_IO05_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_04_INDEX))
#define GPIO_GPIO11_IO06_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_05_INDEX))
#define GPIO_GPIO11_IO07_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_06_INDEX))
#define GPIO_GPIO11_IO08_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_07_INDEX))
#define GPIO_GPIO11_IO09_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_08_INDEX))
#define GPIO_GPIO11_IO10_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_09_INDEX))
#define GPIO_GPIO11_IO11_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_10_INDEX))
#define GPIO_GPIO11_IO12_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_11_INDEX))
#define GPIO_GPIO11_IO13_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_12_INDEX))
#define GPIO_GPIO11_IO14_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_13_INDEX))
#define GPIO_GPIO11_IO15_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_14_INDEX))
#define GPIO_GPIO11_IO16_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_15_INDEX))

#define GPIO_GPIO12_IO00_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_00_INDEX))
#define GPIO_GPIO12_IO01_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_01_INDEX))
#define GPIO_GPIO12_IO02_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_02_INDEX))
#define GPIO_GPIO12_IO03_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_03_INDEX))
#define GPIO_GPIO12_IO04_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_04_INDEX))
#define GPIO_GPIO12_IO05_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_05_INDEX))
#define GPIO_GPIO12_IO06_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_06_INDEX))
#define GPIO_GPIO12_IO07_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_07_INDEX))
#define GPIO_GPIO12_IO08_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_08_INDEX))
#define GPIO_GPIO12_IO09_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_09_INDEX))
#define GPIO_GPIO12_IO10_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_10_INDEX))
#define GPIO_GPIO12_IO11_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_11_INDEX))
#define GPIO_GPIO12_IO12_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_12_INDEX))
#define GPIO_GPIO12_IO13_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_13_INDEX))
#define GPIO_GPIO12_IO14_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_14_INDEX))
#define GPIO_GPIO12_IO15_1             (GPIO_PERIPH | GPIO_ALT10 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_15_INDEX))

#define GPIO_GPIO13_IO00_1             (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_WAKEUP_INDEX))
#define GPIO_GPIO13_IO01_1             (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_PMIC_ON_REQ_INDEX))
#define GPIO_GPIO13_IO02_1             (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_PMIC_STBY_REQ_INDEX))
#define GPIO_GPIO13_IO03_1             (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SNVS_00_INDEX))
#define GPIO_GPIO13_IO04_1             (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SNVS_01_INDEX))
#define GPIO_GPIO13_IO05_1             (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SNVS_02_INDEX))
#define GPIO_GPIO13_IO06_1             (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SNVS_03_INDEX))
#define GPIO_GPIO13_IO07_1             (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SNVS_04_INDEX))
#define GPIO_GPIO13_IO08_1             (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SNVS_05_INDEX))
#define GPIO_GPIO13_IO09_1             (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SNVS_06_INDEX))
#define GPIO_GPIO13_IO10_1             (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SNVS_07_INDEX))
#define GPIO_GPIO13_IO11_1             (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SNVS_08_INDEX))
#define GPIO_GPIO13_IO12_1             (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SNVS_09_INDEX))

/* General Purpose Timer (GPT) */

#define GPIO_GPT1_CAPTURE1_1           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_12_INDEX))
#define GPIO_GPT1_CAPTURE2_1           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_13_INDEX))
#define GPIO_GPT1_CLK_1                (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_17_INDEX))
#define GPIO_GPT1_COMPARE1_1           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_14_INDEX))
#define GPIO_GPT1_COMPARE2_1           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_15_INDEX))
#define GPIO_GPT1_COMPARE3_1           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_16_INDEX))

#define GPIO_GPT2_CAPTURE1_1           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_00_INDEX))
#define GPIO_GPT2_CAPTURE2_1           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_01_INDEX))
#define GPIO_GPT2_CLK_1                (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_05_INDEX))
#define GPIO_GPT2_COMPARE1_1           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_02_INDEX))
#define GPIO_GPT2_COMPARE2_1           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_03_INDEX))
#define GPIO_GPT2_COMPARE3_1           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_04_INDEX))

#define GPIO_GPT3_CAPTURE1_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_06_INDEX))
#define GPIO_GPT3_CAPTURE1_2           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_06_INDEX))
#define GPIO_GPT3_CAPTURE2_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_07_INDEX))
#define GPIO_GPT3_CAPTURE2_2           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_07_INDEX))
#define GPIO_GPT3_CLK_1                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_05_INDEX))
#define GPIO_GPT3_CLK_2                (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_11_INDEX))
#define GPIO_GPT3_COMPARE1_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_08_INDEX))
#define GPIO_GPT3_COMPARE1_2           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_08_INDEX))
#define GPIO_GPT3_COMPARE2_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_09_INDEX))
#define GPIO_GPT3_COMPARE2_2           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_09_INDEX))
#define GPIO_GPT3_COMPARE3_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_10_INDEX))
#define GPIO_GPT3_COMPARE3_2           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_10_INDEX))

#define GPIO_GPT4_CAPTURE1_1           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_00_INDEX))
#define GPIO_GPT4_CAPTURE2_1           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_01_INDEX))
#define GPIO_GPT4_CLK_1                (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_05_INDEX))
#define GPIO_GPT4_COMPARE1_1           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_02_INDEX))
#define GPIO_GPT4_COMPARE2_1           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_03_INDEX))
#define GPIO_GPT4_COMPARE3_1           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_04_INDEX))

#define GPIO_GPT5_CAPTURE1_1           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_09_INDEX))
#define GPIO_GPT5_CAPTURE2_1           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_10_INDEX))
#define GPIO_GPT5_CLK_1                (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_14_INDEX))
#define GPIO_GPT5_COMPARE1_1           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_11_INDEX))
#define GPIO_GPT5_COMPARE2_1           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_12_INDEX))
#define GPIO_GPT5_COMPARE3_1           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_13_INDEX))

#define GPIO_GPT6_CAPTURE1_1           (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_06_INDEX))
#define GPIO_GPT6_CAPTURE2_1           (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_07_INDEX))
#define GPIO_GPT6_CLK_1                (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_11_INDEX))
#define GPIO_GPT6_COMPARE1_1           (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_08_INDEX))
#define GPIO_GPT6_COMPARE2_1           (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_09_INDEX))

/* JTAG */

#define GPIO_JTAG_MOD_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_13_INDEX))
#define GPIO_JTAG_TCK_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_14_INDEX))
#define GPIO_JTAG_TDI_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_12_INDEX))
#define GPIO_JTAG_TDO_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_11_INDEX))
#define GPIO_JTAG_TMS_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_15_INDEX))
#define GPIO_JTAG_TRSTB_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_10_INDEX))

/* Low Power Inter-Integrated Circuit (LPI2C) */

#define GPIO_LPI2C1_HREQ_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_12_INDEX))
#define GPIO_LPI2C1_SCL_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_32_INDEX))
#define GPIO_LPI2C1_SCL_2              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_08_INDEX))
#define GPIO_LPI2C1_SCLS_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_10_INDEX))
#define GPIO_LPI2C1_SDA_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_33_INDEX))
#define GPIO_LPI2C1_SDA_2              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_09_INDEX))
#define GPIO_LPI2C1_SDAS_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_11_INDEX))

#define GPIO_LPI2C2_SCL_1              (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_00_INDEX))
#define GPIO_LPI2C2_SCL_2              (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_18_INDEX))
#define GPIO_LPI2C2_SDA_1              (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_01_INDEX))
#define GPIO_LPI2C2_SDA_2              (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_19_INDEX))

#define GPIO_LPI2C3_SCL_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_02_INDEX))
#define GPIO_LPI2C3_SCL_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_10_INDEX))
#define GPIO_LPI2C3_SDA_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_03_INDEX))
#define GPIO_LPI2C3_SDA_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_11_INDEX))

#define GPIO_LPI2C4_SCL_1              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_12_INDEX))
#define GPIO_LPI2C4_SCL_2              (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_24_INDEX))
#define GPIO_LPI2C4_SDA_1              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_13_INDEX))
#define GPIO_LPI2C4_SDA_2              (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_25_INDEX))

#define GPIO_LPI2C5_HREQ_1             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_12_INDEX))
#define GPIO_LPI2C5_SCL_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_05_INDEX))
#define GPIO_LPI2C5_SCL_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_09_INDEX))
#define GPIO_LPI2C5_SCLS_1             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_10_INDEX))
#define GPIO_LPI2C5_SDA_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_04_INDEX))
#define GPIO_LPI2C5_SDA_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_08_INDEX))
#define GPIO_LPI2C5_SDAS_1             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_11_INDEX))

#define GPIO_LPI2C6_SCL_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_07_INDEX))
#define GPIO_LPI2C6_SCL_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_11_INDEX))
#define GPIO_LPI2C6_SDA_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_06_INDEX))
#define GPIO_LPI2C6_SDA_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_10_INDEX))

/* Low Power Serial Peripheral Interface (LPSPI) */

#define GPIO_LPSPI1_PCS0_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_29_INDEX))
#define GPIO_LPSPI1_PCS0_2             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_01_INDEX))
#define GPIO_LPSPI1_PCS1_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_18_INDEX))
#define GPIO_LPSPI1_PCS2_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_19_INDEX))
#define GPIO_LPSPI1_PCS3_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_20_INDEX))
#define GPIO_LPSPI1_SCK_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_28_INDEX))
#define GPIO_LPSPI1_SCK_2              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_00_INDEX))
#define GPIO_LPSPI1_SDI_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_31_INDEX))
#define GPIO_LPSPI1_SDI_2              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_03_INDEX))
#define GPIO_LPSPI1_SDO_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_30_INDEX))
#define GPIO_LPSPI1_SDO_2              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_02_INDEX))

#define GPIO_LPSPI2_PCS0_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_25_INDEX))
#define GPIO_LPSPI2_PCS0_2             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_08_INDEX))
#define GPIO_LPSPI2_PCS1_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_21_INDEX))
#define GPIO_LPSPI2_PCS1_2             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_11_INDEX))
#define GPIO_LPSPI2_PCS2_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_22_INDEX))
#define GPIO_LPSPI2_PCS3_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_23_INDEX))
#define GPIO_LPSPI2_SCK_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_24_INDEX))
#define GPIO_LPSPI2_SCK_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_07_INDEX))
#define GPIO_LPSPI2_SDI_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_27_INDEX))
#define GPIO_LPSPI2_SDI_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_10_INDEX))
#define GPIO_LPSPI2_SDO_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_26_INDEX))
#define GPIO_LPSPI2_SDO_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_09_INDEX))

#define GPIO_LPSPI3_PCS0_1             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_05_INDEX))
#define GPIO_LPSPI3_PCS0_2             (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_07_INDEX))
#define GPIO_LPSPI3_PCS1_1             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_08_INDEX))
#define GPIO_LPSPI3_PCS1_2             (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_08_INDEX))
#define GPIO_LPSPI3_PCS2_1             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_09_INDEX))
#define GPIO_LPSPI3_PCS2_2             (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_09_INDEX))
#define GPIO_LPSPI3_PCS3_1             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_10_INDEX))
#define GPIO_LPSPI3_PCS3_2             (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_10_INDEX))
#define GPIO_LPSPI3_SCK_1              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_04_INDEX))
#define GPIO_LPSPI3_SCK_2              (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_04_INDEX))
#define GPIO_LPSPI3_SDI_1              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_07_INDEX))
#define GPIO_LPSPI3_SDI_2              (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_05_INDEX))
#define GPIO_LPSPI3_SDO_1              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_06_INDEX))
#define GPIO_LPSPI3_SDO_2              (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_06_INDEX))

#define GPIO_LPSPI4_PCS0_1             (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_01_INDEX))
#define GPIO_LPSPI4_PCS0_2             (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_15_INDEX))
#define GPIO_LPSPI4_PCS1_1             (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_04_INDEX))
#define GPIO_LPSPI4_PCS2_1             (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_05_INDEX))
#define GPIO_LPSPI4_PCS3_1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_06_INDEX))
#define GPIO_LPSPI4_SCK_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_00_INDEX))
#define GPIO_LPSPI4_SCK_2              (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_12_INDEX))
#define GPIO_LPSPI4_SDI_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_03_INDEX))
#define GPIO_LPSPI4_SDI_2              (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_13_INDEX))
#define GPIO_LPSPI4_SDO_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_02_INDEX))
#define GPIO_LPSPI4_SDO_2              (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_14_INDEX))

#define GPIO_LPSPI5_PCS0_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_03_INDEX))
#define GPIO_LPSPI5_PCS0_2             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_13_INDEX))
#define GPIO_LPSPI5_PCS1_1             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_06_INDEX))
#define GPIO_LPSPI5_PCS2_1             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_07_INDEX))
#define GPIO_LPSPI5_PCS3_1             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_08_INDEX))
#define GPIO_LPSPI5_SCK_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_02_INDEX))
#define GPIO_LPSPI5_SCK_2              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_12_INDEX))
#define GPIO_LPSPI5_SDI_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_05_INDEX))
#define GPIO_LPSPI5_SDI_2              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_15_INDEX))
#define GPIO_LPSPI5_SDO_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_04_INDEX))
#define GPIO_LPSPI5_SDO_2              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_14_INDEX))

#define GPIO_LPSPI6_PCS0_1             (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_09_INDEX))
#define GPIO_LPSPI6_PCS1_1             (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_08_INDEX))
#define GPIO_LPSPI6_PCS2_1             (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_07_INDEX))
#define GPIO_LPSPI6_PCS3_1             (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_06_INDEX))
#define GPIO_LPSPI6_SCK_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_10_INDEX))
#define GPIO_LPSPI6_SDI_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_12_INDEX))
#define GPIO_LPSPI6_SDO_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_11_INDEX))

/* Low Power Universal Asynchronous Receiver/Transmitter (LPUART) */

#define GPIO_LPUART1_CTS_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_26_INDEX))
#define GPIO_LPUART1_RTS_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_27_INDEX))
#define GPIO_LPUART1_RX_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_25_INDEX))
#define GPIO_LPUART1_RX_2              (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_03_INDEX))
#define GPIO_LPUART1_RX_3              (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_09_INDEX))
#define GPIO_LPUART1_TX_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_24_INDEX))
#define GPIO_LPUART1_TX_2              (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_02_INDEX))
#define GPIO_LPUART1_TX_3              (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_08_INDEX))

#define GPIO_LPUART2_CTS_1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_12_INDEX))
#define GPIO_LPUART2_RTS_1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_13_INDEX))
#define GPIO_LPUART2_RX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_11_INDEX))
#define GPIO_LPUART2_TX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_10_INDEX))

#define GPIO_LPUART3_CTS_1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_07_INDEX))
#define GPIO_LPUART3_RTS_1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_08_INDEX))
#define GPIO_LPUART3_RX_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_31_INDEX))
#define GPIO_LPUART3_TX_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_30_INDEX))

#define GPIO_LPUART4_CTS_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_05_INDEX))
#define GPIO_LPUART4_RTS_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_07_INDEX))
#define GPIO_LPUART4_RX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_04_INDEX))
#define GPIO_LPUART4_TX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_06_INDEX))

#define GPIO_LPUART5_CTS_1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_09_INDEX))
#define GPIO_LPUART5_RTS_1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_10_INDEX))
#define GPIO_LPUART5_RX_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_29_INDEX))
#define GPIO_LPUART5_TX_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_28_INDEX))

#define GPIO_LPUART6_CTS_1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_00_INDEX))
#define GPIO_LPUART6_RTS_1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_01_INDEX))
#define GPIO_LPUART6_RX_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_41_INDEX))
#define GPIO_LPUART6_TX_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_40_INDEX))

#define GPIO_LPUART7_CTS_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_02_INDEX))
#define GPIO_LPUART7_RTS_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_03_INDEX))
#define GPIO_LPUART7_RX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_07_INDEX))
#define GPIO_LPUART7_RX_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_01_INDEX))
#define GPIO_LPUART7_TX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_06_INDEX))
#define GPIO_LPUART7_TX_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_00_INDEX))

#define GPIO_LPUART8_CTS_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_04_INDEX))
#define GPIO_LPUART8_RTS_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_05_INDEX))
#define GPIO_LPUART8_RX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_09_INDEX))
#define GPIO_LPUART8_RX_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_03_INDEX))
#define GPIO_LPUART8_TX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_08_INDEX))
#define GPIO_LPUART8_TX_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_02_INDEX))

#define GPIO_LPUART9_CTS_1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_02_INDEX))
#define GPIO_LPUART9_RTS_1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_03_INDEX))
#define GPIO_LPUART9_RX_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_01_INDEX))
#define GPIO_LPUART9_TX_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_00_INDEX))

#define GPIO_LPUART10_CTS_1            (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_34_INDEX))
#define GPIO_LPUART10_RTS_1            (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_35_INDEX))
#define GPIO_LPUART10_RX_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_16_INDEX))
#define GPIO_LPUART10_RX_2             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_33_INDEX))
#define GPIO_LPUART10_TX_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_15_INDEX))
#define GPIO_LPUART10_TX_2             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_32_INDEX))

#define GPIO_LPUART11_CTS_1            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_10_INDEX))
#define GPIO_LPUART11_RTS_1            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_11_INDEX))
#define GPIO_LPUART11_RX_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_09_INDEX))
#define GPIO_LPUART11_RX_2             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_05_INDEX))
#define GPIO_LPUART11_TX_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_08_INDEX))
#define GPIO_LPUART11_TX_2             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_04_INDEX))

#define GPIO_LPUART12_CTS_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_05_INDEX))
#define GPIO_LPUART12_RTS_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_04_INDEX))
#define GPIO_LPUART12_RX_1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_07_INDEX))
#define GPIO_LPUART12_RX_2             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_01_INDEX))
#define GPIO_LPUART12_RX_3             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_11_INDEX))
#define GPIO_LPUART12_TX_1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_06_INDEX))
#define GPIO_LPUART12_TX_2             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_00_INDEX))
#define GPIO_LPUART12_TX_3             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_10_INDEX))

/* XTALOSC Reference Clock */

#define GPIO_REF_CLK_24M_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_01_INDEX))
#define GPIO_REF_CLK_24M_2             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_03_INDEX))
#define GPIO_REF_CLK_24M_3             (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_13_INDEX))
#define GPIO_REF_CLK_32K_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_00_INDEX))

/* Quad Timer (QTimer) */

#define GPIO_QTIMER1_TIMER0_1          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_17_INDEX))
#define GPIO_QTIMER1_TIMER0_2          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_00_INDEX))
#define GPIO_QTIMER1_TIMER0_3          (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_09_INDEX))
#define GPIO_QTIMER1_TIMER1_1          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_38_INDEX))
#define GPIO_QTIMER1_TIMER1_2          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_01_INDEX))
#define GPIO_QTIMER1_TIMER1_3          (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_10_INDEX))
#define GPIO_QTIMER1_TIMER2_1          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_02_INDEX))
#define GPIO_QTIMER1_TIMER2_2          (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_11_INDEX))
#define GPIO_QTIMER1_TIMER3_1          (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_12_INDEX))

#define GPIO_QTIMER2_TIMER0_1          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_18_INDEX))
#define GPIO_QTIMER2_TIMER0_2          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_03_INDEX))
#define GPIO_QTIMER2_TIMER0_3          (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_13_INDEX))
#define GPIO_QTIMER2_TIMER1_1          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_39_INDEX))
#define GPIO_QTIMER2_TIMER1_2          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_04_INDEX))
#define GPIO_QTIMER2_TIMER1_3          (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_14_INDEX))
#define GPIO_QTIMER2_TIMER2_1          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_05_INDEX))
#define GPIO_QTIMER2_TIMER2_2          (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_15_INDEX))
#define GPIO_QTIMER2_TIMER3_1          (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_16_INDEX))

#define GPIO_QTIMER3_TIMER0_1          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_19_INDEX))
#define GPIO_QTIMER3_TIMER0_2          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_06_INDEX))
#define GPIO_QTIMER3_TIMER0_3          (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_17_INDEX))
#define GPIO_QTIMER3_TIMER1_1          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_00_INDEX))
#define GPIO_QTIMER3_TIMER1_2          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_07_INDEX))
#define GPIO_QTIMER3_TIMER1_3          (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_18_INDEX))
#define GPIO_QTIMER3_TIMER2_1          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_08_INDEX))
#define GPIO_QTIMER3_TIMER2_2          (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_19_INDEX))
#define GPIO_QTIMER3_TIMER3_1          (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_20_INDEX))

#define GPIO_QTIMER4_TIMER0_1          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_20_INDEX))
#define GPIO_QTIMER4_TIMER0_2          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_09_INDEX))
#define GPIO_QTIMER4_TIMER0_3          (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_04 ALT9_INDEX))
#define GPIO_QTIMER4_TIMER1_1          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_01_INDEX))
#define GPIO_QTIMER4_TIMER1_2          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_10_INDEX))
#define GPIO_QTIMER4_TIMER1_3          (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_05_INDEX))
#define GPIO_QTIMER4_TIMER2_1          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_11_INDEX))
#define GPIO_QTIMER4_TIMER2_2          (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_06_INDEX))
#define GPIO_QTIMER4_TIMER3_1          (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_07_INDEX))

/* Smart External Memory Controller (SEMC) */

#define GPIO_SEMC_ADDR00_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_09_INDEX))
#define GPIO_SEMC_ADDR01_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_10_INDEX))
#define GPIO_SEMC_ADDR02_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_11_INDEX))
#define GPIO_SEMC_ADDR03_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_12_INDEX))
#define GPIO_SEMC_ADDR04_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_13_INDEX))
#define GPIO_SEMC_ADDR05_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_14_INDEX))
#define GPIO_SEMC_ADDR06_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_15_INDEX))
#define GPIO_SEMC_ADDR07_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_16_INDEX))
#define GPIO_SEMC_ADDR08_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_17_INDEX))
#define GPIO_SEMC_ADDR09_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_18_INDEX))
#define GPIO_SEMC_ADDR10_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_23_INDEX))
#define GPIO_SEMC_ADDR11_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_19_INDEX))
#define GPIO_SEMC_ADDR12_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_20_INDEX))
#define GPIO_SEMC_BA0_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_21_INDEX))
#define GPIO_SEMC_BA1_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_22_INDEX))
#define GPIO_SEMC_CAS_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_24_INDEX))
#define GPIO_SEMC_CKE_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_27_INDEX))
#define GPIO_SEMC_CLK_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_26_INDEX))
#define GPIO_SEMC_CLKX0_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_19_INDEX))
#define GPIO_SEMC_CLKX1_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_20_INDEX))
#define GPIO_SEMC_CS0_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_29_INDEX))
#define GPIO_SEMC_CSX0_1               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_41_INDEX))
#define GPIO_SEMC_CSX1_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_26_INDEX))
#define GPIO_SEMC_CSX2_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_27_INDEX))
#define GPIO_SEMC_CSX3_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_28_INDEX))
#define GPIO_SEMC_DATA00_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_00_INDEX))
#define GPIO_SEMC_DATA01_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_01_INDEX))
#define GPIO_SEMC_DATA02_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_02_INDEX))
#define GPIO_SEMC_DATA03_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_03_INDEX))
#define GPIO_SEMC_DATA04_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_04_INDEX))
#define GPIO_SEMC_DATA05_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_05_INDEX))
#define GPIO_SEMC_DATA06_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_06_INDEX))
#define GPIO_SEMC_DATA07_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_07_INDEX))
#define GPIO_SEMC_DATA08_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_30_INDEX))
#define GPIO_SEMC_DATA09_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_31_INDEX))
#define GPIO_SEMC_DATA10_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_32_INDEX))
#define GPIO_SEMC_DATA11_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_33_INDEX))
#define GPIO_SEMC_DATA12_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_34_INDEX))
#define GPIO_SEMC_DATA13_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_35_INDEX))
#define GPIO_SEMC_DATA14_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_36_INDEX))
#define GPIO_SEMC_DATA15_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_37_INDEX))
#define GPIO_SEMC_DATA16_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_00_INDEX))
#define GPIO_SEMC_DATA17_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_01_INDEX))
#define GPIO_SEMC_DATA18_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_02_INDEX))
#define GPIO_SEMC_DATA19_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_03_INDEX))
#define GPIO_SEMC_DATA20_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_04_INDEX))
#define GPIO_SEMC_DATA21_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_05_INDEX))
#define GPIO_SEMC_DATA22_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_06_INDEX))
#define GPIO_SEMC_DATA23_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_07_INDEX))
#define GPIO_SEMC_DATA24_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_09_INDEX))
#define GPIO_SEMC_DATA25_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_10_INDEX))
#define GPIO_SEMC_DATA26_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_11_INDEX))
#define GPIO_SEMC_DATA27_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_12_INDEX))
#define GPIO_SEMC_DATA28_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_13_INDEX))
#define GPIO_SEMC_DATA29_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_14_INDEX))
#define GPIO_SEMC_DATA30_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_15_INDEX))
#define GPIO_SEMC_DATA31_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_16_INDEX))
#define GPIO_SEMC_DM0_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_08_INDEX))
#define GPIO_SEMC_DM1_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_38_INDEX))
#define GPIO_SEMC_DM2_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_08_INDEX))
#define GPIO_SEMC_DM3_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_17_INDEX))
#define GPIO_SEMC_DQS_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_39_INDEX))
#define GPIO_SEMC_DQS4_1               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_18_INDEX))
#define GPIO_SEMC_RAS_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_25_INDEX))
#define GPIO_SEMC_RDY_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_40_INDEX))
#define GPIO_SEMC_WE_1                 (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B1_28_INDEX))

/* Boot Configuration */

#define GPIO_SRC_BOOT_MODE00_1         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_02_INDEX))
#define GPIO_SRC_BOOT_MODE01_1         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_LPSR_03_INDEX))
#define GPIO_SRC_BOOT_CFG00_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_06_INDEX))
#define GPIO_SRC_BOOT_CFG01_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_07_INDEX))
#define GPIO_SRC_BOOT_CFG02_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_08_INDEX))
#define GPIO_SRC_BOOT_CFG03_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_09_INDEX))
#define GPIO_SRC_BOOT_CFG04_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_10_INDEX))
#define GPIO_SRC_BOOT_CFG05_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_11_INDEX))
#define GPIO_SRC_BOOT_CFG06_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_00_INDEX))
#define GPIO_SRC_BOOT_CFG07_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_01_INDEX))
#define GPIO_SRC_BOOT_CFG08_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_02_INDEX))
#define GPIO_SRC_BOOT_CFG09_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_03_INDEX))
#define GPIO_SRC_BOOT_CFG10_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_04_INDEX))
#define GPIO_SRC_BOOT_CFG11_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_05_INDEX))

/* USB OTG */

#define GPIO_USB_OTG1_OC_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_11_INDEX))
#define GPIO_USB_OTG1_OC_2             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_35_INDEX))
#define GPIO_USB_OTG1_PWR_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_10_INDEX))
#define GPIO_USB_OTG1_PWR_2            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_34_INDEX))
#define GPIO_USB_OTG1_ID_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_09_INDEX))
#define GPIO_USB_OTG1_ID_2             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_33_INDEX))

#define GPIO_USB_OTG2_OC_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_06_INDEX))
#define GPIO_USB_OTG2_OC_2             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_30_INDEX))
#define GPIO_USB_OTG2_PWR_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_07_INDEX))
#define GPIO_USB_OTG2_PWR_2            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_31_INDEX))
#define GPIO_USB_OTG2_ID_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_08_INDEX))
#define GPIO_USB_OTG2_ID_2             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_32_INDEX))

/* Ultra Secured Digital Host Controller (uSDHC) */

#define GPIO_USDHC1_CD_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_08_INDEX))
#define GPIO_USDHC1_CD_2               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_32_INDEX))
#define GPIO_USDHC1_CLK_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_01_INDEX))
#define GPIO_USDHC1_CMD_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_00_INDEX))
#define GPIO_USDHC1_DATA0_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_02_INDEX))
#define GPIO_USDHC1_DATA1_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_03_INDEX))
#define GPIO_USDHC1_DATA2_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_04_INDEX))
#define GPIO_USDHC1_DATA3_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_05_INDEX))
#define GPIO_USDHC1_RESET_1            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_10_INDEX))
#define GPIO_USDHC1_RESET_2            (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_35_INDEX))
#define GPIO_USDHC1_VSELECT_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B2_01_INDEX))
#define GPIO_USDHC1_VSELECT_2          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_34_INDEX))
#define GPIO_USDHC1_WP_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_DISP_B1_09_INDEX))
#define GPIO_USDHC1_WP_2               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_33_INDEX))

#define GPIO_USDHC2_CD_1               (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_01_INDEX))
#define GPIO_USDHC2_CD_2               (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_26_INDEX))
#define GPIO_USDHC2_CLK_1              (GPIO_PERIPH | GPIO_ALT0  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_04_INDEX))
#define GPIO_USDHC2_CMD_1              (GPIO_PERIPH | GPIO_ALT0  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_05_INDEX))
#define GPIO_USDHC2_DATA0_1            (GPIO_PERIPH | GPIO_ALT0  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_03_INDEX))
#define GPIO_USDHC2_DATA1_1            (GPIO_PERIPH | GPIO_ALT0  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_02_INDEX))
#define GPIO_USDHC2_DATA2_1            (GPIO_PERIPH | GPIO_ALT0  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_01_INDEX))
#define GPIO_USDHC2_DATA3_1            (GPIO_PERIPH | GPIO_ALT0  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_00_INDEX))
#define GPIO_USDHC2_DATA4_1            (GPIO_PERIPH | GPIO_ALT0  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_08_INDEX))
#define GPIO_USDHC2_DATA5_1            (GPIO_PERIPH | GPIO_ALT0  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_09_INDEX))
#define GPIO_USDHC2_DATA6_1            (GPIO_PERIPH | GPIO_ALT0  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_10_INDEX))
#define GPIO_USDHC2_DATA7_1            (GPIO_PERIPH | GPIO_ALT0  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_11_INDEX))
#define GPIO_USDHC2_RESET_1            (GPIO_PERIPH | GPIO_ALT0  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_06_INDEX))
#define GPIO_USDHC2_RESET_2            (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_04_INDEX))
#define GPIO_USDHC2_RESET_3            (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_29_INDEX))
#define GPIO_USDHC2_STROBE_1           (GPIO_PERIPH | GPIO_ALT0  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B2_07_INDEX))
#define GPIO_USDHC2_VSELECT_1          (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_03_INDEX))
#define GPIO_USDHC2_VSELECT_2          (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_28_INDEX))
#define GPIO_USDHC2_WP_1               (GPIO_PERIPH | GPIO_ALT1  | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_B2_02_INDEX))
#define GPIO_USDHC2_WP_2               (GPIO_PERIPH | GPIO_ALT11 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_27_INDEX))

#endif /* __ARCH_ARM_SRC_IMXRT_HARDWARE_RT117X_IMXRT117X_PINMUX_H */
