/****************************************************************************
 * arch/arm/src/imxrt/hardware/rt106x/imxrt106x_pinmux.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_IMXRT_HARDWARE_RT106X_IMXRT106X_PINMUX_H
#define __ARCH_ARM_SRC_IMXRT_HARDWARE_RT106X_IMXRT106X_PINMUX_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include "imxrt_iomuxc.h"
#include "imxrt_gpio.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Alternate Pin Functions.
 *
 * Alternative pin selections are provided with a numeric suffix like _1, _2,
 * etc.  Drivers, however, will use the pin selection without the numeric
 * suffix.  Definitions to select the option to be used are required in the
 * board.h file.  For example, if LPUART1 CTS connects via the AD_B1_04 pin,
 * then the following definition should appear in the board.h header file
 * for that board:
 *
 *   #define GPIO_LPUART3_CTS GPIO_LPUART3_CTS_1
 *
 * The driver will then automatically configure to use the AD_B1_04 pin
 * for LPUART1 CTS.
 *
 * Note that a suffix is *still* given even if there's only one option
 * for a pin. This means it must be explicitly configured with other
 * decorations like the IOMUX options. Default IOMUX options can be found
 * in the imxrt_iomuxc.h file.
 *
 * WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific IOMUX options such
 * as frequency, open-drain, push-pull, and pull-up/down!  Just the basic
 * configuration is defined in this file.
 */

/* Analog Comparator (ACMP) */

#define GPIO_ACMP_OUT00_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_12_INDEX))
#define GPIO_ACMP_OUT01_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_13_INDEX))
#define GPIO_ACMP_OUT02_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_14_INDEX))
#define GPIO_ACMP_OUT03_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_15_INDEX))

/* ARM */

#define GPIO_ARM_CM7_RXEV_1            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_15_INDEX))
#define GPIO_ARM_CM7_TXEV_1            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_14_INDEX))

/* Clock Controller Module (CCM) */

#define GPIO_CCM_CLKO1_1               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_04_INDEX))
#define GPIO_CCM_CLKO2_1               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_05_INDEX))
#define GPIO_CCM_PMIC_RDY_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_32_INDEX))
#define GPIO_CCM_PMIC_READY_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_12_INDEX))
#define GPIO_CCM_PMIC_READY_2          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_08_INDEX))
#define GPIO_CCM_PMIC_READY_3          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_01_INDEX))
#define GPIO_CCM_PMIC_READY_4          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_03_INDEX))
#define GPIO_CCM_PMIC_VSTBY_REQ_1      (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_PMIC_STBY_REQ_INDEX))
#define GPIO_CCM_REF_EN_1              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_07_INDEX))
#define GPIO_CCM_STOP_1                (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_04_INDEX))
#define GPIO_CCM_WAIT_1                (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_02_INDEX))

/* CMOS Sensor Interface (CSI) */

#define GPIO_CSI_DATA00_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_10_INDEX))
#define GPIO_CSI_DATA01_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_11_INDEX))
#define GPIO_CSI_DATA02_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_11_INDEX))
#define GPIO_CSI_DATA02_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_15_INDEX))
#define GPIO_CSI_DATA03_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_10_INDEX))
#define GPIO_CSI_DATA03_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_14_INDEX))
#define GPIO_CSI_DATA04_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_09_INDEX))
#define GPIO_CSI_DATA04_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_13_INDEX))
#define GPIO_CSI_DATA05_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_08_INDEX))
#define GPIO_CSI_DATA05_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_12_INDEX))
#define GPIO_CSI_DATA06_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_07_INDEX))
#define GPIO_CSI_DATA06_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_11_INDEX))
#define GPIO_CSI_DATA07_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_06_INDEX))
#define GPIO_CSI_DATA07_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_10_INDEX))
#define GPIO_CSI_DATA08_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_05_INDEX))
#define GPIO_CSI_DATA08_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_09_INDEX))
#define GPIO_CSI_DATA09_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_04_INDEX))
#define GPIO_CSI_DATA09_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_08_INDEX))
#define GPIO_CSI_DATA10_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_09_INDEX))
#define GPIO_CSI_DATA11_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_08_INDEX))
#define GPIO_CSI_DATA12_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_07_INDEX))
#define GPIO_CSI_DATA13_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_06_INDEX))
#define GPIO_CSI_DATA14_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_05_INDEX))
#define GPIO_CSI_DATA15_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_04_INDEX))
#define GPIO_CSI_DATA16_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_37_INDEX))
#define GPIO_CSI_DATA17_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_36_INDEX))
#define GPIO_CSI_DATA18_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_35_INDEX))
#define GPIO_CSI_DATA19_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_34_INDEX))
#define GPIO_CSI_DATA20_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_33_INDEX))
#define GPIO_CSI_DATA21_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_32_INDEX))
#define GPIO_CSI_DATA22_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_31_INDEX))
#define GPIO_CSI_DATA23_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_30_INDEX))
#define GPIO_CSI_FIELD_1               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_38_INDEX))
#define GPIO_CSI_HSYNC_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_14_INDEX))
#define GPIO_CSI_HSYNC_2               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_15_INDEX))
#define GPIO_CSI_HSYNC_3               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_07_INDEX))
#define GPIO_CSI_MCLK_1                (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_15_INDEX))
#define GPIO_CSI_MCLK_2                (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_05_INDEX))
#define GPIO_CSI_PIXCLK_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_12_INDEX))
#define GPIO_CSI_PIXCLK_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_04_INDEX))
#define GPIO_CSI_VSYNC_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_13_INDEX))
#define GPIO_CSI_VSYNC_2               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_14_INDEX))
#define GPIO_CSI_VSYNC_3               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_06_INDEX))

/* Ethernet (ENET) */

#define GPIO_ENET_1588_EVENT0_IN_1     (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_15_INDEX))
#define GPIO_ENET_1588_EVENT0_IN_2     (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_12_INDEX))
#define GPIO_ENET_1588_EVENT0_IN_3     (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_11_INDEX))
#define GPIO_ENET_1588_EVENT0_OUT_1    (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_14_INDEX))
#define GPIO_ENET_1588_EVENT0_OUT_2    (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_13_INDEX))
#define GPIO_ENET_1588_EVENT0_OUT_3    (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_10_INDEX))
#define GPIO_ENET_1588_EVENT1_IN_1     (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_13_INDEX))
#define GPIO_ENET_1588_EVENT1_OUT_1    (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_12_INDEX))
#define GPIO_ENET_1588_EVENT2_IN_1     (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_03_INDEX))
#define GPIO_ENET_1588_EVENT2_OUT_1    (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_02_INDEX))
#define GPIO_ENET_1588_EVENT3_IN_1     (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_08_INDEX))
#define GPIO_ENET_1588_EVENT3_OUT_1    (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_07_INDEX))
#define GPIO_ENET_COL_1                (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_11_INDEX))
#define GPIO_ENET_CRS_1                (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_10_INDEX))
#define GPIO_ENET_MDC_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_14_INDEX))
#define GPIO_ENET_MDC_2                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_04_INDEX))
#define GPIO_ENET_MDC_3                (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_40_INDEX))
#define GPIO_ENET_MDIO_1               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_15_INDEX))
#define GPIO_ENET_MDIO_2               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_05_INDEX))
#define GPIO_ENET_MDIO_3               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_41_INDEX))
#define GPIO_ENET_RDATA00_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_20_INDEX))
#define GPIO_ENET_RDATA01_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_19_INDEX))
#define GPIO_ENET_REF_CLK_1            (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_25_INDEX))
#define GPIO_ENET_REF_CLK_2            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_10_INDEX))
#define GPIO_ENET_RX_CLK_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_06_INDEX))
#define GPIO_ENET_RX_DATA00_1          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_04_INDEX))
#define GPIO_ENET_RX_DATA01_1          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_05_INDEX))
#define GPIO_ENET_RX_DATA02_1          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_09_INDEX))
#define GPIO_ENET_RX_DATA03_1          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_08_INDEX))
#define GPIO_ENET_RX_EN_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_06_INDEX))
#define GPIO_ENET_RX_EN_2              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_23_INDEX))
#define GPIO_ENET_RX_ER_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_11_INDEX))
#define GPIO_ENET_RX_ER_2              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_26_INDEX))
#define GPIO_ENET_TDATA00_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_22_INDEX))
#define GPIO_ENET_TDATA01_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_21_INDEX))
#define GPIO_ENET_TX_CLK_1             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_10_INDEX))
#define GPIO_ENET_TX_CLK_2             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_25_INDEX))
#define GPIO_ENET_TX_DATA00_1          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_07_INDEX))
#define GPIO_ENET_TX_DATA01_1          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_08_INDEX))
#define GPIO_ENET_TX_DATA02_1          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_05_INDEX))
#define GPIO_ENET_TX_DATA03_1          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_04_INDEX))
#define GPIO_ENET_TX_EN_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_09_INDEX))
#define GPIO_ENET_TX_EN_2              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_24_INDEX))
#define GPIO_ENET_TX_ER_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_07_INDEX))

/* Ethernet (ENET2) */

#define GPIO_ENET2_MDC_1                (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_38_INDEX))
#define GPIO_ENET2_MDC_2                (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_00_INDEX))
#define GPIO_ENET2_MDIO_1               (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_39_INDEX))
#define GPIO_ENET2_MDIO_2               (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_01_INDEX))
#define GPIO_ENET2_TDATA0_1             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_30_INDEX))
#define GPIO_ENET2_TDATA0_2             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_12_INDEX))
#define GPIO_ENET2_TDATA0_3             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_14_INDEX))
#define GPIO_ENET2_TDATA1_1             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_31_INDEX))
#define GPIO_ENET2_TDATA1_2             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_13_INDEX))
#define GPIO_ENET2_TDATA1_3             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_15_INDEX))
#define GPIO_ENET2_TDATA2_1             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_05_INDEX))
#define GPIO_ENET2_TDATA3_1             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_04_INDEX))
#define GPIO_ENET2_TX_CLK_1             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_33_INDEX))
#define GPIO_ENET2_TX_CLK_2             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_15_INDEX))
#define GPIO_ENET2_TX_CLK_3             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_01_INDEX))
#define GPIO_ENET2_TX_EN_1              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_32_INDEX))
#define GPIO_ENET2_TX_EN_2              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_14_INDEX))
#define GPIO_ENET2_TX_EN_3              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_00_INDEX))
#define GPIO_ENET2_TX_ER_1              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_07_INDEX))
#define GPIO_ENET2_RDATA0_1             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_35_INDEX))
#define GPIO_ENET2_RDATA0_2             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_01_INDEX))
#define GPIO_ENET2_RDATA0_3             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_03_INDEX))
#define GPIO_ENET2_RDATA1_1             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_36_INDEX))
#define GPIO_ENET2_RDATA1_2             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_02_INDEX))
#define GPIO_ENET2_RDATA1_3             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_04_INDEX))
#define GPIO_ENET2_RDATA2_1             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_09_INDEX))
#define GPIO_ENET2_RDATA3_1             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_09_INDEX))
#define GPIO_ENET2_RX_CLK_1             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_06_INDEX))
#define GPIO_ENET2_RX_EN_1              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_37_INDEX))
#define GPIO_ENET2_RX_EN_2              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_03_INDEX))
#define GPIO_ENET2_RX_EN_3              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_05_INDEX))
#define GPIO_ENET2_RX_ER_1              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_34_INDEX))
#define GPIO_ENET2_RX_ER_2              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_00_INDEX))
#define GPIO_ENET2_RX_ER_3              (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_02_INDEX))
#define GPIO_ENET2_REF_CLK2_1           (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_33_INDEX))
#define GPIO_ENET2_REF_CLK2_2           (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_15_INDEX))
#define GPIO_ENET2_REF_CLK2_3           (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_01_INDEX))
#define GPIO_ENET2_1588_EVENT0_IN_1     (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_11_INDEX))
#define GPIO_ENET2_1588_EVENT0_IN_2     (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_03_INDEX))
#define GPIO_ENET2_COL_1                (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_11_INDEX))
#define GPIO_ENET2_CRS_1                (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_10_INDEX))
#define GPIO_ENET2_1588_EVENT1_IN_1     (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_11_INDEX))
#define GPIO_ENET2_1588_EVENT2_IN_1     (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_13_INDEX))
#define GPIO_ENET2_1588_EVENT3_IN_1     (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_15_INDEX))
#define GPIO_ENET2_1588_EVENT0_OUT_1    (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_00_INDEX))
#define GPIO_ENET2_1588_EVENT0_OUT_2    (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_PIO_B0_02_INDEX))
#define GPIO_ENET2_1588_EVENT1_OUT_1    (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_10_INDEX))
#define GPIO_ENET2_1588_EVENT2_OUT_1    (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_12_INDEX))
#define GPIO_ENET2_1588_EVENT3_OUT_1    (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_14_INDEX))

/* External Watchdog Monitor (EWM) */

#define GPIO_EWM_OUT_1                 (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_11_INDEX))
#define GPIO_EWM_OUT_2                 (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_13_INDEX))
#define GPIO_EWM_OUT_3                 (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_01_INDEX))

/* Flexible Controller Area Network (FLEXCAN) */

#define GPIO_FLEXCAN1_RX_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_09_INDEX))
#define GPIO_FLEXCAN1_RX_2             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_03_INDEX))
#define GPIO_FLEXCAN1_RX_3             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_18_INDEX))
#define GPIO_FLEXCAN1_RX_4             (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_03_INDEX))
#define GPIO_FLEXCAN1_TX_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_08_INDEX))
#define GPIO_FLEXCAN1_TX_2             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_02_INDEX))
#define GPIO_FLEXCAN1_TX_3             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_17_INDEX))
#define GPIO_FLEXCAN1_TX_4             (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_02_INDEX))

#define GPIO_FLEXCAN2_RX_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_03_INDEX))
#define GPIO_FLEXCAN2_RX_2             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_10_INDEX))
#define GPIO_FLEXCAN2_RX_3             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_15_INDEX))
#define GPIO_FLEXCAN2_RX_4             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_09_INDEX))
#define GPIO_FLEXCAN2_TX_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_02_INDEX))
#define GPIO_FLEXCAN2_TX_2             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_09_INDEX))
#define GPIO_FLEXCAN2_TX_3             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_14_INDEX))
#define GPIO_FLEXCAN2_TX_4             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_08_INDEX))

#define GPIO_FLEXCAN3_RX_1             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_11_INDEX))
#define GPIO_FLEXCAN3_RX_2             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_15_INDEX))
#define GPIO_FLEXCAN3_RX_3             (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_37_INDEX))
#define GPIO_FLEXCAN3_TX_1             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_10_INDEX))
#define GPIO_FLEXCAN3_TX_2             (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_14_INDEX))
#define GPIO_FLEXCAN3_TX_3             (GPIO_PERIPH | GPIO_ALT9 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_36_INDEX))

/* Flexible I/O (FlexIO) */

#define GPIO_FLEXIO1_FLEXIO00_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_00_INDEX))
#define GPIO_FLEXIO1_FLEXIO01_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_01_INDEX))
#define GPIO_FLEXIO1_FLEXIO02_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_02_INDEX))
#define GPIO_FLEXIO1_FLEXIO03_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_03_INDEX))
#define GPIO_FLEXIO1_FLEXIO04_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_04_INDEX))
#define GPIO_FLEXIO1_FLEXIO05_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_05_INDEX))
#define GPIO_FLEXIO1_FLEXIO06_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_06_INDEX))
#define GPIO_FLEXIO1_FLEXIO07_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_07_INDEX))
#define GPIO_FLEXIO1_FLEXIO08_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_08_INDEX))
#define GPIO_FLEXIO1_FLEXIO09_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_09_INDEX))
#define GPIO_FLEXIO1_FLEXIO10_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_10_INDEX))
#define GPIO_FLEXIO1_FLEXIO11_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_11_INDEX))
#define GPIO_FLEXIO1_FLEXIO12_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_26_INDEX))
#define GPIO_FLEXIO1_FLEXIO13_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_27_INDEX))
#define GPIO_FLEXIO1_FLEXIO14_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_28_INDEX))
#define GPIO_FLEXIO1_FLEXIO15_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_29_INDEX))

#define GPIO_FLEXIO2_FLEXIO00_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_00_INDEX))
#define GPIO_FLEXIO2_FLEXIO01_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_01_INDEX))
#define GPIO_FLEXIO2_FLEXIO02_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_02_INDEX))
#define GPIO_FLEXIO2_FLEXIO03_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_03_INDEX))
#define GPIO_FLEXIO2_FLEXIO04_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_04_INDEX))
#define GPIO_FLEXIO2_FLEXIO05_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_05_INDEX))
#define GPIO_FLEXIO2_FLEXIO06_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_06_INDEX))
#define GPIO_FLEXIO2_FLEXIO07_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_07_INDEX))
#define GPIO_FLEXIO2_FLEXIO08_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_08_INDEX))
#define GPIO_FLEXIO2_FLEXIO09_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_09_INDEX))
#define GPIO_FLEXIO2_FLEXIO10_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_10_INDEX))
#define GPIO_FLEXIO2_FLEXIO11_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_11_INDEX))
#define GPIO_FLEXIO2_FLEXIO12_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_12_INDEX))
#define GPIO_FLEXIO2_FLEXIO13_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_13_INDEX))
#define GPIO_FLEXIO2_FLEXIO14_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_14_INDEX))
#define GPIO_FLEXIO2_FLEXIO15_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_15_INDEX))
#define GPIO_FLEXIO2_FLEXIO16_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_00_INDEX))
#define GPIO_FLEXIO2_FLEXIO17_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_01_INDEX))
#define GPIO_FLEXIO2_FLEXIO18_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_02_INDEX))
#define GPIO_FLEXIO2_FLEXIO19_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_03_INDEX))
#define GPIO_FLEXIO2_FLEXIO20_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_04_INDEX))
#define GPIO_FLEXIO2_FLEXIO21_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_05_INDEX))
#define GPIO_FLEXIO2_FLEXIO22_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_06_INDEX))
#define GPIO_FLEXIO2_FLEXIO23_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_07_INDEX))
#define GPIO_FLEXIO2_FLEXIO24_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_08_INDEX))
#define GPIO_FLEXIO2_FLEXIO25_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_09_INDEX))
#define GPIO_FLEXIO2_FLEXIO26_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_10_INDEX))
#define GPIO_FLEXIO2_FLEXIO27_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_11_INDEX))
#define GPIO_FLEXIO2_FLEXIO28_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_12_INDEX))
#define GPIO_FLEXIO2_FLEXIO29_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_13_INDEX))
#define GPIO_FLEXIO2_FLEXIO30_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_14_INDEX))
#define GPIO_FLEXIO2_FLEXIO31_1        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_15_INDEX))

/* Enhanced Flex Pulse Width Modulator (eFlexPWM) */

#define GPIO_FLEXPWM1_PWMA00_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_23_INDEX))
#define GPIO_FLEXPWM1_PWMA00_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_00_INDEX))
#define GPIO_FLEXPWM1_PWMA01_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_25_INDEX))
#define GPIO_FLEXPWM1_PWMA01_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_02_INDEX))
#define GPIO_FLEXPWM1_PWMA02_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_27_INDEX))
#define GPIO_FLEXPWM1_PWMA02_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_04_INDEX))
#define GPIO_FLEXPWM1_PWMA03_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_10_INDEX))
#define GPIO_FLEXPWM1_PWMA03_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_38_INDEX))
#define GPIO_FLEXPWM1_PWMA03_3         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_00_INDEX))
#define GPIO_FLEXPWM1_PWMA03_4         (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_12_INDEX))
#define GPIO_FLEXPWM1_PWMA03_5         (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_00_INDEX))
#define GPIO_FLEXPWM1_PWMB00_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_24_INDEX))
#define GPIO_FLEXPWM1_PWMB00_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_01_INDEX))
#define GPIO_FLEXPWM1_PWMB01_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_26_INDEX))
#define GPIO_FLEXPWM1_PWMB01_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_03_INDEX))
#define GPIO_FLEXPWM1_PWMB02_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_28_INDEX))
#define GPIO_FLEXPWM1_PWMB02_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_05_INDEX))
#define GPIO_FLEXPWM1_PWMB03_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_11_INDEX))
#define GPIO_FLEXPWM1_PWMB03_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_39_INDEX))
#define GPIO_FLEXPWM1_PWMB03_3         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_01_INDEX))
#define GPIO_FLEXPWM1_PWMB03_4         (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_13_INDEX))
#define GPIO_FLEXPWM1_PWMB03_5         (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_01_INDEX))
#define GPIO_FLEXPWM1_PWMX00_1         (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_02_INDEX))
#define GPIO_FLEXPWM1_PWMX01_1         (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_03_INDEX))
#define GPIO_FLEXPWM1_PWMX02_1         (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_12_INDEX))
#define GPIO_FLEXPWM1_PWMX03_1         (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_13_INDEX))

#define GPIO_FLEXPWM2_PWMA00_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_06_INDEX))
#define GPIO_FLEXPWM2_PWMA00_2         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_06_INDEX))
#define GPIO_FLEXPWM2_PWMA01_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_08_INDEX))
#define GPIO_FLEXPWM2_PWMA01_2         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_08_INDEX))
#define GPIO_FLEXPWM2_PWMA02_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_10_INDEX))
#define GPIO_FLEXPWM2_PWMA02_2         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_10_INDEX))
#define GPIO_FLEXPWM2_PWMA03_1         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_00_INDEX))
#define GPIO_FLEXPWM2_PWMA03_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_09_INDEX))
#define GPIO_FLEXPWM2_PWMA03_3         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_19_INDEX))
#define GPIO_FLEXPWM2_PWMA03_4         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_02_INDEX))
#define GPIO_FLEXPWM2_PWMA03_5         (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_02_INDEX))
#define GPIO_FLEXPWM2_PWMB00_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_07_INDEX))
#define GPIO_FLEXPWM2_PWMB00_2         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_07_INDEX))
#define GPIO_FLEXPWM2_PWMB01_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_09_INDEX))
#define GPIO_FLEXPWM2_PWMB01_2         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_09_INDEX))
#define GPIO_FLEXPWM2_PWMB02_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_11_INDEX))
#define GPIO_FLEXPWM2_PWMB02_2         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_11_INDEX))
#define GPIO_FLEXPWM2_PWMB03_1         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_01_INDEX))
#define GPIO_FLEXPWM2_PWMB03_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_20_INDEX))
#define GPIO_FLEXPWM2_PWMB03_3         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_03_INDEX))
#define GPIO_FLEXPWM2_PWMB03_4         (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_03_INDEX))

#define GPIO_FLEXPWM3_PWMA00_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_29_INDEX))
#define GPIO_FLEXPWM3_PWMA01_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_31_INDEX))
#define GPIO_FLEXPWM3_PWMA02_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_33_INDEX))
#define GPIO_FLEXPWM3_PWMA03_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_21_INDEX))
#define GPIO_FLEXPWM3_PWMB00_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_30_INDEX))
#define GPIO_FLEXPWM3_PWMB01_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_32_INDEX))
#define GPIO_FLEXPWM3_PWMB02_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_34_INDEX))
#define GPIO_FLEXPWM3_PWMB03_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_22_INDEX))

#define GPIO_FLEXPWM4_PWMA00_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_08_INDEX))
#define GPIO_FLEXPWM4_PWMA00_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_00_INDEX))
#define GPIO_FLEXPWM4_PWMA01_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_09_INDEX))
#define GPIO_FLEXPWM4_PWMA01_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_02_INDEX))
#define GPIO_FLEXPWM4_PWMA02_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_14_INDEX))
#define GPIO_FLEXPWM4_PWMA02_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_04_INDEX))
#define GPIO_FLEXPWM4_PWMA03_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_15_INDEX))
#define GPIO_FLEXPWM4_PWMA03_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_17_INDEX))
#define GPIO_FLEXPWM4_PWMB00_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_01_INDEX))
#define GPIO_FLEXPWM4_PWMB01_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_03_INDEX))
#define GPIO_FLEXPWM4_PWMB02_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_05_INDEX))
#define GPIO_FLEXPWM4_PWMB03_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_18_INDEX))

/* Flexible SPI (FlexSPI) */

#define GPIO_FLEXSPIA_DATA00_1         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_13_INDEX))
#define GPIO_FLEXSPIA_DATA00_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_08_INDEX))
#define GPIO_FLEXSPIA_DATA01_1         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_12_INDEX))
#define GPIO_FLEXSPIA_DATA01_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_09_INDEX))
#define GPIO_FLEXSPIA_DATA02_1         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_11_INDEX))
#define GPIO_FLEXSPIA_DATA02_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_10_INDEX))
#define GPIO_FLEXSPIA_DATA03_1         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_10_INDEX))
#define GPIO_FLEXSPIA_DATA03_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_11_INDEX))
#define GPIO_FLEXSPIA_DQS_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_09_INDEX))
#define GPIO_FLEXSPIA_DQS_2            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_05_INDEX))
#define GPIO_FLEXSPIA_SCLK_1           (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_14_INDEX))
#define GPIO_FLEXSPIA_SCLK_2           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_07_INDEX))
#define GPIO_FLEXSPIA_SS0_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_15_INDEX))
#define GPIO_FLEXSPIA_SS0_2            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_06_INDEX))
#define GPIO_FLEXSPIA_SS1_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_08_INDEX))
#define GPIO_FLEXSPIA_SS1_2            (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_04_INDEX))
#define GPIO_FLEXSPIA_SS1_3            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_00_INDEX))

#define GPIO_FLEXSPIB_DATA00_1         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_07_INDEX))
#define GPIO_FLEXSPIB_DATA00_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_03_INDEX))
#define GPIO_FLEXSPIB_DATA01_1         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_06_INDEX))
#define GPIO_FLEXSPIB_DATA01_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_02_INDEX))
#define GPIO_FLEXSPIB_DATA02_1         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_05_INDEX))
#define GPIO_FLEXSPIB_DATA02_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_01_INDEX))
#define GPIO_FLEXSPIB_DATA03_1         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_04_INDEX))
#define GPIO_FLEXSPIB_DATA03_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_00_INDEX))
#define GPIO_FLEXSPIB_DQS_1            (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_05_INDEX))
#define GPIO_FLEXSPIB_SCLK_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_04_INDEX))
#define GPIO_FLEXSPIB_SS0_1            (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_04_INDEX))
#define GPIO_FLEXSPIB_SS0_2            (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_05_INDEX))
#define GPIO_FLEXSPIB_SS1_1            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_01_INDEX))

/* GPIO */

#define GPIO_GPIO1_IO00_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_00_INDEX))
#define GPIO_GPIO1_IO01_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_01_INDEX))
#define GPIO_GPIO1_IO02_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_02_INDEX))
#define GPIO_GPIO1_IO03_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_03_INDEX))
#define GPIO_GPIO1_IO04_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_04_INDEX))
#define GPIO_GPIO1_IO05_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_05_INDEX))
#define GPIO_GPIO1_IO06_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_06_INDEX))
#define GPIO_GPIO1_IO07_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_07_INDEX))
#define GPIO_GPIO1_IO08_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_08_INDEX))
#define GPIO_GPIO1_IO09_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_09_INDEX))
#define GPIO_GPIO1_IO10_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_10_INDEX))
#define GPIO_GPIO1_IO11_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_11_INDEX))
#define GPIO_GPIO1_IO12_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_12_INDEX))
#define GPIO_GPIO1_IO13_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_13_INDEX))
#define GPIO_GPIO1_IO14_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_14_INDEX))
#define GPIO_GPIO1_IO15_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_15_INDEX))
#define GPIO_GPIO1_IO16_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_00_INDEX))
#define GPIO_GPIO1_IO17_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_01_INDEX))
#define GPIO_GPIO1_IO18_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_02_INDEX))
#define GPIO_GPIO1_IO19_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_03_INDEX))
#define GPIO_GPIO1_IO20_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_04_INDEX))
#define GPIO_GPIO1_IO21_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_05_INDEX))
#define GPIO_GPIO1_IO22_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_06_INDEX))
#define GPIO_GPIO1_IO23_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_07_INDEX))
#define GPIO_GPIO1_IO24_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_08_INDEX))
#define GPIO_GPIO1_IO25_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_09_INDEX))
#define GPIO_GPIO1_IO26_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_10_INDEX))
#define GPIO_GPIO1_IO27_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_11_INDEX))
#define GPIO_GPIO1_IO28_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_12_INDEX))
#define GPIO_GPIO1_IO29_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_13_INDEX))
#define GPIO_GPIO1_IO30_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_14_INDEX))
#define GPIO_GPIO1_IO31_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_15_INDEX))

#define GPIO_GPIO2_IO00_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_00_INDEX))
#define GPIO_GPIO2_IO01_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_01_INDEX))
#define GPIO_GPIO2_IO02_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_02_INDEX))
#define GPIO_GPIO2_IO03_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_03_INDEX))
#define GPIO_GPIO2_IO04_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_04_INDEX))
#define GPIO_GPIO2_IO05_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_05_INDEX))
#define GPIO_GPIO2_IO06_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_06_INDEX))
#define GPIO_GPIO2_IO07_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_07_INDEX))
#define GPIO_GPIO2_IO08_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_08_INDEX))
#define GPIO_GPIO2_IO09_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_09_INDEX))
#define GPIO_GPIO2_IO10_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_10_INDEX))
#define GPIO_GPIO2_IO11_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_11_INDEX))
#define GPIO_GPIO2_IO12_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_12_INDEX))
#define GPIO_GPIO2_IO13_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_13_INDEX))
#define GPIO_GPIO2_IO14_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_14_INDEX))
#define GPIO_GPIO2_IO15_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_15_INDEX))
#define GPIO_GPIO2_IO16_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_00_INDEX))
#define GPIO_GPIO2_IO17_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_01_INDEX))
#define GPIO_GPIO2_IO18_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_02_INDEX))
#define GPIO_GPIO2_IO19_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_03_INDEX))
#define GPIO_GPIO2_IO20_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_04_INDEX))
#define GPIO_GPIO2_IO21_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_05_INDEX))
#define GPIO_GPIO2_IO22_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_06_INDEX))
#define GPIO_GPIO2_IO23_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_07_INDEX))
#define GPIO_GPIO2_IO24_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_08_INDEX))
#define GPIO_GPIO2_IO25_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_09_INDEX))
#define GPIO_GPIO2_IO26_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_10_INDEX))
#define GPIO_GPIO2_IO27_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_11_INDEX))
#define GPIO_GPIO2_IO28_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_12_INDEX))
#define GPIO_GPIO2_IO29_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_13_INDEX))
#define GPIO_GPIO2_IO30_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_14_INDEX))
#define GPIO_GPIO2_IO31_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_15_INDEX))

#define GPIO_GPIO3_IO00_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_00_INDEX))
#define GPIO_GPIO3_IO01_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_01_INDEX))
#define GPIO_GPIO3_IO02_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_02_INDEX))
#define GPIO_GPIO3_IO03_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_03_INDEX))
#define GPIO_GPIO3_IO04_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_04_INDEX))
#define GPIO_GPIO3_IO05_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_05_INDEX))
#define GPIO_GPIO3_IO06_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_06_INDEX))
#define GPIO_GPIO3_IO07_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_07_INDEX))
#define GPIO_GPIO3_IO08_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_08_INDEX))
#define GPIO_GPIO3_IO09_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_09_INDEX))
#define GPIO_GPIO3_IO10_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_10_INDEX))
#define GPIO_GPIO3_IO11_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_11_INDEX))
#define GPIO_GPIO3_IO12_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_00_INDEX))
#define GPIO_GPIO3_IO13_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_01_INDEX))
#define GPIO_GPIO3_IO14_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_02_INDEX))
#define GPIO_GPIO3_IO15_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_03_INDEX))
#define GPIO_GPIO3_IO16_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_04_INDEX))
#define GPIO_GPIO3_IO17_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_05_INDEX))
#define GPIO_GPIO3_IO18_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_32_INDEX))
#define GPIO_GPIO3_IO19_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_33_INDEX))
#define GPIO_GPIO3_IO20_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_34_INDEX))
#define GPIO_GPIO3_IO21_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_35_INDEX))
#define GPIO_GPIO3_IO22_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_36_INDEX))
#define GPIO_GPIO3_IO23_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_37_INDEX))
#define GPIO_GPIO3_IO24_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_38_INDEX))
#define GPIO_GPIO3_IO25_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_39_INDEX))
#define GPIO_GPIO3_IO26_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_40_INDEX))
#define GPIO_GPIO3_IO27_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_41_INDEX))

#define GPIO_GPIO4_IO00_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_00_INDEX))
#define GPIO_GPIO4_IO01_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_01_INDEX))
#define GPIO_GPIO4_IO02_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_02_INDEX))
#define GPIO_GPIO4_IO03_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_03_INDEX))
#define GPIO_GPIO4_IO04_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_04_INDEX))
#define GPIO_GPIO4_IO05_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_05_INDEX))
#define GPIO_GPIO4_IO06_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_06_INDEX))
#define GPIO_GPIO4_IO07_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_07_INDEX))
#define GPIO_GPIO4_IO08_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_08_INDEX))
#define GPIO_GPIO4_IO09_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_09_INDEX))
#define GPIO_GPIO4_IO10_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_10_INDEX))
#define GPIO_GPIO4_IO11_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_11_INDEX))
#define GPIO_GPIO4_IO12_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_12_INDEX))
#define GPIO_GPIO4_IO13_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_13_INDEX))
#define GPIO_GPIO4_IO14_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_14_INDEX))
#define GPIO_GPIO4_IO15_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_15_INDEX))
#define GPIO_GPIO4_IO16_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_16_INDEX))
#define GPIO_GPIO4_IO17_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_17_INDEX))
#define GPIO_GPIO4_IO18_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_18_INDEX))
#define GPIO_GPIO4_IO19_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_19_INDEX))
#define GPIO_GPIO4_IO20_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_20_INDEX))
#define GPIO_GPIO4_IO21_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_21_INDEX))
#define GPIO_GPIO4_IO22_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_22_INDEX))
#define GPIO_GPIO4_IO23_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_23_INDEX))
#define GPIO_GPIO4_IO24_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_24_INDEX))
#define GPIO_GPIO4_IO25_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_25_INDEX))
#define GPIO_GPIO4_IO26_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_26_INDEX))
#define GPIO_GPIO4_IO27_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_27_INDEX))
#define GPIO_GPIO4_IO28_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_28_INDEX))
#define GPIO_GPIO4_IO29_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_29_INDEX))
#define GPIO_GPIO4_IO30_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_30_INDEX))
#define GPIO_GPIO4_IO31_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_31_INDEX))

#define GPIO_GPIO5_IO00_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_WAKEUP_INDEX))
#define GPIO_GPIO5_IO01_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_PMIC_ON_REQ_INDEX))
#define GPIO_GPIO5_IO02_1              (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_PMIC_STBY_REQ_INDEX))

/* General Purpose Timer (GPT) */

#define GPIO_GPT1_CAPTURE1_1           (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_24_INDEX))
#define GPIO_GPT1_CAPTURE1_2           (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_05_INDEX))
#define GPIO_GPT1_CAPTURE2_1           (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_23_INDEX))
#define GPIO_GPT1_CAPTURE2_2           (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_06_INDEX))
#define GPIO_GPT1_CLK_1                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_13_INDEX))
#define GPIO_GPT1_COMPARE1_1           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_35_INDEX))
#define GPIO_GPT1_COMPARE2_1           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_36_INDEX))
#define GPIO_GPT1_COMPARE3_1           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_37_INDEX))

#define GPIO_GPT2_CAPTURE1_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_41_INDEX))
#define GPIO_GPT2_CAPTURE2_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_40_INDEX))
#define GPIO_GPT2_CLK_1                (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_09_INDEX))
#define GPIO_GPT2_COMPARE2_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_07_INDEX))
#define GPIO_GPT2_COMPARE3_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_08_INDEX))
#define GPIO_GPT2_COMPARE3_2           (GPIO_PERIPH | GPIO_ALT8 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_07_INDEX))

/* JTAG */

#define GPIO_JTAG_ACTIVE_1             (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_03_INDEX))
#define GPIO_JTAG_DE_1                 (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_01_INDEX))
#define GPIO_JTAG_DONE_1               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_00_INDEX))
#define GPIO_JTAG_FAIL_1               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_02_INDEX))
#define GPIO_JTAG_MOD_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_08_INDEX))
#define GPIO_JTAG_TCK_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_07_INDEX))
#define GPIO_JTAG_TDI_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_09_INDEX))
#define GPIO_JTAG_TDO_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_10_INDEX))
#define GPIO_JTAG_TRSTB_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_11_INDEX))

/* Keypad Port (KPP) */

#define GPIO_KPP_COL00_1               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_15_INDEX))
#define GPIO_KPP_COL01_1               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_13_INDEX))
#define GPIO_KPP_COL02_1               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_11_INDEX))
#define GPIO_KPP_COL03_1               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_09_INDEX))
#define GPIO_KPP_COL04_1               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_07_INDEX))
#define GPIO_KPP_COL05_1               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_05_INDEX))
#define GPIO_KPP_COL06_1               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_03_INDEX))
#define GPIO_KPP_COL07_1               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_01_INDEX))
#define GPIO_KPP_ROW00_1               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_14_INDEX))
#define GPIO_KPP_ROW01_1               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_12_INDEX))
#define GPIO_KPP_ROW02_1               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_10_INDEX))
#define GPIO_KPP_ROW03_1               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_08_INDEX))
#define GPIO_KPP_ROW04_1               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_06_INDEX))
#define GPIO_KPP_ROW05_1               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_04_INDEX))
#define GPIO_KPP_ROW06_1               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_02_INDEX))
#define GPIO_KPP_ROW07_1               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_00_INDEX))

/* LCD */

#define GPIO_LCD_CLK_1                 (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_00_INDEX))
#define GPIO_LCD_DATA00_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_04_INDEX))
#define GPIO_LCD_DATA01_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_05_INDEX))
#define GPIO_LCD_DATA02_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_06_INDEX))
#define GPIO_LCD_DATA03_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_07_INDEX))
#define GPIO_LCD_DATA04_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_08_INDEX))
#define GPIO_LCD_DATA05_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_09_INDEX))
#define GPIO_LCD_DATA06_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_10_INDEX))
#define GPIO_LCD_DATA07_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_11_INDEX))
#define GPIO_LCD_DATA08_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_12_INDEX))
#define GPIO_LCD_DATA09_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_13_INDEX))
#define GPIO_LCD_DATA10_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_14_INDEX))
#define GPIO_LCD_DATA11_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_15_INDEX))
#define GPIO_LCD_DATA12_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_00_INDEX))
#define GPIO_LCD_DATA13_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_01_INDEX))
#define GPIO_LCD_DATA14_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_02_INDEX))
#define GPIO_LCD_DATA15_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_03_INDEX))
#define GPIO_LCD_DATA16_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_04_INDEX))
#define GPIO_LCD_DATA17_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_05_INDEX))
#define GPIO_LCD_DATA18_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_06_INDEX))
#define GPIO_LCD_DATA19_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_07_INDEX))
#define GPIO_LCD_DATA20_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_08_INDEX))
#define GPIO_LCD_DATA21_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_09_INDEX))
#define GPIO_LCD_DATA22_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_10_INDEX))
#define GPIO_LCD_DATA23_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_11_INDEX))
#define GPIO_LCD_ENABLE_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_01_INDEX))
#define GPIO_LCD_HSYNC_1               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_02_INDEX))
#define GPIO_LCD_VSYNC_1               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_03_INDEX))

/* Low Power Inter-Integrated Circuit (LPI2C) */

#define GPIO_LPI2C1_HREQ_1             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_02_INDEX))
#define GPIO_LPI2C1_SCL_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_04_INDEX))
#define GPIO_LPI2C1_SCL_2              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_00_INDEX))
#define GPIO_LPI2C1_SCLS_1             (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_00_INDEX))
#define GPIO_LPI2C1_SDA_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_05_INDEX))
#define GPIO_LPI2C1_SDA_2              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_01_INDEX))
#define GPIO_LPI2C1_SDAS_1             (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_01_INDEX))

#define GPIO_LPI2C2_SCL_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_04_INDEX))
#define GPIO_LPI2C2_SCL_2              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_11_INDEX))
#define GPIO_LPI2C2_SDA_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_05_INDEX))
#define GPIO_LPI2C2_SDA_2              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_10_INDEX))

#define GPIO_LPI2C3_SCL_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_07_INDEX))
#define GPIO_LPI2C3_SCL_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_22_INDEX))
#define GPIO_LPI2C3_SCL_3              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_00_INDEX))
#define GPIO_LPI2C3_SDA_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_06_INDEX))
#define GPIO_LPI2C3_SDA_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_21_INDEX))
#define GPIO_LPI2C3_SDA_3              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_01_INDEX))

#define GPIO_LPI2C4_SCL_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_12_INDEX))
#define GPIO_LPI2C4_SCL_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_12_INDEX))
#define GPIO_LPI2C4_SDA_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_13_INDEX))
#define GPIO_LPI2C4_SDA_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_11_INDEX))

/* Low Power Serial Peripheral Interface (LPSPI) */

#define GPIO_LPSPI1_PCS0_1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_30_INDEX))
#define GPIO_LPSPI1_PCS0_2             (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_01_INDEX))
#define GPIO_LPSPI1_PCS_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_31_INDEX))
#define GPIO_LPSPI1_PCS2_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_40_INDEX))
#define GPIO_LPSPI1_PCS3_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_41_INDEX))
#define GPIO_LPSPI1_SCK_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_27_INDEX))
#define GPIO_LPSPI1_SCK_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_00_INDEX))
#define GPIO_LPSPI1_SDI_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_29_INDEX))
#define GPIO_LPSPI1_SDI_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_03_INDEX))
#define GPIO_LPSPI1_SDO_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_28_INDEX))
#define GPIO_LPSPI1_SDO_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_02_INDEX))

#define GPIO_LPSPI2_PCS0_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_01_INDEX))
#define GPIO_LPSPI2_PCS0_2             (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_06_INDEX))
#define GPIO_LPSPI2_PCS1_1             (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_14_INDEX))
#define GPIO_LPSPI2_PCS2_1             (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_10_INDEX))
#define GPIO_LPSPI2_PCS3_1             (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_11_INDEX))
#define GPIO_LPSPI2_SCK_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_00_INDEX))
#define GPIO_LPSPI2_SCK_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_07_INDEX))
#define GPIO_LPSPI2_SDI_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_03_INDEX))
#define GPIO_LPSPI2_SDI_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_09_INDEX))
#define GPIO_LPSPI2_SDO_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_02_INDEX))
#define GPIO_LPSPI2_SDO_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_08_INDEX))

#define GPIO_LPSPI3_PCS0_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_12_INDEX))
#define GPIO_LPSPI3_PCS0_2             (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_03_INDEX))
#define GPIO_LPSPI3_PCS1_1             (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_04_INDEX))
#define GPIO_LPSPI3_PCS2_1             (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_05_INDEX))
#define GPIO_LPSPI3_PCS3_1             (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_06_INDEX))
#define GPIO_LPSPI3_SCK_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_15_INDEX))
#define GPIO_LPSPI3_SCK_2              (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_00_INDEX))
#define GPIO_LPSPI3_SDI_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_13_INDEX))
#define GPIO_LPSPI3_SDI_2              (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_02_INDEX))
#define GPIO_LPSPI3_SDO_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_14_INDEX))
#define GPIO_LPSPI3_SDO_2              (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_01_INDEX))

#define GPIO_LPSPI4_PCS0_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_04_INDEX))
#define GPIO_LPSPI4_PCS0_2             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_00_INDEX))
#define GPIO_LPSPI4_PCS1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_03_INDEX))
#define GPIO_LPSPI4_PCS2               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_02_INDEX))
#define GPIO_LPSPI4_PCS3               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_11_INDEX))
#define GPIO_LPSPI4_SCK_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_07_INDEX))
#define GPIO_LPSPI4_SCK_2              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_03_INDEX))
#define GPIO_LPSPI4_SDI_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_05_INDEX))
#define GPIO_LPSPI4_SDI_2              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_01_INDEX))
#define GPIO_LPSPI4_SDO_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_06_INDEX))
#define GPIO_LPSPI4_SDO_2              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_02_INDEX))

/* Low Power Universal Asynchronous Receiver/Transmitter (LPUART) */

#define GPIO_LPUART1_CTS_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_14_INDEX))
#define GPIO_LPUART1_RTS_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_15_INDEX))
#define GPIO_LPUART1_RX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_13_INDEX))
#define GPIO_LPUART1_TX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_12_INDEX))

#define GPIO_LPUART2_CTS_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_00_INDEX))
#define GPIO_LPUART2_RTS_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_01_INDEX))
#define GPIO_LPUART2_RX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_03_INDEX))
#define GPIO_LPUART2_RX_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_10_INDEX))
#define GPIO_LPUART2_TX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_02_INDEX))
#define GPIO_LPUART2_TX_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_11_INDEX))

#define GPIO_LPUART3_CTS_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_04_INDEX))
#define GPIO_LPUART3_CTS_2             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_15_INDEX))
#define GPIO_LPUART3_RTS_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_05_INDEX))
#define GPIO_LPUART3_RTS_2             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_16_INDEX))
#define GPIO_LPUART3_RX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_07_INDEX))
#define GPIO_LPUART3_RX_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_14_INDEX))
#define GPIO_LPUART3_RX_3              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_09_INDEX))
#define GPIO_LPUART3_TX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_06_INDEX))
#define GPIO_LPUART3_TX_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_13_INDEX))
#define GPIO_LPUART3_TX_3              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_08_INDEX))

#define GPIO_LPUART4_CTS_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_17_INDEX))
#define GPIO_LPUART4_RTS_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_18_INDEX))
#define GPIO_LPUART4_RX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_01_INDEX))
#define GPIO_LPUART4_RX_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_20_INDEX))
#define GPIO_LPUART4_RX_3              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_01_INDEX))
#define GPIO_LPUART4_TX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_00_INDEX))
#define GPIO_LPUART4_TX_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_19_INDEX))
#define GPIO_LPUART4_TX_3              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_00_INDEX))

#define GPIO_LPUART5_CTS_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_28_INDEX))
#define GPIO_LPUART5_RTS_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_27_INDEX))
#define GPIO_LPUART5_RX_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_13_INDEX))
#define GPIO_LPUART5_RX_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_24_INDEX))
#define GPIO_LPUART5_TX_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_12_INDEX))
#define GPIO_LPUART5_TX_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_23_INDEX))

#define GPIO_LPUART6_CTS_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_30_INDEX))
#define GPIO_LPUART6_RTS_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_29_INDEX))
#define GPIO_LPUART6_RX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_03_INDEX))
#define GPIO_LPUART6_RX_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_26_INDEX))
#define GPIO_LPUART6_TX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_02_INDEX))
#define GPIO_LPUART6_TX_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_25_INDEX))
#define GPIO_LPUART6_TX_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_25_INDEX))

#define GPIO_LPUART7_CTS_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_06_INDEX))
#define GPIO_LPUART7_RTS_2             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_07_INDEX))
#define GPIO_LPUART7_RX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_32_INDEX))
#define GPIO_LPUART7_RX_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_09_INDEX))
#define GPIO_LPUART7_TX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_31_INDEX))
#define GPIO_LPUART7_TX_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_08_INDEX))

#define GPIO_LPUART8_CTS_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_02_INDEX))
#define GPIO_LPUART8_RTS_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_03_INDEX))
#define GPIO_LPUART8_RX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_11_INDEX))
#define GPIO_LPUART8_RX_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_39_INDEX))
#define GPIO_LPUART8_RX_3              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_05_INDEX))
#define GPIO_LPUART8_TX_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_10_INDEX))
#define GPIO_LPUART8_TX_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_38_INDEX))
#define GPIO_LPUART8_TX_3              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_04_INDEX))

/* Medium Quality Sound (MQS) */

#define GPIO_MQS_LEFT_1                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_05_INDEX))
#define GPIO_MQS_LEFT_2                (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_01_INDEX))
#define GPIO_MQS_LEFT_3                (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_14_INDEX))
#define GPIO_MQS_RIGHT_1               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_04_INDEX))
#define GPIO_MQS_RIGHT_2               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_00_INDEX))
#define GPIO_MQS_RIGHT_3               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_13_INDEX))

/* NMI */

#define GPIO_NMI_GLUE_NMI_1            (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_WAKEUP_INDEX))

/* Periodic Interrupt Timer (PIT) */

#define GPIO_PIT_TRIGGER00_1           (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_04_INDEX))

/* Quad Timer (QTimer) */

#define GPIO_QTIMER1_TIMER0_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_00_INDEX))
#define GPIO_QTIMER1_TIMER1_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_01_INDEX))
#define GPIO_QTIMER1_TIMER2_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_02_INDEX))
#define GPIO_QTIMER1_TIMER3_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_08_INDEX))

#define GPIO_QTIMER2_TIMER0_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_03_INDEX))
#define GPIO_QTIMER2_TIMER0_2          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_19_INDEX))
#define GPIO_QTIMER2_TIMER1_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_04_INDEX))
#define GPIO_QTIMER2_TIMER1_2          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_20_INDEX))
#define GPIO_QTIMER2_TIMER2_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_05_INDEX))
#define GPIO_QTIMER2_TIMER2_2          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_21_INDEX))
#define GPIO_QTIMER2_TIMER3_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_09_INDEX))
#define GPIO_QTIMER2_TIMER3_2          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_22_INDEX))

#define GPIO_QTIMER3_TIMER0_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_00_INDEX))
#define GPIO_QTIMER3_TIMER0_2          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_06_INDEX))
#define GPIO_QTIMER3_TIMER0_3          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_15_INDEX))
#define GPIO_QTIMER3_TIMER1_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_01_INDEX))
#define GPIO_QTIMER3_TIMER1_2          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_07_INDEX))
#define GPIO_QTIMER3_TIMER1_3          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_16_INDEX))
#define GPIO_QTIMER3_TIMER2_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_02_INDEX))
#define GPIO_QTIMER3_TIMER2_2          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_08_INDEX))
#define GPIO_QTIMER3_TIMER2_3          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_17_INDEX))
#define GPIO_QTIMER3_TIMER3_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_03_INDEX))
#define GPIO_QTIMER3_TIMER3_2          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_10_INDEX))
#define GPIO_QTIMER3_TIMER3_3          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_18_INDEX))

#define GPIO_QTIMER4_TIMER0_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_09_INDEX))
#define GPIO_QTIMER4_TIMER1_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_10_INDEX))
#define GPIO_QTIMER4_TIMER2_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_11_INDEX))
#define GPIO_QTIMER4_TIMER3_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_11_INDEX))

/* XTALOSC Reference Clock */

#define GPIO_REF_CLK_24M_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_01_INDEX))
#define GPIO_REF_CLK_24M_2             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_03_INDEX))
#define GPIO_REF_CLK_24M_3             (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_13_INDEX))
#define GPIO_REF_CLK_32K_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_00_INDEX))

/* Synchronous Audio Interface (SAI) */

#define GPIO_SAI1_MCLK_1               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_09_INDEX))
#define GPIO_SAI1_MCLK_2               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_13_INDEX))
#define GPIO_SAI1_MCLK_3               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_03_INDEX))
#define GPIO_SAI1_RX_BCLK_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_11_INDEX))
#define GPIO_SAI1_RX_BCLK_2            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_15_INDEX))
#define GPIO_SAI1_RX_BCLK_3            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_05_INDEX))
#define GPIO_SAI1_RX_DATA00_1          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_12_INDEX))
#define GPIO_SAI1_RX_DATA00_2          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_00_INDEX))
#define GPIO_SAI1_RX_DATA00_3          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_06_INDEX))
#define GPIO_SAI1_RX_SYNC_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_10_INDEX))
#define GPIO_SAI1_RX_SYNC_2            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_14_INDEX))
#define GPIO_SAI1_RX_SYNC_3            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_04_INDEX))
#define GPIO_SAI1_TX_BCLK_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_14_INDEX))
#define GPIO_SAI1_TX_BCLK_2            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_02_INDEX))
#define GPIO_SAI1_TX_BCLK_3            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_08_INDEX))
#define GPIO_SAI1_TX_DATA00_1          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_13_INDEX))
#define GPIO_SAI1_TX_DATA00_2          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_01_INDEX))
#define GPIO_SAI1_TX_DATA00_3          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_07_INDEX))
#define GPIO_SAI1_TX_DATA01_1          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_12_INDEX))
#define GPIO_SAI1_TX_DATA01_2          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_02_INDEX))
#define GPIO_SAI1_TX_DATA02_1          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_11_INDEX))
#define GPIO_SAI1_TX_DATA02_2          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_01_INDEX))
#define GPIO_SAI1_TX_DATA03_1          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_10_INDEX))
#define GPIO_SAI1_TX_DATA03_2          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_00_INDEX))
#define GPIO_SAI1_TX_SYNC_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_15_INDEX))
#define GPIO_SAI1_TX_SYNC_2            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_03_INDEX))
#define GPIO_SAI1_TX_SYNC_3            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_09_INDEX))

#define GPIO_SAI2_MCLK_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_07_INDEX))
#define GPIO_SAI2_MCLK_2               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_10_INDEX))
#define GPIO_SAI2_RX_BCLK_1            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_10_INDEX))
#define GPIO_SAI2_RX_BCLK_2            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_06_INDEX))
#define GPIO_SAI2_RX_DATA_1            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_08_INDEX))
#define GPIO_SAI2_RX_DATA_2            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_08_INDEX))
#define GPIO_SAI2_RX_SYNC_1            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_09_INDEX))
#define GPIO_SAI2_RX_SYNC_2            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_07_INDEX))
#define GPIO_SAI2_TX_BCLK_1            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_06_INDEX))
#define GPIO_SAI2_TX_BCLK_2            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_05_INDEX))
#define GPIO_SAI2_TX_DATA_1            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_04_INDEX))
#define GPIO_SAI2_TX_DATA_2            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_09_INDEX))
#define GPIO_SAI2_TX_SYNC_1            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_05_INDEX))
#define GPIO_SAI2_TX_SYNC_2            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_04_INDEX))

#define GPIO_SAI3_MCLK_1               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_37_INDEX))
#define GPIO_SAI3_RX_BCLK_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_35_INDEX))
#define GPIO_SAI3_RX_DATA_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_33_INDEX))
#define GPIO_SAI3_RX_SYNC_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_34_INDEX))
#define GPIO_SAI3_TX_BCLK_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_38_INDEX))
#define GPIO_SAI3_TX_DATA_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_36_INDEX))
#define GPIO_SAI3_TX_SYNC_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_39_INDEX))

/* Smart External Memory Controller (SEMC) */

#define GPIO_SEMC_ADDR00_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_09_INDEX))
#define GPIO_SEMC_ADDR01_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_10_INDEX))
#define GPIO_SEMC_ADDR02_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_11_INDEX))
#define GPIO_SEMC_ADDR03_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_12_INDEX))
#define GPIO_SEMC_ADDR04_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_13_INDEX))
#define GPIO_SEMC_ADDR05_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_14_INDEX))
#define GPIO_SEMC_ADDR06_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_15_INDEX))
#define GPIO_SEMC_ADDR07_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_16_INDEX))
#define GPIO_SEMC_ADDR08_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_17_INDEX))
#define GPIO_SEMC_ADDR09_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_18_INDEX))
#define GPIO_SEMC_ADDR10_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_23_INDEX))
#define GPIO_SEMC_ADDR11_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_19_INDEX))
#define GPIO_SEMC_ADDR12_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_20_INDEX))
#define GPIO_SEMC_BA0_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_21_INDEX))
#define GPIO_SEMC_BA1_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_22_INDEX))
#define GPIO_SEMC_CAS_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_24_INDEX))
#define GPIO_SEMC_CKE_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_27_INDEX))
#define GPIO_SEMC_CLK_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_26_INDEX))
#define GPIO_SEMC_CS0_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_29_INDEX))
#define GPIO_SEMC_CSX00_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_41_INDEX))
#define GPIO_SEMC_CSX01_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_07_INDEX))
#define GPIO_SEMC_CSX01_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_00_INDEX))
#define GPIO_SEMC_CSX02_1              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_01_INDEX))
#define GPIO_SEMC_CSX02_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_08_INDEX))
#define GPIO_SEMC_CSX03_1              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_02_INDEX))
#define GPIO_SEMC_DATA00_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_00_INDEX))
#define GPIO_SEMC_DATA01_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_01_INDEX))
#define GPIO_SEMC_DATA02_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_02_INDEX))
#define GPIO_SEMC_DATA03_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_03_INDEX))
#define GPIO_SEMC_DATA04_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_04_INDEX))
#define GPIO_SEMC_DATA05_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_05_INDEX))
#define GPIO_SEMC_DATA06_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_06_INDEX))
#define GPIO_SEMC_DATA07_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_07_INDEX))
#define GPIO_SEMC_DATA08_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_30_INDEX))
#define GPIO_SEMC_DATA09_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_31_INDEX))
#define GPIO_SEMC_DATA10_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_32_INDEX))
#define GPIO_SEMC_DATA11_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_33_INDEX))
#define GPIO_SEMC_DATA12_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_34_INDEX))
#define GPIO_SEMC_DATA13_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_35_INDEX))
#define GPIO_SEMC_DATA14_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_36_INDEX))
#define GPIO_SEMC_DATA15_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_37_INDEX))
#define GPIO_SEMC_DM00_1               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_08_INDEX))
#define GPIO_SEMC_DM01_1               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_38_INDEX))
#define GPIO_SEMC_DQS_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_39_INDEX))
#define GPIO_SEMC_RAS_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_25_INDEX))
#define GPIO_SEMC_RDY_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_40_INDEX))
#define GPIO_SEMC_WE_1                 (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_28_INDEX))

/* Secure Non-Volatile Storage (SNVS) */

#define GPIO_SNVS_LP_PMIC_ON_REQ_1     (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_PMIC_ON_REQ_INDEX))
#define GPIO_SNVS_VIO_5_1              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_19_INDEX))
#define GPIO_SNVS_VIO_5_CTL_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_18_INDEX))

/* Sony/Philips Digital Interface (SPDIF) */

#define GPIO_SPDIF_EXT_CLK_1           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_07_INDEX))
#define GPIO_SPDIF_IN_1                (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_03_INDEX))
#define GPIO_SPDIF_IN_2                (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_16_INDEX))
#define GPIO_SPDIF_LOCK_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_06_INDEX))
#define GPIO_SPDIF_OUT_1               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_02_INDEX))
#define GPIO_SPDIF_OUT_2               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_05_INDEX))
#define GPIO_SPDIF_OUT_3               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_15_INDEX))
#define GPIO_SPDIF_SR_CLK_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_04_INDEX))

/* Boot Configuration */

#define GPIO_SRC_BOOT_CFG00_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_04_INDEX))
#define GPIO_SRC_BOOT_CFG01_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_05_INDEX))
#define GPIO_SRC_BOOT_CFG02_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_06_INDEX))
#define GPIO_SRC_BOOT_CFG03_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_07_INDEX))
#define GPIO_SRC_BOOT_CFG04_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_08_INDEX))
#define GPIO_SRC_BOOT_CFG05_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_09_INDEX))
#define GPIO_SRC_BOOT_CFG06_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_10_INDEX))
#define GPIO_SRC_BOOT_CFG07_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_11_INDEX))
#define GPIO_SRC_BOOT_CFG08_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_12_INDEX))
#define GPIO_SRC_BOOT_CFG09_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_13_INDEX))
#define GPIO_SRC_BOOT_CFG10_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_14_INDEX))
#define GPIO_SRC_BOOT_CFG11_1          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_15_INDEX))
#define GPIO_SRC_BOOT_MODE00_1         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_04_INDEX))
#define GPIO_SRC_BOOT_MODE01_1         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_05_INDEX))

/* USB OTG */

#define GPIO_USB_OTG1_ID_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_02_INDEX))
#define GPIO_USB_OTG1_ID_2             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_01_INDEX))
#define GPIO_USB_OTG1_OC_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_03_INDEX))
#define GPIO_USB_OTG1_OC_2             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_03_INDEX))
#define GPIO_USB_OTG1_PWR_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_01_INDEX))
#define GPIO_USB_OTG1_PWR_2            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_02_INDEX))

#define GPIO_USB_OTG2_ID_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_00_INDEX))
#define GPIO_USB_OTG2_ID_2             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_00_INDEX))
#define GPIO_USB_OTG2_OC_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_14_INDEX))
#define GPIO_USB_OTG2_OC_2             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_40_INDEX))
#define GPIO_USB_OTG2_PWR_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_15_INDEX))
#define GPIO_USB_OTG2_PWR_2            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_41_INDEX))

/* Ultra Secured Digital Host Controller (uSDHC) */

#define GPIO_USDHC1_CD_1               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_02_INDEX))
#define GPIO_USDHC1_CD_2               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_12_INDEX))
#define GPIO_USDHC1_CD_3               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_35_INDEX))
#define GPIO_USDHC1_CLK_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_01_INDEX))
#define GPIO_USDHC1_CMD_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_00_INDEX))
#define GPIO_USDHC1_DATA0_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_02_INDEX))
#define GPIO_USDHC1_DATA1_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_03_INDEX))
#define GPIO_USDHC1_DATA2_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_04_INDEX))
#define GPIO_USDHC1_DATA3_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_05_INDEX))
#define GPIO_USDHC1_RESET_1            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_33_INDEX))
#define GPIO_USDHC1_RESET_2            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_00_INDEX))
#define GPIO_USDHC1_RESET_3            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_15_INDEX))
#define GPIO_USDHC1_VSELECT_1          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_34_INDEX))
#define GPIO_USDHC1_VSELECT_2          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_01_INDEX))
#define GPIO_USDHC1_VSELECT_3          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_14_INDEX))
#define GPIO_USDHC1_VSELECT_4          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_41_INDEX))
#define GPIO_USDHC1_WP_1               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_12_INDEX))
#define GPIO_USDHC1_WP_2               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_00_INDEX))
#define GPIO_USDHC1_WP_3               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_13_INDEX))
#define GPIO_USDHC1_WP_4               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_36_INDEX))

#define GPIO_USDHC2_CD_1               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_03_INDEX))
#define GPIO_USDHC2_CD_2               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_39_INDEX))
#define GPIO_USDHC2_CLK_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_04_INDEX))
#define GPIO_USDHC2_CLK_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_09_INDEX))
#define GPIO_USDHC2_CMD_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_05_INDEX))
#define GPIO_USDHC2_CMD_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_08_INDEX))
#define GPIO_USDHC2_DATA0_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_03_INDEX))
#define GPIO_USDHC2_DATA0_2            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_04_INDEX))
#define GPIO_USDHC2_DATA1_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_02_INDEX))
#define GPIO_USDHC2_DATA1_2            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_05_INDEX))
#define GPIO_USDHC2_DATA2_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_01_INDEX))
#define GPIO_USDHC2_DATA2_2            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_06_INDEX))
#define GPIO_USDHC2_DATA3_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_00_INDEX))
#define GPIO_USDHC2_DATA3_2            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_07_INDEX))
#define GPIO_USDHC2_DATA4_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_08_INDEX))
#define GPIO_USDHC2_DATA4_2            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_12_INDEX))
#define GPIO_USDHC2_DATA5_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_09_INDEX))
#define GPIO_USDHC2_DATA5_2            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_13_INDEX))
#define GPIO_USDHC2_DATA6_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_10_INDEX))
#define GPIO_USDHC2_DATA6_2            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_14_INDEX))
#define GPIO_USDHC2_DATA7_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_11_INDEX))
#define GPIO_USDHC2_DATA7_2            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_15_INDEX))
#define GPIO_USDHC2_RESET_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B1_06_INDEX))
#define GPIO_USDHC2_RESET_2            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_11_INDEX))
#define GPIO_USDHC2_RESET_3            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_11_INDEX))
#define GPIO_USDHC2_RESET_4            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_40_INDEX))
#define GPIO_USDHC2_VSELECT            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_38_INDEX))
#define GPIO_USDHC2_WP_1               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_10_INDEX))
#define GPIO_USDHC2_WP_2               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_37_INDEX))

/* USB OTG */

#define GPIO_USB1_OTG_ID_1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_01_INDEX))
#define GPIO_USB1_OTG_ID_2             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_02_INDEX))
#define GPIO_USB1_OTG_OC_1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_03_INDEX))
#define GPIO_USB1_OTG_OC_2             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_03_INDEX))
#define GPIO_USB1_OTG_PWR_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_02_INDEX))
#define GPIO_USB1_OTG_PWR_2            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_01_INDEX))

#define GPIO_USB2_OTG_ID_1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_00_INDEX))
#define GPIO_USB2_OTG_ID_2             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_00_INDEX))
#define GPIO_USB2_OTG_OC_1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_40_INDEX))
#define GPIO_USB2_OTG_OC_2             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_14_INDEX))
#define GPIO_USB2_OTG_PWR_1            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_41_INDEX))
#define GPIO_USB2_OTG_PWR_2            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_15_INDEX))

/* Watchdog Timer (WDOG1-2) */

#define GPIO_WDOG1_1                   (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_13_INDEX))
#define GPIO_WDOG1_2                   (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_10_INDEX))
#define GPIO_WDOG1_3                   (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_00_INDEX))
#define GPIO_WDOG1_WDOG_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_11_INDEX))
#define GPIO_WDOG1_WDOG_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_39_INDEX))
#define GPIO_WDOG1_WDOG_RST_DEB_1      (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_15_INDEX))

#define GPIO_WDOG2_RESET_DEB_1         (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_03_INDEX))
#define GPIO_WDOG2_WDOG_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_12_INDEX))

/* Inter-Peripheral Crossbar Switch A (XBARA) */

#define GPIO_XBAR1_IN02_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_14_INDEX))
#define GPIO_XBAR1_IN03_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_15_INDEX))
#define GPIO_XBAR1_IN03_2              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_01_INDEX))
#define GPIO_XBAR1_IN20_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_15_INDEX))
#define GPIO_XBAR1_IN20_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_08_INDEX))
#define GPIO_XBAR1_IN21_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_16_INDEX))
#define GPIO_XBAR1_IN21_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_09_INDEX))
#define GPIO_XBAR1_IN22_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_36_INDEX))
#define GPIO_XBAR1_IN22_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_10_INDEX))
#define GPIO_XBAR1_IN23_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_37_INDEX))
#define GPIO_XBAR1_IN23_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_11_INDEX))
#define GPIO_XBAR1_IN24_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_14_INDEX))
#define GPIO_XBAR1_IN24_2              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_12_INDEX))
#define GPIO_XBAR1_IN25_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_15_INDEX))
#define GPIO_XBAR1_IN25_2              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_13_INDEX))
#define GPIO_XBAR1_INOUT04_1           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_02_INDEX))
#define GPIO_XBAR1_INOUT04_2           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_00_INDEX))
#define GPIO_XBAR1_INOUT05_1           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_03_INDEX))
#define GPIO_XBAR1_INOUT05_2           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_01_INDEX))
#define GPIO_XBAR1_INOUT06_1           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_04_INDEX))
#define GPIO_XBAR1_INOUT06_2           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_02_INDEX))
#define GPIO_XBAR1_INOUT07_1           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_05_INDEX))
#define GPIO_XBAR1_INOUT07_2           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_03_INDEX))
#define GPIO_XBAR1_INOUT08_1           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_06_INDEX))
#define GPIO_XBAR1_INOUT08_2           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_04_INDEX))
#define GPIO_XBAR1_INOUT09_1           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_07_INDEX))
#define GPIO_XBAR1_INOUT09_2           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_SD_B0_05_INDEX))
#define GPIO_XBAR1_INOUT10_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_12_INDEX))
#define GPIO_XBAR1_INOUT11_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_13_INDEX))
#define GPIO_XBAR1_INOUT12_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_14_INDEX))
#define GPIO_XBAR1_INOUT13_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B0_15_INDEX))
#define GPIO_XBAR1_INOUT14_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_00_INDEX))
#define GPIO_XBAR1_INOUT14_2           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_00_INDEX))
#define GPIO_XBAR1_INOUT15_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_01_INDEX))
#define GPIO_XBAR1_INOUT15_2           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_01_INDEX))
#define GPIO_XBAR1_INOUT16_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_02_INDEX))
#define GPIO_XBAR1_INOUT16_2           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_02_INDEX))
#define GPIO_XBAR1_INOUT17_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_03_INDEX))
#define GPIO_XBAR1_INOUT17_2           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_B1_03_INDEX))
#define GPIO_XBAR1_INOUT17_3           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_08_INDEX))
#define GPIO_XBAR1_INOUT17_4           (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_05_INDEX))
#define GPIO_XBAR1_INOUT18_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_35_INDEX))
#define GPIO_XBAR1_INOUT18_2           (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_06_INDEX))
#define GPIO_XBAR1_INOUT19_3           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_14_INDEX))
#define GPIO_XBAR1_INOUT19_4           (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_07_INDEX))
#define GPIO_XBAR1_XBAR_IN02_1         (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_EMC_00_INDEX))

/* ADC */
#define GPIO_ADC1_CH0                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_11_INDEX))
#define GPIO_ADC1_CH1                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_12_INDEX))
#define GPIO_ADC1_CH2                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_13_INDEX))
#define GPIO_ADC1_CH3                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_14_INDEX))
#define GPIO_ADC1_CH4                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B0_15_INDEX))
#define GPIO_ADC1_CH5                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_00_INDEX))
#define GPIO_ADC1_CH6                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_01_INDEX))
#define GPIO_ADC1_CH7                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_02_INDEX))
#define GPIO_ADC1_CH8                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_03_INDEX))
#define GPIO_ADC1_CH9                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_04_INDEX))
#define GPIO_ADC1_CH10                 (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_05_INDEX))
#define GPIO_ADC1_CH11                 (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_06_INDEX))
#define GPIO_ADC1_CH12                 (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_07_INDEX))
#define GPIO_ADC1_CH13                 (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_08_INDEX))
#define GPIO_ADC1_CH14                 (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_09_INDEX))
#define GPIO_ADC1_CH15                 (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_10_INDEX))
#define GPIO_ADC2_CH0                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_11_INDEX))
#define GPIO_ADC2_CH1                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_12_INDEX))
#define GPIO_ADC2_CH2                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_13_INDEX))
#define GPIO_ADC2_CH3                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_14_INDEX))
#define GPIO_ADC2_CH4                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_15_INDEX))
#define GPIO_ADC2_CH5                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_00_INDEX))
#define GPIO_ADC2_CH6                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_01_INDEX))
#define GPIO_ADC2_CH7                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_02_INDEX))
#define GPIO_ADC2_CH8                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_03_INDEX))
#define GPIO_ADC2_CH9                  (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_04_INDEX))
#define GPIO_ADC2_CH10                 (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_05_INDEX))
#define GPIO_ADC2_CH11                 (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_06_INDEX))
#define GPIO_ADC2_CH12                 (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_07_INDEX))
#define GPIO_ADC2_CH13                 (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_08_INDEX))
#define GPIO_ADC2_CH14                 (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_09_INDEX))
#define GPIO_ADC2_CH15                 (GPIO_PERIPH | GPIO_ALT5 | GPIO_PADMUX(IMXRT_PADMUX_GPIO_AD_B1_10_INDEX))

#endif /* __ARCH_ARM_SRC_IMXRT_HARDWARE_RT106X_IMXRT106X_PINMUX_H */
