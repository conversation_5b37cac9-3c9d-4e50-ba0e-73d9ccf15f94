/*********************************************************************************
 * arch/arm/src/imxrt/hardware/rt117x/imxrt117x_anadig.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 *********************************************************************************/

/* Copyright 2022 NXP */

#ifndef __ARCH_ARM_SRC_IMXRT_HARDWARE_RT117X_IMXRT117X_ANADIG_H
#define __ARCH_ARM_SRC_IMXRT_HARDWARE_RT117X_IMXRT117X_ANADIG_H

/*********************************************************************************
 * Included Files
 *********************************************************************************/

#include <nuttx/config.h>
#include "hardware/imxrt_memorymap.h"

/*********************************************************************************
 * Pre-processor Definitions
 *********************************************************************************/

/* ANADIG_MISC Register Offsets **************************************************/
#define IMXRT_ANADIG_LDO_SNVS_PMU_LDO_LPSR_ANA_OFFSET       (0x0510)
#define IMXRT_ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_2_OFFSET     (0x0520)
#define IMXRT_ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_OFFSET       (0x0530)
#define IMXRT_ANADIG_MISC_MISC_DIFPROG_OFFSET               (0x0800)
#define IMXRT_ANADIG_MISC_VDDSOC_AI_CTRL_OFFSET             (0x0820)
#define IMXRT_ANADIG_MISC_VDDSOC_AI_WDATA_OFFSET            (0x0830)
#define IMXRT_ANADIG_MISC_VDDSOC_AI_RDATA_OFFSET            (0x0840)
#define IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_CTRL_1G_OFFSET      (0x0850)
#define IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_WDATA_1G_OFFSET     (0x0860)
#define IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_RDATA_1G_OFFSET     (0x0870)
#define IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_CTRL_AUDIO_OFFSET   (0x0880)
#define IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_WDATA_AUDIO_OFFSET  (0x0890)
#define IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_RDATA_AUDIO_OFFSET  (0x08a0)
#define IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_CTRL_VIDEO_OFFSET   (0x08b0)
#define IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_WDATA_VIDEO_OFFSET  (0x08c0)
#define IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_RDATA_VIDEO_OFFSET  (0x08d0)
#define IMXRT_ANADIG_MISC_VDDLPSR_AI_CTRL_OFFSET            (0x08e0)
#define IMXRT_ANADIG_MISC_VDDLPSR_AI_WDATA_OFFSET           (0x08f0)
#define IMXRT_ANADIG_MISC_VDDLPSR_AI_RDATA_REFTOP_OFFSET    (0x0900)
#define IMXRT_ANADIG_MISC_VDDLPSR_AI_RDATA_TMPSNS_OFFSET    (0x0910)
#define IMXRT_ANADIG_MISC_VDDLPSR_AI400M_CTRL_OFFSET        (0x0920)
#define IMXRT_ANADIG_MISC_VDDLPSR_AI400M_WDATA_OFFSET       (0x0930)
#define IMXRT_ANADIG_MISC_VDDLPSR_AI400M_RDATA_OFFSET       (0x0940)

/* ANADIG_MISC Register Addresses ************************************************/
#define IMXRT_ANADIG_LDO_SNVS_PMU_LDO_LPSR_ANA       (IMXRT_ANADIG_BASE + IMXRT_ANADIG_LDO_SNVS_PMU_LDO_LPSR_ANA_OFFSET)
#define IMXRT_ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_2     (IMXRT_ANADIG_BASE + IMXRT_ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_2_OFFSET)
#define IMXRT_ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG       (IMXRT_ANADIG_BASE + IMXRT_ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_OFFSET)
#define IMXRT_ANADIG_MISC_MISC_DIFPROG               (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_MISC_DIFPROG_OFFSET)
#define IMXRT_ANADIG_MISC_VDDSOC_AI_CTRL             (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_VDDSOC_AI_CTRL_OFFSET)
#define IMXRT_ANADIG_MISC_VDDSOC_AI_WDATA            (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_VDDSOC_AI_WDATA_OFFSET)
#define IMXRT_ANADIG_MISC_VDDSOC_AI_RDATA            (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_VDDSOC_AI_RDATA_OFFSET)
#define IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_CTRL_1G      (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_CTRL_1G_OFFSET)
#define IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_WDATA_1G     (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_WDATA_1G_OFFSET)
#define IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_RDATA_1G     (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_RDATA_1G_OFFSET)
#define IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_CTRL_AUDIO   (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_CTRL_AUDIO_OFFSET)
#define IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_WDATA_AUDIO  (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_WDATA_AUDIO_OFFSET)
#define IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_RDATA_AUDIO  (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_RDATA_AUDIO_OFFSET)
#define IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_CTRL_VIDEO   (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_CTRL_VIDEO_OFFSET)
#define IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_WDATA_VIDEO  (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_WDATA_VIDEO_OFFSET)
#define IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_RDATA_VIDEO  (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_VDDSOC2PLL_AI_RDATA_VIDEO_OFFSET)
#define IMXRT_ANADIG_MISC_VDDLPSR_AI_CTRL            (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_VDDLPSR_AI_CTRL_OFFSET)
#define IMXRT_ANADIG_MISC_VDDLPSR_AI_WDATA           (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_VDDLPSR_AI_WDATA_OFFSET)
#define IMXRT_ANADIG_MISC_VDDLPSR_AI_RDATA_REFTOP    (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_VDDLPSR_AI_RDATA_REFTOP_OFFSET)
#define IMXRT_ANADIG_MISC_VDDLPSR_AI_RDATA_TMPSNS    (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_VDDLPSR_AI_RDATA_TMPSNS_OFFSET)
#define IMXRT_ANADIG_MISC_VDDLPSR_AI400M_CTRL        (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_VDDLPSR_AI400M_CTRL_OFFSET)
#define IMXRT_ANADIG_MISC_VDDLPSR_AI400M_WDATA       (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_VDDLPSR_AI400M_WDATA_OFFSET)
#define IMXRT_ANADIG_MISC_VDDLPSR_AI400M_RDATA       (IMXRT_ANADIG_BASE + IMXRT_ANADIG_MISC_VDDLPSR_AI400M_RDATA_OFFSET)

/* PMU_LDO_LPSR_ANA_REGISTER (PMU_LDO_LPSR_ANA) */
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_ANA_REG_LP_EN               (1 << 0)   /* Bit 0: reg_lp_en */
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_ANA_REG_DISABLE             (1 << 2)   /* Bit 2: reg_disable */
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_ANA_PULL_DOWN_2MA_EN        (1 << 3)   /* Bit 3: pull_down_2ma_en */
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_ANA_LPSR_ANA_CONTROL_MODE   (1 << 4)   /* Bit 4: LPSR_ANA_CONTROL_MODE */
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_ANA_BYPASS_MODE_EN          (1 << 5)   /* Bit 5: bypass_mode_en */
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_ANA_STANDBY_EN              (1 << 6)   /* Bit 6: standby_en */
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_ANA_ALWAYS_4MA_PULLDOWN_EN  (1 << 8)   /* Bit 8: always_4ma_pulldown_en */
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_ANA_TRACK_MODE_EN           (1 << 19)  /* Bit 19: Track Mode Enable */
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_ANA_PULL_DOWN_20UA_EN       (1 << 20)  /* Bit 20: pull_down_20ua_en */

/* PMU_LDO_LPSR_DIG_2_REGISTER (PMU_LDO_LPSR_DIG_2) */
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_2_VOLTAGE_STEP_INC_SHIFT  (0)  /* Bits 0-2: voltage_step_inc */
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_2_VOLTAGE_STEP_INC_MASK   (0x3 << ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_2_VOLTAGE_STEP_INC_SHIFT)
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_2_VOLTAGE_STEP_INC(n)     (((n) << ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_2_VOLTAGE_STEP_INC_SHIFT) & ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_2_VOLTAGE_STEP_INC_MASK)

/* PMU_LDO_LPSR_DIG_REGISTER (PMU_LDO_LPSR_DIG) */
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_REG_EN                 (1 << 2)   /* Bit 2: ENABLE_ILIMIT */
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_LPSR_DIG_CONTROL_MODE  (1 << 5)   /* Bit 5: LPSR_DIG_CONTROL_MODE */
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_STANDBY_EN             (1 << 6)   /* Bit 6: standby_en */
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_TRACKING_MODE          (1 << 17)  /* Bit 17: tracking_mode */
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_BYPASS_MODE            (1 << 18)  /* Bit 18: bypass_mode */
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_VOLTAGE_SELECT_SHIFT   (20)       /* Bits 20-25: VOLTAGE_SELECT */
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_VOLTAGE_SELECT_MASK    (0x1F << ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_VOLTAGE_SELECT_SHIFT)
#define ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_VOLTAGE_SELECT(n)      (((n) << ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_VOLTAGE_SELECT_SHIFT) & ANADIG_LDO_SNVS_PMU_LDO_LPSR_DIG_VOLTAGE_SELECT_MASK)

/* ANADIG CTRL Register ADDR */

#define IMXRT_ANADIG_MISC_ADDR_PHY_LDO_CTRL0         0x0   /* PHY LDO CTRL0 Register. */
#define IMXRT_ANADIG_MISC_ADDR_PHY_LDO_CTRL0_SET     0x4   /* PHY LDO CTRL0 Set Register. */
#define IMXRT_ANADIG_MISC_ADDR_PHY_LDO_CTRL0_CLR     0x8   /* PHY LDO CTRL0 Clr Register. */
#define IMXRT_ANADIG_MISC_ADDR_PHY_LDO_CTRL0_TOG     0xC   /* PHY LDO CTRL0 TOG Register. */
#define IMXRT_ANADIG_MISC_ADDR_PHY_LDO_STAT0         0x50  /* PHY LDO STAT0 Register. */
#define IMXRT_ANADIG_MISC_ADDR_PHY_LDO_STAT0_SET     0x54  /* PHY LDO STAT0 Set Register. */
#define IMXRT_ANADIG_MISC_ADDR_PHY_LDO_STAT0_CLR     0x58  /* PHY LDO STAT0 Clr Register. */
#define IMXRT_ANADIG_MISC_ADDR_PHY_LDO_STAT0_TOG     0x5C  /* PHY LDO STAT0 Tog Register. */
#define IMXRT_ANADIG_MISC_ADDR_BANDGAP_CTRL0         0x0   /* BANDGAP CTRL0 Register. */
#define IMXRT_ANADIG_MISC_ADDR_BANDGAP_STAT0         0x50  /* BANDGAP STAT0 Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_CTRL0       0x0   /* RC OSC 400M CTRL0 Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_CTRL0_SET   0x4   /* RC OSC 400M CTRL0 SET Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_CTRL0_CLR   0x8   /* RC OSC 400M CTRL0 CLR Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_CTRL0_TOG   0xC   /* RC OSC 400M CTRL0 TOG Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_CTRL1       0x10  /* RC OSC 400M CTRL1 Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_CTRL1_SET   0x14  /* RC OSC 400M CTRL1 SET Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_CTRL1_CLR   0x18  /* RC OSC 400M CTRL1 CLR Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_CTRL1_TOG   0x1C  /* RC OSC 400M CTRL1 TOG Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_CTRL2       0x20  /* RC OSC 400M CTRL2 Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_CTRL2_SET   0x24  /* RC OSC 400M CTRL2 SET Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_CTRL2_CLR   0x28  /* RC OSC 400M CTRL2 CLR Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_CTRL2_TOG   0x2C  /* RC OSC 400M CTRL2 TOG Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_CTRL3       0x30  /* RC OSC 400M CTRL3 Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_CTRL3_SET   0x34  /* RC OSC 400M CTRL3 SET Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_CTRL3_CLR   0x38  /* RC OSC 400M CTRL3 CLR Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_CTRL3_TOG   0x3C  /* RC OSC 400M CTRL3 TOG Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_STAT0       0x50  /* RC OSC 400M STAT0 Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_STAT0_SET   0x54  /* RC OSC 400M STAT0 SET Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_STAT0_CLR   0x58  /* RC OSC 400M STAT0 CLR  Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_STAT0_TOG   0x5C  /* RC OSC 400M STAT0 TOG Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_STAT1       0x60  /* RC OSC 400M STAT1 Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_STAT1_SET   0x64  /* RC OSC 400M STAT1 SET Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_STAT1_CLR   0x68  /* RC OSC 400M STAT1 CLR Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_STAT1_TOG   0x6C  /* RC OSC 400M STAT1 TOG Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_STAT2       0x70  /* RC OSC 400M STAT2 Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_STAT2_SET   0x74  /* RC OSC 400M STAT2 SET Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_STAT2_CLR   0x78  /* RC OSC 400M STAT2 CLR Register. */
#define IMXRT_ANADIG_MISC_ADDR_RCOSC400M_STAT2_TOG   0x7C  /* RC OSC 400M STAT2 TOG Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLL1G_CTRL0           0x0   /* 1G PLL CTRL0 Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLL1G_CTRL0_SET       0x4   /* 1G PLL CTRL0 SET Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLL1G_CTRL0_CLR       0x8   /* 1G PLL CTRL0 CLR Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLL1G_CTRL1           0x10  /* 1G PLL CTRL1 Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLL1G_CTRL1_SET       0x14  /* 1G PLL CTRL1 SET Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLL1G_CTRL1_CLR       0x18  /* 1G PLL CTRL1 CLR Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLL1G_CTRL2           0x20  /* 1G PLL CTRL2 Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLL1G_CTRL2_SET       0x24  /* 1G PLL CTRL2 SET Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLL1G_CTRL2_CLR       0x28  /* 1G PLL CTRL2 CLR Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLL1G_CTRL3           0x30  /* 1G PLL CTRL3 Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLL1G_CTRL3_SET       0x34  /* 1G PLL CTRL3 SET Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLL1G_CTRL3_CLR       0x38  /* 1G PLL CTRL3 CLR Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLAUDIO_CTRL0        0x0   /* AUDIO PLL CTRL0 Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLAUDIO_CTRL0_SET    0x4   /* AUDIO PLL CTRL0 SET Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLAUDIO_CTRL0_CLR    0x8   /* AUDIO PLL CTRL0 CLR Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLAUDIO_CTRL1        0x10  /* AUDIO PLL CTRL1 Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLAUDIO_CTRL1_SET    0x14  /* AUDIO PLL CTRL1 SET Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLAUDIO_CTRL1_CLR    0x18  /* AUDIO PLL CTRL1 CLR Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLAUDIO_CTRL2        0x20  /* AUDIO PLL CTRL2 Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLAUDIO_CTRL2_SET    0x24  /* AUDIO PLL CTRL2 SET Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLAUDIO_CTRL2_CLR    0x28  /* AUDIO PLL CTRL2 CLR Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLAUDIO_CTRL3        0x30  /* AUDIO PLL CTRL3 Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLAUDIO_CTRL3_SET    0x34  /* AUDIO PLL CTRL3 SET Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLAUDIO_CTRL3_CLR    0x38  /* AUDIO PLL CTRL3 CLR Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLVIDEO_CTRL0        0x0   /* VIDEO PLL CTRL0 Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLVIDEO_CTRL0_SET    0x4   /* VIDEO PLL CTRL0 SET Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLVIDEO_CTRL0_CLR    0x8   /* VIDEO PLL CTRL0 CLR Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLVIDEO_CTRL1        0x10  /* VIDEO PLL CTRL1 Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLVIDEO_CTRL1_SET    0x14  /* VIDEO PLL CTRL1 SET Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLVIDEO_CTRL1_CLR    0x18  /* VIDEO PLL CTRL1 CLR Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLVIDEO_CTRL2        0x20  /* VIDEO PLL CTRL2 Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLVIDEO_CTRL2_SET    0x24  /* VIDEO PLL CTRL2 SET Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLVIDEO_CTRL2_CLR    0x28  /* VIDEO PLL CTRL2 CLR Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLVIDEO_CTRL3        0x30  /* VIDEO PLL CTRL3 Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLVIDEO_CTRL3_SET    0x34  /* VIDEO PLL CTRL3 SET Register. */
#define IMXRT_ANADIG_MISC_ADDR_PLLVIDEO_CTRL3_CLR    0x38  /* VIDEO PLL CTRL3 CLR Register. */

#define IMXRT_ANADIG_MISC_ADDR_SET_OFFSET            0x4
#define IMXRT_ANADIG_MISC_ADDR_CLR_OFFSET            0x8

/* CTRL0 - CTRL0 Register */

#define AI_PHY_LDO_CTRL0_LINREG_EN(x) \
    (((uint32_t)(((uint32_t)(x)) << AI_PHY_LDO_CTRL0_LINREG_EN_SHIFT)) & AI_PHY_LDO_CTRL0_LINREG_EN_MASK)
#define AI_PHY_LDO_CTRL0_LINREG_EN_MASK  (0x1U)
#define AI_PHY_LDO_CTRL0_LINREG_EN_SHIFT (0U)

/* LINREG_EN - LinReg master enable
 *  LinReg master enable. Setting this bit will enable the regular
 */

#define AI_PHY_LDO_CTRL0_PWRUPLOAD_DIS(x) \
    (((uint32_t)(((uint32_t)(x)) << AI_PHY_LDO_CTRL0_PWRUPLOAD_DIS_SHIFT)) & AI_PHY_LDO_CTRL0_PWRUPLOAD_DIS_MASK)
#define AI_PHY_LDO_CTRL0_PWRUPLOAD_DIS_MASK  (0x2U)
#define AI_PHY_LDO_CTRL0_PWRUPLOAD_DIS_SHIFT (1U)
/* LINREG_PWRUPLOAD_DIS - LinReg power-up load disable
 *  0b0..Internal pull-down enabled
 *  0b1..Internal pull-down disabled
 */

#define AI_PHY_LDO_CTRL0_LIMIT_EN(x) \
    (((uint32_t)(((uint32_t)(x)) << AI_PHY_LDO_CTRL0_LIMIT_EN_SHIFT)) & AI_PHY_LDO_CTRL0_LIMIT_EN_MASK)
#define AI_PHY_LDO_CTRL0_LIMIT_EN_MASK  (0x4U)
#define AI_PHY_LDO_CTRL0_LIMIT_EN_SHIFT (2U)
/* LINREG_LIMIT_EN - LinReg current limit enable
 *  LinReg current-limit enable. Setting this bit will enable the
 *  current-limiter in the regulator
 */

#define AI_PHY_LDO_CTRL0_OUTPUT_TRG(x) \
    (((uint32_t)(((uint32_t)(x)) << AI_PHY_LDO_CTRL0_OUTPUT_TRG_SHIFT)) & AI_PHY_LDO_CTRL0_OUTPUT_TRG_MASK)
#define AI_PHY_LDO_CTRL0_OUTPUT_TRG_MASK  (0x1F0U)
#define AI_PHY_LDO_CTRL0_OUTPUT_TRG_SHIFT (4U)
/* LINREG_OUTPUT_TRG - LinReg output voltage target setting
 *  0b00000..Set output voltage to x.xV
 *  0b10000..Set output voltage to 1.0V
 *  0b11111..Set output voltage to x.xV
 */

/* CTRL0 - CTRL0 Register */

#define AI_PLL1G_CTRL0_HOLD_RING_OFF(x) \
    (((uint32_t)(((uint32_t)(x)) << AI_PLL1G_CTRL0_HOLD_RING_OFF_SHIFT)) & AI_PLL1G_CTRL0_HOLD_RING_OFF_MASK)
#define AI_PLL1G_CTRL0_HOLD_RING_OFF_MASK  (0x2000UL)
#define AI_PLL1G_CTRL0_HOLD_RING_OFF_SHIFT (13U)

#define AI_PLL1G_CTRL0_POWER_UP(x) \
    (((uint32_t)(((uint32_t)(x)) << AI_PLL1G_CTRL0_POWER_UP_SHIFT)) & AI_PLL1G_CTRL0_POWER_UP_MASK)
#define AI_PLL1G_CTRL0_POWER_UP_MASK  (0x4000UL)
#define AI_PLL1G_CTRL0_POWER_UP_SHIFT (14U)

#define AI_PLL1G_CTRL0_ENABLE(x) \
    (((uint32_t)(((uint32_t)(x)) << AI_PLL1G_CTRL0_ENABLE_SHIFT)) & AI_PLL1G_CTRL0_ENABLE_MASK)
#define AI_PLL1G_CTRL0_ENABLE_MASK  (0x8000UL)
#define AI_PLL1G_CTRL0_ENABLE_SHIFT (15U)

#define AI_PLL1G_CTRL0_BYPASS(x) \
    (((uint32_t)(((uint32_t)(x)) << AI_PLL1G_CTRL0_BYPASS_SHIFT)) & AI_PLL1G_CTRL0_BYPASS_MASK)
#define AI_PLL1G_CTRL0_BYPASS_MASK  (0x10000UL)
#define AI_PLL1G_CTRL0_BYPASS_SHIFT (16U)

#define AI_PLL1G_CTRL0_PLL_REG_EN(x) \
    (((uint32_t)(((uint32_t)(x)) << AI_PLL1G_CTRL0_PLL_REG_EN_SHIFT)) & AI_PLL1G_CTRL0_PLL_REG_EN_MASK)
#define AI_PLL1G_CTRL0_PLL_REG_EN_MASK  (0x400000UL)
#define AI_PLL1G_CTRL0_PLL_REG_EN_SHIFT (22U)

#define AI_PHY_LDO_CTRL0_PHY_ISO_B(x) \
    (((uint32_t)(((uint32_t)(x)) << AI_PHY_LDO_CTRL0_PHY_ISO_B_SHIFT)) & AI_PHY_LDO_CTRL0_PHY_ISO_B_MASK)
#define AI_PHY_LDO_CTRL0_PHY_ISO_B_MASK  (0x8000U)
#define AI_PHY_LDO_CTRL0_PHY_ISO_B_SHIFT (15U)

/* Chip Silicon Version Register (MISC_DIFPROG) */
#define ANADIG_MISC_MISC_DIFPROG_CHIPID_SHIFT  (0)  /* Bits 0-32: Chip ID */
#define ANADIG_MISC_MISC_DIFPROG_CHIPID_MASK   (0xFFFFFFFF << ANADIG_MISC_MISC_DIFPROG_CHIPID_SHIFT)
#define ANADIG_MISC_MISC_DIFPROG_CHIPID(n)     (((n) << ANADIG_MISC_MISC_DIFPROG_CHIPID_SHIFT) & ANADIG_MISC_MISC_DIFPROG_CHIPID_MASK)

/* VDDSOC_AI_CTRL_REGISTER (VDDSOC_AI_CTRL) */
#define ANADIG_MISC_VDDSOC_AI_CTRL_VDDSOC_AI_ADDR_SHIFT  (0)        /* Bits 0-8: VDDSOC_AI_ADDR */
#define ANADIG_MISC_VDDSOC_AI_CTRL_VDDSOC_AI_ADDR_MASK   (0xFF << ANADIG_MISC_VDDSOC_AI_CTRL_VDDSOC_AI_ADDR_SHIFT)
#define ANADIG_MISC_VDDSOC_AI_CTRL_VDDSOC_AI_ADDR(n)     (((n) << ANADIG_MISC_VDDSOC_AI_CTRL_VDDSOC_AI_ADDR_SHIFT) & ANADIG_MISC_VDDSOC_AI_CTRL_VDDSOC_AI_ADDR_MASK)
#define ANADIG_MISC_VDDSOC_AI_CTRL_VDDSOC_AIRWB          (1 << 16)  /* Bit 16: VDDSOC_AIRWB */

/* VDDSOC_AI_WDATA_REGISTER (VDDSOC_AI_WDATA) */
#define ANADIG_MISC_VDDSOC_AI_WDATA_VDDSOC_AI_WDATA_SHIFT  (0)  /* Bits 0-32: VDDSOC_AI_WDATA */
#define ANADIG_MISC_VDDSOC_AI_WDATA_VDDSOC_AI_WDATA_MASK   (0xFFFFFFFF << ANADIG_MISC_VDDSOC_AI_WDATA_VDDSOC_AI_WDATA_SHIFT)
#define ANADIG_MISC_VDDSOC_AI_WDATA_VDDSOC_AI_WDATA(n)     (((n) << ANADIG_MISC_VDDSOC_AI_WDATA_VDDSOC_AI_WDATA_SHIFT) & ANADIG_MISC_VDDSOC_AI_WDATA_VDDSOC_AI_WDATA_MASK)

/* VDDSOC_AI_RDATA_REGISTER (VDDSOC_AI_RDATA) */
#define ANADIG_MISC_VDDSOC_AI_RDATA_VDDSOC_AI_RDATA_SHIFT  (0)  /* Bits 0-32: VDDSOC_AI_RDATA */
#define ANADIG_MISC_VDDSOC_AI_RDATA_VDDSOC_AI_RDATA_MASK   (0xFFFFFFFF << ANADIG_MISC_VDDSOC_AI_RDATA_VDDSOC_AI_RDATA_SHIFT)
#define ANADIG_MISC_VDDSOC_AI_RDATA_VDDSOC_AI_RDATA(n)     (((n) << ANADIG_MISC_VDDSOC_AI_RDATA_VDDSOC_AI_RDATA_SHIFT) & ANADIG_MISC_VDDSOC_AI_RDATA_VDDSOC_AI_RDATA_MASK)

/* VDDSOC2PLL_AI_CTRL_1G_REGISTER (VDDSOC2PLL_AI_CTRL_1G) */
#define ANADIG_MISC_VDDSOC2PLL_AI_CTRL_1G_VDDSOC2PLL_AIADDR_1G_SHIFT   (0)        /* Bits 0-8: VDDSOC2PLL_AIADDR_1G */
#define ANADIG_MISC_VDDSOC2PLL_AI_CTRL_1G_VDDSOC2PLL_AIADDR_1G_MASK    (0xFF << ANADIG_MISC_VDDSOC2PLL_AI_CTRL_1G_VDDSOC2PLL_AIADDR_1G_SHIFT)
#define ANADIG_MISC_VDDSOC2PLL_AI_CTRL_1G_VDDSOC2PLL_AIADDR_1G(n)      (((n) << ANADIG_MISC_VDDSOC2PLL_AI_CTRL_1G_VDDSOC2PLL_AIADDR_1G_SHIFT) & ANADIG_MISC_VDDSOC2PLL_AI_CTRL_1G_VDDSOC2PLL_AIADDR_1G_MASK)
#define ANADIG_MISC_VDDSOC2PLL_AI_CTRL_1G_VDDSOC2PLL_AITOGGLE_1G       (1 << 8)   /* Bit 8: VDDSOC2PLL_AITOGGLE_1G */
#define ANADIG_MISC_VDDSOC2PLL_AI_CTRL_1G_VDDSOC2PLL_AITOGGLE_DONE_1G  (1 << 9)   /* Bit 9: VDDSOC2PLL_AITOGGLE_DONE_1G */
#define ANADIG_MISC_VDDSOC2PLL_AI_CTRL_1G_VDDSOC2PLL_AIRWB_1G          (1 << 16)  /* Bit 16: VDDSOC2PLL_AIRWB_1G */

/* VDDSOC2PLL_AI_WDATA_1G_REGISTER (VDDSOC2PLL_AI_WDATA_1G) */
#define ANADIG_MISC_VDDSOC2PLL_AI_WDATA_1G_VDDSOC2PLL_AI_WDATA_1G_SHIFT  (0)  /* Bits 0-32: VDDSOC2PLL_AI_WDATA_1G */
#define ANADIG_MISC_VDDSOC2PLL_AI_WDATA_1G_VDDSOC2PLL_AI_WDATA_1G_MASK   (0xFFFFFFFF << ANADIG_MISC_VDDSOC2PLL_AI_WDATA_1G_VDDSOC2PLL_AI_WDATA_1G_SHIFT)
#define ANADIG_MISC_VDDSOC2PLL_AI_WDATA_1G_VDDSOC2PLL_AI_WDATA_1G(n)     (((n) << ANADIG_MISC_VDDSOC2PLL_AI_WDATA_1G_VDDSOC2PLL_AI_WDATA_1G_SHIFT) & ANADIG_MISC_VDDSOC2PLL_AI_WDATA_1G_VDDSOC2PLL_AI_WDATA_1G_MASK)

/* VDDSOC2PLL_AI_RDATA_1G_REGISTER (VDDSOC2PLL_AI_RDATA_1G) */
#define ANADIG_MISC_VDDSOC2PLL_AI_RDATA_1G_VDDSOC2PLL_AI_RDATA_1G_SHIFT  (0)  /* Bits 0-32: VDDSOC2PLL_AI_RDATA_1G */
#define ANADIG_MISC_VDDSOC2PLL_AI_RDATA_1G_VDDSOC2PLL_AI_RDATA_1G_MASK   (0xFFFFFFFF << ANADIG_MISC_VDDSOC2PLL_AI_RDATA_1G_VDDSOC2PLL_AI_RDATA_1G_SHIFT)
#define ANADIG_MISC_VDDSOC2PLL_AI_RDATA_1G_VDDSOC2PLL_AI_RDATA_1G(n)     (((n) << ANADIG_MISC_VDDSOC2PLL_AI_RDATA_1G_VDDSOC2PLL_AI_RDATA_1G_SHIFT) & ANADIG_MISC_VDDSOC2PLL_AI_RDATA_1G_VDDSOC2PLL_AI_RDATA_1G_MASK)

/* VDDSOC_AI_CTRL_AUDIO_REGISTER (VDDSOC2PLL_AI_CTRL_AUDIO) */
#define ANADIG_MISC_VDDSOC2PLL_AI_CTRL_AUDIO_VDDSOC2PLL_AI_ADDR_AUDIO_SHIFT  (0)        /* Bits 0-8: VDDSOC2PLL_AI_ADDR_AUDIO */
#define ANADIG_MISC_VDDSOC2PLL_AI_CTRL_AUDIO_VDDSOC2PLL_AI_ADDR_AUDIO_MASK   (0xFF << ANADIG_MISC_VDDSOC2PLL_AI_CTRL_AUDIO_VDDSOC2PLL_AI_ADDR_AUDIO_SHIFT)
#define ANADIG_MISC_VDDSOC2PLL_AI_CTRL_AUDIO_VDDSOC2PLL_AI_ADDR_AUDIO(n)     (((n) << ANADIG_MISC_VDDSOC2PLL_AI_CTRL_AUDIO_VDDSOC2PLL_AI_ADDR_AUDIO_SHIFT) & ANADIG_MISC_VDDSOC2PLL_AI_CTRL_AUDIO_VDDSOC2PLL_AI_ADDR_AUDIO_MASK)
#define ANADIG_MISC_VDDSOC2PLL_AI_CTRL_AUDIO_VDDSOC2PLL_AITOGGLE_AUDIO       (1 << 8)   /* Bit 8: VDDSOC2PLL_AITOGGLE_AUDIO */
#define ANADIG_MISC_VDDSOC2PLL_AI_CTRL_AUDIO_VDDSOC2PLL_AITOGGLE_DONE_AUDIO  (1 << 9)   /* Bit 9: VDDSOC2PLL_AITOGGLE_DONE_AUDIO */
#define ANADIG_MISC_VDDSOC2PLL_AI_CTRL_AUDIO_VDDSOC2PLL_AIRWB_AUDIO          (1 << 16)  /* Bit 16: VDDSOC_AIRWB */

/* VDDSOC_AI_WDATA_AUDIO_REGISTER (VDDSOC2PLL_AI_WDATA_AUDIO) */
#define ANADIG_MISC_VDDSOC2PLL_AI_WDATA_AUDIO_VDDSOC2PLL_AI_WDATA_AUDIO_SHIFT  (0)  /* Bits 0-32: VDDSOC2PLL_AI_WDATA_AUDIO */
#define ANADIG_MISC_VDDSOC2PLL_AI_WDATA_AUDIO_VDDSOC2PLL_AI_WDATA_AUDIO_MASK   (0xFFFFFFFF << ANADIG_MISC_VDDSOC2PLL_AI_WDATA_AUDIO_VDDSOC2PLL_AI_WDATA_AUDIO_SHIFT)
#define ANADIG_MISC_VDDSOC2PLL_AI_WDATA_AUDIO_VDDSOC2PLL_AI_WDATA_AUDIO(n)     (((n) << ANADIG_MISC_VDDSOC2PLL_AI_WDATA_AUDIO_VDDSOC2PLL_AI_WDATA_AUDIO_SHIFT) & ANADIG_MISC_VDDSOC2PLL_AI_WDATA_AUDIO_VDDSOC2PLL_AI_WDATA_AUDIO_MASK)

/* VDDSOC2PLL_AI_RDATA_REGISTER (VDDSOC2PLL_AI_RDATA_AUDIO) */
#define ANADIG_MISC_VDDSOC2PLL_AI_RDATA_AUDIO_VDDSOC2PLL_AI_RDATA_AUDIO_SHIFT  (0)  /* Bits 0-32: VDDSOC2PLL_AI_RDATA_AUDIO */
#define ANADIG_MISC_VDDSOC2PLL_AI_RDATA_AUDIO_VDDSOC2PLL_AI_RDATA_AUDIO_MASK   (0xFFFFFFFF << ANADIG_MISC_VDDSOC2PLL_AI_RDATA_AUDIO_VDDSOC2PLL_AI_RDATA_AUDIO_SHIFT)
#define ANADIG_MISC_VDDSOC2PLL_AI_RDATA_AUDIO_VDDSOC2PLL_AI_RDATA_AUDIO(n)     (((n) << ANADIG_MISC_VDDSOC2PLL_AI_RDATA_AUDIO_VDDSOC2PLL_AI_RDATA_AUDIO_SHIFT) & ANADIG_MISC_VDDSOC2PLL_AI_RDATA_AUDIO_VDDSOC2PLL_AI_RDATA_AUDIO_MASK)

/* VDDSOC2PLL_AI_CTRL_VIDEO_REGISTER (VDDSOC2PLL_AI_CTRL_VIDEO) */
#define ANADIG_MISC_VDDSOC2PLL_AI_CTRL_VIDEO_VDDSOC2PLL_AIADDR_VIDEO_SHIFT   (0)        /* Bits 0-8: VDDSOC2PLL_AIADDR_VIDEO */
#define ANADIG_MISC_VDDSOC2PLL_AI_CTRL_VIDEO_VDDSOC2PLL_AIADDR_VIDEO_MASK    (0xFF << ANADIG_MISC_VDDSOC2PLL_AI_CTRL_VIDEO_VDDSOC2PLL_AIADDR_VIDEO_SHIFT)
#define ANADIG_MISC_VDDSOC2PLL_AI_CTRL_VIDEO_VDDSOC2PLL_AIADDR_VIDEO(n)      (((n) << ANADIG_MISC_VDDSOC2PLL_AI_CTRL_VIDEO_VDDSOC2PLL_AIADDR_VIDEO_SHIFT) & ANADIG_MISC_VDDSOC2PLL_AI_CTRL_VIDEO_VDDSOC2PLL_AIADDR_VIDEO_MASK)
#define ANADIG_MISC_VDDSOC2PLL_AI_CTRL_VIDEO_VDDSOC2PLL_AITOGGLE_VIDEO       (1 << 8)   /* Bit 8: VDDSOC2PLL_AITOGGLE_VIDEO */
#define ANADIG_MISC_VDDSOC2PLL_AI_CTRL_VIDEO_VDDSOC2PLL_AITOGGLE_DONE_VIDEO  (1 << 9)   /* Bit 9: VDDSOC2PLL_AITOGGLE_DONE_VIDEO */
#define ANADIG_MISC_VDDSOC2PLL_AI_CTRL_VIDEO_VDDSOC2PLL_AIRWB_VIDEO          (1 << 16)  /* Bit 16: VDDSOC2PLL_AIRWB_VIDEO */

/* VDDSOC2PLL_AI_WDATA_VIDEO_REGISTER (VDDSOC2PLL_AI_WDATA_VIDEO) */
#define ANADIG_MISC_VDDSOC2PLL_AI_WDATA_VIDEO_VDDSOC2PLL_AI_WDATA_VIDEO_SHIFT  (0)  /* Bits 0-32: VDDSOC2PLL_AI_WDATA_VIDEO */
#define ANADIG_MISC_VDDSOC2PLL_AI_WDATA_VIDEO_VDDSOC2PLL_AI_WDATA_VIDEO_MASK   (0xFFFFFFFF << ANADIG_MISC_VDDSOC2PLL_AI_WDATA_VIDEO_VDDSOC2PLL_AI_WDATA_VIDEO_SHIFT)
#define ANADIG_MISC_VDDSOC2PLL_AI_WDATA_VIDEO_VDDSOC2PLL_AI_WDATA_VIDEO(n)     (((n) << ANADIG_MISC_VDDSOC2PLL_AI_WDATA_VIDEO_VDDSOC2PLL_AI_WDATA_VIDEO_SHIFT) & ANADIG_MISC_VDDSOC2PLL_AI_WDATA_VIDEO_VDDSOC2PLL_AI_WDATA_VIDEO_MASK)

/* VDDSOC2PLL_AI_RDATA_VIDEO_REGISTER (VDDSOC2PLL_AI_RDATA_VIDEO) */
#define ANADIG_MISC_VDDSOC2PLL_AI_RDATA_VIDEO_VDDSOC2PLL_AI_RDATA_VIDEO_SHIFT  (0)  /* Bits 0-32: VDDSOC2PLL_AI_RDATA_VIDEO */
#define ANADIG_MISC_VDDSOC2PLL_AI_RDATA_VIDEO_VDDSOC2PLL_AI_RDATA_VIDEO_MASK   (0xFFFFFFFF << ANADIG_MISC_VDDSOC2PLL_AI_RDATA_VIDEO_VDDSOC2PLL_AI_RDATA_VIDEO_SHIFT)
#define ANADIG_MISC_VDDSOC2PLL_AI_RDATA_VIDEO_VDDSOC2PLL_AI_RDATA_VIDEO(n)     (((n) << ANADIG_MISC_VDDSOC2PLL_AI_RDATA_VIDEO_VDDSOC2PLL_AI_RDATA_VIDEO_SHIFT) & ANADIG_MISC_VDDSOC2PLL_AI_RDATA_VIDEO_VDDSOC2PLL_AI_RDATA_VIDEO_MASK)

/* VDDSOC_AI_CTRL_REGISTER (VDDLPSR_AI_CTRL) */
#define ANADIG_MISC_VDDLPSR_AI_CTRL_VDDLPSR_AI_ADDR_SHIFT  (0)        /* Bits 0-8: VDDLPSR_AI_ADDR */
#define ANADIG_MISC_VDDLPSR_AI_CTRL_VDDLPSR_AI_ADDR_MASK   (0xFF << ANADIG_MISC_VDDLPSR_AI_CTRL_VDDLPSR_AI_ADDR_SHIFT)
#define ANADIG_MISC_VDDLPSR_AI_CTRL_VDDLPSR_AI_ADDR(n)     (((n) << ANADIG_MISC_VDDLPSR_AI_CTRL_VDDLPSR_AI_ADDR_SHIFT) & ANADIG_MISC_VDDLPSR_AI_CTRL_VDDLPSR_AI_ADDR_MASK)
#define ANADIG_MISC_VDDLPSR_AI_CTRL_VDDLPSR_AIRWB          (1 << 16)  /* Bit 16: VDDLPSR_AIRWB */

/* VDDLPSR_AI_WDATA_REGISTER (VDDLPSR_AI_WDATA) */
#define ANADIG_MISC_VDDLPSR_AI_WDATA_VDDLPSR_AI_WDATA_SHIFT  (0)  /* Bits 0-32: VDD_LPSR_AI_WDATA */
#define ANADIG_MISC_VDDLPSR_AI_WDATA_VDDLPSR_AI_WDATA_MASK   (0xFFFFFFFF << ANADIG_MISC_VDDLPSR_AI_WDATA_VDDLPSR_AI_WDATA_SHIFT)
#define ANADIG_MISC_VDDLPSR_AI_WDATA_VDDLPSR_AI_WDATA(n)     (((n) << ANADIG_MISC_VDDLPSR_AI_WDATA_VDDLPSR_AI_WDATA_SHIFT) & ANADIG_MISC_VDDLPSR_AI_WDATA_VDDLPSR_AI_WDATA_MASK)

/* VDDLPSR_AI_RDATA_REFTOP_REGISTER (VDDLPSR_AI_RDATA_REFTOP) */
#define ANADIG_MISC_VDDLPSR_AI_RDATA_REFTOP_VDDLPSR_AI_RDATA_REFTOP_SHIFT  (0)  /* Bits 0-32: VDDLPSR_AI_RDATA_REFTOP */
#define ANADIG_MISC_VDDLPSR_AI_RDATA_REFTOP_VDDLPSR_AI_RDATA_REFTOP_MASK   (0xFFFFFFFF << ANADIG_MISC_VDDLPSR_AI_RDATA_REFTOP_VDDLPSR_AI_RDATA_REFTOP_SHIFT)
#define ANADIG_MISC_VDDLPSR_AI_RDATA_REFTOP_VDDLPSR_AI_RDATA_REFTOP(n)     (((n) << ANADIG_MISC_VDDLPSR_AI_RDATA_REFTOP_VDDLPSR_AI_RDATA_REFTOP_SHIFT) & ANADIG_MISC_VDDLPSR_AI_RDATA_REFTOP_VDDLPSR_AI_RDATA_REFTOP_MASK)

/* VDDLPSR_AI_RDATA_TMPSNS_REGISTER (VDDLPSR_AI_RDATA_TMPSNS) */
#define ANADIG_MISC_VDDLPSR_AI_RDATA_TMPSNS_VDDLPSR_AI_RDATA_TMPSNS_SHIFT  (0)  /* Bits 0-32: VDDLPSR_AI_RDATA_TMPSNS */
#define ANADIG_MISC_VDDLPSR_AI_RDATA_TMPSNS_VDDLPSR_AI_RDATA_TMPSNS_MASK   (0xFFFFFFFF << ANADIG_MISC_VDDLPSR_AI_RDATA_TMPSNS_VDDLPSR_AI_RDATA_TMPSNS_SHIFT)
#define ANADIG_MISC_VDDLPSR_AI_RDATA_TMPSNS_VDDLPSR_AI_RDATA_TMPSNS(n)     (((n) << ANADIG_MISC_VDDLPSR_AI_RDATA_TMPSNS_VDDLPSR_AI_RDATA_TMPSNS_SHIFT) & ANADIG_MISC_VDDLPSR_AI_RDATA_TMPSNS_VDDLPSR_AI_RDATA_TMPSNS_MASK)

/* VDDLPSR_AI400M_CTRL_REGISTER (VDDLPSR_AI400M_CTRL) */
#define ANADIG_MISC_VDDLPSR_AI400M_CTRL_VDDLPSR_AI400M_ADDR_SHIFT   (0)        /* Bits 0-8: VDDLPSR_AI400M_ADDR */
#define ANADIG_MISC_VDDLPSR_AI400M_CTRL_VDDLPSR_AI400M_ADDR_MASK    (0xFF << ANADIG_MISC_VDDLPSR_AI400M_CTRL_VDDLPSR_AI400M_ADDR_SHIFT)
#define ANADIG_MISC_VDDLPSR_AI400M_CTRL_VDDLPSR_AI400M_ADDR(n)      (((n) << ANADIG_MISC_VDDLPSR_AI400M_CTRL_VDDLPSR_AI400M_ADDR_SHIFT) & ANADIG_MISC_VDDLPSR_AI400M_CTRL_VDDLPSR_AI400M_ADDR_MASK)
#define ANADIG_MISC_VDDLPSR_AI400M_CTRL_VDDLPSR_AITOGGLE_400M       (1 << 8)   /* Bit 8: VDDLPSR_AITOGGLE_400M */
#define ANADIG_MISC_VDDLPSR_AI400M_CTRL_VDDLPSR_AITOGGLE_DONE_400M  (1 << 9)   /* Bit 9: VDDLPSR_AITOGGLE_DONE_400M */
#define ANADIG_MISC_VDDLPSR_AI400M_CTRL_VDDLPSR_AI400M_RWB          (1 << 16)  /* Bit 16: VDDLPSR_AI400M_RWB */

/* VDDLPSR_AI400M_WDATA_REGISTER (VDDLPSR_AI400M_WDATA) */
#define ANADIG_MISC_VDDLPSR_AI400M_WDATA_VDDLPSR_AI400M_WDATA_SHIFT  (0)  /* Bits 0-32: VDDLPSR_AI400M_WDATA */
#define ANADIG_MISC_VDDLPSR_AI400M_WDATA_VDDLPSR_AI400M_WDATA_MASK   (0xFFFFFFFF << ANADIG_MISC_VDDLPSR_AI400M_WDATA_VDDLPSR_AI400M_WDATA_SHIFT)
#define ANADIG_MISC_VDDLPSR_AI400M_WDATA_VDDLPSR_AI400M_WDATA(n)     (((n) << ANADIG_MISC_VDDLPSR_AI400M_WDATA_VDDLPSR_AI400M_WDATA_SHIFT) & ANADIG_MISC_VDDLPSR_AI400M_WDATA_VDDLPSR_AI400M_WDATA_MASK)

/* VDDLPSR_AI400M_RDATA_REGISTER (VDDLPSR_AI400M_RDATA) */
#define ANADIG_MISC_VDDLPSR_AI400M_RDATA_VDDLPSR_AI400M_RDATA_SHIFT  (0)  /* Bits 0-32: VDDLPSR_AI400M_RDATA */
#define ANADIG_MISC_VDDLPSR_AI400M_RDATA_VDDLPSR_AI400M_RDATA_MASK   (0xFFFFFFFF << ANADIG_MISC_VDDLPSR_AI400M_RDATA_VDDLPSR_AI400M_RDATA_SHIFT)
#define ANADIG_MISC_VDDLPSR_AI400M_RDATA_VDDLPSR_AI400M_RDATA(n)     (((n) << ANADIG_MISC_VDDLPSR_AI400M_RDATA_VDDLPSR_AI400M_RDATA_SHIFT) & ANADIG_MISC_VDDLPSR_AI400M_RDATA_VDDLPSR_AI400M_RDATA_MASK)

#endif /* __ARCH_ARM_SRC_IMXRT_HARDWARE_RT117X_IMXRT117X_ANADIG_H */
