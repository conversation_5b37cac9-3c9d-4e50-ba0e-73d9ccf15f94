############################################################################
# arch/arm/src/xmc4/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

include armv7-m/Make.defs

# Required XMC4xxx files

CHIP_CSRCS  = xmc4_allocateheap.c xmc4_clockconfig.c xmc4_clockutils.c
CHIP_CSRCS += xmc4_clrpend.c xmc4_irq.c xmc4_lowputc.c xmc4_gpio.c
CHIP_CSRCS += xmc4_serial.c xmc4_start.c xmc4_usic.c

# Configuration-dependent Kinetis files

ifneq ($(CONFIG_ARCH_IDLE_CUSTOM),y)
CHIP_CSRCS += xmc4_idle.c
endif

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += xmc4_timerisr.c
endif

ifeq ($(CONFIG_BUILD_PROTECTED),y)
CHIP_CSRCS += xmc4_userspace.c xmc4_mpuinit.c
endif

ifeq ($(CONFIG_DEBUG_GPIO_INFO),y)
CHIP_CSRCS += xmc4_pindump.c
endif

ifeq ($(CONFIG_XMC4_USCI_SPI),y)
CHIP_CSRCS += xmc4_spi.c
endif
