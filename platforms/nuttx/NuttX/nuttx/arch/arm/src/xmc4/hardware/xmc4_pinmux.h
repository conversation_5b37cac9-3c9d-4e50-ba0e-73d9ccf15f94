/****************************************************************************
 * arch/arm/src/xmc4/hardware/xmc4_pinmux.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/* Reference: XMC4500 Reference Manual V1.5 2014-07 Microcontrollers. */

#ifndef __ARCH_ARM_SRC_XMC4_HARDWARE_XMC4_PINMUX_H
#define __ARCH_ARM_SRC_XMC4_HARDWARE_XMC4_PINMUX_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Alternate Pin Functions.  All members of the XMC4xxx family share the same
 * pin multiplexing (although they may differ in the pins physically
 * available).
 *
 * Alternative pin selections are provided with a numeric suffix like _1, _2,
 * etc. Drivers, however, will use the pin selection without the numeric
 * suffix.
 * Additional definitions are required in the board.h file.  For example, if
 * CAN_N2TXD connects vis P1.9 on some board, then the following definition
 * should appear in the board.h header file for that board:
 *
 * #define GPIO_CAN_N2TXD GPIO_CAN_N2TXD_1
 *
 * The driver will then automatically configure PA11 as the CAN1 RX pin.
 */

/* WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific GPIO options such as
 * frequency, open-drain/push-pull, and pull-up/down!  Just the basics are
 * defined for most pins in this file.
 */

#define GPIO_CAN_N0RXDA          (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_CAN_N0RXDB          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN3)
#define GPIO_CAN_N0RXDC          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN12)
#define GPIO_CAN_N0TXD_1         (GPIO_OUTPUT_ALT2 | GPIO_PORT0 | GPIO_PIN0)
#define GPIO_CAN_N0TXD_2         (GPIO_OUTPUT_ALT2 | GPIO_PORT1 | GPIO_PIN4)
#define GPIO_CAN_N0TXD_3         (GPIO_OUTPUT_ALT2 | GPIO_PORT3 | GPIO_PIN10)
#define GPIO_CAN_N0TXD_4         (GPIO_OUTPUT_ALT2 | GPIO_PORT3 | GPIO_PIN2)
#define GPIO_CAN_N1RXDA          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN6)
#define GPIO_CAN_N1RXDB          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN11)
#define GPIO_CAN_N1RXDC          (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN13)
#define GPIO_CAN_N1RXDD          (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN4)
#define GPIO_CAN_N1TXD_1         (GPIO_OUTPUT_ALT1 | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_CAN_N1TXD_2         (GPIO_OUTPUT_ALT2 | GPIO_PORT1 | GPIO_PIN12)
#define GPIO_CAN_N1TXD_3         (GPIO_OUTPUT_ALT2 | GPIO_PORT2 | GPIO_PIN7)
#define GPIO_CAN_N1TXD_4         (GPIO_OUTPUT_ALT2 | GPIO_PORT3 | GPIO_PIN9)
#define GPIO_CAN_N2RXDA          (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN8)
#define GPIO_CAN_N2RXDB          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN8)
#define GPIO_CAN_N2RXDC          (GPIO_INPUT | GPIO_PORT4 | GPIO_PIN6)
#define GPIO_CAN_N2TXD_1         (GPIO_OUTPUT_ALT2 | GPIO_PORT1 | GPIO_PIN9)
#define GPIO_CAN_N2TXD_2         (GPIO_OUTPUT_ALT2 | GPIO_PORT3 | GPIO_PIN7)
#define GPIO_CAN_N2TXD_3         (GPIO_OUTPUT_ALT2 | GPIO_PORT4 | GPIO_PIN7)

#define GPIO_CCU40_IN0A          (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN3)
#define GPIO_CCU40_IN0B          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_CCU40_IN0C          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN1)
#define GPIO_CCU40_IN1A          (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN2)
#define GPIO_CCU40_IN1B          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_CCU40_IN1C          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN0)
#define GPIO_CCU40_IN2A          (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN1)
#define GPIO_CCU40_IN2B          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_CCU40_IN2C          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN7)
#define GPIO_CCU40_IN3A          (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN0)
#define GPIO_CCU40_IN3B          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_CCU40_IN3C          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN6)
#define GPIO_CCU40_OUT0_1        (GPIO_OUTPUT_ALT3 | GPIO_PORT0 | GPIO_PIN15)
#define GPIO_CCU40_OUT0_2        (GPIO_OUTPUT_ALT3 | GPIO_PORT1 | GPIO_PIN3)
#define GPIO_CCU40_OUT1_1        (GPIO_OUTPUT_ALT3 | GPIO_PORT0 | GPIO_PIN14)
#define GPIO_CCU40_OUT1_2        (GPIO_OUTPUT_ALT3 | GPIO_PORT1 | GPIO_PIN2)
#define GPIO_CCU40_OUT2_1        (GPIO_OUTPUT_ALT3 | GPIO_PORT0 | GPIO_PIN13)
#define GPIO_CCU40_OUT2_2        (GPIO_OUTPUT_ALT3 | GPIO_PORT1 | GPIO_PIN1)
#define GPIO_CCU40_OUT3_1        (GPIO_OUTPUT_ALT3 | GPIO_PORT0 | GPIO_PIN12)
#define GPIO_CCU40_OUT3_2        (GPIO_OUTPUT_ALT3 | GPIO_PORT1 | GPIO_PIN0)
#define GPIO_CCU41_IN0A          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_CCU41_IN0B          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_CCU41_IN0C          (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN4)
#define GPIO_CCU41_IN1A          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_CCU41_IN1B          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_CCU41_IN1C          (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_CCU41_IN2A          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_CCU41_IN2B          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_CCU41_IN2C          (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN10)
#define GPIO_CCU41_IN3A          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_CCU41_IN3B          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_CCU41_IN3C          (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN11)
#define GPIO_CCU41_OUT0_1        (GPIO_OUTPUT_ALT3 | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_CCU41_OUT0_2        (GPIO_OUTPUT_ALT3 | GPIO_PORT3 | GPIO_PIN10)
#define GPIO_CCU41_OUT1_1        (GPIO_OUTPUT_ALT3 | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_CCU41_OUT1_2        (GPIO_OUTPUT_ALT3 | GPIO_PORT3 | GPIO_PIN9)
#define GPIO_CCU41_OUT2_1        (GPIO_OUTPUT_ALT3 | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_CCU41_OUT2_2        (GPIO_OUTPUT_ALT3 | GPIO_PORT3 | GPIO_PIN8)
#define GPIO_CCU41_OUT3_1        (GPIO_OUTPUT_ALT3 | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_CCU41_OUT3_2        (GPIO_OUTPUT_ALT3 | GPIO_PORT3 | GPIO_PIN7)
#define GPIO_CCU42_IN0A          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN6)
#define GPIO_CCU42_IN0B          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN15)
#define GPIO_CCU42_IN0C          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN15)
#define GPIO_CCU42_IN1A          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN5)
#define GPIO_CCU42_IN1B          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN15)
#define GPIO_CCU42_IN1C          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN14)
#define GPIO_CCU42_IN2A          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN4)
#define GPIO_CCU42_IN2B          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN15)
#define GPIO_CCU42_IN2C          (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN15)
#define GPIO_CCU42_IN3A          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN3)
#define GPIO_CCU42_IN3B          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN15)
#define GPIO_CCU42_IN3C          (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN14)
#define GPIO_CCU42_OUT0_1        (GPIO_OUTPUT_ALT3 | GPIO_PORT3 | GPIO_PIN0)
#define GPIO_CCU42_OUT0_2        (GPIO_OUTPUT_ALT3 | GPIO_PORT3 | GPIO_PIN6)
#define GPIO_CCU42_OUT1_1        (GPIO_OUTPUT_ALT3 | GPIO_PORT3 | GPIO_PIN13)
#define GPIO_CCU42_OUT1_2        (GPIO_OUTPUT_ALT3 | GPIO_PORT3 | GPIO_PIN5)
#define GPIO_CCU42_OUT2_1        (GPIO_OUTPUT_ALT3 | GPIO_PORT3 | GPIO_PIN12)
#define GPIO_CCU42_OUT2_2        (GPIO_OUTPUT_ALT3 | GPIO_PORT3 | GPIO_PIN4)
#define GPIO_CCU42_OUT3_1        (GPIO_OUTPUT_ALT3 | GPIO_PORT3 | GPIO_PIN11)
#define GPIO_CCU42_OUT3_2        (GPIO_OUTPUT_ALT3 | GPIO_PORT3 | GPIO_PIN3)
#define GPIO_CCU43_IN0A          (GPIO_INPUT | GPIO_PORT4 | GPIO_PIN6)
#define GPIO_CCU43_IN0B          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN14)
#define GPIO_CCU43_IN0C          (GPIO_INPUT | GPIO_PORT4 | GPIO_PIN7)
#define GPIO_CCU43_IN1A          (GPIO_INPUT | GPIO_PORT4 | GPIO_PIN5)
#define GPIO_CCU43_IN1B          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN14)
#define GPIO_CCU43_IN1C          (GPIO_INPUT | GPIO_PORT4 | GPIO_PIN2)
#define GPIO_CCU43_IN2A          (GPIO_INPUT | GPIO_PORT4 | GPIO_PIN4)
#define GPIO_CCU43_IN2B          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN14)
#define GPIO_CCU43_IN2C          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN13)
#define GPIO_CCU43_IN3A          (GPIO_INPUT | GPIO_PORT4 | GPIO_PIN3)
#define GPIO_CCU43_IN3B          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN14)
#define GPIO_CCU43_IN3C          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN12)
#define GPIO_CCU43_OUT0_1        (GPIO_OUTPUT_ALT3 | GPIO_PORT4 | GPIO_PIN6)
#define GPIO_CCU43_OUT0_2        (GPIO_OUTPUT_ALT3 | GPIO_PORT6 | GPIO_PIN5)
#define GPIO_CCU43_OUT1_1        (GPIO_OUTPUT_ALT3 | GPIO_PORT4 | GPIO_PIN5)
#define GPIO_CCU43_OUT1_2        (GPIO_OUTPUT_ALT3 | GPIO_PORT6 | GPIO_PIN4)
#define GPIO_CCU43_OUT2_1        (GPIO_OUTPUT_ALT3 | GPIO_PORT4 | GPIO_PIN4)
#define GPIO_CCU43_OUT2_2        (GPIO_OUTPUT_ALT3 | GPIO_PORT6 | GPIO_PIN3)
#define GPIO_CCU43_OUT3_1        (GPIO_OUTPUT_ALT3 | GPIO_PORT4 | GPIO_PIN3)
#define GPIO_CCU43_OUT3_2        (GPIO_OUTPUT_ALT3 | GPIO_PORT6 | GPIO_PIN2)
#define GPIO_CCU80_IN0A          (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_CCU80_IN0B          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN4)
#define GPIO_CCU80_IN0C          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN2)
#define GPIO_CCU80_IN1A          (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_CCU80_IN1B          (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN8)
#define GPIO_CCU80_IN1C          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN1)
#define GPIO_CCU80_IN2A          (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_CCU80_IN2B          (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN6)
#define GPIO_CCU80_IN2C          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN0)
#define GPIO_CCU80_IN3A          (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_CCU80_IN3B          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN3)
#define GPIO_CCU80_IN3C          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN13)
#define GPIO_CCU80_OUT00_1       (GPIO_OUTPUT_ALT3 | GPIO_PORT0 | GPIO_PIN5)
#define GPIO_CCU80_OUT00_2       (GPIO_OUTPUT_ALT3 | GPIO_PORT5 | GPIO_PIN11)
#define GPIO_CCU80_OUT01_1       (GPIO_OUTPUT_ALT3 | GPIO_PORT0 | GPIO_PIN2)
#define GPIO_CCU80_OUT01_2       (GPIO_OUTPUT_ALT3 | GPIO_PORT5 | GPIO_PIN8)
#define GPIO_CCU80_OUT02 1       (GPIO_OUTPUT_ALT3 | GPIO_PORT0 | GPIO_PIN10)
#define GPIO_CCU80_OUT03 2       (GPIO_OUTPUT_ALT3 | GPIO_PORT2 | GPIO_PIN7)
#define GPIO_CCU80_OUT10_1       (GPIO_OUTPUT_ALT3 | GPIO_PORT0 | GPIO_PIN4)
#define GPIO_CCU80_OUT10_2       (GPIO_OUTPUT_ALT3 | GPIO_PORT5 | GPIO_PIN10)
#define GPIO_CCU80_OUT11_1       (GPIO_OUTPUT_ALT3 | GPIO_PORT0 | GPIO_PIN1)
#define GPIO_CCU80_OUT11_2       (GPIO_OUTPUT_ALT3 | GPIO_PORT2 | GPIO_PIN15)
#define GPIO_CCU80_OUT12         (GPIO_OUTPUT_ALT3 | GPIO_PORT0 | GPIO_PIN9)
#define GPIO_CCU80_OUT13         (GPIO_OUTPUT_ALT3 | GPIO_PORT2 | GPIO_PIN6)
#define GPIO_CCU80_OUT20_1       (GPIO_OUTPUT_ALT3 | GPIO_PORT0 | GPIO_PIN3)
#define GPIO_CCU80_OUT20_2       (GPIO_OUTPUT_ALT3 | GPIO_PORT5 | GPIO_PIN9)
#define GPIO_CCU80_OUT21_1       (GPIO_OUTPUT_ALT3 | GPIO_PORT0 | GPIO_PIN0)
#define GPIO_CCU80_OUT21_2       (GPIO_OUTPUT_ALT3 | GPIO_PORT2 | GPIO_PIN14)
#define GPIO_CCU80_OUT22_1       (GPIO_OUTPUT_ALT3 | GPIO_PORT2 | GPIO_PIN11)
#define GPIO_CCU80_OUT22_2       (GPIO_OUTPUT_ALT3 | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_CCU80_OUT23         (GPIO_OUTPUT_ALT3 | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_CCU80_OUT30         (GPIO_OUTPUT_ALT3 | GPIO_PORT0 | GPIO_PIN6)
#define GPIO_CCU80_OUT31         (GPIO_OUTPUT_ALT3 | GPIO_PORT0 | GPIO_PIN11)
#define GPIO_CCU80_OUT32         (GPIO_OUTPUT_ALT3 | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_CCU80_OUT33         (GPIO_OUTPUT_ALT3 | GPIO_PORT1 | GPIO_PIN4)
#define GPIO_CCU81_IN0A          (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN0)
#define GPIO_CCU81_IN0B          (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN1)
#define GPIO_CCU81_IN0C          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN0)
#define GPIO_CCU81_IN1A          (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN0)
#define GPIO_CCU81_IN1B          (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN2)
#define GPIO_CCU81_IN1C          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN13)
#define GPIO_CCU81_IN2A          (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN0)
#define GPIO_CCU81_IN2B          (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN3)
#define GPIO_CCU81_IN2C          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN12)
#define GPIO_CCU81_IN3A          (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN0)
#define GPIO_CCU81_IN3B          (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN4)
#define GPIO_CCU81_IN3C          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN11)
#define GPIO_CCU81_OUT00         (GPIO_OUTPUT_ALT3 | GPIO_PORT1 | GPIO_PIN15)
#define GPIO_CCU81_OUT01_1       (GPIO_OUTPUT_ALT2 | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_CCU81_OUT01_2       (GPIO_OUTPUT_ALT3 | GPIO_PORT1 | GPIO_PIN12)
#define GPIO_CCU81_OUT02         (GPIO_OUTPUT_ALT3 | GPIO_PORT5 | GPIO_PIN7)
#define GPIO_CCU81_OUT03         (GPIO_OUTPUT_ALT3 | GPIO_PORT5 | GPIO_PIN6)
#define GPIO_CCU81_OUT10_1       (GPIO_OUTPUT_ALT3 | GPIO_PORT1 | GPIO_PIN14)
#define GPIO_CCU81_OUT10_2       (GPIO_OUTPUT_ALT4 | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_CCU81_OUT11_1       (GPIO_OUTPUT_ALT2 | GPIO_PORT2 | GPIO_PIN1)
#define GPIO_CCU81_OUT11_2       (GPIO_OUTPUT_ALT3 | GPIO_PORT1 | GPIO_PIN11)
#define GPIO_CCU81_OUT12         (GPIO_OUTPUT_ALT3 | GPIO_PORT5 | GPIO_PIN5)
#define GPIO_CCU81_OUT13         (GPIO_OUTPUT_ALT3 | GPIO_PORT5 | GPIO_PIN4)
#define GPIO_CCU81_OUT20_1       (GPIO_OUTPUT_ALT3 | GPIO_PORT1 | GPIO_PIN13)
#define GPIO_CCU81_OUT20_2       (GPIO_OUTPUT_ALT4 | GPIO_PORT1 | GPIO_PIN4)
#define GPIO_CCU81_OUT21_1       (GPIO_OUTPUT_ALT2 | GPIO_PORT2 | GPIO_PIN0)
#define GPIO_CCU81_OUT21_2       (GPIO_OUTPUT_ALT3 | GPIO_PORT1 | GPIO_PIN10)
#define GPIO_CCU81_OUT22         (GPIO_OUTPUT_ALT3 | GPIO_PORT5 | GPIO_PIN3)
#define GPIO_CCU81_OUT23         (GPIO_OUTPUT_ALT3 | GPIO_PORT5 | GPIO_PIN2)
#define GPIO_CCU81_OUT30         (GPIO_OUTPUT_ALT3 | GPIO_PORT6 | GPIO_PIN1)
#define GPIO_CCU81_OUT31         (GPIO_OUTPUT_ALT3 | GPIO_PORT6 | GPIO_PIN0)
#define GPIO_CCU81_OUT32         (GPIO_OUTPUT_ALT3 | GPIO_PORT5 | GPIO_PIN1)
#define GPIO_CCU81_OUT33_1       (GPIO_OUTPUT_ALT3 | GPIO_PORT2 | GPIO_PIN12)
#define GPIO_CCU81_OUT33_2       (GPIO_OUTPUT_ALT3 | GPIO_PORT5 | GPIO_PIN0)

#define GPIO_DAC_OUT0            (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT14 | GPIO_PIN8)
#define GPIO_DAC_OUT1            (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT14 | GPIO_PIN9)

#define GPIO_DAC_TRIGGER4        (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_DAC_TRIGGER5        (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_DB_ETMTRACECLK_1    (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN14)
#define GPIO_DB_ETMTRACECLK_2    (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT6 | GPIO_PIN0)
#define GPIO_DB_ETMTRACEDATA0_1  (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN13)
#define GPIO_DB_ETMTRACEDATA0_2  (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT6 | GPIO_PIN6)
#define GPIO_DB_ETMTRACEDATA1_1  (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN12)
#define GPIO_DB_ETMTRACEDATA1_2  (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT6 | GPIO_PIN5)
#define GPIO_DB_ETMTRACEDATA2_1  (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN11)
#define GPIO_DB_ETMTRACEDATA2_2  (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT6 | GPIO_PIN2)
#define GPIO_DB_ETMTRACEDATA3_1  (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN10)
#define GPIO_DB_ETMTRACEDATA3_2  (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT6 | GPIO_PIN1)
#define GPIO_DB_TDI              (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_DB_TDO              (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN1)

#define GPIO_DB_TRST             (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT0 | GPIO_PIN8)
#define GPIO_DSD_CGPWMN_1        (GPIO_OUTPUT_ALT1 | GPIO_PORT1 | GPIO_PIN0)
#define GPIO_DSD_CGPWMN_2        (GPIO_OUTPUT_ALT2 | GPIO_PORT5 | GPIO_PIN0)
#define GPIO_DSD_CGPWMN_3        (GPIO_OUTPUT_ALT3 | GPIO_PORT2 | GPIO_PIN0)
#define GPIO_DSD_CGPWMP_1        (GPIO_OUTPUT_ALT1 | GPIO_PORT1 | GPIO_PIN1)
#define GPIO_DSD_CGPWMP_2        (GPIO_OUTPUT_ALT2 | GPIO_PORT5 | GPIO_PIN1)
#define GPIO_DSD_CGPWMP_3        (GPIO_OUTPUT_ALT3 | GPIO_PORT2 | GPIO_PIN1)
#define GPIO_DSD_DIN0A           (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN8)
#define GPIO_DSD_DIN0B           (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN7)
#define GPIO_DSD_DIN1A           (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_DSD_DIN1B           (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN6)
#define GPIO_DSD_DIN2A           (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN6)
#define GPIO_DSD_DIN2B           (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_DSD_DIN3A           (GPIO_INPUT | GPIO_PORT6 | GPIO_PIN5)
#define GPIO_DSD_DIN3B           (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN3)
#define GPIO_DSD_MCLK0           (GPIO_OUTPUT_ALT3 | GPIO_PORT4 | GPIO_PIN1)
#define GPIO_DSD_MCLK0A          (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN9)
#define GPIO_DSD_MCLK0B          (GPIO_INPUT | GPIO_PORT4 | GPIO_PIN1)
#define GPIO_DSD_MCLK1_1         (GPIO_OUTPUT_ALT3 | GPIO_PORT1 | GPIO_PIN8)
#define GPIO_DSD_MCLK1_2         (GPIO_OUTPUT_ALT3 | GPIO_PORT4 | GPIO_PIN0)
#define GPIO_DSD_MCLK1A          (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN8)
#define GPIO_DSD_MCLK1B          (GPIO_INPUT | GPIO_PORT4 | GPIO_PIN0)
#define GPIO_DSD_MCLK2_1         (GPIO_OUTPUT_ALT2 | GPIO_PORT1 | GPIO_PIN15)
#define GPIO_DSD_MCLK2_2         (GPIO_OUTPUT_ALT3 | GPIO_PORT1 | GPIO_PIN7)
#define GPIO_DSD_MCLK2A          (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN7)
#define GPIO_DSD_MCLK2B          (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN15)
#define GPIO_DSD_MCLK3_1         (GPIO_OUTPUT_ALT3 | GPIO_PORT6 | GPIO_PIN6)
#define GPIO_DSD_MCLK3_2         (GPIO_OUTPUT_ALT4 | GPIO_PORT3 | GPIO_PIN4)
#define GPIO_DSD_MCLK3A          (GPIO_INPUT | GPIO_PORT6 | GPIO_PIN6)
#define GPIO_DSD_MCLK3B          (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN4)

#define GPIO_EBU_A16             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT6 | GPIO_PIN0)
#define GPIO_EBU_A17             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT6 | GPIO_PIN1)
#define GPIO_EBU_A18             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT6 | GPIO_PIN2)
#define GPIO_EBU_A19             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT6 | GPIO_PIN4)
#define GPIO_EBU_A20             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT5 | GPIO_PIN3)
#define GPIO_EBU_A21             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT5 | GPIO_PIN4)
#define GPIO_EBU_A22             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT5 | GPIO_PIN5)
#define GPIO_EBU_A23             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT5 | GPIO_PIN6)
#define GPIO_EBU_AD0             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT0 | GPIO_PIN2)
#define GPIO_EBU_AD1             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT0 | GPIO_PIN3)
#define GPIO_EBU_AD2             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT0 | GPIO_PIN4)
#define GPIO_EBU_AD3             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT0 | GPIO_PIN5)
#define GPIO_EBU_AD4             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT3 | GPIO_PIN5)
#define GPIO_EBU_AD5             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT3 | GPIO_PIN6)
#define GPIO_EBU_AD6             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_EBU_AD7             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT0 | GPIO_PIN8)
#define GPIO_EBU_AD8             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT4 | GPIO_PIN0)
#define GPIO_EBU_AD9             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT4 | GPIO_PIN1)
#define GPIO_EBU_AD10            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN6)
#define GPIO_EBU_AD11            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN7)
#define GPIO_EBU_AD12            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN8)
#define GPIO_EBU_AD13            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN9)
#define GPIO_EBU_AD14            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN2)
#define GPIO_EBU_AD15            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN3)
#define GPIO_EBU_AD16            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN12)
#define GPIO_EBU_AD17            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN13)
#define GPIO_EBU_AD18            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN14)
#define GPIO_EBU_AD19            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN15)
#define GPIO_EBU_AD20            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN0)
#define GPIO_EBU_AD21            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN1)
#define GPIO_EBU_AD22            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_EBU_AD23            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_EBU_AD24            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_EBU_AD25            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_EBU_AD26            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_EBU_AD27            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_EBU_AD28            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN10)
#define GPIO_EBU_AD29            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN11)
#define GPIO_EBU_AD30            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN12)
#define GPIO_EBU_AD31            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN13)
#define GPIO_EBU_ADV             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT0 | GPIO_PIN6)
#define GPIO_EBU_BC0             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN14)
#define GPIO_EBU_BC1             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN15)
#define GPIO_EBU_BC2             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT6 | GPIO_PIN5)
#define GPIO_EBU_BC3             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT6 | GPIO_PIN6)
#define GPIO_EBU_BFCLKI          (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN6)
#define GPIO_EBU_BFCLKO_1        (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT5 | GPIO_PIN6)
#define GPIO_EBU_BFCLKO_2        (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT5 | GPIO_PIN9)
#define GPIO_EBU_BREQ            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT0 | GPIO_PIN11)
#define GPIO_EBU_CAS             (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT5 | GPIO_PIN5)
#define GPIO_EBU_CKE             (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT5 | GPIO_PIN3)
#define GPIO_EBU_CS0             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT3 | GPIO_PIN2)
#define GPIO_EBU_CS1             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT0 | GPIO_PIN9)
#define GPIO_EBU_CS2             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT5 | GPIO_PIN8)
#define GPIO_EBU_CS3             (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT5 | GPIO_PIN9)
#define GPIO_EBU_D0              (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT0 | GPIO_PIN2)
#define GPIO_EBU_D1              (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT0 | GPIO_PIN3)
#define GPIO_EBU_D2              (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT0 | GPIO_PIN4)
#define GPIO_EBU_D3              (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT0 | GPIO_PIN5)
#define GPIO_EBU_D4              (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT3 | GPIO_PIN5)
#define GPIO_EBU_D5              (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT3 | GPIO_PIN6)
#define GPIO_EBU_D6              (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_EBU_D7              (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT0 | GPIO_PIN8)
#define GPIO_EBU_D8              (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT4 | GPIO_PIN0)
#define GPIO_EBU_D9              (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT4 | GPIO_PIN1)
#define GPIO_EBU_D10             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN6)
#define GPIO_EBU_D11             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN7)
#define GPIO_EBU_D12             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN8)
#define GPIO_EBU_D13             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN9)
#define GPIO_EBU_D14             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN2)
#define GPIO_EBU_D15             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN3)
#define GPIO_EBU_D16             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN12)
#define GPIO_EBU_D17             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN13)
#define GPIO_EBU_D18             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN14)
#define GPIO_EBU_D19             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT1 | GPIO_PIN15)
#define GPIO_EBU_D20             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN0)
#define GPIO_EBU_D21             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN1)
#define GPIO_EBU_D22             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_EBU_D23             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_EBU_D24             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_EBU_D25             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_EBU_D26             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_EBU_D27             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_EBU_D28             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN10)
#define GPIO_EBU_D29             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN11)
#define GPIO_EBU_D30             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN12)
#define GPIO_EBU_D31             (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT2 | GPIO_PIN13)
#define GPIO_EBU_HLDA_1          (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT0 | GPIO_PIN12)
#define GPIO_EBU_HLDA_2          (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT0 | GPIO_PIN12)
#define GPIO_EBU_HOLD            (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT3 | GPIO_PIN4)
#define GPIO_EBU_RAS             (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT5 | GPIO_PIN4)
#define GPIO_EBU_RD              (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT3 | GPIO_PIN0)
#define GPIO_EBU_RDWR            (GPIO_OUTPUT | GPIO_PINCTRL_HW1 | GPIO_PORT3 | GPIO_PIN1)
#define GPIO_EBU_SDCLKI          (GPIO_INPUT | GPIO_PORT6 | GPIO_PIN4)
#define GPIO_EBU_SDCLKO_1        (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT5 | GPIO_PIN8)
#define GPIO_EBU_SDCLKO_2        (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT6 | GPIO_PIN4)
#define GPIO_EBU_WAIT            (GPIO_INPUT | GPIO_PINCTRL_HW1 | GPIO_PORT3 | GPIO_PIN3)
#define GPIO_ERU0_0A0            (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN1)
#define GPIO_ERU0_0A1            (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN2)
#define GPIO_ERU0_0A2            (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_ERU0_0B0            (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN0)
#define GPIO_ERU0_0B1            (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN1)
#define GPIO_ERU0_0B2            (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_ERU0_0B3            (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN0)
#define GPIO_ERU0_1A0            (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN10)
#define GPIO_ERU0_1A2            (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_ERU0_1B0            (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN9)
#define GPIO_ERU0_1B2            (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_ERU0_1B3            (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN6)
#define GPIO_ERU0_2A0            (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_ERU0_2A1            (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN8)
#define GPIO_ERU0_2A2            (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN13)
#define GPIO_ERU0_2B0            (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN4)
#define GPIO_ERU0_2B1            (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_ERU0_2B2            (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN12)
#define GPIO_ERU0_2B3            (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN4)
#define GPIO_ERU0_3A0            (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN1)
#define GPIO_ERU0_3A1            (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN6)
#define GPIO_ERU0_3A2            (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN11)
#define GPIO_ERU0_3B0            (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN0)
#define GPIO_ERU0_3B1            (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN5)
#define GPIO_ERU0_3B2            (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN6)
#define GPIO_ERU0_3B3            (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN2)
#define GPIO_ERU1_0A0            (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_ERU1_0B0            (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN1)
#define GPIO_ERU1_1A0            (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN15)
#define GPIO_ERU1_1B0            (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN7)
#define GPIO_ERU1_2A0            (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN3)
#define GPIO_ERU1_2B0            (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN2)
#define GPIO_ERU1_3A0            (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN5)
#define GPIO_ERU1_3B0            (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN3)
#define GPIO_ERU1_PDOUT0         (GPIO_OUTPUT_ALT4 | GPIO_PORT1 | GPIO_PIN3)
#define GPIO_ERU1_PDOUT1         (GPIO_OUTPUT_ALT4 | GPIO_PORT1 | GPIO_PIN2)
#define GPIO_ERU1_PDOUT2         (GPIO_OUTPUT_ALT4 | GPIO_PORT1 | GPIO_PIN1)
#define GPIO_ERU1_PDOUT3         (GPIO_OUTPUT_ALT4 | GPIO_PORT1 | GPIO_PIN0)

#define GPIO_ETH0_CLKRMIIA       (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN1)
#define GPIO_ETH0_CLKRMIIB       (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN0)
#define GPIO_ETH0_CLKRMIIC       (GPIO_INPUT | GPIO_PORT15 | GPIO_PIN8)
#define GPIO_ETH0_CLKRMIID       (GPIO_INPUT | GPIO_PORT6 | GPIO_PIN5)
#define GPIO_ETH0_CLKRXA         (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN1)
#define GPIO_ETH0_CLKRXB         (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN0)
#define GPIO_ETH0_CLKRXC         (GPIO_INPUT | GPIO_PORT15 | GPIO_PIN8)
#define GPIO_ETH0_CLKRXD         (GPIO_INPUT | GPIO_PORT6 | GPIO_PIN5)
#define GPIO_ETH0_CLKTXA         (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN10)
#define GPIO_ETH0_CLKTXB         (GPIO_INPUT | GPIO_PORT6 | GPIO_PIN6)
#define GPIO_ETH0_COLA           (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN15)
#define GPIO_ETH0_COLD           (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN5)
#define GPIO_ETH0_CRSA           (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN11)
#define GPIO_ETH0_CRSD           (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN4)
#define GPIO_ETH0_CRSDVA_1       (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_ETH0_CRSDVA_2       (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_ETH0_CRSDVB         (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN1)
#define GPIO_ETH0_CRSDVC         (GPIO_INPUT | GPIO_PORT15 | GPIO_PIN9)
#define GPIO_ETH0_CRSDVD         (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN2)
#define GPIO_ETH0_MDC_1          (GPIO_OUTPUT_ALT1 | GPIO_PORT0 | GPIO_PIN10)
#define GPIO_ETH0_MDC_2          (GPIO_OUTPUT_ALT1 | GPIO_PORT1 | GPIO_PIN10)
#define GPIO_ETH0_MDC_3          (GPIO_OUTPUT_ALT1 | GPIO_PORT2 | GPIO_PIN7)
#define GPIO_ETH0_MDIA           (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT0 | GPIO_PIN9)
#define GPIO_ETH0_MDIB           (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN0)
#define GPIO_ETH0_MDIC           (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN11)
#define GPIO_ETH0_MDO_1          (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT0 | GPIO_PIN9)
#define GPIO_ETH0_MDO_2          (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN11)
#define GPIO_ETH0_MDO_3          (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN0)
#define GPIO_ETH0_RXD0A          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_ETH0_RXD0B          (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN2)
#define GPIO_ETH0_RXD0C          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN8)
#define GPIO_ETH0_RXD0D          (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN0)
#define GPIO_ETH0_RXD1A          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_ETH0_RXD1B          (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN3)
#define GPIO_ETH0_RXD1C          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN9)
#define GPIO_ETH0_RXD1D          (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN1)
#define GPIO_ETH0_RXD2A          (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN8)
#define GPIO_ETH0_RXD2B          (GPIO_INPUT | GPIO_PORT6 | GPIO_PIN4)
#define GPIO_ETH0_RXD3A          (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN9)
#define GPIO_ETH0_RXD3B          (GPIO_INPUT | GPIO_PORT6 | GPIO_PIN3)
#define GPIO_ETH0_RXDVB          (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN1)
#define GPIO_ETH0_RXDVC          (GPIO_INPUT | GPIO_PORT15 | GPIO_PIN9)
#define GPIO_ETH0_RXDVD          (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN2)
#define GPIO_ETH0_RXERA          (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_ETH0_RXERB          (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN11)
#define GPIO_ETH0_RXERD          (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN3)
#define GPIO_ETH0_TXD0_1         (GPIO_OUTPUT_ALT1 | GPIO_PORT0 | GPIO_PIN5)
#define GPIO_ETH0_TXD0_2         (GPIO_OUTPUT_ALT1 | GPIO_PORT1 | GPIO_PIN13)
#define GPIO_ETH0_TXD0_3         (GPIO_OUTPUT_ALT1 | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_ETH0_TXD0_4         (GPIO_OUTPUT_ALT4 | GPIO_PORT2 | GPIO_PIN12)
#define GPIO_ETH0_TXD1_1         (GPIO_OUTPUT_ALT1 | GPIO_PORT0 | GPIO_PIN6)
#define GPIO_ETH0_TXD1_2         (GPIO_OUTPUT_ALT1 | GPIO_PORT1 | GPIO_PIN14)
#define GPIO_ETH0_TXD1_3         (GPIO_OUTPUT_ALT1 | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_ETH0_TXD1_4         (GPIO_OUTPUT_ALT4 | GPIO_PORT2 | GPIO_PIN13)
#define GPIO_ETH0_TXD2_1         (GPIO_OUTPUT_ALT1 | GPIO_PORT2 | GPIO_PIN12)
#define GPIO_ETH0_TXD2_2         (GPIO_OUTPUT_ALT1 | GPIO_PORT6 | GPIO_PIN0)
#define GPIO_ETH0_TXD3_1         (GPIO_OUTPUT_ALT1 | GPIO_PORT2 | GPIO_PIN13)
#define GPIO_ETH0_TXD3_2         (GPIO_OUTPUT_ALT1 | GPIO_PORT6 | GPIO_PIN1)
#define GPIO_ETH0_TXEN_1         (GPIO_OUTPUT_ALT1 | GPIO_PORT0 | GPIO_PIN4)
#define GPIO_ETH0_TXEN_2         (GPIO_OUTPUT_ALT1 | GPIO_PORT1 | GPIO_PIN12)
#define GPIO_ETH0_TXEN_3         (GPIO_OUTPUT_ALT1 | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_ETH0_TXEN_4         (GPIO_OUTPUT_ALT4 | GPIO_PORT5 | GPIO_PIN9)
#define GPIO_ETH0_TXER_1         (GPIO_OUTPUT_ALT1 | GPIO_PORT2 | GPIO_PIN11)
#define GPIO_ETH0_TXER_2         (GPIO_OUTPUT_ALT1 | GPIO_PORT6 | GPIO_PIN2)

#define GPIO_G0ORC6              (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN6)
#define GPIO_G0ORC7              (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN7)
#define GPIO_G1ORC6              (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN14)
#define GPIO_G1ORC7              (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN15)

#define GPIO_LEDTS0_COL0_1       (GPIO_OUTPUT_ALT4 | GPIO_PORT0 | GPIO_PIN9)
#define GPIO_LEDTS0_COL0_2       (GPIO_OUTPUT_ALT4 | GPIO_PORT2 | GPIO_PIN1)
#define GPIO_LEDTS0_COL1_1       (GPIO_OUTPUT_ALT4 | GPIO_PORT0 | GPIO_PIN10)
#define GPIO_LEDTS0_COL1_2       (GPIO_OUTPUT_ALT4 | GPIO_PORT2 | GPIO_PIN0)
#define GPIO_LEDTS0_COL2_1       (GPIO_OUTPUT_ALT4 | GPIO_PORT0 | GPIO_PIN0)
#define GPIO_LEDTS0_COL2_2       (GPIO_OUTPUT_ALT4 | GPIO_PORT2 | GPIO_PIN7)
#define GPIO_LEDTS0_COL3_1       (GPIO_OUTPUT_ALT4 | GPIO_PORT0 | GPIO_PIN1)
#define GPIO_LEDTS0_COL3_2       (GPIO_OUTPUT_ALT4 | GPIO_PORT2 | GPIO_PIN6)
#define GPIO_LEDTS0_COLA_1       (GPIO_OUTPUT_ALT4 | GPIO_PORT3 | GPIO_PIN2)
#define GPIO_LEDTS0_COLA_2       (GPIO_OUTPUT_ALT4 | GPIO_PORT5 | GPIO_PIN7)
#define GPIO_LEDTS0_EXTENDED0    (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_LEDTS0_EXTENDED1    (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_LEDTS0_EXTENDED2    (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_LEDTS0_EXTENDED3    (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_LEDTS0_EXTENDED4    (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_LEDTS0_EXTENDED5    (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_LEDTS0_EXTENDED6    (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN15)
#define GPIO_LEDTS0_EXTENDED7    (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT5 | GPIO_PIN10)
#define GPIO_LEDTS0_LINE0_1      (GPIO_OUTPUT_ALT4 | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_LEDTS0_LINE0_2      (GPIO_OUTPUT_ALT4 | GPIO_PORT3 | GPIO_PIN7)
#define GPIO_LEDTS0_LINE1_1      (GPIO_OUTPUT_ALT4 | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_LEDTS0_LINE1_2      (GPIO_OUTPUT_ALT4 | GPIO_PORT3 | GPIO_PIN8)
#define GPIO_LEDTS0_LINE2_1      (GPIO_OUTPUT_ALT4 | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_LEDTS0_LINE2_2      (GPIO_OUTPUT_ALT4 | GPIO_PORT3 | GPIO_PIN9)
#define GPIO_LEDTS0_LINE3_1      (GPIO_OUTPUT_ALT4 | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_LEDTS0_LINE3_2      (GPIO_OUTPUT_ALT4 | GPIO_PORT3 | GPIO_PIN10)
#define GPIO_LEDTS0_LINE4_1      (GPIO_OUTPUT_ALT4 | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_LEDTS0_LINE4_2      (GPIO_OUTPUT_ALT4 | GPIO_PORT3 | GPIO_PIN11)
#define GPIO_LEDTS0_LINE5_1      (GPIO_OUTPUT_ALT4 | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_LEDTS0_LINE5_2      (GPIO_OUTPUT_ALT4 | GPIO_PORT3 | GPIO_PIN12)
#define GPIO_LEDTS0_LINE6_1      (GPIO_OUTPUT_ALT4 | GPIO_PORT2 | GPIO_PIN15)
#define GPIO_LEDTS0_LINE6_2      (GPIO_OUTPUT_ALT4 | GPIO_PORT3 | GPIO_PIN13)
#define GPIO_LEDTS0_LINE7        (GPIO_OUTPUT_ALT4 | GPIO_PORT5 | GPIO_PIN10)
#define GPIO_LEDTS0_TSIN0A       (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_LEDTS0_TSIN1A       (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_LEDTS0_TSIN2A       (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_LEDTS0_TSIN3A       (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_LEDTS0_TSIN4A       (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_LEDTS0_TSIN5A       (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_LEDTS0_TSIN6A       (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN15)
#define GPIO_LEDTS0_TSIN7A       (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT5 | GPIO_PIN10)

#define GPIO_POSIF0_IN0A         (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN3)
#define GPIO_POSIF0_IN0B         (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN7)
#define GPIO_POSIF0_IN1A         (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN2)
#define GPIO_POSIF0_IN1B         (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN6)
#define GPIO_POSIF0_IN2A         (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN1)
#define GPIO_POSIF0_IN2B         (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN5)
#define GPIO_POSIF1_IN0A         (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_POSIF1_IN0B         (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN10)
#define GPIO_POSIF1_IN1A         (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_POSIF1_IN1B         (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN9)
#define GPIO_POSIF1_IN2A         (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_POSIF1_IN2B         (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN8)

#define GPIO_SCU_EXTCLK_1        (GPIO_OUTPUT_ALT1 | GPIO_PORT0 | GPIO_PIN8)
#define GPIO_SCU_EXTCLK_2        (GPIO_OUTPUT_ALT1 | GPIO_PORT1 | GPIO_PIN15)

#define GPIO_SDMMC_BUSPOWER      (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT3 | GPIO_PIN4)
#define GPIO_SDMMC_CLKIN         (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT3 | GPIO_PIN6)
#define GPIO_SDMMC_CLKOUT        (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT3 | GPIO_PIN6)
#define GPIO_SDMMC_CMDIN         (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT3 | GPIO_PIN5)
#define GPIO_SDMMC_CMDOUT        (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT3 | GPIO_PIN5)
#define GPIO_SDMMC_DATA0IN       (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT4 | GPIO_PIN0)
#define GPIO_SDMMC_DATA0OUT      (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT4 | GPIO_PIN0)
#define GPIO_SDMMC_DATA1IN       (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN6)
#define GPIO_SDMMC_DATA1OUT      (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN6)
#define GPIO_SDMMC_DATA2IN       (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN7)
#define GPIO_SDMMC_DATA2OUT      (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN7)
#define GPIO_SDMMC_DATA3IN       (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT4 | GPIO_PIN1)
#define GPIO_SDMMC_DATA3OUT      (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT4 | GPIO_PIN1)
#define GPIO_SDMMC_DATA4IN       (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN8)
#define GPIO_SDMMC_DATA4OUT      (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN8)
#define GPIO_SDMMC_DATA5IN       (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN9)
#define GPIO_SDMMC_DATA5OUT      (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN9)
#define GPIO_SDMMC_DATA6IN       (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN12)
#define GPIO_SDMMC_DATA6OUT      (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN12)
#define GPIO_SDMMC_DATA7IN       (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN13)
#define GPIO_SDMMC_DATA7OUT      (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN13)
#define GPIO_SDMMC_LED           (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT3 | GPIO_PIN3)
#define GPIO_SDMMC_RST           (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT0 | GPIO_PIN11)
#define GPIO_SDMMC_SDCD          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN10)
#define GPIO_SDMMC_SDWC          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN1)
#define GPIO_TRACESWO            (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN1)

#define GPIO_U0C0_DOUT0_1        (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_U0C0_DOUT0_2        (GPIO_OUTPUT_ALT1 | GPIO_PORT5 | GPIO_PIN1)
#define GPIO_U0C0_DOUT0_3        (GPIO_OUTPUT_ALT2 | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_U0C0_DOUT0_4        (GPIO_OUTPUT_ALT2 | GPIO_PORT1 | GPIO_PIN7)
#define GPIO_U0C0_DOUT1_1        (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN4)
#define GPIO_U0C0_DOUT2_2        (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN3)
#define GPIO_U0C0_DOUT3_3        (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN2)
#define GPIO_U0C0_DX0A           (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_U0C0_DX0B           (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN4)
#define GPIO_U0C0_DX0C           (GPIO_INPUT | GPIO_PORT4 | GPIO_PIN7)
#define GPIO_U0C0_DX0D           (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN0)
#define GPIO_U0C0_DX1A           (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN1)
#define GPIO_U0C0_DX1B           (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN8)
#define GPIO_U0C0_DX2A           (GPIO_INPUT | GPIO_PORT1 | GPIO_PIN0)
#define GPIO_U0C0_DX2B           (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_U0C0_HWIN0          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_U0C0_HWIN1          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN4)
#define GPIO_U0C0_HWIN2          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN3)
#define GPIO_U0C0_HWIN3          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT1 | GPIO_PIN2)
#define GPIO_U0C0_MCLKOUT        (GPIO_OUTPUT_ALT2 | GPIO_PORT1 | GPIO_PIN3)
#define GPIO_U0C0_SCLKOUT_1      (GPIO_OUTPUT_ALT2 | GPIO_PORT0 | GPIO_PIN8)
#define GPIO_U0C0_SCLKOUT_2      (GPIO_OUTPUT_ALT2 | GPIO_PORT1 | GPIO_PIN1)
#define GPIO_U0C0_SCLKOUT_3      (GPIO_OUTPUT_ALT2 | GPIO_PORT1 | GPIO_PIN10)
#define GPIO_U0C0_SCLKOUT_4      (GPIO_OUTPUT_ALT2 | GPIO_PORT1 | GPIO_PIN6)
#define GPIO_U0C0_SELO0_1        (GPIO_OUTPUT_ALT2 | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_U0C0_SELO0_2        (GPIO_OUTPUT_ALT2 | GPIO_PORT1 | GPIO_PIN0)
#define GPIO_U0C0_SELO0_3        (GPIO_OUTPUT_ALT2 | GPIO_PORT1 | GPIO_PIN11)
#define GPIO_U0C0_SELO1          (GPIO_OUTPUT_ALT2 | GPIO_PORT1 | GPIO_PIN8)
#define GPIO_U0C0_SELO2          (GPIO_OUTPUT_ALT2 | GPIO_PORT4 | GPIO_PIN6)
#define GPIO_U0C0_SELO3          (GPIO_OUTPUT_ALT2 | GPIO_PORT4 | GPIO_PIN5)
#define GPIO_U0C0_SELO4          (GPIO_OUTPUT_ALT2 | GPIO_PORT4 | GPIO_PIN4)
#define GPIO_U0C0_SELO5          (GPIO_OUTPUT_ALT2 | GPIO_PORT4 | GPIO_PIN3)
#define GPIO_U0C1_DOUT0_1        (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT3 | GPIO_PIN13)
#define GPIO_U0C1_DOUT0_2        (GPIO_OUTPUT_ALT2 | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_U0C1_DOUT0_3        (GPIO_OUTPUT_ALT2 | GPIO_PORT3 | GPIO_PIN13)
#define GPIO_U0C1_DOUT0_4        (GPIO_OUTPUT_ALT2 | GPIO_PORT6 | GPIO_PIN4)
#define GPIO_U0C1_DOUT0_5        (GPIO_OUTPUT_ALT4 | GPIO_PORT3 | GPIO_PIN5)
#define GPIO_U0C1_DOUT1          (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT3 | GPIO_PIN12)
#define GPIO_U0C1_DOUT2          (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT3 | GPIO_PIN11)
#define GPIO_U0C1_DOUT3          (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT3 | GPIO_PIN10)
#define GPIO_U0C1_DX0A           (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_U0C1_DX0B           (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_U0C1_DX0C           (GPIO_INPUT | GPIO_PORT6 | GPIO_PIN3)
#define GPIO_U0C1_DX0D           (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN13)
#define GPIO_U0C1_DX0E           (GPIO_INPUT | GPIO_PORT4 | GPIO_PIN0)
#define GPIO_U0C1_DX1A           (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_U0C1_DX1B           (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN0)
#define GPIO_U0C1_DX1C           (GPIO_INPUT | GPIO_PORT6 | GPIO_PIN2)
#define GPIO_U0C1_DX2A           (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_U0C1_DX2B           (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN1)
#define GPIO_U0C1_DX2C           (GPIO_INPUT | GPIO_PORT6 | GPIO_PIN1)
#define GPIO_U0C1_HWIN0          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT3 | GPIO_PIN13)
#define GPIO_U0C1_HWIN1          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT3 | GPIO_PIN12)
#define GPIO_U0C1_HWIN2          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT3 | GPIO_PIN11)
#define GPIO_U0C1_HWIN3          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT3 | GPIO_PIN10)
#define GPIO_U0C1_MCLKOUT        (GPIO_OUTPUT_ALT2 | GPIO_PORT6 | GPIO_PIN5)
#define GPIO_U0C1_SCLKOUT_1      (GPIO_OUTPUT_ALT2 | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_U0C1_SCLKOUT_2      (GPIO_OUTPUT_ALT2 | GPIO_PORT3 | GPIO_PIN0)
#define GPIO_U0C1_SCLKOUT_3      (GPIO_OUTPUT_ALT2 | GPIO_PORT6 | GPIO_PIN2)
#define GPIO_U0C1_SCLKOUT_4      (GPIO_OUTPUT_ALT4 | GPIO_PORT3 | GPIO_PIN6)
#define GPIO_U0C1_SELO0_1        (GPIO_OUTPUT_ALT2 | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_U0C1_SELO0_2        (GPIO_OUTPUT_ALT2 | GPIO_PORT3 | GPIO_PIN1)
#define GPIO_U0C1_SELO0_3        (GPIO_OUTPUT_ALT2 | GPIO_PORT6 | GPIO_PIN1)
#define GPIO_U0C1_SELO0_4        (GPIO_OUTPUT_ALT4 | GPIO_PORT4 | GPIO_PIN1)
#define GPIO_U0C1_SELO1_1        (GPIO_OUTPUT_ALT2 | GPIO_PORT3 | GPIO_PIN12)
#define GPIO_U0C1_SELO1_2        (GPIO_OUTPUT_ALT2 | GPIO_PORT6 | GPIO_PIN0)
#define GPIO_U0C1_SELO2_1        (GPIO_OUTPUT_ALT2 | GPIO_PORT1 | GPIO_PIN14)
#define GPIO_U0C1_SELO2_2        (GPIO_OUTPUT_ALT2 | GPIO_PORT3 | GPIO_PIN11)
#define GPIO_U0C1_SELO3_1        (GPIO_OUTPUT_ALT2 | GPIO_PORT1 | GPIO_PIN13)
#define GPIO_U0C1_SELO3_2        (GPIO_OUTPUT_ALT2 | GPIO_PORT3 | GPIO_PIN8)
#define GPIO_U1C0_DOUT0_1        (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT0 | GPIO_PIN5)
#define GPIO_U1C0_DOUT0_2        (GPIO_OUTPUT_ALT2 | GPIO_PORT0 | GPIO_PIN5)
#define GPIO_U1C0_DOUT0_3        (GPIO_OUTPUT_ALT2 | GPIO_PORT2 | GPIO_PIN14)
#define GPIO_U1C0_DOUT1          (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT0 | GPIO_PIN4)
#define GPIO_U1C0_DOUT2          (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT0 | GPIO_PIN3)
#define GPIO_U1C0_DOUT3          (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT0 | GPIO_PIN2)
#define GPIO_U1C0_DX0A           (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN4)
#define GPIO_U1C0_DX0B           (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN5)
#define GPIO_U1C0_DX0C           (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN15)
#define GPIO_U1C0_DX0D           (GPIO_INPUT | GPIO_PORT2 | GPIO_PIN14)
#define GPIO_U1C0_DX1A           (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN11)
#define GPIO_U1C0_DX1B           (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN8)
#define GPIO_U1C0_DX2A           (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN6)
#define GPIO_U1C0_DX2B           (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN9)
#define GPIO_U1C0_HWIN0          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT0 | GPIO_PIN5)
#define GPIO_U1C0_HWIN1          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT0 | GPIO_PIN4)
#define GPIO_U1C0_HWIN2          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT0 | GPIO_PIN3)
#define GPIO_U1C0_HWIN3          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT0 | GPIO_PIN2)
#define GPIO_U1C0_MCLKOUT        (GPIO_OUTPUT_ALT2 | GPIO_PORT5 | GPIO_PIN10)
#define GPIO_U1C0_SCLKOUT_1      (GPIO_OUTPUT_ALT2 | GPIO_PORT0 | GPIO_PIN11)
#define GPIO_U1C0_SCLKOUT_2      (GPIO_OUTPUT_ALT2 | GPIO_PORT5 | GPIO_PIN8)
#define GPIO_U1C0_SELO0_1        (GPIO_OUTPUT_ALT2 | GPIO_PORT0 | GPIO_PIN6)
#define GPIO_U1C0_SELO0_2        (GPIO_OUTPUT_ALT2 | GPIO_PORT5 | GPIO_PIN9)
#define GPIO_U1C0_SELO1_1        (GPIO_OUTPUT_ALT2 | GPIO_PORT0 | GPIO_PIN14)
#define GPIO_U1C0_SELO1_2        (GPIO_OUTPUT_ALT2 | GPIO_PORT5 | GPIO_PIN11)
#define GPIO_U1C0_SELO2          (GPIO_OUTPUT_ALT2 | GPIO_PORT0 | GPIO_PIN15)
#define GPIO_U1C0_SELO3          (GPIO_OUTPUT_ALT2 | GPIO_PORT3 | GPIO_PIN14)
#define GPIO_U1C1_DOUT0_1        (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT3 | GPIO_PIN15)
#define GPIO_U1C1_DOUT0_2        (GPIO_OUTPUT_ALT2 | GPIO_PORT0 | GPIO_PIN1)
#define GPIO_U1C1_DOUT0_3        (GPIO_OUTPUT_ALT2 | GPIO_PORT3 | GPIO_PIN15)
#define GPIO_U1C1_DOUT0_4        (GPIO_OUTPUT_ALT2 | GPIO_PORT4 | GPIO_PIN2)
#define GPIO_U1C1_DOUT1          (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT3 | GPIO_PIN14)
#define GPIO_U1C1_DOUT2          (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT0 | GPIO_PIN15)
#define GPIO_U1C1_DOUT3          (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT0 | GPIO_PIN14)
#define GPIO_U1C1_DX0A           (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN15)
#define GPIO_U1C1_DX0B           (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN14)
#define GPIO_U1C1_DX0C           (GPIO_INPUT | GPIO_PORT4 | GPIO_PIN2)
#define GPIO_U1C1_DX0D           (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN0)
#define GPIO_U1C1_DX1A           (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN10)
#define GPIO_U1C1_DX1B           (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN13)
#define GPIO_U1C1_DX1C           (GPIO_INPUT | GPIO_PORT4 | GPIO_PIN0)
#define GPIO_U1C1_DX2A           (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN9)
#define GPIO_U1C1_DX2B           (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN12)
#define GPIO_U1C1_HWIN0          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT3 | GPIO_PIN15)
#define GPIO_U1C1_HWIN1          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT3 | GPIO_PIN14)
#define GPIO_U1C1_HWIN2          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT0 | GPIO_PIN15)
#define GPIO_U1C1_HWIN3          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT0 | GPIO_PIN14)
#define GPIO_U1C1_SCLKOUT_1      (GPIO_OUTPUT_ALT2 | GPIO_PORT0 | GPIO_PIN10)
#define GPIO_U1C1_SCLKOUT_2      (GPIO_OUTPUT_ALT2 | GPIO_PORT0 | GPIO_PIN13)
#define GPIO_U1C1_SELO0_1        (GPIO_OUTPUT_ALT2 | GPIO_PORT0 | GPIO_PIN12)
#define GPIO_U1C1_SELO0_2        (GPIO_OUTPUT_ALT2 | GPIO_PORT0 | GPIO_PIN9)
#define GPIO_U1C1_SELO1_1        (GPIO_OUTPUT_ALT2 | GPIO_PORT0 | GPIO_PIN2)
#define GPIO_U1C1_SELO1_2        (GPIO_OUTPUT_ALT2 | GPIO_PORT3 | GPIO_PIN3)
#define GPIO_U1C1_SELO2          (GPIO_OUTPUT_ALT2 | GPIO_PORT3 | GPIO_PIN4)
#define GPIO_U1C1_SELO3          (GPIO_OUTPUT_ALT2 | GPIO_PORT3 | GPIO_PIN5)
#define GPIO_U1C1_SELO4          (GPIO_OUTPUT_ALT2 | GPIO_PORT3 | GPIO_PIN6)
#define GPIO_U2C0_DOUT0_1        (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT5 | GPIO_PIN0)
#define GPIO_U2C0_DOUT0_2        (GPIO_OUTPUT_ALT1 | GPIO_PORT3 | GPIO_PIN8)
#define GPIO_U2C0_DOUT0_3        (GPIO_OUTPUT_ALT1 | GPIO_PORT5 | GPIO_PIN0)
#define GPIO_U2C0_DOUT1          (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT5 | GPIO_PIN1)
#define GPIO_U2C0_DOUT2          (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT5 | GPIO_PIN7)
#define GPIO_U2C0_DOUT3          (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN6)
#define GPIO_U2C0_DX0A           (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN1)
#define GPIO_U2C0_DX0B           (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN0)
#define GPIO_U2C0_DX0C           (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN7)
#define GPIO_U2C0_DX1A           (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN2)
#define GPIO_U2C0_DX2A           (GPIO_INPUT | GPIO_PORT5 | GPIO_PIN3)
#define GPIO_U2C0_HWIN0          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT5 | GPIO_PIN0)
#define GPIO_U2C0_HWIN1          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT5 | GPIO_PIN1)
#define GPIO_U2C0_HWIN2          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT5 | GPIO_PIN7)
#define GPIO_U2C0_HWIN3          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT2 | GPIO_PIN6)
#define GPIO_U2C0_SCLKOUT_1      (GPIO_OUTPUT_ALT1 | GPIO_PORT3 | GPIO_PIN9)
#define GPIO_U2C0_SCLKOUT_2      (GPIO_OUTPUT_ALT1 | GPIO_PORT5 | GPIO_PIN2)
#define GPIO_U2C0_SELO0_1        (GPIO_OUTPUT_ALT1 | GPIO_PORT3 | GPIO_PIN10)
#define GPIO_U2C0_SELO0_2        (GPIO_OUTPUT_ALT1 | GPIO_PORT5 | GPIO_PIN3)
#define GPIO_U2C0_SELO1          (GPIO_OUTPUT_ALT1 | GPIO_PORT5 | GPIO_PIN4)
#define GPIO_U2C0_SELO2          (GPIO_OUTPUT_ALT1 | GPIO_PORT5 | GPIO_PIN5)
#define GPIO_U2C0_SELO3          (GPIO_OUTPUT_ALT1 | GPIO_PORT5 | GPIO_PIN6)
#define GPIO_U2C0_SELO4          (GPIO_OUTPUT_ALT1 | GPIO_PORT2 | GPIO_PIN6)
#define GPIO_U2C1_DOUT0_1        (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT4 | GPIO_PIN7)
#define GPIO_U2C1_DOUT0_2        (GPIO_OUTPUT_ALT1 | GPIO_PORT3 | GPIO_PIN11)
#define GPIO_U2C1_DOUT0_3        (GPIO_OUTPUT_ALT1 | GPIO_PORT3 | GPIO_PIN5)
#define GPIO_U2C1_DOUT1          (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT4 | GPIO_PIN6)
#define GPIO_U2C1_DOUT2          (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT4 | GPIO_PIN5)
#define GPIO_U2C1_DOUT3          (GPIO_OUTPUT | GPIO_PINCTRL_HW0 | GPIO_PORT4 | GPIO_PIN4)
#define GPIO_U2C1_DX0A           (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN5)
#define GPIO_U2C1_DX0B           (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN4)
#define GPIO_U2C1_DX0C           (GPIO_INPUT | GPIO_PORT4 | GPIO_PIN0)
#define GPIO_U2C1_DX0D           (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN12)
#define GPIO_U2C1_DX1A           (GPIO_INPUT | GPIO_PORT4 | GPIO_PIN2)
#define GPIO_U2C1_DX1B           (GPIO_INPUT | GPIO_PORT3 | GPIO_PIN6)
#define GPIO_U2C1_DX2A           (GPIO_INPUT | GPIO_PORT4 | GPIO_PIN1)
#define GPIO_U2C1_DX2B           (GPIO_INPUT | GPIO_PORT4 | GPIO_PIN1)
#define GPIO_U2C1_HWIN0          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT4 | GPIO_PIN7)
#define GPIO_U2C1_HWIN1          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT4 | GPIO_PIN6)
#define GPIO_U2C1_HWIN2          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT4 | GPIO_PIN5)
#define GPIO_U2C1_HWIN3          (GPIO_INPUT | GPIO_PINCTRL_HW0 | GPIO_PORT4 | GPIO_PIN4)
#define GPIO_U2C1_MCLKOUT        (GPIO_OUTPUT_ALT1 | GPIO_PORT3 | GPIO_PIN4)
#define GPIO_U2C1_SCLKOUT_1      (GPIO_OUTPUT_ALT1 | GPIO_PORT3 | GPIO_PIN13)
#define GPIO_U2C1_SCLKOUT_2      (GPIO_OUTPUT_ALT1 | GPIO_PORT3 | GPIO_PIN6)
#define GPIO_U2C1_SCLKOUT_3      (GPIO_OUTPUT_ALT4 | GPIO_PORT4 | GPIO_PIN2)
#define GPIO_U2C1_SELO0_1        (GPIO_OUTPUT_ALT1 | GPIO_PORT3 | GPIO_PIN0)
#define GPIO_U2C1_SELO0_2        (GPIO_OUTPUT_ALT1 | GPIO_PORT4 | GPIO_PIN1)
#define GPIO_U2C1_SELO1          (GPIO_OUTPUT_ALT1 | GPIO_PORT4 | GPIO_PIN2)
#define GPIO_U2C1_SELO2          (GPIO_OUTPUT_ALT1 | GPIO_PORT4 | GPIO_PIN3)

#define GPIO_USB_DRIVEVBUS_1     (GPIO_OUTPUT_ALT1 | GPIO_PORT0 | GPIO_PIN1)
#define GPIO_USB_DRIVEVBUS_2     (GPIO_OUTPUT_ALT1 | GPIO_PORT3 | GPIO_PIN2)
#define GPIO_USB_ID              (GPIO_INPUT | GPIO_PORT0 | GPIO_PIN9)

#define GPIO_VADC_EMUX00         (GPIO_OUTPUT_ALT1 | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_VADC_EMUX01         (GPIO_OUTPUT_ALT1 | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_VADC_EMUX02         (GPIO_OUTPUT_ALT1 | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_VADC_EMUX10         (GPIO_OUTPUT_ALT1 | GPIO_PORT2 | GPIO_PIN10)
#define GPIO_VADC_EMUX11         (GPIO_OUTPUT_ALT1 | GPIO_PORT2 | GPIO_PIN14)
#define GPIO_VADC_EMUX12         (GPIO_OUTPUT_ALT1 | GPIO_PORT2 | GPIO_PIN15)
#define GPIO_VADC_G0CH0          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN0)
#define GPIO_VADC_G0CH1          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN1)
#define GPIO_VADC_G0CH2          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN2)
#define GPIO_VADC_G0CH3          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN3)
#define GPIO_VADC_G0CH4          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN4)
#define GPIO_VADC_G0CH5          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN5)
#define GPIO_VADC_G0CH6          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN6)
#define GPIO_VADC_G0CH7          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN7)
#define GPIO_VADC_G1CH0          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN8)
#define GPIO_VADC_G1CH1          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN9)
#define GPIO_VADC_G1CH2          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN2)
#define GPIO_VADC_G1CH3          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN3)
#define GPIO_VADC_G1CH4          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN12)
#define GPIO_VADC_G1CH5          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN13)
#define GPIO_VADC_G1CH6          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN14)
#define GPIO_VADC_G1CH7          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN15)
#define GPIO_VADC_G2CH0          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN4)
#define GPIO_VADC_G2CH1          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN5)
#define GPIO_VADC_G2CH2          (GPIO_INPUT | GPIO_PORT15 | GPIO_PIN2)
#define GPIO_VADC_G2CH3          (GPIO_INPUT | GPIO_PORT15 | GPIO_PIN3)
#define GPIO_VADC_G2CH4          (GPIO_INPUT | GPIO_PORT15 | GPIO_PIN4)
#define GPIO_VADC_G2CH5          (GPIO_INPUT | GPIO_PORT15 | GPIO_PIN5)
#define GPIO_VADC_G2CH6          (GPIO_INPUT | GPIO_PORT15 | GPIO_PIN6)
#define GPIO_VADC_G2CH7          (GPIO_INPUT | GPIO_PORT15 | GPIO_PIN7)
#define GPIO_VADC_G3CH0          (GPIO_INPUT | GPIO_PORT15 | GPIO_PIN8)
#define GPIO_VADC_G3CH1          (GPIO_INPUT | GPIO_PORT15 | GPIO_PIN9)
#define GPIO_VADC_G3CH2          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN8)
#define GPIO_VADC_G3CH3          (GPIO_INPUT | GPIO_PORT14 | GPIO_PIN9)
#define GPIO_VADC_G3CH4          (GPIO_INPUT | GPIO_PORT15 | GPIO_PIN12)
#define GPIO_VADC_G3CH5          (GPIO_INPUT | GPIO_PORT15 | GPIO_PIN13)
#define GPIO_VADC_G3CH6          (GPIO_INPUT | GPIO_PORT15 | GPIO_PIN14)
#define GPIO_VADC_G3CH7          (GPIO_INPUT | GPIO_PORT15 | GPIO_PIN15)

#define GPIO_WWDT_SERVICEOUT_1   (GPIO_OUTPUT_ALT1 | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_WWDT_SERVICEOUT_2   (GPIO_OUTPUT_ALT1 | GPIO_PORT1 | GPIO_PIN4)

#endif /* __ARCH_ARM_SRC_XMC4_HARDWARE_XMC4_PINMXU_H */
