#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

comment "Microchip SAMD5x/E5x Configuration Options"

choice
	prompt "Microchip SAMD5x/E5x Chip Selection"
	default ARCH_CHIP_SAMD51P19 if ARCH_CHIP_SAMD5X
	default ARCH_CHIP_SAME54P20 if ARCH_CHIP_SAME5X
	depends on ARCH_CHIP_SAMD5X || ARCH_CHIP_SAME5X

config ARCH_CHIP_SAMD51P20
	bool "SAMD51P20"
	depends on ARCH_CHIP_SAMD5X
	select ARCH_FAMILY_SAMD51
	select SAMD5E5_HAVE_SDHC2
	select SAMD5E5_HAVE_SERCOM6
	select SAMD5E5_HAVE_SERCOM7
	select SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC5
	select SAMD5E5_HAVE_TC6
	select SAMD5E5_HAVE_TC7
	---help---
		Flash 1024KB SRAM 256KB

config ARCH_CHIP_SAMD51P19
	bool "SAMD51P19"
	depends on ARCH_CHIP_SAMD5X
	select <PERSON><PERSON>_FAMILY_<PERSON>MD51
	select SAMD5E5_HAVE_SDHC2
	select SAMD5E5_HAVE_SERCOM6
	select SAMD5E5_HAVE_SERCOM7
	select SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC5
	select SAMD5E5_HAVE_TC6
	select SAMD5E5_HAVE_TC7
	---help---
		Flash 512KB SRAM 192KB

config ARCH_CHIP_SAMD51N20
	bool "SAMD51N20"
	depends on ARCH_CHIP_SAMD5X
	select ARCH_FAMILY_SAMD51
	select SAMD5E5_HAVE_SDHC2
	select SAMD5E5_HAVE_SERCOM6
	select SAMD5E5_HAVE_SERCOM7
	select SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC5
	select SAMD5E5_HAVE_TC6
	select SAMD5E5_HAVE_TC7
	---help---
		Flash 1024KB SRAM 256KB

config ARCH_CHIP_SAMD51N19
	bool "SAMD51N19"
	depends on ARCH_CHIP_SAMD5X
	select ARCH_FAMILY_SAMD51
	select SAMD5E5_HAVE_SDHC2
	select SAMD5E5_HAVE_SERCOM6
	select SAMD5E5_HAVE_SERCOM7
	select SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC5
	select SAMD5E5_HAVE_TC6
	select SAMD5E5_HAVE_TC7
	---help---
		Flash 512KB SRAM 192KB

config ARCH_CHIP_SAMD51J20
	bool "SAMD51J20"
	depends on ARCH_CHIP_SAMD5X
	select ARCH_FAMILY_SAMD51
	select SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC5
	---help---
		Flash 1024KB SRAM 256KB

config ARCH_CHIP_SAMD51J19
	bool "SAMD51J19"
	depends on ARCH_CHIP_SAMD5X
	select ARCH_FAMILY_SAMD51
	select SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC5
	---help---
		Flash 512KB SRAM 192KB

config ARCH_CHIP_SAMD51J18
	bool "SAMD51J18"
	depends on ARCH_CHIP_SAMD5X
	select ARCH_FAMILY_SAMD51
	select SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC5
	---help---
		Flash 256KB SRAM 128KB

config ARCH_CHIP_SAMD51G19
	bool "SAMD51G19"
	depends on ARCH_CHIP_SAMD5X
	select ARCH_FAMILY_SAMD51
	---help---
		Flash 512KB SRAM 192KB

config ARCH_CHIP_SAMD51G18
	bool "SAMD51G18"
	depends on ARCH_CHIP_SAMD5X
	select ARCH_FAMILY_SAMD51
	---help---
		Flash 256KB SRAM 128KB

config ARCH_CHIP_SAME51N20
	bool "SAME51N20"
	depends on ARCH_CHIP_SAME5X
	select ARCH_FAMILY_SAME51
	select SAMD5E5_HAVE_CANFD
	select SAMD5E5_HAVE_SERCOM6
	select SAMD5E5_HAVE_SERCOM7
	select SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC5
	select SAMD5E5_HAVE_TC6
	select SAMD5E5_HAVE_TC7
	---help---
		Flash 1024KB SRAM 256KB

config ARCH_CHIP_SAME51N19
	bool "SAME51N19"
	depends on ARCH_CHIP_SAME5X
	select ARCH_FAMILY_SAME51
	select SAMD5E5_HAVE_CANFD
	select SAMD5E5_HAVE_SERCOM6
	select SAMD5E5_HAVE_SERCOM7
	select SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC5
	select SAMD5E5_HAVE_TC6
	select SAMD5E5_HAVE_TC7
	---help---
		Flash 512KB SRAM 192KB

config ARCH_CHIP_SAME51J20
	bool "SAME51J20"
	depends on ARCH_CHIP_SAME5X
	select ARCH_FAMILY_SAME51
	select SAMD5E5_HAVE_CANFD
	select SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC5
	---help---
		Flash 1024KB SRAM 256KB

config ARCH_CHIP_SAME51J19
	bool "SAME51J19"
	depends on ARCH_CHIP_SAME5X
	select ARCH_FAMILY_SAME51
	---help---
		Flash 512KB SRAM 192KB

config ARCH_CHIP_SAME51J18
	bool "SAME51J18"
	depends on ARCH_CHIP_SAME5X
	select ARCH_FAMILY_SAME51
	---help---
		Flash 256KB SRAM 128KB

config ARCH_CHIP_SAME53N20
	bool "SAME53N20"
	depends on ARCH_CHIP_SAME5X
	select ARCH_FAMILY_SAME53
	select SAMD5E5_HAVE_ETHERNET
	select SAMD5E5_HAVE_SDHC2
	select SAMD5E5_HAVE_SERCOM6
	select SAMD5E5_HAVE_SERCOM7
	select SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC5
	select SAMD5E5_HAVE_TC6
	select SAMD5E5_HAVE_TC7
	---help---
		Flash 1024KB SRAM 256KB

config ARCH_CHIP_SAME53N19
	bool "SAME53N19"
	depends on ARCH_CHIP_SAME5X
	select ARCH_FAMILY_SAME53
	select SAMD5E5_HAVE_ETHERNET
	select SAMD5E5_HAVE_SDHC2
	select SAMD5E5_HAVE_SERCOM6
	select SAMD5E5_HAVE_SERCOM7
	select SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC5
	select SAMD5E5_HAVE_TC6
	select SAMD5E5_HAVE_TC7
	---help---
		Flash 512KB SRAM 192KB

config ARCH_CHIP_SAME53J20
	bool "SAME53J20"
	depends on ARCH_CHIP_SAME5X
	select ARCH_FAMILY_SAME53
	select SAMD5E5_HAVE_ETHERNET
	select SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC5
	---help---
		Flash 1024KB SRAM 256KB

config ARCH_CHIP_SAME53J19
	bool "SAME53J19"
	depends on ARCH_CHIP_SAME5X
	select ARCH_FAMILY_SAME53
	select SAMD5E5_HAVE_ETHERNET
	select SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC5
	---help---
		Flash 512KB SRAM 192KB

config ARCH_CHIP_SAME53J18
	bool "SAME53J18"
	depends on ARCH_CHIP_SAME5X
	select ARCH_FAMILY_SAME53
	select SAMD5E5_HAVE_ETHERNET
	select SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC5
	---help---
		Flash 256KB SRAM 128KB

config ARCH_CHIP_SAME54P20
	bool "SAME54P20"
	depends on ARCH_CHIP_SAME5X
	select ARCH_FAMILY_SAME54
	select SAMD5E5_HAVE_ETHERNET
	select SAMD5E5_HAVE_CANFD
	select SAMD5E5_HAVE_SDHC2
	select SAMD5E5_HAVE_SERCOM6
	select SAMD5E5_HAVE_SERCOM7
	select SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC5
	select SAMD5E5_HAVE_TC6
	select SAMD5E5_HAVE_TC7
	---help---
		Flash 1024KB SRAM 256KB

config ARCH_CHIP_SAME54P19
	bool "SAME54P19"
	select ARCH_FAMILY_SAME54
	select SAMD5E5_HAVE_ETHERNET
	select SAMD5E5_HAVE_CANFD
	select SAMD5E5_HAVE_SDHC2
	select SAMD5E5_HAVE_SERCOM6
	select SAMD5E5_HAVE_SERCOM7
	select SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC5
	select SAMD5E5_HAVE_TC6
	select SAMD5E5_HAVE_TC7
	---help---
		Flash 512KB SRAM 192KB

config ARCH_CHIP_SAME54N20
	bool "SAME54N20"
	select ARCH_FAMILY_SAME54
	select SAMD5E5_HAVE_ETHERNET
	select SAMD5E5_HAVE_CANFD
	select SAMD5E5_HAVE_SDHC2
	select SAMD5E5_HAVE_SERCOM6
	select SAMD5E5_HAVE_SERCOM7
	select SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC5
	select SAMD5E5_HAVE_TC6
	select SAMD5E5_HAVE_TC7
	---help---
		Flash 1024KB SRAM 256KB

config ARCH_CHIP_SAME54N19
	bool "SAME54N19"
	select ARCH_FAMILY_SAME54
	select SAMD5E5_HAVE_ETHERNET
	select SAMD5E5_HAVE_CANFD
	select SAMD5E5_HAVE_SDHC2
	select SAMD5E5_HAVE_SERCOM6
	select SAMD5E5_HAVE_SERCOM7
	select SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC5
	select SAMD5E5_HAVE_TC6
	select SAMD5E5_HAVE_TC7
	---help---
		Flash 512KB SRAM 192KB

endchoice # Microchip SAMD5x/E5x Chip Selection

config ARCH_FAMILY_SAMD5X
	bool
	default n

config ARCH_FAMILY_SAMD51
	bool
	default n

config ARCH_FAMILY_SAME5X
	bool
	default n

config ARCH_FAMILY_SAME51
	bool
	default n

menu "SAMD5x/E5x Peripheral Support"

config SAMD5E5_HAVE_CANFD
	bool
	default n

config SAMD5E5_HAVE_ETHERNET
	bool
	default n

config SAMD5E5_HAVE_SDHC2
	bool
	default n

config SAMD5E5_HAVE_SERCOM6
	bool
	default n

config SAMD5E5_HAVE_SERCOM7
	bool
	default n

config SAMD5E5_HAVE_TC
	bool
	default n

config SAMD5E5_HAVE_TC4
	bool
	default n

config SAMD5E5_HAVE_TC5
	bool
	default n

config SAMD5E5_HAVE_TC6
	bool
	default n

config SAMD5E5_HAVE_TC7
	bool
	default n

config SAMD5E5_SERCOM
	bool
	default n

config SAMD5E5_TC
	bool
	default y

config SAMD5E5_HAVE_TC4
	bool
	default n

config SAMD5E5_HAVE_TC5
	bool
	default n

config SAMD5E5_HAVE_TC6
	bool
	default n

config SAMD5E5_AC
	bool "Analog Comparator"
	default n

config SAMD5E5_ADC
	bool "Analog-to-Digital Converter"
	default n

config SAMD5E5_CMCC
	bool "Cortex M Cache Controller (CMCC)"
	default n
config SAMD5E5_DAC
	bool "Digital-to-Analog Converter"
	default n

config SAMD5E5_DMAC
	bool "DMA Controller"
	default n
	select ARCH_DMA

config SAMD5E5_GMAC
	bool "Ethernet"
	default n
	depends on SAMD5E5_HAVE_ETHERNET
	select NETDEVICES
	select ARCH_HAVE_PHY

config SAMD5E5_EVSYS
	bool "Event System"
	default n

config SAMD5E5_NVMCTRL
	bool "Non-Volatile Memory Controller"
	default n

config SAMD5E5_PTC
	bool "Peripheral Touch Controller"
	default n

config SAMD5E5_RTC
	bool "Real Time Counter"
	default n

config SAMD5E5_SERCOM0
	bool "Serial Communication Interface 0"
	default n
	select SAMD5E5_SERCOM

config SAMD5E5_SERCOM1
	bool "Serial Communication Interface 1"
	default n
	select SAMD5E5_SERCOM

config SAMD5E5_SERCOM2
	bool "Serial Communication Interface 2"
	default n
	select SAMD5E5_SERCOM

config SAMD5E5_SERCOM3
	bool "Serial Communication Interface 3"
	default n
	select SAMD5E5_SERCOM

config SAMD5E5_SERCOM4
	bool "Serial Communication Interface 4"
	default n
	select SAMD5E5_SERCOM

config SAMD5E5_SERCOM5
	bool "Serial Communication Interface 5"
	default n
	select SAMD5E5_SERCOM

config SAMD5E5_SERCOM6
	bool "Serial Communication Interface 6"
	default n
	select SAMD5E5_SERCOM
	depends on SAMD5E5_HAVE_SERCOM6

config SAMD5E5_SERCOM7
	bool "Serial Communication Interface 7"
	default n
	select SAMD5E5_SERCOM
	depends on SAMD5E5_HAVE_SERCOM7

config SAMD5E5_TC0
	bool "Timer/Counter 0"
	default n
	depends on SAMD5E5_TC
	select SAMD5E5_HAVE_TC

config SAMD5E5_TC1
	bool "Timer/Counter 1"
	default n
	depends on SAMD5E5_TC
	select SAMD5E5_HAVE_TC

config SAMD5E5_TC2
	bool "Timer/Counter 2"
	default n
	depends on SAMD5E5_TC
	select SAMD5E5_HAVE_TC

config SAMD5E5_TC3
	bool "Timer/Counter 3"
	default n
	depends on SAMD5E5_TC
	select SAMD5E5_HAVE_TC

config SAMD5E5_TC4
	bool "Timer/Counter 4"
	default n
	depends on SAMD5E5_HAVE_TC4
	select SAMD5E5_HAVE_TC

config SAMD5E5_TC5
	bool "Timer/Counter 5"
	default n
	depends on SAMD5E5_HAVE_TC5
	select SAMD5E5_HAVE_TC

config SAMD5E5_TC6
	bool "Timer/Counter 6"
	default n
	depends on SAMD5E5_HAVE_TC6
	select SAMD5E5_HAVE_TC

config SAMD5E5_TC7
	bool "Timer/Counter 7"
	default n
	depends on SAMD5E5_HAVE_TC7
	select SAMD5E5_HAVE_TC

config SAMD5E5_ONESHOT
	bool "TC one-shot wrapper"
	default n if !SCHED_TICKLESS
	default y if SCHED_TICKLESS
	---help---
		Enable a wrapper around the low level timer/counter functions to
		support one-shot timer.

config SAMD5E5_FREERUN
	bool "TC free-running wrapper"
	default n if !SCHED_TICKLESS
	default y if SCHED_TICKLESS
	---help---
		Enable a wrapper around the low level timer/counter functions to
		support a free-running timer.

if SCHED_TICKLESS

config SAMD5E5_TICKLESS_ONESHOT
	int "Tickless one-shot timer channel"
	default 2
	range 0 7
	---help---
		If the Tickless OS feature is enabled, the one clock must be
		assigned to provided the one-shot timer needed by the OS.
		NOTE: Use even timers (0, 2 or 4) because timers are program
		in 32-bit mode (1, 3 and 5 are slaves).

config SAMD5E5_TICKLESS_FREERUN
	int "Tickless free-running timer channel"
	default 4
	range 0 7
	---help---
		If the Tickless OS feature is enabled, the one clock must be
		assigned to provided the free-running timer needed by the OS.
		NOTE: Use even timers (0, 2 or 4) because timers are program
		in 32-bit mode (1, 3 and 5 are slaves).

endif

config SAMD5E5_USB
	bool "USB"
	default n
	select USBHOST_HAVE_ASYNCH
	---help---
		Build support for the SAMD51 USB full speed USB host.

config SAMD5E5_EIC
	bool "External Interrupt Controller"
	default n

config SAMD5E5_WDT
	bool "Watchdog Timer"
	default n

menuconfig SAMD5E5_PROGMEM
	bool "FLASH program memory"
	default n
	select ARCH_HAVE_PROGMEM
	---help---
		Enable support FLASH interfaces as defined in include/nuttx/progmem.h

if SAMD5E5_PROGMEM

config SAMD5E5_PROGMEM_NSECTORS
	int "Number of 32Kbytes sectors"
	default 2
	range 1 32
	---help---
		This is the number of 32Kbytes FLASH sectors at the end of the program
		flash memory that will be reserved for use with by the interfaces
		prototyped in include/nuttx/progmem.h

endif # SAMD5E5_PROGMEM

endmenu # SAMD5x/E5x Peripheral Support

config SAMD5E5_DMAC_NDESC
	int "Number of additional DMA Descriptors"
	default 0
	depends on SAMD5E5_DMAC
	---help---
		This provides the number of additional DMA descriptors that can be
		use to support multi-linked DMA transfers.  A minimum of 16
		descriptors will always be allocated (16 for the base descriptor which
		overlap the writeback descriptors).  If this value is set to zero,
		then only single block DMA transfers can be supported.

		Each additional DMA descriptor will require 16-bytes for LPRAM
		memory.

menu "SERCOM Configuration"
	depends on SAMD5E5_SERCOM

choice
	prompt "SERCOM0 mode"
	default SAMD5E5_SERCOM0_ISUSART
	depends on SAMD5E5_SERCOM0

config SAMD5E5_SERCOM0_ISI2C
	bool "I2C Master"
	select I2C
	select SAMD5E5_HAVE_I2C_MASTER

config SAMD5E5_SERCOM0_ISSPI
	bool "SPI"
	select SAMD5E5_HAVE_SPI

config SAMD5E5_SERCOM0_ISUSART
	bool "USART"
	select USART0_SERIALDRIVER

endchoice

choice
	prompt "SERCOM1 mode"
	default SAMD5E5_SERCOM1_ISUSART
	depends on SAMD5E5_SERCOM1

config SAMD5E5_SERCOM1_ISI2C
	bool "I2C Master"
	select I2C
	select SAMD5E5_HAVE_I2C_MASTER

config SAMD5E5_SERCOM1_ISSPI
	bool "SPI"
	select SAMD5E5_HAVE_SPI

config SAMD5E5_SERCOM1_ISUSART
	bool "USART"
	select USART1_SERIALDRIVER

endchoice

choice
	prompt "SERCOM2 mode"
	default SAMD5E5_SERCOM2_ISUSART
	depends on SAMD5E5_SERCOM2

config SAMD5E5_SERCOM2_ISI2C
	bool "I2C Master"
	select I2C
	select SAMD5E5_HAVE_I2C_MASTER

config SAMD5E5_SERCOM2_ISSPI
	bool "SPI"
	select SAMD5E5_HAVE_SPI

config SAMD5E5_SERCOM2_ISUSART
	bool "USART"
	select USART2_SERIALDRIVER

endchoice

choice
	prompt "SERCOM3 mode"
	default SAMD5E5_SERCOM3_ISUSART
	depends on SAMD5E5_SERCOM3

config SAMD5E5_SERCOM3_ISI2C
	bool "I2C Master"
	select I2C
	select SAMD5E5_HAVE_I2C_MASTER

config SAMD5E5_SERCOM3_ISSPI
	bool "SPI"
	select SAMD5E5_HAVE_SPI

config SAMD5E5_SERCOM3_ISUSART
	bool "USART"
	select USART3_SERIALDRIVER

endchoice

choice
	prompt "SERCOM4 mode"
	default SAMD5E5_SERCOM4_ISUSART
	depends on SAMD5E5_SERCOM4

config SAMD5E5_SERCOM4_ISI2C
	bool "I2C Master"
	select I2C
	select SAMD5E5_HAVE_I2C_MASTER

config SAMD5E5_SERCOM4_ISSPI
	bool "SPI"
	select SAMD5E5_HAVE_SPI

config SAMD5E5_SERCOM4_ISUSART
	bool "USART"
	select USART4_SERIALDRIVER

endchoice

choice
	prompt "SERCOM5 mode"
	default SAMD5E5_SERCOM5_ISUSART
	depends on SAMD5E5_SERCOM5

config SAMD5E5_SERCOM5_ISI2C
	bool "I2C Master"
	select I2C
	select SAMD5E5_HAVE_I2C_MASTER

config SAMD5E5_SERCOM5_ISSPI
	bool "SPI"
	select SAMD5E5_HAVE_SPI

config SAMD5E5_SERCOM5_ISUSART
	bool "USART"
	select USART5_SERIALDRIVER

endchoice

choice
	prompt "SERCOM6 mode"
	default SAMD5E5_SERCOM6_ISUSART
	depends on SAMD5E5_SERCOM6

config SAMD5E5_SERCOM6_ISI2C
	bool "I2C Master"
	select I2C
	select SAMD5E5_HAVE_I2C_MASTER

config SAMD5E5_SERCOM6_ISSPI
	bool "SPI"
	select SAMD5E5_HAVE_SPI

config SAMD5E5_SERCOM6_ISUSART
	bool "USART"
	select USART6_SERIALDRIVER

endchoice

choice
	prompt "SERCOM7 mode"
	default SAMD5E5_SERCOM7_ISUSART
	depends on SAMD5E5_SERCOM7

config SAMD5E5_SERCOM7_ISI2C
	bool "I2C Master"
	select I2C
	select SAMD5E5_HAVE_I2C_MASTER

config SAMD5E5_SERCOM7_ISSPI
	bool "SPI"
	select SAMD5E5_HAVE_SPI

config SAMD5E5_SERCOM7_ISUSART
	bool "USART"
	select USART7_SERIALDRIVER

endchoice
endmenu # SERCOM Configuration

config SAMD5E5_HAVE_SPI
	bool
	default n
	select SPI

menu "SPI options"
	depends on SAMD5E5_HAVE_I2C_MASTER

config SAMD5E5_SPI_DMA
	bool "SPI DMA"
	default n
	depends on SAMD5E5_DMAC && EXPERIMENTAL
	---help---
		Use DMA for SPI SERCOM peripherals.

config SAMD5E5_SPI_REGDEBUG
	bool "SPI register-Level Debug"
	default n
	depends on DEBUG_SPI_INFO
	---help---
		Enable very low-level register access debug.  Depends on DEBUG_SPI.

endmenu # SPI options

config SAMD5E5_HAVE_I2C_MASTER
	bool
	default n
	select I2C

menu "I2C master options"
	depends on SAMD5E5_HAVE_I2C_MASTER

config SAMD5E5_I2C_REGDEBUG
	bool "I2C register-Level Debug"
	default n
	depends on DEBUG_I2C_INFO
	---help---
		Enable very low-level register access debug.  Depends on DEBUG_I2C.

endmenu # I2C options

menu "USB options"
	depends on SAMD5E5_USB

config SAMD5E5_USB_ENABLE_PPEP
	bool "Enable Ping-Pong Endpoints"
	default n
	---help---
		To maximize throughput, an endpoint can be configured for ping-pong
		operation.  When this is done the input and output endpoint with the same
		address are used in the same direction.  The CPU or DMA Controller can
		then read/write one data buffer while the USB module writes/reads from
		the other buffer.  This gives double buffered communication.

config SAMD5E5_USB_REGDEBUG
	bool "USB register-Level Debug"
	default n
	depends on DEBUG_USB_INFO
	---help---
		Enable very low-level register access debug.  Depends on
		CONFIG_DEBUG_USB_INFO.

endmenu # USB options

if SAMD5E5_GMAC

menu "GMAC device driver options"

config SAMD5E5_GMAC_NRXBUFFERS
	int "Number of RX buffers"
	default 16
	---help---
		GMAC buffer memory is segmented into 128 byte units (not
		configurable).  This setting provides the number of such 128 byte
		units used for reception.  This is also equal to the number of RX
		descriptors that will be allocated  The selected value must be an
		even power of 2.

config SAMD5E5_GMAC_NTXBUFFERS
	int "Number of TX buffers"
	default 8
	---help---
		GMAC buffer memory is segmented into full Ethernet packets (size
		NET_BUFSIZE bytes).  This setting provides the number of such packets
		that can be in flight.  This is also equal to the number of TX
		descriptors that will be allocated.

config SAMD5E5_GMAC_PREALLOCATE
	bool "Preallocate buffers"
	default n
	---help---
		Buffer an descriptor many may either be allocated from the memory
		pool or pre-allocated to lie in .bss.  This options selected pre-
		allocated buffer memory.

config SAMD5E5_GMAC_NBC
	bool "Disable Broadcast"
	default n
	---help---
		Select to disable receipt of broadcast packets.

config SAMD5E5_GMAC_PHYADDR
	int "PHY address"
	default 1
	---help---
		The 5-bit address of the PHY on the board.  Default: 1

config SAMD5E5_GMAC_PHYINIT
	bool "Board-specific PHY Initialization"
	default n
	---help---
		Some boards require specialized initialization of the PHY before it can be used.
		This may include such things as configuring GPIOs, resetting the PHY, etc.  If
		SAMD5E5_GMAC_PHYINIT is defined in the configuration then the board specific logic must
		provide sam_phyinitialize();  The SAMD5E5 GMAC driver will call this function
		one time before it first uses the PHY.

config SAMD5E5_GMAC_AUTONEG
	bool "Use autonegotiation"
	default y
	---help---
		Use PHY autonegotiation to determine speed and mode

if !SAMD5E5_GMAC_AUTONEG

config SAMD5E5_GMAC_ETHFD
	bool "Full duplex"
	default n
	---help---
		If SAMD5E5_GMAC_AUTONEG is not defined, then this may be defined to
		select full duplex mode. Default: half-duplex

choice
	prompt "GMAC Speed"
	default SAMD5E5_GMAC_ETH100MBPS
	---help---
		If autonegotiation is not used, then you must select the fixed speed
		of the PHY

config SAMD5E5_GMAC_ETH10MBPS
	bool "10 Mbps"
	---help---
		If SAMD5E5_GMAC_AUTONEG is not defined, then this may be defined to select 10 MBps
		speed.  Default: 100 Mbps

config SAMD5E5_GMAC_ETH100MBPS
	bool "100 Mbps"
	---help---
		If SAMD5E5_GMAC_AUTONEG is not defined, then this may be defined to select 100 MBps
		speed.  Default: 100 Mbps

endchoice # GMAC speed
endif # !SAMD5E5_GMAC_AUTONEG

config SAMD5E5_GMAC_REGDEBUG
	bool "Register-Level Debug"
	default n
	depends on DEBUG_NET_INFO
	---help---
		Enable very low-level register access debug.  Depends on CONFIG_DEBUG_NET_INFO.

config SAMD5E5_GMAC_USE_MII
	bool "Use MII interface for Ethernet"
	default n
	---help---
		If selected, use MII interface to Ethernet PHY.  Otherwise, RMII will be used.

endmenu # GMAC device driver options
endif # SAMD5E5_GMAC
