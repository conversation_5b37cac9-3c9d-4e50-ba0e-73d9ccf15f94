/****************************************************************************
 * arch/arm/src/stm32f7/hardware/stm32f76xx77xx_pinmap_legacy.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_STM32F7_HARDWARE_STM32F76XX77XX_PINMAP_LEGACY_H
#define __ARCH_ARM_SRC_STM32F7_HARDWARE_STM32F76XX77XX_PINMAP_LEGACY_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

#include "stm32_gpio.h"

#if defined(CONFIG_STM32F7_STM32F76XX) || defined(CONFIG_STM32F7_STM32F77XX)

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Alternate Pin Functions.
 * All members of the STM32F76|7xxx family share the same pin multiplexing
 *(although they may differ in the pins physically available).
 *
 * Alternative pin selections are provided with a numeric suffix like _1, _2,
 * etc. Drivers, however, will use the pin selection without the numeric
 * suffix.
 * Additional definitions are required in the board.h file.  For example, if
 * CAN1_RX connects via PA11 on some board, then the following definitions
 * should should appear in the board.h header file for that board:
 *
 * #define GPIO_CAN1_RX GPIO_CAN1_RX_1
 *
 * The driver will then automatically configure PA11 as the CAN1 RX pin.
 */

/* WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific GPIO options such as
 * frequency, open-drain/push-pull, and pull-up/down!
 *  Just the basics are defined for most pins in this file.
 */

/* ADC */

#define GPIO_ADC1_IN0         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN0)
#define GPIO_ADC1_IN1         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN1)
#define GPIO_ADC1_IN2         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN2)
#define GPIO_ADC1_IN3         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN3)
#define GPIO_ADC1_IN4         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN4)
#define GPIO_ADC1_IN5         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN5)
#define GPIO_ADC1_IN6         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN6)
#define GPIO_ADC1_IN7         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN7)
#define GPIO_ADC1_IN8         (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN0)
#define GPIO_ADC1_IN9         (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN1)
#define GPIO_ADC1_IN10        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN0)
#define GPIO_ADC1_IN11        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN1)
#define GPIO_ADC1_IN12        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN2)
#define GPIO_ADC1_IN13        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN3)
#define GPIO_ADC1_IN14        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN4)
#define GPIO_ADC1_IN15        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN5)

#define GPIO_ADC2_IN0         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN0)
#define GPIO_ADC2_IN1         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN1)
#define GPIO_ADC2_IN2         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN2)
#define GPIO_ADC2_IN3         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN3)
#define GPIO_ADC2_IN4         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN4)
#define GPIO_ADC2_IN5         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN5)
#define GPIO_ADC2_IN6         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN6)
#define GPIO_ADC2_IN7         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN7)
#define GPIO_ADC2_IN8         (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN0)
#define GPIO_ADC2_IN9         (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN1)
#define GPIO_ADC2_IN10        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN0)
#define GPIO_ADC2_IN11        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN1)
#define GPIO_ADC2_IN12        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN2)
#define GPIO_ADC2_IN13        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN3)
#define GPIO_ADC2_IN14        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN4)
#define GPIO_ADC2_IN15        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN5)

#define GPIO_ADC3_IN0         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN0)
#define GPIO_ADC3_IN1         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN1)
#define GPIO_ADC3_IN2         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN2)
#define GPIO_ADC3_IN3         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN3)
#define GPIO_ADC3_IN4         (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN6)
#define GPIO_ADC3_IN5         (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN7)
#define GPIO_ADC3_IN6         (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN8)
#define GPIO_ADC3_IN7         (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN9)
#define GPIO_ADC3_IN8         (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN10)

#define GPIO_ADC3_IN9         (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN3)
#define GPIO_ADC3_IN10        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN0)
#define GPIO_ADC3_IN11        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN1)
#define GPIO_ADC3_IN12        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN2)
#define GPIO_ADC3_IN13        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN3)
#define GPIO_ADC3_IN14        (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN4)
#define GPIO_ADC3_IN15        (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN5)

/* CAN */

#define GPIO_CAN1_RX_1        (GPIO_ALT|GPIO_AF9 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN11)
#define GPIO_CAN1_RX_2        (GPIO_ALT|GPIO_AF9 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN8)
#define GPIO_CAN1_RX_3        (GPIO_ALT|GPIO_AF9 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN0)
#define GPIO_CAN1_RX_4        (GPIO_ALT|GPIO_AF9 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN14)
#define GPIO_CAN1_RX_5        (GPIO_ALT|GPIO_AF9 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN9)

#define GPIO_CAN1_TX_1        (GPIO_ALT|GPIO_AF9 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN12)
#define GPIO_CAN1_TX_2        (GPIO_ALT|GPIO_AF9 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN9)
#define GPIO_CAN1_TX_3        (GPIO_ALT|GPIO_AF9 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN1)
#define GPIO_CAN1_TX_4        (GPIO_ALT|GPIO_AF9 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN13)

#define GPIO_CAN2_RX_1        (GPIO_ALT|GPIO_AF9 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN12)
#define GPIO_CAN2_RX_2        (GPIO_ALT|GPIO_AF9 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN5)
#define GPIO_CAN2_TX_1        (GPIO_ALT|GPIO_AF9 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN13)
#define GPIO_CAN2_TX_2        (GPIO_ALT|GPIO_AF9 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN6)

#define GPIO_CAN3_RX_1        (GPIO_ALT|GPIO_AF11|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN8)
#define GPIO_CAN3_RX_2        (GPIO_ALT|GPIO_AF11|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN3)
#define GPIO_CAN3_TX_1        (GPIO_ALT|GPIO_AF11|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN15)
#define GPIO_CAN3_TX_2        (GPIO_ALT|GPIO_AF11|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN4)

/* DAC -" Once the DAC channel is enabled, the corresponding GPIO pin
 * (PA4 or PA5) is automatically connected to the analog converter output
 * (DAC_OUTx). In order to avoid parasitic consumption, the PA4 or PA5 pin
 * should first be configured to analog (AIN)".
 */

#define GPIO_DAC1_OUT          (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN4)
#define GPIO_DAC2_OUT          (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN5)

/* Digital Camera Interface (DCMI) */

#define GPIO_DCMI_D0_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN9)
#define GPIO_DCMI_D0_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN6)
#define GPIO_DCMI_D0_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTH|GPIO_PIN9)
#define GPIO_DCMI_D1_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN10)
#define GPIO_DCMI_D1_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN7)
#define GPIO_DCMI_D1_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTH|GPIO_PIN10)
#define GPIO_DCMI_D2_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN8)
#define GPIO_DCMI_D2_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTE|GPIO_PIN0)
#define GPIO_DCMI_D2_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN10)
#define GPIO_DCMI_D2_4        (GPIO_ALT|GPIO_AF13|GPIO_PORTH|GPIO_PIN11)
#define GPIO_DCMI_D3_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN9)
#define GPIO_DCMI_D3_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTE|GPIO_PIN1)
#define GPIO_DCMI_D3_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN11)
#define GPIO_DCMI_D3_4        (GPIO_ALT|GPIO_AF13|GPIO_PORTH|GPIO_PIN12)
#define GPIO_DCMI_D4_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN11)
#define GPIO_DCMI_D4_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTE|GPIO_PIN4)
#define GPIO_DCMI_D4_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTH|GPIO_PIN14)
#define GPIO_DCMI_D5_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN6)
#define GPIO_DCMI_D5_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTD|GPIO_PIN3)
#define GPIO_DCMI_D5_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTI|GPIO_PIN4)
#define GPIO_DCMI_D6_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN8)
#define GPIO_DCMI_D6_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTE|GPIO_PIN5)
#define GPIO_DCMI_D6_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTI|GPIO_PIN6)
#define GPIO_DCMI_D7_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN9)
#define GPIO_DCMI_D7_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTE|GPIO_PIN6)
#define GPIO_DCMI_D7_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTI|GPIO_PIN7)
#define GPIO_DCMI_D8_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN10)
#define GPIO_DCMI_D8_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTH|GPIO_PIN6)
#define GPIO_DCMI_D8_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTI|GPIO_PIN1)
#define GPIO_DCMI_D9_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN12)
#define GPIO_DCMI_D9_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTH|GPIO_PIN7)
#define GPIO_DCMI_D9_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTI|GPIO_PIN2)
#define GPIO_DCMI_D10_1       (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN5)
#define GPIO_DCMI_D10_2       (GPIO_ALT|GPIO_AF13|GPIO_PORTD|GPIO_PIN6)
#define GPIO_DCMI_D10_3       (GPIO_ALT|GPIO_AF13|GPIO_PORTI|GPIO_PIN3)
#define GPIO_DCMI_D11_1       (GPIO_ALT|GPIO_AF13|GPIO_PORTD|GPIO_PIN2)
#define GPIO_DCMI_D11_2       (GPIO_ALT|GPIO_AF13|GPIO_PORTF|GPIO_PIN10)
#define GPIO_DCMI_D11_3       (GPIO_ALT|GPIO_AF13|GPIO_PORTH|GPIO_PIN15)
#define GPIO_DCMI_D12_1       (GPIO_ALT|GPIO_AF13|GPIO_PORTF|GPIO_PIN11)
#define GPIO_DCMI_D12_2       (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN6)
#define GPIO_DCMI_D13_1       (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN15)
#define GPIO_DCMI_D13_2       (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN7)
#define GPIO_DCMI_D13_3       (GPIO_ALT|GPIO_AF13|GPIO_PORTI|GPIO_PIN0)
#define GPIO_DCMI_HSYNC_1     (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN4)
#define GPIO_DCMI_HSYNC_2     (GPIO_ALT|GPIO_AF13|GPIO_PORTH|GPIO_PIN8)
#define GPIO_DCMI_PIXCK       (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN6)
#define GPIO_DCMI_VSYNC_1     (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN7)
#define GPIO_DCMI_VSYNC_2     (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN9)
#define GPIO_DCMI_VSYNC_3     (GPIO_ALT|GPIO_AF13|GPIO_PORTI|GPIO_PIN5)

/* DSI Host */

#define DSI_TE_1             (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN11)
#define DSI_TE_2             (GPIO_ALT|GPIO_AF13|GPIO_PORTJ|GPIO_PIN12)

/* Digital filter for sigma delta modulators */

#define GPIO_DFSDM1_CKOUT_1   (GPIO_ALT|GPIO_AF6 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN0)
#define GPIO_DFSDM1_CKOUT_2   (GPIO_ALT|GPIO_AF6 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN2)
#define GPIO_DFSDM1_CKOUT_3   (GPIO_ALT|GPIO_AF3 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN3)
#define GPIO_DFSDM1_CKOUT_4   (GPIO_ALT|GPIO_AF3 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN10)
#define GPIO_DFSDM1_CKOUT_5   (GPIO_ALT|GPIO_AF6 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN9)
#define GPIO_DFSDM1_CKIN0_1   (GPIO_ALT|GPIO_AF3 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN0)
#define GPIO_DFSDM1_CKIN0_2   (GPIO_ALT|GPIO_AF6 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN3)
#define GPIO_DFSDM1_CKIN1_1   (GPIO_ALT|GPIO_AF10|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN2)
#define GPIO_DFSDM1_CKIN1_2   (GPIO_ALT|GPIO_AF10|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN13)
#define GPIO_DFSDM1_CKIN1_3   (GPIO_ALT|GPIO_AF3 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN2)
#define GPIO_DFSDM1_CKIN1_4   (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN7)
#define GPIO_DFSDM1_CKIN2_1   (GPIO_ALT|GPIO_AF10|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN15)
#define GPIO_DFSDM1_CKIN2_2   (GPIO_ALT|GPIO_AF3 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN4)
#define GPIO_DFSDM1_CKIN2_3   (GPIO_ALT|GPIO_AF6 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN8)
#define GPIO_DFSDM1_CKIN3_1   (GPIO_ALT|GPIO_AF6 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN6)
#define GPIO_DFSDM1_CKIN3_2   (GPIO_ALT|GPIO_AF10|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN1)
#define GPIO_DFSDM1_CKIN3_3   (GPIO_ALT|GPIO_AF3 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN8)
#define GPIO_DFSDM1_CKIN3_4   (GPIO_ALT|GPIO_AF10|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN5)
#define GPIO_DFSDM1_CKIN4_1   (GPIO_ALT|GPIO_AF3 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN6)
#define GPIO_DFSDM1_CKIN4_2   (GPIO_ALT|GPIO_AF6 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN11)
#define GPIO_DFSDM1_CKIN5_1   (GPIO_ALT|GPIO_AF6 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN7)
#define GPIO_DFSDM1_CKIN5_2   (GPIO_ALT|GPIO_AF3 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN10)
#define GPIO_DFSDM1_CKIN5_3   (GPIO_ALT|GPIO_AF6 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN13)
#define GPIO_DFSDM1_CKIN6_1   (GPIO_ALT|GPIO_AF3 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN0)
#define GPIO_DFSDM1_CKIN6_2   (GPIO_ALT|GPIO_AF6 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN14)
#define GPIO_DFSDM1_CKIN7_1   (GPIO_ALT|GPIO_AF6 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN8)
#define GPIO_DFSDM1_CKIN7_2   (GPIO_ALT|GPIO_AF6 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN1)
#define GPIO_DFSDM1_CKIN7_3   (GPIO_ALT|GPIO_AF6 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN11)
#define GPIO_DFSDM1_CKIN7_4   (GPIO_ALT|GPIO_AF6 |GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTS|GPIO_PIN1)
#define GPIO_DFSDM1_DATAIN0_1 (GPIO_ALT|GPIO_AF3 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN1)
#define GPIO_DFSDM1_DATAIN0_2 (GPIO_ALT|GPIO_AF6 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN4)
#define GPIO_DFSDM1_DATAIN1_1 (GPIO_ALT|GPIO_AF6 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN1)
#define GPIO_DFSDM1_DATAIN1_2 (GPIO_ALT|GPIO_AF6 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN12)
#define GPIO_DFSDM1_DATAIN1_3 (GPIO_ALT|GPIO_AF3 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN3)
#define GPIO_DFSDM1_DATAIN1_4 (GPIO_ALT|GPIO_AF10|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN6)
#define GPIO_DFSDM1_DATAIN2_1 (GPIO_ALT|GPIO_AF6 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN14)
#define GPIO_DFSDM1_DATAIN2_2 (GPIO_ALT|GPIO_AF3 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN5)
#define GPIO_DFSDM1_DATAIN2_3 (GPIO_ALT|GPIO_AF6 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN7)
#define GPIO_DFSDM1_DATAIN3_1 (GPIO_ALT|GPIO_AF6 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN7)
#define GPIO_DFSDM1_DATAIN3_2 (GPIO_ALT|GPIO_AF3 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN9)
#define GPIO_DFSDM1_DATAIN3_3 (GPIO_ALT|GPIO_AF10|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN4)
#define GPIO_DFSDM1_DATAIN4_1 (GPIO_ALT|GPIO_AF6 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN0)
#define GPIO_DFSDM1_DATAIN4_2 (GPIO_ALT|GPIO_AF3 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN7)
#define GPIO_DFSDM1_DATAIN4_3 (GPIO_ALT|GPIO_AF6 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN10)
#define GPIO_DFSDM1_DATAIN5_1 (GPIO_ALT|GPIO_AF6 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN6)
#define GPIO_DFSDM1_DATAIN5_2 (GPIO_ALT|GPIO_AF3 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN11)
#define GPIO_DFSDM1_DATAIN5_3 (GPIO_ALT|GPIO_AF6 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN12)
#define GPIO_DFSDM1_DATAIN6_1 (GPIO_ALT|GPIO_AF3 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN1)
#define GPIO_DFSDM1_DATAIN6_2 (GPIO_ALT|GPIO_AF6 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN13)
#define GPIO_DFSDM1_DATAIN7_1 (GPIO_ALT|GPIO_AF6 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN9)
#define GPIO_DFSDM1_DATAIN7_2 (GPIO_ALT|GPIO_AF6 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN10)
#define GPIO_DFSDM1_DATAIN7_3 (GPIO_ALT|GPIO_AF6 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN0)

/* Ethernet MAC */

#define GPIO_ETH_MDC          (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN1)
#define GPIO_ETH_MDIO         (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN2)
#define GPIO_ETH_MII_COL_1    (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN3)
#define GPIO_ETH_MII_COL_2    (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN3)
#define GPIO_ETH_MII_CRS_1    (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN0)
#define GPIO_ETH_MII_CRS_2    (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN2)
#define GPIO_ETH_MII_RXD0     (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN4)
#define GPIO_ETH_MII_RXD1     (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN5)
#define GPIO_ETH_MII_RXD2_1   (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN0)
#define GPIO_ETH_MII_RXD2_2   (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN6)
#define GPIO_ETH_MII_RXD3_1   (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN1)
#define GPIO_ETH_MII_RXD3_2   (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN7)
#define GPIO_ETH_MII_RX_CLK   (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN1)
#define GPIO_ETH_MII_RX_DV    (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN7)
#define GPIO_ETH_MII_RX_ER_1  (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN10)
#define GPIO_ETH_MII_RX_ER_2  (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN10)
#define GPIO_ETH_MII_TXD0_1   (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN12)
#define GPIO_ETH_MII_TXD0_2   (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN13)
#define GPIO_ETH_MII_TXD1_1   (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN13)
#define GPIO_ETH_MII_TXD1_2   (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN14)
#define GPIO_ETH_MII_TXD2     (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN2)
#define GPIO_ETH_MII_TXD3_1   (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN8)
#define GPIO_ETH_MII_TXD3_2   (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN2)
#define GPIO_ETH_MII_TX_CLK   (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN3)
#define GPIO_ETH_MII_TX_EN_1  (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN11)
#define GPIO_ETH_MII_TX_EN_2  (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN11)
#define GPIO_ETH_PPS_OUT_1    (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN5)
#define GPIO_ETH_PPS_OUT_2    (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN8)

#define GPIO_ETH_RMII_CRS_DV  (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN7)
#define GPIO_ETH_RMII_REF_CLK (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN1)
#define GPIO_ETH_RMII_RXD0    (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN4)
#define GPIO_ETH_RMII_RXD1    (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN5)
#define GPIO_ETH_RMII_TXD0_1  (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN12)
#define GPIO_ETH_RMII_TXD0_2  (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN13)
#define GPIO_ETH_RMII_TXD1_1  (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN13)
#define GPIO_ETH_RMII_TXD1_2  (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN14)
#define GPIO_ETH_RMII_TX_EN_1 (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN11)
#define GPIO_ETH_RMII_TX_EN_2 (GPIO_ALT|GPIO_AF11|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN11)

/* Event outputs */

#define GPIO_PA0_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN0)
#define GPIO_PA1_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN1)
#define GPIO_PA2_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN2)
#define GPIO_PA3_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN3)
#define GPIO_PA4_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN4)
#define GPIO_PA5_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN5)
#define GPIO_PA6_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN6)
#define GPIO_PA7_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN7)
#define GPIO_PA8_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN8)
#define GPIO_PA9_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN9)
#define GPIO_PA10_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN10)
#define GPIO_PA11_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN11)
#define GPIO_PA12_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN12)
#define GPIO_PA13_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN13)
#define GPIO_PA14_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN14)
#define GPIO_PA15_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN15)

#define GPIO_PB0_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN0)
#define GPIO_PB1_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN1)
#define GPIO_PB2_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN2)
#define GPIO_PB3_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN3)
#define GPIO_PB4_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN4)
#define GPIO_PB5_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN5)
#define GPIO_PB6_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN6)
#define GPIO_PB7_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN7)
#define GPIO_PB8_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN8)
#define GPIO_PB9_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN9)
#define GPIO_PB10_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN10)
#define GPIO_PB11_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN11)
#define GPIO_PB12_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN12)
#define GPIO_PB13_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN13)
#define GPIO_PB14_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN14)
#define GPIO_PB15_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN15)

#define GPIO_PC0_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN0)
#define GPIO_PC1_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN1)
#define GPIO_PC2_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN2)
#define GPIO_PC3_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN3)
#define GPIO_PC4_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN4)
#define GPIO_PC5_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN5)
#define GPIO_PC6_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN6)
#define GPIO_PC7_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN7)
#define GPIO_PC8_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN8)
#define GPIO_PC9_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN9)
#define GPIO_PC10_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN10)
#define GPIO_PC11_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN11)
#define GPIO_PC12_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN12)
#define GPIO_PC13_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN13)
#define GPIO_PC14_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN14)
#define GPIO_PC15_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN15)

#define GPIO_PD0_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN0)
#define GPIO_PD1_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN1)
#define GPIO_PD2_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN2)
#define GPIO_PD3_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN3)
#define GPIO_PD4_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN4)
#define GPIO_PD5_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN5)
#define GPIO_PD6_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN6)
#define GPIO_PD7_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN7)
#define GPIO_PD8_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN8)
#define GPIO_PD9_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN9)
#define GPIO_PD10_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN10)
#define GPIO_PD11_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN11)
#define GPIO_PD12_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN12)
#define GPIO_PD13_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN13)
#define GPIO_PD14_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN14)
#define GPIO_PD15_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN15)

#define GPIO_PE0_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN0)
#define GPIO_PE1_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN1)
#define GPIO_PE2_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN2)
#define GPIO_PE3_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN3)
#define GPIO_PE4_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN4)
#define GPIO_PE5_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN5)
#define GPIO_PE6_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN6)
#define GPIO_PE7_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN7)
#define GPIO_PE8_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN8)
#define GPIO_PE9_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN9)
#define GPIO_PE10_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN10)
#define GPIO_PE11_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN11)
#define GPIO_PE12_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN12)
#define GPIO_PE13_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN13)
#define GPIO_PE14_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN14)
#define GPIO_PE15_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN15)

#define GPIO_PF0_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN0)
#define GPIO_PF1_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN1)
#define GPIO_PF2_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN2)
#define GPIO_PF3_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN3)
#define GPIO_PF4_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN4)
#define GPIO_PF5_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN5)
#define GPIO_PF6_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN6)
#define GPIO_PF7_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN7)
#define GPIO_PF8_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN8)
#define GPIO_PF9_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN9)
#define GPIO_PF10_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN10)
#define GPIO_PF11_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN11)
#define GPIO_PF12_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN12)
#define GPIO_PF13_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN13)
#define GPIO_PF14_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN14)
#define GPIO_PF15_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN15)

#define GPIO_PG0_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN0)
#define GPIO_PG1_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN1)
#define GPIO_PG2_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN2)
#define GPIO_PG3_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN3)
#define GPIO_PG4_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN4)
#define GPIO_PG5_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN5)
#define GPIO_PG6_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN6)
#define GPIO_PG7_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN7)
#define GPIO_PG8_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN8)
#define GPIO_PG9_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN9)
#define GPIO_PG10_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN10)
#define GPIO_PG11_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN11)
#define GPIO_PG12_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN12)
#define GPIO_PG13_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN13)
#define GPIO_PG14_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN14)
#define GPIO_PG15_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN15)

#define GPIO_PH0_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN0)
#define GPIO_PH1_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN1)
#define GPIO_PH2_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN2)
#define GPIO_PH3_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN3)
#define GPIO_PH4_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN4)
#define GPIO_PH5_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN5)
#define GPIO_PH6_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN6)
#define GPIO_PH7_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN7)
#define GPIO_PH8_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN8)
#define GPIO_PH9_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN9)
#define GPIO_PH10_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN10)
#define GPIO_PH11_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN11)
#define GPIO_PH12_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN12)
#define GPIO_PH13_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN13)
#define GPIO_PH14_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN14)
#define GPIO_PH15_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN15)

#define GPIO_PI0_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN0)
#define GPIO_PI1_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN1)
#define GPIO_PI2_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN2)
#define GPIO_PI3_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN3)
#define GPIO_PI4_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN4)
#define GPIO_PI5_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN5)
#define GPIO_PI6_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN6)
#define GPIO_PI7_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN7)
#define GPIO_PI8_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN8)
#define GPIO_PI9_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN9)
#define GPIO_PI10_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN10)
#define GPIO_PI11_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN11)
#define GPIO_PI12_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN12)
#define GPIO_PI13_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN13)
#define GPIO_PI14_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN14)
#define GPIO_PI15_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN15)

#define GPIO_PJ0_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTJ|GPIO_PIN0)
#define GPIO_PJ1_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTJ|GPIO_PIN1)
#define GPIO_PJ2_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTJ|GPIO_PIN2)
#define GPIO_PJ3_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTJ|GPIO_PIN3)
#define GPIO_PJ4_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTJ|GPIO_PIN4)
#define GPIO_PJ5_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTJ|GPIO_PIN5)
#define GPIO_PJ6_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTJ|GPIO_PIN6)
#define GPIO_PJ7_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTJ|GPIO_PIN7)
#define GPIO_PJ8_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTJ|GPIO_PIN8)
#define GPIO_PJ9_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTJ|GPIO_PIN9)
#define GPIO_PJ10_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTJ|GPIO_PIN10)
#define GPIO_PJ11_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTJ|GPIO_PIN11)
#define GPIO_PJ12_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTJ|GPIO_PIN12)
#define GPIO_PJ13_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTJ|GPIO_PIN13)
#define GPIO_PJ14_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTJ|GPIO_PIN14)
#define GPIO_PJ15_EVENTOUT    (GPIO_ALT|GPIO_AF15|GPIO_PORTJ|GPIO_PIN15)

#define GPIO_PK0_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTK|GPIO_PIN0)
#define GPIO_PK1_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTK|GPIO_PIN1)
#define GPIO_PK2_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTK|GPIO_PIN2)
#define GPIO_PK3_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTK|GPIO_PIN3)
#define GPIO_PK4_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTK|GPIO_PIN4)
#define GPIO_PK5_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTK|GPIO_PIN5)
#define GPIO_PK6_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTK|GPIO_PIN6)
#define GPIO_PK7_EVENTOUT     (GPIO_ALT|GPIO_AF15|GPIO_PORTK|GPIO_PIN7)

/* Flexible Static Memory Controller (FMC) */

#define GPIO_FMC_A0           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTF|GPIO_PIN0)
#define GPIO_FMC_A1           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTF|GPIO_PIN1)
#define GPIO_FMC_A2           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTF|GPIO_PIN2)
#define GPIO_FMC_A3           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTF|GPIO_PIN3)
#define GPIO_FMC_A4           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTF|GPIO_PIN4)
#define GPIO_FMC_A5           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTF|GPIO_PIN5)
#define GPIO_FMC_A6           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTF|GPIO_PIN12)
#define GPIO_FMC_A7           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTF|GPIO_PIN13)
#define GPIO_FMC_A8           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTF|GPIO_PIN14)
#define GPIO_FMC_A9           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTF|GPIO_PIN15)
#define GPIO_FMC_A10          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTG|GPIO_PIN0)
#define GPIO_FMC_A11          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTG|GPIO_PIN1)
#define GPIO_FMC_A12          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTG|GPIO_PIN2)
#define GPIO_FMC_A13          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTG|GPIO_PIN3)
#define GPIO_FMC_A14          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTG|GPIO_PIN4)
#define GPIO_FMC_A15          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTG|GPIO_PIN5)
#define GPIO_FMC_A16          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTD|GPIO_PIN11)
#define GPIO_FMC_A17          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTD|GPIO_PIN12)
#define GPIO_FMC_A18          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTD|GPIO_PIN13)
#define GPIO_FMC_A19          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTE|GPIO_PIN3)
#define GPIO_FMC_A20          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTE|GPIO_PIN4)
#define GPIO_FMC_A21          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTE|GPIO_PIN5)
#define GPIO_FMC_A22          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTE|GPIO_PIN6)
#define GPIO_FMC_A23          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTE|GPIO_PIN2)
#define GPIO_FMC_A24          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTG|GPIO_PIN13)
#define GPIO_FMC_A25          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTG|GPIO_PIN14)
#define GPIO_FMC_ALE          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTD|GPIO_PIN12)
#define GPIO_FMC_BA0          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTG|GPIO_PIN4)
#define GPIO_FMC_BA1          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTG|GPIO_PIN5)
#define GPIO_FMC_CLE          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTD|GPIO_PIN11)
#define GPIO_FMC_CLK          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTD|GPIO_PIN3)
#define GPIO_FMC_D0           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTD|GPIO_PIN14)
#define GPIO_FMC_D1           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTD|GPIO_PIN15)
#define GPIO_FMC_D2           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTD|GPIO_PIN0)
#define GPIO_FMC_D3           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTD|GPIO_PIN1)
#define GPIO_FMC_D4           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTE|GPIO_PIN7)
#define GPIO_FMC_D5           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTE|GPIO_PIN8)
#define GPIO_FMC_D6           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTE|GPIO_PIN9)
#define GPIO_FMC_D7           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTE|GPIO_PIN10)
#define GPIO_FMC_D8           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTE|GPIO_PIN11)
#define GPIO_FMC_D9           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTE|GPIO_PIN12)
#define GPIO_FMC_D10          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTE|GPIO_PIN13)
#define GPIO_FMC_D11          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTE|GPIO_PIN14)
#define GPIO_FMC_D12          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTE|GPIO_PIN15)
#define GPIO_FMC_D13          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTD|GPIO_PIN8)
#define GPIO_FMC_D14          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTD|GPIO_PIN9)
#define GPIO_FMC_D15          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTD|GPIO_PIN10)
#define GPIO_FMC_D16          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTH|GPIO_PIN8)
#define GPIO_FMC_D17          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTH|GPIO_PIN9)
#define GPIO_FMC_D18          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTH|GPIO_PIN10)
#define GPIO_FMC_D19          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTH|GPIO_PIN11)
#define GPIO_FMC_D20          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTH|GPIO_PIN12)
#define GPIO_FMC_D21          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTH|GPIO_PIN13)
#define GPIO_FMC_D22          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTH|GPIO_PIN14)
#define GPIO_FMC_D23          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTH|GPIO_PIN15)
#define GPIO_FMC_D24          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTI|GPIO_PIN0)
#define GPIO_FMC_D25          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTI|GPIO_PIN1)
#define GPIO_FMC_D26          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTI|GPIO_PIN2)
#define GPIO_FMC_D27          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTI|GPIO_PIN3)
#define GPIO_FMC_D28          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTI|GPIO_PIN6)
#define GPIO_FMC_D29          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTI|GPIO_PIN7)
#define GPIO_FMC_D30          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTI|GPIO_PIN9)
#define GPIO_FMC_D31          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTI|GPIO_PIN10)
#define GPIO_FMC_INT          (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN7)
#define GPIO_FMC_NBL0         (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTE|GPIO_PIN0)
#define GPIO_FMC_NBL1         (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTE|GPIO_PIN1)
#define GPIO_FMC_NBL2         (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTI|GPIO_PIN4)
#define GPIO_FMC_NBL3         (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTI|GPIO_PIN5)
#define GPIO_FMC_NCE_1        (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTG|GPIO_PIN9)
#define GPIO_FMC_NCE_2        (GPIO_ALT|GPIO_AF9 |GPIO_SPEED_100MHz|GPIO_PORTC|GPIO_PIN8)
#define GPIO_FMC_NE1_1        (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTD|GPIO_PIN7)
#define GPIO_FMC_NE1_2        (GPIO_ALT|GPIO_AF9 |GPIO_SPEED_100MHz|GPIO_PORTC|GPIO_PIN7)
#define GPIO_FMC_NE2_1        (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTG|GPIO_PIN9)
#define GPIO_FMC_NE2_2        (GPIO_ALT|GPIO_AF9 |GPIO_SPEED_100MHz|GPIO_PORTC|GPIO_PIN8)
#define GPIO_FMC_NE3          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTG|GPIO_PIN10)
#define GPIO_FMC_NE4          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTG|GPIO_PIN12)
#define GPIO_FMC_NL           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTB|GPIO_PIN7)
#define GPIO_FMC_NOE          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTD|GPIO_PIN4)
#define GPIO_FMC_NWAIT_1      (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTD|GPIO_PIN6)
#define GPIO_FMC_NWAIT_2      (GPIO_ALT|GPIO_AF9 |GPIO_SPEED_100MHz|GPIO_PORTC|GPIO_PIN6)
#define GPIO_FMC_NWE          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTD|GPIO_PIN5)
#define GPIO_FMC_SDCKE0_1     (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTC|GPIO_PIN3)
#define GPIO_FMC_SDCKE0_2     (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTC|GPIO_PIN5)
#define GPIO_FMC_SDCKE0_3     (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTH|GPIO_PIN2)
#define GPIO_FMC_SDCKE1_1     (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTB|GPIO_PIN5)
#define GPIO_FMC_SDCKE1_2     (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTH|GPIO_PIN7)
#define GPIO_FMC_SDCLK        (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTG|GPIO_PIN8)
#define GPIO_FMC_SDNCAS       (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTG|GPIO_PIN15)
#define GPIO_FMC_SDNE0_1      (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTC|GPIO_PIN2)
#define GPIO_FMC_SDNE0_2      (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTC|GPIO_PIN4)
#define GPIO_FMC_SDNE0_3      (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTH|GPIO_PIN3)
#define GPIO_FMC_SDNE1_1      (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTB|GPIO_PIN6)
#define GPIO_FMC_SDNE1_2      (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTH|GPIO_PIN6)
#define GPIO_FMC_SDNRAS       (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTF|GPIO_PIN11)
#define GPIO_FMC_SDNWE_1      (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTA|GPIO_PIN7)
#define GPIO_FMC_SDNWE_2      (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTC|GPIO_PIN0)
#define GPIO_FMC_SDNWE_3      (GPIO_ALT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PORTH|GPIO_PIN5)

/* HDMI-CEC Controller */

#define GPIO_HDMICEC_1        (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN6)
#define GPIO_HDMICEC_2        (GPIO_ALT|GPIO_AF4|GPIO_PORTA|GPIO_PIN15)

/* I2C */

#define GPIO_I2C1_SCL_1       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN6)
#define GPIO_I2C1_SCL_2       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN8)
#define GPIO_I2C1_SDA_1       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN7)
#define GPIO_I2C1_SDA_2       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN9)
#define GPIO_I2C1_SMBA        (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN5)

#define GPIO_I2C2_SCL_1       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN10)
#define GPIO_I2C2_SCL_2       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTF|GPIO_PIN1)
#define GPIO_I2C2_SCL_3       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTH|GPIO_PIN4)
#define GPIO_I2C2_SDA_1       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN11)
#define GPIO_I2C2_SDA_2       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTF|GPIO_PIN0)
#define GPIO_I2C2_SDA_3       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTH|GPIO_PIN5)
#define GPIO_I2C2_SMBA_1      (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN12)
#define GPIO_I2C2_SMBA_2      (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN2)
#define GPIO_I2C2_SMBA_3      (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN6)

#define GPIO_I2C3_SCL_1       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTA|GPIO_PIN8)
#define GPIO_I2C3_SCL_2       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTH|GPIO_PIN7)
#define GPIO_I2C3_SDA_1       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTC|GPIO_PIN9)
#define GPIO_I2C3_SDA_2       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTH|GPIO_PIN8)
#define GPIO_I2C3_SMBA_1      (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN9)
#define GPIO_I2C3_SMBA_2      (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN9)

#define GPIO_I2C4_SCL_1       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTD|GPIO_PIN12)
#define GPIO_I2C4_SCL_2       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTF|GPIO_PIN14)
#define GPIO_I2C4_SCL_3       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTH|GPIO_PIN11)
#define GPIO_I2C4_SCL_4       (GPIO_ALT|GPIO_AF11|GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN6)
#define GPIO_I2C4_SCL_5       (GPIO_ALT|GPIO_AF1 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN8)
#define GPIO_I2C4_SDA_1       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTD|GPIO_PIN13)
#define GPIO_I2C4_SDA_2       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTF|GPIO_PIN15)
#define GPIO_I2C4_SDA_3       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTH|GPIO_PIN12)
#define GPIO_I2C4_SDA_4       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN9)
#define GPIO_I2C4_SDA_5       (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN7)
#define GPIO_I2C4_SMBA_1      (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN11)
#define GPIO_I2C4_SMBA_2      (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN13)
#define GPIO_I2C4_SMBA_3      (GPIO_ALT|GPIO_AF4 |GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN10)
#define GPIO_I2C4_SMBA_4      (GPIO_ALT|GPIO_AF11|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN9)

/* I2S */

#define GPIO_I2S1_CK_1        (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN5)
#define GPIO_I2S1_CK_2        (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN3)
#define GPIO_I2S1_CK_3        (GPIO_ALT|GPIO_AF5 |GPIO_PORTG|GPIO_PIN11)
#define GPIO_I2S1_MCK         (GPIO_ALT|GPIO_AF5 |GPIO_PORTC|GPIO_PIN4)
#define GPIO_I2S1_SD_1        (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN7)
#define GPIO_I2S1_SD_2        (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN5)
#define GPIO_I2S1_SD_3        (GPIO_ALT|GPIO_AF5 |GPIO_PORTD|GPIO_PIN7)
#define GPIO_I2S1_WS_1        (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN15)
#define GPIO_I2S1_WS_2        (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN4)
#define GPIO_I2S1_WS_3        (GPIO_ALT|GPIO_AF5 |GPIO_PORTG|GPIO_PIN10)

#define GPIO_I2S2_CK_1        (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN9)
#define GPIO_I2S2_CK_2        (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN10)
#define GPIO_I2S2_CK_3        (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN13)
#define GPIO_I2S2_CK_4        (GPIO_ALT|GPIO_AF5 |GPIO_PORTD|GPIO_PIN3)
#define GPIO_I2S2_CK_5        (GPIO_ALT|GPIO_AF5 |GPIO_PORTI|GPIO_PIN1)
#define GPIO_I2S2_CK_6        (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN12)
#define GPIO_I2S2_MCK         (GPIO_ALT|GPIO_AF5 |GPIO_PORTC|GPIO_PIN6)
#define GPIO_I2S2_SD_1        (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN15)
#define GPIO_I2S2_SD_2        (GPIO_ALT|GPIO_AF5 |GPIO_PORTC|GPIO_PIN1)
#define GPIO_I2S2_SD_3        (GPIO_ALT|GPIO_AF5 |GPIO_PORTC|GPIO_PIN3)
#define GPIO_I2S2_SD_4        (GPIO_ALT|GPIO_AF5 |GPIO_PORTI|GPIO_PIN3)
#define GPIO_I2S2_WS_1        (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN12)
#define GPIO_I2S2_WS_2        (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN9)
#define GPIO_I2S2_WS_3        (GPIO_ALT|GPIO_AF5 |GPIO_PORTI|GPIO_PIN0)
#define GPIO_I2S2_WS_4        (GPIO_ALT|GPIO_AF7 |GPIO_PORTB|GPIO_PIN4)
#define GPIO_I2S2_WS_5        (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN11)

#define GPIO_I2S3_CK_1        (GPIO_ALT|GPIO_AF6 |GPIO_PORTB|GPIO_PIN3)
#define GPIO_I2S3_CK_2        (GPIO_ALT|GPIO_AF6 |GPIO_PORTC|GPIO_PIN10)
#define GPIO_I2S3_MCK         (GPIO_ALT|GPIO_AF6 |GPIO_PORTC|GPIO_PIN7)
#define GPIO_I2S3_SD_1        (GPIO_ALT|GPIO_AF5 |GPIO_PORTD|GPIO_PIN6)
#define GPIO_I2S3_SD_2        (GPIO_ALT|GPIO_AF6 |GPIO_PORTB|GPIO_PIN5)
#define GPIO_I2S3_SD_3        (GPIO_ALT|GPIO_AF6 |GPIO_PORTC|GPIO_PIN12)
#define GPIO_I2S3_SD_4        (GPIO_ALT|GPIO_AF7 |GPIO_PORTB|GPIO_PIN2)
#define GPIO_I2S3_WS_1        (GPIO_ALT|GPIO_AF6 |GPIO_PORTA|GPIO_PIN15)
#define GPIO_I2S3_WS_2        (GPIO_ALT|GPIO_AF6 |GPIO_PORTA|GPIO_PIN4)

#define GPIO_I2S4_SD_1        (GPIO_ALT|GPIO_AF11|GPIO_PORTB|GPIO_PIN7)
#define GPIO_I2S4_SD_2        (GPIO_ALT|GPIO_AF1 |GPIO_PORTB|GPIO_PIN9)

#define GPIO_I2S_CKIN         (GPIO_ALT|GPIO_AF5 |GPIO_PORTC|GPIO_PIN9)

/* JTAG */

#define GPIO_JTCK             (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN14)
#define GPIO_JTDI             (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN15)
#define GPIO_JTDO             (GPIO_ALT|GPIO_AF0|GPIO_PORTB|GPIO_PIN3)
#define GPIO_JTMS             (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN13)
#define GPIO_NJTRST           (GPIO_ALT|GPIO_AF0|GPIO_PORTB|GPIO_PIN4)

#define GPIO_SWCLK            (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN14)
#define GPIO_SWDIO            (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN13)

/* LCD-TFT Display Controller (LTDC) */

#define GPIO_LTDC_R0_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN13)
#define GPIO_LTDC_R0_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN2)
#define GPIO_LTDC_R0_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN15)
#define GPIO_LTDC_R1_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN2)
#define GPIO_LTDC_R1_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN3)
#define GPIO_LTDC_R1_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN0)
#define GPIO_LTDC_R2_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN1)
#define GPIO_LTDC_R2_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN10)
#define GPIO_LTDC_R2_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN8)
#define GPIO_LTDC_R2_4        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN1)
#define GPIO_LTDC_R3_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN9)
#define GPIO_LTDC_R3_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN2)
#define GPIO_LTDC_R3_1        (GPIO_ALT|GPIO_AF9 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN0)
#define GPIO_LTDC_R4_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN11)
#define GPIO_LTDC_R4_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN5)
#define GPIO_LTDC_R4_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN10)
#define GPIO_LTDC_R4_4        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN3)
#define GPIO_LTDC_R5_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN12)
#define GPIO_LTDC_R5_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN0)
#define GPIO_LTDC_R5_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN11)
#define GPIO_LTDC_R5_4        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN4)
#define GPIO_LTDC_R5_5        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN9)
#define GPIO_LTDC_R6_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN8)
#define GPIO_LTDC_R6_2        (GPIO_ALT|GPIO_AF9 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN1)
#define GPIO_LTDC_R6_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN12)
#define GPIO_LTDC_R6_4        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN5)
#define GPIO_LTDC_R7_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN15)
#define GPIO_LTDC_R7_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN6)
#define GPIO_LTDC_R7_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN6)
#define GPIO_LTDC_R7_4        (GPIO_ALT|GPIO_AF9 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN0)

#define GPIO_LTDC_B0_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN4)
#define GPIO_LTDC_B0_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN14)
#define GPIO_LTDC_B0_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN12)
#define GPIO_LTDC_B1_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN12)
#define GPIO_LTDC_B1_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN13)
#define GPIO_LTDC_B1_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN10)
#define GPIO_LTDC_B2_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN6)
#define GPIO_LTDC_B2_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN10)
#define GPIO_LTDC_B2_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN14)
#define GPIO_LTDC_B2_4        (GPIO_ALT|GPIO_AF9 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN3)
#define GPIO_LTDC_B2_5        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN9)
#define GPIO_LTDC_B3_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN10)
#define GPIO_LTDC_B3_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN11)
#define GPIO_LTDC_B3_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN15)
#define GPIO_LTDC_B3_4        (GPIO_ALT|GPIO_AF13|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN8)
#define GPIO_LTDC_B4_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN12)
#define GPIO_LTDC_B4_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN4)
#define GPIO_LTDC_B4_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTK|GPIO_PIN3)
#define GPIO_LTDC_B4_4        (GPIO_ALT|GPIO_AF9 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN12)
#define GPIO_LTDC_B4_5        (GPIO_ALT|GPIO_AF9 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN10)
#define GPIO_LTDC_B5_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN3)
#define GPIO_LTDC_B5_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN5)
#define GPIO_LTDC_B5_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTK|GPIO_PIN4)
#define GPIO_LTDC_B6_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN8)
#define GPIO_LTDC_B6_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN6)
#define GPIO_LTDC_B6_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTK|GPIO_PIN5)
#define GPIO_LTDC_B7_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN9)
#define GPIO_LTDC_B7_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN7)
#define GPIO_LTDC_B7_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTK|GPIO_PIN6)

#define GPIO_LTDC_G0_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN5)
#define GPIO_LTDC_G0_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN7)
#define GPIO_LTDC_G0_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN1)
#define GPIO_LTDC_G1_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN6)
#define GPIO_LTDC_G1_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN8)
#define GPIO_LTDC_G1_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN0)
#define GPIO_LTDC_G2_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN6)
#define GPIO_LTDC_G2_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN13)
#define GPIO_LTDC_G2_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN9)
#define GPIO_LTDC_G2_4        (GPIO_ALT|GPIO_AF9 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN15)
#define GPIO_LTDC_G3_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN11)
#define GPIO_LTDC_G3_2        (GPIO_ALT|GPIO_AF9 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN10)
#define GPIO_LTDC_G3_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN14)
#define GPIO_LTDC_G3_4        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN10)
#define GPIO_LTDC_G3_5        (GPIO_ALT|GPIO_AF10|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN9)
#define GPIO_LTDC_G3_6        (GPIO_ALT|GPIO_AF9 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN12)
#define GPIO_LTDC_G4_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN10)
#define GPIO_LTDC_G4_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN15)
#define GPIO_LTDC_G4_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN11)
#define GPIO_LTDC_G4_4        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN4)
#define GPIO_LTDC_G4_5        (GPIO_ALT|GPIO_AF9 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN13)
#define GPIO_LTDC_G5_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN11)
#define GPIO_LTDC_G5_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN0)
#define GPIO_LTDC_G5_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTK|GPIO_PIN0)
#define GPIO_LTDC_G5_4        (GPIO_ALT|GPIO_AF9 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN4)
#define GPIO_LTDC_G6_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN7)
#define GPIO_LTDC_G6_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN1)
#define GPIO_LTDC_G6_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTK|GPIO_PIN1)
#define GPIO_LTDC_G6_4        (GPIO_ALT|GPIO_AF9 |GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN11)
#define GPIO_LTDC_G7_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN3)
#define GPIO_LTDC_G7_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN2)
#define GPIO_LTDC_G7_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTK|GPIO_PIN2)
#define GPIO_LTDC_G7_4        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN5)
#define GPIO_LTDC_G7_5        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN8)

#define GPIO_LTDC_HSYNC_1     (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN6)
#define GPIO_LTDC_HSYNC_2     (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN10)
#define GPIO_LTDC_HSYNC_3     (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN12)
#define GPIO_LTDC_VSYNC_1     (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN4)
#define GPIO_LTDC_VSYNC_2     (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN9)
#define GPIO_LTDC_VSYNC_3     (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN13)
#define GPIO_LTDC_CLK_1       (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN14)
#define GPIO_LTDC_CLK_2       (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN7)
#define GPIO_LTDC_CLK_3       (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN14)
#define GPIO_LTDC_DE_1        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN13)
#define GPIO_LTDC_DE_2        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN10)
#define GPIO_LTDC_DE_3        (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTK|GPIO_PIN7)

/* Low Power Timer */

#define GPIO_LPTIM1_ETR_1     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTE|GPIO_PIN0)
#define GPIO_LPTIM1_ETR_2     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTG|GPIO_PIN14)
#define GPIO_LPTIM1_IN1_1     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTD|GPIO_PIN12)
#define GPIO_LPTIM1_IN1_2     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTG|GPIO_PIN12)
#define GPIO_LPTIM1_IN2_3     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTE|GPIO_PIN1)
#define GPIO_LPTIM1_IN2_4     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTH|GPIO_PIN2)
#define GPIO_LPTIM1_OUT_1     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN13)
#define GPIO_LPTIM1_OUT_2     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN13)

/* Clocks outputs */

#define GPIO_MCO1             (GPIO_ALT|GPIO_AF0|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN8)
#define GPIO_MCO2             (GPIO_ALT|GPIO_AF0|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN9)

/* Management data input/output */

#define MDIOS_MDIO_1          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_50MHz|GPIO_PORTA|GPIO_PIN2)
#define MDIOS_MDC_1           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_50MHz|GPIO_PORTA|GPIO_PIN6)
#define MDIOS_MDC_2           (GPIO_ALT|GPIO_AF12|GPIO_SPEED_50MHz|GPIO_PORTC|GPIO_PIN1)
#define MDIOS_MDIO_2          (GPIO_ALT|GPIO_AF12|GPIO_SPEED_50MHz|GPIO_PORTA|GPIO_PIN10)

/* OTG FS/HS (VBUS PA9 is not an alternate configuration) */

#define GPIO_OTGFS_DM         (GPIO_ALT|GPIO_FLOAT|GPIO_AF10|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN11)
#define GPIO_OTGFS_DP         (GPIO_ALT|GPIO_FLOAT|GPIO_AF10|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN12)
#define GPIO_OTGFS_ID         (GPIO_ALT|GPIO_PULLUP|GPIO_AF10|GPIO_SPEED_100MHz|GPIO_OPENDRAIN|GPIO_PORTA|GPIO_PIN10)
#define GPIO_OTGFS_SOF        (GPIO_ALT|GPIO_FLOAT|GPIO_AF10|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN8)

#define GPIO_OTGHSFS_DM       (GPIO_ALT|GPIO_FLOAT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN14)
#define GPIO_OTGHSFS_DP       (GPIO_ALT|GPIO_FLOAT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN15)
#define GPIO_OTGHSFS_ID       (GPIO_ALT|GPIO_PULLUP|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN12)

#define GPIO_OTGHS_SOF        (GPIO_ALT|GPIO_FLOAT|GPIO_AF12|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN4)

#define GPIO_OTGHS_ULPI_CK    (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN5)
#define GPIO_OTGHS_ULPI_D0    (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN3)
#define GPIO_OTGHS_ULPI_D1    (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN0)
#define GPIO_OTGHS_ULPI_D2    (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN1)
#define GPIO_OTGHS_ULPI_D3    (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN10)
#define GPIO_OTGHS_ULPI_D4    (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN11)
#define GPIO_OTGHS_ULPI_D5    (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN12)
#define GPIO_OTGHS_ULPI_D6    (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN13)
#define GPIO_OTGHS_ULPI_D7    (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN5)
#define GPIO_OTGHS_ULPI_DIR_1 (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN2)
#define GPIO_OTGHS_ULPI_DIR_2 (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN11)
#define GPIO_OTGHS_ULPI_NXT_1 (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN3)
#define GPIO_OTGHS_ULPI_NXT_2 (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN4)
#define GPIO_OTGHS_ULPI_STP   (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN0)

/* QuadSPI */

#define GPIO_QUADSPI_BK1_IO0_1  (GPIO_ALT|GPIO_AF10|GPIO_PORTF|GPIO_PIN8)
#define GPIO_QUADSPI_BK1_IO0_2  (GPIO_ALT|GPIO_AF9 |GPIO_PORTC|GPIO_PIN9)
#define GPIO_QUADSPI_BK1_IO0_3  (GPIO_ALT|GPIO_AF9 |GPIO_PORTD|GPIO_PIN11)
#define GPIO_QUADSPI_BK1_IO1_1  (GPIO_ALT|GPIO_AF10|GPIO_PORTF|GPIO_PIN9)
#define GPIO_QUADSPI_BK1_IO1_2  (GPIO_ALT|GPIO_AF9 |GPIO_PORTC|GPIO_PIN10)
#define GPIO_QUADSPI_BK1_IO1_3  (GPIO_ALT|GPIO_AF9 |GPIO_PORTD|GPIO_PIN12)
#define GPIO_QUADSPI_BK1_IO2_1  (GPIO_ALT|GPIO_AF9 |GPIO_PORTE|GPIO_PIN2)
#define GPIO_QUADSPI_BK1_IO2_2  (GPIO_ALT|GPIO_AF9 |GPIO_PORTF|GPIO_PIN7)
#define GPIO_QUADSPI_BK1_IO3_1  (GPIO_ALT|GPIO_AF9 |GPIO_PORTA|GPIO_PIN1)
#define GPIO_QUADSPI_BK1_IO3_2  (GPIO_ALT|GPIO_AF9 |GPIO_PORTD|GPIO_PIN13)
#define GPIO_QUADSPI_BK1_IO3_3  (GPIO_ALT|GPIO_AF9 |GPIO_PORTF|GPIO_PIN6)
#define GPIO_QUADSPI_BK1_NCS_1  (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN6)
#define GPIO_QUADSPI_BK1_NCS_2  (GPIO_ALT|GPIO_AF9 |GPIO_PORTB|GPIO_PIN10)

#define GPIO_QUADSPI_BK2_IO0_1  (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN7)
#define GPIO_QUADSPI_BK2_IO0_2  (GPIO_ALT|GPIO_AF9 |GPIO_PORTH|GPIO_PIN2)
#define GPIO_QUADSPI_BK2_IO1_1  (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN8)
#define GPIO_QUADSPI_BK2_IO1_2  (GPIO_ALT|GPIO_AF9 |GPIO_PORTH|GPIO_PIN3)
#define GPIO_QUADSPI_BK2_IO2_1  (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN9)
#define GPIO_QUADSPI_BK2_IO2_2  (GPIO_ALT|GPIO_AF9 |GPIO_PORTG|GPIO_PIN9)
#define GPIO_QUADSPI_BK2_IO3_1  (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN10)
#define GPIO_QUADSPI_BK2_IO3_2  (GPIO_ALT|GPIO_AF9 |GPIO_PORTG|GPIO_PIN14)
#define GPIO_QUADSPI_BK2_NCS    (GPIO_ALT|GPIO_AF9 |GPIO_PORTC|GPIO_PIN11)

#define GPIO_QUADSPI_CLK_1      (GPIO_ALT|GPIO_AF9 |GPIO_PORTB|GPIO_PIN2)
#define GPIO_QUADSPI_CLK_2      (GPIO_ALT|GPIO_AF9 |GPIO_PORTF|GPIO_PIN10)

/* RTC */

#define GPIO_RTC_REFIN        (GPIO_ALT|GPIO_AF0|GPIO_PORTB|GPIO_PIN15)

/* Serial Audio Interface */

#define GPIO_SAI1_FS_A        (GPIO_ALT|GPIO_AF6 |GPIO_PORTE|GPIO_PIN4)
#define GPIO_SAI1_FS_B        (GPIO_ALT|GPIO_AF6 |GPIO_PORTF|GPIO_PIN9)
#define GPIO_SAI1_MCLK_A_1    (GPIO_ALT|GPIO_AF6 |GPIO_PORTE|GPIO_PIN2)
#define GPIO_SAI1_MCLK_A_2    (GPIO_ALT|GPIO_AF6 |GPIO_PORTG|GPIO_PIN7)
#define GPIO_SAI1_MCLK_B      (GPIO_ALT|GPIO_AF6 |GPIO_PORTF|GPIO_PIN7)
#define GPIO_SAI1_SCK_A       (GPIO_ALT|GPIO_AF6 |GPIO_PORTE|GPIO_PIN5)
#define GPIO_SAI1_SCK_B       (GPIO_ALT|GPIO_AF6 |GPIO_PORTF|GPIO_PIN8)
#define GPIO_SAI1_SD_A_1      (GPIO_ALT|GPIO_AF6 |GPIO_PORTB|GPIO_PIN2)
#define GPIO_SAI1_SD_A_2      (GPIO_ALT|GPIO_AF6 |GPIO_PORTC|GPIO_PIN1)
#define GPIO_SAI1_SD_A_3      (GPIO_ALT|GPIO_AF6 |GPIO_PORTD|GPIO_PIN6)
#define GPIO_SAI1_SD_A_4      (GPIO_ALT|GPIO_AF6 |GPIO_PORTE|GPIO_PIN6)
#define GPIO_SAI1_SD_B_1      (GPIO_ALT|GPIO_AF6 |GPIO_PORTE|GPIO_PIN3)
#define GPIO_SAI1_SD_B_2      (GPIO_ALT|GPIO_AF6 |GPIO_PORTF|GPIO_PIN6)
#define GPIO_SAI2_FS_A_1      (GPIO_ALT|GPIO_AF10|GPIO_PORTD|GPIO_PIN12)
#define GPIO_SAI2_FS_A_2      (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN7)
#define GPIO_SAI2_FS_B_1      (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN13)
#define GPIO_SAI2_FS_B_2      (GPIO_ALT|GPIO_AF10|GPIO_PORTG|GPIO_PIN9)
#define GPIO_SAI2_FS_B_3      (GPIO_ALT|GPIO_AF8 |GPIO_PORTA|GPIO_PIN12)
#define GPIO_SAI2_FS_B_4      (GPIO_ALT|GPIO_AF8 |GPIO_PORTC|GPIO_PIN0)
#define GPIO_SAI2_MCLK_A_1    (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN0)
#define GPIO_SAI2_MCLK_A_2    (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN4)
#define GPIO_SAI2_MCLK_B_1    (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN1)
#define GPIO_SAI2_MCLK_B_2    (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN14)
#define GPIO_SAI2_MCLK_B_3    (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN6)
#define GPIO_SAI2_MCLK_B_4    (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN3)
#define GPIO_SAI2_SCK_A_1     (GPIO_ALT|GPIO_AF10|GPIO_PORTD|GPIO_PIN13)
#define GPIO_SAI2_SCK_A_2     (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN5)
#define GPIO_SAI2_SCK_B_1     (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN12)
#define GPIO_SAI2_SCK_B_2     (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN2)
#define GPIO_SAI2_SCK_B_3     (GPIO_ALT|GPIO_AF8 |GPIO_PORTA|GPIO_PIN2)
#define GPIO_SAI2_SD_A_1      (GPIO_ALT|GPIO_AF10|GPIO_PORTD|GPIO_PIN11)
#define GPIO_SAI2_SD_A_2      (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN6)
#define GPIO_SAI2_SD_B_1      (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN0)
#define GPIO_SAI2_SD_B_2      (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN11)
#define GPIO_SAI2_SD_B_3      (GPIO_ALT|GPIO_AF10|GPIO_PORTF|GPIO_PIN11)
#define GPIO_SAI2_SD_B_4      (GPIO_ALT|GPIO_AF10|GPIO_PORTG|GPIO_PIN10)

/* SD/MMC
 *
 * Note that the below configures GPIO_SPEED_50MHz I/O, that means for using
 * the SDIO that you must enable I/O Compensation via the configuration
 * option CONFIG_STM32F7_SYSCFG_IOCOMPENSATION=y.
 */

#define GPIO_SDMMC1_CK        (GPIO_ALT|GPIO_AF12|GPIO_SPEED_50MHz|GPIO_PORTC|GPIO_PIN12)
#define GPIO_SDMMC1_CMD       (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN2)
#define GPIO_SDMMC1_D0        (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN8)
#define GPIO_SDMMC1_D1        (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN9)
#define GPIO_SDMMC1_D2        (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN10)
#define GPIO_SDMMC1_D3        (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN11)
#define GPIO_SDMMC1_D4        (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN8)
#define GPIO_SDMMC1_D5        (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN9)
#define GPIO_SDMMC1_D6        (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN6)
#define GPIO_SDMMC1_D7        (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN7)

#define GPIO_SDMMC2_CK        (GPIO_ALT|GPIO_AF11|GPIO_SPEED_50MHz|GPIO_PORTD|GPIO_PIN6)
#define GPIO_SDMMC2_CMD       (GPIO_ALT|GPIO_AF11|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN7)
#define GPIO_SDMMC2_D0_1      (GPIO_ALT|GPIO_AF10|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN14)
#define GPIO_SDMMC2_D1_1      (GPIO_ALT|GPIO_AF10|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN15)
#define GPIO_SDMMC2_D2_1      (GPIO_ALT|GPIO_AF10|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN3)
#define GPIO_SDMMC2_D3_1      (GPIO_ALT|GPIO_AF10|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN4)
#define GPIO_SDMMC2_D0_2      (GPIO_ALT|GPIO_AF11|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN9)
#define GPIO_SDMMC2_D1_2      (GPIO_ALT|GPIO_AF11|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN10)
#define GPIO_SDMMC2_D2_2      (GPIO_ALT|GPIO_AF10|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN11)
#define GPIO_SDMMC2_D3_2      (GPIO_ALT|GPIO_AF11|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN12)
#define GPIO_SDMMC2_D4        (GPIO_ALT|GPIO_AF10|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN8)
#define GPIO_SDMMC2_D5        (GPIO_ALT|GPIO_AF10|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN9)
#define GPIO_SDMMC2_D6        (GPIO_ALT|GPIO_AF10|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN6)
#define GPIO_SDMMC2_D7        (GPIO_ALT|GPIO_AF10|GPIO_PULLUP|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN7)

/* SPDIFRX */

#define GPIO_SPDIFRX_IN0_1    (GPIO_ALT|GPIO_AF7|GPIO_PORTG|GPIO_PIN11)
#define GPIO_SPDIFRX_IN0_2    (GPIO_ALT|GPIO_AF8|GPIO_PORTD|GPIO_PIN7)
#define GPIO_SPDIFRX_IN1_1    (GPIO_ALT|GPIO_AF7|GPIO_PORTG|GPIO_PIN12)
#define GPIO_SPDIFRX_IN1_2    (GPIO_ALT|GPIO_AF8|GPIO_PORTD|GPIO_PIN8)
#define GPIO_SPDIFRX_IN2_1    (GPIO_ALT|GPIO_AF7|GPIO_PORTG|GPIO_PIN8)
#define GPIO_SPDIFRX_IN2_2    (GPIO_ALT|GPIO_AF8|GPIO_PORTC|GPIO_PIN4)
#define GPIO_SPDIFRX_IN3_1    (GPIO_ALT|GPIO_AF7|GPIO_PORTG|GPIO_PIN9)
#define GPIO_SPDIFRX_IN3_2    (GPIO_ALT|GPIO_AF8|GPIO_PORTC|GPIO_PIN5)

/* SPI */

#define GPIO_SPI1_MISO_1      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTA|GPIO_PIN6)
#define GPIO_SPI1_MISO_2      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTB|GPIO_PIN4)
#define GPIO_SPI1_MISO_3      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTG|GPIO_PIN9)
#define GPIO_SPI1_MOSI_1      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTA|GPIO_PIN7)
#define GPIO_SPI1_MOSI_2      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTB|GPIO_PIN5)
#define GPIO_SPI1_MOSI_3      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTD|GPIO_PIN7)
#define GPIO_SPI1_NSS_1       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTA|GPIO_PIN15)
#define GPIO_SPI1_NSS_2       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTA|GPIO_PIN4)
#define GPIO_SPI1_NSS_3       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTG|GPIO_PIN10)
#define GPIO_SPI1_SCK_1       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTA|GPIO_PIN5)
#define GPIO_SPI1_SCK_2       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTB|GPIO_PIN3)
#define GPIO_SPI1_SCK_3       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTG|GPIO_PIN11)

#define GPIO_SPI2_MISO_1      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTB|GPIO_PIN14)
#define GPIO_SPI2_MISO_2      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTC|GPIO_PIN2)
#define GPIO_SPI2_MISO_3      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTI|GPIO_PIN2)
#define GPIO_SPI2_MOSI_1      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTB|GPIO_PIN15)
#define GPIO_SPI2_MOSI_2      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTC|GPIO_PIN3)
#define GPIO_SPI2_MOSI_3      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTI|GPIO_PIN3)
#define GPIO_SPI2_MOSI_4      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTC|GPIO_PIN1)
#define GPIO_SPI2_NSS_1       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTB|GPIO_PIN12)
#define GPIO_SPI2_NSS_2       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTB|GPIO_PIN9)
#define GPIO_SPI2_NSS_3       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTI|GPIO_PIN0)
#define GPIO_SPI2_NSS_4       (GPIO_ALT|GPIO_AF7|GPIO_SPEED_50MHz|GPIO_PORTB|GPIO_PIN4)
#define GPIO_SPI2_NSS_5       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTA|GPIO_PIN11)
#define GPIO_SPI2_SCK_1       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTA|GPIO_PIN9)
#define GPIO_SPI2_SCK_2       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTB|GPIO_PIN10)
#define GPIO_SPI2_SCK_3       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTB|GPIO_PIN13)
#define GPIO_SPI2_SCK_4       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTD|GPIO_PIN3)
#define GPIO_SPI2_SCK_5       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTI|GPIO_PIN1)
#define GPIO_SPI2_SCK_6       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTA|GPIO_PIN12)

#define GPIO_SPI3_MISO_1      (GPIO_ALT|GPIO_AF6|GPIO_SPEED_50MHz|GPIO_PORTB|GPIO_PIN4)
#define GPIO_SPI3_MISO_2      (GPIO_ALT|GPIO_AF6|GPIO_SPEED_50MHz|GPIO_PORTC|GPIO_PIN11)
#define GPIO_SPI3_MOSI_1      (GPIO_ALT|GPIO_AF7|GPIO_SPEED_50MHz|GPIO_PORTB|GPIO_PIN2)
#define GPIO_SPI3_MOSI_2      (GPIO_ALT|GPIO_AF6|GPIO_SPEED_50MHz|GPIO_PORTB|GPIO_PIN5)
#define GPIO_SPI3_MOSI_3      (GPIO_ALT|GPIO_AF6|GPIO_SPEED_50MHz|GPIO_PORTC|GPIO_PIN12)
#define GPIO_SPI3_MOSI_4      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTD|GPIO_PIN6)
#define GPIO_SPI3_NSS_1       (GPIO_ALT|GPIO_AF6|GPIO_SPEED_50MHz|GPIO_PORTA|GPIO_PIN15)
#define GPIO_SPI3_NSS_2       (GPIO_ALT|GPIO_AF6|GPIO_SPEED_50MHz|GPIO_PORTA|GPIO_PIN4)
#define GPIO_SPI3_SCK_1       (GPIO_ALT|GPIO_AF6|GPIO_SPEED_50MHz|GPIO_PORTB|GPIO_PIN3)
#define GPIO_SPI3_SCK_2       (GPIO_ALT|GPIO_AF6|GPIO_SPEED_50MHz|GPIO_PORTC|GPIO_PIN10)

#define GPIO_SPI4_MISO_1      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTE|GPIO_PIN5)
#define GPIO_SPI4_MISO_2      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTE|GPIO_PIN13)
#define GPIO_SPI4_MOSI_1      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTE|GPIO_PIN6)
#define GPIO_SPI4_MOSI_2      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTE|GPIO_PIN14)
#define GPIO_SPI4_NSS_1       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTE|GPIO_PIN4)
#define GPIO_SPI4_NSS_2       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTE|GPIO_PIN11)
#define GPIO_SPI4_SCK_1       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTE|GPIO_PIN2)
#define GPIO_SPI4_SCK_2       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTE|GPIO_PIN12)

#define GPIO_SPI5_MISO_1      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTF|GPIO_PIN8)
#define GPIO_SPI5_MISO_2      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTH|GPIO_PIN7)
#define GPIO_SPI5_MOSI_1      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTF|GPIO_PIN9)
#define GPIO_SPI5_MOSI_2      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTF|GPIO_PIN11)
#define GPIO_SPI5_NSS_1       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTF|GPIO_PIN6)
#define GPIO_SPI5_NSS_2       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTH|GPIO_PIN5)
#define GPIO_SPI5_SCK_1       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTF|GPIO_PIN7)
#define GPIO_SPI5_SCK_2       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTH|GPIO_PIN6)

#define GPIO_SPI6_MISO_1      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTG|GPIO_PIN12)
#define GPIO_SPI6_MOSI_1      (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTG|GPIO_PIN14)
#define GPIO_SPI6_NSS_1       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTG|GPIO_PIN8)
#define GPIO_SPI6_SCK_1       (GPIO_ALT|GPIO_AF5|GPIO_SPEED_50MHz|GPIO_PORTG|GPIO_PIN13)
#define GPIO_SPI6_MISO_2      (GPIO_ALT|GPIO_AF8|GPIO_SPEED_50MHz|GPIO_PORTA|GPIO_PIN6)
#define GPIO_SPI6_MOSI_2      (GPIO_ALT|GPIO_AF8|GPIO_SPEED_50MHz|GPIO_PORTA|GPIO_PIN7)
#define GPIO_SPI6_NSS_2       (GPIO_ALT|GPIO_AF8|GPIO_SPEED_50MHz|GPIO_PORTA|GPIO_PIN4)
#define GPIO_SPI6_SCK_2       (GPIO_ALT|GPIO_AF8|GPIO_SPEED_50MHz|GPIO_PORTA|GPIO_PIN5)
#define GPIO_SPI6_MISO_3      (GPIO_ALT|GPIO_AF8|GPIO_SPEED_50MHz|GPIO_PORTB|GPIO_PIN4)
#define GPIO_SPI6_MOSI_3      (GPIO_ALT|GPIO_AF8|GPIO_SPEED_50MHz|GPIO_PORTB|GPIO_PIN5)
#define GPIO_SPI6_NSS_3       (GPIO_ALT|GPIO_AF7|GPIO_SPEED_50MHz|GPIO_PORTA|GPIO_PIN15)
#define GPIO_SPI6_SCK_3       (GPIO_ALT|GPIO_AF8|GPIO_SPEED_50MHz|GPIO_PORTB|GPIO_PIN3)

/* Timers */

#define GPIO_TIM1_BKIN_1      (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM1_BKIN_2      (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN12)
#define GPIO_TIM1_BKIN_3      (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN15)
#define GPIO_TIM1_BKIN2       (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN6)
#define GPIO_TIM1_CH1NOUT_1   (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM1_CH1NOUT_2   (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN13)
#define GPIO_TIM1_CH1NOUT_3   (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN8)
#define GPIO_TIM1_CH1IN_1     (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN8)
#define GPIO_TIM1_CH1IN_2     (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTE|GPIO_PIN9)
#define GPIO_TIM1_CH1OUT_1    (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN8)
#define GPIO_TIM1_CH1OUT_2    (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN9)
#define GPIO_TIM1_CH1NOUT_1   (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM1_CH1NOUT_2   (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN13)
#define GPIO_TIM1_CH1NOUT_3   (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN8)
#define GPIO_TIM1_CH2IN_1     (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN9)
#define GPIO_TIM1_CH2IN_2     (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTE|GPIO_PIN11)
#define GPIO_TIM1_CH2OUT_1    (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN9)
#define GPIO_TIM1_CH2OUT_2    (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN11)
#define GPIO_TIM1_CH2NOUT_1   (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN0)
#define GPIO_TIM1_CH2NOUT_2   (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN14)
#define GPIO_TIM1_CH2NOUT_3   (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN10)
#define GPIO_TIM1_CH3IN_1     (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN10)
#define GPIO_TIM1_CH3IN_2     (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTE|GPIO_PIN13)
#define GPIO_TIM1_CH3OUT_1    (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN10)
#define GPIO_TIM1_CH3OUT_2    (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN13)
#define GPIO_TIM1_CH3NOUT_1   (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN1)
#define GPIO_TIM1_CH3NOUT_2   (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN15)
#define GPIO_TIM1_CH3NOUT_3   (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN12)
#define GPIO_TIM1_CH4IN_1     (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN11)
#define GPIO_TIM1_CH4IN_2     (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTE|GPIO_PIN14)
#define GPIO_TIM1_CH4OUT_1    (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN11)
#define GPIO_TIM1_CH4OUT_2    (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN14)
#define GPIO_TIM1_ETR_1       (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN12)
#define GPIO_TIM1_ETR_2       (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTE|GPIO_PIN7)

#define GPIO_TIM2_CH1IN_1     (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM2_CH1IN_2     (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN15)
#define GPIO_TIM2_CH1IN_3     (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN5)
#define GPIO_TIM2_CH1OUT_1    (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM2_CH1OUT_2    (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN15)
#define GPIO_TIM2_CH1OUT_3    (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN5)
#define GPIO_TIM2_CH2IN_1     (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN1)
#define GPIO_TIM2_CH2IN_2     (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN3)
#define GPIO_TIM2_CH2OUT_1    (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN1)
#define GPIO_TIM2_CH2OUT_2    (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN3)
#define GPIO_TIM2_CH3IN_1     (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM2_CH3IN_2     (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN10)
#define GPIO_TIM2_CH3OUT_1    (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM2_CH3OUT_2    (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN10)
#define GPIO_TIM2_CH4IN_1     (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM2_CH4IN_2     (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN11)
#define GPIO_TIM2_CH4OUT_1    (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM2_CH4OUT_2    (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN11)
#define GPIO_TIM2_ETR_1       (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM2_ETR_2       (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN15)
#define GPIO_TIM2_ETR_3       (GPIO_ALT|GPIO_AF1|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN5)

#define GPIO_TIM3_CH1IN_1     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM3_CH1IN_2     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN4)
#define GPIO_TIM3_CH1IN_3     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTC|GPIO_PIN6)
#define GPIO_TIM3_CH1OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM3_CH1OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN4)
#define GPIO_TIM3_CH1OUT_3    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN6)
#define GPIO_TIM3_CH2IN_1     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM3_CH2IN_2     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN5)
#define GPIO_TIM3_CH2IN_3     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTC|GPIO_PIN7)
#define GPIO_TIM3_CH2OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM3_CH2OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN5)
#define GPIO_TIM3_CH2OUT_3    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN7)
#define GPIO_TIM3_CH3IN_1     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN0)
#define GPIO_TIM3_CH3IN_2     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTC|GPIO_PIN8)
#define GPIO_TIM3_CH3OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN0)
#define GPIO_TIM3_CH3OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN8)
#define GPIO_TIM3_CH4IN_1     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN1)
#define GPIO_TIM3_CH4IN_2     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTC|GPIO_PIN9)
#define GPIO_TIM3_CH4OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN1)
#define GPIO_TIM3_CH4OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN9)
#define GPIO_TIM3_ETR         (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTD|GPIO_PIN2)

#define GPIO_TIM4_CH1IN_1     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN6)
#define GPIO_TIM4_CH1IN_2     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTD|GPIO_PIN12)
#define GPIO_TIM4_CH1OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN6)
#define GPIO_TIM4_CH1OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN12)
#define GPIO_TIM4_CH2IN_1     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN7)
#define GPIO_TIM4_CH2IN_2     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTD|GPIO_PIN13)
#define GPIO_TIM4_CH2OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN7)
#define GPIO_TIM4_CH2OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN13)
#define GPIO_TIM4_CH3IN_1     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN8)
#define GPIO_TIM4_CH3IN_2     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTD|GPIO_PIN14)
#define GPIO_TIM4_CH3OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN8)
#define GPIO_TIM4_CH3OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN14)
#define GPIO_TIM4_CH4IN_1     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN9)
#define GPIO_TIM4_CH4IN_2     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTD|GPIO_PIN15)
#define GPIO_TIM4_CH4OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN9)
#define GPIO_TIM4_CH4OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN15)
#define GPIO_TIM4_ETR         (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTE|GPIO_PIN0)

#define GPIO_TIM5_CH1IN_1     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM5_CH1IN_2     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTH|GPIO_PIN10)
#define GPIO_TIM5_CH1OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM5_CH1OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN10)

#define GPIO_TIM5_CH2IN_1     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN1)
#define GPIO_TIM5_CH2IN_2     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTH|GPIO_PIN11)
#define GPIO_TIM5_CH2OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN1)
#define GPIO_TIM5_CH2OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN11)

#define GPIO_TIM5_CH3IN_1     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM5_CH3IN_2     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTH|GPIO_PIN12)
#define GPIO_TIM5_CH3OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM5_CH3OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN12)

#define GPIO_TIM5_CH4IN_1     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM5_CH4IN_2     (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTI|GPIO_PIN0)
#define GPIO_TIM5_CH4OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM5_CH4OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN0)

#define GPIO_TIM8_BKIN_1      (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM8_BKIN_2      (GPIO_ALT|GPIO_AF3|GPIO_PORTI|GPIO_PIN4)
#define GPIO_TIM8_BKIN2_1     (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN8)
#define GPIO_TIM8_BKIN2_2     (GPIO_ALT|GPIO_AF3|GPIO_PORTI|GPIO_PIN1)
#define GPIO_TIM8_CH1IN_1     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTC|GPIO_PIN6)
#define GPIO_TIM8_CH1IN_2     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTI|GPIO_PIN5)
#define GPIO_TIM8_CH1OUT_1    (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN6)
#define GPIO_TIM8_CH1OUT_2    (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN5)
#define GPIO_TIM8_CH1NOUT_1   (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN5)
#define GPIO_TIM8_CH1NOUT_2   (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM8_CH1NOUT_3   (GPIO_ALT|GPIO_AF3|GPIO_PORTH|GPIO_PIN13)
#define GPIO_TIM8_CH2IN_1     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTC|GPIO_PIN7)
#define GPIO_TIM8_CH2IN_2     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTI|GPIO_PIN6)
#define GPIO_TIM8_CH2OUT_1    (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN7)
#define GPIO_TIM8_CH2OUT_2    (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN6)
#define GPIO_TIM8_CH2NOUT_1   (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN0)
#define GPIO_TIM8_CH2NOUT_2   (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN14)
#define GPIO_TIM8_CH2NOUT_3   (GPIO_ALT|GPIO_AF3|GPIO_PORTH|GPIO_PIN14)
#define GPIO_TIM8_CH3IN_1     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTC|GPIO_PIN8)
#define GPIO_TIM8_CH3IN_2     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTI|GPIO_PIN7)
#define GPIO_TIM8_CH3OUT_1    (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN8)
#define GPIO_TIM8_CH3OUT_2    (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN7)
#define GPIO_TIM8_CH3NOUT_1   (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN1)
#define GPIO_TIM8_CH3NOUT_2   (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN15)
#define GPIO_TIM8_CH3NOUT_3   (GPIO_ALT|GPIO_AF3|GPIO_PORTH|GPIO_PIN15)
#define GPIO_TIM8_CH4IN_1     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTC|GPIO_PIN9)
#define GPIO_TIM8_CH4IN_2     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTI|GPIO_PIN2)
#define GPIO_TIM8_CH4OUT_1    (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN9)
#define GPIO_TIM8_CH4OUT_2    (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN2)
#define GPIO_TIM8_ETR_1       (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM8_ETR_2       (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTI|GPIO_PIN3)

#define GPIO_TIM9_CH1IN_1     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM9_CH1IN_2     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTE|GPIO_PIN5)
#define GPIO_TIM9_CH1OUT_1    (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM9_CH1OUT_2    (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN5)
#define GPIO_TIM9_CH2IN_1     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM9_CH2IN_2     (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTE|GPIO_PIN6)
#define GPIO_TIM9_CH2OUT_1    (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM9_CH2OUT_2    (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN6)

#define GPIO_TIM10_CH1IN_1    (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN8)
#define GPIO_TIM10_CH1IN_2    (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTF|GPIO_PIN6)
#define GPIO_TIM10_CH1OUT_1   (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN8)
#define GPIO_TIM10_CH1OUT_2   (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN6)

#define GPIO_TIM11_CH1IN_1    (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN9)
#define GPIO_TIM11_CH1IN_2    (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTF|GPIO_PIN7)
#define GPIO_TIM11_CH1OUT_1   (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN9)
#define GPIO_TIM11_CH1OUT_2   (GPIO_ALT|GPIO_AF3|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN7)

#define GPIO_TIM12_CH1IN_1    (GPIO_ALT|GPIO_AF9|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN14)
#define GPIO_TIM12_CH1IN_2    (GPIO_ALT|GPIO_AF9|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTH|GPIO_PIN6)
#define GPIO_TIM12_CH1OUT_1   (GPIO_ALT|GPIO_AF9|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN14)
#define GPIO_TIM12_CH1OUT_2   (GPIO_ALT|GPIO_AF9|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN6)

#define GPIO_TIM12_CH2IN_1    (GPIO_ALT|GPIO_AF9|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN15)
#define GPIO_TIM12_CH2IN_2    (GPIO_ALT|GPIO_AF9|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTH|GPIO_PIN9)
#define GPIO_TIM12_CH2OUT_1   (GPIO_ALT|GPIO_AF9|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN15)
#define GPIO_TIM12_CH2OUT_2   (GPIO_ALT|GPIO_AF9|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN9)

#define GPIO_TIM13_CH1IN_1    (GPIO_ALT|GPIO_AF9|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM13_CH1IN_2    (GPIO_ALT|GPIO_AF9|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTF|GPIO_PIN8)
#define GPIO_TIM13_CH1OUT_1   (GPIO_ALT|GPIO_AF9|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM13_CH1OUT_2   (GPIO_ALT|GPIO_AF9|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN8)

#define GPIO_TIM14_CH1IN_1    (GPIO_ALT|GPIO_AF9|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM14_CH1IN_2    (GPIO_ALT|GPIO_AF9|GPIO_SPEED_50MHz|GPIO_FLOAT|GPIO_PORTF|GPIO_PIN9)
#define GPIO_TIM14_CH1OUT_1   (GPIO_ALT|GPIO_AF9|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM14_CH1OUT_2   (GPIO_ALT|GPIO_AF9|GPIO_SPEED_50MHz|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN9)

/* Trace */

#define GPIO_TRACECLK         (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN2)
#define GPIO_TRACED0_1        (GPIO_ALT|GPIO_AF0|GPIO_PORTC|GPIO_PIN1)
#define GPIO_TRACED0_2        (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN3)
#define GPIO_TRACED0_3        (GPIO_ALT|GPIO_AF0|GPIO_PORTG|GPIO_PIN13)
#define GPIO_TRACED1_1        (GPIO_ALT|GPIO_AF0|GPIO_PORTC|GPIO_PIN8)
#define GPIO_TRACED1_2        (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN4)
#define GPIO_TRACED1_3        (GPIO_ALT|GPIO_AF0|GPIO_PORTG|GPIO_PIN14)
#define GPIO_TRACED2_1        (GPIO_ALT|GPIO_AF0|GPIO_PORTD|GPIO_PIN2)
#define GPIO_TRACED2_2        (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN5)
#define GPIO_TRACED3_1        (GPIO_ALT|GPIO_AF0|GPIO_PORTC|GPIO_PIN12)
#define GPIO_TRACED3_2        (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN6)
#define GPIO_TRACESWO         (GPIO_ALT|GPIO_AF0|GPIO_PORTB|GPIO_PIN3)

/* UARTs/USARTs */

#define GPIO_USART1_CK        (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN8)
#define GPIO_USART1_CTS       (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN11)
#define GPIO_USART1_RTS       (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN12)
#define GPIO_USART1_RX_1      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN10)
#define GPIO_USART1_RX_2      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN7)
#define GPIO_USART1_RX_3      (GPIO_ALT|GPIO_AF4|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN15)
#define GPIO_USART1_TX_1      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN9)
#define GPIO_USART1_TX_2      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN6)
#define GPIO_USART1_TX_3      (GPIO_ALT|GPIO_AF4|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN14)

#define GPIO_USART2_CK_1      (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN4)
#define GPIO_USART2_CK_2      (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN7)
#define GPIO_USART2_CTS_1     (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN0)
#define GPIO_USART2_CTS_2     (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN3)
#define GPIO_USART2_RTS_1     (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN1)
#define GPIO_USART2_RTS_2     (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN4)
#define GPIO_USART2_RX_1      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN3)
#define GPIO_USART2_RX_2      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN6)
#define GPIO_USART2_TX_1      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN2)
#define GPIO_USART2_TX_2      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN5)

#define GPIO_USART3_CK_1      (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN12)
#define GPIO_USART3_CK_2      (GPIO_ALT|GPIO_AF7|GPIO_PORTC|GPIO_PIN12)
#define GPIO_USART3_CK_3      (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN10)
#define GPIO_USART3_CTS_1     (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN13)
#define GPIO_USART3_CTS_2     (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN11)
#define GPIO_USART3_RTS_1     (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN14)
#define GPIO_USART3_RTS_2     (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN12)
#define GPIO_USART3_RX_1      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN11)
#define GPIO_USART3_RX_2      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN11)
#define GPIO_USART3_RX_3      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN9)
#define GPIO_USART3_TX_1      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN10)
#define GPIO_USART3_TX_2      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN10)
#define GPIO_USART3_TX_3      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN8)

#define GPIO_UART4_CTS_1      (GPIO_ALT|GPIO_AF8|GPIO_PORTB|GPIO_PIN0)
#define GPIO_UART4_CTS_2      (GPIO_ALT|GPIO_AF8|GPIO_PORTB|GPIO_PIN15)
#define GPIO_UART4_RTS_1      (GPIO_ALT|GPIO_AF8|GPIO_PORTA|GPIO_PIN15)
#define GPIO_UART4_RTS_2      (GPIO_ALT|GPIO_AF8|GPIO_PORTB|GPIO_PIN14)
#define GPIO_UART4_RX_1       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN1)
#define GPIO_UART4_RX_2       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN11)
#define GPIO_UART4_RX_3       (GPIO_ALT|GPIO_AF6|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN11)
#define GPIO_UART4_RX_4       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN0)
#define GPIO_UART4_RX_5       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN14)
#define GPIO_UART4_RX_6       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN9)
#define GPIO_UART4_TX_1       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN0)
#define GPIO_UART4_TX_2       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN10)
#define GPIO_UART4_TX_3       (GPIO_ALT|GPIO_AF6|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN12)
#define GPIO_UART4_TX_4       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN1)
#define GPIO_UART4_TX_5       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN13)

#define GPIO_UART5_CTS_1      (GPIO_ALT|GPIO_AF7|GPIO_PORTC|GPIO_PIN9)
#define GPIO_UART5_RTS_1      (GPIO_ALT|GPIO_AF7|GPIO_PORTC|GPIO_PIN8)
#define GPIO_UART5_RX_1       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN2)
#define GPIO_UART5_TX_1       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN12)
#define GPIO_UART5_RX_2       (GPIO_ALT|GPIO_AF1|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN5)
#define GPIO_UART5_TX_2       (GPIO_ALT|GPIO_AF1|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN6)
#define GPIO_UART5_RX_3       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN12)
#define GPIO_UART5_TX_3       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN13)
#define GPIO_UART5_RX_4       (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN8)
#define GPIO_UART5_TX_4       (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN9)

#define GPIO_USART6_CK_1      (GPIO_ALT|GPIO_AF8|GPIO_PORTC|GPIO_PIN8)
#define GPIO_USART6_CK_2      (GPIO_ALT|GPIO_AF8|GPIO_PORTG|GPIO_PIN7)
#define GPIO_USART6_CTS_1     (GPIO_ALT|GPIO_AF8|GPIO_PORTG|GPIO_PIN13)
#define GPIO_USART6_CTS_2     (GPIO_ALT|GPIO_AF8|GPIO_PORTG|GPIO_PIN15)
#define GPIO_USART6_RTS_1     (GPIO_ALT|GPIO_AF8|GPIO_PORTG|GPIO_PIN12)
#define GPIO_USART6_RTS_2     (GPIO_ALT|GPIO_AF8|GPIO_PORTG|GPIO_PIN8)
#define GPIO_USART6_RX_1      (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN7)
#define GPIO_USART6_RX_2      (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN9)
#define GPIO_USART6_TX_1      (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN6)
#define GPIO_USART6_TX_2      (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN14)

#define GPIO_UART7_CTS_1      (GPIO_ALT|GPIO_AF8|GPIO_PORTE|GPIO_PIN10)
#define GPIO_UART7_CTS_2      (GPIO_ALT|GPIO_AF8|GPIO_PORTF|GPIO_PIN9)
#define GPIO_UART7_RTS_1      (GPIO_ALT|GPIO_AF8|GPIO_PORTE|GPIO_PIN9)
#define GPIO_UART7_RTS_2      (GPIO_ALT|GPIO_AF8|GPIO_PORTF|GPIO_PIN8)
#define GPIO_UART7_RX_1       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN7)
#define GPIO_UART7_RX_2       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN6)
#define GPIO_UART7_RX_3       (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN8)
#define GPIO_UART7_RX_4       (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN3)
#define GPIO_UART7_TX_1       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN8)
#define GPIO_UART7_TX_2       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN7)
#define GPIO_UART7_TX_3       (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN15)
#define GPIO_UART7_TX_4       (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN4)

#define GPIO_UART8_CTS        (GPIO_ALT|GPIO_AF8|GPIO_PORTD|GPIO_PIN14)
#define GPIO_UART8_RTS        (GPIO_ALT|GPIO_AF8|GPIO_PORTD|GPIO_PIN15)
#define GPIO_UART8_RX         (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN0)
#define GPIO_UART8_TX         (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_SPEED_100MHz|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN1)

#endif /* CONFIG_STM32F7_STM32F76XX || CONFIG_STM32F7_STM32F77XX */
#endif /* __ARCH_ARM_SRC_STM32F7_HARDWARE_STM32F76XX77XX_PINMAP_LEGACY_H */
