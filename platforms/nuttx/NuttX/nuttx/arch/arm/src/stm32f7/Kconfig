#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

if ARCH_CHIP_STM32F7

comment "STM32 F7 Configuration Options"

choice
	prompt "STM32 F7 Chip Selection"
	default ARCH_CHIP_STM32F746NG
	depends on ARCH_CHIP_STM32F7

config ARCH_CHIP_STM32F722RC
	bool "STM32F722RC"
	select STM32F7_STM32F722XX
	select STM32F7_FLASH_CONFIG_C
	select STM32F7_IO_CONFIG_R
	---help---
		STM32 F7 Cortex M7, 256 FLASH, 256K (176+16+64) Kb SRAM

config ARCH_CHIP_STM32F722RE
	bool "STM32F722RE"
	select STM32F7_STM32F722XX
	select STM32F7_FLASH_CONFIG_E
	select STM32F7_IO_CONFIG_R
	---help---
		STM32 F7 Cortex M7, 512 FLASH, 256K (176+16+64) Kb SRAM

config ARCH_CHIP_STM32F722VC
	bool "STM32F722VC"
	select STM32F7_STM32F722XX
	select STM32F7_FLASH_CONFIG_C
	select STM32F7_IO_CONFIG_V
	---help---
		STM32 F7 Cortex M7, 256 FLASH, 256K (176+16+64) Kb SRAM

config ARCH_CHIP_STM32F722VE
	bool "STM32F722VE"
	select STM32F7_STM32F722XX
	select STM32F7_FLASH_CONFIG_E
	select STM32F7_IO_CONFIG_V
	---help---
		STM32 F7 Cortex M7, 512 FLASH, 256K (176+16+64) Kb SRAM

config ARCH_CHIP_STM32F722ZC
	bool "STM32F722ZC"
	select STM32F7_STM32F722XX
	select STM32F7_FLASH_CONFIG_C
	select STM32F7_IO_CONFIG_Z
	---help---
		STM32 F7 Cortex M7, 256 FLASH, 256K (176+16+64) Kb SRAM

config ARCH_CHIP_STM32F722ZE
	bool "STM32F722ZE"
	select STM32F7_STM32F722XX
	select STM32F7_FLASH_CONFIG_E
	select STM32F7_IO_CONFIG_Z
	---help---
		STM32 F7 Cortex M7, 512 FLASH, 256K (176+16+64) Kb SRAM

config ARCH_CHIP_STM32F722IC
	bool "STM32F722IC"
	select STM32F7_STM32F722XX
	select STM32F7_FLASH_CONFIG_C
	select STM32F7_IO_CONFIG_I
	---help---
		STM32 F7 Cortex M7, 256 FLASH, 256K (176+16+64) Kb SRAM

config ARCH_CHIP_STM32F722IE
	bool "STM32F722IE"
	select STM32F7_STM32F722XX
	select STM32F7_FLASH_CONFIG_E
	select STM32F7_IO_CONFIG_I
	---help---
		STM32 F7 Cortex M7, 512 FLASH, 256K (176+16+64) Kb SRAM

config ARCH_CHIP_STM32F723RC
	bool "STM32F723RC"
	select STM32F7_STM32F723XX
	select STM32F7_FLASH_CONFIG_C
	select STM32F7_IO_CONFIG_R
	---help---
		STM32 F7 Cortex M7, 256 FLASH, 256K (176+16+64) Kb SRAM

config ARCH_CHIP_STM32F723RE
	bool "STM32F723RE"
	select STM32F7_STM32F723XX
	select STM32F7_FLASH_CONFIG_E
	select STM32F7_IO_CONFIG_R
	---help---
		STM32 F7 Cortex M7, 512 FLASH, 256K (176+16+64) Kb SRAM

config ARCH_CHIP_STM32F723VC
	bool "STM32F723VC"
	select STM32F7_STM32F723XX
	select STM32F7_HAVE_INTERNAL_ULPI
	select STM32F7_FLASH_CONFIG_C
	select STM32F7_IO_CONFIG_V
	---help---
		STM32 F7 Cortex M7, 256 FLASH, 256K (176+16+64) Kb SRAM

config ARCH_CHIP_STM32F723VE
	bool "STM32F723VE"
	select STM32F7_STM32F723XX
	select STM32F7_HAVE_INTERNAL_ULPI
	select STM32F7_FLASH_CONFIG_E
	select STM32F7_IO_CONFIG_V
	---help---
		STM32 F7 Cortex M7, 512 FLASH, 256K (176+16+64) Kb SRAM

config ARCH_CHIP_STM32F723ZC
	bool "STM32F723ZC"
	select STM32F7_STM32F723XX
	select STM32F7_HAVE_INTERNAL_ULPI
	select STM32F7_FLASH_CONFIG_C
	select STM32F7_IO_CONFIG_Z
	---help---
		STM32 F7 Cortex M7, 256 FLASH, 256K (176+16+64) Kb SRAM

config ARCH_CHIP_STM32F723ZE
	bool "STM32F723ZE"
	select STM32F7_STM32F723XX
	select STM32F7_HAVE_INTERNAL_ULPI
	select STM32F7_FLASH_CONFIG_E
	select STM32F7_IO_CONFIG_Z
	---help---
		STM32 F7 Cortex M7, 512 FLASH, 256K (176+16+64) Kb SRAM

config ARCH_CHIP_STM32F723IC
	bool "STM32F723IC"
	select STM32F7_STM32F723XX
	select STM32F7_HAVE_INTERNAL_ULPI
	select STM32F7_FLASH_CONFIG_C
	select STM32F7_IO_CONFIG_I
	---help---
		STM32 F7 Cortex M7, 256 FLASH, 256K (176+16+64) Kb SRAM

config ARCH_CHIP_STM32F723IE
	bool "STM32F723IE"
	select STM32F7_STM32F723XX
	select STM32F7_HAVE_INTERNAL_ULPI
	select STM32F7_FLASH_CONFIG_E
	select STM32F7_IO_CONFIG_I
	---help---
		STM32 F7 Cortex M7, 512 FLASH, 256K (176+16+64) Kb SRAM

config ARCH_CHIP_STM32F745VG
	bool "STM32F745VG"
	select STM32F7_STM32F745XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_V
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F745VE
	bool "STM32F745VE"
	select STM32F7_STM32F745XX
	select STM32F7_FLASH_CONFIG_E
	select STM32F7_IO_CONFIG_V
	---help---
		STM32 F7 Cortex M7, 512 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F745IG
	bool "STM32F745IG"
	select STM32F7_STM32F745XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_I
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F745IE
	bool "STM32F745IE"
	select STM32F7_STM32F745XX
	select STM32F7_FLASH_CONFIG_E
	select STM32F7_IO_CONFIG_I
	---help---
		STM32 F7 Cortex M7, 512 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F745ZE
	bool "STM32F745ZE"
	select STM32F7_STM32F745XX
	select STM32F7_FLASH_CONFIG_E
	select STM32F7_IO_CONFIG_Z
	---help---
		STM32 F7 Cortex M7, 512 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F745ZG
	bool "STM32F745ZG"
	select STM32F7_STM32F745XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_Z
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F746BG
	bool "STM32F746BG"
	select STM32F7_STM32F746XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_B
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F746VG
	bool "STM32F746VG"
	select STM32F7_STM32F746XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_V
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F746VE
	bool "STM32F746VE"
	select STM32F7_STM32F746XX
	select STM32F7_FLASH_CONFIG_E
	select STM32F7_IO_CONFIG_V
	---help---
		STM32 F7 Cortex M7, 512 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F746BE
	bool "STM32F746BE"
	select STM32F7_STM32F746XX
	select STM32F7_FLASH_CONFIG_E
	select STM32F7_IO_CONFIG_B
	---help---
		STM32 F7 Cortex M7, 512 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F746ZG
	bool "STM32F746ZG"
	select STM32F7_STM32F746XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_Z
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F746IE
	bool "STM32F746IE"
	select STM32F7_STM32F746XX
	select STM32F7_FLASH_CONFIG_E
	select STM32F7_IO_CONFIG_I
	---help---
		STM32 F7 Cortex M7, 512 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F746NG
	bool "STM32F746NG"
	select STM32F7_STM32F746XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_N
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F746NE
	bool "STM32F746NE"
	select STM32F7_STM32F746XX
	select STM32F7_FLASH_CONFIG_E
	select STM32F7_IO_CONFIG_N
	---help---
		STM32 F7 Cortex M7, 512 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F746ZE
	bool "STM32F746ZE"
	select STM32F7_STM32F746XX
	select STM32F7_FLASH_CONFIG_E
	select STM32F7_IO_CONFIG_Z
	---help---
		STM32 F7 Cortex M7, 512 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F746IG
	bool "STM32F746IG"
	select STM32F7_STM32F746XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_I
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F756NG
	bool "STM32F756NG"
	select STM32F7_STM32F756XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_N
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F756BG
	bool "STM32F756BG"
	select STM32F7_STM32F756XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_B
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F756IG
	bool "STM32F756IG"
	select STM32F7_STM32F756XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_I
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F756VG
	bool "STM32F756VG"
	select STM32F7_STM32F756XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_V
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F756ZG
	bool "STM32F756ZG"
	select STM32F7_STM32F756XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_Z
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 320K (240+16+64) Kb SRAM

config ARCH_CHIP_STM32F765NI
	bool "STM32F765NI"
	select STM32F7_STM32F765XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_N
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F765VI
	bool "STM32F765VI"
	select STM32F7_STM32F765XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_V
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F765VG
	bool "STM32F765VG"
	select STM32F7_STM32F765XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_V
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F765BI
	bool "STM32F765BI"
	select STM32F7_STM32F765XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_B
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F765NG
	bool "STM32F765NG"
	select STM32F7_STM32F765XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_N
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F765ZG
	bool "STM32F765ZG"
	select STM32F7_STM32F765XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_Z
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F765ZI
	bool "STM32F765ZI"
	select STM32F7_STM32F765XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_Z
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F765IG
	bool "STM32F765IG"
	select STM32F7_STM32F765XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_I
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F765BG
	bool "STM32F765BG"
	select STM32F7_STM32F765XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_B
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F765II
	bool "STM32F765II"
	select STM32F7_STM32F765XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_I
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F767NG
	bool "STM32F767NG"
	select STM32F7_STM32F767XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_N
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F767IG
	bool "STM32F767IG"
	select STM32F7_STM32F767XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_I
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F767VG
	bool "STM32F767VG"
	select STM32F7_STM32F767XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_V
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F767ZG
	bool "STM32F767ZG"
	select STM32F7_STM32F767XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_Z
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F767NI
	bool "STM32F767NI"
	select STM32F7_STM32F767XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_N
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F767VI
	bool "STM32F767VI"
	select STM32F7_STM32F767XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_V
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F767BG
	bool "STM32F767BG"
	select STM32F7_STM32F767XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_B
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F767ZI
	bool "STM32F767ZI"
	select STM32F7_STM32F767XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_Z
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F767II
	bool "STM32F767II"
	select STM32F7_STM32F767XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_I
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F769BI
	bool "STM32F769BI"
	select STM32F7_STM32F769XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_B
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F769II
	bool "STM32F769II"
	select STM32F7_STM32F769XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_I
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F769BG
	bool "STM32F769BG"
	select STM32F7_STM32F769XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_B
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F769NI
	bool "STM32F769NI"
	select STM32F7_STM32F769XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_N
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F769AI
	bool "STM32F769AI"
	select STM32F7_STM32F769AX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_A
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F769NG
	bool "STM32F769NG"
	select STM32F7_STM32F769XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_N
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F769IG
	bool "STM32F769IG"
	select STM32F7_STM32F769XX
	select STM32F7_FLASH_CONFIG_G
	select STM32F7_IO_CONFIG_I
	---help---
		STM32 F7 Cortex M7, 1024 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F777ZI
	bool "STM32F777ZI"
	select STM32F7_STM32F777XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_Z
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F777VI
	bool "STM32F777VI"
	select STM32F7_STM32F777XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_V
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F777NI
	bool "STM32F777NI"
	select STM32F7_STM32F777XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_N
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F777BI
	bool "STM32F777BI"
	select STM32F7_STM32F777XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_B
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F777II
	bool "STM32F777II"
	select STM32F7_STM32F777XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_I
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F778AI
	bool "STM32F778AI"
	select STM32F7_STM32F778AX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_A
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F779II
	bool "STM32F779II"
	select STM32F7_STM32F779XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_I
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F779NI
	bool "STM32F779NI"
	select STM32F7_STM32F779XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_N
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F779BI
	bool "STM32F779BI"
	select STM32F7_STM32F779XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_B
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

config ARCH_CHIP_STM32F779AI
	bool "STM32F779AI"
	select STM32F7_STM32F779XX
	select STM32F7_FLASH_CONFIG_I
	select STM32F7_IO_CONFIG_A
	---help---
		STM32 F7 Cortex M7, 2048 FLASH, 512K (368+16+128) Kb SRAM

endchoice # STM32 F7 Chip Selection

config STM32F7_STM32F72XX
	bool
	default n

config STM32F7_STM32F73XX
	bool
	default n

config STM32F7_STM32F74XX
	bool
	default n

config STM32F7_STM32F75XX
	bool
	default n

config STM32F7_STM32F76XX
	bool
	default n

config STM32F7_STM32F77XX
	bool
	default n

config STM32F7_IO_CONFIG_R
	bool
	default n

config STM32F7_IO_CONFIG_V
	bool
	default n

config STM32F7_IO_CONFIG_I
	bool
	default n

config STM32F7_IO_CONFIG_Z
	bool
	default n

config STM32F7_IO_CONFIG_N
	bool
	default n

config STM32F7_IO_CONFIG_B
	bool
	default n

config STM32F7_IO_CONFIG_A
	bool
	default n

config STM32F7_STM32F722XX
	bool
	default n
	select STM32F7_STM32F72XX
	select ARCH_HAVE_FPU
	select ARMV7M_HAVE_ICACHE
	select ARMV7M_HAVE_DCACHE
	select ARMV7M_HAVE_ITCM
	select ARMV7M_HAVE_DTCM
	select STM32F7_HAVE_FMC
	select STM32F7_HAVE_RNG
	select STM32F7_HAVE_SPI4 if !STM32F7_IO_CONFIG_R
	select STM32F7_HAVE_SPI5 if !(STM32F7_IO_CONFIG_R || STM32F7_IO_CONFIG_V)
	select STM32F7_HAVE_CRYP
	select STM32F7_HAVE_SDMMC2 if !STM32F7_IO_CONFIG_R
	select STM32F7_HAVE_EXTERNAL_ULPI

config STM32F7_STM32F723XX
	bool
	default n
	select STM32F7_STM32F72XX
	select ARCH_HAVE_FPU
	select ARMV7M_HAVE_ICACHE
	select ARMV7M_HAVE_DCACHE
	select ARMV7M_HAVE_ITCM
	select ARMV7M_HAVE_DTCM
	select STM32F7_HAVE_FMC
	select STM32F7_HAVE_RNG
	select STM32F7_HAVE_SPI4 if !STM32F7_IO_CONFIG_R
	select STM32F7_HAVE_SPI5 if !(STM32F7_IO_CONFIG_R || STM32F7_IO_CONFIG_V)
	select STM32F7_HAVE_CRYP
	select STM32F7_HAVE_SDMMC2 if !STM32F7_IO_CONFIG_R

config STM32F7_STM32F745XX
	bool
	default n
	select STM32F7_STM32F74XX
	select ARCH_HAVE_FPU
	select ARMV7M_HAVE_ICACHE
	select ARMV7M_HAVE_DCACHE
	select ARMV7M_HAVE_ITCM
	select ARMV7M_HAVE_DTCM
	select STM32F7_HAVE_FMC
	select STM32F7_HAVE_ETHRNET
	select STM32F7_HAVE_RNG
	select STM32F7_HAVE_SPI5 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_SPI6 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_DCMI
	select STM32F7_HAVE_DMA2D
	select STM32F7_HAVE_CAN2
	select STM32F7_HAVE_SPI4

config STM32F7_STM32F746XX
	bool
	default n
	select STM32F7_STM32F74XX
	select ARCH_HAVE_FPU
	select ARMV7M_HAVE_ICACHE
	select ARMV7M_HAVE_DCACHE
	select ARMV7M_HAVE_ITCM
	select ARMV7M_HAVE_DTCM
	select STM32F7_HAVE_FMC
	select STM32F7_HAVE_ETHRNET
	select STM32F7_HAVE_RNG
	select STM32F7_HAVE_SPI5 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_SPI6 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_DCMI
	select STM32F7_HAVE_LTDC
	select STM32F7_HAVE_DMA2D
	select STM32F7_HAVE_CAN2
	select STM32F7_HAVE_SPI4
	select STM32F7_HAVE_EXTERNAL_ULPI
	select STM32F7_HAVE_SAI1
	select STM32F7_HAVE_SAI2

config STM32F7_STM32F756XX
	bool
	default n
	select STM32F7_STM32F75XX
	select ARCH_HAVE_FPU
	select ARMV7M_HAVE_ICACHE
	select ARMV7M_HAVE_DCACHE
	select ARMV7M_HAVE_ITCM
	select ARMV7M_HAVE_DTCM
	select STM32F7_HAVE_FMC
	select STM32F7_HAVE_ETHRNET
	select STM32F7_HAVE_RNG
	select STM32F7_HAVE_SPI5 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_SPI6 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_LTDC
	select STM32F7_HAVE_DMA2D
	select STM32F7_HAVE_CRYP
	select STM32F7_HAVE_HASH
	select STM32F7_HAVE_CAN2
	select STM32F7_HAVE_SPI4

config STM32F7_STM32F765XX
	bool
	default n
	select STM32F7_STM32F76XX
	select ARCH_HAVE_FPU
	select ARCH_HAVE_DPFPU
	select ARMV7M_HAVE_ICACHE
	select ARMV7M_HAVE_DCACHE
	select ARMV7M_HAVE_ITCM
	select ARMV7M_HAVE_DTCM
	select STM32F7_HAVE_FMC
	select STM32F7_HAVE_ETHRNET
	select STM32F7_HAVE_RNG # data sheet says yes, Product matrix says no
	select STM32F7_HAVE_SPI5 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_SPI6 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_SDMMC2 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_CAN3
	select STM32F7_HAVE_DCMI
	select STM32F7_HAVE_DMA2D
	select STM32F7_HAVE_DFSDM1
	select STM32F7_HAVE_CAN2
	select STM32F7_HAVE_SPI4

config STM32F7_STM32F767XX
	bool
	default n
	select STM32F7_STM32F76XX
	select ARCH_HAVE_FPU
	select ARCH_HAVE_DPFPU
	select ARMV7M_HAVE_ICACHE
	select ARMV7M_HAVE_DCACHE
	select ARMV7M_HAVE_ITCM
	select ARMV7M_HAVE_DTCM
	select STM32F7_HAVE_FMC
	select STM32F7_HAVE_ETHRNET
	select STM32F7_HAVE_RNG
	select STM32F7_HAVE_SPI5 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_SPI6 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_SDMMC2 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_CAN3
	select STM32F7_HAVE_DCMI
	select STM32F7_HAVE_DSIHOST if !(STM32F7_IO_CONFIG_V || STM32F7_IO_CONFIG_Z)
	select STM32F7_HAVE_LTDC
	select STM32F7_HAVE_DMA2D
	select STM32F7_HAVE_JPEG
	select STM32F7_HAVE_DFSDM1
	select STM32F7_HAVE_CAN2
	select STM32F7_HAVE_SPI4

config STM32F7_STM32F768XX # Revisit When parts released
	bool
	default n
	select STM32F7_STM32F76XX
	select ARCH_HAVE_FPU
	select ARCH_HAVE_DPFPU
	select ARMV7M_HAVE_ICACHE
	select ARMV7M_HAVE_DCACHE
	select ARMV7M_HAVE_ITCM
	select ARMV7M_HAVE_DTCM
	select STM32F7_HAVE_FMC
	select STM32F7_HAVE_ETHRNET
	select STM32F7_HAVE_RNG
	select STM32F7_HAVE_SPI5 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_SPI6 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_SDMMC2 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_CAN3
	select STM32F7_HAVE_DCMI
	select STM32F7_HAVE_DSIHOST if !(STM32F7_IO_CONFIG_V || STM32F7_IO_CONFIG_Z)
	select STM32F7_HAVE_LTDC
	select STM32F7_HAVE_DMA2D
	select STM32F7_HAVE_JPEG
	select STM32F7_HAVE_DFSDM1
	select STM32F7_HAVE_CAN2
	select STM32F7_HAVE_SPI4

config STM32F7_STM32F768AX # Revisit When parts released
	bool
	default n
	select STM32F7_STM32F76XX
	select ARCH_HAVE_FPU
	select ARCH_HAVE_DPFPU
	select ARMV7M_HAVE_ICACHE
	select ARMV7M_HAVE_DCACHE
	select ARMV7M_HAVE_ITCM
	select ARMV7M_HAVE_DTCM
	select STM32F7_HAVE_FMC
	select STM32F7_HAVE_RNG
	select STM32F7_HAVE_SPI5
	select STM32F7_HAVE_SPI6
	select STM32F7_HAVE_SDMMC2
	select STM32F7_HAVE_CAN3
	select STM32F7_HAVE_DCMI
	select STM32F7_HAVE_DSIHOST
	select STM32F7_HAVE_LTDC
	select STM32F7_HAVE_DMA2D
	select STM32F7_HAVE_JPEG
	select STM32F7_HAVE_DFSDM1
	select STM32F7_HAVE_CAN2
	select STM32F7_HAVE_SPI4

config STM32F7_STM32F769XX
	bool
	default n
	select STM32F7_STM32F76XX
	select ARCH_HAVE_FPU
	select ARCH_HAVE_DPFPU
	select ARMV7M_HAVE_ICACHE
	select ARMV7M_HAVE_DCACHE
	select ARMV7M_HAVE_ITCM
	select ARMV7M_HAVE_DTCM
	select STM32F7_HAVE_FMC
	select STM32F7_HAVE_ETHRNET
	select STM32F7_HAVE_RNG
	select STM32F7_HAVE_SPI5 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_SPI6 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_SDMMC2 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_CAN3
	select STM32F7_HAVE_DCMI
	select STM32F7_HAVE_DSIHOST if !(STM32F7_IO_CONFIG_V || STM32F7_IO_CONFIG_Z)
	select STM32F7_HAVE_LTDC
	select STM32F7_HAVE_DMA2D
	select STM32F7_HAVE_JPEG
	select STM32F7_HAVE_DFSDM1
	select STM32F7_HAVE_CAN2
	select STM32F7_HAVE_SPI4

config STM32F7_STM32F769AX # Revisit When parts released
	bool
	default n
	select STM32F7_STM32F76XX
	select ARCH_HAVE_FPU
	select ARCH_HAVE_DPFPU
	select ARMV7M_HAVE_ICACHE
	select ARMV7M_HAVE_DCACHE
	select ARMV7M_HAVE_ITCM
	select ARMV7M_HAVE_DTCM
	select STM32F7_HAVE_FMC
	select STM32F7_HAVE_RNG
	select STM32F7_HAVE_SPI5
	select STM32F7_HAVE_SPI6
	select STM32F7_HAVE_SDMMC2
	select STM32F7_HAVE_CAN3
	select STM32F7_HAVE_DCMI
	select STM32F7_HAVE_DSIHOST
	select STM32F7_HAVE_LTDC
	select STM32F7_HAVE_DMA2D
	select STM32F7_HAVE_JPEG
	select STM32F7_HAVE_DFSDM1
	select STM32F7_HAVE_CAN2
	select STM32F7_HAVE_SPI4

config STM32F7_STM32F777XX
	bool
	default n
	select STM32F7_STM32F77XX
	select ARCH_HAVE_FPU
	select ARCH_HAVE_DPFPU
	select ARMV7M_HAVE_ICACHE
	select ARMV7M_HAVE_DCACHE
	select ARMV7M_HAVE_ITCM
	select ARMV7M_HAVE_DTCM
	select STM32F7_HAVE_FMC
	select STM32F7_HAVE_ETHRNET
	select STM32F7_HAVE_RNG
	select STM32F7_HAVE_SPI5 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_SPI6 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_SDMMC2 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_CAN3
	select STM32F7_HAVE_DCMI
	select STM32F7_HAVE_DSIHOST if !(STM32F7_IO_CONFIG_V || STM32F7_IO_CONFIG_Z)
	select STM32F7_HAVE_LTDC
	select STM32F7_HAVE_DMA2D
	select STM32F7_HAVE_JPEG
	select STM32F7_HAVE_CRYP
	select STM32F7_HAVE_HASH
	select STM32F7_HAVE_DFSDM1
	select STM32F7_HAVE_CAN2
	select STM32F7_HAVE_SPI4

config STM32F7_STM32F778XX # Revisit when parts released
	bool
	default n
	select STM32F7_STM32F77XX
	select ARCH_HAVE_FPU
	select ARCH_HAVE_DPFPU
	select ARMV7M_HAVE_ICACHE
	select ARMV7M_HAVE_DCACHE
	select ARMV7M_HAVE_ITCM
	select ARMV7M_HAVE_DTCM
	select STM32F7_HAVE_FMC
	select STM32F7_HAVE_ETHRNET
	select STM32F7_HAVE_RNG
	select STM32F7_HAVE_SPI5 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_SPI6 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_SDMMC2 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_CAN3
	select STM32F7_HAVE_DCMI
	select STM32F7_HAVE_DSIHOST
	select STM32F7_HAVE_LTDC
	select STM32F7_HAVE_DMA2D
	select STM32F7_HAVE_JPEG
	select STM32F7_HAVE_CRYP
	select STM32F7_HAVE_HASH
	select STM32F7_HAVE_DFSDM1
	select STM32F7_HAVE_CAN2
	select STM32F7_HAVE_SPI4

config STM32F7_STM32F778AX
	bool
	default n
	select STM32F7_STM32F77XX
	select ARCH_HAVE_FPU
	select ARCH_HAVE_DPFPU
	select ARMV7M_HAVE_ICACHE
	select ARMV7M_HAVE_DCACHE
	select ARMV7M_HAVE_ITCM
	select ARMV7M_HAVE_DTCM
	select STM32F7_HAVE_FMC
	select STM32F7_HAVE_RNG
	select STM32F7_HAVE_SPI5
	select STM32F7_HAVE_SPI6
	select STM32F7_HAVE_SDMMC2
	select STM32F7_HAVE_CAN3
	select STM32F7_HAVE_DCMI
	select STM32F7_HAVE_DSIHOST
	select STM32F7_HAVE_LTDC
	select STM32F7_HAVE_DMA2D
	select STM32F7_HAVE_JPEG
	select STM32F7_HAVE_CRYP
	select STM32F7_HAVE_HASH
	select STM32F7_HAVE_DFSDM1
	select STM32F7_HAVE_CAN2
	select STM32F7_HAVE_SPI4

config STM32F7_STM32F779XX
	bool
	default n
	select STM32F7_STM32F77XX
	select ARCH_HAVE_FPU
	select ARCH_HAVE_DPFPU
	select ARMV7M_HAVE_ICACHE
	select ARMV7M_HAVE_DCACHE
	select ARMV7M_HAVE_ITCM
	select ARMV7M_HAVE_DTCM
	select STM32F7_HAVE_FMC
	select STM32F7_HAVE_ETHRNET
	select STM32F7_HAVE_RNG
	select STM32F7_HAVE_SPI5 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_SPI6 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_SDMMC2 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_CAN3
	select STM32F7_HAVE_DCMI
	select STM32F7_HAVE_DSIHOST if !(STM32F7_IO_CONFIG_V || STM32F7_IO_CONFIG_Z)
	select STM32F7_HAVE_LTDC
	select STM32F7_HAVE_DMA2D
	select STM32F7_HAVE_JPEG
	select STM32F7_HAVE_CRYP
	select STM32F7_HAVE_HASH
	select STM32F7_HAVE_DFSDM1
	select STM32F7_HAVE_CAN2
	select STM32F7_HAVE_SPI4

config STM32F7_STM32F779AX
	bool
	default n
	select STM32F7_STM32F77XX
	select ARCH_HAVE_FPU
	select ARCH_HAVE_DPFPU
	select ARMV7M_HAVE_ICACHE
	select ARMV7M_HAVE_DCACHE
	select ARMV7M_HAVE_ITCM
	select ARMV7M_HAVE_DTCM
	select STM32F7_HAVE_FMC
	select STM32F7_HAVE_RNG
	select STM32F7_HAVE_SPI5 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_SPI6 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_SDMMC2 if !STM32F7_IO_CONFIG_V
	select STM32F7_HAVE_CAN3
	select STM32F7_HAVE_DCMI
	select STM32F7_HAVE_DSIHOST if !(STM32F7_IO_CONFIG_V || STM32F7_IO_CONFIG_Z)
	select STM32F7_HAVE_LTDC
	select STM32F7_HAVE_DMA2D
	select STM32F7_HAVE_JPEG
	select STM32F7_HAVE_CRYP
	select STM32F7_HAVE_HASH
	select STM32F7_HAVE_DFSDM1
	select STM32F7_HAVE_CAN2
	select STM32F7_HAVE_SPI4

config STM32F7_FLASH_CONFIG_E
	bool
	default n

config STM32F7_FLASH_CONFIG_G
	bool
	default n

config STM32F7_FLASH_CONFIG_I
	bool
	default n

choice
	prompt "Override Flash Size Designator"
	depends on ARCH_CHIP_STM32F7
	default STM32F7_FLASH_OVERRIDE_DEFAULT
	---help---
		STM32F7 series parts numbering (sans the package type) ends with a letter
		that designates the FLASH size.

				Designator  Size in KiB
				   C	256
				   E	512
				   G	1024
				   I	2048

		This configuration option defaults to using the configuration based on that designator
		or the default smaller size if there is no last character designator is present in the
		STM32 Chip Selection.

		Examples:
		   If the STM32F745VE is chosen, the Flash configuration would be 'E', if a variant of
		   the part with a 2048 KiB Flash is released in the future one could simply select
		   the 'I' designator here.

		   If an STM32F7xxx Series parts is chosen the default Flash configuration will be set
		   herein and can be changed.

config STM32F7_FLASH_OVERRIDE_DEFAULT
			bool "Default"

config STM32F7_FLASH_OVERRIDE_C
			bool "C 256KiB"

config STM32F7_FLASH_OVERRIDE_E
			bool "E 512KiB"

config STM32F7_FLASH_OVERRIDE_G
			bool "G 1024KiB"

config STM32F7_FLASH_OVERRIDE_I
			bool "I 2048KiB"

endchoice # "Override Flash Size Designator"

config STM32F7_FLASH_ART_ACCELERATOR
	bool "Flash ART Accelerator"
	default n
	---help---
		ART Accelerator on the flash memory ITCM interface accelerates code execution
		with a system of instruction prefetch and cache lines.

		Enable if code and/or read-only data is accessed through ITCM bus instead of
		AXIM bus.

config STM32F7_PROGMEM
	bool "Flash progmem support"
	default n
	---help---
		Add progmem support, start block and end block options are provided to
		obtain an uniform flash memory mapping.

menu "STM32 Peripheral Support"

# These "hidden" settings determine whether a peripheral option is available
# for the selected MCU

config STM32F7_HAVE_LTDC
	bool
	default n

config STM32F7_HAVE_FMC
	bool
	default n

config STM32F7_HAVE_ETHRNET
	bool
	default n

config STM32F7_HAVE_PHY_POLLED
	bool
	default n

config STM32F7_HAVE_RNG
	bool
	default n

config STM32F7_HAVE_SPI4
	bool
	default n

config STM32F7_HAVE_SPI5
	bool
	default n

config STM32F7_HAVE_SPI6
	bool
	default n

config STM32F7_HAVE_SDMMC2
	bool
	default n

config STM32F7_HAVE_ADC1_DMA
	bool
	default n

config STM32F7_HAVE_ADC2_DMA
	bool
	default n

config STM32F7_HAVE_ADC3_DMA
	bool
	default n

config STM32F7_HAVE_CAN2
	bool
	default n

config STM32F7_HAVE_CAN3
	bool
	default n

config STM32F7_HAVE_DCMI
	bool
	default n

config STM32F7_HAVE_DSIHOST
	bool
	default n

config STM32F7_HAVE_LTDC
	bool
	default n

config STM32F7_HAVE_DMA2D
	bool
	default n

config STM32F7_HAVE_JPEG
	bool
	default n

config STM32F7_HAVE_CRYP
	bool
	default n

config STM32F7_HAVE_HASH
	bool
	default n

config STM32F7_HAVE_DFSDM1
	bool
	default n

config STM32F7_HAVE_INTERNAL_ULPI
	bool
	default n

config STM32F7_HAVE_EXTERNAL_ULPI
	bool
	default n

config STM32F7_HAVE_SAI1
	bool
	default n

config STM32F7_HAVE_SAI2
	bool
	default n

# These "hidden" settings are the OR of individual peripheral selections
# indicating that the general capability is required.

config STM32F7_ADC
	bool
	default n

config STM32F7_CAN
	bool
	default n

config STM32F7_DAC
	bool
	default n

config STM32F7_DMA
	bool
	default n

config STM32F7_I2C
	bool
	default n

config STM32F7_SAI
	bool
	default n

config STM32F7_SDMMC
	bool
	default n

config STM32F7_SPI
	bool
	default n

config STM32F7_SPI_DMA
	bool
	default n

config STM32F7_TIM
	bool
	default n

config STM32F7_PWM
	bool
	default n
	select ARCH_HAVE_PWM_PULSECOUNT

config STM32F7_USART
	bool
	default n

# These are the peripheral selections proper

config STM32F7_ADC1
	bool "ADC1"
	default n
	select STM32F7_ADC
	select STM32F7_HAVE_ADC1_DMA if STM32F7_DMA2

config STM32F7_ADC2
	bool "ADC2"
	default n
	select STM32F7_ADC
	select STM32F7_HAVE_ADC2_DMA if STM32F7_DMA2

config STM32F7_ADC3
	bool "ADC3"
	default n
	select STM32F7_ADC
	select STM32F7_HAVE_ADC3_DMA if STM32F7_DMA2

config STM32F7_BKPSRAM
	bool "Enable BKP RAM Domain"
	default n

config STM32F7_CAN1
	bool "CAN1"
	default n
	select CAN
	select STM32F7_CAN

config STM32F7_CAN2
	bool "CAN2"
	default n
	select CAN
	select STM32F7_CAN

config STM32F7_CAN3
	bool "CAN3"
	default n
	select CAN
	select STM32F7_CAN
	depends on STM32F7_HAVE_CAN3

config STM32F7_CEC
	bool "CEC"
	default n
	depends on STM32F7_VALUELINE

config STM32F7_CRC
	bool "CRC"
	default n

config STM32F7_CRYP
	bool "CRYP"
	depends on STM32F7_HAVE_CRYP
	default n

config STM32F7_DFSDM1
	bool "DFSDM1"
	default n
	depends on STM32F7_HAVE_DFSDM1
	select ARCH_HAVE_DFSDM1

config STM32F7_DMA1
	bool "DMA1"
	default n
	select STM32F7_DMA
	select ARCH_DMA

config STM32F7_DMA2
	bool "DMA2"
	default n
	select STM32F7_DMA
	select ARCH_DMA

config STM32F7_DAC1
	bool "DAC1"
	default n
	select STM32F7_DAC

config STM32F7_DAC2
	bool "DAC2"
	default n
	select STM32F7_DAC

config STM32F7_DCMI
	bool "DCMI"
	default n
	depends on STM32F7_HAVE_DCMI
	---help---
		The devices embed a camera interface that can connect with camera
		modules and CMOS sensors through an 8-bit to 14-bit parallel interface,
		to receive video data.

config STM32F7_DSIHOST
	bool "DSIHOST"
	default n
	depends on STM32F7_HAVE_DSIHOST
	---help---
		The DSI Host is a dedicated peripheral for interfacing with MIPI® DSI
		compliant displays.

config STM32F7_DMA2D
	bool "DMA2D"
	default n
	select FB
	select FB_OVERLAY
	depends on STM32F7_HAVE_DMA2D
	---help---
		The STM32 DMA2D is an Chrom-Art Accelerator for image manipulation
		available on the STM32 F7 devices.

config STM32F7_JPEG
	bool "JPEG"
	default n
	depends on STM32F7_HAVE_JPEG
	---help---
		The JPEG codec provides an fast and simple hardware compressor and
		decompressor of JPEG images with full management of JPEG headers.

config STM32F7_ETHMAC
	bool "Ethernet MAC"
	default n
	depends on STM32F7_HAVE_ETHRNET
	select NETDEVICES
	select ARCH_HAVE_PHY
	select STM32F7_HAVE_PHY_POLLED

config STM32F7_FMC
	bool "FMC"
	depends on STM32F7_HAVE_FMC
	default n

config STM32F7_HASH
	bool "HASH"
	default n
	depends on STM32F7_HAVE_HASH
	select ARCH_HAVE_HASH

config STM32F7_CEC
	bool "HDMI-CEC"
	default n

config STM32F7_I2C1
	bool "I2C1"
	default n
	select STM32F7_I2C

config STM32F7_I2C2
	bool "I2C2"
	default n
	select STM32F7_I2C

config STM32F7_I2C3
	bool "I2C3"
	default n
	select STM32F7_I2C

config STM32F7_I2C4
	bool "I2C4"
	default n
	select STM32F7_I2C

config STM32F7_LPTIM1
	bool "Low-power timer 1"
	default n

config STM32F7_LTDC
	bool "LTDC"
	default n
	select FB
	depends on STM32F7_HAVE_LTDC
	---help---
		The STM32 LTDC is an LCD-TFT Display Controller available on
		the STM32F7x6, STM32F7x7, STM32F7x8 and STM32F7x9 devices.
		It features a standard RGB888 parallel video interface (along
		with HSYNC, VSYNC, etc.) for controlling TFT LCD displays.
		With the STM32F7x8/9, the graphics signals can optionally
		be output via DSI instead of the parallel interface:
		See config options STM32F7_DSIHOST and STM32F7_LTDC_USE_DSI.

config STM32F7_OTGFS
	bool "OTG FS"
	default n
	select USBHOST_HAVE_ASYNCH if USBHOST

config STM32F7_OTGFSHS
	bool "OTG FS/HS"
	default n
	select USBHOST_HAVE_ASYNCH if USBHOST

config STM32F7_QUADSPI
	bool "QuadSPI"
	default n

config STM32F7_RTC
	bool "RTC"
	default n
	select RTC

config STM32F7_PWR
	bool "PWR"
	default n

config STM32F7_RNG
	bool "RNG"
	default n
	depends on STM32F7_HAVE_RNG
	select ARCH_HAVE_RNG

config STM32F7_SAI1
	bool "SAI1"
	default n
	depends on STM32F7_HAVE_SAI1

config STM32F7_SAI1_A
	bool "SAI1 Block A"
	default n
	select AUDIO
	select I2S
	select SCHED_HPWORK
	select STM32F7_SAI
	depends on STM32F7_SAI1

config STM32F7_SAI1_B
	bool "SAI1 Block B"
	default n
	select AUDIO
	select I2S
	select SCHED_HPWORK
	select STM32F7_SAI
	depends on STM32F7_SAI1

config STM32F7_SAI2
	bool "SAI2"
	default n
	select STM32F7_HAVE_SAI2

config STM32F7_SAI2_A
	bool "SAI2 Block A"
	default n
	select AUDIO
	select I2S
	select SCHED_HPWORK
	select STM32F7_SAI
	depends on STM32F7_SAI2

config STM32F7_SAI2_B
	bool "SAI2 Block B"
	default n
	select AUDIO
	select I2S
	select SCHED_HPWORK
	select STM32F7_SAI
	depends on STM32F7_SAI2

config STM32F7_SDMMC1
	bool "SDMMC1"
	default n
	select STM32F7_SDMMC
	select ARCH_HAVE_SDIO
	select ARCH_HAVE_SDIOWAIT_WRCOMPLETE
	select ARCH_HAVE_SDIO_PREFLIGHT
	select SDIO_BLOCKSETUP

config STM32F7_SDMMC2
	bool "SDMMC2"
	default n
	depends on STM32F7_HAVE_SDMMC2
	select STM32F7_SDMMC
	select ARCH_HAVE_SDIO
	select ARCH_HAVE_SDIOWAIT_WRCOMPLETE
	select ARCH_HAVE_SDIO_PREFLIGHT
	select SDIO_BLOCKSETUP

config STM32F7_SPDIFRX
	bool "SPDIFRX"
	default n

config STM32F7_SPI1
	bool "SPI1"
	default n
	select SPI
	select STM32F7_SPI

config STM32F7_SPI2
	bool "SPI2"
	default n
	select SPI
	select STM32F7_SPI

config STM32F7_SPI3
	bool "SPI3"
	default n
	select SPI
	select STM32F7_SPI

config STM32F7_SPI4
	bool "SPI4"
	default n
	depends on STM32F7_HAVE_SPI4
	select SPI
	select STM32F7_SPI

config STM32F7_SPI5
	bool "SPI5"
	default n
	depends on STM32F7_HAVE_SPI5
	select SPI
	select STM32F7_SPI

config STM32F7_SPI6
	bool "SPI6"
	default n
	depends on STM32F7_HAVE_SPI6
	select SPI
	select STM32F7_SPI

config STM32F7_TIM1
	bool "TIM1"
	default n
	select STM32F7_TIM

config STM32F7_TIM2
	bool "TIM2"
	default n
	select STM32F7_TIM

config STM32F7_TIM3
	bool "TIM3"
	default n
	select STM32F7_TIM

config STM32F7_TIM4
	bool "TIM4"
	default n
	select STM32F7_TIM

config STM32F7_TIM5
	bool "TIM5"
	default n
	select STM32F7_TIM

config STM32F7_TIM6
	bool "TIM6"
	default n
	select STM32F7_TIM

config STM32F7_TIM7
	bool "TIM7"
	default n
	select STM32F7_TIM

config STM32F7_TIM8
	bool "TIM8"
	default n
	select STM32F7_TIM

config STM32F7_TIM9
	bool "TIM9"
	default n
	select STM32F7_TIM

config STM32F7_TIM10
	bool "TIM10"
	default n
	select STM32F7_TIM

config STM32F7_TIM11
	bool "TIM11"
	default n
	select STM32F7_TIM

config STM32F7_TIM12
	bool "TIM12"
	default n
	select STM32F7_TIM

config STM32F7_TIM13
	bool "TIM13"
	default n
	select STM32F7_TIM

config STM32F7_TIM14
	bool "TIM14"
	default n
	select STM32F7_TIM

config STM32F7_USART1
	bool "USART1"
	default n
	select USART1_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS
	select STM32F7_USART

config STM32F7_USART2
	bool "USART2"
	default n
	select USART2_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS
	select STM32F7_USART

config STM32F7_USART3
	bool "USART3"
	default n
	select ARCH_HAVE_SERIAL_TERMIOS
	select USART3_SERIALDRIVER
	select STM32F7_USART

config STM32F7_UART4
	bool "UART4"
	default n
	select ARCH_HAVE_SERIAL_TERMIOS
	select UART4_SERIALDRIVER
	select STM32F7_USART

config STM32F7_UART5
	bool "UART5"
	default n
	select ARCH_HAVE_SERIAL_TERMIOS
	select UART5_SERIALDRIVER
	select STM32F7_USART

config STM32F7_USART6
	bool "USART6"
	default n
	select ARCH_HAVE_SERIAL_TERMIOS
	select USART6_SERIALDRIVER
	select STM32F7_USART

config STM32F7_UART7
	bool "UART7"
	default n
	select ARCH_HAVE_SERIAL_TERMIOS
	select UART7_SERIALDRIVER
	select STM32F7_USART

config STM32F7_UART8
	bool "UART8"
	default n
	select ARCH_HAVE_SERIAL_TERMIOS
	select UART8_SERIALDRIVER
	select STM32F7_USART

config STM32F7_IWDG
	bool "IWDG"
	default n
	select WATCHDOG

config STM32F7_WWDG
	bool "WWDG"
	default n
	select WATCHDOG

endmenu

config STM32F7_USE_LEGACY_PINMAP
	bool "Use the legacy pinmap with GPIO_SPEED_xxx included."
	default y
	---help---
		In the past, pinmap files included GPIO_SPEED_xxxMhz. These speed
		settings should have come from the board.h as it describes the wiring
		of the SoC to the board. The speed is really slew rate control and
		therefore is related to the layout and can only be properly set
		in board.h.

		STM32F7_USE_LEGACY_PINMAP is provided, to allow lazy migration to
		using pinmaps without speeds. The work required to do this can be aided
		by running tools/stm32_pinmap_tool.py. The tools will take a board.h
		file and a legacy pinmap and output the required changes that one needs
		to make to a board.h file.

		Eventually, STM32F7_USE_LEGACY_PINMAP will be deprecated and the legacy
		pinmaps removed from NuttX. Any new boards added should set
		STM32F7_USE_LEGACY_PINMAP=n and fully define the pins in board.h


config STM32F7_SYSCFG_IOCOMPENSATION
	bool "SYSCFG I/O Compensation"
	default n
	---help---
		By default the I/O compensation cell is not used. However when the I/O
		output buffer speed is configured in 50 MHz or 100 MHz mode, it is
		recommended to use the compensation cell for slew rate control on I/O
		tf(IO)out)/tr(IO)out commutation to reduce the I/O noise on power supply.

		The I/O compensation cell can be used only when the supply voltage ranges
		from 2.4 to 3.6 V.

menu "OTG Configuration"
	depends on STM32F7_OTGFS

config OTG_ID_GPIO_DISABLE
	bool "Disable the use of GPIO_OTG_ID pin."
	default n
	---help---
		Disables/Enables the use of GPIO_OTG_ID pin. This allows non OTG use
		cases to reuse this GPIO pin and ensure it is not set incorrectlty
		during OS boot.

endmenu

menu "U[S]ART Configuration"
	depends on STM32F7_USART

config USART1_RS485
	bool "RS-485 on USART1"
	default n
	depends on STM32F7_USART1
	---help---
		Enable RS-485 interface on USART1. Your board config will have to
		provide GPIO_USART1_RS485_DIR pin definition. Currently it cannot be
		used with USART1_RXDMA.

config USART1_RS485_DIR_POLARITY
	int "USART1 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on USART1_RS485
	---help---
		Polarity of DIR pin for RS-485 on USART1. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config USART1_RXDMA
	bool "USART1 Rx DMA"
	default n
	depends on STM32F7_USART1 && STM32F7_DMA2
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

config USART1_TXDMA
	bool "USART1 Tx DMA"
	default n
	depends on STM32F7_USART1 && STM32F7_DMA2
	---help---
		In high data rate usage, Rx DMA may reduce CPU Load

config USART2_RS485
	bool "RS-485 on USART2"
	default n
	depends on STM32F7_USART2
	---help---
		Enable RS-485 interface on USART2. Your board config will have to
		provide GPIO_USART2_RS485_DIR pin definition. Currently it cannot be
		used with USART2_RXDMA.

config USART2_RS485_DIR_POLARITY
	int "USART2 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on USART2_RS485
	---help---
		Polarity of DIR pin for RS-485 on USART2. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config USART2_RXDMA
	bool "USART2 Rx DMA"
	default n
	depends on STM32F7_USART2 && STM32F7_DMA1
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

config USART2_TXDMA
	bool "USART2 Tx DMA"
	default n
	depends on STM32F7_USART2 && STM32F7_DMA1
	---help---
		In high data rate usage, Rx DMA may reduce CPU Load

config USART3_RS485
	bool "RS-485 on USART3"
	default n
	depends on STM32F7_USART3
	---help---
		Enable RS-485 interface on USART3. Your board config will have to
		provide GPIO_USART3_RS485_DIR pin definition. Currently it cannot be
		used with USART3_RXDMA.

config USART3_RS485_DIR_POLARITY
	int "USART3 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on USART3_RS485
	---help---
		Polarity of DIR pin for RS-485 on USART3. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config USART3_RXDMA
	bool "USART3 Rx DMA"
	default n
	depends on STM32F7_USART3 && STM32F7_DMA1
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

config USART3_TXDMA
	bool "USART3 Tx DMA"
	default n
	depends on STM32F7_USART3 && STM32F7_DMA1
	---help---
		In high data rate usage, Rx DMA may reduce CPU Load

config UART4_RS485
	bool "RS-485 on UART4"
	default n
	depends on STM32F7_UART4
	---help---
		Enable RS-485 interface on UART4. Your board config will have to
		provide GPIO_UART4_RS485_DIR pin definition. Currently it cannot be
		used with UART4_RXDMA.

config UART4_RS485_DIR_POLARITY
	int "UART4 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on UART4_RS485
	---help---
		Polarity of DIR pin for RS-485 on UART4. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config UART4_RXDMA
	bool "UART4 Rx DMA"
	default n
	depends on STM32F7_UART4 && STM32F7_DMA1
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

config UART4_TXDMA
	bool "UART4 Tx DMA"
	default n
	depends on STM32F7_UART4 && STM32F7_DMA1
	---help---
		In high data rate usage, Rx DMA may reduce CPU Load

config UART5_RS485
	bool "RS-485 on UART5"
	default n
	depends on STM32F7_UART5
	---help---
		Enable RS-485 interface on UART5. Your board config will have to
		provide GPIO_UART5_RS485_DIR pin definition. Currently it cannot be
		used with UART5_RXDMA.

config UART5_RS485_DIR_POLARITY
	int "UART5 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on UART5_RS485
	---help---
		Polarity of DIR pin for RS-485 on UART5. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config UART5_RXDMA
	bool "UART5 Rx DMA"
	default n
	depends on STM32F7_UART5 && STM32F7_DMA1
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

config UART5_TXDMA
	bool "UART5 Tx DMA"
	default n
	depends on STM32F7_UART5 && STM32F7_DMA1
	---help---
		In high data rate usage, Rx DMA may reduce CPU Load

config USART6_RS485
	bool "RS-485 on USART6"
	default n
	depends on STM32F7_USART6
	---help---
		Enable RS-485 interface on USART6. Your board config will have to
		provide GPIO_USART6_RS485_DIR pin definition. Currently it cannot be
		used with USART6_RXDMA.

config USART6_RS485_DIR_POLARITY
	int "USART6 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on USART6_RS485
	---help---
		Polarity of DIR pin for RS-485 on USART6. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config USART6_RXDMA
	bool "USART6 Rx DMA"
	default n
	depends on STM32F7_USART6 && STM32F7_DMA2
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

config USART6_TXDMA
	bool "USART6 Tx DMA"
	default n
	depends on STM32F7_USART6 && STM32F7_DMA2
	---help---
		In high data rate usage, Rx DMA may reduce CPU Load

config UART7_RS485
	bool "RS-485 on UART7"
	default n
	depends on STM32F7_UART7
	---help---
		Enable RS-485 interface on UART7. Your board config will have to
		provide GPIO_UART7_RS485_DIR pin definition. Currently it cannot be
		used with UART7_RXDMA.

config UART7_RS485_DIR_POLARITY
	int "UART7 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on UART7_RS485
	---help---
		Polarity of DIR pin for RS-485 on UART7. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config UART7_RXDMA
	bool "UART7 Rx DMA"
	default n
	depends on STM32F7_UART7 && STM32F7_DMA1
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

config UART7_TXDMA
	bool "UART7 Tx DMA"
	default n
	depends on STM32F7_UART7 && STM32F7_DMA1
	---help---
		In high data rate usage, Rx DMA may reduce CPU Load

config UART8_RS485
	bool "RS-485 on UART8"
	default n
	depends on STM32F7_UART8
	---help---
		Enable RS-485 interface on UART8. Your board config will have to
		provide GPIO_UART8_RS485_DIR pin definition. Currently it cannot be
		used with UART8_RXDMA.

config UART8_RS485_DIR_POLARITY
	int "UART8 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on UART8_RS485
	---help---
		Polarity of DIR pin for RS-485 on UART8. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config UART8_RXDMA
	bool "UART8 Rx DMA"
	default n
	depends on STM32F7_UART8 && STM32F7_DMA1
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

config UART8_TXDMA
	bool "UART8 Tx DMA"
	default n
	depends on STM32F7_UART8 && STM32F7_DMA1
	---help---
		In high data rate usage, Rx DMA may reduce CPU Load

config STM32F7_SERIAL_RXDMA_BUFFER_SIZE
	int "Rx DMA buffer size"
	default 32
	depends on USART1_RXDMA || USART2_RXDMA || USART3_RXDMA || UART4_RXDMA || UART5_RXDMA || USART6_RXDMA || UART7_RXDMA || UART8_RXDMA
	---help---
		The DMA buffer size when using RX DMA to emulate a FIFO.

		When streaming data, the generic serial layer will be called
		every time the FIFO receives half this number of bytes.

		Value given here will be rounded up to next multiple of 32 bytes.

config STM32F7_SERIAL_DISABLE_REORDERING
	bool "Disable reordering of ttySx devices."
	depends on STM32F7_USART1 || STM32F7_USART2 || STM32F7_USART3 || STM32F7_UART4 || STM32F7_UART5 || STM32F7_USART6 || STM32F7_UART7 || STM32F7_UART8
	default n
	---help---
		NuttX per default reorders the serial ports (/dev/ttySx) so that the
		console is always on /dev/ttyS0. If more than one UART is in use this
		can, however, have the side-effect that all port mappings
		(hardware USART1 -> /dev/ttyS0) change if the console is moved to another
		UART. This is in particular relevant if a project uses the USB console
		in some boards and a serial console in other boards, but does not
		want the side effect of having all serial port names change when just
		the console is moved from serial to USB.

config STM32F7_FLOWCONTROL_BROKEN
	bool "Use Software UART RTS flow control"
	depends on STM32F7_USART && SERIAL_IFLOWCONTROL_WATERMARKS
	default n
	---help---
		Enable UART RTS flow control using Software. Because STM
		Current STM32 have broken HW based RTS behavior (they assert
		nRTS after every byte received)  Enable this setting workaround
		this issue by using software based management of RTS

config STM32F7_USART_BREAKS
	bool "Add TIOxSBRK to support sending Breaks"
	depends on STM32F7_USART
	default n
	---help---
		Add TIOCxBRK routines to send a line break per the STM32 manual, the
		break will be a pulse based on the value M. This is not a BSD compatible
		break.

config STM32F7_SERIALBRK_BSDCOMPAT
	bool "Use GPIO To send Break"
	depends on STM32F7_USART && STM32F7_USART_BREAKS
	default n
	---help---
		Enable using GPIO on the TX pin to send a BSD compatible break:
		TIOCSBRK will start the break and TIOCCBRK will end the break.
		The current STM32 U[S]ARTS have no way to leave the break (TX=LOW)
		on because the SW starts the break and then the HW automatically clears
		the break. This makes it is difficult to sent a long break.

config STM32F7_USART_SINGLEWIRE
	bool "Single Wire Support"
	default n
	depends on STM32F7_USART
	---help---
		Enable single wire UART support.  The option enables support for the
		TIOCSSINGLEWIRE ioctl in the STM32F7 serial driver.

config STM32F7_USART_INVERT
	bool "Signal Invert Support"
	default n
	depends on STM32F7_USART
	---help---
		Enable signal inversion UART support. The option enables support for the
		TIOCSINVERT ioctl in the STM32F7 serial driver.

config STM32F7_USART_SWAP
	bool "Swap RX/TX pins support"
	default n
	depends on STM32F7_USART
	---help---
		Enable RX/TX pin swapping support. The option enables support for the
		TIOCSSWAP ioctl in the STM32F7 serial driver.

if PM

config STM32F7_PM_SERIAL_ACTIVITY
	int "PM serial activity"
	default 10
	---help---
		PM activity reported to power management logic on every serial
		interrupt.

endif

endmenu # U[S]ART Configuration

menu "STM32F7_OTG_HS Configuration"
	depends on STM32F7_OTGFSHS

choice
	prompt "ULPI Selection"
	default STM32F7_NO_ULPI

config STM32F7_NO_ULPI
	bool "No External ULPI"
	---help---
	Select to enable the presence of an external ULPI PHY

config STM32F7_EXTERNAL_ULPI
	bool "External ULPI"
	depends on STM32F7_HAVE_EXTERNAL_ULPI
	---help---
		Select to enable the presence of an external ULPI PHY

config STM32F7_INTERNAL_ULPI
	bool "Internal ULPI PHY"
	depends on STM32F7_HAVE_INTERNAL_ULPI
	---help---
		Select to enable the internal ULPI for USB HS
endchoice #"ULPI Selection"

endmenu # OTG_HS Config

config STM32F7_EXTERNAL_RAM
	bool "External RAM on FMC"
	default n
	depends on STM32F7_FMC
	select ARCH_HAVE_HEAP2
	---help---
		In addition to internal SDRAM, external RAM may be available through the FMC.

menu "QuadSPI Configuration"
	depends on STM32F7_QUADSPI

config STM32F7_QSPI_FLASH_SIZE
	int "Size of attached serial flash, bytes"
	default 16777216
	range 1 2147483648
	---help---
		The STM32F7 QSPI peripheral requires the size of the Flash be specified

config STM32F7_QSPI_FIFO_THESHOLD
	int "Number of bytes before asserting FIFO threshold flag"
	default 4
	range 1 16
	---help---
		The STM32F7 QSPI peripheral requires that the FIFO threshold be specified
		I would leave it at the default value of 4 unless you know what you are doing.

config STM32F7_QSPI_CSHT
	int "Number of cycles Chip Select must be inactive between transactions"
	default 1
	range 1 8
	---help---
		The STM32F7 QSPI peripheral requires that it be specified the minimum number
		of AHB cycles that Chip Select be held inactive between transactions.

choice
	prompt "Transfer technique"
	default STM32F7_QSPI_DMA
	---help---
		You can choose between using polling, interrupts, or DMA to transfer data
		over the QSPI interface.

config STM32F7_QSPI_POLLING
	bool "Polling"
	---help---
		Use conventional register I/O with status polling to transfer data.

config STM32F7_QSPI_INTERRUPTS
	bool "Interrupts"
	---help---
		User interrupt driven I/O transfers.

config STM32F7_QSPI_DMA
	bool "DMA"
	depends on STM32F7_DMA
	---help---
		Use DMA to improve QSPI transfer performance.

endchoice

choice
	prompt "Bank selection"
	default STM32F7_QSPI_MODE_BANK1
	---help---
		You can choose between using polling, interrupts, or DMA to transfer data
		over the QSPI interface.

config STM32F7_QSPI_MODE_BANK1
	bool "Bank 1"

config STM32F7_QSPI_MODE_BANK2
	bool "Bank 2"

config STM32F7_QSPI_MODE_DUAL
	bool "Dual Bank"

endchoice

choice
	prompt "DMA Priority"
	default STM32F7_QSPI_DMAPRIORITY_MEDIUM
	depends on STM32F7_DMA
	---help---
		The DMA controller supports priority levels.  You are probably fine
		with the default of 'medium' except for special cases.  In the event
		of contention between to channels at the same priority, the lower
		numbered channel has hardware priority over the higher numbered one.

config STM32F7_QSPI_DMAPRIORITY_VERYHIGH
	bool "Very High priority"
	depends on STM32F7_DMA
	---help---
		'Highest' priority.

config STM32F7_QSPI_DMAPRIORITY_HIGH
	bool "High priority"
	depends on STM32F7_DMA
	---help---
		'High' priority.

config STM32F7_QSPI_DMAPRIORITY_MEDIUM
	bool "Medium priority"
	depends on STM32F7_DMA
	---help---
		'Medium' priority.

config STM32F7_QSPI_DMAPRIORITY_LOW
	bool "Low priority"
	depends on STM32F7_DMA
	---help---
		'Low' priority.

endchoice

config STM32F7_QSPI_DMATHRESHOLD
	int "QSPI DMA threshold"
	default 4
	depends on STM32F7_QSPI_DMA
	---help---
		When QSPI DMA is enabled, small DMA transfers will still be performed
		by polling logic.  This value is the threshold below which transfers
		will still be performed by conventional register status polling.

config STM32F7_QSPI_DMADEBUG
	bool "QSPI DMA transfer debug"
	depends on STM32F7_QSPI_DMA && DEBUG_SPI && DEBUG_DMA
	default n
	---help---
		Enable special debug instrumentation to analyze QSPI DMA data transfers.
		This logic is as non-invasive as possible:  It samples DMA
		registers at key points in the data transfer and then dumps all of
		the registers at the end of the transfer.

config STM32F7_QSPI_REGDEBUG
	bool "QSPI Register level debug"
	depends on DEBUG_SPI_INFO
	default n
	---help---
		Output detailed register-level QSPI device debug information.
		Requires also CONFIG_DEBUG_SPI_INFO.

endmenu

menu "SPI Configuration"
	depends on STM32F7_SPI

config STM32F7_SPI_INTERRUPTS
	bool "Interrupt driver SPI"
	default n
	---help---
		Select to enable interrupt driven SPI support. Non-interrupt-driven,
		poll-waiting is recommended if the interrupt rate would be to high in
		the interrupt driven case.

config STM32F7_SPI_DMATHRESHOLD
	int "SPI DMA threshold"
	default 4
	depends on STM32F7_SPI_DMA
	---help---
		When SPI DMA is enabled, small DMA transfers will still be performed
		by polling logic.  But we need a threshold value to determine what
		is small.

config STM32F7_SPI1_DMA
	bool "SPI1 DMA"
	default n
	depends on STM32F7_SPI1 && !STM32F7_SPI_INTERRUPT
	select STM32F7_SPI_DMA
	---help---
		Use DMA to improve SPI1 transfer performance.  Cannot be used with STM32F7_SPI_INTERRUPT.

config STM32F7_SPI1_DMA_BUFFER
	int "SPI1 DMA buffer size"
	default 0
	depends on STM32F7_SPI1_DMA
	---help---
		Add a properly aligned DMA buffer for RX and TX DMA for SPI1.

config STM32F7_SPI2_DMA
	bool "SPI2 DMA"
	default n
	depends on STM32F7_SPI2 && !STM32F7_SPI_INTERRUPT
	select STM32F7_SPI_DMA
	---help---
		Use DMA to improve SPI2 transfer performance.  Cannot be used with STM32F7_SPI_INTERRUPT.

config STM32F7_SPI2_DMA_BUFFER
	int "SPI2 DMA buffer size"
	default 0
	depends on STM32F7_SPI2_DMA
	---help---
		Add a properly aligned DMA buffer for RX and TX DMA for SPI2.

config STM32F7_SPI3_DMA
	bool "SPI3 DMA"
	default n
	depends on STM32F7_SPI3 && !STM32F7_SPI_INTERRUPT
	select STM32F7_SPI_DMA
	---help---
		Use DMA to improve SPI3 transfer performance.  Cannot be used with STM32F7_SPI_INTERRUPT.

config STM32F7_SPI3_DMA_BUFFER
	int "SPI3 DMA buffer size"
	default 0
	depends on STM32F7_SPI3_DMA
	---help---
		Add a properly aligned DMA buffer for RX and TX DMA for SPI3.

config STM32F7_SPI4_DMA
	bool "SPI4 DMA"
	default n
	depends on STM32F7_SPI4 && !STM32F7_SPI_INTERRUPT
	select STM32F7_SPI_DMA
	---help---
		Use DMA to improve SPI4 transfer performance.  Cannot be used with STM32F7_SPI_INTERRUPT.

config STM32F7_SPI4_DMA_BUFFER
	int "SPI4 DMA buffer size"
	default 0
	depends on STM32F7_SPI4_DMA
	---help---
		Add a properly aligned DMA buffer for RX and TX DMA for SPI4.

config STM32F7_SPI5_DMA
	bool "SPI5 DMA"
	default n
	depends on STM32F7_SPI5 && !STM32F7_SPI_INTERRUPT
	select STM32F7_SPI_DMA
	---help---
		Use DMA to improve SPI5 transfer performance.  Cannot be used with STM32F7_SPI_INTERRUPT.

config STM32F7_SPI5_DMA_BUFFER
	int "SPI5 DMA buffer size"
	default 0
	depends on STM32F7_SPI5_DMA
	---help---
		Add a properly aligned DMA buffer for RX and TX DMA for SPI5.

config STM32F7_SPI6_DMA
	bool "SPI6 DMA"
	default n
	depends on STM32F7_SPI6 && !STM32F7_SPI_INTERRUPT
	select STM32F7_SPI_DMA
	---help---
		Use DMA to improve SPI6 transfer performance.  Cannot be used with STM32F7_SPI_INTERRUPT.

config STM32F7_SPI6_DMA_BUFFER
	int "SPI6 DMA buffer size"
	default 0
	depends on STM32F7_SPI6_DMA
	---help---
		Add a properly aligned DMA buffer for RX and TX DMA for SPI6.

endmenu # "SPI Configuration"

menu "I2C Configuration"
	depends on STM32F7_I2C

config STM32F7_I2C_DYNTIMEO
	bool "Use dynamic timeouts"
	default n
	depends on STM32F7_I2C

config STM32F7_I2C_DYNTIMEO_USECPERBYTE
	int "Timeout Microseconds per Byte"
	default 500
	depends on STM32F7_I2C_DYNTIMEO

config STM32F7_I2C_DYNTIMEO_STARTSTOP
	int "Timeout for Start/Stop (Milliseconds)"
	default 1000
	depends on STM32F7_I2C_DYNTIMEO

config STM32F7_I2CTIMEOSEC
	int "Timeout seconds"
	default 0
	depends on STM32F7_I2C

config STM32F7_I2CTIMEOMS
	int "Timeout Milliseconds"
	default 500
	depends on STM32F7_I2C && !STM32F7_I2C_DYNTIMEO

config STM32F7_I2CTIMEOTICKS
	int "Timeout for Done and Stop (ticks)"
	default 500
	depends on STM32F7_I2C && !STM32F7_I2C_DYNTIMEO

endmenu # "I2C Configuration"

menu "SD/MMC Configuration"
	depends on STM32F7_SDMMC

config STM32F7_SDMMC_XFRDEBUG
	bool "SDMMC transfer debug"
	depends on DEBUG_FS_INFO
	default n
	---help---
		Enable special debug instrumentation analyze SDMMC data transfers.
		This logic is as non-invasive as possible:  It samples SDMMC
		registers at key points in the data transfer and then dumps all of
		the registers at the end of the transfer.  If DEBUG_DMA is also
		enabled, then DMA register will be collected as well.  Requires also
		DEBUG_FS and CONFIG_DEBUG_INFO.

config STM32F7_SDMMC_DMA
	bool "Support DMA data transfers"
	default n
	select SDIO_DMA
	depends on STM32F7_DMA
	---help---
		Support DMA data transfers.

menu "SDMMC1 Configuration"
	depends on STM32F7_SDMMC1

config STM32F7_SDMMC1_DMAPRIO
	hex "SDMMC1 DMA priority"
	default 0x00010000
	---help---
		Select SDMMC1 DMA priority.

		Options are: 0x00000000 low, 0x00010000 medium,
		0x00020000 high, 0x00030000 very high.  Default: medium.

config SDMMC1_WIDTH_D1_ONLY
	bool "Use D1 only on SDMMC1"
	default n
	---help---
		Select 1-bit transfer mode.  Default: 4-bit transfer mode.

config SDMMC1_SDIO_MODE
	bool "SDIO Card Support"
	default n
	---help---
		Build in additional support needed only for SDIO cards (vs. SD
		memory cards)

config SDMMC1_SDIO_PULLUP
	bool "Enable internal Pull-Ups"
	default n
	---help---
		If you are using an external SDCard module that does not have the
		pull-up resistors for the SDIO interface (like the Gadgeteer SD Card
		Module) then enable this option to activate the internal pull-up
		resistors.

endmenu # "SDMMC1 Configuration"

menu "SDMMC2 Configuration"
	depends on STM32F7_SDMMC2

config STM32F7_SDMMC2_DMAPRIO
	hex "SDMMC2 DMA priority"
	default 0x00010000
	---help---
		Select SDMMC2 DMA priority.

		Options are: 0x00000000 low, 0x00010000 medium,
		0x00020000 high, 0x00030000 very high.  Default: medium.

config SDMMC2_WIDTH_D1_ONLY
	bool "Use D1 only on SDMMC2"
	default n
	---help---
		Select 1-bit transfer mode.  Default: 4-bit transfer mode.

config SDMMC2_SDIO_MODE
	bool "SDIO Card Support"
	default n
	---help---
		Build in additional support needed only for SDIO cards (vs. SD
		memory cards)

config SDMMC2_SDIO_PULLUP
	bool "Enable internal Pull-Ups"
	default n
	---help---
		If you are using an external SDCard module that does not have the
		pull-up resistors for the SDIO interface (like the Gadgeteer SD Card
		Module) then enable this option to activate the internal pull-up
		resistors.

endmenu # "SDMMC2 Configuration"
endmenu # "SD/MMC Configuration"

if STM32F7_BKPSRAM

config STM32F7_BBSRAM
	bool "BBSRAM File Support"
	default n

config STM32F7_BBSRAM_FILES
	int "Max Files to support in BBSRAM"
	default 4
	depends on STM32F7_BBSRAM

config STM32F7_SAVE_CRASHDUMP
	bool "Enable Saving Panic to BBSRAM"
	default n
	depends on STM32F7_BBSRAM

endif # STM32F7_BKPSRAM

config STM32F7_HAVE_RTC_SUBSECONDS
	bool
	select ARCH_HAVE_RTC_SUBSECONDS
	default y

menu "RTC Configuration"
	depends on STM32F7_RTC

config STM32F7_RTC_MAGIC_REG
	int "BKP register"
	default 0
	range  0 31
	---help---
		The BKP register used to store/check the Magic value to determine if
		RTC is already setup

config STM32F7_RTC_MAGIC
	hex "RTC Magic 1"
	default 0xfacefeed
	---help---
		Value used as Magic to determine if the RTC is already setup

config STM32F7_RTC_MAGIC_TIME_SET
	hex "RTC Magic 2"
	default 0xf00dface
	---help---
		Value used as Magic to determine if the RTC has been setup and has
		time set

choice
	prompt "RTC clock source"
	default STM32F7_RTC_LSECLOCK

config STM32F7_RTC_HSECLOCK
	bool "HSE clock"
	---help---
		Drive the RTC with the HSE clock, divided down to 1MHz.

config STM32F7_RTC_LSECLOCK
	bool "LSE clock"
	---help---
		Drive the RTC with the LSE clock

config STM32F7_RTC_LSICLOCK
	bool "LSI clock"
	---help---
		Drive the RTC with the LSI clock

endchoice #"RTC clock source"

if STM32F7_RTC_LSECLOCK

config STM32F7_RTC_AUTO_LSECLOCK_START_DRV_CAPABILITY
	bool "Automatically boost the LSE oscillator drive capability level until it starts-up"
	default n
	---help---
		This will cycle through the values from low to high. To avoid
		damaging the the crystal. We want to use the lowest setting that gets
		the OSC running. See app note AN2867

			0 = Low drive capability (default)
			1 = Medium high drive capability
			2 = Medium low drive capability
			3 = High drive capability

config STM32F7_RTC_LSECLOCK_START_DRV_CAPABILITY
	int "LSE oscillator drive capability level at LSE start-up"
	default 0
	range 0 3
	depends on !STM32F7_RTC_AUTO_LSECLOCK_START_DRV_CAPABILITY
	---help---
		0 = Low drive capability (default)
		1 = Medium high drive capability
		2 = Medium low drive capability
		3 = High drive capability

config STM32F7_RTC_LSECLOCK_RUN_DRV_CAPABILITY
	int "LSE oscillator drive capability level after LSE start-up"
	default 0
	range 0 3
	depends on !STM32F7_RTC_AUTO_LSECLOCK_START_DRV_CAPABILITY
	---help---
		0 = Low drive capability (default)
		1 = Medium high drive capability
		2 = Medium low drive capability
		3 = High drive capability

endif # STM32F7_RTC_LSECLOCK

endmenu # RTC Configuration

config STM32F7_CUSTOM_CLOCKCONFIG
	bool "Custom clock configuration"
	default n
	---help---
		Enables special, board-specific STM32 clock configuration.

config STM32F7_DTCMEXCLUDE
	bool "Exclude DTCM SRAM from the heap"
	default y if LIBC_ARCH_ELF
	depends on ARMV7M_HAVE_DTCM
	---help---
		Exclude DTCM SRAM from the HEAP because it appears to be impossible
		to execute ELF modules from DTCM RAM (REVISIT!).

config STM32F7_DTCM_PROCFS
	bool "DTCM SRAM PROCFS support"
	default n
	depends on ARMV7M_DTCM && FS_PROCFS
	---help---
		Select to build in support for /proc/dtcm.  Reading from /proc/dtcm
		will provide statistics about DTCM memory use similar to what you
		would get from mallinfo() for the user heap.

config STM32F7_DMACAPABLE
	bool "Workaround non-DMA capable memory"
	depends on ARCH_DMA
	default n
	---help---
		This option enables the DMA interface stm32_dmacapable that can be
		used to check if it is possible to do DMA from the selected address.
		Drivers then may use this information to determine if they should
		attempt the DMA or fall back to a different transfer method.

config STM32F7_DMACAPABLE_ASSUME_CACHE_ALIGNED
	bool "Do not disqualify DMA capability based on cache alignment"
	depends on STM32F7_DMACAPABLE && ARMV7M_DCACHE && !ARMV7M_DCACHE_WRITETHROUGH
	default n
	---help---
		This option configures the stm32_dmacapable to not disqualify
		DMA operations on memory that is not dcache aligned based solely
		on the starting address and byte count.
		
		Use this when ALL buffer extents are known to be aligned, but the
		the count does not use the complete buffer.

menu "Timer Configuration"

if SCHED_TICKLESS

config STM32F7_TICKLESS_TIMER
	int "Tickless hardware timer"
	default 2
	range 1 14
	---help---
		If the Tickless OS feature is enabled, then one clock must be
		assigned to provided the timer needed by the OS.

config STM32F7_TICKLESS_CHANNEL
	int "Tickless timer channel"
	default 1
	range 1 4
	---help---
		If the Tickless OS feature is enabled, the one clock must be
		assigned to provided the free-running timer needed by the OS
		and one channel on that clock is needed to handle intervals.

endif # SCHED_TICKLESS

config STM32F7_PWM_LL_OPS
	bool "PWM low-level operations"
	default n
	---help---
		Enable low-level PWM ops.

config STM32F7_TIM1_PWM
	bool "TIM1 PWM"
	default n
	depends on STM32F7_TIM1
	select STM32F7_PWM
	---help---
		Reserve timer 1 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32F7_TIM1
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32F7_TIM1_PWM

config STM32F7_TIM1_MODE
	int "TIM1 Mode"
	default 0
	range 0 4
	---help---
		Specifies the timer mode.

config STM32F7_TIM1_LOCK
	int "TIM1 Lock Level Configuration"
	default 0
	range 0 3
	---help---
		Timer 1 lock level configuration

config STM32F7_TIM1_TDTS
	int "TIM1 t_DTS Division"
	default 0
	range 0 2
	---help---
		Timer 1 dead-time and sampling clock (t_DTS) division

config STM32F7_TIM1_DEADTIME
	int "TIM1 Initial Dead-time"
	default 0
	range 0 255
	---help---
		Timer 1 initial dead-time

if STM32F7_PWM_MULTICHAN

config STM32F7_TIM1_CHANNEL1
	bool "TIM1 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32F7_TIM1_CHANNEL1

config STM32F7_TIM1_CH1MODE
	int "TIM1 Channel 1 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM1_CH1OUT
	bool "TIM1 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32F7_TIM1_CH1NOUT
	bool "TIM1 Channel 1 Complementary Output"
	default n
	---help---
		Enables channel 1 Complementary Output.

endif # STM32F7_TIM1_CHANNEL1

config STM32F7_TIM1_CHANNEL2
	bool "TIM1 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32F7_TIM1_CHANNEL2

config STM32F7_TIM1_CH2MODE
	int "TIM1 Channel 2 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM1_CH2OUT
	bool "TIM1 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

config STM32F7_TIM1_CH2NOUT
	bool "TIM1 Channel 2 Complementary Output"
	default n
	---help---
		Enables channel 2 Complementary Output.

endif # STM32F7_TIM1_CHANNEL2

config STM32F7_TIM1_CHANNEL3
	bool "TIM1 Channel 3"
	default n
	---help---
		Enables channel 3.

if STM32F7_TIM1_CHANNEL3

config STM32F7_TIM1_CH3MODE
	int "TIM1 Channel 3 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM1_CH3OUT
	bool "TIM1 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

config STM32F7_TIM1_CH3NOUT
	bool "TIM1 Channel 3 Complementary Output"
	default n
	---help---
		Enables channel 3 Complementary Output.

endif # STM32F7_TIM1_CHANNEL3

config STM32F7_TIM1_CHANNEL4
	bool "TIM1 Channel 4"
	default n
	---help---
		Enables channel 4.

if STM32F7_TIM1_CHANNEL4

config STM32F7_TIM1_CH4MODE
	int "TIM1 Channel 4 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM1_CH4OUT
	bool "TIM1 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32F7_TIM1_CHANNEL4

config STM32F7_TIM1_CHANNEL5
	bool "TIM1 Channel 5 (internal)"
	default n
	---help---
		Enables channel 5 (not available externally)

if STM32F7_TIM1_CHANNEL5

config STM32F7_TIM1_CH5MODE
	int "TIM1 Channel 5 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM1_CH5OUT
	bool "TIM1 Channel 5 Output"
	default n
	---help---
		Enables channel 5 output.

endif # STM32F7_TIM1_CHANNEL5

config STM32F7_TIM1_CHANNEL6
	bool "TIM1 Channel 6 (internal)"
	default n
	---help---
		Enables channel 6 (not available externally)

if STM32F7_TIM1_CHANNEL6

config STM32F7_TIM1_CH6MODE
	int "TIM1 Channel 6 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM1_CH6OUT
	bool "TIM1 Channel 6 Output"
	default n
	---help---
		Enables channel 6 output.

endif # STM32F7_TIM1_CHANNEL6

endif # STM32F7_PWM_MULTICHAN

if !STM32F7_PWM_MULTICHAN

config STM32F7_TIM1_CHANNEL
	int "TIM1 PWM Output Channel"
	default 1
	range 1 4
	---help---
		If TIM1 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

if STM32F7_TIM1_CHANNEL = 1

config STM32F7_TIM1_CH1OUT
	bool "TIM1 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32F7_TIM1_CH1NOUT
	bool "TIM1 Channel 1 Complementary Output"
	default n
	---help---
		Enables channel 1 Complementary Output.

endif # STM32F7_TIM1_CHANNEL = 1

if STM32F7_TIM1_CHANNEL = 2

config STM32F7_TIM1_CH2OUT
	bool "TIM1 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

config STM32F7_TIM1_CH2NOUT
	bool "TIM1 Channel 2 Complementary Output"
	default n
	---help---
		Enables channel 2 Complementary Output.

endif # STM32F7_TIM1_CHANNEL = 2

if STM32F7_TIM1_CHANNEL = 3

config STM32F7_TIM1_CH3OUT
	bool "TIM1 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

config STM32F7_TIM1_CH3NOUT
	bool "TIM1 Channel 3 Complementary Output"
	default n
	---help---
		Enables channel 3 Complementary Output.

endif # STM32F7_TIM1_CHANNEL = 3

if STM32F7_TIM1_CHANNEL = 4

config STM32F7_TIM1_CH4OUT
	bool "TIM1 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32F7_TIM1_CHANNEL = 4

config STM32F7_TIM1_CHMODE
	int "TIM1 Channel Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32F7_PWM_MULTICHAN

endif # STM32F7_TIM1_PWM

config STM32F7_TIM2_PWM
	bool "TIM2 PWM"
	default n
	depends on STM32F7_TIM2
	select STM32F7_PWM
	---help---
		Reserve timer 2 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32F7_TIM2
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32F7_TIM2_PWM

config STM32F7_TIM2_MODE
	int "TIM2 Mode"
	default 0
	range 0 4
	---help---
		Specifies the timer mode.

if STM32F7_PWM_MULTICHAN

config STM32F7_TIM2_CHANNEL1
	bool "TIM2 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32F7_TIM2_CHANNEL1

config STM32F7_TIM2_CH1MODE
	int "TIM2 Channel 1 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM2_CH1OUT
	bool "TIM2 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM2_CHANNEL1

config STM32F7_TIM2_CHANNEL2
	bool "TIM2 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32F7_TIM2_CHANNEL2

config STM32F7_TIM2_CH2MODE
	int "TIM2 Channel 2 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM2_CH2OUT
	bool "TIM2 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32F7_TIM2_CHANNEL2

config STM32F7_TIM2_CHANNEL3
	bool "TIM2 Channel 3"
	default n
	---help---
		Enables channel 3.

if STM32F7_TIM2_CHANNEL3

config STM32F7_TIM2_CH3MODE
	int "TIM2 Channel 3 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM2_CH3OUT
	bool "TIM2 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32F7_TIM2_CHANNEL3

config STM32F7_TIM2_CHANNEL4
	bool "TIM2 Channel 4"
	default n
	---help---
		Enables channel 4.

if STM32F7_TIM2_CHANNEL4

config STM32F7_TIM2_CH4MODE
	int "TIM2 Channel 4 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM2_CH4OUT
	bool "TIM2 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32F7_TIM2_CHANNEL4

endif # STM32F7_PWM_MULTICHAN

if !STM32F7_PWM_MULTICHAN

config STM32F7_TIM2_CHANNEL
	int "TIM2 PWM Output Channel"
	default 1
	range 1 4
	---help---
		If TIM2 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

if STM32F7_TIM2_CHANNEL = 1

config STM32F7_TIM2_CH1OUT
	bool "TIM2 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM2_CHANNEL = 1

if STM32F7_TIM2_CHANNEL = 2

config STM32F7_TIM2_CH2OUT
	bool "TIM2 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32F7_TIM2_CHANNEL = 2

if STM32F7_TIM2_CHANNEL = 3

config STM32F7_TIM2_CH3OUT
	bool "TIM2 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32F7_TIM2_CHANNEL = 3

if STM32F7_TIM2_CHANNEL = 4

config STM32F7_TIM2_CH4OUT
	bool "TIM2 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32F7_TIM2_CHANNEL = 4

config STM32F7_TIM2_CHMODE
	int "TIM2 Channel Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32F7_PWM_MULTICHAN

endif # STM32F7_TIM2_PWM

config STM32F7_TIM3_PWM
	bool "TIM3 PWM"
	default n
	depends on STM32F7_TIM3
	select STM32F7_PWM
	---help---
		Reserve timer 3 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32F7_TIM3
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32F7_TIM3_PWM

config STM32F7_TIM3_MODE
	int "TIM3 Mode"
	default 0
	range 0 4
	---help---
		Specifies the timer mode.

if STM32F7_PWM_MULTICHAN

config STM32F7_TIM3_CHANNEL1
	bool "TIM3 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32F7_TIM3_CHANNEL1

config STM32F7_TIM3_CH1MODE
	int "TIM3 Channel 1 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM3_CH1OUT
	bool "TIM3 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM3_CHANNEL1

config STM32F7_TIM3_CHANNEL2
	bool "TIM3 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32F7_TIM3_CHANNEL2

config STM32F7_TIM3_CH2MODE
	int "TIM3 Channel 2 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM3_CH2OUT
	bool "TIM3 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32F7_TIM3_CHANNEL2

config STM32F7_TIM3_CHANNEL3
	bool "TIM3 Channel 3"
	default n
	---help---
		Enables channel 3.

if STM32F7_TIM3_CHANNEL3

config STM32F7_TIM3_CH3MODE
	int "TIM3 Channel 3 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM3_CH3OUT
	bool "TIM3 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32F7_TIM3_CHANNEL3

config STM32F7_TIM3_CHANNEL4
	bool "TIM3 Channel 4"
	default n
	---help---
		Enables channel 4.

if STM32F7_TIM3_CHANNEL4

config STM32F7_TIM3_CH4MODE
	int "TIM3 Channel 4 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM3_CH4OUT
	bool "TIM3 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32F7_TIM3_CHANNEL4

endif # STM32F7_PWM_MULTICHAN

if !STM32F7_PWM_MULTICHAN

config STM32F7_TIM3_CHANNEL
	int "TIM3 PWM Output Channel"
	default 1
	range 1 4
	---help---
		If TIM3 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

if STM32F7_TIM3_CHANNEL = 1

config STM32F7_TIM3_CH1OUT
	bool "TIM3 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM3_CHANNEL = 1

if STM32F7_TIM3_CHANNEL = 2

config STM32F7_TIM3_CH2OUT
	bool "TIM3 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32F7_TIM3_CHANNEL = 2

if STM32F7_TIM3_CHANNEL = 3

config STM32F7_TIM3_CH3OUT
	bool "TIM3 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32F7_TIM3_CHANNEL = 3

if STM32F7_TIM3_CHANNEL = 4

config STM32F7_TIM3_CH4OUT
	bool "TIM3 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32F7_TIM3_CHANNEL = 4

config STM32F7_TIM3_CHMODE
	int "TIM3 Channel Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32F7_PWM_MULTICHAN

endif # STM32F7_TIM3_PWM

config STM32F7_TIM4_PWM
	bool "TIM4 PWM"
	default n
	depends on STM32F7_TIM4
	select STM32F7_PWM
	---help---
		Reserve timer 4 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32F7_TIM4
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32F7_TIM4_PWM

config STM32F7_TIM4_MODE
	int "TIM4 Mode"
	default 0
	range 0 4
	---help---
		Specifies the timer mode.

if STM32F7_PWM_MULTICHAN

config STM32F7_TIM4_CHANNEL1
	bool "TIM4 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32F7_TIM4_CHANNEL1

config STM32F7_TIM4_CH1MODE
	int "TIM4 Channel 1 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM4_CH1OUT
	bool "TIM4 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM4_CHANNEL1

config STM32F7_TIM4_CHANNEL2
	bool "TIM4 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32F7_TIM4_CHANNEL2

config STM32F7_TIM4_CH2MODE
	int "TIM4 Channel 2 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM4_CH2OUT
	bool "TIM4 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32F7_TIM4_CHANNEL2

config STM32F7_TIM4_CHANNEL3
	bool "TIM4 Channel 3"
	default n
	---help---
		Enables channel 3.

if STM32F7_TIM4_CHANNEL3

config STM32F7_TIM4_CH3MODE
	int "TIM4 Channel 3 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM4_CH3OUT
	bool "TIM4 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32F7_TIM4_CHANNEL3

config STM32F7_TIM4_CHANNEL4
	bool "TIM4 Channel 4"
	default n
	---help---
		Enables channel 4.

if STM32F7_TIM4_CHANNEL4

config STM32F7_TIM4_CH4MODE
	int "TIM4 Channel 4 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM4_CH4OUT
	bool "TIM4 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32F7_TIM4_CHANNEL4

endif # STM32F7_PWM_MULTICHAN

if !STM32F7_PWM_MULTICHAN

config STM32F7_TIM4_CHANNEL
	int "TIM4 PWM Output Channel"
	default 1
	range 1 4
	---help---
		If TIM4 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

if STM32F7_TIM4_CHANNEL = 1

config STM32F7_TIM4_CH1OUT
	bool "TIM4 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM4_CHANNEL = 1

if STM32F7_TIM4_CHANNEL = 2

config STM32F7_TIM4_CH2OUT
	bool "TIM4 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32F7_TIM4_CHANNEL = 2

if STM32F7_TIM4_CHANNEL = 3

config STM32F7_TIM4_CH3OUT
	bool "TIM4 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32F7_TIM4_CHANNEL = 3

if STM32F7_TIM4_CHANNEL = 4

config STM32F7_TIM4_CH4OUT
	bool "TIM4 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32F7_TIM4_CHANNEL = 4

config STM32F7_TIM4_CHMODE
	int "TIM4 Channel Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32F7_PWM_MULTICHAN

endif # STM32F7_TIM4_PWM

config STM32F7_TIM5_PWM
	bool "TIM5 PWM"
	default n
	depends on STM32F7_TIM5
	select STM32F7_PWM
	---help---
		Reserve timer 5 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32F7_TIM5
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32F7_TIM5_PWM

config STM32F7_TIM5_MODE
	int "TIM5 Mode"
	default 0
	range 0 4
	---help---
		Specifies the timer mode.

if STM32F7_PWM_MULTICHAN

config STM32F7_TIM5_CHANNEL1
	bool "TIM5 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32F7_TIM5_CHANNEL1

config STM32F7_TIM5_CH1MODE
	int "TIM5 Channel 1 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM5_CH1OUT
	bool "TIM5 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM5_CHANNEL1

config STM32F7_TIM5_CHANNEL2
	bool "TIM5 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32F7_TIM5_CHANNEL2

config STM32F7_TIM5_CH2MODE
	int "TIM5 Channel 2 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM5_CH2OUT
	bool "TIM5 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32F7_TIM5_CHANNEL2

config STM32F7_TIM5_CHANNEL3
	bool "TIM5 Channel 3"
	default n
	---help---
		Enables channel 3.

if STM32F7_TIM5_CHANNEL3

config STM32F7_TIM5_CH3MODE
	int "TIM5 Channel 3 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM5_CH3OUT
	bool "TIM5 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32F7_TIM5_CHANNEL3

config STM32F7_TIM5_CHANNEL4
	bool "TIM5 Channel 4"
	default n
	---help---
		Enables channel 4.

if STM32F7_TIM5_CHANNEL4

config STM32F7_TIM5_CH4MODE
	int "TIM5 Channel 4 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM5_CH4OUT
	bool "TIM5 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32F7_TIM5_CHANNEL4

endif # STM32F7_PWM_MULTICHAN

if !STM32F7_PWM_MULTICHAN

config STM32F7_TIM5_CHANNEL
	int "TIM5 PWM Output Channel"
	default 1
	range 1 4
	---help---
		If TIM5 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

if STM32F7_TIM5_CHANNEL = 1

config STM32F7_TIM5_CH1OUT
	bool "TIM5 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM5_CHANNEL = 1

if STM32F7_TIM5_CHANNEL = 2

config STM32F7_TIM5_CH2OUT
	bool "TIM5 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32F7_TIM5_CHANNEL = 2

if STM32F7_TIM5_CHANNEL = 3

config STM32F7_TIM5_CH3OUT
	bool "TIM5 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32F7_TIM5_CHANNEL = 3

if STM32F7_TIM5_CHANNEL = 4

config STM32F7_TIM5_CH4OUT
	bool "TIM5 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32F7_TIM5_CHANNEL = 4

config STM32F7_TIM5_CHMODE
	int "TIM5 Channel Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32F7_PWM_MULTICHAN

endif # STM32F7_TIM5_PWM

config STM32F7_TIM8_PWM
	bool "TIM8 PWM"
	default n
	depends on STM32F7_TIM8
	select STM32F7_PWM
	---help---
		Reserve timer 8 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32F7_TIM8
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32F7_TIM8_PWM

config STM32F7_TIM8_MODE
	int "TIM8 Mode"
	default 0
	range 0 4
	---help---
		Specifies the timer mode.

config STM32F7_TIM8_LOCK
	int "TIM8 Lock Level Configuration"
	default 0
	range 0 3
	---help---
		Timer 8 lock level configuration

config STM32F7_TIM8_DEADTIME
	int "TIM8 Initial Dead-time"
	default 0
	range 0 255
	---help---
		Timer 8 initial dead-time

config STM32F7_TIM8_TDTS
	int "TIM8 t_DTS Division"
	default 0
	range 0 2
	---help---
		Timer 8 dead-time and sampling clock (t_DTS) division

if STM32F7_PWM_MULTICHAN

config STM32F7_TIM8_CHANNEL1
	bool "TIM8 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32F7_TIM8_CHANNEL1

config STM32F7_TIM8_CH1MODE
	int "TIM8 Channel 1 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM8_CH1OUT
	bool "TIM8 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32F7_TIM8_CH1NOUT
	bool "TIM8 Channel 1 Complementary Output"
	default n
	---help---
		Enables channel 1 Complementary Output.

endif # STM32F7_TIM8_CHANNEL1

config STM32F7_TIM8_CHANNEL2
	bool "TIM8 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32F7_TIM8_CHANNEL2

config STM32F7_TIM8_CH2MODE
	int "TIM8 Channel 2 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM8_CH2OUT
	bool "TIM8 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

config STM32F7_TIM8_CH2NOUT
	bool "TIM8 Channel 2 Complementary Output"
	default n
	---help---
		Enables channel 2 Complementary Output.

endif # STM32F7_TIM8_CHANNEL2

config STM32F7_TIM8_CHANNEL3
	bool "TIM8 Channel 3"
	default n
	---help---
		Enables channel 3.

if STM32F7_TIM8_CHANNEL3

config STM32F7_TIM8_CH3MODE
	int "TIM8 Channel 3 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM8_CH3OUT
	bool "TIM8 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

config STM32F7_TIM8_CH3NOUT
	bool "TIM8 Channel 3 Complementary Output"
	default n
	---help---
		Enables channel 3 Complementary Output.

endif # STM32F7_TIM8_CHANNEL3

config STM32F7_TIM8_CHANNEL4
	bool "TIM8 Channel 4"
	default n
	---help---
		Enables channel 4.

if STM32F7_TIM8_CHANNEL4

config STM32F7_TIM8_CH4MODE
	int "TIM8 Channel 4 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM8_CH4OUT
	bool "TIM8 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32F7_TIM8_CHANNEL4

config STM32F7_TIM8_CHANNEL5
	bool "TIM8 Channel 5 (internal)"
	default n
	---help---
		Enables channel 5 (not available externally)

if STM32F7_TIM8_CHANNEL5

config STM32F7_TIM8_CH5MODE
	int "TIM8 Channel 5 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM8_CH5OUT
	bool "TIM8 Channel 5 Output"
	default n
	---help---
		Enables channel 5 output.

endif # STM32F7_TIM8_CHANNEL5

config STM32F7_TIM8_CHANNEL6
	bool "TIM8 Channel 6 (internal)"
	default n
	---help---
		Enables channel 6 (not available externally)

if STM32F7_TIM8_CHANNEL6

config STM32F7_TIM8_CH6MODE
	int "TIM8 Channel 6 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM8_CH6OUT
	bool "TIM8 Channel 6 Output"
	default n
	---help---
		Enables channel 6 output.

endif # STM32F7_TIM8_CHANNEL6

endif # STM32F7_PWM_MULTICHAN

if !STM32F7_PWM_MULTICHAN

config STM32F7_TIM8_CHANNEL
	int "TIM8 PWM Output Channel"
	default 1
	range 1 4
	---help---
		If TIM8 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

if STM32F7_TIM8_CHANNEL = 1

config STM32F7_TIM8_CH1OUT
	bool "TIM8 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32F7_TIM8_CH1NOUT
	bool "TIM8 Channel 1 Complementary Output"
	default n
	---help---
		Enables channel 1 Complementary Output.

endif # STM32F7_TIM8_CHANNEL = 1

if STM32F7_TIM8_CHANNEL = 2

config STM32F7_TIM8_CH2OUT
	bool "TIM8 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

config STM32F7_TIM8_CH2NOUT
	bool "TIM8 Channel 2 Complementary Output"
	default n
	---help---
		Enables channel 2 Complementary Output.

endif # STM32F7_TIM8_CHANNEL = 2

if STM32F7_TIM8_CHANNEL = 3

config STM32F7_TIM8_CH3OUT
	bool "TIM8 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

config STM32F7_TIM8_CH3NOUT
	bool "TIM8 Channel 3 Complementary Output"
	default n
	---help---
		Enables channel 3 Complementary Output.

endif # STM32F7_TIM8_CHANNEL = 3

if STM32F7_TIM8_CHANNEL = 4

config STM32F7_TIM8_CH4OUT
	bool "TIM8 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32F7_TIM8_CHANNEL = 4

config STM32F7_TIM8_CHMODE
	int "TIM8 Channel Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32F7_PWM_MULTICHAN

endif # STM32F7_TIM8_PWM

config STM32F7_TIM9_PWM
	bool "TIM9 PWM"
	default n
	depends on STM32F7_TIM9
	select STM32F7_PWM
	---help---
		Reserve timer 9 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32F7_TIM9
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32F7_TIM9_PWM

if STM32F7_PWM_MULTICHAN

config STM32F7_TIM9_CHANNEL1
	bool "TIM9 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32F7_TIM9_CHANNEL1

config STM32F7_TIM9_CH1MODE
	int "TIM9 Channel 1 Mode"
	default 6
	range 0 9
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM9_CH1OUT
	bool "TIM9 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM9_CHANNEL1

config STM32F7_TIM9_CHANNEL2
	bool "TIM9 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32F7_TIM9_CHANNEL2

config STM32F7_TIM9_CH2MODE
	int "TIM9 Channel 2 Mode"
	default 6
	range 0 9
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM9_CH2OUT
	bool "TIM9 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32F7_TIM9_CHANNEL2

endif # STM32F7_PWM_MULTICHAN

if !STM32F7_PWM_MULTICHAN

config STM32F7_TIM9_CHANNEL
	int "TIM9 PWM Output Channel"
	default 1
	range 1 2
	---help---
		If TIM9 is enabled for PWM usage, you also need specifies the timer output
		channel {1,2}

if STM32F7_TIM9_CHANNEL = 1

config STM32F7_TIM9_CH1OUT
	bool "TIM9 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM9_CHANNEL = 1

if STM32F7_TIM9_CHANNEL = 2

config STM32F7_TIM9_CH2OUT
	bool "TIM9 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32F7_TIM9_CHANNEL = 2

config STM32F7_TIM9_CHMODE
	int "TIM9 Channel Mode"
	default 6
	range 0 9
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32F7_PWM_MULTICHAN

endif # STM32F7_TIM9_PWM

config STM32F7_TIM10_PWM
	bool "TIM10 PWM"
	default n
	depends on STM32F7_TIM10
	select STM32F7_PWM
	---help---
		Reserve timer 10 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32F7_TIM10
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32F7_TIM10_PWM

if STM32F7_PWM_MULTICHAN

config STM32F7_TIM10_CHANNEL1
	bool "TIM10 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32F7_TIM10_CHANNEL1

config STM32F7_TIM10_CH1MODE
	int "TIM10 Channel 1 Mode"
	default 6
	range 0 7
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM10_CH1OUT
	bool "TIM10 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM10_CHANNEL1

endif # STM32F7_PWM_MULTICHAN

if !STM32F7_PWM_MULTICHAN

config STM32F7_TIM10_CHANNEL
	int "TIM10 PWM Output Channel"
	default 1
	range 1 1
	---help---
		If TIM10 is enabled for PWM usage, you also need specifies the timer output
		channel {1}

if STM32F7_TIM10_CHANNEL = 1

config STM32F7_TIM10_CH1OUT
	bool "TIM10 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM10_CHANNEL = 1

config STM32F7_TIM10_CHMODE
	int "TIM10 Channel Mode"
	default 6
	range 0 7
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32F7_PWM_MULTICHAN

endif # STM32F7_TIM10_PWM

config STM32F7_TIM11_PWM
	bool "TIM11 PWM"
	default n
	depends on STM32F7_TIM11
	select STM32F7_PWM
	---help---
		Reserve timer 11 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32F7_TIM11
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32F7_TIM11_PWM

if STM32F7_PWM_MULTICHAN

config STM32F7_TIM11_CHANNEL1
	bool "TIM11 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32F7_TIM11_CHANNEL1

config STM32F7_TIM11_CH1MODE
	int "TIM11 Channel 1 Mode"
	default 6
	range 0 7
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM11_CH1OUT
	bool "TIM11 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM11_CHANNEL1

endif # STM32F7_PWM_MULTICHAN

if !STM32F7_PWM_MULTICHAN

config STM32F7_TIM11_CHANNEL
	int "TIM11 PWM Output Channel"
	default 1
	range 1 1
	---help---
		If TIM11 is enabled for PWM usage, you also need specifies the timer output
		channel {1}

if STM32F7_TIM11_CHANNEL = 1

config STM32F7_TIM11_CH1OUT
	bool "TIM11 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM11_CHANNEL = 1

config STM32F7_TIM11_CHMODE
	int "TIM11 Channel Mode"
	default 6
	range 0 7
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32F7_PWM_MULTICHAN

endif # STM32F7_TIM11_PWM

config STM32F7_TIM12_PWM
	bool "TIM12 PWM"
	default n
	depends on STM32F7_TIM12
	select STM32F7_PWM
	---help---
		Reserve timer 12 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32F7_TIM12
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32F7_TIM12_PWM

if STM32F7_PWM_MULTICHAN

config STM32F7_TIM12_CHANNEL1
	bool "TIM12 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32F7_TIM12_CHANNEL1

config STM32F7_TIM12_CH1MODE
	int "TIM12 Channel 1 Mode"
	default 6
	range 0 9
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM12_CH1OUT
	bool "TIM12 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM12_CHANNEL1

config STM32F7_TIM12_CHANNEL2
	bool "TIM12 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32F7_TIM12_CHANNEL2

config STM32F7_TIM12_CH2MODE
	int "TIM12 Channel 2 Mode"
	default 6
	range 0 9
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM12_CH2OUT
	bool "TIM12 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32F7_TIM12_CHANNEL2

endif # STM32F7_PWM_MULTICHAN

if !STM32F7_PWM_MULTICHAN

config STM32F7_TIM12_CHANNEL
	int "TIM12 PWM Output Channel"
	default 1
	range 1 2
	---help---
		If TIM12 is enabled for PWM usage, you also need specifies the timer output
		channel {1,2}

if STM32F7_TIM12_CHANNEL = 1

config STM32F7_TIM12_CH1OUT
	bool "TIM12 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM12_CHANNEL = 1

if STM32F7_TIM12_CHANNEL = 2

config STM32F7_TIM12_CH2OUT
	bool "TIM12 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32F7_TIM12_CHANNEL = 2

config STM32F7_TIM12_CHMODE
	int "TIM12 Channel Mode"
	default 6
	range 0 9
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32F7_PWM_MULTICHAN

endif # STM32F7_TIM12_PWM

config STM32F7_TIM13_PWM
	bool "TIM13 PWM"
	default n
	depends on STM32F7_TIM13
	select STM32F7_PWM
	---help---
		Reserve timer 13 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32F7_TIM13
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32F7_TIM13_PWM

if STM32F7_PWM_MULTICHAN

config STM32F7_TIM13_CHANNEL1
	bool "TIM13 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32F7_TIM13_CHANNEL1

config STM32F7_TIM13_CH1MODE
	int "TIM13 Channel 1 Mode"
	default 6
	range 0 7
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM13_CH1OUT
	bool "TIM13 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM13_CHANNEL1

endif # STM32F7_PWM_MULTICHAN

if !STM32F7_PWM_MULTICHAN

config STM32F7_TIM13_CHANNEL
	int "TIM13 PWM Output Channel"
	default 1
	range 1 1
	---help---
		If TIM13 is enabled for PWM usage, you also need specifies the timer output
		channel {1}

if STM32F7_TIM13_CHANNEL = 1

config STM32F7_TIM13_CH1OUT
	bool "TIM13 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM13_CHANNEL = 1

config STM32F7_TIM13_CHMODE
	int "TIM13 Channel Mode"
	default 6
	range 0 7
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32F7_PWM_MULTICHAN

endif # STM32F7_TIM13_PWM

config STM32F7_TIM14_PWM
	bool "TIM14 PWM"
	default n
	depends on STM32F7_TIM14
	select STM32F7_PWM
	---help---
		Reserve timer 14 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32F7_TIM14
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32F7_TIM14_PWM

if STM32F7_PWM_MULTICHAN

config STM32F7_TIM14_CHANNEL1
	bool "TIM14 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32F7_TIM14_CHANNEL1

config STM32F7_TIM14_CH1MODE
	int "TIM14 Channel 1 Mode"
	default 6
	range 0 7
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32F7_TIM14_CH1OUT
	bool "TIM14 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM14_CHANNEL1

endif # STM32F7_PWM_MULTICHAN

if !STM32F7_PWM_MULTICHAN

config STM32F7_TIM14_CHANNEL
	int "TIM14 PWM Output Channel"
	default 1
	range 1 1
	---help---
		If TIM14 is enabled for PWM usage, you also need specifies the timer output
		channel {1}

if STM32F7_TIM14_CHANNEL = 1

config STM32F7_TIM14_CH1OUT
	bool "TIM14 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32F7_TIM14_CHANNEL = 1

config STM32F7_TIM14_CHMODE
	int "TIM14 Channel Mode"
	default 6
	range 0 7
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32F7_PWM_MULTICHAN

endif # STM32F7_TIM14_PWM

config STM32F7_PWM_MULTICHAN
	bool "PWM Multiple Output Channels"
	default n
	depends on STM32F7_PWM
	select ARCH_HAVE_PWM_MULTICHAN
	---help---
		Specifies that the PWM driver supports multiple output
		channels per timer.

config STM32F7_PWM_TRGO
	bool "TIM PWM TRGO support"
	default n
	depends on STM32F7_PWM
	---help---
		Enable TRGO support for PWM driver

config STM32F7_TIM1_ADC
	bool "TIM1 ADC"
	default n
	depends on STM32F7_TIM1 && STM32F7_ADC
	---help---
		Reserve timer 1 for use by ADC

		Timer devices may be used for different purposes.  If STM32F7_TIM1 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for ADC conversion. Note that ADC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the ADC, but then you also have to configure which ADC
		channel it is assigned to.

choice
	prompt "Select TIM1 ADC channel"
	default STM32F7_TIM1_ADC1
	depends on STM32F7_TIM1_ADC

config STM32F7_TIM1_ADC1
	bool "TIM1 ADC channel 1"
	depends on STM32F7_ADC1
	select STM32F7_HAVE_ADC1_TIMER
	---help---
		Reserve TIM1 to trigger ADC1

config STM32F7_TIM1_ADC2
	bool "TIM1 ADC channel 2"
	depends on STM32F7_ADC2
	select STM32F7_HAVE_ADC2_TIMER
	---help---
		Reserve TIM1 to trigger ADC2

config STM32F7_TIM1_ADC3
	bool "TIM1 ADC channel 3"
	depends on STM32F7_ADC3
	select STM32F7_HAVE_ADC3_TIMER
	---help---
		Reserve TIM1 to trigger ADC3

endchoice

config STM32F7_TIM2_ADC
	bool "TIM2 ADC"
	default n
	depends on STM32F7_TIM2 && STM32F7_ADC
	---help---
		Reserve timer 1 for use by ADC

		Timer devices may be used for different purposes.  If STM32F7_TIM2 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for ADC conversion. Note that ADC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the ADC, but then you also have to configure which ADC
		channel it is assigned to.

choice
	prompt "Select TIM2 ADC channel"
	default STM32F7_TIM2_ADC1
	depends on STM32F7_TIM2_ADC

config STM32F7_TIM2_ADC1
	bool "TIM2 ADC channel 1"
	depends on STM32F7_ADC1
	select STM32F7_HAVE_ADC1_TIMER
	---help---
		Reserve TIM2 to trigger ADC1

config STM32F7_TIM2_ADC2
	bool "TIM2 ADC channel 2"
	depends on STM32F7_ADC2
	select STM32F7_HAVE_ADC2_TIMER
	---help---
		Reserve TIM2 to trigger ADC2

config STM32F7_TIM2_ADC3
	bool "TIM2 ADC channel 3"
	depends on STM32F7_ADC3
	select STM32F7_HAVE_ADC3_TIMER
	---help---
		Reserve TIM2 to trigger ADC3

endchoice

config STM32F7_TIM3_ADC
	bool "TIM3 ADC"
	default n
	depends on STM32F7_TIM3 && STM32F7_ADC
	---help---
		Reserve timer 1 for use by ADC

		Timer devices may be used for different purposes.  If STM32F7_TIM3 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for ADC conversion. Note that ADC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the ADC, but then you also have to configure which ADC
		channel it is assigned to.

choice
	prompt "Select TIM3 ADC channel"
	default STM32F7_TIM3_ADC1
	depends on STM32F7_TIM3_ADC

config STM32F7_TIM3_ADC1
	bool "TIM3 ADC channel 1"
	depends on STM32F7_ADC1
	select STM32F7_HAVE_ADC1_TIMER
	---help---
		Reserve TIM3 to trigger ADC1

config STM32F7_TIM3_ADC2
	bool "TIM3 ADC channel 2"
	depends on STM32F7_ADC2
	select STM32F7_HAVE_ADC2_TIMER
	---help---
		Reserve TIM3 to trigger ADC2

config STM32F7_TIM3_ADC3
	bool "TIM3 ADC channel 3"
	depends on STM32F7_ADC3
	select STM32F7_HAVE_ADC3_TIMER
	---help---
		Reserve TIM3 to trigger ADC3

endchoice

config STM32F7_TIM4_ADC
	bool "TIM4 ADC"
	default n
	depends on STM32F7_TIM4 && STM32F7_ADC
	---help---
		Reserve timer 1 for use by ADC

		Timer devices may be used for different purposes.  If STM32F7_TIM4 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for ADC conversion. Note that ADC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the ADC, but then you also have to configure which ADC
		channel it is assigned to.

choice
	prompt "Select TIM4 ADC channel"
	default STM32F7_TIM4_ADC1
	depends on STM32F7_TIM4_ADC

config STM32F7_TIM4_ADC1
	bool "TIM4 ADC channel 1"
	depends on STM32F7_ADC1
	select STM32F7_HAVE_ADC1_TIMER
	---help---
		Reserve TIM4 to trigger ADC1

config STM32F7_TIM4_ADC2
	bool "TIM4 ADC channel 2"
	depends on STM32F7_ADC2
	select STM32F7_HAVE_ADC2_TIMER
	---help---
		Reserve TIM4 to trigger ADC2

config STM32F7_TIM4_ADC3
	bool "TIM4 ADC channel 3"
	depends on STM32F7_ADC3
	select STM32F7_HAVE_ADC3_TIMER
	---help---
		Reserve TIM4 to trigger ADC3

endchoice

config STM32F7_TIM5_ADC
	bool "TIM5 ADC"
	default n
	depends on STM32F7_TIM5 && STM32F7_ADC
	---help---
		Reserve timer 1 for use by ADC

		Timer devices may be used for different purposes.  If STM32F7_TIM5 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for ADC conversion. Note that ADC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the ADC, but then you also have to configure which ADC
		channel it is assigned to.

choice
	prompt "Select TIM5 ADC channel"
	default STM32F7_TIM5_ADC1
	depends on STM32F7_TIM5_ADC

config STM32F7_TIM5_ADC1
	bool "TIM5 ADC channel 1"
	depends on STM32F7_ADC1
	select STM32F7_HAVE_ADC1_TIMER
	---help---
		Reserve TIM5 to trigger ADC1

config STM32F7_TIM5_ADC2
	bool "TIM5 ADC channel 2"
	depends on STM32F7_ADC2
	select STM32F7_HAVE_ADC2_TIMER
	---help---
		Reserve TIM5 to trigger ADC2

config STM32F7_TIM5_ADC3
	bool "TIM5 ADC channel 3"
	depends on STM32F7_ADC3
	select STM32F7_HAVE_ADC3_TIMER
	---help---
		Reserve TIM5 to trigger ADC3

endchoice

config STM32F7_TIM8_ADC
	bool "TIM8 ADC"
	default n
	depends on STM32F7_TIM8 && STM32F7_ADC
	---help---
		Reserve timer 1 for use by ADC

		Timer devices may be used for different purposes.  If STM32F7_TIM8 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for ADC conversion. Note that ADC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the ADC, but then you also have to configure which ADC
		channel it is assigned to.

choice
	prompt "Select TIM8 ADC channel"
	default STM32F7_TIM8_ADC1
	depends on STM32F7_TIM8_ADC

config STM32F7_TIM8_ADC1
	bool "TIM8 ADC channel 1"
	depends on STM32F7_ADC1
	select STM32F7_HAVE_ADC1_TIMER
	---help---
		Reserve TIM8 to trigger ADC1

config STM32F7_TIM8_ADC2
	bool "TIM8 ADC channel 2"
	depends on STM32F7_ADC2
	select STM32F7_HAVE_ADC2_TIMER
	---help---
		Reserve TIM8 to trigger ADC2

config STM32F7_TIM8_ADC3
	bool "TIM8 ADC channel 3"
	depends on STM32F7_ADC3
	select STM32F7_HAVE_ADC3_TIMER
	---help---
		Reserve TIM8 to trigger ADC3

endchoice

config STM32F7_HAVE_ADC1_TIMER
	bool

config STM32F7_HAVE_ADC2_TIMER
	bool

config STM32F7_HAVE_ADC3_TIMER
	bool

config STM32F7_ADC1_SAMPLE_FREQUENCY
	int "ADC1 Sampling Frequency"
	default 100
	depends on STM32F7_HAVE_ADC1_TIMER
	---help---
		ADC1 sampling frequency.  Default:  100Hz

config STM32F7_ADC1_TIMTRIG
	int "ADC1 Timer Trigger"
	default 0
	range 0 4
	depends on STM32F7_HAVE_ADC1_TIMER
	---help---
		Values 0:CC1 1:CC2 2:CC3 3:CC4 4:TRGO

config STM32F7_ADC2_SAMPLE_FREQUENCY
	int "ADC2 Sampling Frequency"
	default 100
	depends on STM32F7_HAVE_ADC2_TIMER
	---help---
		ADC2 sampling frequency.  Default:  100Hz

config STM32F7_ADC2_TIMTRIG
	int "ADC2 Timer Trigger"
	default 0
	range 0 4
	depends on STM32F7_HAVE_ADC2_TIMER
	---help---
		Values 0:CC1 1:CC2 2:CC3 3:CC4 4:TRGO

config STM32F7_ADC3_SAMPLE_FREQUENCY
	int "ADC3 Sampling Frequency"
	default 100
	depends on STM32F7_HAVE_ADC3_TIMER
	---help---
		ADC3 sampling frequency.  Default:  100Hz

config STM32F7_ADC3_TIMTRIG
	int "ADC3 Timer Trigger"
	default 0
	range 0 4
	depends on STM32F7_HAVE_ADC3_TIMER
	---help---
		Values 0:CC1 1:CC2 2:CC3 3:CC4 4:TRGO

config STM32F7_TIM1_DAC
	bool "TIM1 DAC"
	default n
	depends on STM32F7_TIM1 && STM32F7_DAC
	---help---
		Reserve timer 1 for use by DAC

		Timer devices may be used for different purposes.  If STM32F7_TIM1 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM1 DAC channel"
	default STM32F7_TIM1_DAC1
	depends on STM32F7_TIM1_DAC

config STM32F7_TIM1_DAC1
	bool "TIM1 DAC channel 1"
	---help---
		Reserve TIM1 to trigger DAC1

config STM32F7_TIM1_DAC2
	bool "TIM1 DAC channel 2"
	---help---
		Reserve TIM1 to trigger DAC2

endchoice

config STM32F7_TIM2_DAC
	bool "TIM2 DAC"
	default n
	depends on STM32F7_TIM2 && STM32F7_DAC
	---help---
		Reserve timer 2 for use by DAC

		Timer devices may be used for different purposes.  If STM32F7_TIM2 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM2 DAC channel"
	default STM32F7_TIM2_DAC1
	depends on STM32F7_TIM2_DAC

config STM32F7_TIM2_DAC1
	bool "TIM2 DAC channel 1"
	---help---
		Reserve TIM2 to trigger DAC1

config STM32F7_TIM2_DAC2
	bool "TIM2 DAC channel 2"
	---help---
		Reserve TIM2 to trigger DAC2

endchoice

config STM32F7_TIM3_DAC
	bool "TIM3 DAC"
	default n
	depends on STM32F7_TIM3 && STM32F7_DAC
	---help---
		Reserve timer 3 for use by DAC

		Timer devices may be used for different purposes.  If STM32F7_TIM3 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM3 DAC channel"
	default STM32F7_TIM3_DAC1
	depends on STM32F7_TIM3_DAC

config STM32F7_TIM3_DAC1
	bool "TIM3 DAC channel 1"
	---help---
		Reserve TIM3 to trigger DAC1

config STM32F7_TIM3_DAC2
	bool "TIM3 DAC channel 2"
	---help---
		Reserve TIM3 to trigger DAC2

endchoice

config STM32F7_TIM4_DAC
	bool "TIM4 DAC"
	default n
	depends on STM32F7_TIM4 && STM32F7_DAC
	---help---
		Reserve timer 4 for use by DAC

		Timer devices may be used for different purposes.  If STM32F7_TIM4 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM4 DAC channel"
	default STM32F7_TIM4_DAC1
	depends on STM32F7_TIM4_DAC

config STM32F7_TIM4_DAC1
	bool "TIM4 DAC channel 1"
	---help---
		Reserve TIM4 to trigger DAC1

config STM32F7_TIM4_DAC2
	bool "TIM4 DAC channel 2"
	---help---
		Reserve TIM4 to trigger DAC2

endchoice

config STM32F7_TIM5_DAC
	bool "TIM5 DAC"
	default n
	depends on STM32F7_TIM5 && STM32F7_DAC
	---help---
		Reserve timer 5 for use by DAC

		Timer devices may be used for different purposes.  If STM32F7_TIM5 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM5 DAC channel"
	default STM32F7_TIM5_DAC1
	depends on STM32F7_TIM5_DAC

config STM32F7_TIM5_DAC1
	bool "TIM5 DAC channel 1"
	---help---
		Reserve TIM5 to trigger DAC1

config STM32F7_TIM5_DAC2
	bool "TIM5 DAC channel 2"
	---help---
		Reserve TIM5 to trigger DAC2

endchoice

config STM32F7_TIM6_DAC
	bool "TIM6 DAC"
	default n
	depends on STM32F7_TIM6 && STM32F7_DAC
	---help---
		Reserve timer 6 for use by DAC

		Timer devices may be used for different purposes.  If STM32F7_TIM6 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM6 DAC channel"
	default STM32F7_TIM6_DAC1
	depends on STM32F7_TIM6_DAC

config STM32F7_TIM6_DAC1
	bool "TIM6 DAC channel 1"
	---help---
		Reserve TIM6 to trigger DAC1

config STM32F7_TIM6_DAC2
	bool "TIM6 DAC channel 2"
	---help---
		Reserve TIM6 to trigger DAC2

endchoice

config STM32F7_TIM7_DAC
	bool "TIM7 DAC"
	default n
	depends on STM32F7_TIM7 && STM32F7_DAC
	---help---
		Reserve timer 7 for use by DAC

		Timer devices may be used for different purposes.  If STM32F7_TIM7 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM7 DAC channel"
	default STM32F7_TIM7_DAC1
	depends on STM32F7_TIM7_DAC

config STM32F7_TIM7_DAC1
	bool "TIM7 DAC channel 1"
	---help---
		Reserve TIM7 to trigger DAC1

config STM32F7_TIM7_DAC2
	bool "TIM7 DAC channel 2"
	---help---
		Reserve TIM7 to trigger DAC2

endchoice

config STM32F7_TIM8_DAC
	bool "TIM8 DAC"
	default n
	depends on STM32F7_TIM8 && STM32F7_DAC
	---help---
		Reserve timer 8 for use by DAC

		Timer devices may be used for different purposes.  If STM32F7_TIM8 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM8 DAC channel"
	default STM32F7_TIM8_DAC1
	depends on STM32F7_TIM8_DAC

config STM32F7_TIM8_DAC1
	bool "TIM8 DAC channel 1"
	---help---
		Reserve TIM8 to trigger DAC1

config STM32F7_TIM8_DAC2
	bool "TIM8 DAC channel 2"
	---help---
		Reserve TIM8 to trigger DAC2

endchoice

config STM32F7_TIM9_DAC
	bool "TIM9 DAC"
	default n
	depends on STM32F7_TIM9 && STM32F7_DAC
	---help---
		Reserve timer 9 for use by DAC

		Timer devices may be used for different purposes.  If STM32F7_TIM9 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM9 DAC channel"
	default STM32F7_TIM9_DAC1
	depends on STM32F7_TIM9_DAC

config STM32F7_TIM9_DAC1
	bool "TIM9 DAC channel 1"
	---help---
		Reserve TIM9 to trigger DAC1

config STM32F7_TIM9_DAC2
	bool "TIM9 DAC channel 2"
	---help---
		Reserve TIM9 to trigger DAC2

endchoice

config STM32F7_TIM10_DAC
	bool "TIM10 DAC"
	default n
	depends on STM32F7_TIM10 && STM32F7_DAC
	---help---
		Reserve timer 10 for use by DAC

		Timer devices may be used for different purposes.  If STM32F7_TIM10 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM10 DAC channel"
	default STM32F7_TIM10_DAC1
	depends on STM32F7_TIM10_DAC

config STM32F7_TIM10_DAC1
	bool "TIM10 DAC channel 1"
	---help---
		Reserve TIM10 to trigger DAC1

config STM32F7_TIM10_DAC2
	bool "TIM10 DAC channel 2"
	---help---
		Reserve TIM10 to trigger DAC2

endchoice

config STM32F7_TIM11_DAC
	bool "TIM11 DAC"
	default n
	depends on STM32F7_TIM11 && STM32F7_DAC
	---help---
		Reserve timer 11 for use by DAC

		Timer devices may be used for different purposes.  If STM32F7_TIM11 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM11 DAC channel"
	default STM32F7_TIM11_DAC1
	depends on STM32F7_TIM11_DAC

config STM32F7_TIM11_DAC1
	bool "TIM11 DAC channel 1"
	---help---
		Reserve TIM11 to trigger DAC1

config STM32F7_TIM11_DAC2
	bool "TIM11 DAC channel 2"
	---help---
		Reserve TIM11 to trigger DAC2

endchoice

config STM32F7_TIM12_DAC
	bool "TIM12 DAC"
	default n
	depends on STM32F7_TIM12 && STM32F7_DAC
	---help---
		Reserve timer 12 for use by DAC

		Timer devices may be used for different purposes.  If STM32F7_TIM12 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM12 DAC channel"
	default STM32F7_TIM12_DAC1
	depends on STM32F7_TIM12_DAC

config STM32F7_TIM12_DAC1
	bool "TIM12 DAC channel 1"
	---help---
		Reserve TIM12 to trigger DAC1

config STM32F7_TIM12_DAC2
	bool "TIM12 DAC channel 2"
	---help---
		Reserve TIM12 to trigger DAC2

endchoice

config STM32F7_TIM13_DAC
	bool "TIM13 DAC"
	default n
	depends on STM32F7_TIM13 && STM32F7_DAC
	---help---
		Reserve timer 13 for use by DAC

		Timer devices may be used for different purposes.  If STM32F7_TIM13 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM13 DAC channel"
	default STM32F7_TIM13_DAC1
	depends on STM32F7_TIM13_DAC

config STM32F7_TIM13_DAC1
	bool "TIM13 DAC channel 1"
	---help---
		Reserve TIM13 to trigger DAC1

config STM32F7_TIM13_DAC2
	bool "TIM13 DAC channel 2"
	---help---
		Reserve TIM13 to trigger DAC2

endchoice

config STM32F7_TIM14_DAC
	bool "TIM14 DAC"
	default n
	depends on STM32F7_TIM14 && STM32F7_DAC
	---help---
		Reserve timer 14 for use by DAC

		Timer devices may be used for different purposes.  If STM32F7_TIM14 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM14 DAC channel"
	default STM32F7_TIM14_DAC1
	depends on STM32F7_TIM14_DAC

config STM32F7_TIM14_DAC1
	bool "TIM14 DAC channel 1"
	---help---
		Reserve TIM14 to trigger DAC1

config STM32F7_TIM14_DAC2
	bool "TIM14 DAC channel 2"
	---help---
		Reserve TIM14 to trigger DAC2

endchoice

config STM32F7_TIM1_CAP
	bool "TIM1 Capture"
	default n
	depends on STM32F7_TIM1
	---help---
		Reserve timer 1 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

config STM32F7_TIM2_CAP
	bool "TIM2 Capture"
	default n
	depends on STM32F7_TIM2
	---help---
		Reserve timer 2 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

config STM32F7_TIM3_CAP
	bool "TIM3 Capture"
	default n
	depends on STM32F7_TIM3
	---help---
		Reserve timer 3 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

config STM32F7_TIM4_CAP
	bool "TIM4 Capture"
	default n
	depends on STM32F7_TIM4
	---help---
		Reserve timer 4 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

config STM32F7_TIM5_CAP
	bool "TIM5 Capture"
	default n
	depends on STM32F7_TIM5
	---help---
		Reserve timer 5 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

config STM32F7_TIM8_CAP
	bool "TIM8 Capture"
	default n
	depends on STM32F7_TIM8
	---help---
		Reserve timer 8 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

config STM32F7_TIM9_CAP
	bool "TIM9 Capture"
	default n
	depends on STM32F7_TIM9
	---help---
		Reserve timer 9 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

config STM32F7_TIM10_CAP
	bool "TIM10 Capture"
	default n
	depends on STM32F7_TIM10
	---help---
		Reserve timer 10 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

config STM32F7_TIM11_CAP
	bool "TIM11 Capture"
	default n
	depends on STM32F7_TIM11
	---help---
		Reserve timer 11 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

config STM32F7_TIM12_CAP
	bool "TIM12 Capture"
	default n
	depends on STM32F7_TIM12
	---help---
		Reserve timer 12 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

config STM32F7_TIM13_CAP
	bool "TIM13 Capture"
	default n
	depends on STM32F7_TIM13
	---help---
		Reserve timer 13 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

config STM32F7_TIM14_CAP
	bool "TIM14 Capture"
	default n
	depends on STM32F7_TIM14
	---help---
		Reserve timer 14 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

menu "STM32 TIMx Outputs Configuration"

config STM32F7_TIM1_CH1POL
	int "TIM1 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM1_CH1OUT
	---help---
		TIM1 Channel 1 output polarity

config STM32F7_TIM1_CH1IDLE
	int "TIM1 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM1_CH1OUT
	---help---
		TIM1 Channel 1 output IDLE

config STM32F7_TIM1_CH1NPOL
	int "TIM1 Channel 1 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM1_CH1NOUT
	---help---
		TIM1 Channel 1 Complementary Output polarity

config STM32F7_TIM1_CH1NIDLE
	int "TIM1 Channel 1 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM1_CH1NOUT
	---help---
		TIM1 Channel 1 Complementary Output IDLE

config STM32F7_TIM1_CH2POL
	int "TIM1 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM1_CH2OUT
	---help---
		TIM1 Channel 2 output polarity

config STM32F7_TIM1_CH2IDLE
	int "TIM1 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM1_CH2OUT
	---help---
		TIM1 Channel 2 output IDLE

config STM32F7_TIM1_CH2NPOL
	int "TIM1 Channel 2 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM1_CH2NOUT
	---help---
		TIM1 Channel 2 Complementary Output polarity

config STM32F7_TIM1_CH2NIDLE
	int "TIM1 Channel 2 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM1_CH2NOUT
	---help---
		TIM1 Channel 2 Complementary Output IDLE

config STM32F7_TIM1_CH3POL
	int "TIM1 Channel 3 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM1_CH3OUT
	---help---
		TIM1 Channel 3 output polarity

config STM32F7_TIM1_CH3IDLE
	int "TIM1 Channel 3 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM1_CH3OUT
	---help---
		TIM1 Channel 3 output IDLE

config STM32F7_TIM1_CH3NPOL
	int "TIM1 Channel 3 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM1_CH3NOUT
	---help---
		TIM1 Channel 3 Complementary Output polarity

config STM32F7_TIM1_CH3NIDLE
	int "TIM1 Channel 3 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM1_CH3NOUT
	---help---
		TIM1 Channel 3 Complementary Output IDLE

config STM32F7_TIM1_CH4POL
	int "TIM1 Channel 4 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM1_CH4OUT
	---help---
		TIM1 Channel 4 output polarity

config STM32F7_TIM1_CH4IDLE
	int "TIM1 Channel 4 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM1_CH4OUT
	---help---
		TIM1 Channel 4 output IDLE

config STM32F7_TIM1_CH5POL
	int "TIM1 Channel 5 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM1_CH5OUT
	---help---
		TIM1 Channel 5 output polarity

config STM32F7_TIM1_CH5IDLE
	int "TIM1 Channel 5 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM1_CH5OUT
	---help---
		TIM1 Channel 5 output IDLE

config STM32F7_TIM1_CH6POL
	int "TIM1 Channel 6 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM1_CH6OUT
	---help---
		TIM1 Channel 6 output polarity

config STM32F7_TIM1_CH6IDLE
	int "TIM1 Channel 6 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM1_CH6OUT
	---help---
		TIM1 Channel 6 output IDLE

config STM32F7_TIM2_CH1POL
	int "TIM2 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM2_CH1OUT
	---help---
		TIM2 Channel 1 output polarity

config STM32F7_TIM2_CH1IDLE
	int "TIM2 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM2_CH1OUT
	---help---
		TIM2 Channel 1 output IDLE

config STM32F7_TIM2_CH2POL
	int "TIM2 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM2_CH2OUT
	---help---
		TIM2 Channel 2 output polarity

config STM32F7_TIM2_CH2IDLE
	int "TIM2 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM2_CH2OUT
	---help---
		TIM2 Channel 2 output IDLE

config STM32F7_TIM2_CH3POL
	int "TIM2 Channel 3 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM2_CH3OUT
	---help---
		TIM2 Channel 3 output polarity

config STM32F7_TIM2_CH3IDLE
	int "TIM2 Channel 3 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM2_CH3OUT
	---help---
		TIM2 Channel 3 output IDLE

config STM32F7_TIM2_CH4POL
	int "TIM2 Channel 4 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM2_CH4OUT
	---help---
		TIM2 Channel 4 output polarity

config STM32F7_TIM2_CH4IDLE
	int "TIM2 Channel 4 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM2_CH4OUT
	---help---
		TIM2 Channel 4 output IDLE

config STM32F7_TIM3_CH1POL
	int "TIM3 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM3_CH1OUT
	---help---
		TIM3 Channel 1 output polarity

config STM32F7_TIM3_CH1IDLE
	int "TIM3 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM3_CH1OUT
	---help---
		TIM3 Channel 1 output IDLE

config STM32F7_TIM3_CH2POL
	int "TIM3 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM3_CH2OUT
	---help---
		TIM3 Channel 2 output polarity

config STM32F7_TIM3_CH2IDLE
	int "TIM3 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM3_CH2OUT
	---help---
		TIM3 Channel 2 output IDLE

config STM32F7_TIM3_CH3POL
	int "TIM3 Channel 3 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM3_CH3OUT
	---help---
		TIM3 Channel 3 output polarity

config STM32F7_TIM3_CH3IDLE
	int "TIM3 Channel 3 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM3_CH3OUT
	---help---
		TIM3 Channel 3 output IDLE

config STM32F7_TIM3_CH4POL
	int "TIM3 Channel 4 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM3_CH4OUT
	---help---
		TIM3 Channel 4 output polarity

config STM32F7_TIM3_CH4IDLE
	int "TIM3 Channel 4 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM3_CH4OUT
	---help---
		TIM3 Channel 4 output IDLE

config STM32F7_TIM4_CH1POL
	int "TIM4 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM4_CH1OUT
	---help---
		TIM4 Channel 1 output polarity

config STM32F7_TIM4_CH1IDLE
	int "TIM4 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM4_CH1OUT
	---help---
		TIM4 Channel 1 output IDLE

config STM32F7_TIM4_CH2POL
	int "TIM4 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM4_CH2OUT
	---help---
		TIM4 Channel 2 output polarity

config STM32F7_TIM4_CH2IDLE
	int "TIM4 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM4_CH2OUT
	---help---
		TIM4 Channel 2 output IDLE

config STM32F7_TIM4_CH3POL
	int "TIM4 Channel 3 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM4_CH3OUT
	---help---
		TIM4 Channel 3 output polarity

config STM32F7_TIM4_CH3IDLE
	int "TIM4 Channel 3 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM4_CH3OUT
	---help---
		TIM4 Channel 3 output IDLE

config STM32F7_TIM4_CH4POL
	int "TIM4 Channel 4 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM4_CH4OUT
	---help---
		TIM4 Channel 4 output polarity

config STM32F7_TIM4_CH4IDLE
	int "TIM4 Channel 4 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM4_CH4OUT
	---help---
		TIM4 Channel 4 output IDLE

config STM32F7_TIM5_CH1POL
	int "TIM5 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM5_CH1OUT
	---help---
		TIM5 Channel 1 output polarity

config STM32F7_TIM5_CH1IDLE
	int "TIM5 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM5_CH1OUT
	---help---
		TIM5 Channel 1 output IDLE

config STM32F7_TIM5_CH2POL
	int "TIM5 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM5_CH2OUT
	---help---
		TIM5 Channel 2 output polarity

config STM32F7_TIM5_CH2IDLE
	int "TIM5 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM5_CH2OUT
	---help---
		TIM5 Channel 2 output IDLE

config STM32F7_TIM5_CH3POL
	int "TIM5 Channel 3 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM5_CH3OUT
	---help---
		TIM5 Channel 3 output polarity

config STM32F7_TIM5_CH3IDLE
	int "TIM5 Channel 3 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM5_CH3OUT
	---help---
		TIM5 Channel 3 output IDLE

config STM32F7_TIM5_CH4POL
	int "TIM5 Channel 4 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM5_CH4OUT
	---help---
		TIM5 Channel 4 output polarity

config STM32F7_TIM5_CH4IDLE
	int "TIM5 Channel 4 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM5_CH4OUT
	---help---
		TIM5 Channel 4 output IDLE

config STM32F7_TIM8_CH1POL
	int "TIM8 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM8_CH1OUT
	---help---
		TIM8 Channel 1 output polarity

config STM32F7_TIM8_CH1IDLE
	int "TIM8 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM8_CH1OUT
	---help---
		TIM8 Channel 1 output IDLE

config STM32F7_TIM8_CH1NPOL
	int "TIM8 Channel 1 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM8_CH1NOUT
	---help---
		TIM8 Channel 1 Complementary Output polarity

config STM32F7_TIM8_CH1NIDLE
	int "TIM8 Channel 1 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM8_CH1NOUT
	---help---
		TIM8 Channel 1 Complementary Output IDLE

config STM32F7_TIM8_CH2POL
	int "TIM8 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM8_CH2OUT
	---help---
		TIM8 Channel 2 output polarity

config STM32F7_TIM8_CH2IDLE
	int "TIM8 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM8_CH2OUT
	---help---
		TIM8 Channel 2 output IDLE

config STM32F7_TIM8_CH2NPOL
	int "TIM8 Channel 2 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM8_CH2NOUT
	---help---
		TIM8 Channel 2 Complementary Output polarity

config STM32F7_TIM8_CH2NIDLE
	int "TIM8 Channel 2 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM8_CH2NOUT
	---help---
		TIM8 Channel 2 Complementary Output IDLE

config STM32F7_TIM8_CH3POL
	int "TIM8 Channel 3 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM8_CH3OUT
	---help---
		TIM8 Channel 3 output polarity

config STM32F7_TIM8_CH3IDLE
	int "TIM8 Channel 3 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM8_CH3OUT
	---help---
		TIM8 Channel 3 output IDLE

config STM32F7_TIM8_CH3NPOL
	int "TIM8 Channel 3 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM8_CH3NOUT
	---help---
		TIM8 Channel 3 Complementary Output polarity

config STM32F7_TIM8_CH3NIDLE
	int "TIM8 Channel 3 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM8_CH3NOUT
	---help---
		TIM8 Channel 3 Complementary Output IDLE

config STM32F7_TIM8_CH4POL
	int "TIM8 Channel 4 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM8_CH4OUT
	---help---
		TIM8 Channel 4 output polarity

config STM32F7_TIM8_CH4IDLE
	int "TIM8 Channel 4 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM8_CH4OUT
	---help---
		TIM8 Channel 4 output IDLE

config STM32F7_TIM8_CH5POL
	int "TIM8 Channel 5 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM8_CH5OUT
	---help---
		TIM8 Channel 5 output polarity

config STM32F7_TIM8_CH5IDLE
	int "TIM8 Channel 5 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM8_CH5OUT
	---help---
		TIM8 Channel 5 output IDLE

config STM32F7_TIM8_CH6POL
	int "TIM8 Channel 6 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM8_CH6OUT
	---help---
		TIM8 Channel 6 output polarity

config STM32F7_TIM8_CH6IDLE
	int "TIM8 Channel 6 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM8_CH6OUT
	---help---
		TIM8 Channel 6 output IDLE

config STM32F7_TIM9_CH1POL
	int "TIM9 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM9_CH1OUT
	---help---
		TIM9 Channel 1 output polarity

config STM32F7_TIM9_CH1IDLE
	int "TIM9 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM9_CH1OUT
	---help---
		TIM9 Channel 1 output IDLE

config STM32F7_TIM9_CH2POL
	int "TIM9 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM9_CH2OUT
	---help---
		TIM9 Channel 2 output polarity

config STM32F7_TIM9_CH2IDLE
	int "TIM9 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM9_CH2OUT
	---help---
		TIM9 Channel 2 output IDLE

config STM32F7_TIM10_CH1POL
	int "TIM10 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM10_CH1OUT
	---help---
		TIM10 Channel 1 output polarity

config STM32F7_TIM10_CH1IDLE
	int "TIM10 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM10_CH1OUT
	---help---
		TIM10 Channel 1 output IDLE

config STM32F7_TIM11_CH1POL
	int "TIM11 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM11_CH1OUT
	---help---
		TIM11 Channel 1 output polarity

config STM32F7_TIM11_CH1IDLE
	int "TIM11 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM11_CH1OUT
	---help---
		TIM11 Channel 1 output IDLE

config STM32F7_TIM12_CH1POL
	int "TIM12 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM12_CH1OUT
	---help---
		TIM12 Channel 1 output polarity

config STM32F7_TIM12_CH1IDLE
	int "TIM12 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM12_CH1OUT
	---help---
		TIM12 Channel 1 output IDLE

config STM32F7_TIM12_CH2POL
	int "TIM12 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM12_CH2OUT
	---help---
		TIM12 Channel 2 output polarity

config STM32F7_TIM12_CH2IDLE
	int "TIM12 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM12_CH2OUT
	---help---
		TIM12 Channel 2 output IDLE

config STM32F7_TIM13_CH1POL
	int "TIM13 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM13_CH1OUT
	---help---
		TIM13 Channel 1 output polarity

config STM32F7_TIM13_CH1IDLE
	int "TIM13 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM13_CH1OUT
	---help---
		TIM13 Channel 1 output IDLE

config STM32F7_TIM14_CH1POL
	int "TIM14 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32F7_TIM14_CH1OUT
	---help---
		TIM14 Channel 1 output polarity

config STM32F7_TIM14_CH1IDLE
	int "TIM14 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32F7_TIM14_CH1OUT
	---help---
		TIM14 Channel 1 output IDLE

endmenu #STM32 TIMx Outputs Configuration

endmenu # Timer Configuration

menu "CAN driver configuration"
	depends on STM32F7_CAN

config STM32F7_CAN1_BAUD
	int "CAN1 BAUD"
	default 250000
	depends on STM32F7_CAN1
	---help---
		CAN1 BAUD rate.  Required if CONFIG_STM32F7_CAN1 is defined.

config STM32F7_CAN2_BAUD
	int "CAN2 BAUD"
	default 250000
	depends on STM32F7_CAN2
	---help---
		CAN2 BAUD rate.  Required if CONFIG_STM32F7_CAN2 is defined.

config STM32F7_CAN3_BAUD
	int "CAN3 BAUD"
	default 250000
	depends on STM32F7_CAN3
	---help---
		CAN2 BAUD rate.  Required if CONFIG_STM32F7_CAN2 is defined.

config STM32F7_CAN_TSEG1
	int "TSEG1 quanta"
	default 6
	---help---
		The number of CAN time quanta in segment 1. Default: 6

config STM32F7_CAN_TSEG2
	int "TSEG2 quanta"
	default 7
	---help---
		The number of CAN time quanta in segment 2. Default: 7

config STM32F7_CAN_REGDEBUG
	bool "CAN Register level debug"
	depends on DEBUG_CAN_INFO
	default n
	---help---
		Output detailed register-level CAN device debug information.
		Requires also CONFIG_DEBUG_CAN_INFO.

endmenu

menu "ADC Configuration"
	depends on STM32F7_ADC

config STM32F7_ADC1_RESOLUTION
	int "ADC1 resolution"
	depends on STM32F7_ADC1
	default 0
	range 0 3
	---help---
		ADC1 resolution. 0 - 12 bit, 1 - 10 bit, 2 - 8 bit, 3 - 6 bit

config STM32F7_ADC2_RESOLUTION
	int "ADC2 resolution"
	depends on STM32F7_ADC2
	default 0
	range 0 3
	---help---
		ADC2 resolution. 0 - 12 bit, 1 - 10 bit, 2 - 8 bit, 3 - 6 bit

config STM32F7_ADC3_RESOLUTION
	int "ADC3 resolution"
	depends on STM32F7_ADC3
	default 0
	range 0 3
	---help---
		ADC3 resolution. 0 - 12 bit, 1 - 10 bit, 2 - 8 bit, 3 - 6 bit

config STM32F7_ADC_MAX_SAMPLES
	int "The maximum number of channels that can be sampled"
	default 16
	---help---
		The maximum number of samples which can be handled without
		overrun depends on various factors. This is the user's
		responsibility to correctly select this value.
		Since the interface to update the sampling time is available
		for all supported devices, the user can change the default
		values in the board initialization logic and avoid ADC overrun.

config STM32F7_ADC_NO_STARTUP_CONV
	bool "Do not start conversion when opening ADC device"
	default n
	---help---
		Do not start conversion when opening ADC device.

config STM32F7_ADC_NOIRQ
	bool "Do not use default ADC interrupts"
	default n
	---help---
		Do not use default ADC interrupts handlers.

config STM32F7_ADC_LL_OPS
	bool "ADC low-level operations"
	default n
	---help---
		Enable low-level ADC ops.

config STM32F7_ADC_CHANGE_SAMPLETIME
	bool "ADC sample time configuration"
	default n
	depends on STM32F7_ADC_LL_OPS
	---help---
		Enable ADC sample time configuration (SMPRx registers).

config STM32F7_ADC1_DMA
	bool "ADC1 DMA"
	depends on STM32F7_ADC1 && STM32F7_HAVE_ADC1_DMA
	default n
	---help---
		If DMA is selected, then the ADC may be configured to support
		DMA transfer, which is necessary if multiple channels are read
		or if very high trigger frequencies are used.

config STM32F7_ADC1_SCAN
	bool "ADC1 scan mode"
	depends on STM32F7_ADC1
	default y if STM32F7_ADC1_DMA
	default n

config STM32F7_ADC1_DMA_CFG
	int "ADC1 DMA configuration"
	depends on STM32F7_ADC1_DMA
	range 0 1
	default 0
	---help---
		0 - ADC1 DMA in One Shot Mode, 1 - ADC1 DMA in Circular Mode

config STM32F7_ADC1_ANIOC_TRIGGER
	int "ADC1 software trigger (ANIOC_TRIGGER) configuration"
	depends on STM32F7_ADC1
	range 1 3
	default 3
	---help---
		1 - ANIOC_TRIGGER only starts regular conversion
		2 - ANIOC_TRIGGER only starts injected conversion
		3 - ANIOC_TRIGGER starts both regular and injected conversions

config STM32F7_ADC2_DMA
	bool "ADC2 DMA"
	depends on STM32F7_ADC2 && STM32F7_HAVE_ADC2_DMA
	default n
	---help---
		If DMA is selected, then the ADC may be configured to support
		DMA transfer, which is necessary if multiple channels are read
		or if very high trigger frequencies are used.

config STM32F7_ADC2_SCAN
	bool "ADC2 scan mode"
	depends on STM32F7_ADC2
	default y if STM32F7_ADC2_DMA
	default n

config STM32F7_ADC2_DMA_CFG
	int "ADC2 DMA configuration"
	depends on STM32F7_ADC2_DMA
	range 0 1
	default 0
	---help---
		0 - ADC2 DMA in One Shot Mode, 1 - ADC2 DMA in Circular Mode

config STM32F7_ADC2_ANIOC_TRIGGER
	int "ADC2 software trigger (ANIOC_TRIGGER) configuration"
	depends on STM32F7_ADC2
	range 1 3
	default 3
	---help---
		1 - ANIOC_TRIGGER only starts regular conversion
		2 - ANIOC_TRIGGER only starts injected conversion
		3 - ANIOC_TRIGGER starts both regular and injected conversions

config STM32F7_ADC3_DMA
	bool "ADC3 DMA"
	depends on STM32F7_ADC3 && STM32F7_HAVE_ADC3_DMA
	default n
	---help---
		If DMA is selected, then the ADC may be configured to support
		DMA transfer, which is necessary if multiple channels are read
		or if very high trigger frequencies are used.

config STM32F7_ADC3_SCAN
	bool "ADC3 scan mode"
	depends on STM32F7_ADC3
	default y if STM32F7_ADC3_DMA
	default n

config STM32F7_ADC3_DMA_CFG
	int "ADC3 DMA configuration"
	depends on STM32F7_ADC3_DMA
	range 0 1
	default 0
	---help---
		0 - ADC3 DMA in One Shot Mode, 1 - ADC3 DMA in Circular Mode

config STM32F7_ADC3_ANIOC_TRIGGER
	int "ADC3 software trigger (ANIOC_TRIGGER) configuration"
	depends on STM32F7_ADC3
	range 1 3
	default 3
	---help---
		1 - ANIOC_TRIGGER only starts regular conversion
		2 - ANIOC_TRIGGER only starts injected conversion
		3 - ANIOC_TRIGGER starts both regular and injected conversions

config STM32F7_ADC1_INJECTED_CHAN
	int "ADC1 injected channels"
	depends on STM32F7_ADC1
	range 0 4
	default 0
	---help---
		Support for ADC1 injected channels.

config STM32F7_ADC2_INJECTED_CHAN
	int "ADC2 injected channels"
	depends on STM32F7_ADC2
	range 0 4
	default 0
	---help---
		Support for ADC2 injected channels.

config STM32F7_ADC3_INJECTED_CHAN
	int "ADC3 injected channels"
	depends on STM32F7_ADC3
	range 0 4
	default 0
	---help---
		Support for ADC3 injected channels.

config STM32F7_ADC1_EXTSEL
	bool "ADC1 external trigger for regular group"
	depends on STM32F7_ADC1 && !STM32F7_HAVE_ADC1_TIMER
	default n
	---help---
		Enable EXTSEL for ADC1.

config STM32F7_ADC2_EXTSEL
	bool "ADC2 external trigger for regular group"
	depends on STM32F7_ADC2 && !STM32F7_HAVE_ADC2_TIMER
	default n
	---help---
		Enable EXTSEL for ADC2.

config STM32F7_ADC3_EXTSEL
	bool "ADC3 external trigger for regular group"
	depends on STM32F7_ADC3 && !STM32F7_HAVE_ADC3_TIMER
	default n
	---help---
		Enable EXTSEL for ADC3.

config STM32F7_ADC1_JEXTSEL
	bool "ADC1 external trigger for injected group"
	depends on STM32F7_ADC1
	default n
	---help---
		Enable JEXTSEL for ADC1.

config STM32F7_ADC2_JEXTSEL
	bool "ADC2 external trigger for injected group"
	depends on STM32F7_ADC2
	default n
	---help---
		Enable JEXTSEL for ADC2.

config STM32F7_ADC3_JEXTSEL
	bool "ADC3 external trigger for injected group"
	depends on STM32F7_ADC3
	default n
	---help---
		Enable JEXTSEL for ADC3.

endmenu # "ADC Configuration"

menu "Ethernet MAC configuration"
	depends on STM32F7_ETHMAC

config STM32F7_PHYADDR
	int "PHY address"
	default 1
	---help---
		The 5-bit address of the PHY on the board.  Default: 1

config STM32F7_PHYINIT
	bool "Board-specific PHY Initialization"
	default n
	---help---
		Some boards require specialized initialization of the PHY before it can be used.
		This may include such things as configuring GPIOs, resetting the PHY, etc.  If
		STM32F7_PHYINIT is defined in the configuration then the board specific logic must
		provide stm32_phyinitialize();  The STM32 Ethernet driver will call this function
		one time before it first uses the PHY.

config STM32F7_PHY_POLLING
	bool "Support network monitoring by poling the PHY"
	default n
	depends on STM32F7_HAVE_PHY_POLLED
	select ARCH_PHY_POLLED
	---help---
		Some boards may not have an interrupt connected to the PHY.
		This option allows the network monitor to be used by polling the
		the PHY for status.

config STM32F7_MII
	bool "Use MII interface"
	default n
	---help---
		Support Ethernet MII interface.

choice
	prompt "MII clock configuration"
	default STM32F7_MII_EXTCLK
	depends on STM32F7_MII

config STM32F7_MII_MCO1
	bool "Use MC01 as MII clock"
	---help---
		Use MCO1 to clock the MII interface.

config STM32F7_MII_MCO2
	bool "Use MC02 as MII clock"
	---help---
		Use MCO2 to clock the MII interface.

config STM32F7_MII_EXTCLK
	bool "External MII clock"
	---help---
		Clocking is provided by external logic.

endchoice

config STM32F7_AUTONEG
	bool "Use autonegotiation"
	default y
	---help---
		Use PHY autonegotiation to determine speed and mode

config STM32F7_ETHFD
	bool "Full duplex"
	default n
	depends on !STM32F7_AUTONEG
	---help---
		If STM32F7_AUTONEG is not defined, then this may be defined to select full duplex
		mode. Default: half-duplex

config STM32F7_ETH100MBPS
	bool "100 Mbps"
	default n
	depends on !STM32F7_AUTONEG
	---help---
		If STM32F7_AUTONEG is not defined, then this may be defined to select 100 MBps
		speed.  Default: 10 Mbps

config STM32F7_PHYSR
	int "PHY Status Register Address (decimal)"
	depends on STM32F7_AUTONEG
	---help---
		This must be provided if STM32F7_AUTONEG is defined.  The PHY status register
		address may diff from PHY to PHY.  This configuration sets the address of
		the PHY status register.

config STM32F7_PHYSR_ALTCONFIG
	bool "PHY Status Alternate Bit Layout"
	default n
	depends on STM32F7_AUTONEG
	---help---
		Different PHYs present speed and mode information in different ways.  Some
		will present separate information for speed and mode (this is the default).
		Those PHYs, for example, may provide a 10/100 Mbps indication and a separate
		full/half duplex indication. This options selects an alternative representation
		where speed and mode information are combined.  This might mean, for example,
		separate bits for 10HD, 100HD, 10FD and 100FD.

config STM32F7_PHYSR_SPEED
	hex "PHY Speed Mask"
	depends on STM32F7_AUTONEG && !STM32F7_PHYSR_ALTCONFIG
	---help---
		This must be provided if STM32F7_AUTONEG is defined.  This provides bit mask
		for isolating the 10 or 100MBps speed indication.

config STM32F7_PHYSR_100MBPS
	hex "PHY 100Mbps Speed Value"
	depends on STM32F7_AUTONEG && !STM32F7_PHYSR_ALTCONFIG
	---help---
		This must be provided if STM32F7_AUTONEG is defined.  This provides the value
		of the speed bit(s) indicating 100MBps speed.

config STM32F7_PHYSR_MODE
	hex "PHY Mode Mask"
	depends on STM32F7_AUTONEG && !STM32F7_PHYSR_ALTCONFIG
	---help---
		This must be provided if STM32F7_AUTONEG is defined.  This provide bit mask
		for isolating the full or half duplex mode bits.

config STM32F7_PHYSR_FULLDUPLEX
	hex "PHY Full Duplex Mode Value"
	depends on STM32F7_AUTONEG && !STM32F7_PHYSR_ALTCONFIG
	---help---
		This must be provided if STM32F7_AUTONEG is defined.  This provides the
		value of the mode bits indicating full duplex mode.

config STM32F7_PHYSR_ALTMODE
	hex "PHY Mode Mask"
	depends on STM32F7_AUTONEG && STM32F7_PHYSR_ALTCONFIG
	---help---
		This must be provided if STM32F7_AUTONEG is defined.  This provide bit mask
		for isolating the speed and full/half duplex mode bits.

config STM32F7_PHYSR_10HD
	hex "10MBase-T Half Duplex Value"
	depends on STM32F7_AUTONEG && STM32F7_PHYSR_ALTCONFIG
	---help---
		This must be provided if STM32F7_AUTONEG is defined.  This is the value
		under the bit mask that represents the 10Mbps, half duplex setting.

config STM32F7_PHYSR_100HD
	hex "100Base-T Half Duplex Value"
	depends on STM32F7_AUTONEG && STM32F7_PHYSR_ALTCONFIG
	---help---
		This must be provided if STM32F7_AUTONEG is defined.  This is the value
		under the bit mask that represents the 100Mbps, half duplex setting.

config STM32F7_PHYSR_10FD
	hex "10Base-T Full Duplex Value"
	depends on STM32F7_AUTONEG && STM32F7_PHYSR_ALTCONFIG
	---help---
		This must be provided if STM32F7_AUTONEG is defined.  This is the value
		under the bit mask that represents the 10Mbps, full duplex setting.

config STM32F7_PHYSR_100FD
	hex "100Base-T Full Duplex Value"
	depends on STM32F7_AUTONEG && STM32F7_PHYSR_ALTCONFIG
	---help---
		This must be provided if STM32F7_AUTONEG is defined.  This is the value
		under the bit mask that represents the 100Mbps, full duplex setting.

config STM32F7_ETH_PTP
	bool "Precision Time Protocol (PTP)"
	default n
	---help---
		Precision Time Protocol (PTP).  Not supported but some hooks are indicated
		with this condition.

config STM32F7_RMII
	bool
	default y if !STM32F7_MII

choice
	prompt "RMII clock configuration"
	default STM32F7_RMII_EXTCLK
	depends on STM32F7_RMII

config STM32F7_RMII_MCO1
	bool "Use MC01 as RMII clock"
	---help---
		Use MCO1 to clock the RMII interface.

config STM32F7_RMII_MCO2
	bool "Use MC02 as RMII clock"
	---help---
		Use MCO2 to clock the RMII interface.

config STM32F7_RMII_EXTCLK
	bool "External RMII clock"
	---help---
		Clocking is provided by external logic.

endchoice # RMII clock configuration

config STM32F7_ETHMAC_REGDEBUG
	bool "Register-Level Debug"
	default n
	depends on DEBUG_NET_INFO
	---help---
		Enable very low-level register access debug.  Depends on
		CONFIG_DEBUG_FEATURES.

endmenu # Ethernet MAC configuration

if STM32F7_LTDC

menu "LTDC Configuration"

config STM32F7_LTDC_USE_DSI
	bool "Use DSI as display connection"
	default n
	depends on STM32F7_DSIHOST
	---help---
		Select this if your display is connected via DSI.
		Deselect option if your display is connected via digital
		RGB+HSYNC+VSYNC

config STM32F7_LTDC_BACKLIGHT
	bool "Backlight support"
	default y

config STM32F7_LTDC_DEFBACKLIGHT
	hex "Default backlight level"
	default 0xf0

config STM32F7_LTDC_BACKCOLOR
	hex "Background color"
	default 0x0
	---help---
		This is the background color that will be used as the LTDC
		background layer color.  It is an RGB888 format value,
		which gets written unmodified to register LTDC_BCCR.

config STM32F7_LTDC_DITHER
	bool "Dither support"
	default n

config STM32F7_LTDC_DITHER_RED
	depends on STM32F7_LTDC_DITHER
	int "Dither red width"
	range 0 7
	default 2
	---help---
		This is the dither red width.

config STM32F7_LTDC_DITHER_GREEN
	depends on STM32F7_LTDC_DITHER
	int "Dither green width"
	range 0 7
	default 2
	---help---
		This is the dither green width.

config STM32F7_LTDC_DITHER_BLUE
	depends on STM32F7_LTDC_DITHER
	int "Dither blue width"
	range 0 7
	default 2
	---help---
		This is the dither blue width.

config STM32F7_LTDC_FB_BASE
	hex "Framebuffer memory start address"
	default 0
	---help---
		If you are using the LTDC, then you must provide the address
		of the start of the framebuffer.  This address will typically
		be in the SRAM or SDRAM memory region of the FMC.

config STM32F7_LTDC_FB_SIZE
	int "Framebuffer memory size (bytes)"
	default 0
	---help---
		Must be the whole size of the active LTDC layer.

config STM32F7_LTDC_L1_CHROMAKEYEN
	bool "Enable chromakey support for layer 1"
	default y

config STM32F7_LTDC_L1_CHROMAKEY
	hex "Layer L1 initial chroma key"
	default 0x00000000

config STM32F7_LTDC_L1_COLOR
	hex "Layer L1 default color"
	default 0x00000000

choice
	prompt "Layer 1 color format"
	default STM32F7_LTDC_L1_RGB565

config STM32F7_LTDC_L1_L8
	bool "8 bpp L8 (8-bit CLUT)"
	depends on STM32F7_FB_CMAP

config STM32F7_LTDC_L1_AL44
	bool "8 bpp AL44 (4-bit alpha + 4-bit CLUT)"
	depends on STM32F7_FB_CMAP

config STM32F7_LTDC_L1_AL88
	bool "16 bpp AL88 (8-bit alpha + 8-bit CLUT)"
	depends on STM32F7_FB_CMAP

config STM32F7_LTDC_L1_RGB565
	bool "16 bpp RGB 565"
	depends on !STM32F7_FB_CMAP

config STM32F7_LTDC_L1_ARGB4444
	bool "16 bpp ARGB 4444"
	depends on !STM32F7_FB_CMAP

config STM32F7_LTDC_L1_ARGB1555
	bool "16 bpp ARGB 1555"
	depends on !STM32F7_FB_CMAP

config STM32F7_LTDC_L1_RGB888
	bool "24 bpp RGB 888"
	depends on !STM32F7_FB_CMAP

config STM32F7_LTDC_L1_ARGB8888
	bool "32 bpp ARGB 8888"
	depends on !STM32F7_FB_CMAP

endchoice # Layer 1 color format

config STM32F7_LTDC_L2
	bool "Enable Layer 2 support"
	default y

if STM32F7_LTDC_L2

config STM32F7_LTDC_L2_COLOR
	hex "Layer L2 default color"
	default 0x00000000

config STM32F7_LTDC_L2_CHROMAKEYEN
	bool "Enable chromakey support for layer 2"
	default y

config STM32F7_LTDC_L2_CHROMAKEY
	hex "Layer L2 initial chroma key"
	default 0x00000000

choice
	prompt "Layer 2 (top layer) color format"
	default STM32F7_LTDC_L2_RGB565

config STM32F7_LTDC_L2_L8
	depends on STM32F7_LTDC_L1_L8
	bool "8 bpp L8 (8-bit CLUT)"

config STM32F7_LTDC_L2_AL44
	depends on STM32F7_LTDC_L1_AL44
	bool "8 bpp AL44 (4-bit alpha + 4-bit CLUT)"

config STM32F7_LTDC_L2_AL88
	depends on STM32F7_LTDC_L1_AL88
	bool "16 bpp AL88 (8-bit alpha + 8-bit CLUT)"

config STM32F7_LTDC_L2_RGB565
	depends on STM32F7_LTDC_L1_RGB565
	bool "16 bpp RGB 565"

config STM32F7_LTDC_L2_ARGB4444
	depends on STM32F7_LTDC_L1_ARGB4444
	bool "16 bpp ARGB 4444"

config STM32F7_LTDC_L2_ARGB1555
	depends on STM32F7_LTDC_L1_ARGB1555
	bool "16 bpp ARGB 1555"

config STM32F7_LTDC_L2_RGB888
	depends on STM32F7_LTDC_L1_RGB888
	bool "24 bpp RGB 888"

config STM32F7_LTDC_L2_ARGB8888
	depends on STM32F7_LTDC_L1_ARGB8888
	bool "32 bpp ARGB 8888"

endchoice # Layer 2 color format

endif # STM32F7_LTDC_L2

config STM32F7_FB_CMAP
	bool "Color map support"
	default y
	select FB_CMAP
	---help---
		EnablingEnablescolor map support is necessary for ltdc L8 format.

config STM32F7_FB_TRANSPARENCY
	bool "Transparency color map support"
	default y
	depends on STM32F7_FB_CMAP
	select FB_TRANSPARENCY
	---help---
		Enables transparency color map support is necessary for ltdc L8 format.

config STM32F7_LTDC_REGDEBUG
	bool "Enable LTDC register value debug messages"
	default n
	---help---
		This gives additional messages for LTDC related register values.
		Additionally, you have to select "Low-level LCD Debug Features"
		to enable the debug messages.

endmenu

endif # STM32F7_LTDC

if STM32F7_DMA2D

menu "DMA2D Configuration"

config STM32F7_DMA2D_NLAYERS
	int "Number DMA2D overlays"
	default 1
	range 1 256
	---help---
		Number of supported DMA2D layer.

config STM32F7_DMA2D_LAYER_SHARED
	bool "Overlays shared memory region"
	default n
	---help---
		Several overlays can share the same memory region.
		Setup a whole memory area (usually multiple size of the visible screen)
		allows image preprocessing before they become visible by blit operation.

config STM32F7_DMA2D_LAYER_PPLINE
	int "Pixel per line"
	default 1
	range 1 65535
	---help---
		If you are using the DMA2D, then you must provide the pixel per line or
		width of the overlay.

config STM32F7_DMA2D_FB_BASE
	hex "Framebuffer memory start address"
	default 0
	---help---
		If you are using the DMA2D, then you must provide the address
		of the start of the DMA2D overlays framebuffer. This address will typically
		be in the SRAM or SDRAM memory region of the FSMC.

config STM32F7_DMA2D_FB_SIZE
	int "Framebuffer memory size (bytes)"
	default 0
	---help---
		Must be the whole size of all DMA2D overlays.

menu "Supported pixel format"

config STM32F7_DMA2D_L8
	depends on STM32F7_FB_CMAP && STM32F7_LTDC_L1_L8
	bool "8 bpp L8 (8-bit CLUT)"
	default y

config STM32F7_DMA2D_AL44
	depends on STM32F7_FB_CMAP && STM32F7_LTDC_L1_AL44
	bool "8 bpp AL44 (4-bit alpha + 4-bit CLUT)"
	default y

config STM32F7_DMA2D_AL88
	depends on STM32F7_FB_CMAP && STM32F7_LTDC_L1_AL88
	bool "16 bpp AL88 (8-bit alpha + 8-bit CLUT)"
	default y

config STM32F7_DMA2D_RGB565
	bool "16 bpp RGB 565"
	depends on STM32F7_LTDC_L1_RGB565
	default y

config STM32F7_DMA2D_ARGB4444
	bool "16 bpp ARGB 4444"
	depends on STM32F7_LTDC_L1_ARGB4444
	default y

config STM32F7_DMA2D_ARGB1555
	bool "16 bpp ARGB 1555"
	depends on STM32F7_LTDC_L1_ARGB15555
	default y

config STM32F7_DMA2D_RGB888
	bool "24 bpp RGB 888"
	depends on STM32F7_LTDC_L1_RGB888
	default y

config STM32F7_DMA2D_ARGB8888
	bool "32 bpp ARGB 8888"
	depends on STM32F7_LTDC_L1_ARGB8888
	default y

endmenu

config STM32F7_DMA2D_REGDEBUG
	bool "DMA2D Register level debug"
	depends on DEBUG_INFO && DEBUG_LCD
	default n
	---help---
		Output detailed register-level DMA2D device debug information.

endmenu
endif # STM32F7_DMA2D

menu "QEncoder Driver"
	depends on SENSORS_QENCODER
	depends on STM32F7_TIM1 || STM32F7_TIM2 || STM32F7_TIM3 || STM32F7_TIM4 || STM32F7_TIM5 || STM32F7_TIM8

config STM32F7_TIM1_QE
	bool "TIM1"
	default n
	depends on STM32F7_TIM1
	---help---
		Reserve TIM1 for use by QEncoder.

if STM32F7_TIM1_QE

config STM32F7_TIM1_QEPSC
	int "TIM1 pulse prescaler"
	default 1
	---help---
		This prescaler divides the number of recorded encoder pulses,
		limiting the count rate at the expense of resolution.

endif

config STM32F7_TIM2_QE
	bool "TIM2"
	default n
	depends on STM32F7_TIM2
	---help---
		Reserve TIM2 for use by QEncoder.

if STM32F7_TIM2_QE

config STM32F7_TIM2_QEPSC
	int "TIM2 pulse prescaler"
	default 1
	---help---
		This prescaler divides the number of recorded encoder pulses,
		limiting the count rate at the expense of resolution.

endif

config STM32F7_TIM3_QE
	bool "TIM3"
	default n
	depends on STM32F7_TIM3
	---help---
		Reserve TIM3 for use by QEncoder.

if STM32F7_TIM3_QE

config STM32F7_TIM3_QEPSC
	int "TIM3 pulse prescaler"
	default 1
	---help---
		This prescaler divides the number of recorded encoder pulses,
		limiting the count rate at the expense of resolution.

endif

config STM32F7_TIM4_QE
	bool "TIM4"
	default n
	depends on STM32F7_TIM4
	---help---
		Reserve TIM4 for use by QEncoder.

if STM32F7_TIM4_QE

config STM32F7_TIM4_QEPSC
	int "TIM4 pulse prescaler"
	default 1
	---help---
		This prescaler divides the number of recorded encoder pulses,
		limiting the count rate at the expense of resolution.

endif

config STM32F7_TIM5_QE
	bool "TIM5"
	default n
	depends on STM32F7_TIM5
	---help---
		Reserve TIM5 for use by QEncoder.

if STM32F7_TIM5_QE

config STM32F7_TIM5_QEPSC
	int "TIM5 pulse prescaler"
	default 1
	---help---
		This prescaler divides the number of recorded encoder pulses,
		limiting the count rate at the expense of resolution.

endif

config STM32F7_TIM8_QE
	bool "TIM8"
	default n
	depends on STM32F7_TIM8
	---help---
		Reserve TIM8 for use by QEncoder.

if STM32F7_TIM8_QE

config STM32F7_TIM8_QEPSC
	int "TIM8 pulse prescaler"
	default 1
	---help---
		This prescaler divides the number of recorded encoder pulses,
		limiting the count rate at the expense of resolution.

endif

config STM32F7_QENCODER_FILTER
	bool "Enable filtering on STM32 QEncoder input"
	default y

choice
	depends on STM32F7_QENCODER_FILTER
	prompt "Input channel sampling frequency"
	default STM32F7_QENCODER_SAMPLE_FDTS_4

config STM32F7_QENCODER_SAMPLE_FDTS
	bool "fDTS"

config STM32F7_QENCODER_SAMPLE_CKINT
	bool "fCK_INT"

config STM32F7_QENCODER_SAMPLE_FDTS_2
	bool "fDTS/2"

config STM32F7_QENCODER_SAMPLE_FDTS_4
	bool "fDTS/4"

config STM32F7_QENCODER_SAMPLE_FDTS_8
	bool "fDTS/8"

config STM32F7_QENCODER_SAMPLE_FDTS_16
	bool "fDTS/16"

config STM32F7_QENCODER_SAMPLE_FDTS_32
	bool "fDTS/32"

endchoice

choice
	depends on STM32F7_QENCODER_FILTER
	prompt "Input channel event count"
	default STM32F7_QENCODER_SAMPLE_EVENT_6

config STM32F7_QENCODER_SAMPLE_EVENT_1
	depends on STM32F7_QENCODER_SAMPLE_FDTS
	bool "1"

config STM32F7_QENCODER_SAMPLE_EVENT_2
	depends on STM32F7_QENCODER_SAMPLE_CKINT
	bool "2"

config STM32F7_QENCODER_SAMPLE_EVENT_4
	depends on STM32F7_QENCODER_SAMPLE_CKINT
	bool "4"

config STM32F7_QENCODER_SAMPLE_EVENT_5
	depends on STM32F7_QENCODER_SAMPLE_FDTS_16 || STM32F7_QENCODER_SAMPLE_FDTS_32
	bool "5"

config STM32F7_QENCODER_SAMPLE_EVENT_6
	depends on !STM32F7_QENCODER_SAMPLE_FDTS && !STM32F7_QENCODER_SAMPLE_CKINT
	bool "6"

config STM32F7_QENCODER_SAMPLE_EVENT_8
	depends on !STM32F7_QENCODER_SAMPLE_FDTS
	bool "8"

endchoice

endmenu

menu "SAI Configuration"
	depends on STM32F7_SAI

choice
	prompt "Operation mode"
	default STM32F7_SAI_DMA
	---help---
		Select the operation mode the SAI driver should use.

config STM32F7_SAI_POLLING
	bool "Polling"
	---help---
		The SAI registers are polled for events.

config STM32F7_SAI_INTERRUPTS
	bool "Interrupt"
	---help---
		Select to enable interrupt driven SAI support.

config STM32F7_SAI_DMA
	bool "DMA"
	---help---
		Use DMA to improve SAI transfer performance.

endchoice # Operation mode

choice
	prompt "SAI1 synchronization enable"
	default STM32F7_SAI1_BOTH_ASYNC
	depends on STM32F7_SAI1_A && STM32F7_SAI1_B
	---help---
		Select the synchronization mode of the SAI sub-blocks

config STM32F7_SAI1_BOTH_ASYNC
	bool "Both asynchronous"

config STM32F7_SAI1_A_SYNC_WITH_B
	bool "Block A is synchronous with Block B"

config STM32F7_SAI1_B_SYNC_WITH_A
	bool "Block B is synchronous with Block A"

endchoice # SAI1 synchronization enable

choice
	prompt "SAI2 synchronization enable"
	default STM32F7_SAI2_BOTH_ASYNC
	depends on STM32F7_SAI2_A && STM32F7_SAI2_B
	---help---
		Select the synchronization mode of the SAI sub-blocks

config STM32F7_SAI2_BOTH_ASYNC
	bool "Both asynchronous"

config STM32F7_SAI2_A_SYNC_WITH_B
	bool "Block A is synchronous with Block B"

config STM32F7_SAI2_B_SYNC_WITH_A
	bool "Block B is synchronous with Block A"

endchoice # SAI2 synchronization enable

endmenu

endif # ARCH_CHIP_STM32F7
