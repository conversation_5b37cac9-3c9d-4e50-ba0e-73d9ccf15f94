/****************************************************************************
 * arch/arm/src/sama5/hardware/_sama5d2x_pinmap.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_SAMA5_HARDWARE__SAMA5D2X_PINMAP_H
#define __ARCH_ARM_SRC_SAMA5_HARDWARE__SAMA5D2X_PINMAP_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

#include "chip.h"
#include "sam_pio.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* PIO pin definitions ******************************************************
 * Alternate Pin Functions.
 *
 * Alternative pin selections are provided with a numeric suffix like _1, _2,
 * etc.  Drivers, however, will use the pin selection without the numeric
 * suffix.  Additional definitions are required in the board.h file.  For
 * example, if we wanted the PCK0 on PB26, then the following definition
 * should appear in the board.h header file for that board:
 *
 *   #define PIO_PMC_PCK0 PIO_PMC_PCK0_1
 *
 * The PCK logic will then automatically configure PB26 as the PCK0 pin.
 */

/* WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific PIO options such as
 * frequency, open-drain/push-pull, and pull-up/down!  Just the basics are
 * defined for most pins in this file at the present time.
 */

/* Analog-to-Digital Converter - ADC */

#define PIO_ADC_AD0          (PIO_ANALOG | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN19)
#define PIO_ADC_AD1          (PIO_ANALOG | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN20)
#define PIO_ADC_AD2          (PIO_ANALOG | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN21)
#define PIO_ADC_AD3          (PIO_ANALOG | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN22)
#define PIO_ADC_AD4          (PIO_ANALOG | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN23)
#define PIO_ADC_AD5          (PIO_ANALOG | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN24)
#define PIO_ADC_AD6          (PIO_ANALOG | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN25)
#define PIO_ADC_AD7          (PIO_ANALOG | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN26)
#define PIO_ADC_AD8          (PIO_ANALOG | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN27)
#define PIO_ADC_AD9          (PIO_ANALOG | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN28)
#define PIO_ADC_AD10         (PIO_ANALOG | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN29)
#define PIO_ADC_AD11         (PIO_ANALOG | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN30)
#define PIO_ADC_TRG          (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOE | PIO_PIN31)

/* Advanced Interrupt Controller - AIC */

#define PIO_FIQ_1            (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN9)
#define PIO_FIQ_2            (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN3)
#define PIO_FIQ_3            (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN4)
#define PIO_FIQ_4            (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN8)
#define PIO_IRQ_1            (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN21)
#define PIO_IRQ_2            (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN12)
#define PIO_IRQ_3            (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN3)
#define PIO_IRQ_4            (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN31)

/* Control Area Network - CAN */

#define PIO_CAN0_RX_1        (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN2)
#define PIO_CAN0_RX_2        (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN11)
#define PIO_CAN0_TX_1        (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN1)
#define PIO_CAN0_TX_2        (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN10)
#define PIO_CAN1_RX          (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN27)
#define PIO_CAN1_TX          (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN26)

/* Audio Class Amplifier - Class D */

#define PIO_CLASSD_L0        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN28)
#define PIO_CLASSD_L1        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN29)
#define PIO_CLASSD_L2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN30)
#define PIO_CLASSD_L3        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN31)
#define PIO_CLASSD_R0        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN1)
#define PIO_CLASSD_R1        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN2)
#define PIO_CLASSD_R2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN3)
#define PIO_CLASSD_R3        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN4)

/* External Bus Interface - EBI */

#define PIO_EBI_A0_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN11)
#define PIO_EBI_A0_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN11)
#define PIO_EBI_A1_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN12)
#define PIO_EBI_A1_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN12)
#define PIO_EBI_A2_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN13)
#define PIO_EBI_A2_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN13)
#define PIO_EBI_A3_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN14)
#define PIO_EBI_A3_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN14)
#define PIO_EBI_A4_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN15)
#define PIO_EBI_A4_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN15)
#define PIO_EBI_A5_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN16)
#define PIO_EBI_A5_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN16)
#define PIO_EBI_A6_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN17)
#define PIO_EBI_A6_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN17)
#define PIO_EBI_A7_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN18)
#define PIO_EBI_A7_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN18)
#define PIO_EBI_A8_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN19)
#define PIO_EBI_A8_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN19)
#define PIO_EBI_A9_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN20)
#define PIO_EBI_A9_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN20)
#define PIO_EBI_A10_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN21)
#define PIO_EBI_A10_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN21)
#define PIO_EBI_A11_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN22)
#define PIO_EBI_A11_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN22)
#define PIO_EBI_A12_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN23)
#define PIO_EBI_A12_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN23)
#define PIO_EBI_A13_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN24)
#define PIO_EBI_A13_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN24)
#define PIO_EBI_A14_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN25)
#define PIO_EBI_A14_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN25)
#define PIO_EBI_A15_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN26)
#define PIO_EBI_A15_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN26)
#define PIO_EBI_A16_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN27)
#define PIO_EBI_A16_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN27)
#define PIO_EBI_A17_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN28)
#define PIO_EBI_A17_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN28)
#define PIO_EBI_A18_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN29)
#define PIO_EBI_A18_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN29)
#define PIO_EBI_A19_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN30)
#define PIO_EBI_A19_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN30)
#define PIO_EBI_A20_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN31)
#define PIO_EBI_A20_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN31)
#define PIO_EBI_A21_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN0)
#define PIO_EBI_A21_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN10)
#define PIO_EBI_A22_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN1)
#define PIO_EBI_A22_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN11)
#define PIO_EBI_A23_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN0)
#define PIO_EBI_A23_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN0)
#define PIO_EBI_A24_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN1)
#define PIO_EBI_A24_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN1)
#define PIO_EBI_A25_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN2)
#define PIO_EBI_A25_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN2)

#define PIO_EBI_D0_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN22)
#define PIO_EBI_D0_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN0)
#define PIO_EBI_D1_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN23)
#define PIO_EBI_D1_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN1)
#define PIO_EBI_D2_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN24)
#define PIO_EBI_D2_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN2)
#define PIO_EBI_D3_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN25)
#define PIO_EBI_D3_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN3)
#define PIO_EBI_D4_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN26)
#define PIO_EBI_D4_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN4)
#define PIO_EBI_D5_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN27)
#define PIO_EBI_D5_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN5)
#define PIO_EBI_D6_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN28)
#define PIO_EBI_D6_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN6)
#define PIO_EBI_D7_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN29)
#define PIO_EBI_D7_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN7)
#define PIO_EBI_D8_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN3)
#define PIO_EBI_D8_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN13)
#define PIO_EBI_D9_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN4)
#define PIO_EBI_D9_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN14)
#define PIO_EBI_D10_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN5)
#define PIO_EBI_D10_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN15)
#define PIO_EBI_D11_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN6)
#define PIO_EBI_D11_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN16)
#define PIO_EBI_D12_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN7)
#define PIO_EBI_D12_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN17)
#define PIO_EBI_D13_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN8)
#define PIO_EBI_D13_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN18)
#define PIO_EBI_D14_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN9)
#define PIO_EBI_D14_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN19)
#define PIO_EBI_D15_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN10)
#define PIO_EBI_D15_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN20)

#define PIO_EBI_NWAIT_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN3)
#define PIO_EBI_NWAIT_2      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN3)

/* FLEXCOM */

#define PIO_FLEXCOM0_IO0     (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN28)
#define PIO_FLEXCOM0_IO1     (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN29)
#define PIO_FLEXCOM0_IO2     (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN30)
#define PIO_FLEXCOM0_IO3     (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN31)
#define PIO_FLEXCOM0_IO4     (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN0)

#define PIO_FLEXCOM1_IO0     (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN24)
#define PIO_FLEXCOM1_IO1     (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN23)
#define PIO_FLEXCOM1_IO2     (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN22)
#define PIO_FLEXCOM1_IO3     (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN25)
#define PIO_FLEXCOM1_IO4     (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN26)

#define PIO_FLEXCOM2_IO0_1   (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN26)
#define PIO_FLEXCOM2_IO0_2   (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN6)
#define PIO_FLEXCOM2_IO1_1   (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN27)
#define PIO_FLEXCOM2_IO1_2   (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN7)
#define PIO_FLEXCOM2_IO2     (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN28)
#define PIO_FLEXCOM2_IO3_1   (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN29)
#define PIO_FLEXCOM2_IO3_2   (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN9)
#define PIO_FLEXCOM2_IO4_1   (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN30)
#define PIO_FLEXCOM2_IO4_2   (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN10)

#define PIO_FLEXCOM3_IO0_1   (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN15)
#define PIO_FLEXCOM3_IO0_2   (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN23)
#define PIO_FLEXCOM3_IO0_3   (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN20)
#define PIO_FLEXCOM3_IO1_1   (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN13)
#define PIO_FLEXCOM3_IO1_2   (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN22)
#define PIO_FLEXCOM3_IO1_3   (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN19)
#define PIO_FLEXCOM3_IO2_1   (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN14)
#define PIO_FLEXCOM3_IO2_2   (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN21)
#define PIO_FLEXCOM3_IO2_3   (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN18)
#define PIO_FLEXCOM3_IO3_1   (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN16)
#define PIO_FLEXCOM3_IO3_2   (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN24)
#define PIO_FLEXCOM3_IO3_3   (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN21)
#define PIO_FLEXCOM3_IO4_1   (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN17)
#define PIO_FLEXCOM3_IO4_2   (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN25)
#define PIO_FLEXCOM3_IO4_3   (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN22)

#define PIO_FLEXCOM4_IO0_1   (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN28)
#define PIO_FLEXCOM4_IO0_2   (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN12)
#define PIO_FLEXCOM4_IO0_3   (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN21)
#define PIO_FLEXCOM4_IO1_1   (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN29)
#define PIO_FLEXCOM4_IO1_2   (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN13)
#define PIO_FLEXCOM4_IO1_3   (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN22)
#define PIO_FLEXCOM4_IO2_1   (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN30)
#define PIO_FLEXCOM4_IO2_2   (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN14)
#define PIO_FLEXCOM4_IO2_3   (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN23)
#define PIO_FLEXCOM4_IO3_1   (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN31)
#define PIO_FLEXCOM4_IO3_2   (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN15)
#define PIO_FLEXCOM4_IO3_3   (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN24)
#define PIO_FLEXCOM4_IO4_1   (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN0)
#define PIO_FLEXCOM4_IO4_2   (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN16)
#define PIO_FLEXCOM4_IO4_3   (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN25)

/* Ethernet MAC -- EMAC0 */

#define PIO_EMAC0_COL_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN23)
#define PIO_EMAC0_COL_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN4)
#define PIO_EMAC0_COL_3      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN9)
#define PIO_EMAC0_CRS_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN22)
#define PIO_EMAC0_CRS_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN3)
#define PIO_EMAC0_CRS_3      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN8)
#define PIO_EMAC0_MDC_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN18)
#define PIO_EMAC0_MDC_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN17)
#define PIO_EMAC0_MDC_3      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN22)
#define PIO_EMAC0_MDIO_1     (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN19)
#define PIO_EMAC0_MDIO_2     (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN18)
#define PIO_EMAC0_MDIO_3     (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN23)
#define PIO_EMAC0_RX0_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN14)
#define PIO_EMAC0_RX0_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN13)
#define PIO_EMAC0_RX0_3      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN18)
#define PIO_EMAC0_RX1_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN15)
#define PIO_EMAC0_RX1_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN14)
#define PIO_EMAC0_RX1_3      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN19)
#define PIO_EMAC0_RX2_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN24)
#define PIO_EMAC0_RX2_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN5)
#define PIO_EMAC0_RX2_3      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN10)
#define PIO_EMAC0_RX3_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN25)
#define PIO_EMAC0_RX3_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN6)
#define PIO_EMAC0_RX3_3      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN11)
#define PIO_EMAC0_RXCK_1     (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN20)
#define PIO_EMAC0_RXCK_2     (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN1)
#define PIO_EMAC0_RXCK_3     (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN7)
#define PIO_EMAC0_RXDV_1     (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN12)
#define PIO_EMAC0_RXDV_2     (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN11)
#define PIO_EMAC0_RXDV_3     (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN16)
#define PIO_EMAC0_RXER_1     (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN13)
#define PIO_EMAC0_RXER_2     (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN12)
#define PIO_EMAC0_RXER_3     (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN17)
#define PIO_EMAC0_TSUCOMP_1  (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN9)
#define PIO_EMAC0_TSUCOMP_2  (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN0)
#define PIO_EMAC0_TSUCOMP_3  (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN5)
#define PIO_EMAC0_TX0_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN16)
#define PIO_EMAC0_TX0_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN15)
#define PIO_EMAC0_TX0_3      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN20)
#define PIO_EMAC0_TX1_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN17)
#define PIO_EMAC0_TX1_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN16)
#define PIO_EMAC0_TX1_3      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN21)
#define PIO_EMAC0_TX2_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN26)
#define PIO_EMAC0_TX2_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN7)
#define PIO_EMAC0_TX2_3      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN12)
#define PIO_EMAC0_TX3_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN27)
#define PIO_EMAC0_TX3_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN8)
#define PIO_EMAC0_TX3_3      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN13)
#define PIO_EMAC0_TXCK_1     (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN10)
#define PIO_EMAC0_TXCK_2     (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN9)
#define PIO_EMAC0_TXCK_3     (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN14)
#define PIO_EMAC0_TXEN_1     (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN11)
#define PIO_EMAC0_TXEN_2     (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN10)
#define PIO_EMAC0_TXEN_3     (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN15)
#define PIO_EMAC0_TXER_1     (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN21)
#define PIO_EMAC0_TXER_2     (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN2)
#define PIO_EMAC0_TXER_3     (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN6)

/* Static Memory Controller - HSMC */

#define PIO_NANDALE_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN0)
#define PIO_NANDALE_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN10)
#define PIO_NANDCLE_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN1)
#define PIO_NANDCLE_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN11)
#define PIO_NANDOE_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN2)
#define PIO_NANDOE_2         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN12)
#define PIO_NANDRDY_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN8)
#define PIO_NANDRDY_2        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN8)
#define PIO_NANDWE           (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN30)
#define PIO_NBS0_1           (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN11)
#define PIO_NBS0_2           (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN11)
#define PIO_NBS1_1           (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN4)
#define PIO_NBS1_2           (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN7)
#define PIO_NCS0_1           (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN5)
#define PIO_NCS0_2           (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN4)
#define PIO_NCS1_1           (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN6)
#define PIO_NCS1_2           (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN5)
#define PIO_NCS2_1           (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN7)
#define PIO_NCS2_2           (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN6)
#define PIO_NCS3_1           (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN31)
#define PIO_NCS3_2           (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN9)
#define PIO_NRD_1            (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN2)
#define PIO_NRD_2            (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN12)
#define PIO_NWE              (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN30)
#define PIO_NWR1_1           (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN4)
#define PIO_NWR1_2           (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN7)

/* Inter-IC Sound Controller - I2SC */

#define PIO_I2SC0_CK_1       (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN1)
#define PIO_I2SC0_CK_2       (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN19)
#define PIO_I2SC0_DI0_1      (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN4)
#define PIO_I2SC0_DI0_2      (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN22)
#define PIO_I2SC0_DO0_1      (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN5)
#define PIO_I2SC0_DO0_2      (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN23)
#define PIO_I2SC0_MCK_1      (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN2)
#define PIO_I2SC0_MCK_2      (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN20)
#define PIO_I2SC0_WS_1       (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN3)
#define PIO_I2SC0_WS_2       (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN21)

#define PIO_I2SC1_CK_1       (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN15)
#define PIO_I2SC1_CK_2       (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN15)
#define PIO_I2SC1_DI0_1      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN17)
#define PIO_I2SC1_DI0_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN17)
#define PIO_I2SC1_DO0_1      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN18)
#define PIO_I2SC1_DO0_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN18)
#define PIO_I2SC1_MCK_1      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN14)
#define PIO_I2SC1_MCK_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN14)
#define PIO_I2SC1_WS_1       (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN16)
#define PIO_I2SC1_WS_2       (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN16)

/* Image Sensor Controller - ISC */

#define PIO_ISC_D0_1         (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN9)
#define PIO_ISC_D0_2         (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN7)
#define PIO_ISC_D0_3         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN26)
#define PIO_ISC_D1_1         (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN10)
#define PIO_ISC_D1_2         (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN8)
#define PIO_ISC_D1_3         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN27)
#define PIO_ISC_D2_1         (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN11)
#define PIO_ISC_D2_2         (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN9)
#define PIO_ISC_D2_3         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN28)
#define PIO_ISC_D3_1         (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN12)
#define PIO_ISC_D3_2         (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN10)
#define PIO_ISC_D3_3         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN29)
#define PIO_ISC_D4_1         (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN13)
#define PIO_ISC_D4_2         (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN11)
#define PIO_ISC_D4_3         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN30)
#define PIO_ISC_D4_4         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN12)
#define PIO_ISC_D5_1         (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN14)
#define PIO_ISC_D5_2         (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN12)
#define PIO_ISC_D5_3         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN31)
#define PIO_ISC_D5_4         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN13)
#define PIO_ISC_D6_1         (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN15)
#define PIO_ISC_D6_2         (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN13)
#define PIO_ISC_D6_3         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN0)
#define PIO_ISC_D6_4         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN14)
#define PIO_ISC_D7_1         (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN16)
#define PIO_ISC_D7_2         (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN14)
#define PIO_ISC_D7_3         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN1)
#define PIO_ISC_D7_4         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN15)
#define PIO_ISC_D8_1         (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN17)
#define PIO_ISC_D8_2         (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN6)
#define PIO_ISC_D8_3         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN2)
#define PIO_ISC_D8_4         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN16)
#define PIO_ISC_D9_1         (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN18)
#define PIO_ISC_D9_2         (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN5)
#define PIO_ISC_D9_3         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN3)
#define PIO_ISC_D9_4         (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN17)
#define PIO_ISC_D10_1        (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN19)
#define PIO_ISC_D10_2        (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN4)
#define PIO_ISC_D10_3        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN24)
#define PIO_ISC_D10_4        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN18)
#define PIO_ISC_D11_1        (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN20)
#define PIO_ISC_D11_2        (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN3)
#define PIO_ISC_D11_3        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN25)
#define PIO_ISC_D11_4        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN19)
#define PIO_ISC_FIELD_1      (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN25)
#define PIO_ISC_FIELD_2      (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN18)
#define PIO_ISC_FIELD_3      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN8)
#define PIO_ISC_FIELD_4      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN23)
#define PIO_ISC_HSYNC_1      (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN23)
#define PIO_ISC_HSYNC_2      (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN17)
#define PIO_ISC_HSYNC_3      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN6)
#define PIO_ISC_HSYNC_4      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN22)
#define PIO_ISC_MCK_1        (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN24)
#define PIO_ISC_MCK_2        (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN2)
#define PIO_ISC_MCK_3        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN7)
#define PIO_ISC_MCK_4        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN11)
#define PIO_ISC_PCK_1        (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN21)
#define PIO_ISC_PCK_2        (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN15)
#define PIO_ISC_PCK_3        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN4)
#define PIO_ISC_PCK_4        (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN20)
#define PIO_ISC_VSYNC_1      (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN22)
#define PIO_ISC_VSYNC_2      (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN16)
#define PIO_ISC_VSYNC_3      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN5)
#define PIO_ISC_VSYNC_4      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN21)

/* ICE and JTAG */

#define PIO_JTAG_TCK_1       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN14)
#define PIO_JTAG_TCK_2       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN6)
#define PIO_JTAG_TCK_3       (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN27)
#define PIO_JTAG_TCK_4       (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN22)
#define PIO_JTAG_TDI_1       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN15)
#define PIO_JTAG_TDI_2       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN7)
#define PIO_JTAG_TDI_3       (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN28)
#define PIO_JTAG_TDI_4       (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN23)
#define PIO_JTAG_TDO_1       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN16)
#define PIO_JTAG_TDO_2       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN8)
#define PIO_JTAG_TDO_3       (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN29)
#define PIO_JTAG_TDO_4       (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN24)
#define PIO_JTAG_TMS_1       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN17)
#define PIO_JTAG_TMS_2       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN9)
#define PIO_JTAG_TMS_3       (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN30)
#define PIO_JTAG_TMS_4       (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN25)

/* LCD Controller - LCDC */

#define PIO_LCD_DAT0         (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN11)
#define PIO_LCD_DAT1         (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN12)
#define PIO_LCD_DAT2_1       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN13)
#define PIO_LCD_DAT2_2       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN10)
#define PIO_LCD_DAT3_1       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN14)
#define PIO_LCD_DAT3_2       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN11)
#define PIO_LCD_DAT4_1       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN15)
#define PIO_LCD_DAT4_2       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN12)
#define PIO_LCD_DAT5_1       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN16)
#define PIO_LCD_DAT5_2       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN13)
#define PIO_LCD_DAT6_1       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN17)
#define PIO_LCD_DAT6_2       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN14)
#define PIO_LCD_DAT7_1       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN18)
#define PIO_LCD_DAT7_2       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN15)
#define PIO_LCD_DAT8         (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN19)
#define PIO_LCD_DAT9         (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN20)
#define PIO_LCD_DAT10_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN21)
#define PIO_LCD_DAT10_2      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN16)
#define PIO_LCD_DAT11_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN22)
#define PIO_LCD_DAT11_2      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN17)
#define PIO_LCD_DAT12_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN23)
#define PIO_LCD_DAT12_2      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN18)
#define PIO_LCD_DAT13_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN24)
#define PIO_LCD_DAT13_2      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN19)
#define PIO_LCD_DAT14_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN25)
#define PIO_LCD_DAT14_2      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN20)
#define PIO_LCD_DAT15_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN26)
#define PIO_LCD_DAT15_2      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN21)
#define PIO_LCD_DAT16        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN27)
#define PIO_LCD_DAT17        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN28)
#define PIO_LCD_DAT18_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN29)
#define PIO_LCD_DAT18_2      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN22)
#define PIO_LCD_DAT19_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN30)
#define PIO_LCD_DAT19_2      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN23)
#define PIO_LCD_DAT20_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN31)
#define PIO_LCD_DAT20_2      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN24)
#define PIO_LCD_DAT21_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN0)
#define PIO_LCD_DAT21_2      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN25)
#define PIO_LCD_DAT22_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN1)
#define PIO_LCD_DAT22_2      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN26)
#define PIO_LCD_DAT23_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN2)
#define PIO_LCD_DAT23_2      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN27)
#define PIO_LCD_DEN_1        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN8)
#define PIO_LCD_DEN_2        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN1)
#define PIO_LCD_DISP_1       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN29)
#define PIO_LCD_DISP_2       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN4)
#define PIO_LCD_HSYNC_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN31)
#define PIO_LCD_HSYNC_2      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN6)
#define PIO_LCD_PCK_1        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN7)
#define PIO_LCD_PCK_2        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN0)
#define PIO_LCD_PWM_1        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN28)
#define PIO_LCD_PWM_2        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN3)
#define PIO_LCD_VSYNC_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN30)
#define PIO_LCD_VSYNC_2      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN5)

/* Clocks, Oscillators and PLLs */

#define PIO_PMC_PCK0_1       (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN19)
#define PIO_PMC_PCK0_2       (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN8)
#define PIO_PMC_PCK0_3       (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN31)
#define PIO_PMC_PCK1_1       (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN6)
#define PIO_PMC_PCK1_2       (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN13)
#define PIO_PMC_PCK1_3       (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN27)
#define PIO_PMC_PCK1_4       (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN20)
#define PIO_PMC_PCK2_1       (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN21)
#define PIO_PMC_PCK2_2       (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN11)
#define PIO_PMC_PCK2_3       (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN28)

/* Pulse Density Modulation Interface Controller - PDMIC */

#define PIO_PDMIC_CLK_1      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN12)
#define PIO_PDMIC_CLK_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN27)
#define PIO_PDMIC_DAT_1      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN11)
#define PIO_PDMIC_DAT_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN26)

/* Pulse Width Modulation Controller - PWM */

#define PIO_PWM0_EXTRG0      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN3)
#define PIO_PWM0_EXTRG1      (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN10)
#define PIO_PWM0_FI0         (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN2)
#define PIO_PWM0_FI1         (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN9)
#define PIO_PWM0_H0          (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN30)
#define PIO_PWM0_H1          (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN0)
#define PIO_PWM0_H2          (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN5)
#define PIO_PWM0_H3          (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN7)
#define PIO_PWM0_L0          (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN31)
#define PIO_PWM0_L1          (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN1)
#define PIO_PWM0_L2          (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN6)
#define PIO_PWM0_L3          (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN8)

/* Reset Control -- RSTC */

#define PIO_RSTC_NTRST_1     (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN10)
#define PIO_RSTC_NTRST_2     (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN18)
#define PIO_RSTC_NTRST_3     (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN31)
#define PIO_RSTC_NTRST_4     (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN26)

/* Quad IO SPI - QSPI */

#define PIO_QSPI0_CS_1       (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN1)
#define PIO_QSPI0_CS_2       (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN15)
#define PIO_QSPI0_CS_3       (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN23)
#define PIO_QSPI0_IO0_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN2)
#define PIO_QSPI0_IO0_2      (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN16)
#define PIO_QSPI0_IO0_3      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN24)
#define PIO_QSPI0_IO1_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN3)
#define PIO_QSPI0_IO1_2      (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN17)
#define PIO_QSPI0_IO1_3      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN25)
#define PIO_QSPI0_IO2_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN4)
#define PIO_QSPI0_IO2_2      (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN18)
#define PIO_QSPI0_IO2_3      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN26)
#define PIO_QSPI0_IO3_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN5)
#define PIO_QSPI0_IO3_2      (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN19)
#define PIO_QSPI0_IO3_3      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN27)
#define PIO_QSPI0_SCK_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN0)
#define PIO_QSPI0_SCK_2      (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN14)
#define PIO_QSPI0_SCK_3      (PIO_PERIPHF | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN22)

#define PIO_QSPI1_CS_1       (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN11)
#define PIO_QSPI1_CS_2       (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN6)
#define PIO_QSPI1_CS_3       (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN15)
#define PIO_QSPI1_IO0_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN7)
#define PIO_QSPI1_IO0_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN7)
#define PIO_QSPI1_IO0_3      (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN16)
#define PIO_QSPI1_IO1_1      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN8)
#define PIO_QSPI1_IO1_2      (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN17)
#define PIO_QSPI1_IO2_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN9)
#define PIO_QSPI1_IO2_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN9)
#define PIO_QSPI1_IO2_3      (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN18)
#define PIO_QSPI1_IO3_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN10)
#define PIO_QSPI1_IO3_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN10)
#define PIO_QSPI1_IO3_3      (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN19)
#define PIO_QSPI1_SCK_1      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN6)
#define PIO_QSPI1_SCK_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN5)
#define PIO_QSPI1_SCK_3      (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN14)

/* Secure Data Memory Card - SDMMC */

#define PIO_SDMMC0_CD        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN13 | PIO_CFG_DEGLITCH | PIO_INT_BOTHEDGES | PIO_INPUT )
#define PIO_SDMMC0_CK        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN0)
#define PIO_SDMMC0_CMD       (PIO_PERIPHA | PIO_CFG_PULLUP  | PIO_PORT_PIOA | PIO_PIN1)
#define PIO_SDMMC0_DAT0      (PIO_PERIPHA | PIO_CFG_PULLUP  | PIO_PORT_PIOA | PIO_PIN2)
#define PIO_SDMMC0_DAT1      (PIO_PERIPHA | PIO_CFG_PULLUP  | PIO_PORT_PIOA | PIO_PIN3)
#define PIO_SDMMC0_DAT2      (PIO_PERIPHA | PIO_CFG_PULLUP  | PIO_PORT_PIOA | PIO_PIN4)
#define PIO_SDMMC0_DAT3      (PIO_PERIPHA | PIO_CFG_PULLUP  | PIO_PORT_PIOA | PIO_PIN5)
#define PIO_SDMMC0_DAT4      (PIO_PERIPHA | PIO_CFG_PULLUP  | PIO_PORT_PIOA | PIO_PIN6)
#define PIO_SDMMC0_DAT5      (PIO_PERIPHA | PIO_CFG_PULLUP  | PIO_PORT_PIOA | PIO_PIN7)
#define PIO_SDMMC0_DAT6      (PIO_PERIPHA | PIO_CFG_PULLUP  | PIO_PORT_PIOA | PIO_PIN8)
#define PIO_SDMMC0_DAT7      (PIO_PERIPHA | PIO_CFG_PULLUP  | PIO_PORT_PIOA | PIO_PIN9)
#define PIO_SDMMC0_RSTN      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN10)
#define PIO_SDMMC0_VDDSEL    (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN11)
#define PIO_SDMMC0_WP        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN12)

#define PIO_SDMMC1_CD        (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN30)
#define PIO_SDMMC1_CK        (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN22)
#define PIO_SDMMC1_CMD       (PIO_PERIPHE | PIO_CFG_PULLUP  | PIO_PORT_PIOA | PIO_PIN28)
#define PIO_SDMMC1_DAT0      (PIO_PERIPHE | PIO_CFG_PULLUP  | PIO_PORT_PIOA | PIO_PIN18)
#define PIO_SDMMC1_DAT1      (PIO_PERIPHE | PIO_CFG_PULLUP  | PIO_PORT_PIOA | PIO_PIN19)
#define PIO_SDMMC1_DAT2      (PIO_PERIPHE | PIO_CFG_PULLUP  | PIO_PORT_PIOA | PIO_PIN20)
#define PIO_SDMMC1_DAT3      (PIO_PERIPHE | PIO_CFG_PULLUP  | PIO_PORT_PIOA | PIO_PIN21)
#define PIO_SDMMC1_RSTN      (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN27)
#define PIO_SDMMC1_WP        (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN29)

/* Serial Peripheral Interface - SPI */

#define PIO_SPI0_MISO_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN16)
#define PIO_SPI0_MISO_2      (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN31)
#define PIO_SPI0_MOSI_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN15)
#define PIO_SPI0_MOSI_2      (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN0)
#define PIO_SPI0_NPCS0_1     (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN17)
#define PIO_SPI0_NPCS0_2     (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN30)
#define PIO_SPI0_NPCS1_1     (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN18)
#define PIO_SPI0_NPCS1_2     (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN29)
#define PIO_SPI0_NPCS2_1     (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN19)
#define PIO_SPI0_NPCS2_2     (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN27)
#define PIO_SPI0_NPCS3_1     (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN20)
#define PIO_SPI0_NPCS3_2     (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN28)
#define PIO_SPI0_SPCK_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN14)
#define PIO_SPI0_SPCK_2      (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN1)

#define PIO_SPI1_MISO_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN27)
#define PIO_SPI1_MISO_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN24)
#define PIO_SPI1_MISO_3      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN3)
#define PIO_SPI1_MOSI_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN26)
#define PIO_SPI1_MOSI_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN23)
#define PIO_SPI1_MOSI_3      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN2)
#define PIO_SPI1_NPCS0_1     (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN28)
#define PIO_SPI1_NPCS0_2     (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN25)
#define PIO_SPI1_NPCS0_3     (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN4)
#define PIO_SPI1_NPCS1_1     (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN29)
#define PIO_SPI1_NPCS1_2     (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN26)
#define PIO_SPI1_NPCS1_3     (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN5)
#define PIO_SPI1_NPCS2_1     (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN30)
#define PIO_SPI1_NPCS2_2     (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN27)
#define PIO_SPI1_NPCS2_3     (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN6)
#define PIO_SPI1_NPCS3_1     (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN28)
#define PIO_SPI1_NPCS3_2     (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN7)
#define PIO_SPI1_SPCK_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN25)
#define PIO_SPI1_SPCK_2      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN22)
#define PIO_SPI1_SPCK_3      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN1)

/* Synchronous Serial Controller - SSC */

#define PIO_SSC0_RD_1        (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN23)
#define PIO_SSC0_RD_2        (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN15)
#define PIO_SSC0_RF_1        (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN25)
#define PIO_SSC0_RF_2        (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN17)
#define PIO_SSC0_RK_1        (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN24)
#define PIO_SSC0_RK_2        (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN16)
#define PIO_SSC0_TD0_1       (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN22)
#define PIO_SSC0_TD0_2       (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN14)
#define PIO_SSC0_TF0_1       (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN21)
#define PIO_SSC0_TF0_2       (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN13)
#define PIO_SSC0_TK0_1       (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN20)
#define PIO_SSC0_TK0_2       (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN12)

#define PIO_SSC1_RD_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN17)
#define PIO_SSC1_RD_2        (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN17)
#define PIO_SSC1_RF_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN19)
#define PIO_SSC1_RF_2        (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN19)
#define PIO_SSC1_RK_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN18)
#define PIO_SSC1_RK_2        (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN18)
#define PIO_SSC1_TD1_1       (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN16)
#define PIO_SSC1_TD1_2       (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN16)
#define PIO_SSC1_TF1_1       (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN15)
#define PIO_SSC1_TF1_2       (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN15)
#define PIO_SSC1_TK1_1       (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN14)
#define PIO_SSC1_TK1_2       (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN14)

/* Timer/Counter - TC */

#define PIO_TC0_CLK          (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN21)
#define PIO_TC0_IOA          (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN19)
#define PIO_TC0_IOB          (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN20)

#define PIO_TC1_CLK_1        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN29)
#define PIO_TC1_CLK_2        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN13)
#define PIO_TC1_CLK_3        (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN5)
#define PIO_TC1_IOA_1        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN27)
#define PIO_TC1_IOA_2        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN11)
#define PIO_TC1_IOA_3        (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN3)
#define PIO_TC1_IOB_1        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN28)
#define PIO_TC1_IOB_2        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN12)
#define PIO_TC1_IOB_3        (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN4)

#define PIO_TC2_CLK_1        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN5)
#define PIO_TC2_CLK_2        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN22)
#define PIO_TC2_CLK_3        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN24)
#define PIO_TC2_IOA_1        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN6)
#define PIO_TC2_IOA_2        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN20)
#define PIO_TC2_IOA_3        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN22)
#define PIO_TC2_IOB_1        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN7)
#define PIO_TC2_IOB_2        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN21)
#define PIO_TC2_IOB_3        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN23)

#define PIO_TC3_CLK_1        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN8)
#define PIO_TC3_CLK_2        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN21)
#define PIO_TC3_CLK_3        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN31)
#define PIO_TC3_IOA_1        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN9)
#define PIO_TC3_IOA_2        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN19)
#define PIO_TC3_IOA_3        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN29)
#define PIO_TC3_IOB_1        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN10)
#define PIO_TC3_IOB_2        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN20)
#define PIO_TC3_IOB_3        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN30)

#define PIO_TC4_CLK_1        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN11)
#define PIO_TC4_CLK_2        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN11)
#define PIO_TC4_IOA_1        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN9)
#define PIO_TC4_IOA_2        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN9)
#define PIO_TC4_IOB_1        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN10)
#define PIO_TC4_IOB_2        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN10)

#define PIO_TC5_CLK_1        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOa | PIO_PIN8)
#define PIO_TC5_CLK_2        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN30)
#define PIO_TC5_IOA_1        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN6)
#define PIO_TC5_IOA_2        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN28)
#define PIO_TC5_IOB_1        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOA | PIO_PIN7)
#define PIO_TC5_IOB_2        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN29)

/* Two-Wire Interface - TWI */

#define PIO_TWI0_CK_1        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN22)
#define PIO_TWI0_CK_2        (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN0)
#define PIO_TWI0_CK_3        (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN28)
#define PIO_TWI0_CK_4        (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN30)
#define PIO_TWI0_D_1         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN21)
#define PIO_TWI0_D_2         (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN31)
#define PIO_TWI0_D_3         (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN27)
#define PIO_TWI0_D_4         (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN29)

#define PIO_TWI1_CK_1        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN5)
#define PIO_TWI1_CK_2        (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN20)
#define PIO_TWI1_CK_3        (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN7)
#define PIO_TWI1_D_1         (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN4)
#define PIO_TWI1_D_2         (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN19)
#define PIO_TWI1_D_3         (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN6)

/* Universal Asynchronous Receiver Transmitter - UART */

#define PIO_UART0_TXD        (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN27)
#define PIO_UART0_RXD        (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN26)

#define PIO_UART1_RXD_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN2)
#define PIO_UART1_RXD_2      (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN7)
#define PIO_UART1_TXD_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN3)
#define PIO_UART1_TXD_2      (PIO_PERIPHE | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN8)

#define PIO_UART2_RXD_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN23)
#define PIO_UART2_RXD_2      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN4)
#define PIO_UART2_RXD_3      (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN19)
#define PIO_UART2_TXD_1      (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN24)
#define PIO_UART2_TXD_2      (PIO_PERIPHB | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN5)
#define PIO_UART2_TXD_3      (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN20)

#define PIO_UART3_RXD_1      (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN11)
#define PIO_UART3_RXD_2      (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN31)
#define PIO_UART3_RXD_3      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN12)
#define PIO_UART3_TXD_1      (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN12)
#define PIO_UART3_TXD_2      (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN0)
#define PIO_UART3_TXD_3      (PIO_PERIPHD | PIO_CFG_DEFAULT | PIO_PORT_PIOC | PIO_PIN13)

#define PIO_UART4_RXD        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN3)
#define PIO_UART4_TXD        (PIO_PERIPHA | PIO_CFG_DEFAULT | PIO_PORT_PIOB | PIO_PIN4)

/* UTMI */

#define PIO_UTMI_CDRBISTEN   (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN16)
#define PIO_UTMI_CDRCPDIVEN  (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN15)
#define PIO_UTMI_CDRCPSEL0   (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN13)
#define PIO_UTMI_CDRCPSEL1   (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN14)
#define PIO_UTMI_CDRCPSELDIV (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN17)
#define PIO_UTMI_HDIS        (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN10)
#define PIO_UTMI_LS0         (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN11)
#define PIO_UTMI_LS1         (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN12)
#define PIO_UTMI_RXACT       (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN9)
#define PIO_UTMI_RXERR       (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN8)
#define PIO_UTMI_RXVAL       (PIO_PERIPHC | PIO_CFG_DEFAULT | PIO_PORT_PIOD | PIO_PIN7)

#endif /* __ARCH_ARM_SRC_SAMA5_HARDWARE__SAMA5D2X_PINMAP_H */
