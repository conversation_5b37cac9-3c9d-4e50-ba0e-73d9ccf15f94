/****************************************************************************
 * arch/arm/src/sama5/hardware/sam_emac.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_SAMA5_HARDWARE_SAM_EMAC_H
#define __ARCH_ARM_SRC_SAMA5_HARDWARE_SAM_EMAC_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

/* These two EMAC implementations differ in naming and in register layout but
 * are functionally equivalent.  Here they are distinguished as 'A' and 'B'.
 * For now, the 'A' and 'B' drivers are kept separate (mostly because the 'B'
 * driver needs to support two EMAC blocks.
 * But the 'B' driver should replace the 'A' driver someday.
 */

#if defined(CONFIG_SAMA5_EMACA)
#  include "hardware/sam_emaca.h"
#elif defined(CONFIG_SAMA5_EMACB)
#  include "hardware/sam_emacb.h"
#endif

#endif /* __ARCH_ARM_SRC_SAMA5_HARDWARE_SAM_EMAC_H */
