#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

comment "LPC17xx/40xx Configuration Options"

choice
	prompt "NXP LPC17XX/LPC40XX Chip Selection"
	default ARCH_CHIP_LPC1768
	depends on ARCH_CHIP_LPC17XX_40XX

config ARCH_CHIP_LPC1751
	bool "LPC1751"
	select ARCH_FAMILY_LPC175X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1752
	bool "LPC1752"
	select ARCH_FAMILY_LPC175X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1754
	bool "LPC1754"
	select ARCH_FAMILY_LPC175X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1756
	bool "LPC1756"
	select ARCH_FAMILY_LPC175X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1758
	bool "LPC1758"
	select ARCH_FAMILY_LPC175X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1759
	bool "LPC1759"
	select ARCH_FAMILY_LPC175X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1764
	bool "LPC1764"
	select ARCH_FAMILY_LPC176X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1765
	bool "LPC1765"
	select ARCH_FAMILY_LPC176X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1766
	bool "LPC1766"
	select ARCH_FAMILY_LPC176X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1767
	bool "LPC1767"
	select ARCH_FAMILY_LPC176X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1768
	bool "LPC1768"
	select ARCH_FAMILY_LPC176X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1769
	bool "LPC1769"
	select ARCH_FAMILY_LPC176X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1773
	bool "LPC1773"
	select ARCH_FAMILY_LPC177X
	select LPC17_40_HAVE_SPIFI
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1774
	bool "LPC1774"
	select ARCH_FAMILY_LPC177X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1776
	bool "LPC1776"
	select ARCH_FAMILY_LPC177X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1777
	bool "LPC1777"
	select ARCH_FAMILY_LPC177X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1778
	bool "LPC1778"
	select ARCH_FAMILY_LPC177X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1785
	bool "LPC1785"
	select ARCH_FAMILY_LPC178X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1786
	bool "LPC1786"
	select ARCH_FAMILY_LPC178X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1787
	bool "LPC1787"
	select ARCH_FAMILY_LPC178X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC1788
	bool "LPC1788"
	select ARCH_FAMILY_LPC178X
	select ARCH_CORTEXM3

config ARCH_CHIP_LPC4072
	bool "LPC4072"
	select ARCH_FAMILY_LPC407X
	select LPC17_40_HAVE_SPIFI
	select ARCH_CORTEXM4

config ARCH_CHIP_LPC4074
	bool "LPC4074"
	select ARCH_FAMILY_LPC407X
	select LPC17_40_HAVE_SPIFI
	select ARCH_CORTEXM4

config ARCH_CHIP_LPC4076
	bool "LPC4076"
	select ARCH_FAMILY_LPC407X
	select LPC17_40_HAVE_SPIFI
	select ARCH_CORTEXM4
	select ARCH_HAVE_FPU

config ARCH_CHIP_LPC4078
	bool "LPC4078"
	select ARCH_FAMILY_LPC407X
	select LPC17_40_HAVE_SPIFI
	select ARCH_CORTEXM4
	select ARCH_HAVE_FPU

config ARCH_CHIP_LPC4088
	bool "LPC4088"
	select ARCH_FAMILY_LPC408X
	select LPC17_40_HAVE_SPIFI
	select ARCH_CORTEXM4
	select ARCH_HAVE_FPU

endchoice

config ARCH_FAMILY_LPC175X
	bool

config ARCH_FAMILY_LPC176X
	bool

config ARCH_FAMILY_LPC177X
	bool

config ARCH_FAMILY_LPC178X
	bool

config ARCH_FAMILY_LPC407X
	bool

config ARCH_FAMILY_LPC408X
	bool

config LPC17_40_HAVE_SPIFI
	bool

config LPC17_40_CAN
	bool

menu "LPC17xx/LPC40xx Peripheral Support"

config LPC17_40_MAINOSC
	bool "Main oscillator"
	default y

config LPC17_40_PLL0
	bool "PLL0"
	default y

config LPC17_40_PLL1
	bool "PLL1"
	default y

config LPC17_40_EMC
	bool "EMC"
	default y
	depends on ARCH_FAMILY_LPC177X || ARCH_FAMILY_LPC178X || ARCH_FAMILY_LPC407X || ARCH_FAMILY_LPC408X
	select LPC17_40_HAVE_EXTNAND
	select LPC17_40_HAVE_EXTSRAM0
	select LPC17_40_HAVE_EXTDRAM
	select LPC17_40_HAVE_EXTNOR

config LPC17_40_ETHERNET
	bool "Ethernet"
	select NETDEVICES
	select ARCH_HAVE_PHY
	select ARCH_HAVE_NETDEV_STATISTICS
	default n

config LPC17_40_LCD
	bool "LCD controller"
	default n
	depends on ARCH_FAMILY_LPC178X || ARCH_FAMILY_LPC408X

config LPC17_40_USBHOST
	bool "USB host"
	select USBHOST
	select USBHOST_HAVE_ASYNCH
	default n

config LPC17_40_USBDEV
	bool "USB Device"
	select USBDEV
	default n

config LPC17_40_USBOTG
	bool "USB OTG"
	default n
	depends on LPC17_40_USBHOST && LPC17_40_USBDEV

config LPC17_40_SDCARD
	bool "SD Card Interface"
	depends on ARCH_FAMILY_LPC177X || ARCH_FAMILY_LPC178X || ARCH_FAMILY_LPC407X || ARCH_FAMILY_LPC408X
	select ARCH_HAVE_SDIO
	default n

config LPC17_40_UART0
	bool "UART0"
	default n
	select UART0_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config LPC17_40_UART1
	bool "UART1"
	default n
	select UART1_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config LPC17_40_UART2
	bool "UART2"
	default n
	select UART2_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config LPC17_40_UART3
	bool "UART3"
	default n
	select UART3_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config LPC17_40_UART4
	bool "UART4"
	depends on ARCH_FAMILY_LPC177X || ARCH_FAMILY_LPC178X || ARCH_FAMILY_LPC407X || ARCH_FAMILY_LPC408X
	default n
	select UART4_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config LPC17_40_CAN1
	bool "CAN1"
	select LPC17_40_CAN
	default n

config LPC17_40_CAN2
	bool "CAN2"
	select LPC17_40_CAN
	default n

config LPC17_40_SPI
	bool "SPI"
	default n
	depends on ARCH_FAMILY_LPC175X || ARCH_FAMILY_LPC176X

config LPC17_40_SSP0
	bool "SSP0"
	default n

config LPC17_40_SSP1
	bool "SSP1"
	default n

config LPC17_40_SSP2
	bool "SSP2"
	default n
	depends on ARCH_FAMILY_LPC177X || ARCH_FAMILY_LPC178X || ARCH_FAMILY_LPC407X || ARCH_FAMILY_LPC408X

config LPC17_40_SPIFI
	bool "SPIFI Interface"
	depends on LPC17_40_HAVE_SPIFI
	default n

config LPC17_40_I2C0
	bool "I2C0"
	default n

config LPC17_40_I2C1
	bool "I2C1"
	default n

config LPC17_40_I2C2
	bool "I2C2"
	default n

config LPC17_40_I2S
	bool "I2S"
	default n

config LPC17_40_TMR0
	bool "Timer 0"
	default n

config LPC17_40_MAT0_PIN
	int "TIM1 MAT0 Output Pin"
	default 1
	range 1 4
	depends on LPC17_40_TMR0
	---help---
		If TIM1 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

config LPC17_40_TMR1
	bool "Timer 1"
	default n

config LPC17_40_TMR2
	bool "Timer 2"
	default n

config LPC17_40_TMR3
	bool "Timer 3"
	default n

config LPC17_40_RIT
	bool "RIT"
	default n

config LPC17_40_PWM0
	bool "PWM0"
	depends on ARCH_FAMILY_LPC177X || ARCH_FAMILY_LPC178X || ARCH_FAMILY_LPC407X || ARCH_FAMILY_LPC408X
	default n

config LPC17_40_PWM1
	bool "PWM1"
	select ARCH_HAVE_PWM_MULTICHAN
	default n

config LPC17_40_PWM1_PIN
	int "TIM1 PWM Output Pin"
	default 1
	range 1 4
	depends on LPC17_40_PWM1
	---help---
		If TIM1 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

config LPC17_40_PWM1_CHANNEL1
	bool "PWM1 Channel 1"
	default y
	depends on LPC17_40_PWM1
	---help---
		Enable PWM1 channel 1, board has to define pin GPIO_PWM1p1

config LPC17_40_PWM1_CHANNEL2
	bool "PWM1 Channel 2"
	default n
	depends on LPC17_40_PWM1
	---help---
		Enable PWM1 channel 2, board has to define pin GPIO_PWM1p2

config LPC17_40_PWM1_CHANNEL3
	bool "PWM1 Channel 3"
	default n
	depends on LPC17_40_PWM1
	---help---
		Enable PWM1 channel 3, board has to define pin GPIO_PWM1p3

config LPC17_40_PWM1_CHANNEL4
	bool "PWM1 Channel 4"
	default n
	depends on LPC17_40_PWM1
	---help---
		Enable PWM1 channel 4, board has to define pin GPIO_PWM1p4

config LPC17_40_PWM1_CHANNEL5
	bool "PWM1 Channel 5"
	default n
	depends on LPC17_40_PWM1
	---help---
		Enable PWM1 channel 5, board has to define pin GPIO_PWM1p5

config LPC17_40_PWM1_CHANNEL6
	bool "PWM1 Channel 6"
	default n
	depends on LPC17_40_PWM1
	---help---
		Enable PWM1 channel 6, board has to define pin GPIO_PWM1p6

config LPC17_40_MCPWM
	bool "MCPWM"
	default n
	depends on ARCH_FAMILY_LPC175X || ARCH_FAMILY_LPC176X

config LPC17_40_MCPWM1_PIN
	int "TIM1 MCPWM Output Pin"
	default 1
	range 1 4
	depends on LPC17_40_MCPWM
	---help---
		If TIM1 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

config LPC17_40_QEI
	bool "QEI"
	default n

config LPC17_40_RTC
	bool "RTC"
	default n

config LPC17_40_RTCEV
	bool "RTC event monitor"
	default n
	depends on LPC17_40_RTC

config LPC17_40_WDT
	bool "WDT"
	default n
	select WATCHDOG

config LPC17_40_ADC
	bool "ADC"
	default n

config LPC17_40_DAC
	bool "DAC"
	default n

config LPC17_40_GPDMA
	bool "GPDMA"
	default n
	select ARCH_DMA

config LPC17_40_CRC
	bool "CRC engine"
	default n
	depends on ARCH_FAMILY_LPC177X || ARCH_FAMILY_LPC178X || ARCH_FAMILY_LPC407X || ARCH_FAMILY_LPC408X

config LPC17_40_FLASH
	bool "FLASH"
	default n

config LPC17_40_EEPROM
	bool "EEPROM"
	default n
	depends on ARCH_FAMILY_LPC177X || ARCH_FAMILY_LPC178X || ARCH_FAMILY_LPC407X || ARCH_FAMILY_LPC408X

config LPC17_40_PROGMEM
	bool "PROGMEM"
	default n
	select ARCH_HAVE_PROGMEM
	---help---
		Use a part of LPC17xx/LPC40xx's internal flash memory as a
		Memory-Technology-Device (MTD).

endmenu

menu "External Memory Configuration"

config LPC17_40_HAVE_EXTNAND
	bool

config LPC17_40_HAVE_EXTNOR
	bool

config LPC17_40_HAVE_EXTDRAM
	bool

config LPC17_40_HAVE_EXTSRAM0
	bool

config LPC17_40_EXTNAND
	bool "Configure external NAND"
	default n
	depends on LPC17_40_HAVE_EXTNAND
	---help---
		Configure external NAND memory and, if applicable, map then external
		NAND into the memory map.

if LPC17_40_EXTNAND

config LPC17_40_EXTNANDSIZE
	int "External NAND size"
	default 0
	---help---
		Size of the external NAND in bytes.

endif

config LPC17_40_EXTNOR
	bool "Configure external NOR memory"
	default n
	depends on LPC17_40_HAVE_EXTNOR
	---help---
		Configure external NOR memory and, if applicable, map then external
		NOR into the memory map.

if LPC17_40_EXTNOR

config LPC17_40_EXTNORSIZE
	int "External NOR size"
	default 0
	---help---
		Size of the external NOR in bytes.

endif

config LPC17_40_EXTDRAM
	bool "Configure external DRAM"
	default n
	depends on LPC17_40_HAVE_EXTDRAM
	select ARCH_HAVE_SDRAM
	---help---
		Configure external DRAM memory and, if applicable, map then external
		DRAM into the memory map.

if LPC17_40_EXTDRAM

config LPC17_40_EXTDRAMSIZE
	int "External SDRAM size"
	default 0
	---help---
		Size of the external SDRAM in bytes.

choice
	prompt "SDRAM Width Selection"
	default LPC17_40_SDRAM_16BIT

config LPC17_40_SDRAM_8BIT
	bool "8-bit"

config LPC17_40_SDRAM_16BIT
	bool "16-bit"

config LPC17_40_SDRAM_32BIT
	bool "32-bit"

endchoice

config LPC17_40_EXTDRAMHEAP
	bool "Add external SDRAM to the heap"
	default y
	---help---
		Add the external SDRAM into the heap.

config LPC17_40_EXTDRAMHEAP_OFFSET
	int "DRAM heap offset"
	default 0
	depends on LPC17_40_EXTDRAMHEAP
	---help---
		Memory may be reserved at the beginning of DRAM for other purposes
		(for example for video framebuffers).  Memory can similar be
		reserved at the end of DRAM using LPC17_40_EXTDRAMSIZE.  The amount to
		be added to the heap will be from DRAM_BASE + LPC17_40_EXTDRAMHEAP_OFFSET
		through DRAM_BASE + LPC17_40_EXTDRAMSIZE where (DRAM_BASE is the base
		address of CS0).

endif

config LPC17_40_EXTSRAM0
	bool "Configure external SRAM (Bank 0)"
	default n
	depends on LPC17_40_HAVE_EXTSRAM0
	---help---
		Configure external SRAM Bank 0 memory and, if applicable, map then
		external SRAM Bank 0 into the memory map.

if LPC17_40_EXTSRAM0

config LPC17_40_EXTSRAM0SIZE
	int "External SRAM size"
	default 0
	---help---
		Size of the external SRAM Bank 0 in bytes.

config LPC17_40_EXTSRAM0HEAP
	bool "Add external SRAM (Bank 0) to the heap"
	default y
	---help---
		Add external SRAM Bank 0 into the heap.

endif
endmenu

menu "Serial driver options"
	depends on LPC17_40_UART0 || LPC17_40_UART1 || LPC17_40_UART2 || LPC17_40_UART3 || LPC17_40_UART4

config LPC17_40_UART1_RINGINDICATOR
	bool "UART1 ring indicator"
	depends on LPC17_40_UART1
	default n
	---help---
		Enable UART1 ring indicator

config LPC17_40_UART1_RS485
	bool "RS-485 on UART1"
	default n
	depends on LPC17_40_UART1
	---help---
		Enable RS-485 interface on UART1. Your board config will have to
		provide GPIO_UART1_RS485_DIR pin definition.

config LPC17_40_RS485_DIR_POLARITY
	int "UART1 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on LPC17_40_UART1_RS485
	---help---
		Polarity of DIR pin for RS-485 on UART1. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config LPC17_40_UART1_RS485_DIR_DTR
	bool "UART1 RS-485 DIR pin use DTR"
	default n
	depends on LPC17_40_UART1_RS485
	---help---
		Selects between RTS and DTR pins for RS485 DIR. This must correspond to
		the GPIO_USART1_RS485_DIR pin specified in your board config. The DTR pin
		will be used if selected, the RTS pin will be used otherwise.

config LPC17_40_UART_USE_FRACTIONAL_DIVIDER
	bool "Use fractional divider for UART baud rate"
	default n

endmenu

menu "ADC driver options"
	depends on LPC17_40_ADC

config LPC17_40_ADC0_AVERAGE
	int "ADC0 average"
	default 200

config LPC17_40_ADC0_MASK
	hex "ADC0 mask"
	default 0x01

config LPC17_40_ADC0_SPS
	int "ADC0 SPS"
	default 1000

config LPC17_40_ADC_CHANLIST
	bool "Use ADC channel list"
	default n
	---help---
		The errata that states: "A/D Global Data register should not be used
		with burst mode or hardware triggering".  If this option is selected,
		then the ADC driver will grab from the individual channel registers
		rather than from the global data register as this is the stated
		workaround in the errata.

		The ADC interrupt will trigger on conversion complete on the last
		channel listed in the array g_adc_chanlist[] (as opposed to
		triggering interrupt from the global DONE flag).

		If this option is enabled, then the platform specific code must do
		two things:  (1) define LPC17_40_ADC_NCHANNELS in the configuration file
		and (2) provide an array g_adc_chanlist[] with the channel numbers
		matching the LPC17_40_ADC0_MASK within the board-specific library.

config LPC17_40_ADC_BURSTMODE
	bool "One interrupt at the end of all ADC conversions"
	default n
	---help---
		Select this if you want to generate only one interrupt once all selected
		channels has been converted by the ADC

config LPC17_40_ADC_NCHANNELS
	int "ADC0 number of channels"
	depends on LPC17_40_ADC_CHANLIST
	default 0
	---help---
		If LPC17_40_ADC_CHANLIST is enabled, then the platform specific code
		must do two things:  (1) define LPC17_40_ADC_NCHANNELS in the configuration
		file and (2) provide an array g_adc_chanlist[] with the channel
		numbers matching the LPC17_40_ADC0_MASK within the board-specific
		library.

endmenu

menu "CAN driver options"
	depends on LPC17_40_CAN

config LPC17_40_CAN1_BAUD
	int "CAN1 BAUD"
	depends on LPC17_40_CAN1
	---help---
		CAN1 BAUD rate.  Required if LPC17_40_CAN1 is defined.

config LPC17_40_CAN2_BAUD
	int "CAN2 BAUD"
	depends on LPC17_40_CAN2
	---help---
		CAN2 BAUD rate.  Required if LPC17_40_CAN2 is defined.

config LPC17_40_CAN1_DIVISOR
	int "CAN1 CCLK divisor"
	depends on LPC17_40_CAN1
	default 4
	---help---
		CAN1 is clocked at CCLK divided by this number. (the CCLK frequency is divided
		by this number to get the CAN clock). Options = {1,2,4,6}. Default: 4.

config LPC17_40_CAN2_DIVISOR
	int "CAN2 CCLK divisor"
	depends on LPC17_40_CAN2
	default 4
	---help---
		CAN2 is clocked at CCLK divided by this number. (the CCLK frequency is divided
		by this number to get the CAN clock). Options = {1,2,4,6}. Default: 4.

config LPC17_40_CAN_TSEG1
	int "TSEG1 quanta"
	default 6
	---help---
		The number of CAN time quanta in segment 1. Default: 6

config LPC17_40_CAN_TSEG2
	int "TSEG2 quanta"
	default 4
	---help---
		The number of CAN time quanta in segment 2. Default: 7

config LPC17_40_CAN_SAM
	bool "CAN sampling"
	default n
	---help---
		The bus is sampled 3 times (recommended for low to medium speed buses to spikes on the bus-line).

config LPC17_40_CAN_REGDEBUG
	bool "Register level debug"
	depends on DEBUG_CAN_INFO
	default n
	---help---
		Output detailed register-level CAN debug information.  Requires also
		CONFIG_DEBUG_CAN_INFO.

endmenu

config LPC17_40_GPIOIRQ
	bool "GPIO interrupt support"
	default n
	---help---
		Enable support for GPIO interrupts

menu "I2C driver options"
	depends on LPC17_40_I2C0 || LPC17_40_I2C1 || LPC17_40_I2C2

config LPC17_40_I2C0_FREQUENCY
	int "I2C0 frequency"
	depends on LPC17_40_I2C0
	default 100000

config LPC17_40_I2C1_FREQUENCY
	int "I2C1 frequency"
	depends on LPC17_40_I2C1
	default 100000

config LPC17_40_I2C2_FREQUENCY
	int "I2C2 frequency"
	depends on LPC17_40_I2C2
	default 100000

endmenu

menu "SDCARD Configuration"
	depends on LPC17_40_SDCARD

config LPC17_40_SDCARD_DMA
	bool "Support DMA data transfers"
	default y if LPC17_40_GPDMA
	select SDIO_DMA
	depends on LPC17_40_GPDMA
	---help---
		Support DMA data transfers.
		Enable SD card DMA data transfers.  This is marginally optional.
		For most usages, SD accesses will cause data overruns if used without
		DMA.

config LPC17_40_SDCARD_WIDTH_D1_ONLY
	bool "Use D1 only"
	default n
	---help---
		Select 1-bit transfer mode.  This may be selected to force the driver
		operate with only a single data line (the default is to use all
		4 SD data lines).Default: 4-bit transfer mode.

endmenu

menu "Ethernet driver options"
	depends on LPC17_40_ETHERNET

config LPC17_40_PHY_AUTONEG
	bool "Autonegotiation"
	---help---
		Enable auto-negotiation

config LPC17_40_PHY_CEMENT_DISABLE
	bool "Don't re-write the PHY mode"
	---help---
		Re-writing the PHY mode might cause problems with some PHYs.

config LPC17_40_PHY_SPEED100
	bool "100Mbit/Sec"
	depends on !LPC17_40_PHY_AUTONEG
	---help---
		Select 100Mbit vs. 10Mbit speed.

config LPC17_40_PHY_FDUPLEX
	bool "Full duplex"
	depends on !LPC17_40_PHY_AUTONEG
	---help---
		Select full (vs. half) duplex

config LPC17_40_EMACRAM_SIZE
	int "EMAC RAM Size"
	default 16384
	---help---
		Size of EMAC RAM.  Default: 16384 bytes

config LPC17_40_ETH_NTXDESC
	int "Number of Tx descriptors"
	default 13
	---help---
		Configured number of Tx descriptors. Default: 13

config LPC17_40_ETH_NRXDESC
	int "Number of Rx descriptors"
	default 13
	---help---
		Configured number of Rx descriptors. Default: 13

config LPC17_40_NET_WOL
	bool "Wake-up on LAN"
	default n
	---help---
		Enable Wake-up on Lan (not fully implemented).

config LPC17_40_NET_REGDEBUG
	bool "Ethernet register-level debug"
	depends on DEBUG_NET_INFO
	default n
	---help---
		Enable low level register debug.  Also needs CONFIG_DEBUG_NET_INFO.

config LPC17_40_ETH_HASH
	bool "Hashing"
	default n
	---help---
		Enable receipt of near-perfect match frames.

config LPC17_40_MULTICAST
	bool "Multicast"
	default y if NET_MCASTGROUP
	default n if !NET_MCASTGROUP
	---help---
		Enable receipt of multicast (and unicast) frames. Automatically set
		if NET_MCASTGROUP is selected.

endmenu

menu "LCD device driver options"
	depends on LPC17_40_LCD

config LPC17_40_LCD_VRAMBASE
	hex "Video RAM base address"
	default 0xa0010000
	---help---
		Base address of the video RAM frame buffer.  The default is
		(LPC17_40_EXTDRAM_CS0 + 0x00010000)

config LPC17_40_LCD_REFRESH_FREQ
	int "LCD refresh rate (Hz)"
	default 50
	---help---
		LCD refresh rate (Hz)

config LPC17_40_LCD_BACKLIGHT
	bool "Enable backlight"
	default y
	---help---
		Enable backlight support.  If LPC17_40_LCD_BACKLIGHT is selected, then
		the board-specific logic must provide this lpc17_40_backlight()
		interface so that the LCD driver can turn the backlight on and off
		as necessary.  You should select this option and implement
		lpc17_40_backlight() if your board provides GPIO control over the
		backlight.  This interface provides only ON/OFF control of the
		backlight.  If you want finer control over the backlight level (for
		example, using PWM), then this interface would need to be extended.

config LPC17_40_LCD_TFTPANEL
	bool "TFT Panel"
	default y
	---help---
		TFT Panel vs. STN display.  STN display panels require algorithmic
		pixel pattern generation to provide pseudo gray scaling on
		monochrome displays, or color creation on color displays.  TFT
		display panels require the digital color value of each pixel to be
		applied to the display data inputs.

config LPC17_40_LCD_MONOCHROME
	bool "Monochrome LCD"
	default n
	depends on !LPC17_40_LCD_TFTPANEL
	---help---
		STN LCD monochrome/color selection.  Selects monochrome LCD.  This
		selection has no meaning for a TFT panel.

choice
	prompt "Bits per pixel"
	default LPC17_40_LCD_BPP24 if LPC17_40_LCD_TFTPANEL
	default LPC17_40_LCD_BPP16_565 if !LPC17_40_LCD_TFTPANEL

config LPC17_40_LCD_BPP1
	bool "1 bit per pixel"

config LPC17_40_LCD_BPP2
	bool "2 bit per pixel"

config LPC17_40_LCD_BPP4
	bool "4 bit per pixel"

config LPC17_40_LCD_BPP8
	bool "8 bit per pixel"

config LPC17_40_LCD_BPP16
	bool "16 bit per pixel"
	depends on !LPC17_40_LCD_MONOCHROME

config LPC17_40_LCD_BPP24
	bool "24 bit per pixel"
	depends on LPC17_40_LCD_TFTPANEL

config LPC17_40_LCD_BPP16_565
	bool "16 bpp, 5:6:5 mode"
	depends on !LPC17_40_LCD_MONOCHROME

config LPC17_40_LCD_BPP12_444
	bool "12 bpp, 4:4:4 mode"
	depends on !LPC17_40_LCD_MONOCHROME

endchoice

config LPC17_40_LCD_BACKCOLOR
	hex "Initial background color"
	default 0x0
	---help---
		Initial background color

config LPC17_40_LCD_HWIDTH
	int "Display width (pixels)"
	default 480
	---help---
		Horizontal width the display in pixels

config LPC17_40_LCD_HPULSE
	int "Horizontal pulse"
	default 2

config LPC17_40_LCD_HFRONTPORCH
	int "Horizontal front porch"
	default 5

config LPC17_40_LCD_HBACKPORCH
	int "Horizontal back porch"
	default 40

config LPC17_40_LCD_VHEIGHT
	int "Display height (rows)"
	default 272
	---help---
		Vertical height of the display in rows

config LPC17_40_LCD_VPULSE
	int "Vertical pulse"
	default 2

config LPC17_40_LCD_VFRONTPORCH
	int "Vertical front porch"
	default 8

config LPC17_40_LCD_VBACKPORCH
	int "Vertical back porch"
	default 8

endmenu

menu "USB device driver options"
	depends on LPC17_40_USBDEV

config LPC17_40_USBDEV_EP0_MAXSIZE
	int "EP0 Max packet size"
	default 64
	---help---
		Endpoint 0 maximum packet size.  Default: 64

config LPC17_40_USBDEV_FRAME_INTERRUPT
	bool "USB frame interrupt"
	default n
	---help---
		Handle USB Start-Of-Frame events.  Enable reading SOF from interrupt
		handler vs. simply reading on demand. Probably a bad idea... Unless
		there is some issue with sampling the SOF from hardware asynchronously.

config LPC17_40_USBDEV_EPFAST_INTERRUPT
	bool "EP fast interrupt handling"
	default n
	---help---
		Enable high priority interrupts.  I have no idea why you might want to do that

config LPC17_40_USBDEV_NDMADESCRIPTORS
	int "Number of DMA descriptors"
	default 8
	---help---
		Number of DMA descriptors to allocate in SRAM.  Default: 8

config LPC17_40_USBDEV_DMA
	bool "Enable USB device DMA"
	default n
	---help---
		Enable lpc17xx/lpc40xx-specific DMA support

config LPC17_40_USBDEV_NOVBUS
	bool "Disable VBUS support"
	default n
	---help---
		Define if the hardware implementation does not support the VBUS signal

config LPC17_40_USBDEV_NOSOFTCONNECT
	bool "Disable Softconnect support"
	default n
	---help---
		Define if the hardware implementation does not support the Softconnect signal

config LPC17_40_USBDEV_NOLED
	bool "Disable USB device LED support"
	default n
	---help---
		Define if the hardware implementation does not support the LED output

config LPC17_40_USBDEV_REGDEBUG
	bool "Register level debug"
	depends on DEBUG_USB_INFO
	default n
	---help---
		Output detailed register-level USB device debug information.  Requires
		also CONFIG_DEBUG_USB_INFO.

endmenu

menu "USB host driver options"
	depends on LPC17_40_USBHOST

config LPC17_40_OHCIRAM_SIZE
	int "OHCI RAM Size"
	default 16384
	---help---
		Total size of OHCI RAM (in AHB SRAM Bank 1).  Default: 16384

config LPC17_40_USBHOST_NEDS
	int "Number of Endpoint Descriptors"
	default 2
	---help---
		Number of endpoint descriptors.  Default: 2

config LPC17_40_USBHOST_NTDS
	int "Number of transfer descriptors"
	default 3
	---help---
		Number of transfer descriptors. Default: 3

config LPC17_40_USBHOST_TDBUFFERS
	int "Number of descriptor buffers"
	default 2
	---help---
		Number of transfer descriptor buffers.  Default: 2

config LPC17_40_USBHOST_TDBUFSIZE
	int "Descriptor buffer size"
	default 128
	---help---
		Size of one transfer descriptor buffer.  Default 128

config LPC17_40_USBHOST_IOBUFSIZE
	int "I/O buffer size"
	default 512
	---help---
		Size of one end-user I/O buffer.  This can be zero if the application
		can guarantee that all end-user I/O buffers reside in AHB SRAM.

config LPC17_40_USBHOST_NPREALLOC
	int "Max concurrent transfers"
	default 8 if USBHOST_HUB
	default 4 if !USBHOST_HUB
	---help---
		This number represents a number of pre-allocated structures to support
		concurrent data transfers.  This number limits that number of concurrent
		asynchronous IN endpoint transfer that can be supported.

config LPC17_40_USBHOST_REGDEBUG
	bool "Register level debug"
	depends on DEBUG_USB_INFO
	default n
	---help---
		Output detailed register-level USB host debug information.  Requires
		also CONFIG_DEBUG_USB_INFO.

endmenu
