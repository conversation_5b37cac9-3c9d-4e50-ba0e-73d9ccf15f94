/****************************************************************************
 * arch/arm/src/lpc17xx_40xx/hardware/lpc178x_40xx_pinconfig.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_LPC17XX_40XX_HARDWARE_LPC178X_40XX_PINCONFIG_H
#define __ARCH_ARM_SRC_LPC17XX_40XX_HARDWARE_LPC178X_40XX_PINCONFIG_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* GPIO pin definitions *****************************************************/

/* NOTE
 * that functions have a alternate pins that can be selected.  These
 * alternates are identified with a numerical suffix like _1, _2, or _3.
 * Your board.h file should select the correct alternative for your board
 * by including definitions such as:
 *
 * #define GPIO_UART1_RXD GPIO_UART1_RXD_1
 *
 * (without the suffix)
 */

#define GPIO_CAN1_RD_1     (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN0)
#define GPIO_UART3_TXD_1   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN0)
#define GPIO_I2C1_SDA_1    (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN0)
#define GPIO_UART0_TXD_1   (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN0)

#define GPIO_CAN1_TD_1     (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN1)
#define GPIO_UART3_RXD_1   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN1)
#define GPIO_I2C1_SCL_1    (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN1)
#define GPIO_UART0_RXD_1   (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN1)

#define GPIO_UART0_TXD_2   (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN2)
#define GPIO_UART3_TXD_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN2)

#define GPIO_UART0_RXD_2   (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN3)
#define GPIO_UART3_RXD_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN3)

#define GPIO_I2S_RXCLK_1   (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN4)
#define GPIO_CAN2_RD_1     (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN4)
#define GPIO_CAP2p0_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN4)
#define GPIO_CMP_ROSC_1    (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN4) /* Only on LPC40xx */
#define GPIO_LCD_VD0_1     (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN4)

#define GPIO_I2S_RXWS_1    (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN5)
#define GPIO_CAN2_TD_1     (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN5)
#define GPIO_CAP2p1_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN5)
#define GPIO_CMP_RESET     (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN5) /* Only on LPC40xx */
#define GPIO_LCD_VD1_1     (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN5)

#define GPIO_I2S_RXSDA_1   (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN6)
#define GPIO_SSP1_SSEL_1   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN6)
#define GPIO_MAT2p0_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN6)
#define GPIO_UART1_RTS_1   (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN6)
#define GPIO_CMP_ROSC_2    (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN6) /* Only on LPC40xx */
#define GPIO_LCD_VD8_1     (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN6)

#define GPIO_I2S_TXCLK_1   (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_SSP1_SCK_1    (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_MAT2p1_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_RTC_EV0_1     (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_CMP_VREF      (GPIO_ALT5 | GPIO_FLOAT | GPIO_MODE_ANALOG | GPIO_PORT0 | GPIO_PIN7) /* Only on LPC40xx */
#define GPIO_LCD_VD9_1     (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN7)

#define GPIO_I2S_TXWS_1    (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN8)
#define GPIO_SSP1_MISO_1   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN8)
#define GPIO_MAT2p2_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN8)
#define GPIO_RTC_EV1_1     (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN8)
#define GPIO_CMP1_IN_4     (GPIO_ALT5 | GPIO_FLOAT | GPIO_MODE_ANALOG | GPIO_PORT0 | GPIO_PIN8) /* Only on LPC40xx */
#define GPIO_LCD_VD16      (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN8)

#define GPIO_I2S_TXSDA_1   (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN9)
#define GPIO_SSP1_MOSI_1   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN9)
#define GPIO_MAT2p3_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN9)
#define GPIO_RTC_EV2_1     (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN9)
#define GPIO_CMP1_IN_3     (GPIO_ALT5 | GPIO_FLOAT | GPIO_MODE_ANALOG | GPIO_PORT0 | GPIO_PIN9) /* Only on LPC40xx */
#define GPIO_LCD_VD17      (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN9)

#define GPIO_UART2_TXD_1   (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN10)
#define GPIO_I2C2_SDA_1    (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN10)
#define GPIO_MAT3p0_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN10)
#define GPIO_LCD_VD5_3     (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN10) /* Only on LPC40xx */

#define GPIO_UART2_RXD_1   (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN11)
#define GPIO_I2C2_SCL_1    (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN11)
#define GPIO_MAT3p1_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN11)
#define GPIO_LCD_VD10_4    (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN11) /* Only on LPC40xx */

#define GPIO_USB_PPWR2     (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN12)
#define GPIO_SSP1_MISO_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN12)
#define GPIO_AD0p6         (GPIO_ALT3 | GPIO_FLOAT | GPIO_MODE_ANALOG | GPIO_PORT0 | GPIO_PIN12)

#define GPIO_USB_LED2      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN13)
#define GPIO_SSP1_MOSI_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN13)
#define GPIO_AD0p7         (GPIO_ALT3 | GPIO_FLOAT | GPIO_MODE_ANALOG | GPIO_PORT0 | GPIO_PIN13)

#define GPIO_USB_HSTEN2    (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN14)
#define GPIO_SSP1_SSEL_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN14)
#define GPIO_USB_CONNECT2  (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN14)

#define GPIO_UART1_TXD_1   (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN15)
#define GPIO_SSP0_SCK_1    (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN15)
#define GPIO_SPIFI_IO2     (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN15)

#define GPIO_UART1_RXD_1   (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN16)
#define GPIO_SSP0_SSEL_1   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN16)
#define GPIO_SPIFI_IO3     (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN16)

#define GPIO_UART1_CTS_1   (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN17)
#define GPIO_SSP0_MISO_1   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN17)
#define GPIO_SPIFI_IO1     (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN17)

#define GPIO_UART1_DCD_1   (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN18)
#define GPIO_SSP0_MOSI_1   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN18)
#define GPIO_SPIFI_IO0     (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN18)

#define GPIO_UART1_DSR_1   (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN19)
#define GPIO_SD_CLK_1      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN19)
#define GPIO_I2C1_SDA_2    (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN19)
#define GPIO_LCD_VD13_3    (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN19) /* Only on LPC40xx */

#define GPIO_UART1_DTR_1   (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN20)
#define GPIO_SD_CMD_1      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN20)
#define GPIO_I2C1_SCL_2    (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN20)
#define GPIO_LCD_VD14_3    (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN20) /* Only on LPC40xx */

#define GPIO_UART1_RI_1    (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN21)
#define GPIO_SD_PWR_1      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN21)
#define GPIO_UART4_OE      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN21)
#define GPIO_CAN1_RD_2     (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN21)
#define GPIO_UART4_SCLK    (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN21)

#define GPIO_UART1_RTS_2   (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN22)
#define GPIO_SD_DAT0_1     (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN22)
#define GPIO_UART4_TXD_1   (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN22)
#define GPIO_CAN1_TD_2     (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN22)
#define GPIO_SPIFI_SCLK    (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN22)

#define GPIO_AD0p0         (GPIO_ALT1 | GPIO_FLOAT | GPIO_MODE_ANALOG | GPIO_PORT0 | GPIO_PIN23)
#define GPIO_I2S_RXCLK_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN23)
#define GPIO_CAP3p0_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN23)

#define GPIO_AD0p1         (GPIO_ALT1 | GPIO_FLOAT | GPIO_MODE_ANALOG | GPIO_PORT0 | GPIO_PIN24)
#define GPIO_I2S_RXWS_2    (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN24)
#define GPIO_CAP3p1_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN24)

#define GPIO_AD0p2         (GPIO_ALT1 | GPIO_FLOAT | GPIO_MODE_ANALOG | GPIO_PORT0 | GPIO_PIN25)
#define GPIO_I2S_RXSDA_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN25)
#define GPIO_UART3_TXD_3   (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN25)

#define GPIO_AD0p3         (GPIO_ALT1 | GPIO_FLOAT | GPIO_MODE_ANALOG | GPIO_PORT0 | GPIO_PIN26)
#define GPIO_AOUT          (GPIO_ALT2 | GPIO_FLOAT | GPIO_MODE_ANALOG | GPIO_PORT0 | GPIO_PIN26)
#define GPIO_UART3_RXD_3   (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN26)

#define GPIO_I2C0_SDA_1    (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN27)
#define GPIO_USB_SDA       (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN27)

#define GPIO_I2C0_SCL_1    (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN28)
#define GPIO_USB_SCL       (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN28)

#define GPIO_USB1DP        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN29)
#define GPIO_EINT0_1       (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN29)

#define GPIO_USB1DM        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN30)
#define GPIO_EINT1_1       (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN30)

#define GPIO_USB2_DP       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT0 | GPIO_PIN31)

#define GPIO_ENET_TXD0     (GPIO_ALT1 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN0)
#define GPIO_CAP3p1_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN0)
#define GPIO_SSP2_SCK_1    (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN0)

#define GPIO_ENET_TXD1     (GPIO_ALT1 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN1)
#define GPIO_MAT3p3_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN1)
#define GPIO_SSP2_MOSI_1   (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN1)

#define GPIO_ENET_TXD2     (GPIO_ALT1 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN2)
#define GPIO_SD_CLK_2      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN2)
#define GPIO_PWM0p1_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN2)

#define GPIO_ENET_TXD3     (GPIO_ALT1 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN3)
#define GPIO_SD_CMD_2      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN3)
#define GPIO_PWM0p2_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN3)

#define GPIO_ENET_TXEN     (GPIO_ALT1 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN4)
#define GPIO_MAT3p2_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN4)
#define GPIO_SSP2_MISO     (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN4)

#define GPIO_ENET_TX_ER    (GPIO_ALT1 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_SD_PWR_2      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_PWM0p3_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_CMP1_IN_2     (GPIO_ALT5 | GPIO_FLOAT | GPIO_MODE_ANALOG | GPIO_PORT1 | GPIO_PIN5) /* Only on LPC40xx */

#define GPIO_ENET_TX_CLK   (GPIO_ALT1 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN6)
#define GPIO_SD_DAT0_2     (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN6)
#define GPIO_PWM0p4_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN6)
#define GPIO_CMP0_IN_4     (GPIO_ALT5 | GPIO_FLOAT | GPIO_MODE_ANALOG | GPIO_PORT1 | GPIO_PIN6) /* Only on LPC40xx */

#define GPIO_ENET_COL      (GPIO_ALT1 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN7)
#define GPIO_SD_DAT1_1     (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN7)
#define GPIO_PWM0p5_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN7)
#define GPIO_CMP1_IN_1     (GPIO_ALT5 | GPIO_FLOAT | GPIO_MODE_ANALOG | GPIO_PORT1 | GPIO_PIN7) /* Only on LPC40xx */

#define GPIO_ENET_CRSDV    (GPIO_ALT1 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN8)
#define GPIO_ENET_CRS      (GPIO_ENET_CRSDV) /* Alternate pin name for LPC17 Ethernet driver */
#define GPIO_MAT3p1_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN8)
#define GPIO_SSP2_SSEL_1   (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN8)

#define GPIO_ENET_RXD0     (GPIO_ALT1 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN9)
#define GPIO_MAT3p0_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN9)

#define GPIO_ENET_RXD1     (GPIO_ALT1 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN10)
#define GPIO_CAP3p0_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN10)

#define GPIO_ENET_RXD2     (GPIO_ALT1 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN11)
#define GPIO_SD_DAT2_1     (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN11)
#define GPIO_PWM0p6_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN11)

#define GPIO_ENET_RXD3     (GPIO_ALT1 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN12)
#define GPIO_SD_DAT3_1     (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN12)
#define GPIO_PWM0CAPp0_1   (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN12)
#define GPIO_CMP1_OUT      (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN12) /* Only on LPC40xx */

#define GPIO_ENET_RX_DV    (GPIO_ALT1 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN13)

#define GPIO_ENET_RXER     (GPIO_ALT1 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN14)
#define GPIO_CAP2p0_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN14)
#define GPIO_CMP0_IN_1     (GPIO_ALT5 | GPIO_FLOAT | GPIO_MODE_ANALOG | GPIO_PORT1 | GPIO_PIN14) /* Only on LPC40xx */

#define GPIO_ENET_REFCLK   (GPIO_ALT1 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN15)
#define GPIO_I2C2_SDA_2    (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN15)

#define GPIO_ENET_MDC_1    (GPIO_ALT1 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN16)
#define GPIO_I2S_TXMCLK    (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN16)
#define GPIO_CMP0_IN_2     (GPIO_ALT5 | GPIO_FLOAT | GPIO_MODE_ANALOG | GPIO_PORT1 | GPIO_PIN16) /* Only on LPC40xx */

#define GPIO_ENET_MDIO_1   (GPIO_ALT1 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN17)
#define GPIO_I2S_RXMCLK    (GPIO_ALT2 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN17)
#define GPIO_CMP0_IN_3     (GPIO_ALT5 | GPIO_FLOAT | GPIO_MODE_ANALOG | GPIO_PORT1 | GPIO_PIN17) /* Only on LPC40xx */

#define GPIO_USB_UPLED     (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN18)
#define GPIO_PWM1p1_1      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN18)
#define GPIO_CAP1p0_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN18)
#define GPIO_SSP1_MISO_3   (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN18)

#define GPIO_USB1_TXE      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN19)
#define GPIO_USB1_PPWR     (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN19)
#define GPIO_CAP1p1_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN19)
#define GPIO_MCPWM_MC0A    (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN19)
#define GPIO_SSP1_SCK_2    (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN19)
#define GPIO_UART2_OE_1    (GPIO_ALT6 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN19)

#define GPIO_USB1_TXDP     (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN20)
#define GPIO_PWM1p2_1      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN20)
#define GPIO_QEI_PHA       (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN20)
#define GPIO_MCPWM_MCFB0   (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN20)
#define GPIO_SSP0_SCK_2    (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN20)
#define GPIO_LCD_VD6_1     (GPIO_ALT6 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN20)
#define GPIO_LCD_VD10_1    (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN20)

#define GPIO_USB1_TXDM     (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN21)
#define GPIO_PWM1p3_1      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN21)
#define GPIO_SSP0_SSEL_2   (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN21)
#define GPIO_MCPWM_ABORT   (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN21)
#define GPIO_LCD_VD7_1     (GPIO_ALT6 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN21)
#define GPIO_LCD_VD11_1    (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN21)

#define GPIO_USB1_RCV      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN22)
#define GPIO_USB1_PWRD     (GPIO_ALT2 | GPIO_PULLDN | GPIO_PORT1 | GPIO_PIN22)
#define GPIO_MAT1p0_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN22)
#define GPIO_MCPWM_MCOB    (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN22)
#define GPIO_SSP1_MOSI_3   (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN22)
#define GPIO_LCD_VD8_2     (GPIO_ALT6 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN22)
#define GPIO_LCD_VD12_1    (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN22)

#define GPIO_USB1_RXDP     (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN23)
#define GPIO_PWM1p4_1      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN23)
#define GPIO_QEI_PHB       (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN23)
#define GPIO_MCPWM_MCFB1   (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN23)
#define GPIO_SSP0_MOSI_2   (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN23)
#define GPIO_LCD_VD9_2     (GPIO_ALT6 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN23)
#define GPIO_LCD_VD13_1    (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN23)

#define GPIO_USB1_RXDM     (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN24)
#define GPIO_PWM1p5_1      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN24)
#define GPIO_QEI_IDX       (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN24)
#define GPIO_MCPWM_MCFB2   (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN24)
#define GPIO_SSP0_MOSI_3   (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN24)
#define GPIO_LCD_VD10_2    (GPIO_ALT6 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN24)
#define GPIO_LCD_VD14_1    (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN24)

#define GPIO_USB1_LS       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN25)
#define GPIO_USB1_HSTEN    (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN25)
#define GPIO_MAT1p1_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN25)
#define GPIO_MCPWM_MC1A    (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN25)
#define GPIO_CLKOUT_       (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN25)
#define GPIO_LCD_VD11_2    (GPIO_ALT6 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN25)
#define GPIO_LCD_VD15_1    (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN25)

#define GPIO_USB1_SSPND    (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN26)
#define GPIO_PWM1p6_1      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN26)
#define GPIO_CAP0p0_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN26)
#define GPIO_MCPWM_MC1B    (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN26)
#define GPIO_SSP1_SSEL_3   (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN26)
#define GPIO_LCD_VD12_2    (GPIO_ALT6 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN26)
#define GPIO_LCD_VD20      (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN26)

#define GPIO_USB1_INT      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN27)
#define GPIO_USB1_OVRCR    (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN27)
#define GPIO_CAP0p1_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN27)
#define GPIO_CLKOUT_2      (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN27)
#define GPIO_LCD_VD13_2    (GPIO_ALT6 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN27)
#define GPIO_LCD_VD21      (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN27)

#define GPIO_USB1_SCL      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN28)
#define GPIO_PCAP1p0_1     (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN28)
#define GPIO_MAT0p0_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN28)
#define GPIO_MCPWM_MC2A    (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN28)
#define GPIO_SSP0_SSEL_3   (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN28)
#define GPIO_LCD_VD14_2    (GPIO_ALT6 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN28)
#define GPIO_LCD_VD22      (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN28)

#define GPIO_USB1_SDA      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN29)
#define GPIO_PCAP1p1       (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN29)
#define GPIO_MAT0p1_1      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN29)
#define GPIO_MCPWM_MC2B    (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN29)
#define GPIO_UART4_TXD_2   (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN29)
#define GPIO_LCD_VD15_2    (GPIO_ALT6 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN29)
#define GPIO_LCD_VD23      (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN29)

#define GPIO_USB2_PWRD     (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN30)
#define GPIO_USB_VBUS      (GPIO_ALT2 | GPIO_FLOAT  | GPIO_PORT1 | GPIO_PIN30)
#define GPIO_AD0p4         (GPIO_ALT3 | GPIO_FLOAT | GPIO_MODE_ANALOG | GPIO_PORT1 | GPIO_PIN30)
#define GPIO_I2C0_SDA_2    (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN30)
#define GPIO_UART3_OE      (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN30)

#define GPIO_USB2_OVRCR    (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN31)
#define GPIO_SSP1_SCK_3    (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN31)
#define GPIO_AD0p5         (GPIO_ALT3 | GPIO_FLOAT | GPIO_MODE_ANALOG | GPIO_PORT1 | GPIO_PIN31)
#define GPIO_I2C0_SCL_2    (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT1 | GPIO_PIN31)

#define GPIO_PWM1p1_2      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN0)
#define GPIO_UART1_TXD_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN0)
#define GPIO_LCD_PWR       (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN0)

#define GPIO_PWM1p2_2      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN1)
#define GPIO_UART1_RXD_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN1)
#define GPIO_LCD_LE        (GPIO_ALT7 | GPIO_FLOAT | GPIO_PORT2 | GPIO_PIN1)

#define GPIO_PWM1p3_2      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_UART1_CTS_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_MAT2p3_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_TRACEDATA3    (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_LCD_DCLK      (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN2 )

#define GPIO_PWM1p4_2      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_UART1_DCD_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_MAT2p2_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_TRACEDATA2    (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_LCD_FP        (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN3)

#define GPIO_PWM1p5_2      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_UART1_DSR_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_MAT2p1_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_TRACEDATA1    (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_LCD_ENABM     (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN4)

#define GPIO_PWM1p6_2      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_UART1_DTR_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_MAT2p0_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_TRACEDATA0    (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_LCD_LP        (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN5)

#define GPIO_PCAP1p0_2     (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN6)
#define GPIO_UART1_RI_2    (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN6)
#define GPIO_CAP2p0_3      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN6)
#define GPIO_UART2_OE_2    (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN6)
#define GPIO_TRACECLK      (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN6)
#define GPIO_LCD_VD0_2     (GPIO_ALT6 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN6)
#define GPIO_LCD_VD4_1     (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN6)

#define GPIO_CAN2_RD_2     (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN7)
#define GPIO_UART1_RTS_3   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN7)
#define GPIO_SPIFI_CS      (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN7)
#define GPIO_LCD_VD1_2     (GPIO_ALT6 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN7)
#define GPIO_LCD_VD5_1     (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN7)

#define GPIO_CAN2_TD_2     (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_UART2_TXD_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_UART1_CTS_3   (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_ENET_MDC_2    (GPIO_ALT4 | GPIO_FLOAT  | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_LCD_VD2_1     (GPIO_ALT6 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_LCD_VD6_2     (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN8)

#define GPIO_USB1_CONNECT  (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_UART2_RXD_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_UART4_RXD_1   (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_ENET_MDIO_2   (GPIO_ALT4 | GPIO_FLOAT  | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_LCD_VD3_1     (GPIO_ALT6 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_LCD_VD7_2     (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN9)

#define GPIO_EINT0_2       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN10)
#define GPIO_NMI           (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN10)

#define GPIO_EINT1_2       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN11)
#define GPIO_SD_DAT1_2     (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN11)
#define GPIO_I2S_TXCLK_2   (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN11)
#define GPIO_LCD_CLKIN     (GPIO_ALT7 | GPIO_FLOAT | GPIO_PORT2 | GPIO_PIN11)

#define GPIO_EINT2         (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN12)
#define GPIO_SD_DAT2_2     (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN12)
#define GPIO_I2S_TXWS_2    (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN12)
#define GPIO_LCD_VD4_2     (GPIO_ALT4 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN12)
#define GPIO_LCD_VD3_2     (GPIO_ALT5 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN12)
#define GPIO_LCD_VD8_3     (GPIO_ALT6 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN12)
#define GPIO_LCD_VD18      (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN12)

#define GPIO_EINT3         (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN13)
#define GPIO_SD_DAT3_2     (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN13)
#define GPIO_I2S_TXSDA_2   (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN13)
#define GPIO_LCD_VD5_2     (GPIO_ALT5 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN13)
#define GPIO_LCD_VD9_3     (GPIO_ALT6 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN13)
#define GPIO_LCD_VD19      (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT2 | GPIO_PIN13)

#define GPIO_EMC_CS2       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN14)
#define GPIO_I2C1_SDA_3    (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN14)
#define GPIO_CAP2p0_4      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN14)

#define GPIO_EMC_CS3       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN15)
#define GPIO_I2C1_SCL      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN15)
#define GPIO_CAP2p1_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN15)

#define GPIO_EMC_CAS       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN16)
#define GPIO_EMC_RAS       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN17)
#define GPIO_EMC_CLK0      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN18)

#define GPIO_EMC_CLK1      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN19)
#define GPIO_EMC_DYCS0     (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN20)
#define GPIO_EMC_DYCS1     (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN21)

#define GPIO_EMC_DYCS2     (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN22)
#define GPIO_SSP0_SCK_3    (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN22)
#define GPIO_CAP3p0_3      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN22)

#define GPIO_EMC_DYCS3     (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN23)
#define GPIO_SSP0_SSEL_4   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN23)
#define GPIO_CAP3p1_3      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN23)

#define GPIO_EMC_CKE0      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN24)
#define GPIO_EMC_CKE1      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN25)

#define GPIO_EMC_CKE2      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN26)
#define GPIO_SSP0_MISO_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN26)
#define GPIO_MAT3p0_3      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN26)

#define GPIO_EMC_CKE3      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN27)
#define GPIO_SSP0_MOSI_4   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN27)
#define GPIO_MAT3p1_3      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN27)

#define GPIO_EMC_DQM0      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN28)
#define GPIO_EMC_DQM1      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN29)

#define GPIO_EMC_DQM2      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN30)
#define GPIO_I2C2_SDA_3    (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN30)
#define GPIO_MAT3p2_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN30)

#define GPIO_EMC_DQM3      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN31)
#define GPIO_I2C2_SCL_2    (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN31)
#define GPIO_MAT3p3_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT2 | GPIO_PIN31)

#define GPIO_EMC_D0        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN0)
#define GPIO_EMC_D1        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN1)
#define GPIO_EMC_D2        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN2)
#define GPIO_EMC_D3        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN3)
#define GPIO_EMC_D4        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN4)
#define GPIO_EMC_D5        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN5)
#define GPIO_EMC_D6        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN6)
#define GPIO_EMC_D7        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN7)
#define GPIO_EMC_D8        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN8)
#define GPIO_EMC_D9        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN9)
#define GPIO_EMC_D10       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN10)
#define GPIO_EMC_D11       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN11)
#define GPIO_EMC_D12       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN12)
#define GPIO_EMC_D13       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN13)
#define GPIO_EMC_D14       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN14)
#define GPIO_EMC_D15       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN15)

#define GPIO_EMC_D16       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN16)
#define GPIO_PWM0p1_2      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN16)
#define GPIO_UART1_TXD_3   (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN16)

#define GPIO_EMC_D17       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN17)
#define GPIO_PWM0p2_2      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN17)
#define GPIO_UART1_RXD_3   (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN17)

#define GPIO_EMC_D18       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN18)
#define GPIO_PWM0p3_2      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN18)
#define GPIO_UART1_CTS_4   (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN18)

#define GPIO_EMC_D19       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN19)
#define GPIO_PWM0p4_2      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN19)
#define GPIO_UART1_DCD_3   (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN19)

#define GPIO_EMC_D20       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN20)
#define GPIO_PWM0p5_2      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN20)
#define GPIO_UART1_DSR_3   (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN20)

#define GPIO_EMC_D21       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN21)
#define GPIO_PWM0p6_2      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN21)
#define GPIO_UART1_DTR_3   (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN21)

#define GPIO_EMC_D22       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN22)
#define GPIO_PWM0CAPp0_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN22)
#define GPIO_UART1_RI_3    (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN22)

#define GPIO_EMC_D23       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN23)
#define GPIO_PWM1CAPp0     (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN23)
#define GPIO_CAP0p0_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN23)

#define GPIO_EMC_D24       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN24)
#define GPIO_PWM1p1_3      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN24)
#define GPIO_CAP0p1_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN24)

#define GPIO_EMC_D25       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN25)
#define GPIO_PWM1p2_3      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN25)
#define GPIO_MAT0p0_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN25)

#define GPIO_EMC_D26       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN26)
#define GPIO_PWM1p3_3      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN26)
#define GPIO_MAT0p1_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN26)
#define GPIO_STCLK         (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN26)

#define GPIO_EMC_D27       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN27)
#define GPIO_PWM1p4_3      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN27)
#define GPIO_CAP1p0_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN27)

#define GPIO_EMC_D28       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN28)
#define GPIO_PWM1p5_3      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN28)
#define GPIO_CAP1p1_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN28)

#define GPIO_EMC_D29       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN29)
#define GPIO_PWM1p6_3      (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN29)
#define GPIO_MAT1p0_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN29)

#define GPIO_EMC_D30       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN30)
#define GPIO_UART1_RTS_4   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN30)
#define GPIO_MAT1p1_2      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN30)

#define GPIO_EMC_D31       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN31)
#define GPIO_MAT1p2        (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT3 | GPIO_PIN31)

#define GPIO_EMC_A0        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN0)
#define GPIO_EMC_A1        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN1)
#define GPIO_EMC_A2        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN2)
#define GPIO_EMC_A3        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN3)
#define GPIO_EMC_A4        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN4)
#define GPIO_EMC_A5        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN5)
#define GPIO_EMC_A6        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN6)
#define GPIO_EMC_A7        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN7)
#define GPIO_EMC_A8        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN8)
#define GPIO_EMC_A9        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN9)
#define GPIO_EMC_A10       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN10)
#define GPIO_EMC_A11       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN11)
#define GPIO_EMC_A12       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN12)
#define GPIO_EMC_A13       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN13)
#define GPIO_EMC_A14       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN14)
#define GPIO_EMC_A15       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN15)
#define GPIO_EMC_A16       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN16)
#define GPIO_EMC_A17       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN17)
#define GPIO_EMC_A18       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN18)
#define GPIO_EMC_A19       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN19)

#define GPIO_EMC_A20       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN20)
#define GPIO_I2C2_SDA_4    (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN20)
#define GPIO_SSP1_SCK_4    (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN20)

#define GPIO_EMC_A21       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN21)
#define GPIO_I2C2_SCL_4    (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN21)
#define GPIO_SSP1_SSEL_4   (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN21)

#define GPIO_EMC_A22       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN22)
#define GPIO_UART2_TXD_3   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN22)
#define GPIO_SSP1_MISO_4   (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN22)

#define GPIO_EMC_A23       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN23)
#define GPIO_UART2_RXD_3   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN23)
#define GPIO_SSP1_MOSI_4   (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN23)

#define GPIO_EMC_OE        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN24)
#define GPIO_EMC_WE        (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN25)
#define GPIO_EMC_BLS0      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN26)
#define GPIO_EMC_BLS1      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN27)

#define GPIO_EMC_BLS2      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN28)
#define GPIO_UART3_TXD_4   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN28)
#define GPIO_MAT2p0_3      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN28)
#define GPIO_LCD_VD6_3     (GPIO_ALT5 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN28)
#define GPIO_LCD_VD10_3    (GPIO_ALT6 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN28)
#define GPIO_LCD_VD2_2     (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN28)

#define GPIO_EMC_BLS3      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN29)
#define GPIO_UART3_RXD_4   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN29)
#define GPIO_MAT2p1_3      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN29)
#define GPIO_I2C2_SCL_3    (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN29)
#define GPIO_LCD_VD7_3     (GPIO_ALT5 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN29)
#define GPIO_LCD_VD11_3    (GPIO_ALT6 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN29)
#define GPIO_LCD_VD3_3     (GPIO_ALT7 | GPIO_FLOAT | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN29)

#define GPIO_EMC_CS0       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN30)
#define GPIO_CMP0_OUT      (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN30) /* Only on LPC40xx */

#define GPIO_EMC_CS1       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT4 | GPIO_PIN31)

#define GPIO_EMC_A24       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT5 | GPIO_PIN0)
#define GPIO_SSP2_MOSI_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT5 | GPIO_PIN0)
#define GPIO_MAT2p2_3      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT5 | GPIO_PIN0)

#define GPIO_EMC_A25       (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT5 | GPIO_PIN1)
#define GPIO_SSP2_MISO_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT5 | GPIO_PIN1)
#define GPIO_MAT2p3_3      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT5 | GPIO_PIN1)

#define GPIO_SSP2_SCK_2    (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT5 | GPIO_PIN2) /* Only on LPC40xx */
#define GPIO_MAT3p2_3      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT5 | GPIO_PIN2)
#define GPIO_I2C0_SDA_3    (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT5 | GPIO_PIN2)

#define GPIO_SSP2_SSEL_2   (GPIO_ALT2 | GPIO_PULLUP | GPIO_PORT5 | GPIO_PIN3) /* Only on LPC40xx */
#define GPIO_UART4_RXD_2   (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT5 | GPIO_PIN3)
#define GPIO_I2C0_SCL0     (GPIO_ALT5 | GPIO_PULLUP | GPIO_PORT5 | GPIO_PIN3)

#define GPIO_UART0_OE      (GPIO_ALT1 | GPIO_PULLUP | GPIO_PORT5 | GPIO_PIN4)
#define GPIO_MAT3p3_3      (GPIO_ALT3 | GPIO_PULLUP | GPIO_PORT5 | GPIO_PIN4)
#define GPIO_UART4_TXD_3   (GPIO_ALT4 | GPIO_PULLUP | GPIO_PORT5 | GPIO_PIN4)

/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/

#endif /* __ARCH_ARM_SRC_LPC17XX_40XX_HARDWARE_LPC178X_40XX_PINCONFIG_H */
