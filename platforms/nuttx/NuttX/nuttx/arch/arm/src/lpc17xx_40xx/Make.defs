############################################################################
# arch/arm/src/lpc17xx_40xx/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

# Common ARM and Cortex-M3 files

include armv7-m/Make.defs

# Required LPC17xx files

CHIP_CSRCS  = lpc17_40_allocateheap.c lpc17_40_clockconfig.c lpc17_40_clrpend.c
CHIP_CSRCS += lpc17_40_gpio.c lpc17_40_i2c.c lpc17_40_irq.c lpc17_40_lowputc.c
CHIP_CSRCS += lpc17_40_serial.c lpc17_40_spi.c lpc17_40_ssp.c lpc17_40_start.c

# Configuration-dependent LPC17xx files

ifneq ($(CONFIG_ARCH_IDLE_CUSTOM),y)
CHIP_CSRCS += lpc17_40_idle.c
endif

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += lpc17_40_timerisr.c
endif

ifeq ($(CONFIG_BUILD_PROTECTED),y)
CHIP_CSRCS += lpc17_40_userspace.c lpc17_40_mpuinit.c
endif

ifeq ($(CONFIG_LPC17_40_EMC),y)
CHIP_CSRCS += lpc17_40_emc.c
endif

ifeq ($(CONFIG_LPC17_40_GPIOIRQ),y)
CHIP_CSRCS += lpc17_40_gpioint.c
endif

ifeq ($(CONFIG_DEBUG_GPIO_INFO),y)
CHIP_CSRCS += lpc17_40_gpiodbg.c
endif

ifeq ($(CONFIG_LPC17_40_LCD),y)
CHIP_CSRCS += lpc17_40_lcd.c
endif

ifeq ($(CONFIG_USBDEV),y)
CHIP_CSRCS += lpc17_40_usbdev.c
endif

ifeq ($(CONFIG_USBHOST),y)
CHIP_CSRCS += lpc17_40_usbhost.c
endif

ifeq ($(CONFIG_LPC17_40_GPDMA),y)
CHIP_CSRCS += lpc17_40_gpdma.c
endif

ifeq ($(CONFIG_LPC17_40_SDCARD),y)
CHIP_CSRCS += lpc17_40_sdcard.c
endif

ifeq ($(CONFIG_NET),y)
ifeq ($(CONFIG_LPC17_40_ETHERNET),y)
CHIP_CSRCS += lpc17_40_ethernet.c
endif
endif

ifeq ($(CONFIG_LPC17_40_CAN),y)
CHIP_CSRCS += lpc17_40_can.c
endif

ifeq ($(CONFIG_LPC17_40_ADC),y)
CHIP_CSRCS += lpc17_40_adc.c
endif

ifeq ($(CONFIG_LPC17_40_DAC),y)
CHIP_CSRCS += lpc17_40_dac.c
endif

ifeq ($(CONFIG_LPC17_40_RTC),y)
CHIP_CSRCS += lpc176x_rtc.c
endif

ifeq ($(CONFIG_LPC17_40_WDT),y)
CHIP_CSRCS += lpc17_40_wdt.c
endif

ifeq ($(CONFIG_LPC17_40_PWM1),y)
CHIP_CSRCS += lpc17_40_pwm.c
endif

ifeq ($(CONFIG_LPC17_40_MCPWM),y)
CHIP_CSRCS += lpc17_40_mcpwm.c
endif

ifeq ($(CONFIG_LPC17_40_TMR0),y)
CHIP_CSRCS += lpc17_40_timer.c
endif

ifeq ($(CONFIG_LPC17_40_PROGMEM),y)
CHIP_CSRCS += lpc17_40_progmem.c
endif
