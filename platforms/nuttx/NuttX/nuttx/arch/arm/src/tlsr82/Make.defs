############################################################################
# arch/tc32/src/tlsr82/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

include tlsr82/tc32/Make.defs

# Chip devices related files in arch/arm/src/tlsr82

CHIP_CSRCS += tlsr82_start.c tlsr82_irq.c tlsr82_gpio.c tlsr82_timer_isr.c
CHIP_CSRCS += tlsr82_serial.c tlsr82_gpio_cfg.c tlsr82_analog.c
CHIP_CSRCS += tlsr82_clock.c tlsr82_cpu.c tlsr82_flash.c tlsr82_flash_mtd.c

ifeq ($(CONFIG_TLSR82_SPI),y)
  CHIP_CSRCS += tlsr82_spi_console.c
endif

ifeq ($(CONFIG_TLSR82_TIMER),y)
  CHIP_CSRCS += tlsr82_timer.c tlsr82_timer_lowerhalf.c
  ifeq ($(CONFIG_TLSR82_WATCHDOG), y)
    CHIP_CSRCS += tlsr82_watchdog.c
  endif
endif

ifeq ($(CONFIG_TLSR82_PWM),y)
  CHIP_CSRCS += tlsr82_pwm.c
endif

ifeq ($(CONFIG_TLSR82_ADC),y)
  CHIP_CSRCS += tlsr82_adc.c
endif

ifeq ($(CONFIG_TLSR82_AES),y)
  CHIP_CSRCS += tlsr82_aes.c
endif

VPATH += chip/tc32
VPATH += chip/chip/b87/boot

CFLAGS += -I$(TOPDIR)/arch/arm/src/arm
CFLAGS += -I$(TOPDIR)/arch/arm/src/tlsr82/tc32

ifeq ($(CONFIG_TLSR8278_BLE_SDK),y)
  EXTRA_LIBPATHS += -L$(TOPDIR)/$(CONFIG_TLSR8278_BLE_SDK_LIB_PATH)
  EXTRA_LIBS     += -l$(CONFIG_TLSR8278_BLE_SDK_LIB_NAME)
endif

ifeq ($(CONFIG_TLSR82_SOFT_FPU),y)
  EXTRA_LIBPATHS += -L$(TOPDIR)/$(CONFIG_TLSR82_SOFT_FPU_LIB_PATH)
  EXTRA_LIBS     += -l$(CONFIG_TLSR82_SOFT_FPU_LIB_NAME)
endif
