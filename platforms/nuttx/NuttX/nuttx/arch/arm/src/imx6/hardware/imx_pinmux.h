/****************************************************************************
 * arch/arm/src/imx6/hardware/imx_pinmux.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/* Reference:
 *   "i.MX 6Dual/6Quad ApplicationsProcessor Reference Manual,"
 *   Document Number IMX6DQRM, Rev. 3, 07/2015, FreeScale.
 */

#ifndef __ARCH_ARM_SRC_IMX6_HARDWARE_IMX_PINMUX_H
#define __ARCH_ARM_SRC_IMX6_HARDWARE_IMX_PINMUX_H

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Alternate Pin Functions.
 *
 * Alternative pin selections are provided with a numeric suffix like _1, _2,
 * etc.  Drivers, however, will use the pin selection without the numeric
 * suffix.  Additional definitions are required in the board.h file.
 * For example, if UART1 RXD connects via the SD3_DATA6 pin, then the
 * following definition should appear in the board.h header file for that
 * board:
 *
 *   #define GPIO_UART1_RX_DATA GPIO_UART1_RX_DATA_1
 *
 * The driver will then automatically configere to use the SD3_DATA6 pin for
 * UART RXD.
 */

/* WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific IOMUX options such as
 * frequency, open-drain, push-pull, and pull-up/down!  Just the basics are
 * defined for most pins in this file.  See the upper imx_gpio.h and
 * imx_iomuxc.h header files for available definitions.
 */

/* ARM */

#define GPIO_ARM_EVENTI               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_GPIO05_INDEX))
#define GPIO_ARM_EVENTO               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_CSI0_PIXCLK_INDEX))
#define GPIO_ARM_TRACE_CLK            (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA_EN_INDEX))
#define GPIO_ARM_TRACE_CTL            (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_CSI0_HSYNC_INDEX))
#define GPIO_ARM_TRACE00              (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_CSI0_VSYNC_INDEX))
#define GPIO_ARM_TRACE01              (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA04_INDEX))
#define GPIO_ARM_TRACE02              (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA05_INDEX))
#define GPIO_ARM_TRACE03              (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA06_INDEX))
#define GPIO_ARM_TRACE04              (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA07_INDEX))
#define GPIO_ARM_TRACE05              (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA08_INDEX))
#define GPIO_ARM_TRACE06              (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA09_INDEX))
#define GPIO_ARM_TRACE07              (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA10_INDEX))
#define GPIO_ARM_TRACE08              (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA11_INDEX))
#define GPIO_ARM_TRACE09              (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA12_INDEX))
#define GPIO_ARM_TRACE10              (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA13_INDEX))
#define GPIO_ARM_TRACE11              (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA14_INDEX))
#define GPIO_ARM_TRACE12              (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA15_INDEX))
#define GPIO_ARM_TRACE13              (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA16_INDEX))
#define GPIO_ARM_TRACE14              (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA17_INDEX))
#define GPIO_ARM_TRACE15              (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA18_INDEX))

/* Asynchronous Sample Rate Generator (ASRC) */

#define GPIO_ASRC_EXT_CLK_1           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW3_INDEX))
#define GPIO_ASRC_EXT_CLK_2           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_GPIO00_INDEX))
#define GPIO_ASRC_EXT_CLK_3           (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_GPIO18_INDEX))

/* Audio Multiplexor */

#define GPIO_AUD3_RXC                 (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA10_INDEX))
#define GPIO_AUD3_RXD                 (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA07_INDEX))
#define GPIO_AUD3_RXFS                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA11_INDEX))
#define GPIO_AUD3_TXC                 (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA04_INDEX))
#define GPIO_AUD3_TXD                 (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA05_INDEX))
#define GPIO_AUD3_TXFS                (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA06_INDEX))

#define GPIO_AUD4_RXC_1               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_SD2_CMD_INDEX))
#define GPIO_AUD4_RXC_2               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA19_INDEX))
#define GPIO_AUD4_RXD_1               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA23_INDEX))
#define GPIO_AUD4_RXD_2               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_SD2_DATA0_INDEX))
#define GPIO_AUD4_RXFS_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_SD2_CLK_INDEX))
#define GPIO_AUD4_RXFS_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA18_INDEX))
#define GPIO_AUD4_TXC_1               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA20_INDEX))
#define GPIO_AUD4_TXC_2               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_SD2_DATA3_INDEX))
#define GPIO_AUD4_TXD_1               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA21_INDEX))
#define GPIO_AUD4_TXD_2               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_SD2_DATA2_INDEX))
#define GPIO_AUD4_TXFS_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA22_INDEX))
#define GPIO_AUD4_TXFS_2              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_SD2_DATA1_INDEX))

#define GPIO_AUD5_RXC_1               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA14_INDEX))
#define GPIO_AUD5_RXC_2               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA25_INDEX))
#define GPIO_AUD5_RXD_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW1_INDEX))
#define GPIO_AUD5_RXD_2               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA19_INDEX))
#define GPIO_AUD5_RXFS_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA13_INDEX))
#define GPIO_AUD5_RXFS_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA24_INDEX))
#define GPIO_AUD5_TXC_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_KEY_COL0_INDEX))
#define GPIO_AUD5_TXC_2               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA16_INDEX))
#define GPIO_AUD5_TXD_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW0_INDEX))
#define GPIO_AUD5_TXD_2               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA17_INDEX))
#define GPIO_AUD5_TXFS_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_KEY_COL1_INDEX))
#define GPIO_AUD5_TXFS_2              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA18_INDEX))

#define GPIO_AUD6_RXC                 (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA06_INDEX))
#define GPIO_AUD6_RXD                 (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DI0_PIN04_INDEX))
#define GPIO_AUD6_RXFS                (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA05_INDEX))
#define GPIO_AUD6_TXC                 (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DI0_PIN15_INDEX))
#define GPIO_AUD6_TXD                 (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DI0_PIN02_INDEX))
#define GPIO_AUD6_TXFS                (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DI0_PIN03_INDEX))

/* Clock Controller Module (CCM) */

#define GPIO_CCM_CLKO1_1              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_GPIO00_INDEX))
#define GPIO_CCM_CLKO1_2              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_CSI0_HSYNC_INDEX))
#define GPIO_CCM_CLKO1_3              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_GPIO05_INDEX))
#define GPIO_CCM_CLKO1_4              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_GPIO19_INDEX))
#define GPIO_CCM_CLKO2_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_GPIO03_INDEX))
#define GPIO_CCM_CLKO2_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_NAND_CS2_INDEX))
#define GPIO_CCM_PMIC_READY_1         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_GPIO17_INDEX))
#define GPIO_CCM_PMIC_READY_2         (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_EB0_INDEX))
#define GPIO_CCM_REF_EN               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_GPIO09_INDEX))

/* Display Content Integrity Checker (DCIC) */

#define GPIO_DCIC1_OUT_1              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA17_INDEX))
#define GPIO_DCIC1_OUT_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_KEY_COL0_INDEX))

#define GPIO_DCIC2_OUT_1              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW0_INDEX))
#define GPIO_DCIC2_OUT_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_SD2_DATA0_INDEX))

/* Enhanced Configurable SPI (ECSPI) */

#define GPIO_ECSPI1_MISO_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_KEY_COL1_INDEX))
#define GPIO_ECSPI1_MISO_2            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA17_INDEX))
#define GPIO_ECSPI1_MISO_3            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA06_INDEX))
#define GPIO_ECSPI1_MISO_4            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA22_INDEX))
#define GPIO_ECSPI1_MOSI_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW0_INDEX))
#define GPIO_ECSPI1_MOSI_2            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA18_INDEX))
#define GPIO_ECSPI1_MOSI_3            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA05_INDEX))
#define GPIO_ECSPI1_MOSI_4            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA21_INDEX))
#define GPIO_ECSPI1_RDY               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_GPIO19_INDEX))
#define GPIO_ECSPI1_SCLK_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_KEY_COL0_INDEX))
#define GPIO_ECSPI1_SCLK_2            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA16_INDEX))
#define GPIO_ECSPI1_SCLK_3            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA04_INDEX))
#define GPIO_ECSPI1_SCLK_4            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA20_INDEX))
#define GPIO_ECSPI1_SS0_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW1_INDEX))
#define GPIO_ECSPI1_SS0_2             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_EB2_INDEX))
#define GPIO_ECSPI1_SS0_3             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA07_INDEX))
#define GPIO_ECSPI1_SS0_4             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA23_INDEX))
#define GPIO_ECSPI1_SS1_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_KEY_COL2_INDEX))
#define GPIO_ECSPI1_SS1_2             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA19_INDEX))
#define GPIO_ECSPI1_SS1_3             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA15_INDEX))
#define GPIO_ECSPI1_SS2_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW2_INDEX))
#define GPIO_ECSPI1_SS2_2             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA24_INDEX))
#define GPIO_ECSPI1_SS3_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_KEY_COL3_INDEX))
#define GPIO_ECSPI1_SS3_2             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA25_INDEX))

#define GPIO_ECSPI2_MISO_1            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA10_INDEX))
#define GPIO_ECSPI2_MISO_2            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA17_INDEX))
#define GPIO_ECSPI2_MISO_3            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_OE_INDEX))
#define GPIO_ECSPI2_MOSI_1            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA09_INDEX))
#define GPIO_ECSPI2_MOSI_2            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA16_INDEX))
#define GPIO_ECSPI2_MOSI_3            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_CS1_INDEX))
#define GPIO_ECSPI2_RDY               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR25_INDEX))
#define GPIO_ECSPI2_SCLK_1            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA08_INDEX))
#define GPIO_ECSPI2_SCLK_2            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA19_INDEX))
#define GPIO_ECSPI2_SCLK_3            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_CS0_INDEX))
#define GPIO_ECSPI2_SS0_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA11_INDEX))
#define GPIO_ECSPI2_SS0_2             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA18_INDEX))
#define GPIO_ECSPI2_SS0_3             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_RW_INDEX))
#define GPIO_ECSPI2_SS1_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_LBA_INDEX))
#define GPIO_ECSPI2_SS1_2             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA15_INDEX))
#define GPIO_ECSPI2_SS2               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA24_INDEX))
#define GPIO_ECSPI2_SS3               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA25_INDEX))

#define GPIO_ECSPI3_MISO              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA02_INDEX))
#define GPIO_ECSPI3_MOSI              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA01_INDEX))
#define GPIO_ECSPI3_RDY               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA07_INDEX))
#define GPIO_ECSPI3_SCLK              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA00_INDEX))
#define GPIO_ECSPI3_SS0               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA03_INDEX))
#define GPIO_ECSPI3_SS1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA04_INDEX))
#define GPIO_ECSPI3_SS2               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA05_INDEX))
#define GPIO_ECSPI3_SS3               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA06_INDEX))

#define GPIO_ECSPI4_MISO              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA22_INDEX))
#define GPIO_ECSPI4_MOSI              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA28_INDEX))
#define GPIO_ECSPI4_RDY               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_EB3_INDEX))
#define GPIO_ECSPI4_SCLK              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA21_INDEX))
#define GPIO_ECSPI4_SS0_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA20_INDEX))
#define GPIO_ECSPI4_SS0_2             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA29_INDEX))
#define GPIO_ECSPI4_SS1               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR25_INDEX))
#define GPIO_ECSPI4_SS2               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA24_INDEX))
#define GPIO_ECSPI4_SS3               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA25_INDEX))

#define GPIO_ECSPI5_MISO_1            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD1_DATA0_INDEX))
#define GPIO_ECSPI5_MISO_2            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD2_DATA0_INDEX))
#define GPIO_ECSPI5_MOSI_1            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD1_CMD_INDEX))
#define GPIO_ECSPI5_MOSI_2            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD2_CMD_INDEX))
#define GPIO_ECSPI5_RDY               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_GPIO07_INDEX))
#define GPIO_ECSPI5_SCLK_1            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD1_CLK_INDEX))
#define GPIO_ECSPI5_SCLK_2            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD2_CLK_INDEX))
#define GPIO_ECSPI5_SS0_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD1_DATA1_INDEX))
#define GPIO_ECSPI5_SS0_2             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD2_DATA1_INDEX))
#define GPIO_ECSPI5_SS1_1             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD1_DATA2_INDEX))
#define GPIO_ECSPI5_SS1_2             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD2_DATA2_INDEX))
#define GPIO_ECSPI5_SS2               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD1_DATA3_INDEX))
#define GPIO_ECSPI5_SS3               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD2_DATA3_INDEX))

/* EIM-PSRAM/NOR Flash controller */

#define GPIO_EIM_ADDR00               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_AD00_INDEX))
#define GPIO_EIM_ADDR01               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_AD01_INDEX))
#define GPIO_EIM_ADDR02               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_AD02_INDEX))
#define GPIO_EIM_ADDR03               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_AD03_INDEX))
#define GPIO_EIM_ADDR04               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_AD04_INDEX))
#define GPIO_EIM_ADDR05               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_AD05_INDEX))
#define GPIO_EIM_ADDR06               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_AD06_INDEX))
#define GPIO_EIM_ADDR07               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_AD07_INDEX))
#define GPIO_EIM_ADDR08               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_AD08_INDEX))
#define GPIO_EIM_ADDR09               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_AD09_INDEX))
#define GPIO_EIM_ADDR10               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_AD10_INDEX))
#define GPIO_EIM_ADDR11               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_AD11_INDEX))
#define GPIO_EIM_ADDR12               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_AD12_INDEX))
#define GPIO_EIM_ADDR13               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_AD13_INDEX))
#define GPIO_EIM_ADDR14               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_AD14_INDEX))
#define GPIO_EIM_ADDR15               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_AD15_INDEX))
#define GPIO_EIM_ADDR16               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR16_INDEX))
#define GPIO_EIM_ADDR17               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR17_INDEX))
#define GPIO_EIM_ADDR18               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR18_INDEX))
#define GPIO_EIM_ADDR19               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR19_INDEX))
#define GPIO_EIM_ADDR20               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR20_INDEX))
#define GPIO_EIM_ADDR21               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR21_INDEX))
#define GPIO_EIM_ADDR22               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR22_INDEX))
#define GPIO_EIM_ADDR23               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR23_INDEX))
#define GPIO_EIM_ADDR24               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR24_INDEX))
#define GPIO_EIM_ADDR25               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR25_INDEX))
#define GPIO_EIM_ADDR26               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_NAND_CS3_INDEX))
#define GPIO_EIM_BCLK                 (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_BCLK_INDEX))
#define GPIO_EIM_CRE                  (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_NAND_CS2_INDEX))
#define GPIO_EIM_CS0                  (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_CS0_INDEX))
#define GPIO_EIM_CS1                  (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_CS1_INDEX))
#define GPIO_EIM_CS2_1                (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD2_DATA1_INDEX))
#define GPIO_EIM_CS2_2                (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA18_INDEX))
#define GPIO_EIM_CS3_1                (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD2_DATA2_INDEX))
#define GPIO_EIM_CS3_2                (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA19_INDEX))
#define GPIO_EIM_DATA00               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA_EN_INDEX))
#define GPIO_EIM_DATA01               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_CSI0_VSYNC_INDEX))
#define GPIO_EIM_DATA02               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA04_INDEX))
#define GPIO_EIM_DATA03               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA05_INDEX))
#define GPIO_EIM_DATA04               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA06_INDEX))
#define GPIO_EIM_DATA05               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA07_INDEX))
#define GPIO_EIM_DATA06               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA08_INDEX))
#define GPIO_EIM_DATA07               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA09_INDEX))
#define GPIO_EIM_DATA08               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA12_INDEX))
#define GPIO_EIM_DATA09               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA13_INDEX))
#define GPIO_EIM_DATA10               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA14_INDEX))
#define GPIO_EIM_DATA11               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA15_INDEX))
#define GPIO_EIM_DATA12               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA16_INDEX))
#define GPIO_EIM_DATA13               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA17_INDEX))
#define GPIO_EIM_DATA14               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA18_INDEX))
#define GPIO_EIM_DATA15               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA19_INDEX))
#define GPIO_EIM_DATA16               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA16_INDEX))
#define GPIO_EIM_DATA17               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA17_INDEX))
#define GPIO_EIM_DATA18               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA18_INDEX))
#define GPIO_EIM_DATA19               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA19_INDEX))
#define GPIO_EIM_DATA20               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA20_INDEX))
#define GPIO_EIM_DATA21               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA21_INDEX))
#define GPIO_EIM_DATA22               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA22_INDEX))
#define GPIO_EIM_DATA23               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA23_INDEX))
#define GPIO_EIM_DATA24               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA24_INDEX))
#define GPIO_EIM_DATA25               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA25_INDEX))
#define GPIO_EIM_DATA26               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA26_INDEX))
#define GPIO_EIM_DATA27               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA27_INDEX))
#define GPIO_EIM_DATA28               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA28_INDEX))
#define GPIO_EIM_DATA29               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA29_INDEX))
#define GPIO_EIM_DATA30               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA30_INDEX))
#define GPIO_EIM_DATA31               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA31_INDEX))
#define GPIO_EIM_DTACK                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_WAIT_INDEX))
#define GPIO_EIM_EB0                  (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_EB0_INDEX))
#define GPIO_EIM_EB1                  (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_EB1_INDEX))
#define GPIO_EIM_EB2                  (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_EB2_INDEX))
#define GPIO_EIM_EB3                  (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_EB3_INDEX))
#define GPIO_EIM_LBA                  (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_LBA_INDEX))
#define GPIO_EIM_OE                   (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_OE_INDEX))
#define GPIO_EIM_RW                   (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_RW_INDEX))
#define GPIO_EIM_WAIT                 (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_EIM_WAIT_INDEX))

/* Ethernet */

#define GPIO_ENET_1588_EVENT0_IN      (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_ENET_TX_DATA1_INDEX))
#define GPIO_ENET_1588_EVENT0_OUT     (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_GPIO19_INDEX))
#define GPIO_ENET_1588_EVENT1_IN      (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_ENET_MDC_INDEX))
#define GPIO_ENET_1588_EVENT1_OUT     (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_ENET_MDIO_INDEX))
#define GPIO_ENET_1588_EVENT2_IN      (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_GPIO16_INDEX))
#define GPIO_ENET_1588_EVENT2_OUT     (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_ENET_RX_ER_INDEX))
#define GPIO_ENET_1588_EVENT3_IN      (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_GPIO17_INDEX))
#define GPIO_ENET_1588_EVENT3_OUT     (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_ENET_RX_DATA1_INDEX))
#define GPIO_ENET_COL                 (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW1_INDEX))
#define GPIO_ENET_CRS                 (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_KEY_COL3_INDEX))
#define GPIO_ENET_MDC_1               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_ENET_MDC_INDEX))
#define GPIO_ENET_MDC_2               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_KEY_COL2_INDEX))
#define GPIO_ENET_MDIO_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_ENET_MDIO_INDEX))
#define GPIO_ENET_MDIO_2              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_KEY_COL1_INDEX))
#define GPIO_ENET_REF_CLK_1           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_GPIO16_INDEX))
#define GPIO_ENET_REF_CLK_2           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_RGMII_TX_CTL_INDEX))
#define GPIO_ENET_RX_CLK              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_GPIO18_INDEX))
#define GPIO_ENET_RX_DATA0            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_ENET_RX_DATA0_INDEX))
#define GPIO_ENET_RX_DATA1            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_ENET_RX_DATA1_INDEX))
#define GPIO_ENET_RX_DATA2            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_KEY_COL2_INDEX))
#define GPIO_ENET_RX_DATA3            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_KEY_COL0_INDEX))
#define GPIO_ENET_RX_EN               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_ENET_CRS_DV_INDEX))
#define GPIO_ENET_RX_ER               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_ENET_RX_ER_INDEX))
#define GPIO_ENET_TX_CLK              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_ENET_REF_CLK_INDEX))
#define GPIO_ENET_TX_DATA0            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_ENET_TX_DATA0_INDEX))
#define GPIO_ENET_TX_DATA1            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_ENET_TX_DATA1_INDEX))
#define GPIO_ENET_TX_DATA2            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW2_INDEX))
#define GPIO_ENET_TX_DATA3            (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW0_INDEX))
#define GPIO_ENET_TX_EN               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_ENET_TX_EN_INDEX))
#define GPIO_ENET_TX_ER               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_GPIO19_INDEX))

/* Enhanced Periodic Interrupt Timer (EPIT) */

#define GPIO_EPIT1_OUT_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_GPIO07_INDEX))
#define GPIO_EPIT1_OUT_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_GPIO00_INDEX))
#define GPIO_EPIT1_OUT_3              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA19_INDEX))

#define GPIO_EPIT2_OUT_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_GPIO08_INDEX))
#define GPIO_EPIT2_OUT_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA20_INDEX))

/* Enhanced Serial Audio Interface (ESAI) */

#define GPIO_ESAI_RX_CLK_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_GPIO01_INDEX))
#define GPIO_ESAI_RX_CLK_2            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_ENET_MDIO_INDEX))
#define GPIO_ESAI_RX_FS_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_GPIO09_INDEX))
#define GPIO_ESAI_RX_FS_2             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_ENET_REF_CLK_INDEX))
#define GPIO_ESAI_RX_HF_CLK_1         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_GPIO03_INDEX))
#define GPIO_ESAI_RX_HF_CLK_2         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_ENET_RX_ER_INDEX))
#define GPIO_ESAI_TX_CLK_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_GPIO06_INDEX))
#define GPIO_ESAI_TX_CLK_2            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_ENET_CRS_DV_INDEX))
#define GPIO_ESAI_TX_FS_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_GPIO02_INDEX))
#define GPIO_ESAI_TX_FS_2             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_ENET_RX_DATA1_INDEX))
#define GPIO_ESAI_TX_HF_CLK_1         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_GPIO04_INDEX))
#define GPIO_ESAI_TX_HF_CLK_2         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_ENET_RX_DATA0_INDEX))
#define GPIO_ESAI_TX0_1               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_GPIO17_INDEX))
#define GPIO_ESAI_TX0_2               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_NAND_CS2_INDEX))
#define GPIO_ESAI_TX1_1               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_GPIO18_INDEX))
#define GPIO_ESAI_TX1_2               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_NAND_CS3_INDEX))
#define GPIO_ESAI_TX2_RX3_1           (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_GPIO05_INDEX))
#define GPIO_ESAI_TX2_RX3_2           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_ENET_TX_DATA1_INDEX))
#define GPIO_ESAI_TX3_RX2_1           (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_GPIO16_INDEX))
#define GPIO_ESAI_TX3_RX2_2           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_ENET_TX_EN_INDEX))
#define GPIO_ESAI_TX4_RX1_1           (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_GPIO07_INDEX))
#define GPIO_ESAI_TX4_RX1_2           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_ENET_TX_DATA0_INDEX))
#define GPIO_ESAI_TX5_RX0_1           (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_GPIO08_INDEX))
#define GPIO_ESAI_TX5_RX0_2           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_ENET_MDC_INDEX))

/* Flexible Controller Area Network (FLEXCAN) */

#define GPIO_FLEXCAN1_RX_1            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW2_INDEX))
#define GPIO_FLEXCAN1_RX_2            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD3_CLK_INDEX))
#define GPIO_FLEXCAN1_RX_3            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_GPIO08_INDEX))
#define GPIO_FLEXCAN1_TX_1            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_KEY_COL2_INDEX))
#define GPIO_FLEXCAN1_TX_2            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD3_CMD_INDEX))
#define GPIO_FLEXCAN1_TX_3            (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_GPIO07_INDEX))

#define GPIO_FLEXCAN2_RX_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW4_INDEX))
#define GPIO_FLEXCAN2_RX_2            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD3_DATA1_INDEX))
#define GPIO_FLEXCAN2_TX_1            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_KEY_COL4_INDEX))
#define GPIO_FLEXCAN2_TX_2            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD3_DATA0_INDEX))

/* General Purpose Timer (GPT) */

#define GPIO_GPT_CAPTURE1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_SD1_DATA0_INDEX))
#define GPIO_GPT_CAPTURE2             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_SD1_DATA1_INDEX))
#define GPIO_GPT_CLKIN                (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_SD1_CLK_INDEX))
#define GPIO_GPT_COMPARE1             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_SD1_CMD_INDEX))
#define GPIO_GPT_COMPARE2             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD1_DATA2_INDEX))
#define GPIO_GPT_COMPARE3             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD1_DATA3_INDEX))

/* HDMI */

#define GPIO_HDMI_TX_CEC_LINE_1       (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR25_INDEX))
#define GPIO_HDMI_TX_CEC_LINE_2       (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW2_INDEX))
#define GPIO_HDMI_TX_DDC_SCL_1        (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_KEY_COL3_INDEX))
#define GPIO_HDMI_TX_DDC_SCL_2        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_EB2_INDEX))
#define GPIO_HDMI_TX_DDC_SDA_1        (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW3_INDEX))
#define GPIO_HDMI_TX_DDC_SDA_2        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA16_INDEX))

/* High Speed Synchronous Interface (HSI) */

#define GPIO_HSI_RX_DATA              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_RGMII_TD2_INDEX))
#define GPIO_HSI_RX_FLAG              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_RGMII_TD1_INDEX))
#define GPIO_HSI_RX_READY             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_RGMII_RD0_INDEX))
#define GPIO_HSI_RX_WAKE              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_RGMII_TD3_INDEX))
#define GPIO_HSI_TX_DATA              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_RGMII_RD2_INDEX))
#define GPIO_HSI_TX_FLAG              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_RGMII_RD1_INDEX))
#define GPIO_HSI_TX_READY             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_RGMII_TD0_INDEX))
#define GPIO_HSI_TX_WAKE              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_RGMII_RD3_INDEX))

/* I2C */

#define GPIO_I2C1_SCL_1               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA09_INDEX))
#define GPIO_I2C1_SCL_2               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA21_INDEX))
#define GPIO_I2C1_SDA_1               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA28_INDEX))
#define GPIO_I2C1_SDA_2               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA08_INDEX))

#define GPIO_I2C2_SCL_1               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_KEY_COL3_INDEX))
#define GPIO_I2C2_SCL_2               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_EIM_EB2_INDEX))
#define GPIO_I2C2_SDA_1               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW3_INDEX))
#define GPIO_I2C2_SDA_2               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA16_INDEX))

#define GPIO_I2C3_SCL_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_GPIO03_INDEX))
#define GPIO_I2C3_SCL_2               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA17_INDEX))
#define GPIO_I2C3_SCL_3               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_GPIO05_INDEX))
#define GPIO_I2C3_SDA_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_GPIO06_INDEX))
#define GPIO_I2C3_SDA_2               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA18_INDEX))
#define GPIO_I2C3_SDA_3               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_GPIO16_INDEX))

/* Image Processing Unit (IPU) */

#define GPIO_IPU1_CSI0_DATA_EN        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA_EN_INDEX))
#define GPIO_IPU1_CSI0_DATA00         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA27_INDEX))
#define GPIO_IPU1_CSI0_DATA01         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA26_INDEX))
#define GPIO_IPU1_CSI0_DATA02         (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA31_INDEX))
#define GPIO_IPU1_CSI0_DATA03         (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA30_INDEX))
#define GPIO_IPU1_CSI0_DATA04         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA04_INDEX))
#define GPIO_IPU1_CSI0_DATA05         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA05_INDEX))
#define GPIO_IPU1_CSI0_DATA06         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA06_INDEX))
#define GPIO_IPU1_CSI0_DATA07         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA07_INDEX))
#define GPIO_IPU1_CSI0_DATA08         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA08_INDEX))
#define GPIO_IPU1_CSI0_DATA09         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA09_INDEX))
#define GPIO_IPU1_CSI0_DATA10         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA10_INDEX))
#define GPIO_IPU1_CSI0_DATA11         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA11_INDEX))
#define GPIO_IPU1_CSI0_DATA12         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA12_INDEX))
#define GPIO_IPU1_CSI0_DATA13         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA13_INDEX))
#define GPIO_IPU1_CSI0_DATA14         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA14_INDEX))
#define GPIO_IPU1_CSI0_DATA15         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA15_INDEX))
#define GPIO_IPU1_CSI0_DATA16         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA16_INDEX))
#define GPIO_IPU1_CSI0_DATA17         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA17_INDEX))
#define GPIO_IPU1_CSI0_DATA18         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA18_INDEX))
#define GPIO_IPU1_CSI0_DATA19         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA19_INDEX))
#define GPIO_IPU1_CSI0_HSYNC          (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_HSYNC_INDEX))
#define GPIO_IPU1_CSI0_PIXCLK         (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_PIXCLK_INDEX))
#define GPIO_IPU1_CSI0_VSYNC          (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_CSI0_VSYNC_INDEX))
#define GPIO_IPU1_DI0_D0_CS           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA23_INDEX))
#define GPIO_IPU1_DI0_D1_CS           (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR25_INDEX))
#define GPIO_IPU1_DI0_DISP_CLK        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DI0_DISP_CLK_INDEX))
#define GPIO_IPU1_DI0_PIN01           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA22_INDEX))
#define GPIO_IPU1_DI0_PIN02           (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DI0_PIN02_INDEX))
#define GPIO_IPU1_DI0_PIN03           (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DI0_PIN03_INDEX))
#define GPIO_IPU1_DI0_PIN04           (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DI0_PIN04_INDEX))
#define GPIO_IPU1_DI0_PIN05           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA16_INDEX))
#define GPIO_IPU1_DI0_PIN06           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA17_INDEX))
#define GPIO_IPU1_DI0_PIN07           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA18_INDEX))
#define GPIO_IPU1_DI0_PIN08           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA19_INDEX))
#define GPIO_IPU1_DI0_PIN11           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA30_INDEX))
#define GPIO_IPU1_DI0_PIN12           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA31_INDEX))
#define GPIO_IPU1_DI0_PIN13           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA28_INDEX))
#define GPIO_IPU1_DI0_PIN14           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA29_INDEX))
#define GPIO_IPU1_DI0_PIN15           (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DI0_PIN15_INDEX))
#define GPIO_IPU1_DI0_PIN16           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA20_INDEX))
#define GPIO_IPU1_DI0_PIN17           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA21_INDEX))
#define GPIO_IPU1_DI1_D0_CS_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_AD13_INDEX))
#define GPIO_IPU1_DI1_D0_CS_2         (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA18_INDEX))
#define GPIO_IPU1_DI1_D1_CS           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_AD14_INDEX))
#define GPIO_IPU1_DI1_DISP_CLK        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR16_INDEX))
#define GPIO_IPU1_DI1_PIN01           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_AD15_INDEX))
#define GPIO_IPU1_DI1_PIN02_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_AD11_INDEX))
#define GPIO_IPU1_DI1_PIN02_2         (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA23_INDEX))
#define GPIO_IPU1_DI1_PIN03_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_AD12_INDEX))
#define GPIO_IPU1_DI1_PIN03_2         (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_EIM_EB3_INDEX))
#define GPIO_IPU1_DI1_PIN04           (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_AD15_INDEX))
#define GPIO_IPU1_DI1_PIN05           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_CS0_INDEX))
#define GPIO_IPU1_DI1_PIN06           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_CS1_INDEX))
#define GPIO_IPU1_DI1_PIN07           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_OE_INDEX))
#define GPIO_IPU1_DI1_PIN08           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_RW_INDEX))
#define GPIO_IPU1_DI1_PIN11           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA26_INDEX))
#define GPIO_IPU1_DI1_PIN12           (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR25_INDEX))
#define GPIO_IPU1_DI1_PIN13           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA27_INDEX))
#define GPIO_IPU1_DI1_PIN14           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA23_INDEX))
#define GPIO_IPU1_DI1_PIN15_1         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_AD10_INDEX))
#define GPIO_IPU1_DI1_PIN15_2         (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA29_INDEX))
#define GPIO_IPU1_DI1_PIN16           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_BCLK_INDEX))
#define GPIO_IPU1_DI1_PIN17           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_LBA_INDEX))
#define GPIO_IPU1_DISP0_DATA00        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA00_INDEX))
#define GPIO_IPU1_DISP0_DATA01        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA01_INDEX))
#define GPIO_IPU1_DISP0_DATA02        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA02_INDEX))
#define GPIO_IPU1_DISP0_DATA03        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA03_INDEX))
#define GPIO_IPU1_DISP0_DATA04        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA04_INDEX))
#define GPIO_IPU1_DISP0_DATA05        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA05_INDEX))
#define GPIO_IPU1_DISP0_DATA06        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA06_INDEX))
#define GPIO_IPU1_DISP0_DATA07        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA07_INDEX))
#define GPIO_IPU1_DISP0_DATA08        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA08_INDEX))
#define GPIO_IPU1_DISP0_DATA09        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA09_INDEX))
#define GPIO_IPU1_DISP0_DATA10        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA10_INDEX))
#define GPIO_IPU1_DISP0_DATA11        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA11_INDEX))
#define GPIO_IPU1_DISP0_DATA12        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA12_INDEX))
#define GPIO_IPU1_DISP0_DATA13        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA13_INDEX))
#define GPIO_IPU1_DISP0_DATA14        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA14_INDEX))
#define GPIO_IPU1_DISP0_DATA15        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA15_INDEX))
#define GPIO_IPU1_DISP0_DATA16        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA16_INDEX))
#define GPIO_IPU1_DISP0_DATA17        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA17_INDEX))
#define GPIO_IPU1_DISP0_DATA18        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA18_INDEX))
#define GPIO_IPU1_DISP0_DATA19        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA19_INDEX))
#define GPIO_IPU1_DISP0_DATA20        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA20_INDEX))
#define GPIO_IPU1_DISP0_DATA21        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA21_INDEX))
#define GPIO_IPU1_DISP0_DATA22        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA22_INDEX))
#define GPIO_IPU1_DISP0_DATA23        (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA23_INDEX))
#define GPIO_IPU1_DISP1_DATA00        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_AD09_INDEX))
#define GPIO_IPU1_DISP1_DATA01        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_AD08_INDEX))
#define GPIO_IPU1_DISP1_DATA02        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_AD07_INDEX))
#define GPIO_IPU1_DISP1_DATA03        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_AD06_INDEX))
#define GPIO_IPU1_DISP1_DATA04        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_AD05_INDEX))
#define GPIO_IPU1_DISP1_DATA05        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_AD04_INDEX))
#define GPIO_IPU1_DISP1_DATA06        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_AD03_INDEX))
#define GPIO_IPU1_DISP1_DATA07        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_AD02_INDEX))
#define GPIO_IPU1_DISP1_DATA08        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_AD01_INDEX))
#define GPIO_IPU1_DISP1_DATA09        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_AD00_INDEX))
#define GPIO_IPU1_DISP1_DATA10        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_EB1_INDEX)              )
#define GPIO_IPU1_DISP1_DATA11        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_EB0_INDEX))
#define GPIO_IPU1_DISP1_DATA12        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR17_INDEX))
#define GPIO_IPU1_DISP1_DATA13        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR18_INDEX))
#define GPIO_IPU1_DISP1_DATA14        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR19_INDEX))
#define GPIO_IPU1_DISP1_DATA15        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR20_INDEX))
#define GPIO_IPU1_DISP1_DATA16        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR21_INDEX))
#define GPIO_IPU1_DISP1_DATA17        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR22_INDEX))
#define GPIO_IPU1_DISP1_DATA18        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR23_INDEX))
#define GPIO_IPU1_DISP1_DATA19        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR24_INDEX))
#define GPIO_IPU1_DISP1_DATA20        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA31_INDEX))
#define GPIO_IPU1_DISP1_DATA21        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA30_INDEX))
#define GPIO_IPU1_DISP1_DATA22        (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA26_INDEX))
#define GPIO_IPU1_DISP1_DATA23        (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA27_INDEX))
#define GPIO_IPU1_EXT_TRIG            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA28_INDEX))
#define GPIO_IPU1_SISG0               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_NAND_CS2_INDEX))
#define GPIO_IPU1_SISG1               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_NAND_CS3_INDEX))
#define GPIO_IPU1_SISG2_1             (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR24_INDEX))
#define GPIO_IPU1_SISG2_2             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA26_INDEX))
#define GPIO_IPU1_SISG3_1             (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR23_INDEX))
#define GPIO_IPU1_SISG3_2             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA27_INDEX))
#define GPIO_IPU1_SISG4               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_KEY_COL4_INDEX))
#define GPIO_IPU1_SISG5               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW4_INDEX))

#define GPIO_IPU2_CSI1_DATA_EN_1      (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_AD10_INDEX))
#define GPIO_IPU2_CSI1_DATA_EN_2      (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA23_INDEX))
#define GPIO_IPU2_CSI1_DATA00         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_AD09_INDEX))
#define GPIO_IPU2_CSI1_DATA01         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_AD08_INDEX))
#define GPIO_IPU2_CSI1_DATA02         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_AD07_INDEX))
#define GPIO_IPU2_CSI1_DATA03         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_AD06_INDEX))
#define GPIO_IPU2_CSI1_DATA04         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_AD05_INDEX))
#define GPIO_IPU2_CSI1_DATA05         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_AD04_INDEX))
#define GPIO_IPU2_CSI1_DATA06         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_AD03_INDEX))
#define GPIO_IPU2_CSI1_DATA07         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_AD02_INDEX))
#define GPIO_IPU2_CSI1_DATA08         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_AD01_INDEX))
#define GPIO_IPU2_CSI1_DATA09         (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_AD00_INDEX))
#define GPIO_IPU2_CSI1_DATA10_1       (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_EB1_INDEX))
#define GPIO_IPU2_CSI1_DATA10_2       (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA22_INDEX))
#define GPIO_IPU2_CSI1_DATA11_1       (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_EB0_INDEX))
#define GPIO_IPU2_CSI1_DATA11_2       (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA21_INDEX))
#define GPIO_IPU2_CSI1_DATA12_1       (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR17_INDEX))
#define GPIO_IPU2_CSI1_DATA12_2       (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA28_INDEX))
#define GPIO_IPU2_CSI1_DATA13_1       (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR18_INDEX))
#define GPIO_IPU2_CSI1_DATA13_2       (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA27_INDEX))
#define GPIO_IPU2_CSI1_DATA14_1       (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR19_INDEX))
#define GPIO_IPU2_CSI1_DATA14_2       (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA26_INDEX))
#define GPIO_IPU2_CSI1_DATA15_1       (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR20_INDEX))
#define GPIO_IPU2_CSI1_DATA15_2       (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA20_INDEX))
#define GPIO_IPU2_CSI1_DATA16_1       (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR21_INDEX))
#define GPIO_IPU2_CSI1_DATA16_2       (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA19_INDEX))
#define GPIO_IPU2_CSI1_DATA17_1       (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR22_INDEX))
#define GPIO_IPU2_CSI1_DATA17_2       (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA18_INDEX))
#define GPIO_IPU2_CSI1_DATA18_1       (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR23_INDEX))
#define GPIO_IPU2_CSI1_DATA18_2       (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA16_INDEX))
#define GPIO_IPU2_CSI1_DATA19_1       (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR24_INDEX))
#define GPIO_IPU2_CSI1_DATA19_2       (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_EB2_INDEX))
#define GPIO_IPU2_CSI1_HSYNC_1        (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_AD11_INDEX))
#define GPIO_IPU2_CSI1_HSYNC_2        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_EB3_INDEX))
#define GPIO_IPU2_CSI1_PIXCLK_1       (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR16_INDEX))
#define GPIO_IPU2_CSI1_PIXCLK_2       (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA17_INDEX))
#define GPIO_IPU2_CSI1_VSYNC_1        (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_AD12_INDEX))
#define GPIO_IPU2_CSI1_VSYNC_2        (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA29_INDEX))
#define GPIO_IPU2_DI0_DISP_CLK        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DI0_DISP_CLK_INDEX))
#define GPIO_IPU2_DI0_PIN01           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_NAND_READY_INDEX))
#define GPIO_IPU2_DI0_PIN02           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DI0_PIN02_INDEX))
#define GPIO_IPU2_DI0_PIN03           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DI0_PIN03_INDEX))
#define GPIO_IPU2_DI0_PIN04           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DI0_PIN04_INDEX))
#define GPIO_IPU2_DI0_PIN15           (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DI0_PIN15_INDEX))
#define GPIO_IPU2_DISP0_DATA00        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA00_INDEX))
#define GPIO_IPU2_DISP0_DATA01        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA01_INDEX))
#define GPIO_IPU2_DISP0_DATA02        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA02_INDEX))
#define GPIO_IPU2_DISP0_DATA03        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA03_INDEX))
#define GPIO_IPU2_DISP0_DATA04        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA04_INDEX))
#define GPIO_IPU2_DISP0_DATA05        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA05_INDEX))
#define GPIO_IPU2_DISP0_DATA06        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA06_INDEX))
#define GPIO_IPU2_DISP0_DATA07        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA07_INDEX))
#define GPIO_IPU2_DISP0_DATA08        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA08_INDEX))
#define GPIO_IPU2_DISP0_DATA09        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA09_INDEX))
#define GPIO_IPU2_DISP0_DATA10        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA10_INDEX))
#define GPIO_IPU2_DISP0_DATA11        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA11_INDEX))
#define GPIO_IPU2_DISP0_DATA12        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA12_INDEX))
#define GPIO_IPU2_DISP0_DATA13        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA13_INDEX))
#define GPIO_IPU2_DISP0_DATA14        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA14_INDEX))
#define GPIO_IPU2_DISP0_DATA15        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA15_INDEX))
#define GPIO_IPU2_DISP0_DATA16        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA16_INDEX))
#define GPIO_IPU2_DISP0_DATA17        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA17_INDEX))
#define GPIO_IPU2_DISP0_DATA18        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA18_INDEX))
#define GPIO_IPU2_DISP0_DATA19        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA19_INDEX))
#define GPIO_IPU2_DISP0_DATA20        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA20_INDEX))
#define GPIO_IPU2_DISP0_DATA21        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA21_INDEX))
#define GPIO_IPU2_DISP0_DATA22        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA22_INDEX))
#define GPIO_IPU2_DISP0_DATA23        (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA23_INDEX))
#define GPIO_IPU2_SISG0               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_NAND_CS2_INDEX))
#define GPIO_IPU2_SISG1               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_NAND_CS3_INDEX))
#define GPIO_IPU2_SISG2               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR24_INDEX))
#define GPIO_IPU2_SISG3               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR23_INDEX))
#define GPIO_IPU2_SISG4               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_NAND_CLE_INDEX))
#define GPIO_IPU2_SISG5               (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_NAND_WP_INDEX))

/* JTAG */

#define GPIO_JTAG_DE                  (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_GPIO16_INDEX))

/* Keypad Port (KPP) */

#define GPIO_KEY_COL0                 (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_KEY_COL0_INDEX))
#define GPIO_KEY_COL1                 (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_KEY_COL1_INDEX))
#define GPIO_KEY_COL2                 (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_KEY_COL2_INDEX))
#define GPIO_KEY_COL3                 (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_KEY_COL3_INDEX))
#define GPIO_KEY_COL4                 (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_KEY_COL4_INDEX))
#define GPIO_KEY_COL5_1               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_GPIO19_INDEX))
#define GPIO_KEY_COL5_2               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_GPIO00_INDEX))
#define GPIO_KEY_COL5_3               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD2_CLK_INDEX))
#define GPIO_KEY_COL5_4               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA04_INDEX))
#define GPIO_KEY_COL6_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_GPIO09_INDEX))
#define GPIO_KEY_COL6_2               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD2_DATA3_INDEX))
#define GPIO_KEY_COL6_3               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA06_INDEX))
#define GPIO_KEY_COL7_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_GPIO04_INDEX))
#define GPIO_KEY_COL7_2               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA08_INDEX))
#define GPIO_KEY_COL7_3               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_SD2_DATA1_INDEX))
#define GPIO_KEY_ROW0                 (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW0_INDEX))
#define GPIO_KEY_ROW1                 (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW1_INDEX))
#define GPIO_KEY_ROW2                 (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW2_INDEX))
#define GPIO_KEY_ROW3                 (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW3_INDEX))
#define GPIO_KEY_ROW4                 (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW4_INDEX))
#define GPIO_KEY_ROW5_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_GPIO01_INDEX))
#define GPIO_KEY_ROW5_2               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD2_CMD_INDEX))
#define GPIO_KEY_ROW5_3               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA05_INDEX))
#define GPIO_KEY_ROW6_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_GPIO02_INDEX))
#define GPIO_KEY_ROW6_2               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA07_INDEX))
#define GPIO_KEY_ROW6_3               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_SD2_DATA2_INDEX))
#define GPIO_KEY_ROW7_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_GPIO05_INDEX))
#define GPIO_KEY_ROW7_2               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA09_INDEX))
#define GPIO_KEY_ROW7_3               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_SD2_DATA0_INDEX))

/* MediaLB (MLB) */

#define GPIO_MLB_CLK_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_ENET_TX_DATA1_INDEX))
#define GPIO_MLB_CLK_2                (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_GPIO03_INDEX))
#define GPIO_MLB_DATA_1               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_ENET_MDC_INDEX))
#define GPIO_MLB_DATA_2               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_GPIO02_INDEX))
#define GPIO_MLB_SIG_1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_ENET_RX_DATA1_INDEX))
#define GPIO_MLB_SIG_2                (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_GPIO06_INDEX))

/* NAND */

#define GPIO_NAND_ALE                 (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_NAND_ALE_INDEX))
#define GPIO_NAND_CE0                 (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_NAND_CS0_INDEX))
#define GPIO_NAND_CE1                 (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_NAND_CS1_INDEX))
#define GPIO_NAND_CE2                 (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_NAND_CS2_INDEX))
#define GPIO_NAND_CE3                 (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_NAND_CS3_INDEX))
#define GPIO_NAND_CLE                 (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_NAND_CLE_INDEX))
#define GPIO_NAND_DATA00              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_NAND_DATA00_INDEX))
#define GPIO_NAND_DATA01              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_NAND_DATA01_INDEX))
#define GPIO_NAND_DATA02              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_NAND_DATA02_INDEX))
#define GPIO_NAND_DATA03              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_NAND_DATA03_INDEX))
#define GPIO_NAND_DATA04              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_NAND_DATA04_INDEX))
#define GPIO_NAND_DATA05              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_NAND_DATA05_INDEX))
#define GPIO_NAND_DATA06              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_NAND_DATA06_INDEX))
#define GPIO_NAND_DATA07              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_NAND_DATA07_INDEX))
#define GPIO_NAND_DQS                 (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD4_DATA0_INDEX))
#define GPIO_NAND_RE                  (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD4_CMD_INDEX))
#define GPIO_NAND_READY               (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_NAND_READY_INDEX))
#define GPIO_NAND_WE                  (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD4_CLK_INDEX))
#define GPIO_NAND_WP                  (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_NAND_WP_INDEX))

/* Pulse Width Modulation (PWM) */

#define GPIO_PWM1_OUT_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA08_INDEX))
#define GPIO_PWM1_OUT_2               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_SD1_DATA3_INDEX))
#define GPIO_PWM1_OUT_3               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_GPIO09_INDEX))

#define GPIO_PWM2_OUT_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA09_INDEX))
#define GPIO_PWM2_OUT_2               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_SD1_DATA2_INDEX))
#define GPIO_PWM2_OUT_3               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_GPIO01_INDEX))

#define GPIO_PWM3_OUT_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD1_DATA1_INDEX))
#define GPIO_PWM3_OUT_2               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD4_DATA1_INDEX))

#define GPIO_PWM4_OUT_1               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD1_CMD_INDEX))
#define GPIO_PWM4_OUT_2               (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD4_DATA2_INDEX))

/* RGMII */

#define GPIO_RGMII_RD0                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_RGMII_RD0_INDEX))
#define GPIO_RGMII_RD1                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_RGMII_RD1_INDEX))
#define GPIO_RGMII_RD2                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_RGMII_RD2_INDEX))
#define GPIO_RGMII_RD3                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_RGMII_RD3_INDEX))
#define GPIO_RGMII_RX_CTL             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_RGMII_RX_CTL_INDEX))
#define GPIO_RGMII_RXC                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_RGMII_RXC_INDEX))
#define GPIO_RGMII_TD0                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_RGMII_TD0_INDEX))
#define GPIO_RGMII_TD1                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_RGMII_TD1_INDEX))
#define GPIO_RGMII_TD2                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_RGMII_TD2_INDEX))
#define GPIO_RGMII_TD3                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_RGMII_TD3_INDEX))
#define GPIO_RGMII_TX_CTL             (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_RGMII_TX_CTL_INDEX))
#define GPIO_RGMII_TXC                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_RGMII_TXC_INDEX))

/* SD card */

#define GPIO_SD1_CD                   (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_GPIO01_INDEX))
#define GPIO_SD1_CLK                  (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD1_CLK_INDEX))
#define GPIO_SD1_CMD                  (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD1_CMD_INDEX))
#define GPIO_SD1_DATA0                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD1_DATA0_INDEX))
#define GPIO_SD1_DATA1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD1_DATA1_INDEX))
#define GPIO_SD1_DATA2                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD1_DATA2_INDEX))
#define GPIO_SD1_DATA3                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD1_DATA3_INDEX))
#define GPIO_SD1_DATA4                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_NAND_DATA00_INDEX))
#define GPIO_SD1_DATA5                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_NAND_DATA01_INDEX))
#define GPIO_SD1_DATA6                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_NAND_DATA02_INDEX))
#define GPIO_SD1_DATA7                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_NAND_DATA03_INDEX))
#define GPIO_SD1_LCTL                 (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_GPIO16_INDEX))
#define GPIO_SD1_VSELECT_1            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_KEY_COL1_INDEX))
#define GPIO_SD1_VSELECT_2            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW3_INDEX))
#define GPIO_SD1_WP_1                 (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_DI0_PIN04_INDEX))
#define GPIO_SD1_WP_2                 (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_GPIO09_INDEX))

#define GPIO_SD2_CD                   (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_GPIO04_INDEX))
#define GPIO_SD2_CLK                  (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD2_CLK_INDEX))
#define GPIO_SD2_CMD                  (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD2_CMD_INDEX))
#define GPIO_SD2_DATA0                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD2_DATA0_INDEX))
#define GPIO_SD2_DATA1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD2_DATA1_INDEX))
#define GPIO_SD2_DATA2                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD2_DATA2_INDEX))
#define GPIO_SD2_DATA3                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD2_DATA3_INDEX))
#define GPIO_SD2_DATA4                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_NAND_DATA04_INDEX))
#define GPIO_SD2_DATA5                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_NAND_DATA05_INDEX))
#define GPIO_SD2_DATA6                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_NAND_DATA06_INDEX))
#define GPIO_SD2_DATA7                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_NAND_DATA07_INDEX))
#define GPIO_SD2_LCTL                 (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_GPIO06_INDEX))
#define GPIO_SD2_VSELECT_1            (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW2_INDEX))
#define GPIO_SD2_VSELECT_2            (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW1_INDEX))
#define GPIO_SD2_WP                   (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_GPIO02_INDEX))

#define GPIO_SD3_CLK                  (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD3_CLK_INDEX))
#define GPIO_SD3_CMD                  (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD3_CMD_INDEX))
#define GPIO_SD3_DATA0                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD3_DATA0_INDEX))
#define GPIO_SD3_DATA1                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD3_DATA1_INDEX))
#define GPIO_SD3_DATA2                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD3_DATA2_INDEX))
#define GPIO_SD3_DATA3                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD3_DATA3_INDEX))
#define GPIO_SD3_DATA4                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD3_DATA4_INDEX))
#define GPIO_SD3_DATA5                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD3_DATA5_INDEX))
#define GPIO_SD3_DATA6                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD3_DATA6_INDEX))
#define GPIO_SD3_DATA7                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD3_DATA7_INDEX))
#define GPIO_SD3_RESET                (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD3_RESET_INDEX))
#define GPIO_SD3_VSELECT_1            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_GPIO18_INDEX))
#define GPIO_SD3_VSELECT_2            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_NAND_CS1_INDEX))

#define GPIO_SD4_CLK                  (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD4_CLK_INDEX))
#define GPIO_SD4_CMD                  (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_SD4_CMD_INDEX))
#define GPIO_SD4_DATA0                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD4_DATA0_INDEX))
#define GPIO_SD4_DATA1                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD4_DATA1_INDEX))
#define GPIO_SD4_DATA2                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD4_DATA2_INDEX))
#define GPIO_SD4_DATA3                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD4_DATA3_INDEX))
#define GPIO_SD4_DATA4                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD4_DATA4_INDEX))
#define GPIO_SD4_DATA5                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD4_DATA5_INDEX))
#define GPIO_SD4_DATA6                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD4_DATA6_INDEX))
#define GPIO_SD4_DATA7                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD4_DATA7_INDEX))
#define GPIO_SD4_RESET                (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_NAND_ALE_INDEX))
#define GPIO_SD4_VSELECT              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_NAND_CS1_INDEX))

/* Smart Direct Memory Access Controller (SDMA) */

#define GPIO_SDMA_EXT_EVENT0_1        (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_GPIO17_INDEX))
#define GPIO_SDMA_EXT_EVENT0_2        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA16_INDEX))
#define GPIO_SDMA_EXT_EVENT1_1        (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_GPIO18_INDEX))
#define GPIO_SDMA_EXT_EVENT1_2        (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA17_INDEX))

/* Secure Non-Volatile Storage (SNVS) */

#define GPIO_SNVS_VIO_5               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_GPIO00_INDEX))
#define GPIO_SNVS_VIO_5_CTL           (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_GPIO18_INDEX))

/* Sony/Philips Digital Interface (SPDIF) */

#define GPIO_SPDIF_EXT_CLK_1          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_RGMII_TXC_INDEX))
#define GPIO_SPDIF_EXT_CLK_2          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_ENET_CRS_DV_INDEX))
#define GPIO_SPDIF_IN_1               (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_ENET_RX_ER_INDEX))
#define GPIO_SPDIF_IN_2               (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_GPIO16_INDEX))
#define GPIO_SPDIF_IN_3               (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_KEY_COL3_INDEX))
#define GPIO_SPDIF_IN_4               (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA21_INDEX))
#define GPIO_SPDIF_LOCK_1             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_ENET_MDIO_INDEX))
#define GPIO_SPDIF_LOCK_2             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_GPIO07_INDEX))
#define GPIO_SPDIF_OUT_1              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_GPIO19_INDEX))
#define GPIO_SPDIF_OUT_2              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_ENET_RX_DATA0_INDEX))
#define GPIO_SPDIF_OUT_3              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_GPIO17_INDEX))
#define GPIO_SPDIF_OUT_4              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA22_INDEX))
#define GPIO_SPDIF_SR_CLK_1           (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_ENET_REF_CLK_INDEX))
#define GPIO_SPDIF_SR_CLK_2           (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_GPIO08_INDEX))

/* System Reset Controller (SRC) */

#define GPIO_SRC_BOOT_CFG00           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_AD00_INDEX))
#define GPIO_SRC_BOOT_CFG01           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_AD01_INDEX))
#define GPIO_SRC_BOOT_CFG02           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_AD02_INDEX))
#define GPIO_SRC_BOOT_CFG03           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_AD03_INDEX))
#define GPIO_SRC_BOOT_CFG04           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_AD04_INDEX))
#define GPIO_SRC_BOOT_CFG05           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_AD05_INDEX))
#define GPIO_SRC_BOOT_CFG06           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_AD06_INDEX))
#define GPIO_SRC_BOOT_CFG07           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_AD07_INDEX))
#define GPIO_SRC_BOOT_CFG08           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_AD08_INDEX))
#define GPIO_SRC_BOOT_CFG09           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_AD09_INDEX))
#define GPIO_SRC_BOOT_CFG10           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_AD10_INDEX))
#define GPIO_SRC_BOOT_CFG11           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_AD11_INDEX))
#define GPIO_SRC_BOOT_CFG12           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_AD12_INDEX))
#define GPIO_SRC_BOOT_CFG13           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_AD13_INDEX))
#define GPIO_SRC_BOOT_CFG14           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_AD14_INDEX))
#define GPIO_SRC_BOOT_CFG15           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_AD15_INDEX))
#define GPIO_SRC_BOOT_CFG16           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR16_INDEX))
#define GPIO_SRC_BOOT_CFG17           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR17_INDEX))
#define GPIO_SRC_BOOT_CFG18           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR18_INDEX))
#define GPIO_SRC_BOOT_CFG19           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR19_INDEX))
#define GPIO_SRC_BOOT_CFG20           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR20_INDEX))
#define GPIO_SRC_BOOT_CFG21           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR21_INDEX))
#define GPIO_SRC_BOOT_CFG22           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR22_INDEX))
#define GPIO_SRC_BOOT_CFG23           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR23_INDEX))
#define GPIO_SRC_BOOT_CFG24           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_ADDR24_INDEX))
#define GPIO_SRC_BOOT_CFG25           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_WAIT_INDEX))
#define GPIO_SRC_BOOT_CFG26           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_LBA_INDEX))
#define GPIO_SRC_BOOT_CFG27           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_EB0_INDEX))
#define GPIO_SRC_BOOT_CFG28           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_EB1_INDEX))
#define GPIO_SRC_BOOT_CFG29           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_RW_INDEX))
#define GPIO_SRC_BOOT_CFG30           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_EB2_INDEX))
#define GPIO_SRC_BOOT_CFG31           (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_EB3_INDEX))

/* UARTs */

#define IOMUX_UART                    (IOMUX_PULL_UP_100K | IOMUX_CMOS_OUTPUT | IOMUX_DRIVE_40OHM | \
                                       IOMUX_SLEW_FAST | IOMUX_SPEED_MEDIUM | IOMUX_SCHMITT_TRIGGER)

#define GPIO_UART1_CTS_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD3_DATA0_INDEX) | IOMUX_UART)
#define GPIO_UART1_CTS_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA19_INDEX) | IOMUX_UART)
#define GPIO_UART1_DCD                (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA23_INDEX) | IOMUX_UART)
#define GPIO_UART1_DSR                (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA25_INDEX) | IOMUX_UART)
#define GPIO_UART1_DTR                (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA24_INDEX) | IOMUX_UART)
#define GPIO_UART1_RI                 (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_EIM_EB3_INDEX) | IOMUX_UART)
#define GPIO_UART1_RTS_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD3_DATA1_INDEX) | IOMUX_UART)
#define GPIO_UART1_RTS_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA20_INDEX) | IOMUX_UART)
#define GPIO_UART1_RX_DATA_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD3_DATA6_INDEX) | IOMUX_UART)
#define GPIO_UART1_RX_DATA_2          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA11_INDEX) | IOMUX_UART)
#define GPIO_UART1_TX_DATA_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD3_DATA7_INDEX) | IOMUX_UART)
#define GPIO_UART1_TX_DATA_2          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA10_INDEX) | IOMUX_UART)

#define GPIO_UART2_CTS_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD3_CMD_INDEX) | IOMUX_UART)
#define GPIO_UART2_CTS_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD4_DATA6_INDEX) | IOMUX_UART)
#define GPIO_UART2_CTS_3              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA28_INDEX) | IOMUX_UART)
#define GPIO_UART2_RTS_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD3_CLK_INDEX) | IOMUX_UART)
#define GPIO_UART2_RTS_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD4_DATA5_INDEX) | IOMUX_UART)
#define GPIO_UART2_RTS_3              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA29_INDEX) | IOMUX_UART)
#define GPIO_UART2_RX_DATA_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD3_DATA4_INDEX) | IOMUX_UART)
#define GPIO_UART2_RX_DATA_2          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD4_DATA4_INDEX) | IOMUX_UART)
#define GPIO_UART2_RX_DATA_3          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA27_INDEX) | IOMUX_UART)
#define GPIO_UART2_RX_DATA_4          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_GPIO08_INDEX) | IOMUX_UART)
#define GPIO_UART2_TX_DATA_1          (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD3_DATA5_INDEX) | IOMUX_UART)
#define GPIO_UART2_TX_DATA_2          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD4_DATA7_INDEX) | IOMUX_UART)
#define GPIO_UART2_TX_DATA_3          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA26_INDEX) | IOMUX_UART)
#define GPIO_UART2_TX_DATA_4          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_GPIO07_INDEX) | IOMUX_UART)

#define GPIO_UART3_CTS_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD3_DATA3_INDEX) | IOMUX_UART)
#define GPIO_UART3_CTS_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA23_INDEX) | IOMUX_UART)
#define GPIO_UART3_CTS_3              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA30_INDEX) | IOMUX_UART)
#define GPIO_UART3_RTS_1              (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_SD3_RESET_INDEX) | IOMUX_UART)
#define GPIO_UART3_RTS_2              (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_EB3_INDEX) | IOMUX_UART)
#define GPIO_UART3_RTS_3              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA31_INDEX) | IOMUX_UART)
#define GPIO_UART3_RX_DATA_1          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA25_INDEX) | IOMUX_UART)
#define GPIO_UART3_RX_DATA_2          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD4_CLK_INDEX) | IOMUX_UART)
#define GPIO_UART3_TX_DATA_1          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA24_INDEX) | IOMUX_UART)
#define GPIO_UART3_TX_DATA_2          (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD4_CMD_INDEX) | IOMUX_UART)

#define GPIO_UART4_CTS                (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA17_INDEX) | IOMUX_UART)
#define GPIO_UART4_RTS                (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA16_INDEX) | IOMUX_UART)
#define GPIO_UART4_RX_DATA_1          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA13_INDEX) | IOMUX_UART)
#define GPIO_UART4_RX_DATA_2          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW0_INDEX) | IOMUX_UART)
#define GPIO_UART4_TX_DATA_1          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA12_INDEX) | IOMUX_UART)
#define GPIO_UART4_TX_DATA_2          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_KEY_COL0_INDEX) | IOMUX_UART)

#define GPIO_UART5_CTS_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA19_INDEX) | IOMUX_UART)
#define GPIO_UART5_CTS_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW4_INDEX) | IOMUX_UART)
#define GPIO_UART5_RTS_1              (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA18_INDEX) | IOMUX_UART)
#define GPIO_UART5_RTS_2              (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_KEY_COL4_INDEX) | IOMUX_UART)
#define GPIO_UART5_RX_DATA_1          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA15_INDEX) | IOMUX_UART)
#define GPIO_UART5_RX_DATA_2          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW1_INDEX) | IOMUX_UART)
#define GPIO_UART5_TX_DATA_1          (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_CSI0_DATA14_INDEX) | IOMUX_UART)
#define GPIO_UART5_TX_DATA_2          (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_KEY_COL1_INDEX) | IOMUX_UART)

/* USB */

#define GPIO_USB_H1_OC_1              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA30_INDEX))
#define GPIO_USB_H1_OC_2              (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_GPIO03_INDEX))
#define GPIO_USB_H1_PWR_1             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA31_INDEX))
#define GPIO_USB_H1_PWR_2             (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_GPIO00_INDEX))
#define GPIO_USB_H1_PWR_CTL_WAKE      (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_KEY_COL2_INDEX))
#define GPIO_USB_H2_DATA              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_RGMII_TXC_INDEX))
#define GPIO_USB_H2_STROBE            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_RGMII_TX_CTL_INDEX))
#define GPIO_USB_H3_DATA              (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_RGMII_RX_CTL_INDEX))
#define GPIO_USB_H3_STROBE            (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_RGMII_RXC_INDEX))
#define GPIO_USB_OTG_HOST_MODE        (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_GPIO07_INDEX))
#define GPIO_USB_OTG_ID_1             (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_ENET_RX_ER_INDEX))
#define GPIO_USB_OTG_ID_2             (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_GPIO01_INDEX))
#define GPIO_USB_OTG_OC_1             (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_KEY_COL4_INDEX))
#define GPIO_USB_OTG_OC_2             (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA21_INDEX))
#define GPIO_USB_OTG_PWR_1            (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW4_INDEX))
#define GPIO_USB_OTG_PWR_2            (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_EIM_DATA22_INDEX))
#define GPIO_USB_OTG_PWR_CTL_WAKE     (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_GPIO08_INDEX))

/* WDOGs */

#define GPIO_WDOG1_1                  (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_GPIO09_INDEX))
#define GPIO_WDOG1_2                  (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA08_INDEX))
#define GPIO_WDOG1_3                  (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_SD1_DATA2_INDEX))
#define GPIO_WDOG1_RESET_DEB          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_SD1_DATA2_INDEX))

#define GPIO_WDOG2_1                  (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_GPIO01_INDEX))
#define GPIO_WDOG2_2                  (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_DISP0_DATA09_INDEX))
#define GPIO_WDOG2_3                  (GPIO_PERIPH | GPIO_ALT4 | GPIO_PADMUX(IMX_PADMUX_SD1_DATA3_INDEX))
#define GPIO_WDOG2_RESET_DEB          (GPIO_PERIPH | GPIO_ALT6 | GPIO_PADMUX(IMX_PADMUX_SD1_DATA3_INDEX))

/* Crystal Oscillator */

#define GPIO_XTALOSC_OSC32K_32K_OUT_1 (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_ENET_RX_DATA0_INDEX))
#define GPIO_XTALOSC_OSC32K_32K_OUT_2 (GPIO_PERIPH | GPIO_ALT0 | GPIO_PADMUX(IMX_PADMUX_KEY_ROW3_INDEX))
#define GPIO_XTALOSC_OSC32K_32K_OUT_3 (GPIO_PERIPH | GPIO_ALT2 | GPIO_PADMUX(IMX_PADMUX_SD1_CLK_INDEX))
#define GPIO_XTALOSC_REF_CLK_24M_1    (GPIO_PERIPH | GPIO_ALT3 | GPIO_PADMUX(IMX_PADMUX_GPIO03_INDEX))
#define GPIO_XTALOSC_REF_CLK_24M_2    (GPIO_PERIPH | GPIO_ALT7 | GPIO_PADMUX(IMX_PADMUX_RGMII_TXC_INDEX))
#define GPIO_XTALOSC_REF_CLK_32K      (GPIO_PERIPH | GPIO_ALT1 | GPIO_PADMUX(IMX_PADMUX_GPIO08_INDEX))

#endif /* __ARCH_ARM_SRC_IMX6_HARDWARE_IMX_PINMUX_H */
