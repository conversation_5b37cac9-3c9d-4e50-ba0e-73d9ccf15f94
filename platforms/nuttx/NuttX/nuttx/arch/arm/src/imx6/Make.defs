############################################################################
# arch/arm/src/imx6/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

include armv7-a/Make.defs

# i.MX6-specific C source files

CHIP_CSRCS  = imx_boot.c imx_memorymap.c imx_clockconfig.c imx_irq.c
CHIP_CSRCS += imx_timerisr.c imx_gpio.c imx_iomuxc.c
CHIP_CSRCS += imx_serial.c imx_lowputc.c imx_idle.c

ifeq ($(CONFIG_SMP),y)
CHIP_CSRCS += imx_cpuboot.c
endif

ifeq ($(CONFIG_IMX6_ECSPI),y)
CHIP_CSRCS += imx_ecspi.c
endif

ifeq ($(CONFIG_IMX6_ENET),y)
CHIP_CSRCS += imx_enet.c
endif

ifeq ($(CONFIG_MM_PGALLOC),y)
CHIP_CSRCS += imx_pgalloc.c
endif
