#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

if ARCH_CHIP_IMX6

menu "iMX.6 Chip Selection"

choice
	prompt "iMX.6 Core Configuration"
	default ARCH_CHIP_IMX6_6QUAD

config ARCH_CHIP_IMX6_6SOLOLITE
	bool "i.MX 6SoloLite"
	select ARMV7A_HAVE_GICv2
	select ARMV7A_HAVE_GTM
	select ARMV7A_HAVE_PTM

config ARCH_CHIP_IMX6_6SOLO
	bool "i.MX 6Solo"
	select ARMV7A_HAVE_GICv2
	select ARMV7A_HAVE_GTM
	select ARMV7A_HAVE_PTM

config ARCH_CHIP_IMX6_6DUALLITE
	bool "i.MX 6DualLite"
	select ARCH_HAVE_MULTICPU
	select ARMV7A_HAVE_GICv2
	select ARMV7A_HAVE_GTM
	select ARMV7A_HAVE_PTM

config ARCH_CHIP_IMX6_6DUAL
	bool "i.MX 6Dual"
	select ARCH_HAVE_MULTICPU
	select ARMV7A_HAVE_GICv2
	select ARMV7A_HAVE_GTM
	select ARMV7A_HAVE_PTM

config ARCH_CHIP_IMX6_6QUAD
	bool "i.MX 6Quad"
	select ARCH_HAVE_MULTICPU
	select ARMV7A_HAVE_GICv2
	select ARMV7A_HAVE_GTM
	select ARMV7A_HAVE_PTM

endchoice # iMX.6 Chip Selection

config IMX6_HAVE_HDCP
	bool "HDCP enabled"
	default n
	---help---
		HDCP - High-bandwidth Digital Content Protection

endmenu # "iMX.6 Chip Selection"

config IMX6_ECSPI
	bool
	default n

menu "iMX.6 Peripheral Selection"

config IMX6_ECSPI1
	bool "ECSPI1"
	default n
	select IMX6_ECSPI

config IMX6_ECSPI2
	bool "ECSPI2"
	default n
	select IMX6_ECSPI

config IMX6_ECSPI3
	bool "ECSPI3"
	default n
	select IMX6_ECSPI

config IMX6_ECSPI4
	bool "ECSPI4"
	default n
	select IMX6_ECSPI

config IMX6_ECSPI5
	bool "ECSPI5"
	default n
	select IMX6_ECSPI

config IMX6_UART1
	bool "UART1"
	default n
	select UART1_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config IMX6_UART2
	bool "UART2"
	default n
	select UART2_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config IMX6_UART3
	bool "UART3"
	default n
	select UART3_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config IMX6_UART4
	bool "UART4"
	default n
	select UART4_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config IMX6_UART5
	bool "UART5"
	default n
	select UART5_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config IMX6_SPI1
	bool "SPI1"
	default n
	select SPI

config IMX6_SPI2
	bool "SPI2"
	default n
	select SPI

config IMX6_ENET
	bool "Ethernet"
	default n
	select ARCH_HAVE_PHY
	select ARCH_PHY_INTERRUPT
	select ARCH_HAVE_NETDEV_STATISTICS

endmenu # iMX Peripheral Selection

menu "Ethernet Configuration"
	depends on IMX6_ENET

config IMX_ENET_NRXBUFFERS
	int "Number Rx buffers"
	default 6

config IMX_ENET_NTXBUFFERS
	int "Number Tx buffers"
	default 2

config IMX_ENET_ENHANCEDBD
	bool # not optional
	default n

config IMX_ENET_NETHIFS
	int  # Not optional
	default 1

config IMX_ENET_PHYINIT
	bool "Board-specific PHY Initialization"
	default n
	---help---
		Some boards require specialized initialization of the PHY before it
		can be used.  This may include such things as configuring GPIOs,
		resetting the PHY, etc.  If CONFIG_IMX_ENET_PHYINIT is defined in
		the configuration then the board specific logic must provide
		imx_phy_boardinitialize();  The i.MX6 ENET driver will call this
		function one time before it first uses the PHY.

endmenu # IMX_ENET

config IMX_DDR_SIZE
	int "Installed DRAM size (bytes)"
	default 268435456

choice
	prompt "i.MX6 Boot Configuration"
	default IMX6_BOOT_SDRAM
	---help---
		The startup code needs to know if the code is running from internal SRAM,
		external SRAM, or CS0-3 in order to initialize properly.  Note that the
		boot device is not specified for cases where the code is copied into
		RAM.

config IMX6_BOOT_OCRAM
	bool "Running from internal OCRAM"
	select BOOT_RUNFROMISRAM

config IMX6_BOOT_SDRAM
	bool "Running from external SDRAM"
	select BOOT_RUNFROMSDRAM

config IMX6_BOOT_NOR
	bool "Running from external NOR FLASH"
	select BOOT_RUNFROMFLASH

config IMX6_BOOT_SRAM
	bool "Running from external SRAM"
	select BOOT_RUNFROMEXTSRAM

endchoice # i.MX6 Boot Configuration

config IMX6_DDRCS_PGHEAP
	bool "Include DDR-SDRAM in page cache"
	default y
	depends on IMX6_BOOT_SDRAM && ARCH_ADDRENV
	---help---
		Include a portion of DDR-SDRAM memory in the page cache.

if IMX6_DDRCS_PGHEAP

config IMX6_DDRCS_PGHEAP_OFFSET
	hex "DDR-SDRAM heap offset"
	default 0x0
	---help---
		Preserve this number of bytes at the beginning of SDRAM.  The
		portion of DRAM beginning at this offset from the DDRCS base will
		be added to the heap.

config IMX6_DDRCS_PGHEAP_SIZE
	int "DDR-SDRAM heap size"
	default 0
	---help---
		Add the region of DDR-SDRAM beginning at IMX6_DDRCS_PGHEAP_OFFSET
		and of size IMX6_DDRCS_PGHEAP_SIZE to the heap.

endif # IMX6_DDRCS_PGHEAP

endif # ARCH_CHIP_IMX6
