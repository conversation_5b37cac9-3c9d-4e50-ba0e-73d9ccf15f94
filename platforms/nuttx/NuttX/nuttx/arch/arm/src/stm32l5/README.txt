This is a port of Nutt<PERSON> to the STM32L5 Family

Used development boards are the Nucleo L552ZE-Q, and STM32L562E-DK.

Most code is copied and adapted from the STM32L4 port.

The only supported STM32L5 family currently is:

-----------------------------------------------------------------
| NuttX config      | Manual | Chips
| STM32L5           | RM0438 | STM32L552xx and STM32L562xx
------------------------------------------------------------------

TODO list
---------

Extensive testing.  Only initial sniff tests have been done.
A prober TODO list should be generated.
