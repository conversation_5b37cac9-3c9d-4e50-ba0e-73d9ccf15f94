/****************************************************************************
 * arch/arm/src/tms570/hardware/tms570ls04x03x_pinmux.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 *
 ****************************************************************************/

/* References:
 * TMS570LS04x/03x 16/32-Bit RISC Flash Microcontroller,
 * Technical Reference Manual, Texas Instruments,
 * Literature Number: SPNU517A, September 2013
 */

#ifndef __ARCH_ARM_SRC_TMS570_HARDWARE_TMS570LS04X03X_PINMUX_H
#define __ARCH_ARM_SRC_TMS570_HARDWARE_TMS570LS04X03X_PINMUX_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include "hardware/tms570_memorymap.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* DEFAULT       SELECTION   ALTERNATE  SELECTION   ALTERNATE  SELECTION BIT
 * FUNCTION      BIT         FUNCTION 1 BIT         FUNCTION 2 BIT
 *
 * GIOA[0]       PINMMR0[8]  SPI3nCS[3] PINMMR0[9]  - -
 * GIOA[1]       PINMMR1[0]  SPI3nCS[2] PINMMR1[1]  - -
 * GIOA[2]       PINMMR1[8]  SPI3nCS[1] PINMMR1[9]  - -
 * GIOA[3]       PINMMR1[16] SPI2nCS[3] PINMMR1[17] - -
 * GIOA[4]       PINMMR1[24] SPI2nCS[2] PINMMR1[25] - -
 * GIOA[5]       PINMMR2[0]  EXTCLKIN   PINMMR2[1]  - -
 * GIOA[6]       PINMMR2[8]  SPI2nCS[1] PINMMR2[9]  N2HET[31]  PINMMR2[10]
 * GIOA[7]       PINMMR2[16] N2HET[29]  PINMMR2[17] - -
 * MIBSPI1nCS[2] PINMMR3[0]  N2HET[20]  PINMMR3[1]  N2HET[19]  PINMMR3[2]
 * SPI3CLK       PINMMR3[16] EQEPA      PINMMR3[17] - -
 * SPI3nENA      PINMMR3[24] EQEPB      PINMMR3[25] - -
 * SPI3nCS[0]    PINMMR4[0]  EQEPI      PINMMR4[1]  - -
 * MIBSPI1nCS[3] PINMMR4[8]  N2HET[26]  PINMMR4[9]  - -
 * ADEVT         PINMMR4[16] N2HET[28]  PINMMR4[17] - -
 * MIBSPI1nENA   PINMMR5[8]  N2HET[23]  PINMMR5[9]  NHET[30]   PINMMR5[10]
 * MIBSPI1nCS[1] PINMMR6[8]  EQEPS      PINMMR6[9]  N2HET[17]  PINMMR6[10]
 */

#define PINMUX_GIOA0_PINMMR       0
#define PINMUX_GIOA0_SHIFT        8
#define PINMUX_GIOA0_VALUE        1
#define PINMUX_GIOA0_PIN          {PINMUX_GIOA0_PINMMR, PINMUX_GIOA0_SHIFT, PINMUX_GIOA0_VALUE}

#define PINMUX_SPI3NCS3_PINMMR    0
#define PINMUX_SPI3NCS3_SHIFT     8
#define PINMUX_SPI3NCS3_VALUE     2
#define PINMUX_SPI3NCS3_PIN       {PINMUX_SPI3NCS3_PINMMR, PINMUX_SPI3NCS3_SHIFT, PINMUX_SPI3NCS3_VALUE}

#define PINMUX_GIOA1_PINMMR       1
#define PINMUX_GIOA1_SHIFT        0
#define PINMUX_GIOA1_VALUE        1
#define PINMUX_GIOA1_PIN          {PINMUX_GIOA1_PINMMR, PINMUX_GIOA1_SHIFT, PINMUX_GIOA1_VALUE}

#define PINMUX_SPI3NCS2_PINMMR    1
#define PINMUX_SPI3NCS2_SHIFT     0
#define PINMUX_SPI3NCS2_VALUE     2
#define PINMUX_SPI3NCS2_PIN       {PINMUX_SPI3NCS2_PINMMR, PINMUX_SPI3NCS2_SHIFT, PINMUX_SPI3NCS2_VALUE}

#define PINMUX_GIOA2_PINMMR       1
#define PINMUX_GIOA2_SHIFT        8
#define PINMUX_GIOA2_VALUE        1
#define PINMUX_GIOA2_PIN          {PINMUX_GIOA2_PINMMR, PINMUX_GIOA2_SHIFT, PINMUX_GIOA2_VALUE}

#define PINMUX_SPI3NCS1_PINMMR    1
#define PINMUX_SPI3NCS1_SHIFT     8
#define PINMUX_SPI3NCS1_VALUE     2
#define PINMUX_SPI3NCS1_PIN       {PINMUX_SPI3NCS1_PINMMR, PINMUX_SPI3NCS1_SHIFT, PINMUX_SPI3NCS1_VALUE}

#define PINMUX_GIOA3_PINMMR       1
#define PINMUX_GIOA3_SHIFT        16
#define PINMUX_GIOA3_VALUE        1
#define PINMUX_GIOA3_PIN          {PINMUX_GIOA3_PINMMR, PINMUX_GIOA3_SHIFT, PINMUX_GIOA3_VALUE}

#define PINMUX_SPI2NCS3_PINMMR    1
#define PINMUX_SPI2NCS3_SHIFT     16
#define PINMUX_SPI2NCS3_VALUE     2
#define PINMUX_SPI2NCS3_PIN       {PINMUX_SPI2NCS3_PINMMR, PINMUX_SPI2NCS3_SHIFT, PINMUX_SPI2NCS3_VALUE}

#define PINMUX_GIOA4_PINMMR       1
#define PINMUX_GIOA4_SHIFT        24
#define PINMUX_GIOA4_VALUE        1
#define PINMUX_GIOA4_PIN          {PINMUX_GIOA4_PINMMR, PINMUX_GIOA4_SHIFT, PINMUX_GIOA4_VALUE}

#define PINMUX_SPI2NCS2_PINMMR    1
#define PINMUX_SPI2NCS2_SHIFT     24
#define PINMUX_SPI2NCS2_VALUE     2
#define PINMUX_SPI2NCS2_PIN       {PINMUX_SPI2NCS2_PINMMR, PINMUX_SPI2NCS2_SHIFT, PINMUX_SPI2NCS2_VALUE}

#define PINMUX_GIOA5_PINMMR       2
#define PINMUX_GIOA5_SHIFT        0
#define PINMUX_GIOA5_VALUE        1
#define PINMUX_GIOA5_PIN          {PINMUX_GIOA5_PINMMR, PINMUX_GIOA5_SHIFT, PINMUX_GIOA5_VALUE}

#define PINMUX_EXTCLKIN_PINMMR    2
#define PINMUX_EXTCLKIN_SHIFT     0
#define PINMUX_EXTCLKIN_VALUE     2
#define PINMUX_EXTCLKIN_PIN       {PINMUX_EXTCLKIN_PINMMR, PINMUX_EXTCLKIN_SHIFT, PINMUX_EXTCLKIN_VALUE}

#define PINMUX_GIOA6_PINMMR       2
#define PINMUX_GIOA6_SHIFT        8
#define PINMUX_GIOA6_VALUE        1
#define PINMUX_GIOA6_PIN          {PINMUX_GIOA6_PINMMR, PINMUX_GIOA6_SHIFT, PINMUX_GIOA6_VALUE}

#define PINMUX_SPI2NCS1_PINMMR    2
#define PINMUX_SPI2NCS1_SHIFT     8
#define PINMUX_SPI2NCS1_VALUE     2
#define PINMUX_SPI2NCS1_PIN       {PINMUX_SPI2NCS1_PINMMR, PINMUX_SPI2NCS1_SHIFT, PINMUX_SPI2NCS1_VALUE}

#define PINMUX_N2HET31_PINMMR     2
#define PINMUX_N2HET31_SHIFT      8
#define PINMUX_N2HET31_VALUE      4
#define PINMUX_N2HET31_PIN        {PINMUX_N2HET31_PINMMR, PINMUX_N2HET31_SHIFT, PINMUX_N2HET31_VALUE}

#define PINMUX_GIOA7_PINMMR       2
#define PINMUX_GIOA7_SHIFT        16
#define PINMUX_GIOA7_VALUE        1
#define PINMUX_GIOA7_PIN          {PINMUX_GIOA7_PINMMR, PINMUX_GIOA7_SHIFT, PINMUX_GIOA7_VALUE}

#define PINMUX_N2HET29_PINMMR     2
#define PINMUX_N2HET29_SHIFT      16
#define PINMUX_N2HET29_VALUE      2
#define PINMUX_N2HET29_PIN        {PINMUX_N2HET29_PINMMR, PINMUX_N2HET29_SHIFT, PINMUX_N2HET29_VALUE}

#define PINMUX_MIBSPI1NCS2_PINMMR 3
#define PINMUX_MIBSPI1NCS2_SHIFT  0
#define PINMUX_MIBSPI1NCS2_VALUE  1
#define PINMUX_MIBSPI1NCS2_PIN    {PINMUX_MIBSPI1NCS2_PINMMR, PINMUX_MIBSPI1NCS2_SHIFT, PINMUX_MIBSPI1NCS2_VALUE}

#define PINMUX_N2HET20_PINMMR     3
#define PINMUX_N2HET20_SHIFT      0
#define PINMUX_N2HET20_VALUE      2
#define PINMUX_N2HET20_PIN        {PINMUX_N2HET20_PINMMR, PINMUX_N2HET20_SHIFT, PINMUX_N2HET20_VALUE}

#define PINMUX_N2HET19_PINMMR     3
#define PINMUX_N2HET19_SHIFT      0
#define PINMUX_N2HET19_VALUE      4
#define PINMUX_N2HET19_PIN        {PINMUX_N2HET19_PINMMR, PINMUX_N2HET19_SHIFT, PINMUX_N2HET19_VALUE}

#define PINMUX_SPI3CLK_PINMMR     3
#define PINMUX_SPI3CLK_SHIFT      16
#define PINMUX_SPI3CLK_VALUE      1
#define PINMUX_SPI3CLK_PIN        {PINMUX_SPI3CLK_PINMMR, PINMUX_SPI3CLK_SHIFT, PINMUX_N2HET20_VALUE}

#define PINMUX_EQEPA_PINMMR       3
#define PINMUX_EQEPA_SHIFT        16
#define PINMUX_EQEPA_VALUE        2
#define PINMUX_EQEPA_PIN          {PINMUX_EQEPA_PINMMR, PINMUX_EQEPA_SHIFT, PINMUX_EQEPA_VALUE}

#define PINMUX_SPI3NENA_PINMMR    3
#define PINMUX_SPI3NENA_SHIFT     24
#define PINMUX_SPI3NENA_VALUE     1
#define PINMUX_SPI3NENA_PIN       {PINMUX_SPI3NENA_PINMMR, PINMUX_SPI3NENA_SHIFT, PINMUX_SPI3NENA_VALUE}

#define PINMUX_EQEPB_PINMMR       3
#define PINMUX_EQEPB_SHIFT        24
#define PINMUX_EQEPB_VALUE        2
#define PINMUX_EQEPB_PIN          {PINMUX_EQEPB_PINMMR, PINMUX_EQEPB_SHIFT, PINMUX_EQEPB_VALUE}

#define PINMUX_SPI3NCS0_PINMMR    4
#define PINMUX_SPI3NCS0_SHIFT     0
#define PINMUX_SPI3NCS0_VALUE     1
#define PINMUX_SPI3NCS0_PIN       {PINMUX_SPI3NCS0_PINMMR, PINMUX_SPI3NCS0_SHIFT, PINMUX_SPI3NCS0_VALUE}

#define PINMUX_EQEPI_PINMMR       4
#define PINMUX_EQEPI_SHIFT        0
#define PINMUX_EQEPI_VALUE        2
#define PINMUX_EQEPI_PIN          {PINMUX_EQEPI_PINMMR, PINMUX_EQEPI_SHIFT, PINMUX_EQEPI_VALUE}

#define PINMUX_MIBSPI1NCS3_PINMMR 4
#define PINMUX_MIBSPI1NCS3_SHIFT  8
#define PINMUX_MIBSPI1NCS3_VALUE  1
#define PINMUX_MIBSPI1NCS3_PIN    {PINMUX_MIBSPI1NCS3_PINMMR, PINMUX_MIBSPI1NCS3_SHIFT, PINMUX_MIBSPI1NCS3_VALUE}

#define PINMUX_N2HET26_PINMMR     4
#define PINMUX_N2HET26_SHIFT      8
#define PINMUX_N2HET26_VALUE      2
#define PINMUX_N2HET26_PIN        {PINMUX_N2HET26_PINMMR, PINMUX_N2HET26_SHIFT, PINMUX_N2HET26_VALUE}

#define PINMUX_ADEVT_PINMMR       4
#define PINMUX_ADEVT_SHIFT        16
#define PINMUX_ADEVT_VALUE        1
#define PINMUX_ADEVT_PIN          {PINMUX_ADEVT_PINMMR, PINMUX_ADEVT_SHIFT, PINMUX_ADEVT_VALUE}

#define PINMUX_N2HET28_PINMMR     4
#define PINMUX_N2HET28_SHIFT      16
#define PINMUX_N2HET28_VALUE      2
#define PINMUX_N2HET28_PIN        {PINMUX_N2HET28_PINMMR, PINMUX_N2HET28_SHIFT, PINMUX_N2HET28_VALUE}

#define PINMUX_MIBSPI1NENA_PINMMR 5
#define PINMUX_MIBSPI1NENA_SHIFT  8
#define PINMUX_MIBSPI1NENA_VALUE  1
#define PINMUX_MIBSPI1NENA_PIN    {PINMUX_MIBSPI1NENA_PINMMR, PINMUX_MIBSPI1NENA_SHIFT, PINMUX_MIBSPI1NENA_VALUE}

#define PINMUX_N2HET23_PINMMR     5
#define PINMUX_N2HET23_SHIFT      8
#define PINMUX_N2HET23_VALUE      2
#define PINMUX_N2HET23_PIN        {PINMUX_N2HET23_PINMMR, PINMUX_N2HET23_SHIFT, PINMUX_N2HET23_VALUE}

#define PINMUX_N2HET30_PINMMR     5
#define PINMUX_N2HET30_SHIFT      8
#define PINMUX_N2HET30_VALUE      4
#define PINMUX_N2HET30_PIN        {PINMUX_N2HET30_PINMMR, PINMUX_N2HET30_SHIFT, PINMUX_N2HET30_VALUE}

#define PINMUX_MIBSPI1NCS1_PINMMR 6
#define PINMUX_MIBSPI1NCS1_SHIFT  8
#define PINMUX_MIBSPI1NCS1_VALUE  1
#define PINMUX_MIBSPI1NCS1_PIN    {PINMUX_MIBSPI1NCS1_PINMMR, PINMUX_MIBSPI1NCS1_SHIFT, PINMUX_MIBSPI1NCS1_VALUE}

#define PINMUX_EQEPS_PINMMR       6
#define PINMUX_EQEPS_SHIFT        8
#define PINMUX_EQEPS_VALUE        2
#define PINMUX_EQEPS_PIN          {PINMUX_EQEPS_PINMMR, PINMUX_EQEPS_SHIFT, PINMUX_EQEPS_VALUE}

#define PINMUX_N2HET17_PINMMR     6
#define PINMUX_N2HET17_SHIFT      8
#define PINMUX_N2HET17_VALUE      4
#define PINMUX_N2HET17_PIN        {PINMUX_N2HET17_PINMMR, PINMUX_N2HET17_SHIFT, PINMUX_N2HET17_VALUE}

#endif /* __ARCH_ARM_SRC_TMS570_HARDWARE_TMS570LS04X03X_PINMUX_H */
