############################################################################
# arch/arm/src/tms570/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

include armv7-r/Make.defs

# SAMA5-specific C source files

CHIP_CSRCS  = tms570_boot.c tms570_clockconfig.c tms570_esm.c tms570_gio.c
CHIP_CSRCS += tms570_irq.c tms570_lowputc.c tms570_serial.c tms570_spi.c

# Configuration dependent C and assembly language files

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += tms570_timerisr.c
endif

ifeq ($(CONFIG_TMS570_SELFTEST),y)
CHIP_CSRCS += tms570_selftest.c
endif

ifeq ($(CONFIG_TMS570_GIO_IRQ),y)
CHIP_CSRCS += tms570_gioirq.c
endif
