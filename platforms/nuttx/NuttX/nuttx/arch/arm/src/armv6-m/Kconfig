#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

comment "ARMv6-M Configuration Options"

choice
	prompt "Toolchain Selection"
	default ARMV6M_TOOLCHAIN_GNU_EABI

config ARMV6M_TOOLCHAIN_BUILDROOT
	bool "Buildroot (Cygwin or Linux)"
	select ARCH_TOOLCHAIN_GNU

config ARMV6M_TOOLCHAIN_GNU_EABI
	bool "Generic GNU EABI toolchain"
	select ARCH_TOOLCHAIN_GNU
	---help---
		This option should work for any modern GNU toolchain (GCC 4.5 or newer)
		configured for arm-none-eabi.

config ARMV6M_TOOLCHAIN_CLANG
	bool "Generic Clang toolchain"
	select ARCH_TOOLCHAIN_CLANG

endchoice
