############################################################################
# arch/arm/src/armv6-m/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

# Common ARM files

include common/Make.defs

CMN_ASRCS += arm_exception.S

CMN_CSRCS += arm_doirq.c arm_hardfault.c arm_initialstate.c
CMN_CSRCS += arm_schedulesigaction.c arm_sigdeliver.c arm_svcall.c
CMN_CSRCS += arm_systemreset.c arm_tcbinfo.c arm_vectors.c

ifeq ($(CONFIG_DEBUG_FEATURES),y)
  CMN_CSRCS += arm_dumpnvic.c
endif

ifeq ($(CONFIG_ARCH_RAMVECTORS),y)
  CMN_CSRCS += arm_ramvec_initialize.c arm_ramvec_attach.c
endif
