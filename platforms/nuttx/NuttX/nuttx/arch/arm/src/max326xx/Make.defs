############################################################################
# arch/arm/src/max326xx/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

# Common ARMv7-M Source Files

include armv7-m/Make.defs

# Common MAX326XX Source Files

CHIP_CSRCS  = max326_start.c max326_irq.c max326_clrpend.c

ifeq ($(CONFIG_MAX326XX_ICC),y)
CHIP_CSRCS += max326_icc.c
endif

ifeq ($(CONFIG_RTC_DRIVER),y)
CHIP_CSRCS += max326_rtc_lowerhalf.c
endif

# Source Files for the MAX32620 and MAX32630

# Source Files for the MAX32660

ifeq ($(CONFIG_ARCH_FAMILY_MAX32660),y)
CHIP_CSRCS += max32660_clockconfig.c max32660_lowputc.c max32660_gpio.c
endif

# Configuration-Dependent Source Files

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += max326_timerisr.c
else
CHIP_CSRCS += max326_tickless.c
endif

ifeq ($(CONFIG_BUILD_PROTECTED),y)
CHIP_CSRCS += max326_userspace.c max326_mpuinit.c
endif

ifeq ($(CONFIG_MAX326XX_DMA),y)
ifeq ($(CONFIG_ARCH_FAMILY_MAX32660),y)
CHIP_CSRCS += max32660_dma.c
endif
endif

ifneq ($(CONFIG_ARCH_IDLE_CUSTOM),y)
CHIP_CSRCS += max326_idle.c
endif

ifeq ($(CONFIG_MAX326XX_GPIOIRQ),y)
ifeq ($(CONFIG_ARCH_FAMILY_MAX32660),y)
CHIP_CSRCS += max32660_gpioirq.c
endif
endif

ifeq ($(CONFIG_MAX326XX_RTC),y)
ifeq ($(CONFIG_ARCH_FAMILY_MAX32660),y)
CHIP_CSRCS += max32660_rtc.c
endif
endif

ifeq ($(CONFIG_MAX32XX_WDT),y)
ifeq ($(CONFIG_ARCH_FAMILY_MAX32660),y)
CHIP_CSRCS += max32660_rtc.c
endif
ifeq ($(CONFIG_RTC_DRIVER),y)
CHIP_CSRCS += max326_rtc_lowerhalf.c
endif
endif

ifeq ($(CONFIG_MAX326XX_WDOG),y)
ifeq ($(CONFIG_ARCH_FAMILY_MAX32660),y)
CHIP_CSRCS += max32660_wdt.c
endif
endif

ifeq ($(CONFIG_MAX326XX_RNG),y)
CHIP_CSRCS += max326_rng.c
endif

ifeq ($(CONFIG_MAX326XX_HAVE_UART),y)
ifeq ($(CONFIG_ARCH_FAMILY_MAX32660),y)
CHIP_CSRCS += max32660_serial.c
endif
endif

ifeq ($(CONFIG_MAX326XX_HAVE_I2CM),y)
ifeq ($(CONFIG_ARCH_FAMILY_MAX32660),y)
CHIP_CSRCS += max32660_i2cm.c
endif
endif

ifeq ($(CONFIG_MAX326XX_HAVE_SPIM),y)
ifeq ($(CONFIG_ARCH_FAMILY_MAX32660),y)
ifeq ($(CONFIG_MAX326XX_SPIM0),y)
CHIP_CSRCS += max32660_spim.c
endif
ifeq ($(CONFIG_MAX326XX_SPIM1),y)
CHIP_CSRCS += max32660_spimssm.c
endif
endif
endif

# Paths to source files

VPATH += chip/common

ifeq ($(CONFIG_ARCH_FAMILY_MAX32620),y)
VPATH += chip/max32620_30
else ifeq ($(CONFIG_ARCH_FAMILY_MAX32630),y)
VPATH += chip/max32620_30
endif
ifeq ($(CONFIG_ARCH_FAMILY_MAX32660),y)
VPATH += chip/max32660
endif
