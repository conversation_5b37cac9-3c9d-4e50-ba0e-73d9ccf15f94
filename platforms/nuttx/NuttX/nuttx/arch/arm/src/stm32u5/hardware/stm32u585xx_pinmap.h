/****************************************************************************
 * arch/arm/src/stm32u5/hardware/stm32u585xx_pinmap.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_STM32U5_HARDWARE_STM32U585XX_PINMAP_H
#define __ARCH_ARM_SRC_STM32U5_HARDWARE_STM32U585XX_PINMAP_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Alternate Pin Functions.  All members of the STM32U585xx family share the
 * same pin multiplexing (although they may differ in the pins physically
 * available).  See DB3734, Table 28 "Alternate Function AF0
 * to AF7" and Table 29 "Alternate Function AF8 to AF15".
 *
 * Alternative pin selections are provided with a numeric suffix like _1, _2,
 * etc.  Drivers, however, will use the pin selection without the numeric
 * suffix.  Additional definitions are required in the board.h file.  For
 * example, if FDCAN1_RX connects via PA11 on some board, then the following
 * definitions should appear inthe board.h header file for that board:
 *
 * #define GPIO_FDCAN1_RX GPIO_FDCAN1_RX_1
 *
 * The driver will then automatically configure PA11 as the FDCAN1 RX pin.
 *
 * Note that this header file does not specify pull-up or -down resistors
 * (GPIO_FLOAT, GPIO_PULLUP, GPIO_PULLDOWN), output frequency
 * (GPIO_SPEED_2MHZ, GPIO_SPEED_25MHZ, GPIO_SPEED_50MHZ, GPIO_SPEED_100MHZ),
 * or whether the pin is to be operated in push-pull or open-drain mode
 * (GPIO_PUSHPULL, GPIO_OPENDRAIN).  As all of this is application specific,
 * it should be specified in corresponding board.h file.
 */

/* ADC1 - Analog-to-digital converter */

#define GPIO_ADC1_IN1_1         (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN0)
#define GPIO_ADC1_IN2_1         (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN1)
#define GPIO_ADC1_IN3_1         (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN2)
#define GPIO_ADC1_IN4_1         (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN3)
#define GPIO_ADC1_IN5_1         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN0)
#define GPIO_ADC1_IN6_1         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN1)
#define GPIO_ADC1_IN7_1         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN2)
#define GPIO_ADC1_IN8_1         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN3)
#define GPIO_ADC1_IN9_1         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN4)
#define GPIO_ADC1_IN10_1        (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN5)
#define GPIO_ADC1_IN11_1        (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN6)
#define GPIO_ADC1_IN12_1        (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN7)
#define GPIO_ADC1_IN13_1        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN4)
#define GPIO_ADC1_IN14_1        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN5)
#define GPIO_ADC1_IN15_1        (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN0)
#define GPIO_ADC1_IN16_1        (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN1)
#define GPIO_ADC1_IN17_1        (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN1)

/* ADC4 - Analog-to-digital converter */

#define GPIO_ADC4_IN1_1         (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN0)
#define GPIO_ADC4_IN2_1         (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN1)
#define GPIO_ADC4_IN3_1         (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN2)
#define GPIO_ADC4_IN4_1         (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN3)
#define GPIO_ADC4_IN5_1         (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN14)
#define GPIO_ADC4_IN6_1         (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN15)
#define GPIO_ADC4_IN7_1         (GPIO_ANALOG|GPIO_PORTG|GPIO_PIN0)
#define GPIO_ADC4_IN8_1         (GPIO_ANALOG|GPIO_PORTG|GPIO_PIN1)
#define GPIO_ADC4_IN9_1         (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN4)
#define GPIO_ADC4_IN10_1        (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN5)
#define GPIO_ADC4_IN11_1        (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN6)
#define GPIO_ADC4_IN15_1        (GPIO_ANALOG|GPIO_PORTD|GPIO_PIN11)
#define GPIO_ADC4_IN16_1        (GPIO_ANALOG|GPIO_PORTD|GPIO_PIN12)
#define GPIO_ADC4_IN17_1        (GPIO_ANALOG|GPIO_PORTD|GPIO_PIN13)
#define GPIO_ADC4_IN18_1        (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN0)
#define GPIO_ADC4_IN19_1        (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN1)
#define GPIO_ADC4_IN20_1        (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN7)
#define GPIO_ADC4_IN22_1        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN4)
#define GPIO_ADC4_IN23_1        (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN5)

/* ADF1 - Audio digital filter */

#define GPIO_ADF1_CCK0_1        (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN3)
#define GPIO_ADF1_CCK0_2        (GPIO_ALT|GPIO_AF3|GPIO_PORTE|GPIO_PIN9)
#define GPIO_ADF1_CCK1_1        (GPIO_ALT|GPIO_AF3|GPIO_PORTC|GPIO_PIN10)
#define GPIO_ADF1_SDI0_1        (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN4)
#define GPIO_ADF1_SDI0_2        (GPIO_ALT|GPIO_AF3|GPIO_PORTC|GPIO_PIN11)
#define GPIO_ADF1_SDI0_3        (GPIO_ALT|GPIO_AF3|GPIO_PORTE|GPIO_PIN10)

/* COMP1 - Comparator */

#define GPIO_COMP1_OUT_1        (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN0)
#define GPIO_COMP1_OUT_2        (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN10)

/* COMP2 - Comparator */

#define GPIO_COMP2_OUT_1        (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN5)
#define GPIO_COMP2_OUT_2        (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN11)

/* CRS - Clock recovery system */

#define GPIO_CRS_SYNC_1         (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN10)
#define GPIO_CRS_SYNC_2         (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN3)

/* DBG - Debug support */

#define GPIO_JTCK_SWCLK_1       (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN14)
#define GPIO_JTDI_1             (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN15)
#define GPIO_JTDO_TRACESWO_1    (GPIO_ALT|GPIO_AF0|GPIO_PORTB|GPIO_PIN13)
#define GPIO_JTMS_SWDIO_1       (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN13)
#define GPIO_NJTRST_1           (GPIO_ALT|GPIO_AF0|GPIO_PORTB|GPIO_PIN4)
#define GPIO_TRACECLK_1         (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN2)
#define GPIO_TRACED0_1          (GPIO_ALT|GPIO_AF0|GPIO_PORTC|GPIO_PIN9)
#define GPIO_TRACED0_2          (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN3)
#define GPIO_TRACED1_1          (GPIO_ALT|GPIO_AF0|GPIO_PORTC|GPIO_PIN10)
#define GPIO_TRACED1_2          (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN4)
#define GPIO_TRACED2_1          (GPIO_ALT|GPIO_AF0|GPIO_PORTD|GPIO_PIN2)
#define GPIO_TRACED2_2          (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN5)
#define GPIO_TRACED3_1          (GPIO_ALT|GPIO_AF0|GPIO_PORTC|GPIO_PIN12)
#define GPIO_TRACED3_2          (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN6)

/* DCMI - Digital camera interface */

#define GPIO_DCMI_D0_1          (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN10)
#define GPIO_DCMI_D0_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN6)
#define GPIO_DCMI_D0_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN9)
#define GPIO_DCMI_D1_1          (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN9)
#define GPIO_DCMI_D1_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN7)
#define GPIO_DCMI_D1_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN10)
#define GPIO_DCMI_D2_1          (GPIO_ALT|GPIO_AF4|GPIO_PORTC|GPIO_PIN11)
#define GPIO_DCMI_D2_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN8)
#define GPIO_DCMI_D2_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN0)
#define GPIO_DCMI_D2_4          (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN11)
#define GPIO_DCMI_D3_1          (GPIO_ALT|GPIO_AF4|GPIO_PORTC|GPIO_PIN9)
#define GPIO_DCMI_D3_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN1)
#define GPIO_DCMI_D3_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN12)
#define GPIO_DCMI_D4_1          (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN11)
#define GPIO_DCMI_D4_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN4)
#define GPIO_DCMI_D4_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN14)
#define GPIO_DCMI_D5_1          (GPIO_ALT|GPIO_AF4|GPIO_PORTD|GPIO_PIN3)
#define GPIO_DCMI_D5_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN6)
#define GPIO_DCMI_D5_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN4)
#define GPIO_DCMI_D6_1          (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN8)
#define GPIO_DCMI_D6_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN5)
#define GPIO_DCMI_D6_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN6)
#define GPIO_DCMI_D7_1          (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN9)
#define GPIO_DCMI_D7_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN6)
#define GPIO_DCMI_D7_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN7)
#define GPIO_DCMI_D8_1          (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN10)
#define GPIO_DCMI_D8_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN6)
#define GPIO_DCMI_D8_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN1)
#define GPIO_DCMI_D9_1          (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN12)
#define GPIO_DCMI_D9_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN7)
#define GPIO_DCMI_D9_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN2)
#define GPIO_DCMI_D10_1         (GPIO_ALT|GPIO_AF4|GPIO_PORTD|GPIO_PIN6)
#define GPIO_DCMI_D10_2         (GPIO_ALT|GPIO_AF4|GPIO_PORTD|GPIO_PIN6)
#define GPIO_DCMI_D10_3         (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN3)
#define GPIO_DCMI_D11_1         (GPIO_ALT|GPIO_AF10|GPIO_PORTD|GPIO_PIN2)
#define GPIO_DCMI_D11_2         (GPIO_ALT|GPIO_AF10|GPIO_PORTF|GPIO_PIN10)
#define GPIO_DCMI_D11_3         (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN15)
#define GPIO_DCMI_D12_1         (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN5)
#define GPIO_DCMI_D12_2         (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN4)
#define GPIO_DCMI_D12_3         (GPIO_ALT|GPIO_AF10|GPIO_PORTF|GPIO_PIN11)
#define GPIO_DCMI_D13_1         (GPIO_ALT|GPIO_AF10|GPIO_PORTG|GPIO_PIN5)
#define GPIO_DCMI_D13_2         (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN0)
#define GPIO_DCMI_HSYNC_1       (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN4)
#define GPIO_DCMI_HSYNC_2       (GPIO_ALT|GPIO_AF10|GPIO_PORTD|GPIO_PIN8)
#define GPIO_DCMI_HSYNC_3       (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN8)
#define GPIO_DCMI_PIXCLK_1      (GPIO_ALT|GPIO_AF4|GPIO_PORTA|GPIO_PIN6)
#define GPIO_DCMI_PIXCLK_2      (GPIO_ALT|GPIO_AF10|GPIO_PORTD|GPIO_PIN9)
#define GPIO_DCMI_PIXCLK_3      (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN5)
#define GPIO_DCMI_VSYNC_1       (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN7)
#define GPIO_DCMI_VSYNC_2       (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN5)

/* EVENTOUT - Armv8-M Send Event instruction (SEV) */

#define GPIO_PA0_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN0)
#define GPIO_PA1_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN1)
#define GPIO_PA2_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN2)
#define GPIO_PA3_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN3)
#define GPIO_PA4_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN4)
#define GPIO_PA5_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN5)
#define GPIO_PA6_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN6)
#define GPIO_PA7_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN7)
#define GPIO_PA8_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN8)
#define GPIO_PA9_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN9)
#define GPIO_PA10_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN10)
#define GPIO_PA11_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN11)
#define GPIO_PA12_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN12)
#define GPIO_PA13_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN13)
#define GPIO_PA14_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN14)
#define GPIO_PA15_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN15)
#define GPIO_PB0_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN0)
#define GPIO_PB1_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN1)
#define GPIO_PB2_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN2)
#define GPIO_PB3_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN3)
#define GPIO_PB4_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN4)
#define GPIO_PB5_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN5)
#define GPIO_PB6_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN6)
#define GPIO_PB7_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN7)
#define GPIO_PB8_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN8)
#define GPIO_PB9_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN9)
#define GPIO_PB10_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN10)
#define GPIO_PB11_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN11)
#define GPIO_PB12_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN12)
#define GPIO_PB13_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN13)
#define GPIO_PB14_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN14)
#define GPIO_PB15_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN15)
#define GPIO_PC0_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN0)
#define GPIO_PC1_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN1)
#define GPIO_PC2_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN2)
#define GPIO_PC3_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN3)
#define GPIO_PC4_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN4)
#define GPIO_PC5_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN5)
#define GPIO_PC6_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN6)
#define GPIO_PC7_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN7)
#define GPIO_PC8_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN8)
#define GPIO_PC9_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN9)
#define GPIO_PC10_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN10)
#define GPIO_PC11_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN11)
#define GPIO_PC12_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN12)
#define GPIO_PC13_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN13)
#define GPIO_PC14_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN14)
#define GPIO_PC15_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN15)
#define GPIO_PD0_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN0)
#define GPIO_PD1_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN1)
#define GPIO_PD2_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN2)
#define GPIO_PD3_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN3)
#define GPIO_PD4_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN4)
#define GPIO_PD5_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN5)
#define GPIO_PD6_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN6)
#define GPIO_PD7_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN7)
#define GPIO_PD8_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN8)
#define GPIO_PD9_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN9)
#define GPIO_PD10_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN10)
#define GPIO_PD11_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN11)
#define GPIO_PD12_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN12)
#define GPIO_PD13_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN13)
#define GPIO_PD14_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN14)
#define GPIO_PD15_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTD|GPIO_PIN15)
#define GPIO_PE0_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN0)
#define GPIO_PE1_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN1)
#define GPIO_PE2_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN2)
#define GPIO_PE3_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN3)
#define GPIO_PE4_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN4)
#define GPIO_PE5_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN5)
#define GPIO_PE6_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN6)
#define GPIO_PE7_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN7)
#define GPIO_PE8_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN8)
#define GPIO_PE9_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN9)
#define GPIO_PE10_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN10)
#define GPIO_PE11_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN11)
#define GPIO_PE12_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN12)
#define GPIO_PE13_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN13)
#define GPIO_PE14_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN14)
#define GPIO_PE15_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTE|GPIO_PIN15)
#define GPIO_PF0_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN0)
#define GPIO_PF1_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN1)
#define GPIO_PF2_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN2)
#define GPIO_PF3_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN3)
#define GPIO_PF4_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN4)
#define GPIO_PF5_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN5)
#define GPIO_PF6_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN6)
#define GPIO_PF7_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN7)
#define GPIO_PF8_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN8)
#define GPIO_PF9_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN9)
#define GPIO_PF10_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN10)
#define GPIO_PF11_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN11)
#define GPIO_PF12_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN12)
#define GPIO_PF13_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN13)
#define GPIO_PF14_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN14)
#define GPIO_PF15_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTF|GPIO_PIN15)
#define GPIO_PG0_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN0)
#define GPIO_PG1_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN1)
#define GPIO_PG2_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN2)
#define GPIO_PG3_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN3)
#define GPIO_PG4_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN4)
#define GPIO_PG5_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN5)
#define GPIO_PG6_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN6)
#define GPIO_PG7_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN7)
#define GPIO_PG8_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN8)
#define GPIO_PG9_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN9)
#define GPIO_PG10_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN10)
#define GPIO_PG11_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN11)
#define GPIO_PG12_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN12)
#define GPIO_PG13_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN13)
#define GPIO_PG14_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN14)
#define GPIO_PG15_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN15)
#define GPIO_PG15_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTG|GPIO_PIN15)
#define GPIO_PH0_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN0)
#define GPIO_PH1_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN1)
#define GPIO_PH2_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN2)
#define GPIO_PH3_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN3)
#define GPIO_PH4_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN4)
#define GPIO_PH5_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN5)
#define GPIO_PH6_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN6)
#define GPIO_PH7_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN7)
#define GPIO_PH8_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN8)
#define GPIO_PH9_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN9)
#define GPIO_PH10_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN10)
#define GPIO_PH11_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN11)
#define GPIO_PH12_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN12)
#define GPIO_PH13_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN13)
#define GPIO_PH14_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN14)
#define GPIO_PH15_EVENTOUT_1    (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN15)
#define GPIO_PI0_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN0)
#define GPIO_PI1_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN1)
#define GPIO_PI2_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN2)
#define GPIO_PI3_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN3)
#define GPIO_PI4_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN4)
#define GPIO_PI5_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN5)
#define GPIO_PI6_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN6)
#define GPIO_PI7_EVENTOUT_1     (GPIO_ALT|GPIO_AF15|GPIO_PORTI|GPIO_PIN7)

/* FDCAN1 - FD controller area network */

#define GPIO_FDCAN1_RX_1        (GPIO_ALT|GPIO_AF9|GPIO_PORTA|GPIO_PIN11)
#define GPIO_FDCAN1_RX_2        (GPIO_ALT|GPIO_AF9|GPIO_PORTB|GPIO_PIN8)
#define GPIO_FDCAN1_RX_3        (GPIO_ALT|GPIO_AF9|GPIO_PORTD|GPIO_PIN0)
#define GPIO_FDCAN1_RX_4        (GPIO_ALT|GPIO_AF9|GPIO_PORTF|GPIO_PIN7)
#define GPIO_FDCAN1_RX_5        (GPIO_ALT|GPIO_AF9|GPIO_PORTH|GPIO_PIN14)
#define GPIO_FDCAN1_TX_1        (GPIO_ALT|GPIO_AF9|GPIO_PORTA|GPIO_PIN12)
#define GPIO_FDCAN1_TX_2        (GPIO_ALT|GPIO_AF9|GPIO_PORTB|GPIO_PIN9)
#define GPIO_FDCAN1_TX_3        (GPIO_ALT|GPIO_AF9|GPIO_PORTD|GPIO_PIN1)
#define GPIO_FDCAN1_TX_4        (GPIO_ALT|GPIO_AF9|GPIO_PORTF|GPIO_PIN8)
#define GPIO_FDCAN1_TX_5        (GPIO_ALT|GPIO_AF9|GPIO_PORTH|GPIO_PIN13)

/* FMC - Flexible static memory controller */

#define GPIO_FMC_A0_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN0)
#define GPIO_FMC_A1_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN1)
#define GPIO_FMC_A2_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN2)
#define GPIO_FMC_A3_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN3)
#define GPIO_FMC_A4_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN4)
#define GPIO_FMC_A5_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN5)
#define GPIO_FMC_A6_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN12)
#define GPIO_FMC_A7_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN13)
#define GPIO_FMC_A8_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN14)
#define GPIO_FMC_A9_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN15)
#define GPIO_FMC_A10_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN0)
#define GPIO_FMC_A11_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN1)
#define GPIO_FMC_A12_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN2)
#define GPIO_FMC_A13_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN3)
#define GPIO_FMC_A14_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN4)
#define GPIO_FMC_A15_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN5)
#define GPIO_FMC_A16_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN11)
#define GPIO_FMC_A17_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN12)
#define GPIO_FMC_A18_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN13)
#define GPIO_FMC_A19_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN3)
#define GPIO_FMC_A20_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN4)
#define GPIO_FMC_A21_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN5)
#define GPIO_FMC_A22_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN6)
#define GPIO_FMC_A23_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN2)
#define GPIO_FMC_A24_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN13)
#define GPIO_FMC_A25_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN14)
#define GPIO_FMC_CLK_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN3)
#define GPIO_FMC_D0_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN14)
#define GPIO_FMC_D1_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN15)
#define GPIO_FMC_D2_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN0)
#define GPIO_FMC_D3_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN1)
#define GPIO_FMC_D4_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN7)
#define GPIO_FMC_D5_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN8)
#define GPIO_FMC_D6_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN9)
#define GPIO_FMC_D7_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN10)
#define GPIO_FMC_D8_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN11)
#define GPIO_FMC_D9_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN12)
#define GPIO_FMC_D10_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN13)
#define GPIO_FMC_D11_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN14)
#define GPIO_FMC_D12_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN15)
#define GPIO_FMC_D13_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN8)
#define GPIO_FMC_D14_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN9)
#define GPIO_FMC_D15_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN10)
#define GPIO_FMC_INT_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN7)
#define GPIO_FMC_NE1_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN7)
#define GPIO_FMC_NE2_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN9)
#define GPIO_FMC_NE3_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN10)
#define GPIO_FMC_NE4_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN12)
#define GPIO_FMC_NBL0_1         (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN0)
#define GPIO_FMC_NBL1_1         (GPIO_ALT|GPIO_AF11|GPIO_PORTB|GPIO_PIN15)
#define GPIO_FMC_NBL1_2         (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN1)
#define GPIO_FMC_NL_1           (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN7)
#define GPIO_FMC_NOE_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN4)
#define GPIO_FMC_NCE_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN7)
#define GPIO_FMC_NCE_2          (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN9)
#define GPIO_FMC_NWAIT_1        (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN6)
#define GPIO_FMC_NWE_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN5)

/* I2C1 - Inter-integrated circuit interface */

#define GPIO_I2C1_SCL_1         (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN6)
#define GPIO_I2C1_SCL_2         (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN8)
#define GPIO_I2C1_SCL_3         (GPIO_ALT|GPIO_AF4|GPIO_PORTG|GPIO_PIN14)
#define GPIO_I2C1_SDA_1         (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN3)
#define GPIO_I2C1_SDA_2         (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN7)
#define GPIO_I2C1_SDA_3         (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN9)
#define GPIO_I2C1_SDA_4         (GPIO_ALT|GPIO_AF4|GPIO_PORTG|GPIO_PIN13)
#define GPIO_I2C1_SMBA_1        (GPIO_ALT|GPIO_AF4|GPIO_PORTA|GPIO_PIN1)
#define GPIO_I2C1_SMBA_2        (GPIO_ALT|GPIO_AF4|GPIO_PORTA|GPIO_PIN14)
#define GPIO_I2C1_SMBA_3        (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN5)
#define GPIO_I2C1_SMBA_4        (GPIO_ALT|GPIO_AF4|GPIO_PORTG|GPIO_PIN15)

/* I2C2 - Inter-integrated circuit interface */

#define GPIO_I2C2_SCL_1         (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN10)
#define GPIO_I2C2_SCL_2         (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN13)
#define GPIO_I2C2_SCL_3         (GPIO_ALT|GPIO_AF4|GPIO_PORTF|GPIO_PIN1)
#define GPIO_I2C2_SCL_4         (GPIO_ALT|GPIO_AF4|GPIO_PORTH|GPIO_PIN4)
#define GPIO_I2C2_SDA_1         (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN11)
#define GPIO_I2C2_SDA_2         (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN14)
#define GPIO_I2C2_SDA_3         (GPIO_ALT|GPIO_AF4|GPIO_PORTF|GPIO_PIN0)
#define GPIO_I2C2_SDA_4         (GPIO_ALT|GPIO_AF4|GPIO_PORTH|GPIO_PIN5)
#define GPIO_I2C2_SMBA_1        (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN12)
#define GPIO_I2C2_SMBA_2        (GPIO_ALT|GPIO_AF4|GPIO_PORTF|GPIO_PIN2)
#define GPIO_I2C2_SMBA_3        (GPIO_ALT|GPIO_AF4|GPIO_PORTH|GPIO_PIN6)

/* I2C3 - Inter-integrated circuit interface */

#define GPIO_I2C3_SCL_1         (GPIO_ALT|GPIO_AF4|GPIO_PORTA|GPIO_PIN7)
#define GPIO_I2C3_SCL_2         (GPIO_ALT|GPIO_AF4|GPIO_PORTC|GPIO_PIN0)
#define GPIO_I2C3_SCL_3         (GPIO_ALT|GPIO_AF4|GPIO_PORTG|GPIO_PIN7)
#define GPIO_I2C3_SCL_4         (GPIO_ALT|GPIO_AF4|GPIO_PORTH|GPIO_PIN7)
#define GPIO_I2C3_SDA_1         (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN4)
#define GPIO_I2C3_SDA_2         (GPIO_ALT|GPIO_AF4|GPIO_PORTC|GPIO_PIN1)
#define GPIO_I2C3_SDA_3         (GPIO_ALT|GPIO_AF4|GPIO_PORTG|GPIO_PIN8)
#define GPIO_I2C3_SDA_4         (GPIO_ALT|GPIO_AF4|GPIO_PORTH|GPIO_PIN8)
#define GPIO_I2C3_SMBA_1        (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN2)
#define GPIO_I2C3_SMBA_2        (GPIO_ALT|GPIO_AF4|GPIO_PORTG|GPIO_PIN6)
#define GPIO_I2C3_SMBA_3        (GPIO_ALT|GPIO_AF4|GPIO_PORTH|GPIO_PIN9)

/* I2C4 - Inter-integrated circuit interface */

#define GPIO_I2C4_SCL_1         (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN6)
#define GPIO_I2C4_SCL_2         (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN10)
#define GPIO_I2C4_SCL_3         (GPIO_ALT|GPIO_AF4|GPIO_PORTD|GPIO_PIN12)
#define GPIO_I2C4_SCL_4         (GPIO_ALT|GPIO_AF4|GPIO_PORTF|GPIO_PIN14)
#define GPIO_I2C4_SDA_1         (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN7)
#define GPIO_I2C4_SDA_2         (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN11)
#define GPIO_I2C4_SDA_3         (GPIO_ALT|GPIO_AF4|GPIO_PORTD|GPIO_PIN13)
#define GPIO_I2C4_SDA_4         (GPIO_ALT|GPIO_AF4|GPIO_PORTF|GPIO_PIN15)
#define GPIO_I2C4_SMBA_1        (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN14)
#define GPIO_I2C4_SMBA_2        (GPIO_ALT|GPIO_AF4|GPIO_PORTD|GPIO_PIN11)
#define GPIO_I2C4_SMBA_3        (GPIO_ALT|GPIO_AF4|GPIO_PORTF|GPIO_PIN13)

/* LPGPIO1 - Low-power general-purpose I/Os */

#define GPIO_LPGPIO1_P0_1       (GPIO_ALT|GPIO_AF11|GPIO_PORTA|GPIO_PIN1)
#define GPIO_LPGPIO1_P1_1       (GPIO_ALT|GPIO_AF11|GPIO_PORTA|GPIO_PIN3)
#define GPIO_LPGPIO1_P2_1       (GPIO_ALT|GPIO_AF11|GPIO_PORTA|GPIO_PIN6)
#define GPIO_LPGPIO1_P3_1       (GPIO_ALT|GPIO_AF11|GPIO_PORTB|GPIO_PIN1)
#define GPIO_LPGPIO1_P4_1       (GPIO_ALT|GPIO_AF11|GPIO_PORTB|GPIO_PIN10)
#define GPIO_LPGPIO1_P5_1       (GPIO_ALT|GPIO_AF11|GPIO_PORTC|GPIO_PIN2)
#define GPIO_LPGPIO1_P6_1       (GPIO_ALT|GPIO_AF11|GPIO_PORTD|GPIO_PIN13)
#define GPIO_LPGPIO1_P7_1       (GPIO_ALT|GPIO_AF11|GPIO_PORTD|GPIO_PIN2)
#define GPIO_LPGPIO1_P8_1       (GPIO_ALT|GPIO_AF11|GPIO_PORTC|GPIO_PIN10)
#define GPIO_LPGPIO1_P9_1       (GPIO_ALT|GPIO_AF11|GPIO_PORTB|GPIO_PIN0)
#define GPIO_LPGPIO1_P10_1      (GPIO_ALT|GPIO_AF11|GPIO_PORTC|GPIO_PIN12)
#define GPIO_LPGPIO1_P11_1      (GPIO_ALT|GPIO_AF11|GPIO_PORTB|GPIO_PIN3)
#define GPIO_LPGPIO1_P12_1      (GPIO_ALT|GPIO_AF11|GPIO_PORTB|GPIO_PIN4)
#define GPIO_LPGPIO1_P13_1      (GPIO_ALT|GPIO_AF11|GPIO_PORTE|GPIO_PIN0)
#define GPIO_LPGPIO1_P14_1      (GPIO_ALT|GPIO_AF11|GPIO_PORTE|GPIO_PIN1)
#define GPIO_LPGPIO1_P15_1      (GPIO_ALT|GPIO_AF11|GPIO_PORTE|GPIO_PIN2)

/* LPTIM1 - Low-power timer */

#define GPIO_LPTIM1_CH1_1       (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN14)
#define GPIO_LPTIM1_CH1_2       (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN2)
#define GPIO_LPTIM1_CH1_3       (GPIO_ALT|GPIO_AF2|GPIO_PORTB|GPIO_PIN3)
#define GPIO_LPTIM1_CH1_4       (GPIO_ALT|GPIO_AF1|GPIO_PORTC|GPIO_PIN1)
#define GPIO_LPTIM1_CH1_5       (GPIO_ALT|GPIO_AF1|GPIO_PORTG|GPIO_PIN15)
#define GPIO_LPTIM1_CH2_1       (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN1)
#define GPIO_LPTIM1_CH2_2       (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN4)
#define GPIO_LPTIM1_CH2_3       (GPIO_ALT|GPIO_AF1|GPIO_PORTG|GPIO_PIN14)
#define GPIO_LPTIM1_ETR_1       (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN6)
#define GPIO_LPTIM1_ETR_2       (GPIO_ALT|GPIO_AF1|GPIO_PORTC|GPIO_PIN3)
#define GPIO_LPTIM1_ETR_3       (GPIO_ALT|GPIO_AF1|GPIO_PORTG|GPIO_PIN12)
#define GPIO_LPTIM1_IN1_1       (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN5)
#define GPIO_LPTIM1_IN1_2       (GPIO_ALT|GPIO_AF1|GPIO_PORTC|GPIO_PIN0)
#define GPIO_LPTIM1_IN1_3       (GPIO_ALT|GPIO_AF1|GPIO_PORTG|GPIO_PIN10)
#define GPIO_LPTIM1_IN2_1       (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN7)
#define GPIO_LPTIM1_IN2_2       (GPIO_ALT|GPIO_AF1|GPIO_PORTC|GPIO_PIN2)
#define GPIO_LPTIM1_IN2_3       (GPIO_ALT|GPIO_AF1|GPIO_PORTG|GPIO_PIN11)

/* LPTIM2 - Low-power timer */

#define GPIO_LPTIM2_CH1_1       (GPIO_ALT|GPIO_AF14|GPIO_PORTA|GPIO_PIN10)
#define GPIO_LPTIM2_CH1_2       (GPIO_ALT|GPIO_AF14|GPIO_PORTA|GPIO_PIN8)
#define GPIO_LPTIM2_CH1_3       (GPIO_ALT|GPIO_AF14|GPIO_PORTD|GPIO_PIN13)
#define GPIO_LPTIM2_CH2_1       (GPIO_ALT|GPIO_AF2|GPIO_PORTD|GPIO_PIN10)
#define GPIO_LPTIM2_CH2_2       (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN6)
#define GPIO_LPTIM2_CH2_3       (GPIO_ALT|GPIO_AF14|GPIO_PORTC|GPIO_PIN7)
#define GPIO_LPTIM2_ETR_1       (GPIO_ALT|GPIO_AF14|GPIO_PORTA|GPIO_PIN5)
#define GPIO_LPTIM2_ETR_2       (GPIO_ALT|GPIO_AF14|GPIO_PORTC|GPIO_PIN3)
#define GPIO_LPTIM2_ETR_3       (GPIO_ALT|GPIO_AF14|GPIO_PORTD|GPIO_PIN11)
#define GPIO_LPTIM2_IN1_1       (GPIO_ALT|GPIO_AF14|GPIO_PORTB|GPIO_PIN1)
#define GPIO_LPTIM2_IN1_2       (GPIO_ALT|GPIO_AF14|GPIO_PORTC|GPIO_PIN0)
#define GPIO_LPTIM2_IN1_3       (GPIO_ALT|GPIO_AF14|GPIO_PORTD|GPIO_PIN12)
#define GPIO_LPTIM2_IN2_1       (GPIO_ALT|GPIO_AF2|GPIO_PORTA|GPIO_PIN10)
#define GPIO_LPTIM2_IN2_2       (GPIO_ALT|GPIO_AF2|GPIO_PORTB|GPIO_PIN15)
#define GPIO_LPTIM2_IN2_3       (GPIO_ALT|GPIO_AF2|GPIO_PORTD|GPIO_PIN9)

/* LPTIM3 - Low-power timer */

#define GPIO_LPTIM3_CH1_1       (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN0)
#define GPIO_LPTIM3_CH1_2       (GPIO_ALT|GPIO_AF2|GPIO_PORTB|GPIO_PIN10)
#define GPIO_LPTIM3_CH1_3       (GPIO_ALT|GPIO_AF2|GPIO_PORTC|GPIO_PIN3)
#define GPIO_LPTIM3_CH1_4       (GPIO_ALT|GPIO_AF2|GPIO_PORTF|GPIO_PIN5)
#define GPIO_LPTIM3_CH1_5       (GPIO_ALT|GPIO_AF14|GPIO_PORTC|GPIO_PIN8)
#define GPIO_LPTIM3_CH1_6       (GPIO_ALT|GPIO_AF14|GPIO_PORTD|GPIO_PIN14)
#define GPIO_LPTIM3_CH2_1       (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN1)
#define GPIO_LPTIM3_CH2_2       (GPIO_ALT|GPIO_AF2|GPIO_PORTF|GPIO_PIN2)
#define GPIO_LPTIM3_CH2_3       (GPIO_ALT|GPIO_AF14|GPIO_PORTC|GPIO_PIN9)
#define GPIO_LPTIM3_CH2_4       (GPIO_ALT|GPIO_AF14|GPIO_PORTD|GPIO_PIN15)
#define GPIO_LPTIM3_ETR_1       (GPIO_ALT|GPIO_AF2|GPIO_PORTB|GPIO_PIN14)
#define GPIO_LPTIM3_ETR_2       (GPIO_ALT|GPIO_AF2|GPIO_PORTC|GPIO_PIN10)
#define GPIO_LPTIM3_ETR_3       (GPIO_ALT|GPIO_AF2|GPIO_PORTF|GPIO_PIN4)
#define GPIO_LPTIM3_ETR_4       (GPIO_ALT|GPIO_AF14|GPIO_PORTD|GPIO_PIN10)
#define GPIO_LPTIM3_IN1_1       (GPIO_ALT|GPIO_AF2|GPIO_PORTB|GPIO_PIN13)
#define GPIO_LPTIM3_IN1_2       (GPIO_ALT|GPIO_AF2|GPIO_PORTC|GPIO_PIN11)
#define GPIO_LPTIM3_IN1_3       (GPIO_ALT|GPIO_AF2|GPIO_PORTF|GPIO_PIN3)
#define GPIO_LPTIM3_IN1_4       (GPIO_ALT|GPIO_AF14|GPIO_PORTD|GPIO_PIN9)

/* LPUART1 - Low-power universal asynchronous receiver transmitter */

#define GPIO_LPUART1_CTS_1      (GPIO_ALT|GPIO_AF8|GPIO_PORTA|GPIO_PIN6)
#define GPIO_LPUART1_CTS_2      (GPIO_ALT|GPIO_AF8|GPIO_PORTB|GPIO_PIN13)
#define GPIO_LPUART1_CTS_3      (GPIO_ALT|GPIO_AF8|GPIO_PORTG|GPIO_PIN5)
#define GPIO_LPUART1_RTS_DE_1   (GPIO_ALT|GPIO_AF8|GPIO_PORTB|GPIO_PIN1)
#define GPIO_LPUART1_RTS_DE_2   (GPIO_ALT|GPIO_AF8|GPIO_PORTB|GPIO_PIN12)
#define GPIO_LPUART1_RTS_DE_3   (GPIO_ALT|GPIO_AF8|GPIO_PORTG|GPIO_PIN6)
#define GPIO_LPUART1_RX_1       (GPIO_ALT|GPIO_AF8|GPIO_PORTA|GPIO_PIN3)
#define GPIO_LPUART1_RX_2       (GPIO_ALT|GPIO_AF8|GPIO_PORTB|GPIO_PIN10)
#define GPIO_LPUART1_RX_3       (GPIO_ALT|GPIO_AF8|GPIO_PORTC|GPIO_PIN0)
#define GPIO_LPUART1_RX_4       (GPIO_ALT|GPIO_AF8|GPIO_PORTG|GPIO_PIN8)
#define GPIO_LPUART1_TX_1       (GPIO_ALT|GPIO_AF8|GPIO_PORTA|GPIO_PIN2)
#define GPIO_LPUART1_TX_2       (GPIO_ALT|GPIO_AF8|GPIO_PORTB|GPIO_PIN11)
#define GPIO_LPUART1_TX_3       (GPIO_ALT|GPIO_AF8|GPIO_PORTC|GPIO_PIN1)
#define GPIO_LPUART1_TX_4       (GPIO_ALT|GPIO_AF8|GPIO_PORTG|GPIO_PIN7)

/* MDF1 - Multi-function digital filter */

#define GPIO_MDF1_CCK0_1        (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN8)
#define GPIO_MDF1_CCK0_2        (GPIO_ALT|GPIO_AF6|GPIO_PORTE|GPIO_PIN9)
#define GPIO_MDF1_CCK0_3        (GPIO_ALT|GPIO_AF6|GPIO_PORTG|GPIO_PIN7)
#define GPIO_MDF1_CCK1_1        (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN2)
#define GPIO_MDF1_CCK1_2        (GPIO_ALT|GPIO_AF6|GPIO_PORTF|GPIO_PIN10)
#define GPIO_MDF1_CKI0_1        (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN2)
#define GPIO_MDF1_CKI0_2        (GPIO_ALT|GPIO_AF6|GPIO_PORTD|GPIO_PIN4)
#define GPIO_MDF1_CKI1_1        (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN13)
#define GPIO_MDF1_CKI1_2        (GPIO_ALT|GPIO_AF6|GPIO_PORTD|GPIO_PIN7)
#define GPIO_MDF1_CKI2_1        (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN15)
#define GPIO_MDF1_CKI2_2        (GPIO_ALT|GPIO_AF6|GPIO_PORTE|GPIO_PIN8)
#define GPIO_MDF1_CKI3_1        (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN6)
#define GPIO_MDF1_CKI3_2        (GPIO_ALT|GPIO_AF6|GPIO_PORTE|GPIO_PIN5)
#define GPIO_MDF1_CKI4_1        (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN1)
#define GPIO_MDF1_CKI4_2        (GPIO_ALT|GPIO_AF6|GPIO_PORTE|GPIO_PIN11)
#define GPIO_MDF1_CKI5_1        (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN7)
#define GPIO_MDF1_CKI5_2        (GPIO_ALT|GPIO_AF6|GPIO_PORTE|GPIO_PIN13)
#define GPIO_MDF1_SDI0_1        (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN1)
#define GPIO_MDF1_SDI0_2        (GPIO_ALT|GPIO_AF6|GPIO_PORTD|GPIO_PIN3)
#define GPIO_MDF1_SDI1_1        (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN12)
#define GPIO_MDF1_SDI1_2        (GPIO_ALT|GPIO_AF6|GPIO_PORTD|GPIO_PIN6)
#define GPIO_MDF1_SDI2_1        (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN14)
#define GPIO_MDF1_SDI2_2        (GPIO_ALT|GPIO_AF6|GPIO_PORTE|GPIO_PIN7)
#define GPIO_MDF1_SDI3_1        (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN7)
#define GPIO_MDF1_SDI3_2        (GPIO_ALT|GPIO_AF6|GPIO_PORTE|GPIO_PIN4)
#define GPIO_MDF1_SDI4_1        (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN0)
#define GPIO_MDF1_SDI4_2        (GPIO_ALT|GPIO_AF6|GPIO_PORTE|GPIO_PIN10)
#define GPIO_MDF1_SDI5_1        (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN6)
#define GPIO_MDF1_SDI5_2        (GPIO_ALT|GPIO_AF6|GPIO_PORTE|GPIO_PIN12)

/* OCTOSPIM_P1 - OCTOSPI I/O manager - Port 1 */

#define GPIO_OCTOSPIM_P1_CLK_1  (GPIO_ALT|GPIO_AF3|GPIO_PORTF|GPIO_PIN10)
#define GPIO_OCTOSPIM_P1_CLK_2  (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN3)
#define GPIO_OCTOSPIM_P1_CLK_3  (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN10)
#define GPIO_OCTOSPIM_P1_CLK_4  (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN10)
#define GPIO_OCTOSPIM_P1_DQS_1  (GPIO_ALT|GPIO_AF3|GPIO_PORTE|GPIO_PIN3)
#define GPIO_OCTOSPIM_P1_DQS_2  (GPIO_ALT|GPIO_AF3|GPIO_PORTG|GPIO_PIN6)
#define GPIO_OCTOSPIM_P1_DQS_3  (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN1)
#define GPIO_OCTOSPIM_P1_DQS_4  (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN2)
#define GPIO_OCTOSPIM_P1_IO0_1  (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN1)
#define GPIO_OCTOSPIM_P1_IO0_2  (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN12)
#define GPIO_OCTOSPIM_P1_IO0_3  (GPIO_ALT|GPIO_AF10|GPIO_PORTF|GPIO_PIN8)
#define GPIO_OCTOSPIM_P1_IO1_1  (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN0)
#define GPIO_OCTOSPIM_P1_IO1_2  (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN13)
#define GPIO_OCTOSPIM_P1_IO1_3  (GPIO_ALT|GPIO_AF10|GPIO_PORTF|GPIO_PIN9)
#define GPIO_OCTOSPIM_P1_IO2_1  (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN7)
#define GPIO_OCTOSPIM_P1_IO2_2  (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN14)
#define GPIO_OCTOSPIM_P1_IO2_3  (GPIO_ALT|GPIO_AF10|GPIO_PORTF|GPIO_PIN7)
#define GPIO_OCTOSPIM_P1_IO3_1  (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN6)
#define GPIO_OCTOSPIM_P1_IO3_2  (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN15)
#define GPIO_OCTOSPIM_P1_IO3_3  (GPIO_ALT|GPIO_AF10|GPIO_PORTF|GPIO_PIN6)
#define GPIO_OCTOSPIM_P1_IO4_1  (GPIO_ALT|GPIO_AF3|GPIO_PORTH|GPIO_PIN2)
#define GPIO_OCTOSPIM_P1_IO4_2  (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN1)
#define GPIO_OCTOSPIM_P1_IO4_3  (GPIO_ALT|GPIO_AF10|GPIO_PORTD|GPIO_PIN4)
#define GPIO_OCTOSPIM_P1_IO5_1  (GPIO_ALT|GPIO_AF3|GPIO_PORTG|GPIO_PIN11)
#define GPIO_OCTOSPIM_P1_IO5_2  (GPIO_ALT|GPIO_AF3|GPIO_PORTI|GPIO_PIN0)
#define GPIO_OCTOSPIM_P1_IO5_3  (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN2)
#define GPIO_OCTOSPIM_P1_IO5_4  (GPIO_ALT|GPIO_AF10|GPIO_PORTD|GPIO_PIN5)
#define GPIO_OCTOSPIM_P1_IO6_1  (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN3)
#define GPIO_OCTOSPIM_P1_IO6_2  (GPIO_ALT|GPIO_AF10|GPIO_PORTD|GPIO_PIN6)
#define GPIO_OCTOSPIM_P1_IO7_1  (GPIO_ALT|GPIO_AF3|GPIO_PORTC|GPIO_PIN0)
#define GPIO_OCTOSPIM_P1_IO7_2  (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN4)
#define GPIO_OCTOSPIM_P1_IO7_3  (GPIO_ALT|GPIO_AF10|GPIO_PORTD|GPIO_PIN7)
#define GPIO_OCTOSPIM_P1_NCLK_1 (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN5)
#define GPIO_OCTOSPIM_P1_NCLK_2 (GPIO_ALT|GPIO_AF3|GPIO_PORTF|GPIO_PIN11)
#define GPIO_OCTOSPIM_P1_NCLK_3 (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN12)
#define GPIO_OCTOSPIM_P1_NCLK_4 (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN9)
#define GPIO_OCTOSPIM_P1_NCS_1  (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN4)
#define GPIO_OCTOSPIM_P1_NCS_2  (GPIO_ALT|GPIO_AF5|GPIO_PORTC|GPIO_PIN11)
#define GPIO_OCTOSPIM_P1_NCS_3  (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN0)
#define GPIO_OCTOSPIM_P1_NCS_4  (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN2)
#define GPIO_OCTOSPIM_P1_NCS_5  (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN11)
#define GPIO_OCTOSPIM_P1_NCS_6  (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN11)

/* OCTOSPIM_P2 - OCTOSPI I/O manager - Port 2 */

#define GPIO_OCTOSPIM_P2_CLK_1  (GPIO_ALT|GPIO_AF5|GPIO_PORTF|GPIO_PIN4)
#define GPIO_OCTOSPIM_P2_CLK_2  (GPIO_ALT|GPIO_AF5|GPIO_PORTH|GPIO_PIN6)
#define GPIO_OCTOSPIM_P2_CLK_3  (GPIO_ALT|GPIO_AF5|GPIO_PORTI|GPIO_PIN6)
#define GPIO_OCTOSPIM_P2_DQS_1  (GPIO_ALT|GPIO_AF5|GPIO_PORTF|GPIO_PIN12)
#define GPIO_OCTOSPIM_P2_DQS_2  (GPIO_ALT|GPIO_AF5|GPIO_PORTG|GPIO_PIN7)
#define GPIO_OCTOSPIM_P2_DQS_3  (GPIO_ALT|GPIO_AF5|GPIO_PORTG|GPIO_PIN15)
#define GPIO_OCTOSPIM_P2_DQS_4  (GPIO_ALT|GPIO_AF5|GPIO_PORTH|GPIO_PIN4)
#define GPIO_OCTOSPIM_P2_IO0_1  (GPIO_ALT|GPIO_AF5|GPIO_PORTF|GPIO_PIN0)
#define GPIO_OCTOSPIM_P2_IO0_2  (GPIO_ALT|GPIO_AF6|GPIO_PORTI|GPIO_PIN3)
#define GPIO_OCTOSPIM_P2_IO1_1  (GPIO_ALT|GPIO_AF5|GPIO_PORTF|GPIO_PIN1)
#define GPIO_OCTOSPIM_P2_IO1_2  (GPIO_ALT|GPIO_AF6|GPIO_PORTI|GPIO_PIN2)
#define GPIO_OCTOSPIM_P2_IO2_1  (GPIO_ALT|GPIO_AF5|GPIO_PORTF|GPIO_PIN2)
#define GPIO_OCTOSPIM_P2_IO2_2  (GPIO_ALT|GPIO_AF6|GPIO_PORTI|GPIO_PIN1)
#define GPIO_OCTOSPIM_P2_IO3_1  (GPIO_ALT|GPIO_AF5|GPIO_PORTF|GPIO_PIN3)
#define GPIO_OCTOSPIM_P2_IO3_2  (GPIO_ALT|GPIO_AF5|GPIO_PORTH|GPIO_PIN8)
#define GPIO_OCTOSPIM_P2_IO4_1  (GPIO_ALT|GPIO_AF5|GPIO_PORTG|GPIO_PIN0)
#define GPIO_OCTOSPIM_P2_IO4_2  (GPIO_ALT|GPIO_AF5|GPIO_PORTH|GPIO_PIN9)
#define GPIO_OCTOSPIM_P2_IO5_1  (GPIO_ALT|GPIO_AF5|GPIO_PORTG|GPIO_PIN1)
#define GPIO_OCTOSPIM_P2_IO5_2  (GPIO_ALT|GPIO_AF5|GPIO_PORTH|GPIO_PIN10)
#define GPIO_OCTOSPIM_P2_IO6_1  (GPIO_ALT|GPIO_AF5|GPIO_PORTG|GPIO_PIN9)
#define GPIO_OCTOSPIM_P2_IO6_2  (GPIO_ALT|GPIO_AF5|GPIO_PORTH|GPIO_PIN11)
#define GPIO_OCTOSPIM_P2_IO6_3  (GPIO_ALT|GPIO_AF5|GPIO_PORTH|GPIO_PIN15)
#define GPIO_OCTOSPIM_P2_IO7_1  (GPIO_ALT|GPIO_AF5|GPIO_PORTG|GPIO_PIN10)
#define GPIO_OCTOSPIM_P2_IO7_2  (GPIO_ALT|GPIO_AF5|GPIO_PORTH|GPIO_PIN12)
#define GPIO_OCTOSPIM_P2_NCLK_1 (GPIO_ALT|GPIO_AF5|GPIO_PORTF|GPIO_PIN5)
#define GPIO_OCTOSPIM_P2_NCLK_2 (GPIO_ALT|GPIO_AF5|GPIO_PORTH|GPIO_PIN7)
#define GPIO_OCTOSPIM_P2_NCLK_3 (GPIO_ALT|GPIO_AF5|GPIO_PORTI|GPIO_PIN7)
#define GPIO_OCTOSPIM_P2_NCS_1  (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN12)
#define GPIO_OCTOSPIM_P2_NCS_2  (GPIO_ALT|GPIO_AF5|GPIO_PORTF|GPIO_PIN6)
#define GPIO_OCTOSPIM_P2_NCS_3  (GPIO_ALT|GPIO_AF5|GPIO_PORTG|GPIO_PIN12)
#define GPIO_OCTOSPIM_P2_NCS_4  (GPIO_ALT|GPIO_AF5|GPIO_PORTI|GPIO_PIN5)
#define GPIO_OCTOSPIM_P2_NCS_5  (GPIO_ALT|GPIO_AF10|GPIO_PORTD|GPIO_PIN3)

/* OTG_FS - USB on-the-go full-speed */

#define GPIO_OTG_FS_DM_1        (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN11)
#define GPIO_OTG_FS_DP_1        (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN12)
#define GPIO_OTG_FS_ID_1        (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN10)
#define GPIO_OTG_FS_NOE_1       (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN13)
#define GPIO_OTG_FS_NOE_2       (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN9)
#define GPIO_OTG_FS_SOF_1       (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN8)
#define GPIO_OTG_FS_SOF_2       (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN14)

/* PSSI - Parallel synchronous slave interface */

#define GPIO_PSSI_D0_1          (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN10)
#define GPIO_PSSI_D0_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN6)
#define GPIO_PSSI_D0_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN9)
#define GPIO_PSSI_D1_1          (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN9)
#define GPIO_PSSI_D1_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN7)
#define GPIO_PSSI_D1_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN10)
#define GPIO_PSSI_D2_1          (GPIO_ALT|GPIO_AF4|GPIO_PORTC|GPIO_PIN11)
#define GPIO_PSSI_D2_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN8)
#define GPIO_PSSI_D2_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN0)
#define GPIO_PSSI_D2_4          (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN11)
#define GPIO_PSSI_D3_1          (GPIO_ALT|GPIO_AF4|GPIO_PORTC|GPIO_PIN9)
#define GPIO_PSSI_D3_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN1)
#define GPIO_PSSI_D3_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN12)
#define GPIO_PSSI_D4_1          (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN11)
#define GPIO_PSSI_D4_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN4)
#define GPIO_PSSI_D4_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN14)
#define GPIO_PSSI_D5_1          (GPIO_ALT|GPIO_AF4|GPIO_PORTD|GPIO_PIN3)
#define GPIO_PSSI_D5_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN6)
#define GPIO_PSSI_D5_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN4)
#define GPIO_PSSI_D6_1          (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN8)
#define GPIO_PSSI_D6_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN5)
#define GPIO_PSSI_D6_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN6)
#define GPIO_PSSI_D7_1          (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN9)
#define GPIO_PSSI_D7_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN6)
#define GPIO_PSSI_D7_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN7)
#define GPIO_PSSI_D8_1          (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN10)
#define GPIO_PSSI_D8_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN6)
#define GPIO_PSSI_D8_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN1)
#define GPIO_PSSI_D9_1          (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN12)
#define GPIO_PSSI_D9_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN7)
#define GPIO_PSSI_D9_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN2)
#define GPIO_PSSI_D10_1         (GPIO_ALT|GPIO_AF4|GPIO_PORTD|GPIO_PIN6)
#define GPIO_PSSI_D10_2         (GPIO_ALT|GPIO_AF4|GPIO_PORTD|GPIO_PIN6)
#define GPIO_PSSI_D10_3         (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN3)
#define GPIO_PSSI_D11_1         (GPIO_ALT|GPIO_AF10|GPIO_PORTD|GPIO_PIN2)
#define GPIO_PSSI_D11_2         (GPIO_ALT|GPIO_AF10|GPIO_PORTF|GPIO_PIN10)
#define GPIO_PSSI_D11_3         (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN15)
#define GPIO_PSSI_D12_1         (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN5)
#define GPIO_PSSI_D12_2         (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN4)
#define GPIO_PSSI_D12_3         (GPIO_ALT|GPIO_AF10|GPIO_PORTF|GPIO_PIN11)
#define GPIO_PSSI_D13_1         (GPIO_ALT|GPIO_AF10|GPIO_PORTG|GPIO_PIN5)
#define GPIO_PSSI_D13_2         (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN0)
#define GPIO_PSSI_D14_1         (GPIO_ALT|GPIO_AF4|GPIO_PORTA|GPIO_PIN5)
#define GPIO_PSSI_D14_2         (GPIO_ALT|GPIO_AF4|GPIO_PORTF|GPIO_PIN8)
#define GPIO_PSSI_D14_3         (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN4)
#define GPIO_PSSI_D15_1         (GPIO_ALT|GPIO_AF4|GPIO_PORTC|GPIO_PIN5)
#define GPIO_PSSI_D15_2         (GPIO_ALT|GPIO_AF4|GPIO_PORTF|GPIO_PIN9)
#define GPIO_PSSI_D15_3         (GPIO_ALT|GPIO_AF4|GPIO_PORTF|GPIO_PIN10)
#define GPIO_PSSI_DE_1          (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN4)
#define GPIO_PSSI_DE_2          (GPIO_ALT|GPIO_AF10|GPIO_PORTD|GPIO_PIN8)
#define GPIO_PSSI_DE_3          (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN8)
#define GPIO_PSSI_PDCK_1        (GPIO_ALT|GPIO_AF4|GPIO_PORTA|GPIO_PIN6)
#define GPIO_PSSI_PDCK_2        (GPIO_ALT|GPIO_AF10|GPIO_PORTD|GPIO_PIN9)
#define GPIO_PSSI_PDCK_3        (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN5)
#define GPIO_PSSI_RDY_1         (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN7)
#define GPIO_PSSI_RDY_2         (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN5)

/* PWR - Power control */

#define GPIO_CDSTOP_1           (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN6)
#define GPIO_CDSTOP_2           (GPIO_ALT|GPIO_AF0|GPIO_PORTC|GPIO_PIN7)
#define GPIO_CSLEEP_1           (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN5)
#define GPIO_CSLEEP_2           (GPIO_ALT|GPIO_AF0|GPIO_PORTC|GPIO_PIN6)
#define GPIO_SRDSTOP_1          (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN7)
#define GPIO_SRDSTOP_2          (GPIO_ALT|GPIO_AF0|GPIO_PORTC|GPIO_PIN8)

/* RTC - Real-time clock */

#define GPIO_RTC_REFIN_1        (GPIO_ALT|GPIO_AF0|GPIO_PORTB|GPIO_PIN15)

/* RCC - Reset and clock control */

#define GPIO_MCO_1              (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN8)

/* SAI1 - Serial audio interface */

#define GPIO_SAI1_CK1_1         (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN3)
#define GPIO_SAI1_CK1_2         (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN8)
#define GPIO_SAI1_CK1_3         (GPIO_ALT|GPIO_AF3|GPIO_PORTE|GPIO_PIN2)
#define GPIO_SAI1_CK1_4         (GPIO_ALT|GPIO_AF3|GPIO_PORTG|GPIO_PIN7)
#define GPIO_SAI1_CK2_1         (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN8)
#define GPIO_SAI1_CK2_2         (GPIO_ALT|GPIO_AF3|GPIO_PORTE|GPIO_PIN5)
#define GPIO_SAI1_D1_1          (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN10)
#define GPIO_SAI1_D1_2          (GPIO_ALT|GPIO_AF3|GPIO_PORTC|GPIO_PIN3)
#define GPIO_SAI1_D1_3          (GPIO_ALT|GPIO_AF3|GPIO_PORTD|GPIO_PIN6)
#define GPIO_SAI1_D1_4          (GPIO_ALT|GPIO_AF3|GPIO_PORTE|GPIO_PIN6)
#define GPIO_SAI1_D2_1          (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN9)
#define GPIO_SAI1_D2_2          (GPIO_ALT|GPIO_AF3|GPIO_PORTE|GPIO_PIN4)
#define GPIO_SAI1_D3_1          (GPIO_ALT|GPIO_AF3|GPIO_PORTC|GPIO_PIN5)
#define GPIO_SAI1_D3_2          (GPIO_ALT|GPIO_AF3|GPIO_PORTD|GPIO_PIN3)
#define GPIO_SAI1_FS_A_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN9)
#define GPIO_SAI1_FS_A_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN9)
#define GPIO_SAI1_FS_A_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTE|GPIO_PIN4)
#define GPIO_SAI1_FS_B_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN4)
#define GPIO_SAI1_FS_B_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN14)
#define GPIO_SAI1_FS_B_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN6)
#define GPIO_SAI1_FS_B_4        (GPIO_ALT|GPIO_AF13|GPIO_PORTE|GPIO_PIN9)
#define GPIO_SAI1_FS_B_5        (GPIO_ALT|GPIO_AF13|GPIO_PORTF|GPIO_PIN9)
#define GPIO_SAI1_MCLK_A_1      (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN3)
#define GPIO_SAI1_MCLK_A_2      (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN8)
#define GPIO_SAI1_MCLK_A_3      (GPIO_ALT|GPIO_AF13|GPIO_PORTE|GPIO_PIN2)
#define GPIO_SAI1_MCLK_A_4      (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN7)
#define GPIO_SAI1_MCLK_B_1      (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN4)
#define GPIO_SAI1_MCLK_B_2      (GPIO_ALT|GPIO_AF13|GPIO_PORTE|GPIO_PIN10)
#define GPIO_SAI1_MCLK_B_3      (GPIO_ALT|GPIO_AF13|GPIO_PORTF|GPIO_PIN7)
#define GPIO_SAI1_SCK_A_1       (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN8)
#define GPIO_SAI1_SCK_A_2       (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN10)
#define GPIO_SAI1_SCK_A_3       (GPIO_ALT|GPIO_AF13|GPIO_PORTE|GPIO_PIN5)
#define GPIO_SAI1_SCK_B_1       (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN3)
#define GPIO_SAI1_SCK_B_2       (GPIO_ALT|GPIO_AF13|GPIO_PORTE|GPIO_PIN8)
#define GPIO_SAI1_SCK_B_3       (GPIO_ALT|GPIO_AF13|GPIO_PORTF|GPIO_PIN8)
#define GPIO_SAI1_SD_A_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN10)
#define GPIO_SAI1_SD_A_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN1)
#define GPIO_SAI1_SD_A_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN3)
#define GPIO_SAI1_SD_A_4        (GPIO_ALT|GPIO_AF13|GPIO_PORTD|GPIO_PIN6)
#define GPIO_SAI1_SD_A_5        (GPIO_ALT|GPIO_AF13|GPIO_PORTE|GPIO_PIN6)
#define GPIO_SAI1_SD_B_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN13)
#define GPIO_SAI1_SD_B_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN5)
#define GPIO_SAI1_SD_B_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTE|GPIO_PIN3)
#define GPIO_SAI1_SD_B_4        (GPIO_ALT|GPIO_AF13|GPIO_PORTE|GPIO_PIN7)
#define GPIO_SAI1_SD_B_5        (GPIO_ALT|GPIO_AF13|GPIO_PORTF|GPIO_PIN6)

/* SAI2 - Serial audio interface */

#define GPIO_SAI2_FS_A_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN12)
#define GPIO_SAI2_FS_A_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN0)
#define GPIO_SAI2_FS_A_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTD|GPIO_PIN12)
#define GPIO_SAI2_FS_A_4        (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN10)
#define GPIO_SAI2_FS_B_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN15)
#define GPIO_SAI2_FS_B_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN3)
#define GPIO_SAI2_MCLK_A_1      (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN14)
#define GPIO_SAI2_MCLK_A_2      (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN6)
#define GPIO_SAI2_MCLK_A_3      (GPIO_ALT|GPIO_AF13|GPIO_PORTD|GPIO_PIN9)
#define GPIO_SAI2_MCLK_A_4      (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN11)
#define GPIO_SAI2_MCLK_B_1      (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN7)
#define GPIO_SAI2_MCLK_B_2      (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN11)
#define GPIO_SAI2_MCLK_B_3      (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN4)
#define GPIO_SAI2_SCK_A_1       (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN13)
#define GPIO_SAI2_SCK_A_2       (GPIO_ALT|GPIO_AF13|GPIO_PORTD|GPIO_PIN10)
#define GPIO_SAI2_SCK_A_3       (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN9)
#define GPIO_SAI2_SCK_B_1       (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN10)
#define GPIO_SAI2_SCK_B_2       (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN2)
#define GPIO_SAI2_SD_A_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN15)
#define GPIO_SAI2_SD_A_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTD|GPIO_PIN11)
#define GPIO_SAI2_SD_A_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN12)
#define GPIO_SAI2_SD_B_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN12)
#define GPIO_SAI2_SD_B_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN5)

/* SDMMC1 - Secure digital input/output MultiMediaCard interface */

#define GPIO_SDMMC1_CDIR_1      (GPIO_ALT|GPIO_AF8|GPIO_PORTB|GPIO_PIN9)
#define GPIO_SDMMC1_CK_1        (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN12)
#define GPIO_SDMMC1_CKIN_1      (GPIO_ALT|GPIO_AF8|GPIO_PORTB|GPIO_PIN8)
#define GPIO_SDMMC1_CMD_1       (GPIO_ALT|GPIO_AF8|GPIO_PORTD|GPIO_PIN2)
#define GPIO_SDMMC1_D0DIR_1     (GPIO_ALT|GPIO_AF8|GPIO_PORTC|GPIO_PIN6)
#define GPIO_SDMMC1_D123DIR_1   (GPIO_ALT|GPIO_AF8|GPIO_PORTC|GPIO_PIN7)
#define GPIO_SDMMC1_D0_1        (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN8)
#define GPIO_SDMMC1_D1_1        (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN9)
#define GPIO_SDMMC1_D2_1        (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN10)
#define GPIO_SDMMC1_D3_1        (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN11)
#define GPIO_SDMMC1_D4_1        (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN8)
#define GPIO_SDMMC1_D5_1        (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN9)
#define GPIO_SDMMC1_D5_2        (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN0)
#define GPIO_SDMMC1_D6_1        (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN6)
#define GPIO_SDMMC1_D7_1        (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN7)

/* SDMMC2 - Secure digital input/output MultiMediaCard interface */

#define GPIO_SDMMC2_CMD_1       (GPIO_ALT|GPIO_AF12|GPIO_PORTA|GPIO_PIN0)
#define GPIO_SDMMC2_CMD_2       (GPIO_ALT|GPIO_AF11|GPIO_PORTD|GPIO_PIN7)
#define GPIO_SDMMC2_CK_1        (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN1)
#define GPIO_SDMMC2_CK_2        (GPIO_ALT|GPIO_AF11|GPIO_PORTD|GPIO_PIN6)
#define GPIO_SDMMC2_D0_1        (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN14)
#define GPIO_SDMMC2_D1_1        (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN15)
#define GPIO_SDMMC2_D2_1        (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN3)
#define GPIO_SDMMC2_D3_1        (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN4)
#define GPIO_SDMMC2_D4_1        (GPIO_ALT|GPIO_AF11|GPIO_PORTB|GPIO_PIN8)
#define GPIO_SDMMC2_D5_1        (GPIO_ALT|GPIO_AF11|GPIO_PORTB|GPIO_PIN9)
#define GPIO_SDMMC2_D6_1        (GPIO_ALT|GPIO_AF11|GPIO_PORTC|GPIO_PIN6)
#define GPIO_SDMMC2_D7_1        (GPIO_ALT|GPIO_AF11|GPIO_PORTC|GPIO_PIN7)

/* SPI1 - Serial peripheral interface */

#define GPIO_SPI1_MISO_1        (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN6)
#define GPIO_SPI1_MISO_2        (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN10)
#define GPIO_SPI1_MISO_3        (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN4)
#define GPIO_SPI1_MISO_4        (GPIO_ALT|GPIO_AF5|GPIO_PORTE|GPIO_PIN14)
#define GPIO_SPI1_MISO_5        (GPIO_ALT|GPIO_AF5|GPIO_PORTG|GPIO_PIN3)
#define GPIO_SPI1_MOSI_1        (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN7)
#define GPIO_SPI1_MOSI_2        (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN12)
#define GPIO_SPI1_MOSI_3        (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN5)
#define GPIO_SPI1_MOSI_4        (GPIO_ALT|GPIO_AF5|GPIO_PORTE|GPIO_PIN15)
#define GPIO_SPI1_MOSI_5        (GPIO_ALT|GPIO_AF5|GPIO_PORTG|GPIO_PIN4)
#define GPIO_SPI1_NSS_1         (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN4)
#define GPIO_SPI1_NSS_2         (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN15)
#define GPIO_SPI1_NSS_3         (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN0)
#define GPIO_SPI1_NSS_4         (GPIO_ALT|GPIO_AF5|GPIO_PORTE|GPIO_PIN12)
#define GPIO_SPI1_NSS_5         (GPIO_ALT|GPIO_AF5|GPIO_PORTG|GPIO_PIN5)
#define GPIO_SPI1_RDY_1         (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN2)
#define GPIO_SPI1_RDY_2         (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN8)
#define GPIO_SPI1_RDY_3         (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN2)
#define GPIO_SPI1_RDY_4         (GPIO_ALT|GPIO_AF5|GPIO_PORTE|GPIO_PIN11)
#define GPIO_SPI1_RDY_5         (GPIO_ALT|GPIO_AF5|GPIO_PORTG|GPIO_PIN6)
#define GPIO_SPI1_SCK_1         (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN1)
#define GPIO_SPI1_SCK_2         (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN5)
#define GPIO_SPI1_SCK_3         (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN2)
#define GPIO_SPI1_SCK_4         (GPIO_ALT|GPIO_AF5|GPIO_PORTE|GPIO_PIN13)
#define GPIO_SPI1_SCK_5         (GPIO_ALT|GPIO_AF5|GPIO_PORTG|GPIO_PIN2)

/* SPI2 - Serial peripheral interface */

#define GPIO_SPI2_MISO_1        (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN14)
#define GPIO_SPI2_MISO_2        (GPIO_ALT|GPIO_AF5|GPIO_PORTC|GPIO_PIN2)
#define GPIO_SPI2_MISO_3        (GPIO_ALT|GPIO_AF5|GPIO_PORTD|GPIO_PIN3)
#define GPIO_SPI2_MISO_4        (GPIO_ALT|GPIO_AF5|GPIO_PORTI|GPIO_PIN2)
#define GPIO_SPI2_MOSI_1        (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN15)
#define GPIO_SPI2_MOSI_2        (GPIO_ALT|GPIO_AF3|GPIO_PORTC|GPIO_PIN1)
#define GPIO_SPI2_MOSI_3        (GPIO_ALT|GPIO_AF3|GPIO_PORTC|GPIO_PIN3)
#define GPIO_SPI2_MOSI_4        (GPIO_ALT|GPIO_AF5|GPIO_PORTD|GPIO_PIN4)
#define GPIO_SPI2_MOSI_5        (GPIO_ALT|GPIO_AF5|GPIO_PORTI|GPIO_PIN3)
#define GPIO_SPI2_NSS_1         (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN9)
#define GPIO_SPI2_NSS_2         (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN12)
#define GPIO_SPI2_NSS_3         (GPIO_ALT|GPIO_AF5|GPIO_PORTD|GPIO_PIN0)
#define GPIO_SPI2_NSS_4         (GPIO_ALT|GPIO_AF5|GPIO_PORTI|GPIO_PIN0)
#define GPIO_SPI2_RDY_1         (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN11)
#define GPIO_SPI2_RDY_2         (GPIO_ALT|GPIO_AF5|GPIO_PORTC|GPIO_PIN0)
#define GPIO_SPI2_RDY_3         (GPIO_ALT|GPIO_AF5|GPIO_PORTD|GPIO_PIN5)
#define GPIO_SPI2_RDY_4         (GPIO_ALT|GPIO_AF5|GPIO_PORTI|GPIO_PIN4)
#define GPIO_SPI2_SCK_1         (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN9)
#define GPIO_SPI2_SCK_2         (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN10)
#define GPIO_SPI2_SCK_3         (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN13)
#define GPIO_SPI2_SCK_4         (GPIO_ALT|GPIO_AF5|GPIO_PORTD|GPIO_PIN1)
#define GPIO_SPI2_SCK_5         (GPIO_ALT|GPIO_AF3|GPIO_PORTD|GPIO_PIN3)
#define GPIO_SPI2_SCK_6         (GPIO_ALT|GPIO_AF3|GPIO_PORTI|GPIO_PIN1)

/* SPI3 - Serial peripheral interface */

#define GPIO_SPI3_MISO_1        (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN4)
#define GPIO_SPI3_MISO_2        (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN10)
#define GPIO_SPI3_MISO_3        (GPIO_ALT|GPIO_AF6|GPIO_PORTG|GPIO_PIN10)
#define GPIO_SPI3_MOSI_1        (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN5)
#define GPIO_SPI3_MOSI_2        (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN12)
#define GPIO_SPI3_MOSI_3        (GPIO_ALT|GPIO_AF5|GPIO_PORTD|GPIO_PIN6)
#define GPIO_SPI3_MOSI_4        (GPIO_ALT|GPIO_AF6|GPIO_PORTG|GPIO_PIN11)
#define GPIO_SPI3_NSS_1         (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN4)
#define GPIO_SPI3_NSS_2         (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN15)
#define GPIO_SPI3_NSS_3         (GPIO_ALT|GPIO_AF6|GPIO_PORTG|GPIO_PIN12)
#define GPIO_SPI3_RDY_1         (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN0)
#define GPIO_SPI3_RDY_2         (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN7)
#define GPIO_SPI3_RDY_3         (GPIO_ALT|GPIO_AF6|GPIO_PORTG|GPIO_PIN13)
#define GPIO_SPI3_SCK_1         (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN3)
#define GPIO_SPI3_SCK_2         (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN9)
#define GPIO_SPI3_SCK_3         (GPIO_ALT|GPIO_AF6|GPIO_PORTG|GPIO_PIN9)

/* TSC - Touch sensing controller */

#define GPIO_TSC_G1_IO1_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTB|GPIO_PIN12)
#define GPIO_TSC_G1_IO2_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTB|GPIO_PIN13)
#define GPIO_TSC_G1_IO3_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTB|GPIO_PIN14)
#define GPIO_TSC_G1_IO4_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTC|GPIO_PIN3)
#define GPIO_TSC_G2_IO1_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTB|GPIO_PIN4)
#define GPIO_TSC_G2_IO2_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTB|GPIO_PIN5)
#define GPIO_TSC_G2_IO3_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTB|GPIO_PIN6)
#define GPIO_TSC_G2_IO4_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTB|GPIO_PIN7)
#define GPIO_TSC_G3_IO1_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTC|GPIO_PIN2)
#define GPIO_TSC_G3_IO2_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTC|GPIO_PIN10)
#define GPIO_TSC_G3_IO3_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTC|GPIO_PIN11)
#define GPIO_TSC_G3_IO4_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTC|GPIO_PIN12)
#define GPIO_TSC_G4_IO1_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTC|GPIO_PIN6)
#define GPIO_TSC_G4_IO2_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTC|GPIO_PIN7)
#define GPIO_TSC_G4_IO3_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTC|GPIO_PIN8)
#define GPIO_TSC_G4_IO4_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTC|GPIO_PIN9)
#define GPIO_TSC_G5_IO1_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTE|GPIO_PIN10)
#define GPIO_TSC_G5_IO2_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTE|GPIO_PIN11)
#define GPIO_TSC_G5_IO3_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTE|GPIO_PIN12)
#define GPIO_TSC_G5_IO4_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTE|GPIO_PIN13)
#define GPIO_TSC_G6_IO1_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTD|GPIO_PIN10)
#define GPIO_TSC_G6_IO2_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTD|GPIO_PIN11)
#define GPIO_TSC_G6_IO3_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTD|GPIO_PIN12)
#define GPIO_TSC_G6_IO4_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTD|GPIO_PIN13)
#define GPIO_TSC_G7_IO1_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTE|GPIO_PIN2)
#define GPIO_TSC_G7_IO2_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTE|GPIO_PIN3)
#define GPIO_TSC_G7_IO3_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTE|GPIO_PIN4)
#define GPIO_TSC_G7_IO4_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTE|GPIO_PIN5)
#define GPIO_TSC_G8_IO1_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTF|GPIO_PIN14)
#define GPIO_TSC_G8_IO2_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTF|GPIO_PIN15)
#define GPIO_TSC_G8_IO3_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTG|GPIO_PIN0)
#define GPIO_TSC_G8_IO4_1       (GPIO_ALT|GPIO_AF9|GPIO_PORTG|GPIO_PIN1)
#define GPIO_TSC_SYNC_1         (GPIO_ALT|GPIO_AF9|GPIO_PORTB|GPIO_PIN10)
#define GPIO_TSC_SYNC_2         (GPIO_ALT|GPIO_AF9|GPIO_PORTD|GPIO_PIN2)

/* TIM1 - Advanced-control timers */

#define GPIO_TIM1_BKIN_1        (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM1_BKIN_2        (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN12)
#define GPIO_TIM1_BKIN_3        (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN15)
#define GPIO_TIM1_BKIN2_1       (GPIO_ALT|GPIO_AF2|GPIO_PORTA|GPIO_PIN11)
#define GPIO_TIM1_BKIN2_2       (GPIO_ALT|GPIO_AF2|GPIO_PORTE|GPIO_PIN14)
#define GPIO_TIM1_CH1_1         (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN8)
#define GPIO_TIM1_CH1_2         (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN9)
#define GPIO_TIM1_CH1_3         (GPIO_ALT|GPIO_AF1|GPIO_PORTG|GPIO_PIN15)
#define GPIO_TIM1_CH1N_1        (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM1_CH1N_2        (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN13)
#define GPIO_TIM1_CH1N_3        (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN18)
#define GPIO_TIM1_CH2_1         (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN9)
#define GPIO_TIM1_CH2_2         (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN11)
#define GPIO_TIM1_CH2_3         (GPIO_ALT|GPIO_AF1|GPIO_PORTG|GPIO_PIN14)
#define GPIO_TIM1_CH2N_1        (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN0)
#define GPIO_TIM1_CH2N_2        (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN14)
#define GPIO_TIM1_CH2N_3        (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN10)
#define GPIO_TIM1_CH3_1         (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN10)
#define GPIO_TIM1_CH3_2         (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN13)
#define GPIO_TIM1_CH3N_1        (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN1)
#define GPIO_TIM1_CH3N_2        (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN15)
#define GPIO_TIM1_CH3N_3        (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN12)
#define GPIO_TIM1_CH4_1         (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN11)
#define GPIO_TIM1_CH4_2         (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN14)
#define GPIO_TIM1_CH4N_2        (GPIO_ALT|GPIO_AF1|GPIO_PORTC|GPIO_PIN5)
#define GPIO_TIM1_CH4N_3        (GPIO_ALT|GPIO_AF3|GPIO_PORTE|GPIO_PIN15)
#define GPIO_TIM1_ETR_1         (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN12)
#define GPIO_TIM1_ETR_2         (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN7)
#define GPIO_TIM1_ETR_3         (GPIO_ALT|GPIO_AF1|GPIO_PORTG|GPIO_PIN12)

/* TIM2 - General-purpose timers */

#define GPIO_TIM2_CH1_1         (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM2_CH1_2         (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN5)
#define GPIO_TIM2_CH1_3         (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN15)
#define GPIO_TIM2_CH2_1         (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN1)
#define GPIO_TIM2_CH2_2         (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN3)
#define GPIO_TIM2_CH3_1         (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM2_CH3_2         (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN10)
#define GPIO_TIM2_CH4_1         (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM2_CH4_2         (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN11)
#define GPIO_TIM2_ETR_1         (GPIO_ALT|GPIO_AF2|GPIO_PORTA|GPIO_PIN5)
#define GPIO_TIM2_ETR_2         (GPIO_ALT|GPIO_AF2|GPIO_PORTA|GPIO_PIN15)
#define GPIO_TIM2_ETR_3         (GPIO_ALT|GPIO_AF14|GPIO_PORTA|GPIO_PIN0)

/* TIM5 - General-purpose timers */

#define GPIO_TIM5_CH1_1         (GPIO_ALT|GPIO_AF2|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM5_CH1_2         (GPIO_ALT|GPIO_AF2|GPIO_PORTF|GPIO_PIN6)
#define GPIO_TIM5_CH1_3         (GPIO_ALT|GPIO_AF2|GPIO_PORTH|GPIO_PIN10)
#define GPIO_TIM5_CH2_1         (GPIO_ALT|GPIO_AF2|GPIO_PORTA|GPIO_PIN1)
#define GPIO_TIM5_CH2_2         (GPIO_ALT|GPIO_AF2|GPIO_PORTF|GPIO_PIN7)
#define GPIO_TIM5_CH2_3         (GPIO_ALT|GPIO_AF2|GPIO_PORTH|GPIO_PIN11)
#define GPIO_TIM5_CH3_1         (GPIO_ALT|GPIO_AF2|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM5_CH3_2         (GPIO_ALT|GPIO_AF2|GPIO_PORTF|GPIO_PIN8)
#define GPIO_TIM5_CH3_3         (GPIO_ALT|GPIO_AF2|GPIO_PORTH|GPIO_PIN12)
#define GPIO_TIM5_CH4_1         (GPIO_ALT|GPIO_AF2|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM5_CH4_2         (GPIO_ALT|GPIO_AF2|GPIO_PORTF|GPIO_PIN9)
#define GPIO_TIM5_CH4_3         (GPIO_ALT|GPIO_AF2|GPIO_PORTI|GPIO_PIN0)
#define GPIO_TIM5_ETR_1         (GPIO_ALT|GPIO_AF1|GPIO_PORTF|GPIO_PIN6)

/* TIM8 - Advanced-control timers */

#define GPIO_TIM8_BKIN_1        (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM8_BKIN_2        (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN7)
#define GPIO_TIM8_BKIN_3        (GPIO_ALT|GPIO_AF3|GPIO_PORTI|GPIO_PIN4)
#define GPIO_TIM8_BKIN2_1       (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN6)
#define GPIO_TIM8_BKIN2_2       (GPIO_ALT|GPIO_AF1|GPIO_PORTC|GPIO_PIN9)
#define GPIO_TIM8_CH1_1         (GPIO_ALT|GPIO_AF3|GPIO_PORTC|GPIO_PIN6)
#define GPIO_TIM8_CH1_2         (GPIO_ALT|GPIO_AF3|GPIO_PORTI|GPIO_PIN5)
#define GPIO_TIM8_CH2_1         (GPIO_ALT|GPIO_AF3|GPIO_PORTC|GPIO_PIN6)
#define GPIO_TIM8_CH2_2         (GPIO_ALT|GPIO_AF3|GPIO_PORTI|GPIO_PIN6)
#define GPIO_TIM8_CH3_1         (GPIO_ALT|GPIO_AF3|GPIO_PORTC|GPIO_PIN7)
#define GPIO_TIM8_CH3_2         (GPIO_ALT|GPIO_AF3|GPIO_PORTI|GPIO_PIN7)
#define GPIO_TIM8_CH4_1         (GPIO_ALT|GPIO_AF3|GPIO_PORTC|GPIO_PIN8)
#define GPIO_TIM8_CH4_2         (GPIO_ALT|GPIO_AF3|GPIO_PORTI|GPIO_PIN2)
#define GPIO_TIM8_CH1N_1        (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN5)
#define GPIO_TIM8_CH1N_2        (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM8_CH1N_3        (GPIO_ALT|GPIO_AF3|GPIO_PORTH|GPIO_PIN13)
#define GPIO_TIM8_CH2N_1        (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN0)
#define GPIO_TIM8_CH2N_2        (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN14)
#define GPIO_TIM8_CH2N_3        (GPIO_ALT|GPIO_AF3|GPIO_PORTH|GPIO_PIN14)
#define GPIO_TIM8_CH3N_1        (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN1)
#define GPIO_TIM8_CH3N_2        (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN15)
#define GPIO_TIM8_CH3N_3        (GPIO_ALT|GPIO_AF3|GPIO_PORTH|GPIO_PIN15)
#define GPIO_TIM8_CH4N_1        (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN2)
#define GPIO_TIM8_CH4N_2        (GPIO_ALT|GPIO_AF3|GPIO_PORTD|GPIO_PIN0)
#define GPIO_TIM8_CH4N_3        (GPIO_ALT|GPIO_AF3|GPIO_PORTH|GPIO_PIN12)
#define GPIO_TIM8_ETR_1         (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM8_ETR_2         (GPIO_ALT|GPIO_AF3|GPIO_PORTI|GPIO_PIN3)

/* TIM15 - General-purpose timers */

#define GPIO_TIM15_BKIN_1       (GPIO_ALT|GPIO_AF14|GPIO_PORTA|GPIO_PIN9)
#define GPIO_TIM15_BKIN_2       (GPIO_ALT|GPIO_AF14|GPIO_PORTB|GPIO_PIN12)
#define GPIO_TIM15_CH1_1        (GPIO_ALT|GPIO_AF14|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM15_CH1_2        (GPIO_ALT|GPIO_AF14|GPIO_PORTB|GPIO_PIN14)
#define GPIO_TIM15_CH1_3        (GPIO_ALT|GPIO_AF14|GPIO_PORTF|GPIO_PIN9)
#define GPIO_TIM15_CH1_4        (GPIO_ALT|GPIO_AF14|GPIO_PORTG|GPIO_PIN10)
#define GPIO_TIM15_CH1N_1       (GPIO_ALT|GPIO_AF14|GPIO_PORTA|GPIO_PIN1)
#define GPIO_TIM15_CH1N_2       (GPIO_ALT|GPIO_AF14|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM15_CH1N_3       (GPIO_ALT|GPIO_AF14|GPIO_PORTG|GPIO_PIN9)
#define GPIO_TIM15_CH2_1        (GPIO_ALT|GPIO_AF14|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM15_CH2_2        (GPIO_ALT|GPIO_AF14|GPIO_PORTB|GPIO_PIN15)
#define GPIO_TIM15_CH2_3        (GPIO_ALT|GPIO_AF14|GPIO_PORTF|GPIO_PIN10)
#define GPIO_TIM15_CH2_4        (GPIO_ALT|GPIO_AF14|GPIO_PORTG|GPIO_PIN12)

/* TIM16 - General-purpose timers */

#define GPIO_TIM16_BKIN_1       (GPIO_ALT|GPIO_AF14|GPIO_PORTB|GPIO_PIN5)
#define GPIO_TIM16_CH1_1        (GPIO_ALT|GPIO_AF14|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM16_CH1_2        (GPIO_ALT|GPIO_AF14|GPIO_PORTE|GPIO_PIN0)
#define GPIO_TIM16_CH1_3        (GPIO_ALT|GPIO_AF14|GPIO_PORTB|GPIO_PIN8)
#define GPIO_TIM16_CH1N_1       (GPIO_ALT|GPIO_AF14|GPIO_PORTB|GPIO_PIN5)

/* TIM17 - General-purpose timers */

#define GPIO_TIM17_BKIN_1       (GPIO_ALT|GPIO_AF14|GPIO_PORTA|GPIO_PIN11)
#define GPIO_TIM17_BKIN_2       (GPIO_ALT|GPIO_AF14|GPIO_PORTB|GPIO_PIN4)
#define GPIO_TIM17_CH1_1        (GPIO_ALT|GPIO_AF14|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM17_CH1_2        (GPIO_ALT|GPIO_AF14|GPIO_PORTE|GPIO_PIN1)
#define GPIO_TIM17_CH1_3        (GPIO_ALT|GPIO_AF14|GPIO_PORTB|GPIO_PIN9)
#define GPIO_TIM17_CH1N_1       (GPIO_ALT|GPIO_AF14|GPIO_PORTB|GPIO_PIN7)

/* UCPD1 - USB Type-C / USB Power Delivery interface */

#define GPIO_UCPD1_FRSTX1_1     (GPIO_ALT|GPIO_AF11|GPIO_PORTA|GPIO_PIN2)
#define GPIO_UCPD1_FRSTX1_2     (GPIO_ALT|GPIO_AF11|GPIO_PORTG|GPIO_PIN6)
#define GPIO_UCPD1_FRSTX1_3     (GPIO_ALT|GPIO_AF11|GPIO_PORTG|GPIO_PIN7)
#define GPIO_UCPD1_FRSTX2_1     (GPIO_ALT|GPIO_AF11|GPIO_PORTB|GPIO_PIN2)
#define GPIO_UCPD1_FRSTX2_2     (GPIO_ALT|GPIO_AF11|GPIO_PORTC|GPIO_PIN11)
#define GPIO_UCPD1_FRSTX2_3     (GPIO_ALT|GPIO_AF11|GPIO_PORTF|GPIO_PIN13)

/* UART4 - Universal asynchronous receiver transmitter */

#define GPIO_UART4_CTS_1        (GPIO_ALT|GPIO_AF8|GPIO_PORTB|GPIO_PIN7)
#define GPIO_UART4_RTS_DE_1     (GPIO_ALT|GPIO_AF8|GPIO_PORTA|GPIO_PIN15)
#define GPIO_UART4_RX_1         (GPIO_ALT|GPIO_AF8|GPIO_PORTA|GPIO_PIN1)
#define GPIO_UART4_RX_2         (GPIO_ALT|GPIO_AF8|GPIO_PORTC|GPIO_PIN11)
#define GPIO_UART4_TX_1         (GPIO_ALT|GPIO_AF8|GPIO_PORTA|GPIO_PIN0)
#define GPIO_UART4_TX_2         (GPIO_ALT|GPIO_AF8|GPIO_PORTC|GPIO_PIN10)

/* UART5 - Universal asynchronous receiver transmitter */

#define GPIO_UART5_RTS_DE_1     (GPIO_ALT|GPIO_AF8|GPIO_PORTB|GPIO_PIN4)
#define GPIO_UART5_CTS_1        (GPIO_ALT|GPIO_AF8|GPIO_PORTB|GPIO_PIN5)
#define GPIO_UART5_RX_1         (GPIO_ALT|GPIO_AF8|GPIO_PORTD|GPIO_PIN2)
#define GPIO_UART5_TX_1         (GPIO_ALT|GPIO_AF8|GPIO_PORTC|GPIO_PIN12)

/* USART1 - Universal synchronous/asynchronous receiver transmitter */

#define GPIO_USART1_CK_1        (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN8)
#define GPIO_USART1_CK_2        (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN4)
#define GPIO_USART1_CK_3        (GPIO_ALT|GPIO_AF7|GPIO_PORTG|GPIO_PIN13)
#define GPIO_USART1_CTS_1       (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN11)
#define GPIO_USART1_CTS_2       (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN4)
#define GPIO_USART1_CTS_3       (GPIO_ALT|GPIO_AF7|GPIO_PORTG|GPIO_PIN11)
#define GPIO_USART1_RTS_DE_1    (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN12)
#define GPIO_USART1_RTS_DE_2    (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN3)
#define GPIO_USART1_RTS_DE_3    (GPIO_ALT|GPIO_AF7|GPIO_PORTG|GPIO_PIN12)
#define GPIO_USART1_RX_1        (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN10)
#define GPIO_USART1_RX_2        (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN7)
#define GPIO_USART1_RX_3        (GPIO_ALT|GPIO_AF7|GPIO_PORTG|GPIO_PIN10)
#define GPIO_USART1_TX_1        (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN9)
#define GPIO_USART1_TX_2        (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN6)
#define GPIO_USART1_TX_3        (GPIO_ALT|GPIO_AF7|GPIO_PORTG|GPIO_PIN9)

/* USART2 - Universal synchronous/asynchronous receiver transmitter */

#define GPIO_USART2_CK_1        (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN4)
#define GPIO_USART2_CK_2        (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN7)
#define GPIO_USART2_CTS_1       (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN0)
#define GPIO_USART2_CTS_2       (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN3)
#define GPIO_USART2_RTS_DE_1    (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN1)
#define GPIO_USART2_RTS_DE_2    (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN4)
#define GPIO_USART2_RX_1        (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN3)
#define GPIO_USART2_RX_2        (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN15)
#define GPIO_USART2_RX_3        (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN6)
#define GPIO_USART2_TX_1        (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN2)
#define GPIO_USART2_TX_2        (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN5)

/* USART3 - Universal synchronous/asynchronous receiver transmitter */

#define GPIO_USART3_CK_1        (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN0)
#define GPIO_USART3_CK_2        (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN12)
#define GPIO_USART3_CK_3        (GPIO_ALT|GPIO_AF7|GPIO_PORTC|GPIO_PIN12)
#define GPIO_USART3_CK_4        (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN10)
#define GPIO_USART3_CTS_1       (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN6)
#define GPIO_USART3_CTS_2       (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN13)
#define GPIO_USART3_CTS_3       (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN11)
#define GPIO_USART3_RTS_DE_1    (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN15)
#define GPIO_USART3_RTS_DE_2    (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN1)
#define GPIO_USART3_RTS_DE_3    (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN14)
#define GPIO_USART3_RTS_DE_4    (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN2)
#define GPIO_USART3_RTS_DE_5    (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN12)
#define GPIO_USART3_RX_1        (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN5)
#define GPIO_USART3_RX_2        (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN11)
#define GPIO_USART3_RX_3        (GPIO_ALT|GPIO_AF7|GPIO_PORTC|GPIO_PIN5)
#define GPIO_USART3_RX_4        (GPIO_ALT|GPIO_AF7|GPIO_PORTC|GPIO_PIN11)
#define GPIO_USART3_RX_5        (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN9)
#define GPIO_USART3_TX_1        (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN7)
#define GPIO_USART3_TX_2        (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN10)
#define GPIO_USART3_TX_3        (GPIO_ALT|GPIO_AF7|GPIO_PORTC|GPIO_PIN4)
#define GPIO_USART3_TX_4        (GPIO_ALT|GPIO_AF7|GPIO_PORTC|GPIO_PIN10)
#define GPIO_USART3_TX_5        (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN8)

#endif /* __ARCH_ARM_SRC_STM32U5_HARDWARE_STM32U585XX_PINMAP_H */
