##############################################################################
# arch/arm/src/stm32u5/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
##############################################################################

# The start-up, "head", file.  Only common vectors are support so there
# isn't one.

HEAD_ASRC =

# Common ARM and Cortex-M4 files (copied from stm32/Make.defs)

include armv8-m/Make.defs

# Required STM32U5 files

CHIP_ASRCS  =
CHIP_CSRCS  = stm32_allocateheap.c stm32_exti_gpio.c stm32_gpio.c
CHIP_CSRCS += stm32_irq.c stm32_lowputc.c stm32_rcc.c
CHIP_CSRCS += stm32_serial.c stm32_start.c stm32_waste.c stm32_uid.c
CHIP_CSRCS += stm32_spi.c stm32_lse.c stm32_lsi.c
CHIP_CSRCS += stm32_pwr.c stm32_tim.c stm32_flash.c stm32_timerisr.c

ifneq ($(CONFIG_ARCH_IDLE_CUSTOM),y)
CHIP_CSRCS += stm32_idle.c
endif

ifeq ($(CONFIG_TIMER),y)
CHIP_CSRCS += stm32_tim_lowerhalf.c
endif

ifeq ($(CONFIG_BUILD_PROTECTED),y)
CHIP_CSRCS += stm32_userspace.c stm32_mpuinit.c
endif

ifeq ($(CONFIG_DEBUG_FEATURES),y)
CHIP_CSRCS += stm32_dumpgpio.c
endif

# Required chip type specific files

ifeq ($(CONFIG_STM32U5_STM32U585XX),y)
CHIP_CSRCS += stm32u585xx_rcc.c
endif
