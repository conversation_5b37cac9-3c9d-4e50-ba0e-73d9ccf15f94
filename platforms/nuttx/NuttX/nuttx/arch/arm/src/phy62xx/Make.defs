############################################################################
# arch/arm/src/phy62xx/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

include armv6-m/Make.defs

CMN_ASRCS += phy62xx_exception.S phy62xx_start.S

CHIP_CSRCS  = start.c irq.c timer.c idle.c
CHIP_CSRCS += jump_table.c
CHIP_CSRCS += pplus_mtd_flash.c
ifeq ($(CONFIG_PHY6222_BLE),y)
CHIP_CSRCS += phy62xx_ble.c
endif

ifeq ($(CONFIG_TIMER),y)
CHIP_CSRCS += phyplus_tim.c 
CHIP_CSRCS += phyplus_timer_lowerhalf.c
CHIP_CSRCS += phyplus_timerisr.c 
endif

ifeq ($(CONFIG_DEV_GPIO),y)
CHIP_CSRCS += phyplus_gpio.c
endif

#ifeq ($(CONFIG_WATCHDOG),y)
CHIP_CSRCS += phyplus_wdt.c
#endif

ifeq ($(CONFIG_PHYPLUS_STUB),y)
CHIP_CSRCS += phyplus_stub.c
endif

INCLUDES += $(shell $(INCDIR) "$(CC)" $(ARCH_SRCDIR)$(DELIM)chip$(DELIM)include)
INCLUDES += $(shell $(INCDIR) "$(CC)" $(ARCH_SRCDIR)$(DELIM)chip$(DELIM)ble)

CFLAGS += -ffunction-sections
CFLAGS += -DCFG_CP
CFLAGS += -DPHY_MCU_TYPE=MCU_BUMBEE_M0
CFLAGS += -DHOST_CONFIG=4 
CFLAGS += -DHCI_TL_NONE=1 
CFLAGS += -DMTU_SIZE=247 
CFLAGS += -DENABLE_LOG_ROMx=0 
CFLAGS += -DPHY_MCU_TYPE=MCU_BUMBEE_M0 
CFLAGS += -DCFG_SLEEP_MODE=PWR_MODE_NO_SLEEP 
CFLAGS += -DDEBUG_INFO=1 
CFLAGS += -DUSE_SYS_TICK 
CFLAGS += -DHUGE_MODE=0 
CFLAGS += -DMAX_NUM_LL_CONN=1
CFLAGS += -DUSE_ROMSYM_ALIAS
CFLAGS += -Wno-unused-but-set-variable
CFLAGS += -DEXTERN_BLE_FUNC=0
LDFLAGS += "$(ARCH_SRCDIR)$(DELIM)chip$(DELIM)bb_rom_sym_m0.gdbsym"

ifndef CONFIG_PHY6222_SDK
EXTRA_LIBPATHS += -L$(TOPDIR)/arch/arm/src/chip
CHIP_CSRCS += flash.c
CHIP_CSRCS += clock.c
CHIP_CSRCS += gpio.c
CHIP_CSRCS += pwrmgr.c
CHIP_CSRCS += uart.c
CHIP_CSRCS += my_printf.c
CHIP_CSRCS += phy6222_patch.c
else
EXTRA_LIBPATHS += -L$(TOPDIR)/../apps/vendor/phyplus/phy6222_sdk/lib
ifdef CONFIG_PHYAPP_SBPADVSCAN
EXTRA_LIBS += -lphy6222_rf_advscan
else
EXTRA_LIBS += -lphy6222_rf
endif
EXTRA_LIBS += -lphy6222_sec_boot
EXTRA_LIBS += -lphy6222_host
endif

ifdef CONFIG_PHY6222_PHY_MESH
EXTRA_LIBPATHS += -L$(TOPDIR)/../apps/vendor/phyplus/phy6222_sdk/components/ethermind/lib/meshlibs/phyos/armgcc/
EXTRA_LIBS += -lethermind_mesh_core
EXTRA_LIBS += -lethermind_mesh_models
EXTRA_LIBS += -lethermind_utils
endif

ifdef CONFIG_MIJIA_APIS
EXTRA_LIBPATHS += -L$(TOPDIR)/../apps/vendor/phyplus/phy6222_sdk/components/xiaomi/libs/mesh_auth/debug/
EXTRA_LIBS += $(TOPDIR)/../apps/vendor/phyplus/phy6222_sdk/components/xiaomi/libs/mesh_auth/debug/mesh-auth-cortex-m0plus-debug.a
endif

