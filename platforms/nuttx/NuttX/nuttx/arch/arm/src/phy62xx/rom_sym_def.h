/****************************************************************************
 * arch/arm/src/phy62xx/rom_sym_def.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_PHY62XX_ROM_SYM_DEF_H
#define __ARCH_ARM_SRC_PHY62XX_ROM_SYM_DEF_H

#ifdef USE_ROMSYM_ALIAS

  /* #define x _symrom_
   * #define x _symrom_
   */

  #define TIM2_IRQHandler _symrom_TIM2_IRQHandler
  #define gpio_write _symrom_gpio_write
  #define ll_processExtInitIRQ _symrom_ll_processExtInitIRQ
  #define ll_processExtScanIRQ _symrom_ll_processExtScanIRQ
  #define ll_processExtAdvIRQ _symrom_ll_processExtAdvIRQ
  #define LL_SetDataLengh0 _symrom_LL_SetDataLengh0
  #define LL_SetScanParam0 _symrom_LL_SetScanParam0
  #define LL_SetAdvParam0 _symrom_LL_SetAdvParam0
  #define LL_CreateConn0 _symrom_LL_CreateConn0
  #define isPeerRpaStore _symrom_isPeerRpaStore
  #define storeRpaListIndex _symrom_storeRpaListIndex
  #define currentPeerRpa _symrom_currentPeerRpa

  #define isPeerRpaStore _symrom_isPeerRpaStore
  #define LL_ENC_AES128_Encrypt0 _symrom_LL_ENC_AES128_Encrypt0
  #define llProcessTxData0 _symrom_llProcessTxData0
  #define sleep_tick _symrom_sleep_tick
  #define g_currentPeerAddrType _symrom_g_currentPeerAddrType

  #define g_rfPhyTxPower	_symrom_g_rfPhyTxPower
  #define ble_main _symrom_ble_main

  #define HCI_LE_Write_Rf_Path_CompensationCmd	_symrom_HCI_LE_Write_Rf_Path_CompensationCmd
  #define HCI_LE_ReadPeerResolvableAddressCmd	_symrom_HCI_LE_ReadPeerResolvableAddressCmd
  #define HCI_LE_AddDevToPeriodicAdvListCmd	_symrom_HCI_LE_AddDevToPeriodicAdvListCmd
  #define HCI_LE_RemovePeriodicAdvListCmd	_symrom_HCI_LE_RemovePeriodicAdvListCmd
  #define HCI_LE_ClearPeriodicAdvListCmd	_symrom_HCI_LE_ClearPeriodicAdvListCmd
  #define HCI_LE_ReadPeriodicAdvListSizeCmd	_symrom_HCI_LE_ReadPeriodicAdvListSizeCmd
  #define HCI_LE_Read_Transmit_PowerCmd	_symrom_HCI_LE_Read_Transmit_PowerCmd
  #define HCI_LE_Read_Rf_Path_CompensationCmd	_symrom_HCI_LE_Read_Rf_Path_CompensationCmd
  #define HCI_LE_Set_Privacy_ModeCmd	_symrom_HCI_LE_Set_Privacy_ModeCmd

  #define ll_readPeerIRK _symrom_ll_readPeerIRK
  #define ownRandomAddr _symrom_ownRandomAddr
  #define g_currentLocalRpa _symrom_g_currentLocalRpa
  #define g_currentPeerRpa _symrom_g_currentPeerRpa
  #define llProcessSlaveControlProcedures0 _symrom_llProcessSlaveControlProcedures0
  #define llProcessSlaveControlPacket0 _symrom_llProcessSlaveControlPacket0
  #define g_rfTxPathCompensation _symrom_g_rfTxPathCompensation
  #define ll_isLegacyAdv _symrom_ll_isLegacyAdv
  #define g_llScanMode _symrom_g_llScanMode
  #define rf_phy_change_cfg _symrom_rf_phy_change_cfg
  #define llProcessMasterControlProcedures0 _symrom_llProcessMasterControlProcedures0

  #define spif_config _symrom_spif_config
  #define m_in_critical_region _symrom_m_in_critical_region
  #define _spif_read_status_reg _symrom__spif_read_status_reg
  #define _spif_wait_nobusy _symrom__spif_wait_nobusy
  #define adv_param _symrom_adv_param
  #define app_sleep_process _symrom_app_sleep_process
  #define app_wakeup_process _symrom_app_wakeup_process
  #define ate_fun_test _symrom_ate_fun_test
  #define ate_sleep_process _symrom_ate_sleep_process
  #define ate_wakeup_process _symrom_ate_wakeup_process
  #define baseTaskID _symrom_baseTaskID
  #define bit_to_byte _symrom_bit_to_byte
  #define ble_crc24_gen _symrom_ble_crc24_gen
  #define bleEvtMask _symrom_bleEvtMask
  #define boot_init _symrom_boot_init
  #define boot_init0 _symrom_boot_init0
  #define boot_m0 _symrom_boot_m0
  #define bx_to_application _symrom_bx_to_application
  #define byte_to_bit _symrom_byte_to_bit
  #define cachedTRNGdata _symrom_cachedTRNGdata
  #define calculate_whiten_seed _symrom_calculate_whiten_seed
  #define cbTimers _symrom_cbTimers
  #define chanMapUpdate _symrom_chanMapUpdate
  #define clear_timer _symrom_clear_timer
  #define clear_timer_int _symrom_clear_timer_int
  #define clk_get_pclk _symrom_clk_get_pclk
  #define clk_init _symrom_clk_init
  #define clk_set_pclk_div _symrom_clk_set_pclk_div
  #define clk_spif_ref_clk _symrom_clk_spif_ref_clk
  #define config_RTC _symrom_config_RTC
  #define conn_param _symrom_conn_param
  #define connUpdateTimer _symrom_connUpdateTimer
  #define counter_tracking _symrom_counter_tracking
  #define rom_crc16 _symrom_crc16
  #define ctrlToHostEnable _symrom_ctrlToHostEnable
  #define dataPkt _symrom_dataPkt
  #define debug_print _symrom_debug_print
  #define deviceFeatureSet _symrom_deviceFeatureSet
  #define disableSleep _symrom_disableSleep
  #define drv_disable_irq _symrom_drv_disable_irq
  #define drv_enable_irq _symrom_drv_enable_irq
  #define drv_irq_init _symrom_drv_irq_init
  #define dwc_connect _symrom_dwc_connect
  #define dwc_data_process _symrom_dwc_data_process
  #define dwc_loop _symrom_dwc_loop
  #define efuse_read _symrom_efuse_read
  #define enableSleep _symrom_enableSleep
  #define enter_sleep_off_mode _symrom_enter_sleep_off_mode
  #define enterSleepProcess _symrom_enterSleepProcess
  #define ext_adv_hdr _symrom_ext_adv_hdr
  #define extInitInfo _symrom_extInitInfo
  #define extScanInfo _symrom_extScanInfo
  #define fastTxRespTime _symrom_fastTxRespTime
  #define forever_write _symrom_forever_write
  #define g_adv_taskEvent _symrom_g_adv_taskEvent
  #define g_adv_taskID _symrom_g_adv_taskID
  #define g_advPerSlotTick _symrom_g_advPerSlotTick
  #define g_advSetMaximumLen _symrom_g_advSetMaximumLen
  #define g_advSlotPeriodic _symrom_g_advSlotPeriodic
  #define g_blePktVersion _symrom_g_blePktVersion
  #define g_conn_taskEvent _symrom_g_conn_taskEvent
  #define g_conn_taskID _symrom_g_conn_taskID
  #define g_counter_traking_avg _symrom_g_counter_traking_avg
  #define g_counter_traking_cnt _symrom_g_counter_traking_cnt
  #define g_currentAdvTimer _symrom_g_currentAdvTimer
  #define g_currentExtAdv _symrom_g_currentExtAdv
  #define g_currentExtAdv_periodic _symrom_g_currentExtAdv_periodic
  #define g_currentTimerTask _symrom_g_currentTimerTask
  #define g_dle_taskEvent _symrom_g_dle_taskEvent
  #define g_dle_taskID _symrom_g_dle_taskID
  #define g_dtmAccessCode _symrom_g_dtmAccessCode
  #define g_dtmCarrSens _symrom_g_dtmCarrSens
  #define g_dtmCmd _symrom_g_dtmCmd
  #define g_dtmCtrl _symrom_g_dtmCtrl
  #define g_dtmEvt _symrom_g_dtmEvt
  #define g_dtmExtLen _symrom_g_dtmExtLen
  #define g_dtmFoff _symrom_g_dtmFoff
  #define g_dtmFreq _symrom_g_dtmFreq
  #define g_dtmLength _symrom_g_dtmLength
  #define g_dtmModeType _symrom_g_dtmModeType
  #define g_dtmPara _symrom_g_dtmPara
  #define g_dtmPerAutoIntv _symrom_g_dtmPerAutoIntv
  #define g_dtmPKT _symrom_g_dtmPKT
  #define g_dtmPktCount _symrom_g_dtmPktCount
  #define g_dtmPktIntv _symrom_g_dtmPktIntv
  #define g_dtmRsp _symrom_g_dtmRsp
  #define g_dtmRssi _symrom_g_dtmRssi
  #define g_dtmRxCrcNum _symrom_g_dtmRxCrcNum
  #define g_dtmRxTONum _symrom_g_dtmRxTONum
  #define g_dtmStatus _symrom_g_dtmStatus
  #define g_dtmTick _symrom_g_dtmTick
  #define g_dtmTpCalEnable _symrom_g_dtmTpCalEnable
  #define g_dtmTxPower _symrom_g_dtmTxPower
  #define g_extAdvNumber _symrom_g_extAdvNumber
  #define g_getPn23_cnt _symrom_g_getPn23_cnt
  #define g_getPn23_seed _symrom_g_getPn23_seed
  #define g_hclk _symrom_g_hclk
  #define g_interAuxPduDuration _symrom_g_interAuxPduDuration
  #define g_ll_conn_ctx _symrom_g_ll_conn_ctx
  #define g_llHdcDirAdvTime _symrom_g_llHdcDirAdvTime
  #define g_llPduLen _symrom_g_llPduLen
  #define g_llPeriodAdvSyncInfo _symrom_g_llPeriodAdvSyncInfo
  #define g_llResolvinglist _symrom_g_llResolvinglist
  #define g_llRlDeviceNum _symrom_g_llRlDeviceNum
  #define g_llRlEnable _symrom_g_llRlEnable
  #define g_llRlTimeout _symrom_g_llRlTimeout
  #define g_llSleepContext _symrom_g_llSleepContext
  #define g_llWhitelist _symrom_g_llWhitelist
  #define g_llWlDeviceNum _symrom_g_llWlDeviceNum
  #define g_maxConnNum _symrom_g_maxConnNum
  #define g_maxPktPerEventRx _symrom_g_maxPktPerEventRx
  #define g_maxPktPerEventTx _symrom_g_maxPktPerEventTx
  #define g_new_master_delta _symrom_g_new_master_delta
  #define g_osal_tick_trim _symrom_g_osal_tick_trim
  #define g_osalTickTrim_mod _symrom_g_osalTickTrim_mod
  #define g_pAdvSchInfo _symrom_g_pAdvSchInfo
  #define g_pAdvSchInfo_periodic _symrom_g_pAdvSchInfo_periodic
  #define g_perioAdvNumber _symrom_g_perioAdvNumber
  #define g_pExtendedAdvInfo _symrom_g_pExtendedAdvInfo
  #define g_phyChg_taskEvent _symrom_g_phyChg_taskEvent
  #define g_phyChg_taskID _symrom_g_phyChg_taskID
  #define g_pLLcteISample _symrom_g_pLLcteISample
  #define g_pLLcteQSample _symrom_g_pLLcteQSample
  #define g_pmCounters _symrom_g_pmCounters
  #define g_pPeriodicAdvInfo _symrom_g_pPeriodicAdvInfo
  #define ll_isIrkAllZero	_symrom_ll_isIrkAllZero
  #define g_currentLocalAddrType	_symrom_g_currentLocalAddrType
  #define g_rfPhyClkSel _symrom_g_rfPhyClkSel
  #define g_rfPhyDtmCmd _symrom_g_rfPhyDtmCmd
  #define g_rfPhyDtmEvt _symrom_g_rfPhyDtmEvt
  #define g_llAdvMode _symrom_g_llAdvMode
  #define g_rfPhyFreqOffSet _symrom_g_rfPhyFreqOffSet
  #define g_rfPhyPktFmt _symrom_g_rfPhyPktFmt
  #define g_rfPhyRxDcIQ _symrom_g_rfPhyRxDcIQ
  #define g_rfPhyTpCal0 _symrom_g_rfPhyTpCal0
  #define g_rfPhyTpCal0_2Mbps _symrom_g_rfPhyTpCal0_2Mbps
  #define g_rfPhyTpCal1 _symrom_g_rfPhyTpCal1
  #define g_rfPhyTpCal1_2Mbps _symrom_g_rfPhyTpCal1_2Mbps
  /* #define g_rfPhyTxPower _symrom_g_rfPhyTxPower */
  #define g_rx_adv_buf _symrom_g_rx_adv_buf
  #define g_rxAdcClkSel _symrom_g_rxAdcClkSel
  #define g_same_rf_channel_flag _symrom_g_same_rf_channel_flag
  #define g_schExtAdvNum _symrom_g_schExtAdvNum
  #define g_schExtAdvNum_periodic _symrom_g_schExtAdvNum_periodic
  #define g_smartWindowActive _symrom_g_smartWindowActive
  #define g_smartWindowActiveCnt _symrom_g_smartWindowActiveCnt
  #define g_smartWindowLater _symrom_g_smartWindowLater
  #define g_smartWindowPreAnchPoint _symrom_g_smartWindowPreAnchPoint
  #define g_smartWindowRTOCnt _symrom_g_smartWindowRTOCnt
  #define g_smartWindowSize _symrom_g_smartWindowSize
  #define g_smartWindowSizeNew _symrom_g_smartWindowSizeNew
  #define g_system_clk _symrom_g_system_clk
  #define g_TIM2_IRQ_PendingTick _symrom_g_TIM2_IRQ_PendingTick
  #define g_TIM2_IRQ_TIM3_CurrCount _symrom_g_TIM2_IRQ_TIM3_CurrCount
  #define g_TIM2_IRQ_to_Sleep_DeltTick _symrom_g_TIM2_IRQ_to_Sleep_DeltTick
  #define g_TIM2_wakeup_delay _symrom_g_TIM2_wakeup_delay
  #define g_timerExpiryTick _symrom_g_timerExpiryTick
  #define g_tx_adv_buf _symrom_g_tx_adv_buf
  #define g_tx_ext_adv_buf _symrom_g_tx_ext_adv_buf
  #define g_wakeup_rtc_tick _symrom_g_wakeup_rtc_tick
  #define get_rx_read_ptr _symrom_get_rx_read_ptr
  #define get_rx_write_ptr _symrom_get_rx_write_ptr
  #define get_sleep_flag _symrom_get_sleep_flag
  #define get_timer_count _symrom_get_timer_count
  #define get_timer_int _symrom_get_timer_int
  #define get_tx_read_ptr _symrom_get_tx_read_ptr
  #define get_tx_write_ptr _symrom_get_tx_write_ptr
  #define getMcuPrecisionCount _symrom_getMcuPrecisionCount
  #define getPN23RandNumber _symrom_getPN23RandNumber
  #define getRxBufferFree _symrom_getRxBufferFree
  #define getRxBufferSize _symrom_getRxBufferSize
  #define getSleepMode _symrom_getSleepMode
  #define getTxBufferFree _symrom_getTxBufferFree
  #define getTxBufferSize _symrom_getTxBufferSize
  #define gpio_cfg_analog_io _symrom_gpio_cfg_analog_io
  #define gpio_dir _symrom_gpio_dir
  #define gpio_fmux_control _symrom_gpio_fmux_control
  #define gpio_fmux_set _symrom_gpio_fmux_set
  #define gpio_in_trigger _symrom_gpio_in_trigger
  #define gpio_init _symrom_gpio_init
  #define gpio_interrupt_set _symrom_gpio_interrupt_set
  #define GPIO_IRQHandler _symrom_GPIO_IRQHandler
  #define gpio_pull_set _symrom_gpio_pull_set
  #define gpio_read _symrom_gpio_read
  #define gpio_wakeup_set _symrom_gpio_wakeup_set
  #define gpio_write _symrom_gpio_write
  #define HardFault_Handler _symrom_HardFault_Handler
  #define HardFault_IRQHandler _symrom_HardFault_IRQHandler
  #define HCI_bm_alloc _symrom_HCI_bm_alloc
  #define HCI_CommandCompleteEvent _symrom_HCI_CommandCompleteEvent
  #define HCI_CommandStatusEvent _symrom_HCI_CommandStatusEvent
  #define HCI_DataBufferOverflowEvent _symrom_HCI_DataBufferOverflowEvent
  #define HCI_DisconnectCmd _symrom_HCI_DisconnectCmd
  #define HCI_ExtTaskRegister _symrom_HCI_ExtTaskRegister
  #define HCI_GAPTaskRegister _symrom_HCI_GAPTaskRegister
  #define HCI_HardwareErrorEvent _symrom_HCI_HardwareErrorEvent
  #define HCI_HostBufferSizeCmd _symrom_HCI_HostBufferSizeCmd
  #define HCI_HostNumCompletedPktCmd _symrom_HCI_HostNumCompletedPktCmd
  #define HCI_Init _symrom_HCI_Init
  #define HCI_L2CAPTaskRegister _symrom_HCI_L2CAPTaskRegister
  #define HCI_LE_AddDevToResolvingListCmd _symrom_HCI_LE_AddDevToResolvingListCmd
  #define HCI_LE_AddWhiteListCmd _symrom_HCI_LE_AddWhiteListCmd
  #define HCI_LE_ClearAdvSetsCmd _symrom_HCI_LE_ClearAdvSetsCmd
  #define HCI_LE_ClearResolvingListCmd _symrom_HCI_LE_ClearResolvingListCmd
  #define HCI_LE_ClearWhiteListCmd _symrom_HCI_LE_ClearWhiteListCmd
  #define HCI_LE_Connection_CTE_Request_EnableCmd _symrom_HCI_LE_Connection_CTE_Request_EnableCmd
  #define HCI_LE_Connection_CTE_Response_EnableCmd _symrom_HCI_LE_Connection_CTE_Response_EnableCmd
  #define HCI_LE_ConnectionlessCTE_TransmitEnableCmd _symrom_HCI_LE_ConnectionlessCTE_TransmitEnableCmd
  #define HCI_LE_ConnectionlessCTE_TransmitParamCmd _symrom_HCI_LE_ConnectionlessCTE_TransmitParamCmd
  #define HCI_LE_ConnectionlessIQ_SampleEnableCmd _symrom_HCI_LE_ConnectionlessIQ_SampleEnableCmd
  #define HCI_LE_ConnUpdateCmd _symrom_HCI_LE_ConnUpdateCmd
  #define HCI_LE_CreateConnCancelCmd _symrom_HCI_LE_CreateConnCancelCmd
  #define HCI_LE_CreateConnCmd _symrom_HCI_LE_CreateConnCmd
  #define HCI_LE_EncryptCmd _symrom_HCI_LE_EncryptCmd
  #define HCI_LE_ExtendedCreateConnectionCmd _symrom_HCI_LE_ExtendedCreateConnectionCmd
  #define HCI_LE_LtkReqNegReplyCmd _symrom_HCI_LE_LtkReqNegReplyCmd
  #define HCI_LE_LtkReqReplyCmd _symrom_HCI_LE_LtkReqReplyCmd
  #define HCI_LE_PeriodicAdvertisingCreateSyncCancelCmd _symrom_HCI_LE_PeriodicAdvertisingCreateSyncCancelCmd
  #define HCI_LE_PeriodicAdvertisingCreateSyncCmd _symrom_HCI_LE_PeriodicAdvertisingCreateSyncCmd
  #define HCI_LE_PeriodicAdvertisingTerminateSyncCmd _symrom_HCI_LE_PeriodicAdvertisingTerminateSyncCmd
  #define HCI_LE_RandCmd _symrom_HCI_LE_RandCmd
  #define HCI_LE_READ_Anatenna_InfoCmd _symrom_HCI_LE_READ_Anatenna_InfoCmd
  #define HCI_LE_ReadAdvChanTxPowerCmd _symrom_HCI_LE_ReadAdvChanTxPowerCmd
  #define HCI_LE_ReadBufSizeCmd _symrom_HCI_LE_ReadBufSizeCmd
  #define HCI_LE_ReadChannelMapCmd _symrom_HCI_LE_ReadChannelMapCmd
  #define HCI_LE_ReadLocalSupportedFeaturesCmd _symrom_HCI_LE_ReadLocalSupportedFeaturesCmd
  #define HCI_LE_ReadMaxDataLengthCmd _symrom_HCI_LE_ReadMaxDataLengthCmd
  #define HCI_LE_ReadMaximumAdvDataLengthCmd _symrom_HCI_LE_ReadMaximumAdvDataLengthCmd
  #define HCI_LE_ReadNumberOfSupportAdvSetCmd _symrom_HCI_LE_ReadNumberOfSupportAdvSetCmd
  #define HCI_LE_ReadPhyMode _symrom_HCI_LE_ReadPhyMode
  #define HCI_LE_ReadRemoteUsedFeaturesCmd _symrom_HCI_LE_ReadRemoteUsedFeaturesCmd
  #define HCI_LE_ReadResolvingListSizeCmd _symrom_HCI_LE_ReadResolvingListSizeCmd
  #define HCI_LE_ReadSuggestedDefaultDataLengthCmd _symrom_HCI_LE_ReadSuggestedDefaultDataLengthCmd
  #define HCI_LE_ReadSupportedStatesCmd _symrom_HCI_LE_ReadSupportedStatesCmd
  #define HCI_LE_ReadWhiteListSizeCmd _symrom_HCI_LE_ReadWhiteListSizeCmd
  #define HCI_LE_ReceiverTestCmd _symrom_HCI_LE_ReceiverTestCmd
  #define HCI_LE_RemoveAdvSetCmd _symrom_HCI_LE_RemoveAdvSetCmd
  #define HCI_LE_RemoveResolvingListCmd _symrom_HCI_LE_RemoveResolvingListCmd
  #define HCI_LE_RemoveWhiteListCmd _symrom_HCI_LE_RemoveWhiteListCmd
  #define HCI_LE_Set_ConnectionCTE_ReceiveParamCmd _symrom_HCI_LE_Set_ConnectionCTE_ReceiveParamCmd
  #define HCI_LE_Set_ConnectionCTE_TransmitParamCmd _symrom_HCI_LE_Set_ConnectionCTE_TransmitParamCmd
  #define HCI_LE_SetAddressResolutionEnableCmd _symrom_HCI_LE_SetAddressResolutionEnableCmd
  #define HCI_LE_SetAdvDataCmd _symrom_HCI_LE_SetAdvDataCmd
  #define HCI_LE_SetAdvEnableCmd _symrom_HCI_LE_SetAdvEnableCmd
  #define HCI_LE_SetAdvParamCmd _symrom_HCI_LE_SetAdvParamCmd
  #define HCI_LE_SetDataLengthCmd _symrom_HCI_LE_SetDataLengthCmd
  #define HCI_LE_SetDefaultPhyMode _symrom_HCI_LE_SetDefaultPhyMode
  #define HCI_LE_SetEventMaskCmd _symrom_HCI_LE_SetEventMaskCmd
  #define HCI_LE_SetExtAdvDataCmd _symrom_HCI_LE_SetExtAdvDataCmd
  #define HCI_LE_SetExtAdvEnableCmd _symrom_HCI_LE_SetExtAdvEnableCmd
  #define HCI_LE_SetExtAdvParamCmd _symrom_HCI_LE_SetExtAdvParamCmd
  #define HCI_LE_SetExtAdvSetRandomAddressCmd _symrom_HCI_LE_SetExtAdvSetRandomAddressCmd
  #define HCI_LE_SetExtendedScanEnableCmd _symrom_HCI_LE_SetExtendedScanEnableCmd
  #define HCI_LE_SetExtendedScanParametersCmd _symrom_HCI_LE_SetExtendedScanParametersCmd
  #define HCI_LE_SetExtScanRspDataCmd _symrom_HCI_LE_SetExtScanRspDataCmd
  #define HCI_LE_SetHostChanClassificationCmd _symrom_HCI_LE_SetHostChanClassificationCmd
  #define HCI_LE_SetPeriodicAdvDataCmd _symrom_HCI_LE_SetPeriodicAdvDataCmd
  #define HCI_LE_SetPeriodicAdvEnableCmd _symrom_HCI_LE_SetPeriodicAdvEnableCmd
  #define HCI_LE_SetPeriodicAdvParameterCmd _symrom_HCI_LE_SetPeriodicAdvParameterCmd
  #define HCI_LE_SetPhyMode _symrom_HCI_LE_SetPhyMode
  #define HCI_LE_SetRandomAddressCmd _symrom_HCI_LE_SetRandomAddressCmd
  #define HCI_LE_SetResolvablePrivateAddressTimeoutCmd _symrom_HCI_LE_SetResolvablePrivateAddressTimeoutCmd
  #define HCI_LE_SetScanEnableCmd _symrom_HCI_LE_SetScanEnableCmd
  #define HCI_LE_SetScanParamCmd _symrom_HCI_LE_SetScanParamCmd
  #define HCI_LE_SetScanRspDataCmd _symrom_HCI_LE_SetScanRspDataCmd
  #define HCI_LE_StartEncyptCmd _symrom_HCI_LE_StartEncyptCmd
  #define HCI_LE_TestEndCmd _symrom_HCI_LE_TestEndCmd
  #define HCI_LE_TransmitterTestCmd _symrom_HCI_LE_TransmitterTestCmd
  #define HCI_LE_WriteSuggestedDefaultDataLengthCmd _symrom_HCI_LE_WriteSuggestedDefaultDataLengthCmd
  #define HCI_NumOfCompletedPacketsEvent _symrom_HCI_NumOfCompletedPacketsEvent
  #define HCI_ProcessEvent _symrom_HCI_ProcessEvent
  #define HCI_ReadBDADDRCmd _symrom_HCI_ReadBDADDRCmd
  #define HCI_ReadLocalSupportedCommandsCmd _symrom_HCI_ReadLocalSupportedCommandsCmd
  #define HCI_ReadLocalSupportedFeaturesCmd _symrom_HCI_ReadLocalSupportedFeaturesCmd
  #define HCI_ReadLocalVersionInfoCmd _symrom_HCI_ReadLocalVersionInfoCmd
  #define HCI_ReadRemoteVersionInfoCmd _symrom_HCI_ReadRemoteVersionInfoCmd
  #define HCI_ReadRssiCmd _symrom_HCI_ReadRssiCmd
  #define HCI_ReadTransmitPowerLevelCmd _symrom_HCI_ReadTransmitPowerLevelCmd
  #define HCI_ResetCmd _symrom_HCI_ResetCmd
  #define HCI_ReverseBytes _symrom_HCI_ReverseBytes
  #define HCI_SendCommandCompleteEvent _symrom_HCI_SendCommandCompleteEvent
  #define HCI_SendCommandStatusEvent _symrom_HCI_SendCommandStatusEvent
  #define HCI_SendControllerToHostEvent _symrom_HCI_SendControllerToHostEvent
  #define HCI_SendDataPkt _symrom_HCI_SendDataPkt
  #define HCI_SetControllerToHostFlowCtrlCmd _symrom_HCI_SetControllerToHostFlowCtrlCmd
  #define HCI_SetEventMaskCmd _symrom_HCI_SetEventMaskCmd
  #define HCI_SMPTaskRegister _symrom_HCI_SMPTaskRegister
  #define HCI_TestAppTaskRegister _symrom_HCI_TestAppTaskRegister
  #define HCI_ValidConnTimeParams _symrom_HCI_ValidConnTimeParams
  #define HCI_VendorSpecifcCommandCompleteEvent _symrom_HCI_VendorSpecifcCommandCompleteEvent
  /* #define hciCmdTable _symrom_hciCmdTable */
  #define hciCtrlCmdToken _symrom_hciCtrlCmdToken
  #define hciExtTaskID _symrom_hciExtTaskID
  #define hciGapTaskID _symrom_hciGapTaskID
  #define hciInitEventMasks _symrom_hciInitEventMasks
  #define hciL2capTaskID _symrom_hciL2capTaskID
  #define hciPTMenabled _symrom_hciPTMenabled
  #define hciSmpTaskID _symrom_hciSmpTaskID
  #define hciTaskID _symrom_hciTaskID
  #define hciTestTaskID _symrom_hciTestTaskID
  #define hclk_per_us _symrom_hclk_per_us
  #define hclk_per_us_shift _symrom_hclk_per_us_shift
  #define initInfo _symrom_initInfo
  #define ISR_entry_time _symrom_ISR_entry_time
  #define isSleepAllow _symrom_isSleepAllow
  #define isTimer1Running _symrom_isTimer1Running
  #define isTimer4Running _symrom_isTimer4Running
  #define jump_area_init _symrom_jump_area_init
  #define ll_add_adv_task _symrom_ll_add_adv_task
  #define ll_add_adv_task_periodic _symrom_ll_add_adv_task_periodic
  #define LL_AddResolvingListLDevice _symrom_LL_AddResolvingListLDevice
  #define ll_addTask _symrom_ll_addTask
  #define LL_AddWhiteListDevice _symrom_LL_AddWhiteListDevice
  #define ll_adptive_adj_next_time _symrom_ll_adptive_adj_next_time
  #define ll_adptive_smart_window _symrom_ll_adptive_smart_window
  #define ll_adv_scheduler _symrom_ll_adv_scheduler
  #define ll_adv_scheduler_periodic _symrom_ll_adv_scheduler_periodic
  #define LL_AdvReportCback _symrom_LL_AdvReportCback
  #define ll_allocAuxAdvTimeSlot _symrom_ll_allocAuxAdvTimeSlot
  #define ll_allocAuxAdvTimeSlot_prd _symrom_ll_allocAuxAdvTimeSlot_prd
  #define ll_CalcRandomAddr _symrom_ll_CalcRandomAddr
  #define LL_ChanMapUpdate _symrom_LL_ChanMapUpdate
  #define LL_ClearAdvSets _symrom_LL_ClearAdvSets
  #define LL_ClearResolvingList _symrom_LL_ClearResolvingList
  #define LL_ClearWhiteList _symrom_LL_ClearWhiteList
  #define LL_ConnActive _symrom_LL_ConnActive
  #define LL_Connection_CTE_Request_Enable _symrom_LL_Connection_CTE_Request_Enable
  #define LL_Connection_CTE_Response_Enable _symrom_LL_Connection_CTE_Response_Enable
  #define LL_ConnectionCompleteCback _symrom_LL_ConnectionCompleteCback
  #define LL_ConnectionIQReportCback _symrom_LL_ConnectionIQReportCback
  #define LL_ConnectionlessCTE_TransmitEnable _symrom_LL_ConnectionlessCTE_TransmitEnable
  #define LL_ConnectionlessCTE_TransmitParam _symrom_LL_ConnectionlessCTE_TransmitParam
  #define LL_ConnectionlessIQ_SampleEnable _symrom_LL_ConnectionlessIQ_SampleEnable
  #define LL_ConnectionlessIQReportCback _symrom_LL_ConnectionlessIQReportCback
  #define LL_ConnParamUpdateCback _symrom_LL_ConnParamUpdateCback
  #define LL_ConnUpdate _symrom_LL_ConnUpdate
  #define LL_CreateConn _symrom_LL_CreateConn
  #define LL_CreateConnCancel _symrom_LL_CreateConnCancel
  #define LL_CTE_Report_FailedCback _symrom_LL_CTE_Report_FailedCback
  #define LL_CtrlToHostFlowControl _symrom_LL_CtrlToHostFlowControl
  #define LL_DataLengthChangeCback _symrom_LL_DataLengthChangeCback
  #define ll_debug_output _symrom_ll_debug_output
  #define ll_delete_adv_task _symrom_ll_delete_adv_task
  #define ll_delete_adv_task_periodic _symrom_ll_delete_adv_task_periodic
  #define ll_deleteTask _symrom_ll_deleteTask
  #define LL_DirectTestEnd _symrom_LL_DirectTestEnd
  #define LL_DirectTestTxTest _symrom_LL_DirectTestTxTest
  #define LL_Disconnect _symrom_LL_Disconnect
  #define LL_DisconnectCback _symrom_LL_DisconnectCback
  #define LL_ENC_AES128_Encrypt _symrom_LL_ENC_AES128_Encrypt
  #define LL_ENC_Decrypt _symrom_LL_ENC_Decrypt
  #define LL_ENC_Encrypt _symrom_LL_ENC_Encrypt
  #define LL_ENC_GenDeviceIV _symrom_LL_ENC_GenDeviceIV
  #define LL_ENC_GenDeviceSKD _symrom_LL_ENC_GenDeviceSKD
  #define LL_ENC_GenerateNonce _symrom_LL_ENC_GenerateNonce
  #define LL_ENC_GeneratePseudoRandNum _symrom_LL_ENC_GeneratePseudoRandNum
  #define LL_ENC_GenerateTrueRandNum _symrom_LL_ENC_GenerateTrueRandNum
  #define LL_ENC_LoadKey _symrom_LL_ENC_LoadKey
  #define LL_ENC_ReverseBytes _symrom_LL_ENC_ReverseBytes
  #define LL_ENC_sm_ah _symrom_LL_ENC_sm_ah
  #define LL_EncChangeCback _symrom_LL_EncChangeCback
  #define LL_EncKeyRefreshCback _symrom_LL_EncKeyRefreshCback
  #define LL_EncLtkNegReply _symrom_LL_EncLtkNegReply
  #define LL_EncLtkReply _symrom_LL_EncLtkReply
  #define LL_EncLtkReqCback _symrom_LL_EncLtkReqCback
  #define LL_Encrypt _symrom_LL_Encrypt
  #define LL_evt_schedule _symrom_LL_evt_schedule
  #define ll_ext_adv_schedule_next_event _symrom_ll_ext_adv_schedule_next_event
  #define LL_EXT_AdvEventNotice _symrom_LL_EXT_AdvEventNotice
  #define LL_EXT_BuildRevision _symrom_LL_EXT_BuildRevision
  #define LL_EXT_ClkDivOnHalt _symrom_LL_EXT_ClkDivOnHalt
  #define LL_EXT_ConnEventNotice _symrom_LL_EXT_ConnEventNotice
  #define LL_EXT_DeclareNvUsage _symrom_LL_EXT_DeclareNvUsage
  #define LL_EXT_Decrypt _symrom_LL_EXT_Decrypt
  #define LL_EXT_DelaySleep _symrom_LL_EXT_DelaySleep
  #define LL_EXT_DisconnectImmed _symrom_LL_EXT_DisconnectImmed
  #define LL_EXT_EndModemTest _symrom_LL_EXT_EndModemTest
  #define LL_EXT_HaltDuringRf _symrom_LL_EXT_HaltDuringRf
  #define LL_EXT_Init_IQ_pBuff _symrom_LL_EXT_Init_IQ_pBuff
  #define ll_ext_init_schedule_next_event _symrom_ll_ext_init_schedule_next_event
  #define LL_EXT_MapPmIoPort _symrom_LL_EXT_MapPmIoPort
  #define LL_EXT_ModemHopTestTx _symrom_LL_EXT_ModemHopTestTx
  #define LL_EXT_ModemTestRx _symrom_LL_EXT_ModemTestRx
  #define LL_EXT_ModemTestTx _symrom_LL_EXT_ModemTestTx
  #define LL_EXT_NumComplPktsLimit _symrom_LL_EXT_NumComplPktsLimit
  #define LL_EXT_OnePacketPerEvent _symrom_LL_EXT_OnePacketPerEvent
  #define LL_EXT_OverlappedProcessing _symrom_LL_EXT_OverlappedProcessing
  #define LL_EXT_PacketErrorRate _symrom_LL_EXT_PacketErrorRate
  #define LL_EXT_PERbyChan _symrom_LL_EXT_PERbyChan
  #define LL_EXT_ResetSystem _symrom_LL_EXT_ResetSystem
  #define LL_EXT_SaveFreqTune _symrom_LL_EXT_SaveFreqTune
  #define ll_ext_scan_schedule_next_event _symrom_ll_ext_scan_schedule_next_event
  #define LL_EXT_SetBDADDR _symrom_LL_EXT_SetBDADDR
  #define LL_EXT_SetFastTxResponseTime _symrom_LL_EXT_SetFastTxResponseTime
  #define LL_EXT_SetFreqTune _symrom_LL_EXT_SetFreqTune
  #define LL_EXT_SetLocalSupportedFeatures _symrom_LL_EXT_SetLocalSupportedFeatures
  #define LL_EXT_SetMaxDtmTxPower _symrom_LL_EXT_SetMaxDtmTxPower
  #define LL_EXT_SetRxGain _symrom_LL_EXT_SetRxGain
  #define LL_EXT_SetSCA _symrom_LL_EXT_SetSCA
  #define LL_EXT_SetSlaveLatencyOverride _symrom_LL_EXT_SetSlaveLatencyOverride
  #define LL_EXT_SetTxPower _symrom_LL_EXT_SetTxPower
  #define LL_ExtAdvReportCback _symrom_LL_ExtAdvReportCback
  #define LL_extAdvTimerExpProcess _symrom_LL_extAdvTimerExpProcess
  #define LL_ExtendedCreateConnection _symrom_LL_ExtendedCreateConnection
  #define LL_extInitTimerExpProcess _symrom_LL_extInitTimerExpProcess
  #define LL_extScanTimerExpProcess _symrom_LL_extScanTimerExpProcess
  #define ll_generateExtAdvDid _symrom_ll_generateExtAdvDid
  #define ll_generateTxBuffer _symrom_ll_generateTxBuffer
  #define ll_get_next_active_conn _symrom_ll_get_next_active_conn
  #define ll_get_next_timer _symrom_ll_get_next_timer
  #define ll_getFirstAdvChn _symrom_ll_getFirstAdvChn
  #define ll_getRPAListEntry _symrom_ll_getRPAListEntry
  #define ll_hw_clr_irq _symrom_ll_hw_clr_irq
  #define ll_hw_config _symrom_ll_hw_config
  #define ll_hw_get_anchor _symrom_ll_hw_get_anchor
  #define ll_hw_get_fsm_status _symrom_ll_hw_get_fsm_status
  #define ll_hw_get_iq_RawSample _symrom_ll_hw_get_iq_RawSample
  #define ll_hw_get_irq_status _symrom_ll_hw_get_irq_status
  #define ll_hw_get_last_ack _symrom_ll_hw_get_last_ack
  #define ll_hw_get_loop_cycle _symrom_ll_hw_get_loop_cycle
  #define ll_hw_get_loop_time _symrom_ll_hw_get_loop_time
  #define ll_hw_get_nAck _symrom_ll_hw_get_nAck
  #define ll_hw_get_rfifo_depth _symrom_ll_hw_get_rfifo_depth
  #define ll_hw_get_rfifo_info _symrom_ll_hw_get_rfifo_info
  #define ll_hw_get_rxPkt_CrcErr_num _symrom_ll_hw_get_rxPkt_CrcErr_num
  #define ll_hw_get_rxPkt_CrcOk_num _symrom_ll_hw_get_rxPkt_CrcOk_num
  #define ll_hw_get_rxPkt_num _symrom_ll_hw_get_rxPkt_num
  #define ll_hw_get_rxPkt_stats _symrom_ll_hw_get_rxPkt_stats
  #define ll_hw_get_rxPkt_Total_num _symrom_ll_hw_get_rxPkt_Total_num
  #define ll_hw_get_snNesn _symrom_ll_hw_get_snNesn
  #define ll_hw_get_tfifo_info _symrom_ll_hw_get_tfifo_info
  #define ll_hw_get_tfifo_wrptr _symrom_ll_hw_get_tfifo_wrptr
  #define ll_hw_get_tr_mode _symrom_ll_hw_get_tr_mode
  #define ll_hw_get_txAck _symrom_ll_hw_get_txAck
  #define ll_hw_go _symrom_ll_hw_go
  #define ll_hw_ign_rfifo _symrom_ll_hw_ign_rfifo
  #define ll_hw_process_RTO _symrom_ll_hw_process_RTO
  #define ll_hw_read_rfifo _symrom_ll_hw_read_rfifo
  #define ll_hw_read_rfifo_pplus _symrom_ll_hw_read_rfifo_pplus
  #define ll_hw_read_rfifo_zb _symrom_ll_hw_read_rfifo_zb
  #define ll_hw_read_tfifo_packet _symrom_ll_hw_read_tfifo_packet
  #define ll_hw_read_tfifo_rtlp _symrom_ll_hw_read_tfifo_rtlp
  #define ll_hw_rst_rfifo _symrom_ll_hw_rst_rfifo
  #define ll_hw_rst_tfifo _symrom_ll_hw_rst_tfifo
  #define ll_hw_set_ant_pattern _symrom_ll_hw_set_ant_pattern
  #define ll_hw_set_ant_switch_mode _symrom_ll_hw_set_ant_switch_mode
  #define ll_hw_set_ant_switch_timing _symrom_ll_hw_set_ant_switch_timing
  #define ll_hw_set_crc_fmt _symrom_ll_hw_set_crc_fmt
  #define ll_hw_set_cte_rxSupp _symrom_ll_hw_set_cte_rxSupp
  #define ll_hw_set_cte_txSupp _symrom_ll_hw_set_cte_txSupp
  #define ll_hw_set_empty_head _symrom_ll_hw_set_empty_head
  #define ll_hw_set_irq _symrom_ll_hw_set_irq
  #define ll_hw_set_loop_nack_num _symrom_ll_hw_set_loop_nack_num
  #define ll_hw_set_loop_timeout _symrom_ll_hw_set_loop_timeout
  #define ll_hw_set_pplus_pktfmt _symrom_ll_hw_set_pplus_pktfmt
  #define ll_hw_set_rtlp _symrom_ll_hw_set_rtlp
  #define ll_hw_set_rtlp_1st _symrom_ll_hw_set_rtlp_1st
  #define ll_hw_set_rtx _symrom_ll_hw_set_rtx
  #define ll_hw_set_rx_timeout _symrom_ll_hw_set_rx_timeout
  #define ll_hw_set_rx_timeout_1st _symrom_ll_hw_set_rx_timeout_1st
  #define ll_hw_set_rx_tx_interval _symrom_ll_hw_set_rx_tx_interval
  #define ll_hw_set_srx _symrom_ll_hw_set_srx
  #define ll_hw_set_stx _symrom_ll_hw_set_stx
  #define ll_hw_set_tfifo_space _symrom_ll_hw_set_tfifo_space
  #define ll_hw_set_timing _symrom_ll_hw_set_timing
  #define ll_hw_set_trlp _symrom_ll_hw_set_trlp
  #define ll_hw_set_trx _symrom_ll_hw_set_trx
  #define ll_hw_set_trx_settle _symrom_ll_hw_set_trx_settle
  #define ll_hw_set_tx_rx_interval _symrom_ll_hw_set_tx_rx_interval
  #define ll_hw_set_tx_rx_release _symrom_ll_hw_set_tx_rx_release
  #define ll_hw_trigger _symrom_ll_hw_trigger
  #define ll_hw_trx_settle_config _symrom_ll_hw_trx_settle_config
  #define ll_hw_tx2rx_timing_config _symrom_ll_hw_tx2rx_timing_config
  #define ll_hw_update _symrom_ll_hw_update
  #define ll_hw_update_rtlp_mode _symrom_ll_hw_update_rtlp_mode
  #define ll_hw_update_trlp_mode _symrom_ll_hw_update_trlp_mode
  #define ll_hw_write_tfifo _symrom_ll_hw_write_tfifo
  #define LL_Init _symrom_LL_Init
  #define LL_InitConnectContext _symrom_LL_InitConnectContext
  #define LL_InitExtendedAdv _symrom_LL_InitExtendedAdv
  #define LL_InitExtendedScan _symrom_LL_InitExtendedScan
  #define LL_InitPeriodicAdv _symrom_LL_InitPeriodicAdv
  #define LL_IRQHandler _symrom_LL_IRQHandler
  #define ll_isAddrInWhiteList _symrom_ll_isAddrInWhiteList
  #define ll_isFirstAdvChn _symrom_ll_isFirstAdvChn
  #define LL_master_conn_event _symrom_LL_master_conn_event
  #define LL_NumEmptyWlEntries _symrom_LL_NumEmptyWlEntries
  #define ll_parseExtHeader _symrom_ll_parseExtHeader
  #define LL_PeriodicAdvertisingCreateSync _symrom_LL_PeriodicAdvertisingCreateSync
  #define LL_PeriodicAdvertisingCreateSyncCancel _symrom_LL_PeriodicAdvertisingCreateSyncCancel
  #define LL_PeriodicAdvertisingTerminateSync _symrom_LL_PeriodicAdvertisingTerminateSync
  #define LL_PhyUpdate _symrom_LL_PhyUpdate
  #define LL_PhyUpdateCompleteCback _symrom_LL_PhyUpdateCompleteCback
  #define LL_PLUS_AdvDataFilterCBack _symrom_LL_PLUS_AdvDataFilterCBack
  #define LL_PLUS_DisableSlaveLatency _symrom_LL_PLUS_DisableSlaveLatency
  #define LL_PLUS_EnableSlaveLatency _symrom_LL_PLUS_EnableSlaveLatency
  #define LL_PLUS_GetAdvDataExtendData _symrom_LL_PLUS_GetAdvDataExtendData
  #define LL_PLUS_GetScanerAddr _symrom_LL_PLUS_GetScanerAddr
  #define LL_PLUS_GetScanRequestExtendData _symrom_LL_PLUS_GetScanRequestExtendData
  #define LL_PLUS_PerStasReadByChn _symrom_LL_PLUS_PerStasReadByChn
  #define LL_PLUS_PerStats_Init _symrom_LL_PLUS_PerStats_Init
  #define LL_PLUS_PerStatsReset _symrom_LL_PLUS_PerStatsReset
  #define LL_PLUS_ScanRequestFilterCBack _symrom_LL_PLUS_ScanRequestFilterCBack
  #define LL_PLUS_SetAdvDataFilterCB _symrom_LL_PLUS_SetAdvDataFilterCB
  #define LL_PLUS_SetScanRequestData _symrom_LL_PLUS_SetScanRequestData
  #define LL_PLUS_SetScanRequestFilterCB _symrom_LL_PLUS_SetScanRequestFilterCB
  #define LL_PLUS_SetScanRsqData _symrom_LL_PLUS_SetScanRsqData
  #define LL_PLUS_SetScanRsqDataByIndex _symrom_LL_PLUS_SetScanRsqDataByIndex
  #define ll_prd_adv_schedule_next_event _symrom_ll_prd_adv_schedule_next_event
  #define ll_prd_scan_schedule_next_event _symrom_ll_prd_scan_schedule_next_event
  #define LL_PrdAdvReportCback _symrom_LL_PrdAdvReportCback
  #define LL_PrdAdvSyncEstablishedCback _symrom_LL_PrdAdvSyncEstablishedCback
  #define LL_PrdAdvSyncLostCback _symrom_LL_PrdAdvSyncLostCback
  #define LL_prdAdvTimerExpProcess _symrom_LL_prdAdvTimerExpProcess
  #define LL_prdScanTimerExpProcess _symrom_LL_prdScanTimerExpProcess
  #define ll_processBasicIRQ _symrom_ll_processBasicIRQ
  #define LL_ProcessEvent _symrom_LL_ProcessEvent
  /* #define ll_processExtAdvIRQ _symrom_ll_processExtAdvIRQ
   * #define ll_processExtInitIRQ _symrom_ll_processExtInitIRQ
   * #define ll_processExtScanIRQ _symrom_ll_processExtScanIRQ
   */
  #define ll_processMissMasterEvt _symrom_ll_processMissMasterEvt
  #define ll_processMissSlaveEvt _symrom_ll_processMissSlaveEvt
  #define ll_processPrdAdvIRQ _symrom_ll_processPrdAdvIRQ
  #define ll_processPrdScanIRQ _symrom_ll_processPrdScanIRQ
  #define LL_PseudoRand _symrom_LL_PseudoRand
  #define LL_Rand _symrom_LL_Rand
  #define LL_RandCback _symrom_LL_RandCback
  #define LL_READ_Anatenna_Info _symrom_LL_READ_Anatenna_Info
  #define ll_read_rxfifo _symrom_ll_read_rxfifo
  #define LL_ReadAdvChanTxPower _symrom_LL_ReadAdvChanTxPower
  #define LL_ReadBDADDR _symrom_LL_ReadBDADDR
  #define LL_ReadCarrSens _symrom_LL_ReadCarrSens
  #define LL_ReadChanMap _symrom_LL_ReadChanMap
  #define LL_ReadFoff _symrom_LL_ReadFoff
  #define ll_readLocalIRK _symrom_ll_readLocalIRK
  #define LL_ReadLocalSupportedFeatures _symrom_LL_ReadLocalSupportedFeatures
  #define LL_ReadLocalVersionInfo _symrom_LL_ReadLocalVersionInfo
  #define LL_ReadMaximumAdvDataLength _symrom_LL_ReadMaximumAdvDataLength
  #define LL_ReadNumberOfSupportAdvSet _symrom_LL_ReadNumberOfSupportAdvSet
  #define LL_ReadRemoteUsedFeatures _symrom_LL_ReadRemoteUsedFeatures
  #define LL_ReadRemoteUsedFeaturesCompleteCback _symrom_LL_ReadRemoteUsedFeaturesCompleteCback
  #define LL_ReadRemoteVersionInfo _symrom_LL_ReadRemoteVersionInfo
  #define LL_ReadRemoteVersionInfoCback _symrom_LL_ReadRemoteVersionInfoCback
  #define LL_ReadResolvingListSize _symrom_LL_ReadResolvingListSize
  #define LL_ReadRssi _symrom_LL_ReadRssi
  #define LL_ReadSupportedStates _symrom_LL_ReadSupportedStates
  #define LL_ReadTxPowerLevel _symrom_LL_ReadTxPowerLevel
  #define LL_ReadWlSize _symrom_LL_ReadWlSize
  #define ll_remain_time _symrom_ll_remain_time
  #define LL_RemoveAdvSet _symrom_LL_RemoveAdvSet
  #define LL_RemoveResolvingListDevice _symrom_LL_RemoveResolvingListDevice
  #define LL_RemoveWhiteListDevice _symrom_LL_RemoveWhiteListDevice
  #define LL_Reset _symrom_LL_Reset
  #define ll_ResolveRandomAddrs _symrom_ll_ResolveRandomAddrs
  #define LL_RX_bm_alloc _symrom_LL_RX_bm_alloc
  #define LL_RxDataCompleteCback _symrom_LL_RxDataCompleteCback
  #define ll_schedule_next_event _symrom_ll_schedule_next_event
  #define ll_scheduler _symrom_ll_scheduler
  #define LL_Set_ConnectionCTE_ReceiveParam _symrom_LL_Set_ConnectionCTE_ReceiveParam
  #define LL_Set_ConnectionCTE_TransmitParam _symrom_LL_Set_ConnectionCTE_TransmitParam
  #define LL_set_default_conn_params _symrom_LL_set_default_conn_params
  #define LL_SetAddressResolutionEnable _symrom_LL_SetAddressResolutionEnable
  #define LL_SetAdvControl _symrom_LL_SetAdvControl
  #define LL_SetAdvData _symrom_LL_SetAdvData
  #define LL_SetAdvParam _symrom_LL_SetAdvParam
  #define LL_SetDataLengh _symrom_LL_SetDataLengh
  #define LL_SetDefaultPhyMode _symrom_LL_SetDefaultPhyMode
  /* #define LL_SetExtAdvData _symrom_LL_SetExtAdvData
   * #define LL_SetExtAdvEnable _symrom_LL_SetExtAdvEnable
   * #define LL_SetExtAdvParam _symrom_LL_SetExtAdvParam
   */
  #define LL_SetExtAdvSetRandomAddress _symrom_LL_SetExtAdvSetRandomAddress
  #define LL_SetExtendedScanEnable _symrom_LL_SetExtendedScanEnable
  #define LL_SetExtendedScanParameters _symrom_LL_SetExtendedScanParameters
  #define LL_SetExtScanRspData _symrom_LL_SetExtScanRspData
  #define LL_SetPeriodicAdvData _symrom_LL_SetPeriodicAdvData
  #define LL_SetPeriodicAdvEnable _symrom_LL_SetPeriodicAdvEnable
  #define LL_SetPeriodicAdvParameter _symrom_LL_SetPeriodicAdvParameter
  #define LL_SetPhyMode _symrom_LL_SetPhyMode
  #define LL_SetRandomAddress _symrom_LL_SetRandomAddress
  #define LL_SetResolvablePrivateAddressTimeout _symrom_LL_SetResolvablePrivateAddressTimeout
  #define LL_SetScanControl _symrom_LL_SetScanControl
  #define LL_SetScanParam _symrom_LL_SetScanParam
  #define LL_SetScanRspData _symrom_LL_SetScanRspData
  #define LL_SetTxPowerLevel _symrom_LL_SetTxPowerLevel
  #define LL_slave_conn_event _symrom_LL_slave_conn_event
  #define LL_StartEncrypt _symrom_LL_StartEncrypt
  #define LL_TaskID _symrom_LL_TaskID
  #define LL_TX_bm_alloc _symrom_LL_TX_bm_alloc
  #define LL_TxData _symrom_LL_TxData
  #define ll_updateAuxAdvTimeSlot _symrom_ll_updateAuxAdvTimeSlot
  #define ll_updateExtAdvRemainderTime _symrom_ll_updateExtAdvRemainderTime
  #define LL_WriteSuggestedDefaultDataLength _symrom_LL_WriteSuggestedDefaultDataLength
  #define ll24BitTimeCompare _symrom_ll24BitTimeCompare
  #define llAdjSlaveLatencyValue _symrom_llAdjSlaveLatencyValue
  #define llAllocateSyncHandle _symrom_llAllocateSyncHandle
  #define llAllocConnId _symrom_llAllocConnId
  #define llAtLeastTwoChans _symrom_llAtLeastTwoChans
  #define llCalcMaxScanTime _symrom_llCalcMaxScanTime
  #define llCalcScaFactor _symrom_llCalcScaFactor
  #define llCalcTimerDrift _symrom_llCalcTimerDrift
  #define llCheckForLstoDuringSL _symrom_llCheckForLstoDuringSL
  #define llCheckWhiteListUsage _symrom_llCheckWhiteListUsage
  #define llConnCleanup _symrom_llConnCleanup
  #define llConnTerminate _symrom_llConnTerminate
  #define llConnTerminate0 _symrom_llConnTerminate0
  #define llConvertCtrlProcTimeoutToEvent _symrom_llConvertCtrlProcTimeoutToEvent
  #define llConvertLstoToEvent _symrom_llConvertLstoToEvent
  #define llCurrentScanChn _symrom_llCurrentScanChn
  #define llDeleteSyncHandle _symrom_llDeleteSyncHandle
  #define llDequeueCtrlPkt _symrom_llDequeueCtrlPkt
  #define llDequeueDataQ _symrom_llDequeueDataQ
  #define llEnqueueCtrlPkt _symrom_llEnqueueCtrlPkt
  #define llEnqueueDataQ _symrom_llEnqueueDataQ
  #define llEqAlreadyValidAddr _symrom_llEqAlreadyValidAddr
  #define llEqSynchWord _symrom_llEqSynchWord
  #define llEqualBytes _symrom_llEqualBytes
  #define llEventDelta _symrom_llEventDelta
  #define llEventInRange _symrom_llEventInRange
  #define llGenerateCRC _symrom_llGenerateCRC
  #define llGenerateValidAccessAddr _symrom_llGenerateValidAccessAddr
  #define llGetNextAdvChn _symrom_llGetNextAdvChn
  #define llGetNextAuxAdvChn _symrom_llGetNextAuxAdvChn
  #define llGetNextDataChan _symrom_llGetNextDataChan
  #define llGetNextDataChanCSA2 _symrom_llGetNextDataChanCSA2
  #define llGtSixConsecZerosOrOnes _symrom_llGtSixConsecZerosOrOnes
  #define llGtTwentyFourTransitions _symrom_llGtTwentyFourTransitions
  #define llInitFeatureSet _symrom_llInitFeatureSet
  #define llInitFeatureSet2MPHY _symrom_llInitFeatureSet2MPHY
  #define llInitFeatureSetCodedPHY _symrom_llInitFeatureSetCodedPHY
  #define llInitFeatureSetDLE _symrom_llInitFeatureSetDLE
  #define llLtTwoChangesInLastSixBits _symrom_llLtTwoChangesInLastSixBits
  #define llMasterEvt_TaskEndOk _symrom_llMasterEvt_TaskEndOk
  #define llMemCopyDst _symrom_llMemCopyDst
  #define llMemCopySrc _symrom_llMemCopySrc
  #define llOneBitSynchWordDiffer _symrom_llOneBitSynchWordDiffer
  #define llPduLengthManagmentReset _symrom_llPduLengthManagmentReset
  #define llPduLengthUpdate _symrom_llPduLengthUpdate
  #define llPendingUpdateParam _symrom_llPendingUpdateParam
  #define llPhyModeCtrlReset _symrom_llPhyModeCtrlReset
  #define llPhyModeCtrlUpdateNotify _symrom_llPhyModeCtrlUpdateNotify
  #define llProcessChanMap _symrom_llProcessChanMap
  #define llProcessMasterControlPacket _symrom_llProcessMasterControlPacket
  #define llProcessMasterControlProcedures _symrom_llProcessMasterControlProcedures
  #define llProcessRxData _symrom_llProcessRxData
  #define llProcessSlaveControlPacket _symrom_llProcessSlaveControlPacket
  #define llProcessSlaveControlProcedures _symrom_llProcessSlaveControlProcedures
  #define llProcessTxData _symrom_llProcessTxData
  #define llReleaseAllConnId _symrom_llReleaseAllConnId
  #define llReleaseConnId _symrom_llReleaseConnId
  #define llReplaceCtrlPkt _symrom_llReplaceCtrlPkt
  #define llResetConnId _symrom_llResetConnId
  #define llResetRfCounters _symrom_llResetRfCounters
  #define llScanT1 _symrom_llScanT1
  #define llScanTime _symrom_llScanTime
  #define llSecAdvAllow _symrom_llSecAdvAllow
  #define llSecondaryState _symrom_llSecondaryState
  #define llSetNextDataChan _symrom_llSetNextDataChan
  #define llSetNextPhyMode _symrom_llSetNextPhyMode
  #define llSetupAdv _symrom_llSetupAdv
  #define llSetupAdvExtIndPDU _symrom_llSetupAdvExtIndPDU
  #define llSetupAuxAdvIndPDU _symrom_llSetupAuxAdvIndPDU
  #define llSetupAuxChainIndPDU _symrom_llSetupAuxChainIndPDU
  #define llSetupAuxConnectReqPDU _symrom_llSetupAuxConnectReqPDU
  #define llSetupAuxConnectRspPDU _symrom_llSetupAuxConnectRspPDU
  #define llSetupAuxScanRspPDU _symrom_llSetupAuxScanRspPDU
  #define llSetupAuxSyncIndPDU _symrom_llSetupAuxSyncIndPDU
  #define llSetupConn _symrom_llSetupConn
  #define llSetupCTEReq _symrom_llSetupCTEReq
  #define llSetupCTERsp _symrom_llSetupCTERsp
  #define llSetupDataLenghtReq _symrom_llSetupDataLenghtReq
  #define llSetupDataLenghtRsp _symrom_llSetupDataLenghtRsp
  #define llSetupDirectedAdvEvt _symrom_llSetupDirectedAdvEvt
  #define llSetupEncReq _symrom_llSetupEncReq
  #define llSetupEncRsp _symrom_llSetupEncRsp
  #define llSetupExtAdvEvent _symrom_llSetupExtAdvEvent
  /* #define llSetupExtInit _symrom_llSetupExtInit
   * #define llSetupExtScan _symrom_llSetupExtScan
   */
  #define llSetupFeatureSetReq _symrom_llSetupFeatureSetReq
  #define llSetupFeatureSetRsp _symrom_llSetupFeatureSetRsp
  #define llSetupInit _symrom_llSetupInit
  #define llSetupNextMasterEvent _symrom_llSetupNextMasterEvent
  #define llSetupNextSlaveEvent _symrom_llSetupNextSlaveEvent
  #define llSetupNonConnectableAdvEvt _symrom_llSetupNonConnectableAdvEvt
  #define llSetupPauseEncReq _symrom_llSetupPauseEncReq
  #define llSetupPauseEncRsp _symrom_llSetupPauseEncRsp
  #define llSetupPhyReq _symrom_llSetupPhyReq
  #define llSetupPhyRsp _symrom_llSetupPhyRsp
  #define llSetupPhyUpdateInd _symrom_llSetupPhyUpdateInd
  #define llSetupPrdAdvEvent _symrom_llSetupPrdAdvEvent
  /* #define llSetupPrdScan _symrom_llSetupPrdScan */
  #define llSetupRejectExtInd _symrom_llSetupRejectExtInd
  #define llSetupRejectInd _symrom_llSetupRejectInd
  #define llSetupScan _symrom_llSetupScan
  #define llSetupScanInit _symrom_llSetupScanInit
  #define llSetupScannableAdvEvt _symrom_llSetupScannableAdvEvt
  #define llSetupSecAdvEvt _symrom_llSetupSecAdvEvt
  #define llSetupSecConnectableAdvEvt _symrom_llSetupSecConnectableAdvEvt
  #define llSetupSecInit _symrom_llSetupSecInit
  #define llSetupSecNonConnectableAdvEvt _symrom_llSetupSecNonConnectableAdvEvt
  #define llSetupSecScan _symrom_llSetupSecScan
  #define llSetupSecScannableAdvEvt _symrom_llSetupSecScannableAdvEvt
  #define llSetupStartEncReq _symrom_llSetupStartEncReq
  #define llSetupStartEncRsp _symrom_llSetupStartEncRsp
  #define llSetupSyncInfo _symrom_llSetupSyncInfo
  #define llSetupTermInd _symrom_llSetupTermInd
  #define llSetupUndirectedAdvEvt _symrom_llSetupUndirectedAdvEvt
  #define llSetupUnknownRsp _symrom_llSetupUnknownRsp
  #define llSetupUpdateChanReq _symrom_llSetupUpdateChanReq
  #define llSetupUpdateParamReq _symrom_llSetupUpdateParamReq
  #define llSetupVersionIndReq _symrom_llSetupVersionIndReq
  #define llSlaveEvt_TaskAbort _symrom_llSlaveEvt_TaskAbort
  #define llSlaveEvt_TaskEndOk _symrom_llSlaveEvt_TaskEndOk
  #define llState _symrom_llState
  #define llTaskState _symrom_llTaskState
  #define llTrxNumAdaptiveConfig _symrom_llTrxNumAdaptiveConfig
  #define llValidAccessAddr _symrom_llValidAccessAddr
  #define llWaitingIrq _symrom_llWaitingIrq
  #define llWaitUs _symrom_llWaitUs
  #define llWriteTxData _symrom_llWriteTxData
  #define log_clr_putc _symrom_log_clr_putc
  #define log_debug_level _symrom_log_debug_level
  #define log_get_debug_level _symrom_log_get_debug_level
  #define log_printf _symrom_log_printf
  #define log_set_putc _symrom_log_set_putc
  #define log_vsprintf _symrom_log_vsprintf
  #define move_to_master_function _symrom_move_to_master_function
  #define move_to_slave_function _symrom_move_to_slave_function
  #define NMI_Handler _symrom_NMI_Handler
  #define numComplPkts _symrom_numComplPkts
  #define numComplPktsLimit _symrom_numComplPktsLimit
  #define numHostBufs _symrom_numHostBufs
  #define osal_bm_adjust_header _symrom_osal_bm_adjust_header
  #define osal_bm_adjust_tail _symrom_osal_bm_adjust_tail
  #define osal_bm_alloc _symrom_osal_bm_alloc
  #define osal_bm_free _symrom_osal_bm_free
  #define osal_buffer_uint24 _symrom_osal_buffer_uint24
  #define osal_buffer_uint32 _symrom_osal_buffer_uint32
  #define osal_build_uint16 _symrom_osal_build_uint16
  #define osal_build_uint32 _symrom_osal_build_uint32
  #define osal_CbTimerInit _symrom_osal_CbTimerInit
  #define osal_CbTimerProcessEvent _symrom_osal_CbTimerProcessEvent
  #define osal_CbTimerStart _symrom_osal_CbTimerStart
  #define osal_CbTimerStop _symrom_osal_CbTimerStop
  #define osal_CbTimerUpdate _symrom_osal_CbTimerUpdate
  #define osal_clear_event _symrom_osal_clear_event
  #define osal_ConvertUTCSecs _symrom_osal_ConvertUTCSecs
  #define osal_ConvertUTCTime _symrom_osal_ConvertUTCTime
  #define osal_get_timeoutEx _symrom_osal_get_timeoutEx
  #define osal_getClock _symrom_osal_getClock
  #define osal_GetSystemClock _symrom_osal_GetSystemClock
  #define osal_init_system _symrom_osal_init_system
  #define osal_isbufset _symrom_osal_isbufset
  #define osal_mem_alloc _symrom_osal_mem_alloc
  #define osal_mem_free _symrom_osal_mem_free
  #define osal_mem_init _symrom_osal_mem_init
  #define osal_mem_kick _symrom_osal_mem_kick
  #define osal_mem_set_heap _symrom_osal_mem_set_heap
  #define osal_memcmp _symrom_osal_memcmp
  #define osal_memcpy _symrom_osal_memcpy
  #define osal_memdup _symrom_osal_memdup
  #define osal_memset _symrom_osal_memset
  #define osal_msg_allocate _symrom_osal_msg_allocate
  #define osal_msg_deallocate _symrom_osal_msg_deallocate
  #define osal_msg_dequeue _symrom_osal_msg_dequeue
  #define osal_msg_enqueue _symrom_osal_msg_enqueue
  #define osal_msg_enqueue_max _symrom_osal_msg_enqueue_max
  #define osal_msg_extract _symrom_osal_msg_extract
  #define osal_msg_find _symrom_osal_msg_find
  #define osal_msg_push _symrom_osal_msg_push
  #define osal_msg_push_front _symrom_osal_msg_push_front
  #define osal_msg_receive _symrom_osal_msg_receive
  #define osal_msg_send _symrom_osal_msg_send
  #define osal_next_timeout _symrom_osal_next_timeout
  #define osal_pwrmgr_device _symrom_osal_pwrmgr_device
  #define osal_pwrmgr_init _symrom_osal_pwrmgr_init
  #define osal_pwrmgr_powerconserve _symrom_osal_pwrmgr_powerconserve
  #define osal_pwrmgr_task_state _symrom_osal_pwrmgr_task_state
  #define osal_qHead _symrom_osal_qHead
  #define osal_rand _symrom_osal_rand
  #define osal_revmemcpy _symrom_osal_revmemcpy
  #define osal_run_system _symrom_osal_run_system
  #define osal_self _symrom_osal_self
  #define osal_set_event _symrom_osal_set_event
  #define osal_setClock _symrom_osal_setClock
  #define osal_start_reload_timer _symrom_osal_start_reload_timer
  #define osal_start_system _symrom_osal_start_system
  #define osal_start_timerEx _symrom_osal_start_timerEx
  #define osal_stop_timerEx _symrom_osal_stop_timerEx
  #define osal_strlen _symrom_osal_strlen
  #define osal_sys_tick _symrom_osal_sys_tick
  #define osal_timer_num_active _symrom_osal_timer_num_active
  #define OSAL_timeSeconds _symrom_OSAL_timeSeconds
  #define osalAddTimer _symrom_osalAddTimer
  #define osalDeleteTimer _symrom_osalDeleteTimer
  #define osalFindTimer _symrom_osalFindTimer
  #define osalTimerInit _symrom_osalTimerInit
  #define osalTimerUpdate _symrom_osalTimerUpdate
  #define osalTimeUpdate _symrom_osalTimeUpdate
  #define osalTimeUpdate1 _symrom_osalTimeUpdate1
  #define ownPublicAddr _symrom_ownPublicAddr
  #define p_perStatsByChan _symrom_p_perStatsByChan
  #define peerInfo _symrom_peerInfo
  #define PendSV_Handler _symrom_PendSV_Handler
  #define pHciEvtMask _symrom_pHciEvtMask
  #define phy_sec_app_key _symrom_phy_sec_app_key
  #define phy_sec_decrypt _symrom_phy_sec_decrypt
  #define phy_sec_efuse_lock _symrom_phy_sec_efuse_lock
  #define phy_sec_encrypt _symrom_phy_sec_encrypt
  #define phy_sec_init _symrom_phy_sec_init
  #define phy_sec_key_valid _symrom_phy_sec_key_valid
  #define prog_process_data _symrom_prog_process_data
  #define prog_uart_command _symrom_prog_uart_command
  #define prog_uart_fct_command _symrom_prog_uart_fct_command
  #define prog_uart_handle _symrom_prog_uart_handle
  #define pwrmgr_attribute _symrom_pwrmgr_attribute
  #define read_current_fine_time _symrom_read_current_fine_time
  #define read_ll_adv_remainder_time _symrom_read_ll_adv_remainder_time
  #define read_LL_remainder_time _symrom_read_LL_remainder_time
  #define receive_timeout_flag _symrom_receive_timeout_flag
  #define reset_conn_buf _symrom_reset_conn_buf
  /* #define rf_calibrate _symrom_rf_calibrate
   * #define rf_init _symrom_rf_init
   * #define rf_phy_ana_cfg _symrom_rf_phy_ana_cfg
   * #define rf_phy_bb_cfg _symrom_rf_phy_bb_cfg
   * #define rf_phy_change_cfg _symrom_rf_phy_change_cfg
   * #define rf_phy_direct_test_ate _symrom_rf_phy_direct_test_ate
   * #define rf_phy_get_pktFoot _symrom_rf_phy_get_pktFoot
   * #define rf_phy_ini _symrom_rf_phy_ini
   * #define rf_phy_set_txPower _symrom_rf_phy_set_txPower
   * #define rf_rxDcoc_cfg _symrom_rf_rxDcoc_cfg
   * #define rf_tp_cal _symrom_rf_tp_cal
   * #define rf_tpCal_cfg _symrom_rf_tpCal_cfg
   * #define rf_tpCal_cfg_avg _symrom_rf_tpCal_cfg_avg
   * #define rf_tpCal_gen_cap_arrary _symrom_rf_tpCal_gen_cap_arrary
   */
  #define rfCounters _symrom_rfCounters
  #define rom_board_init _symrom_rom_board_init
  #define rtc_clear _symrom_rtc_clear
  #define rtc_config_prescale _symrom_rtc_config_prescale
  #define rtc_get_counter _symrom_rtc_get_counter
  #define rtc_mod_value _symrom_rtc_mod_value
  #define rtc_start _symrom_rtc_start
  #define rtc_stop _symrom_rtc_stop
  #define rxFifoFlowCtrl _symrom_rxFifoFlowCtrl
  #define s_prog_time_save _symrom_s_prog_time_save
  #define s_prog_timeout _symrom_s_prog_timeout
  #define s_rom_debug_level _symrom_s_rom_debug_level
  #define s_spif_ctx _symrom_s_spif_ctx
  #define SCA _symrom_SCA
  #define scanInfo _symrom_scanInfo
  #define scanSyncInfo _symrom_scanSyncInfo
  #define set_access_address _symrom_set_access_address
  #define set_channel _symrom_set_channel
  #define set_crc_seed _symrom_set_crc_seed
  #define set_gpio_pull_down_ate _symrom_set_gpio_pull_down_ate
  #define set_gpio_pull_up_ate _symrom_set_gpio_pull_up_ate
  #define set_int _symrom_set_int
  #define set_max_length _symrom_set_max_length
  #define set_sleep_flag _symrom_set_sleep_flag
  #define set_timer _symrom_set_timer
  #define set_whiten_seed _symrom_set_whiten_seed
  #define setSleepMode _symrom_setSleepMode
  #define slave_conn_event_recv_delay _symrom_slave_conn_event_recv_delay
  #define sleep_flag _symrom_sleep_flag
  #define spif_cmd _symrom_spif_cmd
  #define spif_erase_all _symrom_spif_erase_all
  #define spif_erase_block64 _symrom_spif_erase_block64
  #define spif_erase_chip _symrom_spif_erase_chip
  #define spif_erase_sector _symrom_spif_erase_sector
  #define spif_flash_size _symrom_spif_flash_size
  #define spif_flash_status_reg_0 _symrom_spif_flash_status_reg_0
  #define spif_flash_status_reg_1 _symrom_spif_flash_status_reg_1
  #define spif_init _symrom_spif_init
  #define spif_rddata _symrom_spif_rddata
  #define spif_read _symrom_spif_read
  #define spif_release_deep_sleep _symrom_spif_release_deep_sleep
  #define spif_set_deep_sleep _symrom_spif_set_deep_sleep
  #define spif_wrdata _symrom_spif_wrdata
  #define spif_write _symrom_spif_write
  #define spif_write_protect _symrom_spif_write_protect
  #define spif_write_dma _symrom_spif_write_dma
  #define sram_ret_patch _symrom_sram_ret_patch
  #define supportedCmdsTable _symrom_supportedCmdsTable
  #define syncInfo _symrom_syncInfo
  #define timerHead _symrom_timerHead
  #define tx_scanRsp_desc _symrom_tx_scanRsp_desc
  #define update_rx_read_ptr _symrom_update_rx_read_ptr
  #define update_rx_write_ptr _symrom_update_rx_write_ptr
  #define update_tx_read_ptr _symrom_update_tx_read_ptr
  #define update_tx_write_ptr _symrom_update_tx_write_ptr
  #define verInfo _symrom_verInfo
  #define WaitRTCCount _symrom_WaitRTCCount
  #define wakeup_init _symrom_wakeup_init
  #define wakeup_init0 _symrom_wakeup_init0
  #define wakeupProcess _symrom_wakeupProcess
  #define whiten_seed _symrom_whiten_seed
  #define zigbee_crc16_gen _symrom_zigbee_crc16_gen
  #define WaitUs _symrom_WaitUs

#endif /* USE_ROMSYM_ALIAS */
#endif /* __ARCH_ARM_SRC_PHY62XX_ROM_SYM_DEF_H */
