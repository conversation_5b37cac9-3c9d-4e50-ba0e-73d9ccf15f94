############################################################################
# arch/arm/src/stm32wb/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

# The start-up, "head", file.  Only common vectors are support so there
# isn't one.

# Common ARM and Cortex-M4 files (copied from stm32/Make.defs)

include armv7-m/Make.defs

# Required STM32WB files

CHIP_CSRCS  = stm32wb_allocateheap.c stm32wb_exti_gpio.c stm32wb_gpio.c
CHIP_CSRCS += stm32wb_irq.c stm32wb_lowputc.c stm32wb_rcc.c stm32wb_spi.c
CHIP_CSRCS += stm32wb_serial.c stm32wb_start.c stm32wb_waste.c stm32wb_uid.c
CHIP_CSRCS += stm32wb_rcc_lse.c stm32wb_rcc_lsi.c
CHIP_CSRCS += stm32wb_pwr.c stm32wb_tim.c
CHIP_CSRCS += stm32wb_flash.c

ifneq ($(CONFIG_ARCH_IDLE_CUSTOM),y)
CHIP_CSRCS += stm32wb_idle.c
endif

ifeq ($(CONFIG_TIMER),y)
CHIP_CSRCS += stm32wb_tim_lowerhalf.c
endif

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += stm32wb_timerisr.c
else
CHIP_CSRCS += stm32wb_tickless.c
endif

ifeq ($(CONFIG_STM32WB_ONESHOT),y)
CHIP_CSRCS += stm32wb_oneshot.c stm32wb_oneshot_lowerhalf.c
endif

ifeq ($(CONFIG_STM32WB_FREERUN),y)
CHIP_CSRCS += stm32wb_freerun.c
endif

ifeq ($(CONFIG_BUILD_PROTECTED),y)
CHIP_CSRCS += stm32wb_userspace.c stm32wb_mpuinit.c
endif

ifeq ($(CONFIG_STM32WB_HAVE_HSI48),y)
CHIP_CSRCS += stm32wb_rcc_hsi48.c
endif

ifeq ($(CONFIG_STM32WB_DMA),y)
CHIP_CSRCS += stm32wb_dma.c
endif

ifeq ($(CONFIG_PM),y)
CHIP_CSRCS += stm32wb_pmlpr.c stm32wb_pmsleep.c stm32wb_pmstandby.c
CHIP_CSRCS += stm32wb_pmstop.c

ifneq ($(CONFIG_ARCH_CUSTOM_PMINIT),y)
CHIP_CSRCS += stm32wb_pminitialize.c
endif
endif

ifeq ($(CONFIG_STM32WB_PWR),y)
CHIP_CSRCS += stm32wb_exti_pwr.c
endif

ifeq ($(CONFIG_STM32WB_RTC),y)
ifeq ($(CONFIG_RTC_ALARM),y)
CHIP_CSRCS += stm32wb_exti_alarm.c
endif
ifeq ($(CONFIG_RTC_PERIODIC),y)
CHIP_CSRCS += stm32wb_exti_wakeup.c
endif
ifeq ($(CONFIG_RTC_DRIVER),y)
CHIP_CSRCS += stm32wb_rtc.c stm32wb_rtc_lowerhalf.c
endif
endif

ifeq ($(CONFIG_STM32WB_IPCC),y)
CHIP_CSRCS += stm32wb_ipcc.c
endif

ifeq ($(CONFIG_STM32WB_MBOX),y)
CHIP_CSRCS += stm32wb_mbox.c
endif

ifeq ($(CONFIG_STM32WB_BLE),y)
CHIP_CSRCS += stm32wb_blehci.c
endif

ifeq ($(CONFIG_DEBUG_FEATURES),y)
CHIP_CSRCS += stm32wb_dumpgpio.c
endif
