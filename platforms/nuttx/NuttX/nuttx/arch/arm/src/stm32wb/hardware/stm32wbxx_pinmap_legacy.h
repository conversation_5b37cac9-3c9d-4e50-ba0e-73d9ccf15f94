/****************************************************************************
 * arch/arm/src/stm32wb/hardware/stm32wbxx_pinmap_legacy.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_STM32WB_HARDWARE_STM32WBXX_PINMAP_LEGACY_H
#define __ARCH_ARM_SRC_STM32WB_HARDWARE_STM32WBXX_PINMAP_LEGACY_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include "chip.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Alternate Pin Functions.  All members of the STM32WB3x and STM32WB5x
 * family share the same pin multiplexing (although they may differ in the
 * pins physically available), while the STM32WB1x family have some
 * differences.
 *
 * Alternative pin selections are provided with a numeric suffix like _1, _2,
 * etc.  Drivers, however, will use the pin selection without the numeric
 * suffix.  Additional definitions are required in the board.h file.  For
 * example, if CAN1_RX connects via PA11 on some board, then the following
 * definitions should appear in the board.h header file for that board:
 *
 * #define GPIO_CAN1_RX GPIO_CAN1_RX_1
 *
 * The driver will then automatically configure PA11 as the CAN1 RX pin.
 */

/* WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific GPIO options such as
 * frequency, open-drain/push-pull, and pull-up/down!  Just the basics are
 * defined for most pins in this file.
 */

/* ADC */

#if defined(CONFIG_STM32WB_STM32WB10) || defined(CONFIG_STM32WB_STM32WB15)
#  define GPIO_ADC1_IN2         (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN7)
#  define GPIO_ADC1_IN3         (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN8)
#  define GPIO_ADC1_IN4         (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN9)
#elif defined(CONFIG_STM32WB_IO_CONFIG_R) || defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_ADC1_IN1         (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN0)
#  define GPIO_ADC1_IN2         (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN1)
#  define GPIO_ADC1_IN3         (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN2)
#  define GPIO_ADC1_IN4         (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN3)
#endif

#define GPIO_ADC1_IN5           (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN0)
#define GPIO_ADC1_IN6           (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN1)
#define GPIO_ADC1_IN7           (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN2)
#define GPIO_ADC1_IN8           (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN3)
#define GPIO_ADC1_IN9           (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN4)
#define GPIO_ADC1_IN10          (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN5)
#define GPIO_ADC1_IN11          (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN6)

#if defined(CONFIG_STM32WB_STM32WB30) || defined(CONFIG_STM32WB_STM32WB50) \
    || defined(CONFIG_STM32WB_STM32WB35) || defined(CONFIG_STM32WB_STM32WB55)
#  define GPIO_ADC1_IN12        (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN7)
#  define GPIO_ADC1_IN15        (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN8)
#  define GPIO_ADC1_IN16        (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN9)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_R) || defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_ADC1_IN13        (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN4)
#  define GPIO_ADC1_IN14        (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN5)
#endif

/* Clocks outputs */

#define GPIO_MCO                (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN8)

/* Comparators */

#if defined(CONFIG_STM32WB_STM32WB35) || defined(CONFIG_STM32WB_STM32WB55)

#define GPIO_COMP1_INP_1        (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN1)
#define GPIO_COMP1_INP_2        (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN2)
#define GPIO_COMP1_INM_1        (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN0)
#define GPIO_COMP1_INM_2        (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN4)
#define GPIO_COMP1_INM_3        (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN5)
#define GPIO_COMP1_INM_4        (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN9)
#define GPIO_COMP1_OUT_1        (GPIO_ALT | GPIO_AF12 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_COMP1_OUT_2        (GPIO_ALT | GPIO_AF12 | GPIO_PORTB | GPIO_PIN0)

#if defined(CONFIG_STM32WB_IO_CONFIG_R) || defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_COMP1_INP_3      (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN5)
#  define GPIO_COMP1_INM_5      (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN4)
#  define GPIO_COMP1_OUT_3      (GPIO_ALT | GPIO_AF12 | GPIO_PORTB | GPIO_PIN10)
#endif

#define GPIO_COMP2_INP_1        (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN4)
#define GPIO_COMP2_INP_2        (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN6)
#define GPIO_COMP2_INP_3        (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN3)
#define GPIO_COMP2_INM_1        (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN3)
#define GPIO_COMP2_INM_2        (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN7)
#define GPIO_COMP2_INM_3        (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN2)
#define GPIO_COMP2_INM_4        (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN4)
#define GPIO_COMP2_INM_5        (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN5)
#define GPIO_COMP2_OUT_1        (GPIO_ALT | GPIO_AF12 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_COMP2_OUT_2        (GPIO_ALT | GPIO_AF12 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_COMP2_OUT_3        (GPIO_ALT | GPIO_AF12 | GPIO_PORTB | GPIO_PIN5)

#if defined(CONFIG_STM32WB_IO_CONFIG_R) || defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_COMP2_OUT_4      (GPIO_ALT | GPIO_AF12 | GPIO_PORTB | GPIO_PIN11)
#endif

#endif /* defined(CONFIG_STM32WB_STM32WB35) || defined(CONFIG_STM32WB_STM32WB55) */

/* I2C */

#define GPIO_I2C1_SDA_1         (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN7)
#define GPIO_I2C1_SDA_3         (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTA | GPIO_PIN10)
#define GPIO_I2C1_SCL_1         (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN6)
#define GPIO_I2C1_SCL_2         (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN8)
#define GPIO_I2C1_SCL_3         (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTA | GPIO_PIN9)
#define GPIO_I2C1_SMBA_1        (GPIO_ALT | GPIO_AF4 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_I2C1_SMBA_2        (GPIO_ALT | GPIO_AF4 | GPIO_PORTA | GPIO_PIN14)
#define GPIO_I2C1_SMBA_3        (GPIO_ALT | GPIO_AF4 | GPIO_PORTB | GPIO_PIN5)

#if !defined(CONFIG_STM32WB_IO_CONFIG_C_49)
#  define GPIO_I2C1_SDA_2       (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN9)
#endif

#if defined(CONFIG_STM32WB_STM32WB35) || defined(CONFIG_STM32WB_STM32WB55)

#define GPIO_I2C3_SDA_1         (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN4)
#define GPIO_I2C3_SCL_1         (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTA | GPIO_PIN7)
#define GPIO_I2C3_SMBA_1        (GPIO_ALT | GPIO_AF4 | GPIO_PORTB | GPIO_PIN2)

#if defined(CONFIG_STM32WB_IO_CONFIG_R) || defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_I2C3_SMBA_2      (GPIO_ALT | GPIO_AF4 | GPIO_PORTB | GPIO_PIN12)
#  define GPIO_I2C3_SDA_2       (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN11)
#  define GPIO_I2C3_SDA_3       (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN14)
#  define GPIO_I2C3_SCL_2       (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN10)
#  define GPIO_I2C3_SCL_3       (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN13)
#  define GPIO_I2C3_SCL_4       (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTC | GPIO_PIN0)
#  define GPIO_I2C3_SDA_4       (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTC | GPIO_PIN1)
#endif

#endif /* defined(CONFIG_STM32WB_STM32WB35) || defined(CONFIG_STM32WB_STM32WB55) */

/* JTAG/SWD */

#define GPIO_JTMS_SWDIO         (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN13)
#define GPIO_JTCK_SWCLK         (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN14)
#define GPIO_NJTRST             (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN4)

#if !defined(CONFIG_STM32WB_IO_CONFIG_C_49)
#  define GPIO_JTDO_SWO         (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN3)
#  define GPIO_JTDI             (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN15)
#endif

/* QUADSPI */

#if defined(CONFIG_STM32WB_STM32WB35) || defined(CONFIG_STM32WB_STM32WB55)

#define GPIO_QSPI_NCS_1         (GPIO_ALT | GPIO_AF10 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_QSPI_CLK_1         (GPIO_ALT | GPIO_AF10 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_QSPI_BK1_IO0_1     (GPIO_ALT | GPIO_AF10 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_QSPI_BK1_IO1_1     (GPIO_ALT | GPIO_AF10 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_QSPI_BK1_IO2_1     (GPIO_ALT | GPIO_AF10 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_QSPI_BK1_IO3_1     (GPIO_ALT | GPIO_AF10 | GPIO_PORTA | GPIO_PIN6)

#if defined(CONFIG_STM32WB_IO_CONFIG_R) || defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_QSPI_NCS_2       (GPIO_ALT | GPIO_AF10 | GPIO_PORTB | GPIO_PIN11)
#  define GPIO_QSPI_CLK_2       (GPIO_ALT | GPIO_AF10 | GPIO_PORTB | GPIO_PIN10)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_QSPI_NCS_3       (GPIO_ALT | GPIO_AF10 | GPIO_PORTD | GPIO_PIN3)
#  define GPIO_QSPI_BK1_IO0_2   (GPIO_ALT | GPIO_AF10 | GPIO_PORTD | GPIO_PIN4)
#  define GPIO_QSPI_BK1_IO1_2   (GPIO_ALT | GPIO_AF10 | GPIO_PORTD | GPIO_PIN5)
#  define GPIO_QSPI_BK1_IO2_2   (GPIO_ALT | GPIO_AF10 | GPIO_PORTD | GPIO_PIN6)
#  define GPIO_QSPI_BK1_IO3_2   (GPIO_ALT | GPIO_AF10 | GPIO_PORTD | GPIO_PIN7)
#endif

#endif /* defined(CONFIG_STM32WB_STM32WB35) || defined(CONFIG_STM32WB_STM32WB55) */

/* RTC */

#define GPIO_RTC_OUT_1          (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN2)
#define GPIO_RTC_TAMP2          (GPIO_PORTA | GPIO_PIN0)

#if defined(CONFIG_STM32WB_IO_CONFIG_R) || defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_RTC_OUT_2        (GPIO_ALT | GPIO_AF0 | GPIO_PORTC | GPIO_PIN13)
#  define GPIO_RTC_REFIN        (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN15)
#  define GPIO_RTC_TS           (GPIO_PORTC | GPIO_PIN13)
#  define GPIO_RTC_TAMP1        (GPIO_PORTC | GPIO_PIN13)
#  define GPIO_RTC_TAMP3        (GPIO_PORTC | GPIO_PIN12)
#endif

/* SAI */

#if defined(CONFIG_STM32WB_STM32WB35) || defined(CONFIG_STM32WB_STM32WB55)

#define GPIO_SAI1_EXTCLK_1      (GPIO_ALT | GPIO_AF13 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_SAI1_EXTCLK_2      (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN2)
#define GPIO_SAI1_FS_A_1        (GPIO_ALT | GPIO_AF13 | GPIO_PORTA | GPIO_PIN9)
#define GPIO_SAI1_FS_A_2        (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_SAI1_SCK_A_1       (GPIO_ALT | GPIO_AF13 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_SAI1_SD_A_1        (GPIO_ALT | GPIO_AF13 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_SAI1_MCLK_A_1      (GPIO_ALT | GPIO_AF13 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_SAI1_MCLK_A_2      (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_SAI1_FS_B_1        (GPIO_ALT | GPIO_AF13 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_SAI1_FS_B_2        (GPIO_ALT | GPIO_AF13 | GPIO_PORTA | GPIO_PIN14)
#define GPIO_SAI1_FS_B_3        (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN6)
#define GPIO_SAI1_SCK_B_1       (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_SAI1_SD_B_1        (GPIO_ALT | GPIO_AF13 | GPIO_PORTA | GPIO_PIN5)
#define GPIO_SAI1_SD_B_2        (GPIO_ALT | GPIO_AF13 | GPIO_PORTA | GPIO_PIN13)
#define GPIO_SAI1_SD_B_3        (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_SAI1_PDMCK1_1      (GPIO_ALT | GPIO_AF3 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_SAI1_PDMCK1_2      (GPIO_ALT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_SAI1_PDMCK2        (GPIO_ALT | GPIO_AF3 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_SAI1_PDMDI1_1      (GPIO_ALT | GPIO_AF3 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_SAI1_PDMDI2_1      (GPIO_ALT | GPIO_AF3 | GPIO_PORTA | GPIO_PIN9)
#define GPIO_SAI1_PDMDI2_2      (GPIO_ALT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN9)

#if defined(CONFIG_STM32WB_IO_CONFIG_R) || defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_SAI1_FS_A_3      (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN12)
#  define GPIO_SAI1_SCK_A_2     (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN10)
#  define GPIO_SAI1_SCK_A_3     (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN13)
#  define GPIO_SAI1_SD_A_2      (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN15)
#  define GPIO_SAI1_SD_A_3      (GPIO_ALT | GPIO_AF13 | GPIO_PORTC | GPIO_PIN3)
#  define GPIO_SAI1_MCLK_A_3    (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN14)
#  define GPIO_SAI1_PDMDI1_2    (GPIO_ALT | GPIO_AF3 | GPIO_PORTC | GPIO_PIN3)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_SAI1_SD_A_4      (GPIO_ALT | GPIO_AF13 | GPIO_PORTD | GPIO_PIN6)
#  define GPIO_SAI1_MCLK_A_4    (GPIO_ALT | GPIO_AF13 | GPIO_PORTE | GPIO_PIN2)
#  define GPIO_SAI1_SCK_B_2     (GPIO_ALT | GPIO_AF13 | GPIO_PORTC | GPIO_PIN9)
#  define GPIO_SAI1_PDMCK1_3    (GPIO_ALT | GPIO_AF3 | GPIO_PORTE | GPIO_PIN2)
#  define GPIO_SAI1_PDMDI1_3    (GPIO_ALT | GPIO_AF3 | GPIO_PORTD | GPIO_PIN6)
#endif

#endif /* defined(CONFIG_STM32WB_STM32WB35) || defined(CONFIG_STM32WB_STM32WB55) */

/* SPI */

#define GPIO_SPI1_NSS_1         (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_SPI1_NSS_2         (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN2)
#define GPIO_SPI1_SCK_1         (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_SPI1_SCK_2         (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN5)
#define GPIO_SPI1_MOSI_1        (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_SPI1_MOSI_2        (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN12)
#define GPIO_SPI1_MOSI_3        (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_SPI1_MISO_1        (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_SPI1_MISO_2        (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_SPI1_MISO_3        (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN4)

#if !defined(CONFIG_STM32WB_IO_CONFIG_C_49)
#  define GPIO_SPI1_NSS_3       (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN15)
#  define GPIO_SPI1_SCK_3       (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN3)
#endif

#if defined(CONFIG_STM32WB_STM32WB10) || defined(CONFIG_STM32WB_STM32WB15)
#  define GPIO_SPI1_MOSI_4      (GPIO_ALT | GPIO_AF4 | GPIO_PORTA | GPIO_PIN5)
#  define GPIO_SPI1_MOSI_5      (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN13)
#  define GPIO_SPI1_NSS_4       (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN14)
#  define GPIO_SPI1_NSS_5       (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN6)
#endif

#if defined(CONFIG_STM32WB_STM32WB55)

#define GPIO_SPI2_NSS_1         (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_SPI2_NSS_2         (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_SPI2_NSS_3         (GPIO_ALT | GPIO_AF5 | GPIO_PORTD | GPIO_PIN0)
#define GPIO_SPI2_SCK_1         (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN9)
#define GPIO_SPI2_SCK_2         (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_SPI2_SCK_3         (GPIO_ALT | GPIO_AF5 | GPIO_PORTD | GPIO_PIN1)
#define GPIO_SPI2_SCK_5         (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_SPI2_MOSI_1        (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN15)
#define GPIO_SPI2_MOSI_2        (GPIO_ALT | GPIO_AF3 | GPIO_PORTC | GPIO_PIN1)
#define GPIO_SPI2_MOSI_3        (GPIO_ALT | GPIO_AF5 | GPIO_PORTC | GPIO_PIN3)
#define GPIO_SPI2_MISO_1        (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_SPI2_MISO_2        (GPIO_ALT | GPIO_AF5 | GPIO_PORTC | GPIO_PIN2)

#if defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_SPI2_SCK_4       (GPIO_ALT | GPIO_AF3 | GPIO_PORTD | GPIO_PIN3)
#  define GPIO_SPI2_MOSI_4      (GPIO_ALT | GPIO_AF5 | GPIO_PORTD | GPIO_PIN4)
#  define GPIO_SPI2_MISO_3      (GPIO_ALT | GPIO_AF5 | GPIO_PORTD | GPIO_PIN3)
#endif

#endif /* defined(CONFIG_STM32WB_STM32WB55) */

/* Timers */

#define GPIO_TIM1_ETR_1         (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN12)
#define GPIO_TIM1_BKIN1_1       (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM1_BKIN1_2       (GPIO_ALT | GPIO_AF12 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM1_BKIN1_3       (GPIO_ALT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN7)
#define GPIO_TIM1_BKIN2_1       (GPIO_ALT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_TIM1_BKIN2_2       (GPIO_ALT | GPIO_AF12 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_TIM1_CH1IN_1       (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN8)
#define GPIO_TIM1_CH1OUT_1      (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN8)
#define GPIO_TIM1_CH1N_1        (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM1_CH2IN_1       (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN9)
#define GPIO_TIM1_CH2OUT_1      (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN9)
#define GPIO_TIM1_CH2N_1        (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN8)
#define GPIO_TIM1_CH3IN_1       (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN10)
#define GPIO_TIM1_CH3OUT_1      (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN10)
#define GPIO_TIM1_CH4IN         (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN11)
#define GPIO_TIM1_CH4OUT        (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN11)

#if !defined(CONFIG_STM32WB_IO_CONFIG_C_49)
#  define GPIO_TIM1_CH3N_1      (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN9)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_R) || defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_TIM1_BKIN1_4     (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN12)
#  define GPIO_TIM1_BKIN1_5     (GPIO_ALT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN12)
#  define GPIO_TIM1_CH1N_2      (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN13)
#  define GPIO_TIM1_CH2N_2      (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN14)
#  define GPIO_TIM1_CH3N_2      (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN15)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_TIM1_ETR_2       (GPIO_ALT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN0)
#  define GPIO_TIM1_CH1IN_2     (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTD | GPIO_PIN14)
#  define GPIO_TIM1_CH1OUT_2    (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN14)
#  define GPIO_TIM1_CH2IN_2     (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTD | GPIO_PIN15)
#  define GPIO_TIM1_CH2OUT_2    (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN15)
#endif

#if defined(CONFIG_STM32WB_STM32WB10) || defined(CONFIG_STM32WB_STM32WB15)
#  define GPIO_TIM1_CH3IN_2     (GPIO_ALT | GPIO_AF12 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN7)
#  define GPIO_TIM1_CH3OUT_2    (GPIO_ALT | GPIO_AF12 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN7)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_C_48E)
#  define GPIO_TIM1_CH1IN_3     (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN14)
#  define GPIO_TIM1_CH1OUT_3    (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN14)
#endif

#define GPIO_TIM2_ETR_1         (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_TIM2_ETR_2         (GPIO_ALT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN5)
#define GPIO_TIM2_CH1IN_1       (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN0)
#define GPIO_TIM2_CH1OUT_1      (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN0)
#define GPIO_TIM2_CH1IN_2       (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN5)
#define GPIO_TIM2_CH1OUT_2      (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN5)
#define GPIO_TIM2_CH2IN_1       (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN1)
#define GPIO_TIM2_CH2OUT_1      (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN1)
#define GPIO_TIM2_CH3IN_1       (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM2_CH3OUT_1      (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM2_CH4IN_1       (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN3)
#define GPIO_TIM2_CH4OUT_1      (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN3)

#if !defined(CONFIG_STM32WB_IO_CONFIG_C_49)
#  define GPIO_TIM2_ETR_3       (GPIO_ALT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN15)
#  define GPIO_TIM2_CH1IN_3     (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN15)
#  define GPIO_TIM2_CH1OUT_3    (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN15)
#  define GPIO_TIM2_CH2IN_2     (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN3)
#  define GPIO_TIM2_CH2OUT_2    (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN3)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_R) || defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_TIM2_CH3IN_2     (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN10)
#  define GPIO_TIM2_CH3OUT_2    (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN10)
#  define GPIO_TIM2_CH4IN_2     (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN11)
#  define GPIO_TIM2_CH4OUT_2    (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN11)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_C_48E)
#  define GPIO_TIM2_CH1IN_4     (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN15)
#  define GPIO_TIM2_CH1OUT_4    (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN15)
#  define GPIO_TIM2_CH2IN_3     (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN12)
#  define GPIO_TIM2_CH2OUT_3    (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN12)
#  define GPIO_TIM2_CH3IN_3     (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN13)
#  define GPIO_TIM2_CH3OUT_3    (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN13)
#endif

#if defined(CONFIG_STM32WB_STM32WB30) || defined(CONFIG_STM32WB_STM32WB50) \
    || defined(CONFIG_STM32WB_STM32WB35) || defined(CONFIG_STM32WB_STM32WB55)

#define GPIO_TIM16_BKIN         (GPIO_ALT | GPIO_AF14 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_TIM16_CH1IN_1      (GPIO_ALT | GPIO_AF14 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN8)
#define GPIO_TIM16_CH1OUT_1     (GPIO_ALT | GPIO_AF14 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN8)
#define GPIO_TIM16_CH1IN_2      (GPIO_ALT | GPIO_AF14 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM16_CH1OUT_2     (GPIO_ALT | GPIO_AF14 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM16_CH1N         (GPIO_ALT | GPIO_AF14 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN6)

#if defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_TIM16_CH1IN_3    (GPIO_ALT | GPIO_AF14 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN0)
#  define GPIO_TIM16_CH1OUT_3   (GPIO_ALT | GPIO_AF14 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN0)
#endif

#define GPIO_TIM17_BKIN         (GPIO_ALT | GPIO_AF14 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_TIM17_CH1IN_1      (GPIO_ALT | GPIO_AF14 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN9)
#define GPIO_TIM17_CH1OUT_1     (GPIO_ALT | GPIO_AF14 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN9)
#define GPIO_TIM17_CH1IN_2      (GPIO_ALT | GPIO_AF14 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM17_CH1OUT_2     (GPIO_ALT | GPIO_AF14 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM17_CH1N         (GPIO_ALT | GPIO_AF14 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN7)

#if defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_TIM17_CH1IN_3    (GPIO_ALT | GPIO_AF14 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN1)
#  define GPIO_TIM17_CH1OUT_3   (GPIO_ALT | GPIO_AF14 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN1)
#endif

#endif /* defined(CONFIG_STM32WB_STM32WB30) || defined(CONFIG_STM32WB_STM32WB50) || defined(CONFIG_STM32WB_STM32WB35) || defined(CONFIG_STM32WB_STM32WB55) */

#define GPIO_LPTIM1_ETR_1       (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN6)
#define GPIO_LPTIM1_OUT_1       (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN14)
#define GPIO_LPTIM1_OUT_2       (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN2)
#define GPIO_LPTIM1_IN1_1       (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_LPTIM1_IN2_1       (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN7)

#if defined(CONFIG_STM32WB_IO_CONFIG_R) || defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_LPTIM1_ETR_2     (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN3)
#  define GPIO_LPTIM1_IN1_2     (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN0)
#  define GPIO_LPTIM1_OUT_3     (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN1)
#  define GPIO_LPTIM1_IN2_2     (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN2)
#endif

#define GPIO_LPTIM2_ETR_1       (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN5)
#define GPIO_LPTIM2_OUT_1       (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_LPTIM2_OUT_2       (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_LPTIM2_IN1_1       (GPIO_ALT | GPIO_AF14 | GPIO_PORTB | GPIO_PIN1)

#if defined(CONFIG_STM32WB_IO_CONFIG_R) || defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_LPTIM2_ETR_2     (GPIO_ALT | GPIO_AF14 | GPIO_PORTC | GPIO_PIN3)
#  define GPIO_LPTIM2_IN1_2     (GPIO_ALT | GPIO_AF14 | GPIO_PORTC | GPIO_PIN0)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_LPTIM2_ETR_3     (GPIO_ALT | GPIO_AF14 | GPIO_PORTD | GPIO_PIN11)
#  define GPIO_LPTIM2_OUT_3     (GPIO_ALT | GPIO_AF14 | GPIO_PORTD | GPIO_PIN13)
#  define GPIO_LPTIM2_IN1_3     (GPIO_ALT | GPIO_AF14 | GPIO_PORTD | GPIO_PIN12)
#endif

/* Touch Screen Controller */

#if defined(CONFIG_STM32WB_STM32WB10) || defined(CONFIG_STM32WB_STM32WB15) || defined(CONFIG_STM32WB_STM32WB55)

#if defined(CONFIG_STM32WB_IO_CONFIG_R) || defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_TSC_SYNC_1       (GPIO_ALT | GPIO_AF9 | GPIO_PORTB | GPIO_PIN10)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_TSC_SYNC_2       (GPIO_ALT | GPIO_AF9 | GPIO_PORTD | GPIO_PIN2)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_R) || defined(CONFIG_STM32WB_IO_CONFIG_V) || defined(CONFIG_STM32WB_IO_CONFIG_C_48E)
#  define GPIO_TSC_G1_IO1       (GPIO_ALT | GPIO_AF9 | GPIO_PORTB | GPIO_PIN12)
#  define GPIO_TSC_G1_IO2       (GPIO_ALT | GPIO_AF9 | GPIO_PORTB | GPIO_PIN13)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_R) || defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_TSC_G1_IO3       (GPIO_ALT | GPIO_AF9 | GPIO_PORTB | GPIO_PIN14)
#  define GPIO_TSC_G1_IO4       (GPIO_ALT | GPIO_AF9 | GPIO_PORTB | GPIO_PIN15)
#endif

#define GPIO_TSC_G2_IO1         (GPIO_ALT | GPIO_AF9 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_TSC_G2_IO2         (GPIO_ALT | GPIO_AF9 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_TSC_G2_IO3         (GPIO_ALT | GPIO_AF9 | GPIO_PORTB | GPIO_PIN6)
#define GPIO_TSC_G2_IO4         (GPIO_ALT | GPIO_AF9 | GPIO_PORTB | GPIO_PIN7)

#if !defined(CONFIG_STM32WB_IO_CONFIG_C_49)
#  define GPIO_TSC_G3_IO1       (GPIO_ALT | GPIO_AF9 | GPIO_PORTA | GPIO_PIN15)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_R) || defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_TSC_G3_IO2       (GPIO_ALT | GPIO_AF9 | GPIO_PORTC | GPIO_PIN10)
#  define GPIO_TSC_G3_IO3       (GPIO_ALT | GPIO_AF9 | GPIO_PORTC | GPIO_PIN11)
#  define GPIO_TSC_G3_IO4       (GPIO_ALT | GPIO_AF9 | GPIO_PORTC | GPIO_PIN12)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_C_48E)
#  define GPIO_TSC_G3_IO2       (GPIO_ALT | GPIO_AF9 | GPIO_PORTB | GPIO_PIN10)
#  define GPIO_TSC_G3_IO3       (GPIO_ALT | GPIO_AF9 | GPIO_PORTC | GPIO_PIN1)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_R) || defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_TSC_G4_IO1       (GPIO_ALT | GPIO_AF9 | GPIO_PORTC | GPIO_PIN6)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_TSC_G4_IO2       (GPIO_ALT | GPIO_AF9 | GPIO_PORTC | GPIO_PIN7)
#  define GPIO_TSC_G4_IO3       (GPIO_ALT | GPIO_AF9 | GPIO_PORTC | GPIO_PIN8)
#  define GPIO_TSC_G4_IO4       (GPIO_ALT | GPIO_AF9 | GPIO_PORTC | GPIO_PIN9)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_TSC_G5_IO1       (GPIO_ALT | GPIO_AF9 | GPIO_PORTD | GPIO_PIN4)
#  define GPIO_TSC_G5_IO2       (GPIO_ALT | GPIO_AF9 | GPIO_PORTD | GPIO_PIN5)
#  define GPIO_TSC_G5_IO3       (GPIO_ALT | GPIO_AF9 | GPIO_PORTD | GPIO_PIN6)
#  define GPIO_TSC_G5_IO4       (GPIO_ALT | GPIO_AF9 | GPIO_PORTD | GPIO_PIN7)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_TSC_G6_IO1       (GPIO_ALT | GPIO_AF9 | GPIO_PORTD | GPIO_PIN10)
#  define GPIO_TSC_G6_IO2       (GPIO_ALT | GPIO_AF9 | GPIO_PORTD | GPIO_PIN11)
#  define GPIO_TSC_G6_IO3       (GPIO_ALT | GPIO_AF9 | GPIO_PORTD | GPIO_PIN12)
#  define GPIO_TSC_G6_IO4       (GPIO_ALT | GPIO_AF9 | GPIO_PORTD | GPIO_PIN13)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_TSC_G7_IO1       (GPIO_ALT | GPIO_AF9 | GPIO_PORTE | GPIO_PIN0)
#  define GPIO_TSC_G7_IO2       (GPIO_ALT | GPIO_AF9 | GPIO_PORTE | GPIO_PIN1)
#  define GPIO_TSC_G7_IO3       (GPIO_ALT | GPIO_AF9 | GPIO_PORTE | GPIO_PIN2)
#endif

#if defined(CONFIG_STM32WB_STM32WB15)
#  define GPIO_TSC_G7_IO1       (GPIO_ALT | GPIO_AF9 | GPIO_PORTA | GPIO_PIN13)
#  define GPIO_TSC_G7_IO2       (GPIO_ALT | GPIO_AF9 | GPIO_PORTA | GPIO_PIN10)
#  define GPIO_TSC_G7_IO3       (GPIO_ALT | GPIO_AF9 | GPIO_PORTB | GPIO_PIN8)
#endif

#define GPIO_TSC_G7_IO4         (GPIO_ALT | GPIO_AF9 | GPIO_PORTB | GPIO_PIN9)

#endif /* defined(CONFIG_STM32WB_STM32WB10) || defined(CONFIG_STM32WB_STM32WB15) || defined(CONFIG_STM32WB_STM32WB55) */

/* IR interface (with timers 16 and 17) */

#if defined(CONFIG_STM32WB_STM32WB30) || defined(CONFIG_STM32WB_STM32WB50) \
    || defined(CONFIG_STM32WB_STM32WB35) || defined(CONFIG_STM32WB_STM32WB55)
#  define GPIO_IR_OUT_1         (GPIO_ALT | GPIO_AF8 | GPIO_PORTA | GPIO_PIN13)
#  define GPIO_IR_OUT_2         (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN9)
#endif

/* Trace port */

#if defined(CONFIG_STM32WB_IO_CONFIG_R) || defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_TRACED1          (GPIO_ALT | GPIO_AF0 | GPIO_PORTC | GPIO_PIN10)
#  define GPIO_TRACED3          (GPIO_ALT | GPIO_AF0 | GPIO_PORTC | GPIO_PIN12)
#endif

#if defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_TRACECK          (GPIO_ALT | GPIO_AF0 | GPIO_PORTE | GPIO_PIN2)
#  define GPIO_TRACED0          (GPIO_ALT | GPIO_AF0 | GPIO_PORTD | GPIO_PIN9)
#  define GPIO_TRACED2          (GPIO_ALT | GPIO_AF0 | GPIO_PORTD | GPIO_PIN2)
#endif

/* USART/LPUART */

#define GPIO_USART1_TX_1        (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN9)
#define GPIO_USART1_TX_2        (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN6)
#define GPIO_USART1_RX_1        (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_USART1_RX_2        (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN7)
#define GPIO_USART1_CK_1        (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_USART1_CK_2        (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_USART1_CTS_1       (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_USART1_CTS_2       (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_USART1_RTS_DE_1    (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN12)

#if !defined(CONFIG_STM32WB_IO_CONFIG_C_49)
#  define GPIO_USART1_RTS_DE_2 (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN3)
#endif

#if defined(CONFIG_STM32WB_STM32WB15) || defined(CONFIG_STM32WB_STM32WB35) || defined(CONFIG_STM32WB_STM32WB55)

#define GPIO_LPUART1_TX_1       (GPIO_ALT | GPIO_AF8 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_LPUART1_TX_2       (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_LPUART1_RX_1       (GPIO_ALT | GPIO_AF8 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_LPUART1_RX_2       (GPIO_ALT | GPIO_AF8 | GPIO_PORTA | GPIO_PIN12)
#define GPIO_LPUART1_CTS_1      (GPIO_ALT | GPIO_AF8 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_LPUART1_RTS_DE_1   (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN1)

#if defined(CONFIG_STM32WB_IO_CONFIG_R) || defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_LPUART1_TX_3     (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN11)
#  define GPIO_LPUART1_TX_4     (GPIO_ALT | GPIO_AF8 | GPIO_PORTC | GPIO_PIN1)
#  define GPIO_LPUART1_RX_3     (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN10)
#  define GPIO_LPUART1_RX_4     (GPIO_ALT | GPIO_AF8 | GPIO_PORTC | GPIO_PIN0)
#  define GPIO_LPUART1_CTS_2    (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN13)
#  define GPIO_LPUART1_RTS_DE_2 (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN12)
#endif

#endif /* defined(CONFIG_STM32WB_STM32WB15) || defined(CONFIG_STM32WB_STM32WB35) || defined(CONFIG_STM32WB_STM32WB55) */

/* USB */

#if defined(CONFIG_STM32WB_STM32WB35) || defined(CONFIG_STM32WB_STM32WB55)

#define GPIO_USB_CRS_SYNC       (GPIO_ALT | GPIO_AF10 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_USB_DM             (GPIO_ALT | GPIO_AF10 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN11)
#define GPIO_USB_DP             (GPIO_ALT | GPIO_AF10 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN12)
#define GPIO_USB_NOE_1          (GPIO_ALT | GPIO_AF10 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN13)

#if defined(CONFIG_STM32WB_IO_CONFIG_V)
#  define GPIO_USB_NOE_2        (GPIO_ALT | GPIO_AF10 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN9)
#endif

#endif /* defined(CONFIG_STM32WB_STM32WB35) || defined(CONFIG_STM32WB_STM32WB55) */

#endif /* __ARCH_ARM_SRC_STM32WB_HARDWARE_STM32WBXX_PINMAP_LEGACY_H */
