/****************************************************************************
 * arch/arm/src/stm32wb/stm32wb_dma.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_STM32WB_STM32WB_DMA_H
#define __ARCH_ARM_SRC_STM32WB_STM32WB_DMA_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include <sys/types.h>

#include "chip.h"

#include "hardware/stm32wb_dma.h"
#include "hardware/stm32wb_dmamux.h"

/* These definitions provide the bit encoding of the 'status' parameter
 * passed to the DMA callback function (see dma_callback_t).
 */

#  define DMA_STATUS_TEIF         DMA_CHAN_TEIF_BIT     /* Channel Transfer Error */
#  define DMA_STATUS_HTIF         DMA_CHAN_HTIF_BIT     /* Channel Half Transfer */
#  define DMA_STATUS_TCIF         DMA_CHAN_TCIF_BIT     /* Channel Transfer Complete */

#define DMA_STATUS_ERROR          (DMA_STATUS_TEIF)
#define DMA_STATUS_SUCCESS        (DMA_STATUS_TCIF | DMA_STATUS_HTIF)

/****************************************************************************
 * Public Types
 ****************************************************************************/

/* DMA_HANDLE provides an opaque reference that can be used to represent a
 * DMA channel.
 */

typedef void *DMA_HANDLE;

/* Description:
 *   This is the type of the callback that is used to inform the user of the
 *   completion of the DMA.
 *
 * Input Parameters:
 *   handle - Refers to the DMA channel
 *   status - A bit encoded value that provides the completion status.  See
 *            the DMASTATUS_* definitions above.
 *   arg    - A user-provided value that was provided when
 *            stm32lwb_dmastart() was called.
 */

typedef void (*dma_callback_t)(DMA_HANDLE handle, uint8_t status, void *arg);

#ifdef CONFIG_DEBUG_DMA_INFO
struct stm32wb_dmaregs_s
{
  uint32_t isr;       /* Interrupt Status Register; each channel gets 4 bits */
  uint32_t ccr;       /* Channel Configuration Register; determines functionality */
  uint32_t cndtr;     /* Channel Count Register; determines number of transfers */
  uint32_t cpar;      /* Channel Peripheral Address Register; determines start */
  uint32_t cmar;      /* Channel Memory Address Register; determines start */
#ifndef CONFIG_STM32WB_HAVE_DMAMUX
  uint32_t cselr;     /* Channel Selection Register; chooses peripheral bound */
#else
  struct
  {
    uint32_t ccr;     /* Channel Configuration Register */
    uint32_t csr;     /* Channel Status Register */
    uint32_t rg0cr;   /* Request Generator Channel 0 Configuration Register */
    uint32_t rg1cr;   /* Request Generator Channel 1 Configuration Register */
    uint32_t rg2cr;   /* Request Generator Channel 2 Configuration Register */
    uint32_t rg3cr;   /* Request Generator Channel 3 Configuration Register */
    uint32_t rgsr;    /* Request Generator Interrupt Status Register */
  } dmamux;
#endif
};
#endif

/****************************************************************************
 * Public Data
 ****************************************************************************/

#ifndef __ASSEMBLY__

#undef EXTERN
#if defined(__cplusplus)
#define EXTERN extern "C"
extern "C"
{
#else
#define EXTERN extern
#endif

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/

/****************************************************************************
 * Name: stm32_dmachannel
 *
 * Description:
 *   Allocate a DMA channel.  This function gives the caller mutually
 *   exclusive access to the DMA channel specified by the 'dmamap' argument.
 *   It is common for both DMA controllers (DMA1 and DMA2).
 *
 * Input Parameters:
 *   dmamap - Identifies the stream/channel resource. For the STM32WB, this
 *     is a bit-encoded value as provided by the DMAMAP_* definitions in
 *     hardware/stm32wb_dmamux.h
 *
 * Returned Value:
 *   On success, this function returns a non-NULL, void* DMA channel handle.
 *   NULL is returned on any failure.  This function can fail only if no DMA
 *   channel is available.
 *
 * Assumptions:
 *   - The caller does not hold he DMA channel.
 *   - The caller can wait for the DMA channel to be freed if it is no
 *     available.
 *
 ****************************************************************************/

DMA_HANDLE stm32wb_dmachannel(unsigned int dmamap);

/****************************************************************************
 * Name: stm32wb_dmafree
 *
 * Description:
 *   Release a DMA channel.  If another thread is waiting for this DMA
 *   channel in a call to stm32wb_dmachannel, then this function will
 *   re-assign the DMA channel to that thread and wake it up.
 *
 *   NOTE:  The 'handle' used in this argument must NEVER be used again
 *          until stm32wb_dmachannel() is called again to re-gain access to
 *          the channel.
 *
 * Returned Value:
 *   None
 *
 * Assumptions:
 *   - The caller holds the DMA channel.
 *   - There is no DMA in progress
 *
 ****************************************************************************/

void stm32wb_dmafree(DMA_HANDLE handle);

/****************************************************************************
 * Name: stm32wb_dmasetup
 *
 * Description:
 *   Configure DMA before using
 *
 ****************************************************************************/

void stm32wb_dmasetup(DMA_HANDLE handle, uint32_t paddr, uint32_t maddr,
                      size_t ntransfers, uint32_t ccr);

/****************************************************************************
 * Name: stm32wb_dmastart
 *
 * Description:
 *   Start the DMA transfer
 *
 * Assumptions:
 *   - DMA handle allocated by stm32wb_dmachannel()
 *   - No DMA in progress
 *
 ****************************************************************************/

void stm32wb_dmastart(DMA_HANDLE handle, dma_callback_t callback, void *arg,
                      bool half);

/****************************************************************************
 * Name: stm32wb_dmastop
 *
 * Description:
 *   Cancel the DMA.  After stm32wb_dmastop() is called, the DMA channel is
 *   reset and stm32wb_dmasetup() must be called before stm32wb_dmastart()
 *   can be called again
 *
 * Assumptions:
 *   - DMA handle allocated by stm32wb_dmachannel()
 *
 ****************************************************************************/

void stm32wb_dmastop(DMA_HANDLE handle);

/****************************************************************************
 * Name: stm32wb_dmaresidual
 *
 * Description:
 *   Returns the number of bytes remaining to be transferred
 *
 * Assumptions:
 *   - DMA handle allocated by stm32wb_dmachannel()
 *
 ****************************************************************************/

size_t stm32wb_dmaresidual(DMA_HANDLE handle);

/****************************************************************************
 * Name: stm32wb_dmacapable
 *
 * Description:
 *   Check if the DMA controller can transfer data to/from given memory
 *   address with the given configuration. This depends on the internal
 *   connections in the ARM bus matrix of the processor. Note that this
 *   only applies to memory addresses, it will return false for any
 *   peripheral address.
 *
 * Returned Value:
 *   True, if transfer is possible.
 *
 ****************************************************************************/

#ifdef CONFIG_STM32WB_DMACAPABLE
bool stm32wb_dmacapable(uintptr_t maddr, uint32_t count, uint32_t ccr);
#else
#  define stm32wb_dmacapable(maddr, count, ccr) (true)
#endif

/****************************************************************************
 * Name: stm32wb_dmasample
 *
 * Description:
 *   Sample DMA register contents
 *
 * Assumptions:
 *   - DMA handle allocated by stm32wb_dmachannel()
 *
 ****************************************************************************/

#ifdef CONFIG_DEBUG_DMA_INFO
void stm32wb_dmasample(DMA_HANDLE handle, struct stm32wb_dmaregs_s *regs);
#else
#  define stm32wb_dmasample(handle,regs) ((void)0)
#endif

/****************************************************************************
 * Name: stm32wb_dmadump
 *
 * Description:
 *   Dump previously sampled DMA register contents
 *
 * Assumptions:
 *   - DMA handle allocated by stm32wb_dmachannel()
 *
 ****************************************************************************/

#ifdef CONFIG_DEBUG_DMA_INFO
void stm32wb_dmadump(DMA_HANDLE handle, const struct stm32wb_dmaregs_s *regs,
                     const char *msg);
#else
#  define stm32wb_dmadump(handle,regs,msg) ((void)0)
#endif

#undef EXTERN
#if defined(__cplusplus)
}
#endif

#endif /* __ASSEMBLY__ */
#endif /* __ARCH_ARM_SRC_STM32WB_STM32WB_DMA_H */
