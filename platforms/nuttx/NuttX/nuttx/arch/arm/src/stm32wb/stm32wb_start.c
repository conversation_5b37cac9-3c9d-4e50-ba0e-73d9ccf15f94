/****************************************************************************
 * arch/arm/src/stm32wb/stm32wb_start.c
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

#include <stdint.h>
#include <assert.h>
#include <debug.h>

#include <nuttx/init.h>
#include <arch/board/board.h>

#include "arm_internal.h"
#include "nvic.h"
#include "stm32wb_rcc.h"
#include "stm32wb_gpio.h"
#include "stm32wb_ipcc.h"
#include "stm32wb_userspace.h"
#include "stm32wb_start.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Memory Map ***************************************************************/

/* 0x0800:0000 - Beginning of the internal FLASH.   Address of vectors.
 *               Mapped as boot memory address 0x0000:0000 at reset.
 * 0x080f:ffff - End of flash region (assuming the max of 1MiB of FLASH).
 * 0x1000:0000 - Start of internal SRAM2a
 * 0x1000:8000 - Start of internal SRAM2b
 * 0x2000:0000 - Start of internal SRAM1 and start of .data (_sdata)
 *             - End of .data (_edata) and start of .bss (_sbss)
 *             - End of .bss (_ebss) and bottom of idle stack
 *             - _ebss + CONFIG_IDLETHREAD_STACKSIZE = end of idle stack,
 *               start of heap. NOTE that the ARM uses a decrement before
 *               store stack so that the correct initial value is the end of
 *               the stack + 4;
 */

#define HEAP_BASE  ((uintptr_t)&_ebss+CONFIG_IDLETHREAD_STACKSIZE)

#ifdef CONFIG_STM32WB_SRAM2A_HEAP
#  define SRAM2A_START  (STM32WB_SRAM2A_BASE + CONFIG_STM32WB_SRAM2A_USER_BASE_OFFSET)
#  define SRAM2A_END    (SRAM2A_START + CONFIG_STM32WB_SRAM2A_USER_SIZE)
#endif

#ifdef CONFIG_STM32WB_SRAM2B_HEAP
#  define SRAM2B_START  STM32WB_SRAM2B_BASE
#  define SRAM2B_END    (SRAM2B_START + CONFIG_STM32WB_SRAM2B_USER_SIZE)
#endif

/* g_idle_topstack: _sbss is the start of the BSS region as defined by the
 * linker script. _ebss lies at the end of the BSS region. The idle task
 * stack starts at the end of BSS and is of size CONFIG_IDLETHREAD_STACKSIZE.
 * The IDLE thread is the thread that the system boots on and, eventually,
 * becomes the IDLE, do nothing task that runs only when there is nothing
 * else to run.  The heap continues from there until the end of memory.
 * g_idle_topstack is a read-only variable the provides this computed
 * address.
 */

const uintptr_t g_idle_topstack = HEAP_BASE;

/****************************************************************************
 * Private Functions
 ****************************************************************************/

/****************************************************************************
 * Name: showprogress
 *
 * Description:
 *   Print a character on the UART to show boot status.
 *
 ****************************************************************************/

#ifdef CONFIG_DEBUG_FEATURES
#  define showprogress(c) arm_lowputc(c)
#else
#  define showprogress(c)
#endif

/****************************************************************************
 * Public Functions
 ****************************************************************************/

#ifdef CONFIG_ARMV7M_STACKCHECK
/* we need to get r10 set before we can allow instrumentation calls */

void __start(void) noinstrument_function;
#endif

/****************************************************************************
 * Public Functions
 ****************************************************************************/

/****************************************************************************
 * Name: __start
 *
 * Description:
 *   This is the reset entry point.
 *
 ****************************************************************************/

void __start(void)
{
  const uint32_t *src;
  uint32_t *dest;

#ifdef CONFIG_ARMV7M_STACKCHECK
  /* Set the stack limit before we attempt to call any functions */

  __asm__ volatile ("sub r10, sp, %0" : :
                    "r"(CONFIG_IDLETHREAD_STACKSIZE - 64) :);
#endif

  /* The SRAM2 region is parity checked, but upon power up, it will be in
   * a random state and probably invalid with respect to parity, potentially
   * generating faults if accessed.  If elected, we will write zeros to the
   * memory, forcing the parity to be set to a valid state.
   * NOTE:  this is optional because this may be inappropriate, especially
   * if the memory is being used for it's battery backed purpose.  In that
   * case, the first-time initialization needs to be performed by the board
   * under application-specific circumstances.  On the other hand, if we're
   * using this memory for, say, additional heap space, then this is handy.
   */

#ifdef CONFIG_STM32WB_SRAM2A_INIT
  for (dest = (uint32_t *)SRAM2A_START; dest < (uint32_t *)SRAM2A_END; )
    {
      *dest++ = 0;
    }
#endif

#ifdef CONFIG_STM32WB_SRAM2B_INIT
  for (dest = (uint32_t *)SRAM2B_START; dest < (uint32_t *)SRAM2B_END; )
    {
      *dest++ = 0;
    }
#endif

  /* Configure the UART so that we can get debug output as soon as possible */

  stm32wb_clockconfig();
#ifdef CONFIG_STM32WB_IPCC
  stm32wb_ipccreset();
#endif
  arm_fpuconfig();

  /* Todo: stm32wb_lowsetup(); */

  stm32wb_gpioinit();
  showprogress('A');

  /* Clear .bss.  We'll do this inline (vs. calling memset) just to be
   * certain that there are no issues with the state of global variables.
   */

  for (dest = &_sbss; dest < &_ebss; )
    {
      *dest++ = 0;
    }

  showprogress('B');

  /* Move the initialized data section from his temporary holding spot in
   * FLASH into the correct place in SRAM.  The correct place in SRAM is
   * give by _sdata and _edata.  The temporary location is in FLASH at the
   * end of all of the other read-only data (.text, .rodata) at _eronly.
   */

  for (src = &_eronly, dest = &_sdata; dest < &_edata; )
    {
      *dest++ = *src++;
    }

  showprogress('C');

  /* Perform early serial initialization */

#ifdef USE_EARLYSERIALINIT
  arm_earlyserialinit();
#endif
  showprogress('D');

  /* For the case of the separate user-/kernel-space build, perform whatever
   * platform specific initialization of the user memory is required.
   * Normally this just means initializing the user space .data and .bss
   * segments.
   */

#ifdef CONFIG_BUILD_PROTECTED
  stm32wb_userspace();
  showprogress('E');
#endif

  /* Initialize onboard resources */

  stm32wb_board_initialize();
  showprogress('F');

  /* Then start NuttX */

  showprogress('\r');
  showprogress('\n');

  nx_start();

  /* Shouldn't get here */

  for (; ; );
}
