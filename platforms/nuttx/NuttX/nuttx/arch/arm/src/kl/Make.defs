############################################################################
# arch/arm/src/kl/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

include armv6-m/Make.defs

CHIP_CSRCS  = kl_clockconfig.c kl_gpio.c kl_irq.c kl_lowputc.c
CHIP_CSRCS += kl_serial.c kl_start.c kl_cfmconfig.c

# Configuration-dependent Kinetis L files

ifneq ($(CONFIG_ARCH_IDLE_CUSTOM),y)
CHIP_CSRCS += kl_idle.c
endif

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += kl_timerisr.c
endif

ifeq ($(CONFIG_BUILD_PROTECTED),y)
CHIP_CSRCS += kl_userspace.c
endif

ifeq ($(CONFIG_KL_GPIOIRQ),y)
CHIP_CSRCS += kl_gpioirq.c
endif

ifeq ($(CONFIG_ARCH_IRQPRIO),y)
CHIP_CSRCS += kl_irqprio.c
endif

ifeq ($(CONFIG_KL_SPI0),y)
CHIP_CSRCS += kl_spi.c
else
ifeq ($(CONFIG_KL_SPI1),y)
CHIP_CSRCS += kl_spi.c
endif
endif

ifeq ($(CONFIG_PWM),y)
CHIP_CSRCS += kl_pwm.c
endif

ifeq ($(CONFIG_DEBUG_FEATURES),y)
CHIP_CSRCS += kl_dumpgpio.c
endif
