#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

comment "Kinetis Configuration Options"

choice
	prompt "Kinetis Chip Selection"
	default ARCH_CHIP_MKL25Z128
	depends on ARCH_CHIP_KL

config ARCH_CHIP_MKL25Z64
	bool "MKL25Z64"
	select ARCH_FAMILY_KL2X

config ARCH_CHIP_MKL25Z128
	bool "MKL25Z128"
	select ARCH_FAMILY_KL2X

config ARCH_CHIP_MKL26Z128
	bool "MKL26Z128"
	select ARCH_FAMILY_KL2X

endchoice

# Chip families

config ARCH_FAMILY_KL2X
	bool
	default n

menu "Kinetis Peripheral Support"

config KL_TRACE
	bool "Trace"
	default n
	---help---
		Enable trace clocking on power up.

config KL_FLEXBUS
	bool "FlexBus"
	default n
	---help---
		Enable flexbus clocking on power up.

config KL_UART0
	bool "UART0"
	default n
	select UART0_SERIALDRIVER
	---help---
		Support UART0

config KL_UART1
	bool "UART1"
	default n
	select UART1_SERIALDRIVER
	---help---
		Support UART1

config KL_UART2
	bool "UART2"
	default n
	select UART2_SERIALDRIVER
	---help---
		Support UART2

config KL_RNGB
	bool "Random number generator"
	default n
	depends on ARCH_FAMILY_K60
	select ARCH_HAVE_RNG
	---help---
		Support the random number generator(K60 only)

config KL_FLEXCAN0
	bool "FlexCAN0"
	default n
	---help---
		Support FlexCAN0

config KL_FLEXCAN1
	bool "FlexCAN1"
	default n
	---help---
		Support FlexCAN1

config KL_SPI0
	bool "SPI0"
	default n
	select SPI
	select SPI_EXCHANGE
	---help---
		Support SPI0

config KL_SPI1
	bool "SPI1"
	default n
	select SPI
	select SPI_EXCHANGE
	---help---
		Support SPI1

config KL_SPI2
	bool "SPI2"
	default n
	---help---
		Support SPI2

config KL_I2C0
	bool "I2C0"
	default n
	---help---
		Support I2C0

config KL_I2C1
	bool "I2C1"
	default n
	---help---
		Support I2C1

config KL_I2S
	bool "I2S"
	default n
	---help---
		Support I2S

config KL_DAC0
	bool "DAC0"
	default n
	---help---
		Support DAC0

config KL_DAC1
	bool "DAC1"
	default n
	---help---
		Support DAC1

config KL_ADC0
	bool "ADC0"
	default n
	---help---
		Support ADC0

config KL_ADC1
	bool "ADC1"
	default n
	---help---
		Support ADC1

config KL_CMP
	bool "CMP"
	default n
	---help---
		Support CMP

config KL_VREF
	bool "VREF"
	default n
	---help---
		Support VREF

config KL_TPM0
	bool "Timer/PWM Module 0"
	default n
	---help---
		Support FlexTimer 0

config KL_TPM1
	bool "Timer/PWM Module 1"
	default n
	---help---
		Support FlexTimer 1

config KL_TPM2
	bool "Timer/PWM Module 2"
	default n
	---help---
		Support FlexTimer 2

config KL_LPTIMER
	bool "Low power timer (LPTIMER)"
	default n
	---help---
		Support the low power timer

config KL_RTC
	bool "RTC"
	default n
	---help---
		Support RTC

config KL_SLCD
	bool "Segment LCD (SLCD)"
	default n
	depends on ARCH_FAMILY_K40
	---help---
		Support the segment LCD (K40 only)

config KL_EWM
	bool "External watchdog (WVM)"
	default n
	---help---
		Support the external watchdog

config KL_CMT
	bool "Carrier modulator transmitter (CMT)"
	default n
	---help---
		Support Carrier Modulator Transmitter

config KL_USBOTG
	bool "USB OTG"
	default n
	---help---
		Support USB OTG (see also USBHOST and USBDEV)

config KL_USBDCD
	bool "USB device controller"
	default n
	---help---
		Support the USB Device Charger Detection module

config KL_LLWU
	bool "Low leakage wake-up unit (LLWU)"
	default n
	---help---
		Support the Low Leakage Wake-Up Unit

config KL_TSI
	bool "Touchscreen interface (TSI)"
	default n
	---help---
		Support the touch screeen interface

config KL_FTFL
	bool "FLASH (FTFL)"
	default n
	---help---
		Support FLASH

config KL_DMA
	bool "DMA"
	default n
	---help---
		Support DMA

config KL_CRC
	bool "CRC"
	default n
	---help---
		Support CRC

config KL_PDB
	bool "Programmable delay block (PDB)"
	default n
	---help---
		Support the Programmable Delay Block

config KL_PIT
	bool "Programmable interval timer (PIT)"
	default n
	---help---
		Support Programmable Interval Timers

endmenu

choice
	prompt "SysTick clock source"
	default KL_SYSTICK_CORECLK

config KL_SYSTICK_CORECLK
	bool "Cortex-M0 core clock"

config KL_SYSTICK_CORECLK_DIV16
	bool "Cortex-M0 core clock divided by 16"

endchoice

config KL_TPM0_PWM
	bool "TPM0 PWM"
	default n
	depends on KL_TPM0
	---help---
		Reserve timer 0 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If KL_TPM0
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

config KL_TPM0_CHANNEL
	int "TPM0 PWM Output Channel"
	default 0
	range 0 5
	depends on KL_TPM0_PWM
	---help---
		If TIM0 is enabled for PWM usage, you also need specifies the timer output
		channel {0,..,5}

config KL_TPM1_PWM
	bool "TPM1 PWM"
	default n
	depends on KL_TPM1
	---help---
		Reserve timer 1 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If KL_TPM1
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

config KL_TPM1_CHANNEL
	int "TPM1 PWM Output Channel"
	default 0
	range 0 1 if ARCH_CHIP_MKL25Z64 || ARCH_CHIP_MKL25Z128 || ARCH_CHIP_MKL26Z128
	range 0 5 if !ARCH_CHIP_MKL25Z64 && !ARCH_CHIP_MKL25Z128 && !ARCH_CHIP_MKL26Z128
	depends on KL_TPM1_PWM
	---help---
		If TIM1 is enabled for PWM usage, you also need specifies the timer output
		channel {0,..,5}

config KL_TPM2_PWM
	bool "TPM2 PWM"
	default n
	depends on KL_TPM2
	---help---
		Reserve timer 2 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If KL_TPM2
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

config KL_TPM2_CHANNEL
	int "TPM2 PWM Output Channel"
	default 0
	range 0 1 if ARCH_CHIP_MKL25Z64 || ARCH_CHIP_MKL25Z128 || ARCH_CHIP_MKL26Z128
	range 0 5 if !ARCH_CHIP_MKL25Z64 && !ARCH_CHIP_MKL25Z128 && !ARCH_CHIP_MKL26Z128
	depends on KL_TPM2_PWM
	---help---
		If TIM2 is enabled for PWM usage, you also need specifies the timer output
		channel {0,..,5}

comment "Kinetis GPIO Interrupt Configuration"

config KL_GPIOIRQ
	bool "GPIO pin interrupts"
	---help---
		Enable support for interrupting GPIO pins

if KL_GPIOIRQ

config KL_PORTAINTS
	bool "GPIOA interrupts"
	---help---
		Enable support for 32 interrupts from GPIO port A pins

config KL_PORTDINTS
	bool "GPIOD interrupts"
	---help---
		Enable support for 32 interrupts from GPIO port D pins

endif
