/****************************************************************************
 * arch/arm/src/a1x/hardware/a10_piocfg.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_A1X_HARDWARE_A10_PIOCFG_H
#define __ARCH_ARM_SRC_A1X_HARDWARE_A10_PIOCFG_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include "hardware/a1x_memorymap.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* PIO pin definitions ******************************************************/

/* Alternate Pin Functions.
 *
 * Alternative pin selections are provided with a numeric suffix like _1, _2,
 * etc. Drivers, however, will use the pin selection without the numeric
 * suffix.
 * Additional definitions are required in the board.h file.
 */

/* WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific PIO options
 * (such as pull-up or-down).
 * Just the basics are defined for most pins in this file at the present
 * time.
 */

/* AC97 */

#define PIO_AC97_BCLK   (PIO_PERIPH3 | PIO_PORT_PIOB | PIO_PIN6)
#define PIO_AC97_DI     (PIO_PERIPH3 | PIO_PORT_PIOB | PIO_PIN12)
#define PIO_AC97_DO     (PIO_PERIPH3 | PIO_PORT_PIOB | PIO_PIN8)
#define PIO_AC97_MCLK   (PIO_PERIPH3 | PIO_PORT_PIOB | PIO_PIN5)
#define PIO_AC97_SYNC   (PIO_PERIPH3 | PIO_PORT_PIOB | PIO_PIN7)

/* ATA */

#define PIO_ATAA0       (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN0)
#define PIO_ATAA1       (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN1)
#define PIO_ATAA2       (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN2)
#define PIO_ATACS0      (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN23)
#define PIO_ATACS1      (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN24)
#define PIO_ATAD0       (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN4)
#define PIO_ATAD1       (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN5)
#define PIO_ATAD2       (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN6)
#define PIO_ATAD3       (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN7)
#define PIO_ATAD4       (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN8)
#define PIO_ATAD5       (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN9)
#define PIO_ATAD6       (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN10)
#define PIO_ATAD7       (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN11)
#define PIO_ATAD8       (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN12)
#define PIO_ATAD9       (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN13)
#define PIO_ATAD10      (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN14)
#define PIO_ATAD11      (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN15)
#define PIO_ATAD12      (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN16)
#define PIO_ATAD13      (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN17)
#define PIO_ATAD14      (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN18)
#define PIO_ATAD15      (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN19)
#define PIO_ATADACK     (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN22)
#define PIO_ATADREQ     (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN21)
#define PIO_ATAIOR      (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN26)
#define PIO_ATAIORDY    (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN25)
#define PIO_ATAIOW      (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN27)
#define PIO_ATAIRQ      (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN3)
#define PIO_ATAOE       (PIO_PERIPH3 | PIO_PORT_PIOH | PIO_PIN20)

/* CAN */

#define PIO_CAN_RX_1    (PIO_PERIPH3 | PIO_PORT_PIOA | PIO_PIN16)
#define PIO_CAN_RX_2    (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN21)
#define PIO_CAN_TX_1    (PIO_PERIPH3 | PIO_PORT_PIOA | PIO_PIN17)
#define PIO_CAN_TX_2    (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN20)

/* Camera Sensor Interface (CSI) */

#define PIO_CSI0_CK     (PIO_PERIPH3 | PIO_PORT_PIOE | PIO_PIN1)
#define PIO_CSI0_D0     (PIO_PERIPH3 | PIO_PORT_PIOE | PIO_PIN4)
#define PIO_CSI0_D1     (PIO_PERIPH3 | PIO_PORT_PIOE | PIO_PIN5)
#define PIO_CSI0_D2     (PIO_PERIPH3 | PIO_PORT_PIOE | PIO_PIN6)
#define PIO_CSI0_D3     (PIO_PERIPH3 | PIO_PORT_PIOE | PIO_PIN7)
#define PIO_CSI0_D4     (PIO_PERIPH3 | PIO_PORT_PIOE | PIO_PIN8)
#define PIO_CSI0_D5     (PIO_PERIPH3 | PIO_PORT_PIOE | PIO_PIN9)
#define PIO_CSI0_D6     (PIO_PERIPH3 | PIO_PORT_PIOE | PIO_PIN10)
#define PIO_CSI0_D7     (PIO_PERIPH3 | PIO_PORT_PIOE | PIO_PIN11)
#define PIO_CSI0_D8     (PIO_PERIPH5 | PIO_PORT_PIOG | PIO_PIN4)
#define PIO_CSI0_D9     (PIO_PERIPH5 | PIO_PORT_PIOG | PIO_PIN5)
#define PIO_CSI0_D10    (PIO_PERIPH5 | PIO_PORT_PIOG | PIO_PIN6)
#define PIO_CSI0_D11    (PIO_PERIPH5 | PIO_PORT_PIOG | PIO_PIN7)
#define PIO_CSI0_D12    (PIO_PERIPH5 | PIO_PORT_PIOG | PIO_PIN8)
#define PIO_CSI0_D13    (PIO_PERIPH5 | PIO_PORT_PIOG | PIO_PIN9)
#define PIO_CSI0_D14    (PIO_PERIPH5 | PIO_PORT_PIOG | PIO_PIN10)
#define PIO_CSI0_D15    (PIO_PERIPH5 | PIO_PORT_PIOG | PIO_PIN11)
#define PIO_CSI0_HSYNC  (PIO_PERIPH3 | PIO_PORT_PIOE | PIO_PIN2)
#define PIO_CSI0_PCK    (PIO_PERIPH3 | PIO_PORT_PIOE | PIO_PIN0)
#define PIO_CSI0_VSYNC  (PIO_PERIPH3 | PIO_PORT_PIOE | PIO_PIN3)

#define PIO_CSI1_CK     (PIO_PERIPH3 | PIO_PORT_PIOG | PIO_PIN1)
#define PIO_CSI1_D0_1   (PIO_PERIPH3 | PIO_PORT_PIOG | PIO_PIN4)
#define PIO_CSI1_D0_2   (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN0)
#define PIO_CSI1_D1_1   (PIO_PERIPH3 | PIO_PORT_PIOG | PIO_PIN5)
#define PIO_CSI1_D1_2   (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN1)
#define PIO_CSI1_D2_1   (PIO_PERIPH3 | PIO_PORT_PIOG | PIO_PIN6)
#define PIO_CSI1_D2_2   (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN2)
#define PIO_CSI1_D3_1   (PIO_PERIPH3 | PIO_PORT_PIOG | PIO_PIN7)
#define PIO_CSI1_D3_2   (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN3)
#define PIO_CSI1_D4_1   (PIO_PERIPH3 | PIO_PORT_PIOG | PIO_PIN8)
#define PIO_CSI1_D4_2   (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN4)
#define PIO_CSI1_D5_1   (PIO_PERIPH3 | PIO_PORT_PIOG | PIO_PIN9)
#define PIO_CSI1_D5_2   (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN5)
#define PIO_CSI1_D6_1   (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN6)
#define PIO_CSI1_D6_2   (PIO_PERIPH3 | PIO_PORT_PIOG | PIO_PIN10)
#define PIO_CSI1_D7_1   (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN7)
#define PIO_CSI1_D7_2   (PIO_PERIPH3 | PIO_PORT_PIOG | PIO_PIN11)
#define PIO_CSI1_D8     (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN8)
#define PIO_CSI1_D9     (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN9)
#define PIO_CSI1_D10    (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN10)
#define PIO_CSI1_D11    (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN11)
#define PIO_CSI1_D12    (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN12)
#define PIO_CSI1_D13    (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN13)
#define PIO_CSI1_D14    (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN14)
#define PIO_CSI1_D15    (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN15)
#define PIO_CSI1_D16    (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN16)
#define PIO_CSI1_D17    (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN17)
#define PIO_CSI1_D18    (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN18)
#define PIO_CSI1_D19    (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN19)
#define PIO_CSI1_D20    (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN20)
#define PIO_CSI1_D21    (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN21)
#define PIO_CSI1_D22    (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN22)
#define PIO_CSI1_D23    (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN23)
#define PIO_CSI1_FIELD  (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN25)
#define PIO_CSI1_HSYNC  (PIO_PERIPH3 | PIO_PORT_PIOG | PIO_PIN2)
#define PIO_CSI1_HSYNC  (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN26)
#define PIO_CSI1_MCLK   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN20)
#define PIO_CSI1_PCK    (PIO_PERIPH3 | PIO_PORT_PIOG | PIO_PIN0)
#define PIO_CSI1_PCLK   (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN24)
#define PIO_CSI1_VSYNC  (PIO_PERIPH3 | PIO_PORT_PIOG | PIO_PIN3)
#define PIO_CSI1_VSYNC  (PIO_PERIPH7 | PIO_PORT_PIOH | PIO_PIN27)

/* Ethernet MAC */

#define PIO_ECOL        (PIO_PERIPH2 | PIO_PORT_PIOA | PIO_PIN17)
#define PIO_ECRS        (PIO_PERIPH2 | PIO_PORT_PIOA | PIO_PIN15)
#define PIO_EMDC        (PIO_PERIPH2 | PIO_PORT_PIOA | PIO_PIN11)
#define PIO_EMDIO       (PIO_PERIPH2 | PIO_PORT_PIOA | PIO_PIN12)
#define PIO_ERXCK       (PIO_PERIPH2 | PIO_PORT_PIOA | PIO_PIN8)
#define PIO_ERXD0       (PIO_PERIPH2 | PIO_PORT_PIOA | PIO_PIN3)
#define PIO_ERXD1       (PIO_PERIPH2 | PIO_PORT_PIOA | PIO_PIN2)
#define PIO_ERXD2       (PIO_PERIPH2 | PIO_PORT_PIOA | PIO_PIN1)
#define PIO_ERXD3       (PIO_PERIPH2 | PIO_PORT_PIOA | PIO_PIN0)
#define PIO_ERXDV       (PIO_PERIPH2 | PIO_PORT_PIOA | PIO_PIN10)
#define PIO_ERXERR      (PIO_PERIPH2 | PIO_PORT_PIOA | PIO_PIN9)
#define PIO_ETXCK       (PIO_PERIPH2 | PIO_PORT_PIOA | PIO_PIN14)
#define PIO_ETXD0       (PIO_PERIPH2 | PIO_PORT_PIOA | PIO_PIN7)
#define PIO_ETXD1       (PIO_PERIPH2 | PIO_PORT_PIOA | PIO_PIN6)
#define PIO_ETXD2       (PIO_PERIPH2 | PIO_PORT_PIOA | PIO_PIN5)
#define PIO_ETXD3       (PIO_PERIPH2 | PIO_PORT_PIOA | PIO_PIN4)
#define PIO_ETXEN       (PIO_PERIPH2 | PIO_PORT_PIOA | PIO_PIN13)
#define PIO_ETXERR      (PIO_PERIPH2 | PIO_PORT_PIOA | PIO_PIN16)

/* External PIO interrupts */

#define PIO_EINT0       (PIO_EINT | PIO_PORT_PIOH | PIO_PIN0)
#define PIO_EINT1       (PIO_EINT | PIO_PORT_PIOH | PIO_PIN1)
#define PIO_EINT2       (PIO_EINT | PIO_PORT_PIOH | PIO_PIN2)
#define PIO_EINT3       (PIO_EINT | PIO_PORT_PIOH | PIO_PIN3)
#define PIO_EINT4       (PIO_EINT | PIO_PORT_PIOH | PIO_PIN4)
#define PIO_EINT5       (PIO_EINT | PIO_PORT_PIOH | PIO_PIN5)
#define PIO_EINT6       (PIO_EINT | PIO_PORT_PIOH | PIO_PIN6)
#define PIO_EINT7       (PIO_EINT | PIO_PORT_PIOH | PIO_PIN7)
#define PIO_EINT8       (PIO_EINT | PIO_PORT_PIOH | PIO_PIN8)
#define PIO_EINT9       (PIO_EINT | PIO_PORT_PIOH | PIO_PIN9)
#define PIO_EINT10      (PIO_EINT | PIO_PORT_PIOH | PIO_PIN10)
#define PIO_EINT11      (PIO_EINT | PIO_PORT_PIOH | PIO_PIN11)
#define PIO_EINT12      (PIO_EINT | PIO_PORT_PIOH | PIO_PIN12)
#define PIO_EINT13      (PIO_EINT | PIO_PORT_PIOH | PIO_PIN13)
#define PIO_EINT14      (PIO_EINT | PIO_PORT_PIOH | PIO_PIN14)
#define PIO_EINT15      (PIO_EINT | PIO_PORT_PIOH | PIO_PIN15)
#define PIO_EINT16      (PIO_EINT | PIO_PORT_PIOH | PIO_PIN16)
#define PIO_EINT17      (PIO_EINT | PIO_PORT_PIOH | PIO_PIN17)
#define PIO_EINT18      (PIO_EINT | PIO_PORT_PIOH | PIO_PIN18)
#define PIO_EINT19      (PIO_EINT | PIO_PORT_PIOH | PIO_PIN19)
#define PIO_EINT20      (PIO_EINT | PIO_PORT_PIOH | PIO_PIN20)
#define PIO_EINT21      (PIO_EINT | PIO_PORT_PIOH | PIO_PIN21)
#define PIO_EINT22      (PIO_EINT | PIO_PORT_PIOI | PIO_PIN10)
#define PIO_EINT23      (PIO_EINT | PIO_PORT_PIOI | PIO_PIN11)
#define PIO_EINT24      (PIO_EINT | PIO_PORT_PIOI | PIO_PIN12)
#define PIO_EINT25      (PIO_EINT | PIO_PORT_PIOI | PIO_PIN13)
#define PIO_EINT26      (PIO_EINT | PIO_PORT_PIOI | PIO_PIN14)
#define PIO_EINT27      (PIO_EINT | PIO_PORT_PIOI | PIO_PIN15)
#define PIO_EINT28      (PIO_EINT | PIO_PORT_PIOI | PIO_PIN16)
#define PIO_EINT29      (PIO_EINT | PIO_PORT_PIOI | PIO_PIN17)
#define PIO_EINT30      (PIO_EINT | PIO_PORT_PIOI | PIO_PIN18)
#define PIO_EINT31      (PIO_EINT | PIO_PORT_PIOI | PIO_PIN19)

/* I2C */

#define PIO_HSCL        (PIO_PERIPH4 | PIO_PORT_PIOI | PIO_PIN20)
#define PIO_HSDA        (PIO_PERIPH4 | PIO_PORT_PIOI | PIO_PIN21)

/* I2S */

#define PIO_I2S_BCLK    (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN6)
#define PIO_I2S_DI      (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN12)
#define PIO_I2S_DO0     (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN8)
#define PIO_I2S_DO1     (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN9)
#define PIO_I2S_DO2     (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN10)
#define PIO_I2S_DO3     (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN11)
#define PIO_I2S_LRCK    (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN7)
#define PIO_I2S_MCLK    (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN5)

/* IR Interface */

#define PIO_IR0_RX      (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN4)
#define PIO_IR0_TX      (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN3)

#define PIO_IR1_RX      (PIO_PERIPH3 | PIO_PORT_PIOB | PIO_PIN23)
#define PIO_IR1_TX      (PIO_PERIPH3 | PIO_PORT_PIOB | PIO_PIN22)

/* JTAG */

#define PIO_JTAG_CK0    (PIO_PERIPH3 | PIO_PORT_PIOB | PIO_PIN15)
#define PIO_JTAG_CK1    (PIO_PERIPH4 | PIO_PORT_PIOF | PIO_PIN5)
#define PIO_JTAG_DI0    (PIO_PERIPH3 | PIO_PORT_PIOB | PIO_PIN17)
#define PIO_JTAG_DI1    (PIO_PERIPH4 | PIO_PORT_PIOF | PIO_PIN1)
#define PIO_JTAG_DO0    (PIO_PERIPH3 | PIO_PORT_PIOB | PIO_PIN16)
#define PIO_JTAG_DO1    (PIO_PERIPH4 | PIO_PORT_PIOF | PIO_PIN3)
#define PIO_JTAG_MS0    (PIO_PERIPH3 | PIO_PORT_PIOB | PIO_PIN14)
#define PIO_JTAG_MS1    (PIO_PERIPH4 | PIO_PORT_PIOF | PIO_PIN0)

/* Keypad */

#define PIO_KP_IN0      (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN8)
#define PIO_KP_IN1      (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN9)
#define PIO_KP_IN2      (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN10)
#define PIO_KP_IN3      (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN11)
#define PIO_KP_IN4      (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN14)
#define PIO_KP_IN5      (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN15)
#define PIO_KP_IN6      (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN16)
#define PIO_KP_IN7      (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN17)
#define PIO_KP_OUT0     (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN18)
#define PIO_KP_OUT1     (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN19)
#define PIO_KP_OUT2     (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN22)
#define PIO_KP_OUT3     (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN23)
#define PIO_KP_OUT4     (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN24)
#define PIO_KP_OUT5     (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN25)
#define PIO_KP_OUT6     (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN26)
#define PIO_KP_OUT7     (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN27)

/* LCD */

#define PIO_LCD0_CLK    (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN24)
#define PIO_LCD0_D0     (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN0)
#define PIO_LCD0_D1     (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN1)
#define PIO_LCD0_D2     (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN2)
#define PIO_LCD0_D3     (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN3)
#define PIO_LCD0_D4     (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN4)
#define PIO_LCD0_D5     (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN5)
#define PIO_LCD0_D6     (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN6)
#define PIO_LCD0_D7     (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN7)
#define PIO_LCD0_D8     (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN8)
#define PIO_LCD0_D9     (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN9)
#define PIO_LCD0_D10    (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN10)
#define PIO_LCD0_D11    (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN11)
#define PIO_LCD0_D12    (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN12)
#define PIO_LCD0_D13    (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN13)
#define PIO_LCD0_D14    (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN14)
#define PIO_LCD0_D15    (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN15)
#define PIO_LCD0_D16    (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN16)
#define PIO_LCD0_D17    (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN17)
#define PIO_LCD0_D18    (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN18)
#define PIO_LCD0_D19    (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN19)
#define PIO_LCD0_D20    (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN20)
#define PIO_LCD0_D21    (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN21)
#define PIO_LCD0_D22    (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN22)
#define PIO_LCD0_D23    (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN23)
#define PIO_LCD0_DE     (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN25)
#define PIO_LCD0_HSYNC  (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN26)
#define PIO_LCD0_VSYNC  (PIO_PERIPH2 | PIO_PORT_PIOD | PIO_PIN27)

#define PIO_LCD1_CLK    (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN24)
#define PIO_LCD1_D0_1   (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN0)
#define PIO_LCD1_D0_2   (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN1)
#define PIO_LCD1_D2     (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN2)
#define PIO_LCD1_D3     (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN3)
#define PIO_LCD1_D4     (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN4)
#define PIO_LCD1_D5     (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN5)
#define PIO_LCD1_D6     (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN6)
#define PIO_LCD1_D7     (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN7)
#define PIO_LCD1_D8     (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN8)
#define PIO_LCD1_D9     (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN9)
#define PIO_LCD1_D10    (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN10)
#define PIO_LCD1_D11    (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN11)
#define PIO_LCD1_D12    (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN12)
#define PIO_LCD1_D13    (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN13)
#define PIO_LCD1_D14    (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN14)
#define PIO_LCD1_D15    (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN15)
#define PIO_LCD1_D16    (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN16)
#define PIO_LCD1_D17    (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN17)
#define PIO_LCD1_D18    (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN18)
#define PIO_LCD1_D19    (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN19)
#define PIO_LCD1_D20    (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN20)
#define PIO_LCD1_D21    (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN21)
#define PIO_LCD1_D22    (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN22)
#define PIO_LCD1_D23    (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN23)
#define PIO_LCD1_DE     (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN25)
#define PIO_LCD1_HSYNC  (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN26)
#define PIO_LCD1_VSYNC  (PIO_PERIPH2 | PIO_PORT_PIOH | PIO_PIN27)

#define PIO_LVDS0_VM3   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN9)
#define PIO_LVDS0_VN0   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN1)
#define PIO_LVDS0_VN1   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN3)
#define PIO_LVDS0_VN2   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN5)
#define PIO_LVDS0_VNC   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN7)
#define PIO_LVDS0_VP0   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN0)
#define PIO_LVDS0_VP1   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN2)
#define PIO_LVDS0_VP2   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN4)
#define PIO_LVDS0_VP3   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN8)
#define PIO_LVDS0_VPC   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN6)

/* LCD/TV Timing Controller */

#define PIO_LVDS1_VN    (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN13)
#define PIO_LVDS1_VN0   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN11)
#define PIO_LVDS1_VN2   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN15)
#define PIO_LVDS1_VN3   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN19)
#define PIO_LVDS1_VNC   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN17)
#define PIO_LVDS1_VP0   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN10)
#define PIO_LVDS1_VP1   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN12)
#define PIO_LVDS1_VP2   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN14)
#define PIO_LVDS1_VP3   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN18)
#define PIO_LVDS1_VPC   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN16)

/* Memory Stick Controller (MSC) */

#define PIO_MS_BS       (PIO_PERIPH5 | PIO_PORT_PIOH | PIO_PIN6)
#define PIO_MS_CLK      (PIO_PERIPH5 | PIO_PORT_PIOH | PIO_PIN7)
#define PIO_MS_D0       (PIO_PERIPH5 | PIO_PORT_PIOH | PIO_PIN8)
#define PIO_MS_D1       (PIO_PERIPH5 | PIO_PORT_PIOH | PIO_PIN9)
#define PIO_MS_D2       (PIO_PERIPH5 | PIO_PORT_PIOH | PIO_PIN10)
#define PIO_MS_D3       (PIO_PERIPH5 | PIO_PORT_PIOH | PIO_PIN11)

/* NAND */

#define PIO_NALE        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN1)
#define PIO_NC          (PIO_PERIPH3 | PIO_PORT_PIOB | PIO_PIN3)
#define PIO_NCE0        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN4)
#define PIO_NCE1        (PIO_PERIPH3 | PIO_PORT_PIOC | PIO_PIN3)
#define PIO_NCE2        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN17)
#define PIO_NCE3        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN18)
#define PIO_NCE4        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN19)
#define PIO_NCE5        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN20)
#define PIO_NCE6        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN21)
#define PIO_NCE7        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN22)
#define PIO_NCLE        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN2)
#define PIO_NDQ0        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN8)
#define PIO_NDQ1        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN9)
#define PIO_NDQ2        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN10)
#define PIO_NDQ3        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN11)
#define PIO_NDQ4        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN12)
#define PIO_NDQ5        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN13)
#define PIO_NDQ6        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN14)
#define PIO_NDQ7        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN15)
#define PIO_NDQS        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN24)
#define PIO_NRB0        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN6)
#define PIO_NRB1        (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN7)
#define PIO_NRE         (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN5)
#define PIO_NWE         (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN0)
#define PIO_NWP         (PIO_PERIPH2 | PIO_PORT_PIOC | PIO_PIN16)
#define PIO_PS2_SCK     (PIO_PERIPH2 | PIO_PORT_PIOI | PIO_PIN20)

/* PS2 */

#define PIO_PS2_SCK1_1  (PIO_PERIPH3 | PIO_PORT_PIOI | PIO_PIN14)
#define PIO_PS2_SCK1_2  (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN12)
#define PIO_PS2_SDA0    (PIO_PERIPH2 | PIO_PORT_PIOI | PIO_PIN21)
#define PIO_PS2_SDA1_1  (PIO_PERIPH3 | PIO_PORT_PIOI | PIO_PIN15)
#define PIO_PS2_SDA1_2  (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN13)

/* PWM */

#define PIO_PWM0        (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN2)
#define PIO_PWM1        (PIO_PERIPH2 | PIO_PORT_PIOI | PIO_PIN3)

/* SD/MMC Card Interface */

#define PIO_SDC0_CLK    (PIO_PERIPH2 | PIO_PORT_PIOF | PIO_PIN2)
#define PIO_SDC0_CMD    (PIO_PERIPH2 | PIO_PORT_PIOF | PIO_PIN3)
#define PIO_SDC0_D0     (PIO_PERIPH2 | PIO_PORT_PIOF | PIO_PIN1)
#define PIO_SDC0_D1     (PIO_PERIPH2 | PIO_PORT_PIOF | PIO_PIN0)
#define PIO_SDC0_D2     (PIO_PERIPH2 | PIO_PORT_PIOF | PIO_PIN5)
#define PIO_SDC0_D3     (PIO_PERIPH2 | PIO_PORT_PIOF | PIO_PIN4)

#define PIO_SDC1_CLK    (PIO_PERIPH4 | PIO_PORT_PIOG | PIO_PIN1)
#define PIO_SDC1_CLK    (PIO_PERIPH5 | PIO_PORT_PIOH | PIO_PIN23)
#define PIO_SDC1_CMD    (PIO_PERIPH4 | PIO_PORT_PIOG | PIO_PIN0)
#define PIO_SDC1_CMD    (PIO_PERIPH5 | PIO_PORT_PIOH | PIO_PIN22)
#define PIO_SDC1_D0_1   (PIO_PERIPH4 | PIO_PORT_PIOG | PIO_PIN2)
#define PIO_SDC1_D0_2   (PIO_PERIPH5 | PIO_PORT_PIOH | PIO_PIN24)
#define PIO_SDC1_D1_1   (PIO_PERIPH4 | PIO_PORT_PIOG | PIO_PIN3)
#define PIO_SDC1_D1_2   (PIO_PERIPH5 | PIO_PORT_PIOH | PIO_PIN25)
#define PIO_SDC1_D2_1   (PIO_PERIPH4 | PIO_PORT_PIOG | PIO_PIN4)
#define PIO_SDC1_D2_2   (PIO_PERIPH5 | PIO_PORT_PIOH | PIO_PIN26)
#define PIO_SDC1_D3_1   (PIO_PERIPH4 | PIO_PORT_PIOG | PIO_PIN5)
#define PIO_SDC1_D3_2   (PIO_PERIPH5 | PIO_PORT_PIOH | PIO_PIN27)

#define PIO_SDC2_CLK    (PIO_PERIPH3 | PIO_PORT_PIOC | PIO_PIN7)
#define PIO_SDC2_CMD    (PIO_PERIPH3 | PIO_PORT_PIOC | PIO_PIN6)
#define PIO_SDC2_D0     (PIO_PERIPH3 | PIO_PORT_PIOC | PIO_PIN8)
#define PIO_SDC2_D1     (PIO_PERIPH3 | PIO_PORT_PIOC | PIO_PIN9)
#define PIO_SDC2_D2     (PIO_PERIPH3 | PIO_PORT_PIOC | PIO_PIN10)
#define PIO_SDC2_D3     (PIO_PERIPH3 | PIO_PORT_PIOC | PIO_PIN11)

#define PIO_SDC3_CLK    (PIO_PERIPH2 | PIO_PORT_PIOI | PIO_PIN5)
#define PIO_SDC3_CMD    (PIO_PERIPH2 | PIO_PORT_PIOI | PIO_PIN4)
#define PIO_SDC3_D0     (PIO_PERIPH2 | PIO_PORT_PIOI | PIO_PIN6)
#define PIO_SDC3_D1     (PIO_PERIPH2 | PIO_PORT_PIOI | PIO_PIN7)
#define PIO_SDC3_D2     (PIO_PERIPH2 | PIO_PORT_PIOI | PIO_PIN8)
#define PIO_SDC3_D3     (PIO_PERIPH2 | PIO_PORT_PIOI | PIO_PIN9)

/* SMC */

#define PIO_SMC_DET     (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN23)
#define PIO_SMC_RST_1   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN25)
#define PIO_SMC_RST_2   (PIO_PERIPH5 | PIO_PORT_PIOH | PIO_PIN13)
#define PIO_SMC_SCK_1   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN26)
#define PIO_SMC_SCK_2   (PIO_PERIPH5 | PIO_PORT_PIOH | PIO_PIN18)
#define PIO_SMC_SDA_1   (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN27)
#define PIO_SMC_SDA_2   (PIO_PERIPH5 | PIO_PORT_PIOH | PIO_PIN19)

#define PIO_SMC_VCCEN_1 (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN24)
#define PIO_SMC_VCCEN_2 (PIO_PERIPH5 | PIO_PORT_PIOH | PIO_PIN17)
#define PIO_SMC_VPPEN_1 (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN21)
#define PIO_SMC_VPPEN_2 (PIO_PERIPH4 | PIO_PORT_PIOE | PIO_PIN5)
#define PIO_SMC_VPPEN_3 (PIO_PERIPH5 | PIO_PORT_PIOH | PIO_PIN14)
#define PIO_SMC_VPPPP_1 (PIO_PERIPH3 | PIO_PORT_PIOD | PIO_PIN22)
#define PIO_SMC_VPPPP_2 (PIO_PERIPH5 | PIO_PORT_PIOH | PIO_PIN15)

/* SPI */

#define PIO_SPI0_CLK_1  (PIO_PERIPH3 | PIO_PORT_PIOC | PIO_PIN2)
#define PIO_SPI0_CLK_2  (PIO_PERIPH2 | PIO_PORT_PIOI | PIO_PIN11)
#define PIO_SPI0_CS0_1  (PIO_PERIPH3 | PIO_PORT_PIOC | PIO_PIN23)
#define PIO_SPI0_CS0_2  (PIO_PERIPH2 | PIO_PORT_PIOI | PIO_PIN10)
#define PIO_SPI0_CS1    (PIO_PERIPH2 | PIO_PORT_PIOI | PIO_PIN14)
#define PIO_SPI0_MISO_1 (PIO_PERIPH3 | PIO_PORT_PIOC | PIO_PIN1)
#define PIO_SPI0_MISO_2 (PIO_PERIPH2 | PIO_PORT_PIOI | PIO_PIN13)
#define PIO_SPI0_MOSI_1 (PIO_PERIPH3 | PIO_PORT_PIOC | PIO_PIN0)
#define PIO_SPI0_MOSI_2 (PIO_PERIPH2 | PIO_PORT_PIOI | PIO_PIN12)

#define PIO_SPI1_CLK_1  (PIO_PERIPH2 | PIO_PORT_PIOI | PIO_PIN17)
#define PIO_SPI1_CLK_2  (PIO_PERIPH3 | PIO_PORT_PIOA | PIO_PIN1)
#define PIO_SPI1_CS0_1  (PIO_PERIPH2 | PIO_PORT_PIOI | PIO_PIN16)
#define PIO_SPI1_CS0_2  (PIO_PERIPH3 | PIO_PORT_PIOA | PIO_PIN0)
#define PIO_SPI1_CS1_1  (PIO_PERIPH3 | PIO_PORT_PIOA | PIO_PIN4)
#define PIO_SPI1_CS1_2  (PIO_PERIPH2 | PIO_PORT_PIOI | PIO_PIN15)
#define PIO_SPI1_MISO_1 (PIO_PERIPH2 | PIO_PORT_PIOI | PIO_PIN19)
#define PIO_SPI1_MISO_2 (PIO_PERIPH3 | PIO_PORT_PIOA | PIO_PIN3)
#define PIO_SPI1_MOSI_1 (PIO_PERIPH2 | PIO_PORT_PIOI | PIO_PIN18)
#define PIO_SPI1_MOSI_2 (PIO_PERIPH3 | PIO_PORT_PIOA | PIO_PIN2)

#define PIO_SPI2_CLK_1  (PIO_PERIPH3 | PIO_PORT_PIOC | PIO_PIN20)
#define PIO_SPI2_CLK_2  (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN15)
#define PIO_SPI2_CS0_1  (PIO_PERIPH3 | PIO_PORT_PIOC | PIO_PIN19)
#define PIO_SPI2_CS0_2  (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN14)
#define PIO_SPI2_CS1    (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN13)
#define PIO_SPI2_MISO_1 (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN17)
#define PIO_SPI2_MISO_2 (PIO_PERIPH3 | PIO_PORT_PIOC | PIO_PIN22)
#define PIO_SPI2_MOSI_1 (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN16)
#define PIO_SPI2_MOSI_2 (PIO_PERIPH3 | PIO_PORT_PIOC | PIO_PIN21)

#define PIO_SPI3_CLK    (PIO_PERIPH3 | PIO_PORT_PIOA | PIO_PIN6)
#define PIO_SPI3_CS0    (PIO_PERIPH3 | PIO_PORT_PIOA | PIO_PIN5)
#define PIO_SPI3_CS1    (PIO_PERIPH3 | PIO_PORT_PIOA | PIO_PIN9)
#define PIO_SPI3_MISO   (PIO_PERIPH3 | PIO_PORT_PIOA | PIO_PIN8)
#define PIO_SPI3_MOSI   (PIO_PERIPH3 | PIO_PORT_PIOA | PIO_PIN7)

/* ? */

#define PIO_STANBYWFI   (PIO_PERIPH4 | PIO_PORT_PIOB | PIO_PIN3)

/* ? */

#define PIO_TCLKIN0     (PIO_PERIPH4 | PIO_PORT_PIOI | PIO_PIN14)
#define PIO_TCLKIN1     (PIO_PERIPH4 | PIO_PORT_PIOI | PIO_PIN15)

/* Touchscreen */

#define PIO_TS0_CLK     (PIO_PERIPH2 | PIO_PORT_PIOE | PIO_PIN0)
#define PIO_TS0_D0      (PIO_PERIPH2 | PIO_PORT_PIOE | PIO_PIN4)
#define PIO_TS0_D1      (PIO_PERIPH2 | PIO_PORT_PIOE | PIO_PIN5)
#define PIO_TS0_D2      (PIO_PERIPH2 | PIO_PORT_PIOE | PIO_PIN6)
#define PIO_TS0_D3      (PIO_PERIPH2 | PIO_PORT_PIOE | PIO_PIN7)
#define PIO_TS0_D4      (PIO_PERIPH2 | PIO_PORT_PIOE | PIO_PIN8)
#define PIO_TS0_D5      (PIO_PERIPH2 | PIO_PORT_PIOE | PIO_PIN9)
#define PIO_TS0_D6      (PIO_PERIPH2 | PIO_PORT_PIOE | PIO_PIN10)
#define PIO_TS0_D7      (PIO_PERIPH2 | PIO_PORT_PIOE | PIO_PIN11)
#define PIO_TS0_DVLD    (PIO_PERIPH2 | PIO_PORT_PIOE | PIO_PIN3)
#define PIO_TS0_ERR     (PIO_PERIPH2 | PIO_PORT_PIOE | PIO_PIN1)
#define PIO_TS0_SYNC    (PIO_PERIPH2 | PIO_PORT_PIOE | PIO_PIN2)

#define PIO_TS1_CLK     (PIO_PERIPH2 | PIO_PORT_PIOG | PIO_PIN0)
#define PIO_TS1_D0      (PIO_PERIPH2 | PIO_PORT_PIOG | PIO_PIN4)
#define PIO_TS1_D1      (PIO_PERIPH2 | PIO_PORT_PIOG | PIO_PIN5)
#define PIO_TS1_D2      (PIO_PERIPH2 | PIO_PORT_PIOG | PIO_PIN6)
#define PIO_TS1_D3      (PIO_PERIPH2 | PIO_PORT_PIOG | PIO_PIN7)
#define PIO_TS1_D4      (PIO_PERIPH2 | PIO_PORT_PIOG | PIO_PIN8)
#define PIO_TS1_D5      (PIO_PERIPH2 | PIO_PORT_PIOG | PIO_PIN9)
#define PIO_TS1_D6      (PIO_PERIPH2 | PIO_PORT_PIOG | PIO_PIN10)
#define PIO_TS1_D7      (PIO_PERIPH2 | PIO_PORT_PIOG | PIO_PIN11)
#define PIO_TS1_DVLD    (PIO_PERIPH2 | PIO_PORT_PIOG | PIO_PIN3)
#define PIO_TS1_ERR     (PIO_PERIPH2 | PIO_PORT_PIOG | PIO_PIN1)
#define PIO_TS1_SYNC    (PIO_PERIPH2 | PIO_PORT_PIOG | PIO_PIN2)

/* TWI */

#define PIO_TWI0_SCK_1  (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN0)
#define PIO_TWI0_SDA_2  (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN1)
#define PIO_TWI1_SCK_1  (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN18)
#define PIO_TWI1_SDA_2  (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN19)

#define PIO_TWI2_SCK    (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN20)
#define PIO_TWI2_SDA    (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN21)

/* UARTs */

#define PIO_UART0_RX_1  (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN23)
#define PIO_UART0_RX_2  (PIO_PERIPH4 | PIO_PORT_PIOF | PIO_PIN4)
#define PIO_UART0_TX_1  (PIO_PERIPH2 | PIO_PORT_PIOB | PIO_PIN22)
#define PIO_UART0_TX_2  (PIO_PERIPH4 | PIO_PORT_PIOF | PIO_PIN2)

#define PIO_UART1_CTS   (PIO_PERIPH4 | PIO_PORT_PIOA | PIO_PIN13)
#define PIO_UART1_DCD   (PIO_PERIPH4 | PIO_PORT_PIOA | PIO_PIN17)
#define PIO_UART1_DSR   (PIO_PERIPH4 | PIO_PORT_PIOA | PIO_PIN15)
#define PIO_UART1_DTR   (PIO_PERIPH4 | PIO_PORT_PIOA | PIO_PIN14)
#define PIO_UART1_RING  (PIO_PERIPH4 | PIO_PORT_PIOA | PIO_PIN16)
#define PIO_UART1_RTS   (PIO_PERIPH4 | PIO_PORT_PIOA | PIO_PIN12)
#define PIO_UART1_RX    (PIO_PERIPH4 | PIO_PORT_PIOA | PIO_PIN11)
#define PIO_UART1_TX    (PIO_PERIPH4 | PIO_PORT_PIOA | PIO_PIN10)

#define PIO_UART2_CTS_1 (PIO_PERIPH3 | PIO_PORT_PIOI | PIO_PIN17)
#define PIO_UART2_CTS_2 (PIO_PERIPH4 | PIO_PORT_PIOA | PIO_PIN1)
#define PIO_UART2_RTS_1 (PIO_PERIPH3 | PIO_PORT_PIOI | PIO_PIN16)
#define PIO_UART2_RTS_2 (PIO_PERIPH4 | PIO_PORT_PIOA | PIO_PIN0)
#define PIO_UART2_RX_1  (PIO_PERIPH3 | PIO_PORT_PIOI | PIO_PIN19)
#define PIO_UART2_RX_2  (PIO_PERIPH4 | PIO_PORT_PIOA | PIO_PIN3)
#define PIO_UART2_TX_1  (PIO_PERIPH3 | PIO_PORT_PIOI | PIO_PIN18)
#define PIO_UART2_TX_2  (PIO_PERIPH4 | PIO_PORT_PIOA | PIO_PIN2)

#define PIO_UART3_CTS_1 (PIO_PERIPH4 | PIO_PORT_PIOG | PIO_PIN9)
#define PIO_UART3_CTS_2 (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN3)
#define PIO_UART3_RTS_1 (PIO_PERIPH4 | PIO_PORT_PIOG | PIO_PIN8)
#define PIO_UART3_RTS_2 (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN2)
#define PIO_UART3_RX_1  (PIO_PERIPH4 | PIO_PORT_PIOG | PIO_PIN7)
#define PIO_UART3_RX_2  (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN1)
#define PIO_UART3_TX_1  (PIO_PERIPH4 | PIO_PORT_PIOG | PIO_PIN6)
#define PIO_UART3_TX_2  (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN0)

#define PIO_UART4_RX_1    (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN5)
#define PIO_UART4_RX_2    (PIO_PERIPH4 | PIO_PORT_PIOG | PIO_PIN11)
#define PIO_UART4_TX_1    (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN4)
#define PIO_UART4_TX_2    (PIO_PERIPH4 | PIO_PORT_PIOG | PIO_PIN10)

#define PIO_UART5_RX_1    (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN7)
#define PIO_UART5_RX_2    (PIO_PERIPH3 | PIO_PORT_PIOI | PIO_PIN11)
#define PIO_UART5_TX_1    (PIO_PERIPH4 | PIO_PORT_PIOH | PIO_PIN6)
#define PIO_UART5_TX_2    (PIO_PERIPH3 | PIO_PORT_PIOI | PIO_PIN10)

#define PIO_UART6_RX_1    (PIO_PERIPH3 | PIO_PORT_PIOA | PIO_PIN13)
#define PIO_UART6_RX_2    (PIO_PERIPH3 | PIO_PORT_PIOI | PIO_PIN13)
#define PIO_UART6_TX_1    (PIO_PERIPH3 | PIO_PORT_PIOA | PIO_PIN12)
#define PIO_UART6_TX_2    (PIO_PERIPH3 | PIO_PORT_PIOI | PIO_PIN12)

#define PIO_UART7_RX_1    (PIO_PERIPH3 | PIO_PORT_PIOI | PIO_PIN21)
#define PIO_UART7_RX_2    (PIO_PERIPH3 | PIO_PORT_PIOA | PIO_PIN15)
#define PIO_UART7_TX_1    (PIO_PERIPH3 | PIO_PORT_PIOI | PIO_PIN20)
#define PIO_UART7_TX_2    (PIO_PERIPH3 | PIO_PORT_PIOA | PIO_PIN14)

#endif /* __ARCH_ARM_SRC_A1X_HARDWARE_A10_PIOCFG_H */
