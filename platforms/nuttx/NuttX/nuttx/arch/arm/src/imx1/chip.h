/****************************************************************************
 * arch/arm/src/imx1/chip.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_IMX1_CHIP_H
#define __ARCH_ARM_SRC_IMX1_CHIP_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include "imx_memorymap.h"
#include "imx_system.h"
#include "imx_wdog.h"
#include "imx_timer.h"
#include "imx_rtc.h"
#include "imx_uart.h"
#include "imx_dma.h"
#include "imx_usbd.h"
#include "imx_i2c.h"
#include "imx_cspi.h"
#include "imx_gpio.h"
#include "imx_eim.h"
#include "imx_aitc.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/****************************************************************************
 * Inline Functions
 ****************************************************************************/

#endif /* __ARCH_ARM_SRC_IMX1_CHIP_H */
