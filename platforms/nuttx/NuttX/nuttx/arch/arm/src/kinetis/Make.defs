############################################################################
# arch/arm/src/kinetis/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

include armv7-m/Make.defs

# Required Kinetis files

CHIP_CSRCS  = kinetis_allocateheap.c kinetis_clockconfig.c kinetis_clrpend.c
CHIP_CSRCS += kinetis_irq.c kinetis_lowputc.c kinetis_pin.c kinetis_pingpio.c
CHIP_CSRCS += kinetis_serialinit.c kinetis_start.c kinetis_uid.c kinetis_wdog.c
CHIP_CSRCS += kinetis_cfmconfig.c kinetis_mpuinit.c

# Configuration-dependent Kinetis K files

ifneq ($(CONFIG_ARCH_IDLE_CUSTOM),y)
CHIP_CSRCS += kinetis_idle.c
endif

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += kinetis_timerisr.c
endif

ifeq ($(CONFIG_BUILD_PROTECTED),y)
CHIP_CSRCS += kinetis_userspace.c
endif

ifeq ($(CONFIG_KINETIS_GPIOIRQ),y)
CHIP_CSRCS += kinetis_pinirq.c
endif

ifeq ($(CONFIG_DEBUG_GPIO_INFO),y)
CHIP_CSRCS += kinetis_pindump.c
endif

ifeq ($(CONFIG_KINETIS_UART),y)
CHIP_CSRCS += kinetis_serial.c
endif

ifeq ($(CONFIG_KINETIS_LPUART),y)
CHIP_CSRCS += kinetis_lpserial.c
endif

ifeq ($(CONFIG_KINETIS_SDHC),y)
CHIP_CSRCS += kinetis_sdhc.c
endif

ifeq ($(CONFIG_SPI),y)
CHIP_CSRCS += kinetis_spi.c
endif

ifeq ($(CONFIG_USBDEV),y)
CHIP_CSRCS += kinetis_usbdev.c
endif

ifeq ($(CONFIG_USBHOST),y)
ifneq ($(CONFIG_KINETIS_USBHS),y)
CHIP_CSRCS += kinetis_usbhost.c
endif
endif

ifeq ($(CONFIG_USBHOST),y)
ifeq ($(CONFIG_KINETIS_USBHS),y)
CHIP_CSRCS += kinetis_usbhshost.c
endif
endif

ifeq ($(CONFIG_KINETIS_EDMA),y)
CHIP_CSRCS += kinetis_edma.c kinetis_pindma.c
endif

ifeq ($(CONFIG_PWM),y)
CHIP_CSRCS += kinetis_pwm.c
endif

ifeq ($(CONFIG_I2C),y)
CHIP_CSRCS += kinetis_i2c.c
endif

ifeq ($(CONFIG_RTC),y)
CHIP_CSRCS += kinetis_rtc.c
ifeq ($(CONFIG_RTC_DRIVER),y)
CHIP_CSRCS += kinetis_rtc_lowerhalf.c
endif
endif

ifeq ($(CONFIG_NET),y)
ifeq ($(CONFIG_KINETIS_ENET),y)
CHIP_CSRCS += kinetis_enet.c
endif

ifeq ($(CONFIG_NET_CAN),y)
ifeq ($(CONFIG_KINETIS_FLEXCAN),y)
CHIP_CSRCS += kinetis_flexcan.c
endif
endif

endif
