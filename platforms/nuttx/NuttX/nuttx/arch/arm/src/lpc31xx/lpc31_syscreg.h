/****************************************************************************
 * arch/arm/src/lpc31xx/lpc31_syscreg.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_LPC31XX_LPC31_SYSCREG_H
#define __ARCH_ARM_SRC_LPC31XX_LPC31_SYSCREG_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include "lpc31_memorymap.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* SYSCREG register base address offset into the APB0 domain ****************/

#define LPC31_SYSCREG_VBASE                    (LPC31_APB0_VADDR+LPC31_APB0_SYSCREG_OFFSET)
#define LPC31_SYSCREG_PBASE                    (LPC31_APB0_PADDR+LPC31_APB0_SYSCREG_OFFSET)

/* SYSCREG register offsets (with respect to the SYSCREG base) **************/

/* Miscellaneous system configuration registers, part1 */

                                             /* 0x000-0x004: Reserved */
#define LPC31_SYSCREG_EBIMPMCPRIO_OFFSET       0x008 /* Priority of MPMC channel for EBI interface */
#define LPC31_SYSCREG_EBNANDCPRIO_OFFSET       0x00c /* Priority of NAND controller channel for EBI interface */
#define LPC31_SYSCREG_EBIUNUSEDPRIO_OFFSET     0x010 /* Priority of unused channel */
#define LPC31_SYSCREG_RINGOSCCFG_OFFSET        0x014 /* RING oscillator configuration register */
#define LPC31_SYSCREG_ADCPDADC10BITS_OFFSET    0x018 /* Powerdown register of ADC 10bits */
#define LPC31_SYSCREG_CGUDYNHP0_OFFSET         0x01c /* reserved */
#define LPC31_SYSCREG_CGUDYNHP1_OFFSET         0x020 /* reserved */
#define LPC31_SYSCREG_ABCCFG_OFFSET            0x024 /* AHB burst control register */
#define LPC31_SYSCREG_SDMMCCFG_OFFSET          0x028 /* SD_MMC (MCI) configuration register */
#define LPC31_SYSCREG_MCIDELAYMODES_OFFSET     0x02c /* Delay register for the SD_MMC (MCI) clocks */

/* USB configuration registers */

#define LPC31_SYSCREG_USB_ATXPLLPDREG_OFFSET   0x030 /* Power down register of USB ATX PLL */
#define LPC31_SYSCREG_USB_OTGCFG_OFFSET        0x034 /* USB OTG configuration register */
#define LPC31_SYSCREG_USB_OTGPORTINDCTL_OFFSET 0x038 /* USB OTG port indicator LED control outputs */
                                                     /* 0x03c: Reserved */
#define LPC31_SYSCREG_USB_PLLNDEC_OFFSET       0x040 /* USB OTG PLL configuration register NOEC */
#define LPC31_SYSCREG_USB_PLLMDEC_OFFSET       0x044 /* USB OTG PLL configuration register MDEC */
#define LPC31_SYSCREG_USB_PLLPDEC_OFFSET       0x048 /* USB OTG PLL configuration register PDEC */
#define LPC31_SYSCREG_USB_PLLSELR_OFFSET       0x04c /* USB OTG PLL configuration register SELR */
#define LPC31_SYSCREG_USB_PLLSELI_OFFSET       0x050 /* USB OTG PLL configuration register SELI */
#define LPC31_SYSCREG_USB_PLLSELP_OFFSET       0x054 /* USB OTG PLL configuration register SELP */

/* ISRAM/ISROM configuration registers */

#define LPC31_SYSCREG_ISRAM0_LATENCYCFG_OFFSET 0x058 /* Internal SRAM 0 latency configuration register */
#define LPC31_SYSCREG_ISRAM1_LATENCYCFG_OFFSET 0x05c /* Internal SRAM 1 latency configuration register */
#define LPC31_SYSCREG_ISROM_LATENCYCFG_OFFSET  0x060 /* Internal SROM latency configuration register */

/* MPMC configuration registers */

#define LPC31_SYSCREG_MPMC_AHBMISC_OFFSET      0x064 /* Configuration register of MPMC */
#define LPC31_SYSCREG_MPMC_DELAYMODES_OFFSET   0x068 /* Configuration of MPMC clock delay */
#define LPC31_SYSCREG_MPMC_WAITRD0_OFFSET      0x06c /* Configuration of the wait cycles for read transfers */
#define LPC31_SYSCREG_MPMC_WAITRD1_OFFSET      0x070 /* Configuration of the wait cycles for read transfers */
#define LPC31_SYSCREG_MPMC_WIREEBIMSZ_OFFSET   0x074 /* Configuration of the memory width for MPMC */
#define LPC31_SYSCREG_MPMC_TESTMODE0_OFFSET    0x078 /* Configuration for refresh generation of MPMC */
#define LPC31_SYSCREG_MPMC_TESTMODE1_OFFSET    0x07c /* Configuration for refresh generation of MPMC */

/* Miscellaneous system configuration registers, part 2 */

#define LPC31_SYSCREG_AHB0EXTPRIO_OFFSET       0x080 /* Priority of the AHB masters */
#define LPC31_SYSCREG_ARM926SHADOWPTR_OFFSET   0x084 /* Memory mapping */
                                                     /* 0x088-0x08c reserved */

/* Pin multiplexing control registers */

#define LPC31_SYSCREG_MUX_LCDEBISEL_OFFSET     0x090 /* Selects between lcd_interface and EBI pins */
#define LPC31_SYSCREG_MUX_GPIOMCISEL_OFFSET    0x094 /* Selects between GPIO and MCI pins */
#define LPC31_SYSCREG_MUX_NANDMCISEL_OFFSET    0x098 /* Selects between NAND flash controller and MCI pins */
#define LPC31_SYSCREG_MUX_UARTSPISEL_OFFSET    0x09c /* Selects between UART and SPI pins */
#define LPC31_SYSCREG_MUX_I2STXPCMSEL_OFFSET   0x0a0 /* Selects between I2STX and PCM pins */

/* Pad configuration registers */

#define LPC31_SYSCREG_PAD_EBID9_OFFSET         0x0a4 /* Control pad EBI_D_9 */
#define LPC31_SYSCREG_PAD_EBID10_OFFSET        0x0a8 /* Control pad EBI_D_10 */
#define LPC31_SYSCREG_PAD_EBID11_OFFSET        0x0ac /* Control pad EBI_D_11 */
#define LPC31_SYSCREG_PAD_EBID12_OFFSET        0x0b0 /* Control pad EBI_D_12 */
#define LPC31_SYSCREG_PAD_EBID13_OFFSET        0x0b4 /* Control pad EBI_D_13 */
#define LPC31_SYSCREG_PAD_EBID14_OFFSET        0x0b8 /* Control pad EBI_D_14 */
#define LPC31_SYSCREG_PAD_I2SRXBCK0_OFFSET     0x0bc /* Control pad I2SRX_BCK0 */
#define LPC31_SYSCREG_PAD_MGPIO9_OFFSET        0x0c0 /* Control pad MGPIO9 */
#define LPC31_SYSCREG_PAD_MGPIO6_OFFSET        0x0c4 /* Control pad MGPIO6 */
#define LPC31_SYSCREG_PAD_MLCDDB7_OFFSET       0x0c8 /* Control pad MLCD_DB_7 */
#define LPC31_SYSCREG_PAD_MLCDDB4_OFFSET       0x0cc /* Control pad MLCD_DB_4 */
#define LPC31_SYSCREG_PAD_MLCDDB2_OFFSET       0x0d0 /* Control pad MLCD_DB_2 */
#define LPC31_SYSCREG_PAD_MNANDRYBN0_OFFSET    0x0d4 /* Control pad MNAND_RYBN0 */
#define LPC31_SYSCREG_PAD_GPIO1_OFFSET         0x0d8 /* Control pad GPIO1 */
#define LPC31_SYSCREG_PAD_EBID4_OFFSET         0x0dc /* Control pad EBI_D_4 */
#define LPC31_SYSCREG_PAD_MI2STXCLK0_OFFSET    0x0e0 /* Control pad MI2STX_CLK0 */
#define LPC31_SYSCREG_PAD_MI2STXBCK0_OFFSET    0x0e4 /* Control pad MI2STX_BCK0 */
#define LPC31_SYSCREG_PAD_EBIA1CLE_OFFSET      0x0e8 /* Control pad EBI_A_1_CLE */
#define LPC31_SYSCREG_PAD_EBINCASBLOUT0_OFFSET 0x0ec /* Control pad EBI_NCAS_BLOUT_0 */
#define LPC31_SYSCREG_PAD_NANDNCS3_OFFSET      0x0f0 /* Control pad NAND_NCS_3 */
#define LPC31_SYSCREG_PAD_MLCDDB0_OFFSET       0x0f4 /* Control pad MLCD_DB_0 */
#define LPC31_SYSCREG_PAD_EBIDQM0NOE_OFFSET    0x0f8 /* Control pad EBI_DQM_0_NOE */
#define LPC31_SYSCREG_PAD_EBID0_OFFSET         0x0fc /* Control pad EBI_D_0 */
#define LPC31_SYSCREG_PAD_EBID1_OFFSET         0x100 /* Control pad EBI_D_1 */
#define LPC31_SYSCREG_PAD_EBID2_OFFSET         0x104 /* Control pad EBI_D_2 */
#define LPC31_SYSCREG_PAD_EBID3_OFFSET         0x108 /* Control pad EBI_D_3 */
#define LPC31_SYSCREG_PAD_EBID5_OFFSET         0x10c /* Control pad EBI_D_5 */
#define LPC31_SYSCREG_PAD_EBID6_OFFSET         0x110 /* Control pad EBI_D_6 */
#define LPC31_SYSCREG_PAD_EBID7_OFFSET         0x114 /* Control pad EBI_D_7 */
#define LPC31_SYSCREG_PAD_EBID8_OFFSET         0x118 /* Control pad EBI_D_8 */
#define LPC31_SYSCREG_PAD_EBID15_OFFSET        0x11c /* Control pad EBI_D_15 */
#define LPC31_SYSCREG_PAD_I2STXDATA1_OFFSET    0x120 /* Control pad I2STX_DATA1 */
#define LPC31_SYSCREG_PAD_I2STXBCK1_OFFSET     0x124 /* Control pad I2STX_BCK1 */
#define LPC31_SYSCREG_PAD_I2STXWS1_OFFSET      0x128 /* Control pad I2STX_WS1 */
#define LPC31_SYSCREG_PAD_I2SRXDATA0_OFFSET    0x12c /* Control pad I2SRX_DATA0 */
#define LPC31_SYSCREG_PAD_I2SRXWS0_OFFSET      0x130 /* Control pad I2SRX_WS0 */
#define LPC31_SYSCREG_PAD_I2SRXDATA1_OFFSET    0x134 /* Control pad I2SRX_DATA1 */
#define LPC31_SYSCREG_PAD_I2SRXBCK1_OFFSET     0x138 /* Control pad I2SRX_BCK1 */
#define LPC31_SYSCREG_PAD_I2SRXWS1_OFFSET      0x13c /* Control pad I2SRX_WS1 */
#define LPC31_SYSCREG_PAD_SYSCLKO_OFFSET       0x140 /* Control pad SYSCLK_O */
#define LPC31_SYSCREG_PAD_PWMDATA_OFFSET       0x144 /* Control pad PWM_DATA */
#define LPC31_SYSCREG_PAD_UARTRXD_OFFSET       0x148 /* Control pad UART_RXD */
#define LPC31_SYSCREG_PAD_UARTTXD_OFFSET       0x14c /* Control pad UART_TXD */
#define LPC31_SYSCREG_PAD_I2CSDA1_OFFSET       0x150 /* Control pad I2C_SDA1 */
#define LPC31_SYSCREG_PAD_I2CSCL1_OFFSET       0x154 /* Control pad I2C_SCL1 */
#define LPC31_SYSCREG_PAD_CLK256FSO_OFFSET     0x158 /* Control pad CLK_256FS_O */
#define LPC31_SYSCREG_PAD_GPIO0_OFFSET         0x15c /* Control pad GPIO0 */
#define LPC31_SYSCREG_PAD_GPIO2_OFFSET         0x160 /* Control pad GPIO2 */
#define LPC31_SYSCREG_PAD_GPIO3_OFFSET         0x164 /* Control pad GPIO3 */
#define LPC31_SYSCREG_PAD_GPIO4_OFFSET         0x168 /* Control pad GPIO4 */
#define LPC31_SYSCREG_PAD_GPIO11_OFFSET        0x16c /* Control pad GPIO11 */
#define LPC31_SYSCREG_PAD_GPIO12_OFFSET        0x170 /* Control pad GPIO12 */
#define LPC31_SYSCREG_PAD_GPIO13_OFFSET        0x174 /* Control pad GPIO13 */
#define LPC31_SYSCREG_PAD_GPIO14_OFFSET        0x178 /* Control pad GPIO14 */
#define LPC31_SYSCREG_PAD_GPIO15_OFFSET        0x17c /* Control pad GPIO15 */
#define LPC31_SYSCREG_PAD_GPIO16_OFFSET        0x180 /* Control pad GPIO16 */
#define LPC31_SYSCREG_PAD_GPIO17_OFFSET        0x184 /* Control pad GPIO17 */
#define LPC31_SYSCREG_PAD_GPIO18_OFFSET        0x188 /* Control pad GPIO18 */
#define LPC31_SYSCREG_PAD_GPIO19_OFFSET        0x18c /* Control pad GPIO19 */
#define LPC31_SYSCREG_PAD_GPIO20_OFFSET        0x190 /* Control pad GPIO20 */
#define LPC31_SYSCREG_PAD_SPIMISO_OFFSET       0x194 /* Control pad SPI_MISO */
#define LPC31_SYSCREG_PAD_SPIMOSI_OFFSET       0x198 /* Control pad SPI_MOSI */
#define LPC31_SYSCREG_PAD_SPICSIN_OFFSET       0x19c /* Control pad SPI_CS_IN */
#define LPC31_SYSCREG_PAD_SPISCK_OFFSET        0x1a0 /* Control pad SPI_SCK */
#define LPC31_SYSCREG_PAD_SPICSOUT0_OFFSET     0x1a4 /* Control pad SPI_CS_OUT0 */
#define LPC31_SYSCREG_PAD_NANDNCS0_OFFSET      0x1a8 /* Control pad NAND_NCS_0 */
#define LPC31_SYSCREG_PAD_NANDNCS1_OFFSET      0x1ac /* Control pad NAND_NCS_1 */
#define LPC31_SYSCREG_PAD_NANDNCS2_OFFSET      0x1b0 /* Control pad NAND_NCS_2 */
#define LPC31_SYSCREG_PAD_MLCDCSB_OFFSET       0x1b4 /* Control pad MLCD_CSB */
#define LPC31_SYSCREG_PAD_MLCDDB1_OFFSET       0x1b8 /* Control pad MLCD_DB_1 */
#define LPC31_SYSCREG_PAD_MLCDERD_OFFSET       0x1bc /* Control pad MLCD_E_RD */
#define LPC31_SYSCREG_PAD_MLCDRS_OFFSET        0x1c0 /* Control pad MLCD_RS */
#define LPC31_SYSCREG_PAD_MLCDRWWR_OFFSET      0x1c4 /* Control pad MLCD_RW_WR */
#define LPC31_SYSCREG_PAD_MLCDDB3_OFFSET       0x1c8 /* Control pad MLCD_DB_3 */
#define LPC31_SYSCREG_PAD_MLCDDB5_OFFSET       0x1cc /* Control pad MLCD_DB_5 */
#define LPC31_SYSCREG_PAD_MLCDDB6_OFFSET       0x1d0 /* Control pad MLCD_DB_6 */
#define LPC31_SYSCREG_PAD_MLCDDB8_OFFSET       0x1d4 /* Control pad MLCD_DB_8 */
#define LPC31_SYSCREG_PAD_MLCDDB9_OFFSET       0x1d8 /* Control pad MLCD_DB_9 */
#define LPC31_SYSCREG_PAD_MLCDDB10_OFFSET      0x1dc /* Control pad MLCD_DB_10 */
#define LPC31_SYSCREG_PAD_MLCDDB11_OFFSET      0x1e0 /* Control pad MLCD_DB_11 */
#define LPC31_SYSCREG_PAD_MLCDDB12_OFFSET      0x1e4 /* Control pad MLCD_DB_12 */
#define LPC31_SYSCREG_PAD_MLCDDB13_OFFSET      0x1e8 /* Control pad MLCD_DB_13 */
#define LPC31_SYSCREG_PAD_MLCDDB14_OFFSET      0x1ec /* Control pad MLCD_DB_14 */
#define LPC31_SYSCREG_PAD_MLCDDB15_OFFSET      0x1f0 /* Control pad MLCD_DB_15 */
#define LPC31_SYSCREG_PAD_MGPIO5_OFFSET        0x1f4 /* Control pad MGPIO5 */
#define LPC31_SYSCREG_PAD_MGPIO7_OFFSET        0x1f8 /* Control pad MGPIO5 */
#define LPC31_SYSCREG_PAD_MGPIO8_OFFSET        0x1fc /* Control pad MGPIO8 */
#define LPC31_SYSCREG_PAD_MGPIO10_OFFSET       0x200 /* Control pad MGPIO10 */
#define LPC31_SYSCREG_PAD_MNANDRYBN1_OFFSET    0x204 /* Control pad MNAND_RYBN1 */
#define LPC31_SYSCREG_PAD_MNANDRYBN2_OFFSET    0x208 /* Control pad MNAND_RYBN2 */
#define LPC31_SYSCREG_PAD_MNANDRYBN3_OFFSET    0x20c /* Control pad MNAND_RYBN3 */
#define LPC31_SYSCREG_PAD_MUARTCTSN_OFFSET     0x210 /* Control pad MUART_CTS_N */
#define LPC31_SYSCREG_PAD_MI2STXDATA0_OFFSET   0x218 /* Control pad MI2STX_DATA0 */
#define LPC31_SYSCREG_PAD_MI2STXWS0_OFFSET     0x21c /* Control pad MI2STX_WS0 */
#define LPC31_SYSCREG_PAD_EBINRASBLOUT1_OFFSET 0x220 /* Control pad EBI_NRAS_BLOUT_1 */
#define LPC31_SYSCREG_PAD_EBIA0ALE_OFFSET      0x224 /* Control pad EBI_A_0_ALE */
#define LPC31_SYSCREG_PAD_EBINWE_OFFSET        0x228 /* Control pad EBI_NWE */
#define LPC31_SYSCREG_PAD_ESHCTRLSUP4_OFFSET   0x22c /* Control pad at 1.8 and 3.3V (Nandflash/EBI pads) */
#define LPC31_SYSCREG_PAD_ESHCTRLSUP8_OFFSET   0x230 /* Control pad at 1.8 and 3.3V (LCD interface/SDRAM pads) */

/* SYSCREG register (virtual) addresses *************************************/

/* Miscellaneous system configuration registers, part1 */

#define LPC31_SYSCREG_EBIMPMCPRIO              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_EBIMPMCPRIO_OFFSET)
#define LPC31_SYSCREG_EBNANDCPRIO              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_EBNANDCPRIO_OFFSET)
#define LPC31_SYSCREG_EBIUNUSEDPRIO            (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_EBIUNUSEDPRIO_OFFSET)
#define LPC31_SYSCREG_RINGOSCCFG               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_RINGOSCCFG_OFFSET)
#define LPC31_SYSCREG_ADCPDADC10BITS           (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_ADCPDADC10BITS_OFFSET)
#define LPC31_SYSCREG_ABCCFG                   (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_ABCCFG_OFFSET)
#define LPC31_SYSCREG_SDMMCCFG                 (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_SDMMCCFG_OFFSET)
#define LPC31_SYSCREG_MCIDELAYMODES            (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_MCIDELAYMODES_OFFSET)

/* USB configuration registers */

#define LPC31_SYSCREG_USB_ATXPLLPDREG          (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_USB_ATXPLLPDREG_OFFSET)
#define LPC31_SYSCREG_USB_OTGCFG               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_USB_OTGCFG_OFFSET)
#define LPC31_SYSCREG_USB_OTGPORTINDCTL        (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_USB_OTGPORTINDCTL_OFFSET)
#define LPC31_SYSCREG_USB_PLLNDEC              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_USB_PLLNDEC_OFFSET)
#define LPC31_SYSCREG_USB_PLLMDEC              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_USB_PLLMDEC_OFFSET)
#define LPC31_SYSCREG_USB_PLLPDEC              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_USB_PLLPDEC_OFFSET)
#define LPC31_SYSCREG_USB_PLLSELR              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_USB_PLLSELR_OFFSET)
#define LPC31_SYSCREG_USB_PLLSELI              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_USB_PLLSELI_OFFSET)
#define LPC31_SYSCREG_USB_PLLSELP              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_USB_PLLSELP_OFFSET)

/* ISRAM/ISROM configuration registers */

#define LPC31_SYSCREG_ISRAM0_LATENCYCFG        (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_ISRAM0_LATENCYCFG_OFFSET)
#define LPC31_SYSCREG_ISRAM1_LATENCYCFG        (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_ISRAM1_LATENCYCFG_OFFSET)
#define LPC31_SYSCREG_ISROM_LATENCYCFG         (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_ISROM_LATENCYCFG_OFFSET)

/* MPMC configuration registers */

#define LPC31_SYSCREG_MPMC_AHBMISC             (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_MPMC_AHBMISC_OFFSET)
#define LPC31_SYSCREG_MPMC_DELAYMODES          (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_MPMC_DELAYMODES_OFFSET)
#define LPC31_SYSCREG_MPMC_WAITRD0             (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_MPMC_WAITRD0_OFFSET)
#define LPC31_SYSCREG_MPMC_WAITRD1             (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_MPMC_WAITRD1_OFFSET)
#define LPC31_SYSCREG_MPMC_WIREEBIMSZ          (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_MPMC_WIREEBIMSZ_OFFSET)
#define LPC31_SYSCREG_MPMC_TESTMODE0           (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_MPMC_TESTMODE0_OFFSET)
#define LPC31_SYSCREG_MPMC_TESTMODE1           (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_MPMC_TESTMODE1_OFFSET)

/* Miscellaneous system configuration registers, part 2 */

#define LPC31_SYSCREG_AHB0EXTPRIO              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_AHB0EXTPRIO_OFFSET)
#define LPC31_SYSCREG_ARM926SHADOWPTR          (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_ARM926SHADOWPTR_OFFSET)

/* Pin multiplexing control registers */

#define LPC31_SYSCREG_MUX_LCDEBISEL            (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_MUX_LCDEBISEL_OFFSET)
#define LPC31_SYSCREG_MUX_GPIOMCISEL           (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_MUX_GPIOMCISEL_OFFSET)
#define LPC31_SYSCREG_MUX_NANDMCISEL           (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_MUX_NANDMCISEL_OFFSET)
#define LPC31_SYSCREG_MUX_UARTSPISEL           (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_MUX_UARTSPISEL_OFFSET)
#define LPC31_SYSCREG_MUX_I2STXPCMSEL          (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_MUX_I2STXPCMSEL_OFFSET)

/* Pad configuration registers */

#define LPC31_SYSCREG_PAD_EBID9                (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBID9_OFFSET)
#define LPC31_SYSCREG_PAD_EBID10               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBID10_OFFSET)
#define LPC31_SYSCREG_PAD_EBID11               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBID11_OFFSET)
#define LPC31_SYSCREG_PAD_EBID12               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBID12_OFFSET)
#define LPC31_SYSCREG_PAD_EBID13               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBID13_OFFSET)
#define LPC31_SYSCREG_PAD_EBID14               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBID14_OFFSET)
#define LPC31_SYSCREG_PAD_I2SRXBCK0            (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_I2SRXBCK0_OFFSET)
#define LPC31_SYSCREG_PAD_MGPIO9               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MGPIO9_OFFSET)
#define LPC31_SYSCREG_PAD_MGPIO6               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MGPIO6_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDDB7              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDDB7_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDDB4              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDDB4_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDDB2              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDDB2_OFFSET)
#define LPC31_SYSCREG_PAD_MNANDRYBN0           (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MNANDRYBN0_OFFSET)
#define LPC31_SYSCREG_PAD_GPIO1                (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_GPIO1_OFFSET)
#define LPC31_SYSCREG_PAD_EBID4                (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBID4_OFFSET)
#define LPC31_SYSCREG_PAD_MI2STXCLK0           (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MI2STXCLK0_OFFSET)
#define LPC31_SYSCREG_PAD_MI2STXBCK0           (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MI2STXBCK0_OFFSET)
#define LPC31_SYSCREG_PAD_EBIA1CLE             (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBIA1CLE_OFFSET)
#define LPC31_SYSCREG_PAD_EBINCASBLOUT0        (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBINCASBLOUT0_OFFSET)
#define LPC31_SYSCREG_PAD_NANDNCS3             (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_NANDNCS3_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDDB0              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDDB0_OFFSET)
#define LPC31_SYSCREG_PAD_EBIDQM0NOE           (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBIDQM0NOE_OFFSET)
#define LPC31_SYSCREG_PAD_EBID0                (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBID0_OFFSET)
#define LPC31_SYSCREG_PAD_EBID1                (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBID1_OFFSET)
#define LPC31_SYSCREG_PAD_EBID2                (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBID2_OFFSET)
#define LPC31_SYSCREG_PAD_EBID3                (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBID3_OFFSET)
#define LPC31_SYSCREG_PAD_EBID5                (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBID5_OFFSET)
#define LPC31_SYSCREG_PAD_EBID6                (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBID6_OFFSET)
#define LPC31_SYSCREG_PAD_EBID7                (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBID7_OFFSET)
#define LPC31_SYSCREG_PAD_EBID8                (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBID8_OFFSET)
#define LPC31_SYSCREG_PAD_EBID15               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBID15_OFFSET)
#define LPC31_SYSCREG_PAD_I2STXDATA1           (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_I2STXDATA1_OFFSET)
#define LPC31_SYSCREG_PAD_I2STXBCK1            (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_I2STXBCK1_OFFSET)
#define LPC31_SYSCREG_PAD_I2STXWS1             (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_I2STXWS1_OFFSET)
#define LPC31_SYSCREG_PAD_I2SRXDATA0           (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_I2SRXDATA0_OFFSET)
#define LPC31_SYSCREG_PAD_I2SRXWS0             (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_I2SRXWS0_OFFSET)
#define LPC31_SYSCREG_PAD_I2SRXDATA1           (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_I2SRXDATA1_OFFSET)
#define LPC31_SYSCREG_PAD_I2SRXBCK1            (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_I2SRXBCK1_OFFSET)
#define LPC31_SYSCREG_PAD_I2SRXWS1             (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_I2SRXWS1_OFFSET)
#define LPC31_SYSCREG_PAD_SYSCLKO              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_SYSCLKO_OFFSET)
#define LPC31_SYSCREG_PAD_PWMDATA              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_PWMDATA_OFFSET)
#define LPC31_SYSCREG_PAD_UARTRXD              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_UARTRXD_OFFSET)
#define LPC31_SYSCREG_PAD_UARTTXD              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_UARTTXD_OFFSET)
#define LPC31_SYSCREG_PAD_I2CSDA1              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_I2CSDA1_OFFSET)
#define LPC31_SYSCREG_PAD_I2CSCL1              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_I2CSCL1_OFFSET)
#define LPC31_SYSCREG_PAD_CLK256FSO            (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_CLK256FSO_OFFSET)
#define LPC31_SYSCREG_PAD_GPIO0                (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_GPIO0_OFFSET)
#define LPC31_SYSCREG_PAD_GPIO2                (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_GPIO2_OFFSET)
#define LPC31_SYSCREG_PAD_GPIO3                (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_GPIO3_OFFSET)
#define LPC31_SYSCREG_PAD_GPIO4                (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_GPIO4_OFFSET)
#define LPC31_SYSCREG_PAD_GPIO11               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_GPIO11_OFFSET)
#define LPC31_SYSCREG_PAD_GPIO12               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_GPIO12_OFFSET)
#define LPC31_SYSCREG_PAD_GPIO13               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_GPIO13_OFFSET)
#define LPC31_SYSCREG_PAD_GPIO14               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_GPIO14_OFFSET)
#define LPC31_SYSCREG_PAD_GPIO15               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_GPIO15_OFFSET)
#define LPC31_SYSCREG_PAD_GPIO16               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_GPIO16_OFFSET)
#define LPC31_SYSCREG_PAD_GPIO17               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_GPIO17_OFFSET)
#define LPC31_SYSCREG_PAD_GPIO18               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_GPIO18_OFFSET)
#define LPC31_SYSCREG_PAD_GPIO19               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_GPIO19_OFFSET)
#define LPC31_SYSCREG_PAD_GPIO20               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_GPIO20_OFFSET)
#define LPC31_SYSCREG_PAD_SPIMISO              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_SPIMISO_OFFSET)
#define LPC31_SYSCREG_PAD_SPIMOSI              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_SPIMOSI_OFFSET)
#define LPC31_SYSCREG_PAD_SPICSIN              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_SPICSIN_OFFSET)
#define LPC31_SYSCREG_PAD_SPISCK               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_SPISCK_OFFSET)
#define LPC31_SYSCREG_PAD_SPICSOUT0            (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_SPICSOUT0_OFFSET)
#define LPC31_SYSCREG_PAD_NANDNCS0             (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_NANDNCS0_OFFSET)
#define LPC31_SYSCREG_PAD_NANDNCS1             (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_NANDNCS1_OFFSET)
#define LPC31_SYSCREG_PAD_NANDNCS2             (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_NANDNCS2_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDCSB              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDCSB_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDDB1              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDDB1_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDERD              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDERD_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDRS               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDRS_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDRWWR             (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDRWWR_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDDB3              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDDB3_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDDB5              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDDB5_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDDB6              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDDB6_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDDB8              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDDB8_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDDB9              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDDB9_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDDB10             (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDDB10_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDDB11             (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDDB11_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDDB12             (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDDB12_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDDB13             (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDDB13_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDDB14             (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDDB14_OFFSET)
#define LPC31_SYSCREG_PAD_MLCDDB15             (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MLCDDB15_OFFSET)
#define LPC31_SYSCREG_PAD_MGPIO5               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MGPIO5_OFFSET)
#define LPC31_SYSCREG_PAD_MGPIO7               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MGPIO7_OFFSET)
#define LPC31_SYSCREG_PAD_MGPIO8               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MGPIO8_OFFSET)
#define LPC31_SYSCREG_PAD_MGPIO10              (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MGPIO10_OFFSET)
#define LPC31_SYSCREG_PAD_MNANDRYBN1           (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MNANDRYBN1_OFFSET)
#define LPC31_SYSCREG_PAD_MNANDRYBN2           (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MNANDRYBN2_OFFSET)
#define LPC31_SYSCREG_PAD_MNANDRYBN3           (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MNANDRYBN3_OFFSET)
#define LPC31_SYSCREG_PAD_MUARTCTSN            (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MUARTCTSN_OFFSET)
#define LPC31_SYSCREG_PAD_MI2STXDATA0          (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MI2STXDATA0_OFFSET)
#define LPC31_SYSCREG_PAD_MI2STXWS0            (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_MI2STXWS0_OFFSET)
#define LPC31_SYSCREG_PAD_EBINRASBLOUT1        (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBINRASBLOUT1_OFFSET)
#define LPC31_SYSCREG_PAD_EBIA0ALE             (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBIA0ALE_OFFSET)
#define LPC31_SYSCREG_PAD_EBINWE               (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_EBINWE_OFFSET)
#define LPC31_SYSCREG_PAD_ESHCTRLSUP4          (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_ESHCTRLSUP4_OFFSET)
#define LPC31_SYSCREG_PAD_ESHCTRLSUP8          (LPC31_SYSCREG_VBASE+LPC31_SYSCREG_PAD_ESHCTRLSUP8_OFFSET)

/* SYSCREG register bit definitions *****************************************/

/* Miscellaneous system configuration registers, part1 */

/* SYSCREG_EBIMPMCPRIO, address 0x13002808
 * SYSCREG_EBINANDCPRIO address 0x1300280c
 * SYSCREG_EBIUNUSEDPRIO address 0x13002810
 */

#define SYSCREG_EBI_TIMEOUT_SHIFT                (0)       /* Bits 0-9:  Time MPMC, NAND or unused channel */
#define SYSCREG_EBI_TIMEOUT_MASK                 (0x3ff << SYSCREG_EBI_TIMEOUT_SHIFT)

/* RINGOSCCFG address 0x13002814 */

#define SYSCREG_RINGOSCCFG_OSC1EN                (1 << 1)  /* Bit 1:  Enable ring oscillator 1 */
#define SYSCREG_RINGOSCCFG_OSC0EN                (1 << 0)  /* Bit 0:  Enable oscillator 0 */

/* SYSCREG_ADCPDADC10BITS address 0x13002818 */

#define SYSCREG_ADCPDADC10BITS_PWRDOWN           (1 << 0)  /* Bit 0:  Power down ADC */

/* SYSCREG_ABCCFG address 0x13002824 */

#define SYSCREG_ABCCFG_USBOTG_SHIFT              (9)       /* Bits 9-11: USB_OTG AHB bus bandwidth control */
#define SYSCREG_ABCCFG_USBOTG_MASK               (7 << SYSCREG_ABCCFG_USBOTG_SHIFT)
#  define SYSCREG_ABCCFG_USBOTG_NORMAL           (0 << SYSCREG_ABCCFG_USBOTG_SHIFT)     /* Normal mode */
#  define SYSCREG_ABCCFG_USBOTG_NONSEQ           (1 << SYSCREG_ABCCFG_USBOTG_SHIFT)     /* Make non-sequential */
#  define SYSCREG_ABCCFG_USBOTG_SPLIT4           (2 << SYSCREG_ABCCFG_USBOTG_SHIFT)     /* Split to 4-beat */
#  define SYSCREG_ABCCFG_USBOTG_SPLIT8           (3 << SYSCREG_ABCCFG_USBOTG_SHIFT)     /* Split to 8-beat */
#  define SYSCREG_ABCCFG_USBOTG_EXT8             (4 << SYSCREG_ABCCFG_USBOTG_SHIFT)     /* Extend to 8-beat */
#  define SYSCREG_ABCCFG_USBOTG_EXT16            (5 << SYSCREG_ABCCFG_USBOTG_SHIFT)     /* Extend to 16-beat */
#  define SYSCREG_ABCCFG_USBOTG_SPLIT4W          (6 << SYSCREG_ABCCFG_USBOTG_SHIFT)     /* Split to 4-beat */
#  define SYSCREG_ABCCFG_USBOTG_EXT32            (7 << SYSCREG_ABCCFG_USBOTG_SHIFT)     /* extend to 32-beat */

#define SYSCREG_ABCCFG_ARM926EJSI_SHIFT          (6)       /* Bits 6-8: ARM926EJS instruction AHB bus bandwidth control */
#define SYSCREG_ABCCFG_ARM926EJSI_MASK           (7 << SYSCREG_ABCCFG_ARM926EJSI_SHIFT)
#  define SYSCREG_ABCCFG_ARM926EJSI_NORMAL       (0 << SYSCREG_ABCCFG_ARM926EJSI_SHIFT) /* Normal mode */
#  define SYSCREG_ABCCFG_ARM926EJSI_NONSEQ       (1 << SYSCREG_ABCCFG_ARM926EJSI_SHIFT) /* Make non-sequential */
#  define SYSCREG_ABCCFG_ARM926EJSI_SPLIT4       (2 << SYSCREG_ABCCFG_ARM926EJSI_SHIFT) /* Split to 4-beat */
#  define SYSCREG_ABCCFG_ARM926EJSI_SPLIT8       (3 << SYSCREG_ABCCFG_ARM926EJSI_SHIFT) /* Split to 8-beat */
#  define SYSCREG_ABCCFG_ARM926EJSI_EXT8         (4 << SYSCREG_ABCCFG_ARM926EJSI_SHIFT) /* Extend to 8-beat */
#  define SYSCREG_ABCCFG_ARM926EJSI_EXT16        (5 << SYSCREG_ABCCFG_ARM926EJSI_SHIFT) /* Extend to 16-beat */
#  define SYSCREG_ABCCFG_ARM926EJSI_SPLIT4W      (6 << SYSCREG_ABCCFG_ARM926EJSI_SHIFT) /* Split to 4-beat */
#  define SYSCREG_ABCCFG_ARM926EJSI_EXT32        (7 << SYSCREG_ABCCFG_ARM926EJSI_SHIFT) /* extend to 32-beat */

#define SYSCREG_ABCCFG_ARM926EJSD_SHIFT          (3)       /* Bits 3-5: ARM926EJS data AHB bus bandwidth control */
#define SYSCREG_ABCCFG_ARM926EJSD_MASK           (7 << SYSCREG_ABCCFG_ARM926EJSD_SHIFT)
#  define SYSCREG_ABCCFG_ARM926EJSD_NORMAL       (0 << SYSCREG_ABCCFG_ARM926EJSD_SHIFT) /* Normal mode */
#  define SYSCREG_ABCCFG_ARM926EJSD_NONSEQ       (1 << SYSCREG_ABCCFG_ARM926EJSD_SHIFT) /* Make non-sequential */
#  define SYSCREG_ABCCFG_ARM926EJSD_SPLIT4       (2 << SYSCREG_ABCCFG_ARM926EJSD_SHIFT) /* Split to 4-beat */
#  define SYSCREG_ABCCFG_ARM926EJSD_SPLIT8       (3 << SYSCREG_ABCCFG_ARM926EJSD_SHIFT) /* Split to 8-beat */
#  define SYSCREG_ABCCFG_ARM926EJSD_EXT8         (4 << SYSCREG_ABCCFG_ARM926EJSD_SHIFT) /* Extend to 8-beat */
#  define SYSCREG_ABCCFG_ARM926EJSD_EXT16        (5 << SYSCREG_ABCCFG_ARM926EJSD_SHIFT) /* Extend to 16-beat */
#  define SYSCREG_ABCCFG_ARM926EJSD_SPLIT4W      (6 << SYSCREG_ABCCFG_ARM926EJSD_SHIFT) /* Split to 4-beat */
#  define SYSCREG_ABCCFG_ARM926EJSD_EXT32        (7 << SYSCREG_ABCCFG_ARM926EJSD_SHIFT) /* extend to 32-beat */

#define SYSCREG_ABCCFG_DMA_SHIFT                 (0)       /* Bits 0-2: 2:0 DMA AHB bus bandwidth control */
#define SYSCREG_ABCCFG_DMA_MASK                  (7 << SYSCREG_ABCCFG_DMA_SHIFT)
#  define SYSCREG_ABCCFG_DMA_NORMAL              (0 << SYSCREG_ABCCFG_DMA_SHIFT)        /* Normal mode */
#  define SYSCREG_ABCCFG_DMA_NONSEQ              (1 << SYSCREG_ABCCFG_DMA_SHIFT)        /* Make non-sequential */
#  define SYSCREG_ABCCFG_DMA_SPLIT4              (2 << SYSCREG_ABCCFG_DMA_SHIFT)        /* Split to 4-beat */
#  define SYSCREG_ABCCFG_DMA_SPLIT8              (3 << SYSCREG_ABCCFG_DMA_SHIFT)        /* Split to 8-beat */
#  define SYSCREG_ABCCFG_DMA_EXT8                (4 << SYSCREG_ABCCFG_DMA_SHIFT)        /* Extend to 8-beat */
#  define SYSCREG_ABCCFG_DMA_EXT16               (5 << SYSCREG_ABCCFG_DMA_SHIFT)        /* Extend to 16-beat */
#  define SYSCREG_ABCCFG_DMA_SPLIT4W             (6 << SYSCREG_ABCCFG_DMA_SHIFT)        /* Split to 4-beat */
#  define SYSCREG_ABCCFG_DMA_EXT32               (7 << SYSCREG_ABCCFG_DMA_SHIFT)        /* extend to 32-beat */

/* SYSCREG_SDMMCCFG address 0x13002828 */

#define SYSCREG_SDMMCCFG_CARDDETECT              (1 << 1)  /* Bit 1:  Card detect signal */
#define SYSCREG_SDMMCCFG_CARDWRITEPRT            (1 << 0)  /* Bit 0:  Card write protect signal for SD cards */

/* SYSCREG_MCIDELAYMODES address 0x1300282c */

#define SYSCREG_MCIDELAYMODES_DELAYENABLE        (1 << 4)  /* Bit 4:  Enable delay cells */
#define SYSCREG_MCIDELAYMODES_DELAYCELLS_SHIFT   (0)       /* Bits 0-3: Number of delay cells needed */
#define SYSCREG_MCIDELAYMODES_DELAYCELLS_MASK    (15 << SYSCREG_MCIDELAYMODES_DELAYCELLS_SHIFT)

/* USB configuration registers */

/* USB_ATXPLLPDREG address 0x13002830 */

#define SYSCREG_USB_ATXPLLPDREG_PWRDOWN          (1 << 0)  /* Bit 0: Powerdown */

/* USB_OTGCFG address 0x13002834 */

#define SYSCREG_USB_OTGCFG_VBUSPWRFAULT          (1 << 3)  /* Bit 3:  Charge pump overcurrent */
#define SYSCREG_USB_OTGCFG_DEVWAKEUP             (1 << 2)  /* Bit 2:  External wakeup (device mode) */
#define SYSCREG_USB_OTGCFG_HOSTWAKEUP            (1 << 1)  /* Bit 1:  External wake-up (host mode) */

/*  USB_OTGPORTINDCTL address 0x1300 2838 */

#define SYSCREG_USB_OTGPORTINDCTL_SHIFT          (0)       /* Bits 0-1: Status bits for USB connector LEDs */
#define SYSCREG_USB_OTGPORTINDCTL_MASK           (3 << SYSCREG_USB_OTGPORTINDCTL_SHIFT)
#  define SYSCREG_USB_OTGPORTINDCTL_OFF          (0 << SYSCREG_USB_OTGPORTINDCTL_SHIFT) /* off */
#  define SYSCREG_USB_OTGPORTINDCTL_AMBER        (1 << SYSCREG_USB_OTGPORTINDCTL_SHIFT) /* amber */
#  define SYSCREG_USB_OTGPORTINDCTL_GREEN        (2 << SYSCREG_USB_OTGPORTINDCTL_SHIFT) /* green */

/* USB_PLLNDEC address 0x13002840 */

#define SYSCREG_USB_PLLNDEC_SHIFT                (0)       /* Bits 0-9: Pre-divider for the USB pll */
#define SYSCREG_USB_PLLNDEC_MASK                 (0x3ff << SYSCREG_USB_PLLNDEC_SHIFT)

/* USB_PLLMDEC address 0x13002844 */

#define SYSCREG_USB_PLLMDEC_SHIFT                (0)       /* Bits 0-16: Feedback-divider for the USB pll */
#define SYSCREG_USB_PLLMDEC_MASK                 (0x1ffff << SYSCREG_USB_PLLMDEC_SHIFT)

/* USB_PLLPDEC address 0x13002848 */

#define SYSCREG_USB_PLLPDEC_SHIFT                (0)       /* Bits 0-3: Feedback-divider for the USB pll */
#define SYSCREG_USB_PLLPDEC_MASK                 (15 << SYSCREG_USB_PLLPDEC_SHIFT)

/* USB_PLLSELR address 0x1300284c */

#define SYSCREG_USB_PLLSELR_SHIFT                (0)       /* Bits 0-3: Bandwidth selection */
#define SYSCREG_USB_PLLSELR_MASK                 (15 << SYSCREG_USB_PLLSELR_SHIFT)

/* USB_PLLSELI address 0x13002850 */

#define SYSCREG_USB_PLLSELI_SHIFT                (0)       /* Bits 0-3: Bandwidth selection */
#define SYSCREG_USB_PLLSELI_MASK                 (15 << SYSCREG_USB_PLLSELI_SHIFT)

/* USB_PLLSELP address 0x13002854 */

#define SYSCREG_USB_PLLSELP_SHIFT                (0)       /* Bits 0-3: Bandwidth selection */
#define SYSCREG_USB_PLLSELP_MASK                 (15 << SYSCREG_USB_PLLSELP_SHIFT)

/* ISRAM/ISROM configuration registers */

/* SYSCREG_ISRAM0_LATENCYCFG address 0x13002858 */

#define SYSCREG_ISRAM0_LATENCYCFG_SHIFT          (0)       /* Bits 0-1:  Number of waitstates */
#define SYSCREG_ISRAM0_LATENCYCFG_MASK           (3 << SYSCREG_ISRAM0_LATENCYCFG_SHIFT)

/* SYSCREG_ISRAM1_LATENCYCFG address 0x1300285c */

#define SYSCREG_ISRAM1_LATENCYCFG_SHIFT          (0)       /* Bits 0-1:  Number of waitstates */
#define SYSCREG_ISRAM1_LATENCYCFG_MASK           (3 << SYSCREG_ISRAM1_LATENCYCFG_SHIFT)

/* SYSCREG_ISROM_LATENCYCFG address 0x13002860 */

#define SYSCREG_ISROM_LATENCYCFG_SHIFT           (0)       /* Bits 0-1:  Number of waitstates */
#define SYSCREG_ISROM_LATENCYCFG_MASK            (3 << SYSCREG_ISROM_LATENCYCFG_SHIFT)

/* MPMC configuration registers */

/* SYSCREG_AHB_MPMC_MISC (address 0x13002864 */

#define SYSCREG_MPMC_MISC_REL1CONFIG             (1 << 8)  /* Bit 8:  Static memory address mode select */
#define SYSCREG_MPMC_MISC_STCS1PB                (1 << 7)  /* Bit 7:  Polarity of byte lane select for static memory CS1 */
#define SYSCREG_MPMC_MISC_STCS1POL               (1 << 4)  /* Bit 4:  Polarity of static memory CS1 */
#define SYSCREG_MPMC_MISC_STCS0POL               (1 << 3)  /* Bit 3:  Polarity of static memory CS0 */
#define SYSCREG_MPMC_MISC_SREFREQ                (1 << 0)  /* Bit 0:  Self refresh request */

/* SYSCREG_MPMC_DELAYMODES address 0x13002868 */

#define SYSCREG_MPMC_DELAYMODES_DEL1_SHIFT       (12)      /* Bits 12-17: Delay cells for MPMCCLKOUT */
#define SYSCREG_MPMC_DELAYMODES_DEL1_MASK        (63 << SYSCREG_MPMC_DELAYMODES_DEL1_SHIFT)
#define SYSCREG_MPMC_DELAYMODES_DEL2_SHIFT       (6)       /* Bits 6-11: Delay cells between MPMCCLK and MPMCCLKDELAY */
#define SYSCREG_MPMC_DELAYMODES_DEL2_MASK        (63 << SYSCREG_MPMC_DELAYMODES_DEL2_SHIFT)
#define SYSCREG_MPMC_DELAYMODES_DEL3_SHIFT       (0)       /* Bits 0-5: Delay cells between MPMCCLK and MPMCFBCLKIN */
#define SYSCREG_MPMC_DELAYMODES_DEL3_MASK        (63 << SYSCREG_MPMC_DELAYMODES_DEL3_SHIFT)

/* SYSCREG_MPMC_WAITRD0 address 0x1300286c */

#define SYSCREG_MPMC_WAITRD0_EXTRAOE             (1 << 5)  /* Bit 5:  Enable the extra inactive OE cycle */
#define SYSCREG_MPMC_WAITRD0_SHIFT               (0)       /* Bits 0-4: Value for MPMCStaticWaitRd0 */
#define SYSCREG_MPMC_WAITRD0_MASK                (31 << SYSCREG_MPMC_WAITRD0_SHIFT)

/* SYSCREG_MPMC_WAITRD1 address 0x13002870 */

#define SYSCREG_MPMC_WAITRD1_EXTRAOE             (1 << 5)  /* Bit 5:  Enable the extra inactive OE cycle */
#define SYSCREG_MPMC_WAITRD1_SHIFT               (0)       /* Bits 0-4: Value for MPMCStaticWaitRd1 */
#define SYSCREG_MPMC_WAITRD1_MASK                (31 << SYSCREG_MPMC_WAITRD1_SHIFT)

/* SYSCREG_WIR_EBIMSINIT address 0x13002874 */

#define SYSCREG_MPMC_WIREEBIMSZ_SHIFT            (0)       /* Bits 0-1: Memory width of CS1 */
#define SYSCREG_MPMC_WIREEBIMSZ_MASK             (3 << SYSCREG_MPMC_WIREEBIMSZ_SHIFT)

/* MPMC_TESTMODE0 address 0x13002878 */

#define SYSCREG_MPMC_TESTMODE0_EXTREFENABLE      (1 << 12) /* Bit 13: External refresh of MPMC */
#define SYSCREG_MPMC_TESTMODE0_EXTREFCNT_SHIFT   (0)       /* Bits 0-11: Period of external refresh */
#define SYSCREG_MPMC_TESTMODE0_EXTREFCNT_MASK    (0xfff << SYSCREG_MPMC_TESTMODE0_EXTREFCNT_SHIFT)

/* MPMC_TESTMODE1 address 0x1300287c */

#define SYSCREG_MPMC_TESTMODE1_HSENABLE_SHIFT    (0)       /* Bits 0-7: Allows AHB to run faster while refreshing */
#define SYSCREG_MPMC_TESTMODE1_HSENABLE_MASK     (0xff << SYSCREG_MPMC_TESTMODE1_HSENABLE_SHIFT)

/* Miscellaneous system configuration registers, part 2 */

/* AHB0EXTPRIO address 0x13002880 */

#define SYSCREG_AHB0EXTPRIO_USBOTG               (1 << 3)  /* Bit 3:  USBOTG has higher priority */
#define SYSCREG_AHB0EXTPRIO_ARM926DATA           (1 << 2)  /* Bit 2:  ARM926 Data has higher priority */
#define SYSCREG_AHB0EXTPRIO_ARM926NSTR           (1 << 1)  /* Bit 1:  ARM926 Instruction has higher priority */
#define SYSCREG_AHB0EXTPRIO_DMA                  (1 << 0)  /* Bit 0:  DMA has higher priority */

/* Pin multiplexing control registers */

/* SYSCREG_MUX_LCDEBISEL address 0x13002890 */

#define SYSCREG_MUX_LCDEBISEL_EBIMPMC            (1 << 0)  /* Bit 0:  Selects between LCD and EBI/MPMC pins */

/* SYSCREG_MUX_GPIOMCISEL address 0x13002894 */

#define SYSCREG_MUX_GPIOMCISEL_MCI               (1 << 0)  /* Bit 0:  Selects between GPIO and MCI pins */

/* SYSCREG_MUX_NANDMCISEL address 0x13002898 */

#define SYSCREG_MUX_NANDMCISEL_MCI               (1 << 0)  /* Bit 0:  Selects between NAND and MCI pins */

/* SYSCREG_MUX_UARTSPISEL address 0x1300289c */

#define SYSCREG_MUX_UARTSPISEL_SPI               (1 << 0)  /* Bit 0:  Selects between SPI and UART pins */

/* SYSCREG_MUX_I2STXIPCMSEL address 0x130028a0 */

#define SYSCREG_MUX_I2STXPCMSEL_PCM              (1 << 0)  /* Bit 0:  Selects between I2STX_0 and IPINT_1 pins */

/* Pad configuration registers */

/* SYSCREG_PAD_padname addresses 0x130028a4 to 0x13002a28 */

#define SYSCREG_PAD_P2                           (1 << 1)  /* Bit 1: The logic pin p2 of the pad */
#define SYSCREG_PAD_P1                           (1 << 0)  /* Bit 0: The logic pin p1 of the pad */
#define SYSCREG_PAD_PULLUP                       (0)
#define SYSCREG_PAD_INPUT                        (SYSCREG_PAD_P2)
#define SYSCREG_PAD_REPEATER                     (SYSCREG_PAD_P1)
#define SYSCREG_PAD_WEAKPULLUP                   (SYSCREG_PAD_P1|SYSCREG_PAD_P2)

/* SYSCREG_ESHCTRLSUP4 address 0x13002a2c */

#define SYSCREG_PAD_ESHCTRLSUP4_LESS             (1 << 0)  /* Bit 0: Domain SUP4 less switching noise */

/* SYSCREG_ESHCTRLSUP8 address 0x13002a2c */

#define SYSCREG_PAD_ESHCTRLSUP8_LESS             (1 << 0)  /* Bit 0: Domain SUP8 switching less noise */

/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Functions Prototypes
 ****************************************************************************/

#endif /* __ARCH_ARM_SRC_LPC31XX_LPC31_SYSCREG_H */
