############################################################################
# arch/arm/src/stm32h7/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

# The start-up, "head", file.  Only common vectors are support so there
# isn't one.

# Common ARM and Cortex-M7 files

include armv7-m/Make.defs

ifeq ($(CONFIG_STM32H7_PROGMEM),y)
CHIP_CSRCS += stm32_flash.c
endif

# Required STM32H7 files

CHIP_CSRCS += stm32_allocateheap.c stm32_exti_gpio.c stm32_gpio.c stm32_irq.c
CHIP_CSRCS += stm32_start.c stm32_rcc.c stm32_lowputc.c stm32_serial.c
CHIP_CSRCS += stm32_uid.c

ifeq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += stm32_tickless.c
else
CHIP_CSRCS += stm32_timerisr.c
endif

ifeq ($(CONFIG_STM32H7_ONESHOT),y)
CHIP_CSRCS += stm32_oneshot.c stm32_oneshot_lowerhalf.c
endif

ifeq ($(CONFIG_BUILD_PROTECTED),y)
CHIP_CSRCS += stm32_userspace.c stm32_mpuinit.c
endif

ifeq ($(CONFIG_ARMV7M_DTCM),y)
CHIP_CSRCS += stm32_dtcm.c
endif

ifeq ($(CONFIG_STM32H7_ADC),y)
CHIP_CSRCS += stm32_adc.c
endif

ifeq ($(CONFIG_STM32H7_FDCAN),y)
CHIP_CSRCS += stm32_fdcan_sock.c
endif

ifeq ($(CONFIG_STM32H7_BBSRAM),y)
CHIP_CSRCS += stm32_bbsram.c
endif

ifeq ($(CONFIG_STM32H7_DMA),y)
CHIP_CSRCS += stm32_dma.c
endif

ifeq ($(CONFIG_STM32H7_FMC),y)
CHIP_CSRCS += stm32_fmc.c
endif

ifeq ($(filter y,$(CONFIG_STM32H7_IWDG) $(CONFIG_STM32H7_RTC_LSICLOCK)),y)
CHIP_CSRCS += stm32_lsi.c
endif

ifeq ($(CONFIG_STM32H7_RTC_LSECLOCK),y)
CHIP_CSRCS += stm32_lse.c
endif

ifeq ($(CONFIG_STM32H7_I2C),y)
CHIP_CSRCS += stm32_i2c.c
endif

ifeq ($(CONFIG_STM32H7_PWR),y)
CHIP_CSRCS += stm32_pwr.c
endif

ifeq ($(CONFIG_STM32H7_QUADSPI),y)
CHIP_CSRCS += stm32_qspi.c
endif

ifeq ($(CONFIG_STM32H7_RTC),y)
CHIP_CSRCS += stm32_rtc.c
ifeq ($(CONFIG_RTC_ALARM),y)
CHIP_CSRCS += stm32_exti_alarm.c
endif
ifeq ($(CONFIG_RTC_PERIODIC),y)
CHIP_CSRCS += stm32_exti_wakeup.c
endif
ifeq ($(CONFIG_RTC_DRIVER),y)
CHIP_CSRCS += stm32_rtc_lowerhalf.c
endif
endif

ifeq ($(CONFIG_STM32H7_SPI),y)
CHIP_CSRCS += stm32_spi.c
endif

ifeq ($(CONFIG_SPI_SLAVE),y)
CHIP_CSRCS += stm32_spi_slave.c
endif

ifeq ($(CONFIG_STM32H7_SDMMC),y)
CHIP_CSRCS += stm32_sdmmc.c
endif

ifeq ($(CONFIG_USBDEV),y)
CHIP_CSRCS += stm32_otgdev.c
endif

ifeq ($(CONFIG_USBHOST),y)
CHIP_CSRCS += stm32_otghost.c
ifeq ($(CONFIG_USBHOST_TRACE),y)
CHIP_CSRCS += stm32_usbhost.c
else
ifeq ($(CONFIG_DEBUG_USB),y)
CHIP_CSRCS += stm32_usbhost.c
endif
endif
endif

ifeq ($(CONFIG_STM32H7_TIM),y)
CHIP_CSRCS += stm32_tim.c
endif

ifeq ($(CONFIG_STM32H7_LPTIM),y)
CHIP_CSRCS += stm32_lptim.c
endif

ifeq ($(CONFIG_STM32H7_PWM),y)
CHIP_CSRCS += stm32_pwm.c
endif

ifeq ($(CONFIG_STM32H7_ETHMAC),y)
CHIP_CSRCS += stm32_ethernet.c
endif

ifeq ($(CONFIG_SENSORS_QENCODER),y)
CHIP_CSRCS += stm32_qencoder.c
endif

ifeq ($(CONFIG_PM),y)
CHIP_CSRCS += stm32_pmsleep.c stm32_pmstandby.c stm32_pmstop.c
ifneq ($(CONFIG_ARCH_CUSTOM_PMINIT),y)
CHIP_CSRCS += stm32_pminitialize.c
endif
endif

ifeq ($(CONFIG_STM32H7_IWDG),y)
CHIP_CSRCS += stm32_iwdg.c
endif

ifeq ($(CONFIG_STM32H7_WWDG),y)
CHIP_CSRCS += stm32_wwdg.c
endif
