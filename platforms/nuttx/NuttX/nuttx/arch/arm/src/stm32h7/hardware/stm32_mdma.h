/****************************************************************************
 * arch/arm/src/stm32h7/hardware/stm32_mdma.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_STM32H7_HARDWARE_STM32_MDMA_H
#define __ARCH_ARM_SRC_STM32H7_HARDWARE_STM32_MDMA_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include "chip.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Register Offsets *********************************************************/

#define STM32_MDMA_OFFSET(x)          (0x40+0x40*(x))
#define STM32_MDMA_GISR0_OFFSET       0x0000 /* MDMA global interrupt/status register */
                                             /* 0x0004-0x003C: Reserved */

#define STM32_MDMACH_CISR_OFFSET      0x0040
#define STM32_MDMACH_CIFCR_OFFSET     0x0044
#define STM32_MDMACH_CESR_OFFSET      0x0048
#define STM32_MDMACH_CCR_OFFSET       0x004C
#define STM32_MDMACH_CTCR_OFFSET      0x0050
#define STM32_MDMACH_CBNDTR_OFFSET    0x0054
#define STM32_MDMACH_CSAR_OFFSET      0x0058
#define STM32_MDMACH_CDAR_OFFSET      0x005C
#define STM32_MDMACH_CBRUR_OFFSET     0x0060
#define STM32_MDMACH_CLAR_OFFSET      0x0064
#define STM32_MDMACH_CTBR_OFFSET      0x0068
#define STM32_MDMACH_CMAR_OFFSET      0x0070
#define STM32_MDMACH_CMDR_OFFSET      0x0074

#define STM32_MDMA_CXISR_OFFSET(x)    (0x0040+0x040*(x)) /* MDMA channel x interrupt/status register*/
#define STM32_MDMA_C0ISR_OFFSET       STM32_MDMA_CXISR_OFFSET(0)
#define STM32_MDMA_C1ISR_OFFSET       STM32_MDMA_CXISR_OFFSET(1)
#define STM32_MDMA_C2ISR_OFFSET       STM32_MDMA_CXISR_OFFSET(2)
#define STM32_MDMA_C3ISR_OFFSET       STM32_MDMA_CXISR_OFFSET(3)
#define STM32_MDMA_C4ISR_OFFSET       STM32_MDMA_CXISR_OFFSET(4)
#define STM32_MDMA_C5ISR_OFFSET       STM32_MDMA_CXISR_OFFSET(5)
#define STM32_MDMA_C6ISR_OFFSET       STM32_MDMA_CXISR_OFFSET(6)
#define STM32_MDMA_C7ISR_OFFSET       STM32_MDMA_CXISR_OFFSET(7)
#define STM32_MDMA_C8ISR_OFFSET       STM32_MDMA_CXISR_OFFSET(8)
#define STM32_MDMA_C9ISR_OFFSET       STM32_MDMA_CXISR_OFFSET(9)
#define STM32_MDMA_C10ISR_OFFSET      STM32_MDMA_CXISR_OFFSET(10)
#define STM32_MDMA_C11ISR_OFFSET      STM32_MDMA_CXISR_OFFSET(11)
#define STM32_MDMA_C12ISR_OFFSET      STM32_MDMA_CXISR_OFFSET(12)
#define STM32_MDMA_C13ISR_OFFSET      STM32_MDMA_CXISR_OFFSET(13)
#define STM32_MDMA_C14ISR_OFFSET      STM32_MDMA_CXISR_OFFSET(14)
#define STM32_MDMA_C15ISR_OFFSET      STM32_MDMA_CXISR_OFFSET(15)

#define STM32_MDMA_CXIFCR_OFFSET(x)   (0x0044+0x040*(x)) /* MDMA channel x interrupt flag clear register */
#define STM32_MDMA_C0IFCR_OFFSET      STM32_MDMA_CXIFCR_OFFSET(0)
#define STM32_MDMA_C1IFCR_OFFSET      STM32_MDMA_CXIFCR_OFFSET(1)
#define STM32_MDMA_C2IFCR_OFFSET      STM32_MDMA_CXIFCR_OFFSET(2)
#define STM32_MDMA_C3IFCR_OFFSET      STM32_MDMA_CXIFCR_OFFSET(3)
#define STM32_MDMA_C4IFCR_OFFSET      STM32_MDMA_CXIFCR_OFFSET(4)
#define STM32_MDMA_C5IFCR_OFFSET      STM32_MDMA_CXIFCR_OFFSET(5)
#define STM32_MDMA_C6IFCR_OFFSET      STM32_MDMA_CXIFCR_OFFSET(6)
#define STM32_MDMA_C7IFCR_OFFSET      STM32_MDMA_CXIFCR_OFFSET(7)
#define STM32_MDMA_C8IFCR_OFFSET      STM32_MDMA_CXIFCR_OFFSET(8)
#define STM32_MDMA_C9IFCR_OFFSET      STM32_MDMA_CXIFCR_OFFSET(9)
#define STM32_MDMA_C10IFCR_OFFSET     STM32_MDMA_CXIFCR_OFFSET(10)
#define STM32_MDMA_C11IFCR_OFFSET     STM32_MDMA_CXIFCR_OFFSET(11)
#define STM32_MDMA_C12IFCR_OFFSET     STM32_MDMA_CXIFCR_OFFSET(12)
#define STM32_MDMA_C13IFCR_OFFSET     STM32_MDMA_CXIFCR_OFFSET(13)
#define STM32_MDMA_C14IFCR_OFFSET     STM32_MDMA_CXIFCR_OFFSET(14)
#define STM32_MDMA_C15IFCR_OFFSET     STM32_MDMA_CXIFCR_OFFSET(15)

#define STM32_MDMA_CXESR_OFFSET(x)    (0x0048+0x040*(x)) /* MDMA channel x error status register */
#define STM32_MDMA_C0ESR_OFFSET       STM32_MDMA_CXESR_OFFSET0)
#define STM32_MDMA_C1ESR_OFFSET       STM32_MDMA_CXESR_OFFSET1)
#define STM32_MDMA_C2ESR_OFFSET       STM32_MDMA_CXESR_OFFSET2)
#define STM32_MDMA_C3ESR_OFFSET       STM32_MDMA_CXESR_OFFSET3)
#define STM32_MDMA_C4ESR_OFFSET       STM32_MDMA_CXESR_OFFSET4)
#define STM32_MDMA_C5ESR_OFFSET       STM32_MDMA_CXESR_OFFSET5)
#define STM32_MDMA_C6ESR_OFFSET       STM32_MDMA_CXESR_OFFSET6)
#define STM32_MDMA_C7ESR_OFFSET       STM32_MDMA_CXESR_OFFSET7)
#define STM32_MDMA_C8ESR_OFFSET       STM32_MDMA_CXESR_OFFSET8)
#define STM32_MDMA_C9ESR_OFFSET       STM32_MDMA_CXESR_OFFSET9)
#define STM32_MDMA_C10ESR_OFFSET      STM32_MDMA_CXESR_OFFSET10)
#define STM32_MDMA_C11ESR_OFFSET      STM32_MDMA_CXESR_OFFSET11)
#define STM32_MDMA_C12ESR_OFFSET      STM32_MDMA_CXESR_OFFSET12)
#define STM32_MDMA_C13ESR_OFFSET      STM32_MDMA_CXESR_OFFSET13)
#define STM32_MDMA_C14ESR_OFFSET      STM32_MDMA_CXESR_OFFSET14)
#define STM32_MDMA_C15ESR_OFFSET      STM32_MDMA_CXESR_OFFSET15)

#define STM32_MDMA_CXCR_OFFSET(x)     (0x004C+0x040*(x)) /* MDMA channel x control register */
#define STM32_MDMA_C0CR_OFFSET        STM32_MDMA_CXCR_OFFSET(0)
#define STM32_MDMA_C1CR_OFFSET        STM32_MDMA_CXCR_OFFSET(1)
#define STM32_MDMA_C2CR_OFFSET        STM32_MDMA_CXCR_OFFSET(2)
#define STM32_MDMA_C3CR_OFFSET        STM32_MDMA_CXCR_OFFSET(3)
#define STM32_MDMA_C4CR_OFFSET        STM32_MDMA_CXCR_OFFSET(4)
#define STM32_MDMA_C5CR_OFFSET        STM32_MDMA_CXCR_OFFSET(5)
#define STM32_MDMA_C6CR_OFFSET        STM32_MDMA_CXCR_OFFSET(6)
#define STM32_MDMA_C7CR_OFFSET        STM32_MDMA_CXCR_OFFSET(7)
#define STM32_MDMA_C8CR_OFFSET        STM32_MDMA_CXCR_OFFSET(8)
#define STM32_MDMA_C9CR_OFFSET        STM32_MDMA_CXCR_OFFSET(9)
#define STM32_MDMA_C10CR_OFFSET       STM32_MDMA_CXCR_OFFSET(10)
#define STM32_MDMA_C11CR_OFFSET       STM32_MDMA_CXCR_OFFSET(11)
#define STM32_MDMA_C12CR_OFFSET       STM32_MDMA_CXCR_OFFSET(12)
#define STM32_MDMA_C13CR_OFFSET       STM32_MDMA_CXCR_OFFSET(13)
#define STM32_MDMA_C14CR_OFFSET       STM32_MDMA_CXCR_OFFSET(14)
#define STM32_MDMA_C15CR_OFFSET       STM32_MDMA_CXCR_OFFSET(15)

#define STM32_MDMA_CXTCR_OFFSET(x)    (0x0050+0x040*(x)) /* MDMA channel x transfer configuration register */
#define STM32_MDMA_C0TCR_OFFSET       STM32_MDMA_CXTCR_OFFSET(0)
#define STM32_MDMA_C1TCR_OFFSET       STM32_MDMA_CXTCR_OFFSET(1)
#define STM32_MDMA_C2TCR_OFFSET       STM32_MDMA_CXTCR_OFFSET(2)
#define STM32_MDMA_C3TCR_OFFSET       STM32_MDMA_CXTCR_OFFSET(3)
#define STM32_MDMA_C4TCR_OFFSET       STM32_MDMA_CXTCR_OFFSET(4)
#define STM32_MDMA_C5TCR_OFFSET       STM32_MDMA_CXTCR_OFFSET(5)
#define STM32_MDMA_C6TCR_OFFSET       STM32_MDMA_CXTCR_OFFSET(6)
#define STM32_MDMA_C7TCR_OFFSET       STM32_MDMA_CXTCR_OFFSET(7)
#define STM32_MDMA_C8TCR_OFFSET       STM32_MDMA_CXTCR_OFFSET(8)
#define STM32_MDMA_C9TCR_OFFSET       STM32_MDMA_CXTCR_OFFSET(9)
#define STM32_MDMA_C10TCR_OFFSET      STM32_MDMA_CXTCR_OFFSET(10)
#define STM32_MDMA_C11TCR_OFFSET      STM32_MDMA_CXTCR_OFFSET(11)
#define STM32_MDMA_C12TCR_OFFSET      STM32_MDMA_CXTCR_OFFSET(12)
#define STM32_MDMA_C13TCR_OFFSET      STM32_MDMA_CXTCR_OFFSET(13)
#define STM32_MDMA_C14TCR_OFFSET      STM32_MDMA_CXTCR_OFFSET(14)
#define STM32_MDMA_C15TCR_OFFSET      STM32_MDMA_CXTCR_OFFSET(15)

#define STM32_MDMA_CXBNDTR_OFFSET(x)  (0x0054+0x040*(x)) /* MDMA channel x block number of data register */
#define STM32_MDMA_C0BNDTR_OFFSET     STM32_MDMA_CXBNDTR_OFFSET(0)
#define STM32_MDMA_C1BNDTR_OFFSET     STM32_MDMA_CXBNDTR_OFFSET(1)
#define STM32_MDMA_C2BNDTR_OFFSET     STM32_MDMA_CXBNDTR_OFFSET(2)
#define STM32_MDMA_C3BNDTR_OFFSET     STM32_MDMA_CXBNDTR_OFFSET(3)
#define STM32_MDMA_C4BNDTR_OFFSET     STM32_MDMA_CXBNDTR_OFFSET(4)
#define STM32_MDMA_C5BNDTR_OFFSET     STM32_MDMA_CXBNDTR_OFFSET(5)
#define STM32_MDMA_C6BNDTR_OFFSET     STM32_MDMA_CXBNDTR_OFFSET(6)
#define STM32_MDMA_C7BNDTR_OFFSET     STM32_MDMA_CXBNDTR_OFFSET(7)
#define STM32_MDMA_C8BNDTR_OFFSET     STM32_MDMA_CXBNDTR_OFFSET(8)
#define STM32_MDMA_C9BNDTR_OFFSET     STM32_MDMA_CXBNDTR_OFFSET(9)
#define STM32_MDMA_C10BNDTR_OFFSET    STM32_MDMA_CXBNDTR_OFFSET(10)
#define STM32_MDMA_C11BNDTR_OFFSET    STM32_MDMA_CXBNDTR_OFFSET(11)
#define STM32_MDMA_C12BNDTR_OFFSET    STM32_MDMA_CXBNDTR_OFFSET(12)
#define STM32_MDMA_C13BNDTR_OFFSET    STM32_MDMA_CXBNDTR_OFFSET(13)
#define STM32_MDMA_C14BNDTR_OFFSET    STM32_MDMA_CXBNDTR_OFFSET(14)
#define STM32_MDMA_C15BNDTR_OFFSET    STM32_MDMA_CXBNDTR_OFFSET(15)

#define STM32_MDMA_CXSAR_OFFSET(x)  (0x0058+0x040*(x)) /* MDMA channel x source address register */
#define STM32_MDMA_C0SAR_OFFSET     STM32_MDMA_CXSAR_OFFSET(0)
#define STM32_MDMA_C1SAR_OFFSET     STM32_MDMA_CXSAR_OFFSET(1)
#define STM32_MDMA_C2SAR_OFFSET     STM32_MDMA_CXSAR_OFFSET(2)
#define STM32_MDMA_C3SAR_OFFSET     STM32_MDMA_CXSAR_OFFSET(3)
#define STM32_MDMA_C4SAR_OFFSET     STM32_MDMA_CXSAR_OFFSET(4)
#define STM32_MDMA_C5SAR_OFFSET     STM32_MDMA_CXSAR_OFFSET(5)
#define STM32_MDMA_C6SAR_OFFSET     STM32_MDMA_CXSAR_OFFSET(6)
#define STM32_MDMA_C7SAR_OFFSET     STM32_MDMA_CXSAR_OFFSET(7)
#define STM32_MDMA_C8SAR_OFFSET     STM32_MDMA_CXSAR_OFFSET(8)
#define STM32_MDMA_C9SAR_OFFSET     STM32_MDMA_CXSAR_OFFSET(9)
#define STM32_MDMA_C10SAR_OFFSET    STM32_MDMA_CXSAR_OFFSET(10)
#define STM32_MDMA_C11SAR_OFFSET    STM32_MDMA_CXSAR_OFFSET(11)
#define STM32_MDMA_C12SAR_OFFSET    STM32_MDMA_CXSAR_OFFSET(12)
#define STM32_MDMA_C13SAR_OFFSET    STM32_MDMA_CXSAR_OFFSET(13)
#define STM32_MDMA_C14SAR_OFFSET    STM32_MDMA_CXSAR_OFFSET(14)
#define STM32_MDMA_C15SAR_OFFSET    STM32_MDMA_CXSAR_OFFSET(15)

#define STM32_MDMA_CXDAR_OFFSET(x)  (0x005C+0x040*(x)) /* MDMA channel x destination address register */
#define STM32_MDMA_C0DAR_OFFSET     STM32_MDMA_CXDAR_OFFSET(0)
#define STM32_MDMA_C1DAR_OFFSET     STM32_MDMA_CXDAR_OFFSET(1)
#define STM32_MDMA_C2DAR_OFFSET     STM32_MDMA_CXDAR_OFFSET(2)
#define STM32_MDMA_C3DAR_OFFSET     STM32_MDMA_CXDAR_OFFSET(3)
#define STM32_MDMA_C4DAR_OFFSET     STM32_MDMA_CXDAR_OFFSET(4)
#define STM32_MDMA_C5DAR_OFFSET     STM32_MDMA_CXDAR_OFFSET(5)
#define STM32_MDMA_C6DAR_OFFSET     STM32_MDMA_CXDAR_OFFSET(6)
#define STM32_MDMA_C7DAR_OFFSET     STM32_MDMA_CXDAR_OFFSET(7)
#define STM32_MDMA_C8DAR_OFFSET     STM32_MDMA_CXDAR_OFFSET(8)
#define STM32_MDMA_C9DAR_OFFSET     STM32_MDMA_CXDAR_OFFSET(9)
#define STM32_MDMA_C10DAR_OFFSET    STM32_MDMA_CXDAR_OFFSET(10)
#define STM32_MDMA_C11DAR_OFFSET    STM32_MDMA_CXDAR_OFFSET(11)
#define STM32_MDMA_C12DAR_OFFSET    STM32_MDMA_CXDAR_OFFSET(12)
#define STM32_MDMA_C13DAR_OFFSET    STM32_MDMA_CXDAR_OFFSET(13)
#define STM32_MDMA_C14DAR_OFFSET    STM32_MDMA_CXDAR_OFFSET(14)
#define STM32_MDMA_C15DAR_OFFSET    STM32_MDMA_CXDAR_OFFSET(15)

#define STM32_MDMA_CXBRUR_OFFSET(x)  (0x0060+0x040*(x)) /* MDMA channel x block repeat address update register */
#define STM32_MDMA_C0BRUR_OFFSET     STM32_MDMA_CXBRUR_OFFSET(0)
#define STM32_MDMA_C1BRUR_OFFSET     STM32_MDMA_CXBRUR_OFFSET(1)
#define STM32_MDMA_C2BRUR_OFFSET     STM32_MDMA_CXBRUR_OFFSET(2)
#define STM32_MDMA_C3BRUR_OFFSET     STM32_MDMA_CXBRUR_OFFSET(3)
#define STM32_MDMA_C4BRUR_OFFSET     STM32_MDMA_CXBRUR_OFFSET(4)
#define STM32_MDMA_C5BRUR_OFFSET     STM32_MDMA_CXBRUR_OFFSET(5)
#define STM32_MDMA_C6BRUR_OFFSET     STM32_MDMA_CXBRUR_OFFSET(6)
#define STM32_MDMA_C7BRUR_OFFSET     STM32_MDMA_CXBRUR_OFFSET(7)
#define STM32_MDMA_C8BRUR_OFFSET     STM32_MDMA_CXBRUR_OFFSET(8)
#define STM32_MDMA_C9BRUR_OFFSET     STM32_MDMA_CXBRUR_OFFSET(9)
#define STM32_MDMA_C10BRUR_OFFSET    STM32_MDMA_CXBRUR_OFFSET(10)
#define STM32_MDMA_C11BRUR_OFFSET    STM32_MDMA_CXBRUR_OFFSET(11)
#define STM32_MDMA_C12BRUR_OFFSET    STM32_MDMA_CXBRUR_OFFSET(12)
#define STM32_MDMA_C13BRUR_OFFSET    STM32_MDMA_CXBRUR_OFFSET(13)
#define STM32_MDMA_C14BRUR_OFFSET    STM32_MDMA_CXBRUR_OFFSET(14)
#define STM32_MDMA_C15BRUR_OFFSET    STM32_MDMA_CXBRUR_OFFSET(15)

#define STM32_MDMA_CXLAR_OFFSET(x)  (0x0064+0x040*(x)) /* MDMA channel x link address register */
#define STM32_MDMA_C0LAR_OFFSET     STM32_MDMA_CXLAR_OFFSET(0)
#define STM32_MDMA_C1LAR_OFFSET     STM32_MDMA_CXLAR_OFFSET(1)
#define STM32_MDMA_C2LAR_OFFSET     STM32_MDMA_CXLAR_OFFSET(2)
#define STM32_MDMA_C3LAR_OFFSET     STM32_MDMA_CXLAR_OFFSET(3)
#define STM32_MDMA_C4LAR_OFFSET     STM32_MDMA_CXLAR_OFFSET(4)
#define STM32_MDMA_C5LAR_OFFSET     STM32_MDMA_CXLAR_OFFSET(5)
#define STM32_MDMA_C6LAR_OFFSET     STM32_MDMA_CXLAR_OFFSET(6)
#define STM32_MDMA_C7LAR_OFFSET     STM32_MDMA_CXLAR_OFFSET(7)
#define STM32_MDMA_C8LAR_OFFSET     STM32_MDMA_CXLAR_OFFSET(8)
#define STM32_MDMA_C9LAR_OFFSET     STM32_MDMA_CXLAR_OFFSET(9)
#define STM32_MDMA_C10LAR_OFFSET    STM32_MDMA_CXLAR_OFFSET(10)
#define STM32_MDMA_C11LAR_OFFSET    STM32_MDMA_CXLAR_OFFSET(11)
#define STM32_MDMA_C12LAR_OFFSET    STM32_MDMA_CXLAR_OFFSET(12)
#define STM32_MDMA_C13LAR_OFFSET    STM32_MDMA_CXLAR_OFFSET(13)
#define STM32_MDMA_C14LAR_OFFSET    STM32_MDMA_CXLAR_OFFSET(14)
#define STM32_MDMA_C15LAR_OFFSET    STM32_MDMA_CXLAR_OFFSET(15)

#define STM32_MDMA_CXTBR_OFFSET(x)  (0x0068+0x040*(x)) /* MDMA channel x trigger and bus selection register */
#define STM32_MDMA_C0TBR_OFFSET     STM32_MDMA_CXTBR_OFFSET(0)
#define STM32_MDMA_C1TBR_OFFSET     STM32_MDMA_CXTBR_OFFSET(1)
#define STM32_MDMA_C2TBR_OFFSET     STM32_MDMA_CXTBR_OFFSET(2)
#define STM32_MDMA_C3TBR_OFFSET     STM32_MDMA_CXTBR_OFFSET(3)
#define STM32_MDMA_C4TBR_OFFSET     STM32_MDMA_CXTBR_OFFSET(4)
#define STM32_MDMA_C5TBR_OFFSET     STM32_MDMA_CXTBR_OFFSET(5)
#define STM32_MDMA_C6TBR_OFFSET     STM32_MDMA_CXTBR_OFFSET(6)
#define STM32_MDMA_C7TBR_OFFSET     STM32_MDMA_CXTBR_OFFSET(7)
#define STM32_MDMA_C8TBR_OFFSET     STM32_MDMA_CXTBR_OFFSET(8)
#define STM32_MDMA_C9TBR_OFFSET     STM32_MDMA_CXTBR_OFFSET(9)
#define STM32_MDMA_C10TBR_OFFSET    STM32_MDMA_CXTBR_OFFSET(10)
#define STM32_MDMA_C11TBR_OFFSET    STM32_MDMA_CXTBR_OFFSET(11)
#define STM32_MDMA_C12TBR_OFFSET    STM32_MDMA_CXTBR_OFFSET(12)
#define STM32_MDMA_C13TBR_OFFSET    STM32_MDMA_CXTBR_OFFSET(13)
#define STM32_MDMA_C14TBR_OFFSET    STM32_MDMA_CXTBR_OFFSET(14)
#define STM32_MDMA_C15TBR_OFFSET    STM32_MDMA_CXTBR_OFFSET(15)

#define STM32_MDMA_CXMAR_OFFSET(x)  (0x0070+0x040*(x)) /* MDMA channel x mask address register */
#define STM32_MDMA_C0MAR_OFFSET     STM32_MDMA_CXMAR_OFFSET(0)
#define STM32_MDMA_C1MAR_OFFSET     STM32_MDMA_CXMAR_OFFSET(1)
#define STM32_MDMA_C2MAR_OFFSET     STM32_MDMA_CXMAR_OFFSET(2)
#define STM32_MDMA_C3MAR_OFFSET     STM32_MDMA_CXMAR_OFFSET(3)
#define STM32_MDMA_C4MAR_OFFSET     STM32_MDMA_CXMAR_OFFSET(4)
#define STM32_MDMA_C5MAR_OFFSET     STM32_MDMA_CXMAR_OFFSET(5)
#define STM32_MDMA_C6MAR_OFFSET     STM32_MDMA_CXMAR_OFFSET(6)
#define STM32_MDMA_C7MAR_OFFSET     STM32_MDMA_CXMAR_OFFSET(7)
#define STM32_MDMA_C8MAR_OFFSET     STM32_MDMA_CXMAR_OFFSET(8)
#define STM32_MDMA_C9MAR_OFFSET     STM32_MDMA_CXMAR_OFFSET(9)
#define STM32_MDMA_C10MAR_OFFSET    STM32_MDMA_CXMAR_OFFSET(10)
#define STM32_MDMA_C11MAR_OFFSET    STM32_MDMA_CXMAR_OFFSET(11)
#define STM32_MDMA_C12MAR_OFFSET    STM32_MDMA_CXMAR_OFFSET(12)
#define STM32_MDMA_C13MAR_OFFSET    STM32_MDMA_CXMAR_OFFSET(13)
#define STM32_MDMA_C14MAR_OFFSET    STM32_MDMA_CXMAR_OFFSET(14)
#define STM32_MDMA_C15MAR_OFFSET    STM32_MDMA_CXMAR_OFFSET(15)

#define STM32_MDMA_CXMDR_OFFSET(x)  (0x0074+0x040*(x)) /* MDMA channel x mask data register */
#define STM32_MDMA_C0MDR_OFFSET     STM32_MDMA_CXMDR_OFFSET(0)
#define STM32_MDMA_C1MDR_OFFSET     STM32_MDMA_CXMDR_OFFSET(1)
#define STM32_MDMA_C2MDR_OFFSET     STM32_MDMA_CXMDR_OFFSET(2)
#define STM32_MDMA_C3MDR_OFFSET     STM32_MDMA_CXMDR_OFFSET(3)
#define STM32_MDMA_C4MDR_OFFSET     STM32_MDMA_CXMDR_OFFSET(4)
#define STM32_MDMA_C5MDR_OFFSET     STM32_MDMA_CXMDR_OFFSET(5)
#define STM32_MDMA_C6MDR_OFFSET     STM32_MDMA_CXMDR_OFFSET(6)
#define STM32_MDMA_C7MDR_OFFSET     STM32_MDMA_CXMDR_OFFSET(7)
#define STM32_MDMA_C8MDR_OFFSET     STM32_MDMA_CXMDR_OFFSET(8)
#define STM32_MDMA_C9MDR_OFFSET     STM32_MDMA_CXMDR_OFFSET(9)
#define STM32_MDMA_C10MDR_OFFSET    STM32_MDMA_CXMDR_OFFSET(10)
#define STM32_MDMA_C11MDR_OFFSET    STM32_MDMA_CXMDR_OFFSET(11)
#define STM32_MDMA_C12MDR_OFFSET    STM32_MDMA_CXMDR_OFFSET(12)
#define STM32_MDMA_C13MDR_OFFSET    STM32_MDMA_CXMDR_OFFSET(13)
#define STM32_MDMA_C14MDR_OFFSET    STM32_MDMA_CXMDR_OFFSET(14)
#define STM32_MDMA_C15MDR_OFFSET    STM32_MDMA_CXMDR_OFFSET(15)

/* Register Addresses *******************************************************/

#define STM32_MDMA_GISR0          (STM32_MDMA_BASE+STM32_MDMA_GISR0_OFFSET)

#define STM32_MDMA_CXISR(x)       (STM32_MDMA_BASE+STM32_MDMA_CXISR_OFFSET(x))
#define STM32_MDMA_C0ISR          (STM32_MDMA_BASE+STM32_MDMA_C0ISR_OFFSET)
#define STM32_MDMA_C1ISR          (STM32_MDMA_BASE+STM32_MDMA_C1ISR_OFFSET)
#define STM32_MDMA_C2ISR          (STM32_MDMA_BASE+STM32_MDMA_C2ISR_OFFSET)
#define STM32_MDMA_C3ISR          (STM32_MDMA_BASE+STM32_MDMA_C3ISR_OFFSET)
#define STM32_MDMA_C4ISR          (STM32_MDMA_BASE+STM32_MDMA_C4ISR_OFFSET)
#define STM32_MDMA_C5ISR          (STM32_MDMA_BASE+STM32_MDMA_C5ISR_OFFSET)
#define STM32_MDMA_C6ISR          (STM32_MDMA_BASE+STM32_MDMA_C6ISR_OFFSET)
#define STM32_MDMA_C7ISR          (STM32_MDMA_BASE+STM32_MDMA_C7ISR_OFFSET)
#define STM32_MDMA_C8ISR          (STM32_MDMA_BASE+STM32_MDMA_C8ISR_OFFSET)
#define STM32_MDMA_C9ISR          (STM32_MDMA_BASE+STM32_MDMA_C9ISR_OFFSET)
#define STM32_MDMA_C10ISR         (STM32_MDMA_BASE+STM32_MDMA_C10ISR_OFFSET)
#define STM32_MDMA_C11ISR         (STM32_MDMA_BASE+STM32_MDMA_C11ISR_OFFSET)
#define STM32_MDMA_C12ISR         (STM32_MDMA_BASE+STM32_MDMA_C12ISR_OFFSET)
#define STM32_MDMA_C13ISR         (STM32_MDMA_BASE+STM32_MDMA_C13ISR_OFFSET)
#define STM32_MDMA_C14ISR         (STM32_MDMA_BASE+STM32_MDMA_C14ISR_OFFSET)
#define STM32_MDMA_C15ISR         (STM32_MDMA_BASE+STM32_MDMA_C15ISR_OFFSET)

#define STM32_MDMA_CXIFCR(x)      (STM32_MDMA_BASE+STM32_MDMA_CXIFCR_OFFSET(x))
#define STM32_MDMA_C0IFCR         (STM32_MDMA_BASE+STM32_MDMA_C0IFCR_OFFSET)
#define STM32_MDMA_C1IFCR         (STM32_MDMA_BASE+STM32_MDMA_C1IFCR_OFFSET)
#define STM32_MDMA_C2IFCR         (STM32_MDMA_BASE+STM32_MDMA_C2IFCR_OFFSET)
#define STM32_MDMA_C3IFCR         (STM32_MDMA_BASE+STM32_MDMA_C3IFCR_OFFSET)
#define STM32_MDMA_C4IFCR         (STM32_MDMA_BASE+STM32_MDMA_C4IFCR_OFFSET)
#define STM32_MDMA_C5IFCR         (STM32_MDMA_BASE+STM32_MDMA_C5IFCR_OFFSET)
#define STM32_MDMA_C6IFCR         (STM32_MDMA_BASE+STM32_MDMA_C6IFCR_OFFSET)
#define STM32_MDMA_C7IFCR         (STM32_MDMA_BASE+STM32_MDMA_C7IFCR_OFFSET)
#define STM32_MDMA_C8IFCR         (STM32_MDMA_BASE+STM32_MDMA_C8IFCR_OFFSET)
#define STM32_MDMA_C9IFCR         (STM32_MDMA_BASE+STM32_MDMA_C9IFCR_OFFSET)
#define STM32_MDMA_C10IFCR        (STM32_MDMA_BASE+STM32_MDMA_C10IFCR_OFFSET)
#define STM32_MDMA_C11IFCR        (STM32_MDMA_BASE+STM32_MDMA_C11IFCR_OFFSET)
#define STM32_MDMA_C12IFCR        (STM32_MDMA_BASE+STM32_MDMA_C12IFCR_OFFSET)
#define STM32_MDMA_C13IFCR        (STM32_MDMA_BASE+STM32_MDMA_C13IFCR_OFFSET)
#define STM32_MDMA_C14IFCR        (STM32_MDMA_BASE+STM32_MDMA_C14IFCR_OFFSET)
#define STM32_MDMA_C15IFCR        (STM32_MDMA_BASE+STM32_MDMA_C15IFCR_OFFSET)

#define STM32_MDMA_CXESR(x)       (STM32_MDMA_BASE+STM32_MDMA_CXESR_OFFSET(x))
#define STM32_MDMA_C0ESR          (STM32_MDMA_BASE+STM32_MDMA_C0ESR_OFFSET)
#define STM32_MDMA_C1ESR          (STM32_MDMA_BASE+STM32_MDMA_C1ESR_OFFSET)
#define STM32_MDMA_C2ESR          (STM32_MDMA_BASE+STM32_MDMA_C2ESR_OFFSET)
#define STM32_MDMA_C3ESR          (STM32_MDMA_BASE+STM32_MDMA_C3ESR_OFFSET)
#define STM32_MDMA_C4ESR          (STM32_MDMA_BASE+STM32_MDMA_C4ESR_OFFSET)
#define STM32_MDMA_C5ESR          (STM32_MDMA_BASE+STM32_MDMA_C5ESR_OFFSET)
#define STM32_MDMA_C6ESR          (STM32_MDMA_BASE+STM32_MDMA_C6ESR_OFFSET)
#define STM32_MDMA_C7ESR          (STM32_MDMA_BASE+STM32_MDMA_C7ESR_OFFSET)
#define STM32_MDMA_C8ESR          (STM32_MDMA_BASE+STM32_MDMA_C8ESR_OFFSET)
#define STM32_MDMA_C9ESR          (STM32_MDMA_BASE+STM32_MDMA_C9ESR_OFFSET)
#define STM32_MDMA_C10ESR         (STM32_MDMA_BASE+STM32_MDMA_C10ESR_OFFSET)
#define STM32_MDMA_C11ESR         (STM32_MDMA_BASE+STM32_MDMA_C11ESR_OFFSET)
#define STM32_MDMA_C12ESR         (STM32_MDMA_BASE+STM32_MDMA_C12ESR_OFFSET)
#define STM32_MDMA_C13ESR         (STM32_MDMA_BASE+STM32_MDMA_C13ESR_OFFSET)
#define STM32_MDMA_C14ESR         (STM32_MDMA_BASE+STM32_MDMA_C14ESR_OFFSET)
#define STM32_MDMA_C15ESR         (STM32_MDMA_BASE+STM32_MDMA_C15ESR_OFFSET)

#define STM32_MDMA_CXCR(x)        (STM32_MDMA_BASE+STM32_MDMA_CXCR_OFFSET(x))
#define STM32_MDMA_C0CR           (STM32_MDMA_BASE+STM32_MDMA_C0CR_OFFSET)
#define STM32_MDMA_C1CR           (STM32_MDMA_BASE+STM32_MDMA_C1CR_OFFSET)
#define STM32_MDMA_C2CR           (STM32_MDMA_BASE+STM32_MDMA_C2CR_OFFSET)
#define STM32_MDMA_C3CR           (STM32_MDMA_BASE+STM32_MDMA_C3CR_OFFSET)
#define STM32_MDMA_C4CR           (STM32_MDMA_BASE+STM32_MDMA_C4CR_OFFSET)
#define STM32_MDMA_C5CR           (STM32_MDMA_BASE+STM32_MDMA_C5CR_OFFSET)
#define STM32_MDMA_C6CR           (STM32_MDMA_BASE+STM32_MDMA_C6CR_OFFSET)
#define STM32_MDMA_C7CR           (STM32_MDMA_BASE+STM32_MDMA_C7CR_OFFSET)
#define STM32_MDMA_C8CR           (STM32_MDMA_BASE+STM32_MDMA_C8CR_OFFSET)
#define STM32_MDMA_C9CR           (STM32_MDMA_BASE+STM32_MDMA_C9CR_OFFSET)
#define STM32_MDMA_C10CR          (STM32_MDMA_BASE+STM32_MDMA_C10CR_OFFSET)
#define STM32_MDMA_C11CR          (STM32_MDMA_BASE+STM32_MDMA_C11CR_OFFSET)
#define STM32_MDMA_C12CR          (STM32_MDMA_BASE+STM32_MDMA_C12CR_OFFSET)
#define STM32_MDMA_C13CR          (STM32_MDMA_BASE+STM32_MDMA_C13CR_OFFSET)
#define STM32_MDMA_C14CR          (STM32_MDMA_BASE+STM32_MDMA_C14CR_OFFSET)
#define STM32_MDMA_C15CR          (STM32_MDMA_BASE+STM32_MDMA_C15CR_OFFSET)

#define STM32_MDMA_CXTCR(x)       (STM32_MDMA_BASE+STM32_MDMA_CXTCR_OFFSET(x))
#define STM32_MDMA_C0TCR          (STM32_MDMA_BASE+STM32_MDMA_C0TCR_OFFSET)
#define STM32_MDMA_C1TCR          (STM32_MDMA_BASE+STM32_MDMA_C1TCR_OFFSET)
#define STM32_MDMA_C2TCR          (STM32_MDMA_BASE+STM32_MDMA_C2TCR_OFFSET)
#define STM32_MDMA_C3TCR          (STM32_MDMA_BASE+STM32_MDMA_C3TCR_OFFSET)
#define STM32_MDMA_C4TCR          (STM32_MDMA_BASE+STM32_MDMA_C4TCR_OFFSET)
#define STM32_MDMA_C5TCR          (STM32_MDMA_BASE+STM32_MDMA_C5TCR_OFFSET)
#define STM32_MDMA_C6TCR          (STM32_MDMA_BASE+STM32_MDMA_C6TCR_OFFSET)
#define STM32_MDMA_C7TCR          (STM32_MDMA_BASE+STM32_MDMA_C7TCR_OFFSET)
#define STM32_MDMA_C8TCR          (STM32_MDMA_BASE+STM32_MDMA_C8TCR_OFFSET)
#define STM32_MDMA_C9TCR          (STM32_MDMA_BASE+STM32_MDMA_C9TCR_OFFSET)
#define STM32_MDMA_C10TCR         (STM32_MDMA_BASE+STM32_MDMA_C10TCR_OFFSET)
#define STM32_MDMA_C11TCR         (STM32_MDMA_BASE+STM32_MDMA_C11TCR_OFFSET)
#define STM32_MDMA_C12TCR         (STM32_MDMA_BASE+STM32_MDMA_C12TCR_OFFSET)
#define STM32_MDMA_C13TCR         (STM32_MDMA_BASE+STM32_MDMA_C13TCR_OFFSET)
#define STM32_MDMA_C14TCR         (STM32_MDMA_BASE+STM32_MDMA_C14TCR_OFFSET)
#define STM32_MDMA_C15TCR         (STM32_MDMA_BASE+STM32_MDMA_C15TCR_OFFSET)

#define STM32_MDMA_CXBNDTR(x)     (STM32_MDMA_BASE+STM32_MDMA_CXBNDTR_OFFSET(x))
#define STM32_MDMA_C0BNDTR        (STM32_MDMA_BASE+STM32_MDMA_C0BNDTR_OFFSET)
#define STM32_MDMA_C1BNDTR        (STM32_MDMA_BASE+STM32_MDMA_C1BNDTR_OFFSET)
#define STM32_MDMA_C2BNDTR        (STM32_MDMA_BASE+STM32_MDMA_C2BNDTR_OFFSET)
#define STM32_MDMA_C3BNDTR        (STM32_MDMA_BASE+STM32_MDMA_C3BNDTR_OFFSET)
#define STM32_MDMA_C4BNDTR        (STM32_MDMA_BASE+STM32_MDMA_C4BNDTR_OFFSET)
#define STM32_MDMA_C5BNDTR        (STM32_MDMA_BASE+STM32_MDMA_C5BNDTR_OFFSET)
#define STM32_MDMA_C6BNDTR        (STM32_MDMA_BASE+STM32_MDMA_C6BNDTR_OFFSET)
#define STM32_MDMA_C7BNDTR        (STM32_MDMA_BASE+STM32_MDMA_C7BNDTR_OFFSET)
#define STM32_MDMA_C8BNDTR        (STM32_MDMA_BASE+STM32_MDMA_C8BNDTR_OFFSET)
#define STM32_MDMA_C9BNDTR        (STM32_MDMA_BASE+STM32_MDMA_C9BNDTR_OFFSET)
#define STM32_MDMA_C10BNDTR       (STM32_MDMA_BASE+STM32_MDMA_C10BNDTR_OFFSET)
#define STM32_MDMA_C11BNDTR       (STM32_MDMA_BASE+STM32_MDMA_C11BNDTR_OFFSET)
#define STM32_MDMA_C12BNDTR       (STM32_MDMA_BASE+STM32_MDMA_C12BNDTR_OFFSET)
#define STM32_MDMA_C13BNDTR       (STM32_MDMA_BASE+STM32_MDMA_C13BNDTR_OFFSET)
#define STM32_MDMA_C14BNDTR       (STM32_MDMA_BASE+STM32_MDMA_C14BNDTR_OFFSET)
#define STM32_MDMA_C15BNDTR       (STM32_MDMA_BASE+STM32_MDMA_C15BNDTR_OFFSET)

#define STM32_MDMA_CXBRUR(x)      (STM32_MDMA_BASE+STM32_MDMA_CXBRUR_OFFSET(x))
#define STM32_MDMA_C0BRUR         (STM32_MDMA_BASE+STM32_MDMA_C0BRUR_OFFSET)
#define STM32_MDMA_C1BRUR         (STM32_MDMA_BASE+STM32_MDMA_C1BRUR_OFFSET)
#define STM32_MDMA_C2BRUR         (STM32_MDMA_BASE+STM32_MDMA_C2BRUR_OFFSET)
#define STM32_MDMA_C3BRUR         (STM32_MDMA_BASE+STM32_MDMA_C3BRUR_OFFSET)
#define STM32_MDMA_C4BRUR         (STM32_MDMA_BASE+STM32_MDMA_C4BRUR_OFFSET)
#define STM32_MDMA_C5BRUR         (STM32_MDMA_BASE+STM32_MDMA_C5BRUR_OFFSET)
#define STM32_MDMA_C6BRUR         (STM32_MDMA_BASE+STM32_MDMA_C6BRUR_OFFSET)
#define STM32_MDMA_C7BRUR         (STM32_MDMA_BASE+STM32_MDMA_C7BRUR_OFFSET)
#define STM32_MDMA_C8BRUR         (STM32_MDMA_BASE+STM32_MDMA_C8BRUR_OFFSET)
#define STM32_MDMA_C9BRUR         (STM32_MDMA_BASE+STM32_MDMA_C9BRUR_OFFSET)
#define STM32_MDMA_C10BRUR        (STM32_MDMA_BASE+STM32_MDMA_C10BRUR_OFFSET)
#define STM32_MDMA_C11BRUR        (STM32_MDMA_BASE+STM32_MDMA_C11BRUR_OFFSET)
#define STM32_MDMA_C12BRUR        (STM32_MDMA_BASE+STM32_MDMA_C12BRUR_OFFSET)
#define STM32_MDMA_C13BRUR        (STM32_MDMA_BASE+STM32_MDMA_C13BRUR_OFFSET)
#define STM32_MDMA_C14BRUR        (STM32_MDMA_BASE+STM32_MDMA_C14BRUR_OFFSET)
#define STM32_MDMA_C15BRUR        (STM32_MDMA_BASE+STM32_MDMA_C15BRUR_OFFSET)

#define STM32_MDMA_CXLAR(x)       (STM32_MDMA_BASE+STM32_MDMA_CXLAR_OFFSET(x))
#define STM32_MDMA_C0LAR          (STM32_MDMA_BASE+STM32_MDMA_C0LAR_OFFSET)
#define STM32_MDMA_C1LAR          (STM32_MDMA_BASE+STM32_MDMA_C1LAR_OFFSET)
#define STM32_MDMA_C2LAR          (STM32_MDMA_BASE+STM32_MDMA_C2LAR_OFFSET)
#define STM32_MDMA_C3LAR          (STM32_MDMA_BASE+STM32_MDMA_C3LAR_OFFSET)
#define STM32_MDMA_C4LAR          (STM32_MDMA_BASE+STM32_MDMA_C4LAR_OFFSET)
#define STM32_MDMA_C5LAR          (STM32_MDMA_BASE+STM32_MDMA_C5LAR_OFFSET)
#define STM32_MDMA_C6LAR          (STM32_MDMA_BASE+STM32_MDMA_C6LAR_OFFSET)
#define STM32_MDMA_C7LAR          (STM32_MDMA_BASE+STM32_MDMA_C7LAR_OFFSET)
#define STM32_MDMA_C8LAR          (STM32_MDMA_BASE+STM32_MDMA_C8LAR_OFFSET)
#define STM32_MDMA_C9LAR          (STM32_MDMA_BASE+STM32_MDMA_C9LAR_OFFSET)
#define STM32_MDMA_C10LAR         (STM32_MDMA_BASE+STM32_MDMA_C10LAR_OFFSET)
#define STM32_MDMA_C11LAR         (STM32_MDMA_BASE+STM32_MDMA_C11LAR_OFFSET)
#define STM32_MDMA_C12LAR         (STM32_MDMA_BASE+STM32_MDMA_C12LAR_OFFSET)
#define STM32_MDMA_C13LAR         (STM32_MDMA_BASE+STM32_MDMA_C13LAR_OFFSET)
#define STM32_MDMA_C14LAR         (STM32_MDMA_BASE+STM32_MDMA_C14LAR_OFFSET)
#define STM32_MDMA_C15LAR         (STM32_MDMA_BASE+STM32_MDMA_C15LAR_OFFSET)

#define STM32_MDMA_CXTBR(x)       (STM32_MDMA_BASE+STM32_MDMA_CXTBR_OFFSET(x))
#define STM32_MDMA_C0TBR          (STM32_MDMA_BASE+STM32_MDMA_C0TBR_OFFSET)
#define STM32_MDMA_C1TBR          (STM32_MDMA_BASE+STM32_MDMA_C1TBR_OFFSET)
#define STM32_MDMA_C2TBR          (STM32_MDMA_BASE+STM32_MDMA_C2TBR_OFFSET)
#define STM32_MDMA_C3TBR          (STM32_MDMA_BASE+STM32_MDMA_C3TBR_OFFSET)
#define STM32_MDMA_C4TBR          (STM32_MDMA_BASE+STM32_MDMA_C4TBR_OFFSET)
#define STM32_MDMA_C5TBR          (STM32_MDMA_BASE+STM32_MDMA_C5TBR_OFFSET)
#define STM32_MDMA_C6TBR          (STM32_MDMA_BASE+STM32_MDMA_C6TBR_OFFSET)
#define STM32_MDMA_C7TBR          (STM32_MDMA_BASE+STM32_MDMA_C7TBR_OFFSET)
#define STM32_MDMA_C8TBR          (STM32_MDMA_BASE+STM32_MDMA_C8TBR_OFFSET)
#define STM32_MDMA_C9TBR          (STM32_MDMA_BASE+STM32_MDMA_C9TBR_OFFSET)
#define STM32_MDMA_C10TBR         (STM32_MDMA_BASE+STM32_MDMA_C10TBR_OFFSET)
#define STM32_MDMA_C11TBR         (STM32_MDMA_BASE+STM32_MDMA_C11TBR_OFFSET)
#define STM32_MDMA_C12TBR         (STM32_MDMA_BASE+STM32_MDMA_C12TBR_OFFSET)
#define STM32_MDMA_C13TBR         (STM32_MDMA_BASE+STM32_MDMA_C13TBR_OFFSET)
#define STM32_MDMA_C14TBR         (STM32_MDMA_BASE+STM32_MDMA_C14TBR_OFFSET)
#define STM32_MDMA_C15TBR         (STM32_MDMA_BASE+STM32_MDMA_C15TBR_OFFSET)

#define STM32_MDMA_CXMAR(x)       (STM32_MDMA_BASE+STM32_MDMA_CXMAR_OFFSET(x))
#define STM32_MDMA_C0MAR          (STM32_MDMA_BASE+STM32_MDMA_C0MAR_OFFSET)
#define STM32_MDMA_C1MAR          (STM32_MDMA_BASE+STM32_MDMA_C1MAR_OFFSET)
#define STM32_MDMA_C2MAR          (STM32_MDMA_BASE+STM32_MDMA_C2MAR_OFFSET)
#define STM32_MDMA_C3MAR          (STM32_MDMA_BASE+STM32_MDMA_C3MAR_OFFSET)
#define STM32_MDMA_C4MAR          (STM32_MDMA_BASE+STM32_MDMA_C4MAR_OFFSET)
#define STM32_MDMA_C5MAR          (STM32_MDMA_BASE+STM32_MDMA_C5MAR_OFFSET)
#define STM32_MDMA_C6MAR          (STM32_MDMA_BASE+STM32_MDMA_C6MAR_OFFSET)
#define STM32_MDMA_C7MAR          (STM32_MDMA_BASE+STM32_MDMA_C7MAR_OFFSET)
#define STM32_MDMA_C8MAR          (STM32_MDMA_BASE+STM32_MDMA_C8MAR_OFFSET)
#define STM32_MDMA_C9MAR          (STM32_MDMA_BASE+STM32_MDMA_C9MAR_OFFSET)
#define STM32_MDMA_C10MAR         (STM32_MDMA_BASE+STM32_MDMA_C10MAR_OFFSET)
#define STM32_MDMA_C11MAR         (STM32_MDMA_BASE+STM32_MDMA_C11MAR_OFFSET)
#define STM32_MDMA_C12MAR         (STM32_MDMA_BASE+STM32_MDMA_C12MAR_OFFSET)
#define STM32_MDMA_C13MAR         (STM32_MDMA_BASE+STM32_MDMA_C13MAR_OFFSET)
#define STM32_MDMA_C14MAR         (STM32_MDMA_BASE+STM32_MDMA_C14MAR_OFFSET)
#define STM32_MDMA_C15MAR         (STM32_MDMA_BASE+STM32_MDMA_C15MAR_OFFSET)

#define STM32_MDMA_CXMDR(x)       (STM32_MDMA_BASE+STM32_MDMA_CXMDR_OFFSET(x))
#define STM32_MDMA_C0MDR          (STM32_MDMA_BASE+STM32_MDMA_C0MDR_OFFSET)
#define STM32_MDMA_C1MDR          (STM32_MDMA_BASE+STM32_MDMA_C1MDR_OFFSET)
#define STM32_MDMA_C2MDR          (STM32_MDMA_BASE+STM32_MDMA_C2MDR_OFFSET)
#define STM32_MDMA_C3MDR          (STM32_MDMA_BASE+STM32_MDMA_C3MDR_OFFSET)
#define STM32_MDMA_C4MDR          (STM32_MDMA_BASE+STM32_MDMA_C4MDR_OFFSET)
#define STM32_MDMA_C5MDR          (STM32_MDMA_BASE+STM32_MDMA_C5MDR_OFFSET)
#define STM32_MDMA_C6MDR          (STM32_MDMA_BASE+STM32_MDMA_C6MDR_OFFSET)
#define STM32_MDMA_C7MDR          (STM32_MDMA_BASE+STM32_MDMA_C7MDR_OFFSET)
#define STM32_MDMA_C8MDR          (STM32_MDMA_BASE+STM32_MDMA_C8MDR_OFFSET)
#define STM32_MDMA_C9MDR          (STM32_MDMA_BASE+STM32_MDMA_C9MDR_OFFSET)
#define STM32_MDMA_C10MDR         (STM32_MDMA_BASE+STM32_MDMA_C10MDR_OFFSET)
#define STM32_MDMA_C11MDR         (STM32_MDMA_BASE+STM32_MDMA_C11MDR_OFFSET)
#define STM32_MDMA_C12MDR         (STM32_MDMA_BASE+STM32_MDMA_C12MDR_OFFSET)
#define STM32_MDMA_C13MDR         (STM32_MDMA_BASE+STM32_MDMA_C13MDR_OFFSET)
#define STM32_MDMA_C14MDR         (STM32_MDMA_BASE+STM32_MDMA_C14MDR_OFFSET)
#define STM32_MDMA_C15MDR         (STM32_MDMA_BASE+STM32_MDMA_C15MDR_OFFSET)

/* Register Bitfield Definitions ********************************************/

/* MDMA global interrupt/status register */

#define MDMA_CXISR_GIF(x)         (1 << x)

/* MDMA channel x interrupt/status register and channel x interrupt flag
 * clear register
 */

#define MDMA_INT_TEIF             (1 << 0) /* Bit 0: Channel X transfer error flag */
#define BDMA_INT_CTCIF            (1 << 1) /* Bit 1: Channel X transfer complete flag */
#define BDMA_INT_BRTIF            (1 << 2) /* Bit 2: Channel X block repeat transfer complete flag */
#define BDMA_INT_BTIF             (1 << 3) /* Bit 3: Channel X block transfer complete flag */
#define BDMA_INT_TCIF             (1 << 4) /* Bit 4: Channel X buffer transfer complete interrupt flag */
#define BDMA_INT_CRQA             (1 << 5) /* Bit 5: Channel X request active flag */

/* MDMA channel x error status register */

#define MDMA_CESR_TEA_SHIFT       (0)      /* Bits 0-6: Transfer error address */
#define MDMA_CESR_TEA_MASK        (0x7f << MDMA_CESR_TEA_SHIFT)
#define MDMA_CESR_TED             (7)      /* Bit 7: Transfer error direction */
#define MDMA_CESR_TELD            (8)      /* Bit 8: Transfer error link data */
#define MDMA_CESR_TEMD            (9)      /* Bit 9: Transfer error mask data */
#define MDMA_CESR_ASE             (10)     /* Bit 10: Address/size error */
#define MDMA_CESR_BSE             (11)     /* Bit 11: Block size error */

/* MDMA channel x control register */

#define MDMA_CCR_EN               (0)                      /* Bit 0: Channel enable / flag channel ready */
#define MDMA_CCR_TEIE             (1)                      /* Bit 1: Transfer error interrupt enable */
#define MDMA_CCR_CTCIE            (2)                      /* Bit 2: Channel transfer complete interrupt enable */
#define MDMA_CCR_BRTIE            (3)                      /* Bit 3: Block repeat transfer interrupt enable */
#define MDMA_CCR_BTIE             (4)                      /* Bit 4: Block transfer interrupt enable */
#define MDMA_CCR_TCIE             (5)                      /* Bit 5: Buffer transfer complete interrupt enable */
#define MDMA_CCR_PL_SHIFT         (6)                      /* Bis 6-7: Priority level */
#define MDMA_CCR_PL_MASK          (3 << MDMA_CCR_PL_SHIFT)
#  define MDMA_CCR_PRILO          (0 << MDMA_CCR_PL_SHIFT) /* 00: Low */
#  define MDMA_CCR_PRIMED         (1 << MDMA_CCR_PL_SHIFT) /* 01: Medium */
#  define MDMA_CCR_PRIHI          (2 << MDMA_CCR_PL_SHIFT) /* 10: High */
#  define MDMA_CCR_PRIVERYHI      (3 << MDMA_CCR_PL_SHIFT) /* 11: Very high */
#define MDMA_CCR_BEX              (12)                     /* Bit 12: Byte endianness exchange */
#define MDMA_CCR_HEX              (13)                     /* Bit 13: Half word endianness exchange */
#define MDMA_CCR_WEX              (14)                     /* Bit 14: Word endianness exchange */
#define MDMA_CCR_SWRQ             (16)                     /* Bit 16: Software request */

/* MDMA channel x transfer configuration register */

#define MDMA_CTCR_SINC_SHIFT      (0)                         /* Bits 0-1: Source increment mode */
#define MDMA_CTCR_SINC_MASK       (3 << MDMA_CTCR_SINC_SHIFT)
#  define MDMA_CTCR_SINC_FIXED    (0 << MDMA_CTCR_SINC_SHIFT) /* 00: */
#  define MDMA_CTCR_SINC_INCR     (2 << MDMA_CTCR_SINC_SHIFT) /* 10: */
#  define MDMA_CTCR_SINC_DECR     (3 << MDMA_CTCR_SINC_SHIFT) /* 11: */
#define MDMA_CTCR_DINC_SHIFT      (2)                         /* Bits 2-3: Destination increment mode */
#define MDMA_CTCR_DINC_MASK       (3 << MDMA_CTCR_DINC_SHIFT)
#  define MDMA_CTCR_DINC_FIXED    (0 << MDMA_CTCR_DINC_SHIFT) /* 00: */
#  define MDMA_CTCR_DINC_INCR     (2 << MDMA_CTCR_DINC_SHIFT) /* 10: */
#  define MDMA_CTCR_DINC_DECR     (3 << MDMA_CTCR_DINC_SHIFT) /* 11: */
#define MDMA_CTCR_SSIZE_SHIFT     (4)                         /* Bits 4-5: Source data size */
#define MDMA_CTCR_SSIZE_MASK      (3 << MDMA_CTCR_SSIZE_SHIFT)
#  define MDMA_CTCR_SSIZE_8BITS   (0 << MDMA_CTCR_SSIZE_SHIFT) /* 00: */
#  define MDMA_CTCR_SSIZE_16BITS  (1 << MDMA_CTCR_SSIZE_SHIFT) /* 01: */
#  define MDMA_CTCR_SSIZE_32BITS  (2 << MDMA_CTCR_SSIZE_SHIFT) /* 10: */
#  define MDMA_CTCR_SSIZE_64BITS  (3 << MDMA_CTCR_SSIZE_SHIFT) /* 11: */
#define MDMA_CTCR_DSIZE_SHIFT     (6)                          /* Bits 6-7: Destination data size */
#define MDMA_CTCR_DSIZE_MASK      (3 << MDMA_CTCR_DSIZE_SHIFT)
#  define MDMA_CTCR_DSIZE_8BITS   (0 << MDMA_CTCR_DSIZE_SHIFT) /* 00: */
#  define MDMA_CTCR_DSIZE_16BITS  (1 << MDMA_CTCR_DSIZE_SHIFT) /* 01: */
#  define MDMA_CTCR_DSIZE_32BITS  (2 << MDMA_CTCR_DSIZE_SHIFT) /* 10: */
#  define MDMA_CTCR_DSIZE_64BITS  (3 << MDMA_CTCR_DSIZE_SHIFT) /* 11: */
#define MDMA_CTCR_SINCOS_SHIFT    (8)                          /* Bits 8-9: Source increment offset size */
#define MDMA_CTCR_SINCOS_MASK     (3 << MDMA_CTCR_SINCOS_SHIFT)
#  define MDMA_CTCR_SINCOS_8BITS  (0 << MDMA_CTCR_SINCOS_SHIFT) /* 00: */
#  define MDMA_CTCR_SINCOS_16BITS (1 << MDMA_CTCR_SINCOS_SHIFT) /* 01: */
#  define MDMA_CTCR_SINCOS_32BITS (2 << MDMA_CTCR_SINCOS_SHIFT) /* 10: */
#  define MDMA_CTCR_SINCOS_64BITS (3 << MDMA_CTCR_SINCOS_SHIFT) /* 11: */
#define MDMA_CTCR_DINCOS_SHIFT    (10)                          /* Bits 10-11: Destination increment offset size */
#define MDMA_CTCR_DINCOS_MASK     (7 << MDMA_CTCR_DINCOS_SHIFT)
#  define MDMA_CTCR_DINCOS_8BITS  (0 << MDMA_CTCR_DINCOS_SHIFT) /* 00: */
#  define MDMA_CTCR_DINCOS_16BITS (1 << MDMA_CTCR_DINCOS_SHIFT) /* 01: */
#  define MDMA_CTCR_DINCOS_32BITS (2 << MDMA_CTCR_DINCOS_SHIFT) /* 10: */
#  define MDMA_CTCR_DINCOS_64BITS (3 << MDMA_CTCR_DINCOS_SHIFT) /* 11: */
#define MDMA_CTCR_SBURST_SHIFT    (12)                          /* Bits 12-14: Source burst transfer configuration */
#define MDMA_CTCR_SBURST_MASK     (7 << MDMA_CTCR_SBURST_SHIFT)
#  define MDMA_CTCR_SBURST_1      (0 << MDMA_CTCR_SBURST_SHIFT) /* 000: */
#  define MDMA_CTCR_SBURST_2      (1 << MDMA_CTCR_SBURST_SHIFT) /* 001: */
#  define MDMA_CTCR_SBURST_4      (2 << MDMA_CTCR_SBURST_SHIFT) /* 010: */
#  define MDMA_CTCR_SBURST_8      (3 << MDMA_CTCR_SBURST_SHIFT) /* 011: */
#  define MDMA_CTCR_SBURST_16     (4 << MDMA_CTCR_SBURST_SHIFT) /* 100: */
#  define MDMA_CTCR_SBURST_32     (5 << MDMA_CTCR_SBURST_SHIFT) /* 101: */
#  define MDMA_CTCR_SBURST_64     (6 << MDMA_CTCR_SBURST_SHIFT) /* 110: */
#  define MDMA_CTCR_SBURST_128    (7 << MDMA_CTCR_SBURST_SHIFT) /* 111: */
#define MDMA_CTCR_DBURST_SHIFT    (15)                          /* Bits 15-16: Destination burst transfer configuration */
#define MDMA_CTCR_DBURST_MASK     (7 << MDMA_CTCR_DBURST_SHIFT)
#  define MDMA_CTCR_DBURST_1      (0 << MDMA_CTCR_DBURST_SHIFT) /* 000: */
#  define MDMA_CTCR_DBURST_2      (1 << MDMA_CTCR_DBURST_SHIFT) /* 001: */
#  define MDMA_CTCR_DBURST_4      (2 << MDMA_CTCR_DBURST_SHIFT) /* 010: */
#  define MDMA_CTCR_DBURST_8      (3 << MDMA_CTCR_DBURST_SHIFT) /* 011: */
#  define MDMA_CTCR_DBURST_16     (4 << MDMA_CTCR_DBURST_SHIFT) /* 100: */
#  define MDMA_CTCR_DBURST_32     (5 << MDMA_CTCR_DBURST_SHIFT) /* 101: */
#  define MDMA_CTCR_DBURST_64     (6 << MDMA_CTCR_DBURST_SHIFT) /* 110: */
#  define MDMA_CTCR_DBURST_128    (7 << MDMA_CTCR_DBURST_SHIFT) /* 111: */
#define MDMA_CTCR_TLEN_SHIFT      (18)                          /* Bits 18-24: Buffer transfer length - 1  */
#define MDMA_CTCR_TLEN_MASK       (0x7f << MDMA_CTCR_TLEN_SHIFT)
#  define MDMA_CTCR_TLEN(len)     (((len-1) << MDMA_CTCR_TLEN_SHIFT) & MDMA_CTCR_TLEN_MASK)
#define MDMA_CTCR_PKE             (25)                          /* Bit 25: Pack enable */
#define MDMA_CTCR_PAM_SHIFT       (26)                          /* Bits 26-27: Padding/alignment mode */
#define MDMA_CTCR_PAM_MASK        (3 << MDMA_CTCR_PAM_SHIFT)
#  define MDMA_CTCR_PAM_RIGHT     (0 << MDMA_CTCR_PAM_SHIFT)    /* 00: */
#  define MDMA_CTCR_PAM_SINRIGHT  (1 << MDMA_CTCR_PAM_SHIFT)    /* 01: */
#  define MDMA_CTCR_PAM_LEFT      (2 << MDMA_CTCR_PAM_SHIFT)    /* 10: */
#define MDMA_CTCR_TRGM_SHIFT      (28)                          /* Bits 28-29: Trigger mode */
#define MDMA_CTCR_TRGM_MASK       (3 << MDMA_CTCR_TRGM_SHIFT)
#  define MDMA_CTCR_TRGM_BUFFER   (0 << MDMA_CTCR_TRGM_SHIFT)   /* 00: */
#  define MDMA_CTCR_TRGM_BLOCK    (1 << MDMA_CTCR_TRGM_SHIFT)   /* 01: */
#  define MDMA_CTCR_TRGM_RBLOCK   (2 << MDMA_CTCR_TRGM_SHIFT)   /* 10: */
#  define MDMA_CTCR_TRGM_DATA     (3 << MDMA_CTCR_TRGM_SHIFT)   /* 11: */
#define MDMA_CTCR_SWRM            (30)                          /* Bit 30: Software request mode */
#define MDMA_CTCR_BWM             (31)                          /* Bit 31: Bufferable write mode */

/* MDMA channel x block number of data register */

#define MDMA_CBNDTR_BNDT_SHIFT    (0) /* Bits 0-16: Block number of data bytes to transfer */
#define MDMA_CBNDTR_BNDT_MASK     (0x1ffff << MDMA_CBNDTR_BNDT_SHIFT)
#define MDMA_CBNDTR_BRSUM         (18) /* Bit 18: Block repeat source address update mode */
#define MDMA_CBNDTR_BRDUM         (19) /* Bit 19: Block repeat destination address update mode */
#define MDMA_CBNDTR_BRC_SHIFT     (20) /* Bits 20-31: Block repeat count */
#define MDMA_CBNDTR_BRC_MASK      (0xfff << MDMA_CBNDTR_BNDT_SHIFT)

/* MDMA channel x block repeat address update register */

#define MDMA_CBRUR_SUV_SHIFT      (0)  /* Bits 0-15: Source address update value */
#define MDMA_CBRUR_SUV_MASK       (0xff << MDMA_CBRUR_SUV_SHIFT)
#define MDMA_CBRUR_DUV_SHIFT      (16) /* Bits 16-31: Destination address update value */
#define MDMA_CBRUR_DUV_MASK       (0xff << MDMA_CBRUR_DUV_SHIFT)

/* MDMA channel x trigger and bus selection register */

#define MDMA_TSEL_SHIFT           (0)  /* Bits 0-5: Trigger selection */
#define MDMA_TSEL_MASK            (0x3f << MDMA_TSEL_SHIFT)
#define MDMA_TSEL_SBUS            (16) /* Bit 16: Source BUS select */
#define MDMA_TSEL_DBUS            (17) /* Bit 16: Destination BUS select */

#endif /* __ARCH_ARM_SRC_STM32H7_HARDWARE_STM32_MDMA_H */
