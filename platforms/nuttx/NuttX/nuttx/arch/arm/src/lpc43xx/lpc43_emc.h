/****************************************************************************
 * arch/arm/src/lpc43xx/lpc43_emc.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_LPC43XX_LPC43_EMC_H
#define __ARCH_ARM_SRC_LPC43XX_LPC43_EMC_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include "chip.h"
#include "hardware/lpc43_emc.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Chip select Definitions **************************************************/

#define EMC_CS0    0
#define EMC_CS1    1
#define EMC_CS2    2
#define EMC_CS3    3

#define EMC_DYNCS0 0
#define EMC_DYNCS1 1
#define EMC_DYNCS2 2
#define EMC_DYNCS3 3

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/

void lpc43_emcinit(uint32_t enable,
                   uint32_t clock_ratio, uint32_t endian_mode);

#endif /* __ARCH_ARM_SRC_LPC43XX_LPC43_EMC_H */
