############################################################################
# arch/arm/src/lpc43xx/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

include armv7-m/Make.defs

CHIP_CSRCS  = lpc43_allocateheap.c lpc43_cgu.c lpc43_clrpend.c lpc43_gpio.c
CHIP_CSRCS += lpc43_irq.c lpc43_pinconfig.c lpc43_rgu.c lpc43_serial.c
CHIP_CSRCS += lpc43_start.c lpc43_uart.c

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += lpc43_timerisr.c
else
CHIP_CSRCS += lpc43_tickless_rit.c
endif

ifeq ($(CONFIG_BUILD_PROTECTED),y)
CHIP_CSRCS += lpc43_userspace.c lpc43_mpuinit.c
endif

ifneq ($(CONFIG_ARCH_IDLE_CUSTOM),y)
CHIP_CSRCS += lpc43_idle.c
endif

ifeq ($(CONFIG_DEBUG_FEATURES),y)
CHIP_CSRCS += lpc43_debug.c
endif

ifeq ($(CONFIG_LPC43_GPDMA),y)
CHIP_CSRCS += lpc43_gpdma.c
endif

ifeq ($(CONFIG_LPC43_GPIO_IRQ),y)
CHIP_CSRCS += lpc43_gpioint.c
endif

ifeq ($(CONFIG_LPC43_WWDT),y)
CHIP_CSRCS += lpc43_wwdt.c
endif

ifeq ($(CONFIG_LPC43_SDMMC),y)
CHIP_CSRCS += lpc43_sdmmc.c
endif

ifeq ($(CONFIG_LPC43_ETHERNET),y)
CHIP_CSRCS += lpc43_ethernet.c
endif

ifeq ($(CONFIG_LPC43_EMC),y)
CHIP_CSRCS += lpc43_emc.c
endif

ifeq ($(CONFIG_LPC43_SPI),y)
CHIP_CSRCS += lpc43_spi.c
else ifeq ($(CONFIG_LPC43_SSP0),y)
CHIP_CSRCS += lpc43_spi.c
else ifeq ($(CONFIG_LPC43_SSP1),y)
CHIP_CSRCS += lpc43_spi.c
endif

ifeq ($(CONFIG_LPC43_SPIFI),y)
CHIP_CSRCS += lpc43_spifi.c
endif

ifeq ($(CONFIG_LPC43_SSP0),y)
CHIP_CSRCS += lpc43_ssp.c
else ifeq ($(CONFIG_LPC43_SSP1),y)
CHIP_CSRCS += lpc43_ssp.c
endif

ifeq ($(CONFIG_LPC43_TIMER),y)
CHIP_CSRCS += lpc43_timer.c
endif

ifeq ($(CONFIG_LPC43_RIT),y)
CHIP_CSRCS += lpc43_rit.c
endif

ifeq ($(CONFIG_LPC43_RTC),y)
CHIP_CSRCS += lpc43_rtc.c
endif

ifeq ($(CONFIG_LPC43_I2C0),y)
CHIP_CSRCS += lpc43_i2c.c
else ifeq ($(CONFIG_LPC43_I2C1),y)
CHIP_CSRCS += lpc43_i2c.c
endif

ifeq ($(CONFIG_LPC43_CAN0),y)
CHIP_CSRCS += lpc43_can.c
else ifeq ($(CONFIG_LPC43_CAN1),y)
CHIP_CSRCS += lpc43_can.c
endif

ifeq ($(CONFIG_LPC43_ADC0),y)
CHIP_CSRCS += lpc43_adc.c
else ifeq ($(CONFIG_LPC43_ADC1),y)
CHIP_CSRCS += lpc43_adc.c
endif

ifeq ($(CONFIG_LPC43_DAC),y)
CHIP_CSRCS += lpc43_dac.c
endif

ifeq ($(CONFIG_LPC43_USBOTG),y)
CHIP_CSRCS += lpc43_ehci.c
endif

ifeq ($(CONFIG_CRYPTO_AES),y)
CHIP_CSRCS += lpc43_aes.c
endif

ifeq ($(CONFIG_LPC43_USB0),y)
ifeq ($(CONFIG_USBDEV),y)
CHIP_CSRCS += lpc43_usb0dev.c
endif
endif

include chip/spifi/src/Make.defs
