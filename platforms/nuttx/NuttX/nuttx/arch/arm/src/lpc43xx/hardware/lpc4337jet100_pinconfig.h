/****************************************************************************
 * arch/arm/src/lpc43xx/hardware/lpc4337jet100_pinconfig.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_LPC43XX_HARDWARE_LPC4337JET100_PINCONFIG_H
#define __ARCH_ARM_SRC_LPC43XX_HARDWARE_LPC4337JET100_PINCONFIG_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include "lpc43_pinconfig.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* NOTES:
 *
 * 1. These settings were taken from the LPC43_10_20_30_50 data sheet and
 *    may not be applicable to any other family members.
 *
 * 2. Settings taken from the data sheet include only function, pin set, and
 *    pin number.  Additional settings must be verified before using these
 *    pin configurations (like pull-ups, open-drain, drive strength, input
 *    buffering, etc.).
 *
 * 3. Alternative pin selections are provided with a numeric suffix like _1,
 *    _2, etc. Drivers, however, will use the pin selection without the
 *    numeric suffix.  Additional definitions are required in the board.h
 *    file to select between the alternative pins.  For example, if CAN1_RD
 *    connects via Pins1[18], then the following definition should appear in
 *    the board.h header file for that board:
 *
 * 4. For ADC pins (PINCONF_ADCNpM), the pin must first be configured
 *    configured as a GPIO input.
 *    Then SCU's ADC function select register can be used to select the ADC.
 *
 *    #define PINCONF_CAN1_RD PINCONF_CAN1_RD_1
 *
 *    The driver will then automatically configure Pins1[18] as the CAN1 RD
 *    pin.
 */

#define PINCONF_ADC0p0           (PINCONF_FUNC0|PINCONF_PINS4|PINCONF_PIN_3)
#define PINCONF_ADC0p1           (PINCONF_FUNC0|PINCONF_PINS4|PINCONF_PIN_1)
#define PINCONF_ADC0p2           (PINCONF_FUNC4|PINCONF_PINSF|PINCONF_PIN_8)
#define PINCONF_ADC0p3           (PINCONF_FUNC0|PINCONF_PINS7|PINCONF_PIN_5)
#define PINCONF_ADC0p4           (PINCONF_FUNC0|PINCONF_PINS7|PINCONF_PIN_4)
#define PINCONF_ADC0p5           (PINCONF_FUNC4|PINCONF_PINSF|PINCONF_PIN_10)
#define PINCONF_ADC0p6           (PINCONF_FUNC0|PINCONF_PINSB|PINCONF_PIN_6)

#define PINCONF_ADC1p0           (PINCONF_FUNC4|PINCONF_PINSC|PINCONF_PIN_3)
#define PINCONF_ADC1p1           (PINCONF_FUNC0|PINCONF_PINSC|PINCONF_PIN_0)
#define PINCONF_ADC1p2           (PINCONF_FUNC4|PINCONF_PINSF|PINCONF_PIN_9)
#define PINCONF_ADC1p3           (PINCONF_FUNC4|PINCONF_PINSF|PINCONF_PIN_6)
#define PINCONF_ADC1p4           (PINCONF_FUNC4|PINCONF_PINSF|PINCONF_PIN_5)
#define PINCONF_ADC1p5           (PINCONF_FUNC4|PINCONF_PINSF|PINCONF_PIN_11)
#define PINCONF_ADC1p6           (PINCONF_FUNC0|PINCONF_PINS7|PINCONF_PIN_7)
#define PINCONF_ADC1p7           (PINCONF_FUNC4|PINCONF_PINSF|PINCONF_PIN_7)

#define PINCONF_ADCTRIG0         (PINCONF_FUNC0|PINCONF_PINSE|PINCONF_PIN_2)
#define PINCONF_ADCTRIG1_1       (PINCONF_FUNC2|PINCONF_PINSE|PINCONF_PIN_3)
#define PINCONF_ADCTRIG1_2       (PINCONF_FUNC3|PINCONF_PINS2|PINCONF_PIN_5)

#define PINCONF_CAN0_RD_1        (PINCONF_FUNC1|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_2)
#define PINCONF_CAN0_RD_2        (PINCONF_FUNC2|PINCONF_INBUFFER|PINCONF_PINS3|PINCONF_PIN_1)
#define PINCONF_CAN0_TD_1        (PINCONF_FUNC1|PINCONF_PINSE|PINCONF_PIN_3)
#define PINCONF_CAN0_TD_2        (PINCONF_FUNC2|PINCONF_PINS3|PINCONF_PIN_2)

#define PINCONF_CAN1_RD_1        (PINCONF_FUNC5|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_18)
#define PINCONF_CAN1_RD_2        (PINCONF_FUNC5|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_1)
#define PINCONF_CAN1_RD_3        (PINCONF_FUNC6|PINCONF_INBUFFER|PINCONF_PINS4|PINCONF_PIN_9)
#define PINCONF_CAN1_TD_1        (PINCONF_FUNC5|PINCONF_PINS1|PINCONF_PIN_17)
#define PINCONF_CAN1_TD_2        (PINCONF_FUNC5|PINCONF_PINSE|PINCONF_PIN_0)
#define PINCONF_CAN1_TD_3        (PINCONF_FUNC6|PINCONF_PINS4|PINCONF_PIN_8)

#define PINCONF_CGU_OUT0         (PINCONF_FUNC6|PINCONF_PINS8|PINCONF_PIN_8)
#define PINCONF_CGU_OUT1_1       (PINCONF_FUNC4|PINCONF_PINS3|PINCONF_PIN_3)
#define PINCONF_CGU_OUT1_2       (PINCONF_FUNC6|PINCONF_PINSA|PINCONF_PIN_0)

#define PINCONF_CLKOUT           (PINCONF_FUNC4|PINCONF_PINS1|PINCONF_PIN_19)

#define PINCONF_CTIN0_1          (PINCONF_FUNC1|PINCONF_PINSD|PINCONF_PIN_13)
#define PINCONF_CTIN0_2          (PINCONF_FUNC3|PINCONF_PINS2|PINCONF_PIN_4)
#define PINCONF_CTIN1_1          (PINCONF_FUNC1|PINCONF_PINSD|PINCONF_PIN_10)
#define PINCONF_CTIN1_2          (PINCONF_FUNC3|PINCONF_PINS2|PINCONF_PIN_3)
#define PINCONF_CTIN2_1          (PINCONF_FUNC1|PINCONF_PINS2|PINCONF_PIN_5)
#define PINCONF_CTIN2_2          (PINCONF_FUNC1|PINCONF_PINS4|PINCONF_PIN_10)
#define PINCONF_CTIN2_3          (PINCONF_FUNC2|PINCONF_PINSF|PINCONF_PIN_8)
#define PINCONF_CTIN3_1          (PINCONF_FUNC1|PINCONF_PINS1|PINCONF_PIN_0)
#define PINCONF_CTIN3_2          (PINCONF_FUNC1|PINCONF_PINS7|PINCONF_PIN_3)
#define PINCONF_CTIN3_3          (PINCONF_FUNC1|PINCONF_PINSE|PINCONF_PIN_10)
#define PINCONF_CTIN4_1          (PINCONF_FUNC1|PINCONF_PINS2|PINCONF_PIN_13)
#define PINCONF_CTIN4_2          (PINCONF_FUNC1|PINCONF_PINS7|PINCONF_PIN_2)
#define PINCONF_CTIN4_3          (PINCONF_FUNC1|PINCONF_PINSE|PINCONF_PIN_9)
#define PINCONF_CTIN5_1          (PINCONF_FUNC1|PINCONF_PINS1|PINCONF_PIN_6)
#define PINCONF_CTIN5_2          (PINCONF_FUNC1|PINCONF_PINS4|PINCONF_PIN_8)
#define PINCONF_CTIN5_3          (PINCONF_FUNC1|PINCONF_PINSD|PINCONF_PIN_7)
#define PINCONF_CTIN5_4          (PINCONF_FUNC5|PINCONF_PINSB|PINCONF_PIN_4)
#define PINCONF_CTIN6_1          (PINCONF_FUNC1|PINCONF_PINS4|PINCONF_PIN_9)
#define PINCONF_CTIN6_2          (PINCONF_FUNC1|PINCONF_PINS6|PINCONF_PIN_4)
#define PINCONF_CTIN6_3          (PINCONF_FUNC1|PINCONF_PINSD|PINCONF_PIN_8)
#define PINCONF_CTIN6_4          (PINCONF_FUNC5|PINCONF_PINS2|PINCONF_PIN_2)
#define PINCONF_CTIN6_5          (PINCONF_FUNC5|PINCONF_PINSB|PINCONF_PIN_6)
#define PINCONF_CTIN7_1          (PINCONF_FUNC5|PINCONF_PINS2|PINCONF_PIN_6)
#define PINCONF_CTIN7_2          (PINCONF_FUNC5|PINCONF_PINSB|PINCONF_PIN_5)

#define PINCONF_CTOUT0_1         (PINCONF_FUNC1|PINCONF_PINS2|PINCONF_PIN_8)
#define PINCONF_CTOUT0_2         (PINCONF_FUNC1|PINCONF_PINS4|PINCONF_PIN_2)
#define PINCONF_CTOUT0_3         (PINCONF_FUNC1|PINCONF_PINSE|PINCONF_PIN_15)
#define PINCONF_CTOUT1_1         (PINCONF_FUNC1|PINCONF_PINS2|PINCONF_PIN_7)
#define PINCONF_CTOUT1_2         (PINCONF_FUNC1|PINCONF_PINS4|PINCONF_PIN_1)
#define PINCONF_CTOUT1_3         (PINCONF_FUNC2|PINCONF_PINSF|PINCONF_PIN_9)
#define PINCONF_CTOUT2_1         (PINCONF_FUNC1|PINCONF_PINS2|PINCONF_PIN_10)
#define PINCONF_CTOUT2_2         (PINCONF_FUNC1|PINCONF_PINS4|PINCONF_PIN_4)
#define PINCONF_CTOUT2_3         (PINCONF_FUNC1|PINCONF_PINSE|PINCONF_PIN_6)
#define PINCONF_CTOUT3_1         (PINCONF_FUNC1|PINCONF_PINS2|PINCONF_PIN_9)
#define PINCONF_CTOUT3_2         (PINCONF_FUNC1|PINCONF_PINS4|PINCONF_PIN_3)
#define PINCONF_CTOUT3_3         (PINCONF_FUNC1|PINCONF_PINSE|PINCONF_PIN_5)
#define PINCONF_CTOUT4_1         (PINCONF_FUNC1|PINCONF_PINS2|PINCONF_PIN_12)
#define PINCONF_CTOUT4_2         (PINCONF_FUNC1|PINCONF_PINS4|PINCONF_PIN_6)
#define PINCONF_CTOUT4_3         (PINCONF_FUNC1|PINCONF_PINSE|PINCONF_PIN_8)
#define PINCONF_CTOUT5_1         (PINCONF_FUNC1|PINCONF_PINS2|PINCONF_PIN_11)
#define PINCONF_CTOUT5_2         (PINCONF_FUNC1|PINCONF_PINS4|PINCONF_PIN_5)
#define PINCONF_CTOUT5_3         (PINCONF_FUNC1|PINCONF_PINSE|PINCONF_PIN_7)
#define PINCONF_CTOUT6_1         (PINCONF_FUNC1|PINCONF_PINS1|PINCONF_PIN_2)
#define PINCONF_CTOUT6_2         (PINCONF_FUNC1|PINCONF_PINS6|PINCONF_PIN_5)
#define PINCONF_CTOUT6_3         (PINCONF_FUNC1|PINCONF_PINSD|PINCONF_PIN_3)
#define PINCONF_CTOUT6_4         (PINCONF_FUNC5|PINCONF_PINSB|PINCONF_PIN_1)
#define PINCONF_CTOUT7_1         (PINCONF_FUNC1|PINCONF_PINS1|PINCONF_PIN_1)
#define PINCONF_CTOUT7_2         (PINCONF_FUNC1|PINCONF_PINS6|PINCONF_PIN_12)
#define PINCONF_CTOUT7_3         (PINCONF_FUNC1|PINCONF_PINSD|PINCONF_PIN_2)
#define PINCONF_CTOUT7_4         (PINCONF_FUNC5|PINCONF_PINSB|PINCONF_PIN_2)
#define PINCONF_CTOUT8_1         (PINCONF_FUNC1|PINCONF_PINS1|PINCONF_PIN_3)
#define PINCONF_CTOUT8_2         (PINCONF_FUNC1|PINCONF_PINS7|PINCONF_PIN_7)
#define PINCONF_CTOUT8_3         (PINCONF_FUNC1|PINCONF_PINSD|PINCONF_PIN_4)
#define PINCONF_CTOUT8_4         (PINCONF_FUNC5|PINCONF_PINSB|PINCONF_PIN_3)
#define PINCONF_CTOUT8_5         (PINCONF_FUNC6|PINCONF_PINSD|PINCONF_PIN_15)
#define PINCONF_CTOUT9_1         (PINCONF_FUNC1|PINCONF_PINS1|PINCONF_PIN_4)
#define PINCONF_CTOUT9_2         (PINCONF_FUNC1|PINCONF_PINSA|PINCONF_PIN_4)
#define PINCONF_CTOUT9_3         (PINCONF_FUNC1|PINCONF_PINSD|PINCONF_PIN_5)
#define PINCONF_CTOUT10_1        (PINCONF_FUNC1|PINCONF_PINS1|PINCONF_PIN_5)
#define PINCONF_CTOUT10_2        (PINCONF_FUNC1|PINCONF_PINSB|PINCONF_PIN_0)
#define PINCONF_CTOUT10_3        (PINCONF_FUNC1|PINCONF_PINSD|PINCONF_PIN_6)
#define PINCONF_CTOUT10_4        (PINCONF_FUNC6|PINCONF_PINSD|PINCONF_PIN_12)
#define PINCONF_CTOUT11_1        (PINCONF_FUNC1|PINCONF_PINS7|PINCONF_PIN_6)
#define PINCONF_CTOUT11_2        (PINCONF_FUNC1|PINCONF_PINSE|PINCONF_PIN_12)
#define PINCONF_CTOUT11_3        (PINCONF_FUNC2|PINCONF_PINS1|PINCONF_PIN_9)
#define PINCONF_CTOUT11_4        (PINCONF_FUNC6|PINCONF_PINSD|PINCONF_PIN_14)
#define PINCONF_CTOUT12_1        (PINCONF_FUNC1|PINCONF_PINS7|PINCONF_PIN_5)
#define PINCONF_CTOUT12_2        (PINCONF_FUNC1|PINCONF_PINSE|PINCONF_PIN_11)
#define PINCONF_CTOUT12_3        (PINCONF_FUNC2|PINCONF_PINS1|PINCONF_PIN_8)
#define PINCONF_CTOUT12_4        (PINCONF_FUNC6|PINCONF_PINSD|PINCONF_PIN_16)
#define PINCONF_CTOUT13_1        (PINCONF_FUNC1|PINCONF_PINS7|PINCONF_PIN_4)
#define PINCONF_CTOUT13_2        (PINCONF_FUNC1|PINCONF_PINSD|PINCONF_PIN_9)
#define PINCONF_CTOUT13_3        (PINCONF_FUNC2|PINCONF_PINS1|PINCONF_PIN_7)
#define PINCONF_CTOUT13_4        (PINCONF_FUNC6|PINCONF_PINSD|PINCONF_PIN_13)
#define PINCONF_CTOUT14_1        (PINCONF_FUNC1|PINCONF_PINS7|PINCONF_PIN_0)
#define PINCONF_CTOUT14_2        (PINCONF_FUNC1|PINCONF_PINSE|PINCONF_PIN_13)
#define PINCONF_CTOUT14_3        (PINCONF_FUNC2|PINCONF_PINS1|PINCONF_PIN_10)
#define PINCONF_CTOUT14_4        (PINCONF_FUNC6|PINCONF_PINSD|PINCONF_PIN_11)
#define PINCONF_CTOUT15_1        (PINCONF_FUNC1|PINCONF_PINS7|PINCONF_PIN_1)
#define PINCONF_CTOUT15_2        (PINCONF_FUNC1|PINCONF_PINSD|PINCONF_PIN_0)
#define PINCONF_CTOUT15_3        (PINCONF_FUNC2|PINCONF_PINS1|PINCONF_PIN_11)

#define PINCONF_EMC_A0           (PINCONF_FUNC3|PINCONF_PINS2|PINCONF_PIN_9)
#define PINCONF_EMC_A1           (PINCONF_FUNC3|PINCONF_PINS2|PINCONF_PIN_10)
#define PINCONF_EMC_A2           (PINCONF_FUNC3|PINCONF_PINS2|PINCONF_PIN_11)
#define PINCONF_EMC_A3           (PINCONF_FUNC3|PINCONF_PINS2|PINCONF_PIN_12)
#define PINCONF_EMC_A4           (PINCONF_FUNC3|PINCONF_PINS2|PINCONF_PIN_13)
#define PINCONF_EMC_A5           (PINCONF_FUNC2|PINCONF_PINS1|PINCONF_PIN_0)
#define PINCONF_EMC_A6           (PINCONF_FUNC2|PINCONF_PINS1|PINCONF_PIN_1)
#define PINCONF_EMC_A7           (PINCONF_FUNC2|PINCONF_PINS1|PINCONF_PIN_2)
#define PINCONF_EMC_A8           (PINCONF_FUNC3|PINCONF_PINS2|PINCONF_PIN_8)
#define PINCONF_EMC_A9           (PINCONF_FUNC3|PINCONF_PINS2|PINCONF_PIN_7)
#define PINCONF_EMC_A10          (PINCONF_FUNC2|PINCONF_PINS2|PINCONF_PIN_6)
#define PINCONF_EMC_A11          (PINCONF_FUNC2|PINCONF_PINS2|PINCONF_PIN_2)
#define PINCONF_EMC_A12          (PINCONF_FUNC2|PINCONF_PINS2|PINCONF_PIN_1)
#define PINCONF_EMC_A13          (PINCONF_FUNC2|PINCONF_PINS2|PINCONF_PIN_0)
#define PINCONF_EMC_A14          (PINCONF_FUNC1|PINCONF_PINS6|PINCONF_PIN_8)
#define PINCONF_EMC_A15          (PINCONF_FUNC1|PINCONF_PINS6|PINCONF_PIN_7)
#define PINCONF_EMC_A16          (PINCONF_FUNC2|PINCONF_PINSD|PINCONF_PIN_16)
#define PINCONF_EMC_A17          (PINCONF_FUNC2|PINCONF_PINSD|PINCONF_PIN_15)
#define PINCONF_EMC_A18          (PINCONF_FUNC3|PINCONF_PINSE|PINCONF_PIN_0)
#define PINCONF_EMC_A19          (PINCONF_FUNC3|PINCONF_PINSE|PINCONF_PIN_1)
#define PINCONF_EMC_A20          (PINCONF_FUNC3|PINCONF_PINSE|PINCONF_PIN_2)
#define PINCONF_EMC_A21          (PINCONF_FUNC3|PINCONF_PINSE|PINCONF_PIN_3)
#define PINCONF_EMC_A22          (PINCONF_FUNC3|PINCONF_PINSE|PINCONF_PIN_4)
#define PINCONF_EMC_A23          (PINCONF_FUNC3|PINCONF_PINSA|PINCONF_PIN_4)
#define PINCONF_EMC_BLS0         (PINCONF_FUNC3|PINCONF_PINS1|PINCONF_PIN_4)
#define PINCONF_EMC_BLS1         (PINCONF_FUNC1|PINCONF_PINS6|PINCONF_PIN_6)
#define PINCONF_EMC_BLS2         (PINCONF_FUNC2|PINCONF_PINSD|PINCONF_PIN_13)
#define PINCONF_EMC_BLS3         (PINCONF_FUNC2|PINCONF_PINSD|PINCONF_PIN_10)
#define PINCONF_EMC_CAS          (PINCONF_FUNC3|PINCONF_PINS6|PINCONF_PIN_4)
#define PINCONF_EMC_CKEOUT0      (PINCONF_FUNC3|PINCONF_PINS6|PINCONF_PIN_11)
#define PINCONF_EMC_CKEOUT1      (PINCONF_FUNC1|PINCONF_PINS6|PINCONF_PIN_2)
#define PINCONF_EMC_CKEOUT2      (PINCONF_FUNC2|PINCONF_PINSD|PINCONF_PIN_1)
#define PINCONF_EMC_CKEOUT3      (PINCONF_FUNC3|PINCONF_PINSE|PINCONF_PIN_15)
#define PINCONF_EMC_CS0          (PINCONF_FUNC3|PINCONF_PINS1|PINCONF_PIN_5)
#define PINCONF_EMC_CS1          (PINCONF_FUNC3|PINCONF_PINS6|PINCONF_PIN_3)
#define PINCONF_EMC_CS2          (PINCONF_FUNC2|PINCONF_PINSD|PINCONF_PIN_12)
#define PINCONF_EMC_CS3          (PINCONF_FUNC2|PINCONF_PINSD|PINCONF_PIN_11)
#define PINCONF_EMC_D0           (PINCONF_FUNC3|PINCONF_PINS1|PINCONF_PIN_7)
#define PINCONF_EMC_D1           (PINCONF_FUNC3|PINCONF_PINS1|PINCONF_PIN_8)
#define PINCONF_EMC_D2           (PINCONF_FUNC3|PINCONF_PINS1|PINCONF_PIN_9)
#define PINCONF_EMC_D3           (PINCONF_FUNC3|PINCONF_PINS1|PINCONF_PIN_10)
#define PINCONF_EMC_D4           (PINCONF_FUNC3|PINCONF_PINS1|PINCONF_PIN_11)
#define PINCONF_EMC_D5           (PINCONF_FUNC3|PINCONF_PINS1|PINCONF_PIN_12)
#define PINCONF_EMC_D6           (PINCONF_FUNC3|PINCONF_PINS1|PINCONF_PIN_13)
#define PINCONF_EMC_D7           (PINCONF_FUNC3|PINCONF_PINS1|PINCONF_PIN_14)
#define PINCONF_EMC_D8           (PINCONF_FUNC2|PINCONF_PINS5|PINCONF_PIN_4)
#define PINCONF_EMC_D9           (PINCONF_FUNC2|PINCONF_PINS5|PINCONF_PIN_5)
#define PINCONF_EMC_D10          (PINCONF_FUNC2|PINCONF_PINS5|PINCONF_PIN_6)
#define PINCONF_EMC_D11          (PINCONF_FUNC2|PINCONF_PINS5|PINCONF_PIN_7)
#define PINCONF_EMC_D12          (PINCONF_FUNC2|PINCONF_PINS5|PINCONF_PIN_0)
#define PINCONF_EMC_D13          (PINCONF_FUNC2|PINCONF_PINS5|PINCONF_PIN_1)
#define PINCONF_EMC_D14          (PINCONF_FUNC2|PINCONF_PINS5|PINCONF_PIN_2)
#define PINCONF_EMC_D15          (PINCONF_FUNC2|PINCONF_PINS5|PINCONF_PIN_3)
#define PINCONF_EMC_D16          (PINCONF_FUNC2|PINCONF_PINSD|PINCONF_PIN_2)
#define PINCONF_EMC_D17          (PINCONF_FUNC2|PINCONF_PINSD|PINCONF_PIN_3)
#define PINCONF_EMC_D18          (PINCONF_FUNC2|PINCONF_PINSD|PINCONF_PIN_4)
#define PINCONF_EMC_D19          (PINCONF_FUNC2|PINCONF_PINSD|PINCONF_PIN_5)
#define PINCONF_EMC_D20          (PINCONF_FUNC2|PINCONF_PINSD|PINCONF_PIN_6)
#define PINCONF_EMC_D21          (PINCONF_FUNC2|PINCONF_PINSD|PINCONF_PIN_7)
#define PINCONF_EMC_D22          (PINCONF_FUNC2|PINCONF_PINSD|PINCONF_PIN_8)
#define PINCONF_EMC_D23          (PINCONF_FUNC2|PINCONF_PINSD|PINCONF_PIN_9)
#define PINCONF_EMC_D24          (PINCONF_FUNC3|PINCONF_PINSE|PINCONF_PIN_5)
#define PINCONF_EMC_D25          (PINCONF_FUNC3|PINCONF_PINSE|PINCONF_PIN_6)
#define PINCONF_EMC_D26          (PINCONF_FUNC3|PINCONF_PINSE|PINCONF_PIN_7)
#define PINCONF_EMC_D27          (PINCONF_FUNC3|PINCONF_PINSE|PINCONF_PIN_8)
#define PINCONF_EMC_D28          (PINCONF_FUNC3|PINCONF_PINSE|PINCONF_PIN_9)
#define PINCONF_EMC_D29          (PINCONF_FUNC3|PINCONF_PINSE|PINCONF_PIN_10)
#define PINCONF_EMC_D30          (PINCONF_FUNC3|PINCONF_PINSE|PINCONF_PIN_11)
#define PINCONF_EMC_D31          (PINCONF_FUNC3|PINCONF_PINSE|PINCONF_PIN_12)
#define PINCONF_EMC_DQMOUT0      (PINCONF_FUNC3|PINCONF_PINS6|PINCONF_PIN_12)
#define PINCONF_EMC_DQMOUT1      (PINCONF_FUNC3|PINCONF_PINS6|PINCONF_PIN_10)
#define PINCONF_EMC_DQMOUT2      (PINCONF_FUNC2|PINCONF_PINSD|PINCONF_PIN_0)
#define PINCONF_EMC_DQMOUT3      (PINCONF_FUNC3|PINCONF_PINSE|PINCONF_PIN_13)
#define PINCONF_EMC_DYCS0        (PINCONF_FUNC3|PINCONF_PINS6|PINCONF_PIN_9)
#define PINCONF_EMC_DYCS1        (PINCONF_FUNC1|PINCONF_PINS6|PINCONF_PIN_1)
#define PINCONF_EMC_DYCS2        (PINCONF_FUNC2|PINCONF_PINSD|PINCONF_PIN_14)
#define PINCONF_EMC_DYCS3        (PINCONF_FUNC3|PINCONF_PINSE|PINCONF_PIN_14)
#define PINCONF_EMC_OE           (PINCONF_FUNC3|PINCONF_PINS1|PINCONF_PIN_3)
#define PINCONF_EMC_RAS          (PINCONF_FUNC3|PINCONF_PINS6|PINCONF_PIN_5)
#define PINCONF_EMC_WE           (PINCONF_FUNC3|PINCONF_PINS1|PINCONF_PIN_6)

#define PINCONF_ENET_COL_1       (PINCONF_FUNC2|PINCONF_PINS0|PINCONF_PIN_1)
#define PINCONF_ENET_COL_2       (PINCONF_FUNC5|PINCONF_PINS9|PINCONF_PIN_6)
#define PINCONF_ENET_COL_3       (PINCONF_FUNC7|PINCONF_PINS4|PINCONF_PIN_1)
#define PINCONF_ENET_CRS_1       (PINCONF_FUNC3|PINCONF_PINS1|PINCONF_PIN_16)
#define PINCONF_ENET_CRS_2       (PINCONF_FUNC5|PINCONF_PINS9|PINCONF_PIN_0)
#define PINCONF_ENET_MDC_1       (PINCONF_FUNC3|PINCONF_PINSC|PINCONF_PIN_1)
#define PINCONF_ENET_MDC_2       (PINCONF_FUNC6|PINCONF_PINS7|PINCONF_PIN_7)
#define PINCONF_ENET_MDC_3       (PINCONF_FUNC7|PINCONF_PINS2|PINCONF_PIN_0)
#define PINCONF_ENET_MDIO        (PINCONF_FUNC3|PINCONF_PINS1|PINCONF_PIN_17|PINCONF_FLOAT|PINCONF_INBUFFER|PINCONF_GLITCH)
#define PINCONF_ENET_REF_CLK     (PINCONF_FUNC0|PINCONF_PINS1|PINCONF_PIN_19|PINCONF_SLEW_FAST|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_FLOAT)
#define PINCONF_ENET_RXD0        (PINCONF_FUNC3|PINCONF_PINS1|PINCONF_PIN_15|PINCONF_FLOAT|PINCONF_INBUFFER|PINCONF_GLITCH)
#define PINCONF_ENET_RXD1        (PINCONF_FUNC2|PINCONF_PINS0|PINCONF_PIN_0|PINCONF_FLOAT|PINCONF_INBUFFER|PINCONF_GLITCH)
#define PINCONF_ENET_RXD2_1      (PINCONF_FUNC3|PINCONF_PINSC|PINCONF_PIN_6)
#define PINCONF_ENET_RXD2_2      (PINCONF_FUNC5|PINCONF_PINS9|PINCONF_PIN_3)
#define PINCONF_ENET_RXD3_1      (PINCONF_FUNC3|PINCONF_PINSC|PINCONF_PIN_7)
#define PINCONF_ENET_RXD3_2      (PINCONF_FUNC5|PINCONF_PINS9|PINCONF_PIN_2)
#define PINCONF_ENET_RX_CLK      (PINCONF_FUNC3|PINCONF_PINSC|PINCONF_PIN_0)
#define PINCONF_ENET_RX_DV_1     (PINCONF_FUNC3|PINCONF_PINSC|PINCONF_PIN_8)
#define PINCONF_ENET_RX_DV_2     (PINCONF_FUNC7|PINCONF_PINS1|PINCONF_PIN_16|PINCONF_FLOAT|PINCONF_INBUFFER|PINCONF_GLITCH)
#define PINCONF_ENET_RX_ER_1     (PINCONF_FUNC3|PINCONF_PINSC|PINCONF_PIN_9)
#define PINCONF_ENET_RX_ER_2     (PINCONF_FUNC5|PINCONF_PINS9|PINCONF_PIN_1)
#define PINCONF_ENET_TXD0        (PINCONF_FUNC3|PINCONF_PINS1|PINCONF_PIN_18|PINCONF_FLOAT|PINCONF_GLITCH)
#define PINCONF_ENET_TXD1        (PINCONF_FUNC3|PINCONF_PINS1|PINCONF_PIN_20|PINCONF_FLOAT|PINCONF_GLITCH)
#define PINCONF_ENET_TXD2_1      (PINCONF_FUNC3|PINCONF_PINSC|PINCONF_PIN_2)
#define PINCONF_ENET_TXD2_2      (PINCONF_FUNC5|PINCONF_PINS9|PINCONF_PIN_4)
#define PINCONF_ENET_TXD3_1      (PINCONF_FUNC3|PINCONF_PINSC|PINCONF_PIN_3)
#define PINCONF_ENET_TXD3_2      (PINCONF_FUNC5|PINCONF_PINS9|PINCONF_PIN_5)
#define PINCONF_ENET_TX_CLK      (PINCONF_FUNC0|PINCONF_PINS1|PINCONF_PIN_19)
#define PINCONF_ENET_TX_EN_1     (PINCONF_FUNC6|PINCONF_PINS0|PINCONF_PIN_1|PINCONF_FLOAT|PINCONF_GLITCH)
#define PINCONF_ENET_TX_EN_2     (PINCONF_FUNC3|PINCONF_PINSC|PINCONF_PIN_4)
#define PINCONF_ENET_TX_ER_1     (PINCONF_FUNC3|PINCONF_PINSC|PINCONF_PIN_5)
#define PINCONF_ENET_TX_ER_2     (PINCONF_FUNC6|PINCONF_PINSC|PINCONF_PIN_14)

#define PINCONF_GPIO0p0          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS0|PINCONF_PIN_0)
#define PINCONF_GPIO0p1          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS0|PINCONF_PIN_1)
#define PINCONF_GPIO0p2          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_15)
#define PINCONF_GPIO0p3          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_16)
#define PINCONF_GPIO0p4          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_0)
#define PINCONF_GPIO0p5          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS6|PINCONF_PIN_6)
#define PINCONF_GPIO0p6          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS3|PINCONF_PIN_6)
#define PINCONF_GPIO0p7          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_7)
#define PINCONF_GPIO0p8          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_1)
#define PINCONF_GPIO0p9          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_2)
#define PINCONF_GPIO0p10         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_3)
#define PINCONF_GPIO0p11         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_4)
#define PINCONF_GPIO0p12         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_17)
#define PINCONF_GPIO0p13         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_18)
#define PINCONF_GPIO0p14         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_10)
#define PINCONF_GPIO0p15         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_20)
#define PINCONF_GPIO1p0          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_7)
#define PINCONF_GPIO1p1          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_8)
#define PINCONF_GPIO1p2          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_9)
#define PINCONF_GPIO1p3          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_10)
#define PINCONF_GPIO1p4          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_11)
#define PINCONF_GPIO1p5          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_12)
#define PINCONF_GPIO1p6          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_13)
#define PINCONF_GPIO1p7          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_14)
#define PINCONF_GPIO1p8          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_5)
#define PINCONF_GPIO1p9          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_6)
#define PINCONF_GPIO1p10         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_9)
#define PINCONF_GPIO1p11         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_11)
#define PINCONF_GPIO1p12         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_12)
#define PINCONF_GPIO1p13         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_13)
#define PINCONF_GPIO1p14         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS3|PINCONF_PIN_4)
#define PINCONF_GPIO1p15         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS3|PINCONF_PIN_5)
#define PINCONF_GPIO2p0          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS4|PINCONF_PIN_0)
#define PINCONF_GPIO2p1          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS4|PINCONF_PIN_1)
#define PINCONF_GPIO2p2          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS4|PINCONF_PIN_2)
#define PINCONF_GPIO2p3          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS4|PINCONF_PIN_3)
#define PINCONF_GPIO2p4          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS4|PINCONF_PIN_4)
#define PINCONF_GPIO2p5          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS4|PINCONF_PIN_5)
#define PINCONF_GPIO2p6          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS4|PINCONF_PIN_6)
#define PINCONF_GPIO2p7          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS5|PINCONF_PIN_7)
#define PINCONF_GPIO2p8          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS6|PINCONF_PIN_12)
#define PINCONF_GPIO2p9          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS5|PINCONF_PIN_0)
#define PINCONF_GPIO2p10         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS5|PINCONF_PIN_1)
#define PINCONF_GPIO2p11         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS5|PINCONF_PIN_2)
#define PINCONF_GPIO2p12         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS5|PINCONF_PIN_3)
#define PINCONF_GPIO2p13         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS5|PINCONF_PIN_4)
#define PINCONF_GPIO2p14         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS5|PINCONF_PIN_5)
#define PINCONF_GPIO2p15         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS5|PINCONF_PIN_6)
#define PINCONF_GPIO3p0          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS6|PINCONF_PIN_1)
#define PINCONF_GPIO3p1          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS6|PINCONF_PIN_2)
#define PINCONF_GPIO3p2          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS6|PINCONF_PIN_3)
#define PINCONF_GPIO3p3          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS6|PINCONF_PIN_4)
#define PINCONF_GPIO3p4          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS6|PINCONF_PIN_5)
#define PINCONF_GPIO3p5          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS6|PINCONF_PIN_9)
#define PINCONF_GPIO3p6          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS6|PINCONF_PIN_10)
#define PINCONF_GPIO3p7          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS6|PINCONF_PIN_11)
#define PINCONF_GPIO3p8          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS7|PINCONF_PIN_0)
#define PINCONF_GPIO3p9          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS7|PINCONF_PIN_1)
#define PINCONF_GPIO3p10         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS7|PINCONF_PIN_2)
#define PINCONF_GPIO3p11         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS7|PINCONF_PIN_3)
#define PINCONF_GPIO3p12         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS7|PINCONF_PIN_4)
#define PINCONF_GPIO3p13         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS7|PINCONF_PIN_5)
#define PINCONF_GPIO3p14         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS7|PINCONF_PIN_6)
#define PINCONF_GPIO3p15         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS7|PINCONF_PIN_7)
#define PINCONF_GPIO4p0          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS8|PINCONF_PIN_0)
#define PINCONF_GPIO4p1          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS8|PINCONF_PIN_1)
#define PINCONF_GPIO4p2          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS8|PINCONF_PIN_2)
#define PINCONF_GPIO4p3          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS8|PINCONF_PIN_3)
#define PINCONF_GPIO4p4          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS8|PINCONF_PIN_4)
#define PINCONF_GPIO4p5          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS8|PINCONF_PIN_5)
#define PINCONF_GPIO4p6          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS8|PINCONF_PIN_6)
#define PINCONF_GPIO4p7          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS8|PINCONF_PIN_7)
#define PINCONF_GPIO4p8          (PINCONF_FUNC0|PINCONF_PINSA|PINCONF_PIN_1)
#define PINCONF_GPIO4p9          (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINSA|PINCONF_PIN_2)
#define PINCONF_GPIO4p10         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINSA|PINCONF_PIN_3)
#define PINCONF_GPIO4p11         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS9|PINCONF_PIN_6)
#define PINCONF_GPIO4p12         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS9|PINCONF_PIN_0)
#define PINCONF_GPIO4p13         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS9|PINCONF_PIN_1)
#define PINCONF_GPIO4p14         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS9|PINCONF_PIN_2)
#define PINCONF_GPIO4p15         (PINCONF_FUNC0|PINCONF_INBUFFER|PINCONF_PINS9|PINCONF_PIN_3)
#define PINCONF_GPIO5p0          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_0)
#define PINCONF_GPIO5p1          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_1)
#define PINCONF_GPIO5p2          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_2)
#define PINCONF_GPIO5p3          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_3)
#define PINCONF_GPIO5p4          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_4)
#define PINCONF_GPIO5p5          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_5)
#define PINCONF_GPIO5p6          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_6)
#define PINCONF_GPIO5p7          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_8)
#define PINCONF_GPIO5p8          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINS3|PINCONF_PIN_1)
#define PINCONF_GPIO5p9          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINS3|PINCONF_PIN_2)
#define PINCONF_GPIO5p10         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINS3|PINCONF_PIN_7)
#define PINCONF_GPIO5p11         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINS3|PINCONF_PIN_8)
#define PINCONF_GPIO5p12         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINS4|PINCONF_PIN_8)
#define PINCONF_GPIO5p13         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINS4|PINCONF_PIN_9)
#define PINCONF_GPIO5p14         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINS4|PINCONF_PIN_10)
#define PINCONF_GPIO5p15         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINS6|PINCONF_PIN_7)
#define PINCONF_GPIO5p16         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINS6|PINCONF_PIN_8)
#define PINCONF_GPIO5p17         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINS9|PINCONF_PIN_4)
#define PINCONF_GPIO5p18         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINS9|PINCONF_PIN_5)
#define PINCONF_GPIO5p19         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSA|PINCONF_PIN_4)
#define PINCONF_GPIO5p20         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSB|PINCONF_PIN_0)
#define PINCONF_GPIO5p21         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSB|PINCONF_PIN_1)
#define PINCONF_GPIO5p22         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSB|PINCONF_PIN_2)
#define PINCONF_GPIO5p23         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSB|PINCONF_PIN_3)
#define PINCONF_GPIO5p24         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSB|PINCONF_PIN_4)
#define PINCONF_GPIO5p25         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSB|PINCONF_PIN_5)
#define PINCONF_GPIO5p26         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSB|PINCONF_PIN_6)
#define PINCONF_GPIO6p0          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_1)
#define PINCONF_GPIO6p1          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_2)
#define PINCONF_GPIO6p2          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_3)
#define PINCONF_GPIO6p3          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_4)
#define PINCONF_GPIO6p4          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_5)
#define PINCONF_GPIO6p5          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_6)
#define PINCONF_GPIO6p6          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_7)
#define PINCONF_GPIO6p7          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_8)
#define PINCONF_GPIO6p8          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_9)
#define PINCONF_GPIO6p9          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_10)
#define PINCONF_GPIO6p10         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_11)
#define PINCONF_GPIO6p11         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_12)
#define PINCONF_GPIO6p12         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_13)
#define PINCONF_GPIO6p13         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_14)
#define PINCONF_GPIO6p14         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSD|PINCONF_PIN_0)
#define PINCONF_GPIO6p15         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSD|PINCONF_PIN_1)
#define PINCONF_GPIO6p16         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSD|PINCONF_PIN_2)
#define PINCONF_GPIO6p17         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSD|PINCONF_PIN_3)
#define PINCONF_GPIO6p18         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSD|PINCONF_PIN_4)
#define PINCONF_GPIO6p19         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSD|PINCONF_PIN_5)
#define PINCONF_GPIO6p20         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSD|PINCONF_PIN_6)
#define PINCONF_GPIO6p21         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSD|PINCONF_PIN_7)
#define PINCONF_GPIO6p22         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSD|PINCONF_PIN_8)
#define PINCONF_GPIO6p23         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSD|PINCONF_PIN_9)
#define PINCONF_GPIO6p24         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSD|PINCONF_PIN_10)
#define PINCONF_GPIO6p25         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSD|PINCONF_PIN_11)
#define PINCONF_GPIO6p26         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSD|PINCONF_PIN_12)
#define PINCONF_GPIO6p27         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSD|PINCONF_PIN_13)
#define PINCONF_GPIO6p28         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSD|PINCONF_PIN_14)
#define PINCONF_GPIO6p29         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSD|PINCONF_PIN_15)
#define PINCONF_GPIO6p30         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSD|PINCONF_PIN_16)
#define PINCONF_GPIO7p0          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_0)
#define PINCONF_GPIO7p1          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_1)
#define PINCONF_GPIO7p2          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_2)
#define PINCONF_GPIO7p3          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_3)
#define PINCONF_GPIO7p4          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_4)
#define PINCONF_GPIO7p5          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_5)
#define PINCONF_GPIO7p6          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_6)
#define PINCONF_GPIO7p7          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_7)
#define PINCONF_GPIO7p8          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_8)
#define PINCONF_GPIO7p9          (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_9)
#define PINCONF_GPIO7p10         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_10)
#define PINCONF_GPIO7p11         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_11)
#define PINCONF_GPIO7p12         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_12)
#define PINCONF_GPIO7p13         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_13)
#define PINCONF_GPIO7p14         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_14)
#define PINCONF_GPIO7p15         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_15)
#define PINCONF_GPIO7p16         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSF|PINCONF_PIN_1)
#define PINCONF_GPIO7p17         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSF|PINCONF_PIN_2)
#define PINCONF_GPIO7p18         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSF|PINCONF_PIN_3)
#define PINCONF_GPIO7p19         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSF|PINCONF_PIN_5)
#define PINCONF_GPIO7p20         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSF|PINCONF_PIN_6)
#define PINCONF_GPIO7p21         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSF|PINCONF_PIN_7)
#define PINCONF_GPIO7p22         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSF|PINCONF_PIN_8)
#define PINCONF_GPIO7p23         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSF|PINCONF_PIN_9)
#define PINCONF_GPIO7p24         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSF|PINCONF_PIN_10)
#define PINCONF_GPIO7p25         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_PINSF|PINCONF_PIN_11)

#define PINCONF_GP_CLKIN_1       (PINCONF_FUNC1|PINCONF_PINS4|PINCONF_PIN_7)
#define PINCONF_GP_CLKIN_2       (PINCONF_FUNC1|PINCONF_PINSF|PINCONF_PIN_0)
#define PINCONF_GP_CLKIN_3       (PINCONF_FUNC1|PINCONF_PINSF|PINCONF_PIN_4)

#define PINCONF_I2C1_SCL_1       (PINCONF_FUNC1|PINCONF_PINS2|PINCONF_PIN_4)
#define PINCONF_I2C1_SCL_2       (PINCONF_FUNC2|PINCONF_PINSE|PINCONF_PIN_15)
#define PINCONF_I2C1_SDA_1       (PINCONF_FUNC1|PINCONF_PINS2|PINCONF_PIN_3)
#define PINCONF_I2C1_SDA_2       (PINCONF_FUNC2|PINCONF_PINSE|PINCONF_PIN_13)

#define PINCONF_I2S0_RX_MCLK_1   (PINCONF_FUNC1|PINCONF_PINS3|PINCONF_PIN_0)
#define PINCONF_I2S0_RX_MCLK_2   (PINCONF_FUNC1|PINCONF_PINS6|PINCONF_PIN_0)
#define PINCONF_I2S0_RX_MCLK_3   (PINCONF_FUNC6|PINCONF_PINS1|PINCONF_PIN_19)
#define PINCONF_I2S0_RX_SCK_1    (PINCONF_FUNC0|PINCONF_PINS3|PINCONF_PIN_0)
#define PINCONF_I2S0_RX_SCK_2    (PINCONF_FUNC4|PINCONF_PINS6|PINCONF_PIN_0)
#define PINCONF_I2S0_RX_SCK_3    (PINCONF_FUNC7|PINCONF_PINSF|PINCONF_PIN_4)
#define PINCONF_I2S0_RX_SDA_1    (PINCONF_FUNC1|PINCONF_PINS3|PINCONF_PIN_2)
#define PINCONF_I2S0_RX_SDA_2    (PINCONF_FUNC3|PINCONF_PINS6|PINCONF_PIN_2)
#define PINCONF_I2S0_RX_WS_1     (PINCONF_FUNC1|PINCONF_PINS3|PINCONF_PIN_1)
#define PINCONF_I2S0_RX_WS_2     (PINCONF_FUNC3|PINCONF_PINS6|PINCONF_PIN_1)
#define PINCONF_I2S0_TXWS        (PINCONF_FUNC6|PINCONF_PINS0|PINCONF_PIN_0)
#define PINCONF_I2S0_TX_MCLK_1   (PINCONF_FUNC3|PINCONF_PINS3|PINCONF_PIN_0)
#define PINCONF_I2S0_TX_MCLK_2   (PINCONF_FUNC6|PINCONF_PINS3|PINCONF_PIN_3)
#define PINCONF_I2S0_TX_MCLK_3   (PINCONF_FUNC6|PINCONF_PINSF|PINCONF_PIN_4)
#define PINCONF_I2S0_TX_SCK_1    (PINCONF_FUNC2|PINCONF_PINS3|PINCONF_PIN_0)
#define PINCONF_I2S0_TX_SCK_2    (PINCONF_FUNC7|PINCONF_PINS4|PINCONF_PIN_7)
#define PINCONF_I2S0_TX_SDA_1    (PINCONF_FUNC0|PINCONF_PINS3|PINCONF_PIN_2)
#define PINCONF_I2S0_TX_SDA_2    (PINCONF_FUNC2|PINCONF_PINS7|PINCONF_PIN_2)
#define PINCONF_I2S0_TX_SDA_3    (PINCONF_FUNC4|PINCONF_PINS9|PINCONF_PIN_2)
#define PINCONF_I2S0_TX_SDA_4    (PINCONF_FUNC5|PINCONF_PINS3|PINCONF_PIN_5)
#define PINCONF_I2S0_TX_SDA_5    (PINCONF_FUNC6|PINCONF_PINSC|PINCONF_PIN_12)
#define PINCONF_I2S0_TX_WS_1     (PINCONF_FUNC0|PINCONF_PINS3|PINCONF_PIN_1)
#define PINCONF_I2S0_TX_WS_2     (PINCONF_FUNC2|PINCONF_PINS7|PINCONF_PIN_1)
#define PINCONF_I2S0_TX_WS_3     (PINCONF_FUNC4|PINCONF_PINS9|PINCONF_PIN_1)
#define PINCONF_I2S0_TX_WS_4     (PINCONF_FUNC5|PINCONF_PINS3|PINCONF_PIN_4)
#define PINCONF_I2S0_TX_WS_5     (PINCONF_FUNC6|PINCONF_PINSC|PINCONF_PIN_13)

#define PINCONF_I2S1_RX_MCLK     (PINCONF_FUNC5|PINCONF_PINSA|PINCONF_PIN_0)
#define PINCONF_I2S1_RX_SDA      (PINCONF_FUNC6|PINCONF_PINS3|PINCONF_PIN_4)
#define PINCONF_I2S1_RX_WS       (PINCONF_FUNC6|PINCONF_PINS3|PINCONF_PIN_5)
#define PINCONF_I2S1_TXSDA       (PINCONF_FUNC7|PINCONF_PINS0|PINCONF_PIN_1)
#define PINCONF_I2S1_TXWS        (PINCONF_FUNC7|PINCONF_PINS0|PINCONF_PIN_0)
#define PINCONF_I2S1_TX_MCLK_1   (PINCONF_FUNC7|PINCONF_PINS8|PINCONF_PIN_8)
#define PINCONF_I2S1_TX_MCLK_2   (PINCONF_FUNC7|PINCONF_PINSF|PINCONF_PIN_0)
#define PINCONF_I2S1_TX_SCK_1    (PINCONF_FUNC6|PINCONF_PINS4|PINCONF_PIN_7)
#define PINCONF_I2S1_TX_SCK_2    (PINCONF_FUNC7|PINCONF_PINS1|PINCONF_PIN_19)
#define PINCONF_I2S1_TX_SCK_3    (PINCONF_FUNC7|PINCONF_PINS3|PINCONF_PIN_3)
#define PINCONF_I2S1_TX_SDA      (PINCONF_FUNC7|PINCONF_PINSF|PINCONF_PIN_6)
#define PINCONF_I2S1_TX_WS       (PINCONF_FUNC7|PINCONF_PINSF|PINCONF_PIN_7)

#define PINCONF_LCD_DCLK_1       (PINCONF_FUNC0|PINCONF_PINS4|PINCONF_PIN_7)
#define PINCONF_LCD_DCLK_2       (PINCONF_FUNC4|PINCONF_PINSC|PINCONF_PIN_0)
#define PINCONF_LCD_ENAB         (PINCONF_FUNC2|PINCONF_PINS4|PINCONF_PIN_6)
#define PINCONF_LCD_FP           (PINCONF_FUNC2|PINCONF_PINS4|PINCONF_PIN_5)
#define PINCONF_LCD_LCDM         (PINCONF_FUNC2|PINCONF_PINS4|PINCONF_PIN_6)
#define PINCONF_LCD_LE           (PINCONF_FUNC3|PINCONF_PINS7|PINCONF_PIN_0)
#define PINCONF_LCD_LP_1         (PINCONF_FUNC3|PINCONF_PINS7|PINCONF_PIN_6)
#define PINCONF_LCD_LP_2         (PINCONF_FUNC4|PINCONF_PINS8|PINCONF_PIN_6)
#define PINCONF_LCD_PWR_1        (PINCONF_FUNC3|PINCONF_PINS7|PINCONF_PIN_7)
#define PINCONF_LCD_PWR_2        (PINCONF_FUNC4|PINCONF_PINS8|PINCONF_PIN_7)
#define PINCONF_LCD_PWR_3        (PINCONF_FUNC6|PINCONF_PINSB|PINCONF_PIN_5)
#define PINCONF_LCD_VD0          (PINCONF_FUNC2|PINCONF_PINS4|PINCONF_PIN_1)
#define PINCONF_LCD_VD1          (PINCONF_FUNC2|PINCONF_PINS4|PINCONF_PIN_4)
#define PINCONF_LCD_VD2          (PINCONF_FUNC2|PINCONF_PINS4|PINCONF_PIN_3)
#define PINCONF_LCD_VD3          (PINCONF_FUNC2|PINCONF_PINS4|PINCONF_PIN_2)
#define PINCONF_LCD_VD4_1        (PINCONF_FUNC3|PINCONF_PINS8|PINCONF_PIN_7)
#define PINCONF_LCD_VD4_2        (PINCONF_FUNC4|PINCONF_PINS7|PINCONF_PIN_4)
#define PINCONF_LCD_VD5_1        (PINCONF_FUNC3|PINCONF_PINS8|PINCONF_PIN_6)
#define PINCONF_LCD_VD5_2        (PINCONF_FUNC4|PINCONF_PINS7|PINCONF_PIN_3)
#define PINCONF_LCD_VD6_1        (PINCONF_FUNC3|PINCONF_PINS8|PINCONF_PIN_5)
#define PINCONF_LCD_VD6_2        (PINCONF_FUNC4|PINCONF_PINS7|PINCONF_PIN_2)
#define PINCONF_LCD_VD7_1        (PINCONF_FUNC3|PINCONF_PINS8|PINCONF_PIN_4)
#define PINCONF_LCD_VD7_2        (PINCONF_FUNC4|PINCONF_PINS7|PINCONF_PIN_1)
#define PINCONF_LCD_VD8_1        (PINCONF_FUNC3|PINCONF_PINS7|PINCONF_PIN_5)
#define PINCONF_LCD_VD8_2        (PINCONF_FUNC4|PINCONF_PINS8|PINCONF_PIN_5)
#define PINCONF_LCD_VD9          (PINCONF_FUNC2|PINCONF_PINS4|PINCONF_PIN_8)
#define PINCONF_LCD_VD10         (PINCONF_FUNC2|PINCONF_PINS4|PINCONF_PIN_10)
#define PINCONF_LCD_VD11         (PINCONF_FUNC2|PINCONF_PINS4|PINCONF_PIN_9)
#define PINCONF_LCD_VD12_1       (PINCONF_FUNC3|PINCONF_PINS8|PINCONF_PIN_3)
#define PINCONF_LCD_VD12_2       (PINCONF_FUNC5|PINCONF_PINS4|PINCONF_PIN_2)
#define PINCONF_LCD_VD12_3       (PINCONF_FUNC7|PINCONF_PINS3|PINCONF_PIN_5)
#define PINCONF_LCD_VD13_1       (PINCONF_FUNC2|PINCONF_PINSB|PINCONF_PIN_6)
#define PINCONF_LCD_VD13_2       (PINCONF_FUNC5|PINCONF_PINS4|PINCONF_PIN_0)
#define PINCONF_LCD_VD13_3       (PINCONF_FUNC7|PINCONF_PINS3|PINCONF_PIN_4)
#define PINCONF_LCD_VD14_1       (PINCONF_FUNC2|PINCONF_PINSB|PINCONF_PIN_5)
#define PINCONF_LCD_VD14_2       (PINCONF_FUNC5|PINCONF_PINS4|PINCONF_PIN_10)
#define PINCONF_LCD_VD14_3       (PINCONF_FUNC6|PINCONF_PINS3|PINCONF_PIN_2)
#define PINCONF_LCD_VD15_1       (PINCONF_FUNC2|PINCONF_PINSB|PINCONF_PIN_4)
#define PINCONF_LCD_VD15_2       (PINCONF_FUNC5|PINCONF_PINS4|PINCONF_PIN_9)
#define PINCONF_LCD_VD15_3       (PINCONF_FUNC6|PINCONF_PINS3|PINCONF_PIN_1)
#define PINCONF_LCD_VD16_1       (PINCONF_FUNC3|PINCONF_PINS7|PINCONF_PIN_4)
#define PINCONF_LCD_VD16_2       (PINCONF_FUNC4|PINCONF_PINS8|PINCONF_PIN_4)
#define PINCONF_LCD_VD17         (PINCONF_FUNC3|PINCONF_PINS7|PINCONF_PIN_3)
#define PINCONF_LCD_VD18         (PINCONF_FUNC3|PINCONF_PINS7|PINCONF_PIN_2)
#define PINCONF_LCD_VD19_1       (PINCONF_FUNC3|PINCONF_PINS7|PINCONF_PIN_1)
#define PINCONF_LCD_VD19_2       (PINCONF_FUNC4|PINCONF_PINS8|PINCONF_PIN_3)
#define PINCONF_LCD_VD19_3       (PINCONF_FUNC5|PINCONF_PINS4|PINCONF_PIN_1)
#define PINCONF_LCD_VD19_4       (PINCONF_FUNC6|PINCONF_PINSB|PINCONF_PIN_6)
#define PINCONF_LCD_VD20_1       (PINCONF_FUNC2|PINCONF_PINSB|PINCONF_PIN_3)
#define PINCONF_LCD_VD20_2       (PINCONF_FUNC5|PINCONF_PINS4|PINCONF_PIN_4)
#define PINCONF_LCD_VD21_1       (PINCONF_FUNC2|PINCONF_PINSB|PINCONF_PIN_2)
#define PINCONF_LCD_VD21_2       (PINCONF_FUNC5|PINCONF_PINS4|PINCONF_PIN_3)
#define PINCONF_LCD_VD22_1       (PINCONF_FUNC2|PINCONF_PINSB|PINCONF_PIN_1)
#define PINCONF_LCD_VD22_2       (PINCONF_FUNC5|PINCONF_PINS4|PINCONF_PIN_8)
#define PINCONF_LCD_VD23_1       (PINCONF_FUNC2|PINCONF_PINSB|PINCONF_PIN_0)
#define PINCONF_LCD_VD23_2       (PINCONF_FUNC4|PINCONF_PINS7|PINCONF_PIN_5)

#define PINCONF_MCABORT_1        (PINCONF_FUNC1|PINCONF_PINS6|PINCONF_PIN_10)
#define PINCONF_MCABORT_2        (PINCONF_FUNC1|PINCONF_PINS9|PINCONF_PIN_0)
#define PINCONF_MCI0_1           (PINCONF_FUNC1|PINCONF_PINS5|PINCONF_PIN_3)
#define PINCONF_MCI0_2           (PINCONF_FUNC3|PINCONF_PINS8|PINCONF_PIN_2)
#define PINCONF_MCI1_1           (PINCONF_FUNC1|PINCONF_PINS5|PINCONF_PIN_2)
#define PINCONF_MCI1_2           (PINCONF_FUNC3|PINCONF_PINS8|PINCONF_PIN_1)
#define PINCONF_MCI2_1           (PINCONF_FUNC1|PINCONF_PINS5|PINCONF_PIN_1)
#define PINCONF_MCI2_2           (PINCONF_FUNC3|PINCONF_PINS8|PINCONF_PIN_0)
#define PINCONF_MCOA0_1          (PINCONF_FUNC1|PINCONF_PINS4|PINCONF_PIN_0)
#define PINCONF_MCOA0_2          (PINCONF_FUNC1|PINCONF_PINS9|PINCONF_PIN_3)
#define PINCONF_MCOA1_1          (PINCONF_FUNC1|PINCONF_PINS5|PINCONF_PIN_5)
#define PINCONF_MCOA1_2          (PINCONF_FUNC1|PINCONF_PINS9|PINCONF_PIN_5)
#define PINCONF_MCOA2_1          (PINCONF_FUNC1|PINCONF_PINS5|PINCONF_PIN_7)
#define PINCONF_MCOA2_2          (PINCONF_FUNC1|PINCONF_PINS9|PINCONF_PIN_1)
#define PINCONF_MCOB0_1          (PINCONF_FUNC1|PINCONF_PINS5|PINCONF_PIN_4)
#define PINCONF_MCOB0_2          (PINCONF_FUNC1|PINCONF_PINS9|PINCONF_PIN_4)
#define PINCONF_MCOB1_1          (PINCONF_FUNC1|PINCONF_PINS5|PINCONF_PIN_6)
#define PINCONF_MCOB1_2          (PINCONF_FUNC1|PINCONF_PINS9|PINCONF_PIN_6)
#define PINCONF_MCOB2_1          (PINCONF_FUNC1|PINCONF_PINS5|PINCONF_PIN_0)
#define PINCONF_MCOB2_2          (PINCONF_FUNC1|PINCONF_PINS9|PINCONF_PIN_2)

#define PINCONF_NMI_1            (PINCONF_FUNC1|PINCONF_PINSE|PINCONF_PIN_4)
#define PINCONF_NMI_2            (PINCONF_FUNC2|PINCONF_PINS4|PINCONF_PIN_0)

#define PINCONF_QEI_IDX          (PINCONF_FUNC1|PINCONF_PINSA|PINCONF_PIN_1)
#define PINCONF_QEI_PHA          (PINCONF_FUNC1|PINCONF_PINSA|PINCONF_PIN_3)
#define PINCONF_QEI_PHB          (PINCONF_FUNC1|PINCONF_PINSA|PINCONF_PIN_2)

#define PINCONF_SD_CD_1          (PINCONF_FUNC7|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_SLEW_FAST|PINCONF_PINS1|PINCONF_PIN_13)
#define PINCONF_SD_CD_2          (PINCONF_FUNC7|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_SLEW_FAST|PINCONF_PINSC|PINCONF_PIN_8)
#define PINCONF_SD_CLK           (PINCONF_FUNC7|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_SLEW_FAST|PINCONF_PINSC|PINCONF_PIN_0)
#define PINCONF_SD_CMD_1         (PINCONF_FUNC7|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_PINS1|PINCONF_PIN_6)
#define PINCONF_SD_CMD_2         (PINCONF_FUNC7|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_PINSC|PINCONF_PIN_10)
#define PINCONF_SD_DAT0_1        (PINCONF_FUNC7|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_PINS1|PINCONF_PIN_9)
#define PINCONF_SD_DAT0_2        (PINCONF_FUNC7|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_PINSC|PINCONF_PIN_4)
#define PINCONF_SD_DAT1_1        (PINCONF_FUNC7|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_PINS1|PINCONF_PIN_10)
#define PINCONF_SD_DAT1_2        (PINCONF_FUNC7|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_PINSC|PINCONF_PIN_5)
#define PINCONF_SD_DAT2_1        (PINCONF_FUNC7|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_PINS1|PINCONF_PIN_11)
#define PINCONF_SD_DAT2_2        (PINCONF_FUNC7|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_PINSC|PINCONF_PIN_6)
#define PINCONF_SD_DAT3_1        (PINCONF_FUNC7|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_PINS1|PINCONF_PIN_12)
#define PINCONF_SD_DAT3_2        (PINCONF_FUNC7|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_PINSC|PINCONF_PIN_7)
#define PINCONF_SD_DAT4          (PINCONF_FUNC7|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_PINSC|PINCONF_PIN_11)
#define PINCONF_SD_DAT5          (PINCONF_FUNC7|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_PINSC|PINCONF_PIN_12)
#define PINCONF_SD_DAT6          (PINCONF_FUNC7|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_PINSC|PINCONF_PIN_13)
#define PINCONF_SD_DAT7          (PINCONF_FUNC7|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_SLEW_FAST|PINCONF_PINSC|PINCONF_PIN_14)
#define PINCONF_SD_POW_1         (PINCONF_FUNC5|PINCONF_PINSD|PINCONF_PIN_1)
#define PINCONF_SD_POW_2         (PINCONF_FUNC7|PINCONF_PINS1|PINCONF_PIN_5)
#define PINCONF_SD_POW_3         (PINCONF_FUNC7|PINCONF_PINSC|PINCONF_PIN_9)
#define PINCONF_SD_RST_1         (PINCONF_FUNC7|PINCONF_PINS1|PINCONF_PIN_3)
#define PINCONF_SD_RST_2         (PINCONF_FUNC7|PINCONF_PINSC|PINCONF_PIN_2)
#define PINCONF_SD_VOLT0_1       (PINCONF_FUNC7|PINCONF_PINS1|PINCONF_PIN_8)
#define PINCONF_SD_VOLT0_2       (PINCONF_FUNC7|PINCONF_PINSC|PINCONF_PIN_1)
#define PINCONF_SD_VOLT1_1       (PINCONF_FUNC7|PINCONF_PINS1|PINCONF_PIN_4)
#define PINCONF_SD_VOLT1_2       (PINCONF_FUNC7|PINCONF_PINSC|PINCONF_PIN_3)
#define PINCONF_SD_VOLT2_1       (PINCONF_FUNC5|PINCONF_PINSD|PINCONF_PIN_16)
#define PINCONF_SD_VOLT2_2       (PINCONF_FUNC6|PINCONF_PINSF|PINCONF_PIN_11)
#define PINCONF_SD_WP_1          (PINCONF_FUNC5|PINCONF_PINSD|PINCONF_PIN_15)
#define PINCONF_SD_WP_2          (PINCONF_FUNC6|PINCONF_PINSF|PINCONF_PIN_10)

#define PINCONF_SGPIO0_1         (PINCONF_FUNC3|PINCONF_PINS0|PINCONF_PIN_0)
#define PINCONF_SGPIO0_2         (PINCONF_FUNC6|PINCONF_PINS9|PINCONF_PIN_0)
#define PINCONF_SGPIO0_3         (PINCONF_FUNC6|PINCONF_PINSF|PINCONF_PIN_1)
#define PINCONF_SGPIO1_1         (PINCONF_FUNC3|PINCONF_PINS0|PINCONF_PIN_1)
#define PINCONF_SGPIO1_2         (PINCONF_FUNC6|PINCONF_PINS9|PINCONF_PIN_1)
#define PINCONF_SGPIO1_3         (PINCONF_FUNC6|PINCONF_PINSF|PINCONF_PIN_2)
#define PINCONF_SGPIO2_1         (PINCONF_FUNC2|PINCONF_PINS1|PINCONF_PIN_15)
#define PINCONF_SGPIO2_2         (PINCONF_FUNC6|PINCONF_PINS9|PINCONF_PIN_2)
#define PINCONF_SGPIO2_3         (PINCONF_FUNC6|PINCONF_PINSF|PINCONF_PIN_3)
#define PINCONF_SGPIO3_1         (PINCONF_FUNC2|PINCONF_PINS1|PINCONF_PIN_16)
#define PINCONF_SGPIO3_2         (PINCONF_FUNC6|PINCONF_PINS9|PINCONF_PIN_5)
#define PINCONF_SGPIO3_3         (PINCONF_FUNC6|PINCONF_PINSF|PINCONF_PIN_9)
#define PINCONF_SGPIO4_1         (PINCONF_FUNC0|PINCONF_PINS2|PINCONF_PIN_0)
#define PINCONF_SGPIO4_2         (PINCONF_FUNC2|PINCONF_PINS6|PINCONF_PIN_3)
#define PINCONF_SGPIO4_3         (PINCONF_FUNC6|PINCONF_PINS9|PINCONF_PIN_4)
#define PINCONF_SGPIO4_4         (PINCONF_FUNC6|PINCONF_PINSF|PINCONF_PIN_5)
#define PINCONF_SGPIO4_5         (PINCONF_FUNC7|PINCONF_PINS7|PINCONF_PIN_0)
#define PINCONF_SGPIO4_6         (PINCONF_FUNC7|PINCONF_PINSD|PINCONF_PIN_0)
#define PINCONF_SGPIO5_1         (PINCONF_FUNC0|PINCONF_PINS2|PINCONF_PIN_1)
#define PINCONF_SGPIO5_2         (PINCONF_FUNC2|PINCONF_PINS6|PINCONF_PIN_6)
#define PINCONF_SGPIO5_3         (PINCONF_FUNC6|PINCONF_PINSF|PINCONF_PIN_6)
#define PINCONF_SGPIO5_4         (PINCONF_FUNC7|PINCONF_PINS7|PINCONF_PIN_1)
#define PINCONF_SGPIO5_5         (PINCONF_FUNC7|PINCONF_PINSD|PINCONF_PIN_1)
#define PINCONF_SGPIO6_1         (PINCONF_FUNC0|PINCONF_PINS2|PINCONF_PIN_2)
#define PINCONF_SGPIO6_2         (PINCONF_FUNC2|PINCONF_PINS6|PINCONF_PIN_7)
#define PINCONF_SGPIO6_3         (PINCONF_FUNC6|PINCONF_PINSF|PINCONF_PIN_7)
#define PINCONF_SGPIO6_4         (PINCONF_FUNC7|PINCONF_PINS7|PINCONF_PIN_2)
#define PINCONF_SGPIO6_5         (PINCONF_FUNC7|PINCONF_PINSD|PINCONF_PIN_2)
#define PINCONF_SGPIO7_1         (PINCONF_FUNC0|PINCONF_PINS2|PINCONF_PIN_6)
#define PINCONF_SGPIO7_2         (PINCONF_FUNC2|PINCONF_PINS6|PINCONF_PIN_8)
#define PINCONF_SGPIO7_3         (PINCONF_FUNC6|PINCONF_PINS1|PINCONF_PIN_0)
#define PINCONF_SGPIO7_4         (PINCONF_FUNC6|PINCONF_PINSF|PINCONF_PIN_8)
#define PINCONF_SGPIO7_5         (PINCONF_FUNC7|PINCONF_PINS7|PINCONF_PIN_7)
#define PINCONF_SGPIO7_6         (PINCONF_FUNC7|PINCONF_PINSD|PINCONF_PIN_3)
#define PINCONF_SGPIO8_1         (PINCONF_FUNC3|PINCONF_PINS1|PINCONF_PIN_1)
#define PINCONF_SGPIO8_2         (PINCONF_FUNC4|PINCONF_PINS8|PINCONF_PIN_0)
#define PINCONF_SGPIO8_3         (PINCONF_FUNC6|PINCONF_PINS1|PINCONF_PIN_12)
#define PINCONF_SGPIO8_4         (PINCONF_FUNC6|PINCONF_PINS9|PINCONF_PIN_6)
#define PINCONF_SGPIO8_5         (PINCONF_FUNC7|PINCONF_PINS4|PINCONF_PIN_2)
#define PINCONF_SGPIO8_6         (PINCONF_FUNC7|PINCONF_PINSD|PINCONF_PIN_4)
#define PINCONF_SGPIO9_1         (PINCONF_FUNC3|PINCONF_PINS1|PINCONF_PIN_2)
#define PINCONF_SGPIO9_2         (PINCONF_FUNC4|PINCONF_PINS8|PINCONF_PIN_1)
#define PINCONF_SGPIO9_3         (PINCONF_FUNC6|PINCONF_PINS1|PINCONF_PIN_13)
#define PINCONF_SGPIO9_4         (PINCONF_FUNC6|PINCONF_PINS9|PINCONF_PIN_3)
#define PINCONF_SGPIO9_5         (PINCONF_FUNC7|PINCONF_PINS4|PINCONF_PIN_3)
#define PINCONF_SGPIO9_6         (PINCONF_FUNC7|PINCONF_PINSD|PINCONF_PIN_5)
#define PINCONF_SGPIO10_1        (PINCONF_FUNC2|PINCONF_PINS1|PINCONF_PIN_3)
#define PINCONF_SGPIO10_2        (PINCONF_FUNC4|PINCONF_PINS8|PINCONF_PIN_2)
#define PINCONF_SGPIO10_3        (PINCONF_FUNC6|PINCONF_PINS1|PINCONF_PIN_14)
#define PINCONF_SGPIO10_4        (PINCONF_FUNC7|PINCONF_PINS4|PINCONF_PIN_4)
#define PINCONF_SGPIO10_5        (PINCONF_FUNC7|PINCONF_PINSD|PINCONF_PIN_6)
#define PINCONF_SGPIO11_1        (PINCONF_FUNC2|PINCONF_PINS1|PINCONF_PIN_4)
#define PINCONF_SGPIO11_2        (PINCONF_FUNC5|PINCONF_PINSC|PINCONF_PIN_12)
#define PINCONF_SGPIO11_3        (PINCONF_FUNC6|PINCONF_PINS1|PINCONF_PIN_17)
#define PINCONF_SGPIO11_4        (PINCONF_FUNC7|PINCONF_PINS4|PINCONF_PIN_5)
#define PINCONF_SGPIO11_5        (PINCONF_FUNC7|PINCONF_PINSD|PINCONF_PIN_7)
#define PINCONF_SGPIO12_1        (PINCONF_FUNC0|PINCONF_PINS2|PINCONF_PIN_3)
#define PINCONF_SGPIO12_2        (PINCONF_FUNC5|PINCONF_PINSC|PINCONF_PIN_13)
#define PINCONF_SGPIO12_3        (PINCONF_FUNC6|PINCONF_PINS1|PINCONF_PIN_18)
#define PINCONF_SGPIO12_4        (PINCONF_FUNC7|PINCONF_PINS4|PINCONF_PIN_6)
#define PINCONF_SGPIO12_5        (PINCONF_FUNC7|PINCONF_PINSD|PINCONF_PIN_8)
#define PINCONF_SGPIO13_1        (PINCONF_FUNC0|PINCONF_PINS2|PINCONF_PIN_4)
#define PINCONF_SGPIO13_2        (PINCONF_FUNC5|PINCONF_PINSC|PINCONF_PIN_14)
#define PINCONF_SGPIO13_3        (PINCONF_FUNC6|PINCONF_PINS1|PINCONF_PIN_20)
#define PINCONF_SGPIO13_4        (PINCONF_FUNC7|PINCONF_PINS4|PINCONF_PIN_8)
#define PINCONF_SGPIO13_5        (PINCONF_FUNC7|PINCONF_PINSD|PINCONF_PIN_9)
#define PINCONF_SGPIO14_1        (PINCONF_FUNC0|PINCONF_PINS2|PINCONF_PIN_5)
#define PINCONF_SGPIO14_2        (PINCONF_FUNC6|PINCONF_PINS1|PINCONF_PIN_6)
#define PINCONF_SGPIO14_3        (PINCONF_FUNC7|PINCONF_PINS4|PINCONF_PIN_9)
#define PINCONF_SGPIO15_1        (PINCONF_FUNC0|PINCONF_PINS2|PINCONF_PIN_8)
#define PINCONF_SGPIO15_2        (PINCONF_FUNC6|PINCONF_PINS1|PINCONF_PIN_5)
#define PINCONF_SGPIO15_3        (PINCONF_FUNC7|PINCONF_PINS4|PINCONF_PIN_10)

#define PINCONF_SPIFI_CS         (PINCONF_FUNC3|PINCONF_SLEW_FAST|PINCONF_PINS3|PINCONF_PIN_8)
#define PINCONF_SPIFI_MISO       (PINCONF_FUNC3|PINCONF_SLEW_FAST|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_PINS3|PINCONF_PIN_6)
#define PINCONF_SPIFI_MOSI       (PINCONF_FUNC3|PINCONF_SLEW_FAST|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_PINS3|PINCONF_PIN_7)
#define PINCONF_SPIFI_SCK        (PINCONF_FUNC3|PINCONF_SLEW_FAST|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_PINS3|PINCONF_PIN_3)
#define PINCONF_SPIFI_SIO2       (PINCONF_FUNC3|PINCONF_SLEW_FAST|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_PINS3|PINCONF_PIN_5)
#define PINCONF_SPIFI_SIO3       (PINCONF_FUNC3|PINCONF_SLEW_FAST|PINCONF_INBUFFER|PINCONF_GLITCH|PINCONF_PINS3|PINCONF_PIN_4)

#define PINCONF_SPI_MISO         (PINCONF_FUNC1|PINCONF_PINS3|PINCONF_PIN_6)
#define PINCONF_SPI_MOSI         (PINCONF_FUNC1|PINCONF_PINS3|PINCONF_PIN_7)
#define PINCONF_SPI_SCK          (PINCONF_FUNC1|PINCONF_PINS3|PINCONF_PIN_3)
#define PINCONF_SPI_SSEL         (PINCONF_FUNC1|PINCONF_PINS3|PINCONF_PIN_8)

#define PINCONF_SSP0_MISO_1      (PINCONF_FUNC2|PINCONF_PINS3|PINCONF_PIN_7)
#define PINCONF_SSP0_MISO_2      (PINCONF_FUNC2|PINCONF_PINSF|PINCONF_PIN_2)
#define PINCONF_SSP0_MISO_3      (PINCONF_FUNC5|PINCONF_PINS1|PINCONF_PIN_1)
#define PINCONF_SSP0_MISO_4      (PINCONF_FUNC5|PINCONF_PINS3|PINCONF_PIN_6)
#define PINCONF_SSP0_MISO_5      (PINCONF_FUNC7|PINCONF_PINS9|PINCONF_PIN_1)
#define PINCONF_SSP0_MOSI_1      (PINCONF_FUNC2|PINCONF_PINS3|PINCONF_PIN_8)
#define PINCONF_SSP0_MOSI_2      (PINCONF_FUNC2|PINCONF_PINSF|PINCONF_PIN_3)
#define PINCONF_SSP0_MOSI_3      (PINCONF_FUNC5|PINCONF_PINS1|PINCONF_PIN_2)
#define PINCONF_SSP0_MOSI_4      (PINCONF_FUNC5|PINCONF_PINS3|PINCONF_PIN_7)
#define PINCONF_SSP0_MOSI_5      (PINCONF_FUNC7|PINCONF_PINS9|PINCONF_PIN_2)
#define PINCONF_SSP0_SCK_1       (PINCONF_FUNC0|PINCONF_PINSF|PINCONF_PIN_0)
#define PINCONF_SSP0_SCK_2       (PINCONF_FUNC2|PINCONF_PINS3|PINCONF_PIN_3)
#define PINCONF_SSP0_SCK_3       (PINCONF_FUNC4|PINCONF_PINS3|PINCONF_PIN_0)
#define PINCONF_SSP0_SSEL_1      (PINCONF_FUNC2|PINCONF_PINS3|PINCONF_PIN_6)
#define PINCONF_SSP0_SSEL_2      (PINCONF_FUNC2|PINCONF_PINSF|PINCONF_PIN_1)
#define PINCONF_SSP0_SSEL_3      (PINCONF_FUNC5|PINCONF_PINS1|PINCONF_PIN_0)
#define PINCONF_SSP0_SSEL_4      (PINCONF_FUNC5|PINCONF_PINS3|PINCONF_PIN_8)
#define PINCONF_SSP0_SSEL_5      (PINCONF_FUNC7|PINCONF_PINS9|PINCONF_PIN_0)

#define PINCONF_SSP1_MISO_1      (PINCONF_FUNC1|PINCONF_PINS0|PINCONF_PIN_0)
#define PINCONF_SSP1_MISO_2      (PINCONF_FUNC2|PINCONF_PINSF|PINCONF_PIN_6)
#define PINCONF_SSP1_MISO_3      (PINCONF_FUNC5|PINCONF_PINS1|PINCONF_PIN_3)
#define PINCONF_SSP1_MOSI_1      (PINCONF_FUNC1|PINCONF_PINS0|PINCONF_PIN_1)
#define PINCONF_SSP1_MOSI_2      (PINCONF_FUNC2|PINCONF_PINSF|PINCONF_PIN_7)
#define PINCONF_SSP1_MOSI_3      (PINCONF_FUNC5|PINCONF_PINS1|PINCONF_PIN_4)
#define PINCONF_SSP1_SCK_1       (PINCONF_FUNC0|PINCONF_PINSF|PINCONF_PIN_4)
#define PINCONF_SSP1_SCK_2       (PINCONF_FUNC1|PINCONF_PINS1|PINCONF_PIN_19)
#define PINCONF_SSP1_SSEL_1      (PINCONF_FUNC1|PINCONF_PINS1|PINCONF_PIN_20)
#define PINCONF_SSP1_SSEL_2      (PINCONF_FUNC2|PINCONF_PINSF|PINCONF_PIN_5)
#define PINCONF_SSP1_SSEL_3      (PINCONF_FUNC5|PINCONF_PINS1|PINCONF_PIN_5)

#define PINCONF_T0_CAP0_1        (PINCONF_FUNC4|PINCONF_PINS1|PINCONF_PIN_13)
#define PINCONF_T0_CAP0_2        (PINCONF_FUNC7|PINCONF_PINS8|PINCONF_PIN_4)
#define PINCONF_T0_CAP1_1        (PINCONF_FUNC4|PINCONF_PINS1|PINCONF_PIN_12)
#define PINCONF_T0_CAP1_2        (PINCONF_FUNC7|PINCONF_PINS8|PINCONF_PIN_5)
#define PINCONF_T0_CAP2_1        (PINCONF_FUNC4|PINCONF_PINS1|PINCONF_PIN_20)
#define PINCONF_T0_CAP2_2        (PINCONF_FUNC7|PINCONF_PINS8|PINCONF_PIN_6)
#define PINCONF_T0_CAP3_1        (PINCONF_FUNC4|PINCONF_PINS1|PINCONF_PIN_17)
#define PINCONF_T0_CAP3_2        (PINCONF_FUNC7|PINCONF_PINS8|PINCONF_PIN_7)
#define PINCONF_T0_MAT0_1        (PINCONF_FUNC4|PINCONF_PINS1|PINCONF_PIN_16)
#define PINCONF_T0_MAT0_2        (PINCONF_FUNC7|PINCONF_PINS8|PINCONF_PIN_0)
#define PINCONF_T0_MAT1_1        (PINCONF_FUNC4|PINCONF_PINS1|PINCONF_PIN_15)
#define PINCONF_T0_MAT1_2        (PINCONF_FUNC7|PINCONF_PINS8|PINCONF_PIN_1)
#define PINCONF_T0_MAT2_1        (PINCONF_FUNC4|PINCONF_PINS1|PINCONF_PIN_14)
#define PINCONF_T0_MAT2_2        (PINCONF_FUNC7|PINCONF_PINS8|PINCONF_PIN_2)
#define PINCONF_T0_MAT3_1        (PINCONF_FUNC4|PINCONF_PINS1|PINCONF_PIN_18)
#define PINCONF_T0_MAT3_2        (PINCONF_FUNC7|PINCONF_PINS8|PINCONF_PIN_3)

#define PINCONF_T1_CAP0          (PINCONF_FUNC5|PINCONF_PINS5|PINCONF_PIN_0)
#define PINCONF_T1_CAP1          (PINCONF_FUNC5|PINCONF_PINS5|PINCONF_PIN_1)
#define PINCONF_T1_CAP2          (PINCONF_FUNC5|PINCONF_PINS5|PINCONF_PIN_2)
#define PINCONF_T1_CAP3          (PINCONF_FUNC5|PINCONF_PINS5|PINCONF_PIN_3)
#define PINCONF_T1_MAT0          (PINCONF_FUNC5|PINCONF_PINS5|PINCONF_PIN_4)
#define PINCONF_T1_MAT1          (PINCONF_FUNC5|PINCONF_PINS5|PINCONF_PIN_5)
#define PINCONF_T1_MAT2          (PINCONF_FUNC5|PINCONF_PINS5|PINCONF_PIN_6)
#define PINCONF_T1_MAT3          (PINCONF_FUNC5|PINCONF_PINS5|PINCONF_PIN_7)

#define PINCONF_T2_CAP0          (PINCONF_FUNC5|PINCONF_PINS6|PINCONF_PIN_1)
#define PINCONF_T2_CAP1          (PINCONF_FUNC5|PINCONF_PINS6|PINCONF_PIN_2)
#define PINCONF_T2_CAP2          (PINCONF_FUNC5|PINCONF_PINS6|PINCONF_PIN_3)
#define PINCONF_T2_CAP3          (PINCONF_FUNC5|PINCONF_PINS6|PINCONF_PIN_6)
#define PINCONF_T2_MAT0          (PINCONF_FUNC5|PINCONF_PINS6|PINCONF_PIN_7)
#define PINCONF_T2_MAT1          (PINCONF_FUNC5|PINCONF_PINS6|PINCONF_PIN_8)
#define PINCONF_T2_MAT2          (PINCONF_FUNC5|PINCONF_PINS6|PINCONF_PIN_9)
#define PINCONF_T2_MAT3          (PINCONF_FUNC5|PINCONF_PINS6|PINCONF_PIN_11)

#define PINCONF_T3_CAP0_1        (PINCONF_FUNC6|PINCONF_PINS2|PINCONF_PIN_0)
#define PINCONF_T3_CAP0_2        (PINCONF_FUNC6|PINCONF_PINSC|PINCONF_PIN_1)
#define PINCONF_T3_CAP1_1        (PINCONF_FUNC6|PINCONF_PINS2|PINCONF_PIN_1)
#define PINCONF_T3_CAP1_2        (PINCONF_FUNC6|PINCONF_PINSC|PINCONF_PIN_4)
#define PINCONF_T3_CAP2_1        (PINCONF_FUNC6|PINCONF_PINS2|PINCONF_PIN_2)
#define PINCONF_T3_CAP2_2        (PINCONF_FUNC6|PINCONF_PINSC|PINCONF_PIN_5)
#define PINCONF_T3_CAP3_1        (PINCONF_FUNC6|PINCONF_PINS2|PINCONF_PIN_6)
#define PINCONF_T3_CAP3_2        (PINCONF_FUNC6|PINCONF_PINSC|PINCONF_PIN_6)
#define PINCONF_T3_MAT0_1        (PINCONF_FUNC6|PINCONF_PINS2|PINCONF_PIN_3)
#define PINCONF_T3_MAT0_2        (PINCONF_FUNC6|PINCONF_PINSC|PINCONF_PIN_7)
#define PINCONF_T3_MAT1_1        (PINCONF_FUNC6|PINCONF_PINS2|PINCONF_PIN_4)
#define PINCONF_T3_MAT1_2        (PINCONF_FUNC6|PINCONF_PINSC|PINCONF_PIN_8)
#define PINCONF_T3_MAT2_1        (PINCONF_FUNC6|PINCONF_PINS2|PINCONF_PIN_5)
#define PINCONF_T3_MAT2_2        (PINCONF_FUNC6|PINCONF_PINSC|PINCONF_PIN_9)
#define PINCONF_T3_MAT3_1        (PINCONF_FUNC6|PINCONF_PINS2|PINCONF_PIN_7)
#define PINCONF_T3_MAT3_2        (PINCONF_FUNC6|PINCONF_PINSC|PINCONF_PIN_10)

#define PINCONF_TRACECLK         (PINCONF_FUNC2|PINCONF_PINSF|PINCONF_PIN_4)
#define PINCONF_TRACEDATA0_1     (PINCONF_FUNC3|PINCONF_PINSF|PINCONF_PIN_5)
#define PINCONF_TRACEDATA0_2     (PINCONF_FUNC5|PINCONF_PINS7|PINCONF_PIN_4)
#define PINCONF_TRACEDATA1_1     (PINCONF_FUNC3|PINCONF_PINSF|PINCONF_PIN_6)
#define PINCONF_TRACEDATA1_2     (PINCONF_FUNC5|PINCONF_PINS7|PINCONF_PIN_5)
#define PINCONF_TRACEDATA2_1     (PINCONF_FUNC3|PINCONF_PINSF|PINCONF_PIN_7)
#define PINCONF_TRACEDATA2_2     (PINCONF_FUNC5|PINCONF_PINS7|PINCONF_PIN_6)
#define PINCONF_TRACEDATA3_1     (PINCONF_FUNC3|PINCONF_PINSF|PINCONF_PIN_8)
#define PINCONF_TRACEDATA3_2     (PINCONF_FUNC5|PINCONF_PINS7|PINCONF_PIN_7)

#define PINCONF_U0_DIR_1         (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_6)
#define PINCONF_U0_DIR_2         (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSF|PINCONF_PIN_9)
#define PINCONF_U0_DIR_3         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS6|PINCONF_PIN_2)
#define PINCONF_U0_RXD_1         (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_1)
#define PINCONF_U0_RXD_2         (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSF|PINCONF_PIN_11)
#define PINCONF_U0_RXD_3         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS6|PINCONF_PIN_5)
#define PINCONF_U0_RXD_4         (PINCONF_FUNC7|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS9|PINCONF_PIN_6)
#define PINCONF_U0_TXD_1         (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_0)
#define PINCONF_U0_TXD_2         (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSF|PINCONF_PIN_10)
#define PINCONF_U0_TXD_3         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS6|PINCONF_PIN_4)
#define PINCONF_U0_TXD_4         (PINCONF_FUNC7|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS9|PINCONF_PIN_5)
#define PINCONF_U0_UCLK_1        (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_2)
#define PINCONF_U0_UCLK_2        (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSF|PINCONF_PIN_8)
#define PINCONF_U0_UCLK_3        (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS6|PINCONF_PIN_1)

#define PINCONF_U1_CTS_1         (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_11)
#define PINCONF_U1_CTS_2         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_2)
#define PINCONF_U1_CTS_3         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_7)
#define PINCONF_U1_CTS_4         (PINCONF_FUNC4|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS5|PINCONF_PIN_4)
#define PINCONF_U1_DCD_1         (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_12)
#define PINCONF_U1_DCD_2         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_11)
#define PINCONF_U1_DCD_3         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_9)
#define PINCONF_U1_DCD_4         (PINCONF_FUNC4|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS5|PINCONF_PIN_5)
#define PINCONF_U1_DSR_1         (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_7)
#define PINCONF_U1_DSR_2         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_10)
#define PINCONF_U1_DSR_3         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_8)
#define PINCONF_U1_DSR_4         (PINCONF_FUNC4|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS5|PINCONF_PIN_0)
#define PINCONF_U1_DTR_1         (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_8)
#define PINCONF_U1_DTR_2         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_12)
#define PINCONF_U1_DTR_3         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_10)
#define PINCONF_U1_DTR_4         (PINCONF_FUNC4|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS5|PINCONF_PIN_1)
#define PINCONF_U1_RI_1          (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_10)
#define PINCONF_U1_RI_2          (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_1)
#define PINCONF_U1_RI_3          (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_6)
#define PINCONF_U1_RI_4          (PINCONF_FUNC4|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS5|PINCONF_PIN_3)
#define PINCONF_U1_RTS_1         (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_9)
#define PINCONF_U1_RTS_2         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_3)
#define PINCONF_U1_RTS_3         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_5)
#define PINCONF_U1_RTS_4         (PINCONF_FUNC4|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS5|PINCONF_PIN_2)
#define PINCONF_U1_RXD_1         (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_14)
#define PINCONF_U1_RXD_2         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_14)
#define PINCONF_U1_RXD_3         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_12)
#define PINCONF_U1_RXD_4         (PINCONF_FUNC4|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS3|PINCONF_PIN_5)
#define PINCONF_U1_RXD_5         (PINCONF_FUNC4|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS5|PINCONF_PIN_7)
#define PINCONF_U1_TXD_1         (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_13)
#define PINCONF_U1_TXD_2         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSC|PINCONF_PIN_13)
#define PINCONF_U1_TXD_3         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSE|PINCONF_PIN_11)
#define PINCONF_U1_TXD_4         (PINCONF_FUNC4|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS3|PINCONF_PIN_4)
#define PINCONF_U1_TXD_5         (PINCONF_FUNC4|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS5|PINCONF_PIN_6)

#define PINCONF_U2_DIR_1         (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_18)
#define PINCONF_U2_DIR_2         (PINCONF_FUNC7|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_13)
#define PINCONF_U2_RXD_1         (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS1|PINCONF_PIN_16)
#define PINCONF_U2_RXD_2         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_11)
#define PINCONF_U2_RXD_3         (PINCONF_FUNC3|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSA|PINCONF_PIN_2)
#define PINCONF_U2_RXD_4         (PINCONF_FUNC6|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS7|PINCONF_PIN_2)
#define PINCONF_U2_TXD_1         (PINCONF_FUNC1|PINCONF_PINS1|PINCONF_PIN_15)
#define PINCONF_U2_TXD_2         (PINCONF_FUNC2|PINCONF_PINS2|PINCONF_PIN_10)
#define PINCONF_U2_TXD_3         (PINCONF_FUNC3|PINCONF_PINSA|PINCONF_PIN_1)
#define PINCONF_U2_TXD_4         (PINCONF_FUNC6|PINCONF_PINS7|PINCONF_PIN_1)
#define PINCONF_U2_UCLK_1        (PINCONF_FUNC1|PINCONF_PINS1|PINCONF_PIN_17)
#define PINCONF_U2_UCLK_2        (PINCONF_FUNC7|PINCONF_PINS2|PINCONF_PIN_12)

#define PINCONF_U3_BAUD_1        (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSF|PINCONF_PIN_7)
#define PINCONF_U3_BAUD_2        (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_9)
#define PINCONF_U3_BAUD_3        (PINCONF_FUNC6|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS4|PINCONF_PIN_3)
#define PINCONF_U3_DIR_1         (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSF|PINCONF_PIN_6)
#define PINCONF_U3_DIR_2         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_8)
#define PINCONF_U3_DIR_3         (PINCONF_FUNC6|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS4|PINCONF_PIN_4)
#define PINCONF_U3_RXD_1         (PINCONF_FUNC1|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINSF|PINCONF_PIN_3)
#define PINCONF_U3_RXD_2         (PINCONF_FUNC2|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS2|PINCONF_PIN_4)
#define PINCONF_U3_RXD_3         (PINCONF_FUNC6|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS4|PINCONF_PIN_2)
#define PINCONF_U3_RXD_4         (PINCONF_FUNC7|PINCONF_PULLUP|PINCONF_INBUFFER|PINCONF_PINS9|PINCONF_PIN_4)
#define PINCONF_U3_TXD_1         (PINCONF_FUNC1|PINCONF_PINSF|PINCONF_PIN_2)
#define PINCONF_U3_TXD_2         (PINCONF_FUNC2|PINCONF_PINS2|PINCONF_PIN_3)
#define PINCONF_U3_TXD_3         (PINCONF_FUNC6|PINCONF_PINS4|PINCONF_PIN_1)
#define PINCONF_U3_TXD_4         (PINCONF_FUNC7|PINCONF_PINS9|PINCONF_PIN_3)
#define PINCONF_U3_UCLK_1        (PINCONF_FUNC1|PINCONF_PINSF|PINCONF_PIN_5)
#define PINCONF_U3_UCLK_2        (PINCONF_FUNC2|PINCONF_PINS2|PINCONF_PIN_7)
#define PINCONF_U3_UCLK_3        (PINCONF_FUNC6|PINCONF_PINS4|PINCONF_PIN_0)

#define PINCONF_USB0_IND0_1      (PINCONF_FUNC1|PINCONF_PINS8|PINCONF_PIN_2)
#define PINCONF_USB0_IND0_2      (PINCONF_FUNC3|PINCONF_PINS2|PINCONF_PIN_6)
#define PINCONF_USB0_IND0_3      (PINCONF_FUNC3|PINCONF_PINS6|PINCONF_PIN_8)
#define PINCONF_USB0_IND0_4      (PINCONF_FUNC4|PINCONF_PINS1|PINCONF_PIN_4)
#define PINCONF_USB0_IND0_5      (PINCONF_FUNC7|PINCONF_PINS2|PINCONF_PIN_5)
#define PINCONF_USB0_IND1_1      (PINCONF_FUNC1|PINCONF_PINS8|PINCONF_PIN_1)
#define PINCONF_USB0_IND1_2      (PINCONF_FUNC3|PINCONF_PINS2|PINCONF_PIN_2)
#define PINCONF_USB0_IND1_3      (PINCONF_FUNC3|PINCONF_PINS6|PINCONF_PIN_7)
#define PINCONF_USB0_IND1_4      (PINCONF_FUNC4|PINCONF_PINS1|PINCONF_PIN_3)
#define PINCONF_USB0_PPWR_1      (PINCONF_FUNC1|PINCONF_PINS6|PINCONF_PIN_3)
#define PINCONF_USB0_PPWR_2      (PINCONF_FUNC3|PINCONF_PINS2|PINCONF_PIN_0)
#define PINCONF_USB0_PPWR_3      (PINCONF_FUNC4|PINCONF_PINS1|PINCONF_PIN_7)
#define PINCONF_USB0_PPWR_4      (PINCONF_FUNC7|PINCONF_PINS2|PINCONF_PIN_3)
#define PINCONF_USB0_PWR_FAULT_1 (PINCONF_FUNC1|PINCONF_PINS8|PINCONF_PIN_0)
#define PINCONF_USB0_PWR_FAULT_2 (PINCONF_FUNC3|PINCONF_PINS2|PINCONF_PIN_1)
#define PINCONF_USB0_PWR_FAULT_3 (PINCONF_FUNC3|PINCONF_PINS6|PINCONF_PIN_6)
#define PINCONF_USB0_PWR_FAULT_4 (PINCONF_FUNC4|PINCONF_PINS1|PINCONF_PIN_5)
#define PINCONF_USB0_PWR_FAULT_5 (PINCONF_FUNC7|PINCONF_PINS2|PINCONF_PIN_4)

#define PINCONF_USB1_IND0_1      (PINCONF_FUNC2|PINCONF_PINS9|PINCONF_PIN_4)
#define PINCONF_USB1_IND0_2      (PINCONF_FUNC3|PINCONF_PINS3|PINCONF_PIN_2)
#define PINCONF_USB1_IND1_1      (PINCONF_FUNC2|PINCONF_PINS9|PINCONF_PIN_3)
#define PINCONF_USB1_IND1_2      (PINCONF_FUNC3|PINCONF_PINS3|PINCONF_PIN_1)
#define PINCONF_USB1_PPWR        (PINCONF_FUNC2|PINCONF_PINS9|PINCONF_PIN_5)
#define PINCONF_USB1_PWR_FAULT   (PINCONF_FUNC2|PINCONF_PINS9|PINCONF_PIN_6)
#define PINCONF_USB1_ULPI_CLK_1  (PINCONF_FUNC1|PINCONF_PINS8|PINCONF_PIN_8)
#define PINCONF_USB1_ULPI_CLK_2  (PINCONF_FUNC1|PINCONF_PINSC|PINCONF_PIN_0)
#define PINCONF_USB1_ULPI_D0_1   (PINCONF_FUNC1|PINCONF_PINS8|PINCONF_PIN_5)
#define PINCONF_USB1_ULPI_D0_2   (PINCONF_FUNC1|PINCONF_PINSC|PINCONF_PIN_8)
#define PINCONF_USB1_ULPI_D0_3   (PINCONF_FUNC5|PINCONF_PINSD|PINCONF_PIN_11)
#define PINCONF_USB1_ULPI_D1_1   (PINCONF_FUNC1|PINCONF_PINS8|PINCONF_PIN_4)
#define PINCONF_USB1_ULPI_D1_2   (PINCONF_FUNC1|PINCONF_PINSC|PINCONF_PIN_7)
#define PINCONF_USB1_ULPI_D2_1   (PINCONF_FUNC1|PINCONF_PINS8|PINCONF_PIN_3)
#define PINCONF_USB1_ULPI_D2_2   (PINCONF_FUNC1|PINCONF_PINSC|PINCONF_PIN_6)
#define PINCONF_USB1_ULPI_D3_1   (PINCONF_FUNC1|PINCONF_PINSB|PINCONF_PIN_6)
#define PINCONF_USB1_ULPI_D3_2   (PINCONF_FUNC1|PINCONF_PINSC|PINCONF_PIN_5)
#define PINCONF_USB1_ULPI_D4_1   (PINCONF_FUNC1|PINCONF_PINSB|PINCONF_PIN_5)
#define PINCONF_USB1_ULPI_D4_2   (PINCONF_FUNC1|PINCONF_PINSC|PINCONF_PIN_4)
#define PINCONF_USB1_ULPI_D5_1   (PINCONF_FUNC0|PINCONF_PINSC|PINCONF_PIN_3)
#define PINCONF_USB1_ULPI_D5_2   (PINCONF_FUNC1|PINCONF_PINSB|PINCONF_PIN_4)
#define PINCONF_USB1_ULPI_D6_1   (PINCONF_FUNC0|PINCONF_PINSC|PINCONF_PIN_2)
#define PINCONF_USB1_ULPI_D6_2   (PINCONF_FUNC1|PINCONF_PINSB|PINCONF_PIN_3)
#define PINCONF_USB1_ULPI_D7_1   (PINCONF_FUNC0|PINCONF_PINSC|PINCONF_PIN_1)
#define PINCONF_USB1_ULPI_D7_2   (PINCONF_FUNC1|PINCONF_PINSB|PINCONF_PIN_2)
#define PINCONF_USB1_ULPI_DIR_1  (PINCONF_FUNC1|PINCONF_PINSB|PINCONF_PIN_1)
#define PINCONF_USB1_ULPI_DIR_2  (PINCONF_FUNC1|PINCONF_PINSC|PINCONF_PIN_11)
#define PINCONF_USB1_ULPI_NXT_1  (PINCONF_FUNC1|PINCONF_PINS8|PINCONF_PIN_6)
#define PINCONF_USB1_ULPI_NXT_2  (PINCONF_FUNC1|PINCONF_PINSC|PINCONF_PIN_9)
#define PINCONF_USB1_ULPI_STP_1  (PINCONF_FUNC1|PINCONF_PINS8|PINCONF_PIN_7)
#define PINCONF_USB1_ULPI_STP_2  (PINCONF_FUNC1|PINCONF_PINSC|PINCONF_PIN_10)
#define PINCONF_USB1_VBUS        (PINCONF_FUNC2|PINCONF_PINS2|PINCONF_PIN_5)

#define PINCONF_CLK0             (PINCONF_PINSO|PINCONF_PIN_0)
#define PINCONF_CLK1             (PINCONF_PINSO|PINCONF_PIN_1)
#define PINCONF_CLK2             (PINCONF_PINSO|PINCONF_PIN_2)
#define PINCONF_CLK3             (PINCONF_PINSO|PINCONF_PIN_3)

#define CLKCONF_CGU_OUT0         (PINCONF_FUNC5|PINCONF_CLK1)
#define CLKCONF_CGU_OUT1         (PINCONF_FUNC5|PINCONF_CLK3)
#define CLKCONF_CLKOUT_1         (PINCONF_FUNC1|PINCONF_CLK0)
#define CLKCONF_CLKOUT_2         (PINCONF_FUNC1|PINCONF_CLK1)
#define CLKCONF_CLKOUT_3         (PINCONF_FUNC1|PINCONF_CLK2)
#define CLKCONF_CLKOUT_4         (PINCONF_FUNC1|PINCONF_CLK3)
#define CLKCONF_EMC_CLK0         (PINCONF_FUNC0|PINCONF_CLK0)
#define CLKCONF_EMC_CLK01        (PINCONF_FUNC5|PINCONF_CLK0)
#define CLKCONF_EMC_CLK1         (PINCONF_FUNC0|PINCONF_CLK1)
#define CLKCONF_EMC_CLK2         (PINCONF_FUNC0|PINCONF_CLK3)
#define CLKCONF_EMC_CLK23        (PINCONF_FUNC5|PINCONF_CLK2)
#define CLKCONF_EMC_CLK3         (PINCONF_FUNC0|PINCONF_CLK2)
#define CLKCONF_ENET_REF_CLK     (PINCONF_FUNC7|PINCONF_CLK0)
#define CLKCONF_ENET_TX_CLK      (PINCONF_FUNC7|PINCONF_CLK0)
#define CLKCONF_I2S0_TX_MCLK     (PINCONF_FUNC6|PINCONF_CLK2)
#define CLKCONF_I2S1_RX_SCK_1    (PINCONF_FUNC7|PINCONF_CLK2)
#define CLKCONF_I2S1_RX_SCK_2    (PINCONF_FUNC7|PINCONF_CLK3)
#define CLKCONF_I2S1_TX_MCLK     (PINCONF_FUNC7|PINCONF_CLK1)
#define CLKCONF_SD_CLK_1         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_CLK0)
#define CLKCONF_SD_CLK_2         (PINCONF_FUNC4|PINCONF_INBUFFER|PINCONF_CLK2)
#define CLKCONF_SSP1_SCK         (PINCONF_FUNC6|PINCONF_CLK0)

/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Functions Prototypes
 ****************************************************************************/

#endif /* __ARCH_ARM_SRC_LPC43XX_HARDWARE_LPC4337JET100_PINCONFIG_H */
