#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

comment "AT91SAM3/4 Configuration Options"

choice
	prompt "AT91SAM3/4 Chip Selection"
	default ARCH_CHIP_ATSAM3U4E
	depends on ARCH_CHIP_SAM34

config ARCH_CHIP_ATSAM3U4E
	bool "ATSAM3U4E"
	select ARCH_CORTEXM3
	select ARCH_CHIP_SAM3U

config ARCH_CHIP_ATSAM3U4C
	bool "ATSAM3U4C"
	select ARCH_CORTEXM3
	select ARCH_CHIP_SAM3U

config ARCH_CHIP_ATSAM3U2E
	bool "ATSAM3U2E"
	select ARCH_CORTEXM3
	select ARCH_CHIP_SAM3U

config ARCH_CHIP_ATSAM3U2C
	bool "ATSAM3U2C"
	select ARCH_CORTEXM3
	select ARCH_CHIP_SAM3U

config ARCH_CHIP_ATSAM3U1E
	bool "ATSAM3U1E"
	select ARCH_CORTEXM3
	select <PERSON>CH_CHIP_SAM3U

config ARCH_CHIP_ATSAM3U1C
	bool "ATSAM3U1C"
	select ARCH_CORTEXM3
	select ARCH_CHIP_SAM3U

config ARCH_CHIP_ATSAM3X8E
	bool "ATSAM3X8E"
	select ARCH_CORTEXM3
	select ARCH_CHIP_SAM3X
	select SAM34_HAVE_EXTNOR
	select SAM34_HAVE_EXTNAND
	select SAM34_HAVE_EXTSRAM0
	select SAM34_HAVE_EXTSRAM1

config ARCH_CHIP_ATSAM3X8C
	bool "ATSAM3X8C"
	select ARCH_CORTEXM3
	select ARCH_CHIP_SAM3X

config ARCH_CHIP_ATSAM3X4E
	bool "ATSAM3X4E"
	select ARCH_CORTEXM3
	select ARCH_CHIP_SAM3X
	select SAM34_HAVE_EXTNOR
	select SAM34_HAVE_EXTNAND
	select SAM34_HAVE_EXTSRAM0
	select SAM34_HAVE_EXTSRAM1

config ARCH_CHIP_ATSAM3X4C
	bool "ATSAM3X4C"
	select ARCH_CORTEXM3
	select ARCH_CHIP_SAM3X

config ARCH_CHIP_ATSAM3A8C
	bool "ATSAM3A8C"
	select ARCH_CORTEXM3
	select ARCH_CHIP_SAM3A

config ARCH_CHIP_ATSAM3A4C
	bool "ATSAM3A4C"
	select ARCH_CORTEXM3
	select ARCH_CHIP_SAM3A

config ARCH_CHIP_ATSAM4CMP16B
	bool "ATSAM4CMP16B"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4CM

config ARCH_CHIP_ATSAM4LC2C
	bool "ATSAM4LC2C"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4L

config ARCH_CHIP_ATSAM4LC2B
	bool "ATSAM4LC2B"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4L

config ARCH_CHIP_ATSAM4LC2A
	bool "ATSAM4LC2A"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4L

config ARCH_CHIP_ATSAM4LC4C
	bool "ATSAM4LC4C"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4L

config ARCH_CHIP_ATSAM4LC4B
	bool "ATSAM4LC4B"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4L

config ARCH_CHIP_ATSAM4LC4A
	bool "ATSAM4LC4A"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4L

config ARCH_CHIP_ATSAM4LS2C
	bool "ATSAM4LS2C"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4L

config ARCH_CHIP_ATSAM4LS2B
	bool "ATSAM4LS2B"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4L

config ARCH_CHIP_ATSAM4LS2A
	bool "ATSAM4LS2A"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4L

config ARCH_CHIP_ATSAM4LS4C
	bool "ATSAM4LS4C"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4L

config ARCH_CHIP_ATSAM4LS4B
	bool "ATSAM4LS4B"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4L

config ARCH_CHIP_ATSAM4LS4A
	bool "ATSAM4LS4A"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4L

config ARCH_CHIP_ATSAM4SD32C
	bool "ATSAM4SD32C"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4S

config ARCH_CHIP_ATSAM4SD32B
	bool "ATSAM4SD32B"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4S

config ARCH_CHIP_ATSAM4SD16C
	bool "ATSAM4SD16C"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4S

config ARCH_CHIP_ATSAM4SD16B
	bool "ATSAM4SD16B"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4S

config ARCH_CHIP_ATSAM4SA16C
	bool "ATSAM4SA16C"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4S

config ARCH_CHIP_ATSAM4SA16B
	bool "ATSAM4SA16B"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4S

config ARCH_CHIP_ATSAM4S16C
	bool "ATSAM4S16C"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4S

config ARCH_CHIP_ATSAM4S16B
	bool "ATSAM4S16B"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4S

config ARCH_CHIP_ATSAM4S8C
	bool "ATSAM4S8C"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4S

config ARCH_CHIP_ATSAM4S8B
	bool "ATSAM4S8B"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4S

config ARCH_CHIP_ATSAM4S4C
	bool "ATSAM4S4C"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4S

config ARCH_CHIP_ATSAM4E16E
	bool "ATSAM4E16E"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4E

config ARCH_CHIP_ATSAM4E16C
	bool "ATSAM4E16C"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4E

config ARCH_CHIP_ATSAM4E8E
	bool "ATSAM4E8E"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4E

config ARCH_CHIP_ATSAM4E8C
	bool "ATSAM4E8C"
	select ARCH_CORTEXM4
	select ARCH_CHIP_SAM4E

endchoice # AT91SAM3/4 Chip Selection

config ARCH_CHIP_SAM3U
	bool
	default n
	select SAM34_HAVE_EXTNOR
	select SAM34_HAVE_EXTNAND
	select SAM34_HAVE_EXTSRAM0
	select SAM34_HAVE_EXTSRAM1

config ARCH_CHIP_SAM3X
	bool
	default n
	select SAM34_HAVE_GPIOD_IRQ
	select SAM34_HAVE_GPIOE_IRQ
	select SAM34_HAVE_GPIOF_IRQ

config ARCH_CHIP_SAM3A
	bool
	default n
	select SAM34_HAVE_GPIOD_IRQ
	select SAM34_HAVE_GPIOE_IRQ
	select SAM34_HAVE_GPIOF_IRQ

config ARCH_CHIP_SAM4CM
	bool
	default n
	select ARCH_HAVE_MULTICPU
	select ARCH_HAVE_TICKLESS

config ARCH_CHIP_SAM4L
	bool
	default n
	select ARCH_HAVE_RAMFUNCS

config ARCH_CHIP_SAM4E
	bool
	default n
	select SAM34_HAVE_EXTNOR
	select SAM34_HAVE_EXTNAND
	select SAM34_HAVE_EXTSRAM0
	select SAM34_HAVE_EXTSRAM1
	select SAM34_HAVE_GPIOD_IRQ
	select SAM34_HAVE_GPIOE_IRQ
	select SAM34_HAVE_GPIOF_IRQ

config ARCH_CHIP_SAM4S
	bool
	default n
	select SAM34_HAVE_EXTNOR
	select SAM34_HAVE_EXTNAND
	select SAM34_HAVE_EXTSRAM0
	select SAM34_HAVE_EXTSRAM1

menu "AT91SAM3/4 Peripheral Support"

config SAM34_ABDACB
	bool "Audio Bitstream DAC (ABDAC)"
	default n
	depends on ARCH_CHIP_SAM4L

config SAM34_ACC
	bool "Analog Comparator (ACC/ACMP)"
	default n
	depends on ARCH_CHIP_SAM4L || ARCH_CHIP_SAM4S || ARCH_CHIP_SAM4E

config SAM34_ADC
	bool "10-bit ADC Controller (ADC)"
	default n
	depends on ARCH_CHIP_SAM3U

config SAM34_ADC12B
	bool "12-bit ADC Controller (ADC12)"
	default n
	depends on !ARCH_CHIP_SAM4E

config SAM34_AES
	bool "Advanced Encryption Standard (AES)"
	default n
	depends on ARCH_CHIP_SAM4CM || ARCH_CHIP_SAM4E

config SAM34_AESA
	bool "Advanced Encryption Standard (AESA)"
	default n
	depends on ARCH_CHIP_SAM4L

config SAM34_AFEC0
	bool "Analog Front End 0 (AFEC0)"
	default n
	depends on ARCH_CHIP_SAM4E

config SAM34_AFEC1
	bool "Analog Front End 1 (AFEC1)"
	default n
	depends on ARCH_CHIP_SAM4E

config SAM34_APBA
	bool "APBA bridge (APBA)"
	default n
	depends on ARCH_CHIP_SAM4L

config SAM34_AST
	bool "Asynchronous Timer (AST)"
	default n
	depends on ARCH_CHIP_SAM4L

config SAM34_CAN0
	bool "CAN0"
	default n
	depends on ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4E

config SAM34_CAN1
	bool "CAN1"
	default n
	depends on ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4E

config SAM34_CATB
	bool "Capacitive Touch Module B (CATB)"
	default n
	depends on ARCH_CHIP_SAM4L

config SAM34_CHIPID
	bool "Chip ID"
	default n
	depends on ARCH_CHIP_SAM4L || ARCH_CHIP_SAM4E

config SAM34_CMCC
	bool "Cortex M Cache Controller (CMCC)"
	default n
	depends on ARCH_CHIP_SAM4E

config SAM34_CRCCU
	bool "CRC Calculation Unit (CRCCU)"
	default n
	depends on ARCH_CHIP_SAM4L || ARCH_CHIP_SAM4S

config SAM34_DACC
	bool "Digital To Analog Converter (DACC)"
	default n
	depends on ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4L || ARCH_CHIP_SAM4S || ARCH_CHIP_SAM4E

config SAM34_EIC
	bool "External Interrupt Controller (EIC)"
	default n
	depends on ARCH_CHIP_SAM4L || ARCH_CHIP_SAM4E

config SAM34_DMAC
	bool
	default n

config SAM34_DMAC0
	bool "DMA controller (DMAC0)"
	default n
	depends on ARCH_CHIP_SAM3U || ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4E
	select ARCH_DMA
	select SAM34_DMAC

config SAM34_DMAC1
	bool
	default n
	select SAM34_DMAC

config SAM34_EMAC
	bool "Ethernet MAC (EMAC)"
	default n
	depends on ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4E
	select NETDEVICES
	select ARCH_HAVE_PHY

config SAM34_FREQM
	bool "Frequency Meter (FREQM)"
	default n
	depends on ARCH_CHIP_SAM4L

config SAM34_GLOC
	bool "GLOC"
	default n
	depends on ARCH_CHIP_SAM4L

config SAM34_HMATRIX
	bool "HMATRIX"
	default n
	depends on ARCH_CHIP_SAM4L

config SAM34_HRAMC1
	bool "HRAMC1 (picoCache RAM)"
	default n
	depends on ARCH_CHIP_SAM4L

config SAM34_HSMCI
	bool "High Speed Multimedia Card Interface (HSMCI)"
	default n
	depends on ARCH_CHIP_SAM3U || ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4S || ARCH_CHIP_SAM4E
	select ARCH_HAVE_SDIO
	select MMCSD

config SAM34_IISC
	bool "Inter-IC Sound (I2S) Controller"
	default n
	depends on ARCH_CHIP_SAM4L

config SAM34_SLCDC
	bool "Segment LCD Controller (SLCDC)"
	default n
	depends on ARCH_CHIP_SAM4CM

config SAM34_LCDCA
	bool "LCD Controller A (LCDCA)"
	default n
	depends on ARCH_CHIP_SAM4L

config SAM34_NAND
	bool "NAND support"
	default n
	depends on ARCH_CHIP_SAM3U || ARCH_CHIP_SAM4E

config SAM34_OCD
	bool "On-chip DEBUG (OCD)"
	depends on ARCH_CHIP_SAM4L
	default y if DEBUG_SYMBOLS
	default n if !DEBUG_SYMBOLS

config SAM34_PARC
	bool "Parallel Capture (PARC)"
	default n
	depends on ARCH_CHIP_SAM4L

config SAM34_PDCA
	bool "Peripheral DMA controller (PDC)"
	default n
	depends on ARCH_CHIP_SAM4L || ARCH_CHIP_SAM4S || ARCH_CHIP_SAM4E
	select ARCH_DMA

config SAM34_PEVC
	bool "Peripheral Event Controller"
	default n
	depends on ARCH_CHIP_SAM4L

config SAM_PICOCACHE
	bool "PicoCACHE"
	default y
	depends on ARCH_CHIP_SAM4L

config SAM34_PICOUART
	bool "PicoUART"
	default n
	depends on ARCH_CHIP_SAM4L
	select UART_SERIALDRIVER

config SAM34_PWM
	bool "Pulse Width Modulation (PWM) Controller"
	default n
	depends on ARCH_CHIP_SAM3U || ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4S || ARCH_CHIP_SAM4E

config SAM34_RTC
	bool "Real Time Clock (RTC)"
	default n
	depends on ARCH_CHIP_SAM3U || ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4S || ARCH_CHIP_SAM4E

config SAM34_RTT
	bool "Real Time Timer (RTT)"
	default n
	depends on ARCH_CHIP_SAM3U || ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4S || ARCH_CHIP_SAM4E

config SAM34_SDRAMC
	bool "SDRAM Controller (SDRAMC)"
	default n
	depends on ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A

config SAM34_SMC
	bool "Static Memory Controller (SMC)"
	default n
	depends on ARCH_CHIP_SAM3U || ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4S || ARCH_CHIP_SAM4E

config SAM34_SPI0
	bool "Serial Peripheral Interface 0 (SPI0)"
	default n
	select SPI

config SAM34_SPI1
	bool "Serial Peripheral Interface 1 (SPI1)"
	default n
	depends on ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A
	select SPI

config SAM34_SSC
	bool "Synchronous Serial Controller (SSC)"
	default n
	depends on ARCH_CHIP_SAM3U || ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4S

config SAM34_TC0
	bool "Timer/Counter 0 (TC0)"
	default n
	select SAM34_TC

config SAM34_TC1
	bool "Timer/Counter 1 (TC1)"
	default n
	select SAM34_TC

config SAM34_TC2
	bool "Timer/Counter 2 (TC2)"
	default n
	depends on ARCH_CHIP_SAM3U || ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4S || ARCH_CHIP_SAM4E
	select SAM34_TC

config SAM34_TC3
	bool "Timer/Counter 3 (TC3)"
	default n
	depends on ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4S || ARCH_CHIP_SAM4E
	select SAM34_TC

config SAM34_TC4
	bool "Timer/Counter 4 (TC4)"
	default n
	depends on ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4S || ARCH_CHIP_SAM4E
	select SAM34_TC

config SAM34_TC5
	bool "Timer/Counter 5 (TC5)"
	default n
	depends on ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4S || ARCH_CHIP_SAM4E
	select SAM34_TC

config SAM34_TC6
	bool "Timer/Counter 6 (TC6)"
	default n
	depends on ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4E
	select SAM34_TC

config SAM34_TC7
	bool "Timer/Counter 7 (TC7)"
	default n
	depends on ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4E
	select SAM34_TC

config SAM34_TC8
	bool "Timer/Counter 8 (TC8)"
	default n
	depends on ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4E
	select SAM34_TC

config SAM34_TRNG
	bool "True Random Number Generator (TRNG)"
	default n
	depends on ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4L
	select ARCH_HAVE_RNG

config SAM34_TWI
	bool
	default n

config SAM34_TWIM
	bool
	default n

config SAM34_TWIS
	bool
	default n

config SAM34_TWIM0
	bool "Two-wire Master Interface 0 (TWIM0)"
	default n
	select SAM34_TWI
	select SAM34_TWIM

config SAM34_TWIS0
	bool "Two-wire Slave Interface 0 (TWIS0)"
	default n
	select SAM34_TWI
	select SAM34_TWIS

config SAM34_TWIM1
	bool "Two-wire Master Interface 1 (TWIM1)"
	default n
	select SAM34_TWI
	select SAM34_TWIM

config SAM34_TWIS1
	bool "Two-wire Slave Interface 1 (TWIS1)"
	default n
	select SAM34_TWI
	select SAM34_TWIS

config SAM34_TWIM2
	bool "Two-wire Master Interface 2 (TWIM2)"
	default n
	depends on ARCH_CHIP_SAM4L
	select SAM34_TWI
	select SAM34_TWIM

config SAM34_TWIM3
	bool "Two-wire Master Interface 3 (TWIM3)"
	default n
	depends on ARCH_CHIP_SAM4L
	select SAM34_TWI
	select SAM34_TWIM

config SAM34_UART0
	bool "UART 0"
	default y
	depends on ARCH_CHIP_SAM3U || ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4CM || ARCH_CHIP_SAM4S || ARCH_CHIP_SAM4E
	select UART0_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config SAM34_UART1
	bool "UART 1"
	default n
	depends on ARCH_CHIP_SAM4CM || ARCH_CHIP_SAM4S || ARCH_CHIP_SAM4E
	select UART1_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

if SAM34_UART1 && ARCH_CHIP_SAM4CM

config SAM34_UART1_OPTICAL
	bool "UART 1 is optical"
	default n

endif

config SAM34_UDP
	bool "USB Device Full Speed (UDP)"
	default n
	depends on ARCH_CHIP_SAM4S || ARCH_CHIP_SAM4E
	select ARCH_USBDEV_STALLQUEUE

config SAM34_UDPHS
	bool "USB Device High Speed (UDPHS)"
	default n
	depends on ARCH_CHIP_SAM3U

config SAM34_UOTGHS
	bool "USB OTG High Speed (UOTGHS)"
	default n
	depends on ARCH_CHIP_SAM3A || ARCH_CHIP_SAM3X

config SAM34_USBC
	bool "USB 2.0 Interface (USBC)"
	default n
	depends on ARCH_CHIP_SAM4L

config SAM34_USART0
	bool "USART 0"
	default n
	select USART0_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config SAM34_USART1
	bool "USART 1"
	default n
	select USART1_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config SAM34_USART2
	bool "USART 2"
	default n
	depends on ARCH_CHIP_SAM3U || ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4CM || ARCH_CHIP_SAM4L
	select USART2_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config SAM34_USART3
	bool "USART 3"
	default n
	depends on ARCH_CHIP_SAM3U || ARCH_CHIP_SAM3X || ARCH_CHIP_SAM3A || ARCH_CHIP_SAM4CM || ARCH_CHIP_SAM4L
	select USART3_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config SAM34_WDT
	bool "Watchdog Timer (WDT)"
	default n

endmenu # AT91SAM3/4 Peripheral Support

if ARCH_CHIP_SAM4L
menu "AT91SAM3/4 Clock Configuration"

config SAM34_RESET_PERIPHCLKS
	bool "Enable all peripheral clocks on reset"
	default n
	---help---
		By default, only a few necessary peripheral clocks are enabled at
		reset. If this setting is enabled, then all clocking will be enabled
		to all of the selected peripherals on reset.

config SAM34_OSC0
	bool "External oscillator 0"
	default n
	---help---
		Oscillator 0 might be automatically selected for several reasons:
		Oscillator 0 might be the system clock or the source clock for
		either PLL0 or DFPLL.  It might also be needed if OSC0 is the source
		clock for GCLK9.  By selecting SAM34_OSC0, you can also force the
		clock to be enabled at boot time for other uses.

config SAM34_OSC32K
	bool "32.768KHz external oscillator"
	default n
	---help---
		The 32K oscillator might be automatically selected for several
		reasons: The 32K oscillator may be the source clock for DFPLL0 or
		the source clock for GLK9 that might be used to driver PLL0.  By
		selecting SAM34_OSC32K, you can also force the clock to be enabled
		at boot time.  OSC32 may needed by other devices as well (AST, WDT,
		PICUART, RTC).

config SAM34_RC80M
	bool "80MHz RC oscillator"
	default n
	---help---
		The 80MHz RC oscillator might be automatically selected for several
		reasons: This might be the system clock or the source clock for the
		DFPLL or it could be the source for GCLK9 that drives PLL0. By
		selecting SAM34_RC80M, you can also force the clock to be enabled at
		boot time for other uses.

config SAM34_RCFAST
	bool "Fast RC oscillator"
	default n
	---help---
		The fast RC oscillator might be automatically selected for several
		reasons: The 12/8/4 fast RC oscillator may be used as the system
		clock or as the source for GLCK9 that drives PLL0. If not then, it
		may be enabled by setting the SAM34_RCFASTxM configuration variable.

if SAM34_RCFAST
choice
	prompt "Fast RC Oscillator Speed"
	default SAM34_RCFAST8M

config SAM34_RCFAST12M
	bool "12MHz"

config SAM34_RCFAST8M
	bool "8MHz"

config SAM34_RCFAST4M
	bool "4MHz"

endchoice # Fast RC Oscillator Speed
endif # SAM34_RCFAST

config SAM34_RC1M
	bool "1MHz RC oscillator"
	default n
	---help---
		The 1MHz RC oscillator might be automatically selected for several
		reasons: The 1MHz RC oscillator may be used as the system block or
		may be the source clock for GLCK9 that drives PLL0.  By selecting
		SAM34_RC1M, you can also force the clock to be enabled at boot time
		for other purposes.

config SAM34_RC32K
	bool "32KHz RC oscillator"
	default n
	---help---
		The 32KHz RC oscillator might be automatically selected for several
		reasons: The 32KHz RC oscillator may be used as the input to DFLL0
		or as the input to GCLK9 that drives PLL0. By selecting SAM34_RC32K,
		you can also force the clock to be enabled at boot time for other
		purposes.

endmenu # AT91SAM3/4 Clock Configuration
endif # ARCH_CHIP_SAM4L

menu "AT91SAM3/4 External Memory Configuration"

config SAM34_HAVE_EXTNAND
	bool

config SAM34_HAVE_EXTNOR
	bool

config SAM34_HAVE_EXTDRAM
	bool

config SAM34_HAVE_EXTSRAM0
	bool

config SAM34_HAVE_EXTSRAM1
	bool

config SAM34_EXTNAND
	bool "Configure external NAND"
	default n
	depends on SAM34_HAVE_EXTNAND
	---help---
		Configure external NAND memory and, if applicable, map then external
		NAND into the memory map.

if SAM34_EXTNAND

config SAM34_EXTNANDSIZE
	int "External NAND size"
	default 0
	---help---
		Size of the external NAND in bytes.
	config SAM34_HAVE_NAND
	bool
	default n

config SAM34_NAND_DUMP
	bool "NAND data dump"
	default n
	depends on DEBUG_FEATURES && DEBUG_FS
	---help---
		Dump the contents of all data read and written to FLASH.  Depends on
		CONFIG_DEBUG_FEATURES and DEBUG_FS.

endif # SAM34_EXTNAND

config SAM34_EXTNOR
	bool "Configure external NOR memory"
	default n
	depends on SAM34_HAVE_EXTNOR
	---help---
		Configure external NOR memory and, if applicable, map then external
		NOR into the memory map.

if SAM34_EXTNOR

config SAM34_EXTNORSIZE
	int "External NOR size"
	default 0
	---help---
		Size of the external NOR in bytes.

endif # SAM34_EXTNOR

config SAM34_EXTDRAM
	bool "Configure external DRAM"
	default n
	depends on SAM34_HAVE_EXTDRAM
	select ARCH_HAVE_SDRAM
	---help---
		Configure external DRAM memory and, if applicable, map then external
		DRAM into the memory map.

if SAM34_EXTDRAM

config SAM34_EXTDRAMSIZE
	int "External SDRAM size"
	default 0
	---help---
		Size of the external SDRAM in bytes.

choice
	prompt "SDRAM Width Selection"
	default SAM34_SDRAM_16BIT

config SAM34_SDRAM_8BIT
	bool "8-bit"

config SAM34_SDRAM_16BIT
	bool "16-bit"

config SAM34_SDRAM_32BIT
	bool "32-bit"

endchoice # SDRAM Width Selection

config SAM34_EXTDRAMHEAP
	bool "Add external SDRAM to the heap"
	default y
	---help---
		Add the external SDRAM into the heap.

endif # SAM34_EXTDRAM

config SAM34_EXTSRAM0
	bool "Configure external SRAM (Bank 0)"
	default n
	depends on SAM34_HAVE_EXTSRAM0
	---help---
		Configure external SRAM Bank 0 memory and, if applicable, map then
		external SRAM Bank 0 into the memory map.

if SAM34_EXTSRAM0

config SAM34_EXTSRAM0SIZE
	int "External SRAM size"
	default 0
	---help---
		Size of the external SRAM Bank 0 in bytes.

config SAM34_EXTSRAM0HEAP
	bool "Add external SRAM (Bank 0) to the heap"
	default y
	---help---
		Add external SRAM Bank 0 into the heap.

endif # SAM34_EXTSRAM0

config SAM34_EXTSRAM1
	bool "Configure external SRAM (Bank 1)"
	default n
	depends on SAM34_HAVE_EXTSRAM1
	---help---
		Configure external SRAM Bank 1 memory and, if applicable, map then
		external SRAM Bank 1 into the memory map.

if SAM34_EXTSRAM1

config SAM34_EXTSRAM1SIZE
	int "External SRAM1 size"
	default 0
	---help---
		Size of the external SRAM Bank 1 in bytes.

config SAM34_EXTSRAM1HEAP
	bool "Add external SRAM (Bank 1) to the heap"
	default y
	---help---
		Add external SRAM Bank 1 into the heap.

endif # SAM34_EXTSRAM1
endmenu # External Memory Configuration

menu "AT91SAM3/4 GPIO Interrupt Configuration"

config SAM34_HAVE_GPIOD_IRQ
	bool
	default n

config SAM34_HAVE_GPIOE_IRQ
	bool
	default n

config SAM34_HAVE_GPIOF_IRQ
	bool
	default n

config SAM34_GPIO_IRQ
	bool "GPIO pin interrupts"
	depends on !ARCH_CHIP_SAM4L
	---help---
		Enable support for interrupting GPIO pins

if SAM34_GPIO_IRQ

config SAM34_GPIOA_IRQ
	bool "GPIOA interrupts"
	default n

config SAM34_GPIOB_IRQ
	bool "GPIOB interrupts"
	default n

config SAM34_GPIOC_IRQ
	bool "GPIOC interrupts"
	default n

config SAM34_GPIOD_IRQ
	bool "GPIOD interrupts"
	default n
	depends on SAM34_HAVE_GPIOD_IRQ

config SAM34_GPIOE_IRQ
	bool "GPIOE interrupts"
	default n
	depends on SAM34_HAVE_GPIOE_IRQ

config SAM34_GPIOF_IRQ
	bool "GPIOF interrupts"
	default n
	depends on SAM34_HAVE_GPIOF_IRQ

endif # SAM34_GPIO_IRQ
endmenu # AT91SAM3/4 GPIO Interrupt Configuration

menu "AT91SAM3/4 Timer/Counter Configuration"
	depends on SAM34_TC && ARCH_CHIP_SAM4CM

config SAM34_TC0_CLK
	bool "Enable TC channel 0 clock input pin"
	default n
	depends on SAM34_TC0

config SAM34_TC0_TIOA
	bool "Enable TC channel 0 output A"
	default n
	depends on SAM34_TC0

config SAM34_TC0_TIOB
	bool "Enable TC channel 0 output B"
	default n
	depends on SAM34_TC0

config SAM34_TC1_CLK
	bool "Enable TC channel 1 clock input pin"
	default n
	depends on SAM34_TC1

config SAM34_TC1_TIOA
	bool "Enable TC channel 1 output A"
	default n
	depends on SAM34_TC1

config SAM34_TC1_TIOB
	bool "Enable TC channel 1 output B"
	default n
	depends on SAM34_TC1

config SAM34_TC2_CLK
	bool "Enable TC channel 2 clock input pin"
	default n
	depends on SAM34_TC2

config SAM34_TC2_TIOA2
	bool "Enable TC channel 2 output A"
	default n
	depends on SAM34_TC2

config SAM34_TC2_TIOB2
	bool "Enable TC channel 2 output B"
	default n
	depends on SAM34_TC2

config SAM34_TC3_CLK
	bool "Enable TC channel 3 clock input pin"
	default n
	depends on SAM34_TC3

config SAM34_TC3_TIOA
	bool "Enable TC channel 3 output A"
	default n
	depends on SAM34_TC3

config SAM34_TC3_TIOB
	bool "Enable TC channel 3 output B"
	default n
	depends on SAM34_TC3

config SAM34_TC4_CLK
	bool "Enable TC channel 4 clock input pin"
	default n
	depends on SAM34_TC4

config SAM34_TC4_TIOA
	bool "Enable TC channel 4 output A"
	default n
	depends on SAM34_TC4

config SAM34_TC4_TIOB
	bool "Enable TC channel 4 output B"
	default n
	depends on SAM34_TC4

config SAM34_TC5_CLK
	bool "Enable TC channel 5 clock input pin"
	default n
	depends on SAM34_TC5

config SAM34_TC5_TIOA
	bool "Enable TC channel 5 output A"
	default n
	depends on SAM34_TC5

config SAM34_TC5_TIOB
	bool "Enable TC channel 5 output B"
	default n
	depends on SAM34_TC5

config SAM34_ONESHOT
	bool "TC one-shot wrapper"
	default n if !SCHED_TICKLESS
	default y if SCHED_TICKLESS
	---help---
		Enable a wrapper around the low level timer/counter functions to
		support one-shot timer.

config SAM34_FREERUN
	bool "TC free-running wrapper"
	default n if !SCHED_TICKLESS
	default y if SCHED_TICKLESS
	---help---
		Enable a wrapper around the low level timer/counter functions to
		support a free-running timer.

if SCHED_TICKLESS

config SAM34_TICKLESS_ONESHOT
	int "Tickless one-shot timer channel"
	default 0
	range 0 8
	---help---
		If the Tickless OS feature is enabled, the one clock must be
		assigned to provided the one-shot timer needed by the OS.

config SAM34_TICKLESS_FREERUN
	int "Tickless free-running timer channel"
	default 1
	range 0 8
	---help---
		If the Tickless OS feature is enabled, the one clock must be
		assigned to provided the free-running timer needed by the OS.

endif # SCHED_TICKLESS
endmenu # AT91SAM3/4 Timer/Counter Configuration

if SAM34_SPI0 || SAM34_SPI1

menu "AT91SAM3/4 SPI device driver options"

config SAM34_SPI_DMA
	bool "SPI DMA"
	default n
	depends on (SAM34_DMAC0 && SAM34_SPI0) || (SAM34_DMAC1 && SAM34_SPI1)
	---help---
		Use DMA to improve SPI transfer performance.

config SAM34_SPI_DMATHRESHOLD
	int "SPI DMA threshold"
	default 4
	depends on SAM34_SPI_DMA
	---help---
		When SPI DMA is enabled, small DMA transfers will still be performed
		by polling logic.  But we need a threshold value to determine what
		is small.  That value is provided by SAM34_SPI_DMATHRESHOLD.

config SAM34_SPI_DMADEBUG
	bool "SPI DMA transfer debug"
	depends on SAM34_SPI_DMA && DEBUG_FEATURES && DEBUG_DMA
	default n
	---help---
		Enable special debug instrumentation analyze SPI DMA data transfers.
		This logic is as non-invasive as possible:  It samples DMA
		registers at key points in the data transfer and then dumps all of
		the registers at the end of the transfer.

config SAM34_SPI_REGDEBUG
	bool "SPI Register level debug"
	depends on DEBUG_SPI_INFO
	default n
	---help---
		Output detailed register-level SPI device debug information.
		Requires also CONFIG_SPI_INFO.

endmenu # AT91SAM3/4 SPI device driver options
endif # SAM34_SPI0 || SAM34_SPI1

if SAM34_TWIM

menu "AT91SAM3/4 TWI master device driver options"

config SAM34_TWIM0_FREQUENCY
	int "TWI0 Frequency"
	default 100000
	depends on SAM34_TWIM0

config SAM34_TWIM1_FREQUENCY
	int "TWI1 Frequency"
	default 100000
	depends on SAM34_TWIM1

config SAM34_TWIM2_FREQUENCY
	int "TWI2 Frequency"
	default 100000
	depends on SAM34_TWIM2

config SAM34_TWIM3_FREQUENCY
	int "TWI3 Frequency"
	default 100000
	depends on SAM34_TWIM3

config SAM34_TWI_REGDEBUG
	bool "TWI register level debug"
	depends on DEBUG_I2C_INFO
	default n
	---help---
		Output detailed register-level TWI device debug information.
		Very invasive! Requires also CONFIG_DEBUG_I2C_INFO.

endmenu # TWI device driver options
endif # SAM34_TWIM

menu "AT91SAM3/4 EMAC device driver options"
	depends on SAM34_EMAC

config SAM34_EMAC_NRXBUFFERS
	int "Number of RX buffers"
	default 16
	---help---
		EMAC buffer memory is segmented into 128 byte units (not
		configurable).  This setting provides the number of such 128 byte
		units used for reception.  This is also equal to the number of RX
		descriptors that will be allocated  The selected value must be an
		even power of 2.

config SAM34_EMAC_NTXBUFFERS
	int "Number of TX buffers"
	default 8
	---help---
		EMAC buffer memory is segmented into full Ethernet packets (size
		NET_BUFSIZE bytes).  This setting provides the number of such packets
		that can be in flight.  This is also equal to the number of TX
		descriptors that will be allocated.

config SAM34_EMAC_PREALLOCATE
	bool "Preallocate buffers"
	default n
	---help---
		Buffer an descriptor many may either be allocated from the memory
		pool or pre-allocated to lie in .bss.  This options selected pre-
		allocated buffer memory.

config SAM34_EMAC_NBC
	bool "Disable Broadcast"
	default n
	---help---
		Select to disable receipt of broadcast packets.

config SAM34_EMAC_PHYADDR
	int "PHY address"
	default 1
	---help---
		The 5-bit address of the PHY on the board.  Default: 1

config SAM34_EMAC_PHYINIT
	bool "Board-specific PHY Initialization"
	default n
	---help---
		Some boards require specialized initialization of the PHY before it can be used.
		This may include such things as configuring GPIOs, resetting the PHY, etc.  If
		SAM34_EMAC_PHYINIT is defined in the configuration then the board specific logic must
		provide sam_phyinitialize();  The SAM34 EMAC driver will call this function
		one time before it first uses the PHY.

choice
	prompt "PHY interface"
	default SAM34_EMAC_MII

config SAM34_EMAC_MII
	bool "MII"
	---help---
		Support Ethernet MII interface (vs RMII).

config SAM34_EMAC_RMII
	bool "RMII"
	depends on !ARCH_CHIP_SAM4E
	---help---
		Support Ethernet RMII interface (vs MII).

endchoice # PHY interface

config SAM34_EMAC_CLAUSE45
	bool "Clause 45 MII"
	depends on SAM34_EMAC_MII
	---help---
		MDIO was originally defined in Clause 22 of IEEE RFC802.3. In the
		original specification, a single MDIO interface is able to access up
		to 32 registers in 32 different PHY devices.  To meet the needs the
		expanding needs of 10-Gigabit Ethernet devices, Clause 45 of the
		802.3ae specification provided the following additions to MDIO:

		- Ability to access 65,536 registers in 32 different devices on
		  32 different ports
		- Additional OP-code and ST-code for Indirect Address register
		  access for 10 Gigabit Ethernet
		- End-to-end fault signaling
		- Multiple loopback points
		- Low voltage electrical specification

		By default, Clause 22 PHYs will be supported unless this option is
		selected.

config SAM34_EMAC_AUTONEG
	bool "Use autonegotiation"
	default y
	---help---
		Use PHY autonegotiation to determine speed and mode

config SAM34_EMAC_ETHFD
	bool "Full duplex"
	default n
	depends on !SAM34_EMAC_AUTONEG
	---help---
		If SAM34_EMAC_AUTONEG is not defined, then this may be defined to select full duplex
		mode. Default: half-duplex

config SAM34_EMAC_ETH100MBPS
	bool "100 Mbps"
	default n
	depends on !SAM34_EMAC_AUTONEG
	---help---
		If SAM34_EMAC_AUTONEG is not defined, then this may be defined to select 100 MBps
		speed.  Default: 10 Mbps

config SAM34_EMAC_PHYSR
	int "PHY Status Register Address (decimal)"
	depends on SAM34_EMAC_AUTONEG
	---help---
		This must be provided if SAM34_EMAC_AUTONEG is defined.  The PHY status register
		address may diff from PHY to PHY.  This configuration sets the address of
		the PHY status register.

config SAM34_EMAC_PHYSR_ALTCONFIG
	bool "PHY Status Alternate Bit Layout"
	default n
	depends on SAM34_EMAC_AUTONEG
	---help---
		Different PHYs present speed and mode information in different ways.  Some
		will present separate information for speed and mode (this is the default).
		Those PHYs, for example, may provide a 10/100 Mbps indication and a separate
		full/half duplex indication. This options selects an alternative representation
		where speed and mode information are combined.  This might mean, for example,
		separate bits for 10HD, 100HD, 10FD and 100FD.

config SAM34_EMAC_PHYSR_SPEED
	hex "PHY Speed Mask"
	depends on SAM34_EMAC_AUTONEG && !SAM34_EMAC_PHYSR_ALTCONFIG
	---help---
		This must be provided if SAM34_EMAC_AUTONEG is defined.  This provides bit mask
		for isolating the 10 or 100MBps speed indication.

config SAM34_EMAC_PHYSR_100MBPS
	hex "PHY 100Mbps Speed Value"
	depends on SAM34_EMAC_AUTONEG && !SAM34_EMAC_PHYSR_ALTCONFIG
	---help---
		This must be provided if SAM34_EMAC_AUTONEG is defined.  This provides the value
		of the speed bit(s) indicating 100MBps speed.

config SAM34_EMAC_PHYSR_MODE
	hex "PHY Mode Mask"
	depends on SAM34_EMAC_AUTONEG && !SAM34_EMAC_PHYSR_ALTCONFIG
	---help---
		This must be provided if SAM34_EMAC_AUTONEG is defined.  This provide bit mask
		for isolating the full or half duplex mode bits.

config SAM34_EMAC_PHYSR_FULLDUPLEX
	hex "PHY Full Duplex Mode Value"
	depends on SAM34_EMAC_AUTONEG && !SAM34_EMAC_PHYSR_ALTCONFIG
	---help---
		This must be provided if SAM34_EMAC_AUTONEG is defined.  This provides the
		value of the mode bits indicating full duplex mode.

config SAM34_EMAC_PHYSR_ALTMODE
	hex "PHY Mode Mask"
	depends on SAM34_EMAC_AUTONEG && SAM34_EMAC_PHYSR_ALTCONFIG
	---help---
		This must be provided if SAM34_EMAC_AUTONEG is defined.  This provide bit mask
		for isolating the speed and full/half duplex mode bits.

config SAM34_EMAC_PHYSR_10HD
	hex "10MBase-T Half Duplex Value"
	depends on SAM34_EMAC_AUTONEG && SAM34_EMAC_PHYSR_ALTCONFIG
	---help---
		This must be provided if SAM34_EMAC_AUTONEG is defined.  This is the value
		under the bit mask that represents the 10Mbps, half duplex setting.

config SAM34_EMAC_PHYSR_100HD
	hex "100Base-T Half Duplex Value"
	depends on SAM34_EMAC_AUTONEG && SAM34_EMAC_PHYSR_ALTCONFIG
	---help---
		This must be provided if SAM34_EMAC_AUTONEG is defined.  This is the value
		under the bit mask that represents the 100Mbps, half duplex setting.

config SAM34_EMAC_PHYSR_10FD
	hex "10Base-T Full Duplex Value"
	depends on SAM34_EMAC_AUTONEG && SAM34_EMAC_PHYSR_ALTCONFIG
	---help---
		This must be provided if SAM34_EMAC_AUTONEG is defined.  This is the value
		under the bit mask that represents the 10Mbps, full duplex setting.

config SAM34_EMAC_PHYSR_100FD
	hex "100Base-T Full Duplex Value"
	depends on SAM34_EMAC_AUTONEG && SAM34_EMAC_PHYSR_ALTCONFIG
	---help---
		This must be provided if SAM34_EMAC_AUTONEG is defined.  This is the value
		under the bit mask that represents the 100Mbps, full duplex setting.

config SAM34_EMAC_ISETH0
	bool
	default y

config SAM34_EMAC_REGDEBUG
	bool "Register-Level Debug"
	default n
	depends on DEBUG_NET_INFO
	---help---
		Enable very low-level register access debug.  Depends on CONFIG_DEBUG_NET_INFO.

endmenu # EMAC device driver options

if SAM34_HSMCI

menu "AT91SAM3/4 HSMCI device driver options"

config SAM34_HSMCI_DMA
	bool "Support DMA data transfers"
	default y
	select SDIO_DMA
	---help---
		Support DMA data transfers.
		Enable SD card DMA data transfers.  This is marginally optional.
		For most usages, SD accesses will cause data overruns if used without
		DMA.

config SAM34_HSMCI_RDPROOF
	bool "Read Proof Enable"
	default n
	---help---
		Enabling Read Proof allows to stop the HSMCI Clock during read
		access if the internal FIFO is full. This will guarantee data
		integrity, not bandwidth.

config SAM34_HSMCI_WRPROOF
	bool "Write Proof Enable"
	default n
	---help---
		Enabling Write Proof allows to stop the HSMCI Clock during write
		access if the internal FIFO is full. This will guarantee data
		integrity, not bandwidth.

config SAM34_HSMCI_XFRDEBUG
	bool "HSMCI transfer debug"
	depends on DEBUG_FS_INFO
	default n
	---help---
		Enable special debug instrumentation analyze HSMCI data transfers.
		This logic is as non-invasive as possible:  It samples HSMCI
		registers at key points in the data transfer and then dumps all of
		the registers at the end of the transfer.  If DEBUG_DMA is also
		enabled, then DMA register will be collected as well.  Requires also
		DEBUG_FS and CONFIG_DEBUG_INFO.

config SAM34_HSMCI_CMDDEBUG
	bool "HSMCI command debug"
	depends on DEBUG_FS_INFO
	default n
	---help---
		Enable special debug instrumentation analyze HSMCI commands. This
		logic is as non-invasive as possible:  It samples HSMCI registers at
		key points in the data transfer and then dumps all of the registers
		at the end of the transfer.  If DEBUG_DMA is also enabled, then DMA
		register will be collected as well.  Requires also DEBUG_FS and
		CONFIG_DEBUG_INFO.

endmenu # HSMCI device driver options
endif # SAM34_HSMCI

menu "AT91SAM3/4 USB Full Speed Device Controller driver (DCD) options"
	depends on SAM34_UDP

config SAM34_UDP_REGDEBUG
	bool "Enable low-level UDP register debug"
	default n
	depends on DEBUG_USB_INFO

endmenu # USB Full Speed Device Controller driver (DCD) options

config SAM34_TC
	bool
	default n
	select ARCH_HAVE_EXTCLK

menu "AT91SAM3/4 Timer/Counter options"
	depends on SAM34_TC

config SAM34_TC_REGDEBUG
	bool "Enable low-level timer/counter register debug"
	default n
	depends on DEBUG_TIMER_INFO

endmenu # USB Full Speed Device Controller driver (DCD) options

menu "AT91SAM3/4 Watchdog Configuration"
	depends on SAM34_WDT

config WDT_ENABLED_ON_RESET
	bool "Watchdog Enabled on reset"
	default n
	---help---
		The WDT can be enabled at reset. This is controlled by the WDTAUTO
		fuse. The WDT will be set in basic mode, RCSYS is set as source for
		CLK_CNT, and PSEL will be set to a value giving Tpsel above 100 ms
		(SAM4L)

		This setting informs that start-up logic that the watchdog is
		enabled.

config WDT_DISABLE_ON_RESET
	bool "Disable watchdog on reset"
	default n
	depends on WDT_ENABLED_ON_RESET
	---help---
		If the WDT can be enabled at reset then this setting may be used to
		configure and disable the watchdog timer very early in the boot
		sequence.

config WDT_TIMEOUT
	int "Watchdog Timeout (ms)"
	default 5000
	depends on !WDT_DISABLE_ON_RESET
	---help---
		Watchdog timeout value in milliseconds.

config WDT_MINTIME
	int "Watchdog Minimum Time (ms)"
	default 2500
	depends on !WDT_DISABLE_ON_RESET
	---help---
		Minimum watchdog kick interval

menuconfig WDT_THREAD
	bool "Watchdog Kicker Thread"
	depends on !WDT_DISABLE_ON_RESET
	default y

if WDT_THREAD

config WDT_THREAD_NAME
	string "Watchdog Thread Name"
	default "wdog"

config WDT_THREAD_INTERVAL
	int "Watchdog Thread Interval (ms)"
	default 2500

config WDT_THREAD_PRIORITY
	int "Watchdog Thread Priority"
	default 200

config WDT_THREAD_STACKSIZE
	int "Watchdog Thread Stacksize"
	default 1024

endif # WDT_THREAD
endmenu #"AT91SAM3/4 Watchdog device driver options"
