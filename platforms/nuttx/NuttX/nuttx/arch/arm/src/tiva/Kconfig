#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

comment "Tiva/Stellaris Configuration Options"

choice
	prompt "Tiva/Stellaris Chip Selection"
	default ARCH_CHIP_LM3S6965
	depends on ARCH_CHIP_LM || ARCH_CHIP_TIVA || ARCH_CHIP_SIMPLELINK

config ARCH_CHIP_LM3S6918
	bool "LM3S6918"
	depends on ARCH_CHIP_LM
	select ARCH_CORTEXM3
	select ARCH_CHIP_LM3S
	select TIVA_HAVE_I2C1
	select TIVA_HAVE_SSI1
	select TIVA_HAVE_TIMER3
	select TIVA_HAVE_ETHERNET

config ARCH_CHIP_LM3S9B92
	bool "LM3S9B92"
	depends on ARCH_CHIP_LM
	select ARCH_CORTEXM3
	select ARCH_CHIP_LM3S
	select TIVA_HAVE_UART3
	select TIVA_HAVE_I2C1
	select TIVA_HAVE_SSI1
	select TIVA_HAVE_TIMER3
	select TIVA_HAVE_ETHERNET
	select TIVA_HAVE_GPIOH_IRQS

config ARCH_CHIP_LM3S9B96
	bool "LM3S9B96"
	depends on ARCH_CHIP_LM
	select ARCH_CORTEXM3
	select ARCH_CHIP_LM3S
	select TIVA_HAVE_UART3
	select TIVA_HAVE_I2C1
	select TIVA_HAVE_SSI1
	select TIVA_HAVE_TIMER3
	select TIVA_HAVE_ETHERNET
	select TIVA_HAVE_GPIOH_IRQS

config ARCH_CHIP_LM3S6432
	bool "LM3S6432"
	depends on ARCH_CHIP_LM
	select ARCH_CORTEXM3
	select ARCH_CHIP_LM3S
	select TIVA_HAVE_ETHERNET

config ARCH_CHIP_LM3S6965
	bool "LM3S6965"
	depends on ARCH_CHIP_LM
	select ARCH_CORTEXM3
	select ARCH_CHIP_LM3S
	select TIVA_HAVE_UART3
	select TIVA_HAVE_I2C1
	select TIVA_HAVE_TIMER3
	select TIVA_HAVE_ETHERNET
	select TIVA_HAVE_GPIOH_IRQS

config ARCH_CHIP_LM3S8962
	bool "LM3S8962"
	depends on ARCH_CHIP_LM
	select ARCH_CORTEXM3
	select ARCH_CHIP_LM3S
	select TIVA_HAVE_UART3
	select TIVA_HAVE_I2C1
	select TIVA_HAVE_TIMER3
	select TIVA_HAVE_TIMER4
	select TIVA_HAVE_TIMER5
	select TIVA_HAVE_ETHERNET
	select TIVA_HAVE_GPIOH_IRQS

config ARCH_CHIP_LM4F120
	bool "LM4F120"
	depends on ARCH_CHIP_LM
	select ARCH_CORTEXM4
	select ARCH_CHIP_LM4F
	select ARCH_HAVE_FPU

config ARCH_CHIP_TM4C123AH6PM
	bool "TM4C123AH6PM"
	depends on ARCH_CHIP_TIVA
	select ARCH_CHIP_TM4C
	select ARCH_CHIP_TM4C123
	select TIVA_HAVE_GPIOA_IRQS
	select TIVA_HAVE_GPIOB_IRQS
	select TIVA_HAVE_GPIOC_IRQS
	select TIVA_HAVE_GPIOD_IRQS
	select TIVA_HAVE_GPIOE_IRQS
	select TIVA_HAVE_GPIOF_IRQS
	select TIVA_HAVE_GPIOG_IRQS
	select TIVA_HAVE_EEPROM
	select TIVA_HAVE_I2C4
	select TIVA_HAVE_I2C5
	select TIVA_HAVE_CAN1
	select TIVA_HAVE_QEI0
	select TIVA_HAVE_QEI1

config ARCH_CHIP_TM4C123GH6ZRB
	bool "TM4C123GH6ZRB"
	depends on ARCH_CHIP_TIVA
	select ARCH_CHIP_TM4C
	select ARCH_CHIP_TM4C123
	select TIVA_HAVE_GPIOP_IRQS
	select TIVA_HAVE_GPIOQ_IRQS
	select TIVA_HAVE_I2C4
	select TIVA_HAVE_I2C5
	select TIVA_HAVE_CAN1
	select TIVA_HAVE_QEI0
	select TIVA_HAVE_QEI1

config ARCH_CHIP_TM4C123GH6PM
	bool "TM4C123GH6PM"
	depends on ARCH_CHIP_TIVA
	select ARCH_CHIP_TM4C
	select ARCH_CHIP_TM4C123
	select TIVA_HAVE_GPIOA_IRQS
	select TIVA_HAVE_GPIOB_IRQS
	select TIVA_HAVE_GPIOC_IRQS
	select TIVA_HAVE_GPIOD_IRQS
	select TIVA_HAVE_GPIOE_IRQS
	select TIVA_HAVE_GPIOF_IRQS
	select TIVA_HAVE_ADC0
	select TIVA_HAVE_ADC1
	select TIVA_HAVE_CAN1
	select TIVA_HAVE_QEI0
	select TIVA_HAVE_QEI1

config ARCH_CHIP_TM4C123GH6PZ
	bool "TM4C123GH6PZ"
	depends on ARCH_CHIP_TIVA
	select ARCH_CHIP_TM4C
	select ARCH_CHIP_TM4C123
	select TIVA_HAVE_GPIOA_IRQS
	select TIVA_HAVE_GPIOB_IRQS
	select TIVA_HAVE_GPIOC_IRQS
	select TIVA_HAVE_GPIOD_IRQS
	select TIVA_HAVE_GPIOE_IRQS
	select TIVA_HAVE_GPIOF_IRQS
	select TIVA_HAVE_GPIOG_IRQS
	select TIVA_HAVE_GPIOH_IRQS
	select TIVA_HAVE_GPIOJ_IRQS
	select TIVA_HAVE_GPIOK_IRQS
	select TIVA_HAVE_ADC0
	select TIVA_HAVE_ADC1
	select TIVA_HAVE_CAN1
	select TIVA_HAVE_QEI0
	select TIVA_HAVE_QEI1

config ARCH_CHIP_TM4C123GH6PGE
	bool "TM4C123GH6PGE"
	depends on ARCH_CHIP_TIVA
	select ARCH_CHIP_TM4C
	select ARCH_CHIP_TM4C123
	select TIVA_HAVE_GPIOA_IRQS
	select TIVA_HAVE_GPIOB_IRQS
	select TIVA_HAVE_GPIOC_IRQS
	select TIVA_HAVE_GPIOD_IRQS
	select TIVA_HAVE_GPIOE_IRQS
	select TIVA_HAVE_GPIOF_IRQS
	select TIVA_HAVE_GPIOG_IRQS
	select TIVA_HAVE_GPIOH_IRQS
	select TIVA_HAVE_GPIOJ_IRQS
	select TIVA_HAVE_GPIOK_IRQS
	select TIVA_HAVE_GPIOL_IRQS
	select TIVA_HAVE_GPIOM_IRQS
	select TIVA_HAVE_GPION_IRQS
	select TIVA_HAVE_GPIOP_IRQS
	select TIVA_HAVE_ADC0
	select TIVA_HAVE_ADC1
	select TIVA_HAVE_CAN1
	select TIVA_HAVE_QEI0
	select TIVA_HAVE_QEI1

config ARCH_CHIP_TM4C1294NCPDT
	bool "TM4C1294NCPDT"
	depends on ARCH_CHIP_TIVA
	select ARCH_CHIP_TM4C
	select ARCH_CHIP_TM4C129
	select TIVA_HAVE_ETHERNET
	select TIVA_HAVE_EEPROM
	select TIVA_HAVE_GPIOG_IRQS
	select TIVA_HAVE_GPIOH_IRQS
	select TIVA_HAVE_GPIOJ_IRQS
	select TIVA_HAVE_GPIOK_IRQS
	select TIVA_HAVE_GPIOL_IRQS
	select TIVA_HAVE_GPIOM_IRQS
	select TIVA_HAVE_GPION_IRQS
	select TIVA_HAVE_GPIOP_IRQS
	select TIVA_HAVE_GPIOQ_IRQS
	select TIVA_HAVE_CAN1
	select TIVA_HAVE_QEI0

config ARCH_CHIP_TM4C129ENCPDT
	bool "TM4C129ENCPDT"
	depends on ARCH_CHIP_TIVA
	select ARCH_CHIP_TM4C
	select ARCH_CHIP_TM4C129
	select TIVA_HAVE_ETHERNET
	select TIVA_HAVE_EEPROM
	select TIVA_HAVE_GPIOG_IRQS
	select TIVA_HAVE_GPIOH_IRQS
	select TIVA_HAVE_GPIOJ_IRQS
	select TIVA_HAVE_GPIOK_IRQS
	select TIVA_HAVE_GPIOL_IRQS
	select TIVA_HAVE_GPIOM_IRQS
	select TIVA_HAVE_GPION_IRQS
	select TIVA_HAVE_GPIOP_IRQS
	select TIVA_HAVE_GPIOQ_IRQS
	select TIVA_HAVE_CAN1
	select TIVA_HAVE_QEI0

config ARCH_CHIP_TM4C129ENCZAD
	bool "TM4C129ENCZAD"
	depends on ARCH_CHIP_TIVA
	select ARCH_CHIP_TM4C
	select ARCH_CHIP_TM4C129
	select TIVA_HAVE_ETHERNET
	select TIVA_HAVE_EEPROM
	select TIVA_HAVE_GPIOG_IRQS
	select TIVA_HAVE_GPIOH_IRQS
	select TIVA_HAVE_GPIOJ_IRQS
	select TIVA_HAVE_GPIOK_IRQS
	select TIVA_HAVE_GPIOL_IRQS
	select TIVA_HAVE_GPIOM_IRQS
	select TIVA_HAVE_GPION_IRQS
	select TIVA_HAVE_GPIOP_IRQS
	select TIVA_HAVE_GPIOQ_IRQS
	select TIVA_HAVE_GPIOR_IRQS
	select TIVA_HAVE_GPIOS_IRQS
	select TIVA_HAVE_GPIOT_IRQS
	select TIVA_HAVE_CAN1
	select TIVA_HAVE_QEI0

config ARCH_CHIP_TM4C129XNCZAD
	bool "TM4C129XNCZAD"
	depends on ARCH_CHIP_TIVA
	select ARCH_CHIP_TM4C
	select ARCH_CHIP_TM4C129
	select TIVA_HAVE_ETHERNET
	select TIVA_HAVE_GPIOG_IRQS
	select TIVA_HAVE_GPIOH_IRQS
	select TIVA_HAVE_GPIOJ_IRQS
	select TIVA_HAVE_GPIOK_IRQS
	select TIVA_HAVE_GPIOL_IRQS
	select TIVA_HAVE_GPIOM_IRQS
	select TIVA_HAVE_GPION_IRQS
	select TIVA_HAVE_GPIOP_IRQS
	select TIVA_HAVE_GPIOQ_IRQS
	select TIVA_HAVE_GPIOR_IRQS
	select TIVA_HAVE_GPIOS_IRQS
	select TIVA_HAVE_GPIOT_IRQS
	select TIVA_HAVE_CAN1
	select TIVA_HAVE_QEI0

config ARCH_CHIP_CC1310
	bool "CC1310 SimpleLink"
	depends on ARCH_CHIP_SIMPLELINK || EXPERIMENTAL
	select ARCH_CHIP_CC13X0

config ARCH_CHIP_CC1312R1
	bool "CC1312R1 SimpleLink"
	depends on ARCH_CHIP_SIMPLELINK || EXPERIMENTAL
	select ARCH_CHIP_CC13X2

config ARCH_CHIP_CC1352R1
	bool "CC1352R1 SimpleLink"
	depends on ARCH_CHIP_SIMPLELINK || EXPERIMENTAL
	select ARCH_CHIP_CC13X2

endchoice

# Chip versions

choice
	prompt "CC13xx Chip Version"
	default ARCH_CHIP_CC13XX_V1
	depends on ARCH_CHIP_CC13X0 || ARCH_CHIP_CC13X2

config ARCH_CHIP_CC13XX_V1
	bool "Version 1"
	depends on ARCH_CHIP_CC13X2

config ARCH_CHIP_CC13XX_V2
	bool "Version 2"

endchoice # CC13x2 Chip Version

# Chip families

config ARCH_CHIP_LM3S
	bool
	select TIVA_HAVE_GPIOA_IRQS
	select TIVA_HAVE_GPIOB_IRQS
	select TIVA_HAVE_GPIOC_IRQS
	select TIVA_HAVE_GPIOD_IRQS
	select TIVA_HAVE_GPIOE_IRQS
	select TIVA_HAVE_GPIOF_IRQS
	select TIVA_HAVE_GPIOG_IRQS
	select TIVA_HAVE_SSI0
	select TIVA_HAVE_UART1
	select TIVA_HAVE_UART2
	select TIVA_HAVE_UART3

config ARCH_CHIP_LM4F
	bool
	select TIVA_HAVE_GPIOA_IRQS
	select TIVA_HAVE_GPIOB_IRQS
	select TIVA_HAVE_GPIOC_IRQS
	select TIVA_HAVE_GPIOD_IRQS
	select TIVA_HAVE_GPIOE_IRQS
	select TIVA_HAVE_GPIOF_IRQS
	select TIVA_HAVE_GPIOG_IRQS
	select TIVA_HAVE_GPIOH_IRQS
	select TIVA_HAVE_I2C1
	select TIVA_HAVE_I2C2
	select TIVA_HAVE_I2C3
	select TIVA_HAVE_UART1
	select TIVA_HAVE_UART2
	select TIVA_HAVE_UART3
	select TIVA_HAVE_UART4
	select TIVA_HAVE_UART5
	select TIVA_HAVE_UART6
	select TIVA_HAVE_UART7
	select TIVA_HAVE_SSI0
	select TIVA_HAVE_SSI1
	select TIVA_HAVE_SSI2
	select TIVA_HAVE_SSI3
	select TIVA_HAVE_TIMER3
	select TIVA_HAVE_TIMER4
	select TIVA_HAVE_TIMER5

config ARCH_CHIP_TM4C123
	bool

config ARCH_CHIP_TM4C129
	bool
	select TIVA_HAVE_I2C4
	select TIVA_HAVE_I2C5
	select TIVA_HAVE_I2C6
	select TIVA_HAVE_I2C7
	select TIVA_HAVE_I2C8
	select TIVA_HAVE_I2C9
	select TIVA_HAVE_TIMER6
	select TIVA_HAVE_TIMER7
	select TIVA_HAVE_UART1
	select TIVA_HAVE_UART2
	select TIVA_HAVE_UART3

config ARCH_CHIP_TM4C
	bool
	select ARCH_CORTEXM4
	select ARCH_HAVE_FPU
	select TIVA_HAVE_ADC0
	select TIVA_HAVE_ADC1
	select TIVA_HAVE_CAN0
	select TIVA_HAVE_I2C1
	select TIVA_HAVE_I2C2
	select TIVA_HAVE_I2C3
	select TIVA_HAVE_SSI0
	select TIVA_HAVE_SSI1
	select TIVA_HAVE_SSI2
	select TIVA_HAVE_SSI3
	select TIVA_HAVE_TIMER0
	select TIVA_HAVE_TIMER1
	select TIVA_HAVE_TIMER2
	select TIVA_HAVE_TIMER3
	select TIVA_HAVE_TIMER4
	select TIVA_HAVE_TIMER5
	select TIVA_HAVE_UART1
	select TIVA_HAVE_UART2
	select TIVA_HAVE_UART3
	select TIVA_HAVE_UART4
	select TIVA_HAVE_UART5
	select TIVA_HAVE_UART6
	select TIVA_HAVE_UART7

config ARCH_CHIP_CC13X0
	bool
	select ARCH_CORTEXM3
	select TIVA_HAVE_ADC0
	select TIVA_HAVE_SSI0
	select TIVA_HAVE_SSI1
	select TIVA_HAVE_TIMER0
	select TIVA_HAVE_TIMER1
	select TIVA_HAVE_TIMER2
	select TIVA_HAVE_TIMER3

config ARCH_CHIP_CC13X2
	bool
	select ARCH_CORTEXM4
	select ARCH_HAVE_FPU
	select TIVA_HAVE_ADC0
	select TIVA_HAVE_I2C1
	select TIVA_HAVE_SSI0
	select TIVA_HAVE_SSI1
	select TIVA_HAVE_TIMER0
	select TIVA_HAVE_TIMER1
	select TIVA_HAVE_TIMER2
	select TIVA_HAVE_TIMER3
	select TIVA_HAVE_UART1

config ARCH_CHIP_CC26X2
	bool

config LM_REVA2
	bool "Rev A2"
	default n
	depends on ARCH_CHIP_LM
	---help---
		Some early silicon returned an increase LDO voltage or 2.75V to work
		around a PLL bug

menu "Tiva/Stellaris Peripheral Support"

# MCU capabilities

config TIVA_ADC
	bool
	default n

config TIVA_HAVE_ADC0
	bool
	default n

config TIVA_HAVE_ADC1
	bool
	default n

config TIVA_CAN
	bool
	default n
	select ARCH_HAVE_CAN_ERRORS
	select CAN_TXREADY
	select CAN_USE_RTR

config TIVA_HAVE_CAN0
	bool
	default n

config TIVA_HAVE_CAN1
	bool
	default n

config TIVA_QEI
	bool
	default n

config TIVA_HAVE_QEI0
	bool
	default n

config TIVA_HAVE_QEI1
	bool
	default n

config TIVA_I2C
	bool
	default n

config TIVA_HAVE_I2C1
	bool
	default n

config TIVA_HAVE_I2C2
	bool
	default n

config TIVA_HAVE_I2C3
	bool
	default n

config TIVA_HAVE_I2C4
	bool
	default n

config TIVA_HAVE_I2C5
	bool
	default n

config TIVA_HAVE_I2C6
	bool
	default n

config TIVA_HAVE_I2C7
	bool
	default n

config TIVA_HAVE_I2C8
	bool
	default n

config TIVA_HAVE_I2C9
	bool
	default n

config TIVA_HAVE_UART1
	bool
	default n

config TIVA_HAVE_UART2
	bool
	default n

config TIVA_HAVE_UART3
	bool
	default n

config TIVA_HAVE_UART4
	bool
	default n

config TIVA_HAVE_UART5
	bool
	default n

config TIVA_HAVE_UART6
	bool
	default n

config TIVA_HAVE_UART7
	bool
	default n

config TIVA_HAVE_SSI0
	bool
	default n

config TIVA_HAVE_SSI1
	bool
	default n

config TIVA_HAVE_SSI2
	bool
	default n

config TIVA_HAVE_SSI3
	bool
	default n

config TIVA_HAVE_EEPROM
	bool
	default n

config TIVA_HAVE_ETHERNET
	bool
	default n

config TIVA_HAVE_TIMER0
	bool
	default n

config TIVA_HAVE_TIMER1
	bool
	default n

config TIVA_HAVE_TIMER2
	bool
	default n

config TIVA_HAVE_TIMER3
	bool
	default n

config TIVA_HAVE_TIMER4
	bool
	default n

config TIVA_HAVE_TIMER5
	bool
	default n

config TIVA_HAVE_TIMER6
	bool
	default n

config TIVA_HAVE_TIMER7
	bool
	default n

# Driver selections

config TIVA_SSI
	bool
	default n

config TIVA_TIMER
	bool
	default n

# Peripheral Selections

config TIVA_ADC0
	bool "ADC0"
	default n
	select TIVA_ADC

config TIVA_ADC1
	bool "ADC1"
	default n
	depends on TIVA_HAVE_ADC0
	select TIVA_ADC

config TIVA_CAN0
	bool "CAN0"
	default n
	depends on TIVA_HAVE_CAN0
	select CAN
	select TIVA_CAN

config TIVA_CAN0_PRIO
	int "CAN0 kthread priority"
	default 300
	depends on TIVA_CAN0
	---help---
		The Tiva CAN driver retrieves messages using a kthread rather
		than in the ISR or using a work queue. The ISR signals the
		kthread, but the kthread can be preempted if needed. This
		option sets the thread priority for CAN module 0.

config TIVA_CAN1
	bool "CAN1"
	default n
	depends on TIVA_HAVE_CAN1
	select CAN
	select TIVA_CAN

config TIVA_CAN1_PRIO
	int "CAN1 kthread priority"
	default 300
	depends on TIVA_CAN1
	---help---
		The Tiva CAN driver retrieves messages using a kthread rather
		than in the ISR or using a work queue. The ISR signals the
		kthread, but the kthread can be preempted if needed. This
		option sets the thread priority for CAN module 1.

config TIVA_QEI0
	bool "QEI0"
	default n
	depends on TIVA_HAVE_QEI0
	select TIVA_QEI

config TIVA_QEI1
	bool "QEI1"
	default n
	depends on TIVA_HAVE_QEI1
	select TIVA_QEI

config TIVA_I2C0
	bool "I2C0"
	default n
	select TIVA_I2C

config TIVA_I2C1
	bool "I2C1"
	default n
	select TIVA_I2C

config TIVA_I2C2
	bool "I2C2"
	default n
	depends on TIVA_HAVE_I2C2
	select TIVA_I2C

config TIVA_I2C3
	bool "I2C3"
	default n
	depends on TIVA_HAVE_I2C3
	select TIVA_I2C

config TIVA_I2C4
	bool "I2C4"
	default n
	depends on TIVA_HAVE_I2C4
	select TIVA_I2C

config TIVA_I2C5
	bool "I2C5"
	default n
	depends on TIVA_HAVE_I2C5
	select TIVA_I2C

config TIVA_I2C6
	bool "I2C6"
	default n
	depends on TIVA_HAVE_I2C6
	select TIVA_I2C

config TIVA_I2C7
	bool "I2C7"
	default n
	depends on TIVA_HAVE_I2C7
	select TIVA_I2C

config TIVA_I2C8
	bool "I2C8"
	default n
	depends on TIVA_HAVE_I2C8
	select TIVA_I2C

config TIVA_I2C9
	bool "I2C9"
	default n
	depends on TIVA_HAVE_I2C9
	select TIVA_I2C

config TIVA_HCIUART
	bool
	default n

choice
	prompt "UART0 Driver Configuration"
	default TIVA_UART0_NONE

config TIVA_UART0_NONE
	bool "Not selected"

config TIVA_UART0
	bool "Standard serial driver"
	select UART0_SERIALDRIVER

config TIVA_UART0_HCIUART
	bool "Bluetooth HCI-UART"
	select TIVA_HCIUART
	depends on WIRELESS_BLUETOOTH

endchoice # UART0 Driver Configuration

config TIVA_CC26X2_POWERLIB
	bool "CC26x2 Power Library"
	default n
	depends on ARCH_CHIP_CC26X2

if TIVA_UART0_HCIUART
config TIVA_HCIUART0_RXBUFSIZE
	int "HCI UART0 Rx buffer size"
	default 80
	---help---
		Characters are buffered as they are received. This specifies
		the size of the receive buffer.  Ideally this should be at least
		the size of the largest frame that can be received

config TIVA_HCIUART0_TXBUFSIZE
	int "HCI UART0 Transmit buffer size"
	default 80
	---help---
		Characters are buffered before being sent.  This specifies
		the size of the transmit buffer.  Ideally this should be at least
		the size of the largest frame that can be sent

config TIVA_HCIUART0_BAUD
	int "HCI UART0 initial BAUD rate"
	default 115200
	---help---
		The configured initial BAUD of the HCI UART used during bringup.
		In most cases this initial rate can be increased by the upper half
		HCI UART driver using vendor-specifi HCI UART commands.

endif # TIVA_UART0_HCIUART

choice
	prompt "UART1 Driver Configuration"
	default TIVA_UART1_NONE

config TIVA_UART1_NONE
	bool "Not selected"

config TIVA_UART1
	bool "Standard serial driver"
	select UART1_SERIALDRIVER

config TIVA_UART1_HCIUART
	bool "Bluetooth HCI-UART"
	select TIVA_HCIUART
	depends on WIRELESS_BLUETOOTH

endchoice # UART1 Driver Configuration

if TIVA_UART1_HCIUART
config TIVA_HCIUART1_RXBUFSIZE
	int "HCI UART1 Rx buffer size"
	default 80
	---help---
		Characters are buffered as they are received. This specifies
		the size of the receive buffer.  Ideally this should be at least
		the size of the largest frame that can be received

config TIVA_HCIUART1_TXBUFSIZE
	int "HCI UART1 Transmit buffer size"
	default 80
	---help---
		Characters are buffered before being sent.  This specifies
		the size of the transmit buffer.  Ideally this should be at least
		the size of the largest frame that can be sent

config TIVA_HCIUART1_BAUD
	int "HCI UART1 initial BAUD rate"
	default 115200
	---help---
		The configured initial BAUD of the HCI UART used during bringup.
		In most cases this initial rate can be increased by the upper half
		HCI UART driver using vendor-specifi HCI UART commands.

endif # TIVA_UART1_HCIUART

choice
	prompt "UART2 Driver Configuration"
	default TIVA_UART2_NONE

config TIVA_UART2_NONE
	bool "Not selected"

config TIVA_UART2
	bool "Standard serial driver"
	select UART2_SERIALDRIVER

config TIVA_UART2_HCIUART
	bool "Bluetooth HCI-UART"
	select TIVA_HCIUART
	depends on WIRELESS_BLUETOOTH

endchoice # UART2 Driver Configuration

if TIVA_UART2_HCIUART
config TIVA_HCIUART2_RXBUFSIZE
	int "HCI UART2 Rx buffer size"
	default 80
	---help---
		Characters are buffered as they are received. This specifies
		the size of the receive buffer.  Ideally this should be at least
		the size of the largest frame that can be received

config TIVA_HCIUART2_TXBUFSIZE
	int "HCI UART2 Transmit buffer size"
	default 80
	---help---
		Characters are buffered before being sent.  This specifies
		the size of the transmit buffer.  Ideally this should be at least
		the size of the largest frame that can be sent

config TIVA_HCIUART2_BAUD
	int "HCI UART2 initial BAUD rate"
	default 115200
	---help---
		The configured initial BAUD of the HCI UART used during bringup.
		In most cases this initial rate can be increased by the upper half
		HCI UART driver using vendor-specifi HCI UART commands.

endif # TIVA_UART2_HCIUART

choice
	prompt "UART3 Driver Configuration"
	default TIVA_UART3_NONE

config TIVA_UART3_NONE
	bool "Not selected"

config TIVA_UART3
	bool "Standard serial driver"
	select UART3_SERIALDRIVER

config TIVA_UART3_HCIUART
	bool "Bluetooth HCI-UART"
	select TIVA_HCIUART
	depends on WIRELESS_BLUETOOTH

endchoice # UART3 Driver Configuration

if TIVA_UART3_HCIUART
config TIVA_HCIUART3_RXBUFSIZE
	int "HCI UART3 Rx buffer size"
	default 80
	---help---
		Characters are buffered as they are received. This specifies
		the size of the receive buffer.  Ideally this should be at least
		the size of the largest frame that can be received

config TIVA_HCIUART3_TXBUFSIZE
	int "HCI UART3 Transmit buffer size"
	default 80
	---help---
		Characters are buffered before being sent.  This specifies
		the size of the transmit buffer.  Ideally this should be at least
		the size of the largest frame that can be sent

config TIVA_HCIUART3_BAUD
	int "HCI UART3 initial BAUD rate"
	default 115200
	---help---
		The configured initial BAUD of the HCI UART used during bringup.
		In most cases this initial rate can be increased by the upper half
		HCI UART driver using vendor-specifi HCI UART commands.

endif # TIVA_UART3_HCIUART

choice
	prompt "UART4 Driver Configuration"
	default TIVA_UART4_NONE

config TIVA_UART4_NONE
	bool "Not selected"

config TIVA_UART4
	bool "Standard serial driver"
	select UART4_SERIALDRIVER

config TIVA_UART4_HCIUART
	bool "Bluetooth HCI-UART"
	select TIVA_HCIUART
	depends on WIRELESS_BLUETOOTH

endchoice # UART4 Driver Configuration

if TIVA_UART4_HCIUART
config TIVA_HCIUART4_RXBUFSIZE
	int "HCI UART4 Rx buffer size"
	default 80
	---help---
		Characters are buffered as they are received. This specifies
		the size of the receive buffer.  Ideally this should be at least
		the size of the largest frame that can be received

config TIVA_HCIUART4_TXBUFSIZE
	int "HCI UART4 Transmit buffer size"
	default 80
	---help---
		Characters are buffered before being sent.  This specifies
		the size of the transmit buffer.  Ideally this should be at least
		the size of the largest frame that can be sent

config TIVA_HCIUART4_BAUD
	int "HCI UART4 initial BAUD rate"
	default 115200
	---help---
		The configured initial BAUD of the HCI UART used during bringup.
		In most cases this initial rate can be increased by the upper half
		HCI UART driver using vendor-specifi HCI UART commands.

endif # TIVA_UART4_HCIUART

choice
	prompt "UART5 Driver Configuration"
	default TIVA_UART5_NONE

config TIVA_UART5_NONE
	bool "Not selected"

config TIVA_UART5
	bool "Standard serial driver"
	select UART5_SERIALDRIVER

config TIVA_UART5_HCIUART
	bool "Bluetooth HCI-UART"
	select TIVA_HCIUART
	depends on WIRELESS_BLUETOOTH

endchoice # UART5 Driver Configuration

if TIVA_UART5_HCIUART
config TIVA_HCIUART5_RXBUFSIZE
	int "HCI UART5 Rx buffer size"
	default 80
	---help---
		Characters are buffered as they are received. This specifies
		the size of the receive buffer.  Ideally this should be at least
		the size of the largest frame that can be received

config TIVA_HCIUART5_TXBUFSIZE
	int "HCI UART5 Transmit buffer size"
	default 80
	---help---
		Characters are buffered before being sent.  This specifies
		the size of the transmit buffer.  Ideally this should be at least
		the size of the largest frame that can be sent

config TIVA_HCIUART5_BAUD
	int "HCI UART5 initial BAUD rate"
	default 115200
	---help---
		The configured initial BAUD of the HCI UART used during bringup.
		In most cases this initial rate can be increased by the upper half
		HCI UART driver using vendor-specifi HCI UART commands.

endif # TIVA_UART5_HCIUART

choice
	prompt "UART6 Driver Configuration"
	default TIVA_UART6_NONE

config TIVA_UART6_NONE
	bool "Not selected"

config TIVA_UART6
	bool "Standard serial driver"
	select UART6_SERIALDRIVER

config TIVA_UART6_HCIUART
	bool "Bluetooth HCI-UART"
	select TIVA_HCIUART
	depends on WIRELESS_BLUETOOTH

endchoice # UART6 Driver Configuration

if TIVA_UART6_HCIUART
config TIVA_HCIUART6_RXBUFSIZE
	int "HCI UART6 Rx buffer size"
	default 80
	---help---
		Characters are buffered as they are received. This specifies
		the size of the receive buffer.  Ideally this should be at least
		the size of the largest frame that can be received

config TIVA_HCIUART6_TXBUFSIZE
	int "HCI UART6 Transmit buffer size"
	default 80
	---help---
		Characters are buffered before being sent.  This specifies
		the size of the transmit buffer.  Ideally this should be at least
		the size of the largest frame that can be sent

config TIVA_HCIUART6_BAUD
	int "HCI UART6 initial BAUD rate"
	default 115200
	---help---
		The configured initial BAUD of the HCI UART used during bringup.
		In most cases this initial rate can be increased by the upper half
		HCI UART driver using vendor-specifi HCI UART commands.

endif # TIVA_UART6_HCIUART

choice
	prompt "UART7 Driver Configuration"
	default TIVA_UART7_NONE

config TIVA_UART7_NONE
	bool "Not selected"

config TIVA_UART7
	bool "Standard serial driver"
	select UART7_SERIALDRIVER

config TIVA_UART7_HCIUART
	bool "Bluetooth HCI-UART"
	select TIVA_HCIUART
	depends on WIRELESS_BLUETOOTH

endchoice # UART7 Driver Configuration

if TIVA_UART7_HCIUART
config TIVA_HCIUART7_RXBUFSIZE
	int "HCI UART7 Rx buffer size"
	default 80
	---help---
		Characters are buffered as they are received. This specifies
		the size of the receive buffer.  Ideally this should be at least
		the size of the largest frame that can be received

config TIVA_HCIUART7_TXBUFSIZE
	int "HCI UART7 Transmit buffer size"
	default 80
	---help---
		Characters are buffered before being sent.  This specifies
		the size of the transmit buffer.  Ideally this should be at least
		the size of the largest frame that can be sent

config TIVA_HCIUART7_BAUD
	int "HCI UART7 initial BAUD rate"
	default 115200
	---help---
		The configured initial BAUD of the HCI UART used during bringup.
		In most cases this initial rate can be increased by the upper half
		HCI UART driver using vendor-specifi HCI UART commands.

endif # TIVA_UART7_HCIUART

menu "HCI UART Driver Configuration"
	depends on TIVA_HCIUART

config TIVA_HCIUART_SW_RXFLOW
	bool "Use Software UART RTS flow control"
	default n
	---help---
		Enable UART RTS flow control using Software.

config TIVA_HCIUART_SW_TXFLOW
	bool "Use Software UART CTS flow control"
	default n
	---help---
		Enable UART CTS flow control using Software.

endmenu # HCI UART Driver Configuration

config TIVA_SSI0
	bool "SSI0"
	default n
	depends on TIVA_HAVE_SSI0
	select TIVA_SSI

config TIVA_SSI1
	bool "SSI1"
	default n
	depends on TIVA_HAVE_SSI1
	select TIVA_SSI

config TIVA_SSI2
	bool "SSI2"
	default n
	depends on TIVA_HAVE_SSI2
	select TIVA_SSI

config TIVA_SSI3
	bool "SSI3"
	default n
	depends on TIVA_HAVE_SSI3
	select TIVA_SSI

config TIVA_TIMER0
	bool "16-/32-bit Timer 0"
	default n
	depends on TIVA_HAVE_TIMER0
	select TIVA_TIMER

config TIVA_TIMER1
	bool "16-/32-bit Timer 1"
	default n
	depends on TIVA_HAVE_TIMER1
	select TIVA_TIMER

config TIVA_TIMER2
	bool "16-/32-bit Timer 2"
	default n
	depends on TIVA_HAVE_TIMER2
	select TIVA_TIMER

config TIVA_TIMER3
	bool "16-/32-bit Timer 3"
	default n
	depends on TIVA_HAVE_TIMER3
	select TIVA_TIMER

config TIVA_TIMER4
	bool "16-/32-bit Timer 4"
	default n
	depends on TIVA_HAVE_TIMER4
	select TIVA_TIMER

config TIVA_TIMER5
	bool "16-/32-bit Timer 5"
	default n
	depends on TIVA_HAVE_TIMER5
	select TIVA_TIMER

config TIVA_TIMER6
	bool "16-/32-bit Timer 6"
	default n
	depends on TIVA_HAVE_TIMER6
	select TIVA_TIMER

config TIVA_TIMER7
	bool "16-/32-bit Timer 7"
	default n
	depends on TIVA_HAVE_TIMER7
	select TIVA_TIMER

config TIVA_ETHERNET
	bool "Ethernet"
	default n
	depends on TIVA_HAVE_ETHERNET
	select NETDEVICES
	select ARCH_HAVE_PHY
	select ARCH_HAVE_NETDEV_STATISTICS if ARCH_CHIP_LM3S || ARCH_CHIP_LM4F
	---help---
		This must be set (along with NET) to build the Stellaris Ethernet driver.

config TIVA_FLASH
	bool "Internal FLASH driver"
	default n
	---help---
		Enable MTD driver support for internal FLASH.

config TIVA_EEPROM
	bool "Internal EEPROM driver"
	default n
	depends on TIVA_HAVE_EEPROM && EXPERIMENTAL
	---help---
		Enable MTD driver support for internal EEPROM.

endmenu

config TIVA_RAMVBAR
	bool "Set VBAR"
	default n
	---help---
		Set the ARM VBAR register to position interrupt vectors at the
		beginning of RAM (vs the beginning of FLASH).  The beginning of RAM
		is that address defined by CONFIG_RAM_START.

menu "Enable GPIO Interrupts"

config TIVA_GPIO_IRQS
	bool
	default n

config TIVA_HAVE_GPIOA_IRQS
	bool
	default n

config TIVA_HAVE_GPIOB_IRQS
	bool
	default n

config TIVA_HAVE_GPIOC_IRQS
	bool
	default n

config TIVA_HAVE_GPIOD_IRQS
	bool
	default n

config TIVA_HAVE_GPIOE_IRQS
	bool
	default n

config TIVA_HAVE_GPIOF_IRQS
	bool
	default n

config TIVA_HAVE_GPIOG_IRQS
	bool
	default n

config TIVA_HAVE_GPIOH_IRQS
	bool
	default n

config TIVA_HAVE_GPIOJ_IRQS
	bool
	default n

config TIVA_HAVE_GPIOK_IRQS
	bool
	default n

config TIVA_HAVE_GPIOL_IRQS
	bool
	default n

config TIVA_HAVE_GPIOM_IRQS
	bool
	default n

config TIVA_HAVE_GPION_IRQS
	bool
	default n

config TIVA_HAVE_GPIOP_IRQS
	bool
	default n

config TIVA_HAVE_GPIOQ_IRQS
	bool
	default n

config TIVA_HAVE_GPIOR_IRQS
	bool
	default n

config TIVA_HAVE_GPIOS_IRQS
	bool
	default n

config TIVA_HAVE_GPIOT_IRQS
	bool
	default n

config TIVA_GPIOA_IRQS
	bool "Enable GPIOA IRQs"
	default n
	depends on TIVA_HAVE_GPIOA_IRQS
	select TIVA_GPIO_IRQS

config TIVA_GPIOB_IRQS
	bool "Enable GPIOB IRQs"
	default n
	depends on TIVA_HAVE_GPIOB_IRQS
	select TIVA_GPIO_IRQS

config TIVA_GPIOC_IRQS
	bool "Enable GPIOC IRQs"
	default n
	depends on TIVA_HAVE_GPIOC_IRQS
	select TIVA_GPIO_IRQS

config TIVA_GPIOD_IRQS
	bool "Enable GPIOD IRQs"
	default n
	depends on TIVA_HAVE_GPIOD_IRQS
	select TIVA_GPIO_IRQS

config TIVA_GPIOE_IRQS
	bool "Enable GPIOE IRQs"
	default n
	depends on TIVA_HAVE_GPIOE_IRQS
	select TIVA_GPIO_IRQS

config TIVA_GPIOF_IRQS
	bool "Enable GPIOF IRQs"
	default n
	depends on TIVA_HAVE_GPIOF_IRQS
	select TIVA_GPIO_IRQS

config TIVA_GPIOG_IRQS
	bool "Enable GPIOG IRQs"
	default n
	depends on TIVA_HAVE_GPIOG_IRQS
	select TIVA_GPIO_IRQS

config TIVA_GPIOH_IRQS
	bool "Enable GPIOH IRQs"
	default n
	depends on TIVA_HAVE_GPIOH_IRQS
	select TIVA_GPIO_IRQS

config TIVA_GPIOJ_IRQS
	bool "Enable GPIOJ IRQs"
	default n
	depends on TIVA_HAVE_GPIOJ_IRQS
	select TIVA_GPIO_IRQS

config TIVA_GPIOK_IRQS
	bool "Enable GPIOK IRQs"
	default n
	depends on TIVA_HAVE_GPIOK_IRQS
	select TIVA_GPIO_IRQS

config TIVA_GPIOL_IRQS
	bool "Enable GPIOL IRQs"
	default n
	depends on TIVA_HAVE_GPIOL_IRQS
	select TIVA_GPIO_IRQS

config TIVA_GPIOM_IRQS
	bool "Enable GPIOM IRQs"
	default n
	depends on TIVA_HAVE_GPIOM_IRQS
	select TIVA_GPIO_IRQS

config TIVA_GPION_IRQS
	bool "Enable GPION IRQs"
	default n
	depends on TIVA_HAVE_GPION_IRQS
	select TIVA_GPIO_IRQS

config TIVA_GPIOP_IRQS
	bool "Enable GPIOP IRQs"
	default n
	depends on TIVA_HAVE_GPIOP_IRQS
	select TIVA_GPIO_IRQS

config TIVA_GPIOQ_IRQS
	bool "Enable GPIOQ IRQs"
	default n
	depends on TIVA_HAVE_GPIOQ_IRQS
	select TIVA_GPIO_IRQS

config TIVA_GPIOR_IRQS
	bool "Enable GPIOR IRQs"
	default n
	depends on TIVA_HAVE_GPIOR_IRQS
	select TIVA_GPIO_IRQS

config TIVA_GPIOS_IRQS
	bool "Enable GPIOS IRQs"
	default n
	depends on TIVA_HAVE_GPIOS_IRQS
	select TIVA_GPIO_IRQS

config TIVA_GPIOT_IRQS
	bool "Enable GPIOT IRQs"
	default n
	depends on TIVA_HAVE_GPIOT_IRQS
	select TIVA_GPIO_IRQS

endmenu

if TIVA_I2C

menu "I2C Configuration"

config TIVA_I2C_DYNTIMEO
	bool "Use dynamic timeouts"
	default n

if TIVA_I2C_DYNTIMEO

config TIVA_I2C_DYNTIMEO_USECPERBYTE
	int "Timeout Microseconds per Byte"
	default 500

config TIVA_I2C_DYNTIMEO_STARTSTOP
	int "Timeout for Start/Stop (Milliseconds)"
	default 1000

endif # TIVA_I2C_DYNTIMEO

config TIVA_I2C_TIMEOSEC
	int "Timeout seconds"
	default 0

if !TIVA_I2C_DYNTIMEO

config TIVA_I2C_TIMEOMS
	int "Timeout Milliseconds"
	default 500
	depends on !TIVA_I2C_DYNTIMEO

config TIVA_I2C_TIMEOTICKS
	int "Timeout for Done and Stop (ticks)"
	default 500
	depends on !TIVA_I2C_DYNTIMEO

endif # !TIVA_I2C_DYNTIMEO

config TIVA_I2C_HIGHSPEED
	bool "High speed support"
	default n
	depends on ARCH_CHIP_TM4C && EXPERIMENTAL
	---help---
		Enable support for high speed I2C transfers.
		Only partially implemented and completely untested.

config TIVA_I2C_REGDEBUG
	bool "Register level debug"
	default n
	depends on DEBUG_I2C_INFO
	---help---
		Enables extremely detailed register access debug output.

endmenu # I2C Configuration
endif # TIVA_I2C

if TIVA_TIMER

menu "Timer Configuration"

config TIVA_TIMER_32BIT
	bool "32-bit timer support"
	default n

if TIVA_TIMER_32BIT

config TIVA_TIMER32_PERIODIC
	bool "32-bit one-shot/periodic timer support"
	default n

config TIVA_TIMER32_RTC
	bool "32-bit RTC (needs 32.768-KHz input)"
	default n

endif # TIVA_TIMER_32BIT

config TIVA_TIMER_16BIT
	bool "16-bit timer support"
	default n

if TIVA_TIMER_16BIT

config TIVA_TIMER16_PERIODIC
	bool "16-bit one-shot/periodic timer support"
	default n

config TIVA_TIMER16_EDGECOUNT
	bool "16-bit input edge-count capture support"
	default n
	depends on EXPERIMENTAL

config TIVA_TIMER16_TIMECAP
	bool "16-bit input time capture support"
	default n
	depends on EXPERIMENTAL

config TIVA_TIMER16_PWM
	bool "16-bit PWM output support"
	default n
	---help---
		Enables 24-bit PWM mode using a 16-bit half-timer of a
		Tiva 16/32-bit General Purpose Timer Module (GPTM) block.
		The 24-bit resolution is achieved by using the prescaler
		as the high 8 bits.

endif # TIVA_TIMER_16BIT

config TIVA_TIMER_REGDEBUG
	bool "Register level debug"
	default n
	depends on DEBUG_TIMER_INFO
	---help---
		Enables extremely detailed register access debug output.

endmenu # Tiva Timer Configuration
endif # TIVA_TIMER

if TIVA_ADC
menu "ADC Configuration"

config TIVA_ADC_REGDEBUG
	bool "Register level debug"
	default n
	depends on DEBUG_ANALOG_INFO
	---help---
		Enables extremely detailed register access debug output.

endmenu # Tiva ADC Configuration
endif # TIVA_ADC

menu "CAN Driver Configuration"
	depends on TIVA_CAN

config TIVA_CAN_REGDEBUG
	bool "CAN register level debug"
	depends on  DEBUG_CAN_INFO
	default n
	---help---
		Output detailed register-level CAN device debug information.
		Requires also CONFIG_DEBUG_CAN_INFO.

config TIVA_CAN_TX_FIFO_DEPTH
	int "Size of the hardware TX FIFO"
	range 1 31
	default 8
	---help---
		This number determines the depth of the hardware transmit
		FIFO, which cannot be resized. Using a transmit FIFO allows
		the application to transmit messages at maximum speed and
		reduces jitter caused by excessive interrupts. However, it
		occupies extra mailboxes that might be used for more fine-
		grained filtering; hence there is a trade-off.

		Because the mailboxes on the Tiva chips cannot be used as
		a ring buffer for outbound messages, a larger TX FIFO
		directly increases the maximum number of messages that
		can be sent in a "burst" at maximum speed.

		However, the TX FIFO makes implementation of
		"pre-arbitration" more difficult. Although the classic
		character device CAN model does not support this anyway,
		transmission of a higher-priority message would require
		cancelling and reenqueuing of the low-priority messages
		already in the hardware.

config TIVA_CAN_DEFAULT_FIFO_DEPTH
	int "Default CAN message RX FIFO depth"
	range 1 31
	default 6
	---help---
		This number determines the default depth for all RX
		hardware FIFOs in the CAN module. The size of a FIFO can be
		adjusted at any time (and made deeper than this number if
		needed) via the various CANIOC_TIVA ioctls. However, this
		option is limited to 31 because the driver must allocate
		at least one mailbox for outbound (TX) messages. The default
		RX filter FIFO is set to allow any messages, but is
		reconfigured as soon as an application uses the
		CANIOC_ADD_STDFILTER or CANIOC_ADD_EXTFILTER.

config TIVA_CAN_FILTERS_MAX
	int "Maximum number of CAN RX filters"
	range 1 31
	default 4
	---help---
		This number applies to both CAN0 and CAN1, if the chip has
		both. (This is the number of filters for each module, not the
		total for both CAN modules.)
		The Tiva CAN modules have 32 message objects. Only one is
		used for TX, so it is possible to have up to 31 unique
		RX filters. However, increasing the FIFO depth of a filter
		uses up message objects and reduces this number. Additionally,
		since the driver has to keep track of the message objects
		assigned to each filter, increasing the number of available
		filters increases the memory footprint even if the filters
		are not used (internally, it is a statically-allocated array).

config TIVA_CAN_ERR_HANDLER_PER
	int "Rate-limited error handling period (milliseconds)"
	range 10 1000
	default 100
	depends on CAN_ERRORS
	---help---
		When error messages (CAN_ERRORS) are enabled, the Tiva
		CAN driver will disable interrupts for individual errors
		when the application fails to call read() quickly enough
		to keep up. This can easily happen during testing if no
		transceiver is connected and the application is trying
		to print error messages to a serial console.

		When this happens, the driver defers error reporting to
		a periodic task in the high-priority work queue. The task
		checks for errors and reports them at the specified
		interval. It is unlikely that the bus would be disturbed
		badly enough to cause interrupt overload and yet be usable
		for real-time communication, so successful transmission or
		reception of a message will return the driver to the
		normal interrupt handling mode.

endmenu

config TIVA_WITH_QEMU
	bool "Run with qemu"
	default n

if TIVA_ETHERNET

menu "Stellaris Ethernet Configuration"
	depends on ARCH_CHIP_LM3S

config TIVA_ETHLEDS
	bool "Ethernet LEDs"
	default n
	---help---
	Enable to use Ethernet LEDs on the board.

config TIVA_ETHHDUPLEX
	bool "Force Half Duplex"
	default n
	---help---
		Set to force half duplex operation

config TIVA_ETHNOAUTOCRC
	bool "Disable auto-CRC"
	default n
	---help---
		Set to suppress auto-CRC generation

config TIVA_ETHNOPAD
	bool "Disable Tx Padding"
	default n
	---help---
		Set to suppress Tx padding

config TIVA_MULTICAST
	bool "Enable Multicast"
	default n
	---help---
		Set to enable multicast frames

config TIVA_PROMISCUOUS
	bool "Enable Promiscuous Mode"
	default n
	---help---
		Set to enable promiscuous mode

config TIVA_TIMESTAMP
	bool "Enable Timestamping"
	default n

config TIVA_BADCRC
	bool "Enable Bad CRC Rejection"
	default n
	---help---
		Set to enable bad CRC rejection.

config TIVA_DUMPPACKET
	bool "Dump Packets"
	default n
	---help---
		Dump each packet received/sent to the console.

endmenu # Stellaris Ethernet Configuration

menu "Ethernet Configuration"
	depends on ARCH_CHIP_TM4C

choice
	prompt "PHY selection"
	default TIVA_PHY_INTERNAL

config TIVA_PHY_INTERNAL
	bool "Internal PHY"
	---help---
		Use the built-in, internal Tiva PHY

config TIVA_PHY_MII
	bool "External MII interface"
	depends on EXPERIMENTAL
	---help---
		Support external PHY MII interface.

config TIVA_PHY_RMII
	bool "External RMII interface"
	depends on EXPERIMENTAL
	---help---
		Support external PHY RMII interface.

endchoice # PHY selection

config TIVA_AUTONEG
	bool "Use autonegotiation"
	default y
	---help---
		Use PHY autonegotiation to determine speed and mode

if !TIVA_PHY_INTERNAL

config TIVA_PHYADDR
	int "PHY address"
	default 1
	---help---
		The 5-bit address of the PHY on the board.  Default: 1

if !TIVA_AUTONEG
config TIVA_ETHFD
	bool "Full duplex"
	default n
	---help---
		If TIVA_AUTONEG is not defined, then this may be defined to select full duplex
		mode. Default: half-duplex

config TIVA_ETH100MBPS
	bool "100 Mbps"
	default n
	---help---
		If TIVA_AUTONEG is not defined, then this may be defined to select 100 MBps
		speed.  Default: 10 Mbps

endif # !TIVA_AUTONEG
if TIVA_AUTONEG

config TIVA_PHYSR
	int "PHY Status Register Address (decimal)"
	---help---
		This must be provided if TIVA_AUTONEG is defined.  The PHY status register
		address may diff from PHY to PHY.  This configuration sets the address of
		the PHY status register.

config TIVA_PHYSR_ALTCONFIG
	bool "PHY Status Alternate Bit Layout"
	default n
	---help---
		Different PHYs present speed and mode information in different ways.  Some
		will present separate information for speed and mode (this is the default).
		Those PHYs, for example, may provide a 10/100 Mbps indication and a separate
		full/half duplex indication. This options selects an alternative representation
		where speed and mode information are combined.  This might mean, for example,
		separate bits for 10HD, 100HD, 10FD and 100FD.

if !TIVA_PHYSR_ALTCONFIG

config TIVA_PHYSR_SPEED
	hex "PHY Speed Mask"
	---help---
		This must be provided if TIVA_AUTONEG is defined.  This provides bit mask
		for isolating the 10 or 100MBps speed indication.

config TIVA_PHYSR_100MBPS
	hex "PHY 100Mbps Speed Value"
	---help---
		This must be provided if TIVA_AUTONEG is defined.  This provides the value
		of the speed bit(s) indicating 100MBps speed.

config TIVA_PHYSR_MODE
	hex "PHY Mode Mask"
	---help---
		This must be provided if TIVA_AUTONEG is defined.  This provide bit mask
		for isolating the full or half duplex mode bits.

config TIVA_PHYSR_FULLDUPLEX
	hex "PHY Full Duplex Mode Value"
	---help---
		This must be provided if TIVA_AUTONEG is defined.  This provides the
		value of the mode bits indicating full duplex mode.

endif # !TIVA_PHYSR_ALTCONFIG
if TIVA_PHYSR_ALTCONFIG

config TIVA_PHYSR_ALTMODE
	hex "PHY Mode Mask"
	---help---
		This must be provided if TIVA_AUTONEG is defined.  This provide bit mask
		for isolating the speed and full/half duplex mode bits.

config TIVA_PHYSR_10HD
	hex "10MBase-T Half Duplex Value"
	---help---
		This must be provided if TIVA_AUTONEG is defined.  This is the value
		under the bit mask that represents the 10Mbps, half duplex setting.

config TIVA_PHYSR_100HD
	hex "100Base-T Half Duplex Value"
	---help---
		This must be provided if TIVA_AUTONEG is defined.  This is the value
		under the bit mask that represents the 100Mbps, half duplex setting.

config TIVA_PHYSR_10FD
	hex "10Base-T Full Duplex Value"
	---help---
		This must be provided if TIVA_AUTONEG is defined.  This is the value
		under the bit mask that represents the 10Mbps, full duplex setting.

config TIVA_PHYSR_100FD
	hex "100Base-T Full Duplex Value"
	---help---
		This must be provided if TIVA_AUTONEG is defined.  This is the value
		under the bit mask that represents the 100Mbps, full duplex setting.

endif # TIVA_PHYSR_ALTCONFIG
endif # TIVA_AUTONEG
endif # !TIVA_PHY_INTERNAL

config TIVA_PHY_INTERRUPTS
	bool "PHY interrupt support"
	select ARCH_PHY_INTERRUPT
	select NETDEV_PHY_IOCTL
	default n
	---help---
		Enable logic to signal user tasks when a PHY interrupt occurs.  The
		PHY interrupt may indicate a change in the link status such as, for
		example, when a cable is plugged in or unplugged.

config TIVA_EMAC_NRXDESC
	int "Number of RX descriptors"
	default 8
	---help---
		Number of RX DMA descriptors to use.

config TIVA_EMAC_NTXDESC
	int "Number of TX descriptors"
	default 4
	---help---
		Number of TX DMA descriptors to use.

config TIVA_EMAC_ENHANCEDDESC
	bool
	default n

config TIVA_EMAC_PTP
	bool "Precision Time Protocol (PTP)"
	default n
	depends on EXPERIMENTAL
	select TIVA_EMAC_ENHANCEDDESC
	---help---
		Precision Time Protocol (PTP).  Not supported but some hooks are indicated
		with this condition.

config TIVA_EMAC_HWCHECKSUM
	bool "Use hardware checksums"
	default n
	depends on EXPERIMENTAL
	---help---
		Use the hardware checksum capabilities of the Tiva chip

config TIVA_ETHERNET_REGDEBUG
	bool "Register-Level Debug"
	default n
	depends on DEBUG_NET_INFO
	---help---
		Enable very low-level register access debug.  Depends on CONFIG_DEBUG_FEATURES.

endmenu # Tiva Ethernet Configuration

config TIVA_BOARDMAC
	bool "Board MAC"
	default n
	---help---
		If the board-specific logic can provide a MAC address (via
		tiva_ethernetmac()), then this should be selected.

endif # TIVA_ETHERNET

if TIVA_SSI
menu "Tiva/Stellaris SSI Configuration"

config SSI_POLLWAIT
	bool "Poll Wait (No-Interrupt) Mode"
	default y

config SSI_TXLIMIT
	int "Tx Limit"
	default 4
	---help---
		Default of 4 assumes half of the 8 entry FIFO

endmenu
endif

if TIVA_FLASH
menu "Tiva/Stellaris Internal Flash Driver Configuration"

config TIVA_FLASH_STARTPAGE
	int "First page accessible by the MTD driver"
	default 250
	---help---
		To prevent accessing FLASH sections where code is stored.

endmenu
endif
