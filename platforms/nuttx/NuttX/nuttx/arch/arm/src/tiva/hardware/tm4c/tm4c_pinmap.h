/****************************************************************************
 * arch/arm/src/tiva/hardware/tm4c/tm4c_pinmap.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_TIVA_HARDWARE_TM4C_TM4C_PINMAP_H
#define __ARCH_ARM_SRC_TIVA_HARDWARE_TM4C_TM4C_PINMAP_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Alternate Pin Functions.
 * All members of the TM4C family share the same pin multiplexing
 * (although they may differ in the pins physically available).
 *
 * Alternative pin selections are provided with a numeric suffix like _1, _2,
 * etc. Drivers, however, will use the pin selection without the numeric
 * suffix. Additional definitions are required in the board.h file.
 * For example, if CAN1_RX connects via PN0 on some board, then the following
 * definitions should appear in the board.h header file for that board:
 *
 * #define GPIO_CAN1_RX GPIO_CAN1_RX_1
 *
 * The driver will then automatically configure PA11 as the CAN1 RX pin.
 */

/* WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific GPIO options such as
 * frequency, open-drain/push-pull, and pull-up/down!  Just the basics are
 * defined for most pins in this file.
 */

#if defined(CONFIG_ARCH_CHIP_TM4C123GH6ZRB)

#  define GPIO_ADC_AIN0        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_3)
#  define GPIO_ADC_AIN1        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_2)
#  define GPIO_ADC_AIN2        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_1)
#  define GPIO_ADC_AIN3        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_0)
#  define GPIO_ADC_AIN4        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_ADC_AIN5        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_ADC_AIN6        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_ADC_AIN7        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_ADC_AIN8        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_ADC_AIN9        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_ADC_AIN10       (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_ADC_AIN11       (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_ADC_AIN12       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_ADC_AIN13       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_ADC_AIN14       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_ADC_AIN15       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_ADC_AIN16       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_ADC_AIN17       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_ADC_AIN18       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_ADC_AIN19       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_3)
#  define GPIO_ADC_AIN20       (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_7)
#  define GPIO_ADC_AIN21       (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_6)
#  define GPIO_ADC_AIN22       (GPIO_FUNC_ANINPUT                | GPIO_PORTN | GPIO_PIN_1)
#  define GPIO_ADC_AIN23       (GPIO_FUNC_ANINPUT                | GPIO_PORTN | GPIO_PIN_0)

#  define GPIO_CAN0_RX_1       (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_0)
#  define GPIO_CAN0_RX_2       (GPIO_FUNC_PFINPUT  | GPIO_ALT_3  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_CAN0_RX_3       (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_CAN0_RX_4       (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_CAN0_TX_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_1)
#  define GPIO_CAN0_TX_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_CAN0_TX_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_CAN0_TX_4       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_CAN1_RX_1       (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_CAN1_RX_2       (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTE | GPIO_PIN_6)
#  define GPIO_CAN1_TX_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_CAN1_TX_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTE | GPIO_PIN_7)

#  define GPIO_CMP0_NIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_CMP0_OUT_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_CMP0_OUT_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_9  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_CMP0_PIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_CMP1_NIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_CMP1_OUT_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTK | GPIO_PIN_5)
#  define GPIO_CMP1_OUT_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_9  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_CMP1_PIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_CMP2_NIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTJ | GPIO_PIN_5)
#  define GPIO_CMP2_OUT_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_CMP2_OUT_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_9  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_CMP2_PIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTJ | GPIO_PIN_4)

#  define GPIO_HIB_RTCCLK      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTK | GPIO_PIN_4)

#  define GPIO_I2C0_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_I2C0_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C1_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_I2C1_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_I2C1_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C1_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_I2C2_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTF | GPIO_PIN_6)
#  define GPIO_I2C2_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTE | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTF | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C3_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_I2C3_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_I2C3_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C3_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C4_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_I2C4_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C4_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C5_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_6)
#  define GPIO_I2C5_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_6)
#  define GPIO_I2C5_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C5_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)

#  define GPIO_M0_PWM0_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_0)
#  define GPIO_M0_PWM0_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTB | GPIO_PIN_6)
#  define GPIO_M0_PWM0_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_M0_PWM1_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_1)
#  define GPIO_M0_PWM1_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTB | GPIO_PIN_7)
#  define GPIO_M0_PWM1_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_M0_PWM2_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_2)
#  define GPIO_M0_PWM2_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_M0_PWM2_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_M0_PWM3_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_3)
#  define GPIO_M0_PWM3_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_M0_PWM3_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_M0_PWM4_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_4)
#  define GPIO_M0_PWM4_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTM | GPIO_PIN_6)
#  define GPIO_M0_PWM4_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_M0_PWM4_4       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_M0_PWM4_5       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_4)
#  define GPIO_M0_PWM5_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_5)
#  define GPIO_M0_PWM5_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTM | GPIO_PIN_7)
#  define GPIO_M0_PWM5_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_M0_PWM5_4       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_M0_PWM5_5       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_5)
#  define GPIO_M0_PWM6_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_6)
#  define GPIO_M0_PWM6_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_2)
#  define GPIO_M0_PWM6_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_M0_PWM6_4       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_M0_PWM6_5       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_6)
#  define GPIO_M0_PWM6_6       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_6)
#  define GPIO_M0_PWM7_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_7)
#  define GPIO_M0_PWM7_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_3)
#  define GPIO_M0_PWM7_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_M0_PWM7_4       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_M0_PWM7_5       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_7)
#  define GPIO_M0_PWM7_6       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_7)
#  define GPIO_M0_PWM_FAULT0_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_M0_PWM_FAULT0_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_M0_PWM_FAULT0_3 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_M0_PWM_FAULT0_4 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_M0_PWM_FAULT0_5 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_M0_PWM_FAULT1_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_M0_PWM_FAULT1_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_M0_PWM_FAULT1_3 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_M0_PWM_FAULT1_4 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_M0_PWM_FAULT1_5 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_5)
#  define GPIO_M0_PWM_FAULT2_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_M0_PWM_FAULT2_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_3)
#  define GPIO_M0_PWM_FAULT2_3 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_M0_PWM_FAULT2_4 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_M0_PWM_FAULT3_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTF | GPIO_PIN_5)
#  define GPIO_M0_PWM_FAULT3_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_M0_PWM_FAULT3_3 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_M1_PWM0_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTQ | GPIO_PIN_0)
#  define GPIO_M1_PWM0_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_M1_PWM0_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_M1_PWM1_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTQ | GPIO_PIN_1)
#  define GPIO_M1_PWM1_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_M1_PWM1_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_3)
#  define GPIO_M1_PWM2_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTQ | GPIO_PIN_2)
#  define GPIO_M1_PWM2_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_M1_PWM2_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_M1_PWM2_4       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_M1_PWM3_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTQ | GPIO_PIN_3)
#  define GPIO_M1_PWM3_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_M1_PWM3_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_M1_PWM3_4       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_M1_PWM4_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTQ | GPIO_PIN_4)
#  define GPIO_M1_PWM4_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_4)
#  define GPIO_M1_PWM4_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_M1_PWM5_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTQ | GPIO_PIN_5)
#  define GPIO_M1_PWM5_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_5)
#  define GPIO_M1_PWM5_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_M1_PWM6_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTQ | GPIO_PIN_6)
#  define GPIO_M1_PWM6_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_6)
#  define GPIO_M1_PWM6_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_M1_PWM7_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTQ | GPIO_PIN_7)
#  define GPIO_M1_PWM7_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_7)
#  define GPIO_M1_PWM7_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_M1_PWM_FAULT0_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_M1_PWM_FAULT0_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_M1_PWM_FAULT0_3 (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_7)
#  define GPIO_M1_PWM_FAULT1_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_M1_PWM_FAULT1_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_M1_PWM_FAULT2_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_1)
#  define GPIO_M1_PWM_FAULT2_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_M1_PWM_FAULT3   (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_3)

#  define GPIO_NMI_1           (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_NMI_2           (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTF | GPIO_PIN_0)

#  define GPIO_QEI0_IDX_1      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_QEI0_IDX_2      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTJ | GPIO_PIN_2)
#  define GPIO_QEI0_IDX_3      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_QEI0_IDX_4      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_QEI0_PHA_1      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTH | GPIO_PIN_4)
#  define GPIO_QEI0_PHA_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_QEI0_PHA_3      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_QEI0_PHB_1      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTH | GPIO_PIN_5)
#  define GPIO_QEI0_PHB_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_QEI0_PHB_3      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_QEI1_IDX_1      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_7)
#  define GPIO_QEI1_IDX_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_QEI1_IDX_3      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_QEI1_PHA_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_QEI1_PHA_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_QEI1_PHA_3      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_3)
#  define GPIO_QEI1_PHB_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_QEI1_PHB_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_1)
#  define GPIO_QEI1_PHB_3      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_4)

#  define GPIO_SSI0_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_SSI0_FSS        (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_3)
#  define GPIO_SSI0_RX         (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_SSI0_TX         (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_5)
#  define GPIO_SSI1_CLK_1      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_SSI1_CLK_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_SSI1_FSS_1      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_SSI1_FSS_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_SSI1_RX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_SSI1_RX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_SSI1_TX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_SSI1_TX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_SSI2_CLK_1      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_SSI2_CLK_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_4)
#  define GPIO_SSI2_FSS_1      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_SSI2_FSS_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_5)
#  define GPIO_SSI2_RX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_6)
#  define GPIO_SSI2_RX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_6)
#  define GPIO_SSI2_TX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_7)
#  define GPIO_SSI2_TX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_7)
#  define GPIO_SSI3_CLK_1      (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_SSI3_CLK_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_SSI3_CLK_3      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_SSI3_FSS_1      (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_SSI3_FSS_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_SSI3_FSS_3      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_SSI3_RX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_SSI3_RX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_SSI3_RX_3       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_SSI3_TX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_SSI3_TX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_SSI3_TX_3       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_3)

#  define GPIO_TCK_SWCLK       (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_0)
#  define GPIO_TDI             (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_2)
#  define GPIO_TDO_SWO         (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_3)
#  define GPIO_TMS_SWDIO       (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_1)

#  define GPIO_TR_CLK          (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_TR_D0           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_TR_D1           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_TR_D2           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_TR_D3           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_4)

#  define GPIO_TIM0_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_6)
#  define GPIO_TIM0_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_TIM0_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTL | GPIO_PIN_0)
#  define GPIO_TIM0_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_7)
#  define GPIO_TIM0_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_TIM0_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_TIM1_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_TIM1_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_TIM1_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTJ | GPIO_PIN_0)
#  define GPIO_TIM1_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTL | GPIO_PIN_2)
#  define GPIO_TIM1_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_TIM1_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_TIM1_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTJ | GPIO_PIN_1)
#  define GPIO_TIM1_CCP1_4     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTL | GPIO_PIN_3)
#  define GPIO_TIM2_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_TIM2_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_TIM2_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTJ | GPIO_PIN_2)
#  define GPIO_TIM2_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTL | GPIO_PIN_4)
#  define GPIO_TIM2_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_TIM2_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_5)
#  define GPIO_TIM2_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTJ | GPIO_PIN_3)
#  define GPIO_TIM2_CCP1_4     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTL | GPIO_PIN_5)
#  define GPIO_TIM3_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_TIM3_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_6)
#  define GPIO_TIM3_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTJ | GPIO_PIN_4)
#  define GPIO_TIM3_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTL | GPIO_PIN_6)
#  define GPIO_TIM3_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_3)
#  define GPIO_TIM3_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_7)
#  define GPIO_TIM3_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTJ | GPIO_PIN_5)
#  define GPIO_TIM3_CCP1_4     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTL | GPIO_PIN_7)
#  define GPIO_TIM4_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_0)
#  define GPIO_TIM4_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_TIM4_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTM | GPIO_PIN_0)
#  define GPIO_TIM4_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTP | GPIO_PIN_0)
#  define GPIO_TIM4_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_1)
#  define GPIO_TIM4_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_1)
#  define GPIO_TIM4_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTM | GPIO_PIN_1)
#  define GPIO_TIM4_CCP1_4     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTP | GPIO_PIN_1)
#  define GPIO_TIM5_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_2)
#  define GPIO_TIM5_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_TIM5_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTM | GPIO_PIN_2)
#  define GPIO_TIM5_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTP | GPIO_PIN_2)
#  define GPIO_TIM5_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_3)
#  define GPIO_TIM5_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_3)
#  define GPIO_TIM5_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTM | GPIO_PIN_3)
#  define GPIO_TIM5_CCP1_4     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTP | GPIO_PIN_3)

#  define GPIO_UART0_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_UART0_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_UART1_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_UART1_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_UART1_DCD       (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_UART1_DSR       (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_UART1_DTR       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_UART1_RI        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_7)
#  define GPIO_UART1_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_UART1_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_UART1_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_UART1_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_UART1_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_UART1_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_UART2_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_UART2_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_UART2_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_UART2_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_UART3_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_UART3_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_UART4_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_UART4_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_0)
#  define GPIO_UART4_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_UART4_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_1)
#  define GPIO_UART5_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_UART5_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_2)
#  define GPIO_UART5_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_UART5_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_3)
#  define GPIO_UART6_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_UART6_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_4)
#  define GPIO_UART6_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_UART6_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_5)
#  define GPIO_UART7_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_0)
#  define GPIO_UART7_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_UART7_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_1)
#  define GPIO_UART7_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_5)

#  define GPIO_USB0_DM         (GPIO_FUNC_ANIO                   | GPIO_PORTL | GPIO_PIN_7)
#  define GPIO_USB0_DP         (GPIO_FUNC_ANIO                   | GPIO_PORTL | GPIO_PIN_6)
#  define GPIO_USB0_EPEN_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_USB0_EPEN_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_USB0_EPEN_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_USB0_EPEN_4     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_USB0_ID         (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_USB0_PFLT_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_USB0_PFLT_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_USB0_PFLT_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTF | GPIO_PIN_5)
#  define GPIO_USB0_PFLT_4     (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_USB0_VBUS       (GPIO_FUNC_ANIO                   | GPIO_PORTB | GPIO_PIN_1)

#  define GPIO_WTIM0_CCP0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_WTIM0_CCP0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_WTIM0_CCP0_3    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTM | GPIO_PIN_6)
#  define GPIO_WTIM0_CCP0_4    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTP | GPIO_PIN_4)
#  define GPIO_WTIM0_CCP0_5    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTL | GPIO_PIN_0)
#  define GPIO_WTIM0_CCP1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_WTIM0_CCP1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_WTIM0_CCP1_3    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTM | GPIO_PIN_7)
#  define GPIO_WTIM0_CCP1_4    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTP | GPIO_PIN_5)
#  define GPIO_WTIM0_CCP1_5    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_WTIM1_CCP0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_WTIM1_CCP0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_6)
#  define GPIO_WTIM1_CCP0_3    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_WTIM1_CCP0_4    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTP | GPIO_PIN_6)
#  define GPIO_WTIM1_CCP0_5    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTL | GPIO_PIN_2)
#  define GPIO_WTIM1_CCP1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_WTIM1_CCP1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_7)
#  define GPIO_WTIM1_CCP1_3    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_WTIM1_CCP1_4    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTP | GPIO_PIN_7)
#  define GPIO_WTIM1_CCP1_5    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTL | GPIO_PIN_3)
#  define GPIO_WTIM2_CCP0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_WTIM2_CCP0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_WTIM2_CCP0_3    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTN | GPIO_PIN_2)
#  define GPIO_WTIM2_CCP0_4    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTQ | GPIO_PIN_0)
#  define GPIO_WTIM2_CCP0_5    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTL | GPIO_PIN_4)
#  define GPIO_WTIM2_CCP1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_WTIM2_CCP1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_WTIM2_CCP1_3    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTN | GPIO_PIN_3)
#  define GPIO_WTIM2_CCP1_4    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTQ | GPIO_PIN_1)
#  define GPIO_WTIM2_CCP1_5    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTL | GPIO_PIN_5)
#  define GPIO_WTIM3_CCP0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_WTIM3_CCP0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_4)
#  define GPIO_WTIM3_CCP0_3    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTN | GPIO_PIN_4)
#  define GPIO_WTIM3_CCP0_4    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTQ | GPIO_PIN_2)
#  define GPIO_WTIM3_CCP0_5    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTL | GPIO_PIN_6)
#  define GPIO_WTIM3_CCP1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_WTIM3_CCP1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_5)
#  define GPIO_WTIM3_CCP1_3    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTN | GPIO_PIN_5)
#  define GPIO_WTIM3_CCP1_4    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTQ | GPIO_PIN_3)
#  define GPIO_WTIM3_CCP1_5    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTL | GPIO_PIN_7)
#  define GPIO_WTIM4_CCP0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_WTIM4_CCP0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_6)
#  define GPIO_WTIM4_CCP0_3    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTN | GPIO_PIN_6)
#  define GPIO_WTIM4_CCP0_4    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTQ | GPIO_PIN_4)
#  define GPIO_WTIM4_CCP0_5    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTM | GPIO_PIN_0)
#  define GPIO_WTIM4_CCP1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_WTIM4_CCP1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_7)
#  define GPIO_WTIM4_CCP1_3    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTN | GPIO_PIN_7)
#  define GPIO_WTIM4_CCP1_4    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTQ | GPIO_PIN_5)
#  define GPIO_WTIM4_CCP1_5    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTM | GPIO_PIN_1)
#  define GPIO_WTIM5_CCP0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_WTIM5_CCP0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_WTIM5_CCP0_3    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTQ | GPIO_PIN_6)
#  define GPIO_WTIM5_CCP0_4    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTM | GPIO_PIN_2)
#  define GPIO_WTIM5_CCP1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_WTIM5_CCP1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_WTIM5_CCP1_3    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTQ | GPIO_PIN_7)
#  define GPIO_WTIM5_CCP1_4    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTM | GPIO_PIN_3)

#elif defined(CONFIG_ARCH_CHIP_TM4C123GH6PM)

#  define GPIO_ADC_AIN0        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_3)
#  define GPIO_ADC_AIN1        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_2)
#  define GPIO_ADC_AIN2        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_1)
#  define GPIO_ADC_AIN3        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_0)
#  define GPIO_ADC_AIN4        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_ADC_AIN5        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_ADC_AIN6        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_ADC_AIN7        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_ADC_AIN8        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_ADC_AIN9        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_ADC_AIN10       (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_ADC_AIN11       (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_5)

#  define GPIO_CAN0_RX_1       (GPIO_FUNC_PFINPUT  | GPIO_ALT_3  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_CAN0_RX_2       (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_CAN0_RX_3       (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_CAN0_TX_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_CAN0_TX_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_CAN0_TX_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_CAN1_RX         (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_CAN1_TX         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTA | GPIO_PIN_1)

#  define GPIO_CMP0_NIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_CMP0_OUT        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_9  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_CMP0_PIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_CMP1_NIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_CMP1_OUT        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_9  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_CMP1_PIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_5)

#  define GPIO_I2C0_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_I2C0_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C1_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_I2C1_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_I2C2_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTE | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C3_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_I2C3_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)

#  define GPIO_M0_PWM0         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTB | GPIO_PIN_6)
#  define GPIO_M0_PWM1         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTB | GPIO_PIN_7)
#  define GPIO_M0_PWM2         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_M0_PWM3         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_M0_PWM4         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_M0_PWM5         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_M0_PWM6         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_M0_PWM7         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_M0_PWM_FAULT0_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_M0_PWM_FAULT0_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_M0_PWM_FAULT0_3 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_M1_PWM0         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_M1_PWM1         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_M1_PWM2_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_M1_PWM2_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_M1_PWM3_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_M1_PWM3_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_M1_PWM4         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_M1_PWM5         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_M1_PWM6         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_M1_PWM7         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_M1_PWM_FAULT0_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_4)

#  define GPIO_NMI_1           (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_NMI_2           (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTF | GPIO_PIN_0)

#  define GPIO_QEI0_IDX_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_QEI0_IDX_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_QEI0_PHA_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_QEI0_PHA_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_QEI0_PHB_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_QEI0_PHB_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_QEI1_IDX        (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_QEI1_PHA        (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_QEI1_PHB        (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTC | GPIO_PIN_6)

#  define GPIO_SSI0_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_SSI0_FSS        (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_3)
#  define GPIO_SSI0_RX         (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_SSI0_TX         (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_5)
#  define GPIO_SSI1_CLK_1      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_SSI1_CLK_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_SSI1_FSS_1      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_SSI1_FSS_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_SSI1_RX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_SSI1_RX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_SSI1_TX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_SSI1_TX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_SSI2_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_SSI2_FSS        (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_SSI2_RX         (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_6)
#  define GPIO_SSI2_TX         (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_7)
#  define GPIO_SSI3_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_SSI3_FSS        (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_SSI3_RX         (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_SSI3_TX         (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_3)

#  define GPIO_TCK_SWCLK       (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_0)
#  define GPIO_TDI             (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_2)
#  define GPIO_TDO_SWO         (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_3)
#  define GPIO_TMS_SWDIO       (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_1)

#  define GPIO_TR_CLK          (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_TR_D0           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_TR_D1           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_1)

#  define GPIO_TIM0_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_6)
#  define GPIO_TIM0_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_TIM0_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_7)
#  define GPIO_TIM0_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_TIM1_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_TIM1_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_TIM1_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_TIM1_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_TIM2_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_TIM2_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_TIM2_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_TIM3_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_TIM3_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_3)
#  define GPIO_TIM4_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_0)
#  define GPIO_TIM4_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_1)
#  define GPIO_TIM5_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_2)
#  define GPIO_TIM5_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_3)

#  define GPIO_UART0_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_UART0_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_UART1_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_UART1_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_UART1_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_UART1_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_UART1_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_UART1_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_UART1_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_UART1_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_UART2_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_UART2_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_UART3_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_UART3_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_UART4_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_UART4_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_UART5_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_UART5_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_UART6_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_UART6_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_UART7_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_0)
#  define GPIO_UART7_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_1)

#  define GPIO_USB0_EPEN_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_USB0_EPEN_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_USB0_EPEN_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_USB0_ID         (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_USB0_PFLT_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_USB0_PFLT_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_USB0_VBUS       (GPIO_FUNC_ANIO                   | GPIO_PORTB | GPIO_PIN_1)

#  define GPIO_WTIM0_CCP0      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_WTIM0_CCP1      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_WTIM1_CCP0      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_WTIM1_CCP1      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_WTIM2_CCP0      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_WTIM2_CCP1      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_WTIM3_CCP0      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_WTIM3_CCP1      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_WTIM4_CCP0      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_WTIM4_CCP1      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_WTIM5_CCP0      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_WTIM5_CCP1      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_7)

#elif defined(CONFIG_ARCH_CHIP_TM4C123GH6PZ)

#  define GPIO_ADC_AIN0        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_3)
#  define GPIO_ADC_AIN1        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_2)
#  define GPIO_ADC_AIN2        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_1)
#  define GPIO_ADC_AIN3        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_0)
#  define GPIO_ADC_AIN4        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_ADC_AIN5        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_ADC_AIN6        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_ADC_AIN7        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_ADC_AIN8        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_ADC_AIN9        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_ADC_AIN10       (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_ADC_AIN11       (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_ADC_AIN12       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_ADC_AIN13       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_ADC_AIN14       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_ADC_AIN15       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_ADC_AIN16       (GPIO_FUNC_ANINPUT                | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_ADC_AIN17       (GPIO_FUNC_ANINPUT                | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_ADC_AIN18       (GPIO_FUNC_ANINPUT                | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_ADC_AIN19       (GPIO_FUNC_ANINPUT                | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_ADC_AIN20       (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_7)
#  define GPIO_ADC_AIN21       (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_6)

#  define GPIO_CAN0_RX_1       (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_CAN0_RX_2       (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_CAN0_RX_3       (GPIO_FUNC_PFINPUT  | GPIO_ALT_3  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_CAN0_TX_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_CAN0_TX_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_CAN0_TX_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_CAN1_RX_1       (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_CAN1_RX_2       (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTE | GPIO_PIN_6)
#  define GPIO_CAN1_TX_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_CAN1_TX_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTE | GPIO_PIN_7)

#  define GPIO_CMP0_NIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_CMP0_OUT        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_9  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_CMP0_PIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_CMP1_NIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_CMP1_OUT        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_9  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_CMP1_PIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_CMP2_NIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTG | GPIO_PIN_7)
#  define GPIO_CMP2_OUT        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_9  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_CMP2_PIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTG | GPIO_PIN_6)

#  define GPIO_I2C0_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_I2C0_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C1_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_I2C1_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_I2C1_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C1_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_I2C2_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTF | GPIO_PIN_6)
#  define GPIO_I2C2_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTE | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTF | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C3_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_I2C3_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_I2C3_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C3_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C4_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_I2C4_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C5_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_6)
#  define GPIO_I2C5_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)

#  define GPIO_M0_PWM0         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_M0_PWM1         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_M0_PWM2_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_M0_PWM2_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_M0_PWM3_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_M0_PWM3_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_M0_PWM4_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_M0_PWM4_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_M0_PWM4_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_4)
#  define GPIO_M0_PWM5_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_M0_PWM5_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_M0_PWM5_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_5)
#  define GPIO_M0_PWM6_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_M0_PWM6_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_M0_PWM6_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_6)
#  define GPIO_M0_PWM6_4       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_6)
#  define GPIO_M0_PWM7_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_M0_PWM7_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_M0_PWM7_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_7)
#  define GPIO_M0_PWM7_4       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_7)
#  define GPIO_M0_PWM_FAULT0_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_M0_PWM_FAULT0_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_M0_PWM_FAULT0_3 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_M0_PWM_FAULT0_4 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_M0_PWM_FAULT1_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_M0_PWM_FAULT1_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_M0_PWM_FAULT1_3 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_M0_PWM_FAULT1_4 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_M0_PWM_FAULT2_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_M0_PWM_FAULT2_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_3)
#  define GPIO_M0_PWM_FAULT2_3 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_M0_PWM_FAULT3_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTF | GPIO_PIN_5)
#  define GPIO_M0_PWM_FAULT3_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_M1_PWM0_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_M1_PWM0_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_M1_PWM1_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_M1_PWM1_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_3)
#  define GPIO_M1_PWM2_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_M1_PWM2_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_M1_PWM2_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_M1_PWM3_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_M1_PWM3_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_M1_PWM3_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_M1_PWM4         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_M1_PWM5         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_M1_PWM6         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_M1_PWM7         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_M1_PWM_FAULT0_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_M1_PWM_FAULT0_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_7)
#  define GPIO_M1_PWM_FAULT0_3 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_M1_PWM_FAULT1_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_M1_PWM_FAULT1_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_M1_PWM_FAULT2_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_1)
#  define GPIO_M1_PWM_FAULT2_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_M1_PWM_FAULT3   (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_3)

#  define GPIO_NMI_1           (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_NMI_2           (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTF | GPIO_PIN_0)

#  define GPIO_QEI0_IDX_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_QEI0_IDX_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_QEI0_IDX_3      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_QEI0_IDX_4      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTJ | GPIO_PIN_2)
#  define GPIO_QEI0_PHA_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_QEI0_PHA_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_QEI0_PHA_3      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTH | GPIO_PIN_4)
#  define GPIO_QEI0_PHB_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_QEI0_PHB_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_QEI0_PHB_3      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTH | GPIO_PIN_5)
#  define GPIO_QEI1_IDX_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_QEI1_IDX_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_QEI1_IDX_3      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_7)
#  define GPIO_QEI1_PHA_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_QEI1_PHA_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_QEI1_PHA_3      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_3)
#  define GPIO_QEI1_PHB_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_QEI1_PHB_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_1)
#  define GPIO_QEI1_PHB_3      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_4)

#  define GPIO_SSI0_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_SSI0_FSS        (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_3)
#  define GPIO_SSI0_RX         (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_SSI0_TX         (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_5)
#  define GPIO_SSI1_CLK_1      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_SSI1_CLK_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_SSI1_FSS_1      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_SSI1_FSS_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_SSI1_RX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_SSI1_RX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_SSI1_TX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_SSI1_TX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_SSI2_CLK_1      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_SSI2_CLK_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_4)
#  define GPIO_SSI2_FSS_1      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_SSI2_FSS_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_5)
#  define GPIO_SSI2_RX         (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_6)
#  define GPIO_SSI2_TX         (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_7)
#  define GPIO_SSI3_CLK_1      (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_SSI3_CLK_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_SSI3_CLK_3      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_SSI3_FSS_1      (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_SSI3_FSS_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_SSI3_FSS_3      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_SSI3_RX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_SSI3_RX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_SSI3_RX_3       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_SSI3_TX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_SSI3_TX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_SSI3_TX_3       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_3)

#  define GPIO_TCK_SWCLK       (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_0)
#  define GPIO_TDI             (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_2)
#  define GPIO_TDO_SWO         (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_3)
#  define GPIO_TMS_SWDIO       (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_1)

#  define GPIO_TR_CLK          (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_TR_D0           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_TR_D1           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_TR_D2           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_TR_D3           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_4)

#  define GPIO_TIM0_CCP0       (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_TIM0_CCP1       (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_TIM1_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_TIM1_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_TIM1_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTJ | GPIO_PIN_0)
#  define GPIO_TIM1_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_TIM1_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_TIM1_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTJ | GPIO_PIN_1)
#  define GPIO_TIM2_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_TIM2_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_TIM2_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTJ | GPIO_PIN_2)
#  define GPIO_TIM2_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_TIM2_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_5)
#  define GPIO_TIM3_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_TIM3_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_6)
#  define GPIO_TIM3_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_3)
#  define GPIO_TIM3_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_7)
#  define GPIO_TIM4_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_0)
#  define GPIO_TIM4_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_TIM4_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_1)
#  define GPIO_TIM4_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_1)
#  define GPIO_TIM5_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_2)
#  define GPIO_TIM5_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_TIM5_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_3)
#  define GPIO_TIM5_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_3)

#  define GPIO_UART0_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_UART0_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_UART1_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_UART1_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_UART1_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_UART1_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_UART1_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_UART1_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_UART1_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_UART1_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_UART1_DCD       (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_UART1_DSR       (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_UART1_DTR       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_UART1_RI        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_7)
#  define GPIO_UART2_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_UART2_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_UART2_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_UART2_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_UART3_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_UART3_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_UART4_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_UART4_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_0)
#  define GPIO_UART4_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_UART4_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_1)
#  define GPIO_UART5_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_UART5_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_2)
#  define GPIO_UART5_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_UART6_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_UART6_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_UART7_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_0)
#  define GPIO_UART7_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_1)

#  define GPIO_USB0_EPEN_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_USB0_EPEN_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_USB0_EPEN_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_USB0_EPEN_4     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_USB0_ID         (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_USB0_PFLT_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_USB0_PFLT_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_USB0_PFLT_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTF | GPIO_PIN_5)
#  define GPIO_USB0_PFLT_4     (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_USB0_VBUS       (GPIO_FUNC_ANIO                   | GPIO_PORTB | GPIO_PIN_1)

#  define GPIO_WTIM0_CCP0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_WTIM0_CCP0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_WTIM0_CCP1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_WTIM0_CCP1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_WTIM1_CCP0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_WTIM1_CCP0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_6)
#  define GPIO_WTIM1_CCP1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_WTIM1_CCP1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_7)
#  define GPIO_WTIM2_CCP0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_WTIM2_CCP0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_WTIM2_CCP1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_WTIM2_CCP1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_WTIM3_CCP0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_WTIM3_CCP0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_4)
#  define GPIO_WTIM3_CCP1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_WTIM3_CCP1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_5)
#  define GPIO_WTIM4_CCP0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_WTIM4_CCP0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_6)
#  define GPIO_WTIM4_CCP1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_WTIM4_CCP1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_7)
#  define GPIO_WTIM5_CCP0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_WTIM5_CCP0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_WTIM5_CCP1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_WTIM5_CCP1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_3)

#elif defined(CONFIG_ARCH_CHIP_TM4C123GH6PGE)

#  define GPIO_ADC_AIN0        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_3)
#  define GPIO_ADC_AIN1        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_2)
#  define GPIO_ADC_AIN2        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_1)
#  define GPIO_ADC_AIN3        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_0)
#  define GPIO_ADC_AIN4        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_ADC_AIN5        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_ADC_AIN6        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_ADC_AIN7        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_ADC_AIN8        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_ADC_AIN9        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_ADC_AIN10       (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_ADC_AIN11       (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_ADC_AIN12       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_ADC_AIN13       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_ADC_AIN14       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_ADC_AIN15       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_ADC_AIN16       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_ADC_AIN17       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_ADC_AIN18       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_ADC_AIN19       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_3)
#  define GPIO_ADC_AIN20       (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_7)
#  define GPIO_ADC_AIN21       (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_6)
#  define GPIO_ADC_AIN22       (GPIO_FUNC_ANINPUT                | GPIO_PORTP | GPIO_PIN_1)
#  define GPIO_ADC_AIN23       (GPIO_FUNC_ANINPUT                | GPIO_PORTP | GPIO_PIN_0)

#  define GPIO_CAN0_RX_1       (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_CAN0_RX_2       (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_CAN0_RX_3       (GPIO_FUNC_PFINPUT  | GPIO_ALT_3  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_CAN0_RX_4       (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_0)
#  define GPIO_CAN0_TX_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_CAN0_TX_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_CAN0_TX_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_CAN0_TX_4       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_1)
#  define GPIO_CAN1_RX_1       (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_CAN1_RX_2       (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTE | GPIO_PIN_6)
#  define GPIO_CAN1_TX_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_CAN1_TX_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTE | GPIO_PIN_7)

#  define GPIO_CMP0_NIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_CMP0_OUT_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_9  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_CMP0_OUT_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_CMP0_PIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_CMP1_NIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_CMP1_OUT_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_9  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_CMP1_OUT_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTK | GPIO_PIN_5)
#  define GPIO_CMP1_PIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_CMP2_NIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTJ | GPIO_PIN_5)
#  define GPIO_CMP2_OUT_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_9  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_CMP2_OUT_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_CMP2_PIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTJ | GPIO_PIN_4)

#  define GPIO_I2C0_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_I2C0_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C1_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_I2C1_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_I2C1_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C1_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_I2C2_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTF | GPIO_PIN_6)
#  define GPIO_I2C2_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTE | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTF | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C3_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_I2C3_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_I2C3_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C3_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C4_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_I2C4_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C5_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_6)
#  define GPIO_I2C5_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)

#  define GPIO_M0_PWM0_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_M0_PWM0_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_0)
#  define GPIO_M0_PWM1_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_M0_PWM1_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_1)
#  define GPIO_M0_PWM2_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_M0_PWM2_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_M0_PWM2_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_2)
#  define GPIO_M0_PWM3_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_M0_PWM3_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_M0_PWM4_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_M0_PWM4_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_M0_PWM4_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_4)
#  define GPIO_M0_PWM4_4       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTM | GPIO_PIN_6)
#  define GPIO_M0_PWM5_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_M0_PWM5_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_M0_PWM5_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_5)
#  define GPIO_M0_PWM5_4       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTM | GPIO_PIN_7)
#  define GPIO_M0_PWM6_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_M0_PWM6_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_M0_PWM6_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_6)
#  define GPIO_M0_PWM6_4       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_6)
#  define GPIO_M0_PWM6_5       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTN | GPIO_PIN_2)
#  define GPIO_M0_PWM7_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_M0_PWM7_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_M0_PWM7_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_7)
#  define GPIO_M0_PWM7_4       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTH | GPIO_PIN_7)
#  define GPIO_M0_PWM7_5       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTN | GPIO_PIN_3)
#  define GPIO_M0_PWM_FAULT0_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_M0_PWM_FAULT0_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_M0_PWM_FAULT0_3 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_M0_PWM_FAULT0_4 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_M0_PWM_FAULT0_5 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_M0_PWM_FAULT1_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_M0_PWM_FAULT1_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_M0_PWM_FAULT1_3 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_M0_PWM_FAULT1_4 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_M0_PWM_FAULT1_5 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_5)
#  define GPIO_M0_PWM_FAULT2_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_M0_PWM_FAULT2_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_3)
#  define GPIO_M0_PWM_FAULT2_3 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_M0_PWM_FAULT2_4 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_M0_PWM_FAULT3_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTF | GPIO_PIN_5)
#  define GPIO_M0_PWM_FAULT3_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_M0_PWM_FAULT3_3 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_M1_PWM0_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_M1_PWM0_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_M1_PWM1_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_M1_PWM1_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_3)
#  define GPIO_M1_PWM2_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_M1_PWM2_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_M1_PWM2_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_M1_PWM3_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_M1_PWM3_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_M1_PWM3_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_M1_PWM4_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_M1_PWM4_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_4)
#  define GPIO_M1_PWM5_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_M1_PWM5_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_5)
#  define GPIO_M1_PWM6_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_M1_PWM6_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_6)
#  define GPIO_M1_PWM7_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_M1_PWM7_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_7)
#  define GPIO_M1_PWM_FAULT0_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_M1_PWM_FAULT0_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_7)
#  define GPIO_M1_PWM_FAULT0_3 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_M1_PWM_FAULT1_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_M1_PWM_FAULT1_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_M1_PWM_FAULT2_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_1)
#  define GPIO_M1_PWM_FAULT2_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_M1_PWM_FAULT3   (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_3)

#  define GPIO_NMI_1           (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_NMI_2           (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTF | GPIO_PIN_0)

#  define GPIO_QEI0_IDX_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_QEI0_IDX_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_QEI0_IDX_3      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_QEI0_IDX_4      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTJ | GPIO_PIN_2)
#  define GPIO_QEI0_PHA_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_QEI0_PHA_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_QEI0_PHA_3      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTH | GPIO_PIN_4)
#  define GPIO_QEI0_PHB_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_QEI0_PHB_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_QEI0_PHB_3      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTH | GPIO_PIN_5)
#  define GPIO_QEI1_IDX_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_QEI1_IDX_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_QEI1_IDX_3      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_7)
#  define GPIO_QEI1_PHA_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_QEI1_PHA_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_QEI1_PHA_3      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_3)
#  define GPIO_QEI1_PHB_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_QEI1_PHB_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_1)
#  define GPIO_QEI1_PHB_3      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_4)

#  define GPIO_SSI0_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_SSI0_FSS        (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_3)
#  define GPIO_SSI0_RX         (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_SSI0_TX         (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_5)
#  define GPIO_SSI1_CLK_1      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_SSI1_CLK_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_SSI1_FSS_1      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_SSI1_FSS_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_SSI1_RX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_SSI1_RX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_SSI1_TX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_SSI1_TX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_SSI2_CLK_1      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_SSI2_CLK_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_4)
#  define GPIO_SSI2_FSS_1      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_SSI2_FSS_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_5)
#  define GPIO_SSI2_RX         (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_6)
#  define GPIO_SSI2_TX         (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_7)
#  define GPIO_SSI3_CLK_1      (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_SSI3_CLK_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_SSI3_CLK_3      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_SSI3_FSS_1      (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_SSI3_FSS_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_SSI3_FSS_3      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_SSI3_RX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_SSI3_RX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_SSI3_RX_3       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_SSI3_TX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_SSI3_TX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_SSI3_TX_3       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_3)

#  define GPIO_TCK_SWCLK       (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_0)
#  define GPIO_TDI             (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_2)
#  define GPIO_TDO_SWO         (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_3)
#  define GPIO_TMS_SWDIO       (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_1)

#  define GPIO_TR_CLK          (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_TR_D0           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_TR_D1           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_TR_D2           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_TR_D3           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_4)

#  define GPIO_TIM0_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_TIM0_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTL | GPIO_PIN_0)
#  define GPIO_TIM0_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_TIM0_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_TIM1_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_TIM1_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_TIM1_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTJ | GPIO_PIN_0)
#  define GPIO_TIM1_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTL | GPIO_PIN_2)
#  define GPIO_TIM1_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_TIM1_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_TIM1_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTJ | GPIO_PIN_1)
#  define GPIO_TIM1_CCP1_4     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTL | GPIO_PIN_3)
#  define GPIO_TIM2_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_TIM2_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_TIM2_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTJ | GPIO_PIN_2)
#  define GPIO_TIM2_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTL | GPIO_PIN_4)
#  define GPIO_TIM2_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_TIM2_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_5)
#  define GPIO_TIM2_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTJ | GPIO_PIN_3)
#  define GPIO_TIM2_CCP1_4     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTL | GPIO_PIN_5)
#  define GPIO_TIM3_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_TIM3_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_6)
#  define GPIO_TIM3_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTJ | GPIO_PIN_4)
#  define GPIO_TIM3_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTL | GPIO_PIN_6)
#  define GPIO_TIM3_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_3)
#  define GPIO_TIM3_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_7)
#  define GPIO_TIM3_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTJ | GPIO_PIN_5)
#  define GPIO_TIM3_CCP1_4     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTL | GPIO_PIN_7)
#  define GPIO_TIM4_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_0)
#  define GPIO_TIM4_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_TIM4_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTM | GPIO_PIN_0)
#  define GPIO_TIM4_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTP | GPIO_PIN_0)
#  define GPIO_TIM4_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_1)
#  define GPIO_TIM4_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_1)
#  define GPIO_TIM4_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTM | GPIO_PIN_1)
#  define GPIO_TIM4_CCP1_4     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTP | GPIO_PIN_1)
#  define GPIO_TIM5_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_2)
#  define GPIO_TIM5_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_TIM5_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTM | GPIO_PIN_2)
#  define GPIO_TIM5_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTP | GPIO_PIN_2)
#  define GPIO_TIM5_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_3)
#  define GPIO_TIM5_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_3)
#  define GPIO_TIM5_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTM | GPIO_PIN_3)

#  define GPIO_UART0_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_UART0_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_UART1_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_UART1_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_UART1_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_UART1_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_UART1_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_UART1_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_UART1_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_UART1_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_UART1_DCD       (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_UART1_DSR       (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_UART1_DTR       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_UART1_RI        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_7)
#  define GPIO_UART2_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_UART2_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_UART2_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_UART2_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_UART3_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_UART3_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_UART4_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_UART4_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_0)
#  define GPIO_UART4_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_UART4_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_1)
#  define GPIO_UART5_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_UART5_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_2)
#  define GPIO_UART5_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_UART5_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_3)
#  define GPIO_UART6_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_UART6_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_4)
#  define GPIO_UART6_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_UART6_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_5)
#  define GPIO_UART7_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_0)
#  define GPIO_UART7_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_UART7_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_1)
#  define GPIO_UART7_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_5)

#  define GPIO_USB0_EPEN_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_USB0_EPEN_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_USB0_EPEN_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_USB0_EPEN_4     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_USB0_ID         (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_USB0_PFLT_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_USB0_PFLT_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_USB0_PFLT_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTF | GPIO_PIN_5)
#  define GPIO_USB0_PFLT_4     (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_USB0_VBUS       (GPIO_FUNC_ANIO                   | GPIO_PORTB | GPIO_PIN_1)

#  define GPIO_WTIM0_CCP0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_WTIM0_CCP0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_WTIM0_CCP0_3    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTL | GPIO_PIN_0)
#  define GPIO_WTIM0_CCP0_4    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTM | GPIO_PIN_6)
#  define GPIO_WTIM0_CCP1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_WTIM0_CCP1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_WTIM0_CCP1_3    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_WTIM0_CCP1_4    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTM | GPIO_PIN_7)
#  define GPIO_WTIM1_CCP0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_WTIM1_CCP0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_6)
#  define GPIO_WTIM1_CCP0_3    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_WTIM1_CCP0_4    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTL | GPIO_PIN_2)
#  define GPIO_WTIM1_CCP1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_WTIM1_CCP1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_7)
#  define GPIO_WTIM1_CCP1_3    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_WTIM1_CCP1_4    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTL | GPIO_PIN_3)

#  define GPIO_WTIM2_CCP0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_WTIM2_CCP0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_WTIM2_CCP0_3    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTL | GPIO_PIN_4)
#  define GPIO_WTIM2_CCP0_4    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTN | GPIO_PIN_2)
#  define GPIO_WTIM2_CCP1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_WTIM2_CCP1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_WTIM2_CCP1_3    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTL | GPIO_PIN_5)
#  define GPIO_WTIM2_CCP1_4    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTN | GPIO_PIN_3)

#  define GPIO_WTIM3_CCP0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_WTIM3_CCP0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_4)
#  define GPIO_WTIM3_CCP0_3    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTL | GPIO_PIN_6)
#  define GPIO_WTIM3_CCP0_4    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTN | GPIO_PIN_4)
#  define GPIO_WTIM3_CCP1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_WTIM3_CCP1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_5)
#  define GPIO_WTIM3_CCP1_3    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTL | GPIO_PIN_7)
#  define GPIO_WTIM3_CCP1_4    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTN | GPIO_PIN_5)

#  define GPIO_WTIM4_CCP0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_WTIM4_CCP0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_6)
#  define GPIO_WTIM4_CCP0_3    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTM | GPIO_PIN_0)
#  define GPIO_WTIM4_CCP0_4    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTN | GPIO_PIN_6)
#  define GPIO_WTIM4_CCP1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_WTIM4_CCP1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_7)
#  define GPIO_WTIM4_CCP1_3    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTM | GPIO_PIN_1)
#  define GPIO_WTIM4_CCP1_4    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTN | GPIO_PIN_7)

#  define GPIO_WTIM5_CCP0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_WTIM5_CCP0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_WTIM5_CCP0_3    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTM | GPIO_PIN_2)
#  define GPIO_WTIM5_CCP1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_WTIM5_CCP1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_WTIM5_CCP1_3    (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTM | GPIO_PIN_3)

#elif defined(CONFIG_ARCH_CHIP_TM4C123AH6PM)

#  define GPIO_ADC_AIN0        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_3)
#  define GPIO_ADC_AIN1        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_2)
#  define GPIO_ADC_AIN2        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_1)
#  define GPIO_ADC_AIN3        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_0)
#  define GPIO_ADC_AIN4        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_ADC_AIN5        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_ADC_AIN6        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_ADC_AIN7        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_ADC_AIN8        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_ADC_AIN9        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_ADC_AIN10       (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_ADC_AIN11       (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_5)

#  define GPIO_CAN0_RX_1       (GPIO_FUNC_PFINPUT  | GPIO_ALT_3  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_CAN0_RX_2       (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_CAN0_RX_3       (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_CAN0_TX_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_CAN0_TX_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_CAN0_TX_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_CAN1_RX_1       (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_CAN1_TX_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTA | GPIO_PIN_1)

#  define GPIO_CMP0_NIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_CMP0_OUT_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_9  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_CMP0_PIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_CMP1_NIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_CMP1_OUT_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_9  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_CMP1_PIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_5)

#  define GPIO_I2C0_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_I2C0_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C1_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_I2C1_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_I2C1_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C1_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_I2C2_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTE | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C3_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_I2C3_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_I2C3_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C3_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C4_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_I2C4_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTG | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C5_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_6)
#  define GPIO_I2C5_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)

#  define GPIO_M0_PWM0         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTB | GPIO_PIN_6)
#  define GPIO_M0_PWM1         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTB | GPIO_PIN_7)
#  define GPIO_M0_PWM2         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_M0_PWM3         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_M0_PWM4_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_M0_PWM4_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_M0_PWM5_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_M0_PWM5_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_M0_PWM6_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_M0_PWM6_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_M0_PWM7_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_M0_PWM7_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_M0_PWM_FAULT0_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_M0_PWM_FAULT0_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_M0_PWM_FAULT0_3 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_M0_PWM_FAULT1_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_M0_PWM_FAULT1_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_M0_PWM_FAULT1_3 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_M0_PWM_FAULT2_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_M0_PWM_FAULT2_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_4  | GPIO_PORTG | GPIO_PIN_3)

#  define GPIO_M1_PWM0_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_M1_PWM0_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_M1_PWM1_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_M1_PWM1_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_3)
#  define GPIO_M1_PWM2_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_M1_PWM2_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_M1_PWM2_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_M1_PWM3_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_M1_PWM3_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_M1_PWM3_3       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_M1_PWM4         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_M1_PWM5         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_M1_PWM6         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_M1_PWM7         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_M1_PWM_FAULT0   (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_M1_PWM_FAULT1   (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_M1_PWM_FAULT2   (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_1)

#  define GPIO_NMI_1           (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_NMI_2           (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTF | GPIO_PIN_0)

#  define GPIO_QEI0_IDX_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_QEI0_IDX_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_QEI0_PHA_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_QEI0_PHA_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_QEI0_PHB_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_QEI0_PHB_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_QEI1_IDX_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_QEI1_IDX_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_QEI1_PHA_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_QEI1_PHA_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_QEI1_PHA_3      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_3)
#  define GPIO_QEI1_PHB_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_QEI1_PHB_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_1)
#  define GPIO_QEI1_PHB_3      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_4)

#  define GPIO_SSI0_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_SSI0_FSS        (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_3)
#  define GPIO_SSI0_RX         (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_SSI0_TX         (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_5)
#  define GPIO_SSI1_CLK_1      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_SSI1_CLK_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_SSI1_FSS_1      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_SSI1_FSS_2      (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_SSI1_RX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_SSI1_RX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_SSI1_TX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_SSI1_TX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_SSI2_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_SSI2_FSS        (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_SSI2_RX         (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_6)
#  define GPIO_SSI2_TX         (GPIO_FUNC_PFIO     | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_7)
#  define GPIO_SSI3_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_SSI3_FSS        (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_SSI3_RX         (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_SSI3_TX         (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_3)

#  define GPIO_TCK_SWCLK       (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_0)
#  define GPIO_TDI             (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_2)
#  define GPIO_TDO_SWO         (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_3)
#  define GPIO_TMS_SWDIO       (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_1)

#  define GPIO_TR_CLK          (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_TR_D0           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_TR_D1           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_1)

#  define GPIO_TIM0_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_6)
#  define GPIO_TIM0_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_TIM0_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_7)
#  define GPIO_TIM0_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_TIM1_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_TIM1_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_TIM1_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_TIM1_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_TIM2_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_TIM2_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_TIM2_CCP1       (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_TIM3_CCP0       (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_TIM3_CCP1       (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_3)
#  define GPIO_TIM4_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_0)
#  define GPIO_TIM4_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_TIM4_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_1)
#  define GPIO_TIM4_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_1)
#  define GPIO_TIM5_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_2)
#  define GPIO_TIM5_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_TIM5_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_3)
#  define GPIO_TIM5_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_3)

#  define GPIO_UART0_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_UART0_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_UART1_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_UART1_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_UART1_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_UART1_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_8  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_UART1_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_UART1_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_UART1_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_UART1_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_UART2_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_UART2_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_UART2_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_UART2_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_UART3_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_UART3_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_UART4_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_UART4_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_UART5_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_UART5_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_UART6_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_UART6_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_UART7_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_0)
#  define GPIO_UART7_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_1)

#  define GPIO_WTIM0_CCP0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_WTIM0_CCP0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_WTIM0_CCP1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_WTIM0_CCP1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_WTIM1_CCP0      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_WTIM1_CCP1      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_WTIM2_CCP0      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_WTIM2_CCP1      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_WTIM3_CCP0      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_WTIM3_CCP1      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_WTIM4_CCP0      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_WTIM4_CCP1      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_WTIM5_CCP0      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_WTIM5_CCP1      (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTD | GPIO_PIN_7)

#elif defined(CONFIG_ARCH_CHIP_TM4C129XNCZAD)

#  define GPIO_ADC_AIN0        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_3)
#  define GPIO_ADC_AIN1        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_2)
#  define GPIO_ADC_AIN2        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_1)
#  define GPIO_ADC_AIN3        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_0)
#  define GPIO_ADC_AIN4        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_ADC_AIN5        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_ADC_AIN6        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_ADC_AIN7        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_ADC_AIN8        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_ADC_AIN9        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_ADC_AIN10       (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_ADC_AIN11       (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_ADC_AIN12       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_ADC_AIN13       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_ADC_AIN14       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_ADC_AIN15       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_ADC_AIN16       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_ADC_AIN17       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_ADC_AIN18       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_ADC_AIN19       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_3)
#  define GPIO_ADC_AIN20       (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_6)
#  define GPIO_ADC_AIN21       (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_7)
#  define GPIO_ADC_AIN22       (GPIO_FUNC_ANINPUT                | GPIO_PORTP | GPIO_PIN_7)
#  define GPIO_ADC_AIN23       (GPIO_FUNC_ANINPUT                | GPIO_PORTP | GPIO_PIN_6)

#  define GPIO_CAN0_RX_1       (GPIO_FUNC_PFINPUT  | GPIO_ALT_7  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_CAN0_RX_2       (GPIO_FUNC_PFINPUT  | GPIO_ALT_7  | GPIO_PORTT | GPIO_PIN_0)
#  define GPIO_CAN0_TX_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_7  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_CAN0_TX_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_7  | GPIO_PORTT | GPIO_PIN_1)
#  define GPIO_CAN1_RX_1       (GPIO_FUNC_PFINPUT  | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_CAN1_RX_2       (GPIO_FUNC_PFINPUT  | GPIO_ALT_7  | GPIO_PORTT | GPIO_PIN_2)
#  define GPIO_CAN1_TX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_CAN1_TX_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_7  | GPIO_PORTT | GPIO_PIN_3)

#  define GPIO_CMP0_OUT_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_CMP0_OUT_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTL | GPIO_PIN_2)
#  define GPIO_CMP1_OUT_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_CMP1_OUT_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTL | GPIO_PIN_3)
#  define GPIO_CMP2_OUT        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_2)

#  define GPIO_EN0_COL         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTM | GPIO_PIN_7)
#  define GPIO_EN0_CRS         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTM | GPIO_PIN_6)
#  define GPIO_EN0_INTRN_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_EN0_INTRN_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTP | GPIO_PIN_0)
#  define GPIO_EN0_LED0_1      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_EN0_LED0_2      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_EN0_LED1_1      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_EN0_LED1_2      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_EN0_LED2_1      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_EN0_LED2_2      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTK | GPIO_PIN_5)
#  define GPIO_EN0_MDC_1       (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_EN0_MDC_2       (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_EN0_MDIO_1      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTB | GPIO_PIN_3)
#  define GPIO_EN0_MDIO_2      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_EN0_PPS_1       (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_EN0_PPS_2       (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTH | GPIO_PIN_5)
#  define GPIO_EN0_PPS_3       (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTJ | GPIO_PIN_0)
#  define GPIO_EN0_RREF_CLK    (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTM | GPIO_PIN_4)
#  define GPIO_EN0_RXCK        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_EN0_RXD0_1      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTQ | GPIO_PIN_5)
#  define GPIO_EN0_RXD0_2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTT | GPIO_PIN_0)
#  define GPIO_EN0_RXD1_1      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTQ | GPIO_PIN_6)
#  define GPIO_EN0_RXD1_2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTT | GPIO_PIN_1)
#  define GPIO_EN0_RXD2        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTK | GPIO_PIN_5)
#  define GPIO_EN0_RXD3        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_EN0_RXDV_1      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTG | GPIO_PIN_7)
#  define GPIO_EN0_RXDV_2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTS | GPIO_PIN_7)
#  define GPIO_EN0_RXER_1      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTG | GPIO_PIN_6)
#  define GPIO_EN0_RXER_2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTS | GPIO_PIN_6)
#  define GPIO_EN0_TXCK        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_EN0_TXD0_1      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_EN0_TXD0_2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTS | GPIO_PIN_4)
#  define GPIO_EN0_TXD1_1      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_EN0_TXD1_2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTS | GPIO_PIN_5)
#  define GPIO_EN0_TXD2        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_EN0_TXD3        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_EN0_TXEN_1      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTG | GPIO_PIN_3)
#  define GPIO_EN0_TXEN_2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTR | GPIO_PIN_7)
#  define GPIO_EN0_TXER        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTN | GPIO_PIN_6)

#  define GPIO_EPI0_S0_1       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_EPI0_S0_2       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_EPI0_S1_1       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_EPI0_S1_2       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_EPI0_S2_1       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_EPI0_S2_2       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_EPI0_S3_1       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_EPI0_S3_2       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTK | GPIO_PIN_3)
#  define GPIO_EPI0_S4         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_EPI0_S5         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_EPI0_S6         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_EPI0_S7         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_EPI0_S8         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_EPI0_S9         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_EPI0_S10        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTG | GPIO_PIN_1)
#  define GPIO_EPI0_S11        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_EPI0_S12        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTM | GPIO_PIN_3)
#  define GPIO_EPI0_S13        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTM | GPIO_PIN_2)
#  define GPIO_EPI0_S14        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTM | GPIO_PIN_1)
#  define GPIO_EPI0_S15        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTM | GPIO_PIN_0)
#  define GPIO_EPI0_S16        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_0)
#  define GPIO_EPI0_S17        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_EPI0_S18        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_2)
#  define GPIO_EPI0_S19        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_3)
#  define GPIO_EPI0_S20        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_0)
#  define GPIO_EPI0_S21        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_1)
#  define GPIO_EPI0_S22        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_2)
#  define GPIO_EPI0_S23        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_3)
#  define GPIO_EPI0_S24        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_EPI0_S25        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_EPI0_S26        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_4)
#  define GPIO_EPI0_S27        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_EPI0_S28        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTB | GPIO_PIN_3)
#  define GPIO_EPI0_S29_1      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTN | GPIO_PIN_2)
#  define GPIO_EPI0_S29_2      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTP | GPIO_PIN_2)
#  define GPIO_EPI0_S30_1      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTN | GPIO_PIN_3)
#  define GPIO_EPI0_S30_2      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTP | GPIO_PIN_3)
#  define GPIO_EPI0_S31        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTK | GPIO_PIN_5)
#  define GPIO_EPI0_S32        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_EPI0_S33        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_5)
#  define GPIO_EPI0_S34        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTN | GPIO_PIN_4)
#  define GPIO_EPI0_S35        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTN | GPIO_PIN_5)

#  define GPIO_I2C0_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_I2C0_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C1_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_I2C1_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTR | GPIO_PIN_0)
#  define GPIO_I2C1_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C1_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTR | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_I2C2_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_I2C2_SCL_3      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTP | GPIO_PIN_5)
#  define GPIO_I2C2_SCL_4      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTR | GPIO_PIN_2)
#  define GPIO_I2C2_SCL_5      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTN | GPIO_PIN_5)
#  define GPIO_I2C2_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTL | GPIO_PIN_0 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SDA_3      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTP | GPIO_PIN_6 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SDA_4      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTR | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SDA_5      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTN | GPIO_PIN_4 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C3_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_I2C3_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_I2C3_SCL_3      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTR | GPIO_PIN_4)
#  define GPIO_I2C3_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C3_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C3_SDA_3      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTR | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C4_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_6)
#  define GPIO_I2C4_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_I2C4_SCL_3      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTR | GPIO_PIN_6)
#  define GPIO_I2C4_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C4_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C4_SDA_3      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTR | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C5_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_I2C5_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_I2C5_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C5_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C6_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_I2C6_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_6)
#  define GPIO_I2C6_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C6_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C7_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_I2C7_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_I2C7_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C7_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C8_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_I2C8_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_I2C8_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C8_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C9_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_I2C9_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTE | GPIO_PIN_6)
#  define GPIO_I2C9_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C9_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTE | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)

#  define GPIO_JTAG_SWCLK      (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_0)
#  define GPIO_JTAG_SWDIO      (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_1)
#  define GPIO_JTAG_SWO        (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_3)
#  define GPIO_JTAG_TCK        (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_0)
#  define GPIO_JTAG_TDI        (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_2)
#  define GPIO_JTAG_TDO        (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_3)
#  define GPIO_JTAG_TMS        (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_1)

#  define GPIO_LCD_AC          (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTJ | GPIO_PIN_6)
#  define GPIO_LCD_CP          (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTR | GPIO_PIN_0)
#  define GPIO_LCD_DATA00      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTR | GPIO_PIN_4)
#  define GPIO_LCD_DATA01      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTR | GPIO_PIN_5)
#  define GPIO_LCD_DATA02      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_7)
#  define GPIO_LCD_DATA03      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTR | GPIO_PIN_3)
#  define GPIO_LCD_DATA04      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTR | GPIO_PIN_6)
#  define GPIO_LCD_DATA05      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTR | GPIO_PIN_7)
#  define GPIO_LCD_DATA06      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTS | GPIO_PIN_4)
#  define GPIO_LCD_DATA07      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTS | GPIO_PIN_5)
#  define GPIO_LCD_DATA08      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTS | GPIO_PIN_6)
#  define GPIO_LCD_DATA09      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTS | GPIO_PIN_7)
#  define GPIO_LCD_DATA10      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTT | GPIO_PIN_0)
#  define GPIO_LCD_DATA11      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTT | GPIO_PIN_1)
#  define GPIO_LCD_DATA12      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTN | GPIO_PIN_7)
#  define GPIO_LCD_DATA13      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTN | GPIO_PIN_6)
#  define GPIO_LCD_DATA14      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTJ | GPIO_PIN_2)
#  define GPIO_LCD_DATA15      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTJ | GPIO_PIN_3)
#  define GPIO_LCD_DATA16      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTJ | GPIO_PIN_4)
#  define GPIO_LCD_DATA17      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTJ | GPIO_PIN_5)
#  define GPIO_LCD_DATA18      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTT | GPIO_PIN_2)
#  define GPIO_LCD_DATA19      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTT | GPIO_PIN_3)
#  define GPIO_LCD_DATA20      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTS | GPIO_PIN_0)
#  define GPIO_LCD_DATA21      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTS | GPIO_PIN_1)
#  define GPIO_LCD_DATA22      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTS | GPIO_PIN_2)
#  define GPIO_LCD_DATA23      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTS | GPIO_PIN_3)
#  define GPIO_LCD_FP          (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTR | GPIO_PIN_1)
#  define GPIO_LCD_LP          (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTR | GPIO_PIN_2)
#  define GPIO_LCD_MCLK        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_6)

#  define GPIO_M0_PWM_FAULT0_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_M0_PWM_FAULT0_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTS | GPIO_PIN_0)
#  define GPIO_M0_PWM_FAULT1_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_M0_PWM_FAULT1_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTS | GPIO_PIN_1)
#  define GPIO_M0_PWM_FAULT2_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_M0_PWM_FAULT2_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTS | GPIO_PIN_2)
#  define GPIO_M0_PWM_FAULT3_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTL | GPIO_PIN_0)
#  define GPIO_M0_PWM_FAULT3_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTS | GPIO_PIN_3)
#  define GPIO_M0_PWM0_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_M0_PWM0_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTR | GPIO_PIN_0)
#  define GPIO_M0_PWM1_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_M0_PWM1_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTR | GPIO_PIN_1)
#  define GPIO_M0_PWM2_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_M0_PWM2_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTR | GPIO_PIN_2)
#  define GPIO_M0_PWM3_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_M0_PWM3_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTR | GPIO_PIN_3)
#  define GPIO_M0_PWM4_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_M0_PWM4_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTR | GPIO_PIN_4)
#  define GPIO_M0_PWM5_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_1)
#  define GPIO_M0_PWM5_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTR | GPIO_PIN_5)
#  define GPIO_M0_PWM6_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_M0_PWM6_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTR | GPIO_PIN_6)
#  define GPIO_M0_PWM7_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_5)
#  define GPIO_M0_PWM7_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTR | GPIO_PIN_7)

#  define GPIO_NMI_1           (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_NMI_2           (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTE | GPIO_PIN_7)

#  define GPIO_OWALT_1         (GPIO_FUNC_PFIO     | GPIO_ALT_4  | GPIO_PORTP | GPIO_PIN_5)
#  define GPIO_OWALT_2         (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_OWIRE_1         (GPIO_FUNC_PFIO     | GPIO_ALT_4  | GPIO_PORTP | GPIO_PIN_4)
#  define GPIO_OWIRE_2         (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTE | GPIO_PIN_3)
#  define GPIO_OWIRE_3         (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_OWIRE_4         (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_6)
#  define GPIO_OWIRE_5         (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_7)
#  define GPIO_OWIRE_6         (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTP | GPIO_PIN_7)

#  define GPIO_QEI0_IDX_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTL | GPIO_PIN_3)
#  define GPIO_QEI0_IDX_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTS | GPIO_PIN_6)
#  define GPIO_QEI0_PHA_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_QEI0_PHA_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTS | GPIO_PIN_4)
#  define GPIO_QEI0_PHB_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTL | GPIO_PIN_2)
#  define GPIO_QEI0_PHB_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTS | GPIO_PIN_5)

#  define GPIO_SSI0_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_SSI0_FSS        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTA | GPIO_PIN_3)
#  define GPIO_SSI0_XDAT0      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_SSI0_XDAT1      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTA | GPIO_PIN_5)
#  define GPIO_SSI0_XDAT2      (GPIO_FUNC_PFIO     | GPIO_ALT_12 | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_SSI0_XDAT3      (GPIO_FUNC_PFIO     | GPIO_ALT_12 | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_SSI1_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_SSI1_FSS        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_SSI1_XDAT0      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_SSI1_XDAT1      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_SSI1_XDAT2      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_SSI1_XDAT3      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_SSI2_CLK_1      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_SSI2_CLK_2      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTG | GPIO_PIN_7)
#  define GPIO_SSI2_FSS_1      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_SSI2_FSS_2      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTG | GPIO_PIN_6)
#  define GPIO_SSI2_XDAT0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_SSI2_XDAT0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_SSI2_XDAT1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_SSI2_XDAT1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_SSI2_XDAT2_1    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_SSI2_XDAT2_2    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTG | GPIO_PIN_3)
#  define GPIO_SSI2_XDAT3_1    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_SSI2_XDAT3_2    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_SSI3_CLK_1      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_SSI3_CLK_2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTQ | GPIO_PIN_0)
#  define GPIO_SSI3_FSS_1      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_SSI3_FSS_2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTQ | GPIO_PIN_1)
#  define GPIO_SSI3_XDAT0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_SSI3_XDAT0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTQ | GPIO_PIN_2)
#  define GPIO_SSI3_XDAT1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_SSI3_XDAT1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTQ | GPIO_PIN_3)
#  define GPIO_SSI3_XDAT2_1    (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_SSI3_XDAT2_2    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTP | GPIO_PIN_0)
#  define GPIO_SSI3_XDAT3_1    (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTF | GPIO_PIN_5)
#  define GPIO_SSI3_XDAT3_2    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTP | GPIO_PIN_1)

#  define GPIO_SYSCON_DIVSCLK  (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTQ | GPIO_PIN_4)

#  define GPIO_TR_CLK          (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_TR_D0           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_TR_D1           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_TR_D2           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_TR_D3           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_4)

#  define GPIO_TIM0_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_TIM0_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_TIM0_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTL | GPIO_PIN_4)
#  define GPIO_TIM0_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTR | GPIO_PIN_4)
#  define GPIO_TIM0_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_TIM0_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_TIM0_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTL | GPIO_PIN_5)
#  define GPIO_TIM0_CCP1_4     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTR | GPIO_PIN_5)
#  define GPIO_TIM1_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_TIM1_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_TIM1_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTL | GPIO_PIN_6)
#  define GPIO_TIM1_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTR | GPIO_PIN_6)
#  define GPIO_TIM1_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_3)
#  define GPIO_TIM1_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_TIM1_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTL | GPIO_PIN_7)
#  define GPIO_TIM1_CCP1_4     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTR | GPIO_PIN_7)
#  define GPIO_TIM2_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_TIM2_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_0)
#  define GPIO_TIM2_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTS | GPIO_PIN_0)
#  define GPIO_TIM2_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_5)
#  define GPIO_TIM2_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_1)
#  define GPIO_TIM2_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTS | GPIO_PIN_1)
#  define GPIO_TIM3_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_TIM3_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_TIM3_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_2)
#  define GPIO_TIM3_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTS | GPIO_PIN_2)
#  define GPIO_TIM3_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_TIM3_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_TIM3_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_3)
#  define GPIO_TIM3_CCP1_4     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTS | GPIO_PIN_3)
#  define GPIO_TIM4_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_TIM4_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_TIM4_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_4)
#  define GPIO_TIM4_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTS | GPIO_PIN_4)
#  define GPIO_TIM4_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_TIM4_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_TIM4_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_5)
#  define GPIO_TIM4_CCP1_4     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTS | GPIO_PIN_5)
#  define GPIO_TIM5_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_TIM5_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_6)
#  define GPIO_TIM5_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTS | GPIO_PIN_6)
#  define GPIO_TIM5_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_3)
#  define GPIO_TIM5_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_7)
#  define GPIO_TIM5_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTS | GPIO_PIN_7)
#  define GPIO_TIM6_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_6)
#  define GPIO_TIM6_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTQ | GPIO_PIN_0)
#  define GPIO_TIM6_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTT | GPIO_PIN_0)
#  define GPIO_TIM6_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTP | GPIO_PIN_0)
#  define GPIO_TIM6_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_7)
#  define GPIO_TIM6_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTQ | GPIO_PIN_1)
#  define GPIO_TIM6_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTT | GPIO_PIN_1)
#  define GPIO_TIM6_CCP1_4     (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTP | GPIO_PIN_1)
#  define GPIO_TIM7_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_TIM7_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTQ | GPIO_PIN_2)
#  define GPIO_TIM7_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTT | GPIO_PIN_2)
#  define GPIO_TIM7_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_TIM7_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTQ | GPIO_PIN_3)
#  define GPIO_TIM7_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTT | GPIO_PIN_3)

#  define GPIO_UART0_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_UART0_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_6)
#  define GPIO_UART0_CTS_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_UART0_CTS_4     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_UART0_CTS_5     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTM | GPIO_PIN_4)
#  define GPIO_UART0_DCD_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_UART0_DCD_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTM | GPIO_PIN_5)
#  define GPIO_UART0_DCD_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTP | GPIO_PIN_3)
#  define GPIO_UART0_DSR_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_UART0_DSR_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTM | GPIO_PIN_6)
#  define GPIO_UART0_DSR_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTP | GPIO_PIN_4)
#  define GPIO_UART0_DTR_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_4)
#  define GPIO_UART0_DTR_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_2)
#  define GPIO_UART0_RI_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_5)
#  define GPIO_UART0_RI_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_UART0_RI_3      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTM | GPIO_PIN_7)
#  define GPIO_UART0_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_UART0_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_7)
#  define GPIO_UART0_RTS_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_UART0_RTS_4     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_UART0_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_UART0_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_UART1_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_1)
#  define GPIO_UART1_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_3)
#  define GPIO_UART1_DCD_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_2)
#  define GPIO_UART1_DCD_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_2)
#  define GPIO_UART1_DCD_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_6)
#  define GPIO_UART1_DSR_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_1)
#  define GPIO_UART1_DSR_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_3)
#  define GPIO_UART1_DSR_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTS | GPIO_PIN_2)
#  define GPIO_UART1_DTR_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_3)
#  define GPIO_UART1_DTR_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_4)
#  define GPIO_UART1_DTR_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTQ | GPIO_PIN_6)
#  define GPIO_UART1_RI_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_UART1_RI_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_5)
#  define GPIO_UART1_RI_3      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTQ | GPIO_PIN_7)
#  define GPIO_UART1_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_0)
#  define GPIO_UART1_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_0)
#  define GPIO_UART1_RTS_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_7)
#  define GPIO_UART1_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_UART1_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTQ | GPIO_PIN_4)
#  define GPIO_UART1_RX_3      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTR | GPIO_PIN_5)
#  define GPIO_UART1_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_UART1_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTQ | GPIO_PIN_5)
#  define GPIO_UART1_TX_3      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTR | GPIO_PIN_6)
#  define GPIO_UART2_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_UART2_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_3)
#  define GPIO_UART2_CTS_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_3)
#  define GPIO_UART2_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_UART2_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_2)
#  define GPIO_UART2_RTS_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_2)
#  define GPIO_UART2_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_UART2_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_UART2_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_UART2_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_UART3_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_5)
#  define GPIO_UART3_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_5)
#  define GPIO_UART3_CTS_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_5)
#  define GPIO_UART3_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_4)
#  define GPIO_UART3_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_4)
#  define GPIO_UART3_RTS_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_4)
#  define GPIO_UART3_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_UART3_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_0)
#  define GPIO_UART3_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_5)
#  define GPIO_UART3_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_1)
#  define GPIO_UART4_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_3)
#  define GPIO_UART4_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_7)
#  define GPIO_UART4_CTS_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_7)
#  define GPIO_UART4_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_6)
#  define GPIO_UART4_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_UART4_RTS_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_6)
#  define GPIO_UART4_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_UART4_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_UART4_RX_3      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTR | GPIO_PIN_1)
#  define GPIO_UART4_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_3)
#  define GPIO_UART4_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_UART4_TX_3      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTR | GPIO_PIN_0)
#  define GPIO_UART5_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_UART5_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_6)
#  define GPIO_UART5_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_UART5_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_7)
#  define GPIO_UART6_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_0)
#  define GPIO_UART6_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_1)
#  define GPIO_UART7_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_UART7_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_6)
#  define GPIO_UART7_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_UART7_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_7)

#  define GPIO_USB0_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTB | GPIO_PIN_3)
#  define GPIO_USB0_D0         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTL | GPIO_PIN_0)
#  define GPIO_USB0_D1         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_USB0_D2         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTL | GPIO_PIN_2)
#  define GPIO_USB0_D3         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTL | GPIO_PIN_3)
#  define GPIO_USB0_D4         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTL | GPIO_PIN_4)
#  define GPIO_USB0_D5         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTL | GPIO_PIN_5)
#  define GPIO_USB0_D6         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTP | GPIO_PIN_5)
#  define GPIO_USB0_D7         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTP | GPIO_PIN_4)
#  define GPIO_USB0_DIR        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTP | GPIO_PIN_3)
#  define GPIO_USB0_EPEN_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_11 | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_USB0_EPEN_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_USB0_EPEN_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_USB0_ID         (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_USB0_NXT        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTP | GPIO_PIN_2)
#  define GPIO_USB0_PFLT_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_USB0_PFLT_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_USB0_STP        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_USB0_VBUS       (GPIO_FUNC_ANIO                   | GPIO_PORTB | GPIO_PIN_1)

#  define GPIO_RTC_CLK_1       (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_RTC_CLK_2       (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_RTC_CLK_3       (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTP | GPIO_PIN_3)

#elif defined(CONFIG_ARCH_CHIP_TM4C1294NCPDT)
#  define GPIO_ADC_AIN0        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_3)
#  define GPIO_ADC_AIN1        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_2)
#  define GPIO_ADC_AIN2        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_1)
#  define GPIO_ADC_AIN3        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_0)
#  define GPIO_ADC_AIN4        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_ADC_AIN5        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_ADC_AIN6        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_ADC_AIN7        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_ADC_AIN8        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_ADC_AIN9        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_ADC_AIN10       (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_ADC_AIN11       (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_ADC_AIN12       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_ADC_AIN13       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_ADC_AIN14       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_ADC_AIN15       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_ADC_AIN16       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_ADC_AIN17       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_ADC_AIN18       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_ADC_AIN19       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_3)

#  define GPIO_CAN0_RX         (GPIO_FUNC_PFINPUT  | GPIO_ALT_7  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_CAN0_TX         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_7  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_CAN1_RX         (GPIO_FUNC_PFINPUT  | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_CAN1_TX         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_1)

#  define GPIO_CMP0_PIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_CMP0_NIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_CMP0_OUT_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_CMP0_OUT_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTL | GPIO_PIN_2)
#  define GPIO_CMP1_PIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_CMP1_NIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_CMP1_OUT_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_CMP1_OUT_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTL | GPIO_PIN_3)
#  define GPIO_CMP2_PIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTP | GPIO_PIN_0)
#  define GPIO_CMP2_NIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTP | GPIO_PIN_1)
#  define GPIO_CMP2_OUT        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_2)

#  define GPIO_EN0_LED0_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_EN0_LED0_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_EN0_LED1_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_EN0_LED1_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_EN0_LED2_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_EN0_LED2_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTK | GPIO_PIN_5)
#  define GPIO_EN0_PPS_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_EN0_PPS_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTJ | GPIO_PIN_0)

#  define GPIO_EPI0_S0_1       (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_EPI0_S0_2       (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_EPI0_S1_1       (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_EPI0_S1_2       (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_EPI0_S2_1       (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_EPI0_S2_2       (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_EPI0_S3_1       (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_EPI0_S3_2       (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTK | GPIO_PIN_3)
#  define GPIO_EPI0_S4         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_EPI0_S5         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_EPI0_S6         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_EPI0_S7         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_EPI0_S8         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_EPI0_S9         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_EPI0_S10        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTG | GPIO_PIN_1)
#  define GPIO_EPI0_S11        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_EPI0_S12        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTM | GPIO_PIN_3)
#  define GPIO_EPI0_S13        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTM | GPIO_PIN_2)
#  define GPIO_EPI0_S14        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTM | GPIO_PIN_1)
#  define GPIO_EPI0_S15        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTM | GPIO_PIN_0)
#  define GPIO_EPI0_S16        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTL | GPIO_PIN_0)
#  define GPIO_EPI0_S17        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_EPI0_S18        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTL | GPIO_PIN_2)
#  define GPIO_EPI0_S19        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTL | GPIO_PIN_3)
#  define GPIO_EPI0_S20        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTQ | GPIO_PIN_0)
#  define GPIO_EPI0_S21        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTQ | GPIO_PIN_1)
#  define GPIO_EPI0_S22        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTQ | GPIO_PIN_2)
#  define GPIO_EPI0_S23        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTQ | GPIO_PIN_3)
#  define GPIO_EPI0_S24        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_EPI0_S25        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_EPI0_S26        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTL | GPIO_PIN_4)
#  define GPIO_EPI0_S27        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_EPI0_S28        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTB | GPIO_PIN_3)
#  define GPIO_EPI0_S29_1      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTN | GPIO_PIN_2)
#  define GPIO_EPI0_S29_2      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTP | GPIO_PIN_2)
#  define GPIO_EPI0_S30_1      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTN | GPIO_PIN_3)
#  define GPIO_EPI0_S30_2      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTP | GPIO_PIN_3)
#  define GPIO_EPI0_S31        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTK | GPIO_PIN_5)
#  define GPIO_EPI0_S32        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_EPI0_S33        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTL | GPIO_PIN_5)
#  define GPIO_EPI0_S34        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTN | GPIO_PIN_4)
#  define GPIO_EPI0_S35        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTN | GPIO_PIN_5)

#  define GPIO_HIB_RTCCLK_1    (GPIO_FUNC_PFOUTPUT | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_HIB_RTCCLK_2    (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_HIB_RTCCLK_3    (GPIO_FUNC_PFOUTPUT | GPIO_ALT_7  | GPIO_PORTP | GPIO_PIN_3)

#  define GPIO_I2C0_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_I2C0_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C1_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_I2C1_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_I2C2_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTP | GPIO_PIN_5)
#  define GPIO_I2C2_SCL_3      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTN | GPIO_PIN_5)
#  define GPIO_I2C2_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTL | GPIO_PIN_0 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTN | GPIO_PIN_4 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C3_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_I2C3_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C4_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_I2C4_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C5_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_I2C5_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_I2C5_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C5_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C6_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_I2C6_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C7_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_I2C7_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_I2C7_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C7_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C8_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_I2C8_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_I2C8_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C8_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C9_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_I2C9_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)

#  define GPIO_JTAG_SWCLK      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_0)
#  define GPIO_JTAG_SWDIO      (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_1)
#  define GPIO_JTAG_SWO        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_3)
#  define GPIO_JTAG_TCK        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_0)
#  define GPIO_JTAG_TDI        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_2)
#  define GPIO_JTAG_TDO        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_3)
#  define GPIO_JTAG_TMS        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_1)

#  define GPIO_M0_PWM_FAULT0   (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_M0_PWM_FAULT1   (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_M0_PWM_FAULT2   (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_M0_PWM_FAULT3   (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTL | GPIO_PIN_0)
#  define GPIO_M0_PWM0         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_M0_PWM1         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_M0_PWM2         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_M0_PWM3         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_M0_PWM4         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_M0_PWM5         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_1)
#  define GPIO_M0_PWM6         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_M0_PWM7         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_5)

#  define GPIO_NMI             (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTD | GPIO_PIN_7)

#  define GPIO_QEI0_IDX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTL | GPIO_PIN_3)
#  define GPIO_QEI0_PHA        (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_QEI0_PHB        (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTL | GPIO_PIN_2)

#  define GPIO_RTC_CLK_1       (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_RTC_CLK_2       (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_RTC_CLK_3       (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTP | GPIO_PIN_3)

#  define GPIO_SSI0_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_SSI0_FSS        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTA | GPIO_PIN_3)
#  define GPIO_SSI0_TX         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_SSI0_XDAT0      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_SSI0_RX         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTA | GPIO_PIN_5)
#  define GPIO_SSI0_XDAT1      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTA | GPIO_PIN_5)
#  define GPIO_SSI0_XDAT2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_SSI0_XDAT3      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_SSI1_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_SSI1_FSS        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_SSI1_TX         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_SSI1_XDAT0      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_SSI1_RX         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_SSI1_XDAT1      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_SSI1_XDAT2      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_SSI1_XDAT3      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_SSI2_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_SSI2_FSS        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_SSI2_TX         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_SSI2_XDAT0      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_SSI2_RX         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_SSI2_XDAT1      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_SSI2_XDAT2      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_SSI2_XDAT3      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_SSI3_CLK_1      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_SSI3_CLK_2      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_0)
#  define GPIO_SSI3_FSS_1      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_SSI3_FSS_2      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_1)
#  define GPIO_SSI3_TX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_SSI3_TX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_2)
#  define GPIO_SSI3_XDAT0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_SSI3_XDAT0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_2)
#  define GPIO_SSI3_RX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_SSI3_RX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_3)
#  define GPIO_SSI3_XDAT1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_SSI3_XDAT1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_3)
#  define GPIO_SSI3_XDAT2_1    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_SSI3_XDAT2_2    (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTP | GPIO_PIN_0)
#  define GPIO_SSI3_XDAT3      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTP | GPIO_PIN_1)

#  define GPIO_SYSCON_DIVSCLK  (GPIO_FUNC_PFOUTPUT | GPIO_ALT_7  | GPIO_PORTQ | GPIO_PIN_4)

#  define GPIO_TR_CLK          (GPIO_FUNC_PFOUTPUT | GPIO_ALT_15 | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_TR_D0           (GPIO_FUNC_PFOUTPUT | GPIO_ALT_15 | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_TR_D1           (GPIO_FUNC_PFOUTPUT | GPIO_ALT_15 | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_TR_D2           (GPIO_FUNC_PFOUTPUT | GPIO_ALT_15 | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_TR_D3           (GPIO_FUNC_PFOUTPUT | GPIO_ALT_15 | GPIO_PORTF | GPIO_PIN_4)

#  define GPIO_TIM0_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_TIM0_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_TIM0_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTL | GPIO_PIN_4)
#  define GPIO_TIM0_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_TIM0_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_TIM0_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTL | GPIO_PIN_5)
#  define GPIO_TIM1_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_TIM1_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_TIM1_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTL | GPIO_PIN_6)
#  define GPIO_TIM1_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_3)
#  define GPIO_TIM1_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_TIM1_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTL | GPIO_PIN_7)
#  define GPIO_TIM2_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_TIM2_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_0)
#  define GPIO_TIM2_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_5)
#  define GPIO_TIM2_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_1)
#  define GPIO_TIM3_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_TIM3_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_TIM3_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_2)
#  define GPIO_TIM3_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_TIM3_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_TIM3_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_3)
#  define GPIO_TIM4_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_TIM4_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_TIM4_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_4)
#  define GPIO_TIM4_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_TIM4_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_TIM4_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_5)
#  define GPIO_TIM5_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_TIM5_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_6)
#  define GPIO_TIM5_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_3)
#  define GPIO_TIM5_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_7)

#  define GPIO_UART0_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_UART0_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_UART0_CTS_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTM | GPIO_PIN_4)
#  define GPIO_UART0_DCD_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_UART0_DCD_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTM | GPIO_PIN_5)
#  define GPIO_UART0_DCD_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTP | GPIO_PIN_3)
#  define GPIO_UART0_DSR_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_UART0_DSR_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTM | GPIO_PIN_6)
#  define GPIO_UART0_DSR_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTP | GPIO_PIN_4)
#  define GPIO_UART0_DTR       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_2)
#  define GPIO_UART0_RI_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_UART0_RI_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTM | GPIO_PIN_7)
#  define GPIO_UART0_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_UART0_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_UART0_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_UART0_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_UART1_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_1)
#  define GPIO_UART1_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_3)
#  define GPIO_UART1_DCD_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_2)
#  define GPIO_UART1_DCD_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_2)
#  define GPIO_UART1_DSR_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_1)
#  define GPIO_UART1_DSR_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_3)
#  define GPIO_UART1_DTR_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_3)
#  define GPIO_UART1_DTR_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_4)
#  define GPIO_UART1_RI_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_UART1_RI_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_5)
#  define GPIO_UART1_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_0)
#  define GPIO_UART1_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_0)
#  define GPIO_UART1_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_UART1_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTQ | GPIO_PIN_4)
#  define GPIO_UART1_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_UART2_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_UART2_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_3)
#  define GPIO_UART2_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_UART2_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_2)
#  define GPIO_UART2_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_UART2_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_UART2_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_UART2_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_UART3_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_5)
#  define GPIO_UART3_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_5)
#  define GPIO_UART3_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_4)
#  define GPIO_UART3_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_4)
#  define GPIO_UART3_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_UART3_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_0)
#  define GPIO_UART3_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_5)
#  define GPIO_UART3_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_1)
#  define GPIO_UART4_CTS       (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_3)
#  define GPIO_UART4_RTS       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_UART4_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_UART4_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_UART4_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_3)
#  define GPIO_UART4_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_UART5_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_UART5_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_UART6_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_0)
#  define GPIO_UART6_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_1)
#  define GPIO_UART7_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_UART7_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_5)

#  define GPIO_USB0_CLK        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_14 | GPIO_PORTB | GPIO_PIN_3)
#  define GPIO_USB0_D0         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_0)
#  define GPIO_USB0_D1         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_USB0_D2         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_2)
#  define GPIO_USB0_D3         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_3)
#  define GPIO_USB0_D4         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_4)
#  define GPIO_USB0_D5         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_5)
#  define GPIO_USB0_D6         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTP | GPIO_PIN_5)
#  define GPIO_USB0_D7         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTP | GPIO_PIN_4)
#  define GPIO_USB0_DIR        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_14 | GPIO_PORTP | GPIO_PIN_3)
#  define GPIO_USB0_DM         (GPIO_FUNC_ANIO                   | GPIO_PORTL | GPIO_PIN_7)
#  define GPIO_USB0_DP         (GPIO_FUNC_ANIO                   | GPIO_PORTL | GPIO_PIN_6)
#  define GPIO_USB0_EPEN_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_USB0_EPEN_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_11 | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_USB0_EPEN_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_USB0_ID         (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_USB0_NXT        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTP | GPIO_PIN_2)
#  define GPIO_USB0_PFLT_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_USB0_PFLT_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_USB0_STP        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_USB0_VBUS       (GPIO_FUNC_ANIO                   | GPIO_PORTB | GPIO_PIN_1)

#elif defined(CONFIG_ARCH_CHIP_TM4C129ENCPDT)
#  define GPIO_ADC_AIN0        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_3)
#  define GPIO_ADC_AIN1        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_2)
#  define GPIO_ADC_AIN2        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_1)
#  define GPIO_ADC_AIN3        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_0)
#  define GPIO_ADC_AIN4        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_ADC_AIN5        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_ADC_AIN6        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_ADC_AIN7        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_ADC_AIN8        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_ADC_AIN9        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_ADC_AIN10       (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_ADC_AIN11       (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_ADC_AIN12       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_ADC_AIN13       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_ADC_AIN14       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_ADC_AIN15       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_ADC_AIN16       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_ADC_AIN17       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_ADC_AIN18       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_ADC_AIN19       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_3)

#  define GPIO_CAN0_RX         (GPIO_FUNC_PFINPUT  | GPIO_ALT_7  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_CAN0_TX         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_7  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_CAN1_RX         (GPIO_FUNC_PFINPUT  | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_CAN1_TX         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_1)

#  define GPIO_CMP0_PIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_CMP0_NIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_CMP0_OUT_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_CMP0_OUT_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTL | GPIO_PIN_2)
#  define GPIO_CMP1_PIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_CMP1_NIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_CMP1_OUT_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_CMP1_OUT_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTL | GPIO_PIN_3)
#  define GPIO_CMP2_PIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTP | GPIO_PIN_0)
#  define GPIO_CMP2_NIN        (GPIO_FUNC_ANINPUT                | GPIO_PORTP | GPIO_PIN_1)
#  define GPIO_CMP2_OUT        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_2)

#  define GPIO_EN0_LED0_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_EN0_LED0_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_EN0_LED1_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_EN0_LED1_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_EN0_LED2_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_EN0_LED2_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTK | GPIO_PIN_5)
#  define GPIO_EN0_PPS_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_EN0_PPS_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTJ | GPIO_PIN_0)

#  define GPIO_EPI0_S0_1       (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_EPI0_S0_2       (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_EPI0_S1_1       (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_EPI0_S1_2       (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_EPI0_S2_1       (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_EPI0_S2_2       (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_EPI0_S3_1       (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_EPI0_S3_2       (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTK | GPIO_PIN_3)
#  define GPIO_EPI0_S4         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_EPI0_S5         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_EPI0_S6         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_EPI0_S7         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_EPI0_S8         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_EPI0_S9         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_EPI0_S10        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTG | GPIO_PIN_1)
#  define GPIO_EPI0_S11        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_EPI0_S12        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTM | GPIO_PIN_3)
#  define GPIO_EPI0_S13        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTM | GPIO_PIN_2)
#  define GPIO_EPI0_S14        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTM | GPIO_PIN_1)
#  define GPIO_EPI0_S15        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTM | GPIO_PIN_0)
#  define GPIO_EPI0_S16        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTL | GPIO_PIN_0)
#  define GPIO_EPI0_S17        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_EPI0_S18        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTL | GPIO_PIN_2)
#  define GPIO_EPI0_S19        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTL | GPIO_PIN_3)
#  define GPIO_EPI0_S20        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTQ | GPIO_PIN_0)
#  define GPIO_EPI0_S21        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTQ | GPIO_PIN_1)
#  define GPIO_EPI0_S22        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTQ | GPIO_PIN_2)
#  define GPIO_EPI0_S23        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTQ | GPIO_PIN_3)
#  define GPIO_EPI0_S24        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_EPI0_S25        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_EPI0_S26        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTL | GPIO_PIN_4)
#  define GPIO_EPI0_S27        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_EPI0_S28        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTB | GPIO_PIN_3)
#  define GPIO_EPI0_S29_1      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTN | GPIO_PIN_2)
#  define GPIO_EPI0_S29_2      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTP | GPIO_PIN_2)
#  define GPIO_EPI0_S30_1      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTN | GPIO_PIN_3)
#  define GPIO_EPI0_S30_2      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTP | GPIO_PIN_3)
#  define GPIO_EPI0_S31        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTK | GPIO_PIN_5)
#  define GPIO_EPI0_S32        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_EPI0_S33        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTL | GPIO_PIN_5)
#  define GPIO_EPI0_S34        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTN | GPIO_PIN_4)
#  define GPIO_EPI0_S35        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTN | GPIO_PIN_5)

#  define GPIO_HIB_RTCCLK_1    (GPIO_FUNC_PFOUTPUT | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_HIB_RTCCLK_2    (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_HIB_RTCCLK_3    (GPIO_FUNC_PFOUTPUT | GPIO_ALT_7  | GPIO_PORTP | GPIO_PIN_3)

#  define GPIO_I2C0_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_I2C0_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C1_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_I2C1_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_I2C2_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTP | GPIO_PIN_5)
#  define GPIO_I2C2_SCL_3      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTN | GPIO_PIN_5)
#  define GPIO_I2C2_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTL | GPIO_PIN_0 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTN | GPIO_PIN_4 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C3_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_I2C3_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C4_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_I2C4_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C5_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_I2C5_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_I2C5_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C5_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C6_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_I2C6_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C7_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_I2C7_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_I2C7_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C7_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C8_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_I2C8_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_I2C8_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C8_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C9_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_I2C9_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)

#  define GPIO_JTAG_SWCLK      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_0)
#  define GPIO_JTAG_SWDIO      (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_1)
#  define GPIO_JTAG_SWO        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_3)
#  define GPIO_JTAG_TCK        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_0)
#  define GPIO_JTAG_TDI        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_2)
#  define GPIO_JTAG_TDO        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_3)
#  define GPIO_JTAG_TMS        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_1)

#  define GPIO_M0_PWM_FAULT0   (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_M0_PWM_FAULT1   (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_M0_PWM_FAULT2   (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_M0_PWM_FAULT3   (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTL | GPIO_PIN_0)
#  define GPIO_M0_PWM0         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_M0_PWM1         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_M0_PWM2         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_M0_PWM3         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_M0_PWM4         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_M0_PWM5         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_1)
#  define GPIO_M0_PWM6         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_M0_PWM7         (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_5)

#  define GPIO_NMI             (GPIO_FUNC_PFINPUT  | GPIO_ALT_8  | GPIO_PORTD | GPIO_PIN_7)

#  define GPIO_QEI0_IDX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTL | GPIO_PIN_3)
#  define GPIO_QEI0_PHA        (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_QEI0_PHB        (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTL | GPIO_PIN_2)

#  define GPIO_RTC_CLK_1       (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_RTC_CLK_2       (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_RTC_CLK_3       (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTP | GPIO_PIN_3)

#  define GPIO_SSI0_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_SSI0_FSS        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTA | GPIO_PIN_3)
#  define GPIO_SSI0_TX         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_SSI0_XDAT0      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_SSI0_RX         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTA | GPIO_PIN_5)
#  define GPIO_SSI0_XDAT1      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTA | GPIO_PIN_5)
#  define GPIO_SSI0_XDAT2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_SSI0_XDAT3      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_SSI1_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_SSI1_FSS        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_SSI1_TX         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_SSI1_XDAT0      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_SSI1_RX         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_SSI1_XDAT1      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_SSI1_XDAT2      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_SSI1_XDAT3      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_SSI2_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_SSI2_FSS        (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_SSI2_TX         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_SSI2_XDAT0      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_SSI2_RX         (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_SSI2_XDAT1      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_SSI2_XDAT2      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_SSI2_XDAT3      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_SSI3_CLK_1      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_SSI3_CLK_2      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_0)
#  define GPIO_SSI3_FSS_1      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_SSI3_FSS_2      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_1)
#  define GPIO_SSI3_TX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_SSI3_TX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_2)
#  define GPIO_SSI3_XDAT0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_SSI3_XDAT0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_2)
#  define GPIO_SSI3_RX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_SSI3_RX_2       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_3)
#  define GPIO_SSI3_XDAT1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_SSI3_XDAT1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_3)
#  define GPIO_SSI3_XDAT2_1    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_SSI3_XDAT2_2    (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTP | GPIO_PIN_0)
#  define GPIO_SSI3_XDAT3      (GPIO_FUNC_PFIO     | GPIO_ALT_15 | GPIO_PORTP | GPIO_PIN_1)

#  define GPIO_SYSCON_DIVSCLK  (GPIO_FUNC_PFOUTPUT | GPIO_ALT_7  | GPIO_PORTQ | GPIO_PIN_4)

#  define GPIO_TR_CLK          (GPIO_FUNC_PFOUTPUT | GPIO_ALT_15 | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_TR_D0           (GPIO_FUNC_PFOUTPUT | GPIO_ALT_15 | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_TR_D1           (GPIO_FUNC_PFOUTPUT | GPIO_ALT_15 | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_TR_D2           (GPIO_FUNC_PFOUTPUT | GPIO_ALT_15 | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_TR_D3           (GPIO_FUNC_PFOUTPUT | GPIO_ALT_15 | GPIO_PORTF | GPIO_PIN_4)

#  define GPIO_TIM0_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_TIM0_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_TIM0_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTL | GPIO_PIN_4)
#  define GPIO_TIM0_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_TIM0_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_TIM0_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTL | GPIO_PIN_5)
#  define GPIO_TIM1_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_TIM1_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_TIM1_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTL | GPIO_PIN_6)
#  define GPIO_TIM1_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_3)
#  define GPIO_TIM1_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_TIM1_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTL | GPIO_PIN_7)
#  define GPIO_TIM2_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_TIM2_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_0)
#  define GPIO_TIM2_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_5)
#  define GPIO_TIM2_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_1)
#  define GPIO_TIM3_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_TIM3_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_TIM3_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_2)
#  define GPIO_TIM3_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_TIM3_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_TIM3_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_3)
#  define GPIO_TIM4_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_TIM4_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_TIM4_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_4)
#  define GPIO_TIM4_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_TIM4_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_TIM4_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_5)
#  define GPIO_TIM5_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_TIM5_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_6)
#  define GPIO_TIM5_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_3)
#  define GPIO_TIM5_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_7)

#  define GPIO_UART0_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_UART0_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_UART0_CTS_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTM | GPIO_PIN_4)
#  define GPIO_UART0_DCD_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_UART0_DCD_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTM | GPIO_PIN_5)
#  define GPIO_UART0_DCD_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTP | GPIO_PIN_3)
#  define GPIO_UART0_DSR_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_UART0_DSR_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTM | GPIO_PIN_6)
#  define GPIO_UART0_DSR_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTP | GPIO_PIN_4)
#  define GPIO_UART0_DTR       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_2)
#  define GPIO_UART0_RI_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_UART0_RI_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTM | GPIO_PIN_7)
#  define GPIO_UART0_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_UART0_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_UART0_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_UART0_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_UART1_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_1)
#  define GPIO_UART1_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_3)
#  define GPIO_UART1_DCD_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_2)
#  define GPIO_UART1_DCD_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_2)
#  define GPIO_UART1_DSR_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_1)
#  define GPIO_UART1_DSR_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_3)
#  define GPIO_UART1_DTR_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_3)
#  define GPIO_UART1_DTR_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_4)
#  define GPIO_UART1_RI_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_UART1_RI_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_5)
#  define GPIO_UART1_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_0)
#  define GPIO_UART1_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_0)
#  define GPIO_UART1_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_UART1_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTQ | GPIO_PIN_4)
#  define GPIO_UART1_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_UART2_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_UART2_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_3)
#  define GPIO_UART2_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_UART2_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_2)
#  define GPIO_UART2_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_UART2_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_UART2_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_UART2_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_UART3_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_5)
#  define GPIO_UART3_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_5)
#  define GPIO_UART3_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_4)
#  define GPIO_UART3_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_4)
#  define GPIO_UART3_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_UART3_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_0)
#  define GPIO_UART3_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_5)
#  define GPIO_UART3_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_1)
#  define GPIO_UART4_CTS       (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_3)
#  define GPIO_UART4_RTS       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_UART4_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_UART4_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_UART4_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_3)
#  define GPIO_UART4_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_UART5_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_UART5_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_UART6_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_0)
#  define GPIO_UART6_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_1)
#  define GPIO_UART7_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_UART7_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_5)

#  define GPIO_USB0_CLK        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_14 | GPIO_PORTB | GPIO_PIN_3)
#  define GPIO_USB0_D0         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_0)
#  define GPIO_USB0_D1         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_USB0_D2         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_2)
#  define GPIO_USB0_D3         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_3)
#  define GPIO_USB0_D4         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_4)
#  define GPIO_USB0_D5         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_5)
#  define GPIO_USB0_D6         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTP | GPIO_PIN_5)
#  define GPIO_USB0_D7         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTP | GPIO_PIN_4)
#  define GPIO_USB0_DIR        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_14 | GPIO_PORTP | GPIO_PIN_3)
#  define GPIO_USB0_DM         (GPIO_FUNC_ANIO                   | GPIO_PORTL | GPIO_PIN_7)
#  define GPIO_USB0_DP         (GPIO_FUNC_ANIO                   | GPIO_PORTL | GPIO_PIN_6)
#  define GPIO_USB0_EPEN_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_USB0_EPEN_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_11 | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_USB0_EPEN_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_USB0_ID         (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_USB0_NXT        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTP | GPIO_PIN_2)
#  define GPIO_USB0_PFLT_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_USB0_PFLT_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_USB0_STP        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_USB0_VBUS       (GPIO_FUNC_ANIO                   | GPIO_PORTB | GPIO_PIN_1)

#elif defined(CONFIG_ARCH_CHIP_TM4C129ENCZAD)

#  define GPIO_ADC_AIN0        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_3)
#  define GPIO_ADC_AIN1        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_2)
#  define GPIO_ADC_AIN2        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_1)
#  define GPIO_ADC_AIN3        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_0)
#  define GPIO_ADC_AIN4        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_ADC_AIN5        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_ADC_AIN6        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_ADC_AIN7        (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_ADC_AIN8        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_ADC_AIN9        (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_ADC_AIN10       (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_ADC_AIN11       (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_ADC_AIN12       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_ADC_AIN13       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_ADC_AIN14       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_ADC_AIN15       (GPIO_FUNC_ANINPUT                | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_ADC_AIN16       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_ADC_AIN17       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_ADC_AIN18       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_ADC_AIN19       (GPIO_FUNC_ANINPUT                | GPIO_PORTK | GPIO_PIN_3)
#  define GPIO_ADC_AIN20       (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_6)
#  define GPIO_ADC_AIN21       (GPIO_FUNC_ANINPUT                | GPIO_PORTE | GPIO_PIN_7)
#  define GPIO_ADC_AIN22       (GPIO_FUNC_ANINPUT                | GPIO_PORTP | GPIO_PIN_7)
#  define GPIO_ADC_AIN23       (GPIO_FUNC_ANINPUT                | GPIO_PORTP | GPIO_PIN_6)

#  define GPIO_CAN0_RX_1       (GPIO_FUNC_PFINPUT  | GPIO_ALT_7  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_CAN0_RX_2       (GPIO_FUNC_PFINPUT  | GPIO_ALT_7  | GPIO_PORTT | GPIO_PIN_0)
#  define GPIO_CAN0_TX_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_7  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_CAN0_TX_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_7  | GPIO_PORTT | GPIO_PIN_1)
#  define GPIO_CAN1_RX_1       (GPIO_FUNC_PFINPUT  | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_CAN1_RX_2       (GPIO_FUNC_PFINPUT  | GPIO_ALT_7  | GPIO_PORTT | GPIO_PIN_2)
#  define GPIO_CAN1_TX_1       (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_CAN1_TX_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_7  | GPIO_PORTT | GPIO_PIN_3)

#  define GPIO_CMP0_OUT_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_CMP0_OUT_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTL | GPIO_PIN_2)
#  define GPIO_CMP1_OUT_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_CMP1_OUT_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTL | GPIO_PIN_3)
#  define GPIO_CMP2_OUT        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_2)

#  define GPIO_EN0_COL         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTM | GPIO_PIN_7)
#  define GPIO_EN0_CRS         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTM | GPIO_PIN_6)
#  define GPIO_EN0_INTRN_1     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_EN0_INTRN_2     (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTP | GPIO_PIN_0)
#  define GPIO_EN0_LED0_1      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_EN0_LED0_2      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_EN0_LED1_1      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_EN0_LED1_2      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_EN0_LED2_1      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_EN0_LED2_2      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTK | GPIO_PIN_5)
#  define GPIO_EN0_MDC_1       (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_EN0_MDC_2       (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_EN0_MDIO_1      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTB | GPIO_PIN_3)
#  define GPIO_EN0_MDIO_2      (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_EN0_PPS_1       (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_EN0_PPS_2       (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTH | GPIO_PIN_5)
#  define GPIO_EN0_PPS_3       (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTJ | GPIO_PIN_0)
#  define GPIO_EN0_RREF_CLK    (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTM | GPIO_PIN_4)
#  define GPIO_EN0_RXCK        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_EN0_RXD0_1      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTQ | GPIO_PIN_5)
#  define GPIO_EN0_RXD0_2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTT | GPIO_PIN_0)
#  define GPIO_EN0_RXD1_1      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTQ | GPIO_PIN_6)
#  define GPIO_EN0_RXD1_2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTT | GPIO_PIN_1)
#  define GPIO_EN0_RXD2        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTK | GPIO_PIN_5)
#  define GPIO_EN0_RXD3        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_EN0_RXDV_1      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTG | GPIO_PIN_7)
#  define GPIO_EN0_RXDV_2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTS | GPIO_PIN_7)
#  define GPIO_EN0_RXER_1      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTG | GPIO_PIN_6)
#  define GPIO_EN0_RXER_2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTS | GPIO_PIN_6)
#  define GPIO_EN0_TXCK        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_EN0_TXD0_1      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_EN0_TXD0_2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTS | GPIO_PIN_4)
#  define GPIO_EN0_TXD1_1      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_EN0_TXD1_2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTS | GPIO_PIN_5)
#  define GPIO_EN0_TXD2        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_EN0_TXD3        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_EN0_TXEN_1      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTG | GPIO_PIN_3)
#  define GPIO_EN0_TXEN_2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTR | GPIO_PIN_7)
#  define GPIO_EN0_TXER        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTN | GPIO_PIN_6)

#  define GPIO_EPI0_S0_1       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_EPI0_S0_2       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_EPI0_S1_1       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_EPI0_S1_2       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_EPI0_S2_1       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_EPI0_S2_2       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_EPI0_S3_1       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_EPI0_S3_2       (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTK | GPIO_PIN_3)
#  define GPIO_EPI0_S4         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_EPI0_S5         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_EPI0_S6         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_EPI0_S7         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_EPI0_S8         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_EPI0_S9         (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_EPI0_S10        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTG | GPIO_PIN_1)
#  define GPIO_EPI0_S11        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_EPI0_S12        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTM | GPIO_PIN_3)
#  define GPIO_EPI0_S13        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTM | GPIO_PIN_2)
#  define GPIO_EPI0_S14        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTM | GPIO_PIN_1)
#  define GPIO_EPI0_S15        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTM | GPIO_PIN_0)
#  define GPIO_EPI0_S16        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_0)
#  define GPIO_EPI0_S17        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_EPI0_S18        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_2)
#  define GPIO_EPI0_S19        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_3)
#  define GPIO_EPI0_S20        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_0)
#  define GPIO_EPI0_S21        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_1)
#  define GPIO_EPI0_S22        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_2)
#  define GPIO_EPI0_S23        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTQ | GPIO_PIN_3)
#  define GPIO_EPI0_S24        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_EPI0_S25        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_EPI0_S26        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_4)
#  define GPIO_EPI0_S27        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_EPI0_S28        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTB | GPIO_PIN_3)
#  define GPIO_EPI0_S29_1      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTN | GPIO_PIN_2)
#  define GPIO_EPI0_S29_2      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTP | GPIO_PIN_2)
#  define GPIO_EPI0_S30_1      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTN | GPIO_PIN_3)
#  define GPIO_EPI0_S30_2      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTP | GPIO_PIN_3)
#  define GPIO_EPI0_S31        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTK | GPIO_PIN_5)
#  define GPIO_EPI0_S32        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_EPI0_S33        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTL | GPIO_PIN_5)
#  define GPIO_EPI0_S34        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTN | GPIO_PIN_4)
#  define GPIO_EPI0_S35        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTN | GPIO_PIN_5)

#  define GPIO_I2C0_SCL        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_I2C0_SDA        (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C1_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_I2C1_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTR | GPIO_PIN_0)
#  define GPIO_I2C1_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C1_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTR | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_I2C2_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_I2C2_SCL_3      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTP | GPIO_PIN_5)
#  define GPIO_I2C2_SCL_4      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTR | GPIO_PIN_2)
#  define GPIO_I2C2_SCL_5      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_3  | GPIO_PORTN | GPIO_PIN_5)
#  define GPIO_I2C2_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTL | GPIO_PIN_0 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SDA_3      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTP | GPIO_PIN_6 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SDA_4      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTR | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C2_SDA_5      (GPIO_FUNC_PFODIO   | GPIO_ALT_3  | GPIO_PORTN | GPIO_PIN_4 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C3_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_I2C3_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_I2C3_SCL_3      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTR | GPIO_PIN_4)
#  define GPIO_I2C3_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C3_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C3_SDA_3      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTR | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C4_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_6)
#  define GPIO_I2C4_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_I2C4_SCL_3      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTR | GPIO_PIN_6)
#  define GPIO_I2C4_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTG | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C4_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTK | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C4_SDA_3      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTR | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C5_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_I2C5_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_I2C5_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C5_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C6_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_I2C6_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_6)
#  define GPIO_I2C6_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C6_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTB | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C7_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_I2C7_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_I2C7_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_5 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C7_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C8_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_I2C8_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_I2C8_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C8_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTD | GPIO_PIN_3 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C9_SCL_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_I2C9_SCL_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTE | GPIO_PIN_6)
#  define GPIO_I2C9_SDA_1      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTA | GPIO_PIN_1 | GPIO_PADTYPE_ODWPU)
#  define GPIO_I2C9_SDA_2      (GPIO_FUNC_PFODIO   | GPIO_ALT_2  | GPIO_PORTE | GPIO_PIN_7 | GPIO_PADTYPE_ODWPU)

#  define GPIO_JTAG_SWCLK      (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_0)
#  define GPIO_JTAG_SWDIO      (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_1)
#  define GPIO_JTAG_SWO        (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_3)
#  define GPIO_JTAG_TCK        (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_0)
#  define GPIO_JTAG_TDI        (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_2)
#  define GPIO_JTAG_TDO        (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_3)
#  define GPIO_JTAG_TMS        (GPIO_FUNC_PFIO     | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_1)

#  define GPIO_LCD_AC          (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTJ | GPIO_PIN_6)
#  define GPIO_LCD_CP          (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTR | GPIO_PIN_0)
#  define GPIO_LCD_DATA00      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTR | GPIO_PIN_4)
#  define GPIO_LCD_DATA01      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTR | GPIO_PIN_5)
#  define GPIO_LCD_DATA02      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_7)
#  define GPIO_LCD_DATA03      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTR | GPIO_PIN_3)
#  define GPIO_LCD_DATA04      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTR | GPIO_PIN_6)
#  define GPIO_LCD_DATA05      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTR | GPIO_PIN_7)
#  define GPIO_LCD_DATA06      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTS | GPIO_PIN_4)
#  define GPIO_LCD_DATA07      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTS | GPIO_PIN_5)
#  define GPIO_LCD_DATA08      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTS | GPIO_PIN_6)
#  define GPIO_LCD_DATA09      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTS | GPIO_PIN_7)
#  define GPIO_LCD_DATA10      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTT | GPIO_PIN_0)
#  define GPIO_LCD_DATA11      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTT | GPIO_PIN_1)
#  define GPIO_LCD_DATA12      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTN | GPIO_PIN_7)
#  define GPIO_LCD_DATA13      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTN | GPIO_PIN_6)
#  define GPIO_LCD_DATA14      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTJ | GPIO_PIN_2)
#  define GPIO_LCD_DATA15      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTJ | GPIO_PIN_3)
#  define GPIO_LCD_DATA16      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTJ | GPIO_PIN_4)
#  define GPIO_LCD_DATA17      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTJ | GPIO_PIN_5)
#  define GPIO_LCD_DATA18      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTT | GPIO_PIN_2)
#  define GPIO_LCD_DATA19      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTT | GPIO_PIN_3)
#  define GPIO_LCD_DATA20      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTS | GPIO_PIN_0)
#  define GPIO_LCD_DATA21      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTS | GPIO_PIN_1)
#  define GPIO_LCD_DATA22      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTS | GPIO_PIN_2)
#  define GPIO_LCD_DATA23      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTS | GPIO_PIN_3)
#  define GPIO_LCD_FP          (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTR | GPIO_PIN_1)
#  define GPIO_LCD_LP          (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTR | GPIO_PIN_2)
#  define GPIO_LCD_MCLK        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_6)

#  define GPIO_M0_PWM_FAULT0_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_M0_PWM_FAULT0_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTS | GPIO_PIN_0)
#  define GPIO_M0_PWM_FAULT1_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_6)
#  define GPIO_M0_PWM_FAULT1_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTS | GPIO_PIN_1)
#  define GPIO_M0_PWM_FAULT2_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_M0_PWM_FAULT2_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTS | GPIO_PIN_2)
#  define GPIO_M0_PWM_FAULT3_1 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTL | GPIO_PIN_0)
#  define GPIO_M0_PWM_FAULT3_2 (GPIO_FUNC_PFINPUT  | GPIO_ALT_6  | GPIO_PORTS | GPIO_PIN_3)
#  define GPIO_M0_PWM0_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_M0_PWM0_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTR | GPIO_PIN_0)
#  define GPIO_M0_PWM1_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_M0_PWM1_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTR | GPIO_PIN_1)
#  define GPIO_M0_PWM2_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_M0_PWM2_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTR | GPIO_PIN_2)
#  define GPIO_M0_PWM3_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_M0_PWM3_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTR | GPIO_PIN_3)
#  define GPIO_M0_PWM4_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_0)
#  define GPIO_M0_PWM4_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTR | GPIO_PIN_4)
#  define GPIO_M0_PWM5_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTG | GPIO_PIN_1)
#  define GPIO_M0_PWM5_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTR | GPIO_PIN_5)
#  define GPIO_M0_PWM6_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_4)
#  define GPIO_M0_PWM6_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTR | GPIO_PIN_6)
#  define GPIO_M0_PWM7_1       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTK | GPIO_PIN_5)
#  define GPIO_M0_PWM7_2       (GPIO_FUNC_PFOUTPUT | GPIO_ALT_6  | GPIO_PORTR | GPIO_PIN_7)

#  define GPIO_NMI_1           (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_NMI_2           (GPIO_FUNC_PFIO     | GPIO_ALT_8  | GPIO_PORTE | GPIO_PIN_7)

#  define GPIO_OWALT_1         (GPIO_FUNC_PFIO     | GPIO_ALT_4  | GPIO_PORTP | GPIO_PIN_5)
#  define GPIO_OWALT_2         (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_OWIRE_1         (GPIO_FUNC_PFIO     | GPIO_ALT_4  | GPIO_PORTP | GPIO_PIN_4)
#  define GPIO_OWIRE_2         (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTE | GPIO_PIN_3)
#  define GPIO_OWIRE_3         (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_OWIRE_4         (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_6)
#  define GPIO_OWIRE_5         (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTG | GPIO_PIN_7)
#  define GPIO_OWIRE_6         (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTP | GPIO_PIN_7)

#  define GPIO_QEI0_IDX_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTL | GPIO_PIN_3)
#  define GPIO_QEI0_IDX_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTS | GPIO_PIN_6)
#  define GPIO_QEI0_PHA_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_QEI0_PHA_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTS | GPIO_PIN_4)
#  define GPIO_QEI0_PHB_1      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTL | GPIO_PIN_2)
#  define GPIO_QEI0_PHB_2      (GPIO_FUNC_PFIO     | GPIO_ALT_6  | GPIO_PORTS | GPIO_PIN_5)

#  define GPIO_SSI0_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_SSI0_FSS        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTA | GPIO_PIN_3)
#  define GPIO_SSI0_XDAT0      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_SSI0_XDAT1      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTA | GPIO_PIN_5)
#  define GPIO_SSI0_XDAT2      (GPIO_FUNC_PFIO     | GPIO_ALT_12 | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_SSI0_XDAT3      (GPIO_FUNC_PFIO     | GPIO_ALT_12 | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_SSI1_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_SSI1_FSS        (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_SSI1_XDAT0      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_SSI1_XDAT1      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTE | GPIO_PIN_5)
#  define GPIO_SSI1_XDAT2      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_SSI1_XDAT3      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_SSI2_CLK_1      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_SSI2_CLK_2      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTG | GPIO_PIN_7)
#  define GPIO_SSI2_FSS_1      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_SSI2_FSS_2      (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTG | GPIO_PIN_6)
#  define GPIO_SSI2_XDAT0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_SSI2_XDAT0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_SSI2_XDAT1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_SSI2_XDAT1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_SSI2_XDAT2_1    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_SSI2_XDAT2_2    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTG | GPIO_PIN_3)
#  define GPIO_SSI2_XDAT3_1    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_SSI2_XDAT3_2    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTG | GPIO_PIN_2)
#  define GPIO_SSI3_CLK_1      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_SSI3_CLK_2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTQ | GPIO_PIN_0)
#  define GPIO_SSI3_FSS_1      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_SSI3_FSS_2      (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTQ | GPIO_PIN_1)
#  define GPIO_SSI3_XDAT0_1    (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_SSI3_XDAT0_2    (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTQ | GPIO_PIN_2)
#  define GPIO_SSI3_XDAT1_1    (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_SSI3_XDAT1_2    (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTQ | GPIO_PIN_3)
#  define GPIO_SSI3_XDAT2_1    (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTF | GPIO_PIN_4)
#  define GPIO_SSI3_XDAT2_2    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTP | GPIO_PIN_0)
#  define GPIO_SSI3_XDAT3_1    (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTF | GPIO_PIN_5)
#  define GPIO_SSI3_XDAT3_2    (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTP | GPIO_PIN_1)

#  define GPIO_SYSCON_DIVSCLK  (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTQ | GPIO_PIN_4)

#  define GPIO_TR_CLK          (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_3)
#  define GPIO_TR_D0           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_2)
#  define GPIO_TR_D1           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_1)
#  define GPIO_TR_D2           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_0)
#  define GPIO_TR_D3           (GPIO_FUNC_PFIO     | GPIO_ALT_14 | GPIO_PORTF | GPIO_PIN_4)

#  define GPIO_TIM0_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_TIM0_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_0)
#  define GPIO_TIM0_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTL | GPIO_PIN_4)
#  define GPIO_TIM0_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTR | GPIO_PIN_4)
#  define GPIO_TIM0_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_TIM0_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_1)
#  define GPIO_TIM0_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTL | GPIO_PIN_5)
#  define GPIO_TIM0_CCP1_4     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTR | GPIO_PIN_5)
#  define GPIO_TIM1_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_TIM1_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_2)
#  define GPIO_TIM1_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTL | GPIO_PIN_6)
#  define GPIO_TIM1_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTR | GPIO_PIN_6)
#  define GPIO_TIM1_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_3)
#  define GPIO_TIM1_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_3)
#  define GPIO_TIM1_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTL | GPIO_PIN_7)
#  define GPIO_TIM1_CCP1_4     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTR | GPIO_PIN_7)
#  define GPIO_TIM2_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_TIM2_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_0)
#  define GPIO_TIM2_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTS | GPIO_PIN_0)
#  define GPIO_TIM2_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_5)
#  define GPIO_TIM2_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_1)
#  define GPIO_TIM2_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTS | GPIO_PIN_1)
#  define GPIO_TIM3_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_TIM3_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_TIM3_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_2)
#  define GPIO_TIM3_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTS | GPIO_PIN_2)
#  define GPIO_TIM3_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_TIM3_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_TIM3_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_3)
#  define GPIO_TIM3_CCP1_4     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTS | GPIO_PIN_3)
#  define GPIO_TIM4_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_TIM4_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_TIM4_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_4)
#  define GPIO_TIM4_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTS | GPIO_PIN_4)
#  define GPIO_TIM4_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_TIM4_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_TIM4_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_5)
#  define GPIO_TIM4_CCP1_4     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTS | GPIO_PIN_5)
#  define GPIO_TIM5_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_TIM5_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_6)
#  define GPIO_TIM5_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTS | GPIO_PIN_6)
#  define GPIO_TIM5_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_3)
#  define GPIO_TIM5_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTM | GPIO_PIN_7)
#  define GPIO_TIM5_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTS | GPIO_PIN_7)
#  define GPIO_TIM6_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_6)
#  define GPIO_TIM6_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTQ | GPIO_PIN_0)
#  define GPIO_TIM6_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTT | GPIO_PIN_0)
#  define GPIO_TIM6_CCP0_4     (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTP | GPIO_PIN_0)
#  define GPIO_TIM6_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTB | GPIO_PIN_7)
#  define GPIO_TIM6_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTQ | GPIO_PIN_1)
#  define GPIO_TIM6_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTT | GPIO_PIN_1)
#  define GPIO_TIM6_CCP1_4     (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTP | GPIO_PIN_1)
#  define GPIO_TIM7_CCP0_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_TIM7_CCP0_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTQ | GPIO_PIN_2)
#  define GPIO_TIM7_CCP0_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTT | GPIO_PIN_2)
#  define GPIO_TIM7_CCP1_1     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_TIM7_CCP1_2     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTQ | GPIO_PIN_3)
#  define GPIO_TIM7_CCP1_3     (GPIO_FUNC_PFIO     | GPIO_ALT_3  | GPIO_PORTT | GPIO_PIN_3)

#  define GPIO_UART0_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_4)
#  define GPIO_UART0_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_6)
#  define GPIO_UART0_CTS_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTG | GPIO_PIN_4)
#  define GPIO_UART0_CTS_4     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_1)
#  define GPIO_UART0_CTS_5     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTM | GPIO_PIN_4)
#  define GPIO_UART0_DCD_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_2)
#  define GPIO_UART0_DCD_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTM | GPIO_PIN_5)
#  define GPIO_UART0_DCD_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTP | GPIO_PIN_3)
#  define GPIO_UART0_DSR_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_3)
#  define GPIO_UART0_DSR_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTM | GPIO_PIN_6)
#  define GPIO_UART0_DSR_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTP | GPIO_PIN_4)
#  define GPIO_UART0_DTR_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_4)
#  define GPIO_UART0_DTR_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_2)
#  define GPIO_UART0_RI_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_5)
#  define GPIO_UART0_RI_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_UART0_RI_3      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTM | GPIO_PIN_7)
#  define GPIO_UART0_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_5)
#  define GPIO_UART0_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_7)
#  define GPIO_UART0_RTS_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTG | GPIO_PIN_5)
#  define GPIO_UART0_RTS_4     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_0)
#  define GPIO_UART0_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_0)
#  define GPIO_UART0_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_1)
#  define GPIO_UART1_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_1)
#  define GPIO_UART1_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_3)
#  define GPIO_UART1_DCD_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_2)
#  define GPIO_UART1_DCD_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_2)
#  define GPIO_UART1_DCD_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_6)
#  define GPIO_UART1_DSR_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_1)
#  define GPIO_UART1_DSR_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_3)
#  define GPIO_UART1_DSR_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTS | GPIO_PIN_2)
#  define GPIO_UART1_DTR_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_3)
#  define GPIO_UART1_DTR_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_4)
#  define GPIO_UART1_DTR_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTQ | GPIO_PIN_6)
#  define GPIO_UART1_RI_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_4)
#  define GPIO_UART1_RI_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_5)
#  define GPIO_UART1_RI_3      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTQ | GPIO_PIN_7)
#  define GPIO_UART1_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTE | GPIO_PIN_0)
#  define GPIO_UART1_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_0)
#  define GPIO_UART1_RTS_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTN | GPIO_PIN_7)
#  define GPIO_UART1_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_UART1_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTQ | GPIO_PIN_4)
#  define GPIO_UART1_RX_3      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTR | GPIO_PIN_5)
#  define GPIO_UART1_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTB | GPIO_PIN_1)
#  define GPIO_UART1_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTQ | GPIO_PIN_5)
#  define GPIO_UART1_TX_3      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTR | GPIO_PIN_6)
#  define GPIO_UART2_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_UART2_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_3)
#  define GPIO_UART2_CTS_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_3)
#  define GPIO_UART2_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_UART2_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_2)
#  define GPIO_UART2_RTS_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_2)
#  define GPIO_UART2_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_UART2_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_4)
#  define GPIO_UART2_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_UART2_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTD | GPIO_PIN_5)
#  define GPIO_UART3_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_5)
#  define GPIO_UART3_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_5)
#  define GPIO_UART3_CTS_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_5)
#  define GPIO_UART3_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_4)
#  define GPIO_UART3_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_4)
#  define GPIO_UART3_RTS_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_4)
#  define GPIO_UART3_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_4)
#  define GPIO_UART3_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_0)
#  define GPIO_UART3_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_5)
#  define GPIO_UART3_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_1)
#  define GPIO_UART4_CTS_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_3)
#  define GPIO_UART4_CTS_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_7)
#  define GPIO_UART4_CTS_3     (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_7)
#  define GPIO_UART4_RTS_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTJ | GPIO_PIN_6)
#  define GPIO_UART4_RTS_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_2)
#  define GPIO_UART4_RTS_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTN | GPIO_PIN_6)
#  define GPIO_UART4_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_2)
#  define GPIO_UART4_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_0)
#  define GPIO_UART4_RX_3      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTR | GPIO_PIN_1)
#  define GPIO_UART4_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTA | GPIO_PIN_3)
#  define GPIO_UART4_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTK | GPIO_PIN_1)
#  define GPIO_UART4_TX_3      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTR | GPIO_PIN_0)
#  define GPIO_UART5_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_6)
#  define GPIO_UART5_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_6)
#  define GPIO_UART5_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_7)
#  define GPIO_UART5_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTH | GPIO_PIN_7)
#  define GPIO_UART6_RX        (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_0)
#  define GPIO_UART6_TX        (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTP | GPIO_PIN_1)
#  define GPIO_UART7_RX_1      (GPIO_FUNC_PFINPUT  | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_4)
#  define GPIO_UART7_RX_2      (GPIO_FUNC_PFINPUT  | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_6)
#  define GPIO_UART7_TX_1      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_1  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_UART7_TX_2      (GPIO_FUNC_PFOUTPUT | GPIO_ALT_2  | GPIO_PORTH | GPIO_PIN_7)

#  define GPIO_USB0_CLK        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTB | GPIO_PIN_3)
#  define GPIO_USB0_D0         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTL | GPIO_PIN_0)
#  define GPIO_USB0_D1         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTL | GPIO_PIN_1)
#  define GPIO_USB0_D2         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTL | GPIO_PIN_2)
#  define GPIO_USB0_D3         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTL | GPIO_PIN_3)
#  define GPIO_USB0_D4         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTL | GPIO_PIN_4)
#  define GPIO_USB0_D5         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTL | GPIO_PIN_5)
#  define GPIO_USB0_D6         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTP | GPIO_PIN_5)
#  define GPIO_USB0_D7         (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTP | GPIO_PIN_4)
#  define GPIO_USB0_DIR        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTP | GPIO_PIN_3)
#  define GPIO_USB0_EPEN_1     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_11 | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_USB0_EPEN_2     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTA | GPIO_PIN_6)
#  define GPIO_USB0_EPEN_3     (GPIO_FUNC_PFOUTPUT | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_6)
#  define GPIO_USB0_ID         (GPIO_FUNC_ANINPUT                | GPIO_PORTB | GPIO_PIN_0)
#  define GPIO_USB0_NXT        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTP | GPIO_PIN_2)
#  define GPIO_USB0_PFLT_1     (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTA | GPIO_PIN_7)
#  define GPIO_USB0_PFLT_2     (GPIO_FUNC_PFINPUT  | GPIO_ALT_5  | GPIO_PORTD | GPIO_PIN_7)
#  define GPIO_USB0_STP        (GPIO_FUNC_PFIO     | GPIO_ALT_13 | GPIO_PORTB | GPIO_PIN_2)
#  define GPIO_USB0_VBUS       (GPIO_FUNC_ANIO                   | GPIO_PORTB | GPIO_PIN_1)

#  define GPIO_RTC_CLK_1       (GPIO_FUNC_PFIO     | GPIO_ALT_5  | GPIO_PORTK | GPIO_PIN_7)
#  define GPIO_RTC_CLK_2       (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTC | GPIO_PIN_5)
#  define GPIO_RTC_CLK_3       (GPIO_FUNC_PFIO     | GPIO_ALT_7  | GPIO_PORTP | GPIO_PIN_3)

#else
#  error "Unknown TIVA chip"
#endif

/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/

#endif /* __ARCH_ARM_SRC_TIVA_HARDWARE_TM4C_TM4C_PINMAP_H */
