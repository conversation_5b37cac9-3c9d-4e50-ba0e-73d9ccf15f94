/****************************************************************************
 * arch/arm/src/tiva/hardware/cc13x2_cc26x2/cc13x2_cc26x2_aux_sysif.h
 *
 *   Copyright (C) 2019 Gregory Nutt. All rights reserved.
 *   Authors: <AUTHORS>
 *
 * Technical content derives from a TI header file that has a
 * compatible BSD license:
 *
 *   Copyright (c) 2015-2017, Texas Instruments Incorporated
 *   All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 * 3. Neither the name <PERSON>utt<PERSON> nor the names of its contributors may be
 *    used to endorse or promote products derived from this software
 *    without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS
 * OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 * AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
 * ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_TIVA_HARDWARE_CC13X2_CC26X2_CC13X2_CC26X2_AUX_SYSIF_H
#define __ARCH_ARM_SRC_TIVA_HARDWARE_CC13X2_CC26X2_CC13X2_CC26X2_AUX_SYSIF_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include "hardware/tiva_memorymap.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* AUX SYSIF Register Offsets ***********************************************/

#define TIVA_AUX_SYSIF_OPMODEREQ_OFFSET                 0x0000  /* Operational Mode Request */
#define TIVA_AUX_SYSIF_OPMODEACK_OFFSET                 0x0004  /* Operational Mode Acknowledgement */
#define TIVA_AUX_SYSIF_PROGWU0CFG_OFFSET                0x0008  /* Programmable Wakeup 0 Configuration */
#define TIVA_AUX_SYSIF_PROGWU1CFG_OFFSET                0x000c  /* Programmable Wakeup 1 Configuration */
#define TIVA_AUX_SYSIF_PROGWU2CFG_OFFSET                0x0010  /* Programmable Wakeup 2 Configuration */
#define TIVA_AUX_SYSIF_PROGWU3CFG_OFFSET                0x0014  /* Programmable Wakeup 3 Configuration */
#define TIVA_AUX_SYSIF_SWWUTRIG_OFFSET                  0x0018  /* Software Wakeup Triggers */
#define TIVA_AUX_SYSIF_WUFLAGS_OFFSET                   0x001c  /* Wakeup Flags */
#define TIVA_AUX_SYSIF_WUFLAGSCLR_OFFSET                0x0020  /* Wakeup Flags Clear */
#define TIVA_AUX_SYSIF_WUGATE_OFFSET                    0x0024  /* Wakeup Gate */
#define TIVA_AUX_SYSIF_VECCFG0_OFFSET                   0x0028  /* Vector Configuration 0 */
#define TIVA_AUX_SYSIF_VECCFG1_OFFSET                   0x002c  /* Vector Configuration 1 */
#define TIVA_AUX_SYSIF_VECCFG2_OFFSET                   0x0030  /* Vector Configuration 2 */
#define TIVA_AUX_SYSIF_VECCFG3_OFFSET                   0x0034  /* Vector Configuration 3 */
#define TIVA_AUX_SYSIF_VECCFG4_OFFSET                   0x0038  /* Vector Configuration 4 */
#define TIVA_AUX_SYSIF_VECCFG5_OFFSET                   0x003c  /* Vector Configuration 5 */
#define TIVA_AUX_SYSIF_VECCFG6_OFFSET                   0x0040  /* Vector Configuration 6 */
#define TIVA_AUX_SYSIF_VECCFG7_OFFSET                   0x0044  /* Vector Configuration 7 */
#define TIVA_AUX_SYSIF_EVSYNCRATE_OFFSET                0x0048  /* Event Synchronization Rate */
#define TIVA_AUX_SYSIF_PEROPRATE_OFFSET                 0x004c  /* Peripheral Operational Rate */
#define TIVA_AUX_SYSIF_ADCCLKCTL_OFFSET                 0x0050  /* ADC Clock Control */
#define TIVA_AUX_SYSIF_TDCCLKCTL_OFFSET                 0x0054  /* TDC Counter Clock Control */
#define TIVA_AUX_SYSIF_TDCREFCLKCTL_OFFSET              0x0058  /* TDC Reference Clock Control */
#define TIVA_AUX_SYSIF_TIMER2CLKCTL_OFFSET              0x005c  /* AUX_TIMER2 Clock Control */
#define TIVA_AUX_SYSIF_TIMER2CLKSTAT_OFFSET             0x0060  /* AUX_TIMER2 Clock Status */
#define TIVA_AUX_SYSIF_TIMER2CLKSWITCH_OFFSET           0x0064  /* AUX_TIMER2 Clock Switch */
#define TIVA_AUX_SYSIF_TIMER2DBGCTL_OFFSET              0x0068  /* AUX_TIMER2 Debug Control */
#define TIVA_AUX_SYSIF_CLKSHIFTDET_OFFSET               0x0070  /* Clock Shift Detection */
#define TIVA_AUX_SYSIF_RECHARGETRIG_OFFSET              0x0074  /* VDDR Recharge Trigger */
#define TIVA_AUX_SYSIF_RECHARGEDET_OFFSET               0x0078  /* VDDR Recharge Detection */
#define TIVA_AUX_SYSIF_RTCSUBSECINC0_OFFSET             0x007c  /* Real Time Counter Sub Second Increment 0 */
#define TIVA_AUX_SYSIF_RTCSUBSECINC1_OFFSET             0x0080  /* Real Time Counter Sub Second Increment 1 */
#define TIVA_AUX_SYSIF_RTCSUBSECINCCTL_OFFSET           0x0084  /* Real Time Counter Sub Second Increment Control */
#define TIVA_AUX_SYSIF_RTCSEC_OFFSET                    0x0088  /* Real Time Counter Second */
#define TIVA_AUX_SYSIF_RTCSUBSEC_OFFSET                 0x008c  /* Real Time Counter Sub-Second */
#define TIVA_AUX_SYSIF_RTCEVCLR_OFFSET                  0x0090  /* AON_RTC Event Clear */
#define TIVA_AUX_SYSIF_BATMONBAT_OFFSET                 0x0094  /* AON_BATMON Battery Voltage Value */
#define TIVA_AUX_SYSIF_BATMONTEMP_OFFSET                0x009c  /* AON_BATMON Temperature Value */
#define TIVA_AUX_SYSIF_TIMERHALT_OFFSET                 0x00a0  /* Timer Halt */
#define TIVA_AUX_SYSIF_TIMER2BRIDGE_OFFSET              0x00b0  /* AUX_TIMER2 Bridge */
#define TIVA_AUX_SYSIF_SWPWRPROF_OFFSET                 0x00b4  /* Software Power Profiler */

/* AUX SYSIF Register Addresses *********************************************/

#define TIVA_AUX_SYSIF_OPMODEREQ                        (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_OPMODEREQ_OFFSET)
#define TIVA_AUX_SYSIF_OPMODEACK                        (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_OPMODEACK_OFFSET)
#define TIVA_AUX_SYSIF_PROGWU0CFG                       (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_PROGWU0CFG_OFFSET)
#define TIVA_AUX_SYSIF_PROGWU1CFG                       (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_PROGWU1CFG_OFFSET)
#define TIVA_AUX_SYSIF_PROGWU2CFG                       (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_PROGWU2CFG_OFFSET)
#define TIVA_AUX_SYSIF_PROGWU3CFG                       (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_PROGWU3CFG_OFFSET)
#define TIVA_AUX_SYSIF_SWWUTRIG                         (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_SWWUTRIG_OFFSET)
#define TIVA_AUX_SYSIF_WUFLAGS                          (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_WUFLAGS_OFFSET)
#define TIVA_AUX_SYSIF_WUFLAGSCLR                       (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_WUFLAGSCLR_OFFSET)
#define TIVA_AUX_SYSIF_WUGATE                           (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_WUGATE_OFFSET)
#define TIVA_AUX_SYSIF_VECCFG0                          (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_VECCFG0_OFFSET)
#define TIVA_AUX_SYSIF_VECCFG1                          (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_VECCFG1_OFFSET)
#define TIVA_AUX_SYSIF_VECCFG2                          (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_VECCFG2_OFFSET)
#define TIVA_AUX_SYSIF_VECCFG3                          (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_VECCFG3_OFFSET)
#define TIVA_AUX_SYSIF_VECCFG4                          (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_VECCFG4_OFFSET)
#define TIVA_AUX_SYSIF_VECCFG5                          (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_VECCFG5_OFFSET)
#define TIVA_AUX_SYSIF_VECCFG6                          (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_VECCFG6_OFFSET)
#define TIVA_AUX_SYSIF_VECCFG7                          (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_VECCFG7_OFFSET)
#define TIVA_AUX_SYSIF_EVSYNCRATE                       (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_EVSYNCRATE_OFFSET)
#define TIVA_AUX_SYSIF_PEROPRATE                        (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_PEROPRATE_OFFSET)
#define TIVA_AUX_SYSIF_ADCCLKCTL                        (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_ADCCLKCTL_OFFSET)
#define TIVA_AUX_SYSIF_TDCCLKCTL                        (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_TDCCLKCTL_OFFSET)
#define TIVA_AUX_SYSIF_TDCREFCLKCTL                     (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_TDCREFCLKCTL_OFFSET)
#define TIVA_AUX_SYSIF_TIMER2CLKCTL                     (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_TIMER2CLKCTL_OFFSET)
#define TIVA_AUX_SYSIF_TIMER2CLKSTAT                    (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_TIMER2CLKSTAT_OFFSET)
#define TIVA_AUX_SYSIF_TIMER2CLKSWITCH                  (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_TIMER2CLKSWITCH_OFFSET)
#define TIVA_AUX_SYSIF_TIMER2DBGCTL                     (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_TIMER2DBGCTL_OFFSET)
#define TIVA_AUX_SYSIF_CLKSHIFTDET                      (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_CLKSHIFTDET_OFFSET)
#define TIVA_AUX_SYSIF_RECHARGETRIG                     (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_RECHARGETRIG_OFFSET)
#define TIVA_AUX_SYSIF_RECHARGEDET                      (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_RECHARGEDET_OFFSET)
#define TIVA_AUX_SYSIF_RTCSUBSECINC0                    (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_RTCSUBSECINC0_OFFSET)
#define TIVA_AUX_SYSIF_RTCSUBSECINC1                    (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_RTCSUBSECINC1_OFFSET)
#define TIVA_AUX_SYSIF_RTCSUBSECINCCTL                  (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_RTCSUBSECINCCTL_OFFSET)
#define TIVA_AUX_SYSIF_RTCSEC                           (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_RTCSEC_OFFSET)
#define TIVA_AUX_SYSIF_RTCSUBSEC                        (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_RTCSUBSEC_OFFSET)
#define TIVA_AUX_SYSIF_RTCEVCLR                         (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_RTCEVCLR_OFFSET)
#define TIVA_AUX_SYSIF_BATMONBAT                        (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_BATMONBAT_OFFSET)
#define TIVA_AUX_SYSIF_BATMONTEMP                       (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_BATMONTEMP_OFFSET)
#define TIVA_AUX_SYSIF_TIMERHALT                        (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_TIMERHALT_OFFSET)
#define TIVA_AUX_SYSIF_TIMER2BRIDGE                     (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_TIMER2BRIDGE_OFFSET)
#define TIVA_AUX_SYSIF_SWPWRPROF                        (TIVA_AUX_SYSIF_BASE + TIVA_AUX_SYSIF_SWPWRPROF_OFFSET)

/* AUX SYSIF Register Bifield Definitions ***********************************/

/* AUX_SYSIF_OPMODEREQ */

#define AUX_SYSIF_OPMODEREQ_REQ_SHIFT                   (0)       /* Bits 0-1: AUX operational mode request */
#define AUX_SYSIF_OPMODEREQ_REQ_MASK                    (3 << AUX_SYSIF_OPMODEREQ_REQ_SHIFT)
#  define AUX_SYSIF_OPMODEREQ_REQ(n)                    ((uint32_t)(n) << AUX_SYSIF_OPMODEREQ_REQ_SHIFT)
#  define AUX_SYSIF_OPMODEREQ_REQ_A                     (0 << AUX_SYSIF_OPMODEREQ_REQ_SHIFT) /* Active operational mode */
#  define AUX_SYSIF_OPMODEREQ_REQ_LP                    (1 << AUX_SYSIF_OPMODEREQ_REQ_SHIFT) /* Low power operational mode */
#  define AUX_SYSIF_OPMODEREQ_REQ_PDA                   (2 << AUX_SYSIF_OPMODEREQ_REQ_SHIFT) /* Power down operational mode
                                                                                              * with wakeup to active mode */
#  define AUX_SYSIF_OPMODEREQ_REQ_PDLP                  (3 << AUX_SYSIF_OPMODEREQ_REQ_SHIFT) /* Powerdown operational mode
                                                                                              *  with wakeup to lowpower mode */

/* AUX_SYSIF_OPMODEACK */

#define AUX_SYSIF_OPMODEACK_ACK_SHIFT                   (0)       /* Bits 0-1: AUX operational mode acknowledgment */
#define AUX_SYSIF_OPMODEACK_ACK_MASK                    (3 << AUX_SYSIF_OPMODEACK_ACK_SHIFT)
#  define AUX_SYSIF_OPMODEACK_ACK_A                     (0 << AUX_SYSIF_OPMODEACK_ACK_SHIFT) /* Active operational mode */
#  define AUX_SYSIF_OPMODEACK_ACK_LP                    (1 << AUX_SYSIF_OPMODEACK_ACK_SHIFT) /* Low power operational mode */
#  define AUX_SYSIF_OPMODEACK_ACK_PDA                   (2 << AUX_SYSIF_OPMODEACK_ACK_SHIFT) /* Power down operational mode
                                                                                              * with wakeup to active mode */
#  define AUX_SYSIF_OPMODEACK_ACK_PDLP                  (3 << AUX_SYSIF_OPMODEACK_ACK_SHIFT) /* Powerdown operational mode
                                                                                              *  with wakeup to lowpower mode */

/* AUX_SYSIF_PROGWU0CFG */

#define AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT                      (0)       /* Bits 0-5: Wakeup source from the asynchronous AUX event bus */
#define AUX_SYSIF_PROGWU0CFG_WU_SRC_MASK                       (0x3f << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC(n)                       ((uint32_t)(n) << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO0                   (0x00 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO1                   (0x01 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO2                   (0x02 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO3                   (0x03 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO4                   (0x04 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO5                   (0x05 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO6                   (0x06 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO7                   (0x07 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO8                   (0x08 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO9                   (0x09 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO10                  (0x0a << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO11                  (0x0b << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO12                  (0x0c << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO13                  (0x0d << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO14                  (0x0e << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO15                  (0x0f << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO16                  (0x10 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO17                  (0x11 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO18                  (0x12 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO19                  (0x13 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO20                  (0x14 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO21                  (0x15 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO22                  (0x16 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO23                  (0x17 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO24                  (0x18 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO25                  (0x19 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO26                  (0x1a << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO27                  (0x1b << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO28                  (0x1c << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO29                  (0x1d << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO30                  (0x1e << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUXIO31                  (0x1f << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_MANUAL_EV                (0x20 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AON_RTC_CH2              (0x21 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AON_RTC_CH2_DLY          (0x22 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AON_RTC_4KHZ             (0x23 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AON_BATMON_BAT_UPD       (0x24 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AON_BATMON_TEMP_UPD      (0x25 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_SCLK_LF                  (0x26 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_PWR_DWN                  (0x27 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_MCU_ACTIVE               (0x28 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_VDDR_RECHARGE            (0x29 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_ACLK_REF                 (0x2a << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_MCU_EV                   (0x2b << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_MCU_OBSMUX0              (0x2c << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_MCU_OBSMUX1              (0x2d << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUX_COMPA                (0x2e << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUX_COMPB                (0x2f << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUX_TIMER2_EV0           (0x30 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUX_TIMER2_EV1           (0x31 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUX_TIMER2_EV2           (0x32 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUX_TIMER2_EV3           (0x33 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUX_TIMER2_PULSE         (0x34 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUX_TIMER1_EV            (0x35 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUX_TIMER0_EV            (0x36 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUX_TDC_DONE             (0x37 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUX_ISRC_RESET_N         (0x38 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUX_ADC_DONE             (0x39 << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUX_ADC_IRQ              (0x3a << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUX_ADC_FIFO_ALMOST_FULL (0x3b << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUX_ADC_FIFO_NOT_EMPTY   (0x3c << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_AUX_SMPH_AUTOTAKE_DONE   (0x3d << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU0CFG_WU_SRC_NO_EVENT                 (0x3f << AUX_SYSIF_PROGWU0CFG_WU_SRC_SHIFT)
#define AUX_SYSIF_PROGWU0CFG_EN                                (1 << 6)  /* Bit 6:  Programmable wakeup flag enable */
#define AUX_SYSIF_PROGWU0CFG_POL                               (1 << 7)  /* Bit 7:  Polarity of WU_SRC */
#  define AUX_SYSIF_PROGWU0CFG_POL_LOW                         AUX_SYSIF_PROGWU0CFG_POL
#  define AUX_SYSIF_PROGWU0CFG_POL_HIGH                        (0)

/* AUX_SYSIF_PROGWU1CFG */

#define AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT                      (0)       /* Bits 0-5: Wakeup source from the asynchronous AUX event bus */
#define AUX_SYSIF_PROGWU1CFG_WU_SRC_MASK                       (0x3f << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO0                   (0x00 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO1                   (0x01 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO2                   (0x02 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO3                   (0x03 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO4                   (0x04 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO5                   (0x05 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO6                   (0x06 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO7                   (0x07 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO8                   (0x08 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO9                   (0x09 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO10                  (0x0a << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO11                  (0x0b << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO12                  (0x0c << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO13                  (0x0d << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO14                  (0x0e << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO15                  (0x0f << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO16                  (0x10 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO17                  (0x11 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO18                  (0x12 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO19                  (0x13 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO20                  (0x14 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO21                  (0x15 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO22                  (0x16 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO23                  (0x17 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO24                  (0x18 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO25                  (0x19 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO26                  (0x1a << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO27                  (0x1b << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO28                  (0x1c << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO29                  (0x1d << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO30                  (0x1e << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUXIO31                  (0x1f << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_MANUAL_EV                (0x20 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AON_RTC_CH2              (0x21 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AON_RTC_CH2_DLY          (0x22 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AON_RTC_4KHZ             (0x23 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AON_BATMON_BAT_UPD       (0x24 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AON_BATMON_TEMP_UPD      (0x25 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_SCLK_LF                  (0x26 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_PWR_DWN                  (0x27 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_MCU_ACTIVE               (0x28 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_VDDR_RECHARGE            (0x29 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_ACLK_REF                 (0x2a << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_MCU_EV                   (0x2b << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_MCU_OBSMUX0              (0x2c << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_MCU_OBSMUX1              (0x2d << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUX_COMPA                (0x2e << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUX_COMPB                (0x2f << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUX_TIMER2_EV0           (0x30 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUX_TIMER2_EV1           (0x31 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUX_TIMER2_EV2           (0x32 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUX_TIMER2_EV3           (0x33 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUX_TIMER2_PULSE         (0x34 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUX_TIMER1_EV            (0x35 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUX_TIMER0_EV            (0x36 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUX_TDC_DONE             (0x37 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUX_ISRC_RESET_N         (0x38 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUX_ADC_DONE             (0x39 << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUX_ADC_IRQ              (0x3a << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUX_ADC_FIFO_ALMOST_FULL (0x3b << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUX_ADC_FIFO_NOT_EMPTY   (0x3c << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_AUX_SMPH_AUTOTAKE_DONE   (0x3d << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU1CFG_WU_SRC_NO_EVENT                 (0x3f << AUX_SYSIF_PROGWU1CFG_WU_SRC_SHIFT)
#define AUX_SYSIF_PROGWU1CFG_EN                                (1 << 6)  /* Bit 6:  Programmable wakeup flag enable */
#define AUX_SYSIF_PROGWU1CFG_POL                               (1 << 7)  /* Bit 7:  Polarity of WU_SRC */
#  define AUX_SYSIF_PROGWU1CFG_POL_LOW                         AUX_SYSIF_PROGWU1CFG_POL
#  define AUX_SYSIF_PROGWU1CFG_POL_HIGH                        (0)

/* AUX_SYSIF_PROGWU2CFG */

#define AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT                      (0)       /* Bits 0-5: Wakeup source from the asynchronous AUX event bus */
#define AUX_SYSIF_PROGWU2CFG_WU_SRC_MASK                       (0x3f << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC(n)                       ((uint32_t)(n) << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO0                   (0x00 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO1                   (0x01 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO2                   (0x02 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO3                   (0x03 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO4                   (0x04 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO5                   (0x05 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO6                   (0x06 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO7                   (0x07 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO8                   (0x08 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO9                   (0x09 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO10                  (0x0a << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO11                  (0x0b << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO12                  (0x0c << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO13                  (0x0d << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO14                  (0x0e << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO15                  (0x0f << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO16                  (0x10 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO17                  (0x11 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO18                  (0x12 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO19                  (0x13 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO20                  (0x14 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO21                  (0x15 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO22                  (0x16 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO23                  (0x17 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO24                  (0x18 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO25                  (0x19 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO26                  (0x1a << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO27                  (0x1b << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO28                  (0x1c << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO29                  (0x1d << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO30                  (0x1e << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUXIO31                  (0x1f << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_MANUAL_EV                (0x20 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AON_RTC_CH2              (0x21 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AON_RTC_CH2_DLY          (0x22 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AON_RTC_4KHZ             (0x23 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AON_BATMON_BAT_UPD       (0x24 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AON_BATMON_TEMP_UPD      (0x25 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_SCLK_LF                  (0x26 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_PWR_DWN                  (0x27 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_MCU_ACTIVE               (0x28 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_VDDR_RECHARGE            (0x29 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_ACLK_REF                 (0x2a << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_MCU_EV                   (0x2b << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_MCU_OBSMUX0              (0x2c << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_MCU_OBSMUX1              (0x2d << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUX_COMPA                (0x2e << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUX_COMPB                (0x2f << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUX_TIMER2_EV0           (0x30 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUX_TIMER2_EV1           (0x31 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUX_TIMER2_EV2           (0x32 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUX_TIMER2_EV3           (0x33 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUX_TIMER2_PULSE         (0x34 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUX_TIMER1_EV            (0x35 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUX_TIMER0_EV            (0x36 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUX_TDC_DONE             (0x37 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUX_ISRC_RESET_N         (0x38 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUX_ADC_DONE             (0x39 << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUX_ADC_IRQ              (0x3a << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUX_ADC_FIFO_ALMOST_FULL (0x3b << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUX_ADC_FIFO_NOT_EMPTY   (0x3c << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_AUX_SMPH_AUTOTAKE_DONE   (0x3d << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU2CFG_WU_SRC_NO_EVENT                 (0x3f << AUX_SYSIF_PROGWU2CFG_WU_SRC_SHIFT)
#define AUX_SYSIF_PROGWU2CFG_EN                                (1 << 6)  /* Bit 6:  Programmable wakeup flag enable */
#define AUX_SYSIF_PROGWU2CFG_POL                               (1 << 7)  /* Bit 7:   */
#  define AUX_SYSIF_PROGWU2CFG_POL_LOW                         AUX_SYSIF_PROGWU2CFG_POL
#  define AUX_SYSIF_PROGWU2CFG_POL_HIGH                        (0)

/* AUX_SYSIF_PROGWU3CFG */

#define AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT                      (0)       /* Bits 0-5: Wakeup source from the asynchronous AUX event bus */
#define AUX_SYSIF_PROGWU3CFG_WU_SRC_MASK                       (0x3f << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC(n)                       ((uint32_t)(n) << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC(n)                       ((uint32_t)(n) << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO0                   (0x00 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO1                   (0x01 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO2                   (0x02 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO3                   (0x03 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO4                   (0x04 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO5                   (0x05 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO6                   (0x06 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO7                   (0x07 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO8                   (0x08 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO9                   (0x09 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO10                  (0x0a << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO11                  (0x0b << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO12                  (0x0c << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO13                  (0x0d << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO14                  (0x0e << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO15                  (0x0f << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO16                  (0x10 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO17                  (0x11 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO18                  (0x12 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO19                  (0x13 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO20                  (0x14 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO21                  (0x15 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO22                  (0x16 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO23                  (0x17 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO24                  (0x18 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO25                  (0x19 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO26                  (0x1a << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO27                  (0x1b << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO28                  (0x1c << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO29                  (0x1d << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO30                  (0x1e << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUXIO31                  (0x1f << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_MANUAL_EV                (0x20 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AON_RTC_CH2              (0x21 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AON_RTC_CH2_DLY          (0x22 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AON_RTC_4KHZ             (0x23 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AON_BATMON_BAT_UPD       (0x24 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AON_BATMON_TEMP_UPD      (0x25 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_SCLK_LF                  (0x26 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_PWR_DWN                  (0x27 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_MCU_ACTIVE               (0x28 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_VDDR_RECHARGE            (0x29 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_ACLK_REF                 (0x2a << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_MCU_EV                   (0x2b << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_MCU_OBSMUX0              (0x2c << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_MCU_OBSMUX1              (0x2d << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUX_COMPA                (0x2e << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUX_COMPB                (0x2f << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUX_TIMER2_EV0           (0x30 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUX_TIMER2_EV1           (0x31 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUX_TIMER2_EV2           (0x32 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUX_TIMER2_EV3           (0x33 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUX_TIMER2_PULSE         (0x34 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUX_TIMER1_EV            (0x35 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUX_TIMER0_EV            (0x36 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUX_TDC_DONE             (0x37 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUX_ISRC_RESET_N         (0x38 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUX_ADC_DONE             (0x39 << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUX_ADC_IRQ              (0x3a << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUX_ADC_FIFO_ALMOST_FULL (0x3b << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUX_ADC_FIFO_NOT_EMPTY   (0x3c << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_AUX_SMPH_AUTOTAKE_DONE   (0x3d << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#  define AUX_SYSIF_PROGWU3CFG_WU_SRC_NO_EVENT                 (0x3f << AUX_SYSIF_PROGWU3CFG_WU_SRC_SHIFT)
#define AUX_SYSIF_PROGWU3CFG_EN                                (1 << 6)  /* Bit 6:  Programmable wakeup flag enable */
#define AUX_SYSIF_PROGWU3CFG_POL                               (1 << 7)  /* Bit 7:  Polarity of WU_SRC */
#  define AUX_SYSIF_PROGWU3CFG_POL_LOW                         AUX_SYSIF_PROGWU3CFG_POL
#  define AUX_SYSIF_PROGWU3CFG_POL_HIGH                        (0)

/* AUX_SYSIF_SWWUTRIG */

#define AUX_SYSIF_SWWUTRIG_SW_WU0                       (1 << 0)  /* Bit 0:  Software wakeup 0 trigger */
#define AUX_SYSIF_SWWUTRIG_SW_WU1                       (1 << 1)  /* Bit 1:  Software wakeup 1 trigger */
#define AUX_SYSIF_SWWUTRIG_SW_WU2                       (1 << 2)  /* Bit 2:  Software wakeup 2 trigger */
#define AUX_SYSIF_SWWUTRIG_SW_WU3                       (1 << 3)  /* Bit 3:  Software wakeup 3 trigger */

/* AUX_SYSIF_WUFLAGS */

#define AUX_SYSIF_WUFLAGS_PROG_WU0                      (1 << 0)  /* Bit 0:  Programmable wakeup 0. */
#define AUX_SYSIF_WUFLAGS_PROG_WU1                      (1 << 1)  /* Bit 1:  Programmable wakeup 1 */
#define AUX_SYSIF_WUFLAGS_PROG_WU2                      (1 << 2)  /* Bit 2:  Programmable wakeup 2 */
#define AUX_SYSIF_WUFLAGS_PROG_WU3                      (1 << 3)  /* Bit 3:  Programmable wakeup 3 */
#define AUX_SYSIF_WUFLAGS_SW_WU0                        (1 << 4)  /* Bit 4:  Software wakeup 0 flag */
#define AUX_SYSIF_WUFLAGS_SW_WU1                        (1 << 5)  /* Bit 5:  Software wakeup 1 flag */
#define AUX_SYSIF_WUFLAGS_SW_WU2                        (1 << 6)  /* Bit 6:  Software wakeup 2 flag */
#define AUX_SYSIF_WUFLAGS_SW_WU3                        (1 << 7)  /* Bit 7:  Software wakeup 3 flag */

/* AUX_SYSIF_WUFLAGSCLR */

#define AUX_SYSIF_WUFLAGSCLR_PROG_WU0                   (1 << 0)  /* Bit 0:  Programmable wakeup flag 0 */
#define AUX_SYSIF_WUFLAGSCLR_PROG_WU1                   (1 << 1)  /* Bit 1:  Programmable wakeup flag 1 */
#define AUX_SYSIF_WUFLAGSCLR_PROG_WU2                   (1 << 2)  /* Bit 2:  Programmable wakeup flag 2 */
#define AUX_SYSIF_WUFLAGSCLR_PROG_WU3                   (1 << 3)  /* Bit 3:  Programmable wakeup flag 3 */
#define AUX_SYSIF_WUFLAGSCLR_SW_WU0                     (1 << 4)  /* Bit 4:  Clear software wakeup flag 0 */
#define AUX_SYSIF_WUFLAGSCLR_SW_WU1                     (1 << 5)  /* Bit 5:  Clear software wakeup flag 1 */
#define AUX_SYSIF_WUFLAGSCLR_SW_WU2                     (1 << 6)  /* Bit 6:  Clear software wakeup flag 2 */
#define AUX_SYSIF_WUFLAGSCLR_SW_WU3                     (1 << 7)  /* Bit 7:   Clear software wakeup flag 3 */

/* AUX_SYSIF_WUGATE */

#define AUX_SYSIF_WUGATE_EN                             (1 << 0)  /* Bit 0:  Wakeup output enable */

/* AUX_SYSIF_VECCFG0 */

#define AUX_SYSIF_VECCFG0_VEC_EV_SHIFT                  (0)       /* Bits 0-3: Select trigger event for vector 0 */
#define AUX_SYSIF_VECCFG0_VEC_EV_MASK                   (15 << AUX_SYSIF_VECCFG0_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG0_VEC_EV(n)                   ((uint32_t)(n) << AUX_SYSIF_VECCFG0_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG0_VEC_EV_NONE                 (0 << AUX_SYSIF_VECCFG0_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG0_VEC_EV_PROG_WU0             (1 << AUX_SYSIF_VECCFG0_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG0_VEC_EV_PROG_WU1             (2 << AUX_SYSIF_VECCFG0_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG0_VEC_EV_PROG_WU2             (3 << AUX_SYSIF_VECCFG0_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG0_VEC_EV_PROG_WU3             (4 << AUX_SYSIF_VECCFG0_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG0_VEC_EV_SW_WU0               (5 << AUX_SYSIF_VECCFG0_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG0_VEC_EV_SW_WU1               (6 << AUX_SYSIF_VECCFG0_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG0_VEC_EV_SW_WU2               (7 << AUX_SYSIF_VECCFG0_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG0_VEC_EV_SW_WU3               (8 << AUX_SYSIF_VECCFG0_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG0_VEC_EV_AON_RTC_CH2_DLY      (9 << AUX_SYSIF_VECCFG0_VEC_EV_SHIFT)

/* AUX_SYSIF_VECCFG1 */

#define AUX_SYSIF_VECCFG1_VEC_EV_SHIFT                  (0)       /* Bits 0-3: Select trigger event for vector 1 */
#define AUX_SYSIF_VECCFG1_VEC_EV_MASK                   (15 << AUX_SYSIF_VECCFG1_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG1_VEC_EV(n)                   ((uint32_t)(n) << AUX_SYSIF_VECCFG1_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG1_VEC_EV_NONE                 (0 << AUX_SYSIF_VECCFG1_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG1_VEC_EV_PROG_WU0             (1 << AUX_SYSIF_VECCFG1_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG1_VEC_EV_PROG_WU1             (2 << AUX_SYSIF_VECCFG1_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG1_VEC_EV_PROG_WU2             (3 << AUX_SYSIF_VECCFG1_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG1_VEC_EV_PROG_WU3             (4 << AUX_SYSIF_VECCFG1_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG1_VEC_EV_SW_WU0               (5 << AUX_SYSIF_VECCFG1_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG1_VEC_EV_SW_WU1               (6 << AUX_SYSIF_VECCFG1_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG1_VEC_EV_SW_WU2               (7 << AUX_SYSIF_VECCFG1_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG1_VEC_EV_SW_WU3               (8 << AUX_SYSIF_VECCFG1_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG1_VEC_EV_AON_RTC_CH2_DLY      (9 << AUX_SYSIF_VECCFG1_VEC_EV_SHIFT)

/* AUX_SYSIF_VECCFG2 */

#define AUX_SYSIF_VECCFG2_VEC_EV_SHIFT                  (0)       /* Bits 0-3: Select trigger event for vector 2 */
#define AUX_SYSIF_VECCFG2_VEC_EV_MASK                   (15 << AUX_SYSIF_VECCFG2_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG2_VEC_EV(n)                   ((uint32_t)(n) << AUX_SYSIF_VECCFG2_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG2_VEC_EV_NONE                 (0 << AUX_SYSIF_VECCFG2_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG2_VEC_EV_PROG_WU0             (1 << AUX_SYSIF_VECCFG2_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG2_VEC_EV_PROG_WU1             (2 << AUX_SYSIF_VECCFG2_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG2_VEC_EV_PROG_WU2             (3 << AUX_SYSIF_VECCFG2_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG2_VEC_EV_PROG_WU3             (4 << AUX_SYSIF_VECCFG2_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG2_VEC_EV_SW_WU0               (5 << AUX_SYSIF_VECCFG2_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG2_VEC_EV_SW_WU1               (6 << AUX_SYSIF_VECCFG2_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG2_VEC_EV_SW_WU2               (7 << AUX_SYSIF_VECCFG2_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG2_VEC_EV_SW_WU3               (8 << AUX_SYSIF_VECCFG2_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG2_VEC_EV_AON_RTC_CH2_DLY      (9 << AUX_SYSIF_VECCFG2_VEC_EV_SHIFT)

/* AUX_SYSIF_VECCFG3 */

#define AUX_SYSIF_VECCFG3_VEC_EV_SHIFT                  (0)       /* Bits 0-3: Select trigger event for vector 3 */
#define AUX_SYSIF_VECCFG3_VEC_EV_MASK                   (15 << AUX_SYSIF_VECCFG3_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG3_VEC_EV(n)                   ((uint32_t)(n) << AUX_SYSIF_VECCFG3_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG3_VEC_EV_NONE                 (0 << AUX_SYSIF_VECCFG3_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG3_VEC_EV_PROG_WU0             (1 << AUX_SYSIF_VECCFG3_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG3_VEC_EV_PROG_WU1             (2 << AUX_SYSIF_VECCFG3_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG3_VEC_EV_PROG_WU2             (3 << AUX_SYSIF_VECCFG3_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG3_VEC_EV_PROG_WU3             (4 << AUX_SYSIF_VECCFG3_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG3_VEC_EV_SW_WU0               (5 << AUX_SYSIF_VECCFG3_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG3_VEC_EV_SW_WU1               (6 << AUX_SYSIF_VECCFG3_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG3_VEC_EV_SW_WU2               (7 << AUX_SYSIF_VECCFG3_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG3_VEC_EV_SW_WU3               (8 << AUX_SYSIF_VECCFG3_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG3_VEC_EV_AON_RTC_CH2_DLY      (9 << AUX_SYSIF_VECCFG3_VEC_EV_SHIFT)

/* AUX_SYSIF_VECCFG4 */

#define AUX_SYSIF_VECCFG4_VEC_EV_SHIFT                  (0)       /* Bits 0-3: Select trigger event for vector 4 */
#define AUX_SYSIF_VECCFG4_VEC_EV_MASK                   (15 << AUX_SYSIF_VECCFG4_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG4_VEC_EV(n)                   ((uint32_t)(n) << AUX_SYSIF_VECCFG4_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG4_VEC_EV_NONE                 (0 << AUX_SYSIF_VECCFG4_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG4_VEC_EV_PROG_WU0             (1 << AUX_SYSIF_VECCFG4_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG4_VEC_EV_PROG_WU1             (2 << AUX_SYSIF_VECCFG4_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG4_VEC_EV_PROG_WU2             (3 << AUX_SYSIF_VECCFG4_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG4_VEC_EV_PROG_WU3             (4 << AUX_SYSIF_VECCFG4_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG4_VEC_EV_SW_WU0               (5 << AUX_SYSIF_VECCFG4_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG4_VEC_EV_SW_WU1               (6 << AUX_SYSIF_VECCFG4_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG4_VEC_EV_SW_WU2               (7 << AUX_SYSIF_VECCFG4_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG4_VEC_EV_SW_WU3               (8 << AUX_SYSIF_VECCFG4_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG4_VEC_EV_AON_RTC_CH2_DLY      (9 << AUX_SYSIF_VECCFG4_VEC_EV_SHIFT)

/* AUX_SYSIF_VECCFG5 */

#define AUX_SYSIF_VECCFG5_VEC_EV_SHIFT                  (0)       /* Bits 0-3: Select trigger event for vector 5 */
#define AUX_SYSIF_VECCFG5_VEC_EV_MASK                   (15 << AUX_SYSIF_VECCFG5_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG5_VEC_EV(n)                   ((uint32_t)(n) << AUX_SYSIF_VECCFG5_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG5_VEC_EV_NONE                 (0 << AUX_SYSIF_VECCFG5_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG5_VEC_EV_PROG_WU0             (1 << AUX_SYSIF_VECCFG5_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG5_VEC_EV_PROG_WU1             (2 << AUX_SYSIF_VECCFG5_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG5_VEC_EV_PROG_WU2             (3 << AUX_SYSIF_VECCFG5_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG5_VEC_EV_PROG_WU3             (4 << AUX_SYSIF_VECCFG5_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG5_VEC_EV_SW_WU0               (5 << AUX_SYSIF_VECCFG5_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG5_VEC_EV_SW_WU1               (6 << AUX_SYSIF_VECCFG5_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG5_VEC_EV_SW_WU2               (7 << AUX_SYSIF_VECCFG5_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG5_VEC_EV_SW_WU3               (8 << AUX_SYSIF_VECCFG5_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG5_VEC_EV_AON_RTC_CH2_DLY      (9 << AUX_SYSIF_VECCFG5_VEC_EV_SHIFT)

/* AUX_SYSIF_VECCFG6 */

#define AUX_SYSIF_VECCFG6_VEC_EV_SHIFT                  (0)       /* Bits 0-3: Select trigger event for vector 6 */
#define AUX_SYSIF_VECCFG6_VEC_EV_MASK                   (15 << AUX_SYSIF_VECCFG6_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG6_VEC_EV(n)                   ((uint32_t)(n) << AUX_SYSIF_VECCFG6_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG6_VEC_EV_NONE                 (0 << AUX_SYSIF_VECCFG6_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG6_VEC_EV_PROG_WU0             (1 << AUX_SYSIF_VECCFG6_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG6_VEC_EV_PROG_WU1             (2 << AUX_SYSIF_VECCFG6_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG6_VEC_EV_PROG_WU2             (3 << AUX_SYSIF_VECCFG6_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG6_VEC_EV_PROG_WU3             (4 << AUX_SYSIF_VECCFG6_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG6_VEC_EV_SW_WU0               (5 << AUX_SYSIF_VECCFG6_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG6_VEC_EV_SW_WU1               (6 << AUX_SYSIF_VECCFG6_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG6_VEC_EV_SW_WU2               (7 << AUX_SYSIF_VECCFG6_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG6_VEC_EV_SW_WU3               (8 << AUX_SYSIF_VECCFG6_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG6_VEC_EV_AON_RTC_CH2_DLY      (9 << AUX_SYSIF_VECCFG6_VEC_EV_SHIFT)

/* AUX_SYSIF_VECCFG7 */

#define AUX_SYSIF_VECCFG7_VEC_EV_SHIFT                  (0)       /* Bits 0-3: Select trigger event for vector 7 */
#define AUX_SYSIF_VECCFG7_VEC_EV_MASK                   (15 << AUX_SYSIF_VECCFG7_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG7_VEC_EV(n)                   ((uint32_t)(n) << AUX_SYSIF_VECCFG7_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG7_VEC_EV_NONE                 (0 << AUX_SYSIF_VECCFG7_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG7_VEC_EV_PROG_WU0             (1 << AUX_SYSIF_VECCFG7_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG7_VEC_EV_PROG_WU1             (2 << AUX_SYSIF_VECCFG7_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG7_VEC_EV_PROG_WU2             (3 << AUX_SYSIF_VECCFG7_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG7_VEC_EV_PROG_WU3             (4 << AUX_SYSIF_VECCFG7_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG7_VEC_EV_SW_WU0               (5 << AUX_SYSIF_VECCFG7_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG7_VEC_EV_SW_WU1               (6 << AUX_SYSIF_VECCFG7_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG7_VEC_EV_SW_WU2               (7 << AUX_SYSIF_VECCFG7_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG7_VEC_EV_SW_WU3               (8 << AUX_SYSIF_VECCFG7_VEC_EV_SHIFT)
#  define AUX_SYSIF_VECCFG7_VEC_EV_AON_RTC_CH2_DLY      (9 << AUX_SYSIF_VECCFG7_VEC_EV_SHIFT)

/* AUX_SYSIF_EVSYNCRATE */

#define AUX_SYSIF_EVSYNCRATE_AUX_TIMER2_SYNC_RATE            (1 << 0)  /* Bit 0:  Select synchronization rate for
                                                                        *         AUX_EVCTL:EVSTAT3.AUX_TIMER2_EV0..AUX_TIMER2_EV3
                                                                        *         and AUX_TIMER2_PULSE */
#  define AUX_SYSIF_EVSYNCRATE_AUX_TIMER2_SYNC_RATE_BUS_RATE AUX_SYSIF_EVSYNCRATE_AUX_TIMER2_SYNC_RATE
#  define AUX_SYSIF_EVSYNCRATE_AUX_TIMER2_SYNC_RATE_SCE_RATE (0)
#define AUX_SYSIF_EVSYNCRATE_AUX_COMPB_SYNC_RATE             (1 << 1)  /* Bit 1:  Select synchronization rate for
                                                                        *         AUX_EVCTL:EVSTAT2.AUX_COMPB event */
#  define AUX_SYSIF_EVSYNCRATE_AUX_COMPB_SYNC_RATE_BUS_RATE  AUX_SYSIF_EVSYNCRATE_AUX_COMPB_SYNC_RATE
#  define AUX_SYSIF_EVSYNCRATE_AUX_COMPB_SYNC_RATE_SCE_RATE  (0)
#define AUX_SYSIF_EVSYNCRATE_AUX_COMPA_SYNC_RATE             (1 << 2)  /* Bit 2: Select synchronization rate for
                                                                        *        AUX_EVCTL:EVSTAT2.AUX_COMPA event  */
#  define AUX_SYSIF_EVSYNCRATE_AUX_COMPA_SYNC_RATE_BUS_RATE  AUX_SYSIF_EVSYNCRATE_AUX_COMPA_SYNC_RATE
#  define AUX_SYSIF_EVSYNCRATE_AUX_COMPA_SYNC_RATE_SCE_RATE  (0)

/* AUX_SYSIF_PEROPRATE */

#define AUX_SYSIF_PEROPRATE_MAC_OP_RATE                  (1 << 0)  /* Bit 0:  Select operational rate for AUX_MAC */
#  define AUX_SYSIF_PEROPRATE_MAC_OP_RATE_BUS_RATE       AUX_SYSIF_PEROPRATE_MAC_OP_RATE
#  define AUX_SYSIF_PEROPRATE_MAC_OP_RATE_SCE_RATE       (0)
#define AUX_SYSIF_PEROPRATE_SPIM_OP_RATE                 (1 << 1)  /* Bit 1:  Select operational rate for AUX_SPIM */
#  define AUX_SYSIF_PEROPRATE_SPIM_OP_RATE_BUS_RATE      AUX_SYSIF_PEROPRATE_SPIM_OP_RATE
#  define AUX_SYSIF_PEROPRATE_SPIM_OP_RATE_SCE_RATE      (0)
#define AUX_SYSIF_PEROPRATE_TIMER01_OP_RATE              (1 << 2)  /* Bit 2:  Select operational rate for AUX_TIMER01 */
#  define AUX_SYSIF_PEROPRATE_TIMER01_OP_RATE_BUS_RATE   AUX_SYSIF_PEROPRATE_TIMER01_OP_RATE
#  define AUX_SYSIF_PEROPRATE_TIMER01_OP_RATE_SCE_RATE   (0)
#define AUX_SYSIF_PEROPRATE_ANAIF_DAC_OP_RATE            (1 << 3)  /* Bit 3:  Select operational rate for AUX_ANAIF
                                                                    * DAC sample clock state machine */
#  define AUX_SYSIF_PEROPRATE_ANAIF_DAC_OP_RATE_BUS_RATE AUX_SYSIF_PEROPRATE_ANAIF_DAC_OP_RATE
#  define AUX_SYSIF_PEROPRATE_ANAIF_DAC_OP_RATE_SCE_RATE (0)

/* AUX_SYSIF_ADCCLKCTL */

#define AUX_SYSIF_ADCCLKCTL_REQ                         (1 << 0)  /* Bit 0:  ADC clock request */
#define AUX_SYSIF_ADCCLKCTL_ACK                         (1 << 1)  /* Bit 1:  Clock acknowledgment */

/* AUX_SYSIF_TDCCLKCTL */

#define AUX_SYSIF_TDCCLKCTL_REQ                         (1 << 0)  /* Bit 0:  TDC counter clock request */
#define AUX_SYSIF_TDCCLKCTL_ACK                         (1 << 1)  /* Bit 1:  TDC counter clock acknowledgment */

/* AUX_SYSIF_TDCREFCLKCTL */

#define AUX_SYSIF_TDCREFCLKCTL_REQ                      (1 << 0)  /* Bit 0:  TDC reference clock request */
#define AUX_SYSIF_TDCREFCLKCTL_ACK                      (1 << 1)  /* Bit 1:  TDC reference clock acknowledgment */

/* AUX_SYSIF_TIMER2CLKCTL */

#define AUX_SYSIF_TIMER2CLKCTL_SRC_SHIFT                (0)       /* Bits 0-2: Select clock source for AUX_TIMER2 */
#define AUX_SYSIF_TIMER2CLKCTL_SRC_MASK                 (7 << AUX_SYSIF_TIMER2CLKCTL_SRC_SHIFT)
#  define AUX_SYSIF_TIMER2CLKCTL_SRC(n)                 ((uint32_t)(n) << AUX_SYSIF_TIMER2CLKCTL_SRC_SHIFT)
#  define AUX_SYSIF_TIMER2CLKCTL_SRC_NONE               (0 << AUX_SYSIF_TIMER2CLKCTL_SRC_SHIFT)) /* No clock */
#  define AUX_SYSIF_TIMER2CLKCTL_SRC_SCLK_LF            (1 << AUX_SYSIF_TIMER2CLKCTL_SRC_SHIFT)) /* SCLK_LF */
#  define AUX_SYSIF_TIMER2CLKCTL_SRC_SCLK_MF            (2 << AUX_SYSIF_TIMER2CLKCTL_SRC_SHIFT)) /* SCLK_MF */
#  define AUX_SYSIF_TIMER2CLKCTL_SRC_SCLK_HFDIV2        (4 << AUX_SYSIF_TIMER2CLKCTL_SRC_SHIFT)) /* SCLK_HF / 2 */

/* AUX_SYSIF_TIMER2CLKSTAT */

#define AUX_SYSIF_TIMER2CLKSTAT_STAT_SHIFT              (0)       /* Bits 0-2: AUX_TIMER2 clock source status */
#define AUX_SYSIF_TIMER2CLKSTAT_STAT_MASK               (7 << AUX_SYSIF_TIMER2CLKSTAT_STAT_SHIFT)
#  define AUX_SYSIF_TIMER2CLKSTAT_STAT(n)               ((uint32_t)(n) << AUX_SYSIF_TIMER2CLKSTAT_STAT_SHIFT)
#  define AUX_SYSIF_TIMER2CLKSTAT_STAT_NONE             (0 << AUX_SYSIF_TIMER2CLKSTAT_STAT_SHIFT)) /* No clock */
#  define AUX_SYSIF_TIMER2CLKSTAT_STAT_SCLK_LF          (1 << AUX_SYSIF_TIMER2CLKSTAT_STAT_SHIFT)) /* SCLK_LF */
#  define AUX_SYSIF_TIMER2CLKSTAT_STAT_SCLK_MF          (2 << AUX_SYSIF_TIMER2CLKSTAT_STAT_SHIFT)) /* SCLK_MF */
#  define AUX_SYSIF_TIMER2CLKSTAT_STAT_SCLK_HFDIV2      (4 << AUX_SYSIF_TIMER2CLKSTAT_STAT_SHIFT)) /* SCLK_HF / 2 */

/* AUX_SYSIF_TIMER2CLKSWITCH */

#define AUX_SYSIF_TIMER2CLKSWITCH_RDY                   (1 << 0)  /* Bit 0:  Status of clock switcher */
                                                                  /* 1: TIMER2CLKCTL.SRC equals TIMER2CLKSTAT.STA */

/* AUX_SYSIF_TIMER2DBGCTL */

#define AUX_SYSIF_TIMER2DBGCTL_DBG_FREEZE_EN            (1 << 0)  /* Bit 0:  Debug freeze enable */

/* AUX_SYSIF_CLKSHIFTDET */

#define AUX_SYSIF_CLKSHIFTDET_STAT                      (1 << 0)  /* Bit 0:  Clock shift detection */

/* AUX_SYSIF_RECHARGETRIG */

#define AUX_SYSIF_RECHARGETRIG_TRIG                     (1 << 0)  /* Bit 0:   Recharge trigger */

/* AUX_SYSIF_RECHARGEDET */

#define AUX_SYSIF_RECHARGEDET_EN                        (1 << 0)  /* Bit 0:  VDDR recharge detector enable */
#define AUX_SYSIF_RECHARGEDET_STAT                      (1 << 1)  /* Bit 1:  VDDR recharge detector status */

/* AUX_SYSIF_RTCSUBSECINC0 */

#define AUX_SYSIF_RTCSUBSECINC0_INC15_0_SHIFT           (0)       /* Bits 0-15: New value for bits 15:0 in AON_RTC:SUBSECINC  */
#define AUX_SYSIF_RTCSUBSECINC0_INC15_0_MASK            (0xffff << AUX_SYSIF_RTCSUBSECINC0_INC15_0_SHIFT)
#  define AUX_SYSIF_RTCSUBSECINC0_INC15_0(n)            ((uint32_t)(n) << AUX_SYSIF_RTCSUBSECINC0_INC15_0_SHIFT)

/* AUX_SYSIF_RTCSUBSECINC1 */

#define AUX_SYSIF_RTCSUBSECINC1_INC23_16_SHIFT          (0)       /* Bits 0-7: New value for bits 23:16 in AON_RTC:SUBSECINC */
#define AUX_SYSIF_RTCSUBSECINC1_INC23_16_MASK           (0xff << AUX_SYSIF_RTCSUBSECINC1_INC23_16_SHIFT)
#  define AUX_SYSIF_RTCSUBSECINC1_INC23_16(n)           ((uint32_t)(n) << AUX_SYSIF_RTCSUBSECINC1_INC23_16_SHIFT)

/* AUX_SYSIF_RTCSUBSECINCCTL */

#define AUX_SYSIF_RTCSUBSECINCCTL_UPD_REQ               (1 << 0)  /* Bit 0:  Request AON_RTC to update AON_RTC:SUBSECINC */
#define AUX_SYSIF_RTCSUBSECINCCTL_UPD_ACK               (1 << 1)  /* Bit 1:  Update acknowledgment */

/* AUX_SYSIF_RTCSEC */

#define AUX_SYSIF_RTCSEC_SEC_SHIFT                      (0)       /* Bits 0-15: Bits 15:0 in AON_RTC:SEC.VALUE */
#define AUX_SYSIF_RTCSEC_SEC_MASK                       (0xffff << AUX_SYSIF_RTCSEC_SEC_SHIFT)
#  define AUX_SYSIF_RTCSEC_SEC(n)                       ((uint32_t)(n) << AUX_SYSIF_RTCSEC_SEC_SHIFT)

/* AUX_SYSIF_RTCSUBSEC */

#define AUX_SYSIF_RTCSUBSEC_SUBSEC_SHIFT                (0)       /* Bits 0-15: Bits 31:16 in AON_RTC:SUBSEC.VALUE */
#define AUX_SYSIF_RTCSUBSEC_SUBSEC_MASK                 (0xffff << AUX_SYSIF_RTCSUBSEC_SUBSEC_SHIFT)
#  define AUX_SYSIF_RTCSUBSEC_SUBSEC(n)                 ((uint32_t)(n) << AUX_SYSIF_RTCSUBSEC_SUBSEC_SHIFT)

/* AUX_SYSIF_RTCEVCLR */

#define AUX_SYSIF_RTCEVCLR_RTC_CH2_EV_CLR               (1 << 0)  /* Bit 0:  Clear events from AON_RTC channel 2 */

/* AUX_SYSIF_BATMONBAT */

#define AUX_SYSIF_BATMONBAT_FRAC_SHIFT                  (0)       /* Bits 0-7: See AON_BATMON:BAT.FRAC */
#define AUX_SYSIF_BATMONBAT_FRAC_MASK                   (0xff << AUX_SYSIF_BATMONBAT_FRAC_SHIFT)
#  define AUX_SYSIF_BATMONBAT_FRAC(n)                   ((uint32_t)(n) << AUX_SYSIF_BATMONBAT_FRAC_SHIFT)
#define AUX_SYSIF_BATMONBAT_INT_SHIFT                   (8)       /* Bits 8-10: See AON_BATMON:BAT.INT */
#define AUX_SYSIF_BATMONBAT_INT_MASK                    (7 << AUX_SYSIF_BATMONBAT_INT_SHIFT)
#  define AUX_SYSIF_BATMONBAT_INT(n)                    ((uint32_t)(n) << xxAUX_SYSIF_BATMONBAT_INT_SHIFT

/* AUX_SYSIF_BATMONTEMP */

#define AUX_SYSIF_BATMONTEMP_FRAC_SHIFT                 (0)       /* Bits 0-1: See AON_BATMON:TEMP.FRAC */
#define AUX_SYSIF_BATMONTEMP_FRAC_MASK                  (3 << AUX_SYSIF_BATMONTEMP_FRAC_SHIFT)
#  define AUX_SYSIF_BATMONTEMP_FRAC(n)                  ((uint32_t)(n) << AUX_SYSIF_BATMONTEMP_FRAC_SHIFT)
#define AUX_SYSIF_BATMONTEMP_INT_SHIFT                  (2)       /* Bits 2-10: See AON_BATMON:TEMP.INT */
#define AUX_SYSIF_BATMONTEMP_INT_MASK                   (0x1ff << AUX_SYSIF_BATMONTEMP_INT_SHIFT)
#  define AUX_SYSIF_BATMONTEMP_INT(n)                   ((uint32_t)(n) << AUX_SYSIF_BATMONTEMP_INT_SHIFT)
#define AUX_SYSIF_BATMONTEMP_SIGN_SHIFT                 (11)       /* Bits 11-15: Sign extension of INT */
#define AUX_SYSIF_BATMONTEMP_SIGN_MASK                  (31 << AUX_SYSIF_BATMONTEMP_SIGN_SHIFT)
#  define AUX_SYSIF_BATMONTEMP_SIGN(n)                  ((uint32_t)(n) << AUX_SYSIF_BATMONTEMP_SIGN_SHIFT)

/* AUX_SYSIF_TIMERHALT */

#define AUX_SYSIF_TIMERHALT_AUX_TIMER0                  (1 << 0)  /* Bit 0:  Halt AUX_TIMER01 Timer 0 */
#define AUX_SYSIF_TIMERHALT_AUX_TIMER1                  (1 << 1)  /* Bit 1:  Halt AUX_TIMER01 Timer 1 */
#define AUX_SYSIF_TIMERHALT_AUX_TIMER2                  (1 << 2)  /* Bit 2:  Halt AUX_TIMER2 */
#define AUX_SYSIF_TIMERHALT_PROGDLY                     (1 << 3)  /* Bit 3:  Halt programmable delay */

/* AUX_SYSIF_TIMER2BRIDGE */

#define AUX_SYSIF_TIMER2BRIDGE_BUSY                     (1 << 0)  /* Bit 0:  Status of bus transactions to AUX_TIMER2 */

/* AUX_SYSIF_SWPWRPROF */

#define AUX_SYSIF_SWPWRPROF_STAT_SHIFT                  (0)       /* Bits 0-2:  Software status bits read by the power profiler */
#define AUX_SYSIF_SWPWRPROF_STAT_MASK                   (7 << AUX_SYSIF_SWPWRPROF_STAT_SHIFT)
#  define AUX_SYSIF_SWPWRPROF_STAT(n)                   ((uint32_t)(n) << AUX_SYSIF_SWPWRPROF_STAT_SHIFT)

#endif /* __ARCH_ARM_SRC_TIVA_HARDWARE_CC13X2_CC26X2_CC13X2_CC26X2_AUX_SYSIF_H */
