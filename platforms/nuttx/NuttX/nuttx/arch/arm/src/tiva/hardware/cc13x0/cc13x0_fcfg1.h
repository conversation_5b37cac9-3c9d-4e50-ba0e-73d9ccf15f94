/****************************************************************************
 * arch/arm/src/tiva/hardware/cc13x0/cc13x0_fcfg1.h
 *
 *   Copyright (C) 2018 Gregory Nutt. All rights reserved.
 *   Authors: <AUTHORS>
 *
 * Technical content derives from a TI header file that has a
 * compatible BSD license:
 *
 *   Copyright (c) 2015-2017, Texas Instruments Incorporated
 *   All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 * 3. Neither the name NuttX nor the names of its contributors may be
 *    used to endorse or promote products derived from this software
 *    without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS
 * OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 * AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 * LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
 * ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_TIVA_HARDWARE_CC13X0_CC13X0_FCFG1_H
#define __ARCH_ARM_SRC_TIVA_HARDWARE_CC13X0_CC13X0_FCFG1_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include "hardware/tiva_memorymap.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* FCFG1 Register Offsets ***************************************************/

#define TIVA_FCFG1_MISC_CONF_1_OFFSET                      0x00a0  /* Misc configurations */
#define TIVA_FCFG1_MISC_CONF_2_OFFSET                      0x00a4
#define TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV5_OFFSET          0x00c4
#define TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV6_OFFSET          0x00c8
#define TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV10_OFFSET         0x00cc
#define TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV12_OFFSET         0x00d0
#define TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV15_OFFSET         0x00d4
#define TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV30_OFFSET         0x00d8
#define TIVA_FCFG1_CONFIG_SYNTH_DIV5_OFFSET                0x00dc
#define TIVA_FCFG1_CONFIG_SYNTH_DIV6_OFFSET                0x00e0
#define TIVA_FCFG1_CONFIG_SYNTH_DIV10_OFFSET               0x00e4
#define TIVA_FCFG1_CONFIG_SYNTH_DIV12_OFFSET               0x00e8
#define TIVA_FCFG1_CONFIG_SYNTH_DIV15_OFFSET               0x00ec
#define TIVA_FCFG1_CONFIG_SYNTH_DIV30_OFFSET               0x00f0
#define TIVA_FCFG1_CONFIG_MISC_ADC_DIV5_OFFSET             0x00f4
#define TIVA_FCFG1_CONFIG_MISC_ADC_DIV6_OFFSET             0x00f8
#define TIVA_FCFG1_CONFIG_MISC_ADC_DIV10_OFFSET            0x00fc
#define TIVA_FCFG1_CONFIG_MISC_ADC_DIV12_OFFSET            0x0100
#define TIVA_FCFG1_CONFIG_MISC_ADC_DIV15_OFFSET            0x0104
#define TIVA_FCFG1_CONFIG_MISC_ADC_DIV30_OFFSET            0x0108
#define TIVA_FCFG1_SHDW_DIE_ID_0_OFFSET                    0x0118  /* Shadow of EFUSE:DIE_ID_0 */
#define TIVA_FCFG1_SHDW_DIE_ID_1_OFFSET                    0x011c  /* Shadow of EFUSE:DIE_ID_1 */
#define TIVA_FCFG1_SHDW_DIE_ID_2_OFFSET                    0x0120  /* Shadow of EFUSE:DIE_ID_2 */
#define TIVA_FCFG1_SHDW_DIE_ID_3_OFFSET                    0x0124  /* Shadow of EFUSE:DIE_ID_3 */
#define TIVA_FCFG1_SHDW_OSC_BIAS_LDO_TRIM_OFFSET           0x0138
#define TIVA_FCFG1_SHDW_ANA_TRIM_OFFSET                    0x013c
#define TIVA_FCFG1_FLASH_NUMBER_OFFSET                     0x0164  /* Flash information */
#define TIVA_FCFG1_FLASH_COORDINATE_OFFSET                 0x016c  /* Flash information */
#define TIVA_FCFG1_FLASH_E_P_OFFSET                        0x0170
#define TIVA_FCFG1_FLASH_C_E_P_R_OFFSET                    0x0174
#define TIVA_FCFG1_FLASH_P_R_PV_OFFSET                     0x0178
#define TIVA_FCFG1_FLASH_EH_SEQ_OFFSET                     0x017c
#define TIVA_FCFG1_FLASH_VHV_E_OFFSET                      0x0180
#define TIVA_FCFG1_FLASH_PP_OFFSET                         0x0184
#define TIVA_FCFG1_FLASH_PROG_EP_OFFSET                    0x0188
#define TIVA_FCFG1_FLASH_ERA_PW_OFFSET                     0x018c
#define TIVA_FCFG1_FLASH_VHV_OFFSET                        0x0190
#define TIVA_FCFG1_FLASH_VHV_PV_OFFSET                     0x0194
#define TIVA_FCFG1_FLASH_V_OFFSET                          0x0198
#define TIVA_FCFG1_USER_ID_OFFSET                          0x0294  /* User Identification. */
#define TIVA_FCFG1_FLASH_OTP_DATA3_OFFSET                  0x02b0
#define TIVA_FCFG1_ANA2_TRIM_OFFSET                        0x02b4
#define TIVA_FCFG1_LDO_TRIM_OFFSET                         0x02b8
#define TIVA_FCFG1_BAT_RC_LDO_TRIM_OFFSET                  0x02bc
#define TIVA_FCFG1_MAC_BLE_0_OFFSET                        0x02e8  /* MAC BLE Address 0 */
#define TIVA_FCFG1_MAC_BLE_1_OFFSET                        0x02ec  /* MAC BLE Address 1 */
#define TIVA_FCFG1_MAC_15_4_0_OFFSET                       0x02f0  /* MAC IEEE 802.15.4 Address 0 */
#define TIVA_FCFG1_MAC_15_4_1_OFFSET                       0x02f4  /* MAC IEEE 802.15.4 Address 1 */
#define TIVA_FCFG1_FLASH_OTP_DATA4_OFFSET                  0x0308
#define TIVA_FCFG1_MISC_TRIM_OFFSET                        0x030c  /* Miscellaneous Trim  Parameters */
#define TIVA_FCFG1_RCOSC_HF_TEMPCOMP_OFFSET                0x0310
#define TIVA_FCFG1_TRIM_CAL_REVISION_OFFSET                0x0314
#define TIVA_FCFG1_ICEPICK_DEVICE_ID_OFFSET                0x0318  /* IcePick Device Identification */
#define TIVA_FCFG1_FCFG1_REVISION_OFFSET                   0x031c  /* Factory Configuration (FCFG1) Revision */
#define TIVA_FCFG1_MISC_OTP_DATA_OFFSET                    0x0320  /* Misc OTP Data */
#define TIVA_FCFG1_IOCONF_OFFSET                           0x0344  /* IO Configuration */
#define TIVA_FCFG1_CONFIG_IF_ADC_OFFSET                    0x034c
#define TIVA_FCFG1_CONFIG_OSC_TOP_OFFSET                   0x0350
#define TIVA_FCFG1_CONFIG_RF_FRONTEND_OFFSET               0x0354
#define TIVA_FCFG1_CONFIG_SYNTH_OFFSET                     0x0358
#define TIVA_FCFG1_SOC_ADC_ABS_GAIN_OFFSET                 0x035c  /* AUX_ADC Gain in Absolute Reference Mode */
#define TIVA_FCFG1_SOC_ADC_REL_GAIN_OFFSET                 0x0360  /* AUX_ADC Gain in Relative Reference Mode */
#define TIVA_FCFG1_SOC_ADC_OFFSET_INT_OFFSET               0x0368  /* AUX_ADC Temperature Offsets in Absolute Reference Mode */
#define TIVA_FCFG1_SOC_ADC_REF_TRIM_AND_OFFSET_EXT_OFFSET  0x036c
#define TIVA_FCFG1_AMPCOMP_TH1_OFFSET                      0x0370
#define TIVA_FCFG1_AMPCOMP_TH2_OFFSET                      0x0374
#define TIVA_FCFG1_AMPCOMP_CTRL1_OFFSET                    0x0378
#define TIVA_FCFG1_ANABYPASS_VALUE2_OFFSET                 0x037c
#define TIVA_FCFG1_CONFIG_MISC_ADC_OFFSET                  0x0380
#define TIVA_FCFG1_VOLT_TRIM_OFFSET                        0x0388
#define TIVA_FCFG1_OSC_CONF_OFFSET                         0x038c  /* OSC Configuration */
#define TIVA_FCFG1_FREQ_OFFSET_OFFSET                      0x0390
#define TVIA_FCFG1_CAP_TRIM_OFFSET                         0x0394
#define TIVA_FCFG1_MISC_OTP_DATA_1_OFFSET                  0x0398
#define TIVA_FCFG1_PWD_CURR_20C_OFFSET                     0x039c  /* Power Down Current Control 20C */
#define TIVA_FCFG1_PWD_CURR_35C_OFFSET                     0x03a0  /* Power Down Current Control 35C */
#define TIVA_FCFG1_PWD_CURR_50C_OFFSET                     0x03a4  /* Power Down Current Control 50C */
#define TIVA_FCFG1_PWD_CURR_65C_OFFSET                     0x03a8  /* Power Down Current Control 65C */
#define TIVA_FCFG1_PWD_CURR_80C_OFFSET                     0x03ac  /* Power Down Current Control 80C */
#define TIVA_FCFG1_PWD_CURR_95C_OFFSET                     0x03b0  /* Power Down Current Control 95C */
#define TIVA_FCFG1_PWD_CURR_110C_OFFSET                    0x03b4  /* Power Down Current Control 110C */
#define TIVA_FCFG1_PWD_CURR_125C_OFFSET                    0x03b8  /* Power Down Current Control 125C */

/* FCFG1 Register Register Addresses ****************************************/

#define TIVA_FCFG1_MISC_CONF_1                             (TIVA_FCFG1_BASE + TIVA_FCFG1_MISC_CONF_1_OFFSET)
#define TIVA_FCFG1_MISC_CONF_2                             (TIVA_FCFG1_BASE + TIVA_FCFG1_MISC_CONF_2_OFFSET)
#define TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV5                 (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV5_OFFSET)
#define TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV6                 (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV6_OFFSET)
#define TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV10                (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV10_OFFSET)
#define TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV12                (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV12_OFFSET)
#define TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV15                (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV15_OFFSET)
#define TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV30                (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV30_OFFSET)
#define TIVA_FCFG1_CONFIG_SYNTH_DIV5                       (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_SYNTH_DIV5_OFFSET)
#define TIVA_FCFG1_CONFIG_SYNTH_DIV6                       (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_SYNTH_DIV6_OFFSET)
#define TIVA_FCFG1_CONFIG_SYNTH_DIV10                      (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_SYNTH_DIV10_OFFSET)
#define TIVA_FCFG1_CONFIG_SYNTH_DIV12                      (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_SYNTH_DIV12_OFFSET)
#define TIVA_FCFG1_CONFIG_SYNTH_DIV15                      (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_SYNTH_DIV15_OFFSET)
#define TIVA_FCFG1_CONFIG_SYNTH_DIV30                      (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_SYNTH_DIV30_OFFSET)
#define TIVA_FCFG1_CONFIG_MISC_ADC_DIV5                    (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_MISC_ADC_DIV5_OFFSET)
#define TIVA_FCFG1_CONFIG_MISC_ADC_DIV6                    (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_MISC_ADC_DIV6_OFFSET)
#define TIVA_FCFG1_CONFIG_MISC_ADC_DIV10                   (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_MISC_ADC_DIV10_OFFSET)
#define TIVA_FCFG1_CONFIG_MISC_ADC_DIV12                   (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_MISC_ADC_DIV12_OFFSET)
#define TIVA_FCFG1_CONFIG_MISC_ADC_DIV15                   (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_MISC_ADC_DIV15_OFFSET)
#define TIVA_FCFG1_CONFIG_MISC_ADC_DIV30                   (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_MISC_ADC_DIV30_OFFSET)
#define TIVA_FCFG1_SHDW_DIE_ID_0                           (TIVA_FCFG1_BASE + TIVA_FCFG1_SHDW_DIE_ID_0_OFFSET)
#define TIVA_FCFG1_SHDW_DIE_ID_1                           (TIVA_FCFG1_BASE + TIVA_FCFG1_SHDW_DIE_ID_1_OFFSET)
#define TIVA_FCFG1_SHDW_DIE_ID_2                           (TIVA_FCFG1_BASE + TIVA_FCFG1_SHDW_DIE_ID_2_OFFSET)
#define TIVA_FCFG1_SHDW_DIE_ID_3                           (TIVA_FCFG1_BASE + TIVA_FCFG1_SHDW_DIE_ID_3_OFFSET)
#define TIVA_FCFG1_SHDW_OSC_BIAS_LDO_TRIM                  (TIVA_FCFG1_BASE + TIVA_FCFG1_SHDW_OSC_BIAS_LDO_TRIM_OFFSET)
#define TIVA_FCFG1_SHDW_ANA_TRIM                           (TIVA_FCFG1_BASE + TIVA_FCFG1_SHDW_ANA_TRIM_OFFSET)
#define TIVA_FCFG1_FLASH_NUMBER                            (TIVA_FCFG1_BASE + TIVA_FCFG1_FLASH_NUMBER_OFFSET)
#define TIVA_FCFG1_FLASH_COORDINATE                        (TIVA_FCFG1_BASE + TIVA_FCFG1_FLASH_COORDINATE_OFFSET)
#define TIVA_FCFG1_FLASH_E_P                               (TIVA_FCFG1_BASE + TIVA_FCFG1_FLASH_E_P_OFFSET)
#define TIVA_FCFG1_FLASH_C_E_P_R                           (TIVA_FCFG1_BASE + TIVA_FCFG1_FLASH_C_E_P_R_OFFSET)
#define TIVA_FCFG1_FLASH_P_R_PV                            (TIVA_FCFG1_BASE + TIVA_FCFG1_FLASH_P_R_PV_OFFSET)
#define TIVA_FCFG1_FLASH_EH_SEQ                            (TIVA_FCFG1_BASE + TIVA_FCFG1_FLASH_EH_SEQ_OFFSET)
#define TIVA_FCFG1_FLASH_VHV_E                             (TIVA_FCFG1_BASE + TIVA_FCFG1_FLASH_VHV_E_OFFSET)
#define TIVA_FCFG1_FLASH_PP                                (TIVA_FCFG1_BASE + TIVA_FCFG1_FLASH_PP_OFFSET)
#define TIVA_FCFG1_FLASH_PROG_EP                           (TIVA_FCFG1_BASE + TIVA_FCFG1_FLASH_PROG_EP_OFFSET)
#define TIVA_FCFG1_FLASH_ERA_PW                            (TIVA_FCFG1_BASE + TIVA_FCFG1_FLASH_ERA_PW_OFFSET)
#define TIVA_FCFG1_FLASH_VHV                               (TIVA_FCFG1_BASE + TIVA_FCFG1_FLASH_VHV_OFFSET)
#define TIVA_FCFG1_FLASH_VHV_PV                            (TIVA_FCFG1_BASE + TIVA_FCFG1_FLASH_VHV_PV_OFFSET)
#define TIVA_FCFG1_FLASH_V                                 (TIVA_FCFG1_BASE + TIVA_FCFG1_FLASH_V_OFFSET)
#define TIVA_FCFG1_USER_ID                                 (TIVA_FCFG1_BASE + TIVA_FCFG1_USER_ID_OFFSET)
#define TIVA_FCFG1_FLASH_OTP_DATA3                         (TIVA_FCFG1_BASE + TIVA_FCFG1_FLASH_OTP_DATA3_OFFSET)
#define TIVA_FCFG1_ANA2_TRIM                               (TIVA_FCFG1_BASE + TIVA_FCFG1_ANA2_TRIM_OFFSET)
#define TIVA_FCFG1_LDO_TRIM                                (TIVA_FCFG1_BASE + TIVA_FCFG1_LDO_TRIM_OFFSET)
#define TIVA_FCFG1_BAT_RC_LDO_TRIM                         (TIVA_FCFG1_BASE + TIVA_FCFG1_BAT_RC_LDO_TRIM_OFFSET)
#define TIVA_FCFG1_MAC_BLE_0                               (TIVA_FCFG1_BASE + TIVA_FCFG1_MAC_BLE_0_OFFSET)
#define TIVA_FCFG1_MAC_BLE_1                               (TIVA_FCFG1_BASE + TIVA_FCFG1_MAC_BLE_1_OFFSET)
#define TIVA_FCFG1_MAC_15_4_0                              (TIVA_FCFG1_BASE + TIVA_FCFG1_MAC_15_4_0_OFFSET)
#define TIVA_FCFG1_MAC_15_4_1                              (TIVA_FCFG1_BASE + TIVA_FCFG1_MAC_15_4_1_OFFSET)
#define TIVA_FCFG1_FLASH_OTP_DATA4                         (TIVA_FCFG1_BASE + TIVA_FCFG1_FLASH_OTP_DATA4_OFFSET)
#define TIVA_FCFG1_MISC_TRIM                               (TIVA_FCFG1_BASE + TIVA_FCFG1_MISC_TRIM_OFFSET)
#define TIVA_FCFG1_RCOSC_HF_TEMPCOMP                       (TIVA_FCFG1_BASE + TIVA_FCFG1_RCOSC_HF_TEMPCOMP_OFFSET)
#define TIVA_FCFG1_TRIM_CAL_REVISION                       (TIVA_FCFG1_BASE + TIVA_FCFG1_TRIM_CAL_REVISION_OFFSET)
#define TIVA_FCFG1_ICEPICK_DEVICE_ID                       (TIVA_FCFG1_BASE + TIVA_FCFG1_ICEPICK_DEVICE_ID_OFFSET)
#define TIVA_FCFG1_FCFG1_REVISION                          (TIVA_FCFG1_BASE + TIVA_FCFG1_FCFG1_REVISION_OFFSET)
#define TIVA_FCFG1_MISC_OTP_DATA                           (TIVA_FCFG1_BASE + TIVA_FCFG1_MISC_OTP_DATA_OFFSET)
#define TIVA_FCFG1_IOCONF                                  (TIVA_FCFG1_BASE + TIVA_FCFG1_IOCONF_OFFSET)
#define TIVA_FCFG1_CONFIG_IF_ADC                           (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_IF_ADC_OFFSET)
#define TIVA_FCFG1_CONFIG_OSC_TOP                          (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_OSC_TOP_OFFSET)
#define TIVA_FCFG1_CONFIG_RF_FRONTEND                      (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_RF_FRONTEND_OFFSET)
#define TIVA_FCFG1_CONFIG_SYNTH                            (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_SYNTH_OFFSET)
#define TIVA_FCFG1_SOC_ADC_ABS_GAIN                        (TIVA_FCFG1_BASE + TIVA_FCFG1_SOC_ADC_ABS_GAIN_OFFSET)
#define TIVA_FCFG1_SOC_ADC_REL_GAIN                        (TIVA_FCFG1_BASE + TIVA_FCFG1_SOC_ADC_REL_GAIN_OFFSET)
#define TIVA_FCFG1_SOC_ADC_OFFSET_INT                      (TIVA_FCFG1_BASE + TIVA_FCFG1_SOC_ADC_OFFSET_INT_OFFSET)
#define TIVA_FCFG1_SOC_ADC_REF_TRIM_AND_OFFSET_EXT         (TIVA_FCFG1_BASE + TIVA_FCFG1_SOC_ADC_REF_TRIM_AND_OFFSET_EXT_OFFSET)
#define TIVA_FCFG1_AMPCOMP_TH1                             (TIVA_FCFG1_BASE + TIVA_FCFG1_AMPCOMP_TH1_OFFSET)
#define TIVA_FCFG1_AMPCOMP_TH2                             (TIVA_FCFG1_BASE + TIVA_FCFG1_AMPCOMP_TH2_OFFSET)
#define TIVA_FCFG1_AMPCOMP_CTRL1                           (TIVA_FCFG1_BASE + TIVA_FCFG1_AMPCOMP_CTRL1_OFFSET)
#define TIVA_FCFG1_ANABYPASS_VALUE2                        (TIVA_FCFG1_BASE + TIVA_FCFG1_ANABYPASS_VALUE2_OFFSET)
#define TIVA_FCFG1_CONFIG_MISC_ADC                         (TIVA_FCFG1_BASE + TIVA_FCFG1_CONFIG_MISC_ADC_OFFSET)
#define TIVA_FCFG1_VOLT_TRIM                               (TIVA_FCFG1_BASE + TIVA_FCFG1_VOLT_TRIM_OFFSET)
#define TIVA_FCFG1_OSC_CONF                                (TIVA_FCFG1_BASE + TIVA_FCFG1_OSC_CONF_OFFSET)
#define TIVA_FCFG1_FREQ_OFFSET                             (TIVA_FCFG1_BASE + TIVA_FCFG1_FREQ_OFFSET_OFFSET)
#define TVIA_FCFG1_CAP_TRIM                                (TIVA_FCFG1_BASE + TVIA_FCFG1_CAP_TRIM_OFFSET)
#define TIVA_FCFG1_MISC_OTP_DATA_1                         (TIVA_FCFG1_BASE + TIVA_FCFG1_MISC_OTP_DATA_1_OFFSET)
#define TIVA_FCFG1_PWD_CURR_20C                            (TIVA_FCFG1_BASE + TIVA_FCFG1_PWD_CURR_20C_OFFSET)
#define TIVA_FCFG1_PWD_CURR_35C                            (TIVA_FCFG1_BASE + TIVA_FCFG1_PWD_CURR_35C_OFFSET)
#define TIVA_FCFG1_PWD_CURR_50C                            (TIVA_FCFG1_BASE + TIVA_FCFG1_PWD_CURR_50C_OFFSET)
#define TIVA_FCFG1_PWD_CURR_65C                            (TIVA_FCFG1_BASE + TIVA_FCFG1_PWD_CURR_65C_OFFSET)
#define TIVA_FCFG1_PWD_CURR_80C                            (TIVA_FCFG1_BASE + TIVA_FCFG1_PWD_CURR_80C_OFFSET)
#define TIVA_FCFG1_PWD_CURR_95C                            (TIVA_FCFG1_BASE + TIVA_FCFG1_PWD_CURR_95C_OFFSET)
#define TIVA_FCFG1_PWD_CURR_110C                           (TIVA_FCFG1_BASE + TIVA_FCFG1_PWD_CURR_110C_OFFSET)
#define TIVA_FCFG1_PWD_CURR_125C                           (TIVA_FCFG1_BASE + TIVA_FCFG1_PWD_CURR_125C_OFFSET)

/* FCFG1 Bitfield Definitions ***********************************************/

/* TIVA_FCFG1_MISC_CONF_1 */

#define FCFG1_MISC_CONF_1_DEVICE_MINOR_REV_SHIFT           (0)        /* Bits 0-7: HW minor revision number */
#define FCFG1_MISC_CONF_1_DEVICE_MINOR_REV_MASK            (0xff << FCFG1_MISC_CONF_1_DEVICE_MINOR_REV_SHIFT)
#  define FCFG1_MISC_CONF_1_DEVICE_MINOR_REV(n)            ((uint32_t)(n) << FCFG1_MISC_CONF_1_DEVICE_MINOR_REV_SHIFT)

/* TIVA_FCFG1_MISC_CONF_2 */

#define FCFG1_MISC_CONF_2_HPOSC_COMP_P3_SHIFT              (0)        /* Bits 0-7 */
#define FCFG1_MISC_CONF_2_HPOSC_COMP_P3_MASK               (0xff << FCFG1_MISC_CONF_2_HPOSC_COMP_P3_SHIFT)
#  define FCFG1_MISC_CONF_2_HPOSC_COMP_P3(n)               ((uint32_t)(n) << FCFG1_MISC_CONF_2_HPOSC_COMP_P3_SHIFT)

/* TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP */

#define FCFG1_CONFIG_RF_FRONTEND_DIV5_RFLDO_TRIM_OUTPUT_SHIFT  (0)        /* Bits 0-6 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV5_RFLDO_TRIM_OUTPUT_MASK   (0x7f << FCFG1_CONFIG_RF_FRONTEND_DIV5_RFLDO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV5_RFLDO_TRIM_OUTPUT(n)   ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV5_RFLDO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_CTL_PA0_TRIM_SHIFT (14)       /* Bits 14-18 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_CTL_PA0_TRIM_MASK  (31 << FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_CTL_PA0_TRIM_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_CTL_PA0_TRIM(n)  ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_CTL_PA0_TRIM_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_IFAMP_TRIM_SHIFT   (19)       /* Bits 19-23 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_IFAMP_TRIM_MASK    (31 << FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_IFAMP_TRIM_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_IFAMP_TRIM(n)    ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_IFAMP_TRIM_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_LNA_IB_SHIFT       (24)       /* Bits 24-27 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_LNA_IB_MASK        (15 << FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_LNA_IB_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_LNA_IB(n)        ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_LNA_IB_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_IFAMP_IB_SHIFT     (28)       /* Bits 28-31  */
#define FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_IFAMP_IB_MASK      (15 << FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_IFAMP_IB_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_IFAMP_IB(n)      ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV5_IFAMP_IFAMP_IB_SHIFT)

/* TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV6 */

#define FCFG1_CONFIG_RF_FRONTEND_DIV6_RFLDO_TRIM_OUTPUT_SHIFT  (0)      /* Bits 0-6 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV6_RFLDO_TRIM_OUTPUT_MASK   (0x7f << FCFG1_CONFIG_RF_FRONTEND_DIV6_RFLDO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV6_RFLDO_TRIM_OUTPUT(n)   ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV6_RFLDO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV6_CTL_PA0_TRIM_SHIFT       (14)      /* Bits 14-18 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV6_CTL_PA0_TRIM_MASK        (31 << FCFG1_CONFIG_RF_FRONTEND_DIV6_CTL_PA0_TRIM_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV6_CTL_PA0_TRIM(n)        ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV6_CTL_PA0_TRIM_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV6_IFAMP_TRIM_SHIFT         (19)      /* Bits 19-23 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV6_IFAMP_TRIM_MASK          (31 << FCFG1_CONFIG_RF_FRONTEND_DIV6_IFAMP_TRIM_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV6_IFAMP_TRIM(n)          ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV6_IFAMP_TRIM_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV6_LNA_IB_SHIFT             (24)      /* Bits 24-27 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV6_LNA_IB_MASK              (15 << FCFG1_CONFIG_RF_FRONTEND_DIV6_LNA_IB_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV6_LNA_IB(n)              ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV6_LNA_IB_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV6_IFAMP_IB_SHIFT           (28)      /* Bits 28-31 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV6_IFAMP_IB_MASK            (15 << FCFG1_CONFIG_RF_FRONTEND_DIV6_IFAMP_IB_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV6_IFAMP_IB(n)            ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV6_IFAMP_IB_SHIFT)

/* TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV10 */

#define FCFG1_CONFIG_RF_FRONTEND_DIV10_RFLDO_TRIM_OUTPUT_SHIFT (0)      /* Bits 0-6 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV10_RFLDO_TRIM_OUTPUT_MASK  (0x7f << FCFG1_CONFIG_RF_FRONTEND_DIV10_RFLDO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV10_RFLDO_TRIM_OUTPUT(n)  ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV10_RFLDO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV10_CTL_PA0_TRIM_SHIFT      (14)      /* Bits 14-18 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV10_CTL_PA0_TRIM_MASK       (31 << FCFG1_CONFIG_RF_FRONTEND_DIV10_CTL_PA0_TRIM_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV10_CTL_PA0_TRIM(n)       ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV10_CTL_PA0_TRIM_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV10_IFAMP_TRIM_SHIFT        (19)      /* Bits 19-23 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV10_IFAMP_TRIM_MASK         (31 << FCFG1_CONFIG_RF_FRONTEND_DIV10_IFAMP_TRIM_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV10_IFAMP_TRIM(n)         ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV10_IFAMP_TRIM_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV10_LNA_IB_SHIFT            (24)      /* Bits 24-27 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV10_LNA_IB_MASK             (15 << FCFG1_CONFIG_RF_FRONTEND_DIV10_LNA_IB_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV10_LNA_IB(n)             ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV10_LNA_IB_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV10_IFAMP_IB_SHIFT          (28)      /* Bits 28-31 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV10_IFAMP_IB_MASK           (15 << FCFG1_CONFIG_RF_FRONTEND_DIV10_IFAMP_IB_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV10_IFAMP_IB(n)           ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV10_IFAMP_IB_SHIFT)

/* TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV12 */

#define FCFG1_CONFIG_RF_FRONTEND_DIV12_RFLDO_TRIM_OUTPUT_SHIFT (0)      /* Bits 0-6 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV12_RFLDO_TRIM_OUTPUT_MASK  (0x7f << FCFG1_CONFIG_RF_FRONTEND_DIV12_RFLDO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV12_RFLDO_TRIM_OUTPUT(n)  ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV12_RFLDO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV12_CTL_PA0_TRIM_SHIFT      (14)      /* Bits 14-18 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV12_CTL_PA0_TRIM_MASK       (31 << FCFG1_CONFIG_RF_FRONTEND_DIV12_CTL_PA0_TRIM_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV12_CTL_PA0_TRIM(n)       ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV12_CTL_PA0_TRIM_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV12_IFAMP_TRIM_SHIFT        (19)      /* Bits 19-23 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV12_IFAMP_TRIM_MASK         (31 << FCFG1_CONFIG_RF_FRONTEND_DIV12_IFAMP_TRIM_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV12_IFAMP_TRIM(n)         ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV12_IFAMP_TRIM_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV12_LNA_IB_SHIFT            (24)      /* Bits 24-27 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV12_LNA_IB_MASK             (15 << FCFG1_CONFIG_RF_FRONTEND_DIV12_LNA_IB_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV12_LNA_IB(n)             ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV12_LNA_IB_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV12_IFAMP_IB_SHIFT          (28)      /* Bits 28-31 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV12_IFAMP_IB_MASK           (15 << FCFG1_CONFIG_RF_FRONTEND_DIV12_IFAMP_IB_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV12_IFAMP_IB(n)           ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV10_IFAMP_IB_SHIFT)

/* TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV15 */

#define FCFG1_CONFIG_RF_FRONTEND_DIV15_RFLDO_TRIM_OUTPUT_SHIFT (0)      /* Bits 0-6 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV15_RFLDO_TRIM_OUTPUT_MASK  (0x7f << FCFG1_CONFIG_RF_FRONTEND_DIV15_RFLDO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV15_RFLDO_TRIM_OUTPUT(n)  ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV15_RFLDO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV15_CTL_PA0_TRIM_SHIFT      (14)      /* Bits 14-18 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV15_CTL_PA0_TRIM_MASK       (31 << FCFG1_CONFIG_RF_FRONTEND_DIV15_CTL_PA0_TRIM_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV15_CTL_PA0_TRIM(n)       ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV15_CTL_PA0_TRIM_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV15_IFAMP_TRIM_SHIFT        (19)      /* Bits 19-23 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV15_IFAMP_TRIM_MASK         (31 << FCFG1_CONFIG_RF_FRONTEND_DIV15_IFAMP_TRIM_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV15_IFAMP_TRIM(n)         ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV15_IFAMP_TRIM_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV15_LNA_IB_SHIFT            (24)      /* Bits 24-27 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV15_LNA_IB_MASK             (15 << FCFG1_CONFIG_RF_FRONTEND_DIV15_LNA_IB_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV15_LNA_IB(n)             ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV15_LNA_IB_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV15_IFAMP_IB_SHIFT          (28)      /* Bits 28-31 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV15_IFAMP_IB_MASK           (15 << FCFG1_CONFIG_RF_FRONTEND_DIV15_IFAMP_IB_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV15_IFAMP_IB(n)           ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV10_IFAMP_IB_SHIFT)

/* TIVA_FCFG1_CONFIG_RF_FRONTEND_DIV30 */

#define FCFG1_CONFIG_RF_FRONTEND_DIV30_RFLDO_TRIM_OUTPUT_SHIFT (0)      /* Bits 0-6 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV30_RFLDO_TRIM_OUTPUT_MASK  (0x7f << FCFG1_CONFIG_RF_FRONTEND_DIV30_RFLDO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV30_RFLDO_TRIM_OUTPUT(n)  ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV30_RFLDO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV30_CTL_PA0_TRIM_SHIFT      (14)      /* Bits 14-18 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV30_CTL_PA0_TRIM_MASK       (31 << FCFG1_CONFIG_RF_FRONTEND_DIV30_CTL_PA0_TRIM_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV30_CTL_PA0_TRIM(n)       ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV30_CTL_PA0_TRIM_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV30_IFAMP_TRIM_SHIFT        (19)      /* Bits 19-23 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV30_IFAMP_TRIM_MASK         (31 << FCFG1_CONFIG_RF_FRONTEND_DIV30_IFAMP_TRIM_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV30_IFAMP_TRIM(n)         ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV30_IFAMP_TRIM_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV30_LNA_IB_SHIFT            (24)      /* Bits 24-27 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV30_LNA_IB_MASK             (15 << FCFG1_CONFIG_RF_FRONTEND_DIV30_LNA_IB_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV30_LNA_IB(n)             ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV30_LNA_IB_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_DIV30_IFAMP_IB_SHIFT          (28)      /* Bits 28-31 */
#define FCFG1_CONFIG_RF_FRONTEND_DIV30_IFAMP_IB_MASK           (15 << FCFG1_CONFIG_RF_FRONTEND_DIV30_IFAMP_IB_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_DIV30_IFAMP_IB(n)           ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_DIV10_IFAMP_IB_SHIFT)

/* TIVA_FCFG1_CONFIG_SYNTH_DIV5 */

#define FCFG1_CONFIG_SYNTH_DIV5_SLDO_TRIM_OUTPUT_SHIFT     (0)      /* Bits 0-5 */
#define FCFG1_CONFIG_SYNTH_DIV5_SLDO_TRIM_OUTPUT_MASK      (0x3f << FCFG1_CONFIG_SYNTH_DIV5_SLDO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_SYNTH_DIV5_SLDO_TRIM_OUTPUT(n)      ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_DIV5_SLDO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_SYNTH_DIV5_LDOVCO_TRIM_OUTPUT_SHIFT   (6)      /* Bits 6-11 */
#define FCFG1_CONFIG_SYNTH_DIV5_LDOVCO_TRIM_OUTPUT_MASK    (0x3f << FCFG1_CONFIG_SYNTH_DIV5_LDOVCO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_SYNTH_DIV5_LDOVCO_TRIM_OUTPUT(n)    ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_DIV5_LDOVCO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_SYNTH_DIV5_RFC_MDM_DEMIQMC0_SHIFT     (12)      /* Bits 12-27: Trim value for RF Core */
#define FCFG1_CONFIG_SYNTH_DIV5_RFC_MDM_DEMIQMC0_MASK      (0xffff << FCFG1_CONFIG_SYNTH_DIV5_RFC_MDM_DEMIQMC0_SHIFT)
#  define FCFG1_CONFIG_SYNTH_DIV5_RFC_MDM_DEMIQMC0(n)      ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_DIV5_RFC_MDM_DEMIQMC0_SHIFT)
#define FCFG1_CONFIG_SYNTH_DIV5_DISABLE_CORNER_CAP         (1 << 28) /* Bit 28 */

/* TIVA_FCFG1_CONFIG_SYNTH_DIV6 */

#define FCFG1_CONFIG_SYNTH_DIV6_SLDO_TRIM_OUTPUT_SHIFT     (0)      /* Bits 0-5 */
#define FCFG1_CONFIG_SYNTH_DIV6_SLDO_TRIM_OUTPUT_MASK      (0x3f << FCFG1_CONFIG_SYNTH_DIV6_SLDO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_SYNTH_DIV6_SLDO_TRIM_OUTPUT(n)      ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_DIV6_SLDO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_SYNTH_DIV6_LDOVCO_TRIM_OUTPUT_SHIFT   (6)      /* Bits 6-11 */
#define FCFG1_CONFIG_SYNTH_DIV6_LDOVCO_TRIM_OUTPUT_MASK    (0x3f << FCFG1_CONFIG_SYNTH_DIV6_LDOVCO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_SYNTH_DIV6_LDOVCO_TRIM_OUTPUT(n)    ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_DIV6_LDOVCO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_SYNTH_DIV6_RFC_MDM_DEMIQMC0_SHIFT     (12)      /* Bits 12-27: Trim value for RF Core */
#define FCFG1_CONFIG_SYNTH_DIV6_RFC_MDM_DEMIQMC0_MASK      (0xffff << FCFG1_CONFIG_SYNTH_DIV6_RFC_MDM_DEMIQMC0_SHIFT)
#  define FCFG1_CONFIG_SYNTH_DIV6_RFC_MDM_DEMIQMC0(n)      ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_DIV6_RFC_MDM_DEMIQMC0_SHIFT)
#define FCFG1_CONFIG_SYNTH_DIV6_DISABLE_CORNER_CAP         (1 << 28) /* Bit 28 */

/* TIVA_FCFG1_CONFIG_SYNTH_DIV10 */

#define FCFG1_CONFIG_SYNTH_DIV10_SLDO_TRIM_OUTPUT_SHIFT    (0)      /* Bits 0-5 */
#define FCFG1_CONFIG_SYNTH_DIV10_SLDO_TRIM_OUTPUT_MASK     (0x3f << FCFG1_CONFIG_SYNTH_DIV10_SLDO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_SYNTH_DIV10_SLDO_TRIM_OUTPUT(n)     ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_DIV10_SLDO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_SYNTH_DIV10_LDOVCO_TRIM_OUTPUT_SHIFT  (6)      /* Bits 6-11 */
#define FCFG1_CONFIG_SYNTH_DIV10_LDOVCO_TRIM_OUTPUT_MASK   (0x3f << FCFG1_CONFIG_SYNTH_DIV10_LDOVCO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_SYNTH_DIV10_LDOVCO_TRIM_OUTPUT(n)   ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_DIV10_LDOVCO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_SYNTH_DIV10_RFC_MDM_DEMIQMC0_SHIFT    (12)      /* Bits 12-27: Trim value for RF Core */
#define FCFG1_CONFIG_SYNTH_DIV10_RFC_MDM_DEMIQMC0_MASK     (0xffff << FCFG1_CONFIG_SYNTH_DIV10_RFC_MDM_DEMIQMC0_SHIFT)
#  define FCFG1_CONFIG_SYNTH_DIV10_RFC_MDM_DEMIQMC0(n)     ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_DIV10_RFC_MDM_DEMIQMC0_SHIFT)
#define FCFG1_CONFIG_SYNTH_DIV10_DISABLE_CORNER_CAP        (1 << 28) /* Bit 28 */

/*  FCFG1_CONFIG_SYNTH_DIV12 */

#define FCFG1_CONFIG_SYNTH_DIV12_SLDO_TRIM_OUTPUT_SHIFT    (0)      /* Bits 0-5 */
#define FCFG1_CONFIG_SYNTH_DIV12_SLDO_TRIM_OUTPUT_MASK     (0x3f << FCFG1_CONFIG_SYNTH_DIV12_SLDO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_SYNTH_DIV12_SLDO_TRIM_OUTPUT(n)     ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_DIV12_SLDO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_SYNTH_DIV12_LDOVCO_TRIM_OUTPUT_SHIFT  (6)      /* Bits 6-11 */
#define FCFG1_CONFIG_SYNTH_DIV12_LDOVCO_TRIM_OUTPUT_MASK   (0x3f << FCFG1_CONFIG_SYNTH_DIV12_LDOVCO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_SYNTH_DIV12_LDOVCO_TRIM_OUTPUT(n)   ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_DIV12_LDOVCO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_SYNTH_DIV12_RFC_MDM_DEMIQMC0_SHIFT    (12)      /* Bits 12-27: Trim value for RF Core */
#define FCFG1_CONFIG_SYNTH_DIV12_RFC_MDM_DEMIQMC0_MASK     (0xffff << FCFG1_CONFIG_SYNTH_DIV12_RFC_MDM_DEMIQMC0_SHIFT)
#  define FCFG1_CONFIG_SYNTH_DIV12_RFC_MDM_DEMIQMC0(n)     ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_DIV12_RFC_MDM_DEMIQMC0_SHIFT)
#define FCFG1_CONFIG_SYNTH_DIV12_DISABLE_CORNER_CAP        (1 << 28) /* Bit 28 */

/* TIVA_FCFG1_CONFIG_SYNTH_DIV15 */

#define FCFG1_CONFIG_SYNTH_DIV15_SLDO_TRIM_OUTPUT_SHIFT    (0)      /* Bits 0-5 */
#define FCFG1_CONFIG_SYNTH_DIV15_SLDO_TRIM_OUTPUT_MASK     (0x3f << FCFG1_CONFIG_SYNTH_DIV15_SLDO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_SYNTH_DIV15_SLDO_TRIM_OUTPUT(n)     ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_DIV15_SLDO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_SYNTH_DIV15_LDOVCO_TRIM_OUTPUT_SHIFT  (6)      /* Bits 6-11 */
#define FCFG1_CONFIG_SYNTH_DIV15_LDOVCO_TRIM_OUTPUT_MASK   (0x3f << FCFG1_CONFIG_SYNTH_DIV15_LDOVCO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_SYNTH_DIV15_LDOVCO_TRIM_OUTPUT(n)   ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_DIV15_LDOVCO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_SYNTH_DIV15_RFC_MDM_DEMIQMC0_SHIFT    (12)      /* Bits 12-27: Trim value for RF Core */
#define FCFG1_CONFIG_SYNTH_DIV15_RFC_MDM_DEMIQMC0_MASK     (0xffff << FCFG1_CONFIG_SYNTH_DIV15_RFC_MDM_DEMIQMC0_SHIFT)
#  define FCFG1_CONFIG_SYNTH_DIV15_RFC_MDM_DEMIQMC0(n)     ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_DIV15_RFC_MDM_DEMIQMC0_SHIFT)
#define FCFG1_CONFIG_SYNTH_DIV15_DISABLE_CORNER_CAP        (1 << 28) /* Bit 28 */

/* TIVA_FCFG1_CONFIG_SYNTH_DIV30 */

#define FCFG1_CONFIG_SYNTH_DIV30_SLDO_TRIM_OUTPUT_SHIFT    (0)      /* Bits 0-5 */
#define FCFG1_CONFIG_SYNTH_DIV30_SLDO_TRIM_OUTPUT_MASK     (0x3f << FCFG1_CONFIG_SYNTH_DIV30_SLDO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_SYNTH_DIV30_SLDO_TRIM_OUTPUT(n)     ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_DIV30_SLDO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_SYNTH_DIV30_LDOVCO_TRIM_OUTPUT_SHIFT  (6)      /* Bits 6-11 */
#define FCFG1_CONFIG_SYNTH_DIV30_LDOVCO_TRIM_OUTPUT_MASK   (0x3f << FCFG1_CONFIG_SYNTH_DIV30_LDOVCO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_SYNTH_DIV30_LDOVCO_TRIM_OUTPUT(n)   ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_DIV30_LDOVCO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_SYNTH_DIV30_RFC_MDM_DEMIQMC0_SHIFT    (12)      /* Bits 12-27: Trim value for RF Core */
#define FCFG1_CONFIG_SYNTH_DIV30_RFC_MDM_DEMIQMC0_MASK     (0xffff << FCFG1_CONFIG_SYNTH_DIV30_RFC_MDM_DEMIQMC0_SHIFT)
#  define FCFG1_CONFIG_SYNTH_DIV30_RFC_MDM_DEMIQMC0(n)     ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_DIV30_RFC_MDM_DEMIQMC0_SHIFT)
#define FCFG1_CONFIG_SYNTH_DIV30_DISABLE_CORNER_CAP        (1 << 28) /* Bit 28 */

/* TIVA_FCFG1_CONFIG_MISC_ADC_DIV5 */

#define FCFG1_CONFIG_MISC_ADC_DIV5_DACTRIM_SHIFT            (0)       /* Bits 0-5 */
#define FCFG1_CONFIG_MISC_ADC_DIV5_DACTRIM_MASK             (0x3f << FCFG1_CONFIG_MISC_ADC_DIV5_DACTRIM_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV5_DACTRIM(n)             ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV5_DACTRIM_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_DIV5_QUANTCTLTHRES_SHIFT      (6)       /* Bits 6-8 */
#define FCFG1_CONFIG_MISC_ADC_DIV5_QUANTCTLTHRES_MASK       (7 << FCFG1_CONFIG_MISC_ADC_DIV5_QUANTCTLTHRES_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV5_QUANTCTLTHRES(n)       ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV5_QUANTCTLTHRES_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_DIV5_RSSI_OFFSET_SHIFT        (9)      /* Bits 9-16 */
#define FCFG1_CONFIG_MISC_ADC_DIV5_RSSI_OFFSET_MASK         (0xff << FCFG1_CONFIG_MISC_ADC_DIV5_RSSI_OFFSET_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV5_RSSI_OFFSET(n)         ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV5_RSSI_OFFSET_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_DIV5_MIN_ALLOWED_RTRIM_SHIFT  (18)      /* Bits 18-21 */
#define FCFG1_CONFIG_MISC_ADC_DIV5_MIN_ALLOWED_RTRIM_MASK   (15 << FCFG1_CONFIG_MISC_ADC_DIV5_MIN_ALLOWED_RTRIM_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV5_MIN_ALLOWED_RTRIM(n)    ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV5_MIN_ALLOWED_RTRIM_SHIFT)

/* TIVA_FCFG1_CONFIG_MISC_ADC_DIV6 */

#define FCFG1_CONFIG_MISC_ADC_DIV6_DACTRIM_SHIFT            (0)       /* Bits 0-5 */
#define FCFG1_CONFIG_MISC_ADC_DIV6_DACTRIM_MASK             (0x3f << FCFG1_CONFIG_MISC_ADC_DIV6_DACTRIM_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV6_DACTRIM(n)             ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV6_DACTRIM_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_DIV6_QUANTCTLTHRES_SHIFT      (6)       /* Bits 6-8 */
#define FCFG1_CONFIG_MISC_ADC_DIV6_QUANTCTLTHRES_MASK       (7 << FCFG1_CONFIG_MISC_ADC_DIV6_QUANTCTLTHRES_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV6_QUANTCTLTHRES(n)       ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV6_QUANTCTLTHRES_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_DIV6_RSSI_OFFSET_SHIFT        (9)      /* Bits 9-16 */
#define FCFG1_CONFIG_MISC_ADC_DIV6_RSSI_OFFSET_MASK         (0xff << FCFG1_CONFIG_MISC_ADC_DIV6_RSSI_OFFSET_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV6_RSSI_OFFSET(n)         ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV6_RSSI_OFFSET_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_DIV6_MIN_ALLOWED_RTRIM_SHIFT  (18)      /* Bits 18-21 */
#define FCFG1_CONFIG_MISC_ADC_DIV6_MIN_ALLOWED_RTRIM_MASK   (15 << FCFG1_CONFIG_MISC_ADC_DIV6_MIN_ALLOWED_RTRIM_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV6_MIN_ALLOWED_RTRIM(n)    ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV6_MIN_ALLOWED_RTRIM_SHIFT)

/* TIVA_FCFG1_CONFIG_MISC_ADC_DIV10 */

#define FCFG1_CONFIG_MISC_ADC_DIV10_DACTRIM_SHIFT           (0)       /* Bits 0-5 */
#define FCFG1_CONFIG_MISC_ADC_DIV10_DACTRIM_MASK            (0x3f << FCFG1_CONFIG_MISC_ADC_DIV10_DACTRIM_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV10_DACTRIM(n)            ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV10_DACTRIM_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_DIV10_QUANTCTLTHRES_SHIFT     (6)       /* Bits 6-8 */
#define FCFG1_CONFIG_MISC_ADC_DIV10_QUANTCTLTHRES_MASK      (7 << FCFG1_CONFIG_MISC_ADC_DIV10_QUANTCTLTHRES_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV10_QUANTCTLTHRES(n)      ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV10_QUANTCTLTHRES_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_DIV10_RSSI_OFFSET_SHIFT       (9)      /* Bits 9-16 */
#define FCFG1_CONFIG_MISC_ADC_DIV10_RSSI_OFFSET_MASK        (0xff << FCFG1_CONFIG_MISC_ADC_DIV10_RSSI_OFFSET_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV10_RSSI_OFFSET(n)        ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV10_RSSI_OFFSET_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_DIV10_MIN_ALLOWED_RTRIM_SHIFT (18)      /* Bits 18-21 */
#define FCFG1_CONFIG_MISC_ADC_DIV10_MIN_ALLOWED_RTRIM_MASK  (15 << FCFG1_CONFIG_MISC_ADC_DIV10_MIN_ALLOWED_RTRIM_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV10_MIN_ALLOWED_RTRIM(n)   ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV10_MIN_ALLOWED_RTRIM_SHIFT)

/* TIVA_FCFG1_CONFIG_MISC_ADC_DIV12 */

#define FCFG1_CONFIG_MISC_ADC_DIV12_DACTRIM_SHIFT           (0)       /* Bits 0-5 */
#define FCFG1_CONFIG_MISC_ADC_DIV12_DACTRIM_MASK            (0x3f << FCFG1_CONFIG_MISC_ADC_DIV12_DACTRIM_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV12_DACTRIM(n)            ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV12_DACTRIM_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_DIV12_QUANTCTLTHRES_SHIFT     (6)       /* Bits 6-8 */
#define FCFG1_CONFIG_MISC_ADC_DIV12_QUANTCTLTHRES_MASK      (7 << FCFG1_CONFIG_MISC_ADC_DIV12_QUANTCTLTHRES_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV12_QUANTCTLTHRES(n)      ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV12_QUANTCTLTHRES_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_DIV12_RSSI_OFFSET_SHIFT       (9)      /* Bits 9-16 */
#define FCFG1_CONFIG_MISC_ADC_DIV12_RSSI_OFFSET_MASK        (0xff << FCFG1_CONFIG_MISC_ADC_DIV12_RSSI_OFFSET_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV12_RSSI_OFFSET(n)        ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV12_RSSI_OFFSET_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_DIV12_MIN_ALLOWED_RTRIM_SHIFT (18)      /* Bits 18-21 */
#define FCFG1_CONFIG_MISC_ADC_DIV12_MIN_ALLOWED_RTRIM_MASK  (15 << FCFG1_CONFIG_MISC_ADC_DIV12_MIN_ALLOWED_RTRIM_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV12_MIN_ALLOWED_RTRIM(n)   ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV12_MIN_ALLOWED_RTRIM_SHIFT)

/* TIVA_FCFG1_CONFIG_MISC_ADC_DIV15 */

#define FCFG1_CONFIG_MISC_ADC_DIV15_DACTRIM_SHIFT           (0)       /* Bits 0-5 */
#define FCFG1_CONFIG_MISC_ADC_DIV15_DACTRIM_MASK            (0x3f << FCFG1_CONFIG_MISC_ADC_DIV15_DACTRIM_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV15_DACTRIM(n)            ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV15_DACTRIM_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_DIV15_QUANTCTLTHRES_SHIFT     (6)       /* Bits 6-8 */
#define FCFG1_CONFIG_MISC_ADC_DIV15_QUANTCTLTHRES_MASK      (7 << FCFG1_CONFIG_MISC_ADC_DIV15_QUANTCTLTHRES_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV15_QUANTCTLTHRES(n)      ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV15_QUANTCTLTHRES_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_DIV15_RSSI_OFFSET_SHIFT       (9)      /* Bits 9-16 */
#define FCFG1_CONFIG_MISC_ADC_DIV15_RSSI_OFFSET_MASK        (0xff << FCFG1_CONFIG_MISC_ADC_DIV15_RSSI_OFFSET_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV15_RSSI_OFFSET(n)        ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV15_RSSI_OFFSET_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_DIV15_MIN_ALLOWED_RTRIM_SHIFT (18)      /* Bits 18-21 */
#define FCFG1_CONFIG_MISC_ADC_DIV15_MIN_ALLOWED_RTRIM_MASK  (15 << FCFG1_CONFIG_MISC_ADC_DIV15_MIN_ALLOWED_RTRIM_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV15_MIN_ALLOWED_RTRIM(n)   ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV15_MIN_ALLOWED_RTRIM_SHIFT)

/* TIVA_FCFG1_CONFIG_MISC_ADC_DIV30 */

#define FCFG1_CONFIG_MISC_ADC_DIV30_DACTRIM_SHIFT           (0)       /* Bits 0-5 */
#define FCFG1_CONFIG_MISC_ADC_DIV30_DACTRIM_MASK            (0x3f << FCFG1_CONFIG_MISC_ADC_DIV30_DACTRIM_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV30_DACTRIM(n)            ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV30_DACTRIM_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_DIV30_QUANTCTLTHRES_SHIFT     (6)       /* Bits 6-8 */
#define FCFG1_CONFIG_MISC_ADC_DIV30_QUANTCTLTHRES_MASK      (7 << FCFG1_CONFIG_MISC_ADC_DIV30_QUANTCTLTHRES_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV30_QUANTCTLTHRES(n)      ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV30_QUANTCTLTHRES_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_DIV30_RSSI_OFFSET_SHIFT       (9)      /* Bits 9-16 */
#define FCFG1_CONFIG_MISC_ADC_DIV30_RSSI_OFFSET_MASK        (0xff << FCFG1_CONFIG_MISC_ADC_DIV30_RSSI_OFFSET_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV30_RSSI_OFFSET(n)        ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV30_RSSI_OFFSET_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_DIV30_MIN_ALLOWED_RTRIM_SHIFT (18)      /* Bits 18-21 */
#define FCFG1_CONFIG_MISC_ADC_DIV30_MIN_ALLOWED_RTRIM_MASK  (15 << FCFG1_CONFIG_MISC_ADC_DIV30_MIN_ALLOWED_RTRIM_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DIV30_MIN_ALLOWED_RTRIM(n)   ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DIV30_MIN_ALLOWED_RTRIM_SHIFT)

/* TIVA_FCFG1_SHDW_DIE_ID_0 (32-bit value,
 * Shadow of the DIE_ID_0 register in eFuse row number 3)
 */

/* TIVA_FCFG1_SHDW_DIE_ID_1 (32-bit value,
 * Shadow of the DIE_ID_1 register in eFuse row number 4)
 */

/* TIVA_FCFG1_SHDW_DIE_ID_2 (32-bit value,
 * Shadow of the DIE_ID_2 register in eFuse row number 5)
 */

/* TIVA_FCFG1_SHDW_DIE_ID_3 (32-bit value,
 * Shadow of the DIE_ID_3 register in eFuse row number 6)
 */

/* TIVA_FCFG1_SHDW_OSC_BIAS_LDO_TRIM */

#define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_RCOSCHF_CTRIM_SHIFT   (0)      /* Bits 0-7 */
#define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_RCOSCHF_CTRIM_MASK    (0xff << FCFG1_SHDW_OSC_BIAS_LDO_TRIM_RCOSCHF_CTRIM_SHIFT)
#  define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_RCOSCHF_CTRIM(n)    ((uint32_t)(n) << FCFG1_SHDW_OSC_BIAS_LDO_TRIM_RCOSCHF_CTRIM_SHIFT)
#define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_VTRIM_COARSE_SHIFT    (8)      /* Bits 8-11 */
#define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_VTRIM_COARSE_MASK     (15 << FCFG1_SHDW_OSC_BIAS_LDO_TRIM_VTRIM_COARSE_SHIFT)
#  define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_VTRIM_COARSE(n)     ((uint32_t)(n) << FCFG1_SHDW_OSC_BIAS_LDO_TRIM_VTRIM_COARSE_SHIFT)
#define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_VTRIM_DIG_SHIFT       (12)      /* Bits 12-15 */
#define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_VTRIM_DIG_MASK        (15 << FCFG1_SHDW_OSC_BIAS_LDO_TRIM_VTRIM_DIG_SHIFT)
#  define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_VTRIM_DIG(n)        ((uint32_t)(n) << FCFG1_SHDW_OSC_BIAS_LDO_TRIM_VTRIM_DIG_SHIFT)
#define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_ITRIM_DIG_LDO_SHIFT   (16)      /* Bits 16-17 */
#define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_ITRIM_DIG_LDO_MASK    (3 << FCFG1_SHDW_OSC_BIAS_LDO_TRIM_ITRIM_DIG_LDO_SHIFT)
#  define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_ITRIM_DIG_LDO(n)    ((uint32_t)(n) << FCFG1_SHDW_OSC_BIAS_LDO_TRIM_ITRIM_DIG_LDO_SHIFT)
#define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_TRIMIREF_SHIFT        (18)      /* Bits 18-22 */
#define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_TRIMIREF_MASK         (31 << FCFG1_SHDW_OSC_BIAS_LDO_TRIM_TRIMIREF_SHIFT)
#  define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_TRIMIREF(n)         ((uint32_t)(n) << FCFG1_SHDW_OSC_BIAS_LDO_TRIM_TRIMIREF_SHIFT)
#define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_TRIMMAG_SHIFT         (23)      /* Bits 23-26 */
#define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_TRIMMAG_MASK          (15 << FCFG1_SHDW_OSC_BIAS_LDO_TRIM_TRIMMAG_SHIFT)
#  define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_TRIMMAG(n)          ((uint32_t)(n) << FCFG1_SHDW_OSC_BIAS_LDO_TRIM_TRIMMAG_SHIFT)
#define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_SET_RCOSC_HF_COARSE_RESISTOR_SHIFT (27)  /* Bits 27-28 */
#define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_SET_RCOSC_HF_COARSE_RESISTOR_MASK \
  (3 << FCFG1_SHDW_OSC_BIAS_LDO_TRIM_SET_RCOSC_HF_COARSE_RESISTOR_SHIFT)
#  define FCFG1_SHDW_OSC_BIAS_LDO_TRIM_SET_RCOSC_HF_COARSE_RESISTOR(n) \
  ((uint32_t)(n) << FCFG1_SHDW_OSC_BIAS_LDO_TRIM_SET_RCOSC_HF_COARSE_RESISTOR_SHIFT)

/* TIVA_FCFG1_SHDW_ANA_TRIM */

#define FCFG1_SHDW_ANA_TRIM_TRIMTEMP_SHIFT                 (18)      /* Bits 0-5 */
#define FCFG1_SHDW_ANA_TRIM_TRIMTEMP_MASK                  (0x3f << FCFG1_SHDW_ANA_TRIM_TRIMTEMP_SHIFT)
#  define FCFG1_SHDW_ANA_TRIM_TRIMTEMP(n)                  ((uint32_t)(n) << FCFG1_SHDW_ANA_TRIM_TRIMTEMP_SHIFT)
#define FCFG1_SHDW_ANA_TRIM_TRIMBOD_EXTMODE_SHIFT          (6)      /* Bits 6-10 */
#define FCFG1_SHDW_ANA_TRIM_TRIMBOD_EXTMODE_MASK           (31 << FCFG1_SHDW_ANA_TRIM_TRIMBOD_EXTMODE_SHIFT)
#  define FCFG1_SHDW_ANA_TRIM_TRIMBOD_EXTMODE(n)           ((uint32_t)(n) << FCFG1_SHDW_ANA_TRIM_TRIMBOD_EXTMODE_SHIFT)
#define FCFG1_SHDW_ANA_TRIM_TRIMBOD_INTMODE_SHIFT          (11)      /* Bits 11-15 */
#define FCFG1_SHDW_ANA_TRIM_TRIMBOD_INTMODE_MASK           (31 << FCFG1_SHDW_ANA_TRIM_TRIMBOD_INTMODE_SHIFT)
#  define FCFG1_SHDW_ANA_TRIM_TRIMBOD_INTMODE(n)           ((uint32_t)(n) << FCFG1_SHDW_ANA_TRIM_TRIMBOD_INTMODE_SHIFT)
#define FCFG1_SHDW_ANA_TRIM_VDDR_TRIM_SHIFT                (16)      /* Bits 16-20 */
#define FCFG1_SHDW_ANA_TRIM_VDDR_TRIM_MASK                 (31 << FCFG1_SHDW_ANA_TRIM_VDDR_TRIM_SHIFT)
#  define FCFG1_SHDW_ANA_TRIM_VDDR_TRIM(n)                 ((uint32_t)(n) << FCFG1_SHDW_ANA_TRIM_VDDR_TRIM_SHIFT)
#define FCFG1_SHDW_ANA_TRIM_IPTAT_TRIM_SHIFT               (21)      /* Bits 21-22 */
#define FCFG1_SHDW_ANA_TRIM_IPTAT_TRIM_MASK                (2 << FCFG1_SHDW_ANA_TRIM_IPTAT_TRIM_SHIFT)
#  define FCFG1_SHDW_ANA_TRIM_IPTAT_TRIM(n)                ((uint32_t)(n) << FCFG1_SHDW_ANA_TRIM_IPTAT_TRIM_SHIFT)
#define FCFG1_SHDW_ANA_TRIM_VDDR_OK_HYS                    (1 << 23) /* Bit 23 */
#define FCFG1_SHDW_ANA_TRIM_VDDR_ENABLE_PG1                (1 << 24) /* Bit 24 */
#define FCFG1_SHDW_ANA_TRIM_BOD_BANDGAP_TRIM_CNF_SHIFT     (25)      /* Bits 25-26 */
#define FCFG1_SHDW_ANA_TRIM_BOD_BANDGAP_TRIM_CNF_MASK      (3 << FCFG1_SHDW_ANA_TRIM_BOD_BANDGAP_TRIM_CNF_SHIFT)
#  define FCFG1_SHDW_ANA_TRIM_BOD_BANDGAP_TRIM_CNF(n)      ((uint32_t)(n) << FCFG1_SHDW_ANA_TRIM_BOD_BANDGAP_TRIM_CNF_SHIFT)

/* TIVA_FCFG1_FLASH_NUMBER */

/* TIVA_FCFG1_FLASH_COORDINATE */

#define FCFG1_FLASH_COORDINATE_YCOORDINATE_SHIFT           (0)       /* Bits 0-15: Y coordinate of this unit on the wafer */
#define FCFG1_FLASH_COORDINATE_YCOORDINATE_MASK            (0xffff << FCFG1_FLASH_COORDINATE_YCOORDINATE_SHIFT)
#  define FCFG1_FLASH_COORDINATE_YCOORDINATE(n)            ((uint32_t)(n) << FCFG1_FLASH_COORDINATE_YCOORDINATE_SHIFT)
#define FCFG1_FLASH_COORDINATE_XCOORDINATE_SHIFT           (16)      /* Bits 16-31:  X coordinate of this unit on the wafer */
#define FCFG1_FLASH_COORDINATE_XCOORDINATE_MASK            (0xffff << FCFG1_FLASH_COORDINATE_XCOORDINATE_SHIFT)
#  define FCFG1_FLASH_COORDINATE_XCOORDINATE(n)            ((uint32_t)(n) << FCFG1_FLASH_COORDINATE_XCOORDINATE_SHIFT)

/* TIVA_FCFG1_FLASH_E_P */

#define FCFG1_FLASH_E_P_EVSU_SHIFT                         (0)       /* Bits 0-7 */
#define FCFG1_FLASH_E_P_EVSU_MASK                          (0xff << FCFG1_FLASH_E_P_EVSU_SHIFT)
#  define FCFG1_FLASH_E_P_EVSU(n)                          ((uint32_t)(n) << FCFG1_FLASH_E_P_EVSU_SHIFT)
#define FCFG1_FLASH_E_P_PVSU_SHIFT                         (8)       /* Bits 8-15 */
#define FCFG1_FLASH_E_P_ESU_SHIFT                          (16)      /* Bits 16-23 */
#define FCFG1_FLASH_E_P_ESU_MASK                           (0xff << FCFG1_FLASH_E_P_ESU_SHIFT)
#  define FCFG1_FLASH_E_P_ESU(n)                           ((uint32_t)(n) << FCFG1_FLASH_E_P_ESU_SHIFT)
#define FCFG1_FLASH_E_P_PVSU_MASK                          (0xff << FCFG1_FLASH_E_P_PVSU_SHIFT)
#  define FCFG1_FLASH_E_P_PVSU(n)                          ((uint32_t)(n) << FCFG1_FLASH_E_P_PVSU_SHIFT)
#define FCFG1_FLASH_E_P_PSU_SHIFT                          (24)      /* Bits 24-31 */
#define FCFG1_FLASH_E_P_PSU_MASK                           (0xff << FCFG1_FLASH_E_P_PSU_SHIFT)
#  define FCFG1_FLASH_E_P_PSU(n)                           ((uint32_t)(n) << FCFG1_FLASH_E_P_PSU_SHIFT)

/* TIVA_FCFG1_FLASH_C_E_P_R */

#define FCFG1_FLASH_C_E_P_R_CVSU_SHIFT                     (0)       /* Bits 0-11 */
#define FCFG1_FLASH_C_E_P_R_CVSU_MASK                      (0xfff << FCFG1_FLASH_C_E_P_R_CVSU_SHIFT)
#  define FCFG1_FLASH_C_E_P_R_CVSU(n)                      ((uint32_t)(n) << FCFG1_FLASH_C_E_P_R_CVSU_SHIFT)
#define FCFG1_FLASH_C_E_P_R_A_EXEZ_SETUP_SHIFT             (12)      /* Bits 12-15 */
#define FCFG1_FLASH_C_E_P_R_A_EXEZ_SETUP_MASK              (15 << FCFG1_FLASH_C_E_P_R_A_EXEZ_SETUP_SHIFT)
#  define FCFG1_FLASH_C_E_P_R_A_EXEZ_SETUP(n)              ((uint32_t)(n) << FCFG1_FLASH_C_E_P_R_A_EXEZ_SETUP_SHIFT)
#define FCFG1_FLASH_C_E_P_R_PV_ACCESS_SHIFT                (16)      /* Bits 16-23 */
#define FCFG1_FLASH_C_E_P_R_PV_ACCESS_MASK                 (0xff << FCFG1_FLASH_C_E_P_R_PV_ACCESS_SHIFT)
#  define FCFG1_FLASH_C_E_P_R_PV_ACCESS(n)                 ((uint32_t)(n) << FCFG1_FLASH_C_E_P_R_PV_ACCESS_SHIFT)
#define FCFG1_FLASH_C_E_P_R_RVSU_SHIFT                     (24)      /* Bits 24-31 */
#define FCFG1_FLASH_C_E_P_R_RVSU_MASK                      (0xff << FCFG1_FLASH_C_E_P_R_RVSU_SHIFT)
#  define FCFG1_FLASH_C_E_P_R_RVSU(n)                      ((uint32_t)(n) << FCFG1_FLASH_C_E_P_R_RVSU_SHIFT)

/* TIVA_FCFG1_FLASH_P_R_PV */

#define FCFG1_FLASH_P_R_PV_PVH2_SHIFT                      (0)      /* Bits 0-7 */
#define FCFG1_FLASH_P_R_PV_PVH2_MASK                       (0xff << FCFG1_FLASH_P_R_PV_PVH2_SHIFT)
#  define FCFG1_FLASH_P_R_PV_PVH2(n)                       ((uint32_t)(n) << FCFG1_FLASH_P_R_PV_PVH2_SHIFT)
#define FCFG1_FLASH_P_R_PV_PVH_SHIFT                       (8)      /* Bits 8-15 */
#define FCFG1_FLASH_P_R_PV_PVH_MASK                        (0xff << FCFG1_FLASH_P_R_PV_PVH_SHIFT)
#  define FCFG1_FLASH_P_R_PV_PVH(n)                        ((uint32_t)(n) << FCFG1_FLASH_P_R_PV_PVH_SHIFT)
#define FCFG1_FLASH_P_R_PV_RH_SHIFT                        (16)      /* Bits 16-23 */
#define FCFG1_FLASH_P_R_PV_RH_MASK                         (0xff << FCFG1_FLASH_P_R_PV_RH_SHIFT)
#  define FCFG1_FLASH_P_R_PV_RH(n)                         ((uint32_t)(n) << FCFG1_FLASH_P_R_PV_RH_SHIFT)
#define FCFG1_FLASH_P_R_PV_PH_SHIFT                        (24)      /* Bits 24-31 */
#define FCFG1_FLASH_P_R_PV_PH_MASK                         (0xff << FCFG1_FLASH_P_R_PV_PH_SHIFT)
#  define FCFG1_FLASH_P_R_PV_PH(n)                         ((uint32_t)(n) << FCFG1_FLASH_P_R_PV_PH_SHIFT)

/* TIVA_FCFG1_FLASH_EH_SEQ */

#define FCFG1_FLASH_EH_SEQ_SM_FREQUENCY_SHIFT              (0)       /* Bits 0-11 */
#define FCFG1_FLASH_EH_SEQ_SM_FREQUENCY_MASK               (0xfff << FCFG1_FLASH_EH_SEQ_SM_FREQUENCY_SHIFT)
#  define FCFG1_FLASH_EH_SEQ_SM_FREQUENCY(n)               ((uint32_t)(n) << FCFG1_FLASH_EH_SEQ_SM_FREQUENCY_SHIFT)
#define FCFG1_FLASH_EH_SEQ_VSTAT_SHIFT                     (12)      /* Bits 12-15 */
#define FCFG1_FLASH_EH_SEQ_VSTAT_MASK                      (15 << FCFG1_FLASH_EH_SEQ_VSTAT_SHIFT)
#  define FCFG1_FLASH_EH_SEQ_VSTAT(n)                      ((uint32_t)(n) << FCFG1_FLASH_EH_SEQ_VSTAT_SHIFT)
#define FCFG1_FLASH_EH_SEQ_SEQ_SHIFT                       (16)      /* Bits 16-23 */
#define FCFG1_FLASH_EH_SEQ_SEQ_MASK                        (0xff << FCFG1_FLASH_EH_SEQ_SEQ_SHIFT)
#  define FCFG1_FLASH_EH_SEQ_SEQ(n)                        ((uint32_t)(n) << FCFG1_FLASH_EH_SEQ_SEQ_SHIFT)
#define FCFG1_FLASH_EH_SEQ_EH_SHIFT                        (24)      /* Bits 24-31 */
#define FCFG1_FLASH_EH_SEQ_EH_MASK                         (0xff << FCFG1_FLASH_EH_SEQ_EH_SHIFT)
#  define FCFG1_FLASH_EH_SEQ_EH(n)                         ((uint32_t)(n) << FCFG1_FLASH_EH_SEQ_EH_SHIFT)

/* TIVA_FCFG1_FLASH_VHV_E */

#define FCFG1_FLASH_VHV_E_VHV_E_STEP_HIGHT_SHIFT           (0)       /* Bits 0-15 */
#define FCFG1_FLASH_VHV_E_VHV_E_STEP_HIGHT_MASK            (0xffff << FCFG1_FLASH_VHV_E_VHV_E_STEP_HIGHT_SHIFT)
#  define FCFG1_FLASH_VHV_E_VHV_E_STEP_HIGHT(n)            ((uint32_t)(n) << FCFG1_FLASH_VHV_E_VHV_E_STEP_HIGHT_SHIFT)
#define FCFG1_FLASH_VHV_E_VHV_E_START_SHIFT                (16)      /* Bits 16-31 */
#define FCFG1_FLASH_VHV_E_VHV_E_START_MASK                 (0xffff << FCFG1_FLASH_VHV_E_VHV_E_START_SHIFT)
#  define FCFG1_FLASH_VHV_E_VHV_E_START(n)                 ((uint32_t)(n) << FCFG1_FLASH_VHV_E_VHV_E_START_SHIFT)

/* TIVA_FCFG1_FLASH_PP */

#define FCFG1_FLASH_PP_MAX_PP_SHIFT                        (0)       /* Bits 0-15 */
#define FCFG1_FLASH_PP_MAX_PP_MASK                         (0xffff << FCFG1_FLASH_PP_MAX_PP_SHIFT)
#  define FCFG1_FLASH_PP_MAX_PP(n)                         ((uint32_t)(n) << FCFG1_FLASH_PP_MAX_PP_SHIFT)
#define FCFG1_FLASH_PP_PUMP_SU_SHIFT                       (24)      /* Bits 24-31 */
#define FCFG1_FLASH_PP_PUMP_SU_MASK                        (0xff << FCFG1_FLASH_PP_PUMP_SU_SHIFT)
#  define FCFG1_FLASH_PP_PUMP_SU(n)                        ((uint32_t)(n) << FCFG1_FLASH_PP_PUMP_SU_SHIFT)

/* TIVA_FCFG1_FLASH_PROG_EP */

#define FCFG1_FLASH_PROG_EP_PROGRAM_PW_SHIFT               (0)       /* Bits 0-15 */
#define FCFG1_FLASH_PROG_EP_PROGRAM_PW_MASK                (0xffff << FCFG1_FLASH_PROG_EP_PROGRAM_PW_SHIFT)
#  define FCFG1_FLASH_PROG_EP_PROGRAM_PW(n)                ((uint32_t)(n) << FCFG1_FLASH_PROG_EP_PROGRAM_PW_SHIFT)
#define FCFG1_FLASH_PROG_EP_MAX_EP_SHIFT                   (16)      /* Bits 16-31 */
#define FCFG1_FLASH_PROG_EP_MAX_EP_MASK                    (0xffff << FCFG1_FLASH_PROG_EP_MAX_EP_SHIFT)
#  define FCFG1_FLASH_PROG_EP_MAX_EP(n)                    ((uint32_t)(n) << FCFG1_FLASH_PROG_EP_MAX_EP_SHIFT)

/* TIVA_FCFG1_FLASH_ERA_PW (32-bit value) */

/* TIVA_FCFG1_FLASH_VHV */

#define FCFG1_FLASH_VHV_VHV_E_SHIFT                        (0)       /* Bits 0-3 */
#define FCFG1_FLASH_VHV_VHV_E_MASK                         (15 << FCFG1_FLASH_VHV_VHV_E_SHIFT)
#  define FCFG1_FLASH_VHV_VHV_E(n)                         ((uint32_t)(n) << FCFG1_FLASH_VHV_VHV_E_SHIFT)
#define FCFG1_FLASH_VHV_TRIM13_E_SHIFT                     (8)       /* Bits 8-11 */
#define FCFG1_FLASH_VHV_TRIM13_E_MASK                      (15 << FCFG1_FLASH_VHV_TRIM13_E_SHIFT)
#  define FCFG1_FLASH_VHV_TRIM13_E(n)                      ((uint32_t)(n) << FCFG1_FLASH_VHV_TRIM13_E_SHIFT)
#define FCFG1_FLASH_VHV_VHV_P_SHIFT                        (16)      /* Bits 16-19 */
#define FCFG1_FLASH_VHV_VHV_P_MASK                         (15 << FCFG1_FLASH_VHV_VHV_P_SHIFT)
#  define FCFG1_FLASH_VHV_VHV_P(n)                         ((uint32_t)(n) << FCFG1_FLASH_VHV_VHV_P_SHIFT)
#define FCFG1_FLASH_VHV_TRIM13_P_SHIFT                     (24)      /* Bits 24-27 */
#define FCFG1_FLASH_VHV_TRIM13_P_MASK                      (15 << FCFG1_FLASH_VHV_TRIM13_P_SHIFT)
#  define FCFG1_FLASH_VHV_TRIM13_P(n)                      ((uint32_t)(n) << FCFG1_FLASH_VHV_TRIM13_P_SHIFT)

/* TIVA_FCFG1_FLASH_VHV_PV */

#define FCFG1_FLASH_VHV_PV_VINH_SHIFT                      (0)       /* Bits 0-7 */
#define FCFG1_FLASH_VHV_PV_VINH_MASK                       (0xff << FCFG1_FLASH_VHV_PV_VINH_SHIFT)
#  define FCFG1_FLASH_VHV_PV_VINH(n)                       ((uint32_t)(n) << FCFG1_FLASH_VHV_PV_VINH_SHIFT)
#define FCFG1_FLASH_VHV_PV_VCG2P5_SHIFT                    (8)       /* Bits 8-15 */
#define FCFG1_FLASH_VHV_PV_VCG2P5_MASK                     (0xff << FCFG1_FLASH_VHV_PV_VCG2P5_SHIFT)
#  define FCFG1_FLASH_VHV_PV_VCG2P5(n)                     ((uint32_t)(n) << FCFG1_FLASH_VHV_PV_VCG2P5_SHIFT)
#define FCFG1_FLASH_VHV_PV_VHV_PV_SHIFT                    (16)      /* Bits 16-19 */
#define FCFG1_FLASH_VHV_PV_VHV_PV_MASK                     (15 << FCFG1_FLASH_VHV_PV_VHV_PV_SHIFT)
#  define FCFG1_FLASH_VHV_PV_VHV_PV(n)                     ((uint32_t)(n) << FCFG1_FLASH_VHV_PV_VHV_PV_SHIFT)
#define FCFG1_FLASH_VHV_PV_TRIM13_PV_SHIFT                 (24)      /* Bits 24-27 */
#define FCFG1_FLASH_VHV_PV_TRIM13_PV_MASK                  (15 << FCFG1_FLASH_VHV_PV_TRIM13_PV_SHIFT)
#  define FCFG1_FLASH_VHV_PV_TRIM13_PV(n)                  ((uint32_t)(n) << FCFG1_FLASH_VHV_PV_TRIM13_PV_SHIFT)

/* TIVA_FCFG1_FLASH_V */

#define FCFG1_FLASH_V_V_READ_SHIFT                         (8)       /* Bits 8-15 */
#define FCFG1_FLASH_V_V_READ_MASK                          (0xff << FCFG1_FLASH_V_V_READ_SHIFT)
#  define FCFG1_FLASH_V_V_READ(n)                          ((uint32_t)(n) << FCFG1_FLASH_V_V_READ_SHIFT)
#define FCFG1_FLASH_V_VWL_P_SHIFT                          (16)      /* Bits 16-23 */
#define FCFG1_FLASH_V_VWL_P_MASK                           (0xff << FCFG1_FLASH_V_VWL_P_SHIFT)
#  define FCFG1_FLASH_V_VWL_P(n)                           ((uint32_t)(n) << FCFG1_FLASH_V_VWL_P_SHIFT)
#define FCFG1_FLASH_V_VSL_P_SHIFT                          (24)      /* Bits 24-31 */
#define FCFG1_FLASH_V_VSL_P_MASK                           (0xff << FCFG1_FLASH_V_VSL_P_SHIFT)
#  define FCFG1_FLASH_V_VSL_P(n)                           ((uint32_t)(n) << FCFG1_FLASH_V_VSL_P_SHIFT)

/* TIVA_FCFG1_USER_ID */

#define FCFG1_USER_ID_PROTOCOL_SHIFT                       (12)      /* Bits 12-15: Protocols supported */
#define FCFG1_USER_ID_PROTOCOL_MASK                        (15 << FCFG1_USER_ID_PROTOCOL_SHIFT)
#  define FCFG1_USER_ID_PROTOCOL_BLE                       (0 << FCFG1_USER_ID_PROTOCOL_SHIFT) /* BLE */
#  define FCFG1_USER_ID_PROTOCOL_RF4CE                     (2 << FCFG1_USER_ID_PROTOCOL_SHIFT) /* RF4CE */
#  define FCFG1_USER_ID_PROTOCOL_802154                    (4 << FCFG1_USER_ID_PROTOCOL_SHIFT) /* Zigbee/6lowpan */
#  define FCFG1_USER_ID_PROTOCOL_PROP                      (8 << FCFG1_USER_ID_PROTOCOL_SHIFT) /* Proprietary */

#define FCFG1_USER_ID_PKG_SHIFT                            (16)      /* Bits 16-18: Package type */
#define FCFG1_USER_ID_PKG_MASK                             (7 << FCFG1_USER_ID_PKG_SHIFT)
#  define FCFG1_USER_ID_PKG_RHG                            (0 << FCFG1_USER_ID_PKG_SHIFT) /* 4x4mm QFN (RHB) package */
#  define FCFG1_USER_ID_PKG_RSM                            (1 << FCFG1_USER_ID_PKG_SHIFT) /* 5x5mm QFN (RSM) package */
#  define FCFG1_USER_ID_PKG_RGZ                            (2 << FCFG1_USER_ID_PKG_SHIFT) /* 7x7mm QFN (RGZ) package */
#  define FCFG1_USER_ID_PKG_WSP                            (3 << FCFG1_USER_ID_PKG_SHIFT) /* Wafer sale package (naked die) */
#  define FCFG1_USER_ID_PKG_QFNWF                          (5 << FCFG1_USER_ID_PKG_SHIFT) /* 7x7mm QFN package with Wettable Flanks */

#define FCFG1_USER_ID_SEQUENCE_SHIFT                       (19)      /* Bits 19-22: Sequence */
#define FCFG1_USER_ID_SEQUENCE_MASK                        (15 << FCFG1_USER_ID_SEQUENCE_SHIFT)
#  define FCFG1_USER_ID_SEQUENCE(n)                        ((uint32_t)(n) << FCFG1_USER_ID_SEQUENCE_SHIFT)
#define FCFG1_USER_ID_VER_SHIFT                            (26)      /* Bits 26-27: Version number encoding */
#define FCFG1_USER_ID_VER_MASK                             (3 << FCFG1_USER_ID_VER_SHIFT)
#  define FCFG1_USER_ID_VER(n)                             ((uint32_t)(n) << FCFG1_USER_ID_VER_SHIFT)
#define FCFG1_USER_ID_PG_REV_SHIFT                         (28)      /* Bits 28-31: Revision of the device */
#define FCFG1_USER_ID_PG_REV_MASK                          (15 << FCFG1_USER_ID_PG_REV_SHIFT)
#  define FCFG1_USER_ID_PG_REV(n)                          ((uint32_t)(n) << FCFG1_USER_ID_PG_REV_SHIFT)

/* TIVA_FCFG1_FLASH_OTP_DATA3 */

#define FCFG1_FLASH_OTP_DATA3_WAIT_SYSCODE_SHIFT           (0)      /* Bits 9-7 */
#define FCFG1_FLASH_OTP_DATA3_WAIT_SYSCODE_MASK            (0xff << FCFG1_FLASH_OTP_DATA3_WAIT_SYSCODE_SHIFT)
#  define FCFG1_FLASH_OTP_DATA3_WAIT_SYSCODE(n)            ((uint32_t)(n) << FCFG1_FLASH_OTP_DATA3_WAIT_SYSCODE_SHIFT)
#define FCFG1_FLASH_OTP_DATA3_FLASH_SIZE_SHIFT             (8)      /* Bits 8-15 */
#define FCFG1_FLASH_OTP_DATA3_FLASH_SIZE_MASK              (0xff << FCFG1_FLASH_OTP_DATA3_FLASH_SIZE_SHIFT)
#  define FCFG1_FLASH_OTP_DATA3_FLASH_SIZE(n)              ((uint32_t)(n) << FCFG1_FLASH_OTP_DATA3_FLASH_SIZE_SHIFT)
#define FCFG1_FLASH_OTP_DATA3_TRIM_1P7_SHIFT               (16)      /* Bits 16-17 */
#define FCFG1_FLASH_OTP_DATA3_TRIM_1P7_MASK                (3 << FCFG1_FLASH_OTP_DATA3_TRIM_1P7_SHIFT)
#  define FCFG1_FLASH_OTP_DATA3_TRIM_1P7(n)                ((uint32_t)(n) << FCFG1_FLASH_OTP_DATA3_TRIM_1P7_SHIFT)
#define FCFG1_FLASH_OTP_DATA3_MAX_EC_LEVEL_SHIFT           (18)      /* Bits 18-21 */
#define FCFG1_FLASH_OTP_DATA3_MAX_EC_LEVEL_MASK            (15 << FCFG1_FLASH_OTP_DATA3_MAX_EC_LEVEL_SHIFT)
#  define FCFG1_FLASH_OTP_DATA3_MAX_EC_LEVEL(n)            ((uint32_t)(n) << FCFG1_FLASH_OTP_DATA3_MAX_EC_LEVEL_SHIFT)
#define FCFG1_FLASH_OTP_DATA3_DO_PRECOND                   (1 << 22) /* Bit 22 */
#define FCFG1_FLASH_OTP_DATA3_EC_STEP_SIZE_SHIFT           (23)      /* Bits 23-31 */
#define FCFG1_FLASH_OTP_DATA3_EC_STEP_SIZE_MASK            (0x1ff << FCFG1_FLASH_OTP_DATA3_EC_STEP_SIZE_SHIFT)
#  define FCFG1_FLASH_OTP_DATA3_EC_STEP_SIZE(n)            ((uint32_t)(n) << FCFG1_FLASH_OTP_DATA3_EC_STEP_SIZE_SHIFT)

/* TIVA_FCFG1_ANA2_TRIM */

#define FCFG1_ANA2_TRIM_DCDC_HIGH_EN_SEL_SHIFT             (0)       /* Bits 0-2 */
#define FCFG1_ANA2_TRIM_DCDC_HIGH_EN_SEL_MASK              (7 << FCFG1_ANA2_TRIM_DCDC_HIGH_EN_SEL_SHIFT)
#  define FCFG1_ANA2_TRIM_DCDC_HIGH_EN_SEL(n)              ((uint32_t)(n) << FCFG1_ANA2_TRIM_DCDC_HIGH_EN_SEL_SHIFT)
#define FCFG1_ANA2_TRIM_DCDC_LOW_EN_SEL_SHIFT              (3)       /* Bits 3-5 */
#define FCFG1_ANA2_TRIM_DCDC_LOW_EN_SEL_MASK               (7 << FCFG1_ANA2_TRIM_DCDC_LOW_EN_SEL_SHIFT)
#  define FCFG1_ANA2_TRIM_DCDC_LOW_EN_SEL(n)               ((uint32_t)(n) << FCFG1_ANA2_TRIM_DCDC_LOW_EN_SEL_SHIFT)
#define FCFG1_ANA2_TRIM_DEAD_TIME_TRIM_SHIFT               (6)       /* Bits 6-7 */
#define FCFG1_ANA2_TRIM_DEAD_TIME_TRIM_MASK                (3 << FCFG1_ANA2_TRIM_DEAD_TIME_TRIM_SHIFT)
#  define FCFG1_ANA2_TRIM_DEAD_TIME_TRIM(n)                ((uint32_t)(n) << FCFG1_ANA2_TRIM_DEAD_TIME_TRIM_SHIFT)
#define FCFG1_ANA2_TRIM_DCDC_IPEAK_SHIFT                   (8)       /* Bits 8-10 */
#define FCFG1_ANA2_TRIM_DCDC_IPEAK_MASK                    (7 << FCFG1_ANA2_TRIM_DCDC_IPEAK_SHIFT)
#  define FCFG1_ANA2_TRIM_DCDC_IPEAK(n)                    ((uint32_t)(n) << FCFG1_ANA2_TRIM_DCDC_IPEAK_SHIFT)
#define FCFG1_ANA2_TRIM_DITHER_EN                          (1 << 11) /* Bit 11 */
#define FCFG1_ANA2_TRIM_NANOAMP_RES_TRIM_SHIFT             (16)      /* Bits 16-21 */
#define FCFG1_ANA2_TRIM_NANOAMP_RES_TRIM_MASK              (0x3f << FCFG1_ANA2_TRIM_NANOAMP_RES_TRIM_SHIFT)
#  define FCFG1_ANA2_TRIM_NANOAMP_RES_TRIM(n)              ((uint32_t)(n) << FCFG1_ANA2_TRIM_NANOAMP_RES_TRIM_SHIFT)
#define FCFG1_ANA2_TRIM_ATESTLF_UDIGLDO_IBIAS_TRIM         (1 << 22) /* Bit 22 */
#define FCFG1_ANA2_TRIM_SET_RCOSC_HF_FINE_RESISTOR_SHIFT   (23)      /* Bits 23-24 */
#define FCFG1_ANA2_TRIM_SET_RCOSC_HF_FINE_RESISTOR_MASK    (3 << FCFG1_ANA2_TRIM_SET_RCOSC_HF_FINE_RESISTOR_SHIFT)
#  define FCFG1_ANA2_TRIM_SET_RCOSC_HF_FINE_RESISTOR(n)    ((uint32_t)(n) << FCFG1_ANA2_TRIM_SET_RCOSC_HF_FINE_RESISTOR_SHIFT)
#define FCFG1_ANA2_TRIM_RCOSCHFCTRIMFRACT_SHIFT            (26)      /* Bits 26-30 */
#define FCFG1_ANA2_TRIM_RCOSCHFCTRIMFRACT_MASK             (31 << FCFG1_ANA2_TRIM_RCOSCHFCTRIMFRACT_SHIFT)
#  define FCFG1_ANA2_TRIM_RCOSCHFCTRIMFRACT(n)             ((uint32_t)(n) << FCFG1_ANA2_TRIM_RCOSCHFCTRIMFRACT_SHIFT)
#define FCFG1_ANA2_TRIM_RCOSCHFCTRIMFRACT_EN               (1 << 31)  /* Bit 31 */

/* TIVA_FCFG1_LDO_TRIM */

#define FCFG1_LDO_TRIM_VTRIM_DELTA_SHIFT                   (0)      /* Bits 0-2 */
#define FCFG1_LDO_TRIM_VTRIM_DELTA_MASK                    (7 << FCFG1_LDO_TRIM_VTRIM_DELTA_SHIFT)
#  define FCFG1_LDO_TRIM_VTRIM_DELTA(n)                    ((uint32_t)(n) << FCFG1_LDO_TRIM_VTRIM_DELTA_SHIFT)
#define FCFG1_LDO_TRIM_ITRIM_UDIGLDO_SHIFT                 (8)      /* Bits 8-19 */
#define FCFG1_LDO_TRIM_ITRIM_UDIGLDO_MASK                  (7 << FCFG1_LDO_TRIM_ITRIM_UDIGLDO_SHIFT)
#  define FCFG1_LDO_TRIM_ITRIM_UDIGLDO(n)                  ((uint32_t)(n) << FCFG1_LDO_TRIM_ITRIM_UDIGLDO_SHIFT)
#define FCFG1_LDO_TRIM_ITRIM_DIGLDO_LOAD_SHIFT             (11)      /* Bits 11-12 */
#define FCFG1_LDO_TRIM_ITRIM_DIGLDO_LOAD_MASK              (3 << FCFG1_LDO_TRIM_ITRIM_DIGLDO_LOAD_SHIFT)
#  define FCFG1_LDO_TRIM_ITRIM_DIGLDO_LOAD(n)              ((uint32_t)(n) << FCFG1_LDO_TRIM_ITRIM_DIGLDO_LOAD_SHIFT)
#define FCFG1_LDO_TRIM_GLDO_CURSRC_SHIFT                   (16)      /* Bits 16-18 */
#define FCFG1_LDO_TRIM_GLDO_CURSRC_MASK                    (7 << FCFG1_LDO_TRIM_GLDO_CURSRC_SHIFT)
#  define FCFG1_LDO_TRIM_GLDO_CURSRC(n)                    ((uint32_t)(n) << FCFG1_LDO_TRIM_GLDO_CURSRC_SHIFT)
#define FCFG1_LDO_TRIM_VDDR_TRIM_SLEEP_SHIFT               (24)      /* Bits 24-28 */
#define FCFG1_LDO_TRIM_VDDR_TRIM_SLEEP_MASK                (31 << FCFG1_LDO_TRIM_VDDR_TRIM_SLEEP_SHIFT)
#  define FCFG1_LDO_TRIM_VDDR_TRIM_SLEEP(n)                ((uint32_t)(n) << FCFG1_LDO_TRIM_VDDR_TRIM_SLEEP_SHIFT)

/* TIVA_FCFG1_BAT_RC_LDO_TRIM */

#define FCFG1_BAT_RC_LDO_TRIM_MEASUREPER_SHIFT             (0)      /* Bits 0-1 */
#define FCFG1_BAT_RC_LDO_TRIM_MEASUREPER_MASK              (3 << FCFG1_BAT_RC_LDO_TRIM_MEASUREPER_SHIFT)
#  define FCFG1_BAT_RC_LDO_TRIM_MEASUREPER(n)              ((uint32_t)(n) << FCFG1_BAT_RC_LDO_TRIM_MEASUREPER_SHIFT)
#define FCFG1_BAT_RC_LDO_TRIM_RCOSCHF_ITUNE_TRIM_SHIFT     (8)      /* Bits 8-11 */
#define FCFG1_BAT_RC_LDO_TRIM_RCOSCHF_ITUNE_TRIM_MASK      (15 << FCFG1_BAT_RC_LDO_TRIM_RCOSCHF_ITUNE_TRIM_SHIFT)
#  define FCFG1_BAT_RC_LDO_TRIM_RCOSCHF_ITUNE_TRIM(n)      ((uint32_t)(n) << FCFG1_BAT_RC_LDO_TRIM_RCOSCHF_ITUNE_TRIM_SHIFT)
#define FCFG1_BAT_RC_LDO_TRIM_VTRIM_UDIG_SHIFT             (16)      /* Bits 16-19 */
#define FCFG1_BAT_RC_LDO_TRIM_VTRIM_UDIG_MASK              (15 << FCFG1_BAT_RC_LDO_TRIM_VTRIM_UDIG_SHIFT)
#  define FCFG1_BAT_RC_LDO_TRIM_VTRIM_UDIG(n)              ((uint32_t)(n) << FCFG1_BAT_RC_LDO_TRIM_VTRIM_UDIG_SHIFT)
#define FCFG1_BAT_RC_LDO_TRIM_VTRIM_BOD_SHIFT              (24)      /* Bits 24-27 */
#define FCFG1_BAT_RC_LDO_TRIM_VTRIM_BOD_MASK               (15 << FCFG1_BAT_RC_LDO_TRIM_VTRIM_BOD_SHIFT)
#  define FCFG1_BAT_RC_LDO_TRIM_VTRIM_BOD(n)               ((uint32_t)(n) << FCFG1_BAT_RC_LDO_TRIM_VTRIM_BOD_SHIFT)

/* TIVA_FCFG1_MAC_BLE_0
 * (32-bit value, The first 32-bits of the 64-bit MAC BLE address)
 */

/* TIVA_FCFG1_MAC_BLE_1
 * (32-bit value,  The last 32-bits of the 64-bit MAC BLE address)
 */

/* TIVA_FCFG1_MAC_15_4_0
 * (32-bit value, The first 32-bits of the 64-bit MAC 15.4 address)
 */

/* TIVA_FCFG1_MAC_15_4_1
 * (32-bit value, The last 32-bits of the 64-bit MAC 15.4 address)
 */

/* TIVA_FCFG1_FLASH_OTP_DATA4 */

#define FCFG1_FLASH_OTP_DATA4_VIN_AT_X_EXT_RD_SHIFT        (0)       /* Bits 0-2 */
#define FCFG1_FLASH_OTP_DATA4_VIN_AT_X_EXT_RD_MASK         (7 << FCFG1_FLASH_OTP_DATA4_VIN_AT_X_EXT_RD_SHIFT)
#  define FCFG1_FLASH_OTP_DATA4_VIN_AT_X_EXT_RD(n)         ((uint32_t)(n) << FCFG1_FLASH_OTP_DATA4_VIN_AT_X_EXT_RD_SHIFT)
#define FCFG1_FLASH_OTP_DATA4_DIS_IDLE_EXT_RD              (1 << 3)  /* Bit 3 */
#define FCFG1_FLASH_OTP_DATA4_DIS_STANDBY_EXT_RD           (1 << 4)  /* Bit 4 */
#define FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_EXT_RD_SHIFT  (5)       /* Bits 5-6 */
#define FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_EXT_RD_MASK   (3 << FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_EXT_RD_SHIFT)
#  define FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_EXT_RD(n)   ((uint32_t)(n) << FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_EXT_RD_SHIFT)
#define FCFG1_FLASH_OTP_DATA4_STANDBY_MODE_SEL_EXT_RD      (1 << 7)  /* Bit 7 */
#define FCFG1_FLASH_OTP_DATA4_VIN_AT_X_INT_RD_SHIFT        (8)       /* Bits 8-10 */
#define FCFG1_FLASH_OTP_DATA4_VIN_AT_X_INT_RD_MASK         (7 << FCFG1_FLASH_OTP_DATA4_VIN_AT_X_INT_RD_SHIFT)
#  define FCFG1_FLASH_OTP_DATA4_VIN_AT_X_INT_RD(n)         ((uint32_t)(n) << FCFG1_FLASH_OTP_DATA4_VIN_AT_X_INT_RD_SHIFT)
#define FCFG1_FLASH_OTP_DATA4_DIS_IDLE_INT_RD              (1 << 11) /* Bit 11 */
#define FCFG1_FLASH_OTP_DATA4_DIS_STANDBY_INT_RD           (1 << 12) /* Bit 12 */
#define FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_INT_RD_SHIFT  (13)      /* Bits 13-14 */
#define FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_INT_RD_MASK   (3 << FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_INT_RD_SHIFT)
#  define FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_INT_RD(n)   ((uint32_t)(n) << FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_INT_RD_SHIFT)
#define FCFG1_FLASH_OTP_DATA4_STANDBY_MODE_SEL_INT_RD      (1 << 15) /* Bit 15 */
#define FCFG1_FLASH_OTP_DATA4_VIN_AT_X_EXT_WRT_SHIFT       (16)      /* Bits nn-nn */
#define FCFG1_FLASH_OTP_DATA4_VIN_AT_X_EXT_WRT_MASK        (7 << FCFG1_FLASH_OTP_DATA4_VIN_AT_X_EXT_WRT_SHIFT)
#  define FCFG1_FLASH_OTP_DATA4_VIN_AT_X_EXT_WRT(n)        ((uint32_t)(n) << FCFG1_FLASH_OTP_DATA4_VIN_AT_X_EXT_WRT_SHIFT)
#define FCFG1_FLASH_OTP_DATA4_DIS_IDLE_EXT_WRT             (1 << 19) /* Bit 19 */
#define FCFG1_FLASH_OTP_DATA4_DIS_STANDBY_EXT_WRT          (1 << 20) /* Bit 20 */
#define FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_EXT_WRT_SHIFT (21)      /* Bits 21-22 */
#define FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_EXT_WRT_MASK  (3 << FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_EXT_WRT_SHIFT)
#  define FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_EXT_WRT(n)  ((uint32_t)(n) << FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_EXT_WRT_SHIFT)
#define FCFG1_FLASH_OTP_DATA4_STANDBY_MODE_SEL_EXT_WRT     (1 << 23) /* Bit 23 */
#define FCFG1_FLASH_OTP_DATA4_VIN_AT_X_INT_WRT_SHIFT       (24)      /* Bits 24-26 */
#define FCFG1_FLASH_OTP_DATA4_VIN_AT_X_INT_WRT_MASK        (7 << FCFG1_FLASH_OTP_DATA4_VIN_AT_X_INT_WRT_SHIFT)
#  define FCFG1_FLASH_OTP_DATA4_VIN_AT_X_INT_WRT(n)        ((uint32_t)(n) << FCFG1_FLASH_OTP_DATA4_VIN_AT_X_INT_WRT_SHIFT)
#define FCFG1_FLASH_OTP_DATA4_DIS_IDLE_INT_WRT             (1 << 27) /* Bit 27 */
#define FCFG1_FLASH_OTP_DATA4_DIS_STANDBY_INT_WRT          (1 << 28) /* Bit 28 */
#define FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_INT_WRT_SHIFT (29)      /* Bits 29-30 */
#define FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_INT_WRT_MASK  (3 << FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_INT_WRT_SHIFT)
#  define FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_INT_WRT(n)  ((uint32_t)(n) << FCFG1_FLASH_OTP_DATA4_STANDBY_PW_SEL_INT_WRT_SHIFT)
#define FCFG1_FLASH_OTP_DATA4_STANDBY_MODE_SEL_INT_WRT     (1 << 31) /* Bit 31 */

/* TIVA_FCFG1_MISC_TRIM */

#define FCFG1_MISC_TRIM_TEMPVSLOPE_SHIFT                   (0)       /* Bits 0-7: TEMP slope with battery voltage,
                                                                      * degrees in C */
#define FCFG1_MISC_TRIM_TEMPVSLOPE_MASK                    (0xff << FCFG1_MISC_TRIM_TEMPVSLOPE_SHIFT)
#  define FCFG1_MISC_TRIM_TEMPVSLOPE(n)                    ((uint32_t)(n) << FCFG1_MISC_TRIM_TEMPVSLOPE_SHIFT)

/* TIVA_FCFG1_RCOSC_HF_TEMPCOMP */

#define FCFG1_RCOSC_HF_TEMPCOMP_CTRIMFRACT_SLOPE_SHIFT     (0)       /* Bits 0-7 */
#define FCFG1_RCOSC_HF_TEMPCOMP_CTRIMFRACT_SLOPE_MASK      (0xff << FCFG1_RCOSC_HF_TEMPCOMP_CTRIMFRACT_SLOPE_SHIFT)
#  define FCFG1_RCOSC_HF_TEMPCOMP_CTRIMFRACT_SLOPE(n)      ((uint32_t)(n) << FCFG1_RCOSC_HF_TEMPCOMP_CTRIMFRACT_SLOPE_SHIFT)
#define FCFG1_RCOSC_HF_TEMPCOMP_CTRIMFRACT_QUAD_SHIFT      (8)       /* Bits 8-15 */
#define FCFG1_RCOSC_HF_TEMPCOMP_CTRIMFRACT_QUAD_MASK       (0xff << FCFG1_RCOSC_HF_TEMPCOMP_CTRIMFRACT_QUAD_SHIFT)
#  define FCFG1_RCOSC_HF_TEMPCOMP_CTRIMFRACT_QUAD(n)       ((uint32_t)(n) << FCFG1_RCOSC_HF_TEMPCOMP_CTRIMFRACT_QUAD_SHIFT)
#define FCFG1_RCOSC_HF_TEMPCOMP_CTRIM_SHIFT                (16)      /* Bits 16-24 */
#define FCFG1_RCOSC_HF_TEMPCOMP_CTRIM_MASK                 (0xff << FCFG1_RCOSC_HF_TEMPCOMP_CTRIM_SHIFT)
#  define FCFG1_RCOSC_HF_TEMPCOMP_CTRIM(n)                 ((uint32_t)(n) << FCFG1_RCOSC_HF_TEMPCOMP_CTRIM_SHIFT)
#define FCFG1_RCOSC_HF_TEMPCOMP_FINE_RESISTOR_SHIFT        (24)      /* Bits 24-31 */
#define FCFG1_RCOSC_HF_TEMPCOMP_FINE_RESISTOR_MASK         (0xff << FCFG1_RCOSC_HF_TEMPCOMP_FINE_RESISTOR_SHIFT)
#  define FCFG1_RCOSC_HF_TEMPCOMP_FINE_RESISTOR(n)         ((uint32_t)(n) << FCFG1_RCOSC_HF_TEMPCOMP_FINE_RESISTOR_SHIFT)

/* TIVA_FCFG1_TRIM_CAL_REVISION */

#define FCFG1_TRIM_CAL_REVISION_MP1_SHIFT                  (0)       /* Bits 0-15 */
#define FCFG1_TRIM_CAL_REVISION_MP1_MASK                   (0xffff << FCFG1_TRIM_CAL_REVISION_MP1_SHIFT)
#  define FCFG1_TRIM_CAL_REVISION_MP1(n)                   ((uint32_t)(n) << FCFG1_TRIM_CAL_REVISION_MP1_SHIFT)
#define FCFG1_TRIM_CAL_REVISION_FT1_SHIFT                  (16)      /* Bits 16-31 */
#define FCFG1_TRIM_CAL_REVISION_FT1_MASK                   (0xffff << FCFG1_TRIM_CAL_REVISION_FT1_SHIFT)
#  define FCFG1_TRIM_CAL_REVISION_FT1(n)                   ((uint32_t)(n) << FCFG1_TRIM_CAL_REVISION_FT1_SHIFT)

/* TIVA_FCFG1_ICEPICK_DEVICE_ID */

#define FCFG1_ICEPICK_DEVICE_ID_MANUFACTURER_ID_SHIFT      (0)       /* Bits 0-11:  0x02F: Texas Instruments */
#define FCFG1_ICEPICK_DEVICE_ID_MANUFACTURER_ID_MASK       (0xfff << FCFG1_ICEPICK_DEVICE_ID_MANUFACTURER_ID_SHIFT)
#  define FCFG1_ICEPICK_DEVICE_ID_MANUFACTURER_ID(n)       ((uint32_t)(n) << FCFG1_ICEPICK_DEVICE_ID_MANUFACTURER_ID_SHIFT)
#define FCFG1_ICEPICK_DEVICE_ID_WAFER_ID_SHIFT             (12)      /* Bits 12-27: Identifies silicon die */
#define FCFG1_ICEPICK_DEVICE_ID_WAFER_ID_MASK              (0xffff << FCFG1_ICEPICK_DEVICE_ID_WAFER_ID_SHIFT)
#  define FCFG1_ICEPICK_DEVICE_ID_WAFER_ID(n)              ((uint32_t)(n) << FCFG1_ICEPICK_DEVICE_ID_WAFER_ID_SHIFT)
#define FCFG1_ICEPICK_DEVICE_ID_PG_REV_SHIFT               (28)      /* Bits 28-31:  Revision of the device */
#define FCFG1_ICEPICK_DEVICE_ID_PG_REV_MASK                (15 << FCFG1_ICEPICK_DEVICE_ID_PG_REV_SHIFT)
#  define FCFG1_ICEPICK_DEVICE_ID_PG_REV(n)                ((uint32_t)(n) << FCFG1_ICEPICK_DEVICE_ID_PG_REV_SHIFT)

/* TIVA_FCFG1_FCFG1_REVISION
 * (32-bit value,  The revision number of the FCFG1 layout)
 */

/* TIVA_FCFG1_MISC_OTP_DATA */

#define FCFG1_MISC_OTP_DATA_TEST_PROGRAM_REV_SHIFT         (0)       /* Bits 0-7: The revision of the test program used in the production process */
#define FCFG1_MISC_OTP_DATA_TEST_PROGRAM_REV_MASK          (0xff << FCFG1_MISC_OTP_DATA_TEST_PROGRAM_REV_SHIFT)
#  define FCFG1_MISC_OTP_DATA_TEST_PROGRAM_REV(n)          ((uint32_t)(n) << FCFG1_MISC_OTP_DATA_TEST_PROGRAM_REV_SHIFT)
#define FCFG1_MISC_OTP_DATA_MIN_ALLOWED_RTRIM_DIV5_SHIFT   (8)       /* Bits 8-11 */
#define FCFG1_MISC_OTP_DATA_MIN_ALLOWED_RTRIM_DIV5_MASK    (0xff << FCFG1_MISC_OTP_DATA_MIN_ALLOWED_RTRIM_DIV5_SHIFT)
#  define FCFG1_MISC_OTP_DATA_MIN_ALLOWED_RTRIM_DIV5(n)    ((uint32_t)(n) << FCFG1_MISC_OTP_DATA_MIN_ALLOWED_RTRIM_DIV5_SHIFT)
#define FCFG1_MISC_OTP_DATA_PER_E_SHIFT                    (12)      /* Bits 12-14 */
#define FCFG1_MISC_OTP_DATA_PER_E_MASK                     (7 << FCFG1_MISC_OTP_DATA_PER_E_SHIFT)
#  define FCFG1_MISC_OTP_DATA_PER_E(n)                     ((uint32_t)(n) << FCFG1_MISC_OTP_DATA_PER_E_SHIFT)
#define FCFG1_MISC_OTP_DATA_PER_M_SHIFT                    (15)      /* Bits 15-19 */
#define FCFG1_MISC_OTP_DATA_PER_M_MASK                     (31 << FCFG1_MISC_OTP_DATA_PER_M_SHIFT)
#  define FCFG1_MISC_OTP_DATA_PER_M(n)                     ((uint32_t)(n) << FCFG1_MISC_OTP_DATA_PER_M_SHIFT)
#define FCFG1_MISC_OTP_DATA_RCOSC_HF_CRIM_SHIFT            (20)      /* Bits 20-17 */
#define FCFG1_MISC_OTP_DATA_RCOSC_HF_CRIM_MASK             (0xff << FCFG1_MISC_OTP_DATA_RCOSC_HF_CRIM_SHIFT)
#  define FCFG1_MISC_OTP_DATA_RCOSC_HF_CRIM(n)             ((uint32_t)(n) << FCFG1_MISC_OTP_DATA_RCOSC_HF_CRIM_SHIFT)
#define FCFG1_MISC_OTP_DATA_RCOSC_HF_ITUNE_SHIFT           (28)      /* Bits 28-31 */
#define FCFG1_MISC_OTP_DATA_RCOSC_HF_ITUNE_MASK            (15 << FCFG1_MISC_OTP_DATA_RCOSC_HF_ITUNE_SHIFT)
#  define FCFG1_MISC_OTP_DATA_RCOSC_HF_ITUNE(n)            ((uint32_t)(n) << FCFG1_MISC_OTP_DATA_RCOSC_HF_ITUNE_SHIFT)

/* TIVA_FCFG1_IOCONF */

#define FCFG1_IOCONF_GPIO_CNT_SHIFT                        (0)       /* Bits 0-6: Number of available DIOs */
#define FCFG1_IOCONF_GPIO_CNT_MASK                         (0x75 << FCFG1_IOCONF_GPIO_CNT_SHIFT)
#  define FCFG1_IOCONF_GPIO_CNT(n)                         ((uint32_t)(n) << FCFG1_IOCONF_GPIO_CNT_SHIFT)

/* TIVA_FCFG1_CONFIG_IF_ADC */

#define FCFG1_CONFIG_IF_ADC_IFANALDO_TRIM_OUTPUT_SHIFT     (0)       /* Bits 0-4 */
#define FCFG1_CONFIG_IF_ADC_IFANALDO_TRIM_OUTPUT_MASK      (0x1f << FCFG1_CONFIG_IF_ADC_IFANALDO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_IF_ADC_IFANALDO_TRIM_OUTPUT(n)      ((uint32_t)(n) << FCFG1_CONFIG_IF_ADC_IFANALDO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_IF_ADC_IFDIGLDO_TRIM_OUTPUT_SHIFT     (5)       /* Bits 5-9 */
#define FCFG1_CONFIG_IF_ADC_IFDIGLDO_TRIM_OUTPUT_MASK      (31 << FCFG1_CONFIG_IF_ADC_IFDIGLDO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_IF_ADC_IFDIGLDO_TRIM_OUTPUT(n)      ((uint32_t)(n) << FCFG1_CONFIG_IF_ADC_IFDIGLDO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_IF_ADC_INT2ADJ_SHIFT                  (10)      /* Bits 10-13 */
#define FCFG1_CONFIG_IF_ADC_INT2ADJ_MASK                   (15 << FCFG1_CONFIG_IF_ADC_INT2ADJ_SHIFT)
#  define FCFG1_CONFIG_IF_ADC_INT2ADJ(n)                   ((uint32_t)(n) << FCFG1_CONFIG_IF_ADC_INT2ADJ_SHIFT)
#define FCFG1_CONFIG_IF_ADC_AAFCAP_SHIFT                   (14)      /* Bits 14-15 */
#define FCFG1_CONFIG_IF_ADC_AAFCAP_MASK                    (3 << FCFG1_CONFIG_IF_ADC_AAFCAP_SHIFT)
#  define FCFG1_CONFIG_IF_ADC_AAFCAP(n)                    ((uint32_t)(n) << FCFG1_CONFIG_IF_ADC_AAFCAP_SHIFT)
#define FCFG1_CONFIG_IF_ADC_FF1ADJ_SHIFT                   (16)      /* Bits 16-19 */
#define FCFG1_CONFIG_IF_ADC_FF1ADJ_MASK                    (15 << FCFG1_CONFIG_IF_ADC_FF1ADJ_SHIFT)
#  define FCFG1_CONFIG_IF_ADC_FF1ADJ(n)                    ((uint32_t)(n) << FCFG1_CONFIG_IF_ADC_FF1ADJ_SHIFT)
#define FCFG1_CONFIG_IF_ADC_INT3ADJ_SHIFT                  (20)      /* Bits 20-23 */
#define FCFG1_CONFIG_IF_ADC_INT3ADJ_MASK                   (15 << FCFG1_CONFIG_IF_ADC_INT3ADJ_SHIFT)
#  define FCFG1_CONFIG_IF_ADC_INT3ADJ(n)                   ((uint32_t)(n) << FCFG1_CONFIG_IF_ADC_INT3ADJ_SHIFT)
#define FCFG1_CONFIG_IF_ADC_FF3ADJ_SHIFT                   (24)      /* Bits 24-27 */
#define FCFG1_CONFIG_IF_ADC_FF3ADJ_MASK                    (15 << FCFG1_CONFIG_IF_ADC_FF3ADJ_SHIFT)
#  define FCFG1_CONFIG_IF_ADC_FF3ADJ(n)                    ((uint32_t)(n) << FCFG1_CONFIG_IF_ADC_FF3ADJ_SHIFT)
#define FCFG1_CONFIG_IF_ADC_FF2ADJ_SHIFT                   (28)      /* Bits 28-31 */
#define FCFG1_CONFIG_IF_ADC_FF2ADJ_MASK                    (15 << FCFG1_CONFIG_IF_ADC_FF2ADJ_SHIFT)
#  define FCFG1_CONFIG_IF_ADC_FF2ADJ(n)                    ((uint32_t)(n) << FCFG1_CONFIG_IF_ADC_FF2ADJ_SHIFT)

/* TIVA_FCFG1_CONFIG_OSC_TOP */

#define FCFG1_CONFIG_OSC_TOP_RCOSCLF_RTUNE_TRIM_SHIFT      (0)       /* Bits 0-1 */
#define FCFG1_CONFIG_OSC_TOP_RCOSCLF_RTUNE_TRIM_MASK       (3 << FCFG1_CONFIG_OSC_TOP_RCOSCLF_RTUNE_TRIM_SHIFT)
#  define FCFG1_CONFIG_OSC_TOP_RCOSCLF_RTUNE_TRIM(n)       ((uint32_t)(n) << FCFG1_CONFIG_OSC_TOP_RCOSCLF_RTUNE_TRIM_SHIFT)
#define FCFG1_CONFIG_OSC_TOP_RCOSCLF_CTUNE_TRIM_SHIFT      (2)       /* Bits 2-9 */
#define FCFG1_CONFIG_OSC_TOP_RCOSCLF_CTUNE_TRIM_MASK       (0xff << FCFG1_CONFIG_OSC_TOP_RCOSCLF_CTUNE_TRIM_SHIFT)
#  define FCFG1_CONFIG_OSC_TOP_RCOSCLF_CTUNE_TRIM(n)       ((uint32_t)(n) << FCFG1_CONFIG_OSC_TOP_RCOSCLF_CTUNE_TRIM_SHIFT)
#define FCFG1_CONFIG_OSC_TOP_XOSC_HF_COLUMN_Q12_SHIFT      (10)      /* Bits 10-25 */
#define FCFG1_CONFIG_OSC_TOP_XOSC_HF_COLUMN_Q12_MASK       (0xffff << FCFG1_CONFIG_OSC_TOP_XOSC_HF_COLUMN_Q12_SHIFT)
#  define FCFG1_CONFIG_OSC_TOP_XOSC_HF_COLUMN_Q12(n)       ((uint32_t)(n) << FCFG1_CONFIG_OSC_TOP_XOSC_HF_COLUMN_Q12_SHIFT)
#define FCFG1_CONFIG_OSC_TOP_XOSC_HF_ROW_Q12_SHIFT         (26)      /* Bits 26-29 */
#define FCFG1_CONFIG_OSC_TOP_XOSC_HF_ROW_Q12_MASK          (15 << FCFG1_CONFIG_OSC_TOP_XOSC_HF_ROW_Q12_SHIFT)
#  define FCFG1_CONFIG_OSC_TOP_XOSC_HF_ROW_Q12(n)          ((uint32_t)(n) << FCFG1_CONFIG_OSC_TOP_XOSC_HF_ROW_Q12_SHIFT)

/* TIVA_FCFG1_CONFIG_RF_FRONTEND */

#define FCFG1_CONFIG_RF_FRONTEND_RFLDO_TRIM_OUTPUT_SHIFT   (0)      /* Bits 0-6 */
#define FCFG1_CONFIG_RF_FRONTEND_RFLDO_TRIM_OUTPUT_MASK    (0x7f << FCFG1_CONFIG_RF_FRONTEND_RFLDO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_RFLDO_TRIM_OUTPUT(n)    ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_RFLDO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_PATRIMCOMPLETE_N          (1 << 13) /* Bit 13 */
#define FCFG1_CONFIG_RF_FRONTEND_CTL_PA0_TRIM_SHIFT        (14)      /* Bits 14-18 */
#define FCFG1_CONFIG_RF_FRONTEND_CTL_PA0_TRIM_MASK         (31 << FCFG1_CONFIG_RF_FRONTEND_CTL_PA0_TRIM_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_CTL_PA0_TRIM(n)         ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_CTL_PA0_TRIM_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_IFAMP_TRIM_SHIFT          (19)      /* Bits 19-23 */
#define FCFG1_CONFIG_RF_FRONTEND_IFAMP_TRIM_MASK           (31 << FCFG1_CONFIG_RF_FRONTEND_IFAMP_TRIM_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_IFAMP_TRIM(n)           ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_IFAMP_TRIM_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_LNA_IB_SHIFT              (24)      /* Bits 24-27 */
#define FCFG1_CONFIG_RF_FRONTEND_LNA_IB_MASK               (15 << FCFG1_CONFIG_RF_FRONTEND_LNA_IB_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_LNA_IB(n)               ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_LNA_IB_SHIFT)
#define FCFG1_CONFIG_RF_FRONTEND_IFAMP_IB_SHIFT            (28)      /* Bits 28-31 */
#define FCFG1_CONFIG_RF_FRONTEND_IFAMP_IB_MASK             (15 << FCFG1_CONFIG_RF_FRONTEND_IFAMP_IB_SHIFT)
#  define FCFG1_CONFIG_RF_FRONTEND_IFAMP_IB(n)             ((uint32_t)(n) << FCFG1_CONFIG_RF_FRONTEND_IFAMP_IB_SHIFT)

/* TIVA_FCFG1_CONFIG_SYNTH */

#define FCFG1_CONFIG_SYNTH_SLDO_TRIM_OUTPUT_SHIFT          (0)      /* Bits 0-5 */
#define FCFG1_CONFIG_SYNTH_SLDO_TRIM_OUTPUT_MASK           (0x3f << FCFG1_CONFIG_SYNTH_SLDO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_SYNTH_SLDO_TRIM_OUTPUT(n)           ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_SLDO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_SYNTH_LDOVCO_TRIM_OUTPUT_SHIFT        (6)      /* Bits 6-11 */
#define FCFG1_CONFIG_SYNTH_LDOVCO_TRIM_OUTPUT_MASK         (0x3f << FCFG1_CONFIG_SYNTH_LDOVCO_TRIM_OUTPUT_SHIFT)
#  define FCFG1_CONFIG_SYNTH_LDOVCO_TRIM_OUTPUT(n)         ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_LDOVCO_TRIM_OUTPUT_SHIFT)
#define FCFG1_CONFIG_SYNTH_RFC_MDM_DEMIQMC0_SHIFT          (12)      /* Bits 12-27 */
#define FCFG1_CONFIG_SYNTH_RFC_MDM_DEMIQMC0_MASK           (0xffff << FCFG1_CONFIG_SYNTH_RFC_MDM_DEMIQMC0_SHIFT)
#  define FCFG1_CONFIG_SYNTH_RFC_MDM_DEMIQMC0(n)           ((uint32_t)(n) << FCFG1_CONFIG_SYNTH_RFC_MDM_DEMIQMC0_SHIFT)
#define FCFG1_CONFIG_SYNTH_DISABLE_CORNER_CAP              (1 << 28) /* Bit 28 */

/* TIVA_FCFG1_SOC_ADC_ABS_GAIN */

#define FCFG1_SOC_ADC_ABS_GAIN_SOC_ADC_ABS_GAIN_TEMP1_SHIFT  (0)       /* Bits 0-15: SOC_ADC gain in absolute reference mode at temperature 1 (30C) */
#define FCFG1_SOC_ADC_ABS_GAIN_SOC_ADC_ABS_GAIN_TEMP1_MASK   (0xffff << FCFG1_SOC_ADC_ABS_GAIN_SOC_ADC_ABS_GAIN_TEMP1_SHIFT)
#  define FCFG1_SOC_ADC_ABS_GAIN_SOC_ADC_ABS_GAIN_TEMP1(n)   ((uint32_t)(n) << FCFG1_SOC_ADC_ABS_GAIN_SOC_ADC_ABS_GAIN_TEMP1_SHIFT)

/* TIVA_FCFG1_SOC_ADC_REL_GAIN */

#define FCFG1_SOC_ADC_REL_GAIN_SOC_ADC_REL_GAIN_TEMP1_SHIFT  (0)       /* Bits 0-15: SOC_ADC gain in relative reference mode at temperature 1 (30C) */
#define FCFG1_SOC_ADC_REL_GAIN_SOC_ADC_REL_GAIN_TEMP1_MASK   (0xffff << FCFG1_SOC_ADC_REL_GAIN_SOC_ADC_REL_GAIN_TEMP1_SHIFT)
#  define FCFG1_SOC_ADC_REL_GAIN_SOC_ADC_REL_GAIN_TEMP1(n)   ((uint32_t)(n) << FCFG1_SOC_ADC_REL_GAIN_SOC_ADC_REL_GAIN_TEMP1_SHIFT)

/* TIVA_FCFG1_SOC_ADC_OFFSET_INT */

#define FCFG1_SOC_ADC_OFFSET_INT_SOC_ADC_ABS_OFFSET_TEMP1_SHIFT  (0)      /* Bits 0-7: SOC_ADC offset in absolute reference mode at temperature 1 (30C) */
#define FCFG1_SOC_ADC_OFFSET_INT_SOC_ADC_ABS_OFFSET_TEMP1_MASK   (0xff << FCFG1_SOC_ADC_OFFSET_INT_SOC_ADC_ABS_OFFSET_TEMP1_SHIFT)
#  define FCFG1_SOC_ADC_OFFSET_INT_SOC_ADC_ABS_OFFSET_TEMP1(n)   ((uint32_t)(n) << FCFG1_SOC_ADC_OFFSET_INT_SOC_ADC_ABS_OFFSET_TEMP1_SHIFT)
#define FCFG1_SOC_ADC_OFFSET_INT_SOC_ADC_REL_OFFSET_TEMP1_SHIFT  (16)      /* Bits 16-23: SOC_ADC offset in relative reference mode at temperature 1 (30C) */
#define FCFG1_SOC_ADC_OFFSET_INT_SOC_ADC_REL_OFFSET_TEMP1_MASK   (0xff << FCFG1_SOC_ADC_OFFSET_INT_SOC_ADC_REL_OFFSET_TEMP1_SHIFT)
#  define FCFG1_SOC_ADC_OFFSET_INT_SOC_ADC_REL_OFFSET_TEMP1(n)   ((uint32_t)(n) << FCFG1_SOC_ADC_OFFSET_INT_SOC_ADC_REL_OFFSET_TEMP1_SHIFT)

/* TIVA_FCFG1_SOC_ADC_REF_TRIM_AND_OFFSET_EXT */

#define FCFG1_SOC_ADC_REF_TRIM_AND_OFFSET_EXT_SOC_ADC_REF_VOLTAGE_TRIM_TEMP1_SHIFT (0)       /* Bits 0-5 */
#define FCFG1_SOC_ADC_REF_TRIM_AND_OFFSET_EXT_SOC_ADC_REF_VOLTAGE_TRIM_TEMP1_MASK  \
  (0x3f << FCFG1_SOC_ADC_REF_TRIM_AND_OFFSET_EXT_SOC_ADC_REF_VOLTAGE_TRIM_TEMP1_SHIFT)
#  define FCFG1_SOC_ADC_REF_TRIM_AND_OFFSET_EXT_SOC_ADC_REF_VOLTAGE_TRIM_TEMP1(n) \
  ((uint32_t)(n) << FCFG1_SOC_ADC_REF_TRIM_AND_OFFSET_EXT_SOC_ADC_REF_VOLTAGE_TRIM_TEMP1_SHIFT)

/* TIVA_FCFG1_AMPCOMP_TH1 */

#define FCFG1_AMPCOMP_TH1_HPMRAMP1_TH_SHIFT                (0)       /* Bits 0-5 */
#define FCFG1_AMPCOMP_TH1_HPMRAMP1_TH_MASK                 (0x3f << FCFG1_AMPCOMP_TH1_HPMRAMP1_TH_SHIFT)
#  define FCFG1_AMPCOMP_TH1_HPMRAMP1_TH(n)                 ((uint32_t)(n) << FCFG1_AMPCOMP_TH1_HPMRAMP1_TH_SHIFT)
#define FCFG1_AMPCOMP_TH1_IBIASCAP_LPTOHP_OL_CNT_SHIFT     (6)       /* Bits 6-9:*/
#define FCFG1_AMPCOMP_TH1_IBIASCAP_LPTOHP_OL_CNT_MASK      (15 << FCFG1_AMPCOMP_TH1_IBIASCAP_LPTOHP_OL_CNT_SHIFT)
#  define FCFG1_AMPCOMP_TH1_IBIASCAP_LPTOHP_OL_CNT(n)      ((uint32_t)(n) << FCFG1_AMPCOMP_TH1_IBIASCAP_LPTOHP_OL_CNT_SHIFT)
#define FCFG1_AMPCOMP_TH1_HPMRAMP3_HTH_SHIFT               (10)      /* Bits 10-15 */
#define FCFG1_AMPCOMP_TH1_HPMRAMP3_HTH_MASK                (0x3f << FCFG1_AMPCOMP_TH1_HPMRAMP3_HTH_SHIFT)
#  define FCFG1_AMPCOMP_TH1_HPMRAMP3_HTH(n)                ((uint32_t)(n) << FCFG1_AMPCOMP_TH1_HPMRAMP3_HTH_SHIFT)
#define FCFG1_AMPCOMP_TH1_HPMRAMP3_LTH_SHIFT               (18)      /* Bits 18-23 */
#define FCFG1_AMPCOMP_TH1_HPMRAMP3_LTH_MASK                (0x3f << FCFG1_AMPCOMP_TH1_HPMRAMP3_LTH_SHIFT)
#  define FCFG1_AMPCOMP_TH1_HPMRAMP3_LTH(n)                ((uint32_t)(n) << FCFG1_AMPCOMP_TH1_HPMRAMP3_LTH_SHIFT)

/* TIVA_FCFG1_AMPCOMP_TH2 */

#define FCFG1_AMPCOMP_TH2_ADC_COMP_AMPTH_HPM_SHIFT         (2)       /* Bits 2-7 */
#define FCFG1_AMPCOMP_TH2_ADC_COMP_AMPTH_HPM_MASK          (0x3f << FCFG1_AMPCOMP_TH2_ADC_COMP_AMPTH_HPM_SHIFT)
#  define FCFG1_AMPCOMP_TH2_ADC_COMP_AMPTH_HPM(n)          ((uint32_t)(n) << FCFG1_AMPCOMP_TH2_ADC_COMP_AMPTH_HPM_SHIFT)
#define FCFG1_AMPCOMP_TH2_ADC_COMP_AMPTH_LPM_SHIFT         (10)      /* Bits 10-15 */
#define FCFG1_AMPCOMP_TH2_ADC_COMP_AMPTH_LPM_MASK          (0x3f << FCFG1_AMPCOMP_TH2_ADC_COMP_AMPTH_LPM_SHIFT)
#  define FCFG1_AMPCOMP_TH2_ADC_COMP_AMPTH_LPM(n)          ((uint32_t)(n) << FCFG1_AMPCOMP_TH2_ADC_COMP_AMPTH_LPM_SHIFT)
#define FCFG1_AMPCOMP_TH2_LPMUPDATE_HTM_SHIFT              (18)      /* Bits 18-23 */
#define FCFG1_AMPCOMP_TH2_LPMUPDATE_HTM_MASK               (0x3f << FCFG1_AMPCOMP_TH2_LPMUPDATE_HTM_SHIFT)
#  define FCFG1_AMPCOMP_TH2_LPMUPDATE_HTM(n)               ((uint32_t)(n) << FCFG1_AMPCOMP_TH2_LPMUPDATE_HTM_SHIFT)
#define FCFG1_AMPCOMP_TH2_LPMUPDATE_LTH_SHIFT              (26)      /* Bits 26-31 */
#define FCFG1_AMPCOMP_TH2_LPMUPDATE_LTH_MASK               (0x3f << FCFG1_AMPCOMP_TH2_LPMUPDATE_LTH_SHIFT)
#  define FCFG1_AMPCOMP_TH2_LPMUPDATE_LTH(n)               ((uint32_t)(n) << FCFG1_AMPCOMP_TH2_LPMUPDATE_LTH_SHIFT)

/* TIVA_FCFG1_AMPCOMP_CTRL1 */

#define FCFG1_AMPCOMP_CTRL1_IBIASCAP_HPTOLP_OL_CNT_SHIFT   (0)       /* Bits 0-3 */
#define FCFG1_AMPCOMP_CTRL1_IBIASCAP_HPTOLP_OL_CNT_MASK    (15 << FCFG1_AMPCOMP_CTRL1_IBIASCAP_HPTOLP_OL_CNT_SHIFT)
#  define FCFG1_AMPCOMP_CTRL1_IBIASCAP_HPTOLP_OL_CNT(n)    ((uint32_t)(n) << FCFG1_AMPCOMP_CTRL1_IBIASCAP_HPTOLP_OL_CNT_SHIFT)
#define FCFG1_AMPCOMP_CTRL1_CAP_STEP_SHIFT                 (4)       /* Bits 4-7 */
#define FCFG1_AMPCOMP_CTRL1_CAP_STEP_MASK                  (15 << FCFG1_AMPCOMP_CTRL1_CAP_STEP_SHIFT)
#  define FCFG1_AMPCOMP_CTRL1_CAP_STEP(n)                  ((uint32_t)(n) << FCFG1_AMPCOMP_CTRL1_CAP_STEP_SHIFT)
#define FCFG1_AMPCOMP_CTRL1_LPM_IBIAS_WAIT_CNT_FINAL_SHIFT (8)       /* Bits 8-15 */
#define FCFG1_AMPCOMP_CTRL1_LPM_IBIAS_WAIT_CNT_FINAL_MASK  (0xff << FCFG1_AMPCOMP_CTRL1_LPM_IBIAS_WAIT_CNT_FINAL_SHIFT)
#  define FCFG1_AMPCOMP_CTRL1_LPM_IBIAS_WAIT_CNT_FINAL(n)  ((uint32_t)(n) << FCFG1_AMPCOMP_CTRL1_LPM_IBIAS_WAIT_CNT_FINAL_SHIFT)
#define FCFG1_AMPCOMP_CTRL1_IBIAS_INIT_SHIFT               (16)      /* Bits 16-19 */
#define FCFG1_AMPCOMP_CTRL1_IBIAS_INIT_MASK                (15 << FCFG1_AMPCOMP_CTRL1_IBIAS_INIT_SHIFT)
#  define FCFG1_AMPCOMP_CTRL1_IBIAS_INIT(n)                ((uint32_t)(n) << FCFG1_AMPCOMP_CTRL1_IBIAS_INIT_SHIFT)
#define FCFG1_AMPCOMP_CTRL1_IBIAS_OFFSET_SHIFT             (20)      /* Bits 20-23 */
#define FCFG1_AMPCOMP_CTRL1_IBIAS_OFFSET_MASK              (15 << FCFG1_AMPCOMP_CTRL1_IBIAS_OFFSET_SHIFT)
#  define FCFG1_AMPCOMP_CTRL1_IBIAS_OFFSET(n)              ((uint32_t)(n) << FCFG1_AMPCOMP_CTRL1_IBIAS_OFFSET_SHIFT)
#define FCFG1_AMPCOMP_CTRL1_AMPCOMP_REQ_MODE               (1 << 30) /* Bit 30 */

/* TIVA_FCFG1_ANABYPASS_VALUE2 */

#define FCFG1_ANABYPASS_VALUE2_XOSC_HF_IBIASTHERM_SHIFT    (0)       /* Bits 0-13 */
#define FCFG1_ANABYPASS_VALUE2_XOSC_HF_IBIASTHERM_MASK     (0x3fff << FCFG1_ANABYPASS_VALUE2_XOSC_HF_IBIASTHERM_SHIFT)
#  define FCFG1_ANABYPASS_VALUE2_XOSC_HF_IBIASTHERM(n)     ((uint32_t)(n) << FCFG1_ANABYPASS_VALUE2_XOSC_HF_IBIASTHERM_SHIFT)

/* TIVA_FCFG1_CONFIG_MISC_ADC */

#define FCFG1_CONFIG_MISC_ADC_DACTRIM_SHIFT                (0)      /* Bits 0-5 */
#define FCFG1_CONFIG_MISC_ADC_DACTRIM_MASK                 (0x3f << FCFG1_CONFIG_MISC_ADC_DACTRIM_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_DACTRIM(n)                 ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_DACTRIM_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_QUANTCTLTHRES_SHIFT          (6)      /* Bits 6-8 */
#define FCFG1_CONFIG_MISC_ADC_QUANTCTLTHRES_MASK           (15 << FCFG1_CONFIG_MISC_ADC_QUANTCTLTHRES_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_QUANTCTLTHRES(n)           ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_QUANTCTLTHRES_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_RSSI_OFFSET_SHIFT            (9)      /* Bits 9-16 */
#define FCFG1_CONFIG_MISC_ADC_RSSI_OFFSET_MASK             (0xff << FCFG1_CONFIG_MISC_ADC_RSSI_OFFSET_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_RSSI_OFFSET(n)             ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_RSSI_OFFSET_SHIFT)
#define FCFG1_CONFIG_MISC_ADC_RSSITRIMCOMPLETE_N           (1 << 17) /* Bit 17 */
#define FCFG1_CONFIG_MISC_ADC_MIN_ALLOWED_RTRIM_SHIFT      (18)      /* Bits 18-21 */
#define FCFG1_CONFIG_MISC_ADC_MIN_ALLOWED_RTRIM_MASK       (15 << FCFG1_CONFIG_MISC_ADC_MIN_ALLOWED_RTRIM_SHIFT)
#  define FCFG1_CONFIG_MISC_ADC_MIN_ALLOWED_RTRIM(n)       ((uint32_t)(n) << FCFG1_CONFIG_MISC_ADC_MIN_ALLOWED_RTRIM_SHIFT)

/* TIVA_FCFG1_VOLT_TRIM */

#define FCFG1_VOLT_TRIM_TRIMBOD_H_SHIFT                    (0)       /* Bits 0-4 */
#define FCFG1_VOLT_TRIM_TRIMBOD_H_MASK                     (31 << FCFG1_VOLT_TRIM_TRIMBOD_H_SHIFT)
#  define FCFG1_VOLT_TRIM_TRIMBOD_H(n)                     ((uint32_t)(n) << FCFG1_VOLT_TRIM_TRIMBOD_H_SHIFT)
#define FCFG1_VOLT_TRIM_VDDR_TRIM_SLEEP_H_SHIFT            (8)       /* Bits 8-12 */
#define FCFG1_VOLT_TRIM_VDDR_TRIM_SLEEP_H_MASK             (31 << FCFG1_VOLT_TRIM_VDDR_TRIM_SLEEP_H_SHIFT)
#  define FCFG1_VOLT_TRIM_VDDR_TRIM_SLEEP_H(n)             ((uint32_t)(n) << FCFG1_VOLT_TRIM_VDDR_TRIM_SLEEP_H_SHIFT)
#define FCFG1_VOLT_TRIM_VDDR_TRIM_H_SHIFT                  (16)      /* Bits 16-20 */
#define FCFG1_VOLT_TRIM_VDDR_TRIM_H_MASK                   (31 << FCFG1_VOLT_TRIM_VDDR_TRIM_H_SHIFT)
#  define FCFG1_VOLT_TRIM_VDDR_TRIM_H(n)                   ((uint32_t)(n) << FCFG1_VOLT_TRIM_VDDR_TRIM_H_SHIFT)
#define FCFG1_VOLT_TRIM_VDDR_TRIM_HH_SHIFT                 (24)      /* Bits 24-28 */
#define FCFG1_VOLT_TRIM_VDDR_TRIM_HH_MASK                  (31 << FCFG1_VOLT_TRIM_VDDR_TRIM_HH_SHIFT)
#  define FCFG1_VOLT_TRIM_VDDR_TRIM_HH(n)                  ((uint32_t)(n) << FCFG1_VOLT_TRIM_VDDR_TRIM_HH_SHIFT)

/* TIVA_FCFG1_OSC_CONF */

#define FCFG1_OSC_CONF_HPOSC_DIV3_BYPASS                   (1 << 0)  /* Bit 0 */
#define FCFG1_OSC_CONF_HPOSC_SERIES_CAP_SHIFT              (1)       /* Bits 1-2 */
#define FCFG1_OSC_CONF_HPOSC_SERIES_CAP_MASK               (3 << FCFG1_OSC_CONF_HPOSC_SERIES_CAP_SHIFT)
#  define FCFG1_OSC_CONF_HPOSC_SERIES_CAP(n)               ((uint32_t)(n) << FCFG1_OSC_CONF_HPOSC_SERIES_CAP_SHIFT)
#define FCFG1_OSC_CONF_HPOSC_BIAS_RECHARGE_DELAY_SHIFT     (5)       /* Bits 5-6 */
#define FCFG1_OSC_CONF_HPOSC_BIAS_RECHARGE_DELAY_MASK      (3 << FCFG1_OSC_CONF_HPOSC_BIAS_RECHARGE_DELAY_SHIFT)
#  define FCFG1_OSC_CONF_HPOSC_BIAS_RECHARGE_DELAY(n)      ((uint32_t)(n) << FCFG1_OSC_CONF_HPOSC_BIAS_RECHARGE_DELAY_SHIFT)
#define FCFG1_OSC_CONF_HPOSC_FILTER_EN                     (1 << 7)  /* Bit 7 */
#define FCFG1_OSC_CONF_HPOSC_BIAS_RES_SET_SHIFT            (8)       /* Bits 8-11 */
#define FCFG1_OSC_CONF_HPOSC_BIAS_RES_SET_MASK             (15 << FCFG1_OSC_CONF_HPOSC_BIAS_RES_SET_SHIFT)
#  define FCFG1_OSC_CONF_HPOSC_BIAS_RES_SET(n)             ((uint32_t)(n) << FCFG1_OSC_CONF_HPOSC_BIAS_RES_SET_SHIFT)
#define FCFG1_OSC_CONF_HPOSC_CURRMIRR_RATIO_SHIFT          (12)      /* Bits 12-15 */
#define FCFG1_OSC_CONF_HPOSC_CURRMIRR_RATIO_MASK           (15 << FCFG1_OSC_CONF_HPOSC_CURRMIRR_RATIO_SHIFT)
#  define FCFG1_OSC_CONF_HPOSC_CURRMIRR_RATIO(n)           ((uint32_t)(n) << FCFG1_OSC_CONF_HPOSC_CURRMIRR_RATIO_SHIFT)
#define FCFG1_OSC_CONF_HPOSC_BIAS_HOLD_MODE_EN             (1 << 16) /* Bit 15 */
#define FCFG1_OSC_CONF_HPOSC_OPTION                        (1 << 17) /* Bit 17  */
#define FCFG1_OSC_CONF_XOSC_OPTION                         (1 << 18) /* Bit 18: XOSC_HF available */
#define FCFG1_OSC_CONF_XOSC_HF_FAST_START_SHIFT            (19)      /* Bits 19-20: Trim value for DDI_0_OSC:CTL1.XOSC_HF_FAST_START */
#define FCFG1_OSC_CONF_XOSC_HF_FAST_START_MASK             (3 << FCFG1_OSC_CONF_XOSC_HF_FAST_START_SHIFT)
#  define FCFG1_OSC_CONF_XOSC_HF_FAST_START(n)             ((uint32_t)(n) << FCFG1_OSC_CONF_XOSC_HF_FAST_START_SHIFT)
#define FCFG1_OSC_CONF_XOSCLF_CMIRRWR_RATIO_SHIFT          (21)      /* Bits 21-24: rim value for DDI_0_OSC:LFOSCCTL.XOSCLF_CMIRRWR_RATIO */
#define FCFG1_OSC_CONF_XOSCLF_CMIRRWR_RATIO_MASK           (15 << FCFG1_OSC_CONF_XOSCLF_CMIRRWR_RATIO_SHIFT)
#  define FCFG1_OSC_CONF_XOSCLF_CMIRRWR_RATIO(n)           ((uint32_t)(n) << FCFG1_OSC_CONF_XOSCLF_CMIRRWR_RATIO_SHIFT)
#define FCFG1_OSC_CONF_XOSCLF_REGULATOR_TRIM_SHIFT         (25)      /* Bits 25-36: Trim value for DDI_0_OSC:LFOSCCTL.XOSCLF_REGULATOR_TRIM */
#define FCFG1_OSC_CONF_XOSCLF_REGULATOR_TRIM_MASK          (3 << FCFG1_OSC_CONF_XOSCLF_REGULATOR_TRIM_SHIFT)
#  define FCFG1_OSC_CONF_XOSCLF_REGULATOR_TRIM(n)          ((uint32_t)(n) << FCFG1_OSC_CONF_XOSCLF_REGULATOR_TRIM_SHIFT)
#define FCFG1_OSC_CONF_ATESTLF_RCOSCLF_IBIAS_TRIM          (1 << 27) /* Bit 27: Trim value for DDI_0_OSC:ATESTCTL.ATESTLF_RCOSCLF_IBIAS_TRIM */
#define FCFG1_OSC_CONF_ADC_SH_MODE_EN                      (1 << 28) /* Bit 28: Trim value for DDI_0_OSC:ADCDOUBLERNANOAMPCTL.ADC_SH_MODE_EN */
#define FCFG1_OSC_CONF_ADC_SH_VBUF_EN                      (1 << 29) /* Bit 28: Trim value for DDI_0_OSC:ADCDOUBLERNANOAMPCTL.ADC_SH_VBUF_EN */

/* TIVA_FCFG1_FREQ_OFFSET */

#define FCFG1_FREQ_OFFSET_HPOSC_COMP_P2_SHIFT              (0)       /* Bits 0-7 */
#define FCFG1_FREQ_OFFSET_HPOSC_COMP_P2_MASK               (0xff << FCFG1_FREQ_OFFSET_HPOSC_COMP_P2_SHIFT)
#  define FCFG1_FREQ_OFFSET_HPOSC_COMP_P2(n)               ((uint32_t)(n) << FCFG1_FREQ_OFFSET_HPOSC_COMP_P2_SHIFT)
#define FCFG1_FREQ_OFFSET_HPOSC_COMP_P1_SHIFT              (8)      /* Bits 8-15 */
#define FCFG1_FREQ_OFFSET_HPOSC_COMP_P1_MASK               (0xff << FCFG1_FREQ_OFFSET_HPOSC_COMP_P1_SHIFT)
#  define FCFG1_FREQ_OFFSET_HPOSC_COMP_P1(n)               ((uint32_t)(n) << FCFG1_FREQ_OFFSET_HPOSC_COMP_P1_SHIFT)
#define FCFG1_FREQ_OFFSET_HPOSC_COMP_P0_SHIFT              (16)      /* Bits 16-31 */
#define FCFG1_FREQ_OFFSET_HPOSC_COMP_P0_MASK               (0xffff << FCFG1_FREQ_OFFSET_HPOSC_COMP_P0_SHIFT)
#  define FCFG1_FREQ_OFFSET_HPOSC_COMP_P0(n)               ((uint32_t)(n) << FCFG1_FREQ_OFFSET_HPOSC_COMP_P0_SHIFT)

/* TVIA_FCFG1_CAP_TRIM */

#define FCFG1_CAP_TRIM_FLUX_CAP_0P4_TRIM_SHIFT             (16)      /* Bits 16-31 */
#define FCFG1_CAP_TRIM_FLUX_CAP_0P4_TRIM_MASK              (0xffff << FCFG1_CAP_TRIM_FLUX_CAP_0P4_TRIM_SHIFT)
#  define FCFG1_CAP_TRIM_FLUX_CAP_0P4_TRIM(n)              ((uint32_t)(n) << FCFG1_CAP_TRIM_FLUX_CAP_0P4_TRIM_SHIFT)
#define FCFG1_CAP_TRIM_FLUX_CAP_0P28_TRIM_SHIFT            (16)      /* Bits 16-31 */
#define FCFG1_CAP_TRIM_FLUX_CAP_0P28_TRIM_MASK             (0xffff << FCFG1_CAP_TRIM_FLUX_CAP_0P28_TRIM_SHIFT)
#  define FCFG1_CAP_TRIM_FLUX_CAP_0P28_TRIM(n)             ((uint32_t)(n) << FCFG1_CAP_TRIM_FLUX_CAP_0P28_TRIM_SHIFT)

/* TIVA_FCFG1_MISC_OTP_DATA_1 */

#define FCFG1_MISC_OTP_DATA_1_IDAC_STEP_SHIFT                      (0)       /* Bits 0-3 */
#define FCFG1_MISC_OTP_DATA_1_IDAC_STEP_MASK                       (15 << FCFG1_MISC_OTP_DATA_1_IDAC_STEP_SHIFT)
#  define FCFG1_MISC_OTP_DATA_1_IDAC_STEP(n)                       ((uint32_t)(n) << FCFG1_MISC_OTP_DATA_1_IDAC_STEP_SHIFT)
#define FCFG1_MISC_OTP_DATA_1_LPM_IBIAS_WAIT_CNT_SHIFT             (4)       /* Bits 4-9 */
#define FCFG1_MISC_OTP_DATA_1_LPM_IBIAS_WAIT_CNT_MASK              (0x3f << FCFG1_MISC_OTP_DATA_1_LPM_IBIAS_WAIT_CNT_SHIFT)
#  define FCFG1_MISC_OTP_DATA_1_LPM_IBIAS_WAIT_CNT(n)              ((uint32_t)(n) << FCFG1_MISC_OTP_DATA_1_LPM_IBIAS_WAIT_CNT_SHIFT)
#define FCFG1_MISC_OTP_DATA_1_HPM_IBIAS_WAIT_CNT_SHIFT             (10)      /* Bits 10-19 */
#define FCFG1_MISC_OTP_DATA_1_HPM_IBIAS_WAIT_CNT_MASK              (0x3ff << FCFG1_MISC_OTP_DATA_1_HPM_IBIAS_WAIT_CNT_SHIFT)
#  define FCFG1_MISC_OTP_DATA_1_HPM_IBIAS_WAIT_CNT(n)              ((uint32_t)(n) << FCFG1_MISC_OTP_DATA_1_HPM_IBIAS_WAIT_CNT_SHIFT)
#define FCFG1_MISC_OTP_DATA_1_DBLR_LOOP_FILTER_RESET_VOLTAGE_SHIFT (20)      /* Bits 20-21 */
#define FCFG1_MISC_OTP_DATA_1_DBLR_LOOP_FILTER_RESET_VOLTAGE_MASK  (3 << FCFG1_MISC_OTP_DATA_1_DBLR_LOOP_FILTER_RESET_VOLTAGE_SHIFT)
#  define FCFG1_MISC_OTP_DATA_1_DBLR_LOOP_FILTER_RESET_VOLTAGE(n)  ((uint32_t)(n) << FCFG1_MISC_OTP_DATA_1_DBLR_LOOP_FILTER_RESET_VOLTAGE_SHIFT)
#define FCFG1_MISC_OTP_DATA_1_LP_BUF_ITRIM_SHIFT                   (22)      /* Bits 22-23 */
#define FCFG1_MISC_OTP_DATA_1_LP_BUF_ITRIM_MASK                    (3 << FCFG1_MISC_OTP_DATA_1_LP_BUF_ITRIM_SHIFT)
#  define FCFG1_MISC_OTP_DATA_1_LP_BUF_ITRIM(n)                    ((uint32_t)(n) << FCFG1_MISC_OTP_DATA_1_LP_BUF_ITRIM_SHIFT)
#define FCFG1_MISC_OTP_DATA_1_HP_BUF_ITRIM_SHIFT                   (24)      /* Bits 24-26 */
#define FCFG1_MISC_OTP_DATA_1_HP_BUF_ITRIM_MASK                    (7 << FCFG1_MISC_OTP_DATA_1_HP_BUF_ITRIM_SHIFT)
#  define FCFG1_MISC_OTP_DATA_1_HP_BUF_ITRIM(n)                    ((uint32_t)(n) << FCFG1_MISC_OTP_DATA_1_HP_BUF_ITRIM_SHIFT)
#define FCFG1_MISC_OTP_DATA_1_PEAK_DET_ITRIM_SHIFT                 (27)      /* Bits 27-28 */
#define FCFG1_MISC_OTP_DATA_1_PEAK_DET_ITRIM_MASK                  (3 << FCFG1_MISC_OTP_DATA_1_PEAK_DET_ITRIM_SHIFT)
#  define FCFG1_MISC_OTP_DATA_1_PEAK_DET_ITRIM(n)                  ((uint32_t)(n) << FCFG1_MISC_OTP_DATA_1_PEAK_DET_ITRIM_SHIFT)

/* TIVA_FCFG1_PWD_CURR_20C */

#define FCFG1_PWD_CURR_20C_BASELINE_SHIFT                  (0)      /* Bits 0-7: Worst-case baseline maximum powerdown current, in units of 0.5uA */
#define FCFG1_PWD_CURR_20C_BASELINE_MASK                   (0xff << FCFG1_PWD_CURR_20C_BASELINE_SHIFT)
#  define FCFG1_PWD_CURR_20C_BASELINE(n)                   ((uint32_t)(n) << FCFG1_PWD_CURR_20C_BASELINE_SHIFT)
#define FCFG1_PWD_CURR_20C_DELTA_XOSC_LPM_SHIFT            (8)      /* Bits 8-15: Additional maximum current, in units of 1uA, with XOSC_HF on in low-power mode */
#define FCFG1_PWD_CURR_20C_DELTA_XOSC_LPM_MASK             (0xff << FCFG1_PWD_CURR_20C_DELTA_XOSC_LPM_SHIFT)
#  define FCFG1_PWD_CURR_20C_DELTA_XOSC_LPM(n)             ((uint32_t)(n) << FCFG1_PWD_CURR_20C_DELTA_XOSC_LPM_SHIFT)
#define FCFG1_PWD_CURR_20C_DELTA_RFMEM_RET_SHIFT           (16)      /* Bits 16-23: Additional maximum current, in 1uA units, with RF memory retention */
#define FCFG1_PWD_CURR_20C_DELTA_RFMEM_RET_MASK            (0xff << FCFG1_PWD_CURR_20C_DELTA_RFMEM_RET_SHIFT)
#  define FCFG1_PWD_CURR_20C_DELTA_RFMEM_RET(n)            ((uint32_t)(n) << FCFG1_PWD_CURR_20C_DELTA_RFMEM_RET_SHIFT)
#define FCFG1_PWD_CURR_20C_DELTA_CACHE_REF_SHIFT           (24)      /* Bits 24-31: Additional maximum current, in units of 1uA, with cache retention */
#define FCFG1_PWD_CURR_20C_DELTA_CACHE_REF_MASK            (0xff << FCFG1_PWD_CURR_20C_DELTA_CACHE_REF_SHIFT)
#  define FCFG1_PWD_CURR_20C_DELTA_CACHE_REF(n)            ((uint32_t)(n) << FCFG1_PWD_CURR_20C_DELTA_CACHE_REF_SHIFT)

/* TIVA_FCFG1_PWD_CURR_35C */

#define FCFG1_PWD_CURR_35C_BASELINE_SHIFT                  (0)       /* Bits 0-7:  Worst-case baseline maximum powerdown current, in units of 0.5uA */
#define FCFG1_PWD_CURR_35C_BASELINE_MASK                   (0xff << FCFG1_PWD_CURR_35C_BASELINE_SHIFT)
#  define FCFG1_PWD_CURR_35C_BASELINE(n)                   ((uint32_t)(n) << FCFG1_PWD_CURR_35C_BASELINE_SHIFT)
#define FCFG1_PWD_CURR_35C_DELTA_XOSC_LPM_SHIFT            (8)       /* Bits 8-15: Additional maximum current, in units of 1uA, with XOSC_HF on in low-power mode */
#define FCFG1_PWD_CURR_35C_DELTA_XOSC_LPM_MASK             (0xff << FCFG1_PWD_CURR_35C_DELTA_XOSC_LPM_SHIFT)
#  define FCFG1_PWD_CURR_35C_DELTA_XOSC_LPM(n)             ((uint32_t)(n) << FCFG1_PWD_CURR_35C_DELTA_XOSC_LPM_SHIFT)
#define FCFG1_PWD_CURR_35C_DELTA_RFMEM_RET_SHIFT           (16)      /* Bits 16-23: Additional maximum current, in 1uA units, with RF memory retention */
#define FCFG1_PWD_CURR_35C_DELTA_RFMEM_RET_MASK            (0xff << FCFG1_PWD_CURR_35C_DELTA_RFMEM_RET_SHIFT)
#  define FCFG1_PWD_CURR_35C_DELTA_RFMEM_RET(n)            ((uint32_t)(n) << FCFG1_PWD_CURR_35C_DELTA_RFMEM_RET_SHIFT)
#define FCFG1_PWD_CURR_35C_DELTA_CACHE_REF_SHIFT           (24)      /* Bits 24-31: Additional maximum current, in units of 1uA, with cache retention */
#define FCFG1_PWD_CURR_35C_DELTA_CACHE_REF_MASK            (0xff << FCFG1_PWD_CURR_35C_DELTA_CACHE_REF_SHIFT)
#  define FCFG1_PWD_CURR_35C_DELTA_CACHE_REF(n)            ((uint32_t)(n) << FCFG1_PWD_CURR_35C_DELTA_CACHE_REF_SHIFT)

/* TIVA_FCFG1_PWD_CURR_50C */

#define FCFG1_PWD_CURR_50C_BASELINE_SHIFT                  (0)       /* Bits 0-7: Worst-case baseline maximum powerdown current, in units of 0.5uA */
#define FCFG1_PWD_CURR_50C_BASELINE_MASK                   (0xff << FCFG1_PWD_CURR_50C_BASELINE_SHIFT)
#  define FCFG1_PWD_CURR_50C_BASELINE(n)                   ((uint32_t)(n) << FCFG1_PWD_CURR_50C_BASELINE_SHIFT)
#define FCFG1_PWD_CURR_50C_DELTA_XOSC_LPM_SHIFT            (8)       /* Bits 8-15: Additional maximum current, in units of 1uA, with XOSC_HF on in low-power mode */
#define FCFG1_PWD_CURR_50C_DELTA_XOSC_LPM_MASK             (0xff << FCFG1_PWD_CURR_50C_DELTA_XOSC_LPM_SHIFT)
#  define FCFG1_PWD_CURR_50C_DELTA_XOSC_LPM(n)             ((uint32_t)(n) << FCFG1_PWD_CURR_50C_DELTA_XOSC_LPM_SHIFT)
#define FCFG1_PWD_CURR_50C_DELTA_RFMEM_RET_SHIFT           (16)      /* Bits 16-23: Additional maximum current, in 1uA units, with RF memory retention */
#define FCFG1_PWD_CURR_50C_DELTA_RFMEM_RET_MASK            (0xff << FCFG1_PWD_CURR_50C_DELTA_RFMEM_RET_SHIFT)
#  define FCFG1_PWD_CURR_50C_DELTA_RFMEM_RET(n)            ((uint32_t)(n) << FCFG1_PWD_CURR_50C_DELTA_RFMEM_RET_SHIFT)
#define FCFG1_PWD_CURR_50C_DELTA_CACHE_REF_SHIFT           (24)      /* Bits 24-31: Additional maximum current, in units of 1uA, with cache retention */
#define FCFG1_PWD_CURR_50C_DELTA_CACHE_REF_MASK            (0xff << FCFG1_PWD_CURR_50C_DELTA_CACHE_REF_SHIFT)
#  define FCFG1_PWD_CURR_50C_DELTA_CACHE_REF(n)            ((uint32_t)(n) << FCFG1_PWD_CURR_50C_DELTA_CACHE_REF_SHIFT)

/* TIVA_FCFG1_PWD_CURR_65C */

#define FCFG1_PWD_CURR_65C_BASELINE_SHIFT                  (0)       /* Bits 0-7: Worst-case baseline maximum powerdown current, in units of 0.5uA */
#define FCFG1_PWD_CURR_65C_BASELINE_MASK                   (0xff << FCFG1_PWD_CURR_65C_BASELINE_SHIFT)
#  define FCFG1_PWD_CURR_65C_BASELINE(n)                   ((uint32_t)(n) << FCFG1_PWD_CURR_65C_BASELINE_SHIFT)
#define FCFG1_PWD_CURR_65C_DELTA_XOSC_LPM_SHIFT            (8)       /* Bits 8-15: Additional maximum current, in units of 1uA, with XOSC_HF on in low-power mode */
#define FCFG1_PWD_CURR_65C_DELTA_XOSC_LPM_MASK             (0xff << FCFG1_PWD_CURR_65C_DELTA_XOSC_LPM_SHIFT)
#  define FCFG1_PWD_CURR_65C_DELTA_XOSC_LPM(n)             ((uint32_t)(n) << FCFG1_PWD_CURR_65C_DELTA_XOSC_LPM_SHIFT)
#define FCFG1_PWD_CURR_65C_DELTA_RFMEM_RET_SHIFT           (16)      /* Bits 16-23: Additional maximum current, in 1uA units, with RF memory retention */
#define FCFG1_PWD_CURR_65C_DELTA_RFMEM_RET_MASK            (0xff << FCFG1_PWD_CURR_65C_DELTA_RFMEM_RET_SHIFT)
#  define FCFG1_PWD_CURR_65C_DELTA_RFMEM_RET(n)            ((uint32_t)(n) << FCFG1_PWD_CURR_65C_DELTA_RFMEM_RET_SHIFT)
#define FCFG1_PWD_CURR_65C_DELTA_CACHE_REF_SHIFT           (24)      /* Bits 24-31: Additional maximum current, in units of 1uA, with cache retention */
#define FCFG1_PWD_CURR_65C_DELTA_CACHE_REF_MASK            (0xff << FCFG1_PWD_CURR_65C_DELTA_CACHE_REF_SHIFT)
#  define FCFG1_PWD_CURR_65C_DELTA_CACHE_REF(n)            ((uint32_t)(n) << FCFG1_PWD_CURR_65C_DELTA_CACHE_REF_SHIFT)

/* TIVA_FCFG1_PWD_CURR_80C */

#define FCFG1_PWD_CURR_80C_BASELINE_SHIFT                  (0)       /* Bits 0-7: Worst-case baseline maximum powerdown current, in units of 0.5uA */
#define FCFG1_PWD_CURR_80C_BASELINE_MASK                   (0xff << FCFG1_PWD_CURR_80C_BASELINE_SHIFT)
#  define FCFG1_PWD_CURR_80C_BASELINE(n)                   ((uint32_t)(n) << FCFG1_PWD_CURR_80C_BASELINE_SHIFT)
#define FCFG1_PWD_CURR_80C_DELTA_XOSC_LPM_SHIFT            (8)       /* Bits 8-15: Additional maximum current, in units of 1uA, with XOSC_HF on in low-power mode */
#define FCFG1_PWD_CURR_80C_DELTA_XOSC_LPM_MASK             (0xff << FCFG1_PWD_CURR_80C_DELTA_XOSC_LPM_SHIFT)
#  define FCFG1_PWD_CURR_80C_DELTA_XOSC_LPM(n)             ((uint32_t)(n) << FCFG1_PWD_CURR_80C_DELTA_XOSC_LPM_SHIFT)
#define FCFG1_PWD_CURR_80C_DELTA_RFMEM_RET_SHIFT           (16)      /* Bits nn-nn: Additional maximum current, in 1uA units, with RF memory retention */
#define FCFG1_PWD_CURR_80C_DELTA_RFMEM_RET_MASK            (0xff << FCFG1_PWD_CURR_80C_DELTA_RFMEM_RET_SHIFT)
#  define FCFG1_PWD_CURR_80C_DELTA_RFMEM_RET(n)            ((uint32_t)(n) << FCFG1_PWD_CURR_80C_DELTA_RFMEM_RET_SHIFT)
#define FCFG1_PWD_CURR_80C_DELTA_CACHE_REF_SHIFT           (24)      /* Bits 24-31: Additional maximum current, in units of 1uA, with cache retention */
#define FCFG1_PWD_CURR_80C_DELTA_CACHE_REF_MASK            (0xff << FCFG1_PWD_CURR_80C_DELTA_CACHE_REF_SHIFT)
#  define FCFG1_PWD_CURR_80C_DELTA_CACHE_REF(n)            ((uint32_t)(n) << FCFG1_PWD_CURR_80C_DELTA_CACHE_REF_SHIFT)

/* TIVA_FCFG1_PWD_CURR_95C */

#define FCFG1_PWD_CURR_95C_BASELINE_SHIFT                  (0)       /* Bits 0-7: Worst-case baseline maximum powerdown current, in units of 0.5uA */
#define FCFG1_PWD_CURR_95C_BASELINE_MASK                   (0xff << FCFG1_PWD_CURR_95C_BASELINE_SHIFT)
#  define FCFG1_PWD_CURR_95C_BASELINE(n)                   ((uint32_t)(n) << FCFG1_PWD_CURR_95C_BASELINE_SHIFT)
#define FCFG1_PWD_CURR_95C_DELTA_XOSC_LPM_SHIFT            (8)       /* Bits 8-15: Additional maximum current, in units of 1uA, with XOSC_HF on in low-power mode */
#define FCFG1_PWD_CURR_95C_DELTA_XOSC_LPM_MASK             (0xff << FCFG1_PWD_CURR_95C_DELTA_XOSC_LPM_SHIFT)
#  define FCFG1_PWD_CURR_95C_DELTA_XOSC_LPM(n)             ((uint32_t)(n) << FCFG1_PWD_CURR_95C_DELTA_XOSC_LPM_SHIFT)
#define FCFG1_PWD_CURR_95C_DELTA_RFMEM_RET_SHIFT           (16)      /* Bits 16-23: Additional maximum current, in 1uA units, with RF memory retention */
#define FCFG1_PWD_CURR_95C_DELTA_RFMEM_RET_MASK            (0xff << FCFG1_PWD_CURR_95C_DELTA_RFMEM_RET_SHIFT)
#  define FCFG1_PWD_CURR_95C_DELTA_RFMEM_RET(n)            ((uint32_t)(n) << FCFG1_PWD_CURR_95C_DELTA_RFMEM_RET_SHIFT)
#define FCFG1_PWD_CURR_95C_DELTA_CACHE_REF_SHIFT           (24)      /* Bits 24-31: Additional maximum current, in units of 1uA, with cache retention */
#define FCFG1_PWD_CURR_95C_DELTA_CACHE_REF_MASK            (0xff << FCFG1_PWD_CURR_95C_DELTA_CACHE_REF_SHIFT)
#  define FCFG1_PWD_CURR_95C_DELTA_CACHE_REF(n)            ((uint32_t)(n) << FCFG1_PWD_CURR_95C_DELTA_CACHE_REF_SHIFT)

/* TIVA_FCFG1_PWD_CURR_110C */

#define FCFG1_PWD_CURR_110C_BASELINE_SHIFT                 (0)       /* Bits 0-7: Worst-case baseline maximum powerdown current, in units of 0.5uA */
#define FCFG1_PWD_CURR_110C_BASELINE_MASK                  (0xff << FCFG1_PWD_CURR_110C_BASELINE_SHIFT)
#  define FCFG1_PWD_CURR_110C_BASELINE(n)                  ((uint32_t)(n) << FCFG1_PWD_CURR_110C_BASELINE_SHIFT)
#define FCFG1_PWD_CURR_110C_DELTA_XOSC_LPM_SHIFT           (8)       /* Bits 8-15: Additional maximum current, in units of 1uA, with XOSC_HF on in low-power mode */
#define FCFG1_PWD_CURR_110C_DELTA_XOSC_LPM_MASK            (0xff << FCFG1_PWD_CURR_110C_DELTA_XOSC_LPM_SHIFT)
#  define FCFG1_PWD_CURR_110C_DELTA_XOSC_LPM(n)            ((uint32_t)(n) << FCFG1_PWD_CURR_110C_DELTA_XOSC_LPM_SHIFT)
#define FCFG1_PWD_CURR_110C_DELTA_RFMEM_RET_SHIFT          (16)      /* Bits 16-23: Additional maximum current, in 1uA units, with RF memory retention */
#define FCFG1_PWD_CURR_110C_DELTA_RFMEM_RET_MASK           (0xff << FCFG1_PWD_CURR_110C_DELTA_RFMEM_RET_SHIFT)
#  define FCFG1_PWD_CURR_110C_DELTA_RFMEM_RET(n)           ((uint32_t)(n) << FCFG1_PWD_CURR_110C_DELTA_RFMEM_RET_SHIFT)
#define FCFG1_PWD_CURR_110C_DELTA_CACHE_REF_SHIFT          (24)      /* Bits 24-31: Additional maximum current, in units of 1uA, with cache retention */
#define FCFG1_PWD_CURR_110C_DELTA_CACHE_REF_MASK           (0xff << FCFG1_PWD_CURR_110C_DELTA_CACHE_REF_SHIFT)
#  define FCFG1_PWD_CURR_110C_DELTA_CACHE_REF(n)           ((uint32_t)(n) << FCFG1_PWD_CURR_110C_DELTA_CACHE_REF_SHIFT)

/* TIVA_FCFG1_PWD_CURR_125C */

#define FCFG1_PWD_CURR_125C_BASELINE_SHIFT                 (0)       /* Bits 0-7: Worst-case baseline maximum powerdown current, in units of 0.5uA */
#define FCFG1_PWD_CURR_125C_BASELINE_MASK                  (0xff << FCFG1_PWD_CURR_125C_BASELINE_SHIFT)
#  define FCFG1_PWD_CURR_125C_BASELINE(n)                  ((uint32_t)(n) << FCFG1_PWD_CURR_125C_BASELINE_SHIFT)
#define FCFG1_PWD_CURR_125C_DELTA_XOSC_LPM_SHIFT           (8)       /* Bits 8-15: Additional maximum current, in units of 1uA, with XOSC_HF on in low-power mode */
#define FCFG1_PWD_CURR_125C_DELTA_XOSC_LPM_MASK            (0xff << FCFG1_PWD_CURR_125C_DELTA_XOSC_LPM_SHIFT)
#  define FCFG1_PWD_CURR_125C_DELTA_XOSC_LPM(n)            ((uint32_t)(n) << FCFG1_PWD_CURR_125C_DELTA_XOSC_LPM_SHIFT)
#define FCFG1_PWD_CURR_125C_DELTA_RFMEM_RET_SHIFT          (16)      /* Bits 16-23: Additional maximum current, in 1uA units, with RF memory retention */
#define FCFG1_PWD_CURR_125C_DELTA_RFMEM_RET_MASK           (0xff << FCFG1_PWD_CURR_125C_DELTA_RFMEM_RET_SHIFT)
#  define FCFG1_PWD_CURR_125C_DELTA_RFMEM_RET(n)           ((uint32_t)(n) << FCFG1_PWD_CURR_125C_DELTA_RFMEM_RET_SHIFT)
#define FCFG1_PWD_CURR_125C_DELTA_CACHE_REF_SHIFT          (24)      /* Bits 24-31: Additional maximum current, in units of 1uA, with cache retention */
#define FCFG1_PWD_CURR_125C_DELTA_CACHE_REF_MASK           (0xff << FCFG1_PWD_CURR_125C_DELTA_CACHE_REF_SHIFT)
#  define FCFG1_PWD_CURR_125C_DELTA_CACHE_REF(n)           ((uint32_t)(n) << FCFG1_PWD_CURR_125C_DELTA_CACHE_REF_SHIFT)

#endif /* __ARCH_ARM_SRC_TIVA_HARDWARE_CC13X0_CC13X0_FCFG1_H */
