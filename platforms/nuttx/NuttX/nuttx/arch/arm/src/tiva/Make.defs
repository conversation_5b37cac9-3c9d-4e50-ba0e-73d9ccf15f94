############################################################################
# arch/arm/src/tiva/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

include armv7-m/Make.defs

ifneq ($(CONFIG_ARCH_IDLE_CUSTOM),y)
  CHIP_CSRCS += tiva_idle.c
endif

CHIP_CSRCS += tiva_allocateheap.c tiva_irq.c tiva_lowputc.c tiva_serial.c
CHIP_CSRCS += tiva_ssi.c

ifeq ($(CONFIG_ARCH_CHIP_LM3S),y)
  CHIP_CSRCS += lmxx_tm4c_start.c lm3s_gpio.c lmxx_tm4c_gpioirq.c
  CHIP_CSRCS += lm4xx_tm3c_sysctrl.c
else ifeq ($(CONFIG_ARCH_CHIP_LM4F),y)
  CHIP_CSRCS += lmxx_tm4c_start.c lm4f_gpio.c lmxx_tm4c_gpioirq.c
  CHIP_CSRCS += lm4xx_tm3c_sysctrl.c
else ifeq ($(CONFIG_ARCH_CHIP_TM4C),y)
  CHIP_CSRCS += lmxx_tm4c_start.c tm4c_gpio.c lmxx_tm4c_gpioirq.c
ifeq ($(CONFIG_ARCH_CHIP_TM4C129),y)
  CHIP_CSRCS += tm4c129_sysctrl.c
else
  CHIP_CSRCS += lm4xx_tm3c_sysctrl.c
endif
else ifeq ($(CONFIG_ARCH_CHIP_CC13X0),y)
  CHIP_CSRCS += cc13xx_start.c cc13xx_prcm.c cc13xx_chipinfo.c cc13xx_gpio.c
  CHIP_CSRCS += cc13xx_gpioirq.c cc13xx_enableclks.c cc13xx_enablepwr.c
  CHIP_CSRCS += cc13x0_trim.c cc13x0_rom.c
else ifeq ($(CONFIG_ARCH_CHIP_CC13X2),y)
  CHIP_CSRCS += cc13xx_start.c cc13xx_prcm.c cc13xx_chipinfo.c cc13xx_gpio.c
  CHIP_CSRCS += cc13xx_gpioirq.c cc13xx_enableclks.c cc13xx_enablepwr.c
  CHIP_CSRCS += cc13x2_aux_sysif.c
ifeq ($(CONFIG_ARCH_CHIP_CC13XX_V1),y)
  CHIP_CSRCS += cc13x2_v1_trim.c cc13x2_cc26x2_v1_rom.c
else
  CHIP_CSRCS += cc13x2_v2_trim.c
endif
endif

ifeq ($(CONFIG_DEBUG_GPIO_INFO),y)
  CHIP_CSRCS += tiva_dumpgpio.c
endif

ifneq ($(CONFIG_SCHED_TICKLESS),y)
  CHIP_CSRCS += tiva_timerisr.c
endif

ifeq ($(CONFIG_BUILD_PROTECTED),y)
  CHIP_CSRCS += tiva_userspace.c tiva_mpuinit.c
endif

ifeq ($(CONFIG_TIVA_I2C),y)
  CHIP_CSRCS += tiva_i2c.c
endif

ifeq ($(CONFIG_TIVA_PWM),y)
  CHIP_CSRCS += tiva_pwm.c
endif

ifeq ($(CONFIG_TIVA_QEI),y)
  CHIP_CSRCS += tiva_qencoder.c
endif

ifeq ($(CONFIG_TIVA_TIMER),y)
  CHIP_CSRCS += tiva_timerlib.c
ifeq ($(CONFIG_TIVA_TIMER32_PERIODIC),y)
  CHIP_CSRCS += tiva_timerlow32.c
endif
endif

ifeq ($(CONFIG_TIVA_ADC),y)
  CHIP_CSRCS += tiva_adclow.c
  CHIP_CSRCS += tiva_adclib.c
endif

ifeq ($(CONFIG_TIVA_CAN),y)
  CHIP_CSRCS += tiva_can.c
endif

ifeq ($(CONFIG_TIVA_ETHERNET),y)
ifeq ($(CONFIG_ARCH_CHIP_LM3S),y)
  CHIP_CSRCS += lm3s_ethernet.c
endif
ifeq ($(CONFIG_ARCH_CHIP_TM4C),y)
  CHIP_CSRCS += tm4c_ethernet.c
endif
endif

ifeq ($(CONFIG_TIVA_FLASH),y)
  CHIP_CSRCS += tiva_flash.c
endif

ifeq ($(CONFIG_TIVA_EEPROM),y)
  CHIP_CSRCS += tiva_eeprom.c
endif

ifeq ($(CONFIG_TIVA_HCIUART),y)
CHIP_CSRCS += tiva_hciuart.c
endif

# Paths to source files

VPATH += chip/common

ifeq ($(CONFIG_ARCH_CHIP_LM),y)
VPATH += chip/lm
else ifeq ($(CONFIG_ARCH_CHIP_TM4C),y)
VPATH += chip/tm4c
else ifeq ($(CONFIG_ARCH_CHIP_CC13X0),y)
VPATH += chip/cc13xx
else ifeq ($(CONFIG_ARCH_CHIP_CC13X2),y)
VPATH += chip/cc13xx
endif
