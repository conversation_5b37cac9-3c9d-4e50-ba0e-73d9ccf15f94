############################################################################
# arch/arm/src/lpc54xx/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

include armv7-m/Make.defs

CHIP_CSRCS  = lpc54_start.c lpc54_clockconfig.c lpc54_irq.c lpc54_clrpend.c
CHIP_CSRCS += lpc54_allocateheap.c lpc54_lowputc.c lpc54_gpio.c lpc54_reset.c

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += lpc54_timerisr.c
else
CHIP_CSRCS += lpc54_tickless.c
endif

ifeq ($(CONFIG_BUILD_PROTECTED),y)
CHIP_CSRCS += lpc54_userspace.c lpc54_mpuinit.c
endif

ifeq ($(CONFIG_LPC54_DMA),y)
CHIP_CSRCS += lpc54_dma.c
endif

ifneq ($(CONFIG_ARCH_IDLE_CUSTOM),y)
CHIP_CSRCS += lpc54_idle.c
endif

ifeq ($(CONFIG_LPC54_GPIOIRQ),y)
CHIP_CSRCS += lpc54_gpioirq.c
endif

ifeq ($(CONFIG_RTC),y)
CHIP_CSRCS += lpc54_rtc.c
ifeq ($(CONFIG_RTC_DRIVER),y)
CHIP_CSRCS += lpc54_rtc_lowerhalf.c
endif
endif

ifeq ($(CONFIG_LPC54_WWDT),y)
CHIP_CSRCS += lpc54_wwdt.c
endif

ifeq ($(CONFIG_LPC54_RNG),y)
CHIP_CSRCS += lpc54_rng.c
endif

ifeq ($(CONFIG_LPC54_HAVE_USART),y)
CHIP_CSRCS += lpc54_serial.c
endif

ifeq ($(CONFIG_LPC54_HAVE_I2C_MASTER),y)
CHIP_CSRCS += lpc54_i2c_master.c
endif

ifeq ($(CONFIG_LPC54_HAVE_SPI_MASTER),y)
CHIP_CSRCS += lpc54_spi_master.c
endif

ifeq ($(CONFIG_LPC54_EMC),y)
CHIP_CSRCS += lpc54_emc.c
endif

ifeq ($(CONFIG_LPC54_ETHERNET),y)
CHIP_CSRCS += lpc54_ethernet.c
endif

ifeq ($(CONFIG_LPC54_LCD),y)
CHIP_CSRCS += lpc54_lcd.c
endif

ifeq ($(CONFIG_LPC54_SDMMC),y)
CHIP_CSRCS += lpc54_sdmmc.c
endif
