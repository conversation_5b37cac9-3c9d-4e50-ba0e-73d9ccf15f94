/****************************************************************************
 * arch/arm/src/lpc54xx/hardware/lpc546x_pinmux.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_LPC54XX_HARDWARE_LPC546X_PINMUX_H
#define __ARCH_ARM_SRC_LPC54XX_HARDWARE_LPC546X_PINMUX_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Alternate Pin Functions.
 *
 * Alternative pin selections are provided with a numeric suffix like _1, _2,
 * etc. Drivers, however, will use the pin selection without the numeric
 * suffix. Additional definitions are required in the board.h file.
 * For example, if CAN0 RX connects vis P0.4 on some board, then the
 * following definitions should appear in the board.h header file for that
 * board:
 *
 * #define GPIO_CAN0_RD GPIO_CAN0_RD_1
 *
 * The driver will then automatically configure P0.4 as the CAN0 RX pin.
 *
 * All pins are Type D unless otherwise noted.
 */

/* WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific GPIO options such as
 * frequency, open-drain/push-pull, and pull-up/down!  Just the basics are
 * defined for most pins in this file.
 */

/* Analog-to-Digital Conversion (ADC) */

#define GPIO_ADC0_0                  (GPIO_INPUT | GPIO_MODE_ANALOG | GPIO_PORT0 | GPIO_PIN10) /* Type A */
#define GPIO_ADC0_1                  (GPIO_INPUT | GPIO_MODE_ANALOG | GPIO_PORT0 | GPIO_PIN11) /* Type A */
#define GPIO_ADC0_2                  (GPIO_INPUT | GPIO_MODE_ANALOG | GPIO_PORT0 | GPIO_PIN12) /* Type A */
#define GPIO_ADC0_3                  (GPIO_INPUT | GPIO_MODE_ANALOG | GPIO_PORT0 | GPIO_PIN15) /* Type A */
#define GPIO_ADC0_4                  (GPIO_INPUT | GPIO_MODE_ANALOG | GPIO_PORT0 | GPIO_PIN16) /* Type A */
#define GPIO_ADC0_5                  (GPIO_INPUT | GPIO_MODE_ANALOG | GPIO_PORT0 | GPIO_PIN31) /* Type A */
#define GPIO_ADC0_6                  (GPIO_INPUT | GPIO_MODE_ANALOG | GPIO_PORT1 | GPIO_PIN0)  /* Type A */
#define GPIO_ADC0_7                  (GPIO_INPUT | GPIO_MODE_ANALOG | GPIO_PORT2 | GPIO_PIN0)  /* Type A */
#define GPIO_ADC0_8                  (GPIO_INPUT | GPIO_MODE_ANALOG | GPIO_PORT2 | GPIO_PIN1)  /* Type A */
#define GPIO_ADC0_9                  (GPIO_INPUT | GPIO_MODE_ANALOG | GPIO_PORT3 | GPIO_PIN21) /* Type A */
#define GPIO_ADC0_10                 (GPIO_INPUT | GPIO_MODE_ANALOG | GPIO_PORT3 | GPIO_PIN22) /* Type A */
#define GPIO_ADC0_11                 (GPIO_INPUT | GPIO_MODE_ANALOG | GPIO_PORT0 | GPIO_PIN23) /* Type A */

/* Controller Area Network (CAN) */

#define GPIO_CAN0_RD_1               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN4)
#define GPIO_CAN0_RD_2               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN3)
#define GPIO_CAN0_RD_3               (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN19)
#define GPIO_CAN0_TD_1               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN5)
#define GPIO_CAN0_TD_2               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN2)
#define GPIO_CAN0_TD_3               (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN18)

#define GPIO_CAN1_RD_1               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN0)
#define GPIO_CAN1_RD_2               (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN18)
#define GPIO_CAN1_RD_3               (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN18)
#define GPIO_CAN1_TD_1               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN1)
#define GPIO_CAN1_TD_2               (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN17)
#define GPIO_CAN1_TD_3               (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN17)

/* CLKOUT */

#define GPIO_CLKOUT_1                (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN16) /* Type A */
#define GPIO_CLKOUT_2                (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN26)
#define GPIO_CLKOUT_3                (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN20)
#define GPIO_CLKOUT_4                (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN27)
#define GPIO_CLKOUT_5                (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN29)
#define GPIO_CLKOUT_6                (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN12)

/* Standard counter/timer (CTIMER) */

#define GPIO_CTIMER0_CAP0_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN1)
#define GPIO_CTIMER0_CAP0_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN13) /* Type D+I */
#define GPIO_CTIMER0_CAP0_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN6)
#define GPIO_CTIMER0_CAP1_1          (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN2)
#define GPIO_CTIMER0_CAP1_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN14) /* Type D+I */
#define GPIO_CTIMER0_CAP1_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN7)
#define GPIO_CTIMER0_CAP2_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN0)  /* Type A */
#define GPIO_CTIMER0_CAP2_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN28)
#define GPIO_CTIMER0_CAP2_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN9)
#define GPIO_CTIMER0_CAP3_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN1)
#define GPIO_CTIMER0_CAP3_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN26)
#define GPIO_CTIMER0_CAP3_3          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN3)
#define GPIO_CTIMER0_MAT0_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN0)
#define GPIO_CTIMER0_MAT0_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN30)
#define GPIO_CTIMER0_MAT0_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_CTIMER0_MAT1_1          (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN3)
#define GPIO_CTIMER0_MAT1_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN31) /* Type A */
#define GPIO_CTIMER0_MAT1_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_CTIMER0_MAT2_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN19)
#define GPIO_CTIMER0_MAT2_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN31)
#define GPIO_CTIMER0_MAT2_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN14)
#define GPIO_CTIMER0_MAT3_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN2)
#define GPIO_CTIMER0_MAT3_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN27)
#define GPIO_CTIMER0_MAT3_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN15)

#define GPIO_CTIMER1_CAP0_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN16) /* Type A */
#define GPIO_CTIMER1_CAP0_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN9)
#define GPIO_CTIMER1_CAP0_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN0)  /* Type A */
#define GPIO_CTIMER1_CAP0_4          (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN27)
#define GPIO_CTIMER1_CAP1_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN11)
#define GPIO_CTIMER1_CAP1_2          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN17)
#define GPIO_CTIMER1_CAP1_3          (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN28)
#define GPIO_CTIMER1_CAP2_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN13)
#define GPIO_CTIMER1_CAP2_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN17)
#define GPIO_CTIMER1_CAP2_3          (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN29)
#define GPIO_CTIMER1_CAP3_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN15)
#define GPIO_CTIMER1_CAP3_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN18)
#define GPIO_CTIMER1_CAP3_3          (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN30)
#define GPIO_CTIMER1_MAT0_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN18)
#define GPIO_CTIMER1_MAT0_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN10)
#define GPIO_CTIMER1_MAT0_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN1)  /* Type A */
#define GPIO_CTIMER1_MAT0_4          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN0)
#define GPIO_CTIMER1_MAT0_5          (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN23)
#define GPIO_CTIMER1_MAT1_1          (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN20)
#define GPIO_CTIMER1_MAT1_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN12)
#define GPIO_CTIMER1_MAT1_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_CTIMER1_MAT1_4          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN1)
#define GPIO_CTIMER1_MAT1_5          (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN24)
#define GPIO_CTIMER1_MAT2_1          (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN23) /* Type A */
#define GPIO_CTIMER1_MAT2_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN14)
#define GPIO_CTIMER1_MAT2_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_CTIMER1_MAT2_4          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN2)
#define GPIO_CTIMER1_MAT2_5          (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN25)
#define GPIO_CTIMER1_MAT3_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN16)
#define GPIO_CTIMER1_MAT3_2          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN16)
#define GPIO_CTIMER1_MAT3_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN22)
#define GPIO_CTIMER1_MAT3_4          (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN26)

#define GPIO_CTIMER2_CAP0_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN24)
#define GPIO_CTIMER2_CAP0_2          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN22)
#define GPIO_CTIMER2_CAP1_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN25)
#define GPIO_CTIMER2_CAP1_2          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN26)
#define GPIO_CTIMER2_CAP2_1          (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN10) /* Type A */
#define GPIO_CTIMER2_CAP2_2          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN28)
#define GPIO_CTIMER2_CAP3_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN28)
#define GPIO_CTIMER2_CAP3_2          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN29)
#define GPIO_CTIMER2_MAT0_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN10) /* Type A */
#define GPIO_CTIMER2_MAT0_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_CTIMER2_MAT0_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_CTIMER2_MAT1_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN4)
#define GPIO_CTIMER2_MAT1_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN6)
#define GPIO_CTIMER2_MAT1_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_CTIMER2_MAT2_1          (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN11) /* Type A */
#define GPIO_CTIMER2_MAT2_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN7)
#define GPIO_CTIMER2_MAT2_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN30)
#define GPIO_CTIMER2_MAT3_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN29)
#define GPIO_CTIMER2_MAT3_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN22)
#define GPIO_CTIMER2_MAT3_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN21)

#define GPIO_CTIMER3_CAP0_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN4)
#define GPIO_CTIMER3_CAP0_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN12)
#define GPIO_CTIMER3_CAP0_3          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN2)
#define GPIO_CTIMER3_CAP1_1          (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN6)
#define GPIO_CTIMER3_CAP1_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN13)
#define GPIO_CTIMER3_CAP1_3          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN3)
#define GPIO_CTIMER3_CAP2_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN26)
#define GPIO_CTIMER3_CAP2_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN20)
#define GPIO_CTIMER3_CAP2_3          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN4)
#define GPIO_CTIMER3_CAP3_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN20)
#define GPIO_CTIMER3_CAP3_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN22)
#define GPIO_CTIMER3_CAP3_3          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN5)
#define GPIO_CTIMER3_MAT0_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN5)
#define GPIO_CTIMER3_MAT0_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN10)
#define GPIO_CTIMER3_MAT0_3          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN30)
#define GPIO_CTIMER3_MAT0_4          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN18)
#define GPIO_CTIMER3_MAT1_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN19)
#define GPIO_CTIMER3_MAT1_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN14)
#define GPIO_CTIMER3_MAT1_3          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN31)
#define GPIO_CTIMER3_MAT1_4          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN19)
#define GPIO_CTIMER3_MAT2_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN27)
#define GPIO_CTIMER3_MAT2_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN21)
#define GPIO_CTIMER3_MAT2_3          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN0)
#define GPIO_CTIMER3_MAT2_4          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN20)
#define GPIO_CTIMER3_MAT3_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN21)
#define GPIO_CTIMER3_MAT3_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN23) /* Type A */
#define GPIO_CTIMER3_MAT3_3          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN1)
#define GPIO_CTIMER3_MAT3_4          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN21)

#define GPIO_CTIMER4_CAP0_1          (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN24) /* Type D+I */
#define GPIO_CTIMER4_CAP0_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN15) /* Type A */
#define GPIO_CTIMER4_CAP0_3          (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN20)
#define GPIO_CTIMER4_CAP1_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN0)
#define GPIO_CTIMER4_CAP1_2          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN4)
#define GPIO_CTIMER4_CAP2_1          (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN25)
#define GPIO_CTIMER4_CAP2_2          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN7)
#define GPIO_CTIMER4_CAP2_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN19)
#define GPIO_CTIMER4_CAP3_1          (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN7)
#define GPIO_CTIMER4_CAP3_2          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN8)
#define GPIO_CTIMER4_CAP3_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN20)
#define GPIO_CTIMER4_MAT0_1          (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN13)
#define GPIO_CTIMER4_MAT0_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN6)
#define GPIO_CTIMER4_MAT0_3          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN18)
#define GPIO_CTIMER4_MAT1_1          (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN14)
#define GPIO_CTIMER4_MAT1_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN19)
#define GPIO_CTIMER4_MAT1_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN5)
#define GPIO_CTIMER4_MAT2_1          (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN15)
#define GPIO_CTIMER4_MAT2_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN31)
#define GPIO_CTIMER4_MAT2_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN6)
#define GPIO_CTIMER4_MAT3_1          (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN16)
#define GPIO_CTIMER4_MAT3_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN21) /* Type A */
#define GPIO_CTIMER4_MAT3_3          (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN5)

/* External Memory Controller (EMC) */

#define GPIO_EMC_A0                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN18)
#define GPIO_EMC_A1                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN19)
#define GPIO_EMC_A2                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN20)
#define GPIO_EMC_A3                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN21)
#define GPIO_EMC_A4                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_EMC_A5                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN6)
#define GPIO_EMC_A6                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN7)
#define GPIO_EMC_A7                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN8)
#define GPIO_EMC_A8                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN26)
#define GPIO_EMC_A9                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN27)
#define GPIO_EMC_A10                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN16)
#define GPIO_EMC_A11                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN23)
#define GPIO_EMC_A12                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN24)
#define GPIO_EMC_A13                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN25)
#define GPIO_EMC_A14                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT3 | GPIO_PIN25)
#define GPIO_EMC_A15                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT3 | GPIO_PIN26)
#define GPIO_EMC_A16                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT3 | GPIO_PIN27)
#define GPIO_EMC_A17                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT3 | GPIO_PIN28)
#define GPIO_EMC_A18                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT3 | GPIO_PIN29)
#define GPIO_EMC_A19                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT3 | GPIO_PIN30)
#define GPIO_EMC_A20                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT3 | GPIO_PIN31)
#define GPIO_EMC_A21                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT5 | GPIO_PIN5)
#define GPIO_EMC_A22                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT5 | GPIO_PIN6)
#define GPIO_EMC_A23                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT5 | GPIO_PIN7)
#define GPIO_EMC_A24                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT5 | GPIO_PIN8)
#define GPIO_EMC_A25                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT5 | GPIO_PIN9)
#define GPIO_EMC_BLSN0               (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN17)
#define GPIO_EMC_BLSN1               (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN18)
#define GPIO_EMC_BLSN2               (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN17)
#define GPIO_EMC_BLSN3               (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN18)
#define GPIO_EMC_CASN                (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN9)
#define GPIO_EMC_CKE0                (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN15)
#define GPIO_EMC_CKE1                (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN22)
#define GPIO_EMC_CKE2                (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN5)
#define GPIO_EMC_CKE3                (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN6)
#define GPIO_EMC_CLK0                (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN11)
#define GPIO_EMC_CLK1                (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT3 | GPIO_PIN12)
#define GPIO_EMC_CSN0                (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT0 | GPIO_PIN16) /* Type A */
#define GPIO_EMC_CSN1                (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN0)
#define GPIO_EMC_CSN2                (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN1)
#define GPIO_EMC_CSN3                (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN2)
#define GPIO_EMC_D0                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN2)
#define GPIO_EMC_D1                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN3)
#define GPIO_EMC_D2                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN4)
#define GPIO_EMC_D3                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN5)
#define GPIO_EMC_D4                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN6)
#define GPIO_EMC_D5                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_EMC_D6                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN8)
#define GPIO_EMC_D7                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN9)
#define GPIO_EMC_D8                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN19)
#define GPIO_EMC_D9                  (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN20)
#define GPIO_EMC_D10                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN21)
#define GPIO_EMC_D11                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN4)
#define GPIO_EMC_D12                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN28)
#define GPIO_EMC_D13                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN29)
#define GPIO_EMC_D14                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN30)
#define GPIO_EMC_D15                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN31)
#define GPIO_EMC_D16                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN21)
#define GPIO_EMC_D17                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN22)
#define GPIO_EMC_D18                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN23)
#define GPIO_EMC_D19                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN24)
#define GPIO_EMC_D20                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN25)
#define GPIO_EMC_D21                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN26)
#define GPIO_EMC_D22                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN27)
#define GPIO_EMC_D23                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN28)
#define GPIO_EMC_D24                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN29)
#define GPIO_EMC_D25                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN30)
#define GPIO_EMC_D26                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN31)
#define GPIO_EMC_D27                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT5 | GPIO_PIN0)
#define GPIO_EMC_D28                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT5 | GPIO_PIN1)
#define GPIO_EMC_D29                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT5 | GPIO_PIN2)
#define GPIO_EMC_D30                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT5 | GPIO_PIN3)
#define GPIO_EMC_D31                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT5 | GPIO_PIN4)
#define GPIO_EMC_DQM0                (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN13)
#define GPIO_EMC_DQM1                (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN14)
#define GPIO_EMC_DQM2                (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN19)
#define GPIO_EMC_DQM3                (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN20)
#define GPIO_EMC_DYCSN0              (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN12)
#define GPIO_EMC_DYCSN1              (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT3 | GPIO_PIN10)
#define GPIO_EMC_DYCSN2              (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN3)
#define GPIO_EMC_DYCSN3              (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT4 | GPIO_PIN4)
#define GPIO_EMC_FBCK                (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT3 | GPIO_PIN13)
#define GPIO_EMC_OEN                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT0 | GPIO_PIN17)
#define GPIO_EMC_RASN                (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_SLEW_FAST | GPIO_PORT1 | GPIO_PIN10)
#define GPIO_EMC_WEN                 (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT0 | GPIO_PIN15) /* Type A */

/* Ethernet (ENET) */

#define GPIO_ENET_COL_1              (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN7)
#define GPIO_ENET_COL_2              (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN2)
#define GPIO_ENET_CRS_1              (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_ENET_CRS_2              (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN1)
#define GPIO_ENET_MDC_1              (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN16)
#define GPIO_ENET_MDC_2              (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN15)
#define GPIO_ENET_MDC_3              (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN3)
#define GPIO_ENET_MDC_4              (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN4)
#define GPIO_ENET_MDIO_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN17)
#define GPIO_ENET_MDIO_2             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN16)
#define GPIO_ENET_MDIO_3             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN4)
#define GPIO_ENET_MDIO_4             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN23)
#define GPIO_ENET_MDIO_5             (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN5)
#define GPIO_ENET_RX_CLK_1           (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN15)
#define GPIO_ENET_RX_CLK_2           (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN14)
#define GPIO_ENET_RX_CLK_3           (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN31)
#define GPIO_ENET_RX_CLK_4           (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_ENET_RX_DV_1            (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN14)
#define GPIO_ENET_RX_DV_2            (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN10)
#define GPIO_ENET_RX_DV_3            (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN0)
#define GPIO_ENET_RX_DV_4            (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN6)
#define GPIO_ENET_RX_ER_1            (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN10)
#define GPIO_ENET_RX_ER_2            (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN29)
#define GPIO_ENET_RXD0_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN12)
#define GPIO_ENET_RXD0_2             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN11)
#define GPIO_ENET_RXD0_3             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN23)
#define GPIO_ENET_RXD0_4             (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN13) /* Type D+I */
#define GPIO_ENET_RXD1_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN13)
#define GPIO_ENET_RXD1_2             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN12)
#define GPIO_ENET_RXD1_3             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN24)
#define GPIO_ENET_RXD1_4             (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN14) /* Type D+I */
#define GPIO_ENET_RXD2_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_ENET_RXD2_2             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN25)
#define GPIO_ENET_RXD3_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_ENET_RXD3_2             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN26)
#define GPIO_ENET_TX_CLK_1           (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN6)
#define GPIO_ENET_TX_CLK_2           (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN30)
#define GPIO_ENET_TX_EN_1            (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN11)
#define GPIO_ENET_TX_EN_2            (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN13)
#define GPIO_ENET_TX_EN_3            (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN27)
#define GPIO_ENET_TX_EN_4            (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN15) /* Type A */
#define GPIO_ENET_TX_ER_1            (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_ENET_TX_ER_2            (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN28)
#define GPIO_ENET_TXD0_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN9)
#define GPIO_ENET_TXD0_2             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN19)
#define GPIO_ENET_TXD0_3             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN8)
#define GPIO_ENET_TXD0_4             (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN16) /* Type A */
#define GPIO_ENET_TXD1_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN10)
#define GPIO_ENET_TXD1_2             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN20)
#define GPIO_ENET_TXD1_3             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN9)
#define GPIO_ENET_TXD1_4             (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN17)
#define GPIO_ENET_TXD2_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_ENET_TXD2_2             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN21)
#define GPIO_ENET_TXD3_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_ENET_TXD3_2             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN22)

/* Flexcomm (FC)
 *
 *   Pins used for I2C should add GPIO_FILTER_OFF in your board. h head file.
 *   For standard mode I2C, add GPIO_I2C_FILTER_OFF
 *   For fast speed mode plaseI2C, also add GPIO_I2CDRIVE_HIGH
 *   For high speed slave add both GPIO_I2C_FILTER_OFF and GPIO_I2CDRIVE_HIGH
 */

#define GPIO_FC0_CTS_SDA_SSEL0_1     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN31) /* Type A */
#define GPIO_FC0_CTS_SDA_SSEL0_2     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN8)
#define GPIO_FC0_CTS_SDA_SSEL0_3     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN5)
#define GPIO_FC0_RTS_SCL_SSEL1_1     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN0)  /* Type A */
#define GPIO_FC0_RTS_SCL_SSEL1_2     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN7)
#define GPIO_FC0_RTS_SCL_SSEL1_3     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN4)
#define GPIO_FC0_RXD_SDA_MOSI_1      (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN24)
#define GPIO_FC0_RXD_SDA_MOSI_2      (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN29)
#define GPIO_FC0_RXD_SDA_MOSI_3      (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_FC0_RXD_SDA_MOSI_4      (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN0)  /* Type A */
#define GPIO_FC0_SCK_1               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN28)
#define GPIO_FC0_SCK_2               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN4)
#define GPIO_FC0_SCK_3               (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN11)
#define GPIO_FC0_TXD_SCL_MISO_1      (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN25)
#define GPIO_FC0_TXD_SCL_MISO_2      (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN30)
#define GPIO_FC0_TXD_SCL_MISO_3      (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN6)
#define GPIO_FC0_TXD_SCL_MISO_4      (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN1)  /* Type A */

#define GPIO_FC1_CTS_SDA_SSEL0_1     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN13) /* Type D+I */
#define GPIO_FC1_CTS_SDA_SSEL0_2     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_FC1_RTS_SCL_SSEL1_1     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN14) /* Type D+I */
#define GPIO_FC1_RTS_SCL_SSEL1_2     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN6)
#define GPIO_FC1_RTS_SCL_SSEL1_3     (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN30)
#define GPIO_FC1_RXD_SDA_MOSI_1      (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN10)
#define GPIO_FC1_RXD_SDA_MOSI_2      (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_FC1_RXD_SDA_MOSI_3      (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN28)
#define GPIO_FC1_SCK_1               (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN9)
#define GPIO_FC1_SCK_2               (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN11)
#define GPIO_FC1_SCK_3               (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_FC1_SCK_4               (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN27)
#define GPIO_FC1_TXD_SCL_MISO_1      (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN11)
#define GPIO_FC1_TXD_SCL_MISO_2      (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_FC1_TXD_SCL_MISO_3      (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN10) /* Type A */
#define GPIO_FC1_TXD_SCL_MISO_4      (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN29)

#define GPIO_FC2_CTS_SDA_SSEL0_1     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN26)
#define GPIO_FC2_CTS_SDA_SSEL0_2     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN23) /* Type D+I */
#define GPIO_FC2_CTS_SDA_SSEL0_3     (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN11)
#define GPIO_FC2_CTS_SDA_SSEL0_4     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN23)
#define GPIO_FC2_RTS_SCL_SSEL1_1     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN27)
#define GPIO_FC2_RTS_SCL_SSEL1_2     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN24) /* Type D+I */
#define GPIO_FC2_RTS_SCL_SSEL1_3     (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN12)
#define GPIO_FC2_RTS_SCL_SSEL1_4     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN22)
#define GPIO_FC2_RXD_SDA_MOSI_1      (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN26)
#define GPIO_FC2_RXD_SDA_MOSI_2      (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN24)
#define GPIO_FC2_RXD_SDA_MOSI_3      (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN9)
#define GPIO_FC2_RXD_SDA_MOSI_4      (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN20)
#define GPIO_FC2_SCK_1               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN23)
#define GPIO_FC2_SCK_2               (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN8)
#define GPIO_FC2_SCK_3               (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN19)
#define GPIO_FC2_TXD_SCL_MISO_1      (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN27)
#define GPIO_FC2_TXD_SCL_MISO_2      (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN25)
#define GPIO_FC2_TXD_SCL_MISO_3      (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN10)
#define GPIO_FC2_TXD_SCL_MISO_4      (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN21)

#define GPIO_FC3_CTS_SDA_SSEL0_1     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN20)
#define GPIO_FC3_CTS_SDA_SSEL0_2     (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN1)
#define GPIO_FC3_CTS_SDA_SSEL0_3     (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN21)
#define GPIO_FC3_RTS_SCL_SSEL1_1     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN21)
#define GPIO_FC3_RTS_SCL_SSEL1_2     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_FC3_RTS_SCL_SSEL1_3     (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN20)
#define GPIO_FC3_RXD_SDA_MOSI_1      (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN3)
#define GPIO_FC3_RXD_SDA_MOSI_2      (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN1)
#define GPIO_FC3_RXD_SDA_MOSI_3      (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN18)
#define GPIO_FC3_SCK_1               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN6)
#define GPIO_FC3_SCK_2               (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN0)
#define GPIO_FC3_SCK_3               (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN26)
#define GPIO_FC3_SSEL2_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN9)
#define GPIO_FC3_SSEL2_2             (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN27)
#define GPIO_FC3_SSEL2_3             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN23)
#define GPIO_FC3_SSEL3_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN8)
#define GPIO_FC3_SSEL3_2             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_FC3_SSEL3_3             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN24)
#define GPIO_FC3_TXD_SCL_MISO_1      (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN12) /* Type A */
#define GPIO_FC3_TXD_SCL_MISO_2      (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN2)
#define GPIO_FC3_TXD_SCL_MISO_3      (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN19)

#define GPIO_FC4_CTS_SDA_SSEL0_1     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN18)
#define GPIO_FC4_CTS_SDA_SSEL0_2     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN28)
#define GPIO_FC4_CTS_SDA_SSEL0_3     (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN2)
#define GPIO_FC4_CTS_SDA_SSEL0_4     (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN9)
#define GPIO_FC4_RTS_SCL_SSEL1_1     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN19)
#define GPIO_FC4_RTS_SCL_SSEL1_2     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN29)
#define GPIO_FC4_RTS_SCL_SSEL1_3     (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN3)
#define GPIO_FC4_RTS_SCL_SSEL1_4     (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN15)
#define GPIO_FC4_RXD_SDA_MOSI_1      (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN5)
#define GPIO_FC4_RXD_SDA_MOSI_2      (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN26)
#define GPIO_FC4_RXD_SDA_MOSI_3      (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN0)
#define GPIO_FC4_RXD_SDA_MOSI_4      (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN21)
#define GPIO_FC4_SCK_1               (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN4)
#define GPIO_FC4_SCK_2               (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN25)
#define GPIO_FC4_SCK_3               (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN31)
#define GPIO_FC4_SCK_4               (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN19)
#define GPIO_FC4_SSEL2_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN17)
#define GPIO_FC4_SSEL2_2             (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN30)
#define GPIO_FC4_SSEL2_3             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN4)
#define GPIO_FC4_SSEL2_4             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN8)
#define GPIO_FC4_SSEL3_1             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN4)
#define GPIO_FC4_SSEL3_2             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN5)
#define GPIO_FC4_SSEL3_3             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN22)
#define GPIO_FC4_TXD_SCL_MISO_1      (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN16) /* Type A */
#define GPIO_FC4_TXD_SCL_MISO_2      (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN27)
#define GPIO_FC4_TXD_SCL_MISO_3      (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN1)
#define GPIO_FC4_TXD_SCL_MISO_4      (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN20)

#define GPIO_FC5_CTS_SDA_SSEL0_1     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN9)
#define GPIO_FC5_CTS_SDA_SSEL0_2     (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN14)
#define GPIO_FC5_CTS_SDA_SSEL0_3     (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN14)
#define GPIO_FC5_RTS_SCL_SSEL1_1     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN10)
#define GPIO_FC5_RTS_SCL_SSEL1_2     (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN15)
#define GPIO_FC5_RTS_SCL_SSEL1_3     (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN15)
#define GPIO_FC5_RXD_SDA_MOSI_1      (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN8)
#define GPIO_FC5_RXD_SDA_MOSI_2      (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN7)
#define GPIO_FC5_RXD_SDA_MOSI_3      (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN12)
#define GPIO_FC5_SCK_1               (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_FC5_SCK_2               (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN6)
#define GPIO_FC5_SCK_3               (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN11)
#define GPIO_FC5_TXD_SCL_MISO_1      (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN9)
#define GPIO_FC5_TXD_SCL_MISO_2      (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN8)
#define GPIO_FC5_TXD_SCL_MISO_3      (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN13)
#define GPIO_FC6_CTS_SDA_SSEL0_1     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN15) /* Type A */

#define GPIO_FC6_CTS_SDA_SSEL0_2     (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN0)
#define GPIO_FC6_RXD_SDA_MOSI_DATA_1 (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN11) /* Type A */
#define GPIO_FC6_RXD_SDA_MOSI_DATA_2 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN13)
#define GPIO_FC6_RXD_SDA_MOSI_DATA_3 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN2)
#define GPIO_FC6_SCK_1               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN10) /* Type A */
#define GPIO_FC6_SCK_2               (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN12)
#define GPIO_FC6_SCK_3               (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN1)
#define GPIO_FC6_TXD_SCL_MISO        (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN22)
#define GPIO_FC6_TXD_SCL_MISO_WS_1   (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN16)
#define GPIO_FC6_TXD_SCL_MISO_WS_2   (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN3)

#define GPIO_FC7_CTS_SDA_SSEL0_1     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN21)
#define GPIO_FC7_CTS_SDA_SSEL0_2     (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN28)
#define GPIO_FC7_CTS_SDA_SSEL0_3     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN25)
#define GPIO_FC7_RTS_SCL_SSEL1_1     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN20)
#define GPIO_FC7_RTS_SCL_SSEL1_2     (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN29)
#define GPIO_FC7_RTS_SCL_SSEL1_3     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN24)
#define GPIO_FC7_RXD_SDA_MOSI        (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN20)
#define GPIO_FC7_RXD_SDA_MOSI_DATA_1 (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN29)
#define GPIO_FC7_RXD_SDA_MOSI_DATA_2 (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN19)
#define GPIO_FC7_SCK_1               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN28)
#define GPIO_FC7_SCK_2               (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN18)
#define GPIO_FC7_SCK_3               (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN21)
#define GPIO_FC7_TXD_SCL_MISO        (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN19)
#define GPIO_FC7_TXD_SCL_MISO_WS_1   (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN30)
#define GPIO_FC7_TXD_SCL_MISO_WS_2   (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN20)

#define GPIO_FC8_CTS_SDA_SSEL0_1     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN18)
#define GPIO_FC8_CTS_SDA_SSEL0_2     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN4)
#define GPIO_FC8_CTS_SDA_SSEL0_3     (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN31)
#define GPIO_FC8_RTS_SCL_SSEL1_1     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN22)
#define GPIO_FC8_RTS_SCL_SSEL1_2     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN19)
#define GPIO_FC8_RTS_SCL_SSEL1_3     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN5)
#define GPIO_FC8_RXD_SDA_MOSI_1      (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN16)
#define GPIO_FC8_RXD_SDA_MOSI_2      (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN17)
#define GPIO_FC8_RXD_SDA_MOSI_3      (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN17)
#define GPIO_FC8_SCK_1               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN19)
#define GPIO_FC8_SCK_2               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN15)
#define GPIO_FC8_SCK_3               (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN16)
#define GPIO_FC8_TXD_SCL_MISO_1      (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN17)
#define GPIO_FC8_TXD_SCL_MISO_2      (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN18)
#define GPIO_FC8_TXD_SCL_MISO_3      (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN29)

#define GPIO_FC9_CTS_SDA_SSEL0_1     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN30)
#define GPIO_FC9_CTS_SDA_SSEL0_2     (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN13)
#define GPIO_FC9_CTS_SDA_SSEL0_3     (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN5)
#define GPIO_FC9_RTS_SCL_SSEL1_1     (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN31)
#define GPIO_FC9_RTS_SCL_SSEL1_2     (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN14)
#define GPIO_FC9_RTS_SCL_SSEL1_3     (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN6)
#define GPIO_FC9_RXD_SDA_MOSI_1      (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN21) /* Type A */
#define GPIO_FC9_RXD_SDA_MOSI_2      (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN2)
#define GPIO_FC9_RXD_SDA_MOSI_3      (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN15)
#define GPIO_FC9_SCK_1               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN20)
#define GPIO_FC9_SCK_2               (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN27)
#define GPIO_FC9_SCK_3               (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN14)
#define GPIO_FC9_TXD_SCL_MISO_1      (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN22) /* Type A */
#define GPIO_FC9_TXD_SCL_MISO_2      (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN3)
#define GPIO_FC9_TXD_SCL_MISO_3      (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN16)

/* Frequency Measurement (FREQME) */

#define GPIO_FREQME_GPIO_CLK_A_1     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN11) /* Type A */
#define GPIO_FREQME_GPIO_CLK_A_2     (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN4)
#define GPIO_FREQME_GPIO_CLK_B_1     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN12) /* Type A */
#define GPIO_FREQME_GPIO_CLK_B_2     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN7)

/* LCD */

#define GPIO_LCD_AC                  (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN15)
#define GPIO_LCD_CLKIN               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN17)
#define GPIO_LCD_DCLK                (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN13)
#define GPIO_LCD_FP                  (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN14)
#define GPIO_LCD_LE                  (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN12)
#define GPIO_LCD_LP                  (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN16)
#define GPIO_LCD_PWR                 (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN11)
#define GPIO_LCD_VD0_1               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN18)
#define GPIO_LCD_VD0_2               (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN6)
#define GPIO_LCD_VD1_1               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN19)
#define GPIO_LCD_VD1_2               (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN7)
#define GPIO_LCD_VD2_1               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN20)
#define GPIO_LCD_VD2_2               (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN8)
#define GPIO_LCD_VD3_1               (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN21)
#define GPIO_LCD_VD3_2               (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN9)
#define GPIO_LCD_VD4                 (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN22)
#define GPIO_LCD_VD5                 (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN23)
#define GPIO_LCD_VD6                 (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN24)
#define GPIO_LCD_VD7                 (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN25)
#define GPIO_LCD_VD8                 (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN26)
#define GPIO_LCD_VD9                 (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN27)
#define GPIO_LCD_VD10                (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN28)
#define GPIO_LCD_VD11                (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN29)
#define GPIO_LCD_VD12                (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN30)
#define GPIO_LCD_VD13                (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN31)
#define GPIO_LCD_VD14                (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN0)
#define GPIO_LCD_VD15                (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN1)
#define GPIO_LCD_VD16                (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN2)
#define GPIO_LCD_VD17                (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN3)
#define GPIO_LCD_VD18                (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN4)
#define GPIO_LCD_VD19                (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN5)
#define GPIO_LCD_VD20                (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN6)
#define GPIO_LCD_VD21                (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN7)
#define GPIO_LCD_VD22                (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN8)
#define GPIO_LCD_VD23                (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN9)

/* MCLK */

#define GPIO_MCLK_1                  (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN23) /* Type A */
#define GPIO_MCLK_2                  (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN31)
#define GPIO_MCLK_3                  (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN11)
#define GPIO_MCLK_4                  (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN7)
#define GPIO_MCLK_5                  (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN21)

/* Microphone (PDM) */

#define GPIO_PDM0_CLK_1              (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN0)
#define GPIO_PDM0_CLK_2              (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN8)
#define GPIO_PDM0_CLK_3              (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN0)
#define GPIO_PDM0_CLK_4              (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN26)
#define GPIO_PDM0_DATA_1             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN1)
#define GPIO_PDM0_DATA_2             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN9)
#define GPIO_PDM0_DATA_3             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN1)
#define GPIO_PDM0_DATA_4             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN27)

#define GPIO_PDM1_CLK_1              (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN5)
#define GPIO_PDM1_CLK_2              (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_PDM1_CLK_3              (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN2)
#define GPIO_PDM1_DATA_1             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN6)
#define GPIO_PDM1_DATA_2             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN8)
#define GPIO_PDM1_DATA_3             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN3)

/* SmartCard Interface (SCI) */

#define GPIO_SCI0_IO                 (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN20)
#define GPIO_SCI0_SCLK               (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN21)

#define GPIO_SCI1_IO                 (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN9)
#define GPIO_SCI1_SCLK               (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN18)

/* SCTimer */

#define GPIO_SCT0_GPI0_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN5)
#define GPIO_SCT0_GPI0_2             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN0)
#define GPIO_SCT0_GPI0_3             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN13) /* Type D+I */
#define GPIO_SCT0_GPI0_4             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN24)
#define GPIO_SCT0_GPI0_5             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_SCT0_GPI0_6             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN31)
#define GPIO_SCT0_GPI0_7             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN7)
#define GPIO_SCT0_GPI1_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN6)
#define GPIO_SCT0_GPI1_2             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN1)
#define GPIO_SCT0_GPI1_3             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN14) /* Type D+I */
#define GPIO_SCT0_GPI1_4             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN25)
#define GPIO_SCT0_GPI1_5             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN0)
#define GPIO_SCT0_GPI1_7             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN8)
#define GPIO_SCT0_GPI2_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN7)
#define GPIO_SCT0_GPI2_2             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN2)
#define GPIO_SCT0_GPI2_3             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN20)
#define GPIO_SCT0_GPI2_4             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN1)
#define GPIO_SCT0_GPI2_5             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN9)
#define GPIO_SCT0_GPI3_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN8)
#define GPIO_SCT0_GPI3_2             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN21)
#define GPIO_SCT0_GPI3_3             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN3)
#define GPIO_SCT0_GPI3_4             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN6)
#define GPIO_SCT0_GPI3_5             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN10)
#define GPIO_SCT0_GPI3_6             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN2)
#define GPIO_SCT0_GPI4_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN9)
#define GPIO_SCT0_GPI4_2             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN4)
#define GPIO_SCT0_GPI4_3             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN0)  /* Type A */
#define GPIO_SCT0_GPI4_4             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN7)
#define GPIO_SCT0_GPI4_5             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN11)
#define GPIO_SCT0_GPI4_6             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN3)
#define GPIO_SCT0_GPI5_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN10)
#define GPIO_SCT0_GPI5_2             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN5)
#define GPIO_SCT0_GPI5_3             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN1)
#define GPIO_SCT0_GPI5_4             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN22)
#define GPIO_SCT0_GPI5_5             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN12)
#define GPIO_SCT0_GPI5_6             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN4)
#define GPIO_SCT0_GPI6_1             (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN29)
#define GPIO_SCT0_GPI6_2             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN6)
#define GPIO_SCT0_GPI6_3             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN2)
#define GPIO_SCT0_GPI6_4             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN13)
#define GPIO_SCT0_GPI6_5             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN5)
#define GPIO_SCT0_GPI7_1             (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN17)
#define GPIO_SCT0_GPI7_2             (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN30)
#define GPIO_SCT0_GPI7_3             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN12) /* Type A */
#define GPIO_SCT0_GPI7_4             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN19)
#define GPIO_SCT0_GPI7_5             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN14)
#define GPIO_SCT0_GPI7_6             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN6)
#define GPIO_SCT0_OUT0_1             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN23)
#define GPIO_SCT0_OUT0_2             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN26)
#define GPIO_SCT0_OUT0_3             (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN2)
#define GPIO_SCT0_OUT0_4             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN17)
#define GPIO_SCT0_OUT0_5             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN4)
#define GPIO_SCT0_OUT1_1             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN24)
#define GPIO_SCT0_OUT1_2             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN27)
#define GPIO_SCT0_OUT1_3             (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN3)
#define GPIO_SCT0_OUT1_4             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN18)
#define GPIO_SCT0_OUT1_5             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN8)
#define GPIO_SCT0_OUT2_1             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN25)
#define GPIO_SCT0_OUT2_2             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN28)
#define GPIO_SCT0_OUT2_3             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN15) /* Type A */
#define GPIO_SCT0_OUT2_4             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN19)
#define GPIO_SCT0_OUT2_5             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN9)
#define GPIO_SCT0_OUT3_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN10)
#define GPIO_SCT0_OUT3_2             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN26)
#define GPIO_SCT0_OUT3_3             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN29)
#define GPIO_SCT0_OUT3_4             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN22)
#define GPIO_SCT0_OUT3_5             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN31) /* Type A */
#define GPIO_SCT0_OUT3_6             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN10)
#define GPIO_SCT0_OUT4_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN14)
#define GPIO_SCT0_OUT4_2             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN30)
#define GPIO_SCT0_OUT4_3             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN23) /* Type A */
#define GPIO_SCT0_OUT4_4             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN17)
#define GPIO_SCT0_OUT4_5             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN3)
#define GPIO_SCT0_OUT5_1             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN31)
#define GPIO_SCT0_OUT5_2             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN26)
#define GPIO_SCT0_OUT5_3             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN18)
#define GPIO_SCT0_OUT5_4             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN6)
#define GPIO_SCT0_OUT5_5             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN18)
#define GPIO_SCT0_OUT6_1             (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN2)
#define GPIO_SCT0_OUT6_2             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN27)
#define GPIO_SCT0_OUT6_3             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN31)
#define GPIO_SCT0_OUT6_4             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN7)
#define GPIO_SCT0_OUT6_5             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN19)
#define GPIO_SCT0_OUT7_1             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN19)
#define GPIO_SCT0_OUT7_2             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN22)
#define GPIO_SCT0_OUT7_3             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN28)
#define GPIO_SCT0_OUT7_4             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN8)
#define GPIO_SCT0_OUT7_5             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN20)
#define GPIO_SCT0_OUT8_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN12)
#define GPIO_SCT0_OUT8_2             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN23)
#define GPIO_SCT0_OUT8_3             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN29)
#define GPIO_SCT0_OUT8_4             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN9)
#define GPIO_SCT0_OUT9_1             (GPIO_ALT1 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN13)
#define GPIO_SCT0_OUT9_2             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN24)
#define GPIO_SCT0_OUT9_3             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN30)
#define GPIO_SCT0_OUT9_4             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN10)

/* SD card */

#define GPIO_SD_BACKEND_PWR_1        (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN21) /* Type A */
#define GPIO_SD_BACKEND_PWR_2        (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN4)
#define GPIO_SD_CARD_DET_N_1         (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT0 | GPIO_PIN17)
#define GPIO_SD_CARD_DET_N_2         (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN10)
#define GPIO_SD_CARD_DET_N_3         (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT4 | GPIO_PIN22)
#define GPIO_SD_CARD_INT_N_1         (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN20)
#define GPIO_SD_CARD_INT_N_2         (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT4 | GPIO_PIN24)
#define GPIO_SD_CLK_1                (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT0 | GPIO_PIN7)
#define GPIO_SD_CLK_2                (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT1 | GPIO_PIN8)
#define GPIO_SD_CLK_3                (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN3)
#define GPIO_SD_CLK_4                (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT4 | GPIO_PIN19)
#define GPIO_SD_CMD_1                (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT0 | GPIO_PIN8)
#define GPIO_SD_CMD_2                (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT1 | GPIO_PIN22)
#define GPIO_SD_CMD_3                (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN4)
#define GPIO_SD_CMD_4                (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT4 | GPIO_PIN20)
#define GPIO_SD_CMD_5                (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT1 | GPIO_PIN16)
#define GPIO_SD_D0_1                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT0 | GPIO_PIN24)
#define GPIO_SD_D0_2                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT1 | GPIO_PIN4)
#define GPIO_SD_D0_3                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN6)
#define GPIO_SD_D0_4                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT4 | GPIO_PIN25)
#define GPIO_SD_D1_1                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT0 | GPIO_PIN25)
#define GPIO_SD_D1_2                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT1 | GPIO_PIN7)
#define GPIO_SD_D1_3                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN7)
#define GPIO_SD_D1_4                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT4 | GPIO_PIN26)
#define GPIO_SD_D2_1                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT0 | GPIO_PIN31) /* Type A */
#define GPIO_SD_D2_2                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT1 | GPIO_PIN5)
#define GPIO_SD_D2_3                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN8)
#define GPIO_SD_D2_4                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT4 | GPIO_PIN27)
#define GPIO_SD_D3_1                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_FILTER_OFF | GPIO_PORT1 | GPIO_PIN0) /* Type A */
#define GPIO_SD_D3_2                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT1 | GPIO_PIN6)
#define GPIO_SD_D3_3                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN9)
#define GPIO_SD_D3_4                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT4 | GPIO_PIN28)
#define GPIO_SD_D4_1                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT1 | GPIO_PIN27)
#define GPIO_SD_D4_2                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN16)
#define GPIO_SD_D4_3                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT4 | GPIO_PIN29)
#define GPIO_SD_D5_1                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT1 | GPIO_PIN28)
#define GPIO_SD_D5_2                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN17)
#define GPIO_SD_D5_3                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT4 | GPIO_PIN30)
#define GPIO_SD_D6_1                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT1 | GPIO_PIN29)
#define GPIO_SD_D6_2                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN18)
#define GPIO_SD_D6_3                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT4 | GPIO_PIN31)
#define GPIO_SD_D7_1                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT1 | GPIO_PIN30)
#define GPIO_SD_D7_2                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN19)
#define GPIO_SD_D7_3                 (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT5 | GPIO_PIN0)
#define GPIO_SD_POW_EN_1             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_STANDARD | GPIO_FILTER_OFF | GPIO_PORT0 | GPIO_PIN9)
#define GPIO_SD_POW_EN_2             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_STANDARD | GPIO_FILTER_OFF | GPIO_PORT2 | GPIO_PIN5)
#define GPIO_SD_POW_EN_3             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_STANDARD | GPIO_FILTER_OFF | GPIO_PORT4 | GPIO_PIN21)
#define GPIO_SD_VOLT0_1              (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN11)
#define GPIO_SD_VOLT0_2              (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN1)
#define GPIO_SD_VOLT1_1              (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN12)
#define GPIO_SD_VOLT1_2              (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN2)
#define GPIO_SD_VOLT2_1              (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN13)
#define GPIO_SD_VOLT2_2              (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN3)
#define GPIO_SD_WR_PRT_1             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT0 | GPIO_PIN18)
#define GPIO_SD_WR_PRT_2             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT3 | GPIO_PIN15)
#define GPIO_SD_WR_PRT_3             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_SLEW_FAST | GPIO_FILTER_OFF | GPIO_PORT4 | GPIO_PIN23)

/* SPIFI */

#define GPIO_SPIFI_CLK               (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN26)
#define GPIO_SPIFI_CSN               (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN23) /* Type A */
#define GPIO_SPIFI_IO0               (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN24)
#define GPIO_SPIFI_IO1               (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN25)
#define GPIO_SPIFI_IO2               (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN28)
#define GPIO_SPIFI_IO3               (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN27)
#define GPIO_SWCLK                   (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN11) /* Type A */

/* SWD */

#define GPIO_SWDIO                   (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN12) /* Type A */
#define GPIO_SWO_1                   (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN8)
#define GPIO_SWO_2                   (GPIO_ALT6 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN10) /* Type A */

/* Trace */

#define GPIO_TRACECLK_1              (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN0) /* Type A */
#define GPIO_TRACECLK_2              (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN5)
#define GPIO_TRACECLK_3              (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN12)
#define GPIO_TRACEDATA0_1            (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN31) /* Type A */
#define GPIO_TRACEDATA0_2            (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN6)
#define GPIO_TRACEDATA0_3            (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN10)
#define GPIO_TRACEDATA1_1            (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN30)
#define GPIO_TRACEDATA1_2            (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN7)
#define GPIO_TRACEDATA1_3            (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN13)
#define GPIO_TRACEDATA2_1            (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN29)
#define GPIO_TRACEDATA2_2            (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN8)
#define GPIO_TRACEDATA2_3            (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN14)
#define GPIO_TRACEDATA3_1            (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN28)
#define GPIO_TRACEDATA3_2            (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN9)
#define GPIO_TRACEDATA3_3            (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN11)

/* USBG */

#define GPIO_USB0_FRAME_1            (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN14)
#define GPIO_USB0_FRAME_2            (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN7)
#define GPIO_USB0_FRAME_3            (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN13)
#define GPIO_USB0_IDVALUE_1          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN12)
#define GPIO_USB0_IDVALUE_2          (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN11)
#define GPIO_USB0_IDVALUE_3          (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN26)
#define GPIO_USB0_LEDN_1             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN15)
#define GPIO_USB0_LEDN_2             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN8)
#define GPIO_USB0_LEDN_3             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN14)
#define GPIO_USB0_OVERCURRENTN_1     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN15)
#define GPIO_USB0_OVERCURRENTN_2     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN8)
#define GPIO_USB0_OVERCURRENTN_3     (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN13)
#define GPIO_USB0_OVERCURRENTN_4     (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN28)
#define GPIO_USB0_PORTPWRN_1         (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN14)
#define GPIO_USB0_PORTPWRN_2         (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN7)
#define GPIO_USB0_PORTPWRN_3         (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN12)
#define GPIO_USB0_PORTPWRN_4         (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN3)
#define GPIO_USB0_VBUS_1             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN25)
#define GPIO_USB0_VBUS_2             (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN24) /* Type D+I */
#define GPIO_USB0_VBUS_3             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN11)
#define GPIO_USB0_VBUS_4             (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN22)

#define GPIO_USB1_FRAME_1            (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN16)
#define GPIO_USB1_FRAME_2            (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN9)
#define GPIO_USB1_FRAME_3            (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN29)
#define GPIO_USB1_LEDN_1             (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN17)
#define GPIO_USB1_LEDN_2             (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN10)
#define GPIO_USB1_LEDN_3             (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN30)
#define GPIO_USB1_OVERCURRENTN_1     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN17)
#define GPIO_USB1_OVERCURRENTN_2     (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN10)
#define GPIO_USB1_OVERCURRENTN_3     (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN30)
#define GPIO_USB1_OVERCURRENTN_4     (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN1)
#define GPIO_USB1_PORTPWRN_1         (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT2 | GPIO_PIN16)
#define GPIO_USB1_PORTPWRN_2         (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN9)
#define GPIO_USB1_PORTPWRN_3         (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN29)
#define GPIO_USB1_PORTPWRN_4         (GPIO_ALT7 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN2)

/* Micro-tick Timer (UTICK) */

#define GPIO_UTICK_CAP0_1            (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN13) /* Type D+I */
#define GPIO_UTICK_CAP0_2            (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN19)
#define GPIO_UTICK_CAP0_3            (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN25)
#define GPIO_UTICK_CAP0_4            (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN17)
#define GPIO_UTICK_CAP1_1            (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN14) /* Type D+I */
#define GPIO_UTICK_CAP1_2            (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN22)
#define GPIO_UTICK_CAP1_3            (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN26)
#define GPIO_UTICK_CAP1_4            (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN18)
#define GPIO_UTICK_CAP2_1            (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN15) /* Type A */
#define GPIO_UTICK_CAP2_2            (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN14)
#define GPIO_UTICK_CAP2_3            (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN21) /* Type A */
#define GPIO_UTICK_CAP2_4            (GPIO_ALT4 | GPIO_MODE_DIGITAL | GPIO_PORT4 | GPIO_PIN26)
#define GPIO_UTICK_CAP3_1            (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT0 | GPIO_PIN21)
#define GPIO_UTICK_CAP3_2            (GPIO_ALT2 | GPIO_MODE_DIGITAL | GPIO_PORT1 | GPIO_PIN15)
#define GPIO_UTICK_CAP3_3            (GPIO_ALT3 | GPIO_MODE_DIGITAL | GPIO_PORT3 | GPIO_PIN23) /* Type D+I */
#define GPIO_UTICK_CAP3_5            (GPIO_ALT5 | GPIO_MODE_DIGITAL | GPIO_PORT5 | GPIO_PIN10)

#endif /* __ARCH_ARM_SRC_LPC54XX_HARDWARE_LPC546X_PINMUX_H */
