/****************************************************************************
 * arch/arm/src/lpc54xx/hardware/lpc54_iocon.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_LPC54XX_HARDWARE_LPC54_IOCON_H
#define __ARCH_ARM_SRC_LPC54XX_HARDWARE_LPC54_IOCON_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include "hardware/lpc54_memorymap.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Register offsets *********************************************************/

#define LPC54_IOCON_PIO_n_OFFSET(p)    ((unsigned int)(p) << 2)
#define LPC54_IOCON_PIO_0_OFFSET      (0x0000) /* IOCON Port(n) register 0  */
#define LPC54_IOCON_PIO_1_OFFSET      (0x0004) /* IOCON Port(n) register 1  */
#define LPC54_IOCON_PIO_2_OFFSET      (0x0008) /* IOCON Port(n) register 2  */
#define LPC54_IOCON_PIO_3_OFFSET      (0x000c) /* IOCON Port(n) register 3  */
#define LPC54_IOCON_PIO_4_OFFSET      (0x0010) /* IOCON Port(n) register 4  */
#define LPC54_IOCON_PIO_5_OFFSET      (0x0014) /* IOCON Port(n) register 5  */
#define LPC54_IOCON_PIO_6_OFFSET      (0x0018) /* IOCON Port(n) register 6  */
#define LPC54_IOCON_PIO_7_OFFSET      (0x001c) /* IOCON Port(n) register 7 */
#define LPC54_IOCON_PIO_8_OFFSET      (0x0020) /* IOCON Port(n) register 8  */
#define LPC54_IOCON_PIO_9_OFFSET      (0x0024) /* IOCON Port(n) register 9  */
#define LPC54_IOCON_PIO_10_OFFSET     (0x0028) /* IOCON Port(n) register 10  */
#define LPC54_IOCON_PIO_11_OFFSET     (0x002c) /* IOCON Port(n) register 11  */
#define LPC54_IOCON_PIO_12_OFFSET     (0x0030) /* IOCON Port(n) register 12  */
#define LPC54_IOCON_PIO_13_OFFSET     (0x0034) /* IOCON Port(n) register 13  */
#define LPC54_IOCON_PIO_14_OFFSET     (0x0038) /* IOCON Port(n) register 14  */
#define LPC54_IOCON_PIO_15_OFFSET     (0x003c) /* IOCON Port(n) register 15  */
#define LPC54_IOCON_PIO_16_OFFSET     (0x0040) /* IOCON Port(n) register 16  */
#define LPC54_IOCON_PIO_17_OFFSET     (0x0044) /* IOCON Port(n) register 17  */
#define LPC54_IOCON_PIO_18_OFFSET     (0x0048) /* IOCON Port(n) register 18  */
#define LPC54_IOCON_PIO_19_OFFSET     (0x004c) /* IOCON Port(n) register 19  */
#define LPC54_IOCON_PIO_20_OFFSET     (0x0050) /* IOCON Port(n) register 20  */
#define LPC54_IOCON_PIO_21_OFFSET     (0x0054) /* IOCON Port(n) register 21  */
#define LPC54_IOCON_PIO_22_OFFSET     (0x0058) /* IOCON Port(n) register 22  */
#define LPC54_IOCON_PIO_23_OFFSET     (0x005c) /* IOCON Port(n) register 23  */
#define LPC54_IOCON_PIO_24_OFFSET     (0x0060) /* IOCON Port(n) register 24  */
#define LPC54_IOCON_PIO_25_OFFSET     (0x0064) /* IOCON Port(n) register 25  */
#define LPC54_IOCON_PIO_26_OFFSET     (0x0068) /* IOCON Port(n) register 26  */
#define LPC54_IOCON_PIO_27_OFFSET     (0x006c) /* IOCON Port(n) register 27  */
#define LPC54_IOCON_PIO_28_OFFSET     (0x0070) /* IOCON Port(n) register 28  */
#define LPC54_IOCON_PIO_29_OFFSET     (0x0074) /* IOCON Port(n) register 29  */
#define LPC54_IOCON_PIO_30_OFFSET     (0x0078) /* IOCON Port(n) register 30  */
#define LPC54_IOCON_PIO_31_OFFSET     (0x007c) /* IOCON Port(n) register 31  */

/* Register addresses *******************************************************/

#define LPC54_IOCON_PIO_BASE(b)       (LPC54_IOCON_BASE + ((unsigned int)(b) << 7))
#define LPC54_IOCON_PIO0_BASE         (LPC54_IOCON_BASE + 0x0000)
#define LPC54_IOCON_PIO1_BASE         (LPC54_IOCON_BASE + 0x0080)
#define LPC54_IOCON_PIO2_BASE         (LPC54_IOCON_BASE + 0x0100)
#define LPC54_IOCON_PIO3_BASE         (LPC54_IOCON_BASE + 0x0180)
#define LPC54_IOCON_PIO4_BASE         (LPC54_IOCON_BASE + 0x0200)
#define LPC54_IOCON_PIO5_BASE         (LPC54_IOCON_BASE + 0x0280)

#define LPC54_IOCON_PIO(b,p)          (LPC54_IOCON_PIO_BASE(b) + LPC54_IOCON_PIO_n_OFFSET(p))

#define LPC54_IOCON_PIO0_0            (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_0_OFFSET)
#define LPC54_IOCON_PIO0_1            (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_1_OFFSET)
#define LPC54_IOCON_PIO0_2            (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_2_OFFSET)
#define LPC54_IOCON_PIO0_3            (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_3_OFFSET)
#define LPC54_IOCON_PIO0_4            (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_4_OFFSET)
#define LPC54_IOCON_PIO0_5            (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_5_OFFSET)
#define LPC54_IOCON_PIO0_6            (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_6_OFFSET)
#define LPC54_IOCON_PIO0_7            (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_7_OFFSET)
#define LPC54_IOCON_PIO0_8            (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_8_OFFSET)
#define LPC54_IOCON_PIO0_9            (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_9_OFFSET)
#define LPC54_IOCON_PIO0_10           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_10_OFFSET)
#define LPC54_IOCON_PIO0_11           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_11_OFFSET)
#define LPC54_IOCON_PIO0_12           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_12_OFFSET)
#define LPC54_IOCON_PIO0_13           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_13_OFFSET)
#define LPC54_IOCON_PIO0_14           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_14_OFFSET)
#define LPC54_IOCON_PIO0_15           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_15_OFFSET)
#define LPC54_IOCON_PIO0_16           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_16_OFFSET)
#define LPC54_IOCON_PIO0_17           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_17_OFFSET)
#define LPC54_IOCON_PIO0_18           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_18_OFFSET)
#define LPC54_IOCON_PIO0_19           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_19_OFFSET)
#define LPC54_IOCON_PIO0_20           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_20_OFFSET)
#define LPC54_IOCON_PIO0_21           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_21_OFFSET)
#define LPC54_IOCON_PIO0_22           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_22_OFFSET)
#define LPC54_IOCON_PIO0_23           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_23_OFFSET)
#define LPC54_IOCON_PIO0_24           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_24_OFFSET)
#define LPC54_IOCON_PIO0_25           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_25_OFFSET)
#define LPC54_IOCON_PIO0_26           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_26_OFFSET)
#define LPC54_IOCON_PIO0_27           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_27_OFFSET)
#define LPC54_IOCON_PIO0_28           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_28_OFFSET)
#define LPC54_IOCON_PIO0_29           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_29_OFFSET)
#define LPC54_IOCON_PIO0_30           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_30_OFFSET)
#define LPC54_IOCON_PIO0_31           (LPC54_IOCON_PIO0_BASE + LPC54_IOCON_PIO_31_OFFSET)

#define LPC54_IOCON_PIO1_0            (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_0_OFFSET)
#define LPC54_IOCON_PIO1_1            (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_1_OFFSET)
#define LPC54_IOCON_PIO1_2            (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_2_OFFSET)
#define LPC54_IOCON_PIO1_3            (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_3_OFFSET)
#define LPC54_IOCON_PIO1_4            (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_4_OFFSET)
#define LPC54_IOCON_PIO1_5            (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_5_OFFSET)
#define LPC54_IOCON_PIO1_6            (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_6_OFFSET)
#define LPC54_IOCON_PIO1_7            (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_7_OFFSET)
#define LPC54_IOCON_PIO1_8            (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_8_OFFSET)
#define LPC54_IOCON_PIO1_9            (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_9_OFFSET)
#define LPC54_IOCON_PIO1_10           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_10_OFFSET)
#define LPC54_IOCON_PIO1_11           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_11_OFFSET)
#define LPC54_IOCON_PIO1_12           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_12_OFFSET)
#define LPC54_IOCON_PIO1_13           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_13_OFFSET)
#define LPC54_IOCON_PIO1_14           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_14_OFFSET)
#define LPC54_IOCON_PIO1_15           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_15_OFFSET)
#define LPC54_IOCON_PIO1_16           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_16_OFFSET)
#define LPC54_IOCON_PIO1_17           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_17_OFFSET)
#define LPC54_IOCON_PIO1_18           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_18_OFFSET)
#define LPC54_IOCON_PIO1_19           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_19_OFFSET)
#define LPC54_IOCON_PIO1_20           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_20_OFFSET)
#define LPC54_IOCON_PIO1_21           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_21_OFFSET)
#define LPC54_IOCON_PIO1_22           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_22_OFFSET)
#define LPC54_IOCON_PIO1_23           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_23_OFFSET)
#define LPC54_IOCON_PIO1_24           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_24_OFFSET)
#define LPC54_IOCON_PIO1_25           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_25_OFFSET)
#define LPC54_IOCON_PIO1_26           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_26_OFFSET)
#define LPC54_IOCON_PIO1_27           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_27_OFFSET)
#define LPC54_IOCON_PIO1_28           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_28_OFFSET)
#define LPC54_IOCON_PIO1_29           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_29_OFFSET)
#define LPC54_IOCON_PIO1_30           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_30_OFFSET)
#define LPC54_IOCON_PIO1_31           (LPC54_IOCON_PIO1_BASE + LPC54_IOCON_PIO_31_OFFSET)

#define LPC54_IOCON_PIO2_0            (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_0_OFFSET)
#define LPC54_IOCON_PIO2_1            (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_1_OFFSET)
#define LPC54_IOCON_PIO2_2            (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_2_OFFSET)
#define LPC54_IOCON_PIO2_3            (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_3_OFFSET)
#define LPC54_IOCON_PIO2_4            (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_4_OFFSET)
#define LPC54_IOCON_PIO2_5            (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_5_OFFSET)
#define LPC54_IOCON_PIO2_6            (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_6_OFFSET)
#define LPC54_IOCON_PIO2_7            (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_7_OFFSET)
#define LPC54_IOCON_PIO2_8            (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_8_OFFSET)
#define LPC54_IOCON_PIO2_9            (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_9_OFFSET)
#define LPC54_IOCON_PIO2_10           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_10_OFFSET)
#define LPC54_IOCON_PIO2_11           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_11_OFFSET)
#define LPC54_IOCON_PIO2_12           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_12_OFFSET)
#define LPC54_IOCON_PIO2_13           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_13_OFFSET)
#define LPC54_IOCON_PIO2_14           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_14_OFFSET)
#define LPC54_IOCON_PIO2_15           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_15_OFFSET)
#define LPC54_IOCON_PIO2_16           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_16_OFFSET)
#define LPC54_IOCON_PIO2_17           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_17_OFFSET)
#define LPC54_IOCON_PIO2_18           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_18_OFFSET)
#define LPC54_IOCON_PIO2_19           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_19_OFFSET)
#define LPC54_IOCON_PIO2_20           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_20_OFFSET)
#define LPC54_IOCON_PIO2_21           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_21_OFFSET)
#define LPC54_IOCON_PIO2_22           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_22_OFFSET)
#define LPC54_IOCON_PIO2_23           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_23_OFFSET)
#define LPC54_IOCON_PIO2_24           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_24_OFFSET)
#define LPC54_IOCON_PIO2_25           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_25_OFFSET)
#define LPC54_IOCON_PIO2_26           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_26_OFFSET)
#define LPC54_IOCON_PIO2_27           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_27_OFFSET)
#define LPC54_IOCON_PIO2_28           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_28_OFFSET)
#define LPC54_IOCON_PIO2_29           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_29_OFFSET)
#define LPC54_IOCON_PIO2_30           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_30_OFFSET)
#define LPC54_IOCON_PIO2_31           (LPC54_IOCON_PIO2_BASE + LPC54_IOCON_PIO_31_OFFSET)

#define LPC54_IOCON_PIO3_0            (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_0_OFFSET)
#define LPC54_IOCON_PIO3_1            (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_1_OFFSET)
#define LPC54_IOCON_PIO3_2            (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_2_OFFSET)
#define LPC54_IOCON_PIO3_3            (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_3_OFFSET)
#define LPC54_IOCON_PIO3_4            (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_4_OFFSET)
#define LPC54_IOCON_PIO3_5            (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_5_OFFSET)
#define LPC54_IOCON_PIO3_6            (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_6_OFFSET)
#define LPC54_IOCON_PIO3_7            (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_7_OFFSET)
#define LPC54_IOCON_PIO3_8            (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_8_OFFSET)
#define LPC54_IOCON_PIO3_9            (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_9_OFFSET)
#define LPC54_IOCON_PIO3_10           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_10_OFFSET)
#define LPC54_IOCON_PIO3_11           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_11_OFFSET)
#define LPC54_IOCON_PIO3_12           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_12_OFFSET)
#define LPC54_IOCON_PIO3_13           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_13_OFFSET)
#define LPC54_IOCON_PIO3_14           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_14_OFFSET)
#define LPC54_IOCON_PIO3_15           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_15_OFFSET)
#define LPC54_IOCON_PIO3_16           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_16_OFFSET)
#define LPC54_IOCON_PIO3_17           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_17_OFFSET)
#define LPC54_IOCON_PIO3_18           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_18_OFFSET)
#define LPC54_IOCON_PIO3_19           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_19_OFFSET)
#define LPC54_IOCON_PIO3_20           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_20_OFFSET)
#define LPC54_IOCON_PIO3_21           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_21_OFFSET)
#define LPC54_IOCON_PIO3_22           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_22_OFFSET)
#define LPC54_IOCON_PIO3_23           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_23_OFFSET)
#define LPC54_IOCON_PIO3_24           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_24_OFFSET)
#define LPC54_IOCON_PIO3_25           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_25_OFFSET)
#define LPC54_IOCON_PIO3_26           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_26_OFFSET)
#define LPC54_IOCON_PIO3_27           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_27_OFFSET)
#define LPC54_IOCON_PIO3_28           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_28_OFFSET)
#define LPC54_IOCON_PIO3_29           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_29_OFFSET)
#define LPC54_IOCON_PIO3_30           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_30_OFFSET)
#define LPC54_IOCON_PIO3_31           (LPC54_IOCON_PIO3_BASE + LPC54_IOCON_PIO_31_OFFSET)

#define LPC54_IOCON_PIO4_0            (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_0_OFFSET)
#define LPC54_IOCON_PIO4_1            (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_1_OFFSET)
#define LPC54_IOCON_PIO4_2            (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_2_OFFSET)
#define LPC54_IOCON_PIO4_3            (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_3_OFFSET)
#define LPC54_IOCON_PIO4_4            (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_4_OFFSET)
#define LPC54_IOCON_PIO4_5            (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_5_OFFSET)
#define LPC54_IOCON_PIO4_6            (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_6_OFFSET)
#define LPC54_IOCON_PIO4_7            (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_7_OFFSET)
#define LPC54_IOCON_PIO4_8            (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_8_OFFSET)
#define LPC54_IOCON_PIO4_9            (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_9_OFFSET)
#define LPC54_IOCON_PIO4_10           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_10_OFFSET)
#define LPC54_IOCON_PIO4_11           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_11_OFFSET)
#define LPC54_IOCON_PIO4_12           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_12_OFFSET)
#define LPC54_IOCON_PIO4_13           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_13_OFFSET)
#define LPC54_IOCON_PIO4_14           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_14_OFFSET)
#define LPC54_IOCON_PIO4_15           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_15_OFFSET)
#define LPC54_IOCON_PIO4_16           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_16_OFFSET)
#define LPC54_IOCON_PIO4_17           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_17_OFFSET)
#define LPC54_IOCON_PIO4_18           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_18_OFFSET)
#define LPC54_IOCON_PIO4_19           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_19_OFFSET)
#define LPC54_IOCON_PIO4_20           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_20_OFFSET)
#define LPC54_IOCON_PIO4_21           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_21_OFFSET)
#define LPC54_IOCON_PIO4_22           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_22_OFFSET)
#define LPC54_IOCON_PIO4_23           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_23_OFFSET)
#define LPC54_IOCON_PIO4_24           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_24_OFFSET)
#define LPC54_IOCON_PIO4_25           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_25_OFFSET)
#define LPC54_IOCON_PIO4_26           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_26_OFFSET)
#define LPC54_IOCON_PIO4_27           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_27_OFFSET)
#define LPC54_IOCON_PIO4_28           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_28_OFFSET)
#define LPC54_IOCON_PIO4_29           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_29_OFFSET)
#define LPC54_IOCON_PIO4_30           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_30_OFFSET)
#define LPC54_IOCON_PIO4_31           (LPC54_IOCON_PIO4_BASE + LPC54_IOCON_PIO_31_OFFSET)

#define LPC54_IOCON_PIO5_0            (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_0_OFFSET)
#define LPC54_IOCON_PIO5_1            (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_1_OFFSET)
#define LPC54_IOCON_PIO5_2            (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_2_OFFSET)
#define LPC54_IOCON_PIO5_3            (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_3_OFFSET)
#define LPC54_IOCON_PIO5_4            (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_4_OFFSET)
#define LPC54_IOCON_PIO5_5            (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_5_OFFSET)
#define LPC54_IOCON_PIO5_6            (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_6_OFFSET)
#define LPC54_IOCON_PIO5_7            (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_7_OFFSET)
#define LPC54_IOCON_PIO5_8            (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_8_OFFSET)
#define LPC54_IOCON_PIO5_9            (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_9_OFFSET)
#define LPC54_IOCON_PIO5_10           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_10_OFFSET)
#define LPC54_IOCON_PIO5_11           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_11_OFFSET)
#define LPC54_IOCON_PIO5_12           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_12_OFFSET)
#define LPC54_IOCON_PIO5_13           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_13_OFFSET)
#define LPC54_IOCON_PIO5_14           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_14_OFFSET)
#define LPC54_IOCON_PIO5_15           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_15_OFFSET)
#define LPC54_IOCON_PIO5_16           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_16_OFFSET)
#define LPC54_IOCON_PIO5_17           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_17_OFFSET)
#define LPC54_IOCON_PIO5_18           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_18_OFFSET)
#define LPC54_IOCON_PIO5_19           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_19_OFFSET)
#define LPC54_IOCON_PIO5_20           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_20_OFFSET)
#define LPC54_IOCON_PIO5_21           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_21_OFFSET)
#define LPC54_IOCON_PIO5_22           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_22_OFFSET)
#define LPC54_IOCON_PIO5_23           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_23_OFFSET)
#define LPC54_IOCON_PIO5_24           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_24_OFFSET)
#define LPC54_IOCON_PIO5_25           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_25_OFFSET)
#define LPC54_IOCON_PIO5_26           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_26_OFFSET)
#define LPC54_IOCON_PIO5_27           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_27_OFFSET)
#define LPC54_IOCON_PIO5_28           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_28_OFFSET)
#define LPC54_IOCON_PIO5_29           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_29_OFFSET)
#define LPC54_IOCON_PIO5_30           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_30_OFFSET)
#define LPC54_IOCON_PIO5_31           (LPC54_IOCON_PIO5_BASE + LPC54_IOCON_PIO_31_OFFSET)

/* Register bit definitions *************************************************/

/* IOCON pin function select */

#define IOCON_FUNC_GPIO             (0)
#define IOCON_FUNC_ALT1             (1)
#define IOCON_FUNC_ALT2             (2)
#define IOCON_FUNC_ALT3             (3)
#define IOCON_FUNC_ALT4             (4)
#define IOCON_FUNC_ALT5             (5)
#define IOCON_FUNC_ALT6             (6)
#define IOCON_FUNC_ALT7             (7)

/* Pin modes */

#define IOCON_MODE_FLOAT            (0)   /* 00: pin has neither pull-up nor pull-down */
#define IOCON_MODE_PULLDOWN         (1)   /* 01: pin has a pull-down resistor enabled */
#define IOCON_MODE_PULLUP           (2)   /* 10: pin has a pull-up resistor enabled */
#define IOCON_MODE_REPEATER         (3)   /* 11: pin has repeater mode enabled */

/* Bit field definitions */

#define IOCON_FUNC_SHIFT            (0)   /* Bits 0-3: Pin function.  All types */
#define IOCON_FUNC_MASK             (15 << IOCON_FUNC_SHIFT)
#  define IOCON_FUNC(n)             ((uint32_t)(n) << IOCON_FUNC_SHIFT)
#define IOCON_MODE_SHIFT            (4)   /* Bits 4-5: Function mode.  Types D,A */
#define IOCON_MODE_MASK             (3 << IOCON_MODE_SHIFT)
#  define IOCON_MODE(n)             ((uint32_t)(n) << IOCON_MODE_SHIFT)
#define IOCON_I2CSLEW_SHIFT         (6)   /* Bit 6: I2C slew rate.  Type I */
#define IOCON_I2CSLEW_MASK          (1 << IOCON_I2CSLEW_SHIFT)
#  define IOCON_I2CSLEW_I2CMODE     (0)
#  define IOCON_I2CSLEW_GPIO        IOCON_I2CSLEW_MASK
#define IOCON_INVERT_SHIFT          (7)   /* Bit 7: Input polarity.  Types D,I,A */
#define IOCON_INVERT_MASK           (1 << IOCON_INVERT_SHIFT)
#  define IOCON_INVERT              IOCON_INVERT_MASK
#define IOCON_DIGIMODE_SHIFT        (8)   /* Bit 8: Analog/Digital mode.  Types D,I,A */
#define IOCON_DIGIMODE_MASK         (1 << IOCON_DIGIMODE_SHIFT)
#  define IOCON_DIGIMODE_ANALOG     (0)
#  define IOCON_DIGIMODE_DIGITAL    IOCON_DIGIMODE_MASK
#define IOCON_FILTEROFF_SHIFT       (9)   /* Bit 9: Input glitch filter.  Types D,I,A */
#define IOCON_FILTEROFF_MASK        (1 << IOCON_FILTEROFF_SHIFT)
#  define IOCON_FILTEROFF_ON        (0)
#  define IOCON_FILTEROFF_OFF       IOCON_FILTEROFF_MASK
#define IOCON_SLEW_SHIFT            (10)  /* Bit 10: Driver slew rate.  Type D */
#define IOCON_SLEW_MASK             (1 << IOCON_SLEW_SHIFT)
#  define IOCON_SLEW_STANDARD       (0)
#  define IOCON_SLEW_FAST           IOCON_SLEW_MASK
#define IOCON_I2CDRIVE_SHIFT        (10)  /* Bit 10: Sink capability of pin.  Type I */
#define IOCON_I2CDRIVE_MASK         (1 << IOCON_I2CDRIVE_SHIFT)
#  define IOCON_I2CDRIVE_LOW        (0)
#  define IOCON_I2CDRIVE_HIGH       IOCON_I2CDRIVE_MASK
#define IOCON_OD_SHIFT              (11)  /* Bit 11: Open-drain mode.  Types D,A */
#define IOCON_OD_MASK               (1 << IOCON_OD_SHIFT)
#  define IOCON_OD_PUSHPULL         (0)
#  define IOCON_OD_OPENDRAIN        IOCON_OD_MASK
#define IOCON_I2CFILTEROFF_SHIFT    (11)  /* Bit 11: I2C filter mode.  Type I */
#define IOCON_I2CFILTEROFF_MASK     (1 << IOCON_I2CFILTEROFF_SHIFT)
#  define IOCON_I2CFILTEROFF_ON     (0)
#  define IOCON_I2CFILTEROFF_OFF    IOCON_I2CFILTEROFF_MASK

/* Pin types by port */

#define IOCON_PIO0_TYPED_MASK (0x7f7e63ff) /* P0.0-9,13-14,17-22,24-30 */
#define IOCON_PIO1_TYPED_MASK (0xfffffffe) /* P1.1-31 */
#define IOCON_PIO2_TYPED_MASK (0xfffffffc) /* P2.2-31 */
#define IOCON_PIO3_TYPED_MASK (0xfe000000) /* P3.25-31 */
#define IOCON_PIO4_TYPED_MASK (0xffffffff) /* P4.0-31 */
#define IOCON_PIO5_TYPED_MASK (0x000007ff) /* P5.0-10 */

#define IOCON_PIO0_TYPEI_MASK (0x00006000) /* P0.13-14 */
#define IOCON_PIO1_TYPEI_MASK (0x00000000) /* None */
#define IOCON_PIO2_TYPEI_MASK (0x00000000) /* None */
#define IOCON_PIO3_TYPEI_MASK (0x01800000) /* P3.23-24 */
#define IOCON_PIO4_TYPEI_MASK (0x00000000) /* None */
#define IOCON_PIO5_TYPEI_MASK (0x00000000) /* None */

#define IOCON_PIO0_TYPEA_MASK (0x80831c00) /* p0.10-12,15-16,23,31 */
#define IOCON_PIO1_TYPEA_MASK (0x00000001) /* p1.0 */
#define IOCON_PIO2_TYPEA_MASK (0x00000003) /* p2.0-1 */
#define IOCON_PIO3_TYPEA_MASK (0x00600000) /* p3.21-22 */
#define IOCON_PIO4_TYPEA_MASK (0x00000000) /* None */
#define IOCON_PIO5_TYPEA_MASK (0x00000000) /* None */

#endif /* __ARCH_ARM_SRC_LPC54XX_HARDWARE_LPC54_IOCON_H */
