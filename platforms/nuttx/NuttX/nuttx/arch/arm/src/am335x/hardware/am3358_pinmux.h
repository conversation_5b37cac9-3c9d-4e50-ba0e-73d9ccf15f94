/****************************************************************************
 * arch/arm/src/am335x/hardware/am3358_pinmux.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_AM335X_HARDWARE_AM3358_PINMUX_H
#define __ARCH_ARM_SRC_AM335X_HARDWARE_AM3358_PINMUX_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include "hardware/am335x_memorymap.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Alternate Pin Functions.
 *
 * Alternative pin selections are provided with a numeric suffix like _1, _2,
 * etc. Drivers, however, will use the pin selection without the numeric
 * suffix. Additional definitions are required in the board.h file.
 * For example, if UART2 RXD connects via the MMC0_CLK pin, then the
 * following definition should appear in the board.h header file for that
 * board:
 *
 *   #define GPIO_UART2_RXD GPIO_UART2_RXD_1
 *
 * The driver will then automatically configure to use the MMC0_CLK pin for
 * UART2 RXD.
 */

/* WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific GPIO options
 * (such as pull-up or -down).
 *  Just the basics are defined for most pins in this file at the present
 * time.
 */

/* Debug Subsystem */

#define GPIO_EMU0                     (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_EMU0_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_EMU1                     (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_EMU1_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_EMU2_1                   (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_SCLK_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_EMU2_2                   (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_FSR_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_EMU2_3                   (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_XDMA_EVENT_INTR0_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_EMU3_1                   (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_D0_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_EMU3_2                   (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AXR1_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_EMU3_3                   (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_XDMA_EVENT_INTR1_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_EMU4_1                   (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN3_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_EMU4_2                   (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_CS1_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_EMU4_3                   (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AHCLKX_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)

/* LCD Controller */

#define GPIO_LCD_PCLK                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_PCLK_INDEX) | PINMUX_MODE0)
#define GPIO_LCD_DATA0                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA0_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_LCD_DATA1                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA1_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_LCD_DATA2                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA2_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_LCD_DATA3                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA3_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_LCD_DATA4                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA4_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_LCD_DATA5                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA5_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_LCD_DATA6                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA6_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_LCD_DATA7                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA7_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_LCD_DATA8                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA8_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_LCD_DATA9                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA9_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_LCD_DATA10               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA10_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_LCD_DATA11               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA11_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_LCD_DATA12               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA12_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_LCD_DATA13               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA13_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_LCD_DATA14               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA14_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_LCD_DATA15               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA15_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_LCD_DATA16               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD15_INDEX) | PINMUX_MODE1)
#define GPIO_LCD_DATA17               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD14_INDEX) | PINMUX_MODE1)
#define GPIO_LCD_DATA18               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD13_INDEX) | PINMUX_MODE1)
#define GPIO_LCD_DATA19               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD12_INDEX) | PINMUX_MODE1)
#define GPIO_LCD_DATA20               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD11_INDEX) | PINMUX_MODE1)
#define GPIO_LCD_DATA21               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD10_INDEX) | PINMUX_MODE1)
#define GPIO_LCD_DATA22               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD9_INDEX) | PINMUX_MODE1)
#define GPIO_LCD_DATA23               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD8_INDEX) | PINMUX_MODE1)
#define GPIO_LCD_HSYNC                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_HSYNC_INDEX) | PINMUX_MODE0)
#define GPIO_LCD_VSYNC                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_VSYNC_INDEX) | PINMUX_MODE0)
#define GPIO_LCD_AC_BIAS_EN           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_AC_BIAS_EN_INDEX) | PINMUX_MODE0)
#define GPIO_LCD_MEMORY_CLK_1         (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CLK_INDEX) | PINMUX_MODE1)
#define GPIO_LCD_MEMORY_CLK_2         (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_DV_INDEX) | PINMUX_MODE1)

/* General Purpose Memory Controller */

#define GPIO_GPMC_A0_1                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A0_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_A0_2                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA0_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A1_1                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A1_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_A1_2                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA1_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A1_3                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_VSYNC_INDEX) | PINMUX_MODE2)
#define GPIO_GPMC_A2_1                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A2_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_A2_2                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA2_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A2_3                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_HSYNC_INDEX) | PINMUX_MODE2)
#define GPIO_GPMC_A3_1                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A3_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_A3_2                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN3_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A3_3                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA3_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A4_1                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A4_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_A4_2                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA4_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A5_1                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A5_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_A5_2                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA5_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A6_1                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A6_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_A6_2                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA6_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A7_1                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A7_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_A7_2                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA7_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A8_1                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A8_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_A8_2                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_VSYNC_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A9_1                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A9_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_A9_2                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_HSYNC_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A10_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A10_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_A10_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_PCLK_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A11_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A11_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_A11_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_AC_BIAS_EN_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A12                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA8_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A13                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA9_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A14                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA10_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A15                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA11_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A16_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA12_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A16_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A0_INDEX) | PINMUX_MODE4)
#define GPIO_GPMC_A17_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA13_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A17_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A1_INDEX) | PINMUX_MODE4)
#define GPIO_GPMC_A18_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA14_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A18_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A2_INDEX) | PINMUX_MODE4)
#define GPIO_GPMC_A19_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA15_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A19_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A3_INDEX) | PINMUX_MODE4)
#define GPIO_GPMC_A20_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT3_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A20_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A4_INDEX) | PINMUX_MODE4)
#define GPIO_GPMC_A21_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT2_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A21_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A5_INDEX) | PINMUX_MODE4)
#define GPIO_GPMC_A22_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT1_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A22_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A6_INDEX) | PINMUX_MODE4)
#define GPIO_GPMC_A23_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT0_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A23_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A7_INDEX) | PINMUX_MODE4)
#define GPIO_GPMC_A24_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_CLK_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A24_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A8_INDEX) | PINMUX_MODE4)
#define GPIO_GPMC_A25_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_CMD_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_A25_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A9_INDEX) | PINMUX_MODE4)
#define GPIO_GPMC_A26                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A10_INDEX) | PINMUX_MODE4)
#define GPIO_GPMC_A27                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A11_INDEX) | PINMUX_MODE4)
#define GPIO_GPMC_AD0                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD0_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_AD1                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD1_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_AD2                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD2_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_AD3                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD3_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_AD4                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD4_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_AD5                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD5_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_AD6                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD6_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_AD7                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD7_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_AD8                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD8_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_AD9                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD9_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_AD10                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD10_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_AD11                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD11_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_AD12                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD12_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_AD13                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD13_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_AD14                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD14_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_AD15                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD15_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_CSN0                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN0_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_CSN1                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN1_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_CSN2                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN2_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_CSN3                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN3_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_CSN4                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_WAIT0_INDEX) | PINMUX_MODE2)
#define GPIO_GPMC_CSN5                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_WPN_INDEX) | PINMUX_MODE2)
#define GPIO_GPMC_CSN6                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_BEN1_INDEX) | PINMUX_MODE2)
#define GPIO_GPMC_CLK_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN1_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_CLK_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CLK_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_ADVN_ALE            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_ADVN_ALE_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_OEN_REN             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_OEN_REN_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_WEN                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_WEN_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_BE0N_CLE            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_BEN0_CLE_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_BE1N_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_BEN1_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_BE1N_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN2_INDEX) | PINMUX_MODE1)
#define GPIO_GPMC_WPN                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_WPN_INDEX) | PINMUX_MODE0)
#define GPIO_GPMC_WAIT0               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_WAIT0_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_WAIT1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CLK_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_GPMC_DIR                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_BEN1_INDEX) | PINMUX_MODE4)

/* Miscellaneous */

#define GPIO_CLKOUT1                  (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_XDMA_EVENT_INTR0_INDEX) | PINMUX_MODE3)
#define GPIO_CLKOUT2                  (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_XDMA_EVENT_INTR1_INDEX) | PINMUX_MODE3)
#define GPIO_EXT_WAKEUP               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_EXT_WAKEUP_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_EXTINTN                  (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_NNMI_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_TCLKIN                   (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_XDMA_EVENT_INTR1_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_XDMA_EVENT_INTR0         (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_XDMA_EVENT_INTR0_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_XDMA_EVENT_INTR1         (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_XDMA_EVENT_INTR1_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_XDMA_EVENT_INTR2_1       (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_RMII1_REF_CLK_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_XDMA_EVENT_INTR2_2       (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_CS1_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_XDMA_EVENT_INTR2_3       (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_ECAP0_IN_PWM0_OUT_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)

/* eCAP */

#define GPIO_ECAP0_IN_PWM0_OUT        (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_ECAP0_IN_PWM0_OUT_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)

#define GPIO_ECAP1_IN_PWM1_OUT_1      (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_CS1_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_ECAP1_IN_PWM1_OUT_2      (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_TXD_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_ECAP1_IN_PWM1_OUT_3      (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_I2C0_SCL_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)

#define GPIO_ECAP2_IN_PWM2_OUT_1      (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_RXD_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_ECAP2_IN_PWM2_OUT_2      (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_I2C0_SDA_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_ECAP2_IN_PWM2_OUT_3      (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AHCLKR_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)

/* eHRPWM */

#define GPIO_EHRPWM0A_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_SCLK_INDEX) | PINMUX_MODE3)
#define GPIO_EHRPWM0A_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_ACLKX_INDEX) | PINMUX_MODE1)
#define GPIO_EHRPWM0B_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_D0_INDEX) | PINMUX_MODE3)
#define GPIO_EHRPWM0B_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_FSX_INDEX) | PINMUX_MODE1)
#define GPIO_EHRPWM0_SYNCI_1          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_CS0_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_EHRPWM0_SYNCI_2          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AHCLKR_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_EHRPWM0_SYNCO_1          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD11_INDEX) | PINMUX_MODE4)
#define GPIO_EHRPWM0_SYNCO_2          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A1_INDEX) | PINMUX_MODE6)
#define GPIO_EHRPWM0_SYNCO_3          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA3_INDEX) | PINMUX_MODE3)
#define GPIO_EHRPWM0_SYNCO_4          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA9_INDEX) | PINMUX_MODE2)
#define GPIO_EHRPWM0_TRIPZONE_INPUT_1 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_D1_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_EHRPWM0_TRIPZONE_INPUT_2 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AXR0_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)

#define GPIO_EHRPWM1A_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A2_INDEX) | PINMUX_MODE6)
#define GPIO_EHRPWM1A_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA10_INDEX) | PINMUX_MODE2)
#define GPIO_EHRPWM1B_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A3_INDEX) | PINMUX_MODE6)
#define GPIO_EHRPWM1B_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA11_INDEX) | PINMUX_MODE2)
#define GPIO_EHRPWM1_TRIPZONE_INPUT_1 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A0_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_EHRPWM1_TRIPZONE_INPUT_2 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA8_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)

#define GPIO_EHRPWM2A_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD8_INDEX) | PINMUX_MODE4)
#define GPIO_EHRPWM2A_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA0_INDEX) | PINMUX_MODE3)
#define GPIO_EHRPWM2B_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD9_INDEX) | PINMUX_MODE4)
#define GPIO_EHRPWM2B_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA1_INDEX) | PINMUX_MODE3)
#define GPIO_EHRPWM2_TRIPZONE_INPUT_1 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD10_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_EHRPWM2_TRIPZONE_INPUT_2 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA2_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)

/* eQEP */

#define GPIO_EQEP0A_IN_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_ACLKR_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_EQEP0A_IN_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD1_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_EQEP0B_IN_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_FSR_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_EQEP0B_IN_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD0_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_EQEP0_INDEX_1            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AXR1_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_EQEP0_INDEX_2            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TX_EN_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_EQEP0_STROBE_1           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AHCLKX_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_EQEP0_STROBE_2           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD1_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)

#define GPIO_EQEP1A_IN_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A4_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_EQEP1A_IN_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA12_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_EQEP1B_IN_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A5_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_EQEP1B_IN_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA13_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_EQEP1_INDEX_1            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A6_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_EQEP1_INDEX_2            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA14_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_EQEP1_STROBE_1           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A7_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_EQEP1_STROBE_2           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA15_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)

#define GPIO_EQEP2A_IN_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA4_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_EQEP2A_IN_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD12_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_EQEP2B_IN_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA5_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_EQEP2B_IN_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD13_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_EQEP2_INDEX_1            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA6_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_EQEP2_INDEX_2            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD14_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_EQEP2_STROBE_1           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA7_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_EQEP2_STROBE_2           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD15_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)

/* Timer */

#define GPIO_TIMER4_1                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_XDMA_EVENT_INTR0_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_TIMER4_2                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_I2C0_SDA_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_TIMER4_3                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TX_EN_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_TIMER4_4                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_ADVN_ALE_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)

#define GPIO_TIMER5_1                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_RTSN_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_TIMER5_2                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT3_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_TIMER5_3                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MDC_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_TIMER5_4                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_BEN0_CLE_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)

#define GPIO_TIMER6_1                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_WEN_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_TIMER6_2                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MDIO_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_TIMER6_3                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT2_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_TIMER6_4                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_CTSN_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)

#define GPIO_TIMER7_1                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_OEN_REN_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_TIMER7_2                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_I2C0_SCL_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_TIMER7_3                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_CTSN_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_TIMER7_4                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_XDMA_EVENT_INTR1_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)

/* PRU-ICSS eCAP */

#define GPIO_PR1_ECAP0_CAPIN_APWM_1   (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_ECAP0_IN_PWM0_OUT_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_PR1_ECAP0_CAPIN_APWM_2   (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD15_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)

/* PRU-ICSS ECAT */

#define GPIO_PR1_EDC_LATCH0_IN_1      (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_CTSN_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_EDC_LATCH1_IN_2      (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_RTSN_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_EDC_SYNC0_OUT_1      (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_CTSN_INDEX) | PINMUX_MODE6)
#define GPIO_PR1_EDC_SYNC1_OUT_2      (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_RTSN_INDEX) | PINMUX_MODE6)
#define GPIO_PR1_EDIO_DATA_IN0        (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_D1_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_EDIO_DATA_IN1        (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_CS0_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_EDIO_DATA_IN2        (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_VSYNC_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_PR1_EDIO_DATA_IN3        (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_HSYNC_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_PR1_EDIO_DATA_IN4        (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_PCLK_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_PR1_EDIO_DATA_IN5        (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_AC_BIAS_EN_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_PR1_EDIO_DATA_IN6_1      (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN1_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_PR1_EDIO_DATA_IN6_2      (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA6_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_PR1_EDIO_DATA_IN7_1      (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN2_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_PR1_EDIO_DATA_IN7_2      (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA7_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_PR1_EDIO_DATA_OUT0       (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_D1_INDEX) | PINMUX_MODE6)
#define GPIO_PR1_EDIO_DATA_OUT1       (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_CS0_INDEX) | PINMUX_MODE6)
#define GPIO_PR1_EDIO_DATA_OUT2       (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_VSYNC_INDEX) | PINMUX_MODE4)
#define GPIO_PR1_EDIO_DATA_OUT3       (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_HSYNC_INDEX) | PINMUX_MODE4)
#define GPIO_PR1_EDIO_DATA_OUT4       (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_PCLK_INDEX) | PINMUX_MODE4)
#define GPIO_PR1_EDIO_DATA_OUT5       (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_AC_BIAS_EN_INDEX) | PINMUX_MODE4)
#define GPIO_PR1_EDIO_DATA_OUT6_1     (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN1_INDEX) | PINMUX_MODE4)
#define GPIO_PR1_EDIO_DATA_OUT6_2     (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA6_INDEX) | PINMUX_MODE4)
#define GPIO_PR1_EDIO_DATA_OUT7_1     (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN2_INDEX) | PINMUX_MODE4)
#define GPIO_PR1_EDIO_DATA_OUT7_2     (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA7_INDEX) | PINMUX_MODE4)
#define GPIO_PR1_EDIO_LATCH_IN        (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_D0_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_EDIO_SOF             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_SCLK_INDEX) | PINMUX_MODE5)

/* PRU-ICSS MDIO */

#define GPIO_PR1_MDIO_DATA            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN3_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MDIO_MDCLK           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CLK_INDEX) | PINMUX_MODE5)

/* PRU-ICSS MII */

#define GPIO_PR1_MII0_COL             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD9_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII0_CRS_1           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN3_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII0_CRS_2           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_PCLK_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII0_RXD0            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA11_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII0_RXD1            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA10_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII0_RXD2            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA9_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII0_RXD3            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA8_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII0_RXDV            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA15_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII0_RXER            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA13_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII0_RXLINK          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA12_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII0_TXD0_1          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD14_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_MII0_TXD0_2          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA5_INDEX) | PINMUX_MODE2)
#define GPIO_PR1_MII0_TXD1_1          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD13_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_MII0_TXD1_2          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA4_INDEX) | PINMUX_MODE2)
#define GPIO_PR1_MII0_TXD2_1          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD12_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_MII0_TXD2_2          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA3_INDEX) | PINMUX_MODE2)
#define GPIO_PR1_MII0_TXD3_1          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD11_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_MII0_TXD3_2          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA2_INDEX) | PINMUX_MODE2)
#define GPIO_PR1_MII0_TXEN_1          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD10_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_MII0_TXEN_2          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA1_INDEX) | PINMUX_MODE2)
#define GPIO_PR1_MII_MR0_CLK          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA14_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII_MT0_CLK_1        (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA0_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII_MT0_CLK_2        (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD8_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)

#define GPIO_PR1_MII1_COL             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_WAIT0_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII1_CRS_1           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_AC_BIAS_EN_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII1_CRS_2           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CLK_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII1_RXD0            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A8_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII1_RXD1            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A7_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII1_RXD2            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A6_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII1_RXD3            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A5_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII1_RXDV            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A10_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII1_RXER            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A11_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII1_RXLINK          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_BEN1_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII1_TXD0            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A4_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_MII1_TXD1            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A3_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_MII1_TXD2            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A2_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_MII1_TXD3            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A1_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_MII1_TXEN            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_WPN_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_MII_MR1_CLK          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A9_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_MII_MT1_CLK          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A0_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)

/* PRU-ICSS UART0 */

#define GPIO_PR1_UART0_CTSN_1         (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_SCLK_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_PR1_UART0_CTSN_2         (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_CTSN_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_UART0_RTSN_1         (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_D0_INDEX) | PINMUX_MODE4)
#define GPIO_PR1_UART0_RTSN_2         (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_RTSN_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_UART0_RXD_1          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_D1_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_PR1_UART0_RXD_2          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_RXD_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_PR1_UART0_TXD_1          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_CS0_INDEX) | PINMUX_MODE4)
#define GPIO_PR1_UART0_TXD_2          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_TXD_INDEX) | PINMUX_MODE5)

/* PRU-ICSS PRU0 General Purpose Inputs */

#define GPIO_PR1_PRU0_R31_0           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_ACLKX_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU0_R31_1           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_FSX_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU0_R31_2           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AXR0_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU0_R31_3           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AHCLKR_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU0_R31_4           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_ACLKR_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU0_R31_5           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_FSR_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU0_R31_6           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AXR1_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU0_R31_7           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AHCLKX_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU0_R31_8           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT3_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU0_R31_9           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT2_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU0_R31_10          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT1_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU0_R31_11          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT0_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU0_R31_12          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_CLK_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU0_R31_13          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_CMD_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU0_R31_14          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD14_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU0_R31_15          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD15_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU0_R31_16_1        (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_TXD_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU0_R31_16_2        (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_XDMA_EVENT_INTR1_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)

/* PRU_ICSS PRU0 General Purpose Outputs */

#define GPIO_PR1_PRU0_R30_0           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_ACLKX_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU0_R30_1           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_FSX_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU0_R30_2           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AXR0_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU0_R30_3           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AHCLKR_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU0_R30_4           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_ACLKR_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU0_R30_5           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_FSR_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU0_R30_6           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AXR1_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU0_R30_7           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AHCLKX_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU0_R30_8           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT3_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU0_R30_9           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT2_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU0_R30_10          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT1_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU0_R30_11          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT0_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU0_R30_12          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_CLK_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU0_R30_13          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_CMD_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU0_R30_14          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD12_INDEX) | PINMUX_MODE6)
#define GPIO_PR1_PRU0_R30_15          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD13_INDEX) | PINMUX_MODE6)

/* PRU-ICSS PRU1 General Purpose Inputs */

#define GPIO_PR1_PRU1_R31_0           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA0_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU1_R31_1           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA1_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU1_R31_2           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA2_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU1_R31_3           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA3_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU1_R31_4           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA4_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU1_R31_5           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA5_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU1_R31_6           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA6_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU1_R31_7           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA7_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU1_R31_8           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_VSYNC_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU1_R31_9           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_HSYNC_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU1_R31_10          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_PCLK_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU1_R31_11          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_AC_BIAS_EN_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU1_R31_12          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN1_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU1_R31_13          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN2_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU1_R31_14          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_RXD_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU1_R31_15          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_TXD_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU1_R31_16_1        (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_RXD_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_PR1_PRU1_R31_16_2        (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_XDMA_EVENT_INTR0_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)

/* PRU_ICSS PRU1 General Purpose Outputs */

#define GPIO_PR1_PRU1_R30_0           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA0_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU1_R30_1           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA1_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU1_R30_2           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA2_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU1_R30_3           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA3_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU1_R30_4           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA4_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU1_R30_5           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA5_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU1_R30_6           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA6_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU1_R30_7           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA7_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU1_R30_8           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_VSYNC_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU1_R30_9           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_HSYNC_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU1_R30_10          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_PCLK_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU1_R30_11          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_AC_BIAS_EN_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU1_R30_12          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN1_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU1_R30_13          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN2_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU1_R30_14          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_RXD_INDEX) | PINMUX_MODE5)
#define GPIO_PR1_PRU1_R30_15          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_TXD_INDEX) | PINMUX_MODE5)

/* Removable Media SD/MMC Card Interfaces */

#define GPIO_MMC0_CLK                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_CLK_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_MMC0_CMD                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_CMD_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_MMC0_DAT0                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT0_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_MMC0_DAT1                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT1_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_MMC0_DAT2                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT2_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_MMC0_DAT3                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT3_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_MMC0_DAT4                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD2_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC0_DAT5                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD3_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC0_DAT6                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_CLK_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC0_DAT7                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TX_CLK_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC0_POW_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_RMII1_REF_CLK_INDEX) | PINMUX_MODE5)
#define GPIO_MMC0_POW_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_CS1_INDEX) | PINMUX_MODE3)
#define GPIO_MMC0_SDCD_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_ACLKX_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MMC0_SDCD_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MDIO_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MMC0_SDCD_3              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_CS1_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_MMC0_SDWP_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_ECAP0_IN_PWM0_OUT_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_MMC0_SDWP_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_ACLKR_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MMC0_SDWP_3              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MDC_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)

#define GPIO_MMC1_CLK_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN1_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_CLK_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MDC_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_CLK_3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD0_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_CMD_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN2_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_CMD_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MDIO_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_CMD_3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD1_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT0_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD0_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT0_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD8_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT0_3              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TX_CLK_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT1_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD1_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT1_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD9_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT1_3              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_CLK_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT2_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD2_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT2_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD10_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT2_3              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD3_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT3_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD3_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT3_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD11_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT3_3              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD2_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT4_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD4_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT4_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD12_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT5_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD5_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT5_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD13_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT6_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD6_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT6_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD14_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT7_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD7_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_DAT7_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD15_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_SDCD_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_WAIT0_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_SDCD_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_FSX_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_SDWP_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_D1_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_MMC1_SDWP_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_RXD_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)

#define GPIO_MMC2_CLK_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CLK_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_CLK_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MDC_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_CLK_3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD1_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_CMD_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN3_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_CMD_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MDIO_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_CMD_3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TX_EN_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT0_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A1_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT0_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD12_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT0_3              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_DV_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT1_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A2_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT1_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD13_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT1_3              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD3_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT2_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A3_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT2_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD14_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT2_3              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD2_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT3_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD15_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT3_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_BEN1_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT3_3              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_COL_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT4_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A6_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT4_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD8_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT5_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A7_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT5_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD9_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT6_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A8_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT6_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD10_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT7_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A9_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_DAT7_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_AD11_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_SDCD_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_WPN_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_SDCD_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AXR0_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_SDWP_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_CS0_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_MMC2_SDWP_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_TXD_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)

/* CAN */

#define GPIO_DCAN0_RX_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD2_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP)
#define GPIO_DCAN0_RX_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_TXD_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP)
#define GPIO_DCAN0_RX_3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_RTSN_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP)
#define GPIO_DCAN0_TX_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD3_INDEX) | PINMUX_MODE1)
#define GPIO_DCAN0_TX_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_RXD_INDEX) | PINMUX_MODE2)
#define GPIO_DCAN0_TX_3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_CTSN_INDEX) | PINMUX_MODE2)

#define GPIO_DCAN1_RX_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_CMD_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP)
#define GPIO_DCAN1_RX_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_RTSN_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP)
#define GPIO_DCAN1_RX_3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_TXD_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP)
#define GPIO_DCAN1_TX_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_CLK_INDEX) | PINMUX_MODE4)
#define GPIO_DCAN1_TX_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_CTSN_INDEX) | PINMUX_MODE2)
#define GPIO_DCAN1_TX_3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_RXD_INDEX) | PINMUX_MODE2)

/* GEMAC CPSW MDIO */

#define GPIO_MDIO_CLK                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MDC_INDEX) | PINMUX_MODE0)
#define GPIO_MDIO_DATA                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MDIO_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)

/* GEMAC CPSW MII */

#define GPIO_GMII1_COL                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_COL_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GMII1_CRS                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_CRS_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GMII1_RXCLK              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_CLK_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GMII1_RXD0               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD0_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GMII1_RXD1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD1_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GMII1_RXD2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD2_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GMII1_RXD3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD3_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GMII1_RXDV               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_DV_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GMII1_RXERR              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_ER_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GMII1_TXCLK              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TX_CLK_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_GMII1_TXD0               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD0_INDEX) | PINMUX_MODE0)
#define GPIO_GMII1_TXD1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD1_INDEX) | PINMUX_MODE0)
#define GPIO_GMII1_TXD2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD2_INDEX) | PINMUX_MODE0)
#define GPIO_GMII1_TXD3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD3_INDEX) | PINMUX_MODE0)
#define GPIO_GMII1_TXEN               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TX_EN_INDEX) | PINMUX_MODE0)

#define GPIO_GMII2_COL                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_BEN1_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_GMII2_CRS                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_WAIT0_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_GMII2_RXCLK              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A7_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_GMII2_RXD0               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A11_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_GMII2_RXD1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A10_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_GMII2_RXD2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A9_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_GMII2_RXD3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A8_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_GMII2_RXDV               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A1_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_GMII2_RXERR              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_WPN_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_GMII2_TXCLK              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A6_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_GMII2_TXD0               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A5_INDEX) | PINMUX_MODE1)
#define GPIO_GMII2_TXD1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A4_INDEX) | PINMUX_MODE1)
#define GPIO_GMII2_TXD2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A3_INDEX) | PINMUX_MODE1)
#define GPIO_GMII2_TXD3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A2_INDEX) | PINMUX_MODE1)
#define GPIO_GMII2_TXEN               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A0_INDEX) | PINMUX_MODE1)

/* GEMAC CPSW RGMII */

#define GPIO_RGMII1_RCLK              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_CLK_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_RGMII1_RCTL              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_DV_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_RGMII1_RD0               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD0_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_RGMII1_RD1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD1_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_RGMII1_RD2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD2_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_RGMII1_RD3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD3_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_RGMII1_TCLK              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TX_CLK_INDEX) | PINMUX_MODE2)
#define GPIO_RGMII1_TCTL              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TX_EN_INDEX) | PINMUX_MODE2)
#define GPIO_RGMII1_TD0               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD0_INDEX) | PINMUX_MODE2)
#define GPIO_RGMII1_TD1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD1_INDEX) | PINMUX_MODE2)
#define GPIO_RGMII1_TD2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD2_INDEX) | PINMUX_MODE2)
#define GPIO_RGMII1_TD3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD3_INDEX) | PINMUX_MODE2)

#define GPIO_RGMII2_RCLK              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A7_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_RGMII2_RCTL              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A1_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_RGMII2_RD0               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A11_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_RGMII2_RD1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A10_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_RGMII2_RX2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A9_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_RGMII2_RD3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A8_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_RGMII2_TCLK              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A6_INDEX) | PINMUX_MODE2)
#define GPIO_RGMII2_TCTL              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A0_INDEX) | PINMUX_MODE2)
#define GPIO_RGMII2_TD0               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A5_INDEX) | PINMUX_MODE2)
#define GPIO_RGMII2_TD1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A4_INDEX) | PINMUX_MODE2)
#define GPIO_RGMII2_TD2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A3_INDEX) | PINMUX_MODE2)
#define GPIO_RGMII2_TD3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A2_INDEX) | PINMUX_MODE2)

/* GEMAC CPSW RMII */

#define GPIO_RMII1_CRS_DV             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_CRS_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_RMII1_REFCLK             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_RMII1_REF_CLK_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_RMII1_RXD0               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD0_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_RMII1_RXD1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD1_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_RMII1_RXERR              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_ER_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_RMII1_TXD0               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD0_INDEX) | PINMUX_MODE1)
#define GPIO_RMII1_TXD1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD1_INDEX) | PINMUX_MODE1)
#define GPIO_RMII1_TXEN               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TX_EN_INDEX) | PINMUX_MODE1)

#define GPIO_RMII2_CRS_DV_1           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CSN3_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_RMII2_CRS_DV_2           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_WAIT0_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_RMII2_REFCLK             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_COL_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_RMII2_RXD0               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A11_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_RMII2_RXD1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A10_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_RMII2_RXERR              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_WPN_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_RMII2_TXD0               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A5_INDEX) | PINMUX_MODE3)
#define GPIO_RMII2_TXD1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A4_INDEX) | PINMUX_MODE3)
#define GPIO_RMII2_TXEN               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A0_INDEX) | PINMUX_MODE3)

/* I2C */

#define GPIO_I2C0_SCL                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_I2C0_SCL_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP | PINMUX_SLEW_SLOW)
#define GPIO_I2C0_SDA                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_I2C0_SDA_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP | PINMUX_SLEW_SLOW)

#define GPIO_I2C1_SCL_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_ER_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP | PINMUX_SLEW_SLOW)
#define GPIO_I2C1_SCL_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_CS0_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP | PINMUX_SLEW_SLOW)
#define GPIO_I2C1_SCL_3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_RTSN_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP | PINMUX_SLEW_SLOW)
#define GPIO_I2C1_SCL_4               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_TXD_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP | PINMUX_SLEW_SLOW)
#define GPIO_I2C1_SDA_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_CRS_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP | PINMUX_SLEW_SLOW)
#define GPIO_I2C1_SDA_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_D1_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP | PINMUX_SLEW_SLOW)
#define GPIO_I2C1_SDA_3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_CTSN_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP | PINMUX_SLEW_SLOW)
#define GPIO_I2C1_SDA_4               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_RXD_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP | PINMUX_SLEW_SLOW)

#define GPIO_I2C2_SCL_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_D0_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP | PINMUX_SLEW_SLOW)
#define GPIO_I2C2_SCL_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_TXD_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP | PINMUX_SLEW_SLOW)
#define GPIO_I2C2_SCL_3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_RTSN_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP | PINMUX_SLEW_SLOW)
#define GPIO_I2C2_SDA_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_SCLK_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP | PINMUX_SLEW_SLOW)
#define GPIO_I2C2_SDA_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_RXD_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP | PINMUX_SLEW_SLOW)
#define GPIO_I2C2_SDA_3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_CTSN_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE | PINMUX_PULL_TYPE_UP | PINMUX_SLEW_SLOW)

/* McASP */

#define GPIO_MCASP0_ACLKR_1           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_BEN1_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_ACLKR_2           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA12_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_ACLKR_3           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_ACLKR_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_ACLKR_4           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_DV_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_ACLKX_1           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A8_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_ACLKX_2           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA8_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_ACLKX_3           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_ACLKX_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_ACLKX_4           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TX_CLK_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AHCLKR_1          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA11_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AHCLKR_2          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AHCLKR_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AHCLKX_1          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA15_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AHCLKX_2          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AHCLKX_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AHCLKX_3          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD2_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AXR0_1            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A10_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AXR0_2            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA10_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AXR0_3            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AXR0_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AXR0_4            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD3_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AXR1_1            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A11_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AXR1_2            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA14_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AXR1_3            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AXR1_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AXR1_4            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD2_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AXR2_1            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA11_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AXR2_2            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA12_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AXR2_3            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_ACLKR_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AXR2_4            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AHCLKR_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AXR2_5            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_COL_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AXR3_1            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA13_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AXR3_2            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA15_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AXR3_3            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AHCLKX_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AXR3_4            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_FSR_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_AXR3_5            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD0_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_FSR_1             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_CLK_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_FSR_2             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA13_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_FSR_3             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_FSR_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_FSR_4             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD3_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_FSX_1             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_A9_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_FSX_2             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA9_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_FSX_3             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_FSX_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_MCASP0_FSX_4             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_CLK_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)

#define GPIO_MCASP1_ACLKR_1           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD0_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_ACLKR_2           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD0_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_ACLKX_1           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_ACLKR_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_ACLKX_2           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_DV_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_ACLKX_3           (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_CRS_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_AHCLKR_1          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD0_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_AHCLKX_1          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD0_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_AHCLKX_2          (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_RMII1_REF_CLK_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_AXR0_1            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AXR1_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_AXR0_2            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TX_EN_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_AXR0_3            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD2_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_AXR1_1            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AHCLKX_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_AXR1_2            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD1_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_AXR2_1            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_COL_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_AXR2_2            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD0_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_AXR3_1            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD1_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_AXR3_2            (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_RMII1_REF_CLK_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_FSR_1             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD1_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_FSR_2             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD1_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_FSX_1             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_FSR_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_FSX_2             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_ER_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_MCASP1_FSX_3             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD3_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)

/* SPI */

#define GPIO_SPI0_CS0                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_CS0_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_SPI0_CS1                 (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_CS1_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_SPI0_D0                  (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_D0_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_SPI0_D1                  (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_D1_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_SPI0_SCLK                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_SCLK_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)

#define GPIO_SPI1_CS0_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AHCLKR_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_SPI1_CS0_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_RMII1_REF_CLK_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_SPI1_CS0_3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_RXD_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_SPI1_CS0_4               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_RTSN_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_SPI1_CS0_5               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_CTSN_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_SPI1_CS1_1               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_ECAP0_IN_PWM0_OUT_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_SPI1_CS1_2               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_TXD_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_SPI1_CS1_3               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_RTSN_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_SPI1_CS1_4               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_XDMA_EVENT_INTR0_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_SPI1_D0_1                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_FSX_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_SPI1_D0_2                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_CRS_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_SPI1_D0_3                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_CTSN_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_SPI1_D1_1                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_AXR0_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_SPI1_D1_2                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_ER_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_SPI1_D1_3                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_RTSN_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_SPI1_SCLK_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_ECAP0_IN_PWM0_OUT_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_SPI1_SCLK_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MCASP0_ACLKX_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_SPI1_SCLK_3              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_COL_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)

/* UART */

#define GPIO_UART0_CTSN               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_CTSN_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_UART0_RTSN               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_RTSN_INDEX) | PINMUX_MODE0)
#define GPIO_UART0_RXD                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_RXD_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_UART0_TXD                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_TXD_INDEX) | PINMUX_MODE0)

#define GPIO_UART1_CTSN               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_CTSN_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_UART1_DCDN_1             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TX_CLK_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_UART1_DCDN_2             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT3_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_UART1_DSRN_1             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_CLK_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_UART1_DSRN_2             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT2_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_UART1_DTRN_1             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD3_INDEX) | PINMUX_MODE5)
#define GPIO_UART1_DTRN_2             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT1_INDEX) | PINMUX_MODE4)
#define GPIO_UART1_RIN_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD2_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_UART1_RIN_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT0_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_UART1_RTSN               (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_RTSN_INDEX) | PINMUX_MODE0)
#define GPIO_UART1_RXD                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_RXD_INDEX) | PINMUX_MODE0 | PINMUX_RX_ENABLE)
#define GPIO_UART1_TXD                (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART1_TXD_INDEX) | PINMUX_MODE0)

#define GPIO_UART2_CTSN_1             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_I2C0_SDA_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_UART2_CTSN_2             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA8_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_UART2_RTSN_1             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_I2C0_SCL_INDEX) | PINMUX_MODE2)
#define GPIO_UART2_RTSN_2             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA9_INDEX) | PINMUX_MODE6)
#define GPIO_UART2_RXD_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TX_CLK_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE
#define GPIO_UART2_RXD_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_CRS_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_UART2_RXD_3              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_CLK_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_UART2_RXD_4              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_SCLK_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_UART2_TXD_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_ER_INDEX) | PINMUX_MODE6)
#define GPIO_UART2_TXD_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_CLK_INDEX) | PINMUX_MODE1)
#define GPIO_UART2_TXD_3              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_CMD_INDEX) | PINMUX_MODE3)
#define GPIO_UART2_TXD_4              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_D0_INDEX) | PINMUX_MODE1)

#define GPIO_UART3_CTSN_1             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA10_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_UART3_CTSN_2             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MDIO_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_UART3_CTSN_3             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_CLK_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_UART3_RTSN_1             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA11_INDEX) | PINMUX_MODE6)
#define GPIO_UART3_RTSN_2             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MDC_INDEX) | PINMUX_MODE3)
#define GPIO_UART3_RTSN_3             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_CMD_INDEX) | PINMUX_MODE2)
#define GPIO_UART3_RXD_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD3_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_UART3_RXD_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT1_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_UART3_RXD_3              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_SPI0_CS1_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_UART3_TXD_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_ECAP0_IN_PWM0_OUT_INDEX) | PINMUX_MODE1)
#define GPIO_UART3_TXD_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RXD2_INDEX) | PINMUX_MODE1)
#define GPIO_UART3_TXD_3              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT0_INDEX) | PINMUX_MODE3)

#define GPIO_UART4_CTSN_1             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA12_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_UART4_CTSN_2             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT3_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_UART4_RTSN_1             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA13_INDEX) | PINMUX_MODE6)
#define GPIO_UART4_RTSN_2             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT2_INDEX) | PINMUX_MODE2)
#define GPIO_UART4_RXD_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_WAIT0_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_UART4_RXD_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD3_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_UART4_RXD_3              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_CTSN_INDEX) | PINMUX_MODE1 | PINMUX_RX_ENABLE)
#define GPIO_UART4_TXD_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_GPMC_WPN_INDEX) | PINMUX_MODE6)
#define GPIO_UART4_TXD_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_TXD2_INDEX) | PINMUX_MODE3)
#define GPIO_UART4_TXD_3              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_UART0_RTSN_INDEX) | PINMUX_MODE1)

#define GPIO_UART5_CTSN_1             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA14_INDEX) | PINMUX_MODE6 | PINMUX_RX_ENABLE)
#define GPIO_UART5_CTSN_2             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_CRS_INDEX) | PINMUX_MODE5 | PINMUX_RX_ENABLE)
#define GPIO_UART5_CTSN_3             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT1_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_UART5_RTSN_1             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA15_INDEX) | PINMUX_MODE6)
#define GPIO_UART5_RTSN_2             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_ER_INDEX) | PINMUX_MODE5)
#define GPIO_UART5_RTSN_3             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MMC0_DAT0_INDEX) | PINMUX_MODE2)
#define GPIO_UART5_RXD_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA9_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_UART5_RXD_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA14_INDEX) | PINMUX_MODE4 | PINMUX_RX_ENABLE)
#define GPIO_UART5_RXD_3              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MDIO_INDEX) | PINMUX_MODE2 | PINMUX_RX_ENABLE)
#define GPIO_UART5_RXD_4              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_COL_INDEX) | PINMUX_MODE3 | PINMUX_RX_ENABLE)
#define GPIO_UART5_TXD_1              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_LCD_DATA8_INDEX) | PINMUX_MODE4)
#define GPIO_UART5_TXD_2              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MDC_INDEX) | PINMUX_MODE2)
#define GPIO_UART5_TXD_3              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_MII1_RX_DV_INDEX) | PINMUX_MODE3)
#define GPIO_UART5_TXD_4              (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_RMII1_REF_CLK_INDEX) | PINMUX_MODE3)

/* USB */

#define GPIO_USB0_DRVVBUS             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_USB0_DRVVBUS_INDEX) | PINMUX_MODE0)

#define GPIO_USB1_DRVVBUS             (GPIO_PERIPH | GPIO_PADCTL(AM335X_PADCTL_USB1_DRVVBUS_INDEX) | PINMUX_MODE0)

#endif /* __ARCH_ARM_SRC_AM335X_HARDWARE_AM3358_PINMUX_H */
