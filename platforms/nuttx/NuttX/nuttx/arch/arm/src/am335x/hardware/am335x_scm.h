/****************************************************************************
 * arch/arm/src/am335x/hardware/am335x_scm.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_AM335X_HARDWARE_AM335X_SCM_H
#define __ARCH_ARM_SRC_AM335X_HARDWARE_AM335X_SCM_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include "hardware/am335x_memorymap.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Control Module Register Offsets ******************************************/

#define AM335X_SCM_CTRL_SYS_CONF_OFFSET         0x0010
#define AM335X_SCM_CTRL_STATUS_OFFSET           0x0040
#define AM335X_SCM_CTRL_EMIF_SDRAM_CONF_OFFSET  0x0110
#define AM335X_SCM_CORE_SLDO_CTRL_OFFSET        0x0428
#define AM335X_SCM_MPU_SLDO_CTRL_OFFSET         0x042c
#define AM335X_SCM_CLK32KDIVRATIO_CTRL_OFFSET   0x0444
#define AM335X_SCM_BANDGAP_CTRL_OFFSET          0x0448
#define AM335X_SCM_BANDGAP_TRIM_OFFSET          0x044c
#define AM335X_SCM_PLL_CLKINPULOW_CTRL_OFFSET   0x0458
#define AM335X_SCM_MOSC_CTRL_OFFSET             0x0468
#define AM335X_SCM_DEEPSLEEP_CTRL_OFFSET        0x0470
#define AM335X_SCM_DPLL_PWR_SW_STATUS_OFFSET    0x050c
#define AM335X_SCM_DEVICE_ID_OFFSET             0x0600
#define AM335X_SCM_DEV_FEATURE_OFFSET           0x0604
#define AM335X_SCM_INIT_PRIORITY_0_OFFSET       0x0608
#define AM335X_SCM_INIT_PRIORITY_1_OFFSET       0x060c
#define AM335X_SCM_TPTC_CFG_OFFSET              0x0614
#define AM335X_SCM_USB_CTRL0_OFFSET             0x0620
#define AM335X_SCM_USB_STS0_OFFSET              0x0624
#define AM335X_SCM_USB_CTRL1_OFFSET             0x0628
#define AM335X_SCM_USB_STS1_OFFSET              0x062c
#define AM335X_SCM_MAC_ID0_LO_OFFSET            0x0630
#define AM335X_SCM_MAC_ID0_HI_OFFSET            0x0634
#define AM335X_SCM_MAC_ID1_LO_OFFSET            0x0638
#define AM335X_SCM_MAC_ID1_HI_OFFSET            0x063c
#define AM335X_SCM_DCAN_RAMINIT_OFFSET          0x0644
#define AM335X_SCM_USB_WKUP_CTRL_OFFSET         0x0648
#define AM335X_SCM_GMII_SEL_OFFSET              0x0650
#define AM335X_SCM_PWMSS_CTRL_OFFSET            0x0664
#define AM335X_SCM_MREGPRIO_0_OFFSET            0x0670
#define AM335X_SCM_MREGPRIO_1_OFFSET            0x0674
#define AM335X_SCM_HW_EVENT_SEL_GRP1_OFFSET     0x0690
#define AM335X_SCM_HW_EVENT_SEL_GRP2_OFFSET     0x0694
#define AM335X_SCM_HW_EVENT_SEL_GRP3_OFFSET     0x0698
#define AM335X_SCM_HW_EVENT_SEL_GRP4_OFFSET     0x069c
#define AM335X_SCM_SMRT_CTRL_OFFSET             0x06a0
#define AM335X_SCM_MPUSS_HW_DEBUG_SEL_OFFSET    0x06a4
#define AM335X_SCM_MPUSS_HW_DBG_INFO_OFFSET     0x06a8
#define AM335X_SCM_VDD_MPU_OPP_050_OFFSET       0x0770
#define AM335X_SCM_VDD_MPU_OPP_100_OFFSET       0x0774
#define AM335X_SCM_VDD_MPU_OPP_120_OFFSET       0x0778
#define AM335X_SCM_VDD_MPU_OPP_TURBO_OFFSET     0x077c
#define AM335X_SCM_VDD_CORE_OPP_050_OFFSET      0x07b8
#define AM335X_SCM_VDD_CORE_OPP_100_OFFSET      0x07bc
#define AM335X_SCM_BB_SCALE_OFFSET              0x07d0
#define AM335X_SCM_USB_VID_PID_OFFSET           0x07f4
#define AM335X_SCM_EFUSE_SMA_OFFSET             0x07fc

#define AM335X_SCM_CQDETECT_STATUS_OFFSET       0x0e00
#define AM335X_SCM_DDR_IO_CTRL_OFFSET           0x0e04
#define AM335X_SCM_VTP_CTRL_OFFSET              0x0e0c
#define AM335X_SCM_VREF_CTRL_OFFSET             0x0e14
#define AM335X_SCM_TPCC_EVT_MUX_0_3_OFFSET      0x0f90
#define AM335X_SCM_TPCC_EVT_MUX_4_7_OFFSET      0x0f94
#define AM335X_SCM_TPCC_EVT_MUX_8_11_OFFSET     0x0f98
#define AM335X_SCM_TPCC_EVT_MUX_12_15_OFFSET    0x0f9c
#define AM335X_SCM_TPCC_EVT_MUX_16_19_OFFSET    0x0fa0
#define AM335X_SCM_TPCC_EVT_MUX_20_23_OFFSET    0x0fa4
#define AM335X_SCM_TPCC_EVT_MUX_24_27_OFFSET    0x0fa8
#define AM335X_SCM_TPCC_EVT_MUX_28_31_OFFSET    0x0fac
#define AM335X_SCM_TPCC_EVT_MUX_32_35_OFFSET    0x0fb0
#define AM335X_SCM_TPCC_EVT_MUX_36_39_OFFSET    0x0fb4
#define AM335X_SCM_TPCC_EVT_MUX_40_43_OFFSET    0x0fb8
#define AM335X_SCM_TPCC_EVT_MUX_44_47_OFFSET    0x0fbc
#define AM335X_SCM_TPCC_EVT_MUX_48_51_OFFSET    0x0fc0
#define AM335X_SCM_TPCC_EVT_MUX_52_55_OFFSET    0x0fc4
#define AM335X_SCM_TPCC_EVT_MUX_56_59_OFFSET    0x0fc8
#define AM335X_SCM_TPCC_EVT_MUX_60_63_OFFSET    0x0fcc
#define AM335X_SCM_TIMER_EVT_CAPT_OFFSET        0x0fd0
#define AM335X_SCM_ECAP_EVT_CAPT_OFFSET         0x0fd4
#define AM335X_SCM_ADC_EVT_CAPT_OFFSET          0x0fd8
#define AM335X_SCM_RESET_ISO_OFFSET             0x1000
#define AM335X_SCM_DPLL_PWR_SW_CTRL_OFFSET      0x1318
#define AM335X_SCM_DDR_CKE_CTRL_OFFSET          0x131c
#define AM335X_SCM_SMA2_OFFSET                  0x1320
#define AM335X_SCM_M3_TXEV_EOI_OFFSET           0x1324
#define AM335X_SCM_IPC_MSG_REG0_OFFSET          0x1328
#define AM335X_SCM_IPC_MSG_REG1_OFFSET          0x132c
#define AM335X_SCM_IPC_MSG_REG2_OFFSET          0x1330
#define AM335X_SCM_IPC_MSG_REG3_OFFSET          0x1334
#define AM335X_SCM_IPC_MSG_REG4_OFFSET          0x1338
#define AM335X_SCM_IPC_MSG_REG5_OFFSET          0x133c
#define AM335X_SCM_IPC_MSG_REG6_OFFSET          0x1340
#define AM335X_SCM_IPC_MSG_REG7_OFFSET          0x1344
#define AM335X_SCM_DDR_CMD0_IOCTRL_OFFSET       0x1404
#define AM335X_SCM_DDR_CMD1_IOCTRL_OFFSET       0x1408
#define AM335X_SCM_DDR_CMD2_IOCTRL_OFFSET       0x140c
#define AM335X_SCM_DDR_DATA0_IOCTRL_OFFSET      0x1440
#define AM335X_SCM_DDR_DATA1_IOCTRL_OFFSET      0x1444

/* Pad Control Registers */

/* Pad Control Register Indices (used by software for table lookups) */

#define AM335X_PADCTL_GPMC_AD0_INDEX            0
#define AM335X_PADCTL_GPMC_AD1_INDEX            1
#define AM335X_PADCTL_GPMC_AD2_INDEX            2
#define AM335X_PADCTL_GPMC_AD3_INDEX            3
#define AM335X_PADCTL_GPMC_AD4_INDEX            4
#define AM335X_PADCTL_GPMC_AD5_INDEX            5
#define AM335X_PADCTL_GPMC_AD6_INDEX            6
#define AM335X_PADCTL_GPMC_AD7_INDEX            7
#define AM335X_PADCTL_GPMC_AD8_INDEX            8
#define AM335X_PADCTL_GPMC_AD9_INDEX            9
#define AM335X_PADCTL_GPMC_AD10_INDEX           10
#define AM335X_PADCTL_GPMC_AD11_INDEX           11
#define AM335X_PADCTL_GPMC_AD12_INDEX           12
#define AM335X_PADCTL_GPMC_AD13_INDEX           13
#define AM335X_PADCTL_GPMC_AD14_INDEX           14
#define AM335X_PADCTL_GPMC_AD15_INDEX           15
#define AM335X_PADCTL_GPMC_A0_INDEX             16
#define AM335X_PADCTL_GPMC_A1_INDEX             17
#define AM335X_PADCTL_GPMC_A2_INDEX             18
#define AM335X_PADCTL_GPMC_A3_INDEX             19
#define AM335X_PADCTL_GPMC_A4_INDEX             20
#define AM335X_PADCTL_GPMC_A5_INDEX             21
#define AM335X_PADCTL_GPMC_A6_INDEX             22
#define AM335X_PADCTL_GPMC_A7_INDEX             23
#define AM335X_PADCTL_GPMC_A8_INDEX             24
#define AM335X_PADCTL_GPMC_A9_INDEX             25
#define AM335X_PADCTL_GPMC_A10_INDEX            26
#define AM335X_PADCTL_GPMC_A11_INDEX            27
#define AM335X_PADCTL_GPMC_WAIT0_INDEX          28
#define AM335X_PADCTL_GPMC_WPN_INDEX            29
#define AM335X_PADCTL_GPMC_BEN1_INDEX           30
#define AM335X_PADCTL_GPMC_CSN0_INDEX           31
#define AM335X_PADCTL_GPMC_CSN1_INDEX           32
#define AM335X_PADCTL_GPMC_CSN2_INDEX           33
#define AM335X_PADCTL_GPMC_CSN3_INDEX           34
#define AM335X_PADCTL_GPMC_CLK_INDEX            35
#define AM335X_PADCTL_GPMC_ADVN_ALE_INDEX       36
#define AM335X_PADCTL_GPMC_OEN_REN_INDEX        37
#define AM335X_PADCTL_GPMC_WEN_INDEX            38
#define AM335X_PADCTL_GPMC_BEN0_CLE_INDEX       39
#define AM335X_PADCTL_LCD_DATA0_INDEX           40
#define AM335X_PADCTL_LCD_DATA1_INDEX           41
#define AM335X_PADCTL_LCD_DATA2_INDEX           42
#define AM335X_PADCTL_LCD_DATA3_INDEX           43
#define AM335X_PADCTL_LCD_DATA4_INDEX           44
#define AM335X_PADCTL_LCD_DATA5_INDEX           45
#define AM335X_PADCTL_LCD_DATA6_INDEX           46
#define AM335X_PADCTL_LCD_DATA7_INDEX           47
#define AM335X_PADCTL_LCD_DATA8_INDEX           48
#define AM335X_PADCTL_LCD_DATA9_INDEX           49
#define AM335X_PADCTL_LCD_DATA10_INDEX          50
#define AM335X_PADCTL_LCD_DATA11_INDEX          51
#define AM335X_PADCTL_LCD_DATA12_INDEX          52
#define AM335X_PADCTL_LCD_DATA13_INDEX          53
#define AM335X_PADCTL_LCD_DATA14_INDEX          54
#define AM335X_PADCTL_LCD_DATA15_INDEX          55
#define AM335X_PADCTL_LCD_VSYNC_INDEX           56
#define AM335X_PADCTL_LCD_HSYNC_INDEX           57
#define AM335X_PADCTL_LCD_PCLK_INDEX            58
#define AM335X_PADCTL_LCD_AC_BIAS_EN_INDEX      59
#define AM335X_PADCTL_MMC0_DAT3_INDEX           60
#define AM335X_PADCTL_MMC0_DAT2_INDEX           61
#define AM335X_PADCTL_MMC0_DAT1_INDEX           62
#define AM335X_PADCTL_MMC0_DAT0_INDEX           63
#define AM335X_PADCTL_MMC0_CLK_INDEX            64
#define AM335X_PADCTL_MMC0_CMD_INDEX            65
#define AM335X_PADCTL_MII1_COL_INDEX            66
#define AM335X_PADCTL_MII1_CRS_INDEX            67
#define AM335X_PADCTL_MII1_RX_ER_INDEX          68
#define AM335X_PADCTL_MII1_TX_EN_INDEX          69
#define AM335X_PADCTL_MII1_RX_DV_INDEX          70
#define AM335X_PADCTL_MII1_TXD3_INDEX           71
#define AM335X_PADCTL_MII1_TXD2_INDEX           72
#define AM335X_PADCTL_MII1_TXD1_INDEX           73
#define AM335X_PADCTL_MII1_TXD0_INDEX           74
#define AM335X_PADCTL_MII1_TX_CLK_INDEX         75
#define AM335X_PADCTL_MII1_RX_CLK_INDEX         76
#define AM335X_PADCTL_MII1_RXD3_INDEX           77
#define AM335X_PADCTL_MII1_RXD2_INDEX           78
#define AM335X_PADCTL_MII1_RXD1_INDEX           79
#define AM335X_PADCTL_MII1_RXD0_INDEX           80
#define AM335X_PADCTL_RMII1_REF_CLK_INDEX       81
#define AM335X_PADCTL_MDIO_INDEX                82
#define AM335X_PADCTL_MDC_INDEX                 83
#define AM335X_PADCTL_SPI0_SCLK_INDEX           84
#define AM335X_PADCTL_SPI0_D0_INDEX             85
#define AM335X_PADCTL_SPI0_D1_INDEX             86
#define AM335X_PADCTL_SPI0_CS0_INDEX            87
#define AM335X_PADCTL_SPI0_CS1_INDEX            88
#define AM335X_PADCTL_ECAP0_IN_PWM0_OUT_INDEX   89
#define AM335X_PADCTL_UART0_CTSN_INDEX          90
#define AM335X_PADCTL_UART0_RTSN_INDEX          91
#define AM335X_PADCTL_UART0_RXD_INDEX           92
#define AM335X_PADCTL_UART0_TXD_INDEX           93
#define AM335X_PADCTL_UART1_CTSN_INDEX          94
#define AM335X_PADCTL_UART1_RTSN_INDEX          95
#define AM335X_PADCTL_UART1_RXD_INDEX           96
#define AM335X_PADCTL_UART1_TXD_INDEX           97
#define AM335X_PADCTL_I2C0_SDA_INDEX            98
#define AM335X_PADCTL_I2C0_SCL_INDEX            99
#define AM335X_PADCTL_MCASP0_ACLKX_INDEX        100
#define AM335X_PADCTL_MCASP0_FSX_INDEX          101
#define AM335X_PADCTL_MCASP0_AXR0_INDEX         102
#define AM335X_PADCTL_MCASP0_AHCLKR_INDEX       103
#define AM335X_PADCTL_MCASP0_ACLKR_INDEX        104
#define AM335X_PADCTL_MCASP0_FSR_INDEX          105
#define AM335X_PADCTL_MCASP0_AXR1_INDEX         106
#define AM335X_PADCTL_MCASP0_AHCLKX_INDEX       107
#define AM335X_PADCTL_XDMA_EVENT_INTR0_INDEX    108
#define AM335X_PADCTL_XDMA_EVENT_INTR1_INDEX    109
#define AM335X_PADCTL_WARMRSTN_INDEX            110
#define AM335X_PADCTL_NNMI_INDEX                112
#define AM335X_PADCTL_TMS_INDEX                 116
#define AM335X_PADCTL_TDI_INDEX                 117
#define AM335X_PADCTL_TDO_INDEX                 118
#define AM335X_PADCTL_TCK_INDEX                 119
#define AM335X_PADCTL_TRSTN_INDEX               120
#define AM335X_PADCTL_EMU0_INDEX                121
#define AM335X_PADCTL_EMU1_INDEX                122
#define AM335X_PADCTL_RTC_PWRONRSTN_INDEX       126
#define AM335X_PADCTL_PMIC_POWER_EN_INDEX       127
#define AM335X_PADCTL_EXT_WAKEUP_INDEX          128
#define AM335X_PADCTL_RTC_KALDO_ENN_INDEX       129
#define AM335X_PADCTL_USB0_DRVVBUS_INDEX        135
#define AM335X_PADCTL_USB1_DRVVBUS_INDEX        141

#define AM335X_PADCTL_NREGISTERS                142

/* Pad Control Register Offsets */

#define AM335X_PADCTL_OFFSET(n)                 (0x0800 + ((unsigned int)(n) << 2))

#define AM335X_PADCTL_GPMC_AD0_OFFSET           0x0800
#define AM335X_PADCTL_GPMC_AD1_OFFSET           0x0804
#define AM335X_PADCTL_GPMC_AD2_OFFSET           0x0808
#define AM335X_PADCTL_GPMC_AD3_OFFSET           0x080c
#define AM335X_PADCTL_GPMC_AD4_OFFSET           0x0810
#define AM335X_PADCTL_GPMC_AD5_OFFSET           0x0814
#define AM335X_PADCTL_GPMC_AD6_OFFSET           0x0818
#define AM335X_PADCTL_GPMC_AD7_OFFSET           0x081c
#define AM335X_PADCTL_GPMC_AD8_OFFSET           0x0820
#define AM335X_PADCTL_GPMC_AD9_OFFSET           0x0824
#define AM335X_PADCTL_GPMC_AD10_OFFSET          0x0828
#define AM335X_PADCTL_GPMC_AD11_OFFSET          0x082c
#define AM335X_PADCTL_GPMC_AD12_OFFSET          0x0830
#define AM335X_PADCTL_GPMC_AD13_OFFSET          0x0834
#define AM335X_PADCTL_GPMC_AD14_OFFSET          0x0838
#define AM335X_PADCTL_GPMC_AD15_OFFSET          0x083c
#define AM335X_PADCTL_GPMC_A0_OFFSET            0x0840
#define AM335X_PADCTL_GPMC_A1_OFFSET            0x0844
#define AM335X_PADCTL_GPMC_A2_OFFSET            0x0848
#define AM335X_PADCTL_GPMC_A3_OFFSET            0x084c
#define AM335X_PADCTL_GPMC_A4_OFFSET            0x0850
#define AM335X_PADCTL_GPMC_A5_OFFSET            0x0854
#define AM335X_PADCTL_GPMC_A6_OFFSET            0x0858
#define AM335X_PADCTL_GPMC_A7_OFFSET            0x085c
#define AM335X_PADCTL_GPMC_A8_OFFSET            0x0860
#define AM335X_PADCTL_GPMC_A9_OFFSET            0x0864
#define AM335X_PADCTL_GPMC_A10_OFFSET           0x0868
#define AM335X_PADCTL_GPMC_A11_OFFSET           0x086c
#define AM335X_PADCTL_GPMC_WAIT0_OFFSET         0x0870
#define AM335X_PADCTL_GPMC_WPN_OFFSET           0x0874
#define AM335X_PADCTL_GPMC_BEN1_OFFSET          0x0878
#define AM335X_PADCTL_GPMC_CSN0_OFFSET          0x087c
#define AM335X_PADCTL_GPMC_CSN1_OFFSET          0x0880
#define AM335X_PADCTL_GPMC_CSN2_OFFSET          0x0884
#define AM335X_PADCTL_GPMC_CSN3_OFFSET          0x0888
#define AM335X_PADCTL_GPMC_CLK_OFFSET           0x088c
#define AM335X_PADCTL_GPMC_ADVN_ALE_OFFSET      0x0890
#define AM335X_PADCTL_GPMC_OEN_REN_OFFSET       0x0894
#define AM335X_PADCTL_GPMC_WEN_OFFSET           0x0898
#define AM335X_PADCTL_GPMC_BEN0_CLE_OFFSET      0x089c
#define AM335X_PADCTL_LCD_DATA0_OFFSET          0x08a0
#define AM335X_PADCTL_LCD_DATA1_OFFSET          0x08a4
#define AM335X_PADCTL_LCD_DATA2_OFFSET          0x08a8
#define AM335X_PADCTL_LCD_DATA3_OFFSET          0x08ac
#define AM335X_PADCTL_LCD_DATA4_OFFSET          0x08b0
#define AM335X_PADCTL_LCD_DATA5_OFFSET          0x08b4
#define AM335X_PADCTL_LCD_DATA6_OFFSET          0x08b8
#define AM335X_PADCTL_LCD_DATA7_OFFSET          0x08bc
#define AM335X_PADCTL_LCD_DATA8_OFFSET          0x08c0
#define AM335X_PADCTL_LCD_DATA9_OFFSET          0x08c4
#define AM335X_PADCTL_LCD_DATA10_OFFSET         0x08c8
#define AM335X_PADCTL_LCD_DATA11_OFFSET         0x08cc
#define AM335X_PADCTL_LCD_DATA12_OFFSET         0x08d0
#define AM335X_PADCTL_LCD_DATA13_OFFSET         0x08d4
#define AM335X_PADCTL_LCD_DATA14_OFFSET         0x08d8
#define AM335X_PADCTL_LCD_DATA15_OFFSET         0x08dc
#define AM335X_PADCTL_LCD_VSYNC_OFFSET          0x08e0
#define AM335X_PADCTL_LCD_HSYNC_OFFSET          0x08e4
#define AM335X_PADCTL_LCD_PCLK_OFFSET           0x08e8
#define AM335X_PADCTL_LCD_AC_BIAS_EN_OFFSET     0x08ec
#define AM335X_PADCTL_MMC0_DAT3_OFFSET          0x08f0
#define AM335X_PADCTL_MMC0_DAT2_OFFSET          0x08f4
#define AM335X_PADCTL_MMC0_DAT1_OFFSET          0x08f8
#define AM335X_PADCTL_MMC0_DAT0_OFFSET          0x08fc
#define AM335X_PADCTL_MMC0_CLK_OFFSET           0x0900
#define AM335X_PADCTL_MMC0_CMD_OFFSET           0x0904
#define AM335X_PADCTL_MII1_COL_OFFSET           0x0908
#define AM335X_PADCTL_MII1_CRS_OFFSET           0x090c
#define AM335X_PADCTL_MII1_RX_ER_OFFSET         0x0910
#define AM335X_PADCTL_MII1_TX_EN_OFFSET         0x0914
#define AM335X_PADCTL_MII1_RX_DV_OFFSET         0x0918
#define AM335X_PADCTL_MII1_TXD3_OFFSET          0x091c
#define AM335X_PADCTL_MII1_TXD2_OFFSET          0x0920
#define AM335X_PADCTL_MII1_TXD1_OFFSET          0x0924
#define AM335X_PADCTL_MII1_TXD0_OFFSET          0x0928
#define AM335X_PADCTL_MII1_TX_CLK_OFFSET        0x092c
#define AM335X_PADCTL_MII1_RX_CLK_OFFSET        0x0930
#define AM335X_PADCTL_MII1_RXD3_OFFSET          0x0934
#define AM335X_PADCTL_MII1_RXD2_OFFSET          0x0938
#define AM335X_PADCTL_MII1_RXD1_OFFSET          0x093c
#define AM335X_PADCTL_MII1_RXD0_OFFSET          0x0940
#define AM335X_PADCTL_RMII1_REF_CLK_OFFSET      0x0944
#define AM335X_PADCTL_MDIO_OFFSET               0x0948
#define AM335X_PADCTL_MDC_OFFSET                0x094c
#define AM335X_PADCTL_SPI0_SCLK_OFFSET          0x0950
#define AM335X_PADCTL_SPI0_D0_OFFSET            0x0954
#define AM335X_PADCTL_SPI0_D1_OFFSET            0x0958
#define AM335X_PADCTL_SPI0_CS0_OFFSET           0x095c
#define AM335X_PADCTL_SPI0_CS1_OFFSET           0x0960
#define AM335X_PADCTL_ECAP0_IN_PWM0_OUT_OFFSET  0x0964
#define AM335X_PADCTL_UART0_CTSN_OFFSET         0x0968
#define AM335X_PADCTL_UART0_RTSN_OFFSET         0x096c
#define AM335X_PADCTL_UART0_RXD_OFFSET          0x0970
#define AM335X_PADCTL_UART0_TXD_OFFSET          0x0974
#define AM335X_PADCTL_UART1_CTSN_OFFSET         0x0978
#define AM335X_PADCTL_UART1_RTSN_OFFSET         0x097c
#define AM335X_PADCTL_UART1_RXD_OFFSET          0x0980
#define AM335X_PADCTL_UART1_TXD_OFFSET          0x0984
#define AM335X_PADCTL_I2C0_SDA_OFFSET           0x0988
#define AM335X_PADCTL_I2C0_SCL_OFFSET           0x098c
#define AM335X_PADCTL_MCASP0_ACLKX_OFFSET       0x0990
#define AM335X_PADCTL_MCASP0_FSX_OFFSET         0x0994
#define AM335X_PADCTL_MCASP0_AXR0_OFFSET        0x0998
#define AM335X_PADCTL_MCASP0_AHCLKR_OFFSET      0x099c
#define AM335X_PADCTL_MCASP0_ACLKR_OFFSET       0x09a0
#define AM335X_PADCTL_MCASP0_FSR_OFFSET         0x09a4
#define AM335X_PADCTL_MCASP0_AXR1_OFFSET        0x09a8
#define AM335X_PADCTL_MCASP0_AhCLKX_OFFSET      0x09ac
#define AM335X_PADCTL_XDMA_EVENT_INTR0_OFFSET   0x09b0
#define AM335X_PADCTL_XDMA_EVENT_INTR1_OFFSET   0x09b4
#define AM335X_PADCTL_WARMRSTN_OFFSET           0x09b8
#define AM335X_PADCTL_NNMI_OFFSET               0x09c0
#define AM335X_PADCTL_TMS_OFFSET                0x09d0
#define AM335X_PADCTL_TDI_OFFSET                0x09d4
#define AM335X_PADCTL_TDO_OFFSET                0x09d8
#define AM335X_PADCTL_TCK_OFFSET                0x09dc
#define AM335X_PADCTL_TRSTN_OFFSET              0x09e0
#define AM335X_PADCTL_EMU0_OFFSET               0x09e4
#define AM335X_PADCTL_EMU1_OFFSET               0x09e8
#define AM335X_PADCTL_RTC_PWRONRSTN_OFFSET      0x09f8
#define AM335X_PADCTL_PMIC_POWER_EN_OFFSET      0x09fc
#define AM335X_PADCTL_EXT_WAKEUP_OFFSET         0x0a00
#define AM335X_PADCTL_RTC_KALDO_ENN_OFFSET      0x0a04
#define AM335X_PADCTL_USB0_DRVVBUS_OFFSET       0x0a1c
#define AM335X_PADCTL_USB1_DRVVBUS_OFFSET       0x0a34

/* Control Module Register Addresses ****************************************/

#define AM335X_SCM_CTRL_SYS_CONF                (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_CTRL_SYS_CONF_OFFSET)
#define AM335X_SCM_CTRL_STATUS                  (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_CTRL_STATUS_OFFSET)
#define AM335X_SCM_CTRL_EMIF_SDRAM_CONF         (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_CTRL_EMIF_SDRAM_CONF_OFFSET)
#define AM335X_SCM_CORE_SLDO_CTRL               (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_CORE_SLDO_CTRL_OFFSET)
#define AM335X_SCM_MPU_SLDO_CTRL                (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_MPU_SLDO_CTRL_OFFSET)
#define AM335X_SCM_CLK32KDIVRATIO_CTRL          (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_CLK32KDIVRATIO_CTRL_OFFSET)
#define AM335X_SCM_BANDGAP_CTRL                 (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_BANDGAP_CTRL_OFFSET)
#define AM335X_SCM_BANDGAP_TRIM                 (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_BANDGAP_TRIM_OFFSET)
#define AM335X_SCM_PLL_CLKINPULOW_CTRL          (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_PLL_CLKINPULOW_CTRL_OFFSET)
#define AM335X_SCM_MOSC_CTRL                    (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_MOSC_CTRL_OFFSET)
#define AM335X_SCM_DEEPSLEEP_CTRL               (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_DEEPSLEEP_CTRL_OFFSET)
#define AM335X_SCM_DPLL_PWR_SW_STATUS           (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_DPLL_PWR_SW_STATUS_OFFSET)
#define AM335X_SCM_DEVICE_ID                    (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_DEVICE_ID_OFFSET)
#define AM335X_SCM_DEV_FEATURE                  (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_DEV_FEATURE_OFFSET)
#define AM335X_SCM_INIT_PRIORITY_0              (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_INIT_PRIORITY_0_OFFSET)
#define AM335X_SCM_INIT_PRIORITY_1              (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_INIT_PRIORITY_1_OFFSET)
#define AM335X_SCM_TPTC_CFG                     (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_TPTC_CFG_OFFSET)
#define AM335X_SCM_USB_CTRL0                    (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_USB_CTRL0_OFFSET)
#define AM335X_SCM_USB_STS0                     (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_USB_STS0_OFFSET)
#define AM335X_SCM_USB_CTRL1                    (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_USB_CTRL1_OFFSET)
#define AM335X_SCM_USB_STS1                     (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_USB_STS1_OFFSET)
#define AM335X_SCM_MAC_ID0_LO                   (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_MAC_ID0_LO_OFFSET)
#define AM335X_SCM_MAC_ID0_HI                   (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_MAC_ID0_HI_OFFSET)
#define AM335X_SCM_MAC_ID1_LO                   (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_MAC_ID1_LO_OFFSET)
#define AM335X_SCM_MAC_ID1_HI                   (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_MAC_ID1_HI_OFFSET)
#define AM335X_SCM_DCAN_RAMINIT                 (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_DCAN_RAMINIT_OFFSET)
#define AM335X_SCM_USB_WKUP_CTRL                (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_USB_WKUP_CTRL_OFFSET)
#define AM335X_SCM_GMII_SEL                     (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_GMII_SEL_OFFSET)
#define AM335X_SCM_PWMSS_CTRL                   (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_PWMSS_CTRL_OFFSET)
#define AM335X_SCM_MREGPRIO_0                   (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_MREGPRIO_0_OFFSET)
#define AM335X_SCM_MREGPRIO_1                   (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_MREGPRIO_1_OFFSET)
#define AM335X_SCM_HW_EVENT_SEL_GRP1            (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_HW_EVENT_SEL_GRP1_OFFSET)
#define AM335X_SCM_HW_EVENT_SEL_GRP2            (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_HW_EVENT_SEL_GRP2_OFFSET)
#define AM335X_SCM_HW_EVENT_SEL_GRP3            (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_HW_EVENT_SEL_GRP3_OFFSET)
#define AM335X_SCM_HW_EVENT_SEL_GRP4            (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_HW_EVENT_SEL_GRP4_OFFSET)
#define AM335X_SCM_SMRT_CTRL                    (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_SMRT_CTRL_OFFSET)
#define AM335X_SCM_MPUSS_HW_DEBUG_SEL           (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_MPUSS_HW_DEBUG_SEL_OFFSET)
#define AM335X_SCM_MPUSS_HW_DBG_INFO            (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_MPUSS_HW_DBG_INFO_OFFSET)
#define AM335X_SCM_VDD_MPU_OPP_050              (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_VDD_MPU_OPP_050_OFFSET)
#define AM335X_SCM_VDD_MPU_OPP_100              (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_VDD_MPU_OPP_100_OFFSET)
#define AM335X_SCM_VDD_MPU_OPP_120              (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_VDD_MPU_OPP_120_OFFSET)
#define AM335X_SCM_VDD_MPU_OPP_TURBO            (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_VDD_MPU_OPP_TURBO_OFFSET)
#define AM335X_SCM_VDD_CORE_OPP_050             (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_VDD_CORE_OPP_050_OFFSET)
#define AM335X_SCM_VDD_CORE_OPP_100             (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_VDD_CORE_OPP_100_OFFSET)
#define AM335X_SCM_BB_SCALE                     (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_BB_SCALE_OFFSET)
#define AM335X_SCM_USB_VID_PID                  (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_USB_VID_PID_OFFSET)
#define AM335X_SCM_EFUSE_SMA                    (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_EFUSE_SMA_OFFSET)

#define AM335X_SCM_CQDETECT_STATUS              (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_CQDETECT_STATUS_OFFSET)
#define AM335X_SCM_DDR_IO_CTRL                  (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_DDR_IO_CTRL_OFFSET)
#define AM335X_SCM_VTP_CTRL                     (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_VTP_CTRL_OFFSET)
#define AM335X_SCM_VREF_CTRL                    (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_VREF_CTRL_OFFSET)
#define AM335X_SCM_TPCC_EVT_MUX_0_3             (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_TPCC_EVT_MUX_0_3_OFFSET)
#define AM335X_SCM_TPCC_EVT_MUX_4_7             (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_TPCC_EVT_MUX_4_7_OFFSET)
#define AM335X_SCM_TPCC_EVT_MUX_8_11            (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_TPCC_EVT_MUX_8_11_OFFSET)
#define AM335X_SCM_TPCC_EVT_MUX_12_15           (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_TPCC_EVT_MUX_12_15_OFFSET)
#define AM335X_SCM_TPCC_EVT_MUX_16_19           (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_TPCC_EVT_MUX_16_19_OFFSET)
#define AM335X_SCM_TPCC_EVT_MUX_20_23           (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_TPCC_EVT_MUX_20_23_OFFSET)
#define AM335X_SCM_TPCC_EVT_MUX_24_27           (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_TPCC_EVT_MUX_24_27_OFFSET)
#define AM335X_SCM_TPCC_EVT_MUX_28_31           (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_TPCC_EVT_MUX_28_31_OFFSET)
#define AM335X_SCM_TPCC_EVT_MUX_32_35           (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_TPCC_EVT_MUX_32_35_OFFSET)
#define AM335X_SCM_TPCC_EVT_MUX_36_39           (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_TPCC_EVT_MUX_36_39_OFFSET)
#define AM335X_SCM_TPCC_EVT_MUX_40_43           (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_TPCC_EVT_MUX_40_43_OFFSET)
#define AM335X_SCM_TPCC_EVT_MUX_44_47           (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_TPCC_EVT_MUX_44_47_OFFSET)
#define AM335X_SCM_TPCC_EVT_MUX_48_51           (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_TPCC_EVT_MUX_48_51_OFFSET)
#define AM335X_SCM_TPCC_EVT_MUX_52_55           (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_TPCC_EVT_MUX_52_55_OFFSET)
#define AM335X_SCM_TPCC_EVT_MUX_56_59           (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_TPCC_EVT_MUX_56_59_OFFSET)
#define AM335X_SCM_TPCC_EVT_MUX_60_63           (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_TPCC_EVT_MUX_60_63_OFFSET)
#define AM335X_SCM_TIMER_EVT_CAPT               (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_TIMER_EVT_CAPT_OFFSET)
#define AM335X_SCM_ECAP_EVT_CAPT                (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_ECAP_EVT_CAPT_OFFSET)
#define AM335X_SCM_ADC_EVT_CAPT                 (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_ADC_EVT_CAPT_OFFSET)
#define AM335X_SCM_RESET_ISO                    (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_RESET_ISO_OFFSET)
#define AM335X_SCM_DPLL_PWR_SW_CTRL             (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_DPLL_PWR_SW_CTRL_OFFSET)
#define AM335X_SCM_DDR_CKE_CTRL                 (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_DDR_CKE_CTRL_OFFSET)
#define AM335X_SCM_SMA2                         (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_SMA2_OFFSET)
#define AM335X_SCM_M3_TXEV_EOI                  (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_M3_TXEV_EOI_OFFSET)
#define AM335X_SCM_IPC_MSG_REG0                 (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_IPC_MSG_REG0_OFFSET)
#define AM335X_SCM_IPC_MSG_REG1                 (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_IPC_MSG_REG1_OFFSET)
#define AM335X_SCM_IPC_MSG_REG2                 (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_IPC_MSG_REG2_OFFSET)
#define AM335X_SCM_IPC_MSG_REG3                 (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_IPC_MSG_REG3_OFFSET)
#define AM335X_SCM_IPC_MSG_REG4                 (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_IPC_MSG_REG4_OFFSET)
#define AM335X_SCM_IPC_MSG_REG5                 (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_IPC_MSG_REG5_OFFSET)
#define AM335X_SCM_IPC_MSG_REG6                 (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_IPC_MSG_REG6_OFFSET)
#define AM335X_SCM_IPC_MSG_REG7                 (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_IPC_MSG_REG7_OFFSET)
#define AM335X_SCM_DDR_CMD0_IOCTRL              (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_DDR_CMD0_IOCTRL_OFFSET)
#define AM335X_SCM_DDR_CMD1_IOCTRL              (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_DDR_CMD1_IOCTRL_OFFSET)
#define AM335X_SCM_DDR_CMD2_IOCTRL              (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_DDR_CMD2_IOCTRL_OFFSET)
#define AM335X_SCM_DDR_DATA0_IOCTRL             (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_DDR_DATA0_IOCTRL_OFFSET)
#define AM335X_SCM_DDR_DATA1_IOCTRL             (AM335X_CONTROL_MODULE_VADDR + AM335X_SCM_DDR_DATA1_IOCTRL_OFFSET)

/* Pad Control Registers */

#define AM335X_PADCTL_ADDRESS(n)                (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_OFFSET(n))

#define AM335X_PADCTL_GPMC_AD0                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_AD0_OFFSET)
#define AM335X_PADCTL_GPMC_AD1                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_AD1_OFFSET)
#define AM335X_PADCTL_GPMC_AD2                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_AD2_OFFSET)
#define AM335X_PADCTL_GPMC_AD3                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_AD3_OFFSET)
#define AM335X_PADCTL_GPMC_AD4                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_AD4_OFFSET)
#define AM335X_PADCTL_GPMC_AD5                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_AD5_OFFSET)
#define AM335X_PADCTL_GPMC_AD6                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_AD6_OFFSET)
#define AM335X_PADCTL_GPMC_AD7                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_AD7_OFFSET)
#define AM335X_PADCTL_GPMC_AD8                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_AD8_OFFSET)
#define AM335X_PADCTL_GPMC_AD9                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_AD9_OFFSET)
#define AM335X_PADCTL_GPMC_AD10                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_AD10_OFFSET)
#define AM335X_PADCTL_GPMC_AD11                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_AD11_OFFSET)
#define AM335X_PADCTL_GPMC_AD12                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_AD12_OFFSET)
#define AM335X_PADCTL_GPMC_AD13                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_AD13_OFFSET)
#define AM335X_PADCTL_GPMC_AD14                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_AD14_OFFSET)
#define AM335X_PADCTL_GPMC_AD15                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_AD15_OFFSET)
#define AM335X_PADCTL_GPMC_A0                   (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_A0_OFFSET)
#define AM335X_PADCTL_GPMC_A1                   (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_A1_OFFSET)
#define AM335X_PADCTL_GPMC_A2                   (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_A2_OFFSET)
#define AM335X_PADCTL_GPMC_A3                   (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_A3_OFFSET)
#define AM335X_PADCTL_GPMC_A4                   (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_A4_OFFSET)
#define AM335X_PADCTL_GPMC_A5                   (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_A5_OFFSET)
#define AM335X_PADCTL_GPMC_A6                   (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_A6_OFFSET)
#define AM335X_PADCTL_GPMC_A7                   (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_A7_OFFSET)
#define AM335X_PADCTL_GPMC_A8                   (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_A8_OFFSET)
#define AM335X_PADCTL_GPMC_A9                   (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_A9_OFFSET)
#define AM335X_PADCTL_GPMC_A10                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_A10_OFFSET)
#define AM335X_PADCTL_GPMC_A11                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_A11_OFFSET)
#define AM335X_PADCTL_GPMC_WAIT0                (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_WAIT0_OFFSET)
#define AM335X_PADCTL_GPMC_WPN                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_WPN_OFFSET)
#define AM335X_PADCTL_GPMC_BEN1                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_BEN1_OFFSET)
#define AM335X_PADCTL_GPMC_CSN0                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_CSN0_OFFSET)
#define AM335X_PADCTL_GPMC_CSN1                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_CSN1_OFFSET)
#define AM335X_PADCTL_GPMC_CSN2                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_CSN2_OFFSET)
#define AM335X_PADCTL_GPMC_CSN3                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_CSN3_OFFSET)
#define AM335X_PADCTL_GPMC_CLK                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_CLK_OFFSET)
#define AM335X_PADCTL_GPMC_ADVN_ALE             (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_ADVN_ALE_OFFSET)
#define AM335X_PADCTL_GPMC_OEN_REN              (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_OEN_REN_OFFSET)
#define AM335X_PADCTL_GPMC_WEN                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_WEN_OFFSET)
#define AM335X_PADCTL_GPMC_BEN0_CLE             (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_GPMC_BEN0_CLE_OFFSET)
#define AM335X_PADCTL_LCD_DATA0                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_DATA0_OFFSET)
#define AM335X_PADCTL_LCD_DATA1                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_DATA1_OFFSET)
#define AM335X_PADCTL_LCD_DATA2                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_DATA2_OFFSET)
#define AM335X_PADCTL_LCD_DATA3                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_DATA3_OFFSET)
#define AM335X_PADCTL_LCD_DATA4                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_DATA4_OFFSET)
#define AM335X_PADCTL_LCD_DATA5                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_DATA5_OFFSET)
#define AM335X_PADCTL_LCD_DATA6                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_DATA6_OFFSET)
#define AM335X_PADCTL_LCD_DATA7                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_DATA7_OFFSET)
#define AM335X_PADCTL_LCD_DATA8                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_DATA8_OFFSET)
#define AM335X_PADCTL_LCD_DATA9                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_DATA9_OFFSET)
#define AM335X_PADCTL_LCD_DATA10                (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_DATA10_OFFSET)
#define AM335X_PADCTL_LCD_DATA11                (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_DATA11_OFFSET)
#define AM335X_PADCTL_LCD_DATA12                (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_DATA12_OFFSET)
#define AM335X_PADCTL_LCD_DATA13                (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_DATA13_OFFSET)
#define AM335X_PADCTL_LCD_DATA14                (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_DATA14_OFFSET)
#define AM335X_PADCTL_LCD_DATA15                (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_DATA15_OFFSET)
#define AM335X_PADCTL_LCD_VSYNC                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_VSYNC_OFFSET)
#define AM335X_PADCTL_LCD_HSYNC                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_HSYNC_OFFSET)
#define AM335X_PADCTL_LCD_PCLK                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_PCLK_OFFSET)
#define AM335X_PADCTL_LCD_AC_BIAS_EN            (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_LCD_AC_BIAS_EN_OFFSET)
#define AM335X_PADCTL_MMC0_DAT3                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MMC0_DAT3_OFFSET)
#define AM335X_PADCTL_MMC0_DAT2                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MMC0_DAT2_OFFSET)
#define AM335X_PADCTL_MMC0_DAT1                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MMC0_DAT1_OFFSET)
#define AM335X_PADCTL_MMC0_DAT0                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MMC0_DAT0_OFFSET)
#define AM335X_PADCTL_MMC0_CLK                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MMC0_CLK_OFFSET)
#define AM335X_PADCTL_MMC0_CMD                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MMC0_CMD_OFFSET)
#define AM335X_PADCTL_MII1_COL                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MII1_COL_OFFSET)
#define AM335X_PADCTL_MII1_CRS                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MII1_CRS_OFFSET)
#define AM335X_PADCTL_MII1_RX_ER                (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MII1_RX_ER_OFFSET)
#define AM335X_PADCTL_MII1_TX_EN                (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MII1_TX_EN_OFFSET)
#define AM335X_PADCTL_MII1_RX_DV                (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MII1_RX_DV_OFFSET)
#define AM335X_PADCTL_MII1_TXD3                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MII1_TXD3_OFFSET)
#define AM335X_PADCTL_MII1_TXD2                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MII1_TXD2_OFFSET)
#define AM335X_PADCTL_MII1_TXD1                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MII1_TXD1_OFFSET)
#define AM335X_PADCTL_MII1_TXD0                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MII1_TXD0_OFFSET)
#define AM335X_PADCTL_MII1_TX_CLK               (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MII1_TX_CLK_OFFSET)
#define AM335X_PADCTL_MII1_RX_CLK               (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MII1_RX_CLK_OFFSET)
#define AM335X_PADCTL_MII1_RXD3                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MII1_RXD3_OFFSET)
#define AM335X_PADCTL_MII1_RXD2                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MII1_RXD2_OFFSET)
#define AM335X_PADCTL_MII1_RXD1                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MII1_RXD1_OFFSET)
#define AM335X_PADCTL_MII1_RXD0                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MII1_RXD0_OFFSET)
#define AM335X_PADCTL_RMII1_REF_CLK             (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_RMII1_REF_CLK_OFFSET)
#define AM335X_PADCTL_MDIO                      (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MDIO_OFFSET)
#define AM335X_PADCTL_MDC                       (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MDC_OFFSET)
#define AM335X_PADCTL_SPI0_SCLK                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_SPI0_SCLK_OFFSET)
#define AM335X_PADCTL_SPI0_D0                   (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_SPI0_D0_OFFSET)
#define AM335X_PADCTL_SPI0_D1                   (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_SPI0_D1_OFFSET)
#define AM335X_PADCTL_SPI0_CS0                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_SPI0_CS0_OFFSET)
#define AM335X_PADCTL_SPI0_CS1                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_SPI0_CS1_OFFSET)
#define AM335X_PADCTL_ECAP0_IN_PWM0_OUT         (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_ECAP0_IN_PWM0_OUT_OFFSET)
#define AM335X_PADCTL_UART0_CTSN                (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_UART0_CTSN_OFFSET)
#define AM335X_PADCTL_UART0_RTSN                (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_UART0_RTSN_OFFSET)
#define AM335X_PADCTL_UART0_RXD                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_UART0_RXD_OFFSET)
#define AM335X_PADCTL_UART0_TXD                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_UART0_TXD_OFFSET)
#define AM335X_PADCTL_UART1_CTSN                (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_UART1_CTSN_OFFSET)
#define AM335X_PADCTL_UART1_RTSN                (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_UART1_RTSN_OFFSET)
#define AM335X_PADCTL_UART1_RXD                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_UART1_RXD_OFFSET)
#define AM335X_PADCTL_UART1_TXD                 (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_UART1_TXD_OFFSET)
#define AM335X_PADCTL_I2C0_SDA                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_I2C0_SDA_OFFSET)
#define AM335X_PADCTL_I2C0_SCL                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_I2C0_SCL_OFFSET)
#define AM335X_PADCTL_MCASP0_ACLKX              (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MCASP0_ACLKX_OFFSET)
#define AM335X_PADCTL_MCASP0_FSX                (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MCASP0_FSX_OFFSET)
#define AM335X_PADCTL_MCASP0_AXR0               (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MCASP0_AXR0_OFFSET)
#define AM335X_PADCTL_MCASP0_AHCLKR             (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MCASP0_AHCLKR_OFFSET)
#define AM335X_PADCTL_MCASP0_ACLKR              (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MCASP0_ACLKR_OFFSET)
#define AM335X_PADCTL_MCASP0_FSR                (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MCASP0_FSR_OFFSET)
#define AM335X_PADCTL_MCASP0_AXR1               (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MCASP0_AXR1_OFFSET)
#define AM335X_PADCTL_MCASP0_AhCLKX             (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_MCASP0_AhCLKX_OFFSET)
#define AM335X_PADCTL_XDMA_EVENT_INTR0          (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_XDMA_EVENT_INTR0_OFFSET)
#define AM335X_PADCTL_XDMA_EVENT_INTR1          (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_XDMA_EVENT_INTR1_OFFSET)
#define AM335X_PADCTL_WARMRSTN                  (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_WARMRSTN_OFFSET)
#define AM335X_PADCTL_NNMI                      (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_NNMI_OFFSET)
#define AM335X_PADCTL_TMS                       (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_TMS_OFFSET)
#define AM335X_PADCTL_TDI                       (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_TDI_OFFSET)
#define AM335X_PADCTL_TDO                       (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_TDO_OFFSET)
#define AM335X_PADCTL_TCK                       (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_TCK_OFFSET)
#define AM335X_PADCTL_TRSTN                     (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_TRSTN_OFFSET)
#define AM335X_PADCTL_EMU0                      (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_EMU0_OFFSET)
#define AM335X_PADCTL_EMU1                      (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_EMU1_OFFSET)
#define AM335X_PADCTL_RTC_PWRONRSTN             (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_RTC_PWRONRSTN_OFFSET)
#define AM335X_PADCTL_PMIC_POWER_EN             (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_PMIC_POWER_EN_OFFSET)
#define AM335X_PADCTL_EXT_WAKEUP                (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_EXT_WAKEUP_OFFSET)
#define AM335X_PADCTL_RTC_KALDO_ENN             (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_RTC_KALDO_ENN_OFFSET)
#define AM335X_PADCTL_USB0_DRVVBUS              (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_USB0_DRVVBUS_OFFSET)
#define AM335X_PADCTL_USB1_DRVVBUS              (AM335X_CONTROL_MODULE_VADDR + AM335X_PADCTL_USB1_DRVVBUS_OFFSET)

/* Control Module Register Bit Definitions **********************************/

/* Control Status Fields */
#define SCM_CTRL_STATUS_SYSBOOT1_SHIFT          (22) /* Bits 22-23:  Crystal clock frequency selection */
#define SCM_CTRL_STATUS_SYSBOOT1_MASK           (3 << SCM_CTRL_STATUS_SYSBOOT1_SHIFT)
#  define SCM_CTRL_STATUS_SYSBOOT1_19p2MHZ      (0 << SCM_CTRL_STATUS_SYSBOOT1_SHIFT)
#  define SCM_CTRL_STATUS_SYSBOOT1_24MHZ        (1 << SCM_CTRL_STATUS_SYSBOOT1_SHIFT)
#  define SCM_CTRL_STATUS_SYSBOOT1_25MHZ        (2 << SCM_CTRL_STATUS_SYSBOOT1_SHIFT)
#  define SCM_CTRL_STATUS_SYSBOOT1_26MHZ        (3 << SCM_CTRL_STATUS_SYSBOOT1_SHIFT)

/* PAD Control Fields */
#define PADCTL_MUXMODE_SHIFT    (0)       /* Bits 0-2: Functional signal mux select */
#define PADCTL_MUXMODE_MASK     (7 << PADCTL_MUXMODE_SHIFT)
#  define PADCTL_MUXMODE_0      (0 << PADCTL_MUXMODE_SHIFT)
#  define PADCTL_MUXMODE_1      (1 << PADCTL_MUXMODE_SHIFT)
#  define PADCTL_MUXMODE_2      (2 << PADCTL_MUXMODE_SHIFT)
#  define PADCTL_MUXMODE_3      (3 << PADCTL_MUXMODE_SHIFT)
#  define PADCTL_MUXMODE_4      (4 << PADCTL_MUXMODE_SHIFT)
#  define PADCTL_MUXMODE_5      (5 << PADCTL_MUXMODE_SHIFT)
#  define PADCTL_MUXMODE_6      (6 << PADCTL_MUXMODE_SHIFT)
#  define PADCTL_MUXMODE_7      (7 << PADCTL_MUXMODE_SHIFT)
#  define PADCTL_MUXMODE(n)     (((n) << PADCTL_MUXMODE_SHIFT) & PADCTL_MUXMODE_MASK)
#define PADCTL_PULLUDEN         (0 << 3) /* Bit 3: Pull up enabled */
#define PADCTL_PULLUDDIS        (1 << 3) /* Bit 3: Pull up disabled */
#define PADCTL_PULLDOWN_EN      (0 << 4) /* Bit 4: Pull Down Selection */
#define PADCTL_PULLUP_EN        (1 << 4) /* Bit 4: Pull Up Selection */
#define PADCTL_RXACTIVE         (1 << 5) /* Bit 5: Receiver enabled */
#define PADCTL_SLEWCTRL         (1 << 6) /* Bit 6: Select between faster or slower slew rate */

#endif /* __ARCH_ARM_SRC_AM335X_HARDWARE_AM335X_SCM_H */
