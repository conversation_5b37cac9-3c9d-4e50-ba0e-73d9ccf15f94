#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

if ARCH_CHIP_AM335X

comment "AM335x Configuration Options"

choice
	prompt "TI AM335X Chip Selection"
	default ARCH_CHIP_AM3358

config ARCH_CHIP_AM3358
	bool "AM3358"

endchoice

menu "TI AM335X Peripheral Support"

config AM335X_UART0
	bool "UART 0"
	default n
	select UART0_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config AM335X_UART1
	bool "UART 1"
	default n
	select UART1_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config AM335X_UART2
	bool "UART 2"
	default n
	select UART2_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config AM335X_UART3
	bool "UART 3"
	default n
	select UART3_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config AM335X_UART4
	bool "UART 4"
	default n
	select UART4_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config AM335X_UART5
	bool "UART 5"
	default n
	select UART5_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config AM335X_TIMER0
	bool "Timer 0"
	default n

config AM335X_TIMER2
	bool "Timer 2"
	default n

config AM335X_TIMER3
	bool "Timer 3"
	default n

config AM335X_TIMER4
	bool "Timer 4"
	default n

config AM335X_TIMER5
	bool "Timer 5"
	default n

config AM335X_TIMER6
	bool "Timer 6"
	default n

config AM335X_TIMER7
	bool "Timer 7"
	default n

config AM335X_IRQ_WD
	bool "Watchdog"
	default n

config AM335X_DMA
	bool "Enhanced Direct Memory Access"
	default n

config AM335X_GPIO
	bool "GPIO"
	default n

config AM335X_LCDC
	bool "LCD controller"
	default n
	depends on VIDEO && EXPERIMENTAL
	select LCD
	select VIDEO_EDID

config AM335X_TSC
	bool "Touchscreen Controller"
	default n

config AM335X_CPSW
	bool "Ethernet subsystem"
	default n

config AM335X_PWMSS
	bool "Pulse-Width Modulation Subsystem"
	default n

config AM335X_USB0
	bool "USB 0"
	default n

config AM335X_USB1
	bool "USB 1"
	default n

config AM335X_MMC0
	bool "MMC/SD/SDIO0 High Speed Host Controller 0"
	default n

config AM335X_MMC1
	bool "MMC/SD/SDIO0 High Speed Host Controller 1"
	default n

config AM335X_MMC2
	bool "MMC/SD/SDIO0 High Speed Host Controller 2"
	default n

config AM335X_I2C0
	bool "Multi-master I2C Controller 0"
	default n

config AM335X_I2C1
	bool "Multi-master I2C Controller 1"
	default n

config AM335X_I2C2
	bool "Multi-master I2C Controller 2"
	default n

config AM335X_MCASP0
	bool "Multichannel Audio Serial Port 0"
	default n

config AM335X_MCASP1
	bool "Multichannel Audio Serial Port 1"
	default n

config AM335X_CAN0
	bool "DCAN Bus 0 Controller"
	default n

config AM335X_CAN1
	bool "DCAN Bus 1 Controller"
	default n

config AM335X_SPI0
	bool "Multichannel Serial Port Interface 0"
	default n

config AM335X_SPI1
	bool "Multichannel Serial Port Interface 1"
	default n

config AM335X_IRQ_PERFMU
	bool "Performance monitor"
	default n

config AM335X_SGX
	bool "Graphics Accelerator"
	default n

endmenu

config AM335X_GPIO_IRQ
	bool "GPIO interrupts"
	default n
	---help---
		Select to enable support for GPIO interrupts.  These will
		be handled through a second level of interrupt decoding and will
		otherwise appear as any other interrupt.

choice
	prompt "Boot device"
	default AM335X_BOOT_SDCARD

config AM335X_BOOT_NAND
	bool "NAND FLASH"

config AM335X_BOOT_SPINOR
	bool "SPI NOR FLASH"

config AM335X_BOOT_SDCARD
	bool "SD card"

config AM335X_BOOT_USB
	bool "USB"

endchoice # Boot device

config AM335X_DDR_MAPOFFSET
	int "Installed SDRAM offset"
	default 0
	---help---
		The size of the installed SRAM memory is required in order to
		properly configure memory mapping.  The mapping will begin at the
		start of SDRAM plus AM335X_DDR_MAPOFFSET and extend for DDR_MAPSIZE
		bytes.  NOTE typically AM335X_MAP_OFFSET is zero and AM335X_DDR_MAPSIZE is
		the full, installed size of the DRAM.  But these values can be
		modified to set aside memory at the beginning or end of SRAM that is
		unmapped (or mapped differently).  NOTE also that this value relates
		closely to other settings:

			RAM_START and RAM_VSTART give this physical and virtual addresses
			of the start of usable memory (beginning with .text).  NOTE that
			this may not necessarily be the actual start of the mapped SDRAM
			region.  It will be larger if NuttX begins at an offset from
			beginning of mapped SDRAM (which is the normal case).

			RAM_SIZE gives the size of the .text, .data, and .bss sections
			plus the size of the available heap.  NOTE that RAM_SIZE may not
			include all of SDRAM up to the end of mapped region.

config AM335X_DDR_MAPSIZE
	int "Installed SDRAM size"
	default 536870912
	---help---
		The size of the installed SRAM memory is required in order to
		properly configure memory mapping.  The mapping will begin at the
		start of SDRAM plus AM335X_DDR_MAPOFFSET and extend for DDR_MAPSIZE
		bytes.  NOTE typically AM335X_MAP_OFFSET is zero and AM335X_DDR_MAPSIZE is
		the full, installed size of the DRAM.  But these values can be
		modified to set aside memory at the beginning or end of SRAM that is
		unmapped (or mapped differently).  NOTE also that this value relates
		closely to other settings:

			RAM_START and RAM_VSTART give this physical and virtual addresses
			of the start of usable memory (beginning with .text).  NOTE that
			this may not necessarily be the actual start of the mapped SDRAM
			region.  It will be larger if NuttX begins at an offset from
			beginning of mapped SDRAM (which is the normal case).

			RAM_SIZE gives the size of the .text, .data, and .bss sections
			plus the size of the available heap.  NOTE that RAM_SIZE may not
			include all of SDRAM up to the end of mapped region.

menu "LCD Configuration"
	depends on AM335X_LCDC

config AM335X_LCDC_FB_VBASE
	hex "Video RAM base address (virtual)"
	default 0x80000000
	---help---
		Base address of the video RAM frame buffer.  The default of 0x80000000
		assumes that the framebuffer lies at the beginning of DRAM and that
		a 1-to-1 virtual-to-physical address mapping is used.

config AM335X_LCDC_FB_PBASE
	hex "Video RAM base address (physical)"
	default 0x80000000
	---help---
		Base address of the video RAM frame buffer.  The default of 0x80000000
		assumes that the framebuffer lies at the beginning of DRAM.

config AM335X_LCDC_FB_SIZE
	hex "Video RAM base size"
	default 0x00100000
	---help---
		Size of the video RAM frame buffer.  Default:  1Mb.

choice
	prompt "Bits per pixel"
	default AM335X_LCDC_BPP16_565

config AM335X_LCDC_BPP1
	bool "1 BPP"

config AM335X_LCDC_BPP2
	bool "2 BPP"

config AM335X_LCDC_BPP4
	bool "4 BPP"

config AM335X_LCDC_BPP8
	bool "8 BPP"

config AM335X_LCDC_BPP12_444
	bool "12 bpp, 4:4:4 mode"

config AM335X_LCDC_BPP16_565
	bool "16 BPP, 5:6:5 mode"

config AM335X_LCDC_BPP24
	bool "24 BPP, 8:8:8 mode (packed)"

config AM335X_LCDC_BPP32
	bool "32 BPP, 8:8:8 mode (unpacked)"

endchoice

config AM335X_LCDC_BACKCOLOR
	hex "Initial background color"
	default 0x0
	---help---
		Initial background color

config AM335X_LCDC_ACBIAS
	int "AC bias pin frequency"
	default 255
	range 0 255

config AM335X_LCDC_ACBIAS_PINT
	int "AC bias pin transitions per interrupt"
	default 0
	range 0 15

choice
	prompt "DMA burst size"
	default AM335X_LCDC_DMA_BURST16

config AM335X_LCDC_DMA_BURST1
	bool "1"

config AM335X_LCDC_DMA_BURST2
	bool "2"

config AM335X_LCDC_DMA_BURST4
	bool "4"

config AM335X_LCDC_DMA_BURST8
	bool "8"

config AM335X_LCDC_DMA_BURST16
	bool "16"

endchoice

config AM335X_LCDC_FDD
	int "Palette loading delay"
	default 128
	range 0 255

config AM335X_LCDC_SYNC_EDGE
	bool "HSYNC/VSYNC rise or fall"
	default n

config AM335X_LCDC_SYNC_CTRL
	bool "Hsync/Vsync pixel clock control on/off"
	default y

config AM335X_LCDC_PIXCLK_INVERT
	bool "Invert pixel clock"
	default y

endmenu # LCD Configuration

endif # ARCH_CHIP_AM335X
