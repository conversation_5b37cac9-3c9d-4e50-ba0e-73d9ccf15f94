#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

comment "ARMv7-M Configuration Options"

config ARMV7M_HAVE_ICACHE
	bool
	default n

config ARMV7M_HAVE_DCACHE
	bool
	default n

config ARMV7M_USEBASEPRI
	bool "Use BASEPRI Register"
	default y if ARCH_HIPRI_INTERRUPT
	depends on ARCH_CORTEXM3 || ARCH_CORTEXM4 || ARCH_CORTEXM7
	---help---
		Use the BASEPRI register to enable and disable interrupts. By
		default, the PRIMASK register is used for this purpose. This
		usually results in hardfaults when supervisor calls are made.
		Though, these hardfaults are properly handled by the RTOS, the
		hardfaults can confuse some debuggers. With the BASEPRI
		register, these hardfaults, will be avoided. For more details see
		https://cwiki.apache.org/confluence/display/NUTTX/ARMv7-M+Hardfaults%2C+SVCALL%2C+and+Debuggers

		WARNING:  If <PERSON><PERSON><PERSON>_<PERSON>CH_HIPRI_INTERRUPT is selected, then you
		MUST select CONFIG_ARMV7M_USEBASEPRI.  The Kconfig dependencies
		here will permit to select an invalid configuration because it
		cannot enforce that requirement.  If you create this invalid
		configuration, you will encounter some problems that may be
		very difficult to debug.

config ARMV7M_BASEPRI_WAR
	bool "Cortex-M7 r0p1 Errata 837070 Workaround"
	default n
	depends on ARMV7M_USEBASEPRI && ARCH_CORTEXM7
	---help---
		Enable workaround for r0p1 Errata 837070: Increasing priority using
		write to BASEPRI does not take effect immediately.

		This update is required to be serialized to the instruction stream
		meaning that after this update completes, it takes effect
		immediately and no exceptions of lower priority than the new boosted
		priority can preempt execution.  Because of this erratum, the
		priority boosting does not take place immediately, allowing the
		instruction after the MSR to be interrupted by an exception of
		lower priority than the new boosted priority. This effect is only
		limited to the next instruction. Subsequent instructions are
		guaranteed to see the new boosted priority.

config ARMV7M_ICACHE
	bool "Use I-Cache"
	default n
	depends on ARMV7M_HAVE_ICACHE
	select ARCH_ICACHE

config ARMV7M_DCACHE
	bool "Use D-Cache"
	default n
	depends on ARMV7M_HAVE_DCACHE
	select ARCH_DCACHE

config ARMV7M_DCACHE_WRITETHROUGH
	bool "D-Cache Write-Through"
	default n
	depends on ARMV7M_DCACHE

config ARMV7M_HAVE_ITCM
	bool
	default n

config ARMV7M_HAVE_DTCM
	bool
	default n

config ARMV7M_ITCM
	bool "Use ITCM"
	default n
	depends on ARMV7M_HAVE_ITCM

config ARMV7M_DTCM
	bool "Use DTCM"
	default n
	depends on ARMV7M_HAVE_DTCM

choice
	prompt "Toolchain Selection"
	default ARMV7M_TOOLCHAIN_GNU_EABI

config ARMV7M_TOOLCHAIN_IAR
	bool "IAR ANSI C/C++ toolchain"
	select CYGWIN_WINTOOL if WINDOWS_CYGWIN
	select ARCH_TOOLCHAIN_IAR

config ARMV7M_TOOLCHAIN_BUILDROOT
	bool "Buildroot (Cygwin or Linux)"
	depends on !WINDOWS_NATIVE
	select ARCH_TOOLCHAIN_GNU

config ARMV7M_TOOLCHAIN_GNU_EABI
	bool "Generic GNU EABI toolchain"
	select CYGWIN_WINTOOL if WINDOWS_CYGWIN
	select ARCH_TOOLCHAIN_GNU
	---help---
		This option should work for any modern GNU toolchain (GCC 4.5 or newer)
		configured for arm-none-eabi.

config ARMV7M_TOOLCHAIN_CLANG
	bool "Generic Clang toolchain"
	select ARCH_TOOLCHAIN_CLANG

endchoice

config ARMV7M_OABI_TOOLCHAIN
	bool "OABI (vs EABI)"
	default n
	depends on ARMV7M_TOOLCHAIN_BUILDROOT
	---help---
		Most of the older buildroot toolchains are OABI and are named
		arm-nuttx-elf- vs. arm-nuttx-eabi-

config ARMV7M_TARGET2_PREL
	bool "R_ARM_TARGET2 is PC relative"
	default n if !CXX_EXCEPTION
	default y if CXX_EXCEPTION
	depends on LIBC_ARCH_ELF
	---help---
		Perform a PC relative relocation for relocation type R_ARM_TARGET2

config ARMV7M_HAVE_STACKCHECK
	bool
	default n

config ARMV7M_STACKCHECK
	bool "Check for stack overflow on each function call"
	default n
	depends on ARMV7M_HAVE_STACKCHECK
	---help---
		This check uses R10 to check for a stack overflow within each
		function call. This has performances and code size impacts, but it
		will be able to catch hard to find stack overflows.

		Currently only available only for the STM32, SAM3/4 and SAMA5D
		architectures.  The changes are not complex and patches for
		other architectures will be accepted.

		This option requires that you are using a GCC toolchain and that
		you also include -finstrument-functions and -ffixed-r10 in your
		CFLAGS when you compile. This addition to your CFLAGS should probably
		be added to the definition of the CFFLAGS in your board Make.defs file.

config ARMV7M_ITMSYSLOG
	bool "ITM SYSLOG support"
	default n
	select ARCH_SYSLOG
	select SYSLOG
	---help---
		Enable hooks to support ITM syslog output.  This requires additional
		MCU support in order to be used.  See arch/arm/src/armv7-m/itm_syslog.h
		for additional initialization information.

if ARMV7M_ITMSYSLOG

config ARMV7M_ITMSYSLOG_PORT
	int "ITM SYSLOG Port"
	default 0
	range 0 31

config ARMV7M_ITMSYSLOG_SWODIV
	int "ITM SYSLOG SWO divider"
	default 15
	range 1 8192

endif # ARMV7M_ITMSYSLOG

config ARMV7M_SYSTICK
	bool "SysTick timer driver"
	depends on TIMER
	---help---
		Enable SysTick timer driver.
