############################################################################
# arch/arm/src/armv7-m/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

# Common ARM files

include common/Make.defs

CMN_ASRCS += arm_exception.S

CMN_CSRCS += arm_busfault.c arm_cache.c arm_doirq.c
CMN_CSRCS += arm_hardfault.c arm_initialstate.c arm_itm.c
CMN_CSRCS += arm_memfault.c arm_perf.c
CMN_CSRCS += arm_schedulesigaction.c arm_sigdeliver.c
CMN_CSRCS += arm_svcall.c arm_systemreset.c arm_tcbinfo.c
CMN_CSRCS += arm_trigger_irq.c arm_usagefault.c arm_vectors.c

ifeq ($(CONFIG_ARMV7M_SYSTICK),y)
  CMN_CSRCS += arm_systick.c
endif

ifeq ($(CONFIG_ARMV7M_ITMSYSLOG),y)
  CMN_CSRCS += arm_itm_syslog.c
endif

ifeq ($(CONFIG_ARMV7M_STACKCHECK),y)
  CMN_CSRCS += arm_stackcheck.c
endif

ifeq ($(CONFIG_ARCH_FPU),y)
  CMN_CSRCS += arm_fpuconfig.c
  CMN_CSRCS += arm_fpucmp.c
endif

ifeq ($(CONFIG_ARCH_RAMVECTORS),y)
  CMN_CSRCS += arm_ramvec_initialize.c arm_ramvec_attach.c
endif

ifneq ($(filter y,$(CONFIG_ARM_MPU) $(CONFIG_ARM_MPU_EARLY_RESET)),)
  CMN_CSRCS += arm_mpu.c
endif
