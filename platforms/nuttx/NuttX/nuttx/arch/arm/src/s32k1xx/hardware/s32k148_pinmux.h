/****************************************************************************
 * arch/arm/src/s32k1xx/hardware/s32k148_pinmux.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_S32K1XX_HARDWARE_S32K148_PINMUX_H
#define __ARCH_ARM_SRC_S32K1XX_HARDWARE_S32K148_PINMUX_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

#include "chip.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* In most cases, there are alternative configurations for various pins.
 * Those alternative pins are labeled with a suffix like _1, _2, etc. in
 * order to distinguish them.  Logic in the board.h file must select the
 * correct pin configuration for the board by defining a pin configuration
 *(with no suffix) that maps to the correct alternative.
 *
 * WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific GPIO options such as
 * frequency, and pull-up/down!  Just the basics are defined for most pins
 * in the initial version of this file.
 */

/* ADC */

#define PIN_ADC0_SE0              (PIN_ANALOG | PIN_PORTA | PIN0)
#define PIN_ADC0_SE1              (PIN_ANALOG | PIN_PORTA | PIN1)
#define PIN_ADC0_SE2              (PIN_ANALOG | PIN_PORTA | PIN6)
#define PIN_ADC0_SE3              (PIN_ANALOG | PIN_PORTA | PIN7)
#define PIN_ADC0_SE4              (PIN_ANALOG | PIN_PORTB | PIN0)
#define PIN_ADC0_SE5              (PIN_ANALOG | PIN_PORTB | PIN1)
#define PIN_ADC0_SE6              (PIN_ANALOG | PIN_PORTB | PIN2)
#define PIN_ADC0_SE7              (PIN_ANALOG | PIN_PORTB | PIN3)
#define PIN_ADC0_SE8_1            (PIN_ANALOG | PIN_PORTB | PIN13)
#define PIN_ADC0_SE8_2            (PIN_ANALOG | PIN_PORTC | PIN0)
#define PIN_ADC0_SE9_1            (PIN_ANALOG | PIN_PORTB | PIN14)
#define PIN_ADC0_SE9_2            (PIN_ANALOG | PIN_PORTC | PIN1)
#define PIN_ADC0_SE10             (PIN_ANALOG | PIN_PORTC | PIN2)
#define PIN_ADC0_SE11             (PIN_ANALOG | PIN_PORTC | PIN3)
#define PIN_ADC0_SE12             (PIN_ANALOG | PIN_PORTC | PIN14)
#define PIN_ADC0_SE13             (PIN_ANALOG | PIN_PORTC | PIN15)
#define PIN_ADC0_SE14             (PIN_ANALOG | PIN_PORTC | PIN16)
#define PIN_ADC0_SE15             (PIN_ANALOG | PIN_PORTC | PIN17)
#define PIN_ADC0_SE16             (PIN_ANALOG | PIN_PORTB | PIN18)
#define PIN_ADC0_SE17             (PIN_ANALOG | PIN_PORTB | PIN20)
#define PIN_ADC0_SE18             (PIN_ANALOG | PIN_PORTB | PIN21)
#define PIN_ADC0_SE19             (PIN_ANALOG | PIN_PORTB | PIN22)
#define PIN_ADC0_SE20             (PIN_ANALOG | PIN_PORTB | PIN23)
#define PIN_ADC0_SE21             (PIN_ANALOG | PIN_PORTB | PIN25)
#define PIN_ADC0_SE22             (PIN_ANALOG | PIN_PORTB | PIN27)
#define PIN_ADC0_SE23             (PIN_ANALOG | PIN_PORTB | PIN28)
#define PIN_ADC0_SE24             (PIN_ANALOG | PIN_PORTB | PIN29)
#define PIN_ADC0_SE25             (PIN_ANALOG | PIN_PORTC | PIN19)
#define PIN_ADC0_SE26             (PIN_ANALOG | PIN_PORTC | PIN23)
#define PIN_ADC0_SE27             (PIN_ANALOG | PIN_PORTC | PIN27)
#define PIN_ADC0_SE28             (PIN_ANALOG | PIN_PORTC | PIN28)
#define PIN_ADC0_SE29             (PIN_ANALOG | PIN_PORTC | PIN29)
#define PIN_ADC0_SE30             (PIN_ANALOG | PIN_PORTC | PIN30)
#define PIN_ADC0_SE31             (PIN_ANALOG | PIN_PORTC | PIN31)

#define PIN_ADC1_SE0              (PIN_ANALOG | PIN_PORTA | PIN2)
#define PIN_ADC1_SE1              (PIN_ANALOG | PIN_PORTA | PIN3)
#define PIN_ADC1_SE2              (PIN_ANALOG | PIN_PORTD | PIN2)
#define PIN_ADC1_SE3              (PIN_ANALOG | PIN_PORTD | PIN3)
#define PIN_ADC1_SE4              (PIN_ANALOG | PIN_PORTC | PIN6)
#define PIN_ADC1_SE5              (PIN_ANALOG | PIN_PORTC | PIN7)
#define PIN_ADC1_SE6              (PIN_ANALOG | PIN_PORTD | PIN4)
#define PIN_ADC1_SE7              (PIN_ANALOG | PIN_PORTB | PIN12)
#define PIN_ADC1_SE8              (PIN_ANALOG | PIN_PORTB | PIN13)
#define PIN_ADC1_SE9              (PIN_ANALOG | PIN_PORTB | PIN14)
#define PIN_ADC1_SE10             (PIN_ANALOG | PIN_PORTE | PIN2)
#define PIN_ADC1_SE11             (PIN_ANALOG | PIN_PORTE | PIN6)
#define PIN_ADC1_SE12             (PIN_ANALOG | PIN_PORTA | PIN15)
#define PIN_ADC1_SE13             (PIN_ANALOG | PIN_PORTA | PIN16)
#define PIN_ADC1_SE14_1           (PIN_ANALOG | PIN_PORTB | PIN0)
#define PIN_ADC1_SE14_2           (PIN_ANALOG | PIN_PORTB | PIN15)
#define PIN_ADC1_SE15_1           (PIN_ANALOG | PIN_PORTB | PIN1)
#define PIN_ADC1_SE15_2           (PIN_ANALOG | PIN_PORTB | PIN16)
#define PIN_ADC1_SE16             (PIN_ANALOG | PIN_PORTD | PIN18)
#define PIN_ADC1_SE17             (PIN_ANALOG | PIN_PORTD | PIN19)
#define PIN_ADC1_SE18             (PIN_ANALOG | PIN_PORTD | PIN22)
#define PIN_ADC1_SE19             (PIN_ANALOG | PIN_PORTD | PIN23)
#define PIN_ADC1_SE20             (PIN_ANALOG | PIN_PORTD | PIN24)
#define PIN_ADC1_SE21             (PIN_ANALOG | PIN_PORTD | PIN27)
#define PIN_ADC1_SE22             (PIN_ANALOG | PIN_PORTD | PIN28)
#define PIN_ADC1_SE23             (PIN_ANALOG | PIN_PORTD | PIN29)
#define PIN_ADC1_SE24             (PIN_ANALOG | PIN_PORTD | PIN30)
#define PIN_ADC1_SE25             (PIN_ANALOG | PIN_PORTE | PIN19)
#define PIN_ADC1_SE26             (PIN_ANALOG | PIN_PORTE | PIN20)
#define PIN_ADC1_SE27             (PIN_ANALOG | PIN_PORTE | PIN21)
#define PIN_ADC1_SE28             (PIN_ANALOG | PIN_PORTE | PIN22)
#define PIN_ADC1_SE29             (PIN_ANALOG | PIN_PORTE | PIN23)
#define PIN_ADC1_SE30             (PIN_ANALOG | PIN_PORTE | PIN24)
#define PIN_ADC1_SE31             (PIN_ANALOG | PIN_PORTE | PIN25)

/* CAN */

#define PIN_CAN0_RX_1             (PIN_ALT3   | PIN_PORTC | PIN2)
#define PIN_CAN0_RX_2             (PIN_ALT5   | PIN_PORTA | PIN28)
#define PIN_CAN0_RX_3             (PIN_ALT5   | PIN_PORTB | PIN0)
#define PIN_CAN0_RX_4             (PIN_ALT5   | PIN_PORTE | PIN4)
#define PIN_CAN0_TX_1             (PIN_ALT3   | PIN_PORTC | PIN3)
#define PIN_CAN0_TX_2             (PIN_ALT5   | PIN_PORTA | PIN27)
#define PIN_CAN0_TX_3             (PIN_ALT5   | PIN_PORTB | PIN1)
#define PIN_CAN0_TX_4             (PIN_ALT5   | PIN_PORTE | PIN5)

#define PIN_CAN1_RX_1             (PIN_ALT3   | PIN_PORTA | PIN12)
#define PIN_CAN1_RX_2             (PIN_ALT3   | PIN_PORTC | PIN6)
#define PIN_CAN1_TX_1             (PIN_ALT3   | PIN_PORTA | PIN13)
#define PIN_CAN1_TX_2             (PIN_ALT3   | PIN_PORTC | PIN7)

#define PIN_CAN2_RX_1             (PIN_ALT3   | PIN_PORTC | PIN16)
#define PIN_CAN2_RX_2             (PIN_ALT3   | PIN_PORTE | PIN25)
#define PIN_CAN2_RX_3             (PIN_ALT4   | PIN_PORTB | PIN12)
#define PIN_CAN2_TX_1             (PIN_ALT3   | PIN_PORTC | PIN17)
#define PIN_CAN2_TX_2             (PIN_ALT3   | PIN_PORTE | PIN24)
#define PIN_CAN2_TX_3             (PIN_ALT4   | PIN_PORTB | PIN13)

/* Output clock */

#define PIN_CLKOUT_1              (PIN_ALT2   | PIN_PORTE | PIN10)
#define PIN_CLKOUT_2              (PIN_ALT5   | PIN_PORTB | PIN5)
#define PIN_CLKOUT_3              (PIN_ALT6   | PIN_PORTD | PIN10)
#define PIN_CLKOUT_4              (PIN_ALT7   | PIN_PORTD | PIN14)

/* Comparators */

#define PIN_CMP0_IN0              (PIN_ANALOG | PIN_PORTA | PIN0)
#define PIN_CMP0_IN1              (PIN_ANALOG | PIN_PORTA | PIN1)
#define PIN_CMP0_IN2              (PIN_ANALOG | PIN_PORTC | PIN4)
#define PIN_CMP0_IN3              (PIN_ANALOG | PIN_PORTE | PIN8)
#define PIN_CMP0_IN4              (PIN_ANALOG | PIN_PORTC | PIN3)
#define PIN_CMP0_IN5              (PIN_ANALOG | PIN_PORTC | PIN2)
#define PIN_CMP0_IN6              (PIN_ANALOG | PIN_PORTD | PIN7)
#define PIN_CMP0_IN7              (PIN_ANALOG | PIN_PORTD | PIN6)
#define PIN_CMP0_OUT_1            (PIN_ALT4   | PIN_PORTA | PIN4)
#define PIN_CMP0_OUT_2            (PIN_ALT7   | PIN_PORTE | PIN3)
#define PIN_CMP0_RRT_1            (PIN_ALT5   | PIN_PORTA | PIN11)
#define PIN_CMP0_RRT_2            (PIN_ALT5   | PIN_PORTD | PIN16)

/* Ethernet */

#define PIN_ENET_TMR0             (PIN_ALT5   | PIN_PORTD | PIN14)
#define PIN_ENET_TMR1             (PIN_ALT5   | PIN_PORTD | PIN13)
#define PIN_ENET_TMR2             (PIN_ALT5   | PIN_PORTD | PIN15)
#define PIN_ENET_TMR3             (PIN_ALT5   | PIN_PORTE | PIN9)

/* Embedded Trace Macrocell (ETM) */

#define PIN_ETM_TRACE_CLKOUT_1    (PIN_ALT6   | PIN_PORTC | PIN2)
#define PIN_ETM_TRACE_CLKOUT_2    (PIN_ALT6   | PIN_PORTD | PIN16)
#define PIN_ETM_TRACE_D0_1        (PIN_ALT5   | PIN_PORTD | PIN0)
#define PIN_ETM_TRACE_D0_2        (PIN_ALT6   | PIN_PORTD | PIN7)
#define PIN_ETM_TRACE_D1_1        (PIN_ALT2   | PIN_PORTE | PIN4)
#define PIN_ETM_TRACE_D1_2        (PIN_ALT4   | PIN_PORTD | PIN12)
#define PIN_ETM_TRACE_D2_1        (PIN_ALT3   | PIN_PORTD | PIN16)
#define PIN_ETM_TRACE_D2_2        (PIN_ALT4   | PIN_PORTD | PIN11)
#define PIN_ETM_TRACE_D3_1        (PIN_ALT3   | PIN_PORTD | PIN15)
#define PIN_ETM_TRACE_D3_2        (PIN_ALT4   | PIN_PORTD | PIN10)

/* External Watchdog Monitor (EWM) */

#define PIN_EWM_IN_1              (PIN_ALT4   | PIN_PORTA | PIN14)
#define PIN_EWM_IN_2              (PIN_ALT4   | PIN_PORTA | PIN3)
#define PIN_EWM_IN_3              (PIN_ALT5   | PIN_PORTC | PIN4)
#define PIN_EWM_IN_4              (PIN_ALT7   | PIN_PORTE | PIN5)
#define PIN_EWM_OUT_1             (PIN_ALT4   | PIN_PORTA | PIN17)
#define PIN_EWM_OUT_2             (PIN_ALT4   | PIN_PORTA | PIN2)
#define PIN_EWM_OUT_3             (PIN_ALT5   | PIN_PORTA | PIN4)
#define PIN_EWM_OUT_4             (PIN_ALT7   | PIN_PORTE | PIN4)

/* FlexTimer Module (FTM) */

#define PIN_FTM0_CH0_1            (PIN_ALT2   | PIN_PORTB | PIN12)
#define PIN_FTM0_CH0_2            (PIN_ALT2   | PIN_PORTC | PIN0)
#define PIN_FTM0_CH0_3            (PIN_ALT2   | PIN_PORTD | PIN15)
#define PIN_FTM0_CH1_1            (PIN_ALT2   | PIN_PORTB | PIN13)
#define PIN_FTM0_CH1_2            (PIN_ALT2   | PIN_PORTC | PIN1)
#define PIN_FTM0_CH1_3            (PIN_ALT2   | PIN_PORTD | PIN16)
#define PIN_FTM0_CH2_1            (PIN_ALT2   | PIN_PORTB | PIN14)
#define PIN_FTM0_CH2_2            (PIN_ALT2   | PIN_PORTC | PIN2)
#define PIN_FTM0_CH2_3            (PIN_ALT2   | PIN_PORTD | PIN0)
#define PIN_FTM0_CH3_1            (PIN_ALT2   | PIN_PORTB | PIN15)
#define PIN_FTM0_CH3_2            (PIN_ALT2   | PIN_PORTC | PIN3)
#define PIN_FTM0_CH3_3            (PIN_ALT2   | PIN_PORTD | PIN1)
#define PIN_FTM0_CH4_1            (PIN_ALT2   | PIN_PORTB | PIN16)
#define PIN_FTM0_CH4_2            (PIN_ALT2   | PIN_PORTB | PIN4)
#define PIN_FTM0_CH5_1            (PIN_ALT2   | PIN_PORTB | PIN17)
#define PIN_FTM0_CH5_2            (PIN_ALT2   | PIN_PORTB | PIN5)
#define PIN_FTM0_CH6_1            (PIN_ALT2   | PIN_PORTA | PIN17)
#define PIN_FTM0_CH6_2            (PIN_ALT2   | PIN_PORTE | PIN8)
#define PIN_FTM0_CH7_1            (PIN_ALT2   | PIN_PORTE | PIN7)
#define PIN_FTM0_CH7_2            (PIN_ALT2   | PIN_PORTE | PIN9)
#define PIN_FTM0_FLT0_1           (PIN_ALT2   | PIN_PORTA | PIN14)
#define PIN_FTM0_FLT0_2           (PIN_ALT2   | PIN_PORTE | PIN3)
#define PIN_FTM0_FLT1_1           (PIN_ALT2   | PIN_PORTA | PIN6)
#define PIN_FTM0_FLT1_2           (PIN_ALT2   | PIN_PORTE | PIN14)
#define PIN_FTM0_FLT2_1           (PIN_ALT2   | PIN_PORTA | PIN7)
#define PIN_FTM0_FLT2_2           (PIN_ALT2   | PIN_PORTD | PIN17)
#define PIN_FTM0_FLT3_1           (PIN_ALT2   | PIN_PORTD | PIN4)
#define PIN_FTM0_FLT3_2           (PIN_ALT2   | PIN_PORTE | PIN12)

#define PIN_FTM1_CH0_1            (PIN_ALT2   | PIN_PORTB | PIN2)
#define PIN_FTM1_CH0_2            (PIN_ALT2   | PIN_PORTC | PIN4)
#define PIN_FTM1_CH1_1            (PIN_ALT2   | PIN_PORTA | PIN1)
#define PIN_FTM1_CH1_2            (PIN_ALT2   | PIN_PORTB | PIN3)
#define PIN_FTM1_CH2_1            (PIN_ALT2   | PIN_PORTA | PIN15)
#define PIN_FTM1_CH2_2            (PIN_ALT2   | PIN_PORTC | PIN14)
#define PIN_FTM1_CH3_1            (PIN_ALT2   | PIN_PORTA | PIN16)
#define PIN_FTM1_CH3_2            (PIN_ALT2   | PIN_PORTC | PIN15)
#define PIN_FTM1_CH4_1            (PIN_ALT2   | PIN_PORTA | PIN10)
#define PIN_FTM1_CH4_2            (PIN_ALT6   | PIN_PORTD | PIN8)
#define PIN_FTM1_CH5_1            (PIN_ALT2   | PIN_PORTA | PIN11)
#define PIN_FTM1_CH5_2            (PIN_ALT6   | PIN_PORTD | PIN9)
#define PIN_FTM1_CH6_1            (PIN_ALT2   | PIN_PORTA | PIN12)
#define PIN_FTM1_CH6_2            (PIN_ALT6   | PIN_PORTC | PIN0)
#define PIN_FTM1_CH7_1            (PIN_ALT2   | PIN_PORTA | PIN13)
#define PIN_FTM1_CH7_2            (PIN_ALT6   | PIN_PORTC | PIN1)
#define PIN_FTM1_FLT0_1           (PIN_ALT3   | PIN_PORTC | PIN8)
#define PIN_FTM1_FLT0_2           (PIN_ALT6   | PIN_PORTA | PIN14)
#define PIN_FTM1_FLT1_1           (PIN_ALT3   | PIN_PORTC | PIN9)
#define PIN_FTM1_FLT1_2           (PIN_ALT6   | PIN_PORTE | PIN1)
#define PIN_FTM1_FLT2_1           (PIN_ALT2   | PIN_PORTC | PIN16)
#define PIN_FTM1_FLT2_2           (PIN_ALT6   | PIN_PORTE | PIN0)
#define PIN_FTM1_FLT3_1           (PIN_ALT2   | PIN_PORTC | PIN17)
#define PIN_FTM1_FLT3_2           (PIN_ALT6   | PIN_PORTA | PIN9)
#define PIN_FTM1_QD_PHA_1         (PIN_ALT4   | PIN_PORTB | PIN3)
#define PIN_FTM1_QD_PHA_2         (PIN_ALT5   | PIN_PORTA | PIN1)
#define PIN_FTM1_QD_PHA_3         (PIN_ALT6   | PIN_PORTC | PIN7)
#define PIN_FTM1_QD_PHB_1         (PIN_ALT4   | PIN_PORTB | PIN2)
#define PIN_FTM1_QD_PHB_2         (PIN_ALT6   | PIN_PORTC | PIN4)
#define PIN_FTM1_QD_PHB_3         (PIN_ALT6   | PIN_PORTC | PIN6)

#define PIN_FTM2_CH0_1            (PIN_ALT2   | PIN_PORTC | PIN5)
#define PIN_FTM2_CH0_2            (PIN_ALT2   | PIN_PORTD | PIN10)
#define PIN_FTM2_CH0_3            (PIN_ALT4   | PIN_PORTD | PIN0)
#define PIN_FTM2_CH1_1            (PIN_ALT2   | PIN_PORTA | PIN0)
#define PIN_FTM2_CH1_2            (PIN_ALT2   | PIN_PORTD | PIN11)
#define PIN_FTM2_CH1_3            (PIN_ALT4   | PIN_PORTD | PIN1)
#define PIN_FTM2_CH2_1            (PIN_ALT2   | PIN_PORTD | PIN12)
#define PIN_FTM2_CH2_2            (PIN_ALT4   | PIN_PORTE | PIN4)
#define PIN_FTM2_CH3_1            (PIN_ALT2   | PIN_PORTD | PIN5)
#define PIN_FTM2_CH3_2            (PIN_ALT4   | PIN_PORTE | PIN5)
#define PIN_FTM2_CH4_1            (PIN_ALT2   | PIN_PORTD | PIN13)
#define PIN_FTM2_CH4_2            (PIN_ALT4   | PIN_PORTE | PIN10)
#define PIN_FTM2_CH5_1            (PIN_ALT2   | PIN_PORTD | PIN14)
#define PIN_FTM2_CH5_2            (PIN_ALT4   | PIN_PORTE | PIN11)
#define PIN_FTM2_CH6_1            (PIN_ALT3   | PIN_PORTC | PIN12)
#define PIN_FTM2_CH6_2            (PIN_ALT4   | PIN_PORTE | PIN15)
#define PIN_FTM2_CH7_1            (PIN_ALT3   | PIN_PORTC | PIN13)
#define PIN_FTM2_CH7_2            (PIN_ALT4   | PIN_PORTE | PIN16)
#define PIN_FTM2_FLT0_1           (PIN_ALT4   | PIN_PORTE | PIN13)
#define PIN_FTM2_FLT0_2           (PIN_ALT4   | PIN_PORTE | PIN3)
#define PIN_FTM2_FLT1_1           (PIN_ALT4   | PIN_PORTD | PIN5)
#define PIN_FTM2_FLT1_2           (PIN_ALT4   | PIN_PORTE | PIN14)
#define PIN_FTM2_FLT2_1           (PIN_ALT4   | PIN_PORTD | PIN6)
#define PIN_FTM2_FLT2_2           (PIN_ALT4   | PIN_PORTD | PIN8)
#define PIN_FTM2_FLT3_1           (PIN_ALT4   | PIN_PORTD | PIN7)
#define PIN_FTM2_FLT3_2           (PIN_ALT4   | PIN_PORTD | PIN9)
#define PIN_FTM2_QD_PHA_1         (PIN_ALT3   | PIN_PORTD | PIN11)
#define PIN_FTM2_QD_PHA_2         (PIN_ALT3   | PIN_PORTE | PIN5)
#define PIN_FTM2_QD_PHA_3         (PIN_ALT5   | PIN_PORTA | PIN0)
#define PIN_FTM2_QD_PHA_4         (PIN_ALT6   | PIN_PORTA | PIN13)
#define PIN_FTM2_QD_PHB_1         (PIN_ALT3   | PIN_PORTD | PIN10)
#define PIN_FTM2_QD_PHB_2         (PIN_ALT3   | PIN_PORTE | PIN4)
#define PIN_FTM2_QD_PHB_3         (PIN_ALT6   | PIN_PORTA | PIN12)
#define PIN_FTM2_QD_PHB_4         (PIN_ALT6   | PIN_PORTC | PIN5)

#define PIN_FTM3_CH0_1            (PIN_ALT2   | PIN_PORTA | PIN2)
#define PIN_FTM3_CH0_2            (PIN_ALT2   | PIN_PORTB | PIN8)
#define PIN_FTM3_CH1_1            (PIN_ALT2   | PIN_PORTA | PIN3)
#define PIN_FTM3_CH1_2            (PIN_ALT2   | PIN_PORTB | PIN9)
#define PIN_FTM3_CH2_1            (PIN_ALT2   | PIN_PORTB | PIN10)
#define PIN_FTM3_CH2_2            (PIN_ALT4   | PIN_PORTC | PIN6)
#define PIN_FTM3_CH3_1            (PIN_ALT2   | PIN_PORTB | PIN11)
#define PIN_FTM3_CH3_2            (PIN_ALT4   | PIN_PORTC | PIN7)
#define PIN_FTM3_CH4_1            (PIN_ALT2   | PIN_PORTC | PIN10)
#define PIN_FTM3_CH4_2            (PIN_ALT2   | PIN_PORTD | PIN2)
#define PIN_FTM3_CH5_1            (PIN_ALT2   | PIN_PORTC | PIN11)
#define PIN_FTM3_CH5_2            (PIN_ALT2   | PIN_PORTD | PIN3)
#define PIN_FTM3_CH6_1            (PIN_ALT2   | PIN_PORTC | PIN12)
#define PIN_FTM3_CH6_2            (PIN_ALT4   | PIN_PORTE | PIN2)
#define PIN_FTM3_CH7_1            (PIN_ALT2   | PIN_PORTC | PIN13)
#define PIN_FTM3_CH7_2            (PIN_ALT4   | PIN_PORTE | PIN6)
#define PIN_FTM3_FLT0_1           (PIN_ALT3   | PIN_PORTA | PIN17)
#define PIN_FTM3_FLT0_2           (PIN_ALT3   | PIN_PORTE | PIN7)
#define PIN_FTM3_FLT1_1           (PIN_ALT3   | PIN_PORTA | PIN14)
#define PIN_FTM3_FLT1_2           (PIN_ALT3   | PIN_PORTB | PIN13)
#define PIN_FTM3_FLT2_1           (PIN_ALT3   | PIN_PORTB | PIN12)
#define PIN_FTM3_FLT2_2           (PIN_ALT5   | PIN_PORTA | PIN9)
#define PIN_FTM3_FLT3_1           (PIN_ALT3   | PIN_PORTD | PIN4)
#define PIN_FTM3_FLT3_2           (PIN_ALT5   | PIN_PORTA | PIN8)

#define PIN_FTM4_CH0_1            (PIN_ALT2   | PIN_PORTA | PIN18)
#define PIN_FTM4_CH0_2            (PIN_ALT2   | PIN_PORTC | PIN24)
#define PIN_FTM4_CH0_3            (PIN_ALT2   | PIN_PORTE | PIN20)
#define PIN_FTM4_CH1_1            (PIN_ALT2   | PIN_PORTA | PIN19)
#define PIN_FTM4_CH1_2            (PIN_ALT2   | PIN_PORTC | PIN25)
#define PIN_FTM4_CH1_3            (PIN_ALT2   | PIN_PORTE | PIN21)
#define PIN_FTM4_CH2_1            (PIN_ALT2   | PIN_PORTA | PIN20)
#define PIN_FTM4_CH2_2            (PIN_ALT2   | PIN_PORTE | PIN22)
#define PIN_FTM4_CH2_3            (PIN_ALT3   | PIN_PORTC | PIN11)
#define PIN_FTM4_CH3_1            (PIN_ALT2   | PIN_PORTA | PIN21)
#define PIN_FTM4_CH3_2            (PIN_ALT2   | PIN_PORTC | PIN26)
#define PIN_FTM4_CH3_3            (PIN_ALT2   | PIN_PORTE | PIN23)
#define PIN_FTM4_CH4_1            (PIN_ALT2   | PIN_PORTA | PIN22)
#define PIN_FTM4_CH4_2            (PIN_ALT2   | PIN_PORTC | PIN27)
#define PIN_FTM4_CH4_3            (PIN_ALT2   | PIN_PORTE | PIN24)
#define PIN_FTM4_CH5_1            (PIN_ALT2   | PIN_PORTE | PIN13)
#define PIN_FTM4_CH5_2            (PIN_ALT2   | PIN_PORTE | PIN25)
#define PIN_FTM4_CH5_3            (PIN_ALT6   | PIN_PORTB | PIN1)
#define PIN_FTM4_CH6_1            (PIN_ALT2   | PIN_PORTA | PIN23)
#define PIN_FTM4_CH6_2            (PIN_ALT2   | PIN_PORTE | PIN26)
#define PIN_FTM4_CH6_3            (PIN_ALT6   | PIN_PORTB | PIN0)
#define PIN_FTM4_CH7_1            (PIN_ALT2   | PIN_PORTA | PIN24)
#define PIN_FTM4_CH7_2            (PIN_ALT2   | PIN_PORTC | PIN28)
#define PIN_FTM4_CH7_3            (PIN_ALT2   | PIN_PORTE | PIN27)
#define PIN_FTM4_FLT0_1           (PIN_ALT5   | PIN_PORTE | PIN16)
#define PIN_FTM4_FLT0_2           (PIN_ALT7   | PIN_PORTA | PIN9)
#define PIN_FTM4_FLT1_1           (PIN_ALT5   | PIN_PORTE | PIN15)
#define PIN_FTM4_FLT1_2           (PIN_ALT6   | PIN_PORTA | PIN8)

#define PIN_FTM5_CH0_1            (PIN_ALT2   | PIN_PORTA | PIN25)
#define PIN_FTM5_CH0_2            (PIN_ALT4   | PIN_PORTC | PIN9)
#define PIN_FTM5_CH1_1            (PIN_ALT2   | PIN_PORTA | PIN26)
#define PIN_FTM5_CH1_2            (PIN_ALT4   | PIN_PORTC | PIN8)
#define PIN_FTM5_CH2_1            (PIN_ALT2   | PIN_PORTA | PIN27)
#define PIN_FTM5_CH2_2            (PIN_ALT2   | PIN_PORTC | PIN29)
#define PIN_FTM5_CH3_1            (PIN_ALT2   | PIN_PORTA | PIN28)
#define PIN_FTM5_CH3_2            (PIN_ALT3   | PIN_PORTA | PIN7)
#define PIN_FTM5_CH4_1            (PIN_ALT2   | PIN_PORTA | PIN29)
#define PIN_FTM5_CH4_2            (PIN_ALT2   | PIN_PORTC | PIN30)
#define PIN_FTM5_CH5_1            (PIN_ALT2   | PIN_PORTA | PIN30)
#define PIN_FTM5_CH5_2            (PIN_ALT4   | PIN_PORTA | PIN6)
#define PIN_FTM5_CH6_1            (PIN_ALT2   | PIN_PORTA | PIN31)
#define PIN_FTM5_CH6_2            (PIN_ALT2   | PIN_PORTC | PIN31)
#define PIN_FTM5_CH7_1            (PIN_ALT2   | PIN_PORTB | PIN18)
#define PIN_FTM5_CH7_2            (PIN_ALT2   | PIN_PORTB | PIN19)
#define PIN_FTM5_CH7_3            (PIN_ALT2   | PIN_PORTD | PIN18)
#define PIN_FTM5_FLT0_1           (PIN_ALT4   | PIN_PORTE | PIN12)
#define PIN_FTM5_FLT0_2           (PIN_ALT5   | PIN_PORTA | PIN17)
#define PIN_FTM5_FLT1_1           (PIN_ALT4   | PIN_PORTB | PIN17)
#define PIN_FTM5_FLT1_2           (PIN_ALT4   | PIN_PORTD | PIN17)

#define PIN_FTM6_CH0_1            (PIN_ALT2   | PIN_PORTB | PIN20)
#define PIN_FTM6_CH0_2            (PIN_ALT2   | PIN_PORTD | PIN19)
#define PIN_FTM6_CH0_3            (PIN_ALT5   | PIN_PORTA | PIN18)
#define PIN_FTM6_CH1_1            (PIN_ALT2   | PIN_PORTB | PIN21)
#define PIN_FTM6_CH1_2            (PIN_ALT2   | PIN_PORTD | PIN20)
#define PIN_FTM6_CH2_1            (PIN_ALT2   | PIN_PORTB | PIN22)
#define PIN_FTM6_CH2_2            (PIN_ALT2   | PIN_PORTD | PIN21)
#define PIN_FTM6_CH3_1            (PIN_ALT2   | PIN_PORTB | PIN23)
#define PIN_FTM6_CH3_2            (PIN_ALT2   | PIN_PORTD | PIN22)
#define PIN_FTM6_CH4_1            (PIN_ALT2   | PIN_PORTB | PIN24)
#define PIN_FTM6_CH4_2            (PIN_ALT2   | PIN_PORTD | PIN23)
#define PIN_FTM6_CH5_1            (PIN_ALT2   | PIN_PORTB | PIN25)
#define PIN_FTM6_CH5_2            (PIN_ALT2   | PIN_PORTD | PIN24)
#define PIN_FTM6_CH6_1            (PIN_ALT2   | PIN_PORTB | PIN26)
#define PIN_FTM6_CH6_2            (PIN_ALT2   | PIN_PORTD | PIN25)
#define PIN_FTM6_CH7_1            (PIN_ALT2   | PIN_PORTB | PIN27)
#define PIN_FTM6_CH7_2            (PIN_ALT2   | PIN_PORTD | PIN26)
#define PIN_FTM6_FLT0_1           (PIN_ALT5   | PIN_PORTB | PIN13)
#define PIN_FTM6_FLT0_2           (PIN_ALT5   | PIN_PORTD | PIN31)
#define PIN_FTM6_FLT1_1           (PIN_ALT3   | PIN_PORTD | PIN30)
#define PIN_FTM6_FLT1_2           (PIN_ALT5   | PIN_PORTB | PIN12)

#define PIN_FTM7_CH0_1            (PIN_ALT2   | PIN_PORTB | PIN28)
#define PIN_FTM7_CH0_2            (PIN_ALT2   | PIN_PORTD | PIN27)
#define PIN_FTM7_CH1_1            (PIN_ALT2   | PIN_PORTB | PIN29)
#define PIN_FTM7_CH1_2            (PIN_ALT2   | PIN_PORTD | PIN28)
#define PIN_FTM7_CH2_1            (PIN_ALT2   | PIN_PORTB | PIN30)
#define PIN_FTM7_CH2_2            (PIN_ALT2   | PIN_PORTD | PIN29)
#define PIN_FTM7_CH3_1            (PIN_ALT2   | PIN_PORTB | PIN31)
#define PIN_FTM7_CH3_2            (PIN_ALT2   | PIN_PORTD | PIN30)
#define PIN_FTM7_CH4_1            (PIN_ALT2   | PIN_PORTC | PIN18)
#define PIN_FTM7_CH4_2            (PIN_ALT2   | PIN_PORTD | PIN31)
#define PIN_FTM7_CH5_1            (PIN_ALT2   | PIN_PORTC | PIN19)
#define PIN_FTM7_CH5_2            (PIN_ALT2   | PIN_PORTE | PIN17)
#define PIN_FTM7_CH6_1            (PIN_ALT2   | PIN_PORTC | PIN20)
#define PIN_FTM7_CH6_2            (PIN_ALT2   | PIN_PORTE | PIN18)
#define PIN_FTM7_CH7_1            (PIN_ALT2   | PIN_PORTC | PIN21)
#define PIN_FTM7_CH7_2            (PIN_ALT2   | PIN_PORTE | PIN19)
#define PIN_FTM7_FLT0_1           (PIN_ALT4   | PIN_PORTC | PIN21)
#define PIN_FTM7_FLT0_2           (PIN_ALT5   | PIN_PORTA | PIN15)
#define PIN_FTM7_FLT1_1           (PIN_ALT2   | PIN_PORTC | PIN22)
#define PIN_FTM7_FLT1_2           (PIN_ALT3   | PIN_PORTE | PIN6)

/* FlexIO */

#define PIN_FXIO_D0_1             (PIN_ALT3   | PIN_PORTA | PIN21)
#define PIN_FXIO_D0_2             (PIN_ALT3   | PIN_PORTC | PIN30)
#define PIN_FXIO_D0_3             (PIN_ALT3   | PIN_PORTD | PIN9)
#define PIN_FXIO_D0_4             (PIN_ALT4   | PIN_PORTA | PIN10)
#define PIN_FXIO_D0_5             (PIN_ALT6   | PIN_PORTD | PIN0)
#define PIN_FXIO_D1_1             (PIN_ALT3   | PIN_PORTA | PIN22)
#define PIN_FXIO_D1_2             (PIN_ALT3   | PIN_PORTC | PIN31)
#define PIN_FXIO_D1_3             (PIN_ALT4   | PIN_PORTA | PIN11)
#define PIN_FXIO_D1_4             (PIN_ALT5   | PIN_PORTD | PIN8)
#define PIN_FXIO_D1_5             (PIN_ALT6   | PIN_PORTD | PIN1)
#define PIN_FXIO_D2_1             (PIN_ALT3   | PIN_PORTA | PIN23)
#define PIN_FXIO_D2_2             (PIN_ALT3   | PIN_PORTD | PIN18)
#define PIN_FXIO_D2_3             (PIN_ALT4   | PIN_PORTA | PIN0)
#define PIN_FXIO_D2_4             (PIN_ALT6   | PIN_PORTE | PIN15)
#define PIN_FXIO_D3_1             (PIN_ALT3   | PIN_PORTA | PIN24)
#define PIN_FXIO_D3_2             (PIN_ALT3   | PIN_PORTD | PIN19)
#define PIN_FXIO_D3_3             (PIN_ALT4   | PIN_PORTA | PIN1)
#define PIN_FXIO_D3_4             (PIN_ALT6   | PIN_PORTE | PIN16)
#define PIN_FXIO_D4_1             (PIN_ALT3   | PIN_PORTE | PIN18)
#define PIN_FXIO_D4_2             (PIN_ALT4   | PIN_PORTD | PIN2)
#define PIN_FXIO_D4_3             (PIN_ALT5   | PIN_PORTA | PIN2)
#define PIN_FXIO_D4_4             (PIN_ALT6   | PIN_PORTE | PIN10)
#define PIN_FXIO_D5_1             (PIN_ALT3   | PIN_PORTE | PIN17)
#define PIN_FXIO_D5_2             (PIN_ALT4   | PIN_PORTD | PIN3)
#define PIN_FXIO_D5_3             (PIN_ALT5   | PIN_PORTA | PIN3)
#define PIN_FXIO_D5_4             (PIN_ALT6   | PIN_PORTE | PIN11)
#define PIN_FXIO_D6_1             (PIN_ALT3   | PIN_PORTD | PIN31)
#define PIN_FXIO_D6_2             (PIN_ALT4   | PIN_PORTA | PIN8)
#define PIN_FXIO_D6_3             (PIN_ALT5   | PIN_PORTD | PIN2)
#define PIN_FXIO_D6_4             (PIN_ALT6   | PIN_PORTE | PIN4)
#define PIN_FXIO_D7_1             (PIN_ALT3   | PIN_PORTD | PIN26)
#define PIN_FXIO_D7_2             (PIN_ALT4   | PIN_PORTA | PIN9)
#define PIN_FXIO_D7_3             (PIN_ALT5   | PIN_PORTD | PIN3)
#define PIN_FXIO_D7_4             (PIN_ALT6   | PIN_PORTE | PIN5)

/* JTAG */

#define PIN_JTAG_TCLK             (PIN_ALT7   | PIN_PORTC | PIN4)
#define PIN_JTAG_TDI              (PIN_ALT7   | PIN_PORTC | PIN5)
#define PIN_JTAG_TDO              (PIN_ALT7   | PIN_PORTA | PIN10)
#define PIN_JTAG_TMS              (PIN_ALT7   | PIN_PORTA | PIN4)

/* LPI2C */

#define PIN_LPI2C0_HREQ_1         (PIN_ALT3   | PIN_PORTB | PIN11)
#define PIN_LPI2C0_HREQ_2         (PIN_ALT3   | PIN_PORTE | PIN1)
#define PIN_LPI2C0_SCL_1          (PIN_ALT2   | PIN_PORTB | PIN7)
#define PIN_LPI2C0_SCL_2          (PIN_ALT3   | PIN_PORTA | PIN3)
#define PIN_LPI2C0_SCLS_1         (PIN_ALT3   | PIN_PORTA | PIN0)
#define PIN_LPI2C0_SCLS_2         (PIN_ALT3   | PIN_PORTB | PIN9)
#define PIN_LPI2C0_SDA_1          (PIN_ALT2   | PIN_PORTB | PIN6)
#define PIN_LPI2C0_SDA_2          (PIN_ALT3   | PIN_PORTA | PIN2)
#define PIN_LPI2C0_SDAS_1         (PIN_ALT3   | PIN_PORTA | PIN1)
#define PIN_LPI2C0_SDAS_2         (PIN_ALT3   | PIN_PORTB | PIN10)

#define PIN_LPI2C1_HREQ_1         (PIN_ALT3   | PIN_PORTD | PIN12)
#define PIN_LPI2C1_HREQ_2         (PIN_ALT4   | PIN_PORTC | PIN5)
#define PIN_LPI2C1_SCL_1          (PIN_ALT2   | PIN_PORTD | PIN9)
#define PIN_LPI2C1_SCL_2          (PIN_ALT4   | PIN_PORTD | PIN19)
#define PIN_LPI2C1_SCL_3          (PIN_ALT4   | PIN_PORTE | PIN1)
#define PIN_LPI2C1_SCLS_1         (PIN_ALT4   | PIN_PORTA | PIN13)
#define PIN_LPI2C1_SCLS_2         (PIN_ALT4   | PIN_PORTC | PIN17)
#define PIN_LPI2C1_SCLS_3         (PIN_ALT4   | PIN_PORTD | PIN18)
#define PIN_LPI2C1_SDA_1          (PIN_ALT2   | PIN_PORTD | PIN8)
#define PIN_LPI2C1_SDA_2          (PIN_ALT4   | PIN_PORTC | PIN31)
#define PIN_LPI2C1_SDA_3          (PIN_ALT4   | PIN_PORTE | PIN0)
#define PIN_LPI2C1_SDAS_1         (PIN_ALT4   | PIN_PORTA | PIN12)
#define PIN_LPI2C1_SDAS_2         (PIN_ALT4   | PIN_PORTC | PIN16)
#define PIN_LPI2C1_SDAS_3         (PIN_ALT4   | PIN_PORTC | PIN30)

/* LPSPI */

#define PIN_LPSPI0_PCS0_1         (PIN_ALT3   | PIN_PORTB | PIN0)
#define PIN_LPSPI0_PCS0_2         (PIN_ALT4   | PIN_PORTA | PIN26)
#define PIN_LPSPI0_PCS0_3         (PIN_ALT4   | PIN_PORTB | PIN5)
#define PIN_LPSPI0_PCS1_1         (PIN_ALT3   | PIN_PORTB | PIN5)
#define PIN_LPSPI0_PCS1_2         (PIN_ALT4   | PIN_PORTA | PIN31)
#define PIN_LPSPI0_PCS2           (PIN_ALT2   | PIN_PORTE | PIN6)
#define PIN_LPSPI0_PCS3           (PIN_ALT3   | PIN_PORTA | PIN15)
#define PIN_LPSPI0_SCK_1          (PIN_ALT2   | PIN_PORTC | PIN23)
#define PIN_LPSPI0_SCK_2          (PIN_ALT2   | PIN_PORTE | PIN0)
#define PIN_LPSPI0_SCK_3          (PIN_ALT3   | PIN_PORTB | PIN2)
#define PIN_LPSPI0_SCK_4          (PIN_ALT4   | PIN_PORTD | PIN15)
#define PIN_LPSPI0_SIN_1          (PIN_ALT2   | PIN_PORTE | PIN1)
#define PIN_LPSPI0_SIN_2          (PIN_ALT3   | PIN_PORTB | PIN3)
#define PIN_LPSPI0_SIN_3          (PIN_ALT4   | PIN_PORTD | PIN16)
#define PIN_LPSPI0_SOUT_1         (PIN_ALT2   | PIN_PORTE | PIN2)
#define PIN_LPSPI0_SOUT_2         (PIN_ALT3   | PIN_PORTB | PIN1)
#define PIN_LPSPI0_SOUT_3         (PIN_ALT3   | PIN_PORTB | PIN4)
#define PIN_LPSPI0_SOUT_4         (PIN_ALT4   | PIN_PORTA | PIN30)

#define PIN_LPSPI1_PCS0_1         (PIN_ALT3   | PIN_PORTA | PIN26)
#define PIN_LPSPI1_PCS0_2         (PIN_ALT3   | PIN_PORTD | PIN3)
#define PIN_LPSPI1_PCS0_3         (PIN_ALT4   | PIN_PORTA | PIN21)
#define PIN_LPSPI1_PCS0_4         (PIN_ALT5   | PIN_PORTE | PIN1)
#define PIN_LPSPI1_PCS1_1         (PIN_ALT3   | PIN_PORTA | PIN6)
#define PIN_LPSPI1_PCS1_2         (PIN_ALT4   | PIN_PORTA | PIN22)
#define PIN_LPSPI1_PCS1_3         (PIN_ALT4   | PIN_PORTB | PIN18)
#define PIN_LPSPI1_PCS2           (PIN_ALT3   | PIN_PORTA | PIN16)
#define PIN_LPSPI1_PCS3           (PIN_ALT3   | PIN_PORTB | PIN17)
#define PIN_LPSPI1_SCK_1          (PIN_ALT3   | PIN_PORTA | PIN28)
#define PIN_LPSPI1_SCK_2          (PIN_ALT3   | PIN_PORTB | PIN14)
#define PIN_LPSPI1_SCK_3          (PIN_ALT3   | PIN_PORTD | PIN0)
#define PIN_LPSPI1_SCK_4          (PIN_ALT4   | PIN_PORTA | PIN19)
#define PIN_LPSPI1_SIN_1          (PIN_ALT3   | PIN_PORTB | PIN15)
#define PIN_LPSPI1_SIN_2          (PIN_ALT3   | PIN_PORTD | PIN1)
#define PIN_LPSPI1_SIN_3          (PIN_ALT4   | PIN_PORTA | PIN20)
#define PIN_LPSPI1_SIN_4          (PIN_ALT5   | PIN_PORTA | PIN29)
#define PIN_LPSPI1_SOUT_1         (PIN_ALT3   | PIN_PORTA | PIN27)
#define PIN_LPSPI1_SOUT_2         (PIN_ALT3   | PIN_PORTB | PIN16)
#define PIN_LPSPI1_SOUT_3         (PIN_ALT3   | PIN_PORTD | PIN2)
#define PIN_LPSPI1_SOUT_4         (PIN_ALT4   | PIN_PORTA | PIN18)
#define PIN_LPSPI1_SOUT_5         (PIN_ALT5   | PIN_PORTE | PIN0)

#define PIN_LPSPI2_PCS0_1         (PIN_ALT2   | PIN_PORTE | PIN11)
#define PIN_LPSPI2_PCS0_2         (PIN_ALT3   | PIN_PORTA | PIN9)
#define PIN_LPSPI2_PCS0_3         (PIN_ALT3   | PIN_PORTC | PIN14)
#define PIN_LPSPI2_PCS0_4         (PIN_ALT5   | PIN_PORTB | PIN25)
#define PIN_LPSPI2_PCS1_1         (PIN_ALT3   | PIN_PORTE | PIN10)
#define PIN_LPSPI2_PCS1_2         (PIN_ALT5   | PIN_PORTC | PIN19)
#define PIN_LPSPI2_PCS2           (PIN_ALT3   | PIN_PORTE | PIN13)
#define PIN_LPSPI2_PCS3           (PIN_ALT4   | PIN_PORTA | PIN15)
#define PIN_LPSPI2_SCK_1          (PIN_ALT3   | PIN_PORTC | PIN15)
#define PIN_LPSPI2_SCK_2          (PIN_ALT3   | PIN_PORTE | PIN15)
#define PIN_LPSPI2_SCK_3          (PIN_ALT5   | PIN_PORTB | PIN29)
#define PIN_LPSPI2_SIN_1          (PIN_ALT3   | PIN_PORTC | PIN0)
#define PIN_LPSPI2_SIN_2          (PIN_ALT3   | PIN_PORTE | PIN16)
#define PIN_LPSPI2_SIN_3          (PIN_ALT5   | PIN_PORTB | PIN28)
#define PIN_LPSPI2_SOUT_1         (PIN_ALT3   | PIN_PORTA | PIN8)
#define PIN_LPSPI2_SOUT_2         (PIN_ALT3   | PIN_PORTC | PIN1)
#define PIN_LPSPI2_SOUT_3         (PIN_ALT5   | PIN_PORTB | PIN27)

/* LPTimer */

#define PIN_LPTMR0_ALT1           (PIN_ALT3   | PIN_PORTE | PIN11)
#define PIN_LPTMR0_ALT2           (PIN_ALT3   | PIN_PORTD | PIN5)
#define PIN_LPTMR0_ALT3_1         (PIN_ALT3   | PIN_PORTE | PIN2)
#define PIN_LPTMR0_ALT3_2         (PIN_ALT4   | PIN_PORTB | PIN0)

/* LPUARTs */

#define PIN_LPUART0_CTS_1         (PIN_ALT6   | PIN_PORTA | PIN0)
#define PIN_LPUART0_CTS_2         (PIN_ALT6   | PIN_PORTC | PIN8)
#define PIN_LPUART0_RTS_1         (PIN_ALT6   | PIN_PORTA | PIN1)
#define PIN_LPUART0_RTS_2         (PIN_ALT6   | PIN_PORTC | PIN9)
#define PIN_LPUART0_RX_1          (PIN_ALT2   | PIN_PORTB | PIN0)
#define PIN_LPUART0_RX_2          (PIN_ALT4   | PIN_PORTA | PIN28)
#define PIN_LPUART0_RX_3          (PIN_ALT4   | PIN_PORTC | PIN2)
#define PIN_LPUART0_RX_4          (PIN_ALT6   | PIN_PORTA | PIN2)
#define PIN_LPUART0_TX_1          (PIN_ALT2   | PIN_PORTB | PIN1)
#define PIN_LPUART0_TX_2          (PIN_ALT4   | PIN_PORTA | PIN27)
#define PIN_LPUART0_TX_3          (PIN_ALT4   | PIN_PORTC | PIN3)
#define PIN_LPUART0_TX_4          (PIN_ALT6   | PIN_PORTA | PIN3)

#define PIN_LPUART1_CTS_1         (PIN_ALT2   | PIN_PORTE | PIN15)
#define PIN_LPUART1_CTS_2         (PIN_ALT6   | PIN_PORTA | PIN6)
#define PIN_LPUART1_CTS_3         (PIN_ALT6   | PIN_PORTE | PIN2)
#define PIN_LPUART1_RTS_1         (PIN_ALT2   | PIN_PORTE | PIN16)
#define PIN_LPUART1_RTS_2         (PIN_ALT6   | PIN_PORTA | PIN7)
#define PIN_LPUART1_RTS_3         (PIN_ALT6   | PIN_PORTE | PIN6)
#define PIN_LPUART1_RX_1          (PIN_ALT2   | PIN_PORTC | PIN6)
#define PIN_LPUART1_RX_2          (PIN_ALT2   | PIN_PORTC | PIN8)
#define PIN_LPUART1_RX_3          (PIN_ALT3   | PIN_PORTA | PIN19)
#define PIN_LPUART1_RX_4          (PIN_ALT3   | PIN_PORTB | PIN23)
#define PIN_LPUART1_RX_5          (PIN_ALT3   | PIN_PORTD | PIN13)
#define PIN_LPUART1_TX_1          (PIN_ALT2   | PIN_PORTC | PIN7)
#define PIN_LPUART1_TX_2          (PIN_ALT2   | PIN_PORTC | PIN9)
#define PIN_LPUART1_TX_3          (PIN_ALT3   | PIN_PORTA | PIN18)
#define PIN_LPUART1_TX_4          (PIN_ALT3   | PIN_PORTD | PIN14)
#define PIN_LPUART1_TX_5          (PIN_ALT5   | PIN_PORTB | PIN22)

#define PIN_LPUART2_CTS_1         (PIN_ALT3   | PIN_PORTE | PIN9)
#define PIN_LPUART2_CTS_2         (PIN_ALT4   | PIN_PORTC | PIN12)
#define PIN_LPUART2_CTS_3         (PIN_ALT6   | PIN_PORTD | PIN11)
#define PIN_LPUART2_RTS_1         (PIN_ALT3   | PIN_PORTE | PIN3)
#define PIN_LPUART2_RTS_2         (PIN_ALT4   | PIN_PORTC | PIN13)
#define PIN_LPUART2_RTS_3         (PIN_ALT6   | PIN_PORTD | PIN12)
#define PIN_LPUART2_RX_1          (PIN_ALT2   | PIN_PORTA | PIN8)
#define PIN_LPUART2_RX_2          (PIN_ALT2   | PIN_PORTD | PIN6)
#define PIN_LPUART2_RX_3          (PIN_ALT3   | PIN_PORTA | PIN30)
#define PIN_LPUART2_RX_4          (PIN_ALT3   | PIN_PORTD | PIN17)
#define PIN_LPUART2_TX_1          (PIN_ALT2   | PIN_PORTA | PIN9)
#define PIN_LPUART2_TX_2          (PIN_ALT2   | PIN_PORTD | PIN7)
#define PIN_LPUART2_TX_3          (PIN_ALT3   | PIN_PORTE | PIN12)
#define PIN_LPUART2_TX_4          (PIN_ALT4   | PIN_PORTA | PIN29)

/* MII / RMII */

#define PIN_MII_COL_1             (PIN_ALT4   | PIN_PORTB | PIN23)
#define PIN_MII_COL_2             (PIN_ALT4   | PIN_PORTC | PIN14)
#define PIN_MII_CRS_1             (PIN_ALT3   | PIN_PORTB | PIN22)
#define PIN_MII_CRS_2             (PIN_ALT4   | PIN_PORTC | PIN15)
#define PIN_MII_MDC_1             (PIN_ALT5   | PIN_PORTE | PIN8)
#define PIN_MII_MDC_2             (PIN_ALT7   | PIN_PORTB | PIN5)
#define PIN_MII_MDIO              (PIN_ALT5   | PIN_PORTB | PIN4)
#define PIN_MII_RX_DV             (PIN_ALT5   | PIN_PORTC | PIN17)
#define PIN_MII_RX_ER             (PIN_ALT5   | PIN_PORTC | PIN16)
#define PIN_MII_RX_CLK            (PIN_ALT5   | PIN_PORTD | PIN10)
#define PIN_MII_RXD1_1            (PIN_ALT5   | PIN_PORTC | PIN0)
#define PIN_MII_RXD1_2            (PIN_ALT5   | PIN_PORTC | PIN1)
#define PIN_MII_RXD1_3            (PIN_ALT4   | PIN_PORTC | PIN0)
#define PIN_MII_RXD1_4            (PIN_ALT4   | PIN_PORTC | PIN1)
#define PIN_MII_RXD2              (PIN_ALT5   | PIN_PORTD | PIN9)
#define PIN_MII_RXD3              (PIN_ALT3   | PIN_PORTD | PIN8)
#define PIN_MII_TX_CLK            (PIN_ALT5   | PIN_PORTD | PIN11)
#define PIN_MII_TX_EN             (PIN_ALT5   | PIN_PORTD | PIN12)
#define PIN_MII_TX_ER             (PIN_ALT5   | PIN_PORTC | PIN3)
#define PIN_MII_TXD1_1            (PIN_ALT5   | PIN_PORTC | PIN2)
#define PIN_MII_TXD1_2            (PIN_ALT5   | PIN_PORTD | PIN7)
#define PIN_MII_TXD2              (PIN_ALT5   | PIN_PORTD | PIN6)
#define PIN_MII_TXD3              (PIN_ALT5   | PIN_PORTD | PIN5)

#define PIN_RMII_MDC_1            (PIN_ALT5   | PIN_PORTE | PIN8)
#define PIN_RMII_MDC_2            (PIN_ALT7   | PIN_PORTB | PIN5)
#define PIN_RMII_MDIO             (PIN_ALT5   | PIN_PORTB | PIN4)
#define PIN_RMII_RX_DV            (PIN_ALT5   | PIN_PORTC | PIN17)
#define PIN_RMII_RX_ER            (PIN_ALT5   | PIN_PORTC | PIN16)
#define PIN_RMII_RXD0_1           (PIN_ALT5   | PIN_PORTC | PIN1)
#define PIN_RMII_RXD1_1           (PIN_ALT4   | PIN_PORTC | PIN0)
#define PIN_RMII_RXD0_2           (PIN_ALT5   | PIN_PORTC | PIN0)
#define PIN_RMII_RXD1_2           (PIN_ALT4   | PIN_PORTC | PIN1)
#define PIN_RMII_TX_CLK           (PIN_ALT5   | PIN_PORTD | PIN11)
#define PIN_RMII_TX_EN            (PIN_ALT5   | PIN_PORTD | PIN12)
#define PIN_RMII_TXD0             (PIN_ALT5   | PIN_PORTC | PIN2)
#define PIN_RMII_TXD1             (PIN_ALT5   | PIN_PORTD | PIN7)

#define PIN_RMII_MDC              PIN_RMII_MDC_2
#define PIN_RMII_RXD0             PIN_RMII_RXD0_1
#define PIN_RMII_RXD1             PIN_RMII_RXD1_1

/* NMI */

#define PIN_NMI                   (PIN_ALT7   | PIN_PORTD | PIN3)

/* GPIO */

#define PIN_PTA0                  (PIN_ALT1   | PIN_PORTA | PIN0)
#define PIN_PTA1                  (PIN_ALT1   | PIN_PORTA | PIN1)
#define PIN_PTA2                  (PIN_ALT1   | PIN_PORTA | PIN2)
#define PIN_PTA3                  (PIN_ALT1   | PIN_PORTA | PIN3)
#define PIN_PTA4                  (PIN_ALT1   | PIN_PORTA | PIN4)
#define PIN_PTA5                  (PIN_ALT1   | PIN_PORTA | PIN5)
#define PIN_PTA6                  (PIN_ALT1   | PIN_PORTA | PIN6)
#define PIN_PTA7                  (PIN_ALT1   | PIN_PORTA | PIN7)
#define PIN_PTA8                  (PIN_ALT1   | PIN_PORTA | PIN8)
#define PIN_PTA9                  (PIN_ALT1   | PIN_PORTA | PIN9)
#define PIN_PTA10                 (PIN_ALT1   | PIN_PORTA | PIN10)
#define PIN_PTA11                 (PIN_ALT1   | PIN_PORTA | PIN11)
#define PIN_PTA12                 (PIN_ALT1   | PIN_PORTA | PIN12)
#define PIN_PTA13                 (PIN_ALT1   | PIN_PORTA | PIN13)
#define PIN_PTA14                 (PIN_ALT1   | PIN_PORTA | PIN14)
#define PIN_PTA15                 (PIN_ALT1   | PIN_PORTA | PIN15)
#define PIN_PTA16                 (PIN_ALT1   | PIN_PORTA | PIN16)
#define PIN_PTA17                 (PIN_ALT1   | PIN_PORTA | PIN17)
#define PIN_PTA18                 (PIN_ALT1   | PIN_PORTA | PIN18)
#define PIN_PTA19                 (PIN_ALT1   | PIN_PORTA | PIN19)
#define PIN_PTA20                 (PIN_ALT1   | PIN_PORTA | PIN20)
#define PIN_PTA21                 (PIN_ALT1   | PIN_PORTA | PIN21)
#define PIN_PTA22                 (PIN_ALT1   | PIN_PORTA | PIN22)
#define PIN_PTA23                 (PIN_ALT1   | PIN_PORTA | PIN23)
#define PIN_PTA24                 (PIN_ALT1   | PIN_PORTA | PIN24)
#define PIN_PTA25                 (PIN_ALT1   | PIN_PORTA | PIN25)
#define PIN_PTA26                 (PIN_ALT1   | PIN_PORTA | PIN26)
#define PIN_PTA27                 (PIN_ALT1   | PIN_PORTA | PIN27)
#define PIN_PTA28                 (PIN_ALT1   | PIN_PORTA | PIN28)
#define PIN_PTA29                 (PIN_ALT1   | PIN_PORTA | PIN29)
#define PIN_PTA30                 (PIN_ALT1   | PIN_PORTA | PIN30)
#define PIN_PTA31                 (PIN_ALT1   | PIN_PORTA | PIN31)

#define PIN_PTB0                  (PIN_ALT1   | PIN_PORTB | PIN0)
#define PIN_PTB1                  (PIN_ALT1   | PIN_PORTB | PIN1)
#define PIN_PTB2                  (PIN_ALT1   | PIN_PORTB | PIN2)
#define PIN_PTB3                  (PIN_ALT1   | PIN_PORTB | PIN3)
#define PIN_PTB4                  (PIN_ALT1   | PIN_PORTB | PIN4)
#define PIN_PTB5                  (PIN_ALT1   | PIN_PORTB | PIN5)
#define PIN_PTB6                  (PIN_ALT1   | PIN_PORTB | PIN6)
#define PIN_PTB7                  (PIN_ALT1   | PIN_PORTB | PIN7)
#define PIN_PTB8                  (PIN_ALT1   | PIN_PORTB | PIN8)
#define PIN_PTB9                  (PIN_ALT1   | PIN_PORTB | PIN9)
#define PIN_PTB10                 (PIN_ALT1   | PIN_PORTB | PIN10)
#define PIN_PTB11                 (PIN_ALT1   | PIN_PORTB | PIN11)
#define PIN_PTB12                 (PIN_ALT1   | PIN_PORTB | PIN12)
#define PIN_PTB13                 (PIN_ALT1   | PIN_PORTB | PIN13)
#define PIN_PTB14                 (PIN_ALT1   | PIN_PORTB | PIN14)
#define PIN_PTB15                 (PIN_ALT1   | PIN_PORTB | PIN15)
#define PIN_PTB16                 (PIN_ALT1   | PIN_PORTB | PIN16)
#define PIN_PTB17                 (PIN_ALT1   | PIN_PORTB | PIN17)
#define PIN_PTB18                 (PIN_ALT1   | PIN_PORTB | PIN18)
#define PIN_PTB19                 (PIN_ALT1   | PIN_PORTB | PIN19)
#define PIN_PTB20                 (PIN_ALT1   | PIN_PORTB | PIN20)
#define PIN_PTB21                 (PIN_ALT1   | PIN_PORTB | PIN21)
#define PIN_PTB22                 (PIN_ALT1   | PIN_PORTB | PIN22)
#define PIN_PTB23                 (PIN_ALT1   | PIN_PORTB | PIN23)
#define PIN_PTB24                 (PIN_ALT1   | PIN_PORTB | PIN24)
#define PIN_PTB25                 (PIN_ALT1   | PIN_PORTB | PIN25)
#define PIN_PTB26                 (PIN_ALT1   | PIN_PORTB | PIN26)
#define PIN_PTB27                 (PIN_ALT1   | PIN_PORTB | PIN27)
#define PIN_PTB28                 (PIN_ALT1   | PIN_PORTB | PIN28)
#define PIN_PTB29                 (PIN_ALT1   | PIN_PORTB | PIN29)
#define PIN_PTB30                 (PIN_ALT1   | PIN_PORTB | PIN30)
#define PIN_PTB31                 (PIN_ALT1   | PIN_PORTB | PIN31)

#define PIN_PTC0                  (PIN_ALT1   | PIN_PORTC | PIN0)
#define PIN_PTC1                  (PIN_ALT1   | PIN_PORTC | PIN1)
#define PIN_PTC2                  (PIN_ALT1   | PIN_PORTC | PIN2)
#define PIN_PTC3                  (PIN_ALT1   | PIN_PORTC | PIN3)
#define PIN_PTC4                  (PIN_ALT1   | PIN_PORTC | PIN4)
#define PIN_PTC5                  (PIN_ALT1   | PIN_PORTC | PIN5)
#define PIN_PTC6                  (PIN_ALT1   | PIN_PORTC | PIN6)
#define PIN_PTC7                  (PIN_ALT1   | PIN_PORTC | PIN7)
#define PIN_PTC8                  (PIN_ALT1   | PIN_PORTC | PIN8)
#define PIN_PTC9                  (PIN_ALT1   | PIN_PORTC | PIN9)
#define PIN_PTC10                 (PIN_ALT1   | PIN_PORTC | PIN10)
#define PIN_PTC11                 (PIN_ALT1   | PIN_PORTC | PIN11)
#define PIN_PTC12                 (PIN_ALT1   | PIN_PORTC | PIN12)
#define PIN_PTC13                 (PIN_ALT1   | PIN_PORTC | PIN13)
#define PIN_PTC14                 (PIN_ALT1   | PIN_PORTC | PIN14)
#define PIN_PTC15                 (PIN_ALT1   | PIN_PORTC | PIN15)
#define PIN_PTC16                 (PIN_ALT1   | PIN_PORTC | PIN16)
#define PIN_PTC17                 (PIN_ALT1   | PIN_PORTC | PIN17)
#define PIN_PTC18                 (PIN_ALT1   | PIN_PORTC | PIN18)
#define PIN_PTC19                 (PIN_ALT1   | PIN_PORTC | PIN19)
#define PIN_PTC20                 (PIN_ALT1   | PIN_PORTC | PIN20)
#define PIN_PTC21                 (PIN_ALT1   | PIN_PORTC | PIN21)
#define PIN_PTC22                 (PIN_ALT1   | PIN_PORTC | PIN22)
#define PIN_PTC23                 (PIN_ALT1   | PIN_PORTC | PIN23)
#define PIN_PTC24                 (PIN_ALT1   | PIN_PORTC | PIN24)
#define PIN_PTC25                 (PIN_ALT1   | PIN_PORTC | PIN25)
#define PIN_PTC26                 (PIN_ALT1   | PIN_PORTC | PIN26)
#define PIN_PTC27                 (PIN_ALT1   | PIN_PORTC | PIN27)
#define PIN_PTC28                 (PIN_ALT1   | PIN_PORTC | PIN28)
#define PIN_PTC29                 (PIN_ALT1   | PIN_PORTC | PIN29)
#define PIN_PTC30                 (PIN_ALT1   | PIN_PORTC | PIN30)
#define PIN_PTC31                 (PIN_ALT1   | PIN_PORTC | PIN31)

#define PIN_PTD0                  (PIN_ALT1   | PIN_PORTD | PIN0)
#define PIN_PTD1                  (PIN_ALT1   | PIN_PORTD | PIN1)
#define PIN_PTD2                  (PIN_ALT1   | PIN_PORTD | PIN2)
#define PIN_PTD3                  (PIN_ALT1   | PIN_PORTD | PIN3)
#define PIN_PTD4                  (PIN_ALT1   | PIN_PORTD | PIN4)
#define PIN_PTD5                  (PIN_ALT1   | PIN_PORTD | PIN5)
#define PIN_PTD6                  (PIN_ALT1   | PIN_PORTD | PIN6)
#define PIN_PTD7                  (PIN_ALT1   | PIN_PORTD | PIN7)
#define PIN_PTD8                  (PIN_ALT1   | PIN_PORTD | PIN8)
#define PIN_PTD9                  (PIN_ALT1   | PIN_PORTD | PIN9)
#define PIN_PTD10                 (PIN_ALT1   | PIN_PORTD | PIN10)
#define PIN_PTD11                 (PIN_ALT1   | PIN_PORTD | PIN11)
#define PIN_PTD12                 (PIN_ALT1   | PIN_PORTD | PIN12)
#define PIN_PTD13                 (PIN_ALT1   | PIN_PORTD | PIN13)
#define PIN_PTD14                 (PIN_ALT1   | PIN_PORTD | PIN14)
#define PIN_PTD15                 (PIN_ALT1   | PIN_PORTD | PIN15)
#define PIN_PTD16                 (PIN_ALT1   | PIN_PORTD | PIN16)
#define PIN_PTD17                 (PIN_ALT1   | PIN_PORTD | PIN17)
#define PIN_PTD18                 (PIN_ALT1   | PIN_PORTD | PIN18)
#define PIN_PTD19                 (PIN_ALT1   | PIN_PORTD | PIN19)
#define PIN_PTD20                 (PIN_ALT1   | PIN_PORTD | PIN20)
#define PIN_PTD21                 (PIN_ALT1   | PIN_PORTD | PIN21)
#define PIN_PTD22                 (PIN_ALT1   | PIN_PORTD | PIN22)
#define PIN_PTD23                 (PIN_ALT1   | PIN_PORTD | PIN23)
#define PIN_PTD24                 (PIN_ALT1   | PIN_PORTD | PIN24)
#define PIN_PTD25                 (PIN_ALT1   | PIN_PORTD | PIN25)
#define PIN_PTD26                 (PIN_ALT1   | PIN_PORTD | PIN26)
#define PIN_PTD27                 (PIN_ALT1   | PIN_PORTD | PIN27)
#define PIN_PTD28                 (PIN_ALT1   | PIN_PORTD | PIN28)
#define PIN_PTD29                 (PIN_ALT1   | PIN_PORTD | PIN29)
#define PIN_PTD30                 (PIN_ALT1   | PIN_PORTD | PIN30)
#define PIN_PTD31                 (PIN_ALT1   | PIN_PORTD | PIN31)

#define PIN_PTE0                  (PIN_ALT1   | PIN_PORTE | PIN0)
#define PIN_PTE1                  (PIN_ALT1   | PIN_PORTE | PIN1)
#define PIN_PTE2                  (PIN_ALT1   | PIN_PORTE | PIN2)
#define PIN_PTE3                  (PIN_ALT1   | PIN_PORTE | PIN3)
#define PIN_PTE4                  (PIN_ALT1   | PIN_PORTE | PIN4)
#define PIN_PTE5                  (PIN_ALT1   | PIN_PORTE | PIN5)
#define PIN_PTE6                  (PIN_ALT1   | PIN_PORTE | PIN6)
#define PIN_PTE7                  (PIN_ALT1   | PIN_PORTE | PIN7)
#define PIN_PTE8                  (PIN_ALT1   | PIN_PORTE | PIN8)
#define PIN_PTE9                  (PIN_ALT1   | PIN_PORTE | PIN9)
#define PIN_PTE10                 (PIN_ALT1   | PIN_PORTE | PIN10)
#define PIN_PTE11                 (PIN_ALT1   | PIN_PORTE | PIN11)
#define PIN_PTE12                 (PIN_ALT1   | PIN_PORTE | PIN12)
#define PIN_PTE13                 (PIN_ALT1   | PIN_PORTE | PIN13)
#define PIN_PTE14                 (PIN_ALT1   | PIN_PORTE | PIN14)
#define PIN_PTE15                 (PIN_ALT1   | PIN_PORTE | PIN15)
#define PIN_PTE16                 (PIN_ALT1   | PIN_PORTE | PIN16)
#define PIN_PTE17                 (PIN_ALT1   | PIN_PORTE | PIN17)
#define PIN_PTE18                 (PIN_ALT1   | PIN_PORTE | PIN18)
#define PIN_PTE19                 (PIN_ALT1   | PIN_PORTE | PIN19)
#define PIN_PTE20                 (PIN_ALT1   | PIN_PORTE | PIN20)
#define PIN_PTE21                 (PIN_ALT1   | PIN_PORTE | PIN21)
#define PIN_PTE22                 (PIN_ALT1   | PIN_PORTE | PIN22)
#define PIN_PTE23                 (PIN_ALT1   | PIN_PORTE | PIN23)
#define PIN_PTE24                 (PIN_ALT1   | PIN_PORTE | PIN24)
#define PIN_PTE25                 (PIN_ALT1   | PIN_PORTE | PIN25)
#define PIN_PTE26                 (PIN_ALT1   | PIN_PORTE | PIN26)
#define PIN_PTE27                 (PIN_ALT1   | PIN_PORTE | PIN27)

/* QuadSPI */

#define PIN_QSPI_A_CS             (PIN_ALT6   | PIN_PORTC | PIN3)
#define PIN_QSPI_A_IO0            (PIN_ALT7   | PIN_PORTD | PIN11)
#define PIN_QSPI_A_IO1            (PIN_ALT7   | PIN_PORTD | PIN7)
#define PIN_QSPI_A_IO2            (PIN_ALT7   | PIN_PORTD | PIN12)
#define PIN_QSPI_A_IO3            (PIN_ALT7   | PIN_PORTC | PIN2)
#define PIN_QSPI_A_SCK            (PIN_ALT7   | PIN_PORTD | PIN10)
#define PIN_QSPI_B_CS             (PIN_ALT7   | PIN_PORTC | PIN15)
#define PIN_QSPI_B_IO0            (PIN_ALT7   | PIN_PORTB | PIN4)
#define PIN_QSPI_B_IO1            (PIN_ALT7   | PIN_PORTD | PIN6)
#define PIN_QSPI_B_IO2            (PIN_ALT7   | PIN_PORTD | PIN5)
#define PIN_QSPI_B_IO3            (PIN_ALT7   | PIN_PORTC | PIN3)
#define PIN_QSPI_B_IO4            (PIN_ALT7   | PIN_PORTD | PIN9)
#define PIN_QSPI_B_IO5            (PIN_ALT7   | PIN_PORTD | PIN8)
#define PIN_QSPI_B_IO6            (PIN_ALT7   | PIN_PORTC | PIN17)
#define PIN_QSPI_B_IO7            (PIN_ALT7   | PIN_PORTC | PIN16)
#define PIN_QSPI_B_RWDS           (PIN_ALT7   | PIN_PORTC | PIN0)
#define PIN_QSPI_B_SCK            (PIN_ALT7   | PIN_PORTC | PIN1)

/* Reset */

#define PIN_RESET                 (PIN_ALT7   | PIN_PORTA | PIN5)

/* RTC */

#define PIN_RTC_CLKIN             (PIN_ALT4   | PIN_PORTA | PIN7)
#define PIN_RTC_CLKOUT_1          (PIN_ALT3   | PIN_PORTC | PIN4)
#define PIN_RTC_CLKOUT_2          (PIN_ALT3   | PIN_PORTC | PIN5)
#define PIN_RTC_CLKOUT_3          (PIN_ALT7   | PIN_PORTD | PIN13)

/* Synchronous Audio Interface (SAI) */

#define PIN_SAI0_BCLK             (PIN_ALT7   | PIN_PORTA | PIN12)
#define PIN_SAI0_D0               (PIN_ALT7   | PIN_PORTA | PIN13)
#define PIN_SAI0_D1               (PIN_ALT7   | PIN_PORTE | PIN1)
#define PIN_SAI0_D2               (PIN_ALT7   | PIN_PORTE | PIN0)
#define PIN_SAI0_D3               (PIN_ALT7   | PIN_PORTA | PIN14)
#define PIN_SAI0_MCLK             (PIN_ALT5   | PIN_PORTD | PIN1)
#define PIN_SAI0_SYNC             (PIN_ALT6   | PIN_PORTA | PIN11)

#define PIN_SAI1_BCLK             (PIN_ALT4   | PIN_PORTB | PIN8)
#define PIN_SAI1_D0               (PIN_ALT4   | PIN_PORTB | PIN9)
#define PIN_SAI1_MCLK             (PIN_ALT4   | PIN_PORTB | PIN10)
#define PIN_SAI1_SYNC             (PIN_ALT7   | PIN_PORTE | PIN2)

/* SWD */

#define PIN_TRACE_SWO             (PIN_ALT7   | PIN_PORTA | PIN10)
#define PIN_SWD_CLK               (PIN_ALT7   | PIN_PORTC | PIN4)
#define PIN_SWD_DIO               (PIN_ALT7   | PIN_PORTA | PIN4)

/* Test Clock Input (TCLK) */

#define PIN_TCLK0                 (PIN_ALT4   | PIN_PORTB | PIN1)
#define PIN_TCLK1_1               (PIN_ALT3   | PIN_PORTA | PIN5)
#define PIN_TCLK1_2               (PIN_ALT3   | PIN_PORTE | PIN0)
#define PIN_TCLK2                 (PIN_ALT2   | PIN_PORTE | PIN5)

/* Trigger Mux Control (TRGMUX) */

#define PIN_TRGMUX_IN0            (PIN_ALT6   | PIN_PORTB | PIN5)
#define PIN_TRGMUX_IN1            (PIN_ALT6   | PIN_PORTB | PIN4)
#define PIN_TRGMUX_IN2            (PIN_ALT6   | PIN_PORTB | PIN3)
#define PIN_TRGMUX_IN3            (PIN_ALT6   | PIN_PORTB | PIN2)
#define PIN_TRGMUX_IN4            (PIN_ALT6   | PIN_PORTD | PIN3)
#define PIN_TRGMUX_IN5            (PIN_ALT6   | PIN_PORTD | PIN2)
#define PIN_TRGMUX_IN6            (PIN_ALT6   | PIN_PORTE | PIN3)
#define PIN_TRGMUX_IN7            (PIN_ALT6   | PIN_PORTD | PIN5)
#define PIN_TRGMUX_IN8            (PIN_ALT6   | PIN_PORTC | PIN15)
#define PIN_TRGMUX_IN9            (PIN_ALT6   | PIN_PORTC | PIN14)
#define PIN_TRGMUX_IN10           (PIN_ALT6   | PIN_PORTC | PIN11)
#define PIN_TRGMUX_IN11           (PIN_ALT6   | PIN_PORTC | PIN10)
#define PIN_TRGMUX_OUT0           (PIN_ALT7   | PIN_PORTA | PIN1)
#define PIN_TRGMUX_OUT1           (PIN_ALT7   | PIN_PORTD | PIN0)
#define PIN_TRGMUX_OUT2           (PIN_ALT7   | PIN_PORTD | PIN1)
#define PIN_TRGMUX_OUT3           (PIN_ALT7   | PIN_PORTA | PIN0)
#define PIN_TRGMUX_OUT4           (PIN_ALT7   | PIN_PORTE | PIN10)
#define PIN_TRGMUX_OUT5           (PIN_ALT7   | PIN_PORTE | PIN11)
#define PIN_TRGMUX_OUT6           (PIN_ALT7   | PIN_PORTE | PIN15)
#define PIN_TRGMUX_OUT7           (PIN_ALT7   | PIN_PORTE | PIN16)

/* External Crystal */

#define PIN_EXTAL                 (PIN_ANALOG | PIN_PORTB | PIN7)
#define PIN_XTAL                  (PIN_ANALOG | PIN_PORTB | PIN6)

#endif /* __ARCH_ARM_SRC_S32K1XX_HARDWARE_S32K148_PINMUX_H */
