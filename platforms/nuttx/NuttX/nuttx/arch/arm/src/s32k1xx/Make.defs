############################################################################
# arch/arm/src/s32k1xx/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

# Source files common to all S32K1xx chip families.

CHIP_CSRCS  = s32k1xx_start.c s32k1xx_lowputc.c s32k1xx_clockconfig.c
CHIP_CSRCS += s32k1xx_periphclocks.c s32k1xx_pin.c s32k1xx_pingpio.c
CHIP_CSRCS += s32k1xx_idle.c s32k1xx_uid.c

ifeq ($(CONFIG_BOOT_RUNFROMFLASH),y)
CHIP_CSRCS += s32k1xx_flashcfg.c
endif

ifeq ($(CONFIG_PWM),y)
CHIP_CSRCS += s32k1xx_pwm.c
endif

ifeq ($(CONFIG_S32K1XX_LPUART),y)
CHIP_CSRCS += s32k1xx_serial.c
endif

ifeq ($(CONFIG_S32K1XX_GPIOIRQ),y)
CHIP_CSRCS += s32k1xx_pinirq.c
endif

ifeq ($(CONFIG_S32K1XX_EDMA),y)
CHIP_CSRCS += s32k1xx_pindma.c
CHIP_CSRCS += s32k1xx_edma.c
endif

ifeq ($(CONFIG_DEBUG_GPIO_INFO),y)
CHIP_CSRCS += s32k1xx_pindump.c
endif

ifeq ($(CONFIG_S32K1XX_LPI2C),y)
CHIP_CSRCS += s32k1xx_lpi2c.c
CHIP_CSRCS += s32k1xx_lpi2c_slave.c
endif

ifeq ($(CONFIG_S32K1XX_LPSPI),y)
CHIP_CSRCS += s32k1xx_lpspi.c
endif

ifeq ($(CONFIG_S32K1XX_ENET),y)
CHIP_CSRCS += s32k1xx_enet.c
endif

ifeq ($(CONFIG_S32K1XX_FLEXCAN),y)
CHIP_CSRCS += s32k1xx_flexcan.c
endif

ifeq ($(CONFIG_S32K1XX_RTC),y)
CHIP_CSRCS += s32k1xx_rtc.c
endif

ifeq ($(CONFIG_S32K1XX_PROGMEM),y)
CHIP_CSRCS += s32k1xx_progmem.c
endif

ifeq ($(CONFIG_S32K1XX_EEEPROM),y)
CHIP_CSRCS += s32k1xx_eeeprom.c
endif

ifneq ($(CONFIG_ARCH_CUSTOM_PMINIT),y)
CHIP_CSRCS += s32k1xx_pminitialize.c
endif

ifeq ($(CONFIG_S32K1XX_RESETCAUSE_PROCFS), y)
CHIP_CSRCS += s32k1xx_resetcause_procfs.c
endif

# Source files specific to the ARM CPU family and to the S32K1xx chip family

ifeq ($(CONFIG_ARCH_CHIP_S32K11X),y)
include s32k1xx/s32k11x/Make.defs
else
include s32k1xx/s32k14x/Make.defs
endif

# Make sure that the S32K1 common directory in included in the VPATH

VPATH += chip/common
