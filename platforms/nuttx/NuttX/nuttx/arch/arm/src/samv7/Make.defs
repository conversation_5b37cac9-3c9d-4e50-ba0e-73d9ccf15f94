############################################################################
# arch/arm/src/samv7/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

# The start-up, "head", file.  Only common vectors are support so there
# isn't one.

# Common ARM and Cortex-M7 files

include armv7-m/Make.defs

# Required SAMV7 files

CHIP_CSRCS  = sam_start.c sam_clockconfig.c sam_irq.c sam_allocateheap.c
CHIP_CSRCS += sam_lowputc.c sam_serial.c sam_gpio.c sam_pck.c sam_uid.c

# Configuration-dependent SAMV7 files

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += sam_timerisr.c
endif

ifeq ($(CONFIG_ARM_MPU),y)
CHIP_CSRCS += sam_mpuinit.c
ifeq ($(CONFIG_BUILD_PROTECTED),y)
CHIP_CSRCS += sam_userspace.c
endif
endif

ifeq ($(CONFIG_SAMV7_GPIO_IRQ),y)
CHIP_CSRCS += sam_gpioirq.c
endif

ifeq ($(CONFIG_SAMV7_XDMAC),y)
CHIP_CSRCS += sam_xdmac.c
endif

ifeq ($(CONFIG_SAMV7_WDT),y)
CHIP_CSRCS += sam_wdt.c
endif

ifeq ($(CONFIG_SAMV7_RSWDT),y)
CHIP_CSRCS += sam_rswdt.c
endif

ifeq ($(CONFIG_SAMV7_SYSTEMRESET),y)
CHIP_CSRCS += sam_systemreset.c
endif

ifeq ($(CONFIG_SAMV7_SPI_MASTER),y)
CHIP_CSRCS += sam_spi.c
endif

ifeq ($(CONFIG_SAMV7_SPI_SLAVE),y)
CHIP_CSRCS += sam_spi_slave.c
endif

ifeq ($(CONFIG_SAMV7_QSPI_SPI_MODE),y)
CHIP_CSRCS += sam_qspi_spi.c
else ifeq ($(CONFIG_SAMV7_QSPI),y)
CHIP_CSRCS += sam_qspi.c
endif

ifeq ($(CONFIG_SAMV7_TWIHS0),y)
CHIP_CSRCS += sam_twihs.c
else ifeq ($(CONFIG_SAMV7_TWIHS1),y)
CHIP_CSRCS += sam_twihs.c
else ifeq ($(CONFIG_SAMV7_TWIHS2),y)
CHIP_CSRCS += sam_twihs.c
endif

ifeq ($(CONFIG_SAMV7_SSC),y)
CHIP_CSRCS += sam_ssc.c
endif

ifeq ($(CONFIG_SAMV7_HAVE_TC),y)
CHIP_CSRCS += sam_tc.c
ifeq ($(CONFIG_TIMER),y)
CHIP_CSRCS += sam_tc_lowerhalf.c
endif
ifeq ($(CONFIG_SAMV7_ONESHOT),y)
CHIP_CSRCS += sam_oneshot.c sam_oneshot_lowerhalf.c
endif
ifeq ($(CONFIG_SAMV7_FREERUN),y)
CHIP_CSRCS += sam_freerun.c
endif
ifeq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += sam_tickless.c
endif
ifeq ($(CONFIG_SENSORS_QENCODER),y)
CHIP_CSRCS += sam_qencoder.c
endif
endif

ifeq ($(CONFIG_SAMV7_HSMCI),y)
CHIP_CSRCS += sam_hsmci.c sam_hsmci_clkdiv.c
endif

ifeq ($(CONFIG_SAMV7_EMAC),y)
CHIP_CSRCS += sam_emac.c sam_ethernet.c
endif

ifeq ($(CONFIG_SAMV7_MCAN),y)
CHIP_CSRCS += sam_mcan.c
endif

ifeq ($(CONFIG_SAMV7_AFEC),y)
CHIP_CSRCS += sam_afec.c
endif

ifeq ($(CONFIG_SAMV7_USBDEVHS),y)
CHIP_CSRCS += sam_usbdevhs.c
endif

ifeq ($(CONFIG_SAMV7_TRNG),y)
CHIP_CSRCS += sam_trng.c
endif

ifeq ($(CONFIG_SAMV7_PROGMEM),y)
CHIP_CSRCS += sam_progmem.c sam_eefc.c
endif

ifeq ($(CONFIG_SAMV7_PWM),y)
CHIP_CSRCS += sam_pwm.c
endif

ifeq ($(CONFIG_SAMV7_DAC),y)
CHIP_CSRCS += sam_dac.c
endif
