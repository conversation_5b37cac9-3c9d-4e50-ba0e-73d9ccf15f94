############################################################################
# arch/arm/src/dm320/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

include arm/Make.defs

CHIP_ASRCS  = dm320_lowputc.S dm320_restart.S

CHIP_CSRCS  = dm320_allocateheap.c dm320_boot.c dm320_decodeirq.c
CHIP_CSRCS += dm320_irq.c dm320_serial.c dm320_framebuffer.c

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += dm320_timerisr.c
endif

ifeq ($(CONFIG_USBDEV),y)
CHIP_CSRCS += dm320_usbdev.c
endif
