/****************************************************************************
 * arch/arm/src/dm320/chip.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_DM320_CHIP_H
#define __ARCH_ARM_SRC_DM320_CHIP_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include "dm320_memorymap.h"
#include "dm320_clkc.h"
#include "dm320_ahb.h"
#include "dm320_busc.h"
#include "dm320_uart.h"
#include "dm320_timer.h"
#include "dm320_intc.h"
#include "dm320_gio.h"
#include "dm320_usb.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/****************************************************************************
 * Inline Functions
 ****************************************************************************/

#endif /* __ARCH_ARM_SRC_DM320_CHIP_H */
