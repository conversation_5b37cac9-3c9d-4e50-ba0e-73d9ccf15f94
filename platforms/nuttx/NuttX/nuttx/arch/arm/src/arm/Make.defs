############################################################################
# arch/arm/src/arm/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

# Common ARM files

include common/Make.defs

HEAD_ASRC  = arm_head.S

CMN_CSRCS += arm_dataabort.c arm_doirq.c arm_initialstate.c
CMN_CSRCS += arm_prefetchabort.c arm_schedulesigaction.c
CMN_CSRCS += arm_sigdeliver.c arm_syscall.c arm_tcbinfo.c
CMN_CSRCS += arm_undefinedinsn.c

CMN_ASRCS += arm_cache.S arm_vectoraddrexcptn.S
CMN_ASRCS += arm_vectors.S arm_vectortab.S

ifeq ($(CONFIG_PAGING),y)
  CMN_CSRCS += arm_pginitialize.c arm_checkmapping.c arm_allocpage.c arm_va2pte.c
endif
