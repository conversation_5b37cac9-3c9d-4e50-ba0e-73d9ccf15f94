/****************************************************************************
 * arch/arm/src/str71x/str71x_wdog.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_STR71X_STR71X_WDOG_H
#define __ARCH_ARM_SRC_STR71X_STR71X_WDOG_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

#include "str71x_map.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Registers ****************************************************************/

#define STR71X_WDOG_CR    (STR71X_WDOG_BASE + 0x0000) /* 16-bits wide */
#define STR71X_WDOG_PR    (STR71X_WDOG_BASE + 0x0004) /* 16-bits wide */
#define STR71X_WDOG_VR    (STR71X_WDOG_BASE + 0x0008) /* 16-bits wide */
#define STR71X_WDOG_CNT   (STR71X_WDOG_BASE + 0x000c) /* 16-bits wide */
#define STR71X_WDOG_SR    (STR71X_WDOG_BASE + 0x0010) /* 16-bits wide */
#define STR71X_WDOG_MR    (STR71X_WDOG_BASE + 0x0014) /* 16-bits wide */
#define STR71X_WDOG_KR    (STR71X_WDOG_BASE + 0x00018 /* 16-bits wide */

/* Register bit settings ****************************************************/

/****************************************************************************
 * Public Types
 ****************************************************************************/

/****************************************************************************
 * Public Data
 ****************************************************************************/

/****************************************************************************
 * Public Functions Prototypes
 ****************************************************************************/

#endif /* __ARCH_ARM_SRC_STR71X_STR71X_WDOG_H */
