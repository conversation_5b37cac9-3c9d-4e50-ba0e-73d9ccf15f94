##############################################################################
# arch/arm/src/str71x/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
##############################################################################

include arm/Make.defs

HEAD_ASRC = str71x_head.S

CHIP_CSRCS  = str71x_prccu.c str71x_lowputc.c str71x_decodeirq.c str71x_irq.c
CHIP_CSRCS += str71x_serial.c

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += str71x_timerisr.c
endif

ifeq ($(CONFIG_USBDEV),y)
CHIP_CSRCS+= str71x_usbdev.c
endif

ifeq ($(CONFIG_STR71X_XTI),y)
CHIP_CSRCS+= str71x_xti.c
endif
