/****************************************************************************
 * arch/arm/src/rtl8720c/amebaZ.c
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include <nuttx/timers/arch_timer.h>
#include "systick.h"
#include "hal_syson.h"
#include "hal_wdt.h"

/****************************************************************************
 * Public Functions
 ****************************************************************************/

#ifdef CONFIG_ARCH_CHIP_AMEBAZ
void up_timer_initialize(void)
{
  up_timer_set_lowerhalf(systick_initialize(true, 100000000, -1));
}

void ameba_reset(int status)
{
  hal_sys_set_fast_boot(0, 0);
  hal_misc_rst_by_wdt();
}

#endif /* CONFIG_ARCH_CHIP_AMEBAZ */
