##############################################################################
# arch/arm/src/lpc2378/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
##############################################################################

include arm/Make.defs

HEAD_ASRC   = lpc23xx_head.S

CHIP_ASRCS  = lpc23xx_lowputc.S
CHIP_CSRCS += lpc23xx_pllsetup.c  lpc23xx_decodeirq.c lpc23xx_irq.c
CHIP_CSRCS += lpc23xx_serial.c lpc23xx_io.c

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += lpc23xx_timerisr.c
endif

ifeq ($(CONFIG_LPC2378_SPI),y)
CHIP_CSRCS += lpc23xx_spi.c
endif

ifeq ($(CONFIG_I2C),y)
CHIP_CSRCS += lpc23xx_i2c.c
endif

ifeq ($(CONFIG_USBDEV),y)
#CHIP_CSRCS += lpc23xx_usbdev.c
endif
