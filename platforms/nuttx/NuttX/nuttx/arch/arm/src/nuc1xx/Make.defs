############################################################################
# arch/arm/src/nuc1xx/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

include armv6-m/Make.defs

CHIP_CSRCS  = nuc_clockconfig.c nuc_gpio.c nuc_irq.c nuc_lowputc.c
CHIP_CSRCS += nuc_serial.c nuc_start.c

# Configuration-dependent NUC1xxfiles

ifneq ($(CONFIG_ARCH_IDLE_CUSTOM),y)
CHIP_CSRCS += nuc_idle.c
endif

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += nuc_timerisr.c
endif

ifeq ($(CONFIG_BUILD_PROTECTED),y)
CHIP_CSRCS += nuc_userspace.c
endif

ifeq ($(CONFIG_DEBUG_FEATURES),y)
CHIP_CSRCS += nuc_dumpgpio.c
endif
