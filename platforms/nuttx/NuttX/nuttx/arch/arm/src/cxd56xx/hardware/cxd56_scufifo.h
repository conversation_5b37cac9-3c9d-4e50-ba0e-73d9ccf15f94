/****************************************************************************
 * arch/arm/src/cxd56xx/hardware/cxd56_scufifo.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_CXD56XX_HARDWARE_CXD56_SCUFIFO_H
#define __ARCH_ARM_SRC_CXD56XX_HARDWARE_CXD56_SCUFIFO_H

#define SCUFIFO_R_CTRL0(fn)      (((fn) * 0x20) + SCUFIFO_D0_R1_C_CTRL0)
#define SCUFIFO_R_CTRL1(fn)      (((fn) * 0x20) + SCUFIFO_D0_R1_C_CTRL1)
#define SCUFIFO_R_STATUS0(fn)    (((fn) * 0x20) + SCUFIFO_D0_R1_C_STATUS0)
#define SCUFIFO_R_STATUS1(fn)    (((fn) * 0x20) + SCUFIFO_D0_R1_C_STATUS1)
#define SCUFIFO_R_TIMESTAMP0(fn) (((fn) * 0x20) + SCUFIFO_D0_R1_C_TIMSTAMP0)
#define SCUFIFO_R_TIMESTAMP1(fn) (((fn) * 0x20) + SCUFIFO_D0_R1_C_TIMSTAMP1)

#define SCUFIFO_W_CTRL0(fn)      (((fn) * 0x20) + SCUFIFO_D0_W0_S_CTRL0)
#define SCUFIFO_W_CTRL1(fn)      (((fn) * 0x20) + SCUFIFO_D0_W0_S_CTRL1)
#define SCUFIFO_W_STATUS0(fn)    (((fn) * 0x20) + SCUFIFO_D0_W0_S_STATUS0)
#define SCUFIFO_W_STATUS1(fn)    (((fn) * 0x20) + SCUFIFO_D0_W0_S_STATUS1)
#define SCUFIFO_W_TIMESTAMP0(fn) (((fn) * 0x20) + SCUFIFO_D0_W0_S_TIMSTAMP0)
#define SCUFIFO_W_TIMESTAMP1(fn) (((fn) * 0x20) + SCUFIFO_D0_W0_S_TIMSTAMP1)

/* SCUFIFO_[RW]_CTRL1 *******************************************************/

#define SCUFIFO_ENABLE        (1 << 24) /* [R] Enable FIFO */
#define SCUFIFO_DMAENABLE     (1 << 25) /* [R] Enable FIFO DMA control signal */
#define SCUFIFO_PHASERESET    (1 << 8)  /* [R] Reset sample phase */
#define SCUFIFO_RESET         (1 << 16) /* [RW] Reset FIFO */
#define SCUFIFO_UNDERRUNCLR   (1 << 1)  /* [R] Clear FIFO under run error */
#define SCUFIFO_OVERRUNCLR    (1 << 0)  /* [R] Clear FIFO over run error */
#define SCUFIFO_OVERWRITE     (1 << 4)  /* [W] Over write when FIFO is full. */
#define SCUFIFO_ENADCINTERVAL (1 << 12) /* [W] Enable ADC interval instead of
                                         * PREDIV.
                                         */

#define SCUFIFO_ADCINTERVAL(x) (((x) & 0xf) << 8) /* [W] ADC interval */
#define SCUFIFO_BPS(x)         ((x) & 0xf)        /* [W] Bytes per sample */

#define SCUFIFO_D0_W0_S_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x0)
#define SCUFIFO_D0_W0_S_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x4)
#define SCUFIFO_D0_W0_S_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x8)
#define SCUFIFO_D0_W0_S_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0xc)
#define SCUFIFO_D0_W0_S_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x10)
#define SCUFIFO_D0_W1_S_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x20)
#define SCUFIFO_D0_W1_S_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x24)
#define SCUFIFO_D0_W1_S_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x28)
#define SCUFIFO_D0_W1_S_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x2c)
#define SCUFIFO_D0_W1_S_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x30)
#define SCUFIFO_D0_W2_S_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x40)
#define SCUFIFO_D0_W2_S_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x44)
#define SCUFIFO_D0_W2_S_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x48)
#define SCUFIFO_D0_W2_S_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x4c)
#define SCUFIFO_D0_W2_S_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x50)
#define SCUFIFO_D0_W3_S_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x60)
#define SCUFIFO_D0_W3_S_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x64)
#define SCUFIFO_D0_W3_S_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x68)
#define SCUFIFO_D0_W3_S_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x6c)
#define SCUFIFO_D0_W3_S_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x70)
#define SCUFIFO_D1_W0_S_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x80)
#define SCUFIFO_D1_W0_S_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x84)
#define SCUFIFO_D1_W0_S_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x88)
#define SCUFIFO_D1_W0_S_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x8c)
#define SCUFIFO_D1_W0_S_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x90)
#define SCUFIFO_D1_W1_S_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0xa0)
#define SCUFIFO_D1_W1_S_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0xa4)
#define SCUFIFO_D1_W1_S_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0xa8)
#define SCUFIFO_D1_W1_S_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0xac)
#define SCUFIFO_D1_W1_S_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0xb0)
#define SCUFIFO_D1_W2_S_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0xc0)
#define SCUFIFO_D1_W2_S_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0xc4)
#define SCUFIFO_D1_W2_S_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0xc8)
#define SCUFIFO_D1_W2_S_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0xcc)
#define SCUFIFO_D1_W2_S_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0xd0)
#define SCUFIFO_D1_W3_S_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0xe0)
#define SCUFIFO_D1_W3_S_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0xe4)
#define SCUFIFO_D1_W3_S_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0xe8)
#define SCUFIFO_D1_W3_S_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0xec)
#define SCUFIFO_D1_W3_S_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0xf0)
#define SCUFIFO_N0_W_S_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x100)
#define SCUFIFO_N0_W_S_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x104)
#define SCUFIFO_N0_W_S_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x108)
#define SCUFIFO_N0_W_S_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x10c)
#define SCUFIFO_N0_W_S_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x110)
#define SCUFIFO_N1_W_S_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x120)
#define SCUFIFO_N1_W_S_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x124)
#define SCUFIFO_N1_W_S_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x128)
#define SCUFIFO_N1_W_S_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x12c)
#define SCUFIFO_N1_W_S_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x130)
#define SCUFIFO_N2_W_S_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x140)
#define SCUFIFO_N2_W_S_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x144)
#define SCUFIFO_N2_W_S_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x148)
#define SCUFIFO_N2_W_S_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x14c)
#define SCUFIFO_N2_W_S_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x150)
#define SCUFIFO_N3_W_S_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x160)
#define SCUFIFO_N3_W_S_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x164)
#define SCUFIFO_N3_W_S_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x168)
#define SCUFIFO_N3_W_S_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x16c)
#define SCUFIFO_N3_W_S_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x170)
#define SCUFIFO_N4_W_S_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x180)
#define SCUFIFO_N4_W_S_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x184)
#define SCUFIFO_N4_W_S_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x188)
#define SCUFIFO_N4_W_S_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x18c)
#define SCUFIFO_N4_W_S_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x190)
#define SCUFIFO_N5_W_S_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1a0)
#define SCUFIFO_N5_W_S_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1a4)
#define SCUFIFO_N5_W_S_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x1a8)
#define SCUFIFO_N5_W_S_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1ac)
#define SCUFIFO_N5_W_S_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1b0)
#define SCUFIFO_N6_W_S_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1c0)
#define SCUFIFO_N6_W_S_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1c4)
#define SCUFIFO_N6_W_S_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x1c8)
#define SCUFIFO_N6_W_S_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1cc)
#define SCUFIFO_N6_W_S_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1d0)
#define SCUFIFO_N7_W_S_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1e0)
#define SCUFIFO_N7_W_S_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1e4)
#define SCUFIFO_N7_W_S_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x1e8)
#define SCUFIFO_N7_W_S_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1ec)
#define SCUFIFO_N7_W_S_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1f0)
#define SCUFIFO_V0_W_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x200)
#define SCUFIFO_V0_W_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x204)
#define SCUFIFO_V0_W_C_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x208)
#define SCUFIFO_V0_W_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x20c)
#define SCUFIFO_V0_W_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x210)
#define SCUFIFO_V0_W_C_TIMSTAMP_SET0	(CXD56_SCU_FIFO_REG_BASE + 0x214)
#define SCUFIFO_V0_W_C_TIMSTAMP_SET1	(CXD56_SCU_FIFO_REG_BASE + 0x218)
#define SCUFIFO_V1_W_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x220)
#define SCUFIFO_V1_W_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x224)
#define SCUFIFO_V1_W_C_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x228)
#define SCUFIFO_V1_W_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x22c)
#define SCUFIFO_V1_W_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x230)
#define SCUFIFO_V1_W_C_TIMSTAMP_SET0	(CXD56_SCU_FIFO_REG_BASE + 0x234)
#define SCUFIFO_V1_W_C_TIMSTAMP_SET1	(CXD56_SCU_FIFO_REG_BASE + 0x238)
#define SCUFIFO_V2_W_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x240)
#define SCUFIFO_V2_W_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x244)
#define SCUFIFO_V2_W_C_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x248)
#define SCUFIFO_V2_W_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x24c)
#define SCUFIFO_V2_W_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x250)
#define SCUFIFO_V2_W_C_TIMSTAMP_SET0	(CXD56_SCU_FIFO_REG_BASE + 0x254)
#define SCUFIFO_V2_W_C_TIMSTAMP_SET1	(CXD56_SCU_FIFO_REG_BASE + 0x258)
#define SCUFIFO_V3_W_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x260)
#define SCUFIFO_V3_W_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x264)
#define SCUFIFO_V3_W_C_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x268)
#define SCUFIFO_V3_W_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x26c)
#define SCUFIFO_V3_W_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x270)
#define SCUFIFO_V3_W_C_TIMSTAMP_SET0	(CXD56_SCU_FIFO_REG_BASE + 0x274)
#define SCUFIFO_V3_W_C_TIMSTAMP_SET1	(CXD56_SCU_FIFO_REG_BASE + 0x278)
#define SCUFIFO_V4_W_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x280)
#define SCUFIFO_V4_W_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x284)
#define SCUFIFO_V4_W_C_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x288)
#define SCUFIFO_V4_W_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x28c)
#define SCUFIFO_V4_W_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x290)
#define SCUFIFO_V4_W_C_TIMSTAMP_SET0	(CXD56_SCU_FIFO_REG_BASE + 0x294)
#define SCUFIFO_V4_W_C_TIMSTAMP_SET1	(CXD56_SCU_FIFO_REG_BASE + 0x298)
#define SCUFIFO_V5_W_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x2a0)
#define SCUFIFO_V5_W_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x2a4)
#define SCUFIFO_V5_W_C_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x2a8)
#define SCUFIFO_V5_W_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x2ac)
#define SCUFIFO_V5_W_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x2b0)
#define SCUFIFO_V5_W_C_TIMSTAMP_SET0	(CXD56_SCU_FIFO_REG_BASE + 0x2b4)
#define SCUFIFO_V5_W_C_TIMSTAMP_SET1	(CXD56_SCU_FIFO_REG_BASE + 0x2b8)
#define SCUFIFO_V6_W_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x2c0)
#define SCUFIFO_V6_W_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x2c4)
#define SCUFIFO_V6_W_C_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x2c8)
#define SCUFIFO_V6_W_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x2cc)
#define SCUFIFO_V6_W_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x2d0)
#define SCUFIFO_V6_W_C_TIMSTAMP_SET0	(CXD56_SCU_FIFO_REG_BASE + 0x2d4)
#define SCUFIFO_V6_W_C_TIMSTAMP_SET1	(CXD56_SCU_FIFO_REG_BASE + 0x2d8)
#define SCUFIFO_V7_W_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x2e0)
#define SCUFIFO_V7_W_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x2e4)
#define SCUFIFO_V7_W_C_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x2e8)
#define SCUFIFO_V7_W_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x2ec)
#define SCUFIFO_V7_W_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x2f0)
#define SCUFIFO_V7_W_C_TIMSTAMP_SET0	(CXD56_SCU_FIFO_REG_BASE + 0x2f4)
#define SCUFIFO_V7_W_C_TIMSTAMP_SET1	(CXD56_SCU_FIFO_REG_BASE + 0x2f8)
#define SCUFIFO_V8_W_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x300)
#define SCUFIFO_V8_W_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x304)
#define SCUFIFO_V8_W_C_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x308)
#define SCUFIFO_V8_W_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x30c)
#define SCUFIFO_V8_W_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x310)
#define SCUFIFO_V8_W_C_TIMSTAMP_SET0	(CXD56_SCU_FIFO_REG_BASE + 0x314)
#define SCUFIFO_V8_W_C_TIMSTAMP_SET1	(CXD56_SCU_FIFO_REG_BASE + 0x318)
#define SCUFIFO_V9_W_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x320)
#define SCUFIFO_V9_W_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x324)
#define SCUFIFO_V9_W_C_STATUS		(CXD56_SCU_FIFO_REG_BASE + 0x328)
#define SCUFIFO_V9_W_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x32c)
#define SCUFIFO_V9_W_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x330)
#define SCUFIFO_V9_W_C_TIMSTAMP_SET0	(CXD56_SCU_FIFO_REG_BASE + 0x334)
#define SCUFIFO_V9_W_C_TIMSTAMP_SET1	(CXD56_SCU_FIFO_REG_BASE + 0x338)
#define SCUFIFO_D0_R1_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1000)
#define SCUFIFO_D0_R1_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1004)
#define SCUFIFO_D0_R1_C_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1008)
#define SCUFIFO_D0_R1_C_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x100c)
#define SCUFIFO_D0_R1_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1010)
#define SCUFIFO_D0_R1_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1014)
#define SCUFIFO_D0_R2_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1020)
#define SCUFIFO_D0_R2_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1024)
#define SCUFIFO_D0_R2_C_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1028)
#define SCUFIFO_D0_R2_C_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x102c)
#define SCUFIFO_D0_R2_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1030)
#define SCUFIFO_D0_R2_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1034)
#define SCUFIFO_D1_R1_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1040)
#define SCUFIFO_D1_R1_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1044)
#define SCUFIFO_D1_R1_C_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1048)
#define SCUFIFO_D1_R1_C_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x104c)
#define SCUFIFO_D1_R1_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1050)
#define SCUFIFO_D1_R1_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1054)
#define SCUFIFO_D1_R2_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1060)
#define SCUFIFO_D1_R2_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1064)
#define SCUFIFO_D1_R2_C_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1068)
#define SCUFIFO_D1_R2_C_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x106c)
#define SCUFIFO_D1_R2_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1070)
#define SCUFIFO_D1_R2_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1074)
#define SCUFIFO_N0_R1_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1080)
#define SCUFIFO_N0_R1_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1084)
#define SCUFIFO_N0_R1_C_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1088)
#define SCUFIFO_N0_R1_C_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x108c)
#define SCUFIFO_N0_R1_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1090)
#define SCUFIFO_N0_R1_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1094)
#define SCUFIFO_N1_R1_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x10a0)
#define SCUFIFO_N1_R1_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x10a4)
#define SCUFIFO_N1_R1_C_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x10a8)
#define SCUFIFO_N1_R1_C_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x10ac)
#define SCUFIFO_N1_R1_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x10b0)
#define SCUFIFO_N1_R1_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x10b4)
#define SCUFIFO_N2_R1_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x10c0)
#define SCUFIFO_N2_R1_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x10c4)
#define SCUFIFO_N2_R1_C_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x10c8)
#define SCUFIFO_N2_R1_C_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x10cc)
#define SCUFIFO_N2_R1_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x10d0)
#define SCUFIFO_N2_R1_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x10d4)
#define SCUFIFO_N3_R1_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x10e0)
#define SCUFIFO_N3_R1_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x10e4)
#define SCUFIFO_N3_R1_C_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x10e8)
#define SCUFIFO_N3_R1_C_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x10ec)
#define SCUFIFO_N3_R1_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x10f0)
#define SCUFIFO_N3_R1_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x10f4)
#define SCUFIFO_N4_R1_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1100)
#define SCUFIFO_N4_R1_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1104)
#define SCUFIFO_N4_R1_C_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1108)
#define SCUFIFO_N4_R1_C_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x110c)
#define SCUFIFO_N4_R1_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1110)
#define SCUFIFO_N4_R1_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1114)
#define SCUFIFO_N5_R1_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1120)
#define SCUFIFO_N5_R1_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1124)
#define SCUFIFO_N5_R1_C_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1128)
#define SCUFIFO_N5_R1_C_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x112c)
#define SCUFIFO_N5_R1_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1130)
#define SCUFIFO_N5_R1_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1134)
#define SCUFIFO_N6_R1_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1140)
#define SCUFIFO_N6_R1_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1144)
#define SCUFIFO_N6_R1_C_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1148)
#define SCUFIFO_N6_R1_C_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x114c)
#define SCUFIFO_N6_R1_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1150)
#define SCUFIFO_N6_R1_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1154)
#define SCUFIFO_N7_R1_C_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1160)
#define SCUFIFO_N7_R1_C_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1164)
#define SCUFIFO_N7_R1_C_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1168)
#define SCUFIFO_N7_R1_C_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x116c)
#define SCUFIFO_N7_R1_C_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1170)
#define SCUFIFO_N7_R1_C_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1174)
#define SCUFIFO_D0_R3_CH_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1180)
#define SCUFIFO_D0_R3_CH_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1184)
#define SCUFIFO_D0_R3_CH_STATUS0	(CXD56_SCU_FIFO_REG_BASE + 0x1188)
#define SCUFIFO_D0_R3_CH_STATUS1	(CXD56_SCU_FIFO_REG_BASE + 0x118c)
#define SCUFIFO_D0_R3_CH_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1190)
#define SCUFIFO_D0_R3_CH_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1194)
#define SCUFIFO_D1_R3_CH_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x11a0)
#define SCUFIFO_D1_R3_CH_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x11a4)
#define SCUFIFO_D1_R3_CH_STATUS0	(CXD56_SCU_FIFO_REG_BASE + 0x11a8)
#define SCUFIFO_D1_R3_CH_STATUS1	(CXD56_SCU_FIFO_REG_BASE + 0x11ac)
#define SCUFIFO_D1_R3_CH_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x11b0)
#define SCUFIFO_D1_R3_CH_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x11b4)
#define SCUFIFO_D0_R0_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x11c0)
#define SCUFIFO_D0_R0_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x11c4)
#define SCUFIFO_D0_R0_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x11c8)
#define SCUFIFO_D0_R0_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x11cc)
#define SCUFIFO_D0_R0_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x11d0)
#define SCUFIFO_D0_R0_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x11d4)
#define SCUFIFO_D1_R0_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x11e0)
#define SCUFIFO_D1_R0_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x11e4)
#define SCUFIFO_D1_R0_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x11e8)
#define SCUFIFO_D1_R0_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x11ec)
#define SCUFIFO_D1_R0_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x11f0)
#define SCUFIFO_D1_R0_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x11f4)
#define SCUFIFO_N0_R0_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1200)
#define SCUFIFO_N0_R0_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1204)
#define SCUFIFO_N0_R0_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1208)
#define SCUFIFO_N0_R0_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x120c)
#define SCUFIFO_N0_R0_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1210)
#define SCUFIFO_N0_R0_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1214)
#define SCUFIFO_N1_R0_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1220)
#define SCUFIFO_N1_R0_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1224)
#define SCUFIFO_N1_R0_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1228)
#define SCUFIFO_N1_R0_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x122c)
#define SCUFIFO_N1_R0_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1230)
#define SCUFIFO_N1_R0_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1234)
#define SCUFIFO_N2_R0_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1240)
#define SCUFIFO_N2_R0_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1244)
#define SCUFIFO_N2_R0_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1248)
#define SCUFIFO_N2_R0_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x124c)
#define SCUFIFO_N2_R0_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1250)
#define SCUFIFO_N2_R0_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1254)
#define SCUFIFO_N3_R0_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1260)
#define SCUFIFO_N3_R0_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1264)
#define SCUFIFO_N3_R0_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1268)
#define SCUFIFO_N3_R0_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x126c)
#define SCUFIFO_N3_R0_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1270)
#define SCUFIFO_N3_R0_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1274)
#define SCUFIFO_N4_R0_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1280)
#define SCUFIFO_N4_R0_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1284)
#define SCUFIFO_N4_R0_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1288)
#define SCUFIFO_N4_R0_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x128c)
#define SCUFIFO_N4_R0_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1290)
#define SCUFIFO_N4_R0_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1294)
#define SCUFIFO_N5_R0_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x12a0)
#define SCUFIFO_N5_R0_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x12a4)
#define SCUFIFO_N5_R0_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x12a8)
#define SCUFIFO_N5_R0_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x12ac)
#define SCUFIFO_N5_R0_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x12b0)
#define SCUFIFO_N5_R0_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x12b4)
#define SCUFIFO_N6_R0_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x12c0)
#define SCUFIFO_N6_R0_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x12c4)
#define SCUFIFO_N6_R0_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x12c8)
#define SCUFIFO_N6_R0_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x12cc)
#define SCUFIFO_N6_R0_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x12d0)
#define SCUFIFO_N6_R0_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x12d4)
#define SCUFIFO_N7_R0_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x12e0)
#define SCUFIFO_N7_R0_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x12e4)
#define SCUFIFO_N7_R0_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x12e8)
#define SCUFIFO_N7_R0_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x12ec)
#define SCUFIFO_N7_R0_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x12f0)
#define SCUFIFO_N7_R0_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x12f4)
#define SCUFIFO_V0_R_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1300)
#define SCUFIFO_V0_R_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1304)
#define SCUFIFO_V0_R_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1308)
#define SCUFIFO_V0_R_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x130c)
#define SCUFIFO_V0_R_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1310)
#define SCUFIFO_V0_R_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1314)
#define SCUFIFO_V1_R_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1320)
#define SCUFIFO_V1_R_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1324)
#define SCUFIFO_V1_R_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1328)
#define SCUFIFO_V1_R_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x132c)
#define SCUFIFO_V1_R_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1330)
#define SCUFIFO_V1_R_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1334)
#define SCUFIFO_V2_R_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1340)
#define SCUFIFO_V2_R_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1344)
#define SCUFIFO_V2_R_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1348)
#define SCUFIFO_V2_R_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x134c)
#define SCUFIFO_V2_R_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1350)
#define SCUFIFO_V2_R_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1354)
#define SCUFIFO_V3_R_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1360)
#define SCUFIFO_V3_R_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1364)
#define SCUFIFO_V3_R_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1368)
#define SCUFIFO_V3_R_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x136c)
#define SCUFIFO_V3_R_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1370)
#define SCUFIFO_V3_R_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1374)
#define SCUFIFO_V4_R_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1380)
#define SCUFIFO_V4_R_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1384)
#define SCUFIFO_V4_R_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1388)
#define SCUFIFO_V4_R_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x138c)
#define SCUFIFO_V4_R_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1390)
#define SCUFIFO_V4_R_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1394)
#define SCUFIFO_V5_R_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x13a0)
#define SCUFIFO_V5_R_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x13a4)
#define SCUFIFO_V5_R_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x13a8)
#define SCUFIFO_V5_R_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x13ac)
#define SCUFIFO_V5_R_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x13b0)
#define SCUFIFO_V5_R_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x13b4)
#define SCUFIFO_V6_R_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x13c0)
#define SCUFIFO_V6_R_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x13c4)
#define SCUFIFO_V6_R_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x13c8)
#define SCUFIFO_V6_R_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x13cc)
#define SCUFIFO_V6_R_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x13d0)
#define SCUFIFO_V6_R_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x13d4)
#define SCUFIFO_V7_R_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x13e0)
#define SCUFIFO_V7_R_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x13e4)
#define SCUFIFO_V7_R_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x13e8)
#define SCUFIFO_V7_R_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x13ec)
#define SCUFIFO_V7_R_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x13f0)
#define SCUFIFO_V7_R_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x13f4)
#define SCUFIFO_V8_R_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1400)
#define SCUFIFO_V8_R_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1404)
#define SCUFIFO_V8_R_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1408)
#define SCUFIFO_V8_R_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x140c)
#define SCUFIFO_V8_R_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1410)
#define SCUFIFO_V8_R_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1414)
#define SCUFIFO_V9_R_H_CTRL0		(CXD56_SCU_FIFO_REG_BASE + 0x1420)
#define SCUFIFO_V9_R_H_CTRL1		(CXD56_SCU_FIFO_REG_BASE + 0x1424)
#define SCUFIFO_V9_R_H_STATUS0		(CXD56_SCU_FIFO_REG_BASE + 0x1428)
#define SCUFIFO_V9_R_H_STATUS1		(CXD56_SCU_FIFO_REG_BASE + 0x142c)
#define SCUFIFO_V9_R_H_TIMSTAMP0	(CXD56_SCU_FIFO_REG_BASE + 0x1430)
#define SCUFIFO_V9_R_H_TIMSTAMP1	(CXD56_SCU_FIFO_REG_BASE + 0x1434)
#define SCUFIFO_DECI_PARTITION_SEL	(CXD56_SCU_FIFO_REG_BASE + 0x1c00)

#define SCUFIFO_FIFO_DATA(x)      (CXD56_SCU_FIFO_REG_BASE + 0x2100 + (0x4 * (x)))

#endif /* __ARCH_ARM_SRC_CXD56XX_HARDWARE_CXD56_SCUFIFO_H */
