#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

comment "CXD56xx Options"

config CXD56_ARCH_OPTS
	bool
	default y
	select ARCH_D<PERSON>
	select SDIO_DMA if MMCSD

menu "CXD56xx Package Configuration"

choice
	prompt "CXD56xx package selection"
	default CXD56_FCBGA if !CXD56_100PIN
	default CXD56_WLCSP if CXD56_100PIN

config CXD56_FCBGA
	bool "FCBGA 185 pin package"

config CXD56_WLCSP
	bool "WLCSP 100 pin package"

endchoice

choice
	prompt "CXD56xx core selection"
	default CXD56_MAINCORE

config CXD56_MAINCORE
	bool "Main Core"

config CXD56_SUBCORE
	bool "Sub Core"

endchoice

config CXD56_CUSTOM_PINCONFIG
	bool "Board-specific pin configuration"
	default n
	---help---
		Custom PIN Configuration of CXD56xx pins to fit a specific board.
		If this option is enabled, some PINs configurations are overwritten
		in "boards/arm/cxd56xx/<board>/include/board_pinconfig.h"
		from "arch/arm/src/cxd56xx/hardware/cxd56xx_pinconfig.h".

comment "Debug Features"

config CXD56_BACKUPLOG
	bool "Backup Log driver"
	default y

config CXD56_RESET_ON_CRASH
	bool "Enable Board Reset on crash"
	default n
	---help---
		If selected the board_crashdump should reset the machine after
		saving the state of the machine

comment "Basic Options"

config CXD56_XOSC_CLOCK
	int
	default 26000000

config CXD56_SPH
	bool
	default y if ASMP

config CXD56_PMIC
	bool
	default y if CXD56_MAINCORE
	default n if CXD56_SUBCORE

if CXD56_PMIC

config CXD56_PMIC_INT
	bool "PMIC interrupt"
	default n
	depends on SCHED_WORKQUEUE
	---help---
		Enable PMIC interrupt.

endif

config CXD56_CPUFIFO
	bool
	default y

config CXD56_ICC
	bool
	default y

config CXD56_CPUFIFO_ENTRIES
	int "CPU Tx FIFO elements"
	default 8
	depends on CXD56_CPUFIFO

config CXD56_CPUFIFO_NBUFFERS
	int "CPU Rx FIFO elements"
	default 8
	depends on CXD56_ICC

config CXD56_FARAPI
	bool
	default y if CXD56_MAINCORE
	default n if CXD56_SUBCORE

menu "Far API Configuration"
	depends on CXD56_FARAPI

config CXD56_FARAPI_VERSION_CHECK
	bool "Far API Version Compatibility Check"
	default y
	---help---
		Enable the Far API version compatibility check. If the version
		mismatch is detected during system bootup, the target system shows
		the message to update the loader and gnssfw firmwares.

if CXD56_FARAPI_VERSION_CHECK

config CXD56_FARAPI_VERSION_FAILED_PANIC
	bool "Far API Version Check Failed to PANIC"
	default n
	---help---
		If the version mismatch is detected, do PANIC() to stop the system.
endif

config CXD56_FARAPI_DEBUG
	bool "Debug Far API"

endmenu # Far API Configuration

comment "Timer Options"

menu "Timer Options"

menuconfig CXD56_RTC
	bool "Real Time Clock (RTC)"
	default y
	---help---
		Support RTC

if CXD56_RTC

config CXD56_RTC_LATEINIT
	bool "Late RTC initialization"
	default y
	---help---
		Enable the late RTC initialization after waiting until the external
		CXD5247 RTC clock is stable. It will take 2 seconds typically at the
		initial boot by power on reset.

endif # CXD56_RTC

menuconfig CXD56_TIMER
	bool "Timer"
	default y
	select TIMER

if CXD56_TIMER

choice
	prompt "Timer divider selection"
	default CXD56_TIMER_DIVIDER_1
	---help---
		Timer divider selects 1, 16 or 256. The smaller the divider, the higher
		time resolution, but the shorter the maximum time until wrap-around.

config CXD56_TIMER_DIVIDER_1
	bool "Divider 1"

config CXD56_TIMER_DIVIDER_16
	bool "Divider 16"

config CXD56_TIMER_DIVIDER_256
	bool "Divider 256"

endchoice
endif # CXD56_TIMER

menuconfig CXD56_WDT
	bool "Watchdog Timer (WDT)"
	default y
	select WATCHDOG

if CXD56_WDT

config CXD56_WDT_INTERRUPT
	bool "Interrupt on timeout"
	default y
	---help---
		This watchdog timer run 2 laps as long as the counter is not reloaded.
		In the first lap, the watchdog interrupt occurs. Without interrupt
		clear, The reset signal is asserted in the second timeout. This setting
		enables the first interrupt.

config CXD56_WDT_REGDEBUG
	bool "Register level debug"
	default n
	depends on DEBUG_WATCHDOG_INFO
	---help---
		Enable low-level register debug output

endif # CXD56_WDT

endmenu

comment "Power Management Options"

menuconfig CXD56_PM
	bool "Power Management"
	default y

if CXD56_PM

config CPUFREQ_RELEASE_LOCK
	bool "Dynamic clock control"
	default n
	---help---
		CPU frequency lock allows the system clock to be changed dynamically.
		Acquiring the higher frequency lock will improve the performance, but
		will increase the power consumption. If this is enabled, the system
		clock is the lower frequency unless the frequency lock is acquired.
		If disabled, the system clock will not change and will remain at the
		maximum system clock.

config CXD56_PM_PROCFS
	bool "Power Management PROCFS support"
	default n
	---help---
		Enable the power domain status or the clock frequency monitor.

config CXD56_PM_DEBUG
	bool "Power Management Debug Features"
	default n
	---help---
		Enable power management debug features.

if CXD56_PM_DEBUG

config CXD56_PM_DEBUG_ERROR
	bool "Power Management Error Output"
	default n
	depends on DEBUG_ERROR
	---help---
		Enable power management error output to SYSLOG.

config CXD56_PM_DEBUG_WARN
	bool "Power Management Warnings Output"
	default n
	depends on DEBUG_WARN
	---help---
		Enable power management warning output to SYSLOG.

config CXD56_PM_DEBUG_INFO
	bool "Power Management Informational Output"
	default n
	depends on DEBUG_INFO
	---help---
		Enable power management informational output to SYSLOG.

endif # CXD56_PM_DEBUG

endif # CXD56_PM

config CXD56_GAUGE
	bool "CXD5247 battery gauge"
	default n
	select BATTERY_GAUGE
	---help---
		Enable battery gauge driver.
		This is not a lower half of power driver, but compatible with NuttX gauge driver.

config CXD56_CHARGER
	bool "CXD5247 battery charger"
	default n
	select BATTERY_CHARGER
	---help---
		Enable battery charger driver.
		This is not a lower half of power driver, but compatible with NuttX charger driver.

if CXD56_CHARGER

config CXD56_CHARGER_TEMP_PRECISE
	bool "Precise Temperature"
	default y
	---help---
		Get temperature precisely. If this option is enabled, driver calculate precisely
		from register value to degrees Celsius.
		This option requires libm.
endif

comment "Peripheral Support"

menu "Peripheral Support"

config CXD56_DMAC
	bool "DMAC"
	default y
	---help---
		Enables DMAC
		Currently supports SPI4 TX/RX and SPI5 TX/RX

config CXD56_GPIO_IRQ
	bool "GPIO interrupt"
	default y
	---help---
		Enable support for GPIO interrupts

config CXD56_UART0
	bool "UART0"
	default n
	---help---
		UART interface with hardware flow control in the application subsystem.

if CXD56_UART0

config CXD56_UART0_BAUD
	int "CXD56 UART0 BAUD"
	default 921600

config CXD56_UART0_PARITY
	int "CXd56 UART0 parity"
	default 0
	range 0 2
	---help---
		CXD56 UART0 parity.  0=None, 1=Odd, 2=Even.  Default: None

config CXD56_UART0_BITS
	int "CXD56 UART0 number of bits"
	default 8
	range 5 8
	---help---
		CXD56 UART0 number of bits.  Default: 8

config CXD56_UART0_2STOP
	int "CXD56 UART0 two stop bits"
	default 0
	---help---
		0=1 stop bit, 1=Two stop bits.  Default: 1 stop bit

config CXD56_UART0_FLOWCONTROL
	bool "CXD56 UART0 flow control"
	default n
	---help---
		Enable CXD56 UART0 RTS flow control

endif

config CXD56_UART1
	bool "UART1"
	default y
	select UART1_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS
	---help---
		UART interface in the communication subsystem. This doesn't have any
		hardware flow control, and is mainly used for debug console.

config CXD56_UART2
	bool "UART2"
	default n
	select UART2_SERIALDRIVER
	---help---
		UART interface with hardware flow control in the application subsystem.

config CXD56_SPI
	bool "SPI"

if CXD56_SPI

config CXD56_SPI_DRIVER
	bool "SPI character driver"
	default y
	select SPI_DRIVER
	---help---
		Build in support for a character driver at /dev/spi[N] that may be
		used to perform SPI bus transfers from applications.  The intent of
		this driver is to support SPI testing.

config CXD56_SPI_DMATHRESHOLD
	int "SPI DMA threshold"
	default 64
	depends on CXD56_DMAC
	---help---
		When SPI DMA is enabled, small DMA transfers will still be performed
		by polling logic.  But we need a threshold value to determine what
		is small.

config CXD56_SPI0
	bool "SPI0"

menuconfig CXD56_SPI3
	bool "SPI3"

if CXD56_SPI3

config CXD56_SPI3_SCUSEQ
	bool "SCU Sequencer"
	default y
	depends on CXD56_SCU
	---help---
		Use the sensor control unit (SCU) sequencer.

config CXD56_SPI3_CS0
	bool "SPI3 Chip Select 0"
	default y
	---help---
		Enable chip select 0 of SPI3

config CXD56_SPI3_CS1
	bool "SPI3 Chip Select 1"
	default n
	---help---
		Enable chip select 1 of SPI3

config CXD56_SPI3_CS2
	bool "SPI3 Chip Select 2"
	default n
	---help---
		Enable chip select 2 of SPI3

endif # CXD56_SPI3

menuconfig CXD56_SPI4
	bool "SPI4"

if CXD56_SPI4

config CXD56_DMAC_SPI4_TX
	bool "DMAC support for SPI4 TX"
	default n
	select CXD56_DMAC
	---help---
		Enables DMAC for SPI4 TX

if CXD56_DMAC_SPI4_TX

config CXD56_DMAC_SPI4_TX_CH
	int "TX channel"
	default 2
	range 2 6

config CXD56_DMAC_SPI4_TX_MAXSIZE
	int "Max size to be sent in bytes"
	default 192000
	range 1 1572864
	---help---
		This value should be same as RX.

endif # CXD56_DMAC_SPI4_TX

config CXD56_DMAC_SPI4_RX
	bool "DMAC support for SPI4 RX"
	default n
	select CXD56_DMAC

	---help---
		Enables DMAC for SPI4 RX

if CXD56_DMAC_SPI4_RX

config CXD56_DMAC_SPI4_RX_CH
	int "RX channel"
	default 3
	range 2 6

config CXD56_DMAC_SPI4_RX_MAXSIZE
	int "Max size to be received in bytes"
	default 192000
	range 1 1572864
	---help---
		This value should be same as TX.

endif # CXD56_DMAC_SPI4_RX

endif # CXD56_SPI4

menuconfig CXD56_SPI5
	bool "SPI5"

if CXD56_SPI5

choice
	prompt "SPI5 pin configuration"
	default CXD56_SPI5_PINMAP_EMMC

config CXD56_SPI5_PINMAP_EMMC
	bool "SPI5 pin assign to eMMC"
	---help---
		SPI5 assigns to the shared pins with eMMC.

config CXD56_SPI5_PINMAP_SDIO
	bool "SPI5 pin assign to SDIO"
	---help---
		SPI5 assigns to the shared pins with SDIO.
endchoice

config CXD56_DMAC_SPI5_TX
	bool "DMAC support for SPI5 TX"
	default n
	select CXD56_DMAC
	---help---
		Enables DMAC for SPI5 TX

if CXD56_DMAC_SPI5_TX

config CXD56_DMAC_SPI5_TX_CH
	int "TX channel"
	default 4
	range 2 6

config CXD56_DMAC_SPI5_TX_MAXSIZE
	int "Max size to be sent in bytes"
	default 1516
	range 1 1572864
	---help---
		This value should be same as RX.

endif # CXD56_DMAC_SPI5_TX

config CXD56_DMAC_SPI5_RX
	bool "DMAC support for SPI5 RX"
	default n
	select CXD56_DMAC

	---help---
		Enables DMAC for SPI5 RX

if CXD56_DMAC_SPI5_RX

config CXD56_DMAC_SPI5_RX_CH
	int "RX channel"
	default 5
	range 2 6

config CXD56_DMAC_SPI5_RX_MAXSIZE
	int "Max size to be received in bytes"
	default 1516
	range 1 1572864
	---help---
		This value should be same as TX.

endif # CXD56_DMAC_SPI5_RX

endif # CXD56_SPI5
endif

config CXD56_I2C
	bool "I2C"
	select I2C

if CXD56_I2C

menuconfig CXD56_I2C0
	bool "I2C0"

if CXD56_I2C0

config CXD56_I2C0_SCUSEQ
	bool "SCU Sequencer"
	default y
	depends on CXD56_SCU
	---help---
		Use the sensor control unit (SCU) sequencer.

endif # CXD56_I2C0

menuconfig CXD56_I2C1
	bool "I2C1"

if CXD56_I2C1

config CXD56_I2C1_SCUSEQ
	bool "SCU Sequencer"
	default y
	depends on CXD56_SCU
	---help---
		Use the sensor control unit (SCU) sequencer.

endif # CXD56_I2C1

config CXD56_I2C2
	bool "I2C2"

config CXD56_I2C_DRIVER
	bool "I2C character driver"
	default n
	select I2C_DRIVER
	---help---
		Build in support for a character driver at /dev/i2c[N] that may be
		used to perform I2C bus transfers from applications.  The intent of
		this driver is to support I2C testing.  It is not suitable for use
		in any real driver application.
endif

config CXD56_USBDEV
	bool "USB"
	default n
	---help---
		Enables USB

config CXD56_PWM
	bool "PWM"

if CXD56_PWM

config CXD56_PWM0
	bool "PWM0"
	default n
	---help---
		Enable PWM channel 0

config CXD56_PWM1
	bool "PWM1"
	default n
	---help---
		Enable PWM channel 1

config CXD56_PWM2
	bool "PWM2"
	default n
	---help---
		Enable PWM channel 2

config CXD56_PWM3
	bool "PWM3"
	default n
	---help---
		Enable PWM channel 3

endif # CXD56_PWM

config CXD56_ADC
	bool "ADC"
	default n
	depends on CXD56_SCU
	---help---
		Support ADC interface

if CXD56_ADC

menuconfig CXD56_HPADC0
	bool "HPADC0"
	default n

if CXD56_HPADC0

config CXD56_HPADC0_FREQ
	int "Coefficient of sampling frequency"
	default 11 if CXD56_SCU_32K
	default 7 if CXD56_SCU_RCOSC
	default 7 if CXD56_SCU_XOSC
	range 9 11 if CXD56_SCU_32K
	range 0 7 if CXD56_SCU_RCOSC
	range 0 7 if CXD56_SCU_XOSC
	---help---
		This configuration set to sampling frequency calculated by the following formula.
			Sampling frequency = adc clock / 2 ^ n [Hz]
			adc clock = SCU32K       : if SCU clock is SCU32K.
			adc clock = RCOSC / 4    : if SCU clock is RCOSC.
			adc clock = XOSC / 3 / 4 : if SCU clock is XOSC 26MHz.
		Note. Setable range depends on the SCU clock mode.
			SCU32K : 9 to 11
			RCOSC  : 0 to 7 (0 ~ 6 : over-sampling mode)
			XOSC   : 0 to 7 (0 ~ 6 : over-sampling mode)

choice
	prompt "Input Gain"
	default CXD56_HPADC0_INPUT_GAIN_0DB

config CXD56_HPADC0_INPUT_GAIN_M6DB
	bool "-6 dB"

config CXD56_HPADC0_INPUT_GAIN_0DB
	bool "0 dB"

config CXD56_HPADC0_INPUT_GAIN_6DB
	bool "+6 dB"

config CXD56_HPADC0_INPUT_GAIN_12DB
	bool "+12 dB"

config CXD56_HPADC0_INPUT_GAIN_14DB
	bool "+14 dB"

endchoice

config CXD56_HPADC0_OFFSET
	int "Offset"
	default 0
	range 0 65535

config CXD56_HPADC0_GAIN
	int "Gain"
	default 0
	range 0 65535

config CXD56_HPADC0_FSIZE
	int "SCU FIFO size"
	default 64
	range 2 40960
	---help---
		SCU FIFO size

config CXD56_HPADC0_HIGHSPEED
	bool "High speed support"
	default n
	depends on !CXD56_LPADC && !CXD56_HPADC1 && !CXD56_I2C0_SCUSEQ && !CXD56_I2C1_SCUSEQ && !CXD56_SPI3_SCUSEQ
	---help---
		Enable high-speed ADC. It supports a sampling rate of up to 512kHz
		only for HPADC0. In other to use the 512kHz sampling rate, SCU clock
		mode should be selected CXD56_SCU_XOSC, and Coefficient of sampling
		frequency CXD56_HPADC0_FREQ should be set 2. When this is enabled,
		other ADC channels and SCU sequencers cannot be used at the same time.

endif # CXD56_HPADC0

menuconfig CXD56_HPADC1
	bool "HPADC1"
	default n

if CXD56_HPADC1

config CXD56_HPADC1_FREQ
	int "Coefficient of sampling frequency"
	default 11 if CXD56_SCU_32K
	default 7 if CXD56_SCU_RCOSC
	default 7 if CXD56_SCU_XOSC
	range 9 11 if CXD56_SCU_32K
	range 0 7 if CXD56_SCU_RCOSC
	range 0 7 if CXD56_SCU_XOSC
	---help---
		This configuration set to sampling frequency calculated by the following formula.
			Sampling frequency = adc clock / 2 ^ n [Hz]
			adc clock = SCU32K       : if SCU clock is SCU32K.
			adc clock = RCOSC / 4    : if SCU clock is RCOSC.
			adc clock = XOSC / 3 / 4 : if SCU clock is XOSC 26MHz.
		Note. Setable range depends on the SCU clock mode.
			SCU32K : 9 to 11
			RCOSC  : 0 to 7 (0 ~ 6 : over-sampling mode)
			XOSC   : 0 to 7 (0 ~ 6 : over-sampling mode)

choice
	prompt "Input Gain"
	default CXD56_HPADC1_INPUT_GAIN_0DB

config CXD56_HPADC1_INPUT_GAIN_M6DB
	bool "-6 dB"

config CXD56_HPADC1_INPUT_GAIN_0DB
	bool "0 dB"

config CXD56_HPADC1_INPUT_GAIN_6DB
	bool "+6 dB"

config CXD56_HPADC1_INPUT_GAIN_12DB
	bool "+12 dB"

config CXD56_HPADC1_INPUT_GAIN_14DB
	bool "+14 dB"

endchoice

config CXD56_HPADC1_OFFSET
	int "Offset"
	default 0
	range 0 65535

config CXD56_HPADC1_GAIN
	int "Gain"
	default 0
	range 0 65535

config CXD56_HPADC1_FSIZE
	int "SCU FIFO size"
	default 64
	range 2 40960
	---help---
		SCU FIFO size

endif # CXD56_HPADC1

config CXD56_LPADC
	bool "LPADC"
	default n

if CXD56_LPADC

choice
	prompt "Select LPADC channel"
	default CXD56_LPADC_ALL

config CXD56_LPADC0
	bool "LPADC channel 0"
	---help---
		Enable LPADC channel 0.
		Other LPADC channel can not be used.

config CXD56_LPADC1
	bool "LPADC channel 1"
	---help---
		Enable LPADC channel 1.
		Other LPADC channel can not be used.

config CXD56_LPADC2
	bool "LPADC channel 2"
	---help---
		Enable LPADC channel 2.
		Other LPADC channel can not be used.

config CXD56_LPADC3
	bool "LPADC channel 3"
	---help---
		Enable LPADC channel 3.
		Other LPADC channel can not be used.

config CXD56_LPADC0_1
	bool "LPADC channel 0 and channel 1"
	---help---
		Enable LPADC channel 0 and LPADC channel 1.
		Switch the LPADC channel 0 sampling and LPADC channel 1 sampling in time division.

config CXD56_LPADC_ALL
	bool "LPADC all channels"
	---help---
		Enable LPADC all channels.
		Switch the LPADC all channels sampling in time division.
endchoice

menu "LPADC0"
	depends on CXD56_LPADC0 || CXD56_LPADC0_1 || CXD56_LPADC_ALL

config CXD56_LPADC0_FREQ
	int "Coefficient of sampling frequency"
	default 12 if CXD56_SCU_32K
	default 7 if CXD56_SCU_RCOSC || CXD56_SCU_XOSC
	range 11 15 if CXD56_SCU_32K && CXD56_LPADC0
	range 3 15 if CXD56_SCU_RCOSC && CXD56_LPADC0
	range 2 15 if CXD56_SCU_XOSC && CXD56_LPADC0
	range 12 15 if CXD56_SCU_32K && CXD56_LPADC0_1
	range 6 15 if CXD56_SCU_RCOSC && CXD56_LPADC0_1
	range 6 15 if CXD56_SCU_XOSC && CXD56_LPADC0_1
	range 11 15 if CXD56_SCU_32K && CXD56_LPADC_ALL
	range 7 15 if CXD56_SCU_RCOSC && CXD56_LPADC_ALL
	range 7 15 if CXD56_SCU_XOSC && CXD56_LPADC_ALL
	---help---
		This configuration set to sampling frequency calculated by the following formula.

		Note: Available range depends on the SCU clock mode.

		(Use channel 0 only) Sampling frequency = SCU32K / 2 ^ n [Hz]
			SCU32K : 11 to 15
			RCOSC  :  3 to 15
			XOSC   :  2 to 15

		(Use channel 0/1) Sampling frequency = SCU32K / 2 ^ (n+1) [Hz]
			SCU32K : 12 to 15
			RCOSC  :  6 to 15
			XOSC   :  6 to 15

		(Use all of channels) Sampling frequency = SCU32K / 2 ^ (n+2) [Hz]
			SCU32K : 11 to 15
			RCOSC  :  7 to 15
			XOSC   :  7 to 15

config CXD56_LPADC0_OFFSET
	int "Offset"
	default 0
	range 0 65535

config CXD56_LPADC0_GAIN
	int "Gain"
	default 0
	range 0 65535

config CXD56_LPADC0_FSIZE
	int "SCU FIFO size"
	default 16
	range 2 40960
	---help---
		SCU FIFO size

endmenu # CXD56_LPADC0

menu "LPADC1"
	depends on CXD56_LPADC1 || CXD56_LPADC0_1 || CXD56_LPADC_ALL

config CXD56_LPADC1_FREQ
	int "Coefficient of sampling frequency"
	default 12 if CXD56_SCU_32K
	default 7 if CXD56_SCU_RCOSC || CXD56_SCU_XOSC
	range 11 15 if CXD56_SCU_32K && CXD56_LPADC1
	range 3 15 if CXD56_SCU_RCOSC && CXD56_LPADC1
	range 2 15 if CXD56_SCU_XOSC && CXD56_LPADC1
	range 12 15 if CXD56_SCU_32K && CXD56_LPADC0_1
	range 6 15 if CXD56_SCU_RCOSC && CXD56_LPADC0_1
	range 6 15 if CXD56_SCU_XOSC && CXD56_LPADC0_1
	range 11 15 if CXD56_SCU_32K && CXD56_LPADC_ALL
	range 7 15 if CXD56_SCU_RCOSC && CXD56_LPADC_ALL
	range 7 15 if CXD56_SCU_XOSC && CXD56_LPADC_ALL
	---help---
		This configuration set to sampling frequency calculated by the following formula.

		Note: Available range depends on the SCU clock mode.

		(Use channel 1 only) Sampling frequency = SCU32K / 2 ^ n [Hz]
			SCU32K : 11 to 15
			RCOSC  :  3 to 15
			XOSC   :  2 to 15

		(Use channel 0/1) Sampling frequency = SCU32K / 2 ^ (n+1) [Hz]
			SCU32K : 12 to 15
			RCOSC  :  6 to 15
			XOSC   :  6 to 15

		(Use all of channels) Sampling frequency = SCU32K / 2 ^ (n+2) [Hz]
			SCU32K : 11 to 15
			RCOSC  :  7 to 15
			XOSC   :  7 to 15

config CXD56_LPADC1_OFFSET
	int "Offset"
	default 0
	range 0 65535

config CXD56_LPADC1_GAIN
	int "Gain"
	default 0
	range 0 65535

config CXD56_LPADC1_FSIZE
	int "SCU FIFO size"
	default 16
	range 2 40960
	---help---
		SCU FIFO size

endmenu # CXD56_LPADC1

menu "LPADC2"
	depends on CXD56_LPADC2 || CXD56_LPADC_ALL

config CXD56_LPADC2_FREQ
	int "Coefficient of sampling frequency"
	default 12 if CXD56_SCU_32K
	default 7 if CXD56_SCU_RCOSC || CXD56_SCU_XOSC
	range 11 15 if CXD56_SCU_32K && CXD56_LPADC2
	range 3 15 if CXD56_SCU_RCOSC && CXD56_LPADC2
	range 2 15 if CXD56_SCU_XOSC && CXD56_LPADC2
	range 11 15 if CXD56_SCU_32K && CXD56_LPADC_ALL
	range 7 15 if CXD56_SCU_RCOSC && CXD56_LPADC_ALL
	range 7 15 if CXD56_SCU_XOSC && CXD56_LPADC_ALL
	---help---
		This configuration set to sampling frequency calculated by the following formula.

		Note: Available range depends on the SCU clock mode.

		(Use channel 2 only) Sampling frequency = SCU32K / 2 ^ n [Hz]
			SCU32K : 11 to 15
			RCOSC  :  3 to 15
			XOSC   :  2 to 15

		(Use all of channels) Sampling frequency = SCU32K / 2 ^ (n+2) [Hz]
			SCU32K : 11 to 15
			RCOSC  :  7 to 15
			XOSC   :  7 to 15

config CXD56_LPADC2_OFFSET
	int "Offset"
	default 0
	range 0 65535

config CXD56_LPADC2_GAIN
	int "Gain"
	default 0
	range 0 65535

config CXD56_LPADC2_FSIZE
	int "SCU FIFO size"
	default 16
	range 2 40960
	---help---
		SCU FIFO size

endmenu # CXD56_LPADC2

menu "LPADC3"
	depends on CXD56_LPADC3 || CXD56_LPADC_ALL

config CXD56_LPADC3_FREQ
	int "Coefficient of sampling frequency"
	default 12 if CXD56_SCU_32K
	default 7 if CXD56_SCU_RCOSC || CXD56_SCU_XOSC
	range 11 15 if CXD56_SCU_32K && CXD56_LPADC3
	range 3 15 if CXD56_SCU_RCOSC && CXD56_LPADC3
	range 2 15 if CXD56_SCU_XOSC && CXD56_LPADC3
	range 11 15 if CXD56_SCU_32K && CXD56_LPADC_ALL
	range 7 15 if CXD56_SCU_RCOSC && CXD56_LPADC_ALL
	range 7 15 if CXD56_SCU_XOSC && CXD56_LPADC_ALL
	---help---
		This configuration set to sampling frequency calculated by the following formula.

		Note: Available range depends on the SCU clock mode.

		(Use channel 3 only) Sampling frequency = SCU32K / 2 ^ n [Hz]
			SCU32K : 11 to 15
			RCOSC  :  3 to 15
			XOSC   :  2 to 15

		(Use all of channels) Sampling frequency = SCU32K / 2 ^ (n+2) [Hz]
			SCU32K : 11 to 15
			RCOSC  :  7 to 15
			XOSC   :  7 to 15

config CXD56_LPADC3_OFFSET
	int "Offset"
	default 0
	range 0 65535

config CXD56_LPADC3_GAIN
	int "Gain"
	default 0
	range 0 65535

config CXD56_LPADC3_FSIZE
	int "SCU FIFO size"
	default 16
	range 2 40960
	---help---
		SCU FIFO size

endmenu # CXD56_LPADC3

endif # CXD56_LPADC

endif # CXD56_ADC
menuconfig CXD56_SCU
	bool "Sensor Control Unit (SCU)"
	default y

if CXD56_SCU

config CXD56_SCU_PREDIV
	int "Sequencer Sampling Predivider"
	default 64
	range 1 256
	---help---
		This configuration ralated to maximum sampling rate based
		on 32.768KHz.
		e.g. 32768 / 64 = 512 (samples)

choice
	prompt "SCU clock mode"
	default CXD56_SCU_RCOSC

config CXD56_SCU_32K
	bool "Same with SCU32K"

config CXD56_SCU_RCOSC
	bool "RCOSC"

config CXD56_SCU_XOSC
	bool "XOSC"
endchoice

if CXD56_SCU_XOSC
config CXD56_SCU_XOSC_DIV
	int "SCU XOSC predivider"
	default 2
	range 1 4
endif

choice
	prompt "SCU32K clock source"
	default CXD56_SCU32K_RTC

config CXD56_SCU32K_RCRTC
	bool "RCRTC"

config CXD56_SCU32K_RTC
	bool "RTC"
endchoice

config CXD56_SCU_DEBUG
	bool "SCU Debug"
	default n

config CXD56_SCU_DEBUG_ERR
	bool "Error Messages"
	default n
	depends on CXD56_SCU_DEBUG

config CXD56_SCU_DEBUG_WARN
	bool "Warning Messages"
	default n
	depends on CXD56_SCU_DEBUG_ERR

config CXD56_SCU_DEBUG_INFO
	bool "Info Messages"
	default n
	depends on CXD56_SCU_DEBUG_WARN

config CXD56_UDMAC
	bool "DMAC support"
	default y
	select ARCH_DMA
	---help---
		Use DMAC for reading sensing data from SCU FIFO.
endif # CXD56_SCU

config CXD56_CISIF
	bool "CMOS image sensor interface"
	default n
	---help---
		CMOS image sensor interface for cx5602 chip

config CXD56_HOSTIF
	bool "Host interface"
	default n
	---help---
		Host interface supports I2C or SPI slave feature.

config CXD56_HOSTIF_DEBUG
	bool "Host interface Debug Features"
	default n
	depends on CXD56_HOSTIF
	---help---
		Enable host interface device debug features.

if CXD56_HOSTIF_DEBUG

config CXD56_HOSTIF_DEBUG_ERROR
	bool "Host interface Error Output"
	default n
	depends on DEBUG_ERROR
	---help---
		Enable host interface error output to SYSLOG.

config CXD56_HOSTIF_DEBUG_WARN
	bool "Host interface Warnings Output"
	default n
	depends on DEBUG_WARN
	---help---
		Enable host interface warning output to SYSLOG.

config CXD56_HOSTIF_DEBUG_INFO
	bool "Host interface Informational Output"
	default n
	depends on DEBUG_INFO
	---help---
		Enable host interface informational output to SYSLOG.

endif # CXD56_HOSTIF_DEBUG

endmenu

comment "Storage Options"

menu "Storage Options"

menuconfig CXD56_SFC
	bool "SPI Flash"
	default n if CXD56_SUBCORE
	default y
	select MTD

if CXD56_SFC

config CXD56_SFC_PAGE_SHIFT_SIZE
	int "SPI Flash page shift size"
	default 12
	range 8 12
	---help---
		Specify page shift size to determine block size of SPI flash access.
		Relationship between page shift size and block size is as follows.
		page shift size : block size
			8 : 256
			9 : 512
			10 : 1024
			11 : 2048
			12 : 4096
		When file system is SMART file system, please set the value to SMART
		Device sector size or less.
		Following equation is satisfied.
		MTD_SMART_SECTOR_SIZE >= (1 << CXD56_SFC_PAGE_SHIFT_SIZE)

config CXD56_SFC_VERIFY_WRITE
	bool "Enable write with verify"
	default n
	---help---
		Enable SPI flash write function with verify.

endif # CXD56_SFC

menuconfig CXD56_SDIO
	bool "SDIO SD Card"
	default n
	select ARCH_HAVE_SDIO
	select SDIO_BLOCKSETUP
	depends on SCHED_WORKQUEUE

if CXD56_SDIO

config CXD56_SDIO_DMA
	bool "Support DMA data transfers"
	default y
	select SDIO_DMA
	---help---
		Support DMA data transfers.
		Enable SD card DMA data transfers.  This is marginally optional.
		For most usages, SD accesses will cause data overruns if used without
		DMA.

config CXD56_SDIO_WIDTH_D1_ONLY
	bool "Use D1 only"
	default n
	---help---
		Select 1-bit transfer mode.  Default: 4-bit transfer mode.

config CXD56_SDIO_DISABLE_CD_WP
	bool "Disable the CD and WP pin for SDIO"
	default y
	---help---
		Disable the CD and WP pin for Embedded SDIO.If the CD pin is not disable,
		the SDIO initialization will be failed.

endif # SDIO Configuration

menuconfig CXD56_SPISD
	bool "SPI SD Card"
	default n
	select MMCSD_SPI

if CXD56_SPISD

config CXD56_SPISD_SLOT_NO
	int "SPI SD Card Slot Number"
	default 0
	---help---
		Select spi sd card slot number.

config CXD56_SPISD_SPI_CH
	int "SPI channel number"
	default 5
	range 4 5
	---help---
		Select spi channel number to use spi sd card.

endif # SPISD Configuration

config CXD56_EMMC
	bool "eMMC"
	default n if CXD56_SUBCORE
	---help---
		Emmc driver for cxd56xx chip

if CXD56_EMMC

config CXD56_EMMC_VENDOR_TOSHIBA
	bool "Toshiba eMMC device"
	default n
	---help---
		Enable vendor-specific commands for Toshiba eMMC device.

endif # CXD56_EMMC

endmenu

config CXD56_GE2D
	bool "Graphics Engine"
	default n
	---help---
		A hardware image processor device.

config CXD56_GNSS
	bool "GNSS device"
	default n

if CXD56_GNSS

menu "GNSS settings"

config CXD56_GNSS_NPOLLWAITERS
	int "GNSS max poll waiters"
	default 4

config CXD56_GNSS_NSIGNALRECEIVERS
	int "GNSS max signal receivers"
	default 4

config CXD56_GNSS_WORKER_STACKSIZE
	int "GNSS worker thread stack size"
	default 128

config CXD56_GNSS_WORKER_THREAD_PRIORITY
	int "GNSS worker thread priority"
	default 255

config CXD56_GNSS_BACKUP_FILENAME
	string "GNSS backup file name"
	default "/mnt/spif/gnss_backup.bin"
	---help---
		Specify the path and file name of backup data.

config CXD56_GNSS_CEP_FILENAME
	string "GNSS CEP file name"
	default "/mnt/sd0/gnss_cep.bin"
	---help---
		Specify the path and file name of cep data.

config CXD56_GNSS_FW_RTK
	bool "Support carrier-phase data output for Real-Time Kinematic"
	default n
	---help---
		This is experimental function.

choice
	prompt "GNSS 1PPS output"
	default CXD56_GNSS_1PPS_PIN_HIF_IRQ_OUT

config CXD56_GNSS_1PPS_PIN_GNSS_1PPS_OUT
	bool "Use PIN_GNSS_1PPS_OUT"
	depends on CXD56_FCBGA
	---help---
		GNSS 1PPS output from PIN_GNSS_1PPS_OUT pin.

config CXD56_GNSS_1PPS_PIN_HIF_IRQ_OUT
	bool "Use PIN_HIF_IRQ_OUT"
	---help---
		GNSS 1PPS output from PIN_HIF_IRQ_OUT pin.
endchoice

config CXD56_GNSS_DEBUG_FEATURE
	bool "GNSS debug feature"

if CXD56_GNSS_DEBUG_FEATURE

config CXD56_GNSS_DEBUG_ERROR
	bool "GNSS debug error"

config CXD56_GNSS_DEBUG_WARN
	bool "GNSS debug warn"

config CXD56_GNSS_DEBUG_INFO
	bool "GNSS debug info"

endif # CXD56_GNSS_DEBUG_FEATURE

endmenu
config CXD56_GEOFENCE
	bool "Geofence Support"
	default y
	depends on CXD56_GNSS

endif

config CXD56_TESTSET
	bool "Use custom testset for spinlock"
	default y
	depends on SMP
	---help---
		Use custom testset

if CXD56_TESTSET

config CXD56_TESTSET_WITH_HWSEM
	bool "Use custom testset based on hardware semaphore"
	default n if CXD56_USE_SYSBUS
	default y if !CXD56_USE_SYSBUS

endif

config CXD56_USE_SYSBUS
	bool "Use the system bus for the data section"
	default y
	select ARCH_USE_TEXT_HEAP if ELF
	---help---
		To make ldrex/strex work correctly, this option must be enabled
endmenu
