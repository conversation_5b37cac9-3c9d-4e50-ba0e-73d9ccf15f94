/****************************************************************************
 * arch/arm/src/stm32wl5/hardware/stm32wl5_pinmap.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_STM32WL5_HARDWARE_STM32WL5_PINMAP_H
#define __ARCH_ARM_SRC_STM32WL5_HARDWARE_STM32WL5_PINMAP_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>
#include "chip.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Alternate Pin Functions.  All members of the STM32WL5xxx family share the
 * same pin multiplexing (although they may differ in the pins physically
 * available).
 *
 * Alternative pin selections are provided with a numeric suffix like _1, _2,
 * etc. Drivers, however, will use the pin selection without the numeric
 * suffix. Additional definitions are required in the board.h file.  For
 * example, if CAN1_RX connects via PA11 on some board, then the following
 * definitions should appear in the board.h header file for that board:
 *
 * #define GPIO_CAN1_RX GPIO_CAN1_RX_1
 *
 * The driver will then automatically configure PA11 as the CAN1 RX pin.
 */

/* WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific GPIO options such as
 * frequency, open-drain/push-pull, and pull-up/down!  Just the basics are
 * defined for most pins in this file.
 */

/* Eventout */

#define GPIO_CM4_EVENTOUT_1       (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN0)
#define GPIO_CM4_EVENTOUT_2       (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN1)
#define GPIO_CM4_EVENTOUT_3       (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN10)
#define GPIO_CM4_EVENTOUT_4       (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN11)
#define GPIO_CM4_EVENTOUT_5       (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN12)
#define GPIO_CM4_EVENTOUT_6       (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN13)
#define GPIO_CM4_EVENTOUT_7       (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN14)
#define GPIO_CM4_EVENTOUT_8       (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN15)
#define GPIO_CM4_EVENTOUT_9       (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN2)
#define GPIO_CM4_EVENTOUT_10       (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN3)
#define GPIO_CM4_EVENTOUT_11       (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN4)
#define GPIO_CM4_EVENTOUT_12       (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN5)
#define GPIO_CM4_EVENTOUT_13       (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN6)
#define GPIO_CM4_EVENTOUT_14       (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN7)
#define GPIO_CM4_EVENTOUT_15       (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN8)
#define GPIO_CM4_EVENTOUT_16       (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN9)
#define GPIO_CM4_EVENTOUT_17       (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN0)
#define GPIO_CM4_EVENTOUT_18       (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN1)
#define GPIO_CM4_EVENTOUT_19       (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN10)
#define GPIO_CM4_EVENTOUT_20       (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN11)
#define GPIO_CM4_EVENTOUT_21       (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN12)
#define GPIO_CM4_EVENTOUT_22       (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN13)
#define GPIO_CM4_EVENTOUT_23       (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN14)
#define GPIO_CM4_EVENTOUT_24       (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN15)
#define GPIO_CM4_EVENTOUT_25       (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN2)
#define GPIO_CM4_EVENTOUT_26       (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN3)
#define GPIO_CM4_EVENTOUT_27       (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN4)
#define GPIO_CM4_EVENTOUT_28       (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN5)
#define GPIO_CM4_EVENTOUT_29       (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN6)
#define GPIO_CM4_EVENTOUT_30       (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN7)
#define GPIO_CM4_EVENTOUT_31       (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN8)
#define GPIO_CM4_EVENTOUT_32       (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN9)
#define GPIO_CM4_EVENTOUT_33       (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN0)
#define GPIO_CM4_EVENTOUT_34       (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN1)
#define GPIO_CM4_EVENTOUT_35       (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN13)
#define GPIO_CM4_EVENTOUT_36       (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN14)
#define GPIO_CM4_EVENTOUT_37       (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN15)
#define GPIO_CM4_EVENTOUT_38       (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN2)
#define GPIO_CM4_EVENTOUT_39       (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN3)
#define GPIO_CM4_EVENTOUT_40       (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN4)
#define GPIO_CM4_EVENTOUT_41       (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN5)
#define GPIO_CM4_EVENTOUT_42       (GPIO_ALT|GPIO_AF15|GPIO_PORTC|GPIO_PIN6)
#define GPIO_CM4_EVENTOUT_43       (GPIO_ALT|GPIO_AF15|GPIO_PORTH|GPIO_PIN3)

/* COMP */

#define GPIO_COMP1_OUT_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTA|GPIO_PIN0)
#define GPIO_COMP1_OUT_2          (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN0)
#define GPIO_COMP1_OUT_3          (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN10)
#define GPIO_COMP2_OUT_1          (GPIO_ALT|GPIO_AF12|GPIO_PORTA|GPIO_PIN2)
#define GPIO_COMP2_OUT_2          (GPIO_ALT|GPIO_AF12|GPIO_PORTA|GPIO_PIN7)
#define GPIO_COMP2_OUT_3          (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN11)
#define GPIO_COMP2_OUT_4          (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN5)

/* DEBUG */

#define GPIO_DEBUG_PWR_LDORDY_1   (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN2)
#define GPIO_DEBUG_PWR_REGLP1S_1  (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN0)
#define GPIO_DEBUG_PWR_REGLP2S_1  (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN1)
#define GPIO_DEBUG_RF_DTB1_1      (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN3)
#define GPIO_DEBUG_RF_HSE32RDY_1  (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN10)
#define GPIO_DEBUG_RF_LDORDY_1    (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN4)
#define GPIO_DEBUG_RF_NRESET_1    (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN11)
#define GPIO_DEBUG_RF_SMPSRDY_1   (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN2)
#define GPIO_DEBUG_SUBGHZSPI_MISOOUT_1 (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN6)
#define GPIO_DEBUG_SUBGHZSPI_MOSIOUT_1 (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN7)
#define GPIO_DEBUG_SUBGHZSPI_NSSOUT_1 (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN4)
#define GPIO_DEBUG_SUBGHZSPI_SCKOUT_1 (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN5)

/* I2C */

#define GPIO_I2C1_SCL_1           (GPIO_ALT|GPIO_AF4 |GPIO_PORTA|GPIO_PIN9)
#define GPIO_I2C1_SCL_2           (GPIO_ALT|GPIO_AF4 |GPIO_PORTB|GPIO_PIN6)
#define GPIO_I2C1_SCL_3           (GPIO_ALT|GPIO_AF4 |GPIO_PORTB|GPIO_PIN8)
#define GPIO_I2C1_SDA_1           (GPIO_ALT|GPIO_AF4 |GPIO_PORTA|GPIO_PIN10)
#define GPIO_I2C1_SDA_2           (GPIO_ALT|GPIO_AF4 |GPIO_PORTB|GPIO_PIN7)
#define GPIO_I2C1_SDA_3           (GPIO_ALT|GPIO_AF4 |GPIO_PORTB|GPIO_PIN9)
#define GPIO_I2C1_SMBA_1          (GPIO_ALT|GPIO_AF4 |GPIO_PORTA|GPIO_PIN1)
#define GPIO_I2C1_SMBA_2          (GPIO_ALT|GPIO_AF4 |GPIO_PORTA|GPIO_PIN14)
#define GPIO_I2C1_SMBA_3          (GPIO_ALT|GPIO_AF4 |GPIO_PORTB|GPIO_PIN5)

#define GPIO_I2C2_SCL_1           (GPIO_ALT|GPIO_AF4 |GPIO_PORTA|GPIO_PIN12)
#define GPIO_I2C2_SCL_2           (GPIO_ALT|GPIO_AF4 |GPIO_PORTB|GPIO_PIN15)
#define GPIO_I2C2_SDA_1           (GPIO_ALT|GPIO_AF4 |GPIO_PORTA|GPIO_PIN11)
#define GPIO_I2C2_SDA_2           (GPIO_ALT|GPIO_AF4 |GPIO_PORTA|GPIO_PIN15)
#define GPIO_I2C2_SMBA_1          (GPIO_ALT|GPIO_AF4 |GPIO_PORTA|GPIO_PIN13)
#define GPIO_I2C2_SMBA_2          (GPIO_ALT|GPIO_AF4 |GPIO_PORTA|GPIO_PIN6)

#define GPIO_I2C3_SCL_1           (GPIO_ALT|GPIO_AF4 |GPIO_PORTA|GPIO_PIN7)
#define GPIO_I2C3_SCL_2           (GPIO_ALT|GPIO_AF4 |GPIO_PORTB|GPIO_PIN10)
#define GPIO_I2C3_SCL_3           (GPIO_ALT|GPIO_AF4 |GPIO_PORTB|GPIO_PIN13)
#define GPIO_I2C3_SCL_4           (GPIO_ALT|GPIO_AF4 |GPIO_PORTC|GPIO_PIN0)
#define GPIO_I2C3_SDA_1           (GPIO_ALT|GPIO_AF4 |GPIO_PORTB|GPIO_PIN11)
#define GPIO_I2C3_SDA_2           (GPIO_ALT|GPIO_AF4 |GPIO_PORTB|GPIO_PIN14)
#define GPIO_I2C3_SDA_3           (GPIO_ALT|GPIO_AF4 |GPIO_PORTB|GPIO_PIN4)
#define GPIO_I2C3_SDA_4           (GPIO_ALT|GPIO_AF4 |GPIO_PORTC|GPIO_PIN1)
#define GPIO_I2C3_SMBA_1          (GPIO_ALT|GPIO_AF4 |GPIO_PORTA|GPIO_PIN0)
#define GPIO_I2C3_SMBA_2          (GPIO_ALT|GPIO_AF4 |GPIO_PORTB|GPIO_PIN12)
#define GPIO_I2C3_SMBA_3          (GPIO_ALT|GPIO_AF4 |GPIO_PORTB|GPIO_PIN2)

/* I2S */

#define GPIO_I2S2_CK_1            (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN8)
#define GPIO_I2S2_CK_2            (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN9)
#define GPIO_I2S2_CK_3            (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN10)
#define GPIO_I2S2_CK_4            (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN13)
#define GPIO_I2S2_MCK_1           (GPIO_ALT|GPIO_AF3 |GPIO_PORTB|GPIO_PIN14)
#define GPIO_I2S2_MCK_2           (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN3)
#define GPIO_I2S2_MCK_3           (GPIO_ALT|GPIO_AF5 |GPIO_PORTC|GPIO_PIN6)
#define GPIO_I2S2_SD_1            (GPIO_ALT|GPIO_AF3 |GPIO_PORTC|GPIO_PIN1)
#define GPIO_I2S2_SD_2            (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN10)
#define GPIO_I2S2_SD_3            (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN15)
#define GPIO_I2S2_SD_4            (GPIO_ALT|GPIO_AF5 |GPIO_PORTC|GPIO_PIN3)
#define GPIO_I2S2_WS_1            (GPIO_ALT|GPIO_AF3 |GPIO_PORTA|GPIO_PIN9)
#define GPIO_I2S2_WS_2            (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN12)
#define GPIO_I2S2_WS_3            (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN9)
#define GPIO_I2S_CKIN_1           (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN0)

/* IR */

#define GPIO_IR_OUT_1             (GPIO_ALT|GPIO_AF8 |GPIO_PORTA|GPIO_PIN13)
#define GPIO_IR_OUT_2             (GPIO_ALT|GPIO_AF8 |GPIO_PORTB|GPIO_PIN9)

/* JTAG */

#define GPIO_JTCK_SWCLK_1         (GPIO_ALT|GPIO_AF0 |GPIO_PORTA|GPIO_PIN14)
#define GPIO_JTDI_1               (GPIO_ALT|GPIO_AF0 |GPIO_PORTA|GPIO_PIN15)
#define GPIO_JTDO_1               (GPIO_ALT|GPIO_AF0 |GPIO_PORTB|GPIO_PIN3)
#define GPIO_JTMS_SWDIO_1         (GPIO_ALT|GPIO_AF0 |GPIO_PORTA|GPIO_PIN13)
#define GPIO_NJTRST_1             (GPIO_ALT|GPIO_AF0 |GPIO_PORTB|GPIO_PIN4)

/* Radio */

#define GPIO_RF_BUSY_1            (GPIO_ALT|GPIO_AF6 |GPIO_PORTA|GPIO_PIN12)
#define GPIO_RF_IRQ0_1            (GPIO_ALT|GPIO_AF6 |GPIO_PORTB|GPIO_PIN3)
#define GPIO_RF_IRQ1_1            (GPIO_ALT|GPIO_AF6 |GPIO_PORTB|GPIO_PIN5)
#define GPIO_RF_IRQ2_1            (GPIO_ALT|GPIO_AF6 |GPIO_PORTB|GPIO_PIN8)

/* RTC */

#define GPIO_RTC_OUT_1            (GPIO_ALT|GPIO_AF0 |GPIO_PORTA|GPIO_PIN4)
#define GPIO_RTC_REFIN_1          (GPIO_ALT|GPIO_AF0 |GPIO_PORTA|GPIO_PIN10)

/* SPI */

#define GPIO_SPI1_MISO_1          (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN11)
#define GPIO_SPI1_MISO_2          (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN6)
#define GPIO_SPI1_MISO_3          (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN4)
#define GPIO_SPI1_MOSI_1          (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN12)
#define GPIO_SPI1_MOSI_2          (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN7)
#define GPIO_SPI1_MOSI_3          (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN5)
#define GPIO_SPI1_NSS_1           (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN15)
#define GPIO_SPI1_NSS_2           (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN4)
#define GPIO_SPI1_NSS_3           (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN2)
#define GPIO_SPI1_SCK_1           (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN1)
#define GPIO_SPI1_SCK_2           (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN5)
#define GPIO_SPI1_SCK_3           (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN3)

#define GPIO_SPI2_MISO_1          (GPIO_ALT|GPIO_AF3 |GPIO_PORTA|GPIO_PIN5)
#define GPIO_SPI2_MISO_2          (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN14)
#define GPIO_SPI2_MISO_3          (GPIO_ALT|GPIO_AF5 |GPIO_PORTC|GPIO_PIN2)
#define GPIO_SPI2_MOSI_1          (GPIO_ALT|GPIO_AF3 |GPIO_PORTC|GPIO_PIN1)
#define GPIO_SPI2_MOSI_2          (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN10)
#define GPIO_SPI2_MOSI_3          (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN15)
#define GPIO_SPI2_MOSI_4          (GPIO_ALT|GPIO_AF5 |GPIO_PORTC|GPIO_PIN3)
#define GPIO_SPI2_NSS_1           (GPIO_ALT|GPIO_AF3 |GPIO_PORTA|GPIO_PIN9)
#define GPIO_SPI2_NSS_2           (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN12)
#define GPIO_SPI2_NSS_3           (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN9)
#define GPIO_SPI2_SCK_1           (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN8)
#define GPIO_SPI2_SCK_2           (GPIO_ALT|GPIO_AF5 |GPIO_PORTA|GPIO_PIN9)
#define GPIO_SPI2_SCK_3           (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN10)
#define GPIO_SPI2_SCK_4           (GPIO_ALT|GPIO_AF5 |GPIO_PORTB|GPIO_PIN13)

/* TIM */

#define GPIO_TIM16_BKIN_1         (GPIO_ALT|GPIO_AF14|GPIO_PORTB|GPIO_PIN5)
#define GPIO_TIM16_CH1N_1         (GPIO_ALT|GPIO_AF14|GPIO_PORTB|GPIO_PIN6)
#define GPIO_TIM16_CH1_1          (GPIO_ALT|GPIO_AF14|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM16_CH1_2          (GPIO_ALT|GPIO_AF14|GPIO_PORTB|GPIO_PIN8)
#define GPIO_TIM17_BKIN_1         (GPIO_ALT|GPIO_AF14|GPIO_PORTA|GPIO_PIN10)
#define GPIO_TIM17_BKIN_2         (GPIO_ALT|GPIO_AF14|GPIO_PORTB|GPIO_PIN4)
#define GPIO_TIM17_CH1N_1         (GPIO_ALT|GPIO_AF14|GPIO_PORTB|GPIO_PIN7)
#define GPIO_TIM17_CH1_1          (GPIO_ALT|GPIO_AF14|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM17_CH1_2          (GPIO_ALT|GPIO_AF14|GPIO_PORTB|GPIO_PIN9)
#define GPIO_TIM1_BKIN2_1         (GPIO_ALT|GPIO_AF12|GPIO_PORTA|GPIO_PIN11)
#define GPIO_TIM1_BKIN2_2         (GPIO_ALT|GPIO_AF2 |GPIO_PORTA|GPIO_PIN11)
#define GPIO_TIM1_BKIN_1          (GPIO_ALT|GPIO_AF1 |GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM1_BKIN_2          (GPIO_ALT|GPIO_AF1 |GPIO_PORTB|GPIO_PIN12)
#define GPIO_TIM1_BKIN_3          (GPIO_ALT|GPIO_AF12|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM1_BKIN_4          (GPIO_ALT|GPIO_AF3 |GPIO_PORTB|GPIO_PIN12)
#define GPIO_TIM1_BKIN_5          (GPIO_ALT|GPIO_AF3 |GPIO_PORTB|GPIO_PIN7)
#define GPIO_TIM1_CH1N_1          (GPIO_ALT|GPIO_AF1 |GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM1_CH1N_2          (GPIO_ALT|GPIO_AF1 |GPIO_PORTB|GPIO_PIN13)
#define GPIO_TIM1_CH1_1           (GPIO_ALT|GPIO_AF1 |GPIO_PORTA|GPIO_PIN8)
#define GPIO_TIM1_CH2N_1          (GPIO_ALT|GPIO_AF1 |GPIO_PORTB|GPIO_PIN14)
#define GPIO_TIM1_CH2N_2          (GPIO_ALT|GPIO_AF1 |GPIO_PORTB|GPIO_PIN8)
#define GPIO_TIM1_CH2_1           (GPIO_ALT|GPIO_AF1 |GPIO_PORTA|GPIO_PIN9)
#define GPIO_TIM1_CH3N_1          (GPIO_ALT|GPIO_AF1 |GPIO_PORTB|GPIO_PIN15)
#define GPIO_TIM1_CH3N_2          (GPIO_ALT|GPIO_AF1 |GPIO_PORTB|GPIO_PIN9)
#define GPIO_TIM1_CH3_1           (GPIO_ALT|GPIO_AF1 |GPIO_PORTA|GPIO_PIN10)
#define GPIO_TIM1_CH4_1           (GPIO_ALT|GPIO_AF1 |GPIO_PORTA|GPIO_PIN11)
#define GPIO_TIM1_ETR_1           (GPIO_ALT|GPIO_AF1 |GPIO_PORTA|GPIO_PIN12)

#define GPIO_TIM2_CH1_1           (GPIO_ALT|GPIO_AF1 |GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM2_CH1_2           (GPIO_ALT|GPIO_AF1 |GPIO_PORTA|GPIO_PIN15)
#define GPIO_TIM2_CH1_3           (GPIO_ALT|GPIO_AF1 |GPIO_PORTA|GPIO_PIN5)
#define GPIO_TIM2_CH2_1           (GPIO_ALT|GPIO_AF1 |GPIO_PORTA|GPIO_PIN1)
#define GPIO_TIM2_CH2_2           (GPIO_ALT|GPIO_AF1 |GPIO_PORTB|GPIO_PIN3)
#define GPIO_TIM2_CH3_1           (GPIO_ALT|GPIO_AF1 |GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM2_CH3_2           (GPIO_ALT|GPIO_AF1 |GPIO_PORTB|GPIO_PIN10)
#define GPIO_TIM2_CH4_1           (GPIO_ALT|GPIO_AF1 |GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM2_CH4_2           (GPIO_ALT|GPIO_AF1 |GPIO_PORTB|GPIO_PIN11)
#define GPIO_TIM2_ETR_1           (GPIO_ALT|GPIO_AF14|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM2_ETR_2           (GPIO_ALT|GPIO_AF2 |GPIO_PORTA|GPIO_PIN15)
#define GPIO_TIM2_ETR_3           (GPIO_ALT|GPIO_AF2 |GPIO_PORTA|GPIO_PIN5)

#define GPIO_LPTIM1_ETR_1         (GPIO_ALT|GPIO_AF1 |GPIO_PORTB|GPIO_PIN6)
#define GPIO_LPTIM1_ETR_2         (GPIO_ALT|GPIO_AF1 |GPIO_PORTC|GPIO_PIN3)
#define GPIO_LPTIM1_IN1_1         (GPIO_ALT|GPIO_AF1 |GPIO_PORTB|GPIO_PIN5)
#define GPIO_LPTIM1_IN1_2         (GPIO_ALT|GPIO_AF1 |GPIO_PORTC|GPIO_PIN0)
#define GPIO_LPTIM1_IN2_1         (GPIO_ALT|GPIO_AF1 |GPIO_PORTB|GPIO_PIN7)
#define GPIO_LPTIM1_IN2_2         (GPIO_ALT|GPIO_AF1 |GPIO_PORTC|GPIO_PIN2)
#define GPIO_LPTIM1_OUT_1         (GPIO_ALT|GPIO_AF1 |GPIO_PORTA|GPIO_PIN14)
#define GPIO_LPTIM1_OUT_2         (GPIO_ALT|GPIO_AF1 |GPIO_PORTA|GPIO_PIN4)
#define GPIO_LPTIM1_OUT_3         (GPIO_ALT|GPIO_AF1 |GPIO_PORTB|GPIO_PIN2)
#define GPIO_LPTIM1_OUT_4         (GPIO_ALT|GPIO_AF1 |GPIO_PORTC|GPIO_PIN1)

#define GPIO_LPTIM2_ETR_1         (GPIO_ALT|GPIO_AF14|GPIO_PORTA|GPIO_PIN5)
#define GPIO_LPTIM2_ETR_2         (GPIO_ALT|GPIO_AF14|GPIO_PORTC|GPIO_PIN3)
#define GPIO_LPTIM2_IN1_1         (GPIO_ALT|GPIO_AF14|GPIO_PORTB|GPIO_PIN1)
#define GPIO_LPTIM2_IN1_2         (GPIO_ALT|GPIO_AF14|GPIO_PORTC|GPIO_PIN0)
#define GPIO_LPTIM2_OUT_1         (GPIO_ALT|GPIO_AF14|GPIO_PORTA|GPIO_PIN4)
#define GPIO_LPTIM2_OUT_2         (GPIO_ALT|GPIO_AF14|GPIO_PORTA|GPIO_PIN8)

#define GPIO_LPTIM3_ETR_1         (GPIO_ALT|GPIO_AF3 |GPIO_PORTA|GPIO_PIN11)
#define GPIO_LPTIM3_IN1_1         (GPIO_ALT|GPIO_AF3 |GPIO_PORTA|GPIO_PIN12)
#define GPIO_LPTIM3_OUT_1         (GPIO_ALT|GPIO_AF3 |GPIO_PORTA|GPIO_PIN1)

/* USART */

#define GPIO_USART1_CK_1          (GPIO_ALT|GPIO_AF7 |GPIO_PORTA|GPIO_PIN8)
#define GPIO_USART1_CK_2          (GPIO_ALT|GPIO_AF7 |GPIO_PORTB|GPIO_PIN5)
#define GPIO_USART1_CTS_1         (GPIO_ALT|GPIO_AF7 |GPIO_PORTA|GPIO_PIN11)
#define GPIO_USART1_CTS_2         (GPIO_ALT|GPIO_AF7 |GPIO_PORTB|GPIO_PIN4)
#define GPIO_USART1_RTS_1         (GPIO_ALT|GPIO_AF7 |GPIO_PORTA|GPIO_PIN12)
#define GPIO_USART1_RTS_2         (GPIO_ALT|GPIO_AF7 |GPIO_PORTB|GPIO_PIN3)
#define GPIO_USART1_RX_1          (GPIO_ALT|GPIO_AF7 |GPIO_PORTA|GPIO_PIN10)
#define GPIO_USART1_RX_2          (GPIO_ALT|GPIO_AF7 |GPIO_PORTB|GPIO_PIN7)
#define GPIO_USART1_TX_1          (GPIO_ALT|GPIO_AF7 |GPIO_PORTA|GPIO_PIN9)
#define GPIO_USART1_TX_2          (GPIO_ALT|GPIO_AF7 |GPIO_PORTB|GPIO_PIN6)

#define GPIO_USART2_CK_1          (GPIO_ALT|GPIO_AF7 |GPIO_PORTA|GPIO_PIN4)
#define GPIO_USART2_CTS_1         (GPIO_ALT|GPIO_AF7 |GPIO_PORTA|GPIO_PIN0)
#define GPIO_USART2_RTS_1         (GPIO_ALT|GPIO_AF7 |GPIO_PORTA|GPIO_PIN1)
#define GPIO_USART2_RX_1          (GPIO_ALT|GPIO_AF7 |GPIO_PORTA|GPIO_PIN3)
#define GPIO_USART2_TX_1          (GPIO_ALT|GPIO_AF7 |GPIO_PORTA|GPIO_PIN2)

#define GPIO_LPUART1_CTS_1        (GPIO_ALT|GPIO_AF8 |GPIO_PORTA|GPIO_PIN6)
#define GPIO_LPUART1_CTS_2        (GPIO_ALT|GPIO_AF8 |GPIO_PORTB|GPIO_PIN13)
#define GPIO_LPUART1_RTS_1        (GPIO_ALT|GPIO_AF8 |GPIO_PORTA|GPIO_PIN1)
#define GPIO_LPUART1_RTS_2        (GPIO_ALT|GPIO_AF8 |GPIO_PORTB|GPIO_PIN12)
#define GPIO_LPUART1_RTS_DE_1     (GPIO_ALT|GPIO_AF8 |GPIO_PORTB|GPIO_PIN1)
#define GPIO_LPUART1_RX_1         (GPIO_ALT|GPIO_AF8 |GPIO_PORTA|GPIO_PIN3)
#define GPIO_LPUART1_RX_2         (GPIO_ALT|GPIO_AF8 |GPIO_PORTB|GPIO_PIN10)
#define GPIO_LPUART1_RX_3         (GPIO_ALT|GPIO_AF8 |GPIO_PORTC|GPIO_PIN0)
#define GPIO_LPUART1_TX_1         (GPIO_ALT|GPIO_AF8 |GPIO_PORTA|GPIO_PIN2)
#define GPIO_LPUART1_TX_2         (GPIO_ALT|GPIO_AF8 |GPIO_PORTB|GPIO_PIN11)
#define GPIO_LPUART1_TX_3         (GPIO_ALT|GPIO_AF8 |GPIO_PORTC|GPIO_PIN1)

/* Misc */

#define GPIO_LSCO_1               (GPIO_ALT|GPIO_AF0 |GPIO_PORTA|GPIO_PIN2)
#define GPIO_MCO_1                (GPIO_ALT|GPIO_AF0 |GPIO_PORTA|GPIO_PIN8)

#endif /* __ARCH_ARM_SRC_STM32WL5_HARDWARE_STM32WL5_PINMAP_H */
