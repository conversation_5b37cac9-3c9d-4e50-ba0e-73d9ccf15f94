/****************************************************************************
 * arch/arm/src/stm32/hardware/stm32g4xxv_pinmap.h
 *
 *  Licensed to the Apache Software Foundation (ASF) under one or more
 *  contributor license agreements.  See the NOTICE file distributed with
 *  this work for additional information regarding copyright ownership.  The
 *  ASF licenses this file to you under the Apache License, Version 2.0 (the
 *  "License"); you may not use this file except in compliance with the
 *  License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 *  License for the specific language governing permissions and limitations
 *  under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_STM32_HARDWARE_STM32G4XXV_PINMAP_H
#define __ARCH_ARM_SRC_STM32_HARDWARE_STM32G4XXV_PINMAP_H

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Alternate Pin Functions.  All members of the STM32G4xxxx family share the
 * same pin multiplexing (although they differ in the pins physically
 * available).
 *
 * Alternative pin selections are provided with a numeric suffix like _1, _2,
 * etc.  Drivers, however, will use the pin selection without the numeric
 * suffix.  Additional definitions are required in the board.h file.  For
 * example, if CAN1_RX connects via PA11 on some board, then the following
 * definitions should appear in the board.h header file for that board:
 *
 * #define GPIO_CAN1_RX GPIO_CAN1_RX_1
 *
 * The driver will then automatically configure PA11 as the CAN1 RX pin.
 */

/* WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific GPIO options such as
 * frequency, open-drain/push-pull, and pull-up/down!  Just the basics are
 * defined for most pins in this file.
 */

/* ADC - Analog Digital Converter *******************************************/

#define GPIO_ADC1_IN1_0                (GPIO_ANALOG | GPIO_PORTA |  GPIO_PIN0)
#define GPIO_ADC1_IN2_0                (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN1)
#define GPIO_ADC1_IN3_0                (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN2)
#define GPIO_ADC1_IN4_0                (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN3)
#define GPIO_ADC1_IN5_0                (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN14)
#define GPIO_ADC1_IN6_0                (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN0)
#define GPIO_ADC1_IN7_0                (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN1)
#define GPIO_ADC1_IN8_0                (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN2)
#define GPIO_ADC1_IN9_0                (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN3)
#define GPIO_ADC1_IN10_0               (GPIO_ANALOG | GPIO_PORTF | GPIO_PIN0)
#define GPIO_ADC1_IN11_0               (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN12)
#define GPIO_ADC1_IN12_0               (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN1)
#define GPIO_ADC1_IN14_0               (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN11)
#define GPIO_ADC1_IN15_0               (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN0)

#define GPIO_ADC2_IN1_0                (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN0)
#define GPIO_ADC2_IN2_0                (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN1)
#define GPIO_ADC2_IN3_0                (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN6)
#define GPIO_ADC2_IN4_0                (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN7)
#define GPIO_ADC2_IN5_0                (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN4)
#define GPIO_ADC2_IN6_0                (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN0)
#define GPIO_ADC2_IN7_0                (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN1)
#define GPIO_ADC2_IN8_0                (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN2)
#define GPIO_ADC2_IN9_0                (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN3)
#define GPIO_ADC2_IN10_0               (GPIO_ANALOG | GPIO_PORTF | GPIO_PIN1)
#define GPIO_ADC2_IN11_0               (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN5)
#define GPIO_ADC2_IN12_0               (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN2)
#define GPIO_ADC2_IN13_0               (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN5)
#define GPIO_ADC2_IN14_0               (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN11)
#define GPIO_ADC2_IN15_0               (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN15)
#define GPIO_ADC2_IN17_0               (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN4)

#define GPIO_ADC3_IN1_0                (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN1)
#define GPIO_ADC3_IN2_0                (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN9)
#define GPIO_ADC3_IN3_0                (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN13)
#define GPIO_ADC3_IN4_0                (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN7)
#define GPIO_ADC3_IN5_0                (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN13)
#define GPIO_ADC3_IN6_0                (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN8)
#define GPIO_ADC3_IN7_0                (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN10)
#define GPIO_ADC3_IN8_0                (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN11)
#define GPIO_ADC3_IN9_0                (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN12)
#define GPIO_ADC3_IN10_0               (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN13)
#define GPIO_ADC3_IN11_0               (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN14)
#define GPIO_ADC3_IN12_0               (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN0)
#define GPIO_ADC3_IN14_0               (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN10)
#define GPIO_ADC3_IN15_0               (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN11)
#define GPIO_ADC3_IN16_0               (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN12)

#define GPIO_ADC4_IN1_0                (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN14)
#define GPIO_ADC4_IN2_0                (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN15)
#define GPIO_ADC4_IN3_0                (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN12)
#define GPIO_ADC4_IN4_0                (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN14)
#define GPIO_ADC4_IN5_0                (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN15)
#define GPIO_ADC4_IN6_0                (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN8)
#define GPIO_ADC4_IN7_0                (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN10)
#define GPIO_ADC4_IN8_0                (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN11)
#define GPIO_ADC4_IN9_0                (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN12)
#define GPIO_ADC4_IN10_0               (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN13)
#define GPIO_ADC4_IN11_0               (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN14)
#define GPIO_ADC4_IN12_0               (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN8)
#define GPIO_ADC4_IN13_0               (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN9)
#define GPIO_ADC4_IN14_0               (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN10)
#define GPIO_ADC4_IN15_0               (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN11)
#define GPIO_ADC4_IN16_0               (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN12)

#define GPIO_ADC5_IN1_0                (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN8)
#define GPIO_ADC5_IN2_0                (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN9)
#define GPIO_ADC5_IN6_0                (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN8)
#define GPIO_ADC5_IN7_0                (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN10)
#define GPIO_ADC5_IN8_0                (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN11)
#define GPIO_ADC5_IN9_0                (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN12)
#define GPIO_ADC5_IN10_0               (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN13)
#define GPIO_ADC5_IN11_0               (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN14)
#define GPIO_ADC5_IN12_0               (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN8)
#define GPIO_ADC5_IN13_0               (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN9)
#define GPIO_ADC5_IN14_0               (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN10)
#define GPIO_ADC5_IN15_0               (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN11)
#define GPIO_ADC5_IN16_0               (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN12)

/* COMP - Comparator ********************************************************/

#define GPIO_COMP1_OUT_1               (GPIO_ALT | GPIO_AF8 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_COMP1_OUT_2               (GPIO_ALT | GPIO_AF8 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_COMP1_OUT_3               (GPIO_ALT | GPIO_AF8 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_COMP1_OUT_4               (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN8)

#define GPIO_COMP2_OUT_1               (GPIO_ALT | GPIO_AF8 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_COMP2_OUT_2               (GPIO_ALT | GPIO_AF8 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_COMP2_OUT_3               (GPIO_ALT | GPIO_AF8 | GPIO_PORTA | GPIO_PIN12)
#define GPIO_COMP2_OUT_4               (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN9)

#define GPIO_COMP3_OUT_1               (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN7)
#define GPIO_COMP3_OUT_2               (GPIO_ALT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN15)
#define GPIO_COMP3_OUT_3               (GPIO_ALT | GPIO_AF3 | GPIO_PORTC | GPIO_PIN2)

#define GPIO_COMP4_OUT_1               (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN1)
#define GPIO_COMP4_OUT_2               (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN6)
#define GPIO_COMP4_OUT_3               (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN14)

#define GPIO_COMP5_OUT_1               (GPIO_ALT | GPIO_AF8 | GPIO_PORTA | GPIO_PIN9)
#define GPIO_COMP5_OUT_2               (GPIO_ALT | GPIO_AF7 | GPIO_PORTC | GPIO_PIN7)

#define GPIO_COMP6_OUT_1               (GPIO_ALT | GPIO_AF8 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_COMP6_OUT_2               (GPIO_ALT | GPIO_AF7 | GPIO_PORTC | GPIO_PIN6)

#define GPIO_COMP7_OUT_1               (GPIO_ALT | GPIO_AF8 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_COMP7_OUT_2               (GPIO_ALT | GPIO_AF7 | GPIO_PORTC | GPIO_PIN8)

/* CRS **********************************************************************/

/* REVISIT: Clock Recovery System (CRS_SYNC signal exposed to pin(s)?)
 * Before using the following defines, make sure they are correct!
 */

#if 0
#  define GPIO_USB_CRS_SYNC_0          (GPIO_ALT | GPIO_AF3 | GPIO_PORTA | GPIO_PIN10)
#  define GPIO_UCPD1_CRS_SYNC_0        (GPIO_ALT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN3)
#endif

/* DAC **********************************************************************/

#define GPIO_DAC1_OUT1_0               (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN4)
#define GPIO_DAC1_OUT2_0               (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN5)
#define GPIO_DAC2_OUT1_0               (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN6)

/* Clocks outputs ***********************************************************/

/* MCU clock output */

#define GPIO_MCO_1                     (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_MCO_2                     (GPIO_ALT | GPIO_AF0 | GPIO_PORTG | GPIO_PIN10)

/* Event outputs ************************************************************/

#define GPIO_PA0_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_PA1_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_PA2_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_PA3_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_PA4_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_PA5_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN5)
#define GPIO_PA6_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_PA7_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_PA8_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_PA9_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN9)
#define GPIO_PA10_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_PA11_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_PA12_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN12)
#define GPIO_PA13_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN13)
#define GPIO_PA14_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN14)
#define GPIO_PA15_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN15)

#define GPIO_PB0_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN0)
#define GPIO_PB1_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN1)
#define GPIO_PB2_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN2)
#define GPIO_PB3_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_PB4_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_PB5_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_PB6_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN6)
#define GPIO_PB7_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN7)
#define GPIO_PB8_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_PB9_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_PB10_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_PB11_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN11)
#define GPIO_PB12_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_PB13_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_PB14_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_PB15_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN15)

#define GPIO_PC0_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN0)
#define GPIO_PC1_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN1)
#define GPIO_PC2_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN2)
#define GPIO_PC3_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN3)
#define GPIO_PC4_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN4)
#define GPIO_PC5_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN5)
#define GPIO_PC6_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN6)
#define GPIO_PC7_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN7)
#define GPIO_PC8_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN8)
#define GPIO_PC9_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN9)
#define GPIO_PC10_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN10)
#define GPIO_PC11_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN11)
#define GPIO_PC12_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN12)
#define GPIO_PC13_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN13)
#define GPIO_PC14_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN14)
#define GPIO_PC15_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN15)

#define GPIO_PD0_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN0)
#define GPIO_PD1_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN1)
#define GPIO_PD2_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN2)
#define GPIO_PD3_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN3)
#define GPIO_PD4_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN4)
#define GPIO_PD5_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN5)
#define GPIO_PD6_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN6)
#define GPIO_PD7_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN7)
#define GPIO_PD8_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN8)
#define GPIO_PD9_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN9)
#define GPIO_PD10_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN10)
#define GPIO_PD11_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN11)
#define GPIO_PD12_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN12)
#define GPIO_PD13_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN13)
#define GPIO_PD14_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN14)
#define GPIO_PD15_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN15)

#define GPIO_PE0_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN0)
#define GPIO_PE1_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN1)
#define GPIO_PE2_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN2)
#define GPIO_PE3_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN3)
#define GPIO_PE4_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN4)
#define GPIO_PE5_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN5)
#define GPIO_PE6_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN6)
#define GPIO_PE7_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN7)
#define GPIO_PE8_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN8)
#define GPIO_PE9_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN9)
#define GPIO_PE10_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN10)
#define GPIO_PE11_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN11)
#define GPIO_PE12_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN12)
#define GPIO_PE13_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN13)
#define GPIO_PE14_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN14)
#define GPIO_PE15_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN15)

#define GPIO_PF0_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN0)
#define GPIO_PF1_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN1)
#define GPIO_PF2_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN2)
#define GPIO_PF9_EVENTOUT_0            (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN9)
#define GPIO_PF10_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN10)

#define GPIO_PG10_EVENTOUT_0           (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN10)

/* FDCAN ********************************************************************/

#define GPIO_FDCAN1_RX_1               (GPIO_ALT | GPIO_AF9 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN11)
#define GPIO_FDCAN1_RX_2               (GPIO_ALT | GPIO_AF9 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN8)
#define GPIO_FDCAN1_TX_1               (GPIO_ALT | GPIO_AF9 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN12)
#define GPIO_FDCAN1_TX_2               (GPIO_ALT | GPIO_AF9 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN9)

#define GPIO_FDCAN2_RX_1               (GPIO_ALT | GPIO_AF9 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN5)
#define GPIO_FDCAN2_RX_2               (GPIO_ALT | GPIO_AF9 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN12)
#define GPIO_FDCAN2_TX_1               (GPIO_ALT | GPIO_AF9 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN6)
#define GPIO_FDCAN2_TX_2               (GPIO_ALT | GPIO_AF9 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN13)

#define GPIO_FDCAN3_RX_1               (GPIO_ALT | GPIO_AF11 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN8)
#define GPIO_FDCAN3_RX_2               (GPIO_ALT | GPIO_AF11 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN3)
#define GPIO_FDCAN3_TX_1               (GPIO_ALT | GPIO_AF11 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN15)
#define GPIO_FDCAN3_TX_2               (GPIO_ALT | GPIO_AF11 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN4)

/* FMC/FSMC - Flexible Static Memory Controller *****************************/

#define GPIO_FMC_A0_0                  (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTF | GPIO_PIN10)
#define GPIO_FMC_A16_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN11)
#define GPIO_FMC_A17_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN12)
#define GPIO_FMC_A18_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN13)
#define GPIO_FMC_A19_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN3)
#define GPIO_FMC_A2_0                  (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTF | GPIO_PIN2)
#define GPIO_FMC_A20_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN4)
#define GPIO_FMC_A21_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN5)
#define GPIO_FMC_A22_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN6)
#define GPIO_FMC_A23_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN2)
#define GPIO_FMC_A25_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTF | GPIO_PIN9)
#define GPIO_FMC_CLK_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN3)
#define GPIO_FMC_D0_0                  (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN14)
#define GPIO_FMC_D1_0                  (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN15)
#define GPIO_FMC_D10_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN13)
#define GPIO_FMC_D11_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN14)
#define GPIO_FMC_D12_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN15)
#define GPIO_FMC_D13_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN8)
#define GPIO_FMC_D14_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN9)
#define GPIO_FMC_D15_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN10)
#define GPIO_FMC_D2_0                  (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN0)
#define GPIO_FMC_D3_0                  (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN1)
#define GPIO_FMC_D4_0                  (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN7)
#define GPIO_FMC_D5_0                  (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN8)
#define GPIO_FMC_D6_0                  (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN9)
#define GPIO_FMC_D7_0                  (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN10)
#define GPIO_FMC_D8_0                  (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN11)
#define GPIO_FMC_D9_0                  (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN12)
#define GPIO_FMC_DA0_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN14)
#define GPIO_FMC_DA1_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN15)
#define GPIO_FMC_DA10_0                (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN13)
#define GPIO_FMC_DA11_0                (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN14)
#define GPIO_FMC_DA12_0                (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN15)
#define GPIO_FMC_DA13_0                (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN8)
#define GPIO_FMC_DA14_0                (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN9)
#define GPIO_FMC_DA15_0                (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN10)
#define GPIO_FMC_DA2_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN0)
#define GPIO_FMC_DA3_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN1)
#define GPIO_FMC_DA4_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN7)
#define GPIO_FMC_DA5_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN8)
#define GPIO_FMC_DA6_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN9)
#define GPIO_FMC_DA7_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN10)
#define GPIO_FMC_DA8_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN11)
#define GPIO_FMC_DA9_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN12)
#define GPIO_FMC_NBL0_0                (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN0)
#define GPIO_FMC_NBL1_0                (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTE | GPIO_PIN1)
#define GPIO_FMC_NCE_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN7)
#define GPIO_FMC_NE1_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN7)
#define GPIO_FMC_NL_0                  (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTB | GPIO_PIN7)
#define GPIO_FMC_NOE_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN4)
#define GPIO_FMC_NWAIT_0               (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN6)
#define GPIO_FMC_NWE_0                 (GPIO_ALT | GPIO_PULLUP | GPIO_AF12 | GPIO_PORTD | GPIO_PIN5)

/* HRTIM - High-Resolution Timer ********************************************/

#define GPIO_HRTIM1_CHA1_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_HRTIM1_CHA2_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTA | GPIO_PIN9)
#define GPIO_HRTIM1_CHB1_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_HRTIM1_CHB2_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_HRTIM1_CHC1_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_HRTIM1_CHC2_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_HRTIM1_CHD1_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_HRTIM1_CHD2_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN15)
#define GPIO_HRTIM1_CHE1_0             (GPIO_ALT | GPIO_AF3 | GPIO_PORTC | GPIO_PIN8)
#define GPIO_HRTIM1_CHE2_0             (GPIO_ALT | GPIO_AF3 | GPIO_PORTC | GPIO_PIN9)
#define GPIO_HRTIM1_CHF1_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTC | GPIO_PIN6)
#define GPIO_HRTIM1_CHF2_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTC | GPIO_PIN7)
#define GPIO_HRTIM1_EEV1_0             (GPIO_ALT | GPIO_AF3 | GPIO_PORTC | GPIO_PIN12)
#define GPIO_HRTIM1_EEV10_1            (GPIO_ALT | GPIO_AF13 | GPIO_PORTC | GPIO_PIN5)
#define GPIO_HRTIM1_EEV10_2            (GPIO_ALT | GPIO_AF3 | GPIO_PORTC | GPIO_PIN6)
#define GPIO_HRTIM1_EEV2_0             (GPIO_ALT | GPIO_AF3 | GPIO_PORTC | GPIO_PIN11)
#define GPIO_HRTIM1_EEV3_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN7)
#define GPIO_HRTIM1_EEV4_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN6)
#define GPIO_HRTIM1_EEV5_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_HRTIM1_EEV6_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_HRTIM1_EEV7_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_HRTIM1_EEV8_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_HRTIM1_EEV9_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_HRTIM1_FLT1_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTA | GPIO_PIN12)
#define GPIO_HRTIM1_FLT2_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_HRTIM1_FLT3_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_HRTIM1_FLT4_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN11)
#define GPIO_HRTIM1_FLT5_1             (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN0)
#define GPIO_HRTIM1_FLT5_2             (GPIO_ALT | GPIO_AF3 | GPIO_PORTC | GPIO_PIN7)
#define GPIO_HRTIM1_FLT6_0             (GPIO_ALT | GPIO_AF13 | GPIO_PORTC | GPIO_PIN10)
#define GPIO_HRTIM1_SCIN_1             (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN2)
#define GPIO_HRTIM1_SCIN_2             (GPIO_ALT | GPIO_AF12 | GPIO_PORTB | GPIO_PIN6)
#define GPIO_HRTIM1_SCOUT_1            (GPIO_ALT | GPIO_AF13 | GPIO_PORTB | GPIO_PIN1)
#define GPIO_HRTIM1_SCOUT_2            (GPIO_ALT | GPIO_AF12 | GPIO_PORTB | GPIO_PIN3)

/* I2C **********************************************************************/

#define GPIO_I2C1_SCL_1                (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTA | GPIO_PIN13)
#define GPIO_I2C1_SCL_2                (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTA | GPIO_PIN15)
#define GPIO_I2C1_SCL_3                (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN8)
#define GPIO_I2C1_SDA_1                (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTA | GPIO_PIN14)
#define GPIO_I2C1_SDA_2                (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN7)
#define GPIO_I2C1_SDA_3                (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN9)
#define GPIO_I2C1_SMBA_0               (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN5)
#define GPIO_I2C2_SCL_1                (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTA | GPIO_PIN9)
#define GPIO_I2C2_SCL_2                (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTC | GPIO_PIN4)
#define GPIO_I2C2_SCL_3                (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTF | GPIO_PIN6)
#define GPIO_I2C2_SDA_1                (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTA | GPIO_PIN8)
#define GPIO_I2C2_SDA_2                (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTF | GPIO_PIN0)
#define GPIO_I2C2_SMBA_1               (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN10)
#define GPIO_I2C2_SMBA_2               (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN12)
#define GPIO_I2C2_SMBA_3               (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTF | GPIO_PIN2)
#define GPIO_I2C3_SCL_1                (GPIO_ALT | GPIO_AF2 | GPIO_OPENDRAIN | GPIO_PORTA | GPIO_PIN8)
#define GPIO_I2C3_SCL_2                (GPIO_ALT | GPIO_AF8 | GPIO_OPENDRAIN | GPIO_PORTC | GPIO_PIN8)
#define GPIO_I2C3_SDA_1                (GPIO_ALT | GPIO_AF8 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN5)
#define GPIO_I2C3_SDA_2                (GPIO_ALT | GPIO_AF8 | GPIO_OPENDRAIN | GPIO_PORTC | GPIO_PIN9)
#define GPIO_I2C3_SDA_3                (GPIO_ALT | GPIO_AF8 | GPIO_OPENDRAIN | GPIO_PORTC | GPIO_PIN11)
#define GPIO_I2C3_SMBA_1               (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN9)
#define GPIO_I2C3_SMBA_2               (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN2)
#define GPIO_I2C4_SCL_1                (GPIO_ALT | GPIO_AF3 | GPIO_OPENDRAIN | GPIO_PORTA | GPIO_PIN13)
#define GPIO_I2C4_SCL_2                (GPIO_ALT | GPIO_AF8 | GPIO_OPENDRAIN | GPIO_PORTC | GPIO_PIN6)
#define GPIO_I2C4_SDA_1                (GPIO_ALT | GPIO_AF3 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN7)
#define GPIO_I2C4_SDA_2                (GPIO_ALT | GPIO_AF8 | GPIO_OPENDRAIN | GPIO_PORTC | GPIO_PIN7)
#define GPIO_I2C4_SMBA_1               (GPIO_ALT | GPIO_AF3 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN14)
#define GPIO_I2C4_SMBA_2               (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN11)

/* I2S **********************************************************************/

#define GPIO_I2S_CKIN_1                (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN12)
#define GPIO_I2S_CKIN_2                (GPIO_ALT | GPIO_AF5 | GPIO_PORTC | GPIO_PIN9)

#define GPIO_I2S2_CK_1                 (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_I2S2_CK_2                 (GPIO_ALT | GPIO_AF5 | GPIO_PORTF | GPIO_PIN1)
#define GPIO_I2S2_MCK_1                (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_I2S2_MCK_2                (GPIO_ALT | GPIO_AF6 | GPIO_PORTC | GPIO_PIN6)
#define GPIO_I2S2_SD_1                 (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_I2S2_SD_2                 (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN15)
#define GPIO_I2S2_WS_1                 (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_I2S2_WS_2                 (GPIO_ALT | GPIO_AF5 | GPIO_PORTF | GPIO_PIN0)

#define GPIO_I2S3_CK_1                 (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_I2S3_CK_2                 (GPIO_ALT | GPIO_AF6 | GPIO_PORTC | GPIO_PIN10)
#define GPIO_I2S3_MCK_1                (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN9)
#define GPIO_I2S3_MCK_2                (GPIO_ALT | GPIO_AF6 | GPIO_PORTC | GPIO_PIN7)
#define GPIO_I2S3_SD_1                 (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_I2S3_SD_2                 (GPIO_ALT | GPIO_AF6 | GPIO_PORTC | GPIO_PIN12)
#define GPIO_I2S3_WS_1                 (GPIO_ALT | GPIO_AF6 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_I2S3_WS_2                 (GPIO_ALT | GPIO_AF6 | GPIO_PORTA | GPIO_PIN15)

/* IR - Infrared with TIM16 channel 1 and TIM17 channel 1 *******************/

#define GPIO_IR_OUT_1                  (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN13)
#define GPIO_IR_OUT_2                  (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN9)

/* LPTIM - Low Power Timer **************************************************/

#define GPIO_LPTIM1_ETR_1              (GPIO_ALT | GPIO_AF11 | GPIO_PORTB | GPIO_PIN6)
#define GPIO_LPTIM1_ETR_2              (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN3)
#define GPIO_LPTIM1_IN1_1              (GPIO_ALT | GPIO_AF11 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_LPTIM1_IN1_2              (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN0)
#define GPIO_LPTIM1_IN2_1              (GPIO_ALT | GPIO_AF11 | GPIO_PORTB | GPIO_PIN7)
#define GPIO_LPTIM1_IN2_2              (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN2)
#define GPIO_LPTIM1_OUT_1              (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN14)
#define GPIO_LPTIM1_OUT_2              (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN2)
#define GPIO_LPTIM1_OUT_3              (GPIO_ALT | GPIO_AF1 | GPIO_PORTC | GPIO_PIN1)

/* LPUART - Low-Power Universal Asynchronous Receiver Transmitter ***********/

#define GPIO_LPUART1_CTS_1             (GPIO_ALT | GPIO_AF12 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_LPUART1_CTS_2             (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_LPUART1_DE_1              (GPIO_ALT | GPIO_AF12 | GPIO_PORTB | GPIO_PIN1)
#define GPIO_LPUART1_DE_2              (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_LPUART1_RTS_1             (GPIO_ALT | GPIO_AF12 | GPIO_PORTB | GPIO_PIN1)
#define GPIO_LPUART1_RTS_2             (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_LPUART1_RX_1              (GPIO_ALT | GPIO_AF12 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_LPUART1_RX_2              (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_LPUART1_RX_3              (GPIO_ALT | GPIO_AF8 | GPIO_PORTC | GPIO_PIN0)
#define GPIO_LPUART1_TX_1              (GPIO_ALT | GPIO_AF12 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_LPUART1_TX_2              (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN11)
#define GPIO_LPUART1_TX_3              (GPIO_ALT | GPIO_AF8 | GPIO_PORTC | GPIO_PIN1)

/* JTAG *********************************************************************/

#define GPIO_JTCK_0                    (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN14)
#define GPIO_JTDI_0                    (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_JTDO_0                    (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_JTMS_0                    (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN13)
#define GPIO_NJTRST_0                  (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_SWCLK_0                   (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN14)
#define GPIO_SWDIO_0                   (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN13)

/* OPAMP (Table 200 from Reference Manual) **********************************/

#define GPIO_OPAMP1_VINM0_0            (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN3)
#define GPIO_OPAMP1_VINM1_0            (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN5)
#define GPIO_OPAMP1_VINP0_0            (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN1)
#define GPIO_OPAMP1_VINP1_0            (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN3)
#define GPIO_OPAMP1_VINP2_0            (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN7)
#define GPIO_OPAMP1_VOUT_0             (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN2)

#define GPIO_OPAMP2_VINM0_0            (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN5)
#define GPIO_OPAMP2_VINM1_0            (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN5)
#define GPIO_OPAMP2_VINP0_0            (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN7)
#define GPIO_OPAMP2_VINP1_0            (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN14)
#define GPIO_OPAMP2_VINP2_0            (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN0)
#define GPIO_OPAMP2_VINP3_0            (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN14)
#define GPIO_OPAMP2_VOUT_0             (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN6)

#define GPIO_OPAMP3_VINM0_0            (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN2)
#define GPIO_OPAMP3_VINM1_0            (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN10)
#define GPIO_OPAMP3_VINP0_0            (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN0)
#define GPIO_OPAMP3_VINP1_0            (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN13)
#define GPIO_OPAMP3_VINP2_0            (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN1)
#define GPIO_OPAMP3_VOUT_0             (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN1)

#define GPIO_OPAMP4_VINM0_0            (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN10)
#define GPIO_OPAMP4_VINM1_0            (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN8)

#define GPIO_OPAMP4_VINP0_0            (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN13)
#define GPIO_OPAMP4_VINP1_0            (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN11)
#define GPIO_OPAMP4_VINP2_0            (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN11)
#define GPIO_OPAMP4_VOUT_0             (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN12)

#define GPIO_OPAMP5_VINM0_0            (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN15)
#define GPIO_OPAMP5_VINM1_0            (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN3)
#define GPIO_OPAMP5_VINP0_0            (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN13)
#define GPIO_OPAMP5_VINP1_0            (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN11)
#define GPIO_OPAMP5_VINP2_0            (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN11)
#define GPIO_OPAMP5_VOUT_0             (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN8)

#define GPIO_OPAMP6_VINM0_0            (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN1)
#define GPIO_OPAMP6_VINM1_0            (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN1)
#define GPIO_OPAMP6_VINP0_0            (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN12)
#define GPIO_OPAMP6_VINP1_0            (GPIO_ANALOG | GPIO_PORTD | GPIO_PIN9)
#define GPIO_OPAMP6_VINP2_0            (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN13)
#define GPIO_OPAMP6_VOUT_0             (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN11)

/* QUADSPI ******************************************************************/

#define GPIO_QUADSPI1_BK1_IO0_1        (GPIO_ALT | GPIO_AF10 | GPIO_PORTB | GPIO_PIN1)
#define GPIO_QUADSPI1_BK1_IO0_2        (GPIO_ALT | GPIO_AF10 | GPIO_PORTE | GPIO_PIN12)
#define GPIO_QUADSPI1_BK1_IO1_1        (GPIO_ALT | GPIO_AF10 | GPIO_PORTB | GPIO_PIN0)
#define GPIO_QUADSPI1_BK1_IO1_2        (GPIO_ALT | GPIO_AF10 | GPIO_PORTE | GPIO_PIN13)
#define GPIO_QUADSPI1_BK1_IO1_3        (GPIO_ALT | GPIO_AF10 | GPIO_PORTF | GPIO_PIN9)
#define GPIO_QUADSPI1_BK1_IO2_1        (GPIO_ALT | GPIO_AF10 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_QUADSPI1_BK1_IO2_2        (GPIO_ALT | GPIO_AF10 | GPIO_PORTE | GPIO_PIN14)
#define GPIO_QUADSPI1_BK1_IO3_1        (GPIO_ALT | GPIO_AF10 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_QUADSPI1_BK1_IO3_2        (GPIO_ALT | GPIO_AF10 | GPIO_PORTE | GPIO_PIN15)
#define GPIO_QUADSPI1_BK1_NCS_1        (GPIO_ALT | GPIO_AF10 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_QUADSPI1_BK1_NCS_2        (GPIO_ALT | GPIO_AF10 | GPIO_PORTB | GPIO_PIN11)
#define GPIO_QUADSPI1_BK1_NCS_3        (GPIO_ALT | GPIO_AF10 | GPIO_PORTE | GPIO_PIN11)
#define GPIO_QUADSPI1_BK2_IO0_1        (GPIO_ALT | GPIO_AF10 | GPIO_PORTC | GPIO_PIN1)
#define GPIO_QUADSPI1_BK2_IO0_2        (GPIO_ALT | GPIO_AF10 | GPIO_PORTD | GPIO_PIN4)
#define GPIO_QUADSPI1_BK2_IO1_1        (GPIO_ALT | GPIO_AF10 | GPIO_PORTB | GPIO_PIN2)
#define GPIO_QUADSPI1_BK2_IO1_2        (GPIO_ALT | GPIO_AF10 | GPIO_PORTC | GPIO_PIN2)
#define GPIO_QUADSPI1_BK2_IO1_3        (GPIO_ALT | GPIO_AF10 | GPIO_PORTD | GPIO_PIN5)
#define GPIO_QUADSPI1_BK2_IO2_1        (GPIO_ALT | GPIO_AF10 | GPIO_PORTC | GPIO_PIN3)
#define GPIO_QUADSPI1_BK2_IO2_2        (GPIO_ALT | GPIO_AF10 | GPIO_PORTD | GPIO_PIN6)
#define GPIO_QUADSPI1_BK2_IO3_1        (GPIO_ALT | GPIO_AF10 | GPIO_PORTC | GPIO_PIN4)
#define GPIO_QUADSPI1_BK2_IO3_2        (GPIO_ALT | GPIO_AF10 | GPIO_PORTD | GPIO_PIN7)
#define GPIO_QUADSPI1_BK2_NCS_0        (GPIO_ALT | GPIO_AF10 | GPIO_PORTD | GPIO_PIN3)
#define GPIO_QUADSPI1_CLK_1            (GPIO_ALT | GPIO_AF10 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_QUADSPI1_CLK_2            (GPIO_ALT | GPIO_AF10 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_QUADSPI1_CLK_3            (GPIO_ALT | GPIO_AF10 | GPIO_PORTE | GPIO_PIN10)
#define GPIO_QUADSPI1_CLK_4            (GPIO_ALT | GPIO_AF10 | GPIO_PORTF | GPIO_PIN10)

/* RTC **********************************************************************/

#define GPIO_RTC_OUT2_0                (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN2)
#define GPIO_RTC_REFIN_1               (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_RTC_REFIN_2               (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN15)

#if 0

/* REVISIT: How do you actually enable OUT1, TAMP1, TAMP2, TAMP3, TS? The
 * datasheet (DS12288 Rev 2, page 54) shows OUT1, TS, and TAMP1 as
 * "additional functions" for PC13 on all STM32G474(C-M-Q-R-V)xxx P/Ns, but
 * the alternate function table (on page 72) makes no mention of these.
 * Meanwhile, page 56 shows TAMP2 as an "additional function" but the same
 * conundrum applies (for now). Granted, these are in the "additional
 * function" column, not the "alternate function" column.
 */

#  define GPIO_RTC_OUT1_0              (GPIO_PORTC | GPIO_PIN13)
#  define GPIO_RTC_TS_0                (GPIO_PORTC | GPIO_PIN13)
#  define GPIO_RTC_TAMP1_0             (GPIO_PORTC | GPIO_PIN13)
#  define GPIO_RTC_TAMP2_0             (GPIO_PORTA | GPIO_PIN0)
#  define GPIO_RTC_TAMP3_0             (GPIO_PORTE | GPIO_PIN6)

#endif

/* SAI - Serial Audio Interface *********************************************/

#define GPIO_SAI1_CK1_1                (GPIO_ALT | GPIO_AF3 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_SAI1_CK1_2                (GPIO_ALT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_SAI1_CK1_3                (GPIO_ALT | GPIO_AF3 | GPIO_PORTE | GPIO_PIN2)
#define GPIO_SAI1_CK2_1                (GPIO_ALT | GPIO_AF12 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_SAI1_CK2_2                (GPIO_ALT | GPIO_AF3 | GPIO_PORTE | GPIO_PIN5)
#define GPIO_SAI1_D1_1                 (GPIO_ALT | GPIO_AF12 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_SAI1_D1_2                 (GPIO_ALT | GPIO_AF3 | GPIO_PORTC | GPIO_PIN3)
#define GPIO_SAI1_D1_3                 (GPIO_ALT | GPIO_AF3 | GPIO_PORTD | GPIO_PIN6)
#define GPIO_SAI1_D1_4                 (GPIO_ALT | GPIO_AF3 | GPIO_PORTE | GPIO_PIN6)
#define GPIO_SAI1_D2_1                 (GPIO_ALT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_SAI1_D2_2                 (GPIO_ALT | GPIO_AF3 | GPIO_PORTE | GPIO_PIN4)
#define GPIO_SAI1_D3_1                 (GPIO_ALT | GPIO_AF3 | GPIO_PORTC | GPIO_PIN5)
#define GPIO_SAI1_D3_2                 (GPIO_ALT | GPIO_AF13 | GPIO_PORTF | GPIO_PIN10)
#define GPIO_SAI1_FS_A_1               (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN9)
#define GPIO_SAI1_FS_A_2               (GPIO_ALT | GPIO_AF14 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_SAI1_FS_A_3               (GPIO_ALT | GPIO_AF13 | GPIO_PORTE | GPIO_PIN4)
#define GPIO_SAI1_FS_B_1               (GPIO_ALT | GPIO_AF13 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_SAI1_FS_B_2               (GPIO_ALT | GPIO_AF13 | GPIO_PORTA | GPIO_PIN14)
#define GPIO_SAI1_FS_B_3               (GPIO_ALT | GPIO_AF14 | GPIO_PORTB | GPIO_PIN6)
#define GPIO_SAI1_FS_B_4               (GPIO_ALT | GPIO_AF13 | GPIO_PORTE | GPIO_PIN9)
#define GPIO_SAI1_FS_B_5               (GPIO_ALT | GPIO_AF13 | GPIO_PORTF | GPIO_PIN9)
#define GPIO_SAI1_MCLK_A_1             (GPIO_ALT | GPIO_AF13 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_SAI1_MCLK_A_2             (GPIO_ALT | GPIO_AF14 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_SAI1_MCLK_A_3             (GPIO_ALT | GPIO_AF13 | GPIO_PORTE | GPIO_PIN2)
#define GPIO_SAI1_MCLK_B_1             (GPIO_ALT | GPIO_AF14 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_SAI1_MCLK_B_2             (GPIO_ALT | GPIO_AF13 | GPIO_PORTE | GPIO_PIN10)
#define GPIO_SAI1_SCK_A_1              (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_SAI1_SCK_A_2              (GPIO_ALT | GPIO_AF14 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_SAI1_SCK_A_3              (GPIO_ALT | GPIO_AF13 | GPIO_PORTE | GPIO_PIN5)
#define GPIO_SAI1_SCK_B_1              (GPIO_ALT | GPIO_AF14 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_SAI1_SCK_B_2              (GPIO_ALT | GPIO_AF13 | GPIO_PORTE | GPIO_PIN8)
#define GPIO_SAI1_SD_A_1               (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_SAI1_SD_A_2               (GPIO_ALT | GPIO_AF13 | GPIO_PORTC | GPIO_PIN1)
#define GPIO_SAI1_SD_A_3               (GPIO_ALT | GPIO_AF13 | GPIO_PORTC | GPIO_PIN3)
#define GPIO_SAI1_SD_A_4               (GPIO_ALT | GPIO_AF13 | GPIO_PORTD | GPIO_PIN6)
#define GPIO_SAI1_SD_A_5               (GPIO_ALT | GPIO_AF13 | GPIO_PORTE | GPIO_PIN6)
#define GPIO_SAI1_SD_B_1               (GPIO_ALT | GPIO_AF13 | GPIO_PORTA | GPIO_PIN13)
#define GPIO_SAI1_SD_B_2               (GPIO_ALT | GPIO_AF12 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_SAI1_SD_B_3               (GPIO_ALT | GPIO_AF13 | GPIO_PORTE | GPIO_PIN3)
#define GPIO_SAI1_SD_B_4               (GPIO_ALT | GPIO_AF13 | GPIO_PORTE | GPIO_PIN7)

/* SPI - Serial Peripheral Interface ****************************************/

#define GPIO_SPI1_MISO_1               (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_SPI1_MISO_2               (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_SPI1_MOSI_1               (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_SPI1_MOSI_2               (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_SPI1_NSS_1                (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_SPI1_NSS_2                (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_SPI1_SCK_1                (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN5)
#define GPIO_SPI1_SCK_2                (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN3)

#define GPIO_SPI2_MISO_1               (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_SPI2_MISO_2               (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_SPI2_MOSI_1               (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_SPI2_MOSI_2               (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN15)
#define GPIO_SPI2_NSS_1                (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_SPI2_NSS_2                (GPIO_ALT | GPIO_AF5 | GPIO_PORTF | GPIO_PIN0)
#define GPIO_SPI2_NSS_3                (GPIO_ALT | GPIO_AF5 | GPIO_PORTF | GPIO_PIN0)
#define GPIO_SPI2_SCK_1                (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_SPI2_SCK_2                (GPIO_ALT | GPIO_AF5 | GPIO_PORTF | GPIO_PIN1)
#define GPIO_SPI2_SCK_3                (GPIO_ALT | GPIO_AF5 | GPIO_PORTF | GPIO_PIN9)
#define GPIO_SPI2_SCK_4                (GPIO_ALT | GPIO_AF5 | GPIO_PORTF | GPIO_PIN10)

#define GPIO_SPI3_MISO_1               (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_SPI3_MISO_2               (GPIO_ALT | GPIO_AF6 | GPIO_PORTC | GPIO_PIN11)
#define GPIO_SPI3_MOSI_1               (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_SPI3_MOSI_2               (GPIO_ALT | GPIO_AF6 | GPIO_PORTC | GPIO_PIN12)
#define GPIO_SPI3_NSS_1                (GPIO_ALT | GPIO_AF6 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_SPI3_NSS_2                (GPIO_ALT | GPIO_AF6 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_SPI3_SCK_1                (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_SPI3_SCK_2                (GPIO_ALT | GPIO_AF6 | GPIO_PORTC | GPIO_PIN10)

#define GPIO_SPI4_MISO_1               (GPIO_ALT | GPIO_AF5 | GPIO_PORTE | GPIO_PIN5)
#define GPIO_SPI4_MISO_2               (GPIO_ALT | GPIO_AF5 | GPIO_PORTE | GPIO_PIN13)
#define GPIO_SPI4_MOSI_1               (GPIO_ALT | GPIO_AF5 | GPIO_PORTE | GPIO_PIN6)
#define GPIO_SPI4_MOSI_2               (GPIO_ALT | GPIO_AF5 | GPIO_PORTE | GPIO_PIN14)
#define GPIO_SPI4_NSS_1                (GPIO_ALT | GPIO_AF5 | GPIO_PORTE | GPIO_PIN3)
#define GPIO_SPI4_NSS_2                (GPIO_ALT | GPIO_AF5 | GPIO_PORTE | GPIO_PIN4)
#define GPIO_SPI4_NSS_3                (GPIO_ALT | GPIO_AF5 | GPIO_PORTE | GPIO_PIN11)
#define GPIO_SPI4_SCK_1                (GPIO_ALT | GPIO_AF5 | GPIO_PORTE | GPIO_PIN2)
#define GPIO_SPI4_SCK_2                (GPIO_ALT | GPIO_AF5 | GPIO_PORTE | GPIO_PIN12)

/* TIM - Timers *************************************************************/

#define GPIO_TIM1_BKIN2_1              (GPIO_ALT | GPIO_AF12 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_TIM1_BKIN2_2              (GPIO_ALT | GPIO_AF6 | GPIO_PORTC | GPIO_PIN3)
#define GPIO_TIM1_BKIN2_3              (GPIO_ALT | GPIO_AF6 | GPIO_PORTE | GPIO_PIN14)
#define GPIO_TIM1_BKIN_1               (GPIO_ALT | GPIO_AF6 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM1_BKIN_2               (GPIO_ALT | GPIO_AF6 | GPIO_PORTA | GPIO_PIN14)
#define GPIO_TIM1_BKIN_3               (GPIO_ALT | GPIO_AF9 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_TIM1_BKIN_4               (GPIO_ALT | GPIO_AF12 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_TIM1_BKIN_5               (GPIO_ALT | GPIO_AF12 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_TIM1_BKIN_6               (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_TIM1_BKIN_7               (GPIO_ALT | GPIO_AF2 | GPIO_PORTC | GPIO_PIN13)
#define GPIO_TIM1_BKIN_8               (GPIO_ALT | GPIO_AF2 | GPIO_PORTE | GPIO_PIN15)
#define GPIO_TIM1_CH1IN_1              (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN8)
#define GPIO_TIM1_CH1IN_2              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN0)
#define GPIO_TIM1_CH1IN_3              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN9)
#define GPIO_TIM1_CH1NIN_1             (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM1_CH1NIN_2             (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN11)
#define GPIO_TIM1_CH1NIN_3             (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN13)
#define GPIO_TIM1_CH1NIN_4             (GPIO_ALT | GPIO_AF4 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN13)
#define GPIO_TIM1_CH1NIN_5             (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN8)
#define GPIO_TIM1_CH1NOUT_1            (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM1_CH1NOUT_2            (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN11)
#define GPIO_TIM1_CH1NOUT_3            (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN13)
#define GPIO_TIM1_CH1NOUT_4            (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN13)
#define GPIO_TIM1_CH1NOUT_5            (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN8)
#define GPIO_TIM1_CH1OUT_1             (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN8)
#define GPIO_TIM1_CH1OUT_2             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN0)
#define GPIO_TIM1_CH1OUT_3             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN9)
#define GPIO_TIM1_CH2IN_1              (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN9)
#define GPIO_TIM1_CH2IN_2              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN1)
#define GPIO_TIM1_CH2IN_3              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN11)
#define GPIO_TIM1_CH2NIN_1             (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN12)
#define GPIO_TIM1_CH2NIN_2             (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN0)
#define GPIO_TIM1_CH2NIN_3             (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN14)
#define GPIO_TIM1_CH2NIN_4             (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN10)
#define GPIO_TIM1_CH2NOUT_1            (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN12)
#define GPIO_TIM1_CH2NOUT_2            (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN0)
#define GPIO_TIM1_CH2NOUT_3            (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN14)
#define GPIO_TIM1_CH2NOUT_4            (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN10)
#define GPIO_TIM1_CH2OUT_1             (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN9)
#define GPIO_TIM1_CH2OUT_2             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN1)
#define GPIO_TIM1_CH2OUT_3             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN11)
#define GPIO_TIM1_CH3IN_1              (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN10)
#define GPIO_TIM1_CH3IN_2              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN2)
#define GPIO_TIM1_CH3IN_3              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN13)
#define GPIO_TIM1_CH3NIN_1             (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN1)
#define GPIO_TIM1_CH3NIN_2             (GPIO_ALT | GPIO_AF12 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN9)
#define GPIO_TIM1_CH3NIN_3             (GPIO_ALT | GPIO_AF4 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN15)
#define GPIO_TIM1_CH3NIN_4             (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN12)
#define GPIO_TIM1_CH3NIN_5             (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTF | GPIO_PIN0)
#define GPIO_TIM1_CH3NOUT_1            (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN1)
#define GPIO_TIM1_CH3NOUT_2            (GPIO_ALT | GPIO_AF12 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN9)
#define GPIO_TIM1_CH3NOUT_3            (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN15)
#define GPIO_TIM1_CH3NOUT_4            (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN12)
#define GPIO_TIM1_CH3NOUT_5            (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTF | GPIO_PIN0)
#define GPIO_TIM1_CH3OUT_1             (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN10)
#define GPIO_TIM1_CH3OUT_2             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN2)
#define GPIO_TIM1_CH3OUT_3             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN13)
#define GPIO_TIM1_CH4IN_1              (GPIO_ALT | GPIO_AF11 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN11)
#define GPIO_TIM1_CH4IN_2              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN3)
#define GPIO_TIM1_CH4IN_3              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN14)
#define GPIO_TIM1_CH4NIN_0             (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN5)
#define GPIO_TIM1_CH4NIN_1             (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN5)
#define GPIO_TIM1_CH4NIN_2             (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN15)
#define GPIO_TIM1_CH4NOUT_1            (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN5)
#define GPIO_TIM1_CH4NOUT_2            (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN15)
#define GPIO_TIM1_CH4OUT_1             (GPIO_ALT | GPIO_AF11 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN11)
#define GPIO_TIM1_CH4OUT_2             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN3)
#define GPIO_TIM1_CH4OUT_3             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN14)
#define GPIO_TIM1_ETR_1                (GPIO_ALT | GPIO_AF11 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN12)
#define GPIO_TIM1_ETR_2                (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN4)
#define GPIO_TIM1_ETR_3                (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN7)

#define GPIO_TIM2_CH1IN_1              (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN0)
#define GPIO_TIM2_CH1IN_2              (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN5)
#define GPIO_TIM2_CH1IN_3              (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN15)
#define GPIO_TIM2_CH1IN_4              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTD | GPIO_PIN3)
#define GPIO_TIM2_CH1OUT_1             (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN0)
#define GPIO_TIM2_CH1OUT_2             (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN5)
#define GPIO_TIM2_CH1OUT_3             (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN15)
#define GPIO_TIM2_CH1OUT_4             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN3)
#define GPIO_TIM2_CH2IN_1              (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN1)
#define GPIO_TIM2_CH2IN_2              (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN3)
#define GPIO_TIM2_CH2IN_3              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTD | GPIO_PIN4)
#define GPIO_TIM2_CH2OUT_1             (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN1)
#define GPIO_TIM2_CH2OUT_2             (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN3)
#define GPIO_TIM2_CH2OUT_3             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN4)
#define GPIO_TIM2_CH3IN_1              (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM2_CH3IN_2              (GPIO_ALT | GPIO_AF10 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN9)
#define GPIO_TIM2_CH3IN_3              (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN10)
#define GPIO_TIM2_CH3IN_4              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTD | GPIO_PIN7)
#define GPIO_TIM2_CH3OUT_1             (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM2_CH3OUT_2             (GPIO_ALT | GPIO_AF10 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN9)
#define GPIO_TIM2_CH3OUT_3             (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN10)
#define GPIO_TIM2_CH3OUT_4             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN7)
#define GPIO_TIM2_CH4IN_1              (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN3)
#define GPIO_TIM2_CH4IN_2              (GPIO_ALT | GPIO_AF10 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN10)
#define GPIO_TIM2_CH4IN_3              (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN11)
#define GPIO_TIM2_CH4IN_4              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTD | GPIO_PIN6)
#define GPIO_TIM2_CH4OUT_1             (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN3)
#define GPIO_TIM2_CH4OUT_2             (GPIO_ALT | GPIO_AF10 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN10)
#define GPIO_TIM2_CH4OUT_3             (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN11)
#define GPIO_TIM2_CH4OUT_4             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN6)
#define GPIO_TIM2_ETR_1                (GPIO_ALT | GPIO_AF14 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN0)
#define GPIO_TIM2_ETR_2                (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN5)
#define GPIO_TIM2_ETR_3                (GPIO_ALT | GPIO_AF14 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN15)
#define GPIO_TIM2_ETR_4                (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTD | GPIO_PIN3)

#define GPIO_TIM3_CH1IN_1              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM3_CH1IN_2              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN4)
#define GPIO_TIM3_CH1IN_3              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN6)
#define GPIO_TIM3_CH1IN_4              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN2)
#define GPIO_TIM3_CH1OUT_1             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM3_CH1OUT_2             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN4)
#define GPIO_TIM3_CH1OUT_3             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN6)
#define GPIO_TIM3_CH1OUT_4             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN2)
#define GPIO_TIM3_CH2IN_1              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN4)
#define GPIO_TIM3_CH2IN_2              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM3_CH2IN_3              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN5)
#define GPIO_TIM3_CH2IN_4              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN7)
#define GPIO_TIM3_CH2IN_5              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN3)
#define GPIO_TIM3_CH2OUT_1             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN4)
#define GPIO_TIM3_CH2OUT_2             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM3_CH2OUT_3             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN5)
#define GPIO_TIM3_CH2OUT_4             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN7)
#define GPIO_TIM3_CH2OUT_5             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN3)
#define GPIO_TIM3_CH3IN_1              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN0)
#define GPIO_TIM3_CH3IN_2              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN8)
#define GPIO_TIM3_CH3IN_3              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN4)
#define GPIO_TIM3_CH3OUT_1             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN0)
#define GPIO_TIM3_CH3OUT_2             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN8)
#define GPIO_TIM3_CH3OUT_3             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN4)
#define GPIO_TIM3_CH4IN_1              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN1)
#define GPIO_TIM3_CH4IN_2              (GPIO_ALT | GPIO_AF10 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN7)
#define GPIO_TIM3_CH4IN_3              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN9)
#define GPIO_TIM3_CH4IN_4              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN5)
#define GPIO_TIM3_CH4OUT_1             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN1)
#define GPIO_TIM3_CH4OUT_2             (GPIO_ALT | GPIO_AF10 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN7)
#define GPIO_TIM3_CH4OUT_3             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN9)
#define GPIO_TIM3_CH4OUT_4             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN5)
#define GPIO_TIM3_ETR_1                (GPIO_ALT | GPIO_AF10 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN3)
#define GPIO_TIM3_ETR_2                (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTD | GPIO_PIN2)

#define GPIO_TIM4_CH1IN_1              (GPIO_ALT | GPIO_AF10 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN11)
#define GPIO_TIM4_CH1IN_2              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN6)
#define GPIO_TIM4_CH1IN_3              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTD | GPIO_PIN12)
#define GPIO_TIM4_CH1OUT_1             (GPIO_ALT | GPIO_AF10 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN11)
#define GPIO_TIM4_CH1OUT_2             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN6)
#define GPIO_TIM4_CH1OUT_3             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN12)
#define GPIO_TIM4_CH2IN_1              (GPIO_ALT | GPIO_AF10 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN12)
#define GPIO_TIM4_CH2IN_2              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN7)
#define GPIO_TIM4_CH2IN_3              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTD | GPIO_PIN13)
#define GPIO_TIM4_CH2OUT_1             (GPIO_ALT | GPIO_AF10 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN12)
#define GPIO_TIM4_CH2OUT_2             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN7)
#define GPIO_TIM4_CH2OUT_3             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN13)
#define GPIO_TIM4_CH3IN_1              (GPIO_ALT | GPIO_AF10 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN13)
#define GPIO_TIM4_CH3IN_2              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN8)
#define GPIO_TIM4_CH3IN_3              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTD | GPIO_PIN14)
#define GPIO_TIM4_CH3OUT_1             (GPIO_ALT | GPIO_AF10 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN13)
#define GPIO_TIM4_CH3OUT_2             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN8)
#define GPIO_TIM4_CH3OUT_3             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN14)
#define GPIO_TIM4_CH4IN_1              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN9)
#define GPIO_TIM4_CH4IN_2              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTD | GPIO_PIN15)
#define GPIO_TIM4_CH4OUT_1             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN9)
#define GPIO_TIM4_CH4OUT_2             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN15)
#define GPIO_TIM4_ETR_1                (GPIO_ALT | GPIO_AF10 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN8)
#define GPIO_TIM4_ETR_2                (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN3)
#define GPIO_TIM4_ETR_3                (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN0)

#define GPIO_TIM5_CH1IN_1              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN0)
#define GPIO_TIM5_CH1IN_2              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN2)
#define GPIO_TIM5_CH1OUT_1             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN0)
#define GPIO_TIM5_CH1OUT_2             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN2)
#define GPIO_TIM5_CH2IN_1              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN1)
#define GPIO_TIM5_CH2IN_2              (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN12)
#define GPIO_TIM5_CH2OUT_1             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN1)
#define GPIO_TIM5_CH2OUT_2             (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN12)
#define GPIO_TIM5_CH3IN_1              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM5_CH3IN_2              (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN8)
#define GPIO_TIM5_CH3OUT_1             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM5_CH3OUT_2             (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN8)
#define GPIO_TIM5_CH4IN_1              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN3)
#define GPIO_TIM5_CH4IN_2              (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN9)
#define GPIO_TIM5_CH4IN_3              (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTF | GPIO_PIN9)
#define GPIO_TIM5_CH4OUT_1             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN3)
#define GPIO_TIM5_CH4OUT_2             (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN9)
#define GPIO_TIM5_CH4OUT_3             (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTF | GPIO_PIN9)
#define GPIO_TIM5_ETR_1                (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN12)
#define GPIO_TIM5_ETR_2                (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTD | GPIO_PIN11)

#define GPIO_TIM8_BKIN2_1              (GPIO_ALT | GPIO_AF10 | GPIO_PORTB | GPIO_PIN6)
#define GPIO_TIM8_BKIN2_2              (GPIO_ALT | GPIO_AF6 | GPIO_PORTC | GPIO_PIN9)
#define GPIO_TIM8_BKIN2_3              (GPIO_ALT | GPIO_AF6 | GPIO_PORTD | GPIO_PIN1)
#define GPIO_TIM8_BKIN_1               (GPIO_ALT | GPIO_AF9 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_TIM8_BKIN_2               (GPIO_ALT | GPIO_AF4 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM8_BKIN_3               (GPIO_ALT | GPIO_AF11 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_TIM8_BKIN_4               (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN7)
#define GPIO_TIM8_BKIN_5               (GPIO_ALT | GPIO_AF4 | GPIO_PORTD | GPIO_PIN2)
#define GPIO_TIM8_CH1IN_1              (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN15)
#define GPIO_TIM8_CH1IN_2              (GPIO_ALT | GPIO_AF5 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN6)
#define GPIO_TIM8_CH1IN_3              (GPIO_ALT | GPIO_AF4 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN6)
#define GPIO_TIM8_CH1NIN_1             (GPIO_ALT | GPIO_AF4 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM8_CH1NIN_2             (GPIO_ALT | GPIO_AF4 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN3)
#define GPIO_TIM8_CH1NIN_3             (GPIO_ALT | GPIO_AF4 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN10)
#define GPIO_TIM8_CH1NOUT_1            (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM8_CH1NOUT_2            (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN3)
#define GPIO_TIM8_CH1NOUT_3            (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN10)
#define GPIO_TIM8_CH1OUT_1             (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN15)
#define GPIO_TIM8_CH1OUT_2             (GPIO_ALT | GPIO_AF5 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN6)
#define GPIO_TIM8_CH1OUT_3             (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN6)
#define GPIO_TIM8_CH2IN_1              (GPIO_ALT | GPIO_AF5 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN14)
#define GPIO_TIM8_CH2IN_2              (GPIO_ALT | GPIO_AF10 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN8)
#define GPIO_TIM8_CH2IN_3              (GPIO_ALT | GPIO_AF4 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN7)
#define GPIO_TIM8_CH2NIN_1             (GPIO_ALT | GPIO_AF4 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN0)
#define GPIO_TIM8_CH2NIN_2             (GPIO_ALT | GPIO_AF4 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN4)
#define GPIO_TIM8_CH2NIN_3             (GPIO_ALT | GPIO_AF4 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN11)
#define GPIO_TIM8_CH2NOUT_1            (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN0)
#define GPIO_TIM8_CH2NOUT_2            (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN4)
#define GPIO_TIM8_CH2NOUT_3            (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN11)
#define GPIO_TIM8_CH2OUT_1             (GPIO_ALT | GPIO_AF5 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN14)
#define GPIO_TIM8_CH2OUT_2             (GPIO_ALT | GPIO_AF10 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN8)
#define GPIO_TIM8_CH2OUT_3             (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN7)
#define GPIO_TIM8_CH3IN_1              (GPIO_ALT | GPIO_AF10 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN9)
#define GPIO_TIM8_CH3IN_2              (GPIO_ALT | GPIO_AF4 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN8)
#define GPIO_TIM8_CH3NIN_1             (GPIO_ALT | GPIO_AF4 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN1)
#define GPIO_TIM8_CH3NIN_2             (GPIO_ALT | GPIO_AF3 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN5)
#define GPIO_TIM8_CH3NIN_3             (GPIO_ALT | GPIO_AF4 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN12)
#define GPIO_TIM8_CH3NOUT_1            (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN1)
#define GPIO_TIM8_CH3NOUT_2            (GPIO_ALT | GPIO_AF3 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN5)
#define GPIO_TIM8_CH3NOUT_3            (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN12)
#define GPIO_TIM8_CH3OUT_1             (GPIO_ALT | GPIO_AF10 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN9)
#define GPIO_TIM8_CH3OUT_2             (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN8)
#define GPIO_TIM8_CH4IN_1              (GPIO_ALT | GPIO_AF4 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN9)
#define GPIO_TIM8_CH4IN_2              (GPIO_ALT | GPIO_AF4 | GPIO_FLOAT | GPIO_PORTD | GPIO_PIN1)
#define GPIO_TIM8_CH4NIN_1             (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN13)
#define GPIO_TIM8_CH4NIN_2             (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTD | GPIO_PIN0)
#define GPIO_TIM8_CH4NOUT_1            (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN13)
#define GPIO_TIM8_CH4NOUT_2            (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN0)
#define GPIO_TIM8_CH4OUT_1             (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN9)
#define GPIO_TIM8_CH4OUT_2             (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN1)
#define GPIO_TIM8_ETR_1                (GPIO_ALT | GPIO_AF10 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN0)
#define GPIO_TIM8_ETR_2                (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN6)

#define GPIO_TIM15_BKIN_1              (GPIO_ALT | GPIO_AF9 | GPIO_PORTA | GPIO_PIN9)
#define GPIO_TIM15_BKIN_2              (GPIO_ALT | GPIO_AF2 | GPIO_PORTC | GPIO_PIN5)
#define GPIO_TIM15_CH1IN_1             (GPIO_ALT | GPIO_AF9 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM15_CH1IN_2             (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN14)
#define GPIO_TIM15_CH1IN_3             (GPIO_ALT | GPIO_AF3 | GPIO_FLOAT | GPIO_PORTF | GPIO_PIN9)
#define GPIO_TIM15_CH1NIN_1            (GPIO_ALT | GPIO_AF9 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN1)
#define GPIO_TIM15_CH1NIN_2            (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN15)
#define GPIO_TIM15_CH1NOUT_1           (GPIO_ALT | GPIO_AF9 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN1)
#define GPIO_TIM15_CH1NOUT_2           (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN15)
#define GPIO_TIM15_CH1OUT_1            (GPIO_ALT | GPIO_AF9 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM15_CH1OUT_2            (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN14)
#define GPIO_TIM15_CH1OUT_3            (GPIO_ALT | GPIO_AF3 | GPIO_PUSHPULL | GPIO_PORTF | GPIO_PIN9)
#define GPIO_TIM15_CH2IN_1             (GPIO_ALT | GPIO_AF9 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN3)
#define GPIO_TIM15_CH2IN_2             (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN15)
#define GPIO_TIM15_CH2IN_3             (GPIO_ALT | GPIO_AF3 | GPIO_FLOAT | GPIO_PORTF | GPIO_PIN10)
#define GPIO_TIM15_CH2OUT_1            (GPIO_ALT | GPIO_AF9 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN3)
#define GPIO_TIM15_CH2OUT_2            (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN15)
#define GPIO_TIM15_CH2OUT_3            (GPIO_ALT | GPIO_AF3 | GPIO_PUSHPULL | GPIO_PORTF | GPIO_PIN10)

#define GPIO_TIM16_BKIN_0              (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_TIM16_CH1IN_1             (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM16_CH1IN_2             (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN12)
#define GPIO_TIM16_CH1IN_3             (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN4)
#define GPIO_TIM16_CH1IN_4             (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN8)
#define GPIO_TIM16_CH1IN_5             (GPIO_ALT | GPIO_AF4 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN0)
#define GPIO_TIM16_CH1NIN_1            (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN13)
#define GPIO_TIM16_CH1NIN_2            (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN6)
#define GPIO_TIM16_CH1NOUT_1           (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN13)
#define GPIO_TIM16_CH1NOUT_2           (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN6)
#define GPIO_TIM16_CH1OUT_1            (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM16_CH1OUT_2            (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN12)
#define GPIO_TIM16_CH1OUT_3            (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN4)
#define GPIO_TIM16_CH1OUT_4            (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN8)
#define GPIO_TIM16_CH1OUT_5            (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN0)

#define GPIO_TIM17_BKIN_1              (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_TIM17_BKIN_2              (GPIO_ALT | GPIO_AF10 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_TIM17_CH1IN_1             (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM17_CH1IN_2             (GPIO_ALT | GPIO_AF10 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN5)
#define GPIO_TIM17_CH1IN_3             (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN9)
#define GPIO_TIM17_CH1IN_4             (GPIO_ALT | GPIO_AF4 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN1)
#define GPIO_TIM17_CH1NIN_0            (GPIO_ALT | GPIO_AF1 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN7)
#define GPIO_TIM17_CH1NOUT_0           (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN7)
#define GPIO_TIM17_CH1OUT_1            (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM17_CH1OUT_2            (GPIO_ALT | GPIO_AF10 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN5)
#define GPIO_TIM17_CH1OUT_3            (GPIO_ALT | GPIO_AF1 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN9)
#define GPIO_TIM17_CH1OUT_4            (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN1)

#define GPIO_TIM20_BKIN_0              (GPIO_ALT | GPIO_AF2 | GPIO_PORTF | GPIO_PIN9)
#define GPIO_TIM20_BKIN2_0             (GPIO_ALT | GPIO_AF2 | GPIO_PORTF | GPIO_PIN10)
#define GPIO_TIM20_CH1IN_1             (GPIO_ALT | GPIO_AF3 | GPIO_FLOAT | GPIO_PORTB | GPIO_PIN2)
#define GPIO_TIM20_CH1IN_2             (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN2)
#define GPIO_TIM20_CH1NIN_0            (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN4)
#define GPIO_TIM20_CH1NOUT_0           (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN4)
#define GPIO_TIM20_CH1OUT_1            (GPIO_ALT | GPIO_AF3 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN2)
#define GPIO_TIM20_CH1OUT_2            (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN2)
#define GPIO_TIM20_CH2IN_1             (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN2)
#define GPIO_TIM20_CH2IN_2             (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN3)
#define GPIO_TIM20_CH2NIN_0            (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN5)
#define GPIO_TIM20_CH2NOUT_0           (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN5)
#define GPIO_TIM20_CH2OUT_1            (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN2)
#define GPIO_TIM20_CH2OUT_2            (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN3)
#define GPIO_TIM20_CH3IN_1             (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTC | GPIO_PIN8)
#define GPIO_TIM20_CH3IN_2             (GPIO_ALT | GPIO_AF2 | GPIO_FLOAT | GPIO_PORTF | GPIO_PIN2)
#define GPIO_TIM20_CH3NIN_0            (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN6)
#define GPIO_TIM20_CH3NOUT_0           (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN6)
#define GPIO_TIM20_CH3OUT_1            (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN8)
#define GPIO_TIM20_CH3OUT_2            (GPIO_ALT | GPIO_AF2 | GPIO_PUSHPULL | GPIO_PORTF | GPIO_PIN2)
#define GPIO_TIM20_CH4IN_0             (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN1)
#define GPIO_TIM20_CH4NIN_0            (GPIO_ALT | GPIO_AF3 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN0)
#define GPIO_TIM20_CH4NOUT_0           (GPIO_ALT | GPIO_AF3 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN0)
#define GPIO_TIM20_CH4OUT_0            (GPIO_ALT | GPIO_AF6 | GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN1)
#define GPIO_TIM20_ETR_0               (GPIO_ALT | GPIO_AF6 | GPIO_FLOAT | GPIO_PORTE | GPIO_PIN0)

/* UARTs/USARTs *************************************************************/

#define GPIO_USART1_CK_0               (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_USART1_CTS_0              (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_USART1_DE_0               (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN12)
#define GPIO_USART1_NSS_0              (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_USART1_RTS_0              (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN12)
#define GPIO_USART1_RX_1               (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_USART1_RX_2               (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN7)
#define GPIO_USART1_RX_3               (GPIO_ALT | GPIO_AF7 | GPIO_PORTC | GPIO_PIN5)
#define GPIO_USART1_RX_4               (GPIO_ALT | GPIO_AF7 | GPIO_PORTE | GPIO_PIN1)
#define GPIO_USART1_TX_1               (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN9)
#define GPIO_USART1_TX_2               (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN6)
#define GPIO_USART1_TX_3               (GPIO_ALT | GPIO_AF7 | GPIO_PORTC | GPIO_PIN4)
#define GPIO_USART1_TX_4               (GPIO_ALT | GPIO_AF7 | GPIO_PORTE | GPIO_PIN0)

#define GPIO_USART2_CK_1               (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_USART2_CK_2               (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_USART2_CK_3               (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN7)
#define GPIO_USART2_CTS_1              (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_USART2_CTS_2              (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN3)
#define GPIO_USART2_DE_1               (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_USART2_DE_2               (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN4)
#define GPIO_USART2_NSS_1              (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_USART2_NSS_2              (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN3)
#define GPIO_USART2_RTS_1              (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_USART2_RTS_2              (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN4)
#define GPIO_USART2_RX_1               (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_USART2_RX_2               (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_USART2_RX_3               (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_USART2_RX_4               (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN6)
#define GPIO_USART2_TX_1               (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_USART2_TX_2               (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN14)
#define GPIO_USART2_TX_3               (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_USART2_TX_4               (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN5)

#define GPIO_USART3_CK_1               (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_USART3_CK_2               (GPIO_ALT | GPIO_AF7 | GPIO_PORTC | GPIO_PIN12)
#define GPIO_USART3_CK_3               (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN10)
#define GPIO_USART3_CTS_1              (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN13)
#define GPIO_USART3_CTS_2              (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_USART3_CTS_3              (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN11)
#define GPIO_USART3_DE_1               (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_USART3_DE_2               (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN12)
#define GPIO_USART3_NSS_1              (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN13)
#define GPIO_USART3_NSS_2              (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_USART3_NSS_3              (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN11)
#define GPIO_USART3_RTS_1              (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_USART3_RTS_2              (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN12)
#define GPIO_USART3_RX_1               (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_USART3_RX_2               (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN11)
#define GPIO_USART3_RX_3               (GPIO_ALT | GPIO_AF7 | GPIO_PORTC | GPIO_PIN11)
#define GPIO_USART3_RX_4               (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN9)
#define GPIO_USART3_RX_5               (GPIO_ALT | GPIO_AF7 | GPIO_PORTE | GPIO_PIN15)
#define GPIO_USART3_TX_1               (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_USART3_TX_2               (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_USART3_TX_3               (GPIO_ALT | GPIO_AF7 | GPIO_PORTC | GPIO_PIN10)
#define GPIO_USART3_TX_4               (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN8)

#define GPIO_UART4_CTS_0               (GPIO_ALT | GPIO_AF14 | GPIO_PORTB | GPIO_PIN7)
#define GPIO_UART4_DE_0                (GPIO_ALT | GPIO_AF8 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_UART4_RTS_0               (GPIO_ALT | GPIO_AF8 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_UART4_RX_0                (GPIO_ALT | GPIO_AF5 | GPIO_PORTC | GPIO_PIN11)
#define GPIO_UART4_TX_0                (GPIO_ALT | GPIO_AF5 | GPIO_PORTC | GPIO_PIN10)

#define GPIO_UART5_CTS_0               (GPIO_ALT | GPIO_AF14 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_UART5_DE_0                (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_UART5_RTS_0               (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_UART5_RX_0                (GPIO_ALT | GPIO_AF5 | GPIO_PORTD | GPIO_PIN2)
#define GPIO_UART5_TX_0                (GPIO_ALT | GPIO_AF5 | GPIO_PORTC | GPIO_PIN12)

/* USB Device Full Speed ****************************************************/

#endif /* __ARCH_ARM_SRC_STM32_HARDWARE_STM32G4XXV_PINMAP_H */
