/****************************************************************************
 * arch/arm/src/stm32/stm32_adc.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_STM32_STM32_ADC_H
#define __ARCH_ARM_SRC_STM32_STM32_ADC_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

#include "chip.h"

#include "hardware/stm32_adc.h"

#include <nuttx/analog/adc.h>

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Generalized definitions for ADC  *****************************************/

#if defined(HAVE_IP_ADC_V1)
#  define STM32_ADC_DMAREG_OFFSET      STM32_ADC_CR2_OFFSET
#  define ADC_DMAREG_DMA               ADC_CR2_DMA
#  define STM32_ADC_EXTREG_OFFSET      STM32_ADC_CR2_OFFSET
#  define ADC_EXTREG_EXTSEL_MASK       ADC_CR2_EXTSEL_MASK
#  define ADC_EXTREG_EXTSEL_SHIFT      ADC_CR2_EXTSEL_SHIFT
#  define STM32_ADC_JEXTREG_OFFSET     STM32_ADC_CR2_OFFSET
#  define ADC_JEXTREG_JEXTSEL_MASK     ADC_CR2_JEXTSEL_MASK
#  define ADC_EXTREG_JEXTSEL_SHIFT     ADC_CR2_JEXTSEL_SHIFT
#  define STM32_ADC_ISR_OFFSET         STM32_ADC_SR_OFFSET
#  define STM32_ADC_IER_OFFSET         STM32_ADC_CR1_OFFSET
#  ifdef HAVE_BASIC_ADC
#    define ADC_EXTREG_EXTEN_MASK      ADC_CR2_EXTTRIG
#    define ADC_EXTREG_EXTEN_NONE      0
#    define ADC_EXTREG_EXTEN_DEFAULT   ADC_CR2_EXTTRIG
#    define ADC_JEXTREG_JEXTEN_MASK    ADC_CR2_JEXTTRIG
#    define ADC_JEXTREG_JEXTEN_NONE    0
#    define ADC_JEXTREG_JEXTEN_DEFAULT ADC_CR2_JEXTTRIG
#  else
#    define ADC_EXTREG_EXTEN_MASK      ADC_CR2_EXTEN_MASK
#    define ADC_EXTREG_EXTEN_NONE      ADC_CR2_EXTEN_NONE
#    define ADC_EXTREG_EXTEN_DEFAULT   ADC_CR2_EXTEN_RISING
#    define ADC_JEXTREG_JEXTEN_MASK    ADC_CR2_JEXTEN_MASK
#    define ADC_JEXTREG_JEXTEN_NONE    ADC_CR2_JEXTEN_NONE
#    define ADC_JEXTREG_JEXTEN_DEFAULT ADC_CR2_JEXTEN_RISING
#  endif
#elif defined(HAVE_IP_ADC_V2)
#  define STM32_ADC_DMAREG_OFFSET      STM32_ADC_CFGR1_OFFSET
#  define ADC_DMAREG_DMA               ADC_CFGR1_DMAEN
#  define STM32_ADC_EXTREG_OFFSET      STM32_ADC_CFGR1_OFFSET
#  define ADC_EXTREG_EXTSEL_MASK       ADC_CFGR1_EXTSEL_MASK
#  define ADC_EXTREG_EXTSEL_SHIFT      ADC_CFGR1_EXTSEL_SHIFT
#  define ADC_EXTREG_EXTEN_MASK        ADC_CFGR1_EXTEN_MASK
#  define ADC_EXTREG_EXTEN_DEFAULT     ADC_CFGR1_EXTEN_RISING
#  define STM32_ADC_JEXTREG_OFFSET     STM32_ADC_JSQR_OFFSET
#  define ADC_JEXTREG_JEXTSEL_MASK     ADC_JSQR_JEXTSEL_MASK
#  define ADC_EXTREG_JEXTSEL_SHIFT     ADC_JSQR_JEXTSEL_SHIFT
#  define ADC_JEXTREG_JEXTEN_MASK      ADC_JSQR_JEXTEN_MASK
#  define ADC_JEXTREG_JEXTEN_DEFAULT   ADC_JSQR_JEXTEN_RISING
#endif

/* Configuration ************************************************************/

/* Timer devices may be used for different purposes.  One special purpose is
 * to control periodic ADC sampling.  If CONFIG_STM32_TIMn is defined then
 * CONFIG_STM32_TIMn_ADC must also be defined to indicate that timer "n" is
 * intended to be used for that purpose.
 */

/* For the STM32 F1 line, timers 1-4 may be used.
 * For the STM32 F3 line, timers 1-4, 6-8, 15, 20 may be used.
 * For the STM32 F2/F4 lines, timers 1-5 and 8 may be used.
 * For the STM32L15XX line, timers 2-4, 6, 7, 9, 10 may be used.
 */

#ifdef CONFIG_STM32_STM32L15XX
#  undef CONFIG_STM32_TIM1_ADC
#  undef CONFIG_STM32_TIM1_ADC1
#  undef CONFIG_STM32_TIM1_ADC2
#  undef CONFIG_STM32_TIM1_ADC3
#  undef CONFIG_STM32_TIM1_ADC4
#else
#  ifndef CONFIG_STM32_TIM1
#    undef CONFIG_STM32_TIM1_ADC
#    undef CONFIG_STM32_TIM1_ADC1
#    undef CONFIG_STM32_TIM1_ADC2
#    undef CONFIG_STM32_TIM1_ADC3
#    undef CONFIG_STM32_TIM1_ADC4
#  endif
#endif

#ifndef CONFIG_STM32_TIM2
#  undef CONFIG_STM32_TIM2_ADC
#  undef CONFIG_STM32_TIM2_ADC1
#  undef CONFIG_STM32_TIM2_ADC2
#  undef CONFIG_STM32_TIM2_ADC3
#  undef CONFIG_STM32_TIM2_ADC4
#endif
#ifndef CONFIG_STM32_TIM3
#  undef CONFIG_STM32_TIM3_ADC
#  undef CONFIG_STM32_TIM3_ADC1
#  undef CONFIG_STM32_TIM3_ADC2
#  undef CONFIG_STM32_TIM3_ADC3
#  undef CONFIG_STM32_TIM3_ADC4
#endif
#ifndef CONFIG_STM32_TIM4
#  undef CONFIG_STM32_TIM4_ADC
#  undef CONFIG_STM32_TIM4_ADC1
#  undef CONFIG_STM32_TIM4_ADC2
#  undef CONFIG_STM32_TIM4_ADC3
#  undef CONFIG_STM32_TIM4_ADC4
#endif

#if defined(CONFIG_STM32_STM32F20XX) || defined(CONFIG_STM32_STM32F4XXX)
#  ifndef CONFIG_STM32_TIM5
#    undef CONFIG_STM32_TIM5_ADC
#    undef CONFIG_STM32_TIM5_ADC1
#    undef CONFIG_STM32_TIM5_ADC2
#    undef CONFIG_STM32_TIM5_ADC3
#    undef CONFIG_STM32_TIM5_ADC4
#  endif
#else
#  undef CONFIG_STM32_TIM5_ADC
#  undef CONFIG_STM32_TIM5_ADC1
#  undef CONFIG_STM32_TIM5_ADC2
#  undef CONFIG_STM32_TIM5_ADC3
#  undef CONFIG_STM32_TIM5_ADC4
#endif

#if defined(CONFIG_STM32_STM32F20XX) || defined(CONFIG_STM32_STM32F30XX) || \
    defined(CONFIG_STM32_STM32F4XXX)
#  ifndef CONFIG_STM32_TIM8
#    undef CONFIG_STM32_TIM8_ADC
#    undef CONFIG_STM32_TIM8_ADC1
#    undef CONFIG_STM32_TIM8_ADC2
#    undef CONFIG_STM32_TIM8_ADC3
#    undef CONFIG_STM32_TIM8_ADC4
#  endif
#else
#  undef CONFIG_STM32_TIM8_ADC
#  undef CONFIG_STM32_TIM8_ADC1
#  undef CONFIG_STM32_TIM8_ADC2
#  undef CONFIG_STM32_TIM8_ADC3
#  undef CONFIG_STM32_TIM8_ADC4
#endif

/* Timers 6, 7, 9, 10 used by STM32L15XX family devices. Though there is only
 * ADC presented in specification and in device as well, the ADC1 is used
 * here in code. See definition of the STM32_NADC
 */

#if defined(CONFIG_STM32_STM32L15XX) || defined(CONFIG_STM32_STM32F30XX)
#  ifndef CONFIG_STM32_TIM6
#    undef CONFIG_STM32_TIM6_ADC
#    undef CONFIG_STM32_TIM6_ADC1
#    undef CONFIG_STM32_TIM6_ADC2
#    undef CONFIG_STM32_TIM6_ADC3
#    undef CONFIG_STM32_TIM6_ADC4
#  endif
#  ifndef CONFIG_STM32_TIM7
#    undef CONFIG_STM32_TIM7_ADC
#    undef CONFIG_STM32_TIM7_ADC1
#    undef CONFIG_STM32_TIM7_ADC2
#    undef CONFIG_STM32_TIM7_ADC3
#    undef CONFIG_STM32_TIM7_ADC4
#  endif
#else
#  undef CONFIG_STM32_TIM6_ADC
#  undef CONFIG_STM32_TIM6_ADC1
#  undef CONFIG_STM32_TIM6_ADC2
#  undef CONFIG_STM32_TIM6_ADC3
#  undef CONFIG_STM32_TIM6_ADC4
#  undef CONFIG_STM32_TIM7_ADC
#  undef CONFIG_STM32_TIM7_ADC1
#  undef CONFIG_STM32_TIM7_ADC2
#  undef CONFIG_STM32_TIM7_ADC3
#  undef CONFIG_STM32_TIM7_ADC4
#endif

#if defined(CONFIG_STM32_STM32L15XX)
#  ifndef CONFIG_STM32_TIM9
#    undef CONFIG_STM32_TIM9_ADC
#    undef CONFIG_STM32_TIM9_ADC1
#    undef CONFIG_STM32_TIM9_ADC2
#    undef CONFIG_STM32_TIM9_ADC3
#    undef CONFIG_STM32_TIM9_ADC4
#  endif
#  ifndef CONFIG_STM32_TIM10
#    undef CONFIG_STM32_TIM10_ADC
#    undef CONFIG_STM32_TIM10_ADC1
#    undef CONFIG_STM32_TIM10_ADC2
#    undef CONFIG_STM32_TIM10_ADC3
#    undef CONFIG_STM32_TIM10_ADC4
#  endif
#else
#  undef CONFIG_STM32_TIM9_ADC
#  undef CONFIG_STM32_TIM9_ADC1
#  undef CONFIG_STM32_TIM9_ADC2
#  undef CONFIG_STM32_TIM9_ADC3
#  undef CONFIG_STM32_TIM9_ADC4
#  undef CONFIG_STM32_TIM10_ADC
#  undef CONFIG_STM32_TIM10_ADC1
#  undef CONFIG_STM32_TIM10_ADC2
#  undef CONFIG_STM32_TIM10_ADC3
#  undef CONFIG_STM32_TIM10_ADC4
#endif

/* Timers 6, 7, and 10-14 are not used with the ADC by any supported family
 */

#undef CONFIG_STM32_TIM11_ADC
#undef CONFIG_STM32_TIM11_ADC1
#undef CONFIG_STM32_TIM11_ADC2
#undef CONFIG_STM32_TIM11_ADC3
#undef CONFIG_STM32_TIM11_ADC4
#undef CONFIG_STM32_TIM12_ADC
#undef CONFIG_STM32_TIM12_ADC1
#undef CONFIG_STM32_TIM12_ADC2
#undef CONFIG_STM32_TIM12_ADC3
#undef CONFIG_STM32_TIM12_ADC4
#undef CONFIG_STM32_TIM13_ADC
#undef CONFIG_STM32_TIM13_ADC1
#undef CONFIG_STM32_TIM13_ADC2
#undef CONFIG_STM32_TIM13_ADC3
#undef CONFIG_STM32_TIM13_ADC4
#undef CONFIG_STM32_TIM14_ADC
#undef CONFIG_STM32_TIM14_ADC1
#undef CONFIG_STM32_TIM14_ADC2
#undef CONFIG_STM32_TIM14_ADC3
#undef CONFIG_STM32_TIM14_ADC4

#ifdef CONFIG_STM32_STM32F30XX
#  ifndef CONFIG_STM32_TIM15
#    undef CONFIG_STM32_TIM15_ADC
#    undef CONFIG_STM32_TIM15_ADC1
#    undef CONFIG_STM32_TIM15_ADC2
#    undef CONFIG_STM32_TIM15_ADC3
#    undef CONFIG_STM32_TIM15_ADC4
#  endif
#  ifndef CONFIG_STM32_TIM20
#    undef CONFIG_STM32_TIM20_ADC
#    undef CONFIG_STM32_TIM20_ADC1
#    undef CONFIG_STM32_TIM20_ADC2
#    undef CONFIG_STM32_TIM20_ADC3
#    undef CONFIG_STM32_TIM20_ADC4
#  endif
#else
#  undef CONFIG_STM32_TIM15_ADC
#  undef CONFIG_STM32_TIM15_ADC1
#  undef CONFIG_STM32_TIM15_ADC2
#  undef CONFIG_STM32_TIM15_ADC3
#  undef CONFIG_STM32_TIM15_ADC4
#  undef CONFIG_STM32_TIM20_ADC
#  undef CONFIG_STM32_TIM20_ADC1
#  undef CONFIG_STM32_TIM20_ADC2
#  undef CONFIG_STM32_TIM20_ADC3
#  undef CONFIG_STM32_TIM20_ADC4
#endif

/* Up to 4 ADC interfaces are supported */

#if STM32_NADC < 4
#  undef CONFIG_STM32_ADC4
#endif

#if STM32_NADC < 3
#  undef CONFIG_STM32_ADC3
#endif

#if STM32_NADC < 2
#  undef CONFIG_STM32_ADC2
#endif

#if STM32_NADC < 1
#  undef CONFIG_STM32_ADC1
#endif

#if defined(CONFIG_STM32_ADC1) || defined(CONFIG_STM32_ADC2) || \
    defined(CONFIG_STM32_ADC3) || defined(CONFIG_STM32_ADC4)

/* DMA support */

#undef ADC_HAVE_DMA
#if defined(CONFIG_STM32_ADC1_DMA) || defined(CONFIG_STM32_ADC2_DMA) || \
    defined(CONFIG_STM32_ADC3_DMA) || defined(CONFIG_STM32_ADC4_DMA)
#  define ADC_HAVE_DMA  1
#endif

#ifdef CONFIG_STM32_ADC1_DMA
#  define ADC1_HAVE_DMA 1
#else
#  undef  ADC1_HAVE_DMA
#endif

#ifdef CONFIG_STM32_ADC2_DMA
#  define ADC2_HAVE_DMA 1
#else
#  undef  ADC2_HAVE_DMA
#endif

#ifdef CONFIG_STM32_ADC3_DMA
#  define ADC3_HAVE_DMA 1
#else
#  undef  ADC3_HAVE_DMA
#endif

#ifdef CONFIG_STM32_ADC4_DMA
#  define ADC4_HAVE_DMA 1
#else
#  undef  ADC4_HAVE_DMA
#endif

/* Injected channels support */

#if (defined(CONFIG_STM32_ADC1) && (CONFIG_STM32_ADC1_INJECTED_CHAN > 0)) || \
    (defined(CONFIG_STM32_ADC2) && (CONFIG_STM32_ADC2_INJECTED_CHAN > 0)) || \
    (defined(CONFIG_STM32_ADC3) && (CONFIG_STM32_ADC3_INJECTED_CHAN > 0)) || \
    (defined(CONFIG_STM32_ADC4) && (CONFIG_STM32_ADC4_INJECTED_CHAN > 0))
#  define ADC_HAVE_INJECTED
#endif

/* Timer configuration:  If a timer trigger is specified, then get
 * information about the timer.
 *
 * STM32L15XX-family has only one ADC onboard, thus there is no definition
 * for other 3 ADC's
 */

#if defined(CONFIG_STM32_TIM1_ADC1)
#    define ADC1_HAVE_TIMER           1
#    define ADC1_TIMER_BASE           STM32_TIM1_BASE
#    define ADC1_TIMER_PCLK_FREQUENCY STM32_APB2_TIM1_CLKIN
#elif defined(CONFIG_STM32_TIM2_ADC1)
#    define ADC1_HAVE_TIMER           1
#    define ADC1_TIMER_BASE           STM32_TIM2_BASE
#    define ADC1_TIMER_PCLK_FREQUENCY STM32_APB1_TIM2_CLKIN
#elif defined(CONFIG_STM32_TIM3_ADC1)
#    define ADC1_HAVE_TIMER           1
#    define ADC1_TIMER_BASE           STM32_TIM3_BASE
#    define ADC1_TIMER_PCLK_FREQUENCY STM32_APB1_TIM3_CLKIN
#elif defined(CONFIG_STM32_TIM4_ADC1)
#    define ADC1_HAVE_TIMER           1
#    define ADC1_TIMER_BASE           STM32_TIM4_BASE
#    define ADC1_TIMER_PCLK_FREQUENCY STM32_APB1_TIM4_CLKIN
#elif defined(CONFIG_STM32_TIM5_ADC1)
#    define ADC1_HAVE_TIMER           1
#    define ADC1_TIMER_BASE           STM32_TIM5_BASE
#    define ADC1_TIMER_PCLK_FREQUENCY STM32_APB1_TIM5_CLKIN
#elif defined(CONFIG_STM32_TIM6_ADC1)
#    define ADC1_HAVE_TIMER           1
#    define ADC1_TIMER_BASE           STM32_TIM6_BASE
#    define ADC1_TIMER_PCLK_FREQUENCY STM32_APB1_TIM6_CLKIN
#elif defined(CONFIG_STM32_TIM7_ADC1)
#    define ADC1_HAVE_TIMER           1
#    define ADC1_TIMER_BASE           STM32_TIM7_BASE
#    define ADC1_TIMER_PCLK_FREQUENCY STM32_APB1_TIM7_CLKIN
#elif defined(CONFIG_STM32_TIM8_ADC1)
#    define ADC1_HAVE_TIMER           1
#    define ADC1_TIMER_BASE           STM32_TIM8_BASE
#    define ADC1_TIMER_PCLK_FREQUENCY STM32_APB2_TIM8_CLKIN
#elif defined(CONFIG_STM32_TIM9_ADC1)
#    define ADC1_HAVE_TIMER           1
#    define ADC1_TIMER_BASE           STM32_TIM9_BASE
#    define ADC1_TIMER_PCLK_FREQUENCY STM32_APB2_TIM9_CLKIN
#elif defined(CONFIG_STM32_TIM10_ADC1)
#    define ADC1_HAVE_TIMER           1
#    define ADC1_TIMER_BASE           STM32_TIM10_BASE
#    define ADC1_TIMER_PCLK_FREQUENCY STM32_APB2_TIM10_CLKIN
#elif defined(CONFIG_STM32_TIM15_ADC1)
#    define ADC1_HAVE_TIMER           1
#    define ADC1_TIMER_BASE           STM32_TIM15_BASE
#    define ADC1_TIMER_PCLK_FREQUENCY STM32_APB2_TIM15_CLKIN
#else
#    undef  ADC1_HAVE_TIMER
#endif

#ifdef ADC1_HAVE_TIMER
#  ifndef CONFIG_STM32_ADC1_SAMPLE_FREQUENCY
#    error "CONFIG_STM32_ADC1_SAMPLE_FREQUENCY not defined"
#  endif
#  ifndef CONFIG_STM32_ADC1_TIMTRIG
#    error "CONFIG_STM32_ADC1_TIMTRIG not defined"
#    warning "Values 0:CC1 1:CC2 2:CC3 3:CC4 4:TRGO"
#  endif
#endif

#if defined(CONFIG_STM32_HRTIM_ADC1_TRG1) || defined(CONFIG_STM32_HRTIM_ADC1_TRG2) || \
    defined(CONFIG_STM32_HRTIM_ADC3_TRG3) || defined(CONFIG_STM32_HRTIM_ADC4_TRG4)
#  define ADC1_HAVE_HRTIM
#else
#  undef ADC1_HAVE_HRTIM
#endif

#if defined(CONFIG_STM32_TIM1_ADC2)
#    define ADC2_HAVE_TIMER           1
#    define ADC2_TIMER_BASE           STM32_TIM1_BASE
#    define ADC2_TIMER_PCLK_FREQUENCY STM32_APB2_TIM1_CLKIN
#elif defined(CONFIG_STM32_TIM2_ADC2)
#    define ADC2_HAVE_TIMER           1
#    define ADC2_TIMER_BASE           STM32_TIM2_BASE
#    define ADC2_TIMER_PCLK_FREQUENCY STM32_APB1_TIM2_CLKIN
#elif defined(CONFIG_STM32_TIM3_ADC2)
#    define ADC2_HAVE_TIMER           1
#    define ADC2_TIMER_BASE           STM32_TIM3_BASE
#    define ADC2_TIMER_PCLK_FREQUENCY STM32_APB1_TIM3_CLKIN
#elif defined(CONFIG_STM32_TIM4_ADC2)
#    define ADC2_HAVE_TIMER           1
#    define ADC2_TIMER_BASE           STM32_TIM4_BASE
#    define ADC2_TIMER_PCLK_FREQUENCY STM32_APB1_TIM4_CLKIN
#elif defined(CONFIG_STM32_TIM5_ADC2)
#    define ADC2_HAVE_TIMER           1
#    define ADC2_TIMER_BASE           STM32_TIM5_BASE
#    define ADC2_TIMER_PCLK_FREQUENCY STM32_APB1_TIM5_CLKIN
#elif defined(CONFIG_STM32_TIM6_ADC2)
#    define ADC2_HAVE_TIMER           1
#    define ADC2_TIMER_BASE           STM32_TIM6_BASE
#    define ADC2_TIMER_PCLK_FREQUENCY STM32_APB1_TIM6_CLKIN
#elif defined(CONFIG_STM32_TIM8_ADC2)
#    define ADC2_HAVE_TIMER           1
#    define ADC2_TIMER_BASE           STM32_TIM8_BASE
#    define ADC2_TIMER_PCLK_FREQUENCY STM32_APB2_TIM8_CLKIN
#elif defined(CONFIG_STM32_TIM15_ADC2)
#    define ADC2_HAVE_TIMER           1
#    define ADC2_TIMER_BASE           STM32_TIM15_BASE
#    define ADC2_TIMER_PCLK_FREQUENCY STM32_APB2_TIM15_CLKIN
#else
#    undef  ADC2_HAVE_TIMER
#endif

#ifdef ADC2_HAVE_TIMER
#  ifndef CONFIG_STM32_ADC2_SAMPLE_FREQUENCY
#    error "CONFIG_STM32_ADC2_SAMPLE_FREQUENCY not defined"
#  endif
#  ifndef CONFIG_STM32_ADC2_TIMTRIG
#    error "CONFIG_STM32_ADC2_TIMTRIG not defined"
#    warning "Values 0:CC1 1:CC2 2:CC3 3:CC4 4:TRGO"
#  endif
#endif

#if defined(CONFIG_STM32_HRTIM_ADC2_TRG1) || defined(CONFIG_STM32_HRTIM_ADC2_TRG2) || \
    defined(CONFIG_STM32_HRTIM_ADC2_TRG3) || defined(CONFIG_STM32_HRTIM_ADC2_TRG4)
#  define ADC2_HAVE_HRTIM
#else
#  undef ADC2_HAVE_HRTIM
#endif

#if defined(CONFIG_STM32_TIM1_ADC3)
#    define ADC3_HAVE_TIMER           1
#    define ADC3_TIMER_BASE           STM32_TIM1_BASE
#    define ADC3_TIMER_PCLK_FREQUENCY STM32_APB2_TIM1_CLKIN
#elif defined(CONFIG_STM32_TIM2_ADC3)
#    define ADC3_HAVE_TIMER           1
#    define ADC3_TIMER_BASE           STM32_TIM2_BASE
#    define ADC3_TIMER_PCLK_FREQUENCY STM32_APB1_TIM2_CLKIN
#elif defined(CONFIG_STM32_TIM3_ADC3)
#    define ADC3_HAVE_TIMER           1
#    define ADC3_TIMER_BASE           STM32_TIM3_BASE
#    define ADC3_TIMER_PCLK_FREQUENCY STM32_APB1_TIM3_CLKIN
#elif defined(CONFIG_STM32_TIM4_ADC3)
#    define ADC3_HAVE_TIMER           1
#    define ADC3_TIMER_BASE           STM32_TIM4_BASE
#    define ADC3_TIMER_PCLK_FREQUENCY STM32_APB1_TIM4_CLKIN
#elif defined(CONFIG_STM32_TIM5_ADC3)
#    define ADC3_HAVE_TIMER           1
#    define ADC3_TIMER_BASE           STM32_TIM5_BASE
#    define ADC3_TIMER_PCLK_FREQUENCY STM32_APB1_TIM5_CLKIN
#elif defined(CONFIG_STM32_TIM7_ADC3)
#    define ADC3_HAVE_TIMER           1
#    define ADC3_TIMER_BASE           STM32_TIM7_BASE
#    define ADC3_TIMER_PCLK_FREQUENCY STM32_APB1_TIM7_CLKIN
#elif defined(CONFIG_STM32_TIM8_ADC3)
#    define ADC3_HAVE_TIMER           1
#    define ADC3_TIMER_BASE           STM32_TIM8_BASE
#    define ADC3_TIMER_PCLK_FREQUENCY STM32_APB2_TIM8_CLKIN
#elif defined(CONFIG_STM32_TIM15_ADC3)
#    define ADC3_HAVE_TIMER           1
#    define ADC3_TIMER_BASE           STM32_TIM15_BASE
#    define ADC3_TIMER_PCLK_FREQUENCY STM32_APB2_TIM15_CLKIN
#elif defined(CONFIG_STM32_TIM20_ADC3)
#    define ADC3_HAVE_TIMER           1
#    define ADC3_TIMER_BASE           STM32_TIM20_BASE
#    define ADC3_TIMER_PCLK_FREQUENCY STM32_APB2_TIM20_CLKIN
#else
#    undef  ADC3_HAVE_TIMER
#endif

#ifdef ADC3_HAVE_TIMER
#  ifndef CONFIG_STM32_ADC3_SAMPLE_FREQUENCY
#    error "CONFIG_STM32_ADC3_SAMPLE_FREQUENCY not defined"
#  endif
#  ifndef CONFIG_STM32_ADC3_TIMTRIG
#    error "CONFIG_STM32_ADC3_TIMTRIG not defined"
#    warning "Values 0:CC1 1:CC2 2:CC3 3:CC4 4:TRGO"
#  endif
#endif

#if defined(CONFIG_STM32_TIM1_ADC4)
#    define ADC4_HAVE_TIMER           1
#    define ADC4_TIMER_BASE           STM32_TIM1_BASE
#    define ADC4_TIMER_PCLK_FREQUENCY STM32_APB2_TIM1_CLKIN
#elif defined(CONFIG_STM32_TIM2_ADC4)
#    define ADC4_HAVE_TIMER           1
#    define ADC4_TIMER_BASE           STM32_TIM2_BASE
#    define ADC4_TIMER_PCLK_FREQUENCY STM32_APB1_TIM2_CLKIN
#elif defined(CONFIG_STM32_TIM3_ADC4)
#    define ADC4_HAVE_TIMER           1
#    define ADC4_TIMER_BASE           STM32_TIM3_BASE
#    define ADC4_TIMER_PCLK_FREQUENCY STM32_APB1_TIM3_CLKIN
#elif defined(CONFIG_STM32_TIM4_ADC4)
#    define ADC4_HAVE_TIMER           1
#    define ADC4_TIMER_BASE           STM32_TIM4_BASE
#    define ADC4_TIMER_PCLK_FREQUENCY STM32_APB1_TIM4_CLKIN
#elif defined(CONFIG_STM32_TIM5_ADC4)
#    define ADC4_HAVE_TIMER           1
#    define ADC4_TIMER_BASE           STM32_TIM5_BASE
#    define ADC4_TIMER_PCLK_FREQUENCY STM32_APB1_TIM5_CLKIN
#elif defined(CONFIG_STM32_TIM7_ADC4)
#    define ADC4_HAVE_TIMER           1
#    define ADC4_TIMER_BASE           STM32_TIM7_BASE
#    define ADC4_TIMER_PCLK_FREQUENCY STM32_APB1_TIM7_CLKIN
#elif defined(CONFIG_STM32_TIM8_ADC4)
#    define ADC4_HAVE_TIMER           1
#    define ADC4_TIMER_BASE           STM32_TIM8_BASE
#    define ADC4_TIMER_PCLK_FREQUENCY STM32_APB2_TIM8_CLKIN
#elif defined(CONFIG_STM32_TIM15_ADC4)
#    define ADC4_HAVE_TIMER           1
#    define ADC4_TIMER_BASE           STM32_TIM15_BASE
#    define ADC4_TIMER_PCLK_FREQUENCY STM32_APB2_TIM15_CLKIN
#elif defined(CONFIG_STM32_TIM20_ADC4)
#    define ADC4_HAVE_TIMER           1
#    define ADC4_TIMER_BASE           STM32_TIM20_BASE
#    define ADC4_TIMER_PCLK_FREQUENCY STM32_APB2_TIM20_CLKIN
#else
#    undef  ADC4_HAVE_TIMER
#endif

#ifdef ADC4_HAVE_TIMER
#  ifndef CONFIG_STM32_ADC4_SAMPLE_FREQUENCY
#    error "CONFIG_STM32_ADC4_SAMPLE_FREQUENCY not defined"
#  endif
#  ifndef CONFIG_STM32_ADC4_TIMTRIG
#    error "CONFIG_STM32_ADC4_TIMTRIG not defined"
#    warning "Values 0:CC1 1:CC2 2:CC3 3:CC4 4:TRGO"
#  endif
#endif

#if defined(ADC1_HAVE_TIMER) || defined(ADC2_HAVE_TIMER) || \
    defined(ADC3_HAVE_TIMER) || defined(ADC4_HAVE_TIMER)
#  define ADC_HAVE_TIMER 1
#  if defined(CONFIG_STM32_STM32F10XX) && !defined(CONFIG_STM32_FORCEPOWER)
#    warning "CONFIG_STM32_FORCEPOWER must be defined to enable the timer(s)"
#  endif
#else
#  undef ADC_HAVE_TIMER
#endif

#if defined(ADC1_HAVE_HRTIM) || defined(ADC2_HAVE_HRTIM)
#  define ADC_HAVE_HRTIM
#else
#  undef ADC_HAVE_HRTIM
#endif

/* NOTE:
 * The following assumes that all possible combinations of timers and
 * values are support EXTSEL.  That is not so and it varies from one STM32
 * to another.  But this (wrong) assumptions keeps the logic as simple as
 * possible.  If unsupported combination is used, an error will show up
 * later during compilation although it may be difficult to track it back
 * to this simplification.
 *
 * STM32L15XX-family has only one ADC onboard, thus there is no definition
 * for other 3 ADC's
 */

#if defined(HAVE_IP_ADC_V2)
#  define ADC1_EXTSEL_T1CC1   ADC12_CFGR1_EXTSEL_T1CC1
#  define ADC1_EXTSEL_T1CC2   ADC12_CFGR1_EXTSEL_T1CC2
#  define ADC1_EXTSEL_T1CC3   ADC12_CFGR1_EXTSEL_T1CC3
#  define ADC1_EXTSEL_T1CC4   ADC12_CFGR1_EXTSEL_T1CC4
#  define ADC1_EXTSEL_T1TRGO  ADC12_CFGR1_EXTSEL_T1TRGO
#  define ADC2_EXTSEL_T1CC1   ADC12_CFGR1_EXTSEL_T1CC1
#  define ADC2_EXTSEL_T1CC2   ADC12_CFGR1_EXTSEL_T1CC2
#  define ADC2_EXTSEL_T1CC3   ADC12_CFGR1_EXTSEL_T1CC3
#  define ADC2_EXTSEL_T1CC4   ADC12_CFGR1_EXTSEL_T1CC4
#  define ADC2_EXTSEL_T1TRGO  ADC12_CFGR1_EXTSEL_T1TRGO
#  define ADC3_EXTSEL_T1CC1   ADC34_CFGR1_EXTSEL_T1CC1
#  define ADC3_EXTSEL_T1CC2   ADC34_CFGR1_EXTSEL_T1CC2
#  define ADC3_EXTSEL_T1CC3   ADC34_CFGR1_EXTSEL_T1CC3
#  define ADC3_EXTSEL_T1CC4   ADC34_CFGR1_EXTSEL_T1CC4
#  define ADC3_EXTSEL_T1TRGO  ADC34_CFGR1_EXTSEL_T1TRGO
#  define ADC4_EXTSEL_T1CC1   ADC34_CFGR1_EXTSEL_T1CC1
#  define ADC4_EXTSEL_T1CC2   ADC34_CFGR1_EXTSEL_T1CC2
#  define ADC4_EXTSEL_T1CC3   ADC34_CFGR1_EXTSEL_T1CC3
#  define ADC4_EXTSEL_T1CC4   ADC34_CFGR1_EXTSEL_T1CC4
#  define ADC4_EXTSEL_T1TRGO  ADC34_CFGR1_EXTSEL_T1TRGO
#  define ADC1_EXTSEL_T2CC1   ADC12_CFGR1_EXTSEL_T2CC1
#  define ADC1_EXTSEL_T2CC2   ADC12_CFGR1_EXTSEL_T2CC2
#  define ADC1_EXTSEL_T2CC3   ADC12_CFGR1_EXTSEL_T2CC3
#  define ADC1_EXTSEL_T2CC4   ADC12_CFGR1_EXTSEL_T2CC4
#  define ADC1_EXTSEL_T2TRGO  ADC12_CFGR1_EXTSEL_T2TRGO
#  define ADC2_EXTSEL_T2CC1   ADC12_CFGR1_EXTSEL_T2CC1
#  define ADC2_EXTSEL_T2CC2   ADC12_CFGR1_EXTSEL_T2CC2
#  define ADC2_EXTSEL_T2CC3   ADC12_CFGR1_EXTSEL_T2CC3
#  define ADC2_EXTSEL_T2CC4   ADC12_CFGR1_EXTSEL_T2CC4
#  define ADC2_EXTSEL_T2TRGO  ADC12_CFGR1_EXTSEL_T2TRGO
#  define ADC3_EXTSEL_T2CC1   ADC34_CFGR1_EXTSEL_T2CC1
#  define ADC3_EXTSEL_T2CC2   ADC34_CFGR1_EXTSEL_T2CC2
#  define ADC3_EXTSEL_T2CC3   ADC34_CFGR1_EXTSEL_T2CC3
#  define ADC3_EXTSEL_T2CC4   ADC34_CFGR1_EXTSEL_T2CC4
#  define ADC3_EXTSEL_T2TRGO  ADC34_CFGR1_EXTSEL_T2TRGO
#  define ADC4_EXTSEL_T2CC1   ADC34_CFGR1_EXTSEL_T2CC1
#  define ADC4_EXTSEL_T2CC2   ADC34_CFGR1_EXTSEL_T2CC2
#  define ADC4_EXTSEL_T2CC3   ADC34_CFGR1_EXTSEL_T2CC3
#  define ADC4_EXTSEL_T2CC4   ADC34_CFGR1_EXTSEL_T2CC4
#  define ADC4_EXTSEL_T2TRGO  ADC34_CFGR1_EXTSEL_T2TRGO
#  define ADC1_EXTSEL_T3CC1   ADC12_CFGR1_EXTSEL_T3CC1
#  define ADC1_EXTSEL_T3CC2   ADC12_CFGR1_EXTSEL_T3CC2
#  define ADC1_EXTSEL_T3CC3   ADC12_CFGR1_EXTSEL_T3CC3
#  define ADC1_EXTSEL_T3CC4   ADC12_CFGR1_EXTSEL_T3CC4
#  define ADC1_EXTSEL_T3TRGO  ADC12_CFGR1_EXTSEL_T3TRGO
#  define ADC2_EXTSEL_T3CC1   ADC12_CFGR1_EXTSEL_T3CC1
#  define ADC2_EXTSEL_T3CC2   ADC12_CFGR1_EXTSEL_T3CC2
#  define ADC2_EXTSEL_T3CC3   ADC12_CFGR1_EXTSEL_T3CC3
#  define ADC2_EXTSEL_T3CC4   ADC12_CFGR1_EXTSEL_T3CC4
#  define ADC2_EXTSEL_T3TRGO  ADC12_CFGR1_EXTSEL_T3TRGO
#  define ADC3_EXTSEL_T3CC1   ADC34_CFGR1_EXTSEL_T3CC1
#  define ADC3_EXTSEL_T3CC2   ADC34_CFGR1_EXTSEL_T3CC2
#  define ADC3_EXTSEL_T3CC3   ADC34_CFGR1_EXTSEL_T3CC3
#  define ADC3_EXTSEL_T3CC4   ADC34_CFGR1_EXTSEL_T3CC4
#  define ADC3_EXTSEL_T3TRGO  ADC34_CFGR1_EXTSEL_T3TRGO
#  define ADC4_EXTSEL_T3CC1   ADC34_CFGR1_EXTSEL_T3CC1
#  define ADC4_EXTSEL_T3CC2   ADC34_CFGR1_EXTSEL_T3CC2
#  define ADC4_EXTSEL_T3CC3   ADC34_CFGR1_EXTSEL_T3CC3
#  define ADC4_EXTSEL_T3CC4   ADC34_CFGR1_EXTSEL_T3CC4
#  define ADC4_EXTSEL_T3TRGO  ADC34_CFGR1_EXTSEL_T3TRGO
#  define ADC1_EXTSEL_T4CC1   ADC12_CFGR1_EXTSEL_T4CC1
#  define ADC1_EXTSEL_T4CC2   ADC12_CFGR1_EXTSEL_T4CC2
#  define ADC1_EXTSEL_T4CC3   ADC12_CFGR1_EXTSEL_T4CC3
#  define ADC1_EXTSEL_T4CC4   ADC12_CFGR1_EXTSEL_T4CC4
#  define ADC1_EXTSEL_T4TRGO  ADC12_CFGR1_EXTSEL_T4TRGO
#  define ADC2_EXTSEL_T4CC1   ADC12_CFGR1_EXTSEL_T4CC1
#  define ADC2_EXTSEL_T4CC2   ADC12_CFGR1_EXTSEL_T4CC2
#  define ADC2_EXTSEL_T4CC3   ADC12_CFGR1_EXTSEL_T4CC3
#  define ADC2_EXTSEL_T4CC4   ADC12_CFGR1_EXTSEL_T4CC4
#  define ADC2_EXTSEL_T4TRGO  ADC12_CFGR1_EXTSEL_T4TRGO
#  define ADC3_EXTSEL_T4CC1   ADC34_CFGR1_EXTSEL_T4CC1
#  define ADC3_EXTSEL_T4CC2   ADC34_CFGR1_EXTSEL_T4CC2
#  define ADC3_EXTSEL_T4CC3   ADC34_CFGR1_EXTSEL_T4CC3
#  define ADC3_EXTSEL_T4CC4   ADC34_CFGR1_EXTSEL_T4CC4
#  define ADC3_EXTSEL_T4TRGO  ADC34_CFGR1_EXTSEL_T4TRGO
#  define ADC4_EXTSEL_T4CC1   ADC34_CFGR1_EXTSEL_T4CC1
#  define ADC4_EXTSEL_T4CC2   ADC34_CFGR1_EXTSEL_T4CC2
#  define ADC4_EXTSEL_T4CC3   ADC34_CFGR1_EXTSEL_T4CC3
#  define ADC4_EXTSEL_T4CC4   ADC34_CFGR1_EXTSEL_T4CC4
#  define ADC4_EXTSEL_T4TRGO  ADC34_CFGR1_EXTSEL_T4TRGO
#  define ADC1_EXTSEL_T5CC1   ADC12_CFGR1_EXTSEL_T5CC1
#  define ADC1_EXTSEL_T5CC2   ADC12_CFGR1_EXTSEL_T5CC2
#  define ADC1_EXTSEL_T5CC3   ADC12_CFGR1_EXTSEL_T5CC3
#  define ADC1_EXTSEL_T5CC4   ADC12_CFGR1_EXTSEL_T5CC4
#  define ADC1_EXTSEL_T5TRGO  ADC12_CFGR1_EXTSEL_T5TRGO
#  define ADC2_EXTSEL_T5CC1   ADC12_CFGR1_EXTSEL_T5CC1
#  define ADC2_EXTSEL_T5CC2   ADC12_CFGR1_EXTSEL_T5CC2
#  define ADC2_EXTSEL_T5CC3   ADC12_CFGR1_EXTSEL_T5CC3
#  define ADC2_EXTSEL_T5CC4   ADC12_CFGR1_EXTSEL_T5CC4
#  define ADC2_EXTSEL_T5TRGO  ADC12_CFGR1_EXTSEL_T5TRGO
#  define ADC3_EXTSEL_T5CC1   ADC34_CFGR1_EXTSEL_T5CC1
#  define ADC3_EXTSEL_T5CC2   ADC34_CFGR1_EXTSEL_T5CC2
#  define ADC3_EXTSEL_T5CC3   ADC34_CFGR1_EXTSEL_T5CC3
#  define ADC3_EXTSEL_T5CC4   ADC34_CFGR1_EXTSEL_T5CC4
#  define ADC3_EXTSEL_T5TRGO  ADC34_CFGR1_EXTSEL_T5TRGO
#  define ADC4_EXTSEL_T5CC1   ADC34_CFGR1_EXTSEL_T5CC1
#  define ADC4_EXTSEL_T5CC2   ADC34_CFGR1_EXTSEL_T5CC2
#  define ADC4_EXTSEL_T5CC3   ADC34_CFGR1_EXTSEL_T5CC3
#  define ADC4_EXTSEL_T5CC4   ADC34_CFGR1_EXTSEL_T5CC4
#  define ADC4_EXTSEL_T5TRGO  ADC34_CFGR1_EXTSEL_T5TRGO
#  define ADC1_EXTSEL_T6CC1   ADC12_CFGR1_EXTSEL_T6CC1
#  define ADC1_EXTSEL_T6CC2   ADC12_CFGR1_EXTSEL_T6CC2
#  define ADC1_EXTSEL_T6CC3   ADC12_CFGR1_EXTSEL_T6CC3
#  define ADC1_EXTSEL_T6CC4   ADC12_CFGR1_EXTSEL_T6CC4
#  define ADC1_EXTSEL_T6TRGO  ADC12_CFGR1_EXTSEL_T6TRGO
#  define ADC2_EXTSEL_T6CC1   ADC12_CFGR1_EXTSEL_T6CC1
#  define ADC2_EXTSEL_T6CC2   ADC12_CFGR1_EXTSEL_T6CC2
#  define ADC2_EXTSEL_T6CC3   ADC12_CFGR1_EXTSEL_T6CC3
#  define ADC2_EXTSEL_T6CC4   ADC12_CFGR1_EXTSEL_T6CC4
#  define ADC2_EXTSEL_T6TRGO  ADC12_CFGR1_EXTSEL_T6TRGO
#  define ADC3_EXTSEL_T6CC1   ADC34_CFGR1_EXTSEL_T6CC1
#  define ADC3_EXTSEL_T6CC2   ADC34_CFGR1_EXTSEL_T6CC2
#  define ADC3_EXTSEL_T6CC3   ADC34_CFGR1_EXTSEL_T6CC3
#  define ADC3_EXTSEL_T6CC4   ADC34_CFGR1_EXTSEL_T6CC4
#  define ADC3_EXTSEL_T6TRGO  ADC34_CFGR1_EXTSEL_T6TRGO
#  define ADC4_EXTSEL_T6CC1   ADC34_CFGR1_EXTSEL_T6CC1
#  define ADC4_EXTSEL_T6CC2   ADC34_CFGR1_EXTSEL_T6CC2
#  define ADC4_EXTSEL_T6CC3   ADC34_CFGR1_EXTSEL_T6CC3
#  define ADC4_EXTSEL_T6CC4   ADC34_CFGR1_EXTSEL_T6CC4
#  define ADC4_EXTSEL_T6TRGO  ADC34_CFGR1_EXTSEL_T6TRGO
#  define ADC1_EXTSEL_T7CC1   ADC12_CFGR1_EXTSEL_T7CC1
#  define ADC1_EXTSEL_T7CC2   ADC12_CFGR1_EXTSEL_T7CC2
#  define ADC1_EXTSEL_T7CC3   ADC12_CFGR1_EXTSEL_T7CC3
#  define ADC1_EXTSEL_T7CC4   ADC12_CFGR1_EXTSEL_T7CC4
#  define ADC1_EXTSEL_T7TRGO  ADC12_CFGR1_EXTSEL_T7TRGO
#  define ADC2_EXTSEL_T7CC1   ADC12_CFGR1_EXTSEL_T7CC1
#  define ADC2_EXTSEL_T7CC2   ADC12_CFGR1_EXTSEL_T7CC2
#  define ADC2_EXTSEL_T7CC3   ADC12_CFGR1_EXTSEL_T7CC3
#  define ADC2_EXTSEL_T7CC4   ADC12_CFGR1_EXTSEL_T7CC4
#  define ADC2_EXTSEL_T7TRGO  ADC12_CFGR1_EXTSEL_T7TRGO
#  define ADC3_EXTSEL_T7CC1   ADC34_CFGR1_EXTSEL_T7CC1
#  define ADC3_EXTSEL_T7CC2   ADC34_CFGR1_EXTSEL_T7CC2
#  define ADC3_EXTSEL_T7CC3   ADC34_CFGR1_EXTSEL_T7CC3
#  define ADC3_EXTSEL_T7CC4   ADC34_CFGR1_EXTSEL_T7CC4
#  define ADC3_EXTSEL_T7TRGO  ADC34_CFGR1_EXTSEL_T7TRGO
#  define ADC4_EXTSEL_T7CC1   ADC34_CFGR1_EXTSEL_T7CC1
#  define ADC4_EXTSEL_T7CC2   ADC34_CFGR1_EXTSEL_T7CC2
#  define ADC4_EXTSEL_T7CC3   ADC34_CFGR1_EXTSEL_T7CC3
#  define ADC4_EXTSEL_T7CC4   ADC34_CFGR1_EXTSEL_T7CC4
#  define ADC4_EXTSEL_T7TRGO  ADC34_CFGR1_EXTSEL_T7TRGO
#  define ADC1_EXTSEL_T8CC1   ADC12_CFGR1_EXTSEL_T8CC1
#  define ADC1_EXTSEL_T8CC2   ADC12_CFGR1_EXTSEL_T8CC2
#  define ADC1_EXTSEL_T8CC3   ADC12_CFGR1_EXTSEL_T8CC3
#  define ADC1_EXTSEL_T8CC4   ADC12_CFGR1_EXTSEL_T8CC4
#  define ADC1_EXTSEL_T8TRGO  ADC12_CFGR1_EXTSEL_T8TRGO
#  define ADC2_EXTSEL_T8CC1   ADC12_CFGR1_EXTSEL_T8CC1
#  define ADC2_EXTSEL_T8CC2   ADC12_CFGR1_EXTSEL_T8CC2
#  define ADC2_EXTSEL_T8CC3   ADC12_CFGR1_EXTSEL_T8CC3
#  define ADC2_EXTSEL_T8CC4   ADC12_CFGR1_EXTSEL_T8CC4
#  define ADC2_EXTSEL_T8TRGO  ADC12_CFGR1_EXTSEL_T8TRGO
#  define ADC3_EXTSEL_T8CC1   ADC34_CFGR1_EXTSEL_T8CC1
#  define ADC3_EXTSEL_T8CC2   ADC34_CFGR1_EXTSEL_T8CC2
#  define ADC3_EXTSEL_T8CC3   ADC34_CFGR1_EXTSEL_T8CC3
#  define ADC3_EXTSEL_T8CC4   ADC34_CFGR1_EXTSEL_T8CC4
#  define ADC3_EXTSEL_T8TRGO  ADC34_CFGR1_EXTSEL_T8TRGO
#  define ADC4_EXTSEL_T8CC1   ADC34_CFGR1_EXTSEL_T8CC1
#  define ADC4_EXTSEL_T8CC2   ADC34_CFGR1_EXTSEL_T8CC2
#  define ADC4_EXTSEL_T8CC3   ADC34_CFGR1_EXTSEL_T8CC3
#  define ADC4_EXTSEL_T8CC4   ADC34_CFGR1_EXTSEL_T8CC4
#  define ADC4_EXTSEL_T8TRGO  ADC34_CFGR1_EXTSEL_T8TRGO
#  define ADC1_EXTSEL_T9CC1   ADC12_CFGR1_EXTSEL_T9CC1
#  define ADC1_EXTSEL_T9CC2   ADC12_CFGR1_EXTSEL_T9CC2
#  define ADC1_EXTSEL_T9CC3   ADC12_CFGR1_EXTSEL_T9CC3
#  define ADC1_EXTSEL_T9CC4   ADC12_CFGR1_EXTSEL_T9CC4
#  define ADC1_EXTSEL_T9TRGO  ADC12_CFGR1_EXTSEL_T9TRGO
#  define ADC2_EXTSEL_T9CC1   ADC12_CFGR1_EXTSEL_T9CC1
#  define ADC2_EXTSEL_T9CC2   ADC12_CFGR1_EXTSEL_T9CC2
#  define ADC2_EXTSEL_T9CC3   ADC12_CFGR1_EXTSEL_T9CC3
#  define ADC2_EXTSEL_T9CC4   ADC12_CFGR1_EXTSEL_T9CC4
#  define ADC2_EXTSEL_T9TRGO  ADC12_CFGR1_EXTSEL_T9TRGO
#  define ADC3_EXTSEL_T9CC1   ADC34_CFGR1_EXTSEL_T9CC1
#  define ADC3_EXTSEL_T9CC2   ADC34_CFGR1_EXTSEL_T9CC2
#  define ADC3_EXTSEL_T9CC3   ADC34_CFGR1_EXTSEL_T9CC3
#  define ADC3_EXTSEL_T9CC4   ADC34_CFGR1_EXTSEL_T9CC4
#  define ADC3_EXTSEL_T9TRGO  ADC34_CFGR1_EXTSEL_T9TRGO
#  define ADC4_EXTSEL_T9CC1   ADC34_CFGR1_EXTSEL_T9CC1
#  define ADC4_EXTSEL_T9CC2   ADC34_CFGR1_EXTSEL_T9CC2
#  define ADC4_EXTSEL_T9CC3   ADC34_CFGR1_EXTSEL_T9CC3
#  define ADC4_EXTSEL_T9CC4   ADC34_CFGR1_EXTSEL_T9CC4
#  define ADC4_EXTSEL_T9TRGO  ADC34_CFGR1_EXTSEL_T9TRGO
#  define ADC1_EXTSEL_T10CC1  ADC12_CFGR1_EXTSEL_T10CC1
#  define ADC1_EXTSEL_T10CC2  ADC12_CFGR1_EXTSEL_T10CC2
#  define ADC1_EXTSEL_T10CC3  ADC12_CFGR1_EXTSEL_T10CC3
#  define ADC1_EXTSEL_T10CC4  ADC12_CFGR1_EXTSEL_T10CC4
#  define ADC1_EXTSEL_T10TRGO ADC12_CFGR1_EXTSEL_T10TRGO
#  define ADC2_EXTSEL_T10CC1  ADC12_CFGR1_EXTSEL_T10CC1
#  define ADC2_EXTSEL_T10CC2  ADC12_CFGR1_EXTSEL_T10CC2
#  define ADC2_EXTSEL_T10CC3  ADC12_CFGR1_EXTSEL_T10CC3
#  define ADC2_EXTSEL_T10CC4  ADC12_CFGR1_EXTSEL_T10CC4
#  define ADC2_EXTSEL_T10TRGO ADC12_CFGR1_EXTSEL_T10TRGO
#  define ADC3_EXTSEL_T10CC1  ADC34_CFGR1_EXTSEL_T10CC1
#  define ADC3_EXTSEL_T10CC2  ADC34_CFGR1_EXTSEL_T10CC2
#  define ADC3_EXTSEL_T10CC3  ADC34_CFGR1_EXTSEL_T10CC3
#  define ADC3_EXTSEL_T10CC4  ADC34_CFGR1_EXTSEL_T10CC4
#  define ADC3_EXTSEL_T10TRGO ADC34_CFGR1_EXTSEL_T10TRGO
#  define ADC4_EXTSEL_T10CC1  ADC34_CFGR1_EXTSEL_T10CC1
#  define ADC4_EXTSEL_T10CC2  ADC34_CFGR1_EXTSEL_T10CC2
#  define ADC4_EXTSEL_T10CC3  ADC34_CFGR1_EXTSEL_T10CC3
#  define ADC4_EXTSEL_T10CC4  ADC34_CFGR1_EXTSEL_T10CC4
#  define ADC4_EXTSEL_T10TRGO ADC34_CFGR1_EXTSEL_T10TRGO
#  define ADC1_EXTSEL_T15CC1  ADC12_CFGR1_EXTSEL_T15CC1
#  define ADC1_EXTSEL_T15CC2  ADC12_CFGR1_EXTSEL_T15CC2
#  define ADC1_EXTSEL_T15CC3  ADC12_CFGR1_EXTSEL_T15CC3
#  define ADC1_EXTSEL_T15CC4  ADC12_CFGR1_EXTSEL_T15CC4
#  define ADC1_EXTSEL_T15TRGO ADC12_CFGR1_EXTSEL_T15TRGO
#  define ADC2_EXTSEL_T15CC1  ADC12_CFGR1_EXTSEL_T15CC1
#  define ADC2_EXTSEL_T15CC2  ADC12_CFGR1_EXTSEL_T15CC2
#  define ADC2_EXTSEL_T15CC3  ADC12_CFGR1_EXTSEL_T15CC3
#  define ADC2_EXTSEL_T15CC4  ADC12_CFGR1_EXTSEL_T15CC4
#  define ADC2_EXTSEL_T15TRGO ADC12_CFGR1_EXTSEL_T15TRGO
#  define ADC3_EXTSEL_T15CC1  ADC34_CFGR1_EXTSEL_T15CC1
#  define ADC3_EXTSEL_T15CC2  ADC34_CFGR1_EXTSEL_T15CC2
#  define ADC3_EXTSEL_T15CC3  ADC34_CFGR1_EXTSEL_T15CC3
#  define ADC3_EXTSEL_T15CC4  ADC34_CFGR1_EXTSEL_T15CC4
#  define ADC3_EXTSEL_T15TRGO ADC34_CFGR1_EXTSEL_T15TRGO
#  define ADC4_EXTSEL_T15CC1  ADC34_CFGR1_EXTSEL_T15CC1
#  define ADC4_EXTSEL_T15CC2  ADC34_CFGR1_EXTSEL_T15CC2
#  define ADC4_EXTSEL_T15CC3  ADC34_CFGR1_EXTSEL_T15CC3
#  define ADC4_EXTSEL_T15CC4  ADC34_CFGR1_EXTSEL_T15CC4
#  define ADC4_EXTSEL_T15TRGO ADC34_CFGR1_EXTSEL_T15TRGO
#  define ADC1_EXTSEL_T20CC1  ADC12_CFGR1_EXTSEL_T20CC1
#  define ADC1_EXTSEL_T20CC2  ADC12_CFGR1_EXTSEL_T20CC2
#  define ADC1_EXTSEL_T20CC3  ADC12_CFGR1_EXTSEL_T20CC3
#  define ADC1_EXTSEL_T20CC4  ADC12_CFGR1_EXTSEL_T20CC4
#  define ADC1_EXTSEL_T20TRGO ADC12_CFGR1_EXTSEL_T20TRGO
#  define ADC2_EXTSEL_T20CC1  ADC12_CFGR1_EXTSEL_T20CC1
#  define ADC2_EXTSEL_T20CC2  ADC12_CFGR1_EXTSEL_T20CC2
#  define ADC2_EXTSEL_T20CC3  ADC12_CFGR1_EXTSEL_T20CC3
#  define ADC2_EXTSEL_T20CC4  ADC12_CFGR1_EXTSEL_T20CC4
#  define ADC2_EXTSEL_T20TRGO ADC12_CFGR1_EXTSEL_T20TRGO
#  define ADC3_EXTSEL_T20CC1  ADC34_CFGR1_EXTSEL_T20CC1
#  define ADC3_EXTSEL_T20CC2  ADC34_CFGR1_EXTSEL_T20CC2
#  define ADC3_EXTSEL_T20CC3  ADC34_CFGR1_EXTSEL_T20CC3
#  define ADC3_EXTSEL_T20CC4  ADC34_CFGR1_EXTSEL_T20CC4
#  define ADC3_EXTSEL_T20TRGO ADC34_CFGR1_EXTSEL_T20TRGO
#  define ADC4_EXTSEL_T20CC1  ADC34_CFGR1_EXTSEL_T20CC1
#  define ADC4_EXTSEL_T20CC2  ADC34_CFGR1_EXTSEL_T20CC2
#  define ADC4_EXTSEL_T20CC3  ADC34_CFGR1_EXTSEL_T20CC3
#  define ADC4_EXTSEL_T20CC4  ADC34_CFGR1_EXTSEL_T20CC4
#  define ADC4_EXTSEL_T20TRGO ADC34_CFGR1_EXTSEL_T20TRGO
#  define ADC1_EXTSEL_HRTTRG1 ADC12_CFGR1_EXTSEL_HRT1TRG1
#  define ADC1_EXTSEL_HRTTRG3 ADC12_CFGR1_EXTSEL_HRT1TRG3
#  define ADC2_EXTSEL_HRTTRG1 ADC12_CFGR1_EXTSEL_HRT1TRG1
#  define ADC2_EXTSEL_HRTTRG3 ADC12_CFGR1_EXTSEL_HRT1TRG3
#else
#  define ADC1_EXTSEL_T1CC1   ADC_CR2_EXTSEL_T1CC1
#  define ADC1_EXTSEL_T1CC2   ADC_CR2_EXTSEL_T1CC2
#  define ADC1_EXTSEL_T1CC3   ADC_CR2_EXTSEL_T1CC3
#  define ADC1_EXTSEL_T1CC4   ADC_CR2_EXTSEL_T1CC4
#  define ADC1_EXTSEL_T1TRGO  ADC_CR2_EXTSEL_T1TRGO
#  define ADC2_EXTSEL_T1CC1   ADC_CR2_EXTSEL_T1CC1
#  define ADC2_EXTSEL_T1CC2   ADC_CR2_EXTSEL_T1CC2
#  define ADC2_EXTSEL_T1CC3   ADC_CR2_EXTSEL_T1CC3
#  define ADC2_EXTSEL_T1CC4   ADC_CR2_EXTSEL_T1CC4
#  define ADC2_EXTSEL_T1TRGO  ADC_CR2_EXTSEL_T1TRGO
#  define ADC3_EXTSEL_T1CC1   ADC_CR2_EXTSEL_T1CC1
#  define ADC3_EXTSEL_T1CC2   ADC_CR2_EXTSEL_T1CC2
#  define ADC3_EXTSEL_T1CC3   ADC_CR2_EXTSEL_T1CC3
#  define ADC3_EXTSEL_T1CC4   ADC_CR2_EXTSEL_T1CC4
#  define ADC3_EXTSEL_T1TRGO  ADC_CR2_EXTSEL_T1TRGO
#  define ADC4_EXTSEL_T1CC1   ADC_CR2_EXTSEL_T1CC1
#  define ADC4_EXTSEL_T1CC2   ADC_CR2_EXTSEL_T1CC2
#  define ADC4_EXTSEL_T1CC3   ADC_CR2_EXTSEL_T1CC3
#  define ADC4_EXTSEL_T1CC4   ADC_CR2_EXTSEL_T1CC4
#  define ADC4_EXTSEL_T1TRGO  ADC_CR2_EXTSEL_T1TRGO
#  define ADC1_EXTSEL_T2CC1   ADC_CR2_EXTSEL_T2CC1
#  define ADC1_EXTSEL_T2CC2   ADC_CR2_EXTSEL_T2CC2
#  define ADC1_EXTSEL_T2CC3   ADC_CR2_EXTSEL_T2CC3
#  define ADC1_EXTSEL_T2CC4   ADC_CR2_EXTSEL_T2CC4
#  define ADC1_EXTSEL_T2TRGO  ADC_CR2_EXTSEL_T2TRGO
#  define ADC2_EXTSEL_T2CC1   ADC_CR2_EXTSEL_T2CC1
#  define ADC2_EXTSEL_T2CC2   ADC_CR2_EXTSEL_T2CC2
#  define ADC2_EXTSEL_T2CC3   ADC_CR2_EXTSEL_T2CC3
#  define ADC2_EXTSEL_T2CC4   ADC_CR2_EXTSEL_T2CC4
#  define ADC2_EXTSEL_T2TRGO  ADC_CR2_EXTSEL_T2TRGO
#  define ADC3_EXTSEL_T2CC1   ADC_CR2_EXTSEL_T2CC1
#  define ADC3_EXTSEL_T2CC2   ADC_CR2_EXTSEL_T2CC2
#  define ADC3_EXTSEL_T2CC3   ADC_CR2_EXTSEL_T2CC3
#  define ADC3_EXTSEL_T2CC4   ADC_CR2_EXTSEL_T2CC4
#  define ADC3_EXTSEL_T2TRGO  ADC_CR2_EXTSEL_T2TRGO
#  define ADC4_EXTSEL_T2CC1   ADC_CR2_EXTSEL_T2CC1
#  define ADC4_EXTSEL_T2CC2   ADC_CR2_EXTSEL_T2CC2
#  define ADC4_EXTSEL_T2CC3   ADC_CR2_EXTSEL_T2CC3
#  define ADC4_EXTSEL_T2CC4   ADC_CR2_EXTSEL_T2CC4
#  define ADC4_EXTSEL_T2TRGO  ADC_CR2_EXTSEL_T2TRGO
#  define ADC1_EXTSEL_T3CC1   ADC_CR2_EXTSEL_T3CC1
#  define ADC1_EXTSEL_T3CC2   ADC_CR2_EXTSEL_T3CC2
#  define ADC1_EXTSEL_T3CC3   ADC_CR2_EXTSEL_T3CC3
#  define ADC1_EXTSEL_T3CC4   ADC_CR2_EXTSEL_T3CC4
#  define ADC1_EXTSEL_T3TRGO  ADC_CR2_EXTSEL_T3TRGO
#  define ADC2_EXTSEL_T3CC1   ADC_CR2_EXTSEL_T3CC1
#  define ADC2_EXTSEL_T3CC2   ADC_CR2_EXTSEL_T3CC2
#  define ADC2_EXTSEL_T3CC3   ADC_CR2_EXTSEL_T3CC3
#  define ADC2_EXTSEL_T3CC4   ADC_CR2_EXTSEL_T3CC4
#  define ADC2_EXTSEL_T3TRGO  ADC_CR2_EXTSEL_T3TRGO
#  define ADC3_EXTSEL_T3CC1   ADC_CR2_EXTSEL_T3CC1
#  define ADC3_EXTSEL_T3CC2   ADC_CR2_EXTSEL_T3CC2
#  define ADC3_EXTSEL_T3CC3   ADC_CR2_EXTSEL_T3CC3
#  define ADC3_EXTSEL_T3CC4   ADC_CR2_EXTSEL_T3CC4
#  define ADC3_EXTSEL_T3TRGO  ADC_CR2_EXTSEL_T3TRGO
#  define ADC4_EXTSEL_T3CC1   ADC_CR2_EXTSEL_T3CC1
#  define ADC4_EXTSEL_T3CC2   ADC_CR2_EXTSEL_T3CC2
#  define ADC4_EXTSEL_T3CC3   ADC_CR2_EXTSEL_T3CC3
#  define ADC4_EXTSEL_T3CC4   ADC_CR2_EXTSEL_T3CC4
#  define ADC4_EXTSEL_T3TRGO  ADC_CR2_EXTSEL_T3TRGO
#  define ADC1_EXTSEL_T4CC1   ADC_CR2_EXTSEL_T4CC1
#  define ADC1_EXTSEL_T4CC2   ADC_CR2_EXTSEL_T4CC2
#  define ADC1_EXTSEL_T4CC3   ADC_CR2_EXTSEL_T4CC3
#  define ADC1_EXTSEL_T4CC4   ADC_CR2_EXTSEL_T4CC4
#  define ADC1_EXTSEL_T4TRGO  ADC_CR2_EXTSEL_T4TRGO
#  define ADC2_EXTSEL_T4CC1   ADC_CR2_EXTSEL_T4CC1
#  define ADC2_EXTSEL_T4CC2   ADC_CR2_EXTSEL_T4CC2
#  define ADC2_EXTSEL_T4CC3   ADC_CR2_EXTSEL_T4CC3
#  define ADC2_EXTSEL_T4CC4   ADC_CR2_EXTSEL_T4CC4
#  define ADC2_EXTSEL_T4TRGO  ADC_CR2_EXTSEL_T4TRGO
#  define ADC3_EXTSEL_T4CC1   ADC_CR2_EXTSEL_T4CC1
#  define ADC3_EXTSEL_T4CC2   ADC_CR2_EXTSEL_T4CC2
#  define ADC3_EXTSEL_T4CC3   ADC_CR2_EXTSEL_T4CC3
#  define ADC3_EXTSEL_T4CC4   ADC_CR2_EXTSEL_T4CC4
#  define ADC3_EXTSEL_T4TRGO  ADC_CR2_EXTSEL_T4TRGO
#  define ADC4_EXTSEL_T4CC1   ADC_CR2_EXTSEL_T4CC1
#  define ADC4_EXTSEL_T4CC2   ADC_CR2_EXTSEL_T4CC2
#  define ADC4_EXTSEL_T4CC3   ADC_CR2_EXTSEL_T4CC3
#  define ADC4_EXTSEL_T4CC4   ADC_CR2_EXTSEL_T4CC4
#  define ADC4_EXTSEL_T4TRGO  ADC_CR2_EXTSEL_T4TRGO
#  define ADC1_EXTSEL_T5CC1   ADC_CR2_EXTSEL_T5CC1
#  define ADC1_EXTSEL_T5CC2   ADC_CR2_EXTSEL_T5CC2
#  define ADC1_EXTSEL_T5CC3   ADC_CR2_EXTSEL_T5CC3
#  define ADC1_EXTSEL_T5CC4   ADC_CR2_EXTSEL_T5CC4
#  define ADC1_EXTSEL_T5TRGO  ADC_CR2_EXTSEL_T5TRGO
#  define ADC2_EXTSEL_T5CC1   ADC_CR2_EXTSEL_T5CC1
#  define ADC2_EXTSEL_T5CC2   ADC_CR2_EXTSEL_T5CC2
#  define ADC2_EXTSEL_T5CC3   ADC_CR2_EXTSEL_T5CC3
#  define ADC2_EXTSEL_T5CC4   ADC_CR2_EXTSEL_T5CC4
#  define ADC2_EXTSEL_T5TRGO  ADC_CR2_EXTSEL_T5TRGO
#  define ADC3_EXTSEL_T5CC1   ADC_CR2_EXTSEL_T5CC1
#  define ADC3_EXTSEL_T5CC2   ADC_CR2_EXTSEL_T5CC2
#  define ADC3_EXTSEL_T5CC3   ADC_CR2_EXTSEL_T5CC3
#  define ADC3_EXTSEL_T5CC4   ADC_CR2_EXTSEL_T5CC4
#  define ADC3_EXTSEL_T5TRGO  ADC_CR2_EXTSEL_T5TRGO
#  define ADC4_EXTSEL_T5CC1   ADC_CR2_EXTSEL_T5CC1
#  define ADC4_EXTSEL_T5CC2   ADC_CR2_EXTSEL_T5CC2
#  define ADC4_EXTSEL_T5CC3   ADC_CR2_EXTSEL_T5CC3
#  define ADC4_EXTSEL_T5CC4   ADC_CR2_EXTSEL_T5CC4
#  define ADC4_EXTSEL_T5TRGO  ADC_CR2_EXTSEL_T5TRGO
#  define ADC1_EXTSEL_T6CC1   ADC_CR2_EXTSEL_T6CC1
#  define ADC1_EXTSEL_T6CC2   ADC_CR2_EXTSEL_T6CC2
#  define ADC1_EXTSEL_T6CC3   ADC_CR2_EXTSEL_T6CC3
#  define ADC1_EXTSEL_T6CC4   ADC_CR2_EXTSEL_T6CC4
#  define ADC1_EXTSEL_T6TRGO  ADC_CR2_EXTSEL_T6TRGO
#  define ADC2_EXTSEL_T6CC1   ADC_CR2_EXTSEL_T6CC1
#  define ADC2_EXTSEL_T6CC2   ADC_CR2_EXTSEL_T6CC2
#  define ADC2_EXTSEL_T6CC3   ADC_CR2_EXTSEL_T6CC3
#  define ADC2_EXTSEL_T6CC4   ADC_CR2_EXTSEL_T6CC4
#  define ADC2_EXTSEL_T6TRGO  ADC_CR2_EXTSEL_T6TRGO
#  define ADC3_EXTSEL_T6CC1   ADC_CR2_EXTSEL_T6CC1
#  define ADC3_EXTSEL_T6CC2   ADC_CR2_EXTSEL_T6CC2
#  define ADC3_EXTSEL_T6CC3   ADC_CR2_EXTSEL_T6CC3
#  define ADC3_EXTSEL_T6CC4   ADC_CR2_EXTSEL_T6CC4
#  define ADC3_EXTSEL_T6TRGO  ADC_CR2_EXTSEL_T6TRGO
#  define ADC4_EXTSEL_T6CC1   ADC_CR2_EXTSEL_T6CC1
#  define ADC4_EXTSEL_T6CC2   ADC_CR2_EXTSEL_T6CC2
#  define ADC4_EXTSEL_T6CC3   ADC_CR2_EXTSEL_T6CC3
#  define ADC4_EXTSEL_T6CC4   ADC_CR2_EXTSEL_T6CC4
#  define ADC4_EXTSEL_T6TRGO  ADC_CR2_EXTSEL_T6TRGO
#  define ADC1_EXTSEL_T7CC1   ADC_CR2_EXTSEL_T7CC1
#  define ADC1_EXTSEL_T7CC2   ADC_CR2_EXTSEL_T7CC2
#  define ADC1_EXTSEL_T7CC3   ADC_CR2_EXTSEL_T7CC3
#  define ADC1_EXTSEL_T7CC4   ADC_CR2_EXTSEL_T7CC4
#  define ADC1_EXTSEL_T7TRGO  ADC_CR2_EXTSEL_T7TRGO
#  define ADC2_EXTSEL_T7CC1   ADC_CR2_EXTSEL_T7CC1
#  define ADC2_EXTSEL_T7CC2   ADC_CR2_EXTSEL_T7CC2
#  define ADC2_EXTSEL_T7CC3   ADC_CR2_EXTSEL_T7CC3
#  define ADC2_EXTSEL_T7CC4   ADC_CR2_EXTSEL_T7CC4
#  define ADC2_EXTSEL_T7TRGO  ADC_CR2_EXTSEL_T7TRGO
#  define ADC3_EXTSEL_T7CC1   ADC_CR2_EXTSEL_T7CC1
#  define ADC3_EXTSEL_T7CC2   ADC_CR2_EXTSEL_T7CC2
#  define ADC3_EXTSEL_T7CC3   ADC_CR2_EXTSEL_T7CC3
#  define ADC3_EXTSEL_T7CC4   ADC_CR2_EXTSEL_T7CC4
#  define ADC3_EXTSEL_T7TRGO  ADC_CR2_EXTSEL_T7TRGO
#  define ADC4_EXTSEL_T7CC1   ADC_CR2_EXTSEL_T7CC1
#  define ADC4_EXTSEL_T7CC2   ADC_CR2_EXTSEL_T7CC2
#  define ADC4_EXTSEL_T7CC3   ADC_CR2_EXTSEL_T7CC3
#  define ADC4_EXTSEL_T7CC4   ADC_CR2_EXTSEL_T7CC4
#  define ADC4_EXTSEL_T7TRGO  ADC_CR2_EXTSEL_T7TRGO
#  define ADC1_EXTSEL_T8CC1   ADC_CR2_EXTSEL_T8CC1
#  define ADC1_EXTSEL_T8CC2   ADC_CR2_EXTSEL_T8CC2
#  define ADC1_EXTSEL_T8CC3   ADC_CR2_EXTSEL_T8CC3
#  define ADC1_EXTSEL_T8CC4   ADC_CR2_EXTSEL_T8CC4
#  define ADC1_EXTSEL_T8TRGO  ADC_CR2_EXTSEL_T8TRGO
#  define ADC2_EXTSEL_T8CC1   ADC_CR2_EXTSEL_T8CC1
#  define ADC2_EXTSEL_T8CC2   ADC_CR2_EXTSEL_T8CC2
#  define ADC2_EXTSEL_T8CC3   ADC_CR2_EXTSEL_T8CC3
#  define ADC2_EXTSEL_T8CC4   ADC_CR2_EXTSEL_T8CC4
#  define ADC2_EXTSEL_T8TRGO  ADC_CR2_EXTSEL_T8TRGO
#  define ADC3_EXTSEL_T8CC1   ADC_CR2_EXTSEL_T8CC1
#  define ADC3_EXTSEL_T8CC2   ADC_CR2_EXTSEL_T8CC2
#  define ADC3_EXTSEL_T8CC3   ADC_CR2_EXTSEL_T8CC3
#  define ADC3_EXTSEL_T8CC4   ADC_CR2_EXTSEL_T8CC4
#  define ADC3_EXTSEL_T8TRGO  ADC_CR2_EXTSEL_T8TRGO
#  define ADC4_EXTSEL_T8CC1   ADC_CR2_EXTSEL_T8CC1
#  define ADC4_EXTSEL_T8CC2   ADC_CR2_EXTSEL_T8CC2
#  define ADC4_EXTSEL_T8CC3   ADC_CR2_EXTSEL_T8CC3
#  define ADC4_EXTSEL_T8CC4   ADC_CR2_EXTSEL_T8CC4
#  define ADC4_EXTSEL_T8TRGO  ADC_CR2_EXTSEL_T8TRGO
#  define ADC1_EXTSEL_T9CC1   ADC_CR2_EXTSEL_T9CC1
#  define ADC1_EXTSEL_T9CC2   ADC_CR2_EXTSEL_T9CC2
#  define ADC1_EXTSEL_T9CC3   ADC_CR2_EXTSEL_T9CC3
#  define ADC1_EXTSEL_T9CC4   ADC_CR2_EXTSEL_T9CC4
#  define ADC1_EXTSEL_T9TRGO  ADC_CR2_EXTSEL_T9TRGO
#  define ADC2_EXTSEL_T9CC1   ADC_CR2_EXTSEL_T9CC1
#  define ADC2_EXTSEL_T9CC2   ADC_CR2_EXTSEL_T9CC2
#  define ADC2_EXTSEL_T9CC3   ADC_CR2_EXTSEL_T9CC3
#  define ADC2_EXTSEL_T9CC4   ADC_CR2_EXTSEL_T9CC4
#  define ADC2_EXTSEL_T9TRGO  ADC_CR2_EXTSEL_T9TRGO
#  define ADC3_EXTSEL_T9CC1   ADC_CR2_EXTSEL_T9CC1
#  define ADC3_EXTSEL_T9CC2   ADC_CR2_EXTSEL_T9CC2
#  define ADC3_EXTSEL_T9CC3   ADC_CR2_EXTSEL_T9CC3
#  define ADC3_EXTSEL_T9CC4   ADC_CR2_EXTSEL_T9CC4
#  define ADC3_EXTSEL_T9TRGO  ADC_CR2_EXTSEL_T9TRGO
#  define ADC4_EXTSEL_T9CC1   ADC_CR2_EXTSEL_T9CC1
#  define ADC4_EXTSEL_T9CC2   ADC_CR2_EXTSEL_T9CC2
#  define ADC4_EXTSEL_T9CC3   ADC_CR2_EXTSEL_T9CC3
#  define ADC4_EXTSEL_T9CC4   ADC_CR2_EXTSEL_T9CC4
#  define ADC4_EXTSEL_T9TRGO  ADC_CR2_EXTSEL_T9TRGO
#  define ADC1_EXTSEL_T10CC1  ADC_CR2_EXTSEL_T10CC1
#  define ADC1_EXTSEL_T10CC2  ADC_CR2_EXTSEL_T10CC2
#  define ADC1_EXTSEL_T10CC3  ADC_CR2_EXTSEL_T10CC3
#  define ADC1_EXTSEL_T10CC4  ADC_CR2_EXTSEL_T10CC4
#  define ADC1_EXTSEL_T10TRGO ADC_CR2_EXTSEL_T10TRGO
#  define ADC2_EXTSEL_T10CC1  ADC_CR2_EXTSEL_T10CC1
#  define ADC2_EXTSEL_T10CC2  ADC_CR2_EXTSEL_T10CC2
#  define ADC2_EXTSEL_T10CC3  ADC_CR2_EXTSEL_T10CC3
#  define ADC2_EXTSEL_T10CC4  ADC_CR2_EXTSEL_T10CC4
#  define ADC2_EXTSEL_T10TRGO ADC_CR2_EXTSEL_T10TRGO
#  define ADC3_EXTSEL_T10CC1  ADC_CR2_EXTSEL_T10CC1
#  define ADC3_EXTSEL_T10CC2  ADC_CR2_EXTSEL_T10CC2
#  define ADC3_EXTSEL_T10CC3  ADC_CR2_EXTSEL_T10CC3
#  define ADC3_EXTSEL_T10CC4  ADC_CR2_EXTSEL_T10CC4
#  define ADC3_EXTSEL_T10TRGO ADC_CR2_EXTSEL_T10TRGO
#  define ADC4_EXTSEL_T10CC1  ADC_CR2_EXTSEL_T10CC1
#  define ADC4_EXTSEL_T10CC2  ADC_CR2_EXTSEL_T10CC2
#  define ADC4_EXTSEL_T10CC3  ADC_CR2_EXTSEL_T10CC3
#  define ADC4_EXTSEL_T10CC4  ADC_CR2_EXTSEL_T10CC4
#  define ADC4_EXTSEL_T10TRGO ADC_CR2_EXTSEL_T10TRGO
#  define ADC1_EXTSEL_T15CC1  ADC_CR2_EXTSEL_T15CC1
#  define ADC1_EXTSEL_T15CC2  ADC_CR2_EXTSEL_T15CC2
#  define ADC1_EXTSEL_T15CC3  ADC_CR2_EXTSEL_T15CC3
#  define ADC1_EXTSEL_T15CC4  ADC_CR2_EXTSEL_T15CC4
#  define ADC1_EXTSEL_T15TRGO ADC_CR2_EXTSEL_T15TRGO
#  define ADC2_EXTSEL_T15CC1  ADC_CR2_EXTSEL_T15CC1
#  define ADC2_EXTSEL_T15CC2  ADC_CR2_EXTSEL_T15CC2
#  define ADC2_EXTSEL_T15CC3  ADC_CR2_EXTSEL_T15CC3
#  define ADC2_EXTSEL_T15CC4  ADC_CR2_EXTSEL_T15CC4
#  define ADC2_EXTSEL_T15TRGO ADC_CR2_EXTSEL_T15TRGO
#  define ADC3_EXTSEL_T15CC1  ADC_CR2_EXTSEL_T15CC1
#  define ADC3_EXTSEL_T15CC2  ADC_CR2_EXTSEL_T15CC2
#  define ADC3_EXTSEL_T15CC3  ADC_CR2_EXTSEL_T15CC3
#  define ADC3_EXTSEL_T15CC4  ADC_CR2_EXTSEL_T15CC4
#  define ADC3_EXTSEL_T15TRGO ADC_CR2_EXTSEL_T15TRGO
#  define ADC4_EXTSEL_T15CC1  ADC_CR2_EXTSEL_T15CC1
#  define ADC4_EXTSEL_T15CC2  ADC_CR2_EXTSEL_T15CC2
#  define ADC4_EXTSEL_T15CC3  ADC_CR2_EXTSEL_T15CC3
#  define ADC4_EXTSEL_T15CC4  ADC_CR2_EXTSEL_T15CC4
#  define ADC4_EXTSEL_T15TRGO ADC_CR2_EXTSEL_T15TRGO
#  define ADC1_EXTSEL_T20CC1  ADC_CR2_EXTSEL_T20CC1
#  define ADC1_EXTSEL_T20CC2  ADC_CR2_EXTSEL_T20CC2
#  define ADC1_EXTSEL_T20CC3  ADC_CR2_EXTSEL_T20CC3
#  define ADC1_EXTSEL_T20CC4  ADC_CR2_EXTSEL_T20CC4
#  define ADC1_EXTSEL_T20TRGO ADC_CR2_EXTSEL_T20TRGO
#  define ADC2_EXTSEL_T20CC1  ADC_CR2_EXTSEL_T20CC1
#  define ADC2_EXTSEL_T20CC2  ADC_CR2_EXTSEL_T20CC2
#  define ADC2_EXTSEL_T20CC3  ADC_CR2_EXTSEL_T20CC3
#  define ADC2_EXTSEL_T20CC4  ADC_CR2_EXTSEL_T20CC4
#  define ADC2_EXTSEL_T20TRGO ADC_CR2_EXTSEL_T20TRGO
#  define ADC3_EXTSEL_T20CC1  ADC_CR2_EXTSEL_T20CC1
#  define ADC3_EXTSEL_T20CC2  ADC_CR2_EXTSEL_T20CC2
#  define ADC3_EXTSEL_T20CC3  ADC_CR2_EXTSEL_T20CC3
#  define ADC3_EXTSEL_T20CC4  ADC_CR2_EXTSEL_T20CC4
#  define ADC3_EXTSEL_T20TRGO ADC_CR2_EXTSEL_T20TRGO
#  define ADC4_EXTSEL_T20CC1  ADC_CR2_EXTSEL_T20CC1
#  define ADC4_EXTSEL_T20CC2  ADC_CR2_EXTSEL_T20CC2
#  define ADC4_EXTSEL_T20CC3  ADC_CR2_EXTSEL_T20CC3
#  define ADC4_EXTSEL_T20CC4  ADC_CR2_EXTSEL_T20CC4
#  define ADC4_EXTSEL_T20TRGO ADC_CR2_EXTSEL_T20TRGO
#endif

/* JEXTSEL definitions.
 * NOTE: Assumptions like for EXTSEL definitions (look above)
 */

#if defined(HAVE_IP_ADC_V2)
#  define ADC1_JEXTSEL_T1CC1   ADC12_JSQR_JEXTSEL_T1CC1
#  define ADC1_JEXTSEL_T1CC2   ADC12_JSQR_JEXTSEL_T1CC2
#  define ADC1_JEXTSEL_T1CC3   ADC12_JSQR_JEXTSEL_T1CC3
#  define ADC1_JEXTSEL_T1CC4   ADC12_JSQR_JEXTSEL_T1CC4
#  define ADC1_JEXTSEL_T1TRGO  ADC12_JSQR_JEXTSEL_T1TRGO
#  define ADC2_JEXTSEL_T1CC1   ADC12_JSQR_JEXTSEL_T1CC1
#  define ADC2_JEXTSEL_T1CC2   ADC12_JSQR_JEXTSEL_T1CC2
#  define ADC2_JEXTSEL_T1CC3   ADC12_JSQR_JEXTSEL_T1CC3
#  define ADC2_JEXTSEL_T1CC4   ADC12_JSQR_JEXTSEL_T1CC4
#  define ADC2_JEXTSEL_T1TRGO  ADC12_JSQR_JEXTSEL_T1TRGO
#  define ADC3_JEXTSEL_T1CC1   ADC34_JSQR_JEXTSEL_T1CC1
#  define ADC3_JEXTSEL_T1CC2   ADC34_JSQR_JEXTSEL_T1CC2
#  define ADC3_JEXTSEL_T1CC3   ADC34_JSQR_JEXTSEL_T1CC3
#  define ADC3_JEXTSEL_T1CC4   ADC34_JSQR_JEXTSEL_T1CC4
#  define ADC3_JEXTSEL_T1TRGO  ADC34_JSQR_JEXTSEL_T1TRGO
#  define ADC4_JEXTSEL_T1CC1   ADC34_JSQR_JEXTSEL_T1CC1
#  define ADC4_JEXTSEL_T1CC2   ADC34_JSQR_JEXTSEL_T1CC2
#  define ADC4_JEXTSEL_T1CC3   ADC34_JSQR_JEXTSEL_T1CC3
#  define ADC4_JEXTSEL_T1CC4   ADC34_JSQR_JEXTSEL_T1CC4
#  define ADC4_JEXTSEL_T1TRGO  ADC34_JSQR_JEXTSEL_T1TRGO
#  define ADC1_JEXTSEL_T2CC1   ADC12_JSQR_JEXTSEL_T2CC1
#  define ADC1_JEXTSEL_T2CC2   ADC12_JSQR_JEXTSEL_T2CC2
#  define ADC1_JEXTSEL_T2CC3   ADC12_JSQR_JEXTSEL_T2CC3
#  define ADC1_JEXTSEL_T2CC4   ADC12_JSQR_JEXTSEL_T2CC4
#  define ADC1_JEXTSEL_T2TRGO  ADC12_JSQR_JEXTSEL_T2TRGO
#  define ADC2_JEXTSEL_T2CC1   ADC12_JSQR_JEXTSEL_T2CC1
#  define ADC2_JEXTSEL_T2CC2   ADC12_JSQR_JEXTSEL_T2CC2
#  define ADC2_JEXTSEL_T2CC3   ADC12_JSQR_JEXTSEL_T2CC3
#  define ADC2_JEXTSEL_T2CC4   ADC12_JSQR_JEXTSEL_T2CC4
#  define ADC2_JEXTSEL_T2TRGO  ADC12_JSQR_JEXTSEL_T2TRGO
#  define ADC3_JEXTSEL_T2CC1   ADC34_JSQR_JEXTSEL_T2CC1
#  define ADC3_JEXTSEL_T2CC2   ADC34_JSQR_JEXTSEL_T2CC2
#  define ADC3_JEXTSEL_T2CC3   ADC34_JSQR_JEXTSEL_T2CC3
#  define ADC3_JEXTSEL_T2CC4   ADC34_JSQR_JEXTSEL_T2CC4
#  define ADC3_JEXTSEL_T2TRGO  ADC34_JSQR_JEXTSEL_T2TRGO
#  define ADC4_JEXTSEL_T2CC1   ADC34_JSQR_JEXTSEL_T2CC1
#  define ADC4_JEXTSEL_T2CC2   ADC34_JSQR_JEXTSEL_T2CC2
#  define ADC4_JEXTSEL_T2CC3   ADC34_JSQR_JEXTSEL_T2CC3
#  define ADC4_JEXTSEL_T2CC4   ADC34_JSQR_JEXTSEL_T2CC4
#  define ADC4_JEXTSEL_T2TRGO  ADC34_JSQR_JEXTSEL_T2TRGO
#  define ADC1_JEXTSEL_T3CC1   ADC12_JSQR_JEXTSEL_T3CC1
#  define ADC1_JEXTSEL_T3CC2   ADC12_JSQR_JEXTSEL_T3CC2
#  define ADC1_JEXTSEL_T3CC3   ADC12_JSQR_JEXTSEL_T3CC3
#  define ADC1_JEXTSEL_T3CC4   ADC12_JSQR_JEXTSEL_T3CC4
#  define ADC1_JEXTSEL_T3TRGO  ADC12_JSQR_JEXTSEL_T3TRGO
#  define ADC2_JEXTSEL_T3CC1   ADC12_JSQR_JEXTSEL_T3CC1
#  define ADC2_JEXTSEL_T3CC2   ADC12_JSQR_JEXTSEL_T3CC2
#  define ADC2_JEXTSEL_T3CC3   ADC12_JSQR_JEXTSEL_T3CC3
#  define ADC2_JEXTSEL_T3CC4   ADC12_JSQR_JEXTSEL_T3CC4
#  define ADC2_JEXTSEL_T3TRGO  ADC12_JSQR_JEXTSEL_T3TRGO
#  define ADC3_JEXTSEL_T3CC1   ADC34_JSQR_JEXTSEL_T3CC1
#  define ADC3_JEXTSEL_T3CC2   ADC34_JSQR_JEXTSEL_T3CC2
#  define ADC3_JEXTSEL_T3CC3   ADC34_JSQR_JEXTSEL_T3CC3
#  define ADC3_JEXTSEL_T3CC4   ADC34_JSQR_JEXTSEL_T3CC4
#  define ADC3_JEXTSEL_T3TRGO  ADC34_JSQR_JEXTSEL_T3TRGO
#  define ADC4_JEXTSEL_T3CC1   ADC34_JSQR_JEXTSEL_T3CC1
#  define ADC4_JEXTSEL_T3CC2   ADC34_JSQR_JEXTSEL_T3CC2
#  define ADC4_JEXTSEL_T3CC3   ADC34_JSQR_JEXTSEL_T3CC3
#  define ADC4_JEXTSEL_T3CC4   ADC34_JSQR_JEXTSEL_T3CC4
#  define ADC4_JEXTSEL_T3TRGO  ADC34_JSQR_JEXTSEL_T3TRGO
#  define ADC1_JEXTSEL_T4CC1   ADC12_JSQR_JEXTSEL_T4CC1
#  define ADC1_JEXTSEL_T4CC2   ADC12_JSQR_JEXTSEL_T4CC2
#  define ADC1_JEXTSEL_T4CC3   ADC12_JSQR_JEXTSEL_T4CC3
#  define ADC1_JEXTSEL_T4CC4   ADC12_JSQR_JEXTSEL_T4CC4
#  define ADC1_JEXTSEL_T4TRGO  ADC12_JSQR_JEXTSEL_T4TRGO
#  define ADC2_JEXTSEL_T4CC1   ADC12_JSQR_JEXTSEL_T4CC1
#  define ADC2_JEXTSEL_T4CC2   ADC12_JSQR_JEXTSEL_T4CC2
#  define ADC2_JEXTSEL_T4CC3   ADC12_JSQR_JEXTSEL_T4CC3
#  define ADC2_JEXTSEL_T4CC4   ADC12_JSQR_JEXTSEL_T4CC4
#  define ADC2_JEXTSEL_T4TRGO  ADC12_JSQR_JEXTSEL_T4TRGO
#  define ADC3_JEXTSEL_T4CC1   ADC34_JSQR_JEXTSEL_T4CC1
#  define ADC3_JEXTSEL_T4CC2   ADC34_JSQR_JEXTSEL_T4CC2
#  define ADC3_JEXTSEL_T4CC3   ADC34_JSQR_JEXTSEL_T4CC3
#  define ADC3_JEXTSEL_T4CC4   ADC34_JSQR_JEXTSEL_T4CC4
#  define ADC3_JEXTSEL_T4TRGO  ADC34_JSQR_JEXTSEL_T4TRGO
#  define ADC4_JEXTSEL_T4CC1   ADC34_JSQR_JEXTSEL_T4CC1
#  define ADC4_JEXTSEL_T4CC2   ADC34_JSQR_JEXTSEL_T4CC2
#  define ADC4_JEXTSEL_T4CC3   ADC34_JSQR_JEXTSEL_T4CC3
#  define ADC4_JEXTSEL_T4CC4   ADC34_JSQR_JEXTSEL_T4CC4
#  define ADC4_JEXTSEL_T4TRGO  ADC34_JSQR_JEXTSEL_T4TRGO
#  define ADC1_JEXTSEL_T5CC1   ADC12_JSQR_JEXTSEL_T5CC1
#  define ADC1_JEXTSEL_T5CC2   ADC12_JSQR_JEXTSEL_T5CC2
#  define ADC1_JEXTSEL_T5CC3   ADC12_JSQR_JEXTSEL_T5CC3
#  define ADC1_JEXTSEL_T5CC4   ADC12_JSQR_JEXTSEL_T5CC4
#  define ADC1_JEXTSEL_T5TRGO  ADC12_JSQR_JEXTSEL_T5TRGO
#  define ADC2_JEXTSEL_T5CC1   ADC12_JSQR_JEXTSEL_T5CC1
#  define ADC2_JEXTSEL_T5CC2   ADC12_JSQR_JEXTSEL_T5CC2
#  define ADC2_JEXTSEL_T5CC3   ADC12_JSQR_JEXTSEL_T5CC3
#  define ADC2_JEXTSEL_T5CC4   ADC12_JSQR_JEXTSEL_T5CC4
#  define ADC2_JEXTSEL_T5TRGO  ADC12_JSQR_JEXTSEL_T5TRGO
#  define ADC3_JEXTSEL_T5CC1   ADC34_JSQR_JEXTSEL_T5CC1
#  define ADC3_JEXTSEL_T5CC2   ADC34_JSQR_JEXTSEL_T5CC2
#  define ADC3_JEXTSEL_T5CC3   ADC34_JSQR_JEXTSEL_T5CC3
#  define ADC3_JEXTSEL_T5CC4   ADC34_JSQR_JEXTSEL_T5CC4
#  define ADC3_JEXTSEL_T5TRGO  ADC34_JSQR_JEXTSEL_T5TRGO
#  define ADC4_JEXTSEL_T5CC1   ADC34_JSQR_JEXTSEL_T5CC1
#  define ADC4_JEXTSEL_T5CC2   ADC34_JSQR_JEXTSEL_T5CC2
#  define ADC4_JEXTSEL_T5CC3   ADC34_JSQR_JEXTSEL_T5CC3
#  define ADC4_JEXTSEL_T5CC4   ADC34_JSQR_JEXTSEL_T5CC4
#  define ADC4_JEXTSEL_T5TRGO  ADC34_JSQR_JEXTSEL_T5TRGO
#  define ADC1_JEXTSEL_T6CC1   ADC12_JSQR_JEXTSEL_T6CC1
#  define ADC1_JEXTSEL_T6CC2   ADC12_JSQR_JEXTSEL_T6CC2
#  define ADC1_JEXTSEL_T6CC3   ADC12_JSQR_JEXTSEL_T6CC3
#  define ADC1_JEXTSEL_T6CC4   ADC12_JSQR_JEXTSEL_T6CC4
#  define ADC1_JEXTSEL_T6TRGO  ADC12_JSQR_JEXTSEL_T6TRGO
#  define ADC2_JEXTSEL_T6CC1   ADC12_JSQR_JEXTSEL_T6CC1
#  define ADC2_JEXTSEL_T6CC2   ADC12_JSQR_JEXTSEL_T6CC2
#  define ADC2_JEXTSEL_T6CC3   ADC12_JSQR_JEXTSEL_T6CC3
#  define ADC2_JEXTSEL_T6CC4   ADC12_JSQR_JEXTSEL_T6CC4
#  define ADC2_JEXTSEL_T6TRGO  ADC12_JSQR_JEXTSEL_T6TRGO
#  define ADC3_JEXTSEL_T6CC1   ADC34_JSQR_JEXTSEL_T6CC1
#  define ADC3_JEXTSEL_T6CC2   ADC34_JSQR_JEXTSEL_T6CC2
#  define ADC3_JEXTSEL_T6CC3   ADC34_JSQR_JEXTSEL_T6CC3
#  define ADC3_JEXTSEL_T6CC4   ADC34_JSQR_JEXTSEL_T6CC4
#  define ADC3_JEXTSEL_T6TRGO  ADC34_JSQR_JEXTSEL_T6TRGO
#  define ADC4_JEXTSEL_T6CC1   ADC34_JSQR_JEXTSEL_T6CC1
#  define ADC4_JEXTSEL_T6CC2   ADC34_JSQR_JEXTSEL_T6CC2
#  define ADC4_JEXTSEL_T6CC3   ADC34_JSQR_JEXTSEL_T6CC3
#  define ADC4_JEXTSEL_T6CC4   ADC34_JSQR_JEXTSEL_T6CC4
#  define ADC4_JEXTSEL_T6TRGO  ADC34_JSQR_JEXTSEL_T6TRGO
#  define ADC1_JEXTSEL_T7CC1   ADC12_JSQR_JEXTSEL_T7CC1
#  define ADC1_JEXTSEL_T7CC2   ADC12_JSQR_JEXTSEL_T7CC2
#  define ADC1_JEXTSEL_T7CC3   ADC12_JSQR_JEXTSEL_T7CC3
#  define ADC1_JEXTSEL_T7CC4   ADC12_JSQR_JEXTSEL_T7CC4
#  define ADC1_JEXTSEL_T7TRGO  ADC12_JSQR_JEXTSEL_T7TRGO
#  define ADC2_JEXTSEL_T7CC1   ADC12_JSQR_JEXTSEL_T7CC1
#  define ADC2_JEXTSEL_T7CC2   ADC12_JSQR_JEXTSEL_T7CC2
#  define ADC2_JEXTSEL_T7CC3   ADC12_JSQR_JEXTSEL_T7CC3
#  define ADC2_JEXTSEL_T7CC4   ADC12_JSQR_JEXTSEL_T7CC4
#  define ADC2_JEXTSEL_T7TRGO  ADC12_JSQR_JEXTSEL_T7TRGO
#  define ADC3_JEXTSEL_T7CC1   ADC34_JSQR_JEXTSEL_T7CC1
#  define ADC3_JEXTSEL_T7CC2   ADC34_JSQR_JEXTSEL_T7CC2
#  define ADC3_JEXTSEL_T7CC3   ADC34_JSQR_JEXTSEL_T7CC3
#  define ADC3_JEXTSEL_T7CC4   ADC34_JSQR_JEXTSEL_T7CC4
#  define ADC3_JEXTSEL_T7TRGO  ADC34_JSQR_JEXTSEL_T7TRGO
#  define ADC4_JEXTSEL_T7CC1   ADC34_JSQR_JEXTSEL_T7CC1
#  define ADC4_JEXTSEL_T7CC2   ADC34_JSQR_JEXTSEL_T7CC2
#  define ADC4_JEXTSEL_T7CC3   ADC34_JSQR_JEXTSEL_T7CC3
#  define ADC4_JEXTSEL_T7CC4   ADC34_JSQR_JEXTSEL_T7CC4
#  define ADC4_JEXTSEL_T7TRGO  ADC34_JSQR_JEXTSEL_T7TRGO
#  define ADC1_JEXTSEL_T8CC1   ADC12_JSQR_JEXTSEL_T8CC1
#  define ADC1_JEXTSEL_T8CC2   ADC12_JSQR_JEXTSEL_T8CC2
#  define ADC1_JEXTSEL_T8CC3   ADC12_JSQR_JEXTSEL_T8CC3
#  define ADC1_JEXTSEL_T8CC4   ADC12_JSQR_JEXTSEL_T8CC4
#  define ADC1_JEXTSEL_T8TRGO  ADC12_JSQR_JEXTSEL_T8TRGO
#  define ADC2_JEXTSEL_T8CC1   ADC12_JSQR_JEXTSEL_T8CC1
#  define ADC2_JEXTSEL_T8CC2   ADC12_JSQR_JEXTSEL_T8CC2
#  define ADC2_JEXTSEL_T8CC3   ADC12_JSQR_JEXTSEL_T8CC3
#  define ADC2_JEXTSEL_T8CC4   ADC12_JSQR_JEXTSEL_T8CC4
#  define ADC2_JEXTSEL_T8TRGO  ADC12_JSQR_JEXTSEL_T8TRGO
#  define ADC3_JEXTSEL_T8CC1   ADC34_JSQR_JEXTSEL_T8CC1
#  define ADC3_JEXTSEL_T8CC2   ADC34_JSQR_JEXTSEL_T8CC2
#  define ADC3_JEXTSEL_T8CC3   ADC34_JSQR_JEXTSEL_T8CC3
#  define ADC3_JEXTSEL_T8CC4   ADC34_JSQR_JEXTSEL_T8CC4
#  define ADC3_JEXTSEL_T8TRGO  ADC34_JSQR_JEXTSEL_T8TRGO
#  define ADC4_JEXTSEL_T8CC1   ADC34_JSQR_JEXTSEL_T8CC1
#  define ADC4_JEXTSEL_T8CC2   ADC34_JSQR_JEXTSEL_T8CC2
#  define ADC4_JEXTSEL_T8CC3   ADC34_JSQR_JEXTSEL_T8CC3
#  define ADC4_JEXTSEL_T8CC4   ADC34_JSQR_JEXTSEL_T8CC4
#  define ADC4_JEXTSEL_T8TRGO  ADC34_JSQR_JEXTSEL_T8TRGO
#  define ADC1_JEXTSEL_T9CC1   ADC12_JSQR_JEXTSEL_T9CC1
#  define ADC1_JEXTSEL_T9CC2   ADC12_JSQR_JEXTSEL_T9CC2
#  define ADC1_JEXTSEL_T9CC3   ADC12_JSQR_JEXTSEL_T9CC3
#  define ADC1_JEXTSEL_T9CC4   ADC12_JSQR_JEXTSEL_T9CC4
#  define ADC1_JEXTSEL_T9TRGO  ADC12_JSQR_JEXTSEL_T9TRGO
#  define ADC2_JEXTSEL_T9CC1   ADC12_JSQR_JEXTSEL_T9CC1
#  define ADC2_JEXTSEL_T9CC2   ADC12_JSQR_JEXTSEL_T9CC2
#  define ADC2_JEXTSEL_T9CC3   ADC12_JSQR_JEXTSEL_T9CC3
#  define ADC2_JEXTSEL_T9CC4   ADC12_JSQR_JEXTSEL_T9CC4
#  define ADC2_JEXTSEL_T9TRGO  ADC12_JSQR_JEXTSEL_T9TRGO
#  define ADC3_JEXTSEL_T9CC1   ADC34_JSQR_JEXTSEL_T9CC1
#  define ADC3_JEXTSEL_T9CC2   ADC34_JSQR_JEXTSEL_T9CC2
#  define ADC3_JEXTSEL_T9CC3   ADC34_JSQR_JEXTSEL_T9CC3
#  define ADC3_JEXTSEL_T9CC4   ADC34_JSQR_JEXTSEL_T9CC4
#  define ADC3_JEXTSEL_T9TRGO  ADC34_JSQR_JEXTSEL_T9TRGO
#  define ADC4_JEXTSEL_T9CC1   ADC34_JSQR_JEXTSEL_T9CC1
#  define ADC4_JEXTSEL_T9CC2   ADC34_JSQR_JEXTSEL_T9CC2
#  define ADC4_JEXTSEL_T9CC3   ADC34_JSQR_JEXTSEL_T9CC3
#  define ADC4_JEXTSEL_T9CC4   ADC34_JSQR_JEXTSEL_T9CC4
#  define ADC4_JEXTSEL_T9TRGO  ADC34_JSQR_JEXTSEL_T9TRGO
#  define ADC1_JEXTSEL_T10CC1  ADC12_JSQR_JEXTSEL_T10CC1
#  define ADC1_JEXTSEL_T10CC2  ADC12_JSQR_JEXTSEL_T10CC2
#  define ADC1_JEXTSEL_T10CC3  ADC12_JSQR_JEXTSEL_T10CC3
#  define ADC1_JEXTSEL_T10CC4  ADC12_JSQR_JEXTSEL_T10CC4
#  define ADC1_JEXTSEL_T10TRGO ADC12_JSQR_JEXTSEL_T10TRGO
#  define ADC2_JEXTSEL_T10CC1  ADC12_JSQR_JEXTSEL_T10CC1
#  define ADC2_JEXTSEL_T10CC2  ADC12_JSQR_JEXTSEL_T10CC2
#  define ADC2_JEXTSEL_T10CC3  ADC12_JSQR_JEXTSEL_T10CC3
#  define ADC2_JEXTSEL_T10CC4  ADC12_JSQR_JEXTSEL_T10CC4
#  define ADC2_JEXTSEL_T10TRGO ADC12_JSQR_JEXTSEL_T10TRGO
#  define ADC3_JEXTSEL_T10CC1  ADC34_JSQR_JEXTSEL_T10CC1
#  define ADC3_JEXTSEL_T10CC2  ADC34_JSQR_JEXTSEL_T10CC2
#  define ADC3_JEXTSEL_T10CC3  ADC34_JSQR_JEXTSEL_T10CC3
#  define ADC3_JEXTSEL_T10CC4  ADC34_JSQR_JEXTSEL_T10CC4
#  define ADC3_JEXTSEL_T10TRGO ADC34_JSQR_JEXTSEL_T10TRGO
#  define ADC4_JEXTSEL_T10CC1  ADC34_JSQR_JEXTSEL_T10CC1
#  define ADC4_JEXTSEL_T10CC2  ADC34_JSQR_JEXTSEL_T10CC2
#  define ADC4_JEXTSEL_T10CC3  ADC34_JSQR_JEXTSEL_T10CC3
#  define ADC4_JEXTSEL_T10CC4  ADC34_JSQR_JEXTSEL_T10CC4
#  define ADC4_JEXTSEL_T10TRGO ADC34_JSQR_JEXTSEL_T10TRGO
#  define ADC1_JEXTSEL_T15CC1  ADC12_JSQR_JEXTSEL_T15CC1
#  define ADC1_JEXTSEL_T15CC2  ADC12_JSQR_JEXTSEL_T15CC2
#  define ADC1_JEXTSEL_T15CC3  ADC12_JSQR_JEXTSEL_T15CC3
#  define ADC1_JEXTSEL_T15CC4  ADC12_JSQR_JEXTSEL_T15CC4
#  define ADC1_JEXTSEL_T15TRGO ADC12_JSQR_JEXTSEL_T15TRGO
#  define ADC2_JEXTSEL_T15CC1  ADC12_JSQR_JEXTSEL_T15CC1
#  define ADC2_JEXTSEL_T15CC2  ADC12_JSQR_JEXTSEL_T15CC2
#  define ADC2_JEXTSEL_T15CC3  ADC12_JSQR_JEXTSEL_T15CC3
#  define ADC2_JEXTSEL_T15CC4  ADC12_JSQR_JEXTSEL_T15CC4
#  define ADC2_JEXTSEL_T15TRGO ADC12_JSQR_JEXTSEL_T15TRGO
#  define ADC3_JEXTSEL_T15CC1  ADC34_JSQR_JEXTSEL_T15CC1
#  define ADC3_JEXTSEL_T15CC2  ADC34_JSQR_JEXTSEL_T15CC2
#  define ADC3_JEXTSEL_T15CC3  ADC34_JSQR_JEXTSEL_T15CC3
#  define ADC3_JEXTSEL_T15CC4  ADC34_JSQR_JEXTSEL_T15CC4
#  define ADC3_JEXTSEL_T15TRGO ADC34_JSQR_JEXTSEL_T15TRGO
#  define ADC4_JEXTSEL_T15CC1  ADC34_JSQR_JEXTSEL_T15CC1
#  define ADC4_JEXTSEL_T15CC2  ADC34_JSQR_JEXTSEL_T15CC2
#  define ADC4_JEXTSEL_T15CC3  ADC34_JSQR_JEXTSEL_T15CC3
#  define ADC4_JEXTSEL_T15CC4  ADC34_JSQR_JEXTSEL_T15CC4
#  define ADC4_JEXTSEL_T15TRGO ADC34_JSQR_JEXTSEL_T15TRGO
#  define ADC1_JEXTSEL_T20CC1  ADC12_JSQR_JEXTSEL_T20CC1
#  define ADC1_JEXTSEL_T20CC2  ADC12_JSQR_JEXTSEL_T20CC2
#  define ADC1_JEXTSEL_T20CC3  ADC12_JSQR_JEXTSEL_T20CC3
#  define ADC1_JEXTSEL_T20CC4  ADC12_JSQR_JEXTSEL_T20CC4
#  define ADC1_JEXTSEL_T20TRGO ADC12_JSQR_JEXTSEL_T20TRGO
#  define ADC2_JEXTSEL_T20CC1  ADC12_JSQR_JEXTSEL_T20CC1
#  define ADC2_JEXTSEL_T20CC2  ADC12_JSQR_JEXTSEL_T20CC2
#  define ADC2_JEXTSEL_T20CC3  ADC12_JSQR_JEXTSEL_T20CC3
#  define ADC2_JEXTSEL_T20CC4  ADC12_JSQR_JEXTSEL_T20CC4
#  define ADC2_JEXTSEL_T20TRGO ADC12_JSQR_JEXTSEL_T20TRGO
#  define ADC3_JEXTSEL_T20CC1  ADC34_JSQR_JEXTSEL_T20CC1
#  define ADC3_JEXTSEL_T20CC2  ADC34_JSQR_JEXTSEL_T20CC2
#  define ADC3_JEXTSEL_T20CC3  ADC34_JSQR_JEXTSEL_T20CC3
#  define ADC3_JEXTSEL_T20CC4  ADC34_JSQR_JEXTSEL_T20CC4
#  define ADC3_JEXTSEL_T20TRGO ADC34_JSQR_JEXTSEL_T20TRGO
#  define ADC4_JEXTSEL_T20CC1  ADC34_JSQR_JEXTSEL_T20CC1
#  define ADC4_JEXTSEL_T20CC2  ADC34_JSQR_JEXTSEL_T20CC2
#  define ADC4_JEXTSEL_T20CC3  ADC34_JSQR_JEXTSEL_T20CC3
#  define ADC4_JEXTSEL_T20CC4  ADC34_JSQR_JEXTSEL_T20CC4
#  define ADC4_JEXTSEL_T20TRGO ADC34_JSQR_JEXTSEL_T20TRGO
#  define ADC1_JEXTSEL_HRTTRG2 ADC12_JSQR_JEXTSEL_HRT1TRG2
#  define ADC1_JEXTSEL_HRTTRG4 ADC12_JSQR_JEXTSEL_HRT1TRG4
#  define ADC2_JEXTSEL_HRTTRG2 ADC12_JSQR_JEXTSEL_HRT1TRG2
#  define ADC2_JEXTSEL_HRTTRG4 ADC12_JSQR_JEXTSEL_HRT1TRG4
#endif

/* EXTSEL configuration *****************************************************/

/* NOTE:
 * this configuration if used only if CONFIG_STM32_TIMx_ADCy is selected.
 * You can still connect the ADC with a timer trigger using the
 * CONFIG_STM32_ADCx_EXTSEL option.
 */

#if defined(CONFIG_STM32_TIM1_ADC1)
#  if CONFIG_STM32_ADC1_TIMTRIG == 0
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T1CC1
#  elif CONFIG_STM32_ADC1_TIMTRIG == 1
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T1CC2
#  elif CONFIG_STM32_ADC1_TIMTRIG == 2
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T1CC3
#  elif CONFIG_STM32_ADC1_TIMTRIG == 3
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T1CC4
#  elif CONFIG_STM32_ADC1_TIMTRIG == 4
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T1TRGO
#  else
#    error "CONFIG_STM32_ADC1_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM2_ADC1)
#  if CONFIG_STM32_ADC1_TIMTRIG == 0
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T2CC1
#  elif CONFIG_STM32_ADC1_TIMTRIG == 1
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T2CC2
#  elif CONFIG_STM32_ADC1_TIMTRIG == 2
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T2CC3
#  elif CONFIG_STM32_ADC1_TIMTRIG == 3
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T2CC4
#  elif CONFIG_STM32_ADC1_TIMTRIG == 4
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T2TRGO
#  else
#    error "CONFIG_STM32_ADC1_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM3_ADC1)
#  if CONFIG_STM32_ADC1_TIMTRIG == 0
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T3CC1
#  elif CONFIG_STM32_ADC1_TIMTRIG == 1
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T3CC2
#  elif CONFIG_STM32_ADC1_TIMTRIG == 2
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T3CC3
#  elif CONFIG_STM32_ADC1_TIMTRIG == 3
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T3CC4
#  elif CONFIG_STM32_ADC1_TIMTRIG == 4
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T3TRGO
#  else
#    error "CONFIG_STM32_ADC1_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM4_ADC1)
#  if CONFIG_STM32_ADC1_TIMTRIG == 0
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T4CC1
#  elif CONFIG_STM32_ADC1_TIMTRIG == 1
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T4CC2
#  elif CONFIG_STM32_ADC1_TIMTRIG == 2
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T4CC3
#  elif CONFIG_STM32_ADC1_TIMTRIG == 3
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T4CC4
#  elif CONFIG_STM32_ADC1_TIMTRIG == 4
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T4TRGO
#  else
#    error "CONFIG_STM32_ADC1_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM5_ADC1)
#  if CONFIG_STM32_ADC1_TIMTRIG == 0
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T5CC1
#  elif CONFIG_STM32_ADC1_TIMTRIG == 1
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T5CC2
#  elif CONFIG_STM32_ADC1_TIMTRIG == 2
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T5CC3
#  elif CONFIG_STM32_ADC1_TIMTRIG == 3
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T5CC4
#  elif CONFIG_STM32_ADC1_TIMTRIG == 4
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T5TRGO
#  else
#    error "CONFIG_STM32_ADC1_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM6_ADC1)
#  if CONFIG_STM32_ADC1_TIMTRIG == 0
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T6CC1
#  elif CONFIG_STM32_ADC1_TIMTRIG == 1
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T6CC2
#  elif CONFIG_STM32_ADC1_TIMTRIG == 2
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T6CC3
#  elif CONFIG_STM32_ADC1_TIMTRIG == 3
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T6CC4
#  elif CONFIG_STM32_ADC1_TIMTRIG == 4
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T6TRGO
#  else
#    error "CONFIG_STM32_ADC1_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM7_ADC1)
#  if CONFIG_STM32_ADC1_TIMTRIG == 0
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T7CC1
#  elif CONFIG_STM32_ADC1_TIMTRIG == 1
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T7CC2
#  elif CONFIG_STM32_ADC1_TIMTRIG == 2
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T7CC3
#  elif CONFIG_STM32_ADC1_TIMTRIG == 3
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T7CC4
#  elif CONFIG_STM32_ADC1_TIMTRIG == 4
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T7TRGO
#  else
#    error "CONFIG_STM32_ADC1_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM8_ADC1)
#  if CONFIG_STM32_ADC1_TIMTRIG == 0
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T8CC1
#  elif CONFIG_STM32_ADC1_TIMTRIG == 1
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T8CC2
#  elif CONFIG_STM32_ADC1_TIMTRIG == 2
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T8CC3
#  elif CONFIG_STM32_ADC1_TIMTRIG == 3
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T8CC4
#  elif CONFIG_STM32_ADC1_TIMTRIG == 4
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T8TRGO
#  else
#    error "CONFIG_STM32_ADC1_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM9_ADC1)
#  if CONFIG_STM32_ADC1_TIMTRIG == 0
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T9CC1
#  elif CONFIG_STM32_ADC1_TIMTRIG == 1
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T9CC2
#  elif CONFIG_STM32_ADC1_TIMTRIG == 2
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T9CC3
#  elif CONFIG_STM32_ADC1_TIMTRIG == 3
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T9CC4
#  elif CONFIG_STM32_ADC1_TIMTRIG == 4
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T9TRGO
#  else
#    error "CONFIG_STM32_ADC1_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM10_ADC1)
#  if CONFIG_STM32_ADC1_TIMTRIG == 0
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T10CC1
#  elif CONFIG_STM32_ADC1_TIMTRIG == 1
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T10CC2
#  elif CONFIG_STM32_ADC1_TIMTRIG == 2
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T10CC3
#  elif CONFIG_STM32_ADC1_TIMTRIG == 3
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T10CC4
#  elif CONFIG_STM32_ADC1_TIMTRIG == 4
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T10TRGO
#  else
#    error "CONFIG_STM32_ADC1_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM15_ADC1)
#  if CONFIG_STM32_ADC1_TIMTRIG == 0
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T15CC1
#  elif CONFIG_STM32_ADC1_TIMTRIG == 1
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T15CC2
#  elif CONFIG_STM32_ADC1_TIMTRIG == 2
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T15CC3
#  elif CONFIG_STM32_ADC1_TIMTRIG == 3
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T15CC4
#  elif CONFIG_STM32_ADC1_TIMTRIG == 4
#    define ADC1_EXTSEL_VALUE ADC1_EXTSEL_T15TRGO
#  else
#    error "CONFIG_STM32_ADC1_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_HRTIM_ADC1_TRG1)
#  define ADC1_EXTSEL_VALUE ADC1_EXTSEL_HRTTRG1
#elif defined(CONFIG_STM32_HRTIM_ADC1_TRG3)
#  define ADC1_EXTSEL_VALUE ADC1_EXTSEL_HRTTRG3
#endif

#if defined(CONFIG_STM32_TIM1_ADC2)
#  if CONFIG_STM32_ADC2_TIMTRIG == 0
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T1CC1
#  elif CONFIG_STM32_ADC2_TIMTRIG == 1
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T1CC2
#  elif CONFIG_STM32_ADC2_TIMTRIG == 2
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T1CC3
#  elif CONFIG_STM32_ADC2_TIMTRIG == 3
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T1CC4
#  elif CONFIG_STM32_ADC2_TIMTRIG == 4
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T1TRGO
#  else
#    error "CONFIG_STM32_ADC2_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM2_ADC2)
#  if CONFIG_STM32_ADC2_TIMTRIG == 0
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T2CC1
#  elif CONFIG_STM32_ADC2_TIMTRIG == 1
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T2CC2
#  elif CONFIG_STM32_ADC2_TIMTRIG == 2
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T2CC3
#  elif CONFIG_STM32_ADC2_TIMTRIG == 3
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T2CC4
#  elif CONFIG_STM32_ADC2_TIMTRIG == 4
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T2TRGO
#  else
#    error "CONFIG_STM32_ADC2_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM3_ADC2)
#  if CONFIG_STM32_ADC2_TIMTRIG == 0
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T3CC1
#  elif CONFIG_STM32_ADC2_TIMTRIG == 1
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T3CC2
#  elif CONFIG_STM32_ADC2_TIMTRIG == 2
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T3CC3
#  elif CONFIG_STM32_ADC2_TIMTRIG == 3
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T3CC4
#  elif CONFIG_STM32_ADC2_TIMTRIG == 4
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T3TRGO
#  else
#    error "CONFIG_STM32_ADC2_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM4_ADC2)
#  if CONFIG_STM32_ADC2_TIMTRIG == 0
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T4CC1
#  elif CONFIG_STM32_ADC2_TIMTRIG == 1
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T4CC2
#  elif CONFIG_STM32_ADC2_TIMTRIG == 2
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T4CC3
#  elif CONFIG_STM32_ADC2_TIMTRIG == 3
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T4CC4
#  elif CONFIG_STM32_ADC2_TIMTRIG == 4
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T4TRGO
#  else
#    error "CONFIG_STM32_ADC2_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM5_ADC2)
#  if CONFIG_STM32_ADC2_TIMTRIG == 0
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T5CC1
#  elif CONFIG_STM32_ADC2_TIMTRIG == 1
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T5CC2
#  elif CONFIG_STM32_ADC2_TIMTRIG == 2
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T5CC3
#  elif CONFIG_STM32_ADC2_TIMTRIG == 3
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T5CC4
#  elif CONFIG_STM32_ADC2_TIMTRIG == 4
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T5TRGO
#  else
#    error "CONFIG_STM32_ADC2_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM6_ADC2)
#  if CONFIG_STM32_ADC2_TIMTRIG == 0
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T6CC1
#  elif CONFIG_STM32_ADC2_TIMTRIG == 1
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T6CC2
#  elif CONFIG_STM32_ADC2_TIMTRIG == 2
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T6CC3
#  elif CONFIG_STM32_ADC2_TIMTRIG == 3
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T6CC4
#  elif CONFIG_STM32_ADC2_TIMTRIG == 4
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T6TRGO
#  else
#    error "CONFIG_STM32_ADC2_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM8_ADC2)
#  if CONFIG_STM32_ADC2_TIMTRIG == 0
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T8CC1
#  elif CONFIG_STM32_ADC2_TIMTRIG == 1
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T8CC2
#  elif CONFIG_STM32_ADC2_TIMTRIG == 2
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T8CC3
#  elif CONFIG_STM32_ADC2_TIMTRIG == 3
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T8CC4
#  elif CONFIG_STM32_ADC2_TIMTRIG == 4
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T8TRGO
#  else
#    error "CONFIG_STM32_ADC2_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM15_ADC2)
#  if CONFIG_STM32_ADC2_TIMTRIG == 0
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T15CC1
#  elif CONFIG_STM32_ADC2_TIMTRIG == 1
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T15CC2
#  elif CONFIG_STM32_ADC2_TIMTRIG == 2
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T15CC3
#  elif CONFIG_STM32_ADC2_TIMTRIG == 3
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T15CC4
#  elif CONFIG_STM32_ADC2_TIMTRIG == 4
#    define ADC2_EXTSEL_VALUE ADC2_EXTSEL_T15TRGO
#  else
#    error "CONFIG_STM32_ADC2_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_HRTIM_ADC2_TRG1)
#  define ADC2_EXTSEL_VALUE ADC1_EXTSEL_HRTTRG1
#elif defined(CONFIG_STM32_HRTIM_ADC1_TRG3)
#  define ADC2_EXTSEL_VALUE ADC1_EXTSEL_HRTTRG3
#endif

#if defined(CONFIG_STM32_TIM1_ADC3)
#  if CONFIG_STM32_ADC3_TIMTRIG == 0
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T1CC1
#  elif CONFIG_STM32_ADC3_TIMTRIG == 1
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T1CC2
#  elif CONFIG_STM32_ADC3_TIMTRIG == 2
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T1CC3
#  elif CONFIG_STM32_ADC3_TIMTRIG == 3
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T1CC4
#  elif CONFIG_STM32_ADC3_TIMTRIG == 4
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T1TRGO
#  else
#    error "CONFIG_STM32_ADC3_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM2_ADC3)
#  if CONFIG_STM32_ADC3_TIMTRIG == 0
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T2CC1
#  elif CONFIG_STM32_ADC3_TIMTRIG == 1
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T2CC2
#  elif CONFIG_STM32_ADC3_TIMTRIG == 2
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T2CC3
#  elif CONFIG_STM32_ADC3_TIMTRIG == 3
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T2CC4
#  elif CONFIG_STM32_ADC3_TIMTRIG == 4
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T2TRGO
#  else
#    error "CONFIG_STM32_ADC3_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM3_ADC3)
#  if CONFIG_STM32_ADC3_TIMTRIG == 0
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T3CC1
#  elif CONFIG_STM32_ADC3_TIMTRIG == 1
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T3CC2
#  elif CONFIG_STM32_ADC3_TIMTRIG == 2
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T3CC3
#  elif CONFIG_STM32_ADC3_TIMTRIG == 3
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T3CC4
#  elif CONFIG_STM32_ADC3_TIMTRIG == 4
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T3TRGO
#  else
#    error "CONFIG_STM32_ADC3_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM4_ADC3)
#  if CONFIG_STM32_ADC3_TIMTRIG == 0
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T4CC1
#  elif CONFIG_STM32_ADC3_TIMTRIG == 1
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T4CC2
#  elif CONFIG_STM32_ADC3_TIMTRIG == 2
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T4CC3
#  elif CONFIG_STM32_ADC3_TIMTRIG == 3
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T4CC4
#  elif CONFIG_STM32_ADC3_TIMTRIG == 4
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T4TRGO
#  else
#    error "CONFIG_STM32_ADC3_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM5_ADC3)
#  if CONFIG_STM32_ADC3_TIMTRIG == 0
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T5CC1
#  elif CONFIG_STM32_ADC3_TIMTRIG == 1
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T5CC2
#  elif CONFIG_STM32_ADC3_TIMTRIG == 2
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T5CC3
#  elif CONFIG_STM32_ADC3_TIMTRIG == 3
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T5CC4
#  elif CONFIG_STM32_ADC3_TIMTRIG == 4
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T5TRGO
#  else
#    error "CONFIG_STM32_ADC3_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM7_ADC3)
#  if CONFIG_STM32_ADC3_TIMTRIG == 0
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T7CC1
#  elif CONFIG_STM32_ADC3_TIMTRIG == 1
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T7CC2
#  elif CONFIG_STM32_ADC3_TIMTRIG == 2
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T7CC3
#  elif CONFIG_STM32_ADC3_TIMTRIG == 3
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T7CC4
#  elif CONFIG_STM32_ADC3_TIMTRIG == 4
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T7TRGO
#  else
#    error "CONFIG_STM32_ADC3_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM8_ADC3)
#  if CONFIG_STM32_ADC3_TIMTRIG == 0
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T8CC1
#  elif CONFIG_STM32_ADC3_TIMTRIG == 1
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T8CC2
#  elif CONFIG_STM32_ADC3_TIMTRIG == 2
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T8CC3
#  elif CONFIG_STM32_ADC3_TIMTRIG == 3
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T8CC4
#  elif CONFIG_STM32_ADC3_TIMTRIG == 4
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T8TRGO
#  else
#    error "CONFIG_STM32_ADC3_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM15_ADC3)
#  if CONFIG_STM32_ADC3_TIMTRIG == 0
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T15CC1
#  elif CONFIG_STM32_ADC3_TIMTRIG == 1
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T15CC2
#  elif CONFIG_STM32_ADC3_TIMTRIG == 2
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T15CC3
#  elif CONFIG_STM32_ADC3_TIMTRIG == 3
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T15CC4
#  elif CONFIG_STM32_ADC3_TIMTRIG == 4
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T15TRGO
#  else
#    error "CONFIG_STM32_ADC3_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM20_ADC3)
#  if CONFIG_STM32_ADC3_TIMTRIG == 0
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T20CC1
#  elif CONFIG_STM32_ADC3_TIMTRIG == 1
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T20CC2
#  elif CONFIG_STM32_ADC3_TIMTRIG == 2
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T20CC3
#  elif CONFIG_STM32_ADC3_TIMTRIG == 3
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T20CC4
#  elif CONFIG_STM32_ADC3_TIMTRIG == 4
#    define ADC3_EXTSEL_VALUE ADC3_EXTSEL_T20TRGO
#  else
#    error "CONFIG_STM32_ADC3_TIMTRIG is out of range"
#  endif
#endif

#if defined(CONFIG_STM32_TIM1_ADC4)
#  if CONFIG_STM32_ADC4_TIMTRIG == 0
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T1CC1
#  elif CONFIG_STM32_ADC4_TIMTRIG == 1
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T1CC2
#  elif CONFIG_STM32_ADC4_TIMTRIG == 2
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T1CC3
#  elif CONFIG_STM32_ADC4_TIMTRIG == 3
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T1CC4
#  elif CONFIG_STM32_ADC4_TIMTRIG == 4
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T1TRGO
#  else
#    error "CONFIG_STM32_ADC4_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM2_ADC4)
#  if CONFIG_STM32_ADC4_TIMTRIG == 0
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T2CC1
#  elif CONFIG_STM32_ADC4_TIMTRIG == 1
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T2CC2
#  elif CONFIG_STM32_ADC4_TIMTRIG == 2
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T2CC3
#  elif CONFIG_STM32_ADC4_TIMTRIG == 3
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T2CC4
#  elif CONFIG_STM32_ADC4_TIMTRIG == 4
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T2TRGO
#  else
#    error "CONFIG_STM32_ADC4_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM3_ADC4)
#  if CONFIG_STM32_ADC4_TIMTRIG == 0
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T3CC1
#  elif CONFIG_STM32_ADC4_TIMTRIG == 1
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T3CC2
#  elif CONFIG_STM32_ADC4_TIMTRIG == 2
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T3CC3
#  elif CONFIG_STM32_ADC4_TIMTRIG == 3
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T3CC4
#  elif CONFIG_STM32_ADC4_TIMTRIG == 4
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T3TRGO
#  else
#    error "CONFIG_STM32_ADC4_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM4_ADC4)
#  if CONFIG_STM32_ADC4_TIMTRIG == 0
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T4CC1
#  elif CONFIG_STM32_ADC4_TIMTRIG == 1
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T4CC2
#  elif CONFIG_STM32_ADC4_TIMTRIG == 2
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T4CC3
#  elif CONFIG_STM32_ADC4_TIMTRIG == 3
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T4CC4
#  elif CONFIG_STM32_ADC4_TIMTRIG == 4
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T4TRGO
#  else
#    error "CONFIG_STM32_ADC4_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM5_ADC4)
#  if CONFIG_STM32_ADC4_TIMTRIG == 0
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T5CC1
#  elif CONFIG_STM32_ADC4_TIMTRIG == 1
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T5CC2
#  elif CONFIG_STM32_ADC4_TIMTRIG == 2
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T5CC3
#  elif CONFIG_STM32_ADC4_TIMTRIG == 3
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T5CC4
#  elif CONFIG_STM32_ADC4_TIMTRIG == 4
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T5TRGO
#  else
#    error "CONFIG_STM32_ADC4_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM7_ADC4)
#  if CONFIG_STM32_ADC4_TIMTRIG == 0
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T7CC1
#  elif CONFIG_STM32_ADC4_TIMTRIG == 1
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T7CC2
#  elif CONFIG_STM32_ADC4_TIMTRIG == 2
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T7CC3
#  elif CONFIG_STM32_ADC4_TIMTRIG == 3
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T7CC4
#  elif CONFIG_STM32_ADC4_TIMTRIG == 4
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T7TRGO
#  else
#    error "CONFIG_STM32_ADC4_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM8_ADC4)
#  if CONFIG_STM32_ADC4_TIMTRIG == 0
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T8CC1
#  elif CONFIG_STM32_ADC4_TIMTRIG == 1
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T8CC2
#  elif CONFIG_STM32_ADC4_TIMTRIG == 2
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T8CC3
#  elif CONFIG_STM32_ADC4_TIMTRIG == 3
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T8CC4
#  elif CONFIG_STM32_ADC4_TIMTRIG == 4
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T8TRGO
#  else
#    error "CONFIG_STM32_ADC4_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM15_ADC4)
#  if CONFIG_STM32_ADC4_TIMTRIG == 0
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T15CC1
#  elif CONFIG_STM32_ADC4_TIMTRIG == 1
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T15CC2
#  elif CONFIG_STM32_ADC4_TIMTRIG == 2
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T15CC3
#  elif CONFIG_STM32_ADC4_TIMTRIG == 3
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T15CC4
#  elif CONFIG_STM32_ADC4_TIMTRIG == 4
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T15TRGO
#  else
#    error "CONFIG_STM32_ADC4_TIMTRIG is out of range"
#  endif
#elif defined(CONFIG_STM32_TIM20_ADC4)
#  if CONFIG_STM32_ADC4_TIMTRIG == 0
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T20CC1
#  elif CONFIG_STM32_ADC4_TIMTRIG == 1
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T20CC2
#  elif CONFIG_STM32_ADC4_TIMTRIG == 2
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T20CC3
#  elif CONFIG_STM32_ADC4_TIMTRIG == 3
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T20CC4
#  elif CONFIG_STM32_ADC4_TIMTRIG == 4
#    define ADC4_EXTSEL_VALUE ADC4_EXTSEL_T20TRGO
#  else
#    error "CONFIG_STM32_ADC4_TIMTRIG is out of range"
#  endif
#endif

/* Regular channels external trigger support */

#ifdef ADC1_EXTSEL_VALUE
#  define ADC1_HAVE_EXTCFG  1
#  define ADC1_EXTCFG_VALUE (ADC1_EXTSEL_VALUE | ADC_EXTREG_EXTEN_DEFAULT)
#elif defined(CONFIG_STM32_ADC1_EXTSEL)
#  define ADC1_HAVE_EXTCFG  1
#  define ADC1_EXTCFG_VALUE 0
#else
#  undef ADC1_HAVE_EXTCFG
#endif
#ifdef ADC2_EXTSEL_VALUE
#  define ADC2_HAVE_EXTCFG  1
#  define ADC2_EXTCFG_VALUE (ADC2_EXTSEL_VALUE | ADC_EXTREG_EXTEN_DEFAULT)
#elif defined(CONFIG_STM32_ADC2_EXTSEL)
#  define ADC2_HAVE_EXTCFG  1
#  define ADC2_EXTCFG_VALUE 0
#else
#  undef ADC2_HAVE_EXTCFG
#endif
#ifdef ADC3_EXTSEL_VALUE
#  define ADC3_HAVE_EXTCFG  1
#  define ADC3_EXTCFG_VALUE (ADC3_EXTSEL_VALUE | ADC_EXTREG_EXTEN_DEFAULT)
#elif defined(CONFIG_STM32_ADC3_EXTSEL)
#  define ADC3_HAVE_EXTCFG  1
#  define ADC3_EXTCFG_VALUE 0
#else
#  undef ADC3_HAVE_EXTCFG
#endif
#ifdef ADC4_EXTSEL_VALUE
#  define ADC4_HAVE_EXTCFG  1
#  define ADC4_EXTCFG_VALUE (ADC4_EXTSEL_VALUE | ADC_EXTREG_EXTEN_DEFAULT)
#elif defined(CONFIG_STM32_ADC4_EXTSEL)
#  define ADC4_HAVE_EXTCFG  1
#  define ADC4_EXTCFG_VALUE 0
#else
#  undef ADC4_HAVE_EXTCFG
#endif

#if defined(ADC1_HAVE_EXTCFG) || defined(ADC2_HAVE_EXTCFG) || \
  defined(ADC3_HAVE_EXTCFG) || defined(ADC3_HAVE_EXTCFG)
#  define ADC_HAVE_EXTCFG
#endif

/* JEXTSEL configuration ****************************************************/

/* There is no automatic timer tirgger configuration from Kconfig for
 * injected channels conversion.
 */

/* ADC1 HRTIM JEXTSEL trigger */

#if defined(CONFIG_STM32_HRTIM_ADC1_TRG2)
#  define ADC1_JEXTSEL_VALUE ADC1_JEXTSEL_HRTTRG2
#elif defined(CONFIG_STM32_HRTIM_ADC1_TRG4)
#  define ADC1_JEXTSEL_VALUE ADC1_JEXTSEL_HRTTRG4
#endif

/* ADC1 HRTIM JEXTSEL trigger */

#if defined(CONFIG_STM32_HRTIM_ADC2_TRG2)
#  define ADC2_JEXTSEL_VALUE ADC2_JEXTSEL_HRTTRG2
#elif defined(CONFIG_STM32_HRTIM_ADC2_TRG4)
#  define ADC2_JEXTSEL_VALUE ADC2_JEXTSEL_HRTTRG4
#endif

/* Injected channels external trigger support */

#ifdef ADC1_JEXTSEL_VALUE
#  define ADC1_HAVE_JEXTCFG  1
#  define ADC1_JEXTCFG_VALUE (ADC1_JEXTSEL_VALUE | ADC_JEXTREG_JEXTEN_DEFAULT)
#elif defined(CONFIG_STM32_ADC1_JEXTSEL)
#  define ADC1_HAVE_JEXTCFG  1
#  define ADC1_JEXTCFG_VALUE 0
#else
#  undef ADC1_HAVE_JEXTCFG
#endif
#ifdef ADC2_JEXTSEL_VALUE
#  define ADC2_HAVE_JEXTCFG  1
#  define ADC2_JEXTCFG_VALUE (ADC2_JEXTSEL_VALUE | ADC_JEXTREG_JEXTEN_DEFAULT)
#elif defined(CONFIG_STM32_ADC2_JEXTSEL)
#  define ADC2_HAVE_JEXTCFG  1
#  define ADC2_JEXTCFG_VALUE 0
#else
#  undef ADC2_HAVE_JEXTCFG
#endif
#ifdef ADC3_JEXTSEL_VALUE
#  define ADC3_HAVE_JEXTCFG  1
#  define ADC3_JEXTCFG_VALUE (ADC3_JEXTSEL_VALUE | ADC_JEXTREG_JEXTEN_DEFAULT)
#elif defined(CONFIG_STM32_ADC3_JEXTSEL)
#  define ADC3_HAVE_JEXTCFG  1
#  define ADC3_JEXTCFG_VALUE 0
#else
#  undef ADC3_HAVE_JEXTCFG
#endif
#ifdef ADC4_JEXTSEL_VALUE
#  define ADC4_HAVE_JEXTCFG  1
#  define ADC4_JEXTCFG_VALUE (ADC4_JEXTSEL_VALUE | ADC_JEXTREG_JEXTEN_DEFAULT)
#elif defined(CONFIG_STM32_ADC4_JEXTSEL)
#  define ADC4_HAVE_JEXTCFG  1
#  define ADC4_JEXTCFG_VALUE 0
#else
#  undef ADC4_HAVE_JEXTCFG
#endif

#if defined(ADC1_HAVE_JEXTCFG) || defined(ADC2_HAVE_JEXTCFG) || \
    defined(ADC3_HAVE_JEXTCFG) || defined(ADC4_HAVE_JEXTCFG)
#  define ADC_HAVE_JEXTCFG
#endif

/* ADC interrupts ***********************************************************/

#if defined(HAVE_IP_ADC_V1)
#  define ADC_ISR_EOC                  ADC_SR_EOC
#  define ADC_IER_EOC                  ADC_CR1_EOCIE
#  define ADC_ISR_AWD                  ADC_SR_AWD
#  define ADC_IER_AWD                  ADC_CR1_AWDIE
#  define ADC_ISR_JEOC                 ADC_SR_JEOC
#  define ADC_IER_JEOC                 ADC_CR1_JEOCIE
#  define ADC_ISR_JEOS                 0 /* No JEOS */
#  define ADC_IER_JEOS                 0 /* No JEOS */
#  ifdef HAVE_BASIC_ADC
#    define ADC_ISR_OVR                0
#    define ADC_IER_OVR                0
#  else
#    define ADC_ISR_OVR                ADC_SR_OVR
#    define ADC_IER_OVR                ADC_CR1_OVRIE
#  endif
#elif defined(HAVE_IP_ADC_V2)
#  define ADC_ISR_EOC                  ADC_INT_EOC
#  define ADC_IER_EOC                  ADC_INT_EOC
#  define ADC_ISR_AWD                  ADC_INT_AWD1
#  define ADC_IER_AWD                  ADC_INT_AWD1
#  define ADC_ISR_JEOC                 ADC_INT_JEOC
#  define ADC_IER_JEOC                 ADC_INT_JEOC
#  define ADC_ISR_OVR                  ADC_INT_OVR
#  define ADC_IER_OVR                  ADC_INT_OVR
#  define ADC_ISR_JEOS                 ADC_INT_JEOS
#  define ADC_IER_JEOS                 ADC_INT_JEOS
#endif

#define ADC_ISR_ALLINTS (ADC_ISR_EOC | ADC_ISR_AWD | ADC_ISR_JEOC | \
                         ADC_ISR_JEOS | ADC_ISR_OVR)
#define ADC_IER_ALLINTS (ADC_IER_EOC | ADC_IER_AWD | ADC_IER_JEOC | \
                         ADC_IER_JEOS | ADC_IER_OVR)

/* Low-level ops helpers ****************************************************/

#define STM32_ADC_INT_ACK(adc, source)              \
        (adc)->llops->int_ack(adc, source)
#define STM32_ADC_INT_GET(adc)                      \
        (adc)->llops->int_get(adc)
#define STM32_ADC_INT_ENABLE(adc, source)           \
        (adc)->llops->int_en(adc, source)
#define STM32_ADC_INT_DISABLE(adc, source)          \
        (adc)->llops->int_dis(adc, source)
#define STM32_ADC_REGDATA_GET(adc)                  \
        (adc)->llops->val_get(adc)
#define STM32_ADC_REGBUF_REGISTER(adc, buffer, len) \
        (adc)->llops->regbuf_reg(adc, buffer, len)
#define STM32_ADC_REG_STARTCONV(adc, state)         \
        (adc)->llops->reg_startconv(adc, state)
#define STM32_ADC_OFFSET_SET(adc, ch, i, o)         \
        (adc)->llops->offset_set(adc, ch, i, o)
#define STM32_ADC_EXTCFG_SET(adc, c)                \
        (adc)->llops->extcfg_set(adc, c)
#define STM32_ADC_INJ_STARTCONV(adc, state)         \
        (adc)->llops->inj_startconv(adc, state)
#define STM32_ADC_INJDATA_GET(adc, chan)            \
        (adc)->llops->inj_get(adc, chan)
#define STM32_ADC_JEXTCFG_SET(adc, c)               \
        (adc)->llops->jextcfg_set(adc, c)
#define STM32_ADC_SAMPLETIME_SET(adc, time_samples) \
        (adc)->llops->stime_set(adc, time_samples)
#define STM32_ADC_SAMPLETIME_WRITE(adc)             \
        (adc)->llops->stime_write(adc)
#define STM32_ADC_DUMP_REGS(adc)                    \
        (adc)->llops->dump_regs(adc)
#define STM32_ADC_SETUP(adc)                        \
        (adc)->llops->setup(adc)
#define STM32_ADC_SHUTDOWN(adc)                     \
        (adc)->llops->shutdown(adc)

/****************************************************************************
 * Public Types
 ****************************************************************************/

/* On STM32F42xx and STM32F43xx devices,VBAT and temperature sensor are
 * connected to the same ADC internal channel (ADC1_IN18).
 * Only one conversion, either temperature sensor or VBAT, must be selected
 * at a time. When both conversion are enabled simultaneously,
 * only the VBAT conversion is performed.
 */

enum adc_io_cmds_e
{
#if defined(HAVE_IP_ADC_V1)
  IO_ENABLE_TEMPER_VOLT_CH,
#endif
#ifdef HAVE_ADC_VBAT
  IO_ENABLE_DISABLE_VBAT_CH,
#endif
  IO_ENABLE_DISABLE_AWDIE,
  IO_ENABLE_DISABLE_EOCIE,
  IO_ENABLE_DISABLE_JEOCIE,
  IO_ENABLE_DISABLE_OVRIE,
  IO_ENABLE_DISABLE_ALL_INTS,
  IO_STOP_ADC,
  IO_START_ADC,
  IO_START_CONV,
  IO_TRIGGER_REG,
#ifdef ADC_HAVE_INJECTED
  IO_TRIGGER_INJ,
#endif
#ifdef HAVE_ADC_POWERDOWN
  IO_ENABLE_DISABLE_PDI,
  IO_ENABLE_DISABLE_PDD,
  IO_ENABLE_DISABLE_PDD_PDI
#endif
};

/* ADC resolution can be reduced in order to perform faster conversion */

enum stm32_adc_resoluton_e
{
  ADC_RESOLUTION_12BIT = 0,     /* 12 bit */
  ADC_RESOLUTION_10BIT = 1,     /* 10 bit */
  ADC_RESOLUTION_8BIT  = 2,     /* 8 bit */
  ADC_RESOLUTION_6BIT  = 3      /* 6 bit */
};

#ifdef CONFIG_STM32_ADC_LL_OPS

#ifdef CONFIG_STM32_ADC_CHANGE_SAMPLETIME

/* Channel and sample time pair */

typedef struct adc_channel_s
{
  uint8_t channel:5;

  /* Sampling time individually for each channel.
   * It differs between families
   */

  uint8_t sample_time:3;
} adc_channel_t;

/* This structure will be used while setting channels to specified by the
 * "channel-sample time" pairs' values
 */

struct adc_sample_time_s
{
  adc_channel_t *channel;                /* Array of channels */
  uint8_t        channels_nbr:5;         /* Number of channels in array */
  bool           all_same:1;             /* All channels will get the
                                          * same value of the sample time */
  uint8_t        all_ch_sample_time:3;   /* Sample time for all channels */
};
#endif /* CONFIG_STM32_ADC_CHANGE_SAMPLETIME */

/* This structure provides the publicly visible representation of the
 * "lower-half" ADC driver structure.
 */

struct stm32_adc_dev_s
{
  /* Publicly visible portion of the "lower-half" ADC driver structure */

  const struct stm32_adc_ops_s *llops;

  /* Require cast-compatibility with private "lower-half" ADC structure */
};

/* Low-level operations for ADC */

struct stm32_adc_ops_s
{
  /* Low-level ADC setup */

  int (*setup)(struct stm32_adc_dev_s *dev);

  /* Low-level ADC shutdown */

  void (*shutdown)(struct stm32_adc_dev_s *dev);

  /* Acknowledge interrupts */

  void (*int_ack)(struct stm32_adc_dev_s *dev, uint32_t source);

  /* Get pending interrupts */

  uint32_t (*int_get)(struct stm32_adc_dev_s *dev);

  /* Enable interrupts */

  void (*int_en)(struct stm32_adc_dev_s *dev, uint32_t source);

  /* Disable interrupts */

  void (*int_dis)(struct stm32_adc_dev_s *dev, uint32_t source);

  /* Get current ADC data register */

  uint32_t (*val_get)(struct stm32_adc_dev_s *dev);

  /* Register buffer for ADC DMA transfer */

  int (*regbuf_reg)(struct stm32_adc_dev_s *dev,
                    uint16_t *buffer, uint8_t len);

  /* Start/stop regular conversion */

  void (*reg_startconv)(struct stm32_adc_dev_s *dev, bool state);

  /* Set offset for channel */

  int (*offset_set)(struct stm32_adc_dev_s *dev, uint8_t ch, uint8_t i,
                    uint16_t offset);

#ifdef ADC_HAVE_EXTCFG
  /* Configure the ADC external trigger for regular conversion */

  void (*extcfg_set)(struct stm32_adc_dev_s *dev, uint32_t extcfg);
#endif

#ifdef ADC_HAVE_JEXTCFG
  /* Configure the ADC external trigger for injected conversion */

  void (*jextcfg_set)(struct stm32_adc_dev_s *dev, uint32_t jextcfg);
#endif

#ifdef ADC_HAVE_INJECTED
  /* Get current ADC injected data register */

  uint32_t (*inj_get)(struct stm32_adc_dev_s *dev, uint8_t chan);

  /* Start/stop injected conversion */

  void (*inj_startconv)(struct stm32_adc_dev_s *dev, bool state);
#endif

#ifdef CONFIG_STM32_ADC_CHANGE_SAMPLETIME
  /* Set ADC sample time */

  void (*stime_set)(struct stm32_adc_dev_s *dev,
                    struct adc_sample_time_s *time_samples);

  /* Write ADC sample time */

  void (*stime_write)(struct stm32_adc_dev_s *dev);
#endif

  void (*dump_regs)(struct stm32_adc_dev_s *dev);
};

#endif /* CONFIG_STM32_ADC_LL_OPS */

/****************************************************************************
 * Public Function Prototypes
 ****************************************************************************/

#ifndef __ASSEMBLY__
#ifdef __cplusplus
#define EXTERN extern "C"
extern "C"
{
#else
#define EXTERN extern
#endif

/****************************************************************************
 * Name: stm32_adcinitialize
 *
 * Description:
 *   Initialize the ADC. See stm32_adc.c for more details.
 *
 * Input Parameters:
 *   intf      - Could be {1,2,3,4} for ADC1, ADC2, ADC3 or ADC4
 *   chanlist  - The list of channels (regular + injected)
 *   nchannels - Number of channels (regular + injected)
 *
 * Returned Value:
 *   Valid ADC device structure reference on success; a NULL on failure
 *
 ****************************************************************************/

struct adc_dev_s;
struct adc_dev_s *stm32_adcinitialize(int intf, const uint8_t *chanlist,
                                      int channels);

#undef EXTERN
#ifdef __cplusplus
}
#endif
#endif /* __ASSEMBLY__ */

#endif /* CONFIG_STM32_ADC1 || CONFIG_STM32_ADC2 ||
        * CONFIG_STM32_ADC3 || CONFIG_STM32_ADC4
        */
#endif /* __ARCH_ARM_SRC_STM32_STM32_ADC_H */
