/****************************************************************************
 * arch/arm/src/stm32/hardware/stm32f30xxx_pinmap.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_STM32_HARDWARE_STM32F30XXX_PINMAP_H
#define __ARCH_ARM_SRC_STM32_HARDWARE_STM32F30XXX_PINMAP_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

#include "stm32_gpio.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Alternate Pin Functions.  All members of the STM32F30xxx family share the
 * same pin multiplexing (although they may differ in the pins physically
 * available).
 *
 * Alternative pin selections are provided with a numeric suffix like _1, _2,
 * etc. Drivers, however, will use the pin selection without the numeric
 * suffix. Additional definitions are required in the board.h file.  For
 * example, if CAN1_RX connects via PA11 on some board, then the following
 * definitions should appear in the board.h header file for that board:
 *
 * #define GPIO_CAN1_RX GPIO_CAN1_RX_1
 *
 * The driver will then automatically configure PA11 as the CAN1 RX pin.
 */

/* WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific GPIO options such as
 * frequency, open-drain/push-pull, and pull-up/down!  Just the basics are
 * defined for most pins in this file.
 */

/* ADC */

#define GPIO_ADC1_IN1_0     (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN0)
#define GPIO_ADC1_IN2_0     (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN1)
#define GPIO_ADC1_IN3_0     (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN2)
#define GPIO_ADC1_IN4_0     (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN3)
#define GPIO_ADC1_IN5_0     (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN4)
#define GPIO_ADC1_IN6_0     (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN0)
#define GPIO_ADC1_IN7_0     (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN1)
#define GPIO_ADC1_IN8_0     (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN2)
#define GPIO_ADC1_IN9_0     (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN3)
#define GPIO_ADC1_IN10_0    (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN2)
#define GPIO_ADC1_IN11_0    (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN0)
#define GPIO_ADC1_IN12_0    (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN1)
#define GPIO_ADC1_IN13_0    (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN13)

#define GPIO_ADC2_IN1_0     (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN4)
#define GPIO_ADC2_IN2_0     (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN5)
#define GPIO_ADC2_IN3_0     (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN6)
#define GPIO_ADC2_IN4_0     (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN7)
#define GPIO_ADC2_IN5_0     (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN4)
#define GPIO_ADC2_IN6_0     (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN0)
#define GPIO_ADC2_IN7_0     (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN1)
#define GPIO_ADC2_IN8_0     (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN2)
#define GPIO_ADC2_IN9_0     (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN3)
#define GPIO_ADC2_IN10_0    (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN2)
#define GPIO_ADC2_IN11_0    (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN5)
#define GPIO_ADC2_IN12_0    (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN2)
#define GPIO_ADC2_IN13_0    (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN12)
#define GPIO_ADC2_IN14_0    (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN14)
#define GPIO_ADC2_IN15_0    (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN15)

#define GPIO_ADC3_IN1_0     (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN1)
#define GPIO_ADC3_IN2_0     (GPIO_ANALOG|GPIO_PORTE|GPIO_PIN9)
#define GPIO_ADC3_IN3_0     (GPIO_ANALOG|GPIO_PORTE|GPIO_PIN13)
#define GPIO_ADC3_IN5_0     (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN13)
#define GPIO_ADC3_IN6_0     (GPIO_ANALOG|GPIO_PORTE|GPIO_PIN8)
#define GPIO_ADC3_IN7_0     (GPIO_ANALOG|GPIO_PORTD|GPIO_PIN10)
#define GPIO_ADC3_IN8_0     (GPIO_ANALOG|GPIO_PORTD|GPIO_PIN11)
#define GPIO_ADC3_IN9_0     (GPIO_ANALOG|GPIO_PORTD|GPIO_PIN12)
#define GPIO_ADC3_IN10_0    (GPIO_ANALOG|GPIO_PORTD|GPIO_PIN13)
#define GPIO_ADC3_IN11_0    (GPIO_ANALOG|GPIO_PORTD|GPIO_PIN14)
#define GPIO_ADC3_IN12_0    (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN0)
#define GPIO_ADC3_IN13_0    (GPIO_ANALOG|GPIO_PORTE|GPIO_PIN7)
#define GPIO_ADC3_IN14_0    (GPIO_ANALOG|GPIO_PORTE|GPIO_PIN10)
#define GPIO_ADC3_IN15_0    (GPIO_ANALOG|GPIO_PORTE|GPIO_PIN11)
#define GPIO_ADC3_IN16_0    (GPIO_ANALOG|GPIO_PORTE|GPIO_PIN12)

#define GPIO_ADC4_IN1_0     (GPIO_ANALOG|GPIO_PORTE|GPIO_PIN14)
#define GPIO_ADC4_IN2_0     (GPIO_ANALOG|GPIO_PORTE|GPIO_PIN15)
#define GPIO_ADC4_IN3_0     (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN12)
#define GPIO_ADC4_IN4_0     (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN14)
#define GPIO_ADC4_IN5_0     (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN15)
#define GPIO_ADC4_IN6_0     (GPIO_ANALOG|GPIO_PORTE|GPIO_PIN8)
#define GPIO_ADC4_IN7_0     (GPIO_ANALOG|GPIO_PORTD|GPIO_PIN10)
#define GPIO_ADC4_IN8_0     (GPIO_ANALOG|GPIO_PORTD|GPIO_PIN11)
#define GPIO_ADC4_IN9_0     (GPIO_ANALOG|GPIO_PORTD|GPIO_PIN12)
#define GPIO_ADC4_IN10_0    (GPIO_ANALOG|GPIO_PORTD|GPIO_PIN13)
#define GPIO_ADC4_IN11_0    (GPIO_ANALOG|GPIO_PORTD|GPIO_PIN14)
#define GPIO_ADC4_IN12_0    (GPIO_ANALOG|GPIO_PORTD|GPIO_PIN8)
#define GPIO_ADC4_IN13_0    (GPIO_ANALOG|GPIO_PORTD|GPIO_PIN9)

/* CAN */

#define GPIO_CAN_RX_1       (GPIO_ALT|GPIO_AF7|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN0)
#define GPIO_CAN_RX_2       (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN11)
#define GPIO_CAN_RX_3       (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN8)
#define GPIO_CAN_TX_1       (GPIO_ALT|GPIO_AF7|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN1)
#define GPIO_CAN_TX_2       (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN12)
#define GPIO_CAN_TX_3       (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN9)

/* Comparator Outputs */

#define GPIO_COMP1_OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_PORTF|GPIO_PIN4)
#define GPIO_COMP1_OUT_2    (GPIO_ALT|GPIO_AF8|GPIO_PORTA|GPIO_PIN0)
#define GPIO_COMP1_OUT_3    (GPIO_ALT|GPIO_AF8|GPIO_PORTA|GPIO_PIN11)
#define GPIO_COMP1_OUT_4    (GPIO_ALT|GPIO_AF8|GPIO_PORTA|GPIO_PIN6)
#define GPIO_COMP1_OUT_5    (GPIO_ALT|GPIO_AF8|GPIO_PORTB|GPIO_PIN8)
#define GPIO_COMP2_OUT_1    (GPIO_ALT|GPIO_AF8|GPIO_PORTA|GPIO_PIN12)
#define GPIO_COMP2_OUT_2    (GPIO_ALT|GPIO_AF8|GPIO_PORTA|GPIO_PIN2)
#define GPIO_COMP2_OUT_3    (GPIO_ALT|GPIO_AF8|GPIO_PORTA|GPIO_PIN7)
#define GPIO_COMP2_OUT_4    (GPIO_ALT|GPIO_AF8|GPIO_PORTB|GPIO_PIN9)
#define GPIO_COMP3_OUT_1    (GPIO_ALT|GPIO_AF7|GPIO_PORTC|GPIO_PIN8)
#define GPIO_COMP3_OUT_2    (GPIO_ALT|GPIO_AF8|GPIO_PORTA|GPIO_PIN8)
#define GPIO_COMP4_OUT_0    (GPIO_ALT|GPIO_AF8|GPIO_PORTB|GPIO_PIN1)
#define GPIO_COMP5_OUT_1    (GPIO_ALT|GPIO_AF7|GPIO_PORTC|GPIO_PIN7)
#define GPIO_COMP5_OUT_2    (GPIO_ALT|GPIO_AF8|GPIO_PORTA|GPIO_PIN9)
#define GPIO_COMP6_OUT_1    (GPIO_ALT|GPIO_AF7|GPIO_PORTC|GPIO_PIN6)
#define GPIO_COMP6_OUT_2    (GPIO_ALT|GPIO_AF8|GPIO_PORTA|GPIO_PIN10)
#define GPIO_COMP7_OUT_0    (GPIO_ALT|GPIO_AF3|GPIO_PORTC|GPIO_PIN2)

/* DAC -" Once the DAC channelx is enabled, the corresponding GPIO pin
 * (PA4 or PA5) is automatically connected to the analog converter output
 * (DAC_OUTx). In order to avoid parasitic consumption, the PA4 or PA5 pin
 * should first be configured to analog (AIN)".
 */

#define GPIO_DAC1_OUT1_0     (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN4)
#define GPIO_DAC1_OUT2_0     (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN5)

/* I2C */

#define GPIO_I2C1_SCL_1     (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTA|GPIO_PIN15)
#define GPIO_I2C1_SCL_2     (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN6)
#define GPIO_I2C1_SCL_3     (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN8)
#define GPIO_I2C1_SDA_1     (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTA|GPIO_PIN14)
#define GPIO_I2C1_SDA_2     (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN7)
#define GPIO_I2C1_SDA_3     (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN9)
#define GPIO_I2C1_SMBA_0    (GPIO_ALT|GPIO_AF4|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN5)

#define GPIO_I2C2_SCL_1     (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTA|GPIO_PIN9)
#define GPIO_I2C2_SCL_2     (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTF|GPIO_PIN1)
#define GPIO_I2C2_SCL_3     (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTF|GPIO_PIN6)
#define GPIO_I2C2_SDA_1     (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTA|GPIO_PIN10)
#define GPIO_I2C2_SDA_2     (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTF|GPIO_PIN0)
#define GPIO_I2C2_SMBA_1    (GPIO_ALT|GPIO_AF4|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN8)
#define GPIO_I2C2_SMBA_2    (GPIO_ALT|GPIO_AF4|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN12)

/* I2S */

#define GPIO_I2S2_CK_0      (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN13)
#define GPIO_I2S2_MCK_1     (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN8)
#define GPIO_I2S2_MCK_2     (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN6)
#define GPIO_I2S2_SD_0      (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN15)
#define GPIO_I2S2_WS_0      (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN12)
#define GPIO_I2S2EXT_SD_1   (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN14)
#define GPIO_I2S3EXT_SD_2   (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN4)
#define GPIO_I2S3EXT_SD_3   (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN11)

#define GPIO_I2S3_CK_1      (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN3)
#define GPIO_I2S3_CK_2      (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN10)
#define GPIO_I2S3_MCK_1     (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN9)
#define GPIO_I2S3_MCK_2     (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN7)
#define GPIO_I2S3_SD_1      (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN5)
#define GPIO_I2S3_SD_2      (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN12)
#define GPIO_I2S3_WS_1      (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN15)
#define GPIO_I2S3_WS_2      (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN4)

#define GPIO_I2S_CKIN_0     (GPIO_ALT|GPIO_AF5|GPIO_PORTC|GPIO_PIN9)

/* IR */

#define GPIO_IR_OUT_1       (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN13)
#define GPIO_IR_OUT_2       (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN9)

/* JTAG/SWD */

#define GPIO_JTDI_0           (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN15)
#define GPIO_JTDO_TRACES_WO_0 (GPIO_ALT|GPIO_AF0|GPIO_PORTB|GPIO_PIN3)
#define GPIO_NJTRST_0         (GPIO_ALT|GPIO_AF0|GPIO_PORTB|GPIO_PIN4)
#define GPIO_SWCLK_JTCK_0     (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN14)
#define GPIO_SWDIO_JTMS_0     (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN13)

/* MCO */

#define GPIO_MCO_0          (GPIO_ALT|GPIO_AF0|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN8)

/* SPI */

#define GPIO_SPI1_MISO_1    (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN6)
#define GPIO_SPI1_MISO_2    (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN4)
#define GPIO_SPI1_MOSI_1    (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN7)
#define GPIO_SPI1_MOSI_2    (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN5)
#define GPIO_SPI1_NSS_1     (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN15)
#define GPIO_SPI1_NSS_2     (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN4)
#define GPIO_SPI1_SCK_1     (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN5)
#define GPIO_SPI1_SCK_2     (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN3)

#define GPIO_SPI2_MISO_0    (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN14)
#define GPIO_SPI2_MOSI_0    (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN15)
#define GPIO_SPI2_NSS_1     (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN12)
#define GPIO_SPI2_NSS_2     (GPIO_ALT|GPIO_AF6|GPIO_PORTD|GPIO_PIN15)
#define GPIO_SPI2_SCK_1     (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN13)
#define GPIO_SPI2_SCK_2     (GPIO_ALT|GPIO_AF5|GPIO_PORTF|GPIO_PIN10)
#define GPIO_SPI2_SCK_3     (GPIO_ALT|GPIO_AF5|GPIO_PORTF|GPIO_PIN9)

#define GPIO_SPI3_MISO_1    (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN4)
#define GPIO_SPI3_MISO_2    (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN11)
#define GPIO_SPI3_MOSI_1    (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN5)
#define GPIO_SPI3_MOSI_2    (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN12)
#define GPIO_SPI3_NSS_1     (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN15)
#define GPIO_SPI3_NSS_2     (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN4)
#define GPIO_SPI3_SCK_1     (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN3)
#define GPIO_SPI3_SCK_2     (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN10)

/* Timers */

#define GPIO_TIM1_BKIN_1    (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN8)
#define GPIO_TIM1_BKIN_2    (GPIO_ALT|GPIO_AF2|GPIO_PORTE|GPIO_PIN15)
#define GPIO_TIM1_BKIN_3    (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN14)
#define GPIO_TIM1_BKIN_4    (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM1_BKIN_5    (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN12)
#define GPIO_TIM1_BKIN_6    (GPIO_ALT|GPIO_AF9|GPIO_PORTA|GPIO_PIN15)
#define GPIO_TIM1_BKIN2_1   (GPIO_ALT|GPIO_AF12|GPIO_PORTA|GPIO_PIN11)
#define GPIO_TIM1_BKIN2_2   (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN3)
#define GPIO_TIM1_BKIN2_3   (GPIO_ALT|GPIO_AF6|GPIO_PORTE|GPIO_PIN14)
#define GPIO_TIM1_CH1IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTE|GPIO_PIN9)
#define GPIO_TIM1_CH1OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTE|GPIO_PIN9)
#define GPIO_TIM1_CH1IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF6|GPIO_PORTA|GPIO_PIN8)
#define GPIO_TIM1_CH1OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF6|GPIO_PORTA|GPIO_PIN8)
#define GPIO_TIM1_CH1N_1    (GPIO_ALT|GPIO_AF2|GPIO_PORTE|GPIO_PIN8)
#define GPIO_TIM1_CH1N_2    (GPIO_ALT|GPIO_AF4|GPIO_PORTC|GPIO_PIN13)
#define GPIO_TIM1_CH1N_3    (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN11)
#define GPIO_TIM1_CH1N_4    (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM1_CH1N_5    (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN13)
#define GPIO_TIM1_CH2IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTE|GPIO_PIN11)
#define GPIO_TIM1_CH2OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTE|GPIO_PIN11)
#define GPIO_TIM1_CH2IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF6|GPIO_PORTA|GPIO_PIN9)
#define GPIO_TIM1_CH2OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF6|GPIO_PORTA|GPIO_PIN9)
#define GPIO_TIM1_CH2N_1    (GPIO_ALT|GPIO_AF2|GPIO_PORTE|GPIO_PIN10)
#define GPIO_TIM1_CH2N_2    (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN12)
#define GPIO_TIM1_CH2N_3    (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN0)
#define GPIO_TIM1_CH2N_4    (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN14)
#define GPIO_TIM1_CH3IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTE|GPIO_PIN13)
#define GPIO_TIM1_CH3OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTE|GPIO_PIN13)
#define GPIO_TIM1_CH3IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF6|GPIO_PORTA|GPIO_PIN10)
#define GPIO_TIM1_CH3OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF6|GPIO_PORTA|GPIO_PIN10)
#define GPIO_TIM1_CH3N_1    (GPIO_ALT|GPIO_AF2|GPIO_PORTE|GPIO_PIN12)
#define GPIO_TIM1_CH3N_2    (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN15)
#define GPIO_TIM1_CH3N_3    (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN1)
#define GPIO_TIM1_CH3N_4    (GPIO_ALT|GPIO_AF6|GPIO_PORTF|GPIO_PIN0)
#define GPIO_TIM1_CH4IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF11|GPIO_PORTA|GPIO_PIN11)
#define GPIO_TIM1_CH4OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF11|GPIO_PORTA|GPIO_PIN11)
#define GPIO_TIM1_CH4IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTE|GPIO_PIN14)
#define GPIO_TIM1_CH4OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTE|GPIO_PIN14)
#define GPIO_TIM1_ETR_1     (GPIO_ALT|GPIO_FLOAT|GPIO_AF11|GPIO_PORTA|GPIO_PIN12)
#define GPIO_TIM1_ETR_2     (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTE|GPIO_PIN7)

#define GPIO_TIM2_CH1_ETR_1 (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM2_CH1_ETR_2 (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTA|GPIO_PIN15)
#define GPIO_TIM2_CH1_ETR_3 (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTA|GPIO_PIN5)
#define GPIO_TIM2_CH1_ETR_4 (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTD|GPIO_PIN3)
#define GPIO_TIM2_CH1IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM2_CH1OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF1|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM2_CH1IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTA|GPIO_PIN15)
#define GPIO_TIM2_CH1OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF1|GPIO_PORTA|GPIO_PIN15)
#define GPIO_TIM2_CH1IN_3   (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTA|GPIO_PIN5)
#define GPIO_TIM2_CH1OUT_3  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF1|GPIO_PORTA|GPIO_PIN5)
#define GPIO_TIM2_CH1IN_4   (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTD|GPIO_PIN3)
#define GPIO_TIM2_CH1OUT_4  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF1|GPIO_PORTD|GPIO_PIN3)
#define GPIO_TIM2_CH2IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTA|GPIO_PIN1)
#define GPIO_TIM2_CH2OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF1|GPIO_PORTA|GPIO_PIN1)
#define GPIO_TIM2_CH2IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTB|GPIO_PIN3)
#define GPIO_TIM2_CH2OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF1|GPIO_PORTB|GPIO_PIN3)
#define GPIO_TIM2_CH2IN_3   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTD|GPIO_PIN4)
#define GPIO_TIM2_CH2OUT_3  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTD|GPIO_PIN4)
#define GPIO_TIM2_CH3IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF10|GPIO_PORTA|GPIO_PIN9)
#define GPIO_TIM2_CH3OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF10|GPIO_PORTA|GPIO_PIN9)
#define GPIO_TIM2_CH3IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM2_CH3OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF1|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM2_CH3IN_3   (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTB|GPIO_PIN10)
#define GPIO_TIM2_CH3OUT_3  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF1|GPIO_PORTB|GPIO_PIN10)
#define GPIO_TIM2_CH3IN_4   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTD|GPIO_PIN7)
#define GPIO_TIM2_CH3OUT_4  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTD|GPIO_PIN7)
#define GPIO_TIM2_CH4IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF10|GPIO_PORTA|GPIO_PIN10)
#define GPIO_TIM2_CH4OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF10|GPIO_PORTA|GPIO_PIN10)
#define GPIO_TIM2_CH4IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM2_CH4OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF1|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM2_CH4IN_3   (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTB|GPIO_PIN11)
#define GPIO_TIM2_CH4OUT_3  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF1|GPIO_PORTB|GPIO_PIN11)
#define GPIO_TIM2_CH4IN_4   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTD|GPIO_PIN6)
#define GPIO_TIM2_CH4OUT_4  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTD|GPIO_PIN6)

#define GPIO_TIM3_CH1IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM3_CH1OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM3_CH1IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTB|GPIO_PIN4)
#define GPIO_TIM3_CH1OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTB|GPIO_PIN4)
#define GPIO_TIM3_CH1IN_3   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTC|GPIO_PIN6)
#define GPIO_TIM3_CH1OUT_3  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTC|GPIO_PIN6)
#define GPIO_TIM3_CH1IN_4   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTE|GPIO_PIN2)
#define GPIO_TIM3_CH1OUT_4  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTE|GPIO_PIN2)
#define GPIO_TIM3_CH2IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTA|GPIO_PIN4)
#define GPIO_TIM3_CH2OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTA|GPIO_PIN4)
#define GPIO_TIM3_CH2IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM3_CH2OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM3_CH2IN_3   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTB|GPIO_PIN5)
#define GPIO_TIM3_CH2OUT_3  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTB|GPIO_PIN5)
#define GPIO_TIM3_CH2IN_4   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTC|GPIO_PIN7)
#define GPIO_TIM3_CH2OUT_4  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTC|GPIO_PIN7)
#define GPIO_TIM3_CH2IN_5   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTE|GPIO_PIN3)
#define GPIO_TIM3_CH2OUT_5  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTE|GPIO_PIN3)
#define GPIO_TIM3_CH3IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTB|GPIO_PIN0)
#define GPIO_TIM3_CH3OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTB|GPIO_PIN0)
#define GPIO_TIM3_CH3IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTC|GPIO_PIN8)
#define GPIO_TIM3_CH3OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTC|GPIO_PIN8)
#define GPIO_TIM3_CH3IN_3   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTE|GPIO_PIN4)
#define GPIO_TIM3_CH3OUT_3  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTE|GPIO_PIN4)
#define GPIO_TIM3_CH4IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF10|GPIO_PORTB|GPIO_PIN7)
#define GPIO_TIM3_CH4OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF10|GPIO_PORTB|GPIO_PIN7)
#define GPIO_TIM3_CH4IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTB|GPIO_PIN1)
#define GPIO_TIM3_CH4OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTB|GPIO_PIN1)
#define GPIO_TIM3_CH4IN_3   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTC|GPIO_PIN9)
#define GPIO_TIM3_CH4OUT_3  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTC|GPIO_PIN9)
#define GPIO_TIM3_CH4IN_4   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTE|GPIO_PIN5)
#define GPIO_TIM3_CH4OUT_4  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTE|GPIO_PIN5)
#define GPIO_TIM3_ETR_1     (GPIO_ALT|GPIO_FLOAT|GPIO_AF10|GPIO_PORTB|GPIO_PIN3)
#define GPIO_TIM3_ETR_2     (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTD|GPIO_PIN2)

#define GPIO_TIM4_CH1IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF10|GPIO_PORTA|GPIO_PIN11)
#define GPIO_TIM4_CH1OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF10|GPIO_PORTA|GPIO_PIN11)
#define GPIO_TIM4_CH1IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTB|GPIO_PIN6)
#define GPIO_TIM4_CH1OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTB|GPIO_PIN6)
#define GPIO_TIM4_CH1IN_3   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTD|GPIO_PIN12)
#define GPIO_TIM4_CH1OUT_3  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTD|GPIO_PIN12)
#define GPIO_TIM4_CH2IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF10|GPIO_PORTA|GPIO_PIN12)
#define GPIO_TIM4_CH2OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF10|GPIO_PORTA|GPIO_PIN12)
#define GPIO_TIM4_CH2IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTB|GPIO_PIN7)
#define GPIO_TIM4_CH2OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTB|GPIO_PIN7)
#define GPIO_TIM4_CH2IN_3   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTD|GPIO_PIN13)
#define GPIO_TIM4_CH2OUT_3  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTD|GPIO_PIN13)
#define GPIO_TIM4_CH3IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF10|GPIO_PORTA|GPIO_PIN13)
#define GPIO_TIM4_CH3OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF10|GPIO_PORTA|GPIO_PIN13)
#define GPIO_TIM4_CH3IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTB|GPIO_PIN8)
#define GPIO_TIM4_CH3OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTB|GPIO_PIN8)
#define GPIO_TIM4_CH3IN_3   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTD|GPIO_PIN14)
#define GPIO_TIM4_CH3OUT_3  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTD|GPIO_PIN14)
#define GPIO_TIM4_CH4IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTB|GPIO_PIN9)
#define GPIO_TIM4_CH4OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTB|GPIO_PIN9)
#define GPIO_TIM4_CH4IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTD|GPIO_PIN15)
#define GPIO_TIM4_CH4OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTD|GPIO_PIN15)
#define GPIO_TIM4_CH4IN_3   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTF|GPIO_PIN6)
#define GPIO_TIM4_CH4OUT_3  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTF|GPIO_PIN6)
#define GPIO_TIM4_ETR_1     (GPIO_ALT|GPIO_FLOAT|GPIO_AF10|GPIO_PORTA|GPIO_PIN8)
#define GPIO_TIM4_ETR_2     (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTB|GPIO_PIN3)
#define GPIO_TIM4_ETR_3     (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTE|GPIO_PIN0)

#define GPIO_TIM8_BKIN_1    (GPIO_ALT|GPIO_AF11|GPIO_PORTA|GPIO_PIN10)
#define GPIO_TIM8_BKIN_2    (GPIO_ALT|GPIO_AF4|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM8_BKIN_3    (GPIO_ALT|GPIO_AF4|GPIO_PORTD|GPIO_PIN2)
#define GPIO_TIM8_BKIN_4    (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN7)
#define GPIO_TIM8_BKIN_5    (GPIO_ALT|GPIO_AF9|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM8_BKIN2_1   (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN6)
#define GPIO_TIM8_BKIN2_2   (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN9)
#define GPIO_TIM8_BKIN2_3   (GPIO_ALT|GPIO_AF6|GPIO_PORTD|GPIO_PIN1)
#define GPIO_TIM8_CH1IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF2|GPIO_PORTA|GPIO_PIN15)
#define GPIO_TIM8_CH1OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF2|GPIO_PORTA|GPIO_PIN15)
#define GPIO_TIM8_CH1IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF4|GPIO_PORTC|GPIO_PIN6)
#define GPIO_TIM8_CH1OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF4|GPIO_PORTC|GPIO_PIN6)
#define GPIO_TIM8_CH1IN_3   (GPIO_ALT|GPIO_FLOAT|GPIO_AF5|GPIO_PORTB|GPIO_PIN6)
#define GPIO_TIM8_CH1OUT_3  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF5|GPIO_PORTB|GPIO_PIN6)
#define GPIO_TIM8_CH1N_1    (GPIO_ALT|GPIO_AF4|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM8_CH1N_2    (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN3)
#define GPIO_TIM8_CH1N_3    (GPIO_ALT|GPIO_AF4|GPIO_PORTC|GPIO_PIN10)
#define GPIO_TIM8_CH2IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF10|GPIO_PORTB|GPIO_PIN8)
#define GPIO_TIM8_CH2OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF10|GPIO_PORTB|GPIO_PIN8)
#define GPIO_TIM8_CH2IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF4|GPIO_PORTC|GPIO_PIN7)
#define GPIO_TIM8_CH2OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF4|GPIO_PORTC|GPIO_PIN7)
#define GPIO_TIM8_CH2IN_3   (GPIO_ALT|GPIO_FLOAT|GPIO_AF5|GPIO_PORTA|GPIO_PIN14)
#define GPIO_TIM8_CH2OUT_3  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF5|GPIO_PORTA|GPIO_PIN14)
#define GPIO_TIM8_CH2N_1    (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN0)
#define GPIO_TIM8_CH2N_2    (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN4)
#define GPIO_TIM8_CH2N_3    (GPIO_ALT|GPIO_AF4|GPIO_PORTC|GPIO_PIN11)
#define GPIO_TIM8_CH3IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF10|GPIO_PORTB|GPIO_PIN9)
#define GPIO_TIM8_CH3OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF10|GPIO_PORTB|GPIO_PIN9)
#define GPIO_TIM8_CH3IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF4|GPIO_PORTC|GPIO_PIN8)
#define GPIO_TIM8_CH3OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF4|GPIO_PORTC|GPIO_PIN8)
#define GPIO_TIM8_CH3N_1    (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN5)
#define GPIO_TIM8_CH3N_2    (GPIO_ALT|GPIO_AF4|GPIO_PORTB|GPIO_PIN1)
#define GPIO_TIM8_CH3N_3    (GPIO_ALT|GPIO_AF4|GPIO_PORTC|GPIO_PIN12)
#define GPIO_TIM8_CH4IN_1   (GPIO_ALT|GPIO_FLOAT|GPIO_AF4|GPIO_PORTC|GPIO_PIN9)
#define GPIO_TIM8_CH4OUT_1  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF4|GPIO_PORTC|GPIO_PIN9)
#define GPIO_TIM8_CH4IN_2   (GPIO_ALT|GPIO_FLOAT|GPIO_AF4|GPIO_PORTD|GPIO_PIN1)
#define GPIO_TIM8_CH4OUT_2  (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF4|GPIO_PORTD|GPIO_PIN1)
#define GPIO_TIM8_ETR_1     (GPIO_ALT|GPIO_FLOAT|GPIO_AF10|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM8_ETR_2     (GPIO_ALT|GPIO_FLOAT|GPIO_AF6|GPIO_PORTB|GPIO_PIN6)

#define GPIO_TIM15_BKIN_0   (GPIO_ALT|GPIO_AF9|GPIO_PORTA|GPIO_PIN9)
#define GPIO_TIM15_CH1IN_1  (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTB|GPIO_PIN14)
#define GPIO_TIM15_CH1OUT_1 (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF1|GPIO_PORTB|GPIO_PIN14)
#define GPIO_TIM15_CH1IN_2  (GPIO_ALT|GPIO_FLOAT|GPIO_AF3|GPIO_PORTF|GPIO_PIN9)
#define GPIO_TIM15_CH1OUT_2 (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF3|GPIO_PORTF|GPIO_PIN9)
#define GPIO_TIM15_CH1IN_3  (GPIO_ALT|GPIO_FLOAT|GPIO_AF9|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM15_CH1OUT_3 (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF9|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM15_CH1N_1   (GPIO_ALT|GPIO_AF2|GPIO_PORTB|GPIO_PIN15)
#define GPIO_TIM15_CH1N_2   (GPIO_ALT|GPIO_AF9|GPIO_PORTA|GPIO_PIN1)
#define GPIO_TIM15_CH2IN_1  (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTB|GPIO_PIN15)
#define GPIO_TIM15_CH2OUT_1 (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF1|GPIO_PORTB|GPIO_PIN15)
#define GPIO_TIM15_CH2IN_2  (GPIO_ALT|GPIO_FLOAT|GPIO_AF3|GPIO_PORTF|GPIO_PIN10)
#define GPIO_TIM15_CH2OUT_2 (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF3|GPIO_PORTF|GPIO_PIN10)
#define GPIO_TIM15_CH2IN_3  (GPIO_ALT|GPIO_FLOAT|GPIO_AF9|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM15_CH2OUT_3 (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF9|GPIO_PORTA|GPIO_PIN3)

#define GPIO_TIM16_BKIN_0   (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN5)
#define GPIO_TIM16_CH1IN_1  (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTA|GPIO_PIN12)
#define GPIO_TIM16_CH1OUT_1 (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF1|GPIO_PORTA|GPIO_PIN12)
#define GPIO_TIM16_CH1IN_2  (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM16_CH1OUT_2 (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF1|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM16_CH1IN_3  (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTB|GPIO_PIN4)
#define GPIO_TIM16_CH1OUT_3 (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF1|GPIO_PORTB|GPIO_PIN4)
#define GPIO_TIM16_CH1IN_4  (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTB|GPIO_PIN8)
#define GPIO_TIM16_CH1OUT_4 (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF1|GPIO_PORTB|GPIO_PIN8)
#define GPIO_TIM16_CH1IN_5  (GPIO_ALT|GPIO_FLOAT|GPIO_AF4|GPIO_PORTE|GPIO_PIN0)
#define GPIO_TIM16_CH1OUT_5 (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF4|GPIO_PORTE|GPIO_PIN0)
#define GPIO_TIM16_CH1N_1   (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN13)
#define GPIO_TIM16_CH1N_2   (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN6)

#define GPIO_TIM17_BKIN_1   (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN4)
#define GPIO_TIM17_BKIN_2   (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN10)
#define GPIO_TIM17_CH1IN_1  (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM17_CH1OUT_1 (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF1|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM17_CH1IN_2  (GPIO_ALT|GPIO_FLOAT|GPIO_AF10|GPIO_PORTB|GPIO_PIN5)
#define GPIO_TIM17_CH1OUT_2 (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF10|GPIO_PORTB|GPIO_PIN5)
#define GPIO_TIM17_CH1IN_3  (GPIO_ALT|GPIO_FLOAT|GPIO_AF1|GPIO_PORTB|GPIO_PIN9)
#define GPIO_TIM17_CH1OUT_3 (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF1|GPIO_PORTB|GPIO_PIN9)
#define GPIO_TIM17_CH1IN_4  (GPIO_ALT|GPIO_FLOAT|GPIO_AF4|GPIO_PORTE|GPIO_PIN1)
#define GPIO_TIM17_CH1OUT_4 (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF4|GPIO_PORTE|GPIO_PIN1)
#define GPIO_TIM17_CH1N_0   (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN7)

/* TSC */

#define GPIO_TSC_G1_IO1_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TSC_G1_IO2_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN1)
#define GPIO_TSC_G1_IO3_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TSC_G1_IO4_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TSC_G2_IO1_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN4)
#define GPIO_TSC_G2_IO2_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN5)
#define GPIO_TSC_G2_IO3_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TSC_G2_IO4_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TSC_G3_IO1_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTC|GPIO_PIN5)
#define GPIO_TSC_G3_IO2_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN0)
#define GPIO_TSC_G3_IO3_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN1)
#define GPIO_TSC_G3_IO4_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN2)
#define GPIO_TSC_G4_IO1_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN9)
#define GPIO_TSC_G4_IO2_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN10)
#define GPIO_TSC_G4_IO3_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN13)
#define GPIO_TSC_G4_IO4_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN14)
#define GPIO_TSC_G5_IO1_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN3)
#define GPIO_TSC_G5_IO2_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN4)
#define GPIO_TSC_G5_IO3_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN6)
#define GPIO_TSC_G5_IO4_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN7)
#define GPIO_TSC_G6_IO1_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN11)
#define GPIO_TSC_G6_IO2_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN12)
#define GPIO_TSC_G6_IO3_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN13)
#define GPIO_TSC_G6_IO4_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN14)
#define GPIO_TSC_G7_IO1_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTE|GPIO_PIN2)
#define GPIO_TSC_G7_IO2_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTE|GPIO_PIN3)
#define GPIO_TSC_G7_IO3_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTE|GPIO_PIN4)
#define GPIO_TSC_G7_IO4_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTE|GPIO_PIN5)
#define GPIO_TSC_G8_IO1_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTD|GPIO_PIN12)
#define GPIO_TSC_G8_IO2_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTD|GPIO_PIN13)
#define GPIO_TSC_G8_IO3_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTD|GPIO_PIN14)
#define GPIO_TSC_G8_IO4_0   (GPIO_ALT|GPIO_AF3|GPIO_PORTD|GPIO_PIN15)
#define GPIO_TSC_SYNC_1     (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN10)
#define GPIO_TSC_SYNC_2     (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN8)

/* Trace */

#define GPIO_TRACECK_0      (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN2)
#define GPIO_TRACED0_0      (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN3)
#define GPIO_TRACED1_0      (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN4)
#define GPIO_TRACED2_0      (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN5)
#define GPIO_TRACED3_0      (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN6)

/* USARTs/UARTs */

#define GPIO_USART1_CK_0    (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN8)
#define GPIO_USART1_CTS_0   (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN11)
#define GPIO_USART1_RTS_0   (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN12)
#define GPIO_USART1_RX_1    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTA|GPIO_PIN10)
#define GPIO_USART1_RX_2    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTB|GPIO_PIN7)
#define GPIO_USART1_RX_3    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTC|GPIO_PIN5)
#define GPIO_USART1_RX_4    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTE|GPIO_PIN1)
#define GPIO_USART1_TX_1    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTA|GPIO_PIN9)
#define GPIO_USART1_TX_2    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTB|GPIO_PIN6)
#define GPIO_USART1_TX_3    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTC|GPIO_PIN4)
#define GPIO_USART1_TX_4    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTE|GPIO_PIN0)

#define GPIO_USART2_CK_1    (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN4)
#define GPIO_USART2_CK_2    (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN5)
#define GPIO_USART2_CK_3    (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN7)
#define GPIO_USART2_CTS_1   (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN0)
#define GPIO_USART2_CTS_2   (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN3)
#define GPIO_USART2_RTS_1   (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN1)
#define GPIO_USART2_RTS_2   (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN4)
#define GPIO_USART2_RX_1    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTA|GPIO_PIN15)
#define GPIO_USART2_RX_2    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTA|GPIO_PIN3)
#define GPIO_USART2_RX_3    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTB|GPIO_PIN4)
#define GPIO_USART2_RX_4    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTD|GPIO_PIN6)
#define GPIO_USART2_TX_1    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTA|GPIO_PIN14)
#define GPIO_USART2_TX_2    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTA|GPIO_PIN2)
#define GPIO_USART2_TX_3    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTB|GPIO_PIN3)
#define GPIO_USART2_TX_4    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTD|GPIO_PIN5)

#define GPIO_USART3_CK_1    (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN12)
#define GPIO_USART3_CK_2    (GPIO_ALT|GPIO_AF7|GPIO_PORTC|GPIO_PIN12)
#define GPIO_USART3_CK_3    (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN10)
#define GPIO_USART3_CTS_1   (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN13)
#define GPIO_USART3_CTS_2   (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN13)
#define GPIO_USART3_CTS_3   (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN11)
#define GPIO_USART3_RTS_1   (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN14)
#define GPIO_USART3_RTS_2   (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN12)
#define GPIO_USART3_RTS_3   (GPIO_ALT|GPIO_AF7|GPIO_PORTF|GPIO_PIN6)
#define GPIO_USART3_RX_1    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTB|GPIO_PIN11)
#define GPIO_USART3_RX_2    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTC|GPIO_PIN11)
#define GPIO_USART3_RX_3    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTD|GPIO_PIN9)
#define GPIO_USART3_RX_4    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTE|GPIO_PIN15)
#define GPIO_USART3_TX_1    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTB|GPIO_PIN10)
#define GPIO_USART3_TX_2    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTC|GPIO_PIN10)
#define GPIO_USART3_TX_3    (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF7|GPIO_PULLUP|GPIO_PORTD|GPIO_PIN8)

#define GPIO_UART4_RX_0     (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF5|GPIO_PULLUP|GPIO_PORTC|GPIO_PIN11)
#define GPIO_UART4_TX_0     (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF5|GPIO_PULLUP|GPIO_PORTC|GPIO_PIN10)

#define GPIO_UART5_RX_0     (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF5|GPIO_PULLUP|GPIO_PORTD|GPIO_PIN2)
#define GPIO_UART5_TX_0     (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF5|GPIO_PULLUP|GPIO_PORTC|GPIO_PIN12)

/* USB */

#define GPIO_USB_DM_0       (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF14|GPIO_PORTA|GPIO_PIN11)
#define GPIO_USB_DP_0       (GPIO_ALT|GPIO_PUSHPULL|GPIO_AF14|GPIO_PORTA|GPIO_PIN12)

/* Event Outputs */

#define GPIO_PA0_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN0)
#define GPIO_PA0_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN0)
#define GPIO_PA0_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN0)
#define GPIO_PA0_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN0)
#define GPIO_PA1_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN1)
#define GPIO_PA2_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN2)
#define GPIO_PA3_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN3)
#define GPIO_PA4_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN4)
#define GPIO_PA5_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN5)
#define GPIO_PA6_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN6)
#define GPIO_PA7_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN7)
#define GPIO_PA8_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN8)
#define GPIO_PA9_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN9)
#define GPIO_PA10_EVENT_OUT_0 (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN10)
#define GPIO_PA11_EVENT_OUT_0 (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN11)
#define GPIO_PA12_EVENT_OUT_0 (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN12)
#define GPIO_PA13_EVENT_OUT_0 (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN13)
#define GPIO_PA14_EVENT_OUT_0 (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN14)
#define GPIO_PA15_EVENT_OUT_0 (GPIO_ALT|GPIO_AF15|GPIO_PORTA|GPIO_PIN15)

#define GPIO_PB0_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN0)
#define GPIO_PB1_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN1)
#define GPIO_PB2_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN2)
#define GPIO_PB3_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN3)
#define GPIO_PB4_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN4)
#define GPIO_PB5_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN5)
#define GPIO_PB6_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN6)
#define GPIO_PB7_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN7)
#define GPIO_PB8_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN8)
#define GPIO_PB9_EVENT_OUT_0  (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN9)
#define GPIO_PB10_EVENT_OUT_0 (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN10)
#define GPIO_PB11_EVENT_OUT_0 (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN11)
#define GPIO_PB12_EVENT_OUT_0 (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN12)
#define GPIO_PB13_EVENT_OUT_0 (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN13)
#define GPIO_PB14_EVENT_OUT_0 (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN14)
#define GPIO_PB15_EVENT_OUT_0 (GPIO_ALT|GPIO_AF15|GPIO_PORTB|GPIO_PIN15)

#define GPIO_PC0_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTC|GPIO_PIN0)
#define GPIO_PC1_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTC|GPIO_PIN1)
#define GPIO_PC2_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTC|GPIO_PIN2)
#define GPIO_PC3_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTC|GPIO_PIN3)
#define GPIO_PC4_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTC|GPIO_PIN4)
#define GPIO_PC5_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTC|GPIO_PIN5)
#define GPIO_PC6_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTC|GPIO_PIN6)
#define GPIO_PC7_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTC|GPIO_PIN7)
#define GPIO_PC8_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTC|GPIO_PIN8)
#define GPIO_PC9_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTC|GPIO_PIN9)
#define GPIO_PC10_EVENT_OUT_0 (GPIO_ALT|GPIO_AF1|GPIO_PORTC|GPIO_PIN10)
#define GPIO_PC11_EVENT_OUT_0 (GPIO_ALT|GPIO_AF1|GPIO_PORTC|GPIO_PIN11)
#define GPIO_PC12_EVENT_OUT_0 (GPIO_ALT|GPIO_AF1|GPIO_PORTC|GPIO_PIN12)

#define GPIO_PD0_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTD|GPIO_PIN0)
#define GPIO_PD1_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTD|GPIO_PIN1)
#define GPIO_PD2_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTD|GPIO_PIN2)
#define GPIO_PD3_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTD|GPIO_PIN3)
#define GPIO_PD4_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTD|GPIO_PIN4)
#define GPIO_PD5_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTD|GPIO_PIN5)
#define GPIO_PD6_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTD|GPIO_PIN6)
#define GPIO_PD7_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTD|GPIO_PIN7)
#define GPIO_PD8_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTD|GPIO_PIN8)
#define GPIO_PD9_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTD|GPIO_PIN9)
#define GPIO_PD10_EVENT_OUT_0 (GPIO_ALT|GPIO_AF1|GPIO_PORTD|GPIO_PIN10)
#define GPIO_PD11_EVENT_OUT_0 (GPIO_ALT|GPIO_AF1|GPIO_PORTD|GPIO_PIN11)
#define GPIO_PD12_EVENT_OUT_0 (GPIO_ALT|GPIO_AF1|GPIO_PORTD|GPIO_PIN12)
#define GPIO_PD13_EVENT_OUT_0 (GPIO_ALT|GPIO_AF1|GPIO_PORTD|GPIO_PIN13)
#define GPIO_PD14_EVENT_OUT_0 (GPIO_ALT|GPIO_AF1|GPIO_PORTD|GPIO_PIN14)
#define GPIO_PD15_EVENT_OUT_0 (GPIO_ALT|GPIO_AF1|GPIO_PORTD|GPIO_PIN15)

#define GPIO_PE0_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN0)
#define GPIO_PE1_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN1)
#define GPIO_PE2_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN2)
#define GPIO_PE3_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN3)
#define GPIO_PE4_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN4)
#define GPIO_PE5_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN5)
#define GPIO_PE6_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN6)
#define GPIO_PE7_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN7)
#define GPIO_PE8_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN8)
#define GPIO_PE9_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN9)
#define GPIO_PE10_EVENT_OUT_0 (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN10)
#define GPIO_PE11_EVENT_OUT_0 (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN11)
#define GPIO_PE12_EVENT_OUT_0 (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN12)
#define GPIO_PE13_EVENT_OUT_0 (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN13)
#define GPIO_PE14_EVENT_OUT_0 (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN14)
#define GPIO_PE15_EVENT_OUT_0 (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN15)

#define GPIO_PF2_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTF|GPIO_PIN2)
#define GPIO_PF4_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTF|GPIO_PIN4)
#define GPIO_PF6_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTF|GPIO_PIN6)
#define GPIO_PF9_EVENT_OUT_0  (GPIO_ALT|GPIO_AF1|GPIO_PORTF|GPIO_PIN9)
#define GPIO_PF10_EVENT_OUT_0 (GPIO_ALT|GPIO_AF1|GPIO_PORTF|GPIO_PIN10)

#endif /* __ARCH_ARM_SRC_STM32_HARDWARE_STM32F30XXX_PINMAP_H */
