/****************************************************************************
 * arch/arm/src/stm32/hardware/stm32l15xxx_pinmap.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_STM32_HARDWARE_STM32L15XXX_PINMAP_H
#define __ARCH_ARM_SRC_STM32_HARDWARE_STM32L15XXX_PINMAP_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

#include "stm32_gpio.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Alternate Pin Functions.
 *
 * Alternative pin selections are provided with a numeric suffix like _1, _2,
 * etc. Drivers, however, will use the pin selection without the numeric
 * suffix. Additional definitions are required in the board.h file.  For
 * example, if CAN1_RX connects via PA11 on some board, then the following
 * definitions should appear in the board.h header file for that board:
 *
 * #define GPIO_I2C1_SCL GPIO_I2C1_SCL_1
 *
 * The driver will then automatically configure PB6 as the I2C1 SCL pin.
 */

/* WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific GPIO options such as
 * frequency, open-drain/push-pull, and pull-up/down!
 *  Just the basics are defined for most pins in this file.
 */

#define GPIO_BOOT1_0       (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN2)

/* ADC
 *
 * ADC_IN16 is internal temperature sensor
 * ADC_IN17 is internal Vrefint
 */

#define GPIO_ADC1_IN0_0     (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN0)
#define GPIO_ADC1_IN1_0     (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN1)
#define GPIO_ADC1_IN2_0     (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN2)
#define GPIO_ADC1_IN3_0     (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN3)
#define GPIO_ADC1_IN4_0     (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN4)
#define GPIO_ADC1_IN5_0     (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN5)
#define GPIO_ADC1_IN6_0     (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN6)
#define GPIO_ADC1_IN7_0     (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN7)
#define GPIO_ADC1_IN8_0     (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN0)
#define GPIO_ADC1_IN9_0     (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN1)
#define GPIO_ADC1_IN10_0    (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN0)
#define GPIO_ADC1_IN11_0    (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN1)
#define GPIO_ADC1_IN12_0    (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN2)
#define GPIO_ADC1_IN13_0    (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN3)
#define GPIO_ADC1_IN14_0    (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN4)
#define GPIO_ADC1_IN15_0    (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN5)

#define GPIO_ADC1_IN18_0    (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN12)
#define GPIO_ADC1_IN19_0    (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN13)
#define GPIO_ADC1_IN20_0    (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN14)
#define GPIO_ADC1_IN21_0    (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN15)
#define GPIO_ADC1_IN22_0    (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN7)
#define GPIO_ADC1_IN23_0    (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN8)
#define GPIO_ADC1_IN24_0    (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN9)
#define GPIO_ADC1_IN25_0    (GPIO_ANALOG | GPIO_PORTE | GPIO_PIN10)

#define GPIO_ADC1_IN27_0    (GPIO_ANALOG | GPIO_PORTF | GPIO_PIN6)
#define GPIO_ADC1_IN28_0    (GPIO_ANALOG | GPIO_PORTF | GPIO_PIN7)
#define GPIO_ADC1_IN29_0    (GPIO_ANALOG | GPIO_PORTF | GPIO_PIN8)
#define GPIO_ADC1_IN30_0    (GPIO_ANALOG | GPIO_PORTF | GPIO_PIN9)
#define GPIO_ADC1_IN31_0    (GPIO_ANALOG | GPIO_PORTF | GPIO_PIN10)

/* DAC */

#define GPIO_DAC1_OUT1_0     (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN4)
#define GPIO_DAC1_OUT2_0     (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN5)

/* I2C */

#define GPIO_I2C1_SCL_1     (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN6)
#define GPIO_I2C1_SCL_2     (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN8)
#define GPIO_I2C1_SDA_1     (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN7)
#define GPIO_I2C1_SDA_2     (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN9)
#define GPIO_I2C1_SMBA_0    (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN5)
#define GPIO_I2C2_SCL_0     (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN10)
#define GPIO_I2C2_SDA_0     (GPIO_ALT | GPIO_AF4 | GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN11)
#define GPIO_I2C2_SMBA_0    (GPIO_ALT | GPIO_AF4 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN12)

/* JTAG/Trace */

#define GPIO_JTCK_SWCLK_0   (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN14)
#define GPIO_JTDI_0         (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_JTDO_0         (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_JTMS_SWDAT_0   (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN13)
#define GPIO_JTRST_0        (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN4)

#define GPIO_TRACECK_0      (GPIO_ALT | GPIO_AF0 | GPIO_PORTE | GPIO_PIN2)
#define GPIO_TRACED0_0      (GPIO_ALT | GPIO_AF0 | GPIO_PORTE | GPIO_PIN3)
#define GPIO_TRACED1_0      (GPIO_ALT | GPIO_AF0 | GPIO_PORTE | GPIO_PIN4)
#define GPIO_TRACED2_0      (GPIO_ALT | GPIO_AF0 | GPIO_PORTE | GPIO_PIN5)
#define GPIO_TRACED3_0      (GPIO_ALT | GPIO_AF0 | GPIO_PORTE | GPIO_PIN6)

/* LCD */

#define GPIO_LCD_COM0_0     (GPIO_ALT | GPIO_AF11 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_LCD_COM1_0     (GPIO_ALT | GPIO_AF11 | GPIO_PORTA | GPIO_PIN9)
#define GPIO_LCD_COM2_0     (GPIO_ALT | GPIO_AF11 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_LCD_COM3_0     (GPIO_ALT | GPIO_AF11 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_LCD_COM4_0     (GPIO_ALT | GPIO_AF11 | GPIO_PORTC | GPIO_PIN10)
#define GPIO_LCD_COM5_0     (GPIO_ALT | GPIO_AF11 | GPIO_PORTC | GPIO_PIN11)
#define GPIO_LCD_COM6_0     (GPIO_ALT | GPIO_AF11 | GPIO_PORTC | GPIO_PIN12)
#define GPIO_LCD_COM7_0     (GPIO_ALT | GPIO_AF11 | GPIO_PORTD | GPIO_PIN2)
#define GPIO_LCD_SEG0_0     (GPIO_ALT | GPIO_AF11 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_LCD_SEG1_0     (GPIO_ALT | GPIO_AF11 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_LCD_SEG2_0     (GPIO_ALT | GPIO_AF11 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_LCD_SEG3_0     (GPIO_ALT | GPIO_AF11 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_LCD_SEG4_0     (GPIO_ALT | GPIO_AF11 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_LCD_SEG5_0     (GPIO_ALT | GPIO_AF11 | GPIO_PORTB | GPIO_PIN0)
#define GPIO_LCD_SEG6_0     (GPIO_ALT | GPIO_AF11 | GPIO_PORTB | GPIO_PIN1)
#define GPIO_LCD_SEG7_0     (GPIO_ALT | GPIO_AF11 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_LCD_SEG8_0     (GPIO_ALT | GPIO_AF11 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_LCD_SEG9_0     (GPIO_ALT | GPIO_AF11 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_LCD_SEG10_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_LCD_SEG11_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTB | GPIO_PIN11)
#define GPIO_LCD_SEG12_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_LCD_SEG13_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_LCD_SEG14_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_LCD_SEG15_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTB | GPIO_PIN15)
#define GPIO_LCD_SEG16_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_LCD_SEG17_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_LCD_SEG18_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTC | GPIO_PIN0)
#define GPIO_LCD_SEG19_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTC | GPIO_PIN1)
#define GPIO_LCD_SEG20_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTC | GPIO_PIN2)
#define GPIO_LCD_SEG21_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTC | GPIO_PIN3)
#define GPIO_LCD_SEG22_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTC | GPIO_PIN4)
#define GPIO_LCD_SEG23_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTC | GPIO_PIN5)
#define GPIO_LCD_SEG24_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTC | GPIO_PIN6)
#define GPIO_LCD_SEG25_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTC | GPIO_PIN7)
#define GPIO_LCD_SEG26_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTC | GPIO_PIN8)
#define GPIO_LCD_SEG27_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTC | GPIO_PIN9)
#define GPIO_LCD_SEG28_1    (GPIO_ALT | GPIO_AF11 | GPIO_PORTC | GPIO_PIN10)
#define GPIO_LCD_SEG28_2    (GPIO_ALT | GPIO_AF11 | GPIO_PORTD | GPIO_PIN8)
#define GPIO_LCD_SEG29_1    (GPIO_ALT | GPIO_AF11 | GPIO_PORTC | GPIO_PIN11)
#define GPIO_LCD_SEG29_2    (GPIO_ALT | GPIO_AF11 | GPIO_PORTD | GPIO_PIN9)
#define GPIO_LCD_SEG30_1    (GPIO_ALT | GPIO_AF11 | GPIO_PORTC | GPIO_PIN12)
#define GPIO_LCD_SEG30_2    (GPIO_ALT | GPIO_AF11 | GPIO_PORTD | GPIO_PIN10)
#define GPIO_LCD_SEG31_1    (GPIO_ALT | GPIO_AF11 | GPIO_PORTD | GPIO_PIN11)
#define GPIO_LCD_SEG31_2    (GPIO_ALT | GPIO_AF11 | GPIO_PORTD | GPIO_PIN2)
#define GPIO_LCD_SEG32_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTD | GPIO_PIN12)
#define GPIO_LCD_SEG33_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTD | GPIO_PIN13)
#define GPIO_LCD_SEG34_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTD | GPIO_PIN14)
#define GPIO_LCD_SEG35_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTD | GPIO_PIN15)
#define GPIO_LCD_SEG36_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTE | GPIO_PIN0)
#define GPIO_LCD_SEG37_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTE | GPIO_PIN1)
#define GPIO_LCD_SEG38_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTE | GPIO_PIN2)
#define GPIO_LCD_SEG39_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTE | GPIO_PIN3)
#define GPIO_LCD_SEG40_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTC | GPIO_PIN10)
#define GPIO_LCD_SEG41_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTC | GPIO_PIN11)
#define GPIO_LCD_SEG42_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTC | GPIO_PIN12)
#define GPIO_LCD_SEG43_0    (GPIO_ALT | GPIO_AF11 | GPIO_PORTD | GPIO_PIN2)

/* Clocking */

#define GPIO_MCO_0          (GPIO_ALT | GPIO_AF0 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN8)
#define GPIO_OSC32_IN_0     (GPIO_ALT | GPIO_AF0 | GPIO_PORTC | GPIO_PIN14)
#define GPIO_OSC32_OUT_0    (GPIO_ALT | GPIO_AF0 | GPIO_PORTC | GPIO_PIN15)
#define GPIO_OSC_IN_0       (GPIO_ALT | GPIO_AF0 | GPIO_PORTH | GPIO_PIN0)
#define GPIO_OSC_OUT_0      (GPIO_ALT | GPIO_AF0 | GPIO_PORTH | GPIO_PIN1)

/* Event outputs */

#define GPIO_PA0_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_PA1_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_PA2_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_PA3_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_PA4_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_PA5_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN5)
#define GPIO_PA6_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_PA7_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_PA8_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_PA9_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN9)
#define GPIO_PA10_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_PA11_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_PA12_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN12)
#define GPIO_PA13_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN13)
#define GPIO_PA14_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN14)
#define GPIO_PA15_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_PB0_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN0)
#define GPIO_PB1_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN1)
#define GPIO_PB2_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN2)
#define GPIO_PB3_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_PB4_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_PB5_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_PB6_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN6)
#define GPIO_PB7_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN7)
#define GPIO_PB8_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_PB9_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_PB10_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_PB11_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN11)
#define GPIO_PB12_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_PB13_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_PB14_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_PB15_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN15)
#define GPIO_PC0_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN0)
#define GPIO_PC1_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN1)
#define GPIO_PC2_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN2)
#define GPIO_PC3_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN3)
#define GPIO_PC4_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN4)
#define GPIO_PC5_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN5)
#define GPIO_PC6_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN6)
#define GPIO_PC7_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN7)
#define GPIO_PC8_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN8)
#define GPIO_PC9_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN9)
#define GPIO_PC10_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN10)
#define GPIO_PC11_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN11)
#define GPIO_PC12_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN12)
#define GPIO_PC13_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN13)
#define GPIO_PC14_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN14)
#define GPIO_PC15_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN15)
#define GPIO_PD0_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN0)
#define GPIO_PD1_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN1)
#define GPIO_PD2_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN2)
#define GPIO_PD3_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN3)
#define GPIO_PD4_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN4)
#define GPIO_PD5_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN5)
#define GPIO_PD6_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN6)
#define GPIO_PD7_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN7)
#define GPIO_PD8_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN8)
#define GPIO_PD9_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN9)
#define GPIO_PD10_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN10)
#define GPIO_PD11_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN11)
#define GPIO_PD12_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN12)
#define GPIO_PD13_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN13)
#define GPIO_PD14_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN14)
#define GPIO_PD15_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN15)
#define GPIO_PE0_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN0)
#define GPIO_PE1_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN1)
#define GPIO_PE2_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN2)
#define GPIO_PE3_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN3)
#define GPIO_PE4_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN4)
#define GPIO_PE5_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN5)
#define GPIO_PE6_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN6)
#define GPIO_PE7_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN7)
#define GPIO_PE8_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN8)
#define GPIO_PE9_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN9)
#define GPIO_PE10_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN10)
#define GPIO_PE11_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN11)
#define GPIO_PE12_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN12)
#define GPIO_PE13_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN13)
#define GPIO_PE14_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN14)
#define GPIO_PE15_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN15)
#define GPIO_PF0_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN0)
#define GPIO_PF1_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN1)
#define GPIO_PF2_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN2)
#define GPIO_PF3_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN3)
#define GPIO_PF4_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN4)
#define GPIO_PF5_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN5)
#define GPIO_PF6_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN6)
#define GPIO_PF7_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN7)
#define GPIO_PF8_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN8)
#define GPIO_PF9_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN9)
#define GPIO_PF10_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN10)
#define GPIO_PF11_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN11)
#define GPIO_PF12_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN12)
#define GPIO_PF13_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN13)
#define GPIO_PF14_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN14)
#define GPIO_PF15_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN15)
#define GPIO_PG0_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN0)
#define GPIO_PG1_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN1)
#define GPIO_PG2_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN2)
#define GPIO_PG3_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN3)
#define GPIO_PG4_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN4)
#define GPIO_PG5_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN5)
#define GPIO_PG6_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN6)
#define GPIO_PG7_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN7)
#define GPIO_PG8_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN8)
#define GPIO_PG9_EVENT_OUT_0  (GPIOALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN9)
#define GPIO_PG10_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN10)
#define GPIO_PG11_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN11)
#define GPIO_PG12_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN12)
#define GPIO_PG13_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN13)
#define GPIO_PG14_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN14)
#define GPIO_PG15_EVENT_OUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN15)

/* RTC */

#define GPIO_RTC_OUT_0      (GPIO_ALT | GPIO_AF0 | GPIO_PORTC | GPIO_PIN13)
#define GPIO_RTC_REFIN_0    (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN15)
#define GPIO_RTC_TAMP1_0    (GPIO_ALT | GPIO_AF0 | GPIO_PORTC | GPIO_PIN13)
#define GPIO_RTC_TS_0       (GPIO_ALT | GPIO_AF0 | GPIO_PORTC | GPIO_PIN13)

/* SPI */

#define GPIO_SPI1_MISO_1    (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_SPI1_MISO_2    (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_SPI1_MISO_3    (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_SPI1_MISO_4    (GPIO_ALT | GPIO_AF5 | GPIO_PORTE | GPIO_PIN14)
#define GPIO_SPI1_MOSI_1    (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN12)
#define GPIO_SPI1_MOSI_2    (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_SPI1_MOSI_3    (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_SPI1_MOSI_4    (GPIO_ALT | GPIO_AF5 | GPIO_PORTE | GPIO_PIN15)
#define GPIO_SPI1_NSS_1     (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_SPI1_NSS_2     (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_SPI1_NSS_3     (GPIO_ALT | GPIO_AF5 | GPIO_PORTE | GPIO_PIN12)
#define GPIO_SPI1_SCK_1     (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN5)
#define GPIO_SPI1_SCK_2     (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_SPI1_SCK_3     (GPIO_ALT | GPIO_AF5 | GPIO_PORTE | GPIO_PIN13)

#define GPIO_SPI2_MISO_1    (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_SPI2_MISO_2    (GPIO_ALT | GPIO_AF5 | GPIO_PORTD | GPIO_PIN3)
#define GPIO_SPI2_MOSI_1    (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN15)
#define GPIO_SPI2_MOSI_2    (GPIO_ALT | GPIO_AF5 | GPIO_PORTD | GPIO_PIN4)
#define GPIO_SPI2_NSS_1     (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_SPI2_NSS_2     (GPIO_ALT | GPIO_AF5 | GPIO_PORTD | GPIO_PIN0)
#define GPIO_SPI2_SCK_1     (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_SPI2_SCK_2     (GPIO_ALT | GPIO_AF5 | GPIO_PORTD | GPIO_PIN1)

#define GPIO_SPI3_MISO_1    (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_SPI3_MISO_2    (GPIO_ALT | GPIO_AF6 | GPIO_PORTC | GPIO_PIN11)
#define GPIO_SPI3_MOSI_1    (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_SPI3_MOSI_2    (GPIO_ALT | GPIO_AF6 | GPIO_PORTC | GPIO_PIN12)
#define GPIO_SPI3_NSS_1     (GPIO_ALT | GPIO_AF6 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_SPI3_NSS_2     (GPIO_ALT | GPIO_AF6 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_SPI3_SCK_1     (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_SPI3_SCK_2     (GPIO_ALT | GPIO_AF6 | GPIO_PORTC | GPIO_PIN10)

/* Timers */

#define GPIO_TIM2_CH1_ETR_1 (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_TIM2_CH1_ETR_2 (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_TIM2_CH1_ETR_3 (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN9)
#define GPIO_TIM2_CH1_ETR_4 (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN5)
#define GPIO_TIM2_CH2IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_TIM2_CH2IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_TIM2_CH2IN_3   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN10)
#define GPIO_TIM2_CH2IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_TIM2_CH2IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_TIM2_CH2IN_3   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN10)
#define GPIO_TIM2_CH2OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_TIM2_CH2OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_TIM2_CH2OUT_3  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTE | GPIO_PIN10)
#define GPIO_TIM2_CH2OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_TIM2_CH2OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_TIM2_CH2OUT_3  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTE | GPIO_PIN10)
#define GPIO_TIM2_CH3IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM2_CH3IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_TIM2_CH3IN_3   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN11)
#define GPIO_TIM2_CH3IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM2_CH3IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_TIM2_CH3IN_3   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN11)
#define GPIO_TIM2_CH3OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM2_CH3OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_TIM2_CH3OUT_3  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTE | GPIO_PIN11)
#define GPIO_TIM2_CH3OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM2_CH3OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_TIM2_CH3OUT_3  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTE | GPIO_PIN11)
#define GPIO_TIM2_CH4IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_TIM2_CH4IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN11)
#define GPIO_TIM2_CH4IN_3   (GPIO_ALT | GPIO_FLOAT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN12)
#define GPIO_TIM2_CH4OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_TIM2_CH4OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTB | GPIO_PIN11)
#define GPIO_TIM2_CH4OUT_3  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF1 | GPIO_PORTE | GPIO_PIN12)

#define GPIO_TIM3_CH1IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM3_CH1IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_TIM3_CH1IN_3   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTC | GPIO_PIN6)
#define GPIO_TIM3_CH1IN_4   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTE | GPIO_PIN3)
#define GPIO_TIM3_CH1OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM3_CH1OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_TIM3_CH1OUT_3  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTC | GPIO_PIN6)
#define GPIO_TIM3_CH1OUT_4  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTE | GPIO_PIN3)
#define GPIO_TIM3_CH2IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM3_CH2IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_TIM3_CH2IN_3   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTC | GPIO_PIN7)
#define GPIO_TIM3_CH2IN_4   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTE | GPIO_PIN4)
#define GPIO_TIM3_CH2OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM3_CH2OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_TIM3_CH2OUT_3  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTC | GPIO_PIN7)
#define GPIO_TIM3_CH2OUT_4  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTE | GPIO_PIN4)
#define GPIO_TIM3_CH3IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN0)
#define GPIO_TIM3_CH3IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTC | GPIO_PIN8)
#define GPIO_TIM3_CH3OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTB | GPIO_PIN0)
#define GPIO_TIM3_CH3OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTC | GPIO_PIN8)
#define GPIO_TIM3_CH4IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN1)
#define GPIO_TIM3_CH4IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTC | GPIO_PIN9)
#define GPIO_TIM3_CH4OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTB | GPIO_PIN1)
#define GPIO_TIM3_CH4OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTC | GPIO_PIN9)
#define GPIO_TIM3_ETR_1     (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTD | GPIO_PIN2)
#define GPIO_TIM3_ETR_2     (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTE | GPIO_PIN2)

#define GPIO_TIM4_CH1IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN6)
#define GPIO_TIM4_CH1IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTD | GPIO_PIN12)
#define GPIO_TIM4_CH1OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTB | GPIO_PIN6)
#define GPIO_TIM4_CH1OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTD | GPIO_PIN12)
#define GPIO_TIM4_CH2IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN7)
#define GPIO_TIM4_CH2IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTD | GPIO_PIN13)
#define GPIO_TIM4_CH2OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTB | GPIO_PIN7)
#define GPIO_TIM4_CH2OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTD | GPIO_PIN13)
#define GPIO_TIM4_CH3IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_TIM4_CH3IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTD | GPIO_PIN14)
#define GPIO_TIM4_CH3OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_TIM4_CH3OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTD | GPIO_PIN14)
#define GPIO_TIM4_CH4IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_TIM4_CH4IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTD | GPIO_PIN15)
#define GPIO_TIM4_CH4OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_TIM4_CH4OUT_2  (GPIO_ALT |GPIO_PUSHPULL |  GPIO_AF2 | GPIO_PORTD | GPIO_PIN15)
#define GPIO_TIM4_ETR_0     (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTE | GPIO_PIN0)

#define GPIO_TIM5_CH1IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_TIM5_CH1OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_TIM5_CH2IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM5_CH2OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM5_CH2IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTF | GPIO_PIN7)
#define GPIO_TIM5_CH2OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTF | GPIO_PIN7)
#define GPIO_TIM5_CH3IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_TIM5_CH3OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_TIM5_CH3IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTF | GPIO_PIN8)
#define GPIO_TIM5_CH3OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTF | GPIO_PIN8)
#define GPIO_TIM5_CH4IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_TIM5_CH4OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_TIM5_CH4IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTF | GPIO_PIN9)
#define GPIO_TIM5_CH4OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF2 | GPIO_PORTF | GPIO_PIN9)
#define GPIO_TIM5_ETR_0     (GPIO_ALT | GPIO_FLOAT | GPIO_AF2 | GPIO_PORTF | GPIO_PIN6)

#define GPIO_TIM9_CH1IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF3 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM9_CH1IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_TIM9_CH1IN_3   (GPIO_ALT | GPIO_FLOAT | GPIO_AF3 | GPIO_PORTD | GPIO_PIN0)
#define GPIO_TIM9_CH1IN_4   (GPIO_ALT | GPIO_FLOAT | GPIO_AF3 | GPIO_PORTE | GPIO_PIN5)
#define GPIO_TIM9_CH1OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF3 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM9_CH1OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF3 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_TIM9_CH1OUT_3  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF3 | GPIO_PORTD | GPIO_PIN0)
#define GPIO_TIM9_CH1OUT_4  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF3 | GPIO_PORTE | GPIO_PIN5)
#define GPIO_TIM9_CH2IN_1   (GPIO_ALT | GPIO_FLOAT | GPIO_AF3 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_TIM9_CH2IN_2   (GPIO_ALT | GPIO_FLOAT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_TIM9_CH2IN_3   (GPIO_ALT | GPIO_FLOAT | GPIO_AF3 | GPIO_PORTD | GPIO_PIN7)
#define GPIO_TIM9_CH2IN_4   (GPIO_ALT | GPIO_FLOAT | GPIO_AF3 | GPIO_PORTE | GPIO_PIN6)
#define GPIO_TIM9_CH2OUT_1  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF3 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_TIM9_CH2OUT_2  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF3 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_TIM9_CH2OUT_3  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF3 | GPIO_PORTD | GPIO_PIN7)
#define GPIO_TIM9_CH2OUT_4  (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF3 | GPIO_PORTE | GPIO_PIN6)

#define GPIO_TIM10_CH1IN_1  (GPIO_ALT | GPIO_FLOAT | GPIO_AF3 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM10_CH1IN_2  (GPIO_ALT | GPIO_FLOAT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_TIM10_CH1IN_3  (GPIO_ALT | GPIO_FLOAT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_TIM10_CH1IN_4  (GPIO_ALT | GPIO_FLOAT | GPIO_AF3 | GPIO_PORTE | GPIO_PIN0)
#define GPIO_TIM10_CH1OUT_1 (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF3 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM10_CH1OUT_2 (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF3 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_TIM10_CH1OUT_3 (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF3 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_TIM10_CH1OUT_4 (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF3 | GPIO_PORTE | GPIO_PIN0)

#define GPIO_TIM11_CH1IN_1  (GPIO_ALT | GPIO_FLOAT | GPIO_AF3 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM11_CH1IN_2  (GPIO_ALT | GPIO_FLOAT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN15)
#define GPIO_TIM11_CH1IN_3  (GPIO_ALT | GPIO_FLOAT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_TIM11_CH1IN_4  (GPIO_ALT | GPIO_FLOAT | GPIO_AF3 | GPIO_PORTE | GPIO_PIN1)
#define GPIO_TIM11_CH1OUT_1 (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF3 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM11_CH1OUT_2 (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF3 | GPIO_PORTB | GPIO_PIN15)
#define GPIO_TIM11_CH1OUT_3 (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF3 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_TIM11_CH1OUT_4 (GPIO_ALT | GPIO_PUSHPULL | GPIO_AF3 | GPIO_PORTE | GPIO_PIN1)

#define GPIO_TIMX_IC1_1     (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_TIMX_IC1_2     (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN12)
#define GPIO_TIMX_IC1_3     (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_TIMX_IC1_4     (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_TIMX_IC1_5     (GPIO_ALT | GPIO_AF14 | GPIO_PORTC | GPIO_PIN0)
#define GPIO_TIMX_IC1_6     (GPIO_ALT | GPIO_AF14 | GPIO_PORTC | GPIO_PIN12)
#define GPIO_TIMX_IC1_7     (GPIO_ALT | GPIO_AF14 | GPIO_PORTC | GPIO_PIN4)
#define GPIO_TIMX_IC1_8     (GPIO_ALT | GPIO_AF14 | GPIO_PORTC | GPIO_PIN8)
#define GPIO_TIMX_IC1_9     (GPIO_ALT | GPIO_AF14 | GPIO_PORTD | GPIO_PIN0)
#define GPIO_TIMX_IC1_10    (GPIO_ALT | GPIO_AF14 | GPIO_PORTD | GPIO_PIN12)
#define GPIO_TIMX_IC1_11    (GPIO_ALT | GPIO_AF14 | GPIO_PORTD | GPIO_PIN4)
#define GPIO_TIMX_IC1_12    (GPIO_ALT | GPIO_AF14 | GPIO_PORTD | GPIO_PIN8)
#define GPIO_TIMX_IC1_13    (GPIO_ALT | GPIO_AF14 | GPIO_PORTE | GPIO_PIN0)
#define GPIO_TIMX_IC1_14    (GPIO_ALT | GPIO_AF14 | GPIO_PORTE | GPIO_PIN12)
#define GPIO_TIMX_IC1_15    (GPIO_ALT | GPIO_AF14 | GPIO_PORTE | GPIO_PIN4)
#define GPIO_TIMX_IC1_16    (GPIO_ALT | GPIO_AF14 | GPIO_PORTE | GPIO_PIN8)

#define GPIO_TIMX_IC2_1     (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_TIMX_IC2_2     (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN13)
#define GPIO_TIMX_IC2_3     (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN5)
#define GPIO_TIMX_IC2_4     (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN9)
#define GPIO_TIMX_IC2_5     (GPIO_ALT | GPIO_AF14 | GPIO_PORTC | GPIO_PIN1)
#define GPIO_TIMX_IC2_6     (GPIO_ALT | GPIO_AF14 | GPIO_PORTC | GPIO_PIN13)
#define GPIO_TIMX_IC2_7     (GPIO_ALT | GPIO_AF14 | GPIO_PORTC | GPIO_PIN5)
#define GPIO_TIMX_IC2_8     (GPIO_ALT | GPIO_AF14 | GPIO_PORTC | GPIO_PIN9)
#define GPIO_TIMX_IC2_9     (GPIO_ALT | GPIO_AF14 | GPIO_PORTD | GPIO_PIN1)
#define GPIO_TIMX_IC2_10    (GPIO_ALT | GPIO_AF14 | GPIO_PORTD | GPIO_PIN13)
#define GPIO_TIMX_IC2_11    (GPIO_ALT | GPIO_AF14 | GPIO_PORTD | GPIO_PIN5)
#define GPIO_TIMX_IC2_12    (GPIO_ALT | GPIO_AF14 | GPIO_PORTD | GPIO_PIN9)
#define GPIO_TIMX_IC2_13    (GPIO_ALT | GPIO_AF14 | GPIO_PORTE | GPIO_PIN1)
#define GPIO_TIMX_IC2_14    (GPIO_ALT | GPIO_AF14 | GPIO_PORTE | GPIO_PIN13)
#define GPIO_TIMX_IC2_15    (GPIO_ALT | GPIO_AF14 | GPIO_PORTE | GPIO_PIN5)
#define GPIO_TIMX_IC2_16    (GPIO_ALT | GPIO_AF14 | GPIO_PORTE | GPIO_PIN9)

#define GPIO_TIMX_IC3_1     (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_TIMX_IC3_2     (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN14)
#define GPIO_TIMX_IC3_3     (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIMX_IC3_4     (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIMX_IC3_5     (GPIO_ALT | GPIO_AF14 | GPIO_PORTC | GPIO_PIN10)
#define GPIO_TIMX_IC3_6     (GPIO_ALT | GPIO_AF14 | GPIO_PORTC | GPIO_PIN14)
#define GPIO_TIMX_IC3_7     (GPIO_ALT | GPIO_AF14 | GPIO_PORTC | GPIO_PIN2)
#define GPIO_TIMX_IC3_8     (GPIO_ALT | GPIO_AF14 | GPIO_PORTC | GPIO_PIN6)
#define GPIO_TIMX_IC3_9     (GPIO_ALT | GPIO_AF14 | GPIO_PORTD | GPIO_PIN10)
#define GPIO_TIMX_IC3_10    (GPIO_ALT | GPIO_AF14 | GPIO_PORTD | GPIO_PIN14)
#define GPIO_TIMX_IC3_11    (GPIO_ALT | GPIO_AF14 | GPIO_PORTD | GPIO_PIN2)
#define GPIO_TIMX_IC3_12    (GPIO_ALT | GPIO_AF14 | GPIO_PORTD | GPIO_PIN6)
#define GPIO_TIMX_IC3_13    (GPIO_ALT | GPIO_AF14 | GPIO_PORTE | GPIO_PIN10)
#define GPIO_TIMX_IC3_14    (GPIO_ALT | GPIO_AF14 | GPIO_PORTE | GPIO_PIN14)
#define GPIO_TIMX_IC3_15    (GPIO_ALT | GPIO_AF14 | GPIO_PORTE | GPIO_PIN2)
#define GPIO_TIMX_IC3_16    (GPIO_ALT | GPIO_AF14 | GPIO_PORTE | GPIO_PIN6)

#define GPIO_TIMX_IC4_1     (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_TIMX_IC4_2     (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_TIMX_IC4_3     (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_TIMX_IC4_4     (GPIO_ALT | GPIO_AF14 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIMX_IC4_5     (GPIO_ALT | GPIO_AF14 | GPIO_PORTC | GPIO_PIN11)
#define GPIO_TIMX_IC4_6     (GPIO_ALT | GPIO_AF14 | GPIO_PORTC | GPIO_PIN15)
#define GPIO_TIMX_IC4_7     (GPIO_ALT | GPIO_AF14 | GPIO_PORTC | GPIO_PIN3)
#define GPIO_TIMX_IC4_8     (GPIO_ALT | GPIO_AF14 | GPIO_PORTC | GPIO_PIN7)
#define GPIO_TIMX_IC4_9     (GPIO_ALT | GPIO_AF14 | GPIO_PORTD | GPIO_PIN11)
#define GPIO_TIMX_IC4_10    (GPIO_ALT | GPIO_AF14 | GPIO_PORTD | GPIO_PIN15)
#define GPIO_TIMX_IC4_11    (GPIO_ALT | GPIO_AF14 | GPIO_PORTD | GPIO_PIN3)
#define GPIO_TIMX_IC4_12    (GPIO_ALT | GPIO_AF14 | GPIO_PORTD | GPIO_PIN7)
#define GPIO_TIMX_IC4_13    (GPIO_ALT | GPIO_AF14 | GPIO_PORTE | GPIO_PIN11)
#define GPIO_TIMX_IC4_14    (GPIO_ALT | GPIO_AF14 | GPIO_PORTE | GPIO_PIN15)
#define GPIO_TIMX_IC4_15    (GPIO_ALT | GPIO_AF14 | GPIO_PORTE | GPIO_PIN3)
#define GPIO_TIMX_IC4_16    (GPIO_ALT | GPIO_AF14 | GPIO_PORTE | GPIO_PIN7)

/* USART */

#define GPIO_USART1_CK_0    (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_USART1_CTS_0   (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_USART1_RTS_0   (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN12)
#define GPIO_USART1_RX_1    (GPIO_ALT | GPIO_PULLUP | GPIO_AF7 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN10)
#define GPIO_USART1_RX_2    (GPIO_ALT | GPIO_PULLUP | GPIO_AF7 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN7)
#define GPIO_USART1_TX_1    (GPIO_ALT | GPIO_PULLUP | GPIO_AF7 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN9)
#define GPIO_USART1_TX_2    (GPIO_ALT | GPIO_PULLUP | GPIO_AF7 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN6)

#define GPIO_USART2_CK_1    (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_USART2_CK_2    (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN7)
#define GPIO_USART2_CTS_1   (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_USART2_CTS_2   (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN3)
#define GPIO_USART2_RTS_1   (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_USART2_RTS_2   (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN4)
#define GPIO_USART2_RX_1    (GPIO_ALT | GPIO_PULLUP | GPIO_AF7 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN3)
#define GPIO_USART2_RX_2    (GPIO_ALT | GPIO_PULLUP | GPIO_AF7 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN6)
#define GPIO_USART2_TX_1    (GPIO_ALT | GPIO_PULLUP | GPIO_AF7 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN2)
#define GPIO_USART2_TX_2    (GPIO_ALT | GPIO_PULLUP | GPIO_AF7 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN5)

#define GPIO_USART3_CK_1    (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_USART3_CK_2    (GPIO_ALT | GPIO_AF7 | GPIO_PORTC | GPIO_PIN12)
#define GPIO_USART3_CK_3    (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN10)
#define GPIO_USART3_CTS_1   (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_USART3_CTS_2   (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN11)
#define GPIO_USART3_RTS_1   (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_USART3_RTS_2   (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN12)
#define GPIO_USART3_RX_1    (GPIO_ALT | GPIO_PULLUP | GPIO_AF7 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN11)
#define GPIO_USART3_RX_2    (GPIO_ALT | GPIO_PULLUP | GPIO_AF7 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN11)
#define GPIO_USART3_RX_3    (GPIO_ALT | GPIO_PULLUP | GPIO_AF7 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN9)
#define GPIO_USART3_TX_1    (GPIO_ALT | GPIO_PULLUP | GPIO_AF7 | GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN10)
#define GPIO_USART3_TX_2    (GPIO_ALT | GPIO_PULLUP | GPIO_AF7 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN10)
#define GPIO_USART3_TX_3    (GPIO_ALT | GPIO_PULLUP | GPIO_AF7 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN8)

#define GPIO_UART4_RX_0     (GPIO_ALT | GPIO_PULLUP | GPIO_AF8 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN11)
#define GPIO_UART4_TX_0     (GPIO_ALT | GPIO_PULLUP | GPIO_AF8 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN10)

#define GPIO_UART5_RX_0     (GPIO_ALT | GPIO_PULLUP | GPIO_AF8 | GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN2)
#define GPIO_UART5_TX_0     (GPIO_ALT | GPIO_PULLUP | GPIO_AF8 | GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN12)

/* USB */

#define GPIO_USB_DM_0       (GPIO_ALT | GPIO_AF10 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN11)
#define GPIO_USB_DP_0       (GPIO_ALT | GPIO_AF10 | GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN12)

/* Wakeup inputs */

#define GPIO_WKUP1_0        (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_WKUP2_0        (GPIO_ALT | GPIO_AF9 | GPIO_PORTC | GPIO_PIN13)
#define GPIO_WKUP3_0        (GPIO_ALT | GPIO_AF0 | GPIO_PORTE | GPIO_PIN6)

#endif /* __ARCH_ARM_SRC_STM32_HARDWARE_STM32L15XXX_PINMAP_H */
