/****************************************************************************
 * arch/arm/src/stm32/hardware/stm32f412xx_pinmap.h
 *
 *  Licensed to the Apache Software Foundation (ASF) under one or more
 *  contributor license agreements.  See the NOTICE file distributed with
 *  this work for additional information regarding copyright ownership.  The
 *  ASF licenses this file to you under the Apache License, Version 2.0 (the
 *  "License"); you may not use this file except in compliance with the
 *  License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 *  License for the specific language governing permissions and limitations
 *  under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_STM32_HARDWARE_STM32F412XX_PINMAP_H
#define __ARCH_ARM_SRC_STM32_HARDWARE_STM32F412XX_PINMAP_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

#include "stm32_gpio.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Alternate Pin Functions.  All members of the STM32F40xxx family share the
 * same pin multiplexing (although they may differ in the pins physically
 * available).
 *
 * Alternative pin selections are provided with a numeric suffix like _1, _2,
 * etc. Drivers, however, will use the pin selection without the numeric
 * suffix. Additional definitions are required in the board.h file.  For
 * example, if CAN1_RX connects via PA11 on some board, then the following
 * definitions should appear in the board.h header file for that board:
 *
 * #define GPIO_CAN1_RX GPIO_CAN1_RX_1
 *
 * The driver will then automatically configure PA11 as the CAN1 RX pin.
 */

/* WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific GPIO options such as
 * frequency, open-drain/push-pull, and pull-up/down!  Just the basics are
 * defined for most pins in this file.
 */

/* ADC */

#define GPIO_ADC1_IN0_0  (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN0)
#define GPIO_ADC1_IN1_0  (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN1)
#define GPIO_ADC1_IN2_0  (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN2)
#define GPIO_ADC1_IN3_0  (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN3)
#define GPIO_ADC1_IN4_0  (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN4)
#define GPIO_ADC1_IN5_0  (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN5)
#define GPIO_ADC1_IN6_0  (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN6)
#define GPIO_ADC1_IN7_0  (GPIO_ANALOG | GPIO_PORTA | GPIO_PIN7)
#define GPIO_ADC1_IN8_0  (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN0)
#define GPIO_ADC1_IN9_0  (GPIO_ANALOG | GPIO_PORTB | GPIO_PIN1)
#define GPIO_ADC1_IN10_0 (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN0)
#define GPIO_ADC1_IN11_0 (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN1)
#define GPIO_ADC1_IN12_0 (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN2)
#define GPIO_ADC1_IN13_0 (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN3)
#define GPIO_ADC1_IN14_0 (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN4)
#define GPIO_ADC1_IN15_0 (GPIO_ANALOG | GPIO_PORTC | GPIO_PIN5)

/* CAN */

#define GPIO_CAN1_RX_1  (GPIO_ALT | GPIO_AF9 |  GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN11)
#define GPIO_CAN1_RX_2  (GPIO_ALT | GPIO_AF8 |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN8)
#define GPIO_CAN1_RX_3  (GPIO_ALT | GPIO_AF9 |  GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN0)
#define GPIO_CAN1_RX_4  (GPIO_ALT | GPIO_AF9 |  GPIO_PUSHPULL | GPIO_PORTG | GPIO_PIN0)
#define GPIO_CAN1_TX_1  (GPIO_ALT | GPIO_AF9 |  GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN12)
#define GPIO_CAN1_TX_2  (GPIO_ALT | GPIO_AF8 |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN9)
#define GPIO_CAN1_TX_3  (GPIO_ALT | GPIO_AF9 |  GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN1)
#define GPIO_CAN1_TX_4  (GPIO_ALT | GPIO_AF9 |  GPIO_PUSHPULL | GPIO_PORTG | GPIO_PIN1)

#define GPIO_CAN2_RX_1  (GPIO_ALT | GPIO_AF9 |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN5)
#define GPIO_CAN2_RX_2  (GPIO_ALT | GPIO_AF9 |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN12)
#define GPIO_CAN2_RX_3  (GPIO_ALT | GPIO_AF9 |  GPIO_PUSHPULL | GPIO_PORTG | GPIO_PIN11)
#define GPIO_CAN2_TX_1  (GPIO_ALT | GPIO_AF9 |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN13)
#define GPIO_CAN2_TX_2  (GPIO_ALT | GPIO_AF9 |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN6)
#define GPIO_CAN2_TX_3  (GPIO_ALT | GPIO_AF9 |  GPIO_PUSHPULL | GPIO_PORTG | GPIO_PIN12)

/* Digital filter for sigma delta modulators */

#define GPIO_DFSDM1_CKOUT_1  (GPIO_ALT | GPIO_AF6  |  GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN9)
#define GPIO_DFSDM1_CKOUT_2  (GPIO_ALT | GPIO_AF8  |  GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN2)
#define GPIO_DFSDM1_CKIN0_1  (GPIO_ALT | GPIO_AF6  |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN2)
#define GPIO_DFSDM1_CKIN0_2  (GPIO_ALT | GPIO_AF6  |  GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN4)
#define GPIO_DFSDM1_CKIN1_1  (GPIO_ALT | GPIO_AF8  |  GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN5)
#define GPIO_DFSDM1_CKIN1_2  (GPIO_ALT | GPIO_AF10 |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN13)
#define GPIO_DFSDM1_CKIN1_3  (GPIO_ALT | GPIO_AF6  |  GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN7)
#define GPIO_DFSDM1_CKIN2_1  (GPIO_ALT | GPIO_AF8  |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN15)
#define GPIO_DFSDM1_CKIN2_2  (GPIO_ALT | GPIO_AF6  |  GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN8)
#define GPIO_DFSDM1_CKIN3_1  (GPIO_ALT | GPIO_AF6  |  GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN6)
#define GPIO_DFSDM1_CKIN3_2  (GPIO_ALT | GPIO_AF8  |  GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN5)

#define GPIO_DFSDM1_DATIN0_1  (GPIO_ALT | GPIO_AF8  |GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN1)
#define GPIO_DFSDM1_DATIN0_2  (GPIO_ALT | GPIO_AF6  |GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN3)
#define GPIO_DFSDM1_DATIN1_1  (GPIO_ALT | GPIO_AF8  |GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN4)
#define GPIO_DFSDM1_DATIN1_2  (GPIO_ALT | GPIO_AF10 |GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN12)
#define GPIO_DFSDM1_DATIN1_3  (GPIO_ALT | GPIO_AF6  |GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN6)
#define GPIO_DFSDM1_DATIN2_1  (GPIO_ALT | GPIO_AF8  |GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN14)
#define GPIO_DFSDM1_DATIN2_2  (GPIO_ALT | GPIO_AF6  |GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN7)
#define GPIO_DFSDM1_DATIN3_1  (GPIO_ALT | GPIO_AF10 |GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN7)
#define GPIO_DFSDM1_DATIN3_2  (GPIO_ALT | GPIO_AF8  |GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTE | GPIO_PIN4)

/* Clocks outputs */

#define GPIO_MCO1_0 (GPIO_ALT | GPIO_AF0 |  GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN8)
#define GPIO_MCO2_0 (GPIO_ALT | GPIO_AF0 |  GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN9)

/* Event outputs */

#define GPIO_PA0_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_PA1_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_PA2_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_PA3_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_PA4_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_PA5_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN5)
#define GPIO_PA6_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_PA7_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_PA8_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_PA9_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN9)
#define GPIO_PA10_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_PA11_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_PA12_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN12)
#define GPIO_PA13_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN13)
#define GPIO_PA14_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN14)
#define GPIO_PA15_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTA | GPIO_PIN15)

#define GPIO_PB0_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN0)
#define GPIO_PB1_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN1)
#define GPIO_PB2_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN2)
#define GPIO_PB3_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_PB4_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_PB5_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_PB6_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN6)
#define GPIO_PB7_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN7)
#define GPIO_PB8_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_PB9_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_PB10_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_PB11_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN11)
#define GPIO_PB12_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_PB13_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_PB14_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_PB15_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTB | GPIO_PIN15)

#define GPIO_PC0_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN0)
#define GPIO_PC1_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN1)
#define GPIO_PC2_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN2)
#define GPIO_PC3_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN3)
#define GPIO_PC4_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN4)
#define GPIO_PC5_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN5)
#define GPIO_PC6_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN6)
#define GPIO_PC7_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN7)
#define GPIO_PC8_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN8)
#define GPIO_PC9_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN9)
#define GPIO_PC10_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN10)
#define GPIO_PC11_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN11)
#define GPIO_PC12_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN12)
#define GPIO_PC13_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN13)
#define GPIO_PC14_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN14)
#define GPIO_PC15_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTC | GPIO_PIN15)

#define GPIO_PD0_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN0)
#define GPIO_PD1_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN1)
#define GPIO_PD2_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN2)
#define GPIO_PD3_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN3)
#define GPIO_PD4_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN4)
#define GPIO_PD5_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN5)
#define GPIO_PD6_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN6)
#define GPIO_PD7_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN7)
#define GPIO_PD8_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN8)
#define GPIO_PD9_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN9)
#define GPIO_PD10_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN10)
#define GPIO_PD11_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN11)
#define GPIO_PD12_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN12)
#define GPIO_PD13_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN13)
#define GPIO_PD14_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN14)
#define GPIO_PD15_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTD | GPIO_PIN15)

#define GPIO_PE0_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN0)
#define GPIO_PE1_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN1)
#define GPIO_PE2_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN2)
#define GPIO_PE3_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN3)
#define GPIO_PE4_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN4)
#define GPIO_PE5_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN5)
#define GPIO_PE6_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN6)
#define GPIO_PE7_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN7)
#define GPIO_PE8_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN8)
#define GPIO_PE9_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN9)
#define GPIO_PE10_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN10)
#define GPIO_PE11_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN11)
#define GPIO_PE12_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN12)
#define GPIO_PE13_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN13)
#define GPIO_PE14_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN14)
#define GPIO_PE15_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTE | GPIO_PIN15)

#define GPIO_PF0_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN0)
#define GPIO_PF1_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN1)
#define GPIO_PF2_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN2)
#define GPIO_PF3_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN3)
#define GPIO_PF4_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN4)
#define GPIO_PF5_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN5)
#define GPIO_PF6_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN6)
#define GPIO_PF7_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN7)
#define GPIO_PF8_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN8)
#define GPIO_PF9_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN9)
#define GPIO_PF10_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN10)
#define GPIO_PF11_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN11)
#define GPIO_PF12_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN12)
#define GPIO_PF13_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN13)
#define GPIO_PF14_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN14)
#define GPIO_PF15_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTF | GPIO_PIN15)

#define GPIO_PG0_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN0)
#define GPIO_PG1_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN1)
#define GPIO_PG2_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN2)
#define GPIO_PG3_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN3)
#define GPIO_PG4_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN4)
#define GPIO_PG5_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN5)
#define GPIO_PG6_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN6)
#define GPIO_PG7_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN7)
#define GPIO_PG8_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN8)
#define GPIO_PG9_EVENTOUT_0  (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN9)
#define GPIO_PG10_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN10)
#define GPIO_PG11_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN11)
#define GPIO_PG12_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN12)
#define GPIO_PG13_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN13)
#define GPIO_PG14_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN14)
#define GPIO_PG15_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTG | GPIO_PIN15)

#define GPIO_PH0_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTH | GPIO_PIN0)
#define GPIO_PH1_EVENTOUT_0 (GPIO_ALT | GPIO_AF15 | GPIO_PORTH | GPIO_PIN1)

/* FMPI2C */

#define GPIO_FMPI2C1_SCL_1   (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTC | GPIO_PIN6)
#define GPIO_FMPI2C1_SCL_2   (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTD | GPIO_PIN12)
#define GPIO_FMPI2C1_SCL_3   (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTD | GPIO_PIN14)
#define GPIO_FMPI2C1_SCL_4   (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTF | GPIO_PIN14)
#define GPIO_FMPI2C1_SCL_5   (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN15)
#define GPIO_FMPI2C1_SCL_6   (GPIO_ALT | GPIO_AF9 |  GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN10)

#define GPIO_FMPI2C1_SDA_1   (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTC | GPIO_PIN7)
#define GPIO_FMPI2C1_SDA_2   (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTD | GPIO_PIN13)
#define GPIO_FMPI2C1_SDA_3   (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTD | GPIO_PIN15)
#define GPIO_FMPI2C1_SDA_4   (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTF | GPIO_PIN15)
#define GPIO_FMPI2C1_SDA_5   (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN3)
#define GPIO_FMPI2C1_SDA_6   (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN14)

#define GPIO_FMPI2C1_SMBA_1  (GPIO_ALT | GPIO_AF4 |  GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN11)
#define GPIO_FMPI2C1_SMBA_2  (GPIO_ALT | GPIO_AF4 |  GPIO_PUSHPULL | GPIO_PORTF | GPIO_PIN13)
#define GPIO_FMPI2C1_SMBA_3  (GPIO_ALT | GPIO_AF4 |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN13)
#define GPIO_FMPI2C1_SMBA_4  (GPIO_ALT | GPIO_AF4 |  GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN5)

/* Flexible Static Memory Controller (FSMC) */

#define GPIO_FSMC_A0_1   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTF | GPIO_PIN0)
#define GPIO_FSMC_A0_2   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTC | GPIO_PIN3)

#define GPIO_FSMC_A1_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTF | GPIO_PIN1)
#define GPIO_FSMC_A2_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTF | GPIO_PIN2)
#define GPIO_FSMC_A3_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTF | GPIO_PIN3)
#define GPIO_FSMC_A4_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTF | GPIO_PIN4)
#define GPIO_FSMC_A5_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTF | GPIO_PIN5)
#define GPIO_FSMC_A6_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTF | GPIO_PIN12)
#define GPIO_FSMC_A7_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTF | GPIO_PIN13)
#define GPIO_FSMC_A8_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTF | GPIO_PIN14)
#define GPIO_FSMC_A9_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTF | GPIO_PIN15)
#define GPIO_FSMC_A10_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTG | GPIO_PIN0)
#define GPIO_FSMC_A11_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTG | GPIO_PIN1)
#define GPIO_FSMC_A12_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTG | GPIO_PIN2)
#define GPIO_FSMC_A13_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTG | GPIO_PIN3)
#define GPIO_FSMC_A14_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTG | GPIO_PIN4)
#define GPIO_FSMC_A15_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTG | GPIO_PIN5)
#define GPIO_FSMC_A16_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN11)
#define GPIO_FSMC_A17_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN12)
#define GPIO_FSMC_A18_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN13)
#define GPIO_FSMC_A19_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN3)
#define GPIO_FSMC_A20_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN4)
#define GPIO_FSMC_A21_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN5)
#define GPIO_FSMC_A22_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN6)
#define GPIO_FSMC_A23_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN2)
#define GPIO_FSMC_A24_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTG | GPIO_PIN13)
#define GPIO_FSMC_A25_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTG | GPIO_PIN14)

#define GPIO_FSMC_CLK_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN3)

#define GPIO_FSMC_D0_1   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN14)
#define GPIO_FSMC_D0_2   (GPIO_ALT | GPIO_AF10 |  GPIO_PORTB | GPIO_PIN14)
#define GPIO_FSMC_D1_1   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN15)
#define GPIO_FSMC_D1_2   (GPIO_ALT | GPIO_AF10 |  GPIO_PORTC | GPIO_PIN6)
#define GPIO_FSMC_D2_1   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN0)
#define GPIO_FSMC_D2_2   (GPIO_ALT | GPIO_AF10 |  GPIO_PORTC | GPIO_PIN11)
#define GPIO_FSMC_D3_1   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN1)
#define GPIO_FSMC_D3_2   (GPIO_ALT | GPIO_AF10 |  GPIO_PORTC | GPIO_PIN12)
#define GPIO_FSMC_D4_1   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN7)
#define GPIO_FSMC_D4_2   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTA | GPIO_PIN2)
#define GPIO_FSMC_D5_1   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN8)
#define GPIO_FSMC_D5_2   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTA | GPIO_PIN3)
#define GPIO_FSMC_D6_1   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN9)
#define GPIO_FSMC_D6_2   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTA | GPIO_PIN4)
#define GPIO_FSMC_D7_1   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN10)
#define GPIO_FSMC_D7_2   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTA | GPIO_PIN5)
#define GPIO_FSMC_D8_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN11)
#define GPIO_FSMC_D9_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN12)
#define GPIO_FSMC_D10_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN13)
#define GPIO_FSMC_D11_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN14)
#define GPIO_FSMC_D12_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN15)
#define GPIO_FSMC_D13_1  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN8)
#define GPIO_FSMC_D13_2  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTB | GPIO_PIN12)
#define GPIO_FSMC_D14_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN9)
#define GPIO_FSMC_D15_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN10)

#define GPIO_FSMC_DA0_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN14)
#define GPIO_FSMC_DA1_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN15)
#define GPIO_FSMC_DA2_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN0)
#define GPIO_FSMC_DA3_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN1)
#define GPIO_FSMC_DA4_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN7)
#define GPIO_FSMC_DA5_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN8)
#define GPIO_FSMC_DA6_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN9)
#define GPIO_FSMC_DA7_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN10)
#define GPIO_FSMC_DA8_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN11)
#define GPIO_FSMC_DA9_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN12)
#define GPIO_FSMC_DA10_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN13)
#define GPIO_FSMC_DA11_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN14)
#define GPIO_FSMC_DA12_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN15)
#define GPIO_FSMC_DA13_1  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTB | GPIO_PIN12)
#define GPIO_FSMC_DA13_2  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN8)
#define GPIO_FSMC_DA14_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN9)
#define GPIO_FSMC_DA15_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN10)

#define GPIO_FSMC_NBL0_0 (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN0)
#define GPIO_FSMC_NBL1_0 (GPIO_ALT | GPIO_AF12 |  GPIO_PORTE | GPIO_PIN1)

#define GPIO_FSMC_NE1_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN7)
#define GPIO_FSMC_NE2_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTG | GPIO_PIN9)
#define GPIO_FSMC_NE3_0  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTG | GPIO_PIN10)
#define GPIO_FSMC_NE4_1  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTC | GPIO_PIN4)
#define GPIO_FSMC_NE4_2  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTG | GPIO_PIN12)

#define GPIO_FSMC_NL_0   (GPIO_ALT | GPIO_AF12 |  GPIO_PORTB | GPIO_PIN7)

#define GPIO_FSMC_NOE_1  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN4)
#define GPIO_FSMC_NOE_2  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTC | GPIO_PIN5)

#define GPIO_FSMC_NWAIT_0 (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN6)

#define GPIO_FSMC_NWE_1  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTC | GPIO_PIN2)
#define GPIO_FSMC_NWE_2  (GPIO_ALT | GPIO_AF10 |  GPIO_PORTD | GPIO_PIN2)
#define GPIO_FSMC_NWE_3  (GPIO_ALT | GPIO_AF12 |  GPIO_PORTD | GPIO_PIN5)

/* I2C */

#define GPIO_I2C1_SCL_1  (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN6)
#define GPIO_I2C1_SCL_2  (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN8)
#define GPIO_I2C1_SDA_1  (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN7)
#define GPIO_I2C1_SDA_2  (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN9)
#define GPIO_I2C1_SMBA_0 (GPIO_ALT | GPIO_AF4 |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN5)

#define GPIO_I2C2_SCL_1   (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN10)
#define GPIO_I2C2_SCL_2   (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTF | GPIO_PIN1)
#define GPIO_I2C2_SDA_1   (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN11)
#define GPIO_I2C2_SDA_2   (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTF | GPIO_PIN0)
#define GPIO_I2C2_SDA_3   (GPIO_ALT | GPIO_AF9 |  GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN3)
#define GPIO_I2C2_SDA_4   (GPIO_ALT | GPIO_AF9 |  GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN9)
#define GPIO_I2C2_SMBA_1  (GPIO_ALT | GPIO_AF4 |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN12)
#define GPIO_I2C2_SMBA_2  (GPIO_ALT | GPIO_AF4 |  GPIO_PUSHPULL | GPIO_PORTF | GPIO_PIN2)

#define GPIO_I2C3_SCL_1  (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTA | GPIO_PIN8)
#define GPIO_I2C3_SDA_1  (GPIO_ALT | GPIO_AF4 |  GPIO_OPENDRAIN | GPIO_PORTC | GPIO_PIN9)
#define GPIO_I2C3_SDA_2  (GPIO_ALT | GPIO_AF9 |  GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN4)
#define GPIO_I2C3_SDA_3  (GPIO_ALT | GPIO_AF9 |  GPIO_OPENDRAIN | GPIO_PORTB | GPIO_PIN8)
#define GPIO_I2C3_SMBA_0 (GPIO_ALT | GPIO_AF4 |  GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN9)

/* I2S */

#define GPIO_I2S1_CK_1     (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN5)
#define GPIO_I2S1_CK_2     (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_I2S1_MCK_0    (GPIO_ALT | GPIO_AF5 | GPIO_PORTC | GPIO_PIN4)
#define GPIO_I2S1_SD_1     (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_I2S1_SD_2     (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_I2S1_WS_1     (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_I2S1_WS_2     (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN15)

#define GPIO_I2S2_CK_1     (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_I2S2_CK_2     (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_I2S2_CK_3     (GPIO_ALT | GPIO_AF5 | GPIO_PORTC | GPIO_PIN7)
#define GPIO_I2S2_CK_4     (GPIO_ALT | GPIO_AF5 | GPIO_PORTD | GPIO_PIN3)
#define GPIO_I2S2_SD_1     (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN15)
#define GPIO_I2S2_SD_2     (GPIO_ALT | GPIO_AF5 | GPIO_PORTC | GPIO_PIN3)
#define GPIO_I2S2_WS_1     (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_I2S2_WS_2     (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_I2S2_MCK_1    (GPIO_ALT | GPIO_AF5 | GPIO_PORTC | GPIO_PIN6)
#define GPIO_I2S2_MCK_2    (GPIO_ALT | GPIO_AF6 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_I2S2_MCK_3    (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_I2S2EXT_SD_1  (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN)
#define GPIO_I2S2EXT_SD_2  (GPIO_ALT | GPIO_AF6 | GPIO_PORTC | GPIO_PIN)
#define GPIO_I2S2_CKIN_1   (GPIO_ALT | GPIO_AF5 | GPIO_PORTC | GPIO_PIN9)
#define GPIO_I2S2_CKIN_2   (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_I2S2_CKIN_3   (GPIO_ALT | GPIO_AF5 | GPIO_PORTB | GPIO_PIN1)

#define GPIO_I2S3_CK_1     (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_I2S3_CK_2     (GPIO_ALT | GPIO_AF6 | GPIO_PORTC | GPIO_PIN10)
#define GPIO_I2S3_CK_3     (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_I2S3_MCK_1    (GPIO_ALT | GPIO_AF6 | GPIO_PORTC | GPIO_PIN7)
#define GPIO_I2S3_MCK_2    (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_I2S3_SD_1     (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_I2S3_SD_2     (GPIO_ALT | GPIO_AF6 | GPIO_PORTC | GPIO_PIN12)
#define GPIO_I2S3_SD_3     (GPIO_ALT | GPIO_AF5 | GPIO_PORTD | GPIO_PIN6)
#define GPIO_I2S3_WS_1     (GPIO_ALT | GPIO_AF6 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_I2S3_WS_2     (GPIO_ALT | GPIO_AF6 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_I2S3EXT_SD_2  (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN)
#define GPIO_I2S3EXT_SD_1  (GPIO_ALT | GPIO_AF5 | GPIO_PORTC | GPIO_PIN)

#define GPIO_I2S4_CK_1    (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_I2S4_CK_2    (GPIO_ALT | GPIO_AF5 | GPIO_PORTE | GPIO_PIN2)
#define GPIO_I2S4_CK_3    (GPIO_ALT | GPIO_AF5 | GPIO_PORTE | GPIO_PIN12)
#define GPIO_I2S4_SD_1    (GPIO_ALT | GPIO_AF5 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_I2S4_SD_2    (GPIO_ALT | GPIO_AF5 | GPIO_PORTE | GPIO_PIN6)
#define GPIO_I2S4_SD_3    (GPIO_ALT | GPIO_AF5 | GPIO_PORTE | GPIO_PIN14)
#define GPIO_I2S4_WS_1    (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_I2S4_WS_2    (GPIO_ALT | GPIO_AF5 | GPIO_PORTE | GPIO_PIN4)
#define GPIO_I2S4_WS_3    (GPIO_ALT | GPIO_AF6 | GPIO_PORTE | GPIO_PIN11)

#define GPIO_I2S5_CK_1    (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN0)
#define GPIO_I2S5_CK_2    (GPIO_ALT | GPIO_AF6 | GPIO_PORTE | GPIO_PIN2)
#define GPIO_I2S5_CK_3    (GPIO_ALT | GPIO_AF6 | GPIO_PORTE | GPIO_PIN12)
#define GPIO_I2S5_SD_1    (GPIO_ALT | GPIO_AF6 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_I2S5_SD_2    (GPIO_ALT | GPIO_AF6 | GPIO_PORTE | GPIO_PIN6)
#define GPIO_I2S5_SD_3    (GPIO_ALT | GPIO_AF6 | GPIO_PORTE | GPIO_PIN14)
#define GPIO_I2S5_SD_4    (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_I2S5_WS_1    (GPIO_ALT | GPIO_AF6 | GPIO_PORTB | GPIO_PIN1)
#define GPIO_I2S5_WS_2    (GPIO_ALT | GPIO_AF6 | GPIO_PORTE | GPIO_PIN4)
#define GPIO_I2S5_WS_3    (GPIO_ALT | GPIO_AF6 | GPIO_PORTE | GPIO_PIN11)

/* JTAG */

#define GPIO_JTCK_SWCLK_0 (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN14)
#define GPIO_JTDI_0       (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_JTDO_SWO_0   (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_JTMS_SWDIO_0 (GPIO_ALT | GPIO_AF0 | GPIO_PORTA | GPIO_PIN13)
#define GPIO_JTRST_0      (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN4)

/* OTG FS/HS (VBUS PA9 is not an alternate configuration) */

#define GPIO_OTGFS_DM_0   (GPIO_ALT | GPIO_FLOAT  | GPIO_AF10 |  GPIO_PUSHPULL  | GPIO_PORTA | GPIO_PIN11)
#define GPIO_OTGFS_DP_0   (GPIO_ALT | GPIO_FLOAT  | GPIO_AF10 |  GPIO_PUSHPULL  | GPIO_PORTA | GPIO_PIN12)
#define GPIO_OTGFS_ID_0   (GPIO_ALT | GPIO_PULLUP | GPIO_AF10 |  GPIO_OPENDRAIN | GPIO_PORTA | GPIO_PIN10)
#define GPIO_OTGFS_SOF_0  (GPIO_ALT | GPIO_FLOAT  | GPIO_AF10 |  GPIO_PUSHPULL  | GPIO_PORTA | GPIO_PIN8)
#define GPIO_OTGFS_VBUS_0 (GPIO_ALT | GPIO_FLOAT  | GPIO_AF10 |  GPIO_PUSHPULL  | GPIO_PORTA | GPIO_PIN9)

/* RTC */

#define GPIO_RTC_50HZ_0 (GPIO_ALT | GPIO_AF0 | GPIO_PORTB | GPIO_PIN15)

/* SDIO
 *
 * Note that the below configures GPIO_SPEED_50MHz I/O, that means for using
 * the SDIO that you must enable I/O Compensation via the configuration
 * option CONFIG_STM32_SYSCFG_IOCOMPENSATION=y.
 */

#define GPIO_SDIO_CMD_1  (GPIO_ALT | GPIO_AF12 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN2)
#define GPIO_SDIO_CMD_2  (GPIO_ALT | GPIO_AF12 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN6)
#define GPIO_SDIO_D0_1   (GPIO_ALT | GPIO_AF12 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN8)
#define GPIO_SDIO_D0_2   (GPIO_ALT | GPIO_AF12 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN6)
#define GPIO_SDIO_D0_3   (GPIO_ALT | GPIO_AF12 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN4)
#define GPIO_SDIO_D3_1   (GPIO_ALT | GPIO_AF12 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN11)
#define GPIO_SDIO_D3_2   (GPIO_ALT | GPIO_AF12 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN5)
#define GPIO_SDIO_D4_0   (GPIO_ALT | GPIO_AF12 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN8)
#define GPIO_SDIO_D5_0   (GPIO_ALT | GPIO_AF12 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN9)
#define GPIO_SDIO_D6_1   (GPIO_ALT | GPIO_AF12 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN6)
#define GPIO_SDIO_D6_2   (GPIO_ALT | GPIO_AF12 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN14)
#define GPIO_SDIO_D7_1   (GPIO_ALT | GPIO_AF12 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN7)
#define GPIO_SDIO_D7_2   (GPIO_ALT | GPIO_AF12 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN10)
#define GPIO_SDIO_D1_1   (GPIO_ALT | GPIO_AF12 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN9)
#define GPIO_SDIO_D1_2   (GPIO_ALT | GPIO_AF12 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN8)
#define GPIO_SDIO_D2_1   (GPIO_ALT | GPIO_AF12 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN10)
#define GPIO_SDIO_D2_2   (GPIO_ALT | GPIO_AF12 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN9)
#define GPIO_SDIO_CK_1   (GPIO_ALT | GPIO_AF12 | GPIO_FLOAT  |  GPIO_PORTC | GPIO_PIN12)
#define GPIO_SDIO_CK_2   (GPIO_ALT | GPIO_AF12 | GPIO_FLOAT  |  GPIO_PORTB | GPIO_PIN15)

/* SPI */

#define GPIO_SPI1_MISO_1  (GPIO_ALT | GPIO_AF5 |  GPIO_PORTA | GPIO_PIN6)
#define GPIO_SPI1_MISO_2  (GPIO_ALT | GPIO_AF5 |  GPIO_PORTB | GPIO_PIN4)
#define GPIO_SPI1_MOSI_1  (GPIO_ALT | GPIO_AF5 |  GPIO_PORTA | GPIO_PIN7)
#define GPIO_SPI1_MOSI_2  (GPIO_ALT | GPIO_AF5 |  GPIO_PORTB | GPIO_PIN5)
#define GPIO_SPI1_NSS_1   (GPIO_ALT | GPIO_AF5 |  GPIO_PORTA | GPIO_PIN15)
#define GPIO_SPI1_NSS_2   (GPIO_ALT | GPIO_AF5 |  GPIO_PORTA | GPIO_PIN4)
#define GPIO_SPI1_SCK_1   (GPIO_ALT | GPIO_AF5 |  GPIO_PORTA | GPIO_PIN5)
#define GPIO_SPI1_SCK_2   (GPIO_ALT | GPIO_AF5 |  GPIO_PORTB | GPIO_PIN3)

#define GPIO_SPI2_MISO_1  (GPIO_ALT | GPIO_AF5 |  GPIO_PORTB | GPIO_PIN14)
#define GPIO_SPI2_MISO_2  (GPIO_ALT | GPIO_AF5 |  GPIO_PORTC | GPIO_PIN2)
#define GPIO_SPI2_MOSI_1  (GPIO_ALT | GPIO_AF5 |  GPIO_PORTB | GPIO_PIN15)
#define GPIO_SPI2_MOSI_2  (GPIO_ALT | GPIO_AF5 |  GPIO_PORTC | GPIO_PIN3)
#define GPIO_SPI2_NSS_1   (GPIO_ALT | GPIO_AF5 |  GPIO_PORTB | GPIO_PIN12)
#define GPIO_SPI2_NSS_2   (GPIO_ALT | GPIO_AF5 |  GPIO_PORTB | GPIO_PIN9)
#define GPIO_SPI2_SCK_1   (GPIO_ALT | GPIO_AF5 |  GPIO_PORTB | GPIO_PIN10)
#define GPIO_SPI2_SCK_2   (GPIO_ALT | GPIO_AF5 |  GPIO_PORTB | GPIO_PIN13)
#define GPIO_SPI2_SCK_3   (GPIO_ALT | GPIO_AF5 |  GPIO_PORTC | GPIO_PIN7)
#define GPIO_SPI2_SCK_4   (GPIO_ALT | GPIO_AF5 |  GPIO_PORTD | GPIO_PIN3)

#define GPIO_SPI3_MISO_1  (GPIO_ALT | GPIO_AF6 |  GPIO_PORTB | GPIO_PIN4)
#define GPIO_SPI3_MISO_2  (GPIO_ALT | GPIO_AF6 |  GPIO_PORTC | GPIO_PIN11)
#define GPIO_SPI3_MOSI_1  (GPIO_ALT | GPIO_AF6 |  GPIO_PORTB | GPIO_PIN5)
#define GPIO_SPI3_MOSI_2  (GPIO_ALT | GPIO_AF6 |  GPIO_PORTC | GPIO_PIN12)
#define GPIO_SPI3_MOSI_3  (GPIO_ALT | GPIO_AF5 |  GPIO_PORTD | GPIO_PIN6)
#define GPIO_SPI3_NSS_1   (GPIO_ALT | GPIO_AF6 |  GPIO_PORTA | GPIO_PIN15)
#define GPIO_SPI3_NSS_2   (GPIO_ALT | GPIO_AF6 |  GPIO_PORTA | GPIO_PIN4)
#define GPIO_SPI3_SCK_1   (GPIO_ALT | GPIO_AF6 |  GPIO_PORTB | GPIO_PIN3)
#define GPIO_SPI3_SCK_2   (GPIO_ALT | GPIO_AF6 |  GPIO_PORTC | GPIO_PIN10)
#define GPIO_SPI3_SCK_3   (GPIO_ALT | GPIO_AF7 |  GPIO_PORTB | GPIO_PIN12)

#define GPIO_SPI4_MISO_1  (GPIO_ALT | GPIO_AF5 |  GPIO_PORTE | GPIO_PIN5)
#define GPIO_SPI4_MISO_2  (GPIO_ALT | GPIO_AF5 |  GPIO_PORTE | GPIO_PIN13)
#define GPIO_SPI4_MISO_3  (GPIO_ALT | GPIO_AF6 |  GPIO_PORTA | GPIO_PIN11)
#define GPIO_SPI4_MOSI_1  (GPIO_ALT | GPIO_AF5 |  GPIO_PORTE | GPIO_PIN6)
#define GPIO_SPI4_MOSI_2  (GPIO_ALT | GPIO_AF5 |  GPIO_PORTE | GPIO_PIN14)
#define GPIO_SPI4_MOSI_3  (GPIO_ALT | GPIO_AF5 |  GPIO_PORTA | GPIO_PIN1)
#define GPIO_SPI4_NSS_1   (GPIO_ALT | GPIO_AF5 |  GPIO_PORTE | GPIO_PIN4)
#define GPIO_SPI4_NSS_2   (GPIO_ALT | GPIO_AF5 |  GPIO_PORTE | GPIO_PIN11)
#define GPIO_SPI4_NSS_3   (GPIO_ALT | GPIO_AF6 |  GPIO_PORTB | GPIO_PIN12)
#define GPIO_SPI4_SCK_1   (GPIO_ALT | GPIO_AF5 |  GPIO_PORTE | GPIO_PIN2)
#define GPIO_SPI4_SCK_2   (GPIO_ALT | GPIO_AF5 |  GPIO_PORTE | GPIO_PIN12)
#define GPIO_SPI4_SCK_3   (GPIO_ALT | GPIO_AF6 |  GPIO_PORTB | GPIO_PIN13)

#define GPIO_SPI5_MISO_1  (GPIO_ALT | GPIO_AF6 |  GPIO_PORTA | GPIO_PIN12)
#define GPIO_SPI5_MISO_2  (GPIO_ALT | GPIO_AF6 |  GPIO_PORTE | GPIO_PIN5)
#define GPIO_SPI5_MISO_3  (GPIO_ALT | GPIO_AF6 |  GPIO_PORTE | GPIO_PIN13)
#define GPIO_SPI5_MOSI_1  (GPIO_ALT | GPIO_AF6 |  GPIO_PORTA | GPIO_PIN10)
#define GPIO_SPI5_MOSI_2  (GPIO_ALT | GPIO_AF6 |  GPIO_PORTB | GPIO_PIN8)
#define GPIO_SPI5_MOSI_3  (GPIO_ALT | GPIO_AF6 |  GPIO_PORTE | GPIO_PIN6)
#define GPIO_SPI5_MOSI_4  (GPIO_ALT | GPIO_AF6 |  GPIO_PORTE | GPIO_PIN14)
#define GPIO_SPI5_NSS_1   (GPIO_ALT | GPIO_AF6 |  GPIO_PORTB | GPIO_PIN1)
#define GPIO_SPI5_NSS_2   (GPIO_ALT | GPIO_AF6 |  GPIO_PORTE | GPIO_PIN4)
#define GPIO_SPI5_NSS_3   (GPIO_ALT | GPIO_AF6 |  GPIO_PORTE | GPIO_PIN11)
#define GPIO_SPI5_SCK_1   (GPIO_ALT | GPIO_AF6 |  GPIO_PORTB | GPIO_PIN0)
#define GPIO_SPI5_SCK_2   (GPIO_ALT | GPIO_AF6 |  GPIO_PORTE | GPIO_PIN2)
#define GPIO_SPI5_SCK_3   (GPIO_ALT | GPIO_AF6 |  GPIO_PORTE | GPIO_PIN12)

/* Timers */

#define GPIO_TIM1_BKIN_1  (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM1_BKIN_2  (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_TIM1_BKIN_3  (GPIO_ALT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN15)
#define GPIO_TIM1_CH1N_1  (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM1_CH1N_2  (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_TIM1_CH1N_3  (GPIO_ALT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN8)
#define GPIO_TIM1_CH2N_1  (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN0)
#define GPIO_TIM1_CH2N_2  (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_TIM1_CH2N_3  (GPIO_ALT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN10)
#define GPIO_TIM1_CH3N_1  (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN1)
#define GPIO_TIM1_CH3N_2  (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN15)
#define GPIO_TIM1_CH3N_3  (GPIO_ALT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN12)
#define GPIO_TIM1_ETR_1   (GPIO_ALT | GPIO_AF1 |  GPIO_FLOAT | GPIO_PORTA | GPIO_PIN12)
#define GPIO_TIM1_ETR_2   (GPIO_ALT | GPIO_AF1 |  GPIO_FLOAT | GPIO_PORTE | GPIO_PIN7)
#define GPIO_TIM1_ETR_3   (GPIO_ALT | GPIO_AF1 |  GPIO_FLOAT | GPIO_PORTF | GPIO_PIN10)
#define GPIO_TIM1_CH1_1   (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_TIM1_CH1_2   (GPIO_ALT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN9)
#define GPIO_TIM1_CH2_1   (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN9)
#define GPIO_TIM1_CH2_2   (GPIO_ALT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN11)
#define GPIO_TIM1_CH3_1   (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN10)
#define GPIO_TIM1_CH3_2   (GPIO_ALT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN13)
#define GPIO_TIM1_CH4_1   (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_TIM1_CH4_2   (GPIO_ALT | GPIO_AF1 | GPIO_PORTE | GPIO_PIN14)

#define GPIO_TIM2_CH1_1   (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_TIM2_CH1_2   (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN5)
#define GPIO_TIM2_CH1_3   (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN15)
#define GPIO_TIM2_CH2_1   (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_TIM2_CH2_2   (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN3)
#define GPIO_TIM2_CH3_1   (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM2_CH3_2   (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN10)
#define GPIO_TIM2_CH4_1   (GPIO_ALT | GPIO_AF1 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_TIM2_CH4_2   (GPIO_ALT | GPIO_AF1 | GPIO_PORTB | GPIO_PIN11)
#define GPIO_TIM2_ETR_1   (GPIO_ALT | GPIO_AF1 |  GPIO_FLOAT | GPIO_PORTA | GPIO_PIN0)
#define GPIO_TIM2_ETR_2   (GPIO_ALT | GPIO_AF1 |  GPIO_FLOAT | GPIO_PORTA | GPIO_PIN15)
#define GPIO_TIM2_ETR_3   (GPIO_ALT | GPIO_AF1 |  GPIO_FLOAT | GPIO_PORTA | GPIO_PIN5)

#define GPIO_TIM3_CH1_1  (GPIO_ALT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM3_CH1_2  (GPIO_ALT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN4)
#define GPIO_TIM3_CH1_3  (GPIO_ALT | GPIO_AF2 | GPIO_PORTC | GPIO_PIN6)
#define GPIO_TIM3_CH2_1  (GPIO_ALT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM3_CH2_2  (GPIO_ALT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN5)
#define GPIO_TIM3_CH2_3  (GPIO_ALT | GPIO_AF2 | GPIO_PORTC | GPIO_PIN7)
#define GPIO_TIM3_CH3_1  (GPIO_ALT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN0)
#define GPIO_TIM3_CH3_2  (GPIO_ALT | GPIO_AF2 | GPIO_PORTC | GPIO_PIN8)
#define GPIO_TIM3_CH4_1  (GPIO_ALT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN1)
#define GPIO_TIM3_CH4_2  (GPIO_ALT | GPIO_AF2 | GPIO_PORTC | GPIO_PIN9)
#define GPIO_TIM3_ETR_0  (GPIO_ALT | GPIO_AF2 |  GPIO_FLOAT | GPIO_PORTD | GPIO_PIN2)

#define GPIO_TIM4_CH1_1  (GPIO_ALT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN6)
#define GPIO_TIM4_CH1_2  (GPIO_ALT | GPIO_AF2 | GPIO_PORTD | GPIO_PIN12)
#define GPIO_TIM4_CH2_1  (GPIO_ALT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN7)
#define GPIO_TIM4_CH2_2  (GPIO_ALT | GPIO_AF2 | GPIO_PORTD | GPIO_PIN13)
#define GPIO_TIM4_CH3_1  (GPIO_ALT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_TIM4_CH3_2  (GPIO_ALT | GPIO_AF2 | GPIO_PORTD | GPIO_PIN14)
#define GPIO_TIM4_CH4_1  (GPIO_ALT | GPIO_AF2 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_TIM4_CH4_2  (GPIO_ALT | GPIO_AF2 | GPIO_PORTD | GPIO_PIN15)
#define GPIO_TIM4_ETR_0  (GPIO_ALT | GPIO_AF2 |  GPIO_FLOAT | GPIO_PORTE | GPIO_PIN0)
#define GPIO_TIM5_CH1_1  (GPIO_ALT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_TIM5_CH1_2  (GPIO_ALT | GPIO_AF2 | GPIO_PORTF | GPIO_PIN3)
#define GPIO_TIM5_CH2_1  (GPIO_ALT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_TIM5_CH2_2  (GPIO_ALT | GPIO_AF2 | GPIO_PORTF | GPIO_PIN4)
#define GPIO_TIM5_CH3_1  (GPIO_ALT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM5_CH3_2  (GPIO_ALT | GPIO_AF2 | GPIO_PORTF | GPIO_PIN5)
#define GPIO_TIM5_CH4_1  (GPIO_ALT | GPIO_AF2 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_TIM5_CH4_2  (GPIO_ALT | GPIO_AF2 | GPIO_PORTF | GPIO_PIN10)

#define GPIO_TIM8_BKIN_1  (GPIO_ALT | GPIO_AF3 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM8_BKIN_2  (GPIO_ALT | GPIO_AF3 | GPIO_PORTF | GPIO_PIN12)
#define GPIO_TIM8_CH1N_1  (GPIO_ALT | GPIO_AF3 | GPIO_PORTA | GPIO_PIN5)
#define GPIO_TIM8_CH1N_2  (GPIO_ALT | GPIO_AF3 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM8_CH2N_1  (GPIO_ALT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN0)
#define GPIO_TIM8_CH2N_2  (GPIO_ALT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_TIM8_CH3N_1  (GPIO_ALT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN1)
#define GPIO_TIM8_CH3N_2  (GPIO_ALT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN15)
#define GPIO_TIM8_ETR_1   (GPIO_ALT | GPIO_AF3 |  GPIO_FLOAT | GPIO_PORTA | GPIO_PIN0)
#define GPIO_TIM8_ETR_2   (GPIO_ALT | GPIO_AF3 |  GPIO_FLOAT | GPIO_PORTF | GPIO_PIN11)
#define GPIO_TIM8_CH1_0   (GPIO_ALT | GPIO_AF3 | GPIO_PORTC | GPIO_PIN6)
#define GPIO_TIM8_CH2_0   (GPIO_ALT | GPIO_AF3 | GPIO_PORTC | GPIO_PIN7)
#define GPIO_TIM8_CH3_0   (GPIO_ALT | GPIO_AF3 | GPIO_PORTC | GPIO_PIN8)
#define GPIO_TIM8_CH4_0   (GPIO_ALT | GPIO_AF3 | GPIO_PORTC | GPIO_PIN9)

#define GPIO_TIM9_CH1_1  (GPIO_ALT | GPIO_AF3 | GPIO_PORTA | GPIO_PIN2)
#define GPIO_TIM9_CH1_2  (GPIO_ALT | GPIO_AF3 | GPIO_PORTE | GPIO_PIN5)
#define GPIO_TIM9_CH2_1  (GPIO_ALT | GPIO_AF3 | GPIO_PORTA | GPIO_PIN3)
#define GPIO_TIM9_CH2_2  (GPIO_ALT | GPIO_AF3 | GPIO_PORTE | GPIO_PIN6)

#define GPIO_TIM10_CH1_1 (GPIO_ALT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN8)
#define GPIO_TIM10_CH1_2 (GPIO_ALT | GPIO_AF3 | GPIO_PORTF | GPIO_PIN6)

#define GPIO_TIM11_CH1_1 (GPIO_ALT | GPIO_AF3 | GPIO_PORTB | GPIO_PIN9)
#define GPIO_TIM11_CH1_2 (GPIO_ALT | GPIO_AF3 | GPIO_PORTF | GPIO_PIN7)

#define GPIO_TIM12_CH1_0 (GPIO_ALT | GPIO_AF9 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_TIM12_CH2_0 (GPIO_ALT | GPIO_AF9 | GPIO_PORTB | GPIO_PIN15)

#define GPIO_TIM13_CH1_1 (GPIO_ALT | GPIO_AF9 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_TIM13_CH1_2 (GPIO_ALT | GPIO_AF9 | GPIO_PORTF | GPIO_PIN8)

#define GPIO_TIM14_CH1_1 (GPIO_ALT | GPIO_AF9 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_TIM14_CH1_2 (GPIO_ALT | GPIO_AF9 | GPIO_PORTF | GPIO_PIN9)

/* Trace */

#define GPIO_TRACECLK_0 (GPIO_ALT | GPIO_AF0 | GPIO_PORTE | GPIO_PIN2)
#define GPIO_TRACED0_1  (GPIO_ALT | GPIO_AF0 | GPIO_PORTE | GPIO_PIN3)
#define GPIO_TRACED0_2  (GPIO_ALT | GPIO_AF0 | GPIO_PORTF | GPIO_PIN6)
#define GPIO_TRACED1_1  (GPIO_ALT | GPIO_AF0 | GPIO_PORTD | GPIO_PIN3)
#define GPIO_TRACED1_2  (GPIO_ALT | GPIO_AF0 | GPIO_PORTE | GPIO_PIN4)
#define GPIO_TRACED1_3  (GPIO_ALT | GPIO_AF0 | GPIO_PORTF | GPIO_PIN7)

#define GPIO_TRACED2_1  (GPIO_ALT | GPIO_AF0 | GPIO_PORTE | GPIO_PIN5)
#define GPIO_TRACED2_2  (GPIO_ALT | GPIO_AF0 | GPIO_PORTG | GPIO_PIN13)
#define GPIO_TRACED3_1  (GPIO_ALT | GPIO_AF0 | GPIO_PORTE | GPIO_PIN6)
#define GPIO_TRACED3_2  (GPIO_ALT | GPIO_AF0 | GPIO_PORTG | GPIO_PIN14)

/* UARTs/USARTs */

#define GPIO_USART1_CK_0   (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN8)
#define GPIO_USART1_CTS_0  (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN11)
#define GPIO_USART1_RTS_0  (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN12)
#define GPIO_USART1_RX_1   (GPIO_ALT | GPIO_AF7 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN10)
#define GPIO_USART1_RX_2   (GPIO_ALT | GPIO_AF7 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN3)
#define GPIO_USART1_RX_3   (GPIO_ALT | GPIO_AF7 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN7)
#define GPIO_USART1_TX_1   (GPIO_ALT | GPIO_AF7 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN9)
#define GPIO_USART1_TX_2   (GPIO_ALT | GPIO_AF7 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN6)
#define GPIO_USART1_TX_3   (GPIO_ALT | GPIO_AF7 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN15)

#define GPIO_USART2_CK_1   (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN4)
#define GPIO_USART2_CK_2   (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN7)
#define GPIO_USART2_CTS_1  (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN0)
#define GPIO_USART2_CTS_2  (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN3)
#define GPIO_USART2_RTS_1  (GPIO_ALT | GPIO_AF7 | GPIO_PORTA | GPIO_PIN1)
#define GPIO_USART2_RTS_2  (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN4)
#define GPIO_USART2_RX_1   (GPIO_ALT | GPIO_AF7 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN3)
#define GPIO_USART2_RX_2   (GPIO_ALT | GPIO_AF7 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN6)
#define GPIO_USART2_TX_1   (GPIO_ALT | GPIO_AF7 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN2)
#define GPIO_USART2_TX_2   (GPIO_ALT | GPIO_AF7 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN5)

#define GPIO_USART3_CK_1   (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN12)
#define GPIO_USART3_CK_2   (GPIO_ALT | GPIO_AF7 | GPIO_PORTC | GPIO_PIN12)
#define GPIO_USART3_CK_3   (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN10)
#define GPIO_USART3_CTS_1  (GPIO_ALT | GPIO_AF8 | GPIO_PORTB | GPIO_PIN13)
#define GPIO_USART3_CTS_2  (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN11)
#define GPIO_USART3_RTS_1  (GPIO_ALT | GPIO_AF7 | GPIO_PORTB | GPIO_PIN14)
#define GPIO_USART3_RTS_2  (GPIO_ALT | GPIO_AF7 | GPIO_PORTD | GPIO_PIN12)
#define GPIO_USART3_RX_1   (GPIO_ALT | GPIO_AF7 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN11)
#define GPIO_USART3_RX_2   (GPIO_ALT | GPIO_AF7 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN11)
#define GPIO_USART3_RX_3   (GPIO_ALT | GPIO_AF7 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN9)
#define GPIO_USART3_RX_4   (GPIO_ALT | GPIO_AF7 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN5)
#define GPIO_USART3_TX_1   (GPIO_ALT | GPIO_AF7 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTB | GPIO_PIN10)
#define GPIO_USART3_TX_2   (GPIO_ALT | GPIO_AF7 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN10)
#define GPIO_USART3_TX_3   (GPIO_ALT | GPIO_AF7 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTD | GPIO_PIN8)

#define GPIO_USART6_CK_1   (GPIO_ALT | GPIO_AF8 | GPIO_PORTC | GPIO_PIN8)
#define GPIO_USART6_CK_2   (GPIO_ALT | GPIO_AF8 | GPIO_PORTG | GPIO_PIN7)
#define GPIO_USART6_CTS_1  (GPIO_ALT | GPIO_AF8 | GPIO_PORTG | GPIO_PIN13)
#define GPIO_USART6_CTS_2  (GPIO_ALT | GPIO_AF8 | GPIO_PORTG | GPIO_PIN15)
#define GPIO_USART6_RTS_1  (GPIO_ALT | GPIO_AF8 | GPIO_PORTG | GPIO_PIN12)
#define GPIO_USART6_RTS_2  (GPIO_ALT | GPIO_AF8 | GPIO_PORTG | GPIO_PIN8)
#define GPIO_USART6_RX_1   (GPIO_ALT | GPIO_AF8 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN7)
#define GPIO_USART6_RX_2   (GPIO_ALT | GPIO_AF8 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTG | GPIO_PIN9)
#define GPIO_USART6_RX_3   (GPIO_ALT | GPIO_AF8 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN12)
#define GPIO_USART6_TX_1   (GPIO_ALT | GPIO_AF8 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTC | GPIO_PIN6)
#define GPIO_USART6_TX_2   (GPIO_ALT | GPIO_AF8 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTG | GPIO_PIN14)
#define GPIO_USART6_TX_3   (GPIO_ALT | GPIO_AF8 | GPIO_PULLUP |  GPIO_PUSHPULL | GPIO_PORTA | GPIO_PIN11)

/* Quad SPI */

#define GPIO_QUADSPI_BK1_IO0_1  (GPIO_ALT | GPIO_AF10 | GPIO_PORTF | GPIO_PIN8)
#define GPIO_QUADSPI_BK1_IO0_2  (GPIO_ALT | GPIO_AF9  | GPIO_PORTC | GPIO_PIN9)
#define GPIO_QUADSPI_BK1_IO0_3  (GPIO_ALT | GPIO_AF9  | GPIO_PORTD | GPIO_PIN11)
#define GPIO_QUADSPI_BK1_IO1_1  (GPIO_ALT | GPIO_AF10 | GPIO_PORTF | GPIO_PIN9)
#define GPIO_QUADSPI_BK1_IO1_2  (GPIO_ALT | GPIO_AF9  | GPIO_PORTC | GPIO_PIN10)
#define GPIO_QUADSPI_BK1_IO1_3  (GPIO_ALT | GPIO_AF9  | GPIO_PORTD | GPIO_PIN12)
#define GPIO_QUADSPI_BK1_IO2_1  (GPIO_ALT | GPIO_AF9  | GPIO_PORTE | GPIO_PIN2)
#define GPIO_QUADSPI_BK1_IO2_2  (GPIO_ALT | GPIO_AF9  | GPIO_PORTF | GPIO_PIN7)
#define GPIO_QUADSPI_BK1_IO2_3  (GPIO_ALT | GPIO_AF9  | GPIO_PORTC | GPIO_PIN8)
#define GPIO_QUADSPI_BK1_IO3_1  (GPIO_ALT | GPIO_AF9  | GPIO_PORTA | GPIO_PIN1)
#define GPIO_QUADSPI_BK1_IO3_2  (GPIO_ALT | GPIO_AF9  | GPIO_PORTD | GPIO_PIN13)
#define GPIO_QUADSPI_BK1_IO3_3  (GPIO_ALT | GPIO_AF9  | GPIO_PORTF | GPIO_PIN6)
#define GPIO_QUADSPI_BK1_NCS_1  (GPIO_ALT | GPIO_AF10 | GPIO_PORTB | GPIO_PIN6)
#define GPIO_QUADSPI_BK1_NCS_2  (GPIO_ALT | GPIO_AF10 | GPIO_PORTG | GPIO_PIN6)

#define GPIO_QUADSPI_BK2_IO0_1  (GPIO_ALT | GPIO_AF10 | GPIO_PORTE | GPIO_PIN7)
#define GPIO_QUADSPI_BK2_IO0_2  (GPIO_ALT | GPIO_AF10 | GPIO_PORTA | GPIO_PIN6)
#define GPIO_QUADSPI_BK2_IO1_1  (GPIO_ALT | GPIO_AF10 | GPIO_PORTE | GPIO_PIN8)
#define GPIO_QUADSPI_BK2_IO1_2  (GPIO_ALT | GPIO_AF10 | GPIO_PORTA | GPIO_PIN7)
#define GPIO_QUADSPI_BK2_IO2_1  (GPIO_ALT | GPIO_AF10 | GPIO_PORTE | GPIO_PIN9)
#define GPIO_QUADSPI_BK2_IO2_2  (GPIO_ALT | GPIO_AF9  | GPIO_PORTG | GPIO_PIN9)
#define GPIO_QUADSPI_BK2_IO2_3  (GPIO_ALT | GPIO_AF10 | GPIO_PORTC | GPIO_PIN4)
#define GPIO_QUADSPI_BK2_IO3_1  (GPIO_ALT | GPIO_AF10 | GPIO_PORTC | GPIO_PIN5)
#define GPIO_QUADSPI_BK2_IO3_2  (GPIO_ALT | GPIO_AF10 | GPIO_PORTE | GPIO_PIN10)
#define GPIO_QUADSPI_BK2_IO3_3  (GPIO_ALT | GPIO_AF9  | GPIO_PORTG | GPIO_PIN14)
#define GPIO_QUADSPI_BK2_NCS_0  (GPIO_ALT | GPIO_AF9  | GPIO_PORTC | GPIO_PIN11)

#define GPIO_QUADSPI_CLK_1      (GPIO_ALT | GPIO_AF9  | GPIO_PORTB | GPIO_PIN1)
#define GPIO_QUADSPI_CLK_2      (GPIO_ALT | GPIO_AF9  | GPIO_PORTB | GPIO_PIN2)
#define GPIO_QUADSPI_CLK_3      (GPIO_ALT | GPIO_AF9  | GPIO_PORTD | GPIO_PIN3)

#endif /* __ARCH_ARM_SRC_STM32_HARDWARE_STM32F412XX_PINMAP_H */
