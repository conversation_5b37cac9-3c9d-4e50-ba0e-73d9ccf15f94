/****************************************************************************
 * arch/arm/src/stm32/hardware/stm32f103z_pinmap.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_STM32_HARDWARE_STM32F103Z_PINMAP_H
#define __ARCH_ARM_SRC_STM32_HARDWARE_STM32F103Z_PINMAP_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

#include "stm32_gpio.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Alternate Pin Functions: */

/* ADC */

#define GPIO_ADC1_IN0_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN0)
#define GPIO_ADC1_IN1_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN1)
#define GPIO_ADC1_IN2_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN2)
#define GPIO_ADC1_IN3_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN3)
#define GPIO_ADC1_IN4_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN4)
#define GPIO_ADC1_IN5_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN5)
#define GPIO_ADC1_IN6_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN6)
#define GPIO_ADC1_IN7_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN7)
#define GPIO_ADC1_IN8_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN0)
#define GPIO_ADC1_IN9_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN1)
#define GPIO_ADC1_IN10_0    (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN0)
#define GPIO_ADC1_IN11_0    (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN1)
#define GPIO_ADC1_IN12_0    (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN2)
#define GPIO_ADC1_IN13_0    (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN3)
#define GPIO_ADC1_IN14_0    (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN4)
#define GPIO_ADC1_IN15_0    (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN5)

#define GPIO_ADC2_IN0_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN0)
#define GPIO_ADC2_IN1_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN1)
#define GPIO_ADC2_IN2_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN2)
#define GPIO_ADC2_IN3_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN3)
#define GPIO_ADC2_IN4_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN4)
#define GPIO_ADC2_IN5_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN5)
#define GPIO_ADC2_IN6_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN6)
#define GPIO_ADC2_IN7_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN7)
#define GPIO_ADC2_IN8_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN0)
#define GPIO_ADC2_IN9_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN1)
#define GPIO_ADC2_IN10_0    (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN0)
#define GPIO_ADC2_IN11_0    (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN1)
#define GPIO_ADC2_IN12_0    (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN2)
#define GPIO_ADC2_IN13_0    (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN3)
#define GPIO_ADC2_IN14_0    (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN4)
#define GPIO_ADC2_IN15_0    (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN5)

#define GPIO_ADC3_IN0_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN0)
#define GPIO_ADC3_IN1_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN1)
#define GPIO_ADC3_IN2_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN2)
#define GPIO_ADC3_IN3_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN3)
#define GPIO_ADC3_IN4_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTF|GPIO_PIN6)
#define GPIO_ADC3_IN5_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTF|GPIO_PIN7)
#define GPIO_ADC3_IN6_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTF|GPIO_PIN8)
#define GPIO_ADC3_IN7_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTF|GPIO_PIN9)
#define GPIO_ADC3_IN8_0     (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTF|GPIO_PIN10)
#define GPIO_ADC3_IN10_0    (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN0)
#define GPIO_ADC3_IN11_0    (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN1)
#define GPIO_ADC3_IN12_0    (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN2)
#define GPIO_ADC3_IN13_0    (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN3)

/* DAC - "Once the DAC channelx is enabled, the corresponding GPIO pin
 * (PA4 or PA5) is automatically connected to the analog converter output
 * (DAC_OUTx). In order to avoid parasitic consumption, the PA4 or PA5 pin
 * should first be configured to analog (AIN)."
 */

#define GPIO_DAC1_OUT1_0    (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN4)
#define GPIO_DAC1_OUT2_0    (GPIO_INPUT|GPIO_CNF_ANALOGIN|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN5)

/* TIMERS */

#if defined(CONFIG_STM32_TIM1_FULL_REMAP)
#  define GPIO_TIM1_ETR_0      (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTE|GPIO_PIN7)
#  define GPIO_TIM1_CH1IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTE|GPIO_PIN9)
#  define GPIO_TIM1_CH1OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN9)
#  define GPIO_TIM1_CH2IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTE|GPIO_PIN11)
#  define GPIO_TIM1_CH2OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN11)
#  define GPIO_TIM1_CH3IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTE|GPIO_PIN13)
#  define GPIO_TIM1_CH3OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN13)
#  define GPIO_TIM1_CH4IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTE|GPIO_PIN14)
#  define GPIO_TIM1_CH4OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN14)
#  define GPIO_TIM1_BKIN_0     (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTE|GPIO_PIN15)
#  define GPIO_TIM1_CH1NOUT_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN8)
#  define GPIO_TIM1_CH2NOUT_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN10)
#  define GPIO_TIM1_CH3NOUT_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN12)
#elif defined(CONFIG_STM32_TIM1_PARTIAL_REMAP)
#  define GPIO_TIM1_ETR_0      (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN12)
#  define GPIO_TIM1_CH1IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN8)
#  define GPIO_TIM1_CH1OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN8)
#  define GPIO_TIM1_CH2IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN9)
#  define GPIO_TIM1_CH2OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN9)
#  define GPIO_TIM1_CH3IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN10)
#  define GPIO_TIM1_CH3OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN10)
#  define GPIO_TIM1_CH4IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN11)
#  define GPIO_TIM1_CH4OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN11)
#  define GPIO_TIM1_BKIN_0     (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN6)
#  define GPIO_TIM1_CH1NOUT_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN7)
#  define GPIO_TIM1_CH2NOUT_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN0)
#  define GPIO_TIM1_CH3NOUT_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN1)
#else
#  define GPIO_TIM1_ETR_0      (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN12)
#  define GPIO_TIM1_CH1IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN8)
#  define GPIO_TIM1_CH1OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN8)
#  define GPIO_TIM1_CH2IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN9)
#  define GPIO_TIM1_CH2OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN9)
#  define GPIO_TIM1_CH3IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN10)
#  define GPIO_TIM1_CH3OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN10)
#  define GPIO_TIM1_CH4IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN11)
#  define GPIO_TIM1_CH4OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN11)
#  define GPIO_TIM1_BKIN_0     (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN12)
#  define GPIO_TIM1_CH1NOUT_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN13)
#  define GPIO_TIM1_CH2NOUT_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN14)
#  define GPIO_TIM1_CH3NOUT_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN15)
#endif

#if defined(CONFIG_STM32_TIM2_FULL_REMAP)
#  define GPIO_TIM2_ETR_0      (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN15)
#  define GPIO_TIM2_CH1IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN15)
#  define GPIO_TIM2_CH1OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN15)
#  define GPIO_TIM2_CH2IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN3)
#  define GPIO_TIM2_CH2OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN3)
#  define GPIO_TIM2_CH3IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN10)
#  define GPIO_TIM2_CH3OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN10)
#  define GPIO_TIM2_CH4IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN11)
#  define GPIO_TIM2_CH4OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN11)
#elif defined(CONFIG_STM32_TIM2_PARTIAL_REMAP_1)
#  define GPIO_TIM2_ETR_0      (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN15)
#  define GPIO_TIM2_CH1IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN15)
#  define GPIO_TIM2_CH1OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN15)
#  define GPIO_TIM2_CH2IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN3)
#  define GPIO_TIM2_CH2OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN3)
#  define GPIO_TIM2_CH3IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN2)
#  define GPIO_TIM2_CH3OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN2)
#  define GPIO_TIM2_CH4IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN3)
#  define GPIO_TIM2_CH4OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN3)
#elif defined(CONFIG_STM32_TIM2_PARTIAL_REMAP_2)
#  define GPIO_TIM2_ETR_0      (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN0)
#  define GPIO_TIM2_CH1IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN0)
#  define GPIO_TIM2_CH1OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN0)
#  define GPIO_TIM2_CH2IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN1)
#  define GPIO_TIM2_CH2OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN1)
#  define GPIO_TIM2_CH3IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN10)
#  define GPIO_TIM2_CH3OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN10)
#  define GPIO_TIM2_CH4IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN11)
#  define GPIO_TIM2_CH4OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN11)
#else
#  define GPIO_TIM2_ETR_0      (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN0)
#  define GPIO_TIM2_CH1IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN0)
#  define GPIO_TIM2_CH1OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN0)
#  define GPIO_TIM2_CH2IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN1)
#  define GPIO_TIM2_CH2OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN1)
#  define GPIO_TIM2_CH3IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN2)
#  define GPIO_TIM2_CH3OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN2)
#  define GPIO_TIM2_CH4IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN3)
#  define GPIO_TIM2_CH4OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN3)
#endif

#if defined(CONFIG_STM32_TIM3_FULL_REMAP)
#  define GPIO_TIM3_CH1IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN6)
#  define GPIO_TIM3_CH1OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN6)
#  define GPIO_TIM3_CH2IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN7)
#  define GPIO_TIM3_CH2OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN7)
#  define GPIO_TIM3_CH3IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN8)
#  define GPIO_TIM3_CH3OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN8)
#  define GPIO_TIM3_CH4IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN9)
#  define GPIO_TIM3_CH4OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN9)
#elif defined(CONFIG_STM32_TIM3_PARTIAL_REMAP)
#  define GPIO_TIM3_CH1IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN4)
#  define GPIO_TIM3_CH1OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN4)
#  define GPIO_TIM3_CH2IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN5)
#  define GPIO_TIM3_CH2OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN5)
#  define GPIO_TIM3_CH3IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN0)
#  define GPIO_TIM3_CH3OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN0)
#  define GPIO_TIM3_CH4IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN1)
#  define GPIO_TIM3_CH4OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN1)
#else
#  define GPIO_TIM3_CH1IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN6)
#  define GPIO_TIM3_CH1OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN6)
#  define GPIO_TIM3_CH2IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN7)
#  define GPIO_TIM3_CH2OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN7)
#  define GPIO_TIM3_CH3IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN0)
#  define GPIO_TIM3_CH3OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN0)
#  define GPIO_TIM3_CH4IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN1)
#  define GPIO_TIM3_CH4OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN1)
#endif

#if defined(CONFIG_STM32_TIM4_REMAP)
#  define GPIO_TIM4_CH1IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTD|GPIO_PIN12)
#  define GPIO_TIM4_CH1OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN12)
#  define GPIO_TIM4_CH2IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTD|GPIO_PIN13)
#  define GPIO_TIM4_CH2OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN13)
#  define GPIO_TIM4_CH3IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTD|GPIO_PIN14)
#  define GPIO_TIM4_CH3OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN14)
#  define GPIO_TIM4_CH4IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTD|GPIO_PIN15)
#  define GPIO_TIM4_CH4OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN15)
#else
#  define GPIO_TIM4_CH1IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN6)
#  define GPIO_TIM4_CH1OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN6)
#  define GPIO_TIM4_CH2IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN7)
#  define GPIO_TIM4_CH2OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN7)
#  define GPIO_TIM4_CH3IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN8)
#  define GPIO_TIM4_CH3OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN8)
#  define GPIO_TIM4_CH4IN_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN9)
#  define GPIO_TIM4_CH4OUT_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN9)
#endif

#define GPIO_TIM5_CH1IN_0      (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM5_CH1OUT_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM5_CH2IN_0      (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN1)
#define GPIO_TIM5_CH2OUT_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN1)
#define GPIO_TIM5_CH3IN_0      (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM5_CH3OUT_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM5_CH4IN_0      (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM5_CH4OUT_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN3)

#define GPIO_TIM8_ETR_0        (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM8_CH1IN_0      (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN6)
#define GPIO_TIM8_CH1OUT_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN6)
#define GPIO_TIM8_CH2IN_0      (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN7)
#define GPIO_TIM8_CH2OUT_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN7)
#define GPIO_TIM8_CH3IN_0      (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN8)
#define GPIO_TIM8_CH3OUT_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN8)
#define GPIO_TIM8_CH4IN_0      (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN9)
#define GPIO_TIM8_CH4OUT_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN9)
#define GPIO_TIM8_BKIN_0       (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM8_CH1NOUT_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM8_CH2NOUT_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN0)
#define GPIO_TIM8_CH3NOUT_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN1)

/* USART */

#if defined(CONFIG_STM32_USART1_REMAP)
#  define GPIO_USART1_TX_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN6)
#  define GPIO_USART1_RX_0  (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN7)
#else
#  define GPIO_USART1_TX_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN9)
#  define GPIO_USART1_RX_0  (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN10)
#endif

#if defined(CONFIG_STM32_USART2_REMAP)
#  define GPIO_USART2_CTS_0 (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTD|GPIO_PIN3)
#  define GPIO_USART2_RTS_0 (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN4)
#  define GPIO_USART2_TX_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN5)
#  define GPIO_USART2_RX_0  (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTD|GPIO_PIN6)
#  define GPIO_USART2_CK_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN7)
#else
#  define GPIO_USART2_CTS_0 (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN0)
#  define GPIO_USART2_RTS_0 (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN1)
#  define GPIO_USART2_TX_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN2)
#  define GPIO_USART2_RX_0  (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN3)
#  define GPIO_USART2_CK_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN4)
#endif

#if defined(CONFIG_STM32_USART3_FULL_REMAP)
#  define GPIO_USART3_TX_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN8)
#  define GPIO_USART3_RX_0  (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTD|GPIO_PIN9)
#  define GPIO_USART3_CK_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN10)
#  define GPIO_USART3_CTS_0 (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTD|GPIO_PIN11)
#  define GPIO_USART3_RTS_0 (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN12)
#elif defined(CONFIG_STM32_USART3_PARTIAL_REMAP)
#  define GPIO_USART3_TX_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN10)
#  define GPIO_USART3_RX_0  (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN11)
#  define GPIO_USART3_CK_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN12)
#  define GPIO_USART3_CTS_0 (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN13)
#  define GPIO_USART3_RTS_0 (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN14)
#else
#  define GPIO_USART3_TX_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN10)
#  define GPIO_USART3_RX_0  (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN11)
#  define GPIO_USART3_CK_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN12)
#  define GPIO_USART3_CTS_0 (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN13)
#  define GPIO_USART3_RTS_0 (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN14)
#endif

#define GPIO_UART4_RX_0     (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN11)
#define GPIO_UART4_TX_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN10)

#define GPIO_UART5_RX_0     (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTD|GPIO_PIN2)
#define GPIO_UART5_TX_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN12)

/* SPI */

#if defined(CONFIG_STM32_SPI1_REMAP)
#  define GPIO_SPI1_NSS_0   (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN15)
#  define GPIO_SPI1_SCK_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN3)
#  define GPIO_SPI1_MISO_0  (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN4)
#  define GPIO_SPI1_MOSI_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN5)
#else
#  define GPIO_SPI1_NSS_0   (GPIO_INPUT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN4)
#  define GPIO_SPI1_SCK_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN5)
#  define GPIO_SPI1_MISO_0  (GPIO_INPUT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN6)
#  define GPIO_SPI1_MOSI_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN7)
#endif

#  define GPIO_SPI2_NSS_0   (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN12)
#  define GPIO_SPI2_SCK_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN13)
#  define GPIO_SPI2_MISO_0  (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN14)
#  define GPIO_SPI2_MOSI_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN15)

#if defined(CONFIG_STM32_SPI3_REMAP)
#  define GPIO_SPI3_NSS_0   (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN4)
#  define GPIO_SPI3_SCK_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN10)
#  define GPIO_SPI3_MISO_0  (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTC|GPIO_PIN11)
#  define GPIO_SPI3_MOSI_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN12)
#else
#  define GPIO_SPI3_NSS_0   (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN15)
#  define GPIO_SPI3_SCK_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN3)
#  define GPIO_SPI3_MISO_0  (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN4)
#  define GPIO_SPI3_MOSI_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN5)
#endif

/* I2C */

#if defined(CONFIG_STM32_I2C1_REMAP)
#  define GPIO_I2C1_SCL_0   (GPIO_ALT|GPIO_CNF_AFOD|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN8)
#  define GPIO_I2C1_SDA_0   (GPIO_ALT|GPIO_CNF_AFOD|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN9)
#else
#  define GPIO_I2C1_SCL_0   (GPIO_ALT|GPIO_CNF_AFOD|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN6)
#  define GPIO_I2C1_SDA_0   (GPIO_ALT|GPIO_CNF_AFOD|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN7)
#endif

#define GPIO_I2C1_SMBA_0  (GPIO_ALT|GPIO_CNF_INFLOAT|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN5)

#define GPIO_I2C2_SCL_0   (GPIO_ALT|GPIO_CNF_AFOD|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN10)
#define GPIO_I2C2_SDA_0   (GPIO_ALT|GPIO_CNF_AFOD|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN11)
#define GPIO_I2C2_SMBA_0  (GPIO_ALT|GPIO_CNF_INFLOAT|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN12)

/* CAN */

#if defined(CONFIG_STM32_CAN1_REMAP1)
#  define GPIO_CAN1_RX_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTB|GPIO_PIN8)
#  define GPIO_CAN1_TX_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN9)
#elif defined(CONFIG_STM32_CAN1_REMAP2)
#  define GPIO_CAN1_RX_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTD|GPIO_PIN0)
#  define GPIO_CAN1_TX_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN1)
#else
#  define GPIO_CAN1_RX_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTA|GPIO_PIN11)
#  define GPIO_CAN1_TX_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTA|GPIO_PIN12)
#endif

/* FSMC: CF */

#define GPIO_CF_A0_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN0)
#define GPIO_CF_A1_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN1)
#define GPIO_CF_A2_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN2)
#define GPIO_CF_A3_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN3)
#define GPIO_CF_A4_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN4)
#define GPIO_CF_A5_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN5)
#define GPIO_CF_A6_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN12)
#define GPIO_CF_A7_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN13)
#define GPIO_CF_A8_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN14)
#define GPIO_CF_A9_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN15)
#define GPIO_CF_A10_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN0)
#define GPIO_CF_D0_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN14)
#define GPIO_CF_D1_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN15)
#define GPIO_CF_D2_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN0)
#define GPIO_CF_D3_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN1)
#define GPIO_CF_D4_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN7)
#define GPIO_CF_D5_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN8)
#define GPIO_CF_D6_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN9)
#define GPIO_CF_D7_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN10)
#define GPIO_CF_D8_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN11)
#define GPIO_CF_D9_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN12)
#define GPIO_CF_D10_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN13)
#define GPIO_CF_D11_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN14)
#define GPIO_CF_D12_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN15)
#define GPIO_CF_D13_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN8)
#define GPIO_CF_D14_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN9)
#define GPIO_CF_D15_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN10)
#define GPIO_CF_NIORD_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN6)
#define GPIO_CF_NREG_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN7)
#define GPIO_CF_NIOWR_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN8)
#define GPIO_CF_CD_0      (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTF|GPIO_PIN9)
#define GPIO_CF_INTR_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTF|GPIO_PIN10)
#define GPIO_CF_NIOS16_0  (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTF|GPIO_PIN11)
#define GPIO_CF_NOE_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN4)
#define GPIO_CF_NWE_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN5)
#define GPIO_CF_NWAIT_0   (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTD|GPIO_PIN6)
#define GPIO_CF_NCE41_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN10)
#define GPIO_CF_NCE42_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN11)

/* FSMC: CF/IDE */

#define GPIO_CFIDE_A0_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN0)
#define GPIO_CFIDE_A1_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN1)
#define GPIO_CFIDE_A2_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN2)
#define GPIO_CFIDE_D0_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN14)
#define GPIO_CFIDE_D1_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN15)
#define GPIO_CFIDE_D2_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN0)
#define GPIO_CFIDE_D3_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN1)
#define GPIO_CFIDE_D4_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN7)
#define GPIO_CFIDE_D5_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN8)
#define GPIO_CFIDE_D6_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN9)
#define GPIO_CFIDE_D7_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN10)
#define GPIO_CFIDE_D8_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN11)
#define GPIO_CFIDE_D9_0      (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN12)
#define GPIO_CFIDE_D10_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN13)
#define GPIO_CFIDE_D11_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN14)
#define GPIO_CFIDE_D12_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN15)
#define GPIO_CFIDE_D13_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN8)
#define GPIO_CFIDE_D14_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN9)
#define GPIO_CFIDE_D15_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN10)
#define GPIO_CFIDE_NIORD_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN6)
#define GPIO_CFIDE_NREG_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN7)
#define GPIO_CFIDE_NIOWR_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN8)
#define GPIO_CFIDE_CD_0      (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTF|GPIO_PIN9)
#define GPIO_CFIDE_INTR_0    (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTF|GPIO_PIN10)
#define GPIO_CFIDE_NIOS16_0  (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTF|GPIO_PIN11)
#define GPIO_CFIDE_NOE_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN4)
#define GPIO_CFIDE_NWE_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN5)
#define GPIO_CFIDE_NWAIT_0   (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTD|GPIO_PIN6)
#define GPIO_CFIDE_NCE41_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN10)
#define GPIO_CFIDE_NCE42_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN11)

/* SDIO */

#define GPIO_SDIO_D0_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN8)

#ifndef CONFIG_STM32_SDIO_WIDTH_D1_ONLY
#  define GPIO_SDIO_D1_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN9)
#  define GPIO_SDIO_D2_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN10)
#  define GPIO_SDIO_D3_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN11)
#  define GPIO_SDIO_D4_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN8)
#  define GPIO_SDIO_D5_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTB|GPIO_PIN9)
#  define GPIO_SDIO_D6_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN6)
#  define GPIO_SDIO_D7_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN7)
#endif

#define GPIO_SDIO_CK_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTC|GPIO_PIN12)
#define GPIO_SDIO_CMD_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN2)

/* FSMC: NOR/PSRAM/SRAM (NPS) */

#define GPIO_NPS_A0_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN0)
#define GPIO_NPS_A1_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN1)
#define GPIO_NPS_A2_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN2)
#define GPIO_NPS_A3_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN3)
#define GPIO_NPS_A4_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN4)
#define GPIO_NPS_A5_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN5)
#define GPIO_NPS_A6_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN12)
#define GPIO_NPS_A7_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN13)
#define GPIO_NPS_A8_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN14)
#define GPIO_NPS_A9_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTF|GPIO_PIN15)
#define GPIO_NPS_A10_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN0)
#define GPIO_NPS_A11_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN1)
#define GPIO_NPS_A12_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN2)
#define GPIO_NPS_A13_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN3)
#define GPIO_NPS_A14_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN4)
#define GPIO_NPS_A15_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN5)
#define GPIO_NPS_A16_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN11)
#define GPIO_NPS_A17_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN12)
#define GPIO_NPS_A18_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN13)
#define GPIO_NPS_A19_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN3)
#define GPIO_NPS_A20_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN4)
#define GPIO_NPS_A21_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN5)
#define GPIO_NPS_A22_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN6)
#define GPIO_NPS_A23_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN2)
#define GPIO_NPS_A24_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN13)
#define GPIO_NPS_A25_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN14)
#define GPIO_NPS_D0_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN14)
#define GPIO_NPS_D1_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN15)
#define GPIO_NPS_D2_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN0)
#define GPIO_NPS_D3_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN1)
#define GPIO_NPS_D4_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN7)
#define GPIO_NPS_D5_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN8)
#define GPIO_NPS_D6_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN9)
#define GPIO_NPS_D7_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN10)
#define GPIO_NPS_D8_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN11)
#define GPIO_NPS_D9_0     (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN12)
#define GPIO_NPS_D10_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN13)
#define GPIO_NPS_D11_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN14)
#define GPIO_NPS_D12_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN15)
#define GPIO_NPS_D13_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN8)
#define GPIO_NPS_D14_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN9)
#define GPIO_NPS_D15_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN10)
#define GPIO_NPS_CLK_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN3)
#define GPIO_NPS_NOE_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN4)
#define GPIO_NPS_NWE_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN5)
#define GPIO_NPS_NWAIT_0  (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTD|GPIO_PIN6)
#define GPIO_NPS_NE1_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN7)
#define GPIO_NPS_NE2_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN9)
#define GPIO_NPS_NE3_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN10)
#define GPIO_NPS_NE4_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN12)
#define GPIO_NPS_NBL0_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN0)
#define GPIO_NPS_NBL1_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN1)

/* FSMC: NOR/PSRAM Multiplex (NPM) */

#define GPIO_NPM_A16_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN11)
#define GPIO_NPM_A17_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN12)
#define GPIO_NPM_A18_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN13)
#define GPIO_NPM_A19_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN3)
#define GPIO_NPM_A20_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN4)
#define GPIO_NPM_A21_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN5)
#define GPIO_NPM_A22_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN6)
#define GPIO_NPM_A23_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN2)
#define GPIO_NPM_DA0_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN14)
#define GPIO_NPM_DA1_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN15)
#define GPIO_NPM_DA2_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN0)
#define GPIO_NPM_DA3_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN1)
#define GPIO_NPM_DA4_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN7)
#define GPIO_NPM_DA5_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN8)
#define GPIO_NPM_DA6_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN9)
#define GPIO_NPM_DA7_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN10)
#define GPIO_NPM_DA8_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN11)
#define GPIO_NPM_DA9_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN12)
#define GPIO_NPM_DA10_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN13)
#define GPIO_NPM_DA11_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN14)
#define GPIO_NPM_DA12_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN15)
#define GPIO_NPM_DA13_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN8)
#define GPIO_NPM_DA14_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN9)
#define GPIO_NPM_DA15_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN10)
#define GPIO_NPM_CLK_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN3)
#define GPIO_NPM_NOE_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN4)
#define GPIO_NPM_NWE_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN5)
#define GPIO_NPM_NWAIT_0  (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTD|GPIO_PIN6)
#define GPIO_NPM_NE1_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN7)
#define GPIO_NPM_NE2_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN9)
#define GPIO_NPM_NE3_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN10)
#define GPIO_NPM_NE4_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN12)
#define GPIO_NPM_A24_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN13)
#define GPIO_NPM_A25_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN14)
#define GPIO_NPM_NBL0_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN0)
#define GPIO_NPM_NBL1_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN1)

/* FSMC: NAND */

#define GPIO_NAND_D4_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN7)
#define GPIO_NAND_D5_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN8)
#define GPIO_NAND_D6_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN9)
#define GPIO_NAND_D7_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN10)
#define GPIO_NAND_D8_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN11)
#define GPIO_NAND_D9_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN12)
#define GPIO_NAND_D10_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN13)
#define GPIO_NAND_D11_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN14)
#define GPIO_NAND_D12_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTE|GPIO_PIN15)
#define GPIO_NAND_D13_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN8)
#define GPIO_NAND_D14_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN9)
#define GPIO_NAND_D15_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN10)
#define GPIO_NAND_CLE_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN11)
#define GPIO_NAND_ALE_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN12)
#define GPIO_NAND_D0_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN14)
#define GPIO_NAND_D1_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN15)
#define GPIO_NAND_INT2_0  (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTG|GPIO_PIN6)
#define GPIO_NAND_INT3_0  (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTG|GPIO_PIN7)
#define GPIO_NAND_D2_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN0)
#define GPIO_NAND_D3_0    (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN1)
#define GPIO_NAND_NOE_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN4)
#define GPIO_NAND_NWE_0   (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN5)
#define GPIO_NAND_NWAIT_0 (GPIO_INPUT|GPIO_CNF_INFLOAT|GPIO_MODE_INPUT|GPIO_PORTD|GPIO_PIN6)
#define GPIO_NAND_NCE2_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTD|GPIO_PIN7)
#define GPIO_NAND_NCE3_0  (GPIO_ALT|GPIO_CNF_AFPP|GPIO_MODE_2MHz|GPIO_PORTG|GPIO_PIN9)

#endif /* __ARCH_ARM_SRC_STM32_HARDWARE_STM32F103Z_PINMAP_H */
