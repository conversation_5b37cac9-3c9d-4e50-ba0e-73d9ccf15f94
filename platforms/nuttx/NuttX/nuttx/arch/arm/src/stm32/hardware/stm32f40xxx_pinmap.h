/****************************************************************************
 * arch/arm/src/stm32/hardware/stm32f40xxx_pinmap.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#ifndef __ARCH_ARM_SRC_STM32_HARDWARE_STM32F40XXX_PINMAP_H
#define __ARCH_ARM_SRC_STM32_HARDWARE_STM32F40XXX_PINMAP_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <nuttx/config.h>

#include "stm32_gpio.h"

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Alternate Pin Functions.
 * All members of the STM32F40xxx family share the same pin multiplexing
 * (although they may differ in the pins physically available).
 *
 * Alternative pin selections are provided with a numeric suffix like _1, _2,
 * etc. Drivers, however, will use the pin selection without the numeric
 * suffix. Additional definitions are required in the board.h file.  For
 * example, if CAN1_RX connects via PA11 on some board, then the following
 * definitions should appear in the board.h header file for that board:
 *
 * #define GPIO_CAN1_RX GPIO_CAN1_RX_1
 *
 * The driver will then automatically configure PA11 as the CAN1 RX pin.
 */

/* WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific GPIO options such as
 * frequency, open-drain/push-pull, and pull-up/down!
 *  Just the basics are defined for most pins in this file.
 */

/* ADC */

#define GPIO_ADC1_IN0_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN0)
#define GPIO_ADC1_IN1_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN1)
#define GPIO_ADC1_IN2_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN2)
#define GPIO_ADC1_IN3_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN3)
#define GPIO_ADC1_IN4_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN4)
#define GPIO_ADC1_IN5_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN5)
#define GPIO_ADC1_IN6_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN6)
#define GPIO_ADC1_IN7_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN7)
#define GPIO_ADC1_IN8_0       (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN0)
#define GPIO_ADC1_IN9_0       (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN1)
#define GPIO_ADC1_IN10_0      (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN0)
#define GPIO_ADC1_IN11_0      (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN1)
#define GPIO_ADC1_IN12_0      (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN2)
#define GPIO_ADC1_IN13_0      (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN3)
#define GPIO_ADC1_IN14_0      (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN4)
#define GPIO_ADC1_IN15_0      (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN5)

#define GPIO_ADC2_IN0_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN0)
#define GPIO_ADC2_IN1_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN1)
#define GPIO_ADC2_IN2_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN2)
#define GPIO_ADC2_IN3_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN3)
#define GPIO_ADC2_IN4_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN4)
#define GPIO_ADC2_IN5_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN5)
#define GPIO_ADC2_IN6_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN6)
#define GPIO_ADC2_IN7_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN7)
#define GPIO_ADC2_IN8_0       (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN0)
#define GPIO_ADC2_IN9_0       (GPIO_ANALOG|GPIO_PORTB|GPIO_PIN1)
#define GPIO_ADC2_IN10_0      (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN0)
#define GPIO_ADC2_IN11_0      (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN1)
#define GPIO_ADC2_IN12_0      (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN2)
#define GPIO_ADC2_IN13_0      (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN3)
#define GPIO_ADC2_IN14_0      (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN4)
#define GPIO_ADC2_IN15_0      (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN5)

#define GPIO_ADC3_IN0_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN0)
#define GPIO_ADC3_IN1_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN1)
#define GPIO_ADC3_IN2_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN2)
#define GPIO_ADC3_IN3_0       (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN3)
#define GPIO_ADC3_IN4_0       (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN6)
#define GPIO_ADC3_IN5_0       (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN7)
#define GPIO_ADC3_IN6_0       (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN8)
#define GPIO_ADC3_IN7_0       (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN9)
#define GPIO_ADC3_IN8_0       (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN10)
#define GPIO_ADC3_IN9_0       (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN3)
#define GPIO_ADC3_IN10_0      (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN0)
#define GPIO_ADC3_IN11_0      (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN1)
#define GPIO_ADC3_IN12_0      (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN2)
#define GPIO_ADC3_IN13_0      (GPIO_ANALOG|GPIO_PORTC|GPIO_PIN3)
#define GPIO_ADC3_IN14_0      (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN4)
#define GPIO_ADC3_IN15_0      (GPIO_ANALOG|GPIO_PORTF|GPIO_PIN5)

/* CAN */

#define GPIO_CAN1_RX_1        (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN11)
#define GPIO_CAN1_RX_2        (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN8)
#define GPIO_CAN1_RX_3        (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN0)
#define GPIO_CAN1_RX_4        (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN9)
#define GPIO_CAN1_TX_1        (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN12)
#define GPIO_CAN1_TX_2        (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN9)
#define GPIO_CAN1_TX_3        (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN1)
#define GPIO_CAN1_TX_4        (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN13)
#define GPIO_CAN1_TX_5        (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN1)

#define GPIO_CAN2_RX_1        (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN12)
#define GPIO_CAN2_RX_2        (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN5)
#define GPIO_CAN2_TX_1        (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN13)
#define GPIO_CAN2_TX_2        (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN6)

/* DAC - "Once the DAC channelx is enabled, the corresponding GPIO pin
 * (PA4 or PA5) is automatically connected to the analog converter output
 * (DAC_OUTx). In order to avoid parasitic consumption, the PA4 or PA5 pin
 * should first be configured to analog (AIN)".
 */

#define GPIO_DAC1_OUT1_0      (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN4)
#define GPIO_DAC1_OUT2_0      (GPIO_ANALOG|GPIO_PORTA|GPIO_PIN5)

/* Digital Camera Interface (DCMI) */

#define GPIO_DCMI_D0_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN9)
#define GPIO_DCMI_D0_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN6)
#define GPIO_DCMI_D0_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTH|GPIO_PIN9)
#define GPIO_DCMI_D1_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN10)
#define GPIO_DCMI_D1_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN7)
#define GPIO_DCMI_D1_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTH|GPIO_PIN10)
#define GPIO_DCMI_D2_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN8)
#define GPIO_DCMI_D2_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTE|GPIO_PIN0)
#define GPIO_DCMI_D2_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTH|GPIO_PIN11)
#define GPIO_DCMI_D3_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN9)
#define GPIO_DCMI_D3_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTE|GPIO_PIN1)
#define GPIO_DCMI_D3_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTH|GPIO_PIN12)
#define GPIO_DCMI_D4_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN11)
#define GPIO_DCMI_D4_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTE|GPIO_PIN4)
#define GPIO_DCMI_D4_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTH|GPIO_PIN14)
#define GPIO_DCMI_D5_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN6)
#define GPIO_DCMI_D5_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTI|GPIO_PIN4)
#define GPIO_DCMI_D6_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN8)
#define GPIO_DCMI_D6_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTE|GPIO_PIN5)
#define GPIO_DCMI_D6_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTI|GPIO_PIN6)
#define GPIO_DCMI_D7_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN9)
#define GPIO_DCMI_D7_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTE|GPIO_PIN6)
#define GPIO_DCMI_D7_3        (GPIO_ALT|GPIO_AF13|GPIO_PORTI|GPIO_PIN7)
#define GPIO_DCMI_D8_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN10)
#define GPIO_DCMI_D8_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTI|GPIO_PIN1)
#define GPIO_DCMI_D9_1        (GPIO_ALT|GPIO_AF13|GPIO_PORTC|GPIO_PIN12)
#define GPIO_DCMI_D9_2        (GPIO_ALT|GPIO_AF13|GPIO_PORTI|GPIO_PIN2)
#define GPIO_DCMI_D10_1       (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN5)
#define GPIO_DCMI_D10_2       (GPIO_ALT|GPIO_AF13|GPIO_PORTI|GPIO_PIN3)
#define GPIO_DCMI_D11_1       (GPIO_ALT|GPIO_AF13|GPIO_PORTD|GPIO_PIN2)
#define GPIO_DCMI_D11_2       (GPIO_ALT|GPIO_AF13|GPIO_PORTH|GPIO_PIN15)
#define GPIO_DCMI_D13_1       (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN15)
#define GPIO_DCMI_D13_2       (GPIO_ALT|GPIO_AF13|GPIO_PORTI|GPIO_PIN0)
#define GPIO_DCMI_HSYNC_1     (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN4)
#define GPIO_DCMI_HSYNC_2     (GPIO_ALT|GPIO_AF13|GPIO_PORTH|GPIO_PIN8)
#define GPIO_DCMI_PIXCLK_0    (GPIO_ALT|GPIO_AF13|GPIO_PORTA|GPIO_PIN6)
#define GPIO_DCMI_VSYNC_1     (GPIO_ALT|GPIO_AF13|GPIO_PORTB|GPIO_PIN7)
#define GPIO_DCMI_VSYNC_2     (GPIO_ALT|GPIO_AF13|GPIO_PORTI|GPIO_PIN5)

#if defined(CONFIG_STM32_STM32F427) || defined(CONFIG_STM32_STM32F429) || \
    defined(CONFIG_STM32_STM32F469)
#  define GPIO_DCMI_D2_4      (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN10)
#  define GPIO_DCMI_D3_4      (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN11)
#  define GPIO_DCMI_D5_3      (GPIO_ALT|GPIO_AF13|GPIO_PORTD|GPIO_PIN3)
#  define GPIO_DCMI_D8_3      (GPIO_ALT|GPIO_AF13|GPIO_PORTH|GPIO_PIN6)
#  define GPIO_DCMI_D9_3      (GPIO_ALT|GPIO_AF13|GPIO_PORTH|GPIO_PIN7)
#  define GPIO_DCMI_D10_3     (GPIO_ALT|GPIO_AF13|GPIO_PORTD|GPIO_PIN6)
#  define GPIO_DCMI_D11_3     (GPIO_ALT|GPIO_AF13|GPIO_PORTF|GPIO_PIN10)
#  define GPIO_DCMI_D12_1     (GPIO_ALT|GPIO_AF13|GPIO_PORTF|GPIO_PIN11)
#  define GPIO_DCMI_D12_2     (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN6)
#  define GPIO_DCMI_D13_3     (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN7)
#  define GPIO_DCMI_VSYNC_3   (GPIO_ALT|GPIO_AF13|GPIO_PORTG|GPIO_PIN9)
#else
#  define GPIO_DCMI_D12_0     (GPIO_ALT|GPIO_AF13|GPIO_PORTF|GPIO_PIN11)
#endif

/* Clocks outputs */

#define GPIO_MCO1_0           (GPIO_ALT|GPIO_AF0|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN8)
#define GPIO_MCO2_0           (GPIO_ALT|GPIO_AF0|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN9)

/* Ethernet MAC */

#if defined(CONFIG_STM32_STM32F401) || defined(CONFIG_STM32_STM32F411) || \
    defined(CONFIG_STM32_STM32F405) || defined(CONFIG_STM32_STM32F407) || \
    defined(CONFIG_STM32_STM32F427) || defined(CONFIG_STM32_STM32F429) || \
    defined(CONFIG_STM32_STM32F469)
#  define GPIO_ETH_MDC_0          (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN1)
#  define GPIO_ETH_MDIO_0         (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN2)
#  define GPIO_ETH_MII_COL_1      (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN3)
#  define GPIO_ETH_MII_COL_2      (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN3)
#  define GPIO_ETH_MII_CRS_1      (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN0)
#  define GPIO_ETH_MII_CRS_2      (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN2)
#  define GPIO_ETH_MII_RXD0_0     (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN4)
#  define GPIO_ETH_MII_RXD1_0     (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN5)
#  define GPIO_ETH_MII_RXD2_1     (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN0)
#  define GPIO_ETH_MII_RXD2_2     (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN6)
#  define GPIO_ETH_MII_RXD3_1     (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN1)
#  define GPIO_ETH_MII_RXD3_2     (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN7)
#  define GPIO_ETH_MII_RX_CLK_0   (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN1)
#  define GPIO_ETH_MII_RX_DV_0    (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN7)
#  define GPIO_ETH_MII_RX_ER_1    (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN10)
#  define GPIO_ETH_MII_RX_ER_2    (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN10)
#  define GPIO_ETH_MII_TXD0_1     (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN12)
#  define GPIO_ETH_MII_TXD0_2     (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN13)
#  define GPIO_ETH_MII_TXD1_1     (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN13)
#  define GPIO_ETH_MII_TXD1_2     (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN14)
#  define GPIO_ETH_MII_TXD2_0     (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN2)
#  define GPIO_ETH_MII_TXD3_1     (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN8)
#  define GPIO_ETH_MII_TXD3_2     (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN2)
#  define GPIO_ETH_MII_TX_CLK_0   (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN3)
#  define GPIO_ETH_MII_TX_EN_1    (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN11)
#  define GPIO_ETH_MII_TX_EN_2    (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN11)
#  define GPIO_ETH_PPS_OUT_1      (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN5)
#  define GPIO_ETH_PPS_OUT_2      (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN8)
#  define GPIO_ETH_RMII_CRS_DV_0  (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN7)
#  define GPIO_ETH_RMII_REF_CLK_0 (GPIO_ALT|GPIO_AF11|GPIO_PORTA|GPIO_PIN1)
#  define GPIO_ETH_RMII_RXD0_0    (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN4)
#  define GPIO_ETH_RMII_RXD1_0    (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN5)
#  define GPIO_ETH_RMII_TXD0_1    (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN12)
#  define GPIO_ETH_RMII_TXD0_2    (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN13)
#  define GPIO_ETH_RMII_TXD1_1    (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN13)
#  define GPIO_ETH_RMII_TXD1_2    (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN14)
#  define GPIO_ETH_RMII_TX_EN_1   (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN11)
#  define GPIO_ETH_RMII_TX_EN_2   (GPIO_ALT|GPIO_AF11|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN11)
#endif

/* Flexible Memory Controller (FMC) */

#define GPIO_FMC_A0_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN0)
#define GPIO_FMC_A1_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN1)
#define GPIO_FMC_A2_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN2)
#define GPIO_FMC_A3_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN3)
#define GPIO_FMC_A4_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN4)
#define GPIO_FMC_A5_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN5)
#define GPIO_FMC_A6_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN12)
#define GPIO_FMC_A7_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN13)
#define GPIO_FMC_A8_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN14)
#define GPIO_FMC_A9_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN15)
#define GPIO_FMC_A10_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN0)
#define GPIO_FMC_A11_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN1)
#define GPIO_FMC_A12_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN2)
#define GPIO_FMC_A13_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN3)
#define GPIO_FMC_A14_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN4)
#define GPIO_FMC_A15_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN5)
#define GPIO_FMC_A16_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN11)
#define GPIO_FMC_A17_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN12)
#define GPIO_FMC_A18_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN13)
#define GPIO_FMC_A19_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN3)
#define GPIO_FMC_A20_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN4)
#define GPIO_FMC_A21_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN5)
#define GPIO_FMC_A22_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN6)
#define GPIO_FMC_A23_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN2)
#define GPIO_FMC_A24_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN13)
#define GPIO_FMC_A25_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN14)
#define GPIO_FMC_NBL1_0      (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN1)
#define GPIO_FMC_CLK_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN3)
#define GPIO_FMC_D0_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN14)
#define GPIO_FMC_D1_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN15)
#define GPIO_FMC_D2_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN0)
#define GPIO_FMC_D3_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN1)
#define GPIO_FMC_D4_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN7)
#define GPIO_FMC_D5_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN8)
#define GPIO_FMC_D6_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN9)
#define GPIO_FMC_D7_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN10)
#define GPIO_FMC_D8_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN11)
#define GPIO_FMC_D9_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN12)
#define GPIO_FMC_D10_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN13)
#define GPIO_FMC_D11_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN14)
#define GPIO_FMC_D12_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN15)
#define GPIO_FMC_D13_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN8)
#define GPIO_FMC_D14_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN9)
#define GPIO_FMC_D15_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN10)
#define GPIO_FMC_NBL0_0      (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN0)
#define GPIO_FMC_NE1_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN7)
#define GPIO_FMC_NE2_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN9)
#define GPIO_FMC_NE3_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN10)
#define GPIO_FMC_NE4_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN12)
#define GPIO_FMC_NL_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN7)
#define GPIO_FMC_NOE_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN4)
#define GPIO_FMC_NWAIT_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN6)
#define GPIO_FMC_NWE_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN5)

#define GPIO_FMC_INT3_0      (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN7)
#define GPIO_FMC_NCE3_0      (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN9)

#if defined(CONFIG_STM32_STM32F427) || defined(CONFIG_STM32_STM32F429)
#  define GPIO_FMC_CD_0      (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN9)
#  define GPIO_FMC_INT2_0    (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN6)
#  define GPIO_FMC_INTR_0    (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN10)
#  define GPIO_FMC_NCE2_0    (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN7)
#  define GPIO_FMC_NCE4_1    (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN10)
#  define GPIO_FMC_NCE4_2    (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN11)
#  define GPIO_FMC_NIORD_0   (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN6)
#  define GPIO_FMC_NIOWR_0   (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN8)
#  define GPIO_FMC_SDCKE0_1  (GPIO_ALT|GPIO_AF12|GPIO_PORTH|GPIO_PIN2)
#  define GPIO_FMC_SDCKE0_2  (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN3)
#  define GPIO_FMC_SDNE0_1   (GPIO_ALT|GPIO_AF12|GPIO_PORTH|GPIO_PIN3)
#  define GPIO_FMC_SDNE0_2   (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN2)
#  define GPIO_FMC_SDNWE_1   (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN0)
#  define GPIO_FMC_SDNWE_2   (GPIO_ALT|GPIO_AF12|GPIO_PORTH|GPIO_PIN5)
#  define GPIO_FMC_SDNRAS_0  (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN11)
#  define GPIO_FMC_SDCLK_0   (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN8)
#  define GPIO_FMC_SDNCAS_0  (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN15)
#  define GPIO_FMC_BA0_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN4)
#  define GPIO_FMC_BA1_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN5)
#  define GPIO_FMC_NREG_0    (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN7)
#endif

#if defined(CONFIG_STM32_STM32F446) || defined(CONFIG_STM32_STM32F469)
#  define GPIO_FMC_SDCKE0_1  (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN3)
#  define GPIO_FMC_SDCKE0_2  (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN5)
#  define GPIO_FMC_SDNE0_1   (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN2)
#  define GPIO_FMC_SDNE0_2   (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN4)
#  define GPIO_FMC_SDNWE_1   (GPIO_ALT|GPIO_AF12|GPIO_PORTA|GPIO_PIN7)
#  define GPIO_FMC_SDNWE_2   (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN0)
#  define GPIO_FMC_SDNRAS_0  (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN11)
#  define GPIO_FMC_SDCLK_0   (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN8)
#  define GPIO_FMC_SDNCAS_0  (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN15)
#  define GPIO_FMC_BA0_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN4)
#  define GPIO_FMC_BA1_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN5)
#endif

#if defined(CONFIG_STM32_STM32F427) || defined(CONFIG_STM32_STM32F429) || \
    defined(CONFIG_STM32_STM32F469)
#  define GPIO_FMC_D16_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTH|GPIO_PIN8)
#  define GPIO_FMC_D17_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTH|GPIO_PIN9)
#  define GPIO_FMC_D18_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTH|GPIO_PIN10)
#  define GPIO_FMC_D19_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTH|GPIO_PIN11)
#  define GPIO_FMC_D20_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTH|GPIO_PIN12)
#  define GPIO_FMC_D21_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTH|GPIO_PIN13)
#  define GPIO_FMC_D22_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTH|GPIO_PIN14)
#  define GPIO_FMC_D23_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTH|GPIO_PIN15)
#  define GPIO_FMC_D24_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTI|GPIO_PIN0)
#  define GPIO_FMC_D25_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTI|GPIO_PIN1)
#  define GPIO_FMC_D26_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTI|GPIO_PIN2)
#  define GPIO_FMC_D27_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTI|GPIO_PIN3)
#  define GPIO_FMC_D28_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTI|GPIO_PIN6)
#  define GPIO_FMC_D29_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTI|GPIO_PIN7)
#  define GPIO_FMC_D30_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTI|GPIO_PIN9)
#  define GPIO_FMC_D31_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTI|GPIO_PIN10)
#  define GPIO_FMC_NBL2_0    (GPIO_ALT|GPIO_AF12|GPIO_PORTI|GPIO_PIN4)
#  define GPIO_FMC_NBL3_0    (GPIO_ALT|GPIO_AF12|GPIO_PORTI|GPIO_PIN5)
#  define GPIO_FMC_SDCKE0_3  (GPIO_ALT|GPIO_AF12|GPIO_PORTH|GPIO_PIN2)
#  define GPIO_FMC_SDCKE1_1  (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN5)
#  define GPIO_FMC_SDCKE1_2  (GPIO_ALT|GPIO_AF12|GPIO_PORTH|GPIO_PIN7)
#  define GPIO_FMC_SDNE0_3   (GPIO_ALT|GPIO_AF12|GPIO_PORTH|GPIO_PIN3)
#  define GPIO_FMC_SDNE1_1   (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN6)
#  define GPIO_FMC_SDNE1_2   (GPIO_ALT|GPIO_AF12|GPIO_PORTH|GPIO_PIN6)
#  define GPIO_FMC_SDNWE_3   (GPIO_ALT|GPIO_AF12|GPIO_PORTH|GPIO_PIN5)
#endif

#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_FMC_SDCKE1_0  (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN5)
#  define GPIO_FMC_SDNE1_0   (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN6)
#endif

#if defined(CONFIG_STM32_STM32F469)
#  define GPIO_FMC_CLE_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN11)
#  define GPIO_FMC_ALE_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN12)
#endif

/* Flexible Static Memory Controller (FSMC) */

#define GPIO_FSMC_A0_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN0)
#define GPIO_FSMC_A1_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN1)
#define GPIO_FSMC_A2_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN2)
#define GPIO_FSMC_A3_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN3)
#define GPIO_FSMC_A4_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN4)
#define GPIO_FSMC_A5_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN5)
#define GPIO_FSMC_A6_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN12)
#define GPIO_FSMC_A7_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN13)
#define GPIO_FSMC_A8_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN14)
#define GPIO_FSMC_A9_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN15)
#define GPIO_FSMC_A10_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN0)
#define GPIO_FSMC_A11_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN1)
#define GPIO_FSMC_A12_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN2)
#define GPIO_FSMC_A13_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN3)
#define GPIO_FSMC_A14_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN4)
#define GPIO_FSMC_A15_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN5)
#define GPIO_FSMC_A16_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN11)
#define GPIO_FSMC_A17_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN12)
#define GPIO_FSMC_A18_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN13)
#define GPIO_FSMC_A19_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN3)
#define GPIO_FSMC_A20_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN4)
#define GPIO_FSMC_A21_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN5)
#define GPIO_FSMC_A22_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN6)
#define GPIO_FSMC_A23_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN2)
#define GPIO_FSMC_A24_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN13)
#define GPIO_FSMC_A25_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN14)
#define GPIO_FSMC_NBL1_0      (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN1)
#define GPIO_FSMC_CLK_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN3)
#define GPIO_FSMC_D0_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN14)
#define GPIO_FSMC_D1_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN15)
#define GPIO_FSMC_D2_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN0)
#define GPIO_FSMC_D3_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN1)
#define GPIO_FSMC_D4_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN7)
#define GPIO_FSMC_D5_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN8)
#define GPIO_FSMC_D6_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN9)
#define GPIO_FSMC_D7_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN10)
#define GPIO_FSMC_D8_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN11)
#define GPIO_FSMC_D9_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN12)
#define GPIO_FSMC_D10_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN13)
#define GPIO_FSMC_D11_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN14)
#define GPIO_FSMC_D12_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN15)
#define GPIO_FSMC_D13_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN8)
#define GPIO_FSMC_D14_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN9)
#define GPIO_FSMC_D15_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN10)
#define GPIO_FSMC_NBL0_0      (GPIO_ALT|GPIO_AF12|GPIO_PORTE|GPIO_PIN0)
#define GPIO_FSMC_NE1_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN7)
#define GPIO_FSMC_NE2_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN9)
#define GPIO_FSMC_NE3_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN10)
#define GPIO_FSMC_NE4_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN12)
#define GPIO_FSMC_NL_0        (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN7)
#define GPIO_FSMC_NOE_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN4)
#define GPIO_FSMC_NWAIT_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN6)
#define GPIO_FSMC_NWE_0       (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN5)

#define GPIO_FSMC_INT3_0      (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN7)
#define GPIO_FSMC_NCE3_0      (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN9)

#if defined(CONFIG_STM32_STM32F401) || defined(CONFIG_STM32_STM32F411) || \
    defined(CONFIG_STM32_STM32F405) || defined(CONFIG_STM32_STM32F407)
#  define GPIO_FSMC_CD_0      (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN9)
#  define GPIO_FSMC_INT2_0    (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN6)
#  define GPIO_FSMC_INTR_0    (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN10)
#  define GPIO_FSMC_NCE2_0    (GPIO_ALT|GPIO_AF12|GPIO_PORTD|GPIO_PIN7)
#  define GPIO_FSMC_NCE4_1    (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN10)
#  define GPIO_FSMC_NCE4_2    (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN11)
#  define GPIO_FSMC_NIORD_0   (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN6)
#  define GPIO_FSMC_NIOWR_0   (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN8)
#  define GPIO_FSMC_SDCKE0_1  (GPIO_ALT|GPIO_AF12|GPIO_PORTH|GPIO_PIN2)
#  define GPIO_FSMC_SDCKE0_2  (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN3)
#  define GPIO_FSMC_SDNE0_1   (GPIO_ALT|GPIO_AF12|GPIO_PORTH|GPIO_PIN3)
#  define GPIO_FSMC_SDNE0_2   (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN2)
#  define GPIO_FSMC_SDNWE_1   (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN0)
#  define GPIO_FSMC_SDNWE_2   (GPIO_ALT|GPIO_AF12|GPIO_PORTH|GPIO_PIN5)
#  define GPIO_FSMC_SDNRAS_0  (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN11)
#  define GPIO_FSMC_SDCLK_0   (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN8)
#  define GPIO_FSMC_SDNCAS_0  (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN15)
#  define GPIO_FSMC_BA0_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN4)
#  define GPIO_FSMC_BA1_0     (GPIO_ALT|GPIO_AF12|GPIO_PORTG|GPIO_PIN5)
#  define GPIO_FSMC_NREG_0    (GPIO_ALT|GPIO_AF12|GPIO_PORTF|GPIO_PIN7)
#endif

/* I2C */

#define GPIO_I2C1_SCL_1       (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN6)
#define GPIO_I2C1_SCL_2       (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN8)
#define GPIO_I2C1_SDA_1       (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN7)
#define GPIO_I2C1_SDA_2       (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN9)
#define GPIO_I2C1_SMBA_0      (GPIO_ALT|GPIO_AF4|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN5)

#define GPIO_I2C2_SCL_1       (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN10)
#define GPIO_I2C2_SCL_2       (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTF|GPIO_PIN1)
#define GPIO_I2C2_SCL_3       (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTH|GPIO_PIN4)
#define GPIO_I2C2_SDA_1       (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN11)
#define GPIO_I2C2_SDA_2       (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTF|GPIO_PIN0)
#define GPIO_I2C2_SDA_3       (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTH|GPIO_PIN5)
#define GPIO_I2C2_SMBA_1      (GPIO_ALT|GPIO_AF4|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN12)
#define GPIO_I2C2_SMBA_2      (GPIO_ALT|GPIO_AF4|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN2)
#define GPIO_I2C2_SMBA_3      (GPIO_ALT|GPIO_AF4|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN6)
#if defined(CONFIG_STM32_STM32F411)
#  define GPIO_I2C2_SDA_4     (GPIO_ALT|GPIO_AF9|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN3)
#  define GPIO_I2C2_SDA_5     (GPIO_ALT|GPIO_AF9|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN9)
#endif
#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_I2C2_SDA_4     (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN3)
#  define GPIO_I2C2_SDA_5     (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTC|GPIO_PIN12)
#endif

#define GPIO_I2C3_SCL_1       (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTA|GPIO_PIN8)
#define GPIO_I2C3_SCL_2       (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTH|GPIO_PIN7)
#define GPIO_I2C3_SDA_1       (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTC|GPIO_PIN9)
#define GPIO_I2C3_SDA_2       (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTH|GPIO_PIN8)
#if defined(CONFIG_STM32_STM32F411)
#  define GPIO_I2C3_SDA_3     (GPIO_ALT|GPIO_AF9|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN4)
#  define GPIO_I2C3_SDA_4     (GPIO_ALT|GPIO_AF9|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN8)
#endif
#define GPIO_I2C3_SMBA_1      (GPIO_ALT|GPIO_AF4|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN9)
#define GPIO_I2C3_SMBA_2      (GPIO_ALT|GPIO_AF4|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN9)
#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_I2C3_SDA_3     (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN4)
#endif

/* I2S */

#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_I2S1_CK_1      (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN5)
#  define GPIO_I2S1_CK_2      (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN3)
#  define GPIO_I2S1_MCK_1     (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN6)
#  define GPIO_I2S1_MCK_2     (GPIO_ALT|GPIO_AF5|GPIO_PORTC|GPIO_PIN4)
#  define GPIO_I2S1_SD_1      (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN7)
#  define GPIO_I2S1_SD_2      (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN5)
#  define GPIO_I2S1_WS_1      (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN4)
#  define GPIO_I2S1_WS_2      (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN15)
#endif

#define GPIO_I2S2_CK_1        (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN10)
#define GPIO_I2S2_CK_2        (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN13)
#define GPIO_I2S2_CK_3        (GPIO_ALT|GPIO_AF5|GPIO_PORTI|GPIO_PIN1)
#define GPIO_I2S2_SD_1        (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN15)
#define GPIO_I2S2_SD_2        (GPIO_ALT|GPIO_AF5|GPIO_PORTC|GPIO_PIN3)
#define GPIO_I2S2_SD_3        (GPIO_ALT|GPIO_AF5|GPIO_PORTI|GPIO_PIN3)
#define GPIO_I2S2_WS_1        (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN12)
#define GPIO_I2S2_WS_2        (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN9)
#define GPIO_I2S2_WS_3        (GPIO_ALT|GPIO_AF5|GPIO_PORTI|GPIO_PIN0)
#if defined(CONFIG_STM32_STM32F427) || defined(CONFIG_STM32_STM32F429) || \
    defined(CONFIG_STM32_STM32F446) || defined(CONFIG_STM32_STM32F469)
#  define GPIO_I2S2_CK_4      (GPIO_ALT|GPIO_AF5|GPIO_PORTD|GPIO_PIN3)
#  define GPIO_I2S2_SD_4      (GPIO_ALT|GPIO_AF5|GPIO_PORTC|GPIO_PIN1)
#endif
#if defined(CONFIG_STM32_STM32F446) || defined(CONFIG_STM32_STM32F469)
#  define GPIO_I2S2_CK_5      (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN9)
#endif
#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_I2S2_CK_6      (GPIO_ALT|GPIO_AF5|GPIO_PORTC|GPIO_PIN7)
#  define GPIO_I2S2_MCK_1     (GPIO_ALT|GPIO_AF5|GPIO_PORTC|GPIO_PIN6)
#  define GPIO_I2S2_MCK_2     (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN6)
#  define GPIO_I2S2_WS_7      (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN4)
#  define GPIO_I2S2_WS_8      (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN1)
#else
#  define GPIO_I2S2_MCK_0     (GPIO_ALT|GPIO_AF5|GPIO_PORTC|GPIO_PIN6)
#endif

#define GPIO_I2S2EXT_SD_1     (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN14)
#define GPIO_I2S2EXT_SD_2     (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN2)
#define GPIO_I2S2EXT_SD_3     (GPIO_ALT|GPIO_AF6|GPIO_PORTI|GPIO_PIN2)

#define GPIO_I2S3_CK_1        (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN3)
#define GPIO_I2S3_CK_2        (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN10)
#define GPIO_I2S3_MCK_0       (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN7)
#define GPIO_I2S3_SD_1        (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN5)
#define GPIO_I2S3_SD_2        (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN12)
#define GPIO_I2S3_WS_1        (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN4)
#define GPIO_I2S3_WS_2        (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN15)
#if defined(CONFIG_STM32_STM32F427) || defined(CONFIG_STM32_STM32F429) || \
    defined(CONFIG_STM32_STM32F446) || defined(CONFIG_STM32_STM32F469)
#  define GPIO_I2S3_SD_3      (GPIO_ALT|GPIO_AF6|GPIO_PORTD|GPIO_PIN6)
#endif
#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_I2S3_SD_4      (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN0)
#  define GPIO_I2S3_SD_5      (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN2)
#  define GPIO_I2S3_SD_6      (GPIO_ALT|GPIO_AF5|GPIO_PORTC|GPIO_PIN1)
#  define GPIO_I2S3_SD_7      (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN1)
#  define GPIO_I2S3_SD_8      (GPIO_ALT|GPIO_AF6|GPIO_PORTD|GPIO_PIN0)
#  define GPIO_I2S3_SD_9      (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN5)
#  define GPIO_I2S3_SD_10     (GPIO_ALT|GPIO_AF5|GPIO_PORTD|GPIO_PIN6)
#else
#  define GPIO_I2S3EXT_SD_2   (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN4)
#endif

#define GPIO_I2S3EXT_SD_1     (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN11)

#define GPIO_I2S_CKIN_0       (GPIO_ALT|GPIO_AF5|GPIO_PORTC|GPIO_PIN9)

/* JTAG */

#define GPIO_JTCK_SWCLK_0     (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN14)
#define GPIO_JTDI_0           (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN15)
#define GPIO_JTDO_0           (GPIO_ALT|GPIO_AF0|GPIO_PORTB|GPIO_PIN3)
#define GPIO_JTMS_SWDIO_0     (GPIO_ALT|GPIO_AF0|GPIO_PORTA|GPIO_PIN13)
#define GPIO_JTRST_0          (GPIO_ALT|GPIO_AF0|GPIO_PORTB|GPIO_PIN4)

/* OTG FS/HS (VBUS PA9 is not an alternate configuration) */

#define GPIO_OTGFS_DM_0       (GPIO_ALT|GPIO_FLOAT|GPIO_AF10|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN11)
#define GPIO_OTGFS_DP_0       (GPIO_ALT|GPIO_FLOAT|GPIO_AF10|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN12)
#define GPIO_OTGFS_ID_0       (GPIO_ALT|GPIO_PULLUP|GPIO_AF10|GPIO_OPENDRAIN|GPIO_PORTA|GPIO_PIN10)
#define GPIO_OTGFS_SOF_0      (GPIO_ALT|GPIO_FLOAT|GPIO_AF10|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN8)

#define GPIO_OTGHSFS_DM_0     (GPIO_ALT|GPIO_FLOAT|GPIO_AF12|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN14)
#define GPIO_OTGHSFS_DP_0     (GPIO_ALT|GPIO_FLOAT|GPIO_AF12|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN15)
#define GPIO_OTGHSFS_ID_0     (GPIO_ALT|GPIO_PULLUP|GPIO_AF12|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN12)

#define GPIO_OTGHS_DM_0       (GPIO_ALT|GPIO_FLOAT|GPIO_AF12|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN14)
#define GPIO_OTGHS_DP_0       (GPIO_ALT|GPIO_FLOAT|GPIO_AF12|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN15)
#define GPIO_OTGHS_ID_0       (GPIO_ALT|GPIO_PULLUP|GPIO_AF12|GPIO_OPENDRAIN|GPIO_PORTB|GPIO_PIN12)
#define GPIO_OTGHS_SOF_0      (GPIO_ALT|GPIO_FLOAT|GPIO_AF12|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN4)

#define GPIO_OTGHS_ULPI_CK_0  (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN5)
#define GPIO_OTGHS_ULPI_D0_0  (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN3)
#define GPIO_OTGHS_ULPI_D1_0  (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN0)
#define GPIO_OTGHS_ULPI_D2_0  (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN1)
#define GPIO_OTGHS_ULPI_D3_0  (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN10)
#define GPIO_OTGHS_ULPI_D5_0  (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN12)
#define GPIO_OTGHS_ULPI_D6_0  (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN13)
#define GPIO_OTGHS_ULPI_D7_0  (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN5)
#define GPIO_OTGHS_ULPI_DIR_1 (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN2)
#define GPIO_OTGHS_ULPI_DIR_2 (GPIO_ALT|GPIO_AF10|GPIO_PORTI|GPIO_PIN11)
#define GPIO_OTGHS_ULPI_NXT_1 (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN3)
#define GPIO_OTGHS_ULPI_NXT_2 (GPIO_ALT|GPIO_AF10|GPIO_PORTH|GPIO_PIN4)
#define GPIO_OTGHS_ULPI_STP_0 (GPIO_ALT|GPIO_AF10|GPIO_PORTC|GPIO_PIN0)

#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_OTGHS_ULPI_D4_1 (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN11)
#  define GPIO_OTGHS_ULPI_D4_2 (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN2)
#else
#  define GPIO_OTGHS_ULPI_D4_0 (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN11)
#endif

/* RTC */

#define GPIO_RTC_50HZ_0       (GPIO_ALT|GPIO_AF0|GPIO_PORTC|GPIO_PIN15)

/* SDIO
 *
 * Note if the board uses GPIO_SPEED_50MHz I/O, for using the SDIO that you
 * must enable I/O Compensation via the configuration
 * option CONFIG_STM32_SYSCFG_IOCOMPENSATION=y.
 */

#define GPIO_SDIO_CMD_0       (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN2)
#define GPIO_SDIO_D0_0        (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN8)
#define GPIO_SDIO_D3_0        (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN11)
#define GPIO_SDIO_D4_0        (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN8)
#define GPIO_SDIO_D5_0        (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN9)
#define GPIO_SDIO_D6_0        (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN6)
#define GPIO_SDIO_D7_0        (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN7)

#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_SDIO_CK_1      (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN12)
#  define GPIO_SDIO_CK_2      (GPIO_ALT|GPIO_AF12|GPIO_PORTB|GPIO_PIN2)
#  define GPIO_SDIO_D1_1      (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN9)
#  define GPIO_SDIO_D1_2      (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN0)
#  define GPIO_SDIO_D2_1      (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN10)
#  define GPIO_SDIO_D2_2      (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN1)
#else
#  define GPIO_SDIO_CK_0      (GPIO_ALT|GPIO_AF12|GPIO_PORTC|GPIO_PIN12)
#  define GPIO_SDIO_D1_0      (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN9)
#  define GPIO_SDIO_D2_0      (GPIO_ALT|GPIO_AF12|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN10)
#endif

/* SPI */

#define GPIO_SPI1_MISO_1      (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN6)
#define GPIO_SPI1_MISO_2      (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN4)
#define GPIO_SPI1_MOSI_1      (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN7)
#define GPIO_SPI1_MOSI_2      (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN5)
#define GPIO_SPI1_NSS_1       (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN15)
#define GPIO_SPI1_NSS_2       (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN4)
#define GPIO_SPI1_SCK_1       (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN5)
#define GPIO_SPI1_SCK_2       (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN3)

#define GPIO_SPI2_MISO_1      (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN14)
#define GPIO_SPI2_MISO_2      (GPIO_ALT|GPIO_AF5|GPIO_PORTC|GPIO_PIN2)
#define GPIO_SPI2_MISO_3      (GPIO_ALT|GPIO_AF5|GPIO_PORTI|GPIO_PIN2)
#define GPIO_SPI2_MOSI_1      (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN15)
#define GPIO_SPI2_MOSI_2      (GPIO_ALT|GPIO_AF5|GPIO_PORTC|GPIO_PIN3)
#define GPIO_SPI2_MOSI_3      (GPIO_ALT|GPIO_AF5|GPIO_PORTI|GPIO_PIN3)
#define GPIO_SPI2_NSS_1       (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN12)
#define GPIO_SPI2_NSS_2       (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN9)
#define GPIO_SPI2_NSS_3       (GPIO_ALT|GPIO_AF5|GPIO_PORTI|GPIO_PIN0)
#define GPIO_SPI2_SCK_1       (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN10)
#define GPIO_SPI2_SCK_2       (GPIO_ALT|GPIO_AF5|GPIO_PORTB|GPIO_PIN13)
#define GPIO_SPI2_SCK_3       (GPIO_ALT|GPIO_AF5|GPIO_PORTI|GPIO_PIN1)
#if defined(CONFIG_STM32_STM32F427) || defined(CONFIG_STM32_STM32F429) || \
    defined(CONFIG_STM32_STM32F446) || defined(CONFIG_STM32_STM32F469)
#  define GPIO_SPI2_SCK_4     (GPIO_ALT|GPIO_AF5|GPIO_PORTD|GPIO_PIN3)
#endif
#if defined(CONFIG_STM32_STM32F446) || defined(CONFIG_STM32_STM32F469)
#  define GPIO_SPI2_SCK_5     (GPIO_ALT|GPIO_AF5|GPIO_PORTA|GPIO_PIN9)
#endif
#if defined(CONFIG_STM32_STM32F469)
#  define GPIO_SPI2_MOSI_4    (GPIO_ALT|GPIO_AF5|GPIO_PORTC|GPIO_PIN1)
#endif
#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_SPI2_MOSI_4    (GPIO_ALT|GPIO_AF7|GPIO_PORTC|GPIO_PIN1)
#  define GPIO_SPI2_NSS_4     (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN4)
#  define GPIO_SPI2_NSS_5     (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN1)
#  define GPIO_SPI2_SCK_6     (GPIO_ALT|GPIO_AF5|GPIO_PORTC|GPIO_PIN7)
#endif

#define GPIO_SPI3_MISO_1      (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN4)
#define GPIO_SPI3_MISO_2      (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN11)
#define GPIO_SPI3_MOSI_1      (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN5)
#define GPIO_SPI3_MOSI_2      (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN12)
#define GPIO_SPI3_NSS_1       (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN15)
#define GPIO_SPI3_NSS_2       (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN4)
#define GPIO_SPI3_SCK_1       (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN3)
#define GPIO_SPI3_SCK_2       (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN10)
#if defined(CONFIG_STM32_STM32F427) || defined(CONFIG_STM32_STM32F429) || \
    defined(CONFIG_STM32_STM32F446) || defined(CONFIG_STM32_STM32F469)
#  define GPIO_SPI3_MOSI_3    (GPIO_ALT|GPIO_AF6|GPIO_PORTD|GPIO_PIN6)
#endif
#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_SPI3_MOSI_4    (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN0)
#  define GPIO_SPI3_MOSI_5    (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN2)
#  define GPIO_SPI3_MOSI_6    (GPIO_ALT|GPIO_AF5|GPIO_PORTC|GPIO_PIN1)
#  define GPIO_SPI3_MOSI_7    (GPIO_ALT|GPIO_AF6|GPIO_PORTD|GPIO_PIN0)
#endif

#if defined(CONFIG_STM32_STM32F427) || defined(CONFIG_STM32_STM32F429) || \
    defined(CONFIG_STM32_STM32F446) || defined(CONFIG_STM32_STM32F469) || \
    defined(CONFIG_STM32_STM32F411)
#  define GPIO_SPI4_MISO_1    (GPIO_ALT|GPIO_AF5|GPIO_PORTE|GPIO_PIN5)
#  define GPIO_SPI4_MISO_2    (GPIO_ALT|GPIO_AF5|GPIO_PORTE|GPIO_PIN13)
#  define GPIO_SPI4_MOSI_1    (GPIO_ALT|GPIO_AF5|GPIO_PORTE|GPIO_PIN6)
#  define GPIO_SPI4_MOSI_2    (GPIO_ALT|GPIO_AF5|GPIO_PORTE|GPIO_PIN14)
#  define GPIO_SPI4_NSS_1     (GPIO_ALT|GPIO_AF5|GPIO_PORTE|GPIO_PIN4)
#  define GPIO_SPI4_NSS_2     (GPIO_ALT|GPIO_AF5|GPIO_PORTE|GPIO_PIN11)
#  define GPIO_SPI4_SCK_1     (GPIO_ALT|GPIO_AF5|GPIO_PORTE|GPIO_PIN2)
#  define GPIO_SPI4_SCK_2     (GPIO_ALT|GPIO_AF5|GPIO_PORTE|GPIO_PIN12)
#endif
#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_SPI4_MISO_3    (GPIO_ALT|GPIO_AF5|GPIO_PORTD|GPIO_PIN0)
#endif

#if defined(CONFIG_STM32_STM32F427) || defined(CONFIG_STM32_STM32F429) || \
    defined(CONFIG_STM32_STM32F469) || defined(CONFIG_STM32_STM32F411)
#  define GPIO_SPI5_MISO_1    (GPIO_ALT|GPIO_AF5|GPIO_PORTF|GPIO_PIN8)
#  define GPIO_SPI5_MISO_2    (GPIO_ALT|GPIO_AF5|GPIO_PORTH|GPIO_PIN7)
#  define GPIO_SPI5_MOSI_1    (GPIO_ALT|GPIO_AF5|GPIO_PORTF|GPIO_PIN9)
#  define GPIO_SPI5_MOSI_2    (GPIO_ALT|GPIO_AF5|GPIO_PORTF|GPIO_PIN11)
#  define GPIO_SPI5_NSS_1     (GPIO_ALT|GPIO_AF5|GPIO_PORTF|GPIO_PIN6)
#  define GPIO_SPI5_NSS_2     (GPIO_ALT|GPIO_AF5|GPIO_PORTH|GPIO_PIN5)
#  define GPIO_SPI5_SCK_1     (GPIO_ALT|GPIO_AF5|GPIO_PORTF|GPIO_PIN7)
#  define GPIO_SPI5_SCK_2     (GPIO_ALT|GPIO_AF5|GPIO_PORTH|GPIO_PIN6)
#endif

#if defined(CONFIG_STM32_STM32F427) || defined(CONFIG_STM32_STM32F429) || \
    defined(CONFIG_STM32_STM32F469)
#  define GPIO_SPI6_MISO_0    (GPIO_ALT|GPIO_AF5|GPIO_PORTG|GPIO_PIN12)
#  define GPIO_SPI6_MOSI_0    (GPIO_ALT|GPIO_AF5|GPIO_PORTG|GPIO_PIN14)
#  define GPIO_SPI6_NSS_0     (GPIO_ALT|GPIO_AF5|GPIO_PORTG|GPIO_PIN8)
#  define GPIO_SPI6_SCK_0     (GPIO_ALT|GPIO_AF5|GPIO_PORTG|GPIO_PIN13)
#endif

/* Timers */

#define GPIO_TIM1_BKIN_1      (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM1_BKIN_2      (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN12)
#define GPIO_TIM1_BKIN_3      (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN15)
#define GPIO_TIM1_CH1N_1      (GPIO_ALT|GPIO_AF1|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM1_CH1N_2      (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN13)
#define GPIO_TIM1_CH1N_3      (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN8)
#define GPIO_TIM1_CH1IN_1     (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN8)
#define GPIO_TIM1_CH1IN_2     (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTE|GPIO_PIN9)
#define GPIO_TIM1_CH1OUT_1    (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN8)
#define GPIO_TIM1_CH1OUT_2    (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN9)
#define GPIO_TIM1_CH2N_1      (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN0)
#define GPIO_TIM1_CH2N_2      (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN14)
#define GPIO_TIM1_CH2N_3      (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN10)
#define GPIO_TIM1_CH2IN_1     (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN9)
#define GPIO_TIM1_CH2IN_2     (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTE|GPIO_PIN11)
#define GPIO_TIM1_CH2OUT_1    (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN9)
#define GPIO_TIM1_CH2OUT_2    (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN11)
#define GPIO_TIM1_CH3N_1      (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN1)
#define GPIO_TIM1_CH3N_2      (GPIO_ALT|GPIO_AF1|GPIO_PORTB|GPIO_PIN15)
#define GPIO_TIM1_CH3N_3      (GPIO_ALT|GPIO_AF1|GPIO_PORTE|GPIO_PIN12)
#define GPIO_TIM1_CH3IN_1     (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN10)
#define GPIO_TIM1_CH3IN_2     (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTE|GPIO_PIN13)
#define GPIO_TIM1_CH3OUT_1    (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN10)
#define GPIO_TIM1_CH3OUT_2    (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN13)
#define GPIO_TIM1_CH4IN_1     (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN11)
#define GPIO_TIM1_CH4IN_2     (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTE|GPIO_PIN14)
#define GPIO_TIM1_CH4OUT_1    (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN11)
#define GPIO_TIM1_CH4OUT_2    (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN14)
#define GPIO_TIM1_ETR_1       (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN12)
#define GPIO_TIM1_ETR_2       (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTE|GPIO_PIN7)

#define GPIO_TIM2_CH1IN_1     (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM2_CH1IN_2     (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN15)
#define GPIO_TIM2_CH1IN_3     (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN5)
#define GPIO_TIM2_CH1OUT_1    (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM2_CH1OUT_2    (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN15)
#define GPIO_TIM2_CH1OUT_3    (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN5)
#define GPIO_TIM2_CH2IN_1     (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN1)
#define GPIO_TIM2_CH2IN_2     (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN3)
#define GPIO_TIM2_CH2OUT_1    (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN1)
#define GPIO_TIM2_CH2OUT_2    (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN3)
#define GPIO_TIM2_CH3IN_1     (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM2_CH3IN_2     (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN10)
#define GPIO_TIM2_CH3OUT_1    (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM2_CH3OUT_2    (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN10)
#define GPIO_TIM2_CH4IN_1     (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM2_CH4IN_2     (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN11)
#define GPIO_TIM2_CH4OUT_1    (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM2_CH4OUT_2    (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN11)
#define GPIO_TIM2_ETR_1       (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM2_ETR_2       (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN15)
#define GPIO_TIM2_ETR_3       (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN5)

#define GPIO_TIM3_CH1IN_1     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM3_CH1IN_2     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN4)
#define GPIO_TIM3_CH1IN_3     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTC|GPIO_PIN6)
#define GPIO_TIM3_CH1OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM3_CH1OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN4)
#define GPIO_TIM3_CH1OUT_3    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN6)
#define GPIO_TIM3_CH2IN_1     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM3_CH2IN_2     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN5)
#define GPIO_TIM3_CH2IN_3     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTC|GPIO_PIN7)
#define GPIO_TIM3_CH2OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM3_CH2OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN5)
#define GPIO_TIM3_CH2OUT_3    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN7)
#define GPIO_TIM3_CH3IN_1     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN0)
#define GPIO_TIM3_CH3IN_2     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTC|GPIO_PIN8)
#define GPIO_TIM3_CH3OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN0)
#define GPIO_TIM3_CH3OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN8)
#define GPIO_TIM3_CH4IN_1     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN1)
#define GPIO_TIM3_CH4IN_2     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTC|GPIO_PIN9)
#define GPIO_TIM3_CH4OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN1)
#define GPIO_TIM3_CH4OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN9)
#define GPIO_TIM3_ETR_0       (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTD|GPIO_PIN2)

#define GPIO_TIM4_CH1IN_1     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN6)
#define GPIO_TIM4_CH1IN_2     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTD|GPIO_PIN12)
#define GPIO_TIM4_CH1OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN6)
#define GPIO_TIM4_CH1OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN12)
#define GPIO_TIM4_CH2IN_1     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN7)
#define GPIO_TIM4_CH2IN_2     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTD|GPIO_PIN13)
#define GPIO_TIM4_CH2OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN7)
#define GPIO_TIM4_CH2OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN13)
#define GPIO_TIM4_CH3IN_1     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN8)
#define GPIO_TIM4_CH3IN_2     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTD|GPIO_PIN14)
#define GPIO_TIM4_CH3OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN8)
#define GPIO_TIM4_CH3OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN14)
#define GPIO_TIM4_CH4IN_1     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN9)
#define GPIO_TIM4_CH4IN_2     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTD|GPIO_PIN15)
#define GPIO_TIM4_CH4OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN9)
#define GPIO_TIM4_CH4OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN15)
#define GPIO_TIM4_ETR_0       (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTE|GPIO_PIN0)

#define GPIO_TIM5_CH1IN_1     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM5_CH1IN_2     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTH|GPIO_PIN10)
#define GPIO_TIM5_CH1OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM5_CH1OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN10)
#define GPIO_TIM5_CH2IN_1     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN1)
#define GPIO_TIM5_CH2IN_2     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTH|GPIO_PIN11)
#define GPIO_TIM5_CH2OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN1)
#define GPIO_TIM5_CH2OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN11)
#define GPIO_TIM5_CH3IN_1     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM5_CH3IN_2     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTH|GPIO_PIN12)
#define GPIO_TIM5_CH3OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM5_CH3OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN12)
#define GPIO_TIM5_CH4IN_1     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM5_CH4IN_2     (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTI|GPIO_PIN0)
#define GPIO_TIM5_CH4OUT_1    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM5_CH4OUT_2    (GPIO_ALT|GPIO_AF2|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN0)
#define GPIO_TIM5_ETR_0       (GPIO_ALT|GPIO_AF2|GPIO_FLOAT|GPIO_PORTH|GPIO_PIN10)

#define GPIO_TIM8_BKIN_1      (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM8_BKIN_2      (GPIO_ALT|GPIO_AF3|GPIO_PORTI|GPIO_PIN4)
#define GPIO_TIM8_CH1N_1      (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN5)
#define GPIO_TIM8_CH1N_2      (GPIO_ALT|GPIO_AF3|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM8_CH1N_3      (GPIO_ALT|GPIO_AF3|GPIO_PORTH|GPIO_PIN13)
#define GPIO_TIM8_CH1IN_1     (GPIO_ALT|GPIO_AF3|GPIO_FLOAT|GPIO_PORTC|GPIO_PIN6)
#define GPIO_TIM8_CH1IN_2     (GPIO_ALT|GPIO_AF3|GPIO_FLOAT|GPIO_PORTI|GPIO_PIN5)
#define GPIO_TIM8_CH1OUT_1    (GPIO_ALT|GPIO_AF3|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN6)
#define GPIO_TIM8_CH1OUT_2    (GPIO_ALT|GPIO_AF3|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN5)
#define GPIO_TIM8_CH2N_1      (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN0)
#define GPIO_TIM8_CH2N_2      (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN14)
#define GPIO_TIM8_CH2N_3      (GPIO_ALT|GPIO_AF3|GPIO_PORTH|GPIO_PIN14)
#define GPIO_TIM8_CH2IN_1     (GPIO_ALT|GPIO_AF3|GPIO_FLOAT|GPIO_PORTC|GPIO_PIN7)
#define GPIO_TIM8_CH2IN_2     (GPIO_ALT|GPIO_AF3|GPIO_FLOAT|GPIO_PORTI|GPIO_PIN6)
#define GPIO_TIM8_CH2OUT_1    (GPIO_ALT|GPIO_AF3|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN7)
#define GPIO_TIM8_CH2OUT_2    (GPIO_ALT|GPIO_AF3|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN6)
#define GPIO_TIM8_CH3N_1      (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN1)
#define GPIO_TIM8_CH3N_2      (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN15)
#define GPIO_TIM8_CH3N_3      (GPIO_ALT|GPIO_AF3|GPIO_PORTH|GPIO_PIN15)
#define GPIO_TIM8_CH3IN_1     (GPIO_ALT|GPIO_AF3|GPIO_FLOAT|GPIO_PORTC|GPIO_PIN8)
#define GPIO_TIM8_CH3IN_2     (GPIO_ALT|GPIO_AF3|GPIO_FLOAT|GPIO_PORTI|GPIO_PIN7)
#define GPIO_TIM8_CH3OUT_1    (GPIO_ALT|GPIO_AF3|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN8)
#define GPIO_TIM8_CH3OUT_2    (GPIO_ALT|GPIO_AF3|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN7)
#define GPIO_TIM8_CH4IN_1     (GPIO_ALT|GPIO_AF3|GPIO_FLOAT|GPIO_PORTC|GPIO_PIN9)
#define GPIO_TIM8_CH4IN_2     (GPIO_ALT|GPIO_AF3|GPIO_FLOAT|GPIO_PORTI|GPIO_PIN2)
#define GPIO_TIM8_CH4OUT_1    (GPIO_ALT|GPIO_AF3|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN9)
#define GPIO_TIM8_CH4OUT_2    (GPIO_ALT|GPIO_AF3|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN2)
#define GPIO_TIM8_ETR_1       (GPIO_ALT|GPIO_AF3|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN0)
#define GPIO_TIM8_ETR_2       (GPIO_ALT|GPIO_AF3|GPIO_FLOAT|GPIO_PORTI|GPIO_PIN3)

#define GPIO_TIM9_CH1IN_1     (GPIO_ALT|GPIO_AF3|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM9_CH1IN_2     (GPIO_ALT|GPIO_AF3|GPIO_FLOAT|GPIO_PORTE|GPIO_PIN5)
#define GPIO_TIM9_CH1OUT_1    (GPIO_ALT|GPIO_AF3|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN2)
#define GPIO_TIM9_CH1OUT_2    (GPIO_ALT|GPIO_AF3|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN5)
#define GPIO_TIM9_CH2IN_1     (GPIO_ALT|GPIO_AF3|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM9_CH2IN_2     (GPIO_ALT|GPIO_AF3|GPIO_FLOAT|GPIO_PORTE|GPIO_PIN6)
#define GPIO_TIM9_CH2OUT_1    (GPIO_ALT|GPIO_AF3|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN3)
#define GPIO_TIM9_CH2OUT_2    (GPIO_ALT|GPIO_AF3|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN6)

#define GPIO_TIM10_CH1IN_1    (GPIO_ALT|GPIO_AF3|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN8)
#define GPIO_TIM10_CH1IN_2    (GPIO_ALT|GPIO_AF3|GPIO_FLOAT|GPIO_PORTF|GPIO_PIN6)
#define GPIO_TIM10_CH1OUT_1   (GPIO_ALT|GPIO_AF3|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN8)
#define GPIO_TIM10_CH1OUT_2   (GPIO_ALT|GPIO_AF3|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN6)

#define GPIO_TIM11_CH1IN_1    (GPIO_ALT|GPIO_AF3|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN9)
#define GPIO_TIM11_CH1IN_2    (GPIO_ALT|GPIO_AF3|GPIO_FLOAT|GPIO_PORTF|GPIO_PIN7)
#define GPIO_TIM11_CH1OUT_1   (GPIO_ALT|GPIO_AF3|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN9)
#define GPIO_TIM11_CH1OUT_2   (GPIO_ALT|GPIO_AF3|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN7)

#define GPIO_TIM12_CH1IN_1    (GPIO_ALT|GPIO_AF9|GPIO_FLOAT|GPIO_PORTH|GPIO_PIN6)
#define GPIO_TIM12_CH1IN_2    (GPIO_ALT|GPIO_AF9|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN14)
#define GPIO_TIM12_CH1OUT_1   (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN6)
#define GPIO_TIM12_CH1OUT_2   (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN14)
#define GPIO_TIM12_CH2IN_1    (GPIO_ALT|GPIO_AF9|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN15)
#define GPIO_TIM12_CH2IN_2    (GPIO_ALT|GPIO_AF9|GPIO_FLOAT|GPIO_PORTH|GPIO_PIN9)
#define GPIO_TIM12_CH2OUT_1   (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN15)
#define GPIO_TIM12_CH2OUT_2   (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN9)

#define GPIO_TIM13_CH1IN_1    (GPIO_ALT|GPIO_AF9|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM13_CH1IN_2    (GPIO_ALT|GPIO_AF9|GPIO_FLOAT|GPIO_PORTF|GPIO_PIN8)
#define GPIO_TIM13_CH1OUT_1   (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN6)
#define GPIO_TIM13_CH1OUT_2   (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN8)

#define GPIO_TIM14_CH1IN_1    (GPIO_ALT|GPIO_AF9|GPIO_FLOAT|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM14_CH1IN_2    (GPIO_ALT|GPIO_AF9|GPIO_FLOAT|GPIO_PORTF|GPIO_PIN9)
#define GPIO_TIM14_CH1OUT_1   (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN7)
#define GPIO_TIM14_CH1OUT_2   (GPIO_ALT|GPIO_AF9|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN9)

#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_TIM2_CH1IN_4   (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN8)
#  define GPIO_TIM2_CH1OUT_4  (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN8)
#  define GPIO_TIM2_CH2IN_3   (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN9)
#  define GPIO_TIM2_CH2OUT_3  (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN9)
#  define GPIO_TIM2_CH4IN_3   (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN2)
#  define GPIO_TIM2_CH4OUT_3  (GPIO_ALT|GPIO_AF1|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN2)
#  define GPIO_TIM2_ETR_4     (GPIO_ALT|GPIO_AF1|GPIO_FLOAT|GPIO_PORTB|GPIO_PIN8)
#endif

/* Trace */

#define GPIO_TRACECLK_0       (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN2)
#define GPIO_TRACESWO_0       (GPIO_ALT|GPIO_AF0|GPIO_PORTB|GPIO_PIN3)

#if defined(CONFIG_STM32_STM32F446) || defined(CONFIG_STM32_STM32F469)
#  define GPIO_TRACED0_1      (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN3)
#  define GPIO_TRACED1_1      (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN4)
#else
#  define GPIO_TRACED0_0      (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN3)
#  define GPIO_TRACED1_0      (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN4)
#endif

#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_TRACED0_2      (GPIO_ALT|GPIO_AF0|GPIO_PORTC|GPIO_PIN8)
#  define GPIO_TRACED1_2      (GPIO_ALT|GPIO_AF0|GPIO_PORTD|GPIO_PIN3)
#endif

#if defined(CONFIG_STM32_STM32F469)
#  define GPIO_TRACED0_3      (GPIO_ALT|GPIO_AF0|GPIO_PORTC|GPIO_PIN1)
#  define GPIO_TRACED0_4      (GPIO_ALT|GPIO_AF0|GPIO_PORTG|GPIO_PIN13)
#  define GPIO_TRACED1_3      (GPIO_ALT|GPIO_AF0|GPIO_PORTC|GPIO_PIN8)
#  define GPIO_TRACED1_4      (GPIO_ALT|GPIO_AF0|GPIO_PORTG|GPIO_PIN14)
#  define GPIO_TRACED2_1      (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN5)
#  define GPIO_TRACED2_2      (GPIO_ALT|GPIO_AF0|GPIO_PORTD|GPIO_PIN2)
#  define GPIO_TRACED3_1      (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN6)
#  define GPIO_TRACED3_2      (GPIO_ALT|GPIO_AF0|GPIO_PORTC|GPIO_PIN12)
#else
#  define GPIO_TRACED2_0      (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN5)
#  define GPIO_TRACED3_0      (GPIO_ALT|GPIO_AF0|GPIO_PORTE|GPIO_PIN6)
#endif

/* UARTs/USARTs */

#define GPIO_USART1_CK_0      (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN8)
#define GPIO_USART1_CTS_0     (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN11)
#define GPIO_USART1_RTS_0     (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN12)
#define GPIO_USART1_RX_1      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN10)
#define GPIO_USART1_RX_2      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN7)

#define GPIO_USART1_TX_1      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN9)
#define GPIO_USART1_TX_2      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN6)

#define GPIO_USART2_CK_1      (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN4)
#define GPIO_USART2_CK_2      (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN7)
#define GPIO_USART2_CTS_1     (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN0)
#define GPIO_USART2_CTS_2     (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN3)
#define GPIO_USART2_RTS_1     (GPIO_ALT|GPIO_AF7|GPIO_PORTA|GPIO_PIN1)
#define GPIO_USART2_RTS_2     (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN4)
#define GPIO_USART2_RX_1      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN3)
#define GPIO_USART2_RX_2      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN6)
#define GPIO_USART2_TX_1      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN2)
#define GPIO_USART2_TX_2      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN5)

#define GPIO_USART3_CK_1      (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN12)
#define GPIO_USART3_CK_2      (GPIO_ALT|GPIO_AF7|GPIO_PORTC|GPIO_PIN12)
#define GPIO_USART3_CK_3      (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN10)
#define GPIO_USART3_CTS_1     (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN13)
#define GPIO_USART3_CTS_2     (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN11)
#define GPIO_USART3_RTS_1     (GPIO_ALT|GPIO_AF7|GPIO_PORTB|GPIO_PIN14)
#define GPIO_USART3_RTS_2     (GPIO_ALT|GPIO_AF7|GPIO_PORTD|GPIO_PIN12)
#define GPIO_USART3_RX_1      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN11)
#define GPIO_USART3_RX_2      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN11)
#define GPIO_USART3_RX_3      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN9)
#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_USART3_RX_4    (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN5)
#endif
#define GPIO_USART3_TX_1      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN10)
#define GPIO_USART3_TX_2      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN10)
#define GPIO_USART3_TX_3      (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN8)

#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_UART4_CTS_1    (GPIO_ALT|GPIO_AF8|GPIO_PORTB|GPIO_PIN0)
#  define GPIO_UART4_RTS_1    (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN15)
#endif
#define GPIO_UART4_RX_1       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN1)
#define GPIO_UART4_RX_2       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN11)
#define GPIO_UART4_TX_1       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN0)
#define GPIO_UART4_TX_2       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN10)

#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_UART5_CTS_1    (GPIO_ALT|GPIO_AF7|GPIO_PORTC|GPIO_PIN9)
#  define GPIO_UART5_RTS_1    (GPIO_ALT|GPIO_AF7|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN8)
#endif
#define GPIO_UART5_RX_0       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN2)
#define GPIO_UART5_TX_0       (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN12)

#define GPIO_USART6_CK_1      (GPIO_ALT|GPIO_AF8|GPIO_PORTC|GPIO_PIN8)
#define GPIO_USART6_CK_2      (GPIO_ALT|GPIO_AF8|GPIO_PORTG|GPIO_PIN7)
#define GPIO_USART6_CTS_1     (GPIO_ALT|GPIO_AF8|GPIO_PORTG|GPIO_PIN13)
#define GPIO_USART6_CTS_2     (GPIO_ALT|GPIO_AF8|GPIO_PORTG|GPIO_PIN15)
#define GPIO_USART6_RTS_1     (GPIO_ALT|GPIO_AF8|GPIO_PORTG|GPIO_PIN12)
#define GPIO_USART6_RTS_2     (GPIO_ALT|GPIO_AF8|GPIO_PORTG|GPIO_PIN8)
#define GPIO_USART6_RX_1      (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN7)
#define GPIO_USART6_RX_2      (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN9)
#define GPIO_USART6_TX_1      (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN6)
#define GPIO_USART6_TX_2      (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN14)

#if defined(CONFIG_STM32_STM32F427) || defined(CONFIG_STM32_STM32F429) || \
    defined(CONFIG_STM32_STM32F469)
#  define GPIO_UART7_RX_1     (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN7)
#  define GPIO_UART7_RX_2     (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN6)
#  define GPIO_UART7_TX_1     (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN8)
#  define GPIO_UART7_TX_2     (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN7)

#  define GPIO_UART8_RX_0     (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN0)
#  define GPIO_UART8_TX_0     (GPIO_ALT|GPIO_AF8|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN1)
#endif

/* LCD-TFT Display Controller (LTDC) */

#if defined(CONFIG_STM32_STM32F427) || defined(CONFIG_STM32_STM32F429) || \
    defined(CONFIG_STM32_STM32F469)
#  define GPIO_LTDC_R0_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN2)
#  define GPIO_LTDC_R0_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN15)
#  define GPIO_LTDC_R1_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN3)
#  define GPIO_LTDC_R1_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN0)
#  define GPIO_LTDC_R2_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN10)
#  define GPIO_LTDC_R2_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN8)
#  define GPIO_LTDC_R2_3      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN1)
#  define GPIO_LTDC_R3_1      (GPIO_ALT|GPIO_AF9 |GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN0)
#  define GPIO_LTDC_R3_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN9)
#  define GPIO_LTDC_R3_3      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN2)
#  define GPIO_LTDC_R4_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN11)
#  define GPIO_LTDC_R4_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN10)
#  define GPIO_LTDC_R4_3      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN3)
#  define GPIO_LTDC_R5_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN12)
#  define GPIO_LTDC_R5_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN11)
#  define GPIO_LTDC_R5_3      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN4)
#  define GPIO_LTDC_R6_1      (GPIO_ALT|GPIO_AF9 |GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN1)
#  define GPIO_LTDC_R6_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN12)
#  define GPIO_LTDC_R6_3      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN5)
#  define GPIO_LTDC_R6_4      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN8)
#  define GPIO_LTDC_R7_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN6)
#  define GPIO_LTDC_R7_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN15)
#  define GPIO_LTDC_R7_3      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN6)

#  define GPIO_LTDC_G0_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN5)
#  define GPIO_LTDC_G0_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN7)
#  define GPIO_LTDC_G1_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN6)
#  define GPIO_LTDC_G1_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN8)
#  define GPIO_LTDC_G2_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN6)
#  define GPIO_LTDC_G2_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN13)
#  define GPIO_LTDC_G2_3      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN9)
#  define GPIO_LTDC_G3_1      (GPIO_ALT|GPIO_AF9 |GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN10)
#  define GPIO_LTDC_G3_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN14)
#  define GPIO_LTDC_G3_3      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN10)
#  define GPIO_LTDC_G3_4      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN11)
#  define GPIO_LTDC_G4_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN10)
#  define GPIO_LTDC_G4_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTH|GPIO_PIN15)
#  define GPIO_LTDC_G4_3      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN11)
#  define GPIO_LTDC_G5_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN11)
#  define GPIO_LTDC_G5_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN0)
#  define GPIO_LTDC_G5_3      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTK|GPIO_PIN0)
#  define GPIO_LTDC_G6_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN7)
#  define GPIO_LTDC_G6_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN1)
#  define GPIO_LTDC_G6_3      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTK|GPIO_PIN1)
#  define GPIO_LTDC_G7_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN3)
#  define GPIO_LTDC_G7_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN2)
#  define GPIO_LTDC_G7_3      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTK|GPIO_PIN2)

#  define GPIO_LTDC_B0_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN4)
#  define GPIO_LTDC_B0_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN12)
#  define GPIO_LTDC_B1_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN12)
#  define GPIO_LTDC_B1_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN13)
#  define GPIO_LTDC_B2_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN6)
#  define GPIO_LTDC_B2_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN10)
#  define GPIO_LTDC_B2_3      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN14)
#  define GPIO_LTDC_B3_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN11)
#  define GPIO_LTDC_B3_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN10)
#  define GPIO_LTDC_B3_3      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTJ|GPIO_PIN15)
#  define GPIO_LTDC_B4_1      (GPIO_ALT|GPIO_AF9 |GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN12)
#  define GPIO_LTDC_B4_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN12)
#  define GPIO_LTDC_B4_3      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN4)
#  define GPIO_LTDC_B4_4      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTK|GPIO_PIN3)
#  define GPIO_LTDC_B5_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN3)
#  define GPIO_LTDC_B5_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN5)
#  define GPIO_LTDC_B5_3      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTK|GPIO_PIN4)
#  define GPIO_LTDC_B6_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN8)
#  define GPIO_LTDC_B6_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN6)
#  define GPIO_LTDC_B6_3      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTK|GPIO_PIN5)
#  define GPIO_LTDC_B7_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTB|GPIO_PIN9)
#  define GPIO_LTDC_B7_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN7)
#  define GPIO_LTDC_B7_3      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTK|GPIO_PIN6)

#  define GPIO_LTDC_VSYNC_1   (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTA|GPIO_PIN4)
#  define GPIO_LTDC_VSYNC_2   (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN9)
#  define GPIO_LTDC_VSYNC_3   (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN13)
#  define GPIO_LTDC_HSYNC_1   (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTC|GPIO_PIN6)
#  define GPIO_LTDC_HSYNC_2   (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN10)
#  define GPIO_LTDC_HSYNC_3   (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN12)
#  define GPIO_LTDC_DE_1      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN10)
#  define GPIO_LTDC_DE_2      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN13)
#  define GPIO_LTDC_DE_3      (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTK|GPIO_PIN7)
#  define GPIO_LTDC_CLK_1     (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTG|GPIO_PIN7)
#  define GPIO_LTDC_CLK_2     (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTE|GPIO_PIN14)
#  define GPIO_LTDC_CLK_3     (GPIO_ALT|GPIO_AF14|GPIO_PULLUP|GPIO_PUSHPULL|GPIO_PORTI|GPIO_PIN14)
#endif

/* Quad SPI */

#if defined(CONFIG_STM32_STM32F446) || defined(CONFIG_STM32_STM32F469)
#  define GPIO_QUADSPI_BK1_IO0_1 (GPIO_ALT|GPIO_AF10|GPIO_PORTF|GPIO_PIN8)
#  define GPIO_QUADSPI_BK1_IO0_2 (GPIO_ALT|GPIO_AF9 |GPIO_PORTC|GPIO_PIN9)
#  define GPIO_QUADSPI_BK1_IO0_3 (GPIO_ALT|GPIO_AF9 |GPIO_PORTD|GPIO_PIN11)
#  define GPIO_QUADSPI_BK1_IO1_1 (GPIO_ALT|GPIO_AF10|GPIO_PORTF|GPIO_PIN9)
#  define GPIO_QUADSPI_BK1_IO1_2 (GPIO_ALT|GPIO_AF9 |GPIO_PORTC|GPIO_PIN10)
#  define GPIO_QUADSPI_BK1_IO1_3 (GPIO_ALT|GPIO_AF9 |GPIO_PORTD|GPIO_PIN12)
#  define GPIO_QUADSPI_BK1_IO2_1 (GPIO_ALT|GPIO_AF9 |GPIO_PORTE|GPIO_PIN2)
#  define GPIO_QUADSPI_BK1_IO2_2 (GPIO_ALT|GPIO_AF9 |GPIO_PORTF|GPIO_PIN7)
#  define GPIO_QUADSPI_BK1_IO3_1 (GPIO_ALT|GPIO_AF9 |GPIO_PORTA|GPIO_PIN1)
#  define GPIO_QUADSPI_BK1_IO3_2 (GPIO_ALT|GPIO_AF9 |GPIO_PORTD|GPIO_PIN13)
#  define GPIO_QUADSPI_BK1_IO3_3 (GPIO_ALT|GPIO_AF9 |GPIO_PORTF|GPIO_PIN6)
#  define GPIO_QUADSPI_BK1_NCS_1 (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN6)
#endif
#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_QUADSPI_BK1_NCS_2 (GPIO_ALT|GPIO_AF10|GPIO_PORTG|GPIO_PIN6)
#elif defined(CONFIG_STM32_STM32F469)
#  define GPIO_QUADSPI_BK1_NCS_3 (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN10)
#endif

#if defined(CONFIG_STM32_STM32F446) || defined(CONFIG_STM32_STM32F469)
#  define GPIO_QUADSPI_BK2_IO0_1 (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN7)
#  define GPIO_QUADSPI_BK2_IO1_1 (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN8)
#  define GPIO_QUADSPI_BK2_IO2_1 (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN9)
#  define GPIO_QUADSPI_BK2_IO2_2 (GPIO_ALT|GPIO_AF9 |GPIO_PORTG|GPIO_PIN9)
#  define GPIO_QUADSPI_BK2_IO3_1 (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN10)
#  define GPIO_QUADSPI_BK2_IO3_2 (GPIO_ALT|GPIO_AF9 |GPIO_PORTG|GPIO_PIN14)
#  define GPIO_QUADSPI_BK2_NCS_0 (GPIO_ALT|GPIO_AF9 |GPIO_PORTC|GPIO_PIN11)
#endif
#if defined(CONFIG_STM32_STM32F469)
#  define GPIO_QUADSPI_BK2_IO0_2 (GPIO_ALT|GPIO_AF9 |GPIO_PORTH|GPIO_PIN2)
#  define GPIO_QUADSPI_BK2_IO1_2 (GPIO_ALT|GPIO_AF9 |GPIO_PORTH|GPIO_PIN3)
#endif

/* SPDIFRX */

#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_SPDIFRX_IN0_1  (GPIO_ALT|GPIO_AF7|GPIO_PORTG|GPIO_PIN11)
#  define GPIO_SPDIFRX_IN0_2  (GPIO_ALT|GPIO_AF8|GPIO_PORTD|GPIO_PIN7)
#  define GPIO_SPDIFRX_IN0_3  (GPIO_ALT|GPIO_AF8|GPIO_PORTB|GPIO_PIN7)
#  define GPIO_SPDIFRX_IN1_1  (GPIO_ALT|GPIO_AF7|GPIO_PORTG|GPIO_PIN12)
#  define GPIO_SPDIFRX_IN1_2  (GPIO_ALT|GPIO_AF8|GPIO_PORTD|GPIO_PIN8)
#  define GPIO_SPDIFRX_IN1_3  (GPIO_ALT|GPIO_AF7|GPIO_PORTC|GPIO_PIN7)
#  define GPIO_SPDIFRX_IN2_1  (GPIO_ALT|GPIO_AF7|GPIO_PORTG|GPIO_PIN8)
#  define GPIO_SPDIFRX_IN2_2  (GPIO_ALT|GPIO_AF8|GPIO_PORTC|GPIO_PIN4)
#  define GPIO_SPDIFRX_IN3_1  (GPIO_ALT|GPIO_AF7|GPIO_PORTG|GPIO_PIN9)
#  define GPIO_SPDIFRX_IN3_2  (GPIO_ALT|GPIO_AF8|GPIO_PORTC|GPIO_PIN5)
#endif

/* Serial Audio Interface */

#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_SAI1_FS_A_1    (GPIO_ALT|GPIO_AF6|GPIO_PORTE|GPIO_PIN4)
#  define GPIO_SAI1_FS_A_2    (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN3)
#  define GPIO_SAI1_FS_B_1    (GPIO_ALT|GPIO_AF6|GPIO_PORTF|GPIO_PIN9)
#  define GPIO_SAI1_FS_B_2    (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN9)
#  define GPIO_SAI1_MCLK_A_0  (GPIO_ALT|GPIO_AF6|GPIO_PORTE|GPIO_PIN2)
#  define GPIO_SAI1_MCLK_B_1  (GPIO_ALT|GPIO_AF6|GPIO_PORTF|GPIO_PIN7)
#  define GPIO_SAI1_MCLK_B_2  (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN0)
#  define GPIO_SAI1_SCK_A_1   (GPIO_ALT|GPIO_AF6|GPIO_PORTE|GPIO_PIN5)
#  define GPIO_SAI1_SCK_A_2   (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN10)
#  define GPIO_SAI1_SCK_B_1   (GPIO_ALT|GPIO_AF6|GPIO_PORTF|GPIO_PIN8)
#  define GPIO_SAI1_SCK_B_2   (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN12)
#  define GPIO_SAI1_SD_A_1    (GPIO_ALT|GPIO_AF6|GPIO_PORTB|GPIO_PIN2)
#  define GPIO_SAI1_SD_B_1    (GPIO_ALT|GPIO_AF6|GPIO_PORTA|GPIO_PIN9)
#elif defined(CONFIG_STM32_STM32F469)
#  define GPIO_SAI1_FS_A_0    (GPIO_ALT|GPIO_AF6|GPIO_PORTE|GPIO_PIN4)
#  define GPIO_SAI1_FS_B_0    (GPIO_ALT|GPIO_AF6|GPIO_PORTF|GPIO_PIN9)
#  define GPIO_SAI1_MCLK_A_1  (GPIO_ALT|GPIO_AF6|GPIO_PORTE|GPIO_PIN2)
#  define GPIO_SAI1_MCLK_A_2  (GPIO_ALT|GPIO_AF6|GPIO_PORTG|GPIO_PIN7)
#  define GPIO_SAI1_MCLK_B_0  (GPIO_ALT|GPIO_AF6|GPIO_PORTF|GPIO_PIN7)
#  define GPIO_SAI1_SCK_A_0   (GPIO_ALT|GPIO_AF6|GPIO_PORTE|GPIO_PIN5)
#  define GPIO_SAI1_SCK_B_0   (GPIO_ALT|GPIO_AF6|GPIO_PORTF|GPIO_PIN8)
#endif

#if defined(CONFIG_STM32_STM32F446) || defined(CONFIG_STM32_STM32F469)
#  define GPIO_SAI1_SD_A_2    (GPIO_ALT|GPIO_AF6|GPIO_PORTC|GPIO_PIN1)
#  define GPIO_SAI1_SD_A_3    (GPIO_ALT|GPIO_AF6|GPIO_PORTD|GPIO_PIN6)
#  define GPIO_SAI1_SD_A_4    (GPIO_ALT|GPIO_AF6|GPIO_PORTE|GPIO_PIN6)
#  define GPIO_SAI1_SD_B_2    (GPIO_ALT|GPIO_AF6|GPIO_PORTE|GPIO_PIN3)
#  define GPIO_SAI1_SD_B_3    (GPIO_ALT|GPIO_AF6|GPIO_PORTF|GPIO_PIN6)
#endif

#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_SAI2_FS_A_1    (GPIO_ALT|GPIO_AF10|GPIO_PORTD|GPIO_PIN12)
#  define GPIO_SAI2_FS_B_1    (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN13)
#  define GPIO_SAI2_FS_B_2    (GPIO_ALT|GPIO_AF10|GPIO_PORTG|GPIO_PIN9)
#  define GPIO_SAI2_FS_B_3    (GPIO_ALT|GPIO_AF8|GPIO_PORTA|GPIO_PIN12)
#  define GPIO_SAI2_MCLK_A_1  (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN0)
#  define GPIO_SAI2_MCLK_B_1  (GPIO_ALT|GPIO_AF10|GPIO_PORTA|GPIO_PIN1)
#  define GPIO_SAI2_MCLK_B_2  (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN14)
#  define GPIO_SAI2_SCK_A_1   (GPIO_ALT|GPIO_AF10|GPIO_PORTD|GPIO_PIN13)
#  define GPIO_SAI2_SCK_A_2   (GPIO_ALT|GPIO_AF8|GPIO_PORTD|GPIO_PIN14)
#  define GPIO_SAI2_SCK_B_1   (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN12)
#  define GPIO_SAI2_SCK_B_2   (GPIO_ALT|GPIO_AF8|GPIO_PORTA|GPIO_PIN2)
#  define GPIO_SAI2_SD_A_1    (GPIO_ALT|GPIO_AF10|GPIO_PORTD|GPIO_PIN11)
#  define GPIO_SAI2_SD_A_2    (GPIO_ALT|GPIO_AF10|GPIO_PORTB|GPIO_PIN11)
#  define GPIO_SAI2_SD_B_1    (GPIO_ALT|GPIO_AF10|GPIO_PORTG|GPIO_PIN10)
#  define GPIO_SAI2_SD_B_2    (GPIO_ALT|GPIO_AF10|GPIO_PORTE|GPIO_PIN11)
#  define GPIO_SAI2_SD_B_3    (GPIO_ALT|GPIO_AF10|GPIO_PORTF|GPIO_PIN11)
#endif

/* HDMI-CEC Controller */

#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_HDMICEC_1      (GPIO_ALT|GPIO_AF3|GPIO_PORTB|GPIO_PIN6)
#  define GPIO_HDMICEC_2      (GPIO_ALT|GPIO_AF4|GPIO_PORTA|GPIO_PIN15)
#endif

/* FMPI2C */

#if defined(CONFIG_STM32_STM32F446)
#  define GPIO_FMPI2C1_SCL_1  (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTC|GPIO_PIN6)
#  define GPIO_FMPI2C1_SCL_2  (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTD|GPIO_PIN12)
#  define GPIO_FMPI2C1_SCL_3  (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTD|GPIO_PIN14)
#  define GPIO_FMPI2C1_SCL_4  (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTF|GPIO_PIN14)

#  define GPIO_FMPI2C1_SDA_1  (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTC|GPIO_PIN7)
#  define GPIO_FMPI2C1_SDA_2  (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTD|GPIO_PIN13)
#  define GPIO_FMPI2C1_SDA_3  (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTD|GPIO_PIN15)
#  define GPIO_FMPI2C1_SDA_4  (GPIO_ALT|GPIO_AF4|GPIO_OPENDRAIN|GPIO_PORTF|GPIO_PIN15)

#  define GPIO_FMPI2C1_SMBA_1 (GPIO_ALT|GPIO_AF4|GPIO_PUSHPULL|GPIO_PORTD|GPIO_PIN11)
#  define GPIO_FMPI2C1_SMBA_2 (GPIO_ALT|GPIO_AF4|GPIO_PUSHPULL|GPIO_PORTF|GPIO_PIN13)
#endif

#endif /* __ARCH_ARM_SRC_STM32_HARDWARE_STM32F40XXX_PINMAP_H */
