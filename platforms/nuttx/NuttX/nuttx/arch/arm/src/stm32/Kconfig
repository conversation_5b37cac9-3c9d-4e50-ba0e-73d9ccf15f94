#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

comment "STM32 Configuration Options"

choice
	prompt "STM32 Chip Selection"
	default ARCH_CHIP_STM32F103ZE
	depends on ARCH_CHIP_STM32

config ARCH_CHIP_STM32L151C6
	bool "STM32L151C6"
	select STM32_STM32L15XX
	select STM32_LOWDENSITY
	---help---
		STM32L 48-pin EnergyLite, 32KB FLASH, 10KB SRAM, 4KB EEPROM

config ARCH_CHIP_STM32L151C8
	bool "STM32L151C8"
	select STM32_STM32L15XX
	select STM32_LOWDENSITY
	---help---
		STM32L 48-pin EnergyLite, 64KB FLASH, 10KB SRAM, 4KB EEPROM

config ARCH_CHIP_STM32L151CB
	bool "STM32L151CB"
	select STM32_STM32L15XX
	select STM32_LOWDENSITY
	---help---
		STM32L 48-pin EnergyLite, 128KB FLASH, 16KB SRAM, 4KB EEPROM

config ARCH_CHIP_STM32L151R6
	bool "STM32L151R6"
	select STM32_STM32L15XX
	select STM32_LOWDENSITY
	---help---
		STM32L 64-pin EnergyLite, 32KB FLASH, 10KB SRAM, 4KB EEPROM

config ARCH_CHIP_STM32L151R8
	bool "STM32L151R8"
	select STM32_STM32L15XX
	select STM32_LOWDENSITY
	---help---
		STM32L 64-pin EnergyLite, 64KB FLASH, 10KB SRAM, 4KB EEPROM

config ARCH_CHIP_STM32L151RB
	bool "STM32L151RB"
	select STM32_STM32L15XX
	select STM32_LOWDENSITY
	---help---
		STM32L 64-pin EnergyLite, 128KB FLASH, 16KB SRAM, 4KB EEPROM

config ARCH_CHIP_STM32L151V6
	bool "STM32L151V6"
	select STM32_STM32L15XX
	select STM32_LOWDENSITY
	---help---
		STM32L 100-pin EnergyLite, 32KB FLASH, 10KB SRAM, 4KB EEPROM

config ARCH_CHIP_STM32L151V8
	bool "STM32L151V8"
	select STM32_STM32L15XX
	select STM32_LOWDENSITY
	---help---
		STM32L 100-pin EnergyLite, 64KB FLASH, 10KB SRAM, 4KB EEPROM

config ARCH_CHIP_STM32L151VB
	bool "STM32L151VB"
	select STM32_STM32L15XX
	select STM32_LOWDENSITY
	---help---
		STM32L 100-pin EnergyLite, 128KB FLASH, 16KB SRAM, 4KB EEPROM

config ARCH_CHIP_STM32L152C6
	bool "STM32L152C6"
	select STM32_STM32L15XX
	select STM32_LOWDENSITY
	---help---
		STM32L 48-pin EnergyLite, 32KB FLASH, 10KB SRAM, 4KB EEPROM with
		4x18 LCD interface

config ARCH_CHIP_STM32L152C8
	bool "STM32L152C8"
	select STM32_STM32L15XX
	select STM32_LOWDENSITY
	---help---
		STM32L 48-pin EnergyLite, 64KB FLASH, 10KB SRAM, 4KB EEPROM with
		4x18 LCD interface

config ARCH_CHIP_STM32L152CB
	bool "STM32L152CB"
	select STM32_STM32L15XX
	select STM32_LOWDENSITY
	---help---
		STM32L 48-pin EnergyLite, 128KB FLASH, 16KB SRAM, 4KB EEPROM with
		4x18 LCD interface

config ARCH_CHIP_STM32L152R6
	bool "STM32L152R6"
	select STM32_STM32L15XX
	select STM32_LOWDENSITY
	---help---
		STM32L 64-pin EnergyLite, 32KB FLASH, 10KB SRAM, 4KB EEPROM with
		4x32/8x28 LCD interface

config ARCH_CHIP_STM32L152R8
	bool "STM32L152R8"
	select STM32_STM32L15XX
	select STM32_LOWDENSITY
	---help---
		STM32L 64-pin EnergyLite, 64KB FLASH, 10KB SRAM, 4KB EEPROM with
		4x32/8x28 LCD interface

config ARCH_CHIP_STM32L152RB
	bool "STM32L152RB"
	select STM32_STM32L15XX
	select STM32_LOWDENSITY
	---help---
		STM32L 64-pin EnergyLite, 128KB FLASH, 16KB SRAM, 4KB EEPROM with
		4x32/8x28 LCD interface

config ARCH_CHIP_STM32L152V6
	bool "STM32L152V6"
	select STM32_STM32L15XX
	select STM32_LOWDENSITY
	---help---
		STM32L 100-pin EnergyLite, 32KB FLASH, 10KB SRAM, 4KB EEPROM with
		4x44/8x40 LCD interface

config ARCH_CHIP_STM32L152V8
	bool "STM32L152V8"
	select STM32_STM32L15XX
	select STM32_LOWDENSITY
	---help---
		STM32L 100-pin EnergyLite, 64KB FLASH, 10KB SRAM, 4KB EEPROM with
		4x44/8x40 LCD interface

config ARCH_CHIP_STM32L152VB
	bool "STM32L152VB"
	select STM32_STM32L15XX
	select STM32_LOWDENSITY
	---help---
		STM32L 100-pin EnergyLite, 128KB FLASH, 16KB SRAM, 4KB EEPROM with
		4x44/8x40 LCD interface

config ARCH_CHIP_STM32L152CC
	bool "STM32L152CC"
	select STM32_STM32L15XX
	select STM32_MEDIUMPLUSDENSITY
	---help---
		STM32L 48-pin EnergyLite, 256KB FLASH, 32KB SRAM, 8KB EEPROM with
		4x18 LCD interface

config ARCH_CHIP_STM32L152RC
	bool "STM32L152RC"
	select STM32_STM32L15XX
	select STM32_MEDIUMPLUSDENSITY
	---help---
		STM32L 64-pin EnergyLite, 256KB FLASH, 32KB SRAM, 8KB EEPROM with
		4x32/8x28 LCD interface

config ARCH_CHIP_STM32L152VC
	bool "STM32L152VC"
	select STM32_STM32L15XX
	select STM32_MEDIUMPLUSDENSITY
	---help---
		STM32L 100-pin EnergyLite, 256KB FLASH, 32KB SRAM, 8KB EEPROM with
		4x44/8x40 LCD interface

config ARCH_CHIP_STM32L151RE
	bool "STM32L151RE"
	select STM32_STM32L15XX
	select STM32_HIGHDENSITY

config ARCH_CHIP_STM32L152RE
	bool "STM32L152RE"
	select STM32_STM32L15XX
	select STM32_HIGHDENSITY

config ARCH_CHIP_STM32L151VE
	bool "STM32L151VE"
	select STM32_STM32L15XX
	select STM32_HIGHDENSITY

config ARCH_CHIP_STM32L152VE
	bool "STM32L152VE"
	select STM32_STM32L15XX
	select STM32_HIGHDENSITY

config ARCH_CHIP_STM32L151QE
	bool "STM32L151QE"
	select STM32_STM32L15XX
	select STM32_HIGHDENSITY

config ARCH_CHIP_STM32L152QE
	bool "STM32L152QE"
	select STM32_STM32L15XX
	select STM32_HIGHDENSITY

config ARCH_CHIP_STM32L151ZE
	bool "STM32L151ZE"
	select STM32_STM32L15XX
	select STM32_HIGHDENSITY

config ARCH_CHIP_STM32L152ZE
	bool "STM32L152ZE"
	select STM32_STM32L15XX
	select STM32_HIGHDENSITY

config ARCH_CHIP_STM32L162ZD
	bool "STM32L162ZD"
	select STM32_STM32L15XX
	select STM32_HIGHDENSITY
	select STM32_HAVE_AES
	---help---
		STM32L 144-pin EnergyLite, 384KB FLASH, 48KB SRAM, 12KB EEPROM with
		4x44/8x40 LCD interface

config ARCH_CHIP_STM32L162VE
	bool "STM32L162VE"
	select STM32_STM32L15XX
	select STM32_HIGHDENSITY
	select STM32_HAVE_AES
	---help---
		STM32L 100-pin EnergyLite, 512KB FLASH, 80KB SRAM, 16KB EEPROM with
		4x44/8x40 LCD interface

config ARCH_CHIP_STM32F100C8
	bool "STM32F100C8"
	select STM32_STM32F10XX
	select STM32_VALUELINE
	select STM32_MEDIUMDENSITY
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F100CB
	bool "STM32F100CB"
	select STM32_STM32F10XX
	select STM32_VALUELINE
	select STM32_MEDIUMDENSITY
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F100R8
	bool "STM32F100R8"
	select STM32_STM32F10XX
	select STM32_VALUELINE
	select STM32_MEDIUMDENSITY
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F100RB
	bool "STM32F100RB"
	select STM32_STM32F10XX
	select STM32_VALUELINE
	select STM32_MEDIUMDENSITY
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F100RC
	bool "STM32F100RC"
	select STM32_STM32F10XX
	select STM32_VALUELINE
	select STM32_HIGHDENSITY
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F100RD
	bool "STM32F100RD"
	select STM32_STM32F10XX
	select STM32_VALUELINE
	select STM32_HIGHDENSITY
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F100RE
	bool "STM32F100RE"
	select STM32_STM32F10XX
	select STM32_VALUELINE
	select STM32_HIGHDENSITY
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F100V8
	bool "STM32F100V8"
	select STM32_STM32F10XX
	select STM32_VALUELINE
	select STM32_MEDIUMDENSITY
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F100VB
	bool "STM32F100VB"
	select STM32_STM32F10XX
	select STM32_VALUELINE
	select STM32_MEDIUMDENSITY
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F100VC
	bool "STM32F100VC"
	select STM32_STM32F10XX
	select STM32_VALUELINE
	select STM32_HIGHDENSITY
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F100VD
	bool "STM32F100VD"
	select STM32_STM32F10XX
	select STM32_VALUELINE
	select STM32_HIGHDENSITY
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F100VE
	bool "STM32F100VE"
	select STM32_STM32F10XX
	select STM32_VALUELINE
	select STM32_HIGHDENSITY
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F102CB
	bool "STM32F102CB"
	select STM32_STM32F10XX
	select STM32_USBACCESSLINE
	select STM32_MEDIUMDENSITY
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F103T8
	bool "STM32F103T8"
	select STM32_STM32F10XX
	select STM32_PERFORMANCELINE
	select STM32_MEDIUMDENSITY
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F103TB
	bool "STM32F103TB"
	select STM32_STM32F10XX
	select STM32_PERFORMANCELINE
	select STM32_MEDIUMDENSITY
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F103C4
	bool "STM32F103C4"
	select STM32_STM32F10XX
	select STM32_PERFORMANCELINE
	select STM32_LOWDENSITY

config ARCH_CHIP_STM32F103C8
	bool "STM32F103C8"
	select STM32_STM32F10XX
	select STM32_PERFORMANCELINE
	select STM32_MEDIUMDENSITY
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F103CB
	bool "STM32F103CB"
	select STM32_STM32F10XX
	select STM32_PERFORMANCELINE
	select STM32_MEDIUMDENSITY
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F103R8
	bool "STM32F103R8"
	select STM32_STM32F10XX
	select STM32_PERFORMANCELINE
	select STM32_MEDIUMDENSITY
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F103RB
	bool "STM32F103RB"
	select STM32_STM32F10XX
	select STM32_PERFORMANCELINE
	select STM32_MEDIUMDENSITY
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F103RC
	bool "STM32F103RC"
	select STM32_STM32F10XX
	select STM32_PERFORMANCELINE
	select STM32_HIGHDENSITY
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F103RD
	bool "STM32F103RD"
	select STM32_STM32F10XX
	select STM32_PERFORMANCELINE
	select STM32_HIGHDENSITY
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F103RE
	bool "STM32F103RE"
	select STM32_STM32F10XX
	select STM32_PERFORMANCELINE
	select STM32_HIGHDENSITY
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F103RG
	bool "STM32F103RG"
	select STM32_STM32F10XX
	select STM32_PERFORMANCELINE
	select STM32_HIGHDENSITY
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F103V8
	bool "STM32F103V8"
	select STM32_STM32F10XX
	select STM32_PERFORMANCELINE
	select STM32_MEDIUMDENSITY
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F103VB
	bool "STM32F103VB"
	select STM32_STM32F10XX
	select STM32_PERFORMANCELINE
	select STM32_MEDIUMDENSITY
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F103VC
	bool "STM32F103VC"
	select STM32_STM32F10XX
	select STM32_PERFORMANCELINE
	select STM32_HIGHDENSITY
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F103VE
	bool "STM32F103VE"
	select STM32_STM32F10XX
	select STM32_PERFORMANCELINE
	select STM32_HIGHDENSITY
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F103ZE
	bool "STM32F103ZE"
	select STM32_STM32F10XX
	select STM32_PERFORMANCELINE
	select STM32_HIGHDENSITY
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F105VB
	bool "STM32F105VBT7"
	select STM32_STM32F10XX
	select STM32_CONNECTIVITYLINE
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F105RB
	bool "STM32F105RB"
	select STM32_STM32F10XX
	select STM32_CONNECTIVITYLINE
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F107VC
	bool "STM32F107VC"
	select STM32_STM32F10XX
	select STM32_CONNECTIVITYLINE
	select STM32_HAVE_DAC1
	select STM32_HAVE_TIM4

config ARCH_CHIP_STM32F205RG
	bool "STM32F205RG"
	select STM32_STM32F20XX
	select STM32_STM32F205

config ARCH_CHIP_STM32F207VC
	bool "STM32F207VC"
	select STM32_STM32F20XX
	select STM32_STM32F207

config ARCH_CHIP_STM32F207VE
	bool "STM32F207VE"
	select STM32_STM32F20XX
	select STM32_STM32F207

config ARCH_CHIP_STM32F207VF
	bool "STM32F207VF"
	select STM32_STM32F20XX
	select STM32_STM32F207

config ARCH_CHIP_STM32F207VG
	bool "STM32F207VG"
	select STM32_STM32F20XX
	select STM32_STM32F207

config ARCH_CHIP_STM32F207IC
	bool "STM32F207IC"
	select STM32_STM32F20XX
	select STM32_STM32F207

config ARCH_CHIP_STM32F207IE
	bool "STM32F207IE"
	select STM32_STM32F20XX
	select STM32_STM32F207

config ARCH_CHIP_STM32F207IF
	bool "STM32F207IF"
	select STM32_STM32F20XX
	select STM32_STM32F207

config ARCH_CHIP_STM32F207IG
	bool "STM32F207IG"
	select STM32_STM32F20XX
	select STM32_STM32F207

config ARCH_CHIP_STM32F207ZC
	bool "STM32F207ZC"
	select STM32_STM32F20XX
	select STM32_STM32F207

config ARCH_CHIP_STM32F207ZE
	bool "STM32F207ZE"
	select STM32_STM32F20XX
	select STM32_STM32F207

config ARCH_CHIP_STM32F207ZF
	bool "STM32F207ZF"
	select STM32_STM32F20XX
	select STM32_STM32F207

config ARCH_CHIP_STM32F207ZG
	bool "STM32F207ZG"
	select STM32_STM32F20XX
	select STM32_STM32F207

config ARCH_CHIP_STM32F302K6
	bool "STM32F302K6"
	select STM32_STM32F30XX
	select STM32_STM32F302
	select STM32_HAVE_I2C3

config ARCH_CHIP_STM32F302K8
	bool "STM32F302K8"
	select STM32_STM32F30XX
	select STM32_STM32F302
	select STM32_HAVE_I2C3

config ARCH_CHIP_STM32F302C6
	bool "STM32F302C6"
	select STM32_STM32F30XX
	select STM32_STM32F302

config ARCH_CHIP_STM32F302C8
	bool "STM32F302C8"
	select STM32_STM32F30XX
	select STM32_STM32F302

config ARCH_CHIP_STM32F302R6
	bool "STM32F302R6"
	select STM32_STM32F30XX
	select STM32_STM32F302

config ARCH_CHIP_STM32F302R8
	bool "STM32F302R8"
	select STM32_STM32F30XX
	select STM32_STM32F302

config ARCH_CHIP_STM32F302CB
	bool "STM32F302CB"
	select STM32_STM32F30XX
	select STM32_STM32F302
	select STM32_HAVE_ADC2
	select STM32_HAVE_USART3

config ARCH_CHIP_STM32F302CC
	bool "STM32F302CC"
	select STM32_STM32F30XX
	select STM32_STM32F302
	select STM32_HAVE_ADC2
	select STM32_HAVE_USART3

config ARCH_CHIP_STM32F302RB
	bool "STM32F302RB"
	select STM32_STM32F30XX
	select STM32_STM32F302
	select STM32_HAVE_ADC2
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5

config ARCH_CHIP_STM32F302RC
	bool "STM32F302RC"
	select STM32_STM32F30XX
	select STM32_STM32F302
	select STM32_HAVE_ADC2
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5

config ARCH_CHIP_STM32F302VB
	bool "STM32F302VB"
	select STM32_STM32F30XX
	select STM32_STM32F302
	select STM32_HAVE_ADC2
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5

config ARCH_CHIP_STM32F302VC
	bool "STM32F302VC"
	select STM32_STM32F30XX
	select STM32_STM32F302
	select STM32_HAVE_ADC2
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5

config ARCH_CHIP_STM32F303K6
	bool "STM32F303K6"
	select STM32_STM32F30XX
	select STM32_STM32F303
	select STM32_HAVE_DAC2

config ARCH_CHIP_STM32F303K8
	bool "STM32F303K8"
	select STM32_STM32F30XX
	select STM32_STM32F303
	select STM32_HAVE_DAC2

config ARCH_CHIP_STM32F303C6
	bool "STM32F303C6"
	select STM32_STM32F30XX
	select STM32_STM32F303
	select STM32_HAVE_DAC2
	select STM32_HAVE_USART3

config ARCH_CHIP_STM32F303C8
	bool "STM32F303C8"
	select STM32_STM32F30XX
	select STM32_STM32F303
	select STM32_HAVE_DAC2
	select STM32_HAVE_USART3

config ARCH_CHIP_STM32F303CB
	bool "STM32F303CB"
	select STM32_STM32F30XX
	select STM32_STM32F303
	select STM32_HAVE_ADC3
	select STM32_HAVE_ADC4
	select STM32_HAVE_I2C2
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM8
	select STM32_HAVE_USART3
	select STM32_HAVE_USBDEV

config ARCH_CHIP_STM32F303CC
	bool "STM32F303CC"
	select STM32_STM32F30XX
	select STM32_STM32F303
	select STM32_HAVE_ADC3
	select STM32_HAVE_ADC4
	select STM32_HAVE_I2C2
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM8
	select STM32_HAVE_USART3
	select STM32_HAVE_USBDEV

config ARCH_CHIP_STM32F303RB
	bool "STM32F303RB"
	select STM32_STM32F30XX
	select STM32_STM32F303
	select STM32_HAVE_ADC3
	select STM32_HAVE_ADC4
	select STM32_HAVE_I2C2
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM8
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5
	select STM32_HAVE_USBDEV

config ARCH_CHIP_STM32F303RC
	bool "STM32F303RC"
	select STM32_STM32F30XX
	select STM32_STM32F303
	select STM32_HAVE_ADC3
	select STM32_HAVE_ADC4
	select STM32_HAVE_I2C2
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM8
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5
	select STM32_HAVE_USBDEV

config ARCH_CHIP_STM32F303RD
	bool "STM32F303RD"
	select STM32_STM32F30XX
	select STM32_STM32F303
	select STM32_HAVE_ADC3
	select STM32_HAVE_ADC4
	select STM32_HAVE_I2C2
	select STM32_HAVE_I2C3
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3
	select STM32_HAVE_SPI4
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM8
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5
	select STM32_HAVE_USBDEV

config ARCH_CHIP_STM32F303RE
	bool "STM32F303RE"
	select STM32_STM32F30XX
	select STM32_STM32F303
	select STM32_HAVE_ADC3
	select STM32_HAVE_ADC4
	select STM32_HAVE_I2C2
	select STM32_HAVE_I2C3
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3
	select STM32_HAVE_SPI4
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM8
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5
	select STM32_HAVE_USBDEV

config ARCH_CHIP_STM32F303VB
	bool "STM32F303VB"
	select STM32_STM32F30XX
	select STM32_STM32F303
	select STM32_HAVE_ADC3
	select STM32_HAVE_ADC4
	select STM32_HAVE_I2C2
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM8
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5
	select STM32_HAVE_USBDEV

config ARCH_CHIP_STM32F303VC
	bool "STM32F303VC"
	select STM32_STM32F30XX
	select STM32_STM32F303
	select STM32_HAVE_ADC3
	select STM32_HAVE_ADC4
	select STM32_HAVE_I2C2
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM8
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5
	select STM32_HAVE_USBDEV

config ARCH_CHIP_STM32F303VD
	bool "STM32F303VD"
	select STM32_STM32F30XX
	select STM32_STM32F303
	select STM32_HAVE_ADC3
	select STM32_HAVE_ADC4
	select STM32_HAVE_USART3

config ARCH_CHIP_STM32F303VE
	bool "STM32F303VE"
	select STM32_STM32F30XX
	select STM32_STM32F303
	select STM32_HAVE_ADC3
	select STM32_HAVE_ADC4
	select STM32_HAVE_USART3

config ARCH_CHIP_STM32F303ZD
	bool "STM32F303ZD"
	select STM32_STM32F30XX
	select STM32_STM32F303
	select STM32_HAVE_ADC3
	select STM32_HAVE_ADC4
	select STM32_HAVE_USART3

config ARCH_CHIP_STM32F303ZE
	bool "STM32F303ZE"
	select STM32_STM32F30XX
	select STM32_STM32F303
	select STM32_HAVE_ADC3
	select STM32_HAVE_ADC4
	select STM32_HAVE_USART3

config ARCH_CHIP_STM32F334K4
	bool "STM32F334K4"
	select STM32_STM32F33XX

config ARCH_CHIP_STM32F334K6
	bool "STM32F334K6"
	select STM32_STM32F33XX

config ARCH_CHIP_STM32F334K8
	bool "STM32F334K8"
	select STM32_STM32F33XX

config ARCH_CHIP_STM32F334C4
	bool "STM32F334C4"
	select STM32_STM32F33XX

config ARCH_CHIP_STM32F334C6
	bool "STM32F334C6"
	select STM32_STM32F33XX

config ARCH_CHIP_STM32F334C8
	bool "STM32F334C8"
	select STM32_STM32F33XX

config ARCH_CHIP_STM32F334R4
	bool "STM32F334R4"
	select STM32_STM32F33XX

config ARCH_CHIP_STM32F334R6
	bool "STM32F334R6"
	select STM32_STM32F33XX

config ARCH_CHIP_STM32F334R8
	bool "STM32F334R8"
	select STM32_STM32F33XX

config ARCH_CHIP_STM32F372C8
	bool "STM32F372C8"
	select STM32_STM32F37XX

config ARCH_CHIP_STM32F372R8
	bool "STM32F372R8"
	select STM32_STM32F37XX

config ARCH_CHIP_STM32F372V8
	bool "STM32F372V8"
	select STM32_STM32F37XX

config ARCH_CHIP_STM32F372CB
	bool "STM32F372CB"
	select STM32_STM32F37XX

config ARCH_CHIP_STM32F372RB
	bool "STM32F372RB"
	select STM32_STM32F37XX

config ARCH_CHIP_STM32F372VB
	bool "STM32F372VB"
	select STM32_STM32F37XX

config ARCH_CHIP_STM32F372CC
	bool "STM32F372CC"
	select STM32_STM32F37XX

config ARCH_CHIP_STM32F372RC
	bool "STM32F372RC"
	select STM32_STM32F37XX

config ARCH_CHIP_STM32F372VC
	bool "STM32F372VC"
	select STM32_STM32F37XX

config ARCH_CHIP_STM32F373C8
	bool "STM32F373C8"
	select STM32_STM32F37XX

config ARCH_CHIP_STM32F373R8
	bool "STM32F373R8"
	select STM32_STM32F37XX

config ARCH_CHIP_STM32F373V8
	bool "STM32F373V8"
	select STM32_STM32F37XX

config ARCH_CHIP_STM32F373CB
	bool "STM32F373CB"
	select STM32_STM32F37XX

config ARCH_CHIP_STM32F373RB
	bool "STM32F373RB"
	select STM32_STM32F37XX

config ARCH_CHIP_STM32F373VB
	bool "STM32F373VB"
	select STM32_STM32F37XX

config ARCH_CHIP_STM32F373CC
	bool "STM32F373CC"
	select STM32_STM32F37XX

config ARCH_CHIP_STM32F373RC
	bool "STM32F373RC"
	select STM32_STM32F37XX

config ARCH_CHIP_STM32F373VC
	bool "STM32F373VC"
	select STM32_STM32F37XX

config ARCH_CHIP_STM32F401CB
	bool "STM32F401CB"
	select STM32_STM32F401xBC

config ARCH_CHIP_STM32F401RB
	bool "STM32F401RB"
	select STM32_STM32F401xBC

config ARCH_CHIP_STM32F401VB
	bool "STM32F401VB"
	select STM32_STM32F401xBC

config ARCH_CHIP_STM32F401CC
	bool "STM32F401CC"
	select STM32_STM32F401xBC

config ARCH_CHIP_STM32F401RC
	bool "STM32F401RC"
	select STM32_STM32F401xBC

config ARCH_CHIP_STM32F401VC
	bool "STM32F401VC"
	select STM32_STM32F401xBC

config ARCH_CHIP_STM32F401CD
	bool "STM32F401CD"
	select STM32_STM32F401xDE

config ARCH_CHIP_STM32F401RD
	bool "STM32F401RD"
	select STM32_STM32F401xDE

config ARCH_CHIP_STM32F401VD
	bool "STM32F401VD"
	select STM32_STM32F401xDE

config ARCH_CHIP_STM32F401CE
	bool "STM32F401CE"
	select STM32_STM32F401xDE

config ARCH_CHIP_STM32F401RE
	bool "STM32F401RE"
	select STM32_STM32F401xDE

config ARCH_CHIP_STM32F401VE
	bool "STM32F401VE"
	select STM32_STM32F401xDE

config ARCH_CHIP_STM32F410RB
	bool "STM32F410RB"
	select STM32_STM32F4XXX
	select STM32_STM32F410

config ARCH_CHIP_STM32F411CE
	bool "STM32F411CE"
	select STM32_STM32F4XXX
	select STM32_STM32F411

config ARCH_CHIP_STM32F411RE
	bool "STM32F411RE"
	select STM32_STM32F4XXX
	select STM32_STM32F411

config ARCH_CHIP_STM32F411VE
	bool "STM32F411VE"
	select STM32_STM32F4XXX
	select STM32_STM32F411

config ARCH_CHIP_STM32F412CE
	bool "STM32F412CE"
	select STM32_STM32F4XXX
	select STM32_STM32F412

config ARCH_CHIP_STM32F412ZG
	bool "STM32F412ZG"
	select STM32_STM32F4XXX
	select STM32_STM32F412

config ARCH_CHIP_STM32F405RG
	bool "STM32F405RG"
	select STM32_STM32F4XXX
	select STM32_STM32F405

config ARCH_CHIP_STM32F405VG
	bool "STM32F405VG"
	select STM32_STM32F4XXX
	select STM32_STM32F405

config ARCH_CHIP_STM32F405ZG
	bool "STM32F405ZG"
	select STM32_STM32F4XXX
	select STM32_STM32F405

config ARCH_CHIP_STM32F407VE
	bool "STM32F407VE"
	select STM32_STM32F4XXX
	select STM32_STM32F407

config ARCH_CHIP_STM32F407VG
	bool "STM32F407VG"
	select STM32_STM32F4XXX
	select STM32_STM32F407

config ARCH_CHIP_STM32F407ZE
	bool "STM32F407ZE"
	select STM32_STM32F4XXX
	select STM32_STM32F407

config ARCH_CHIP_STM32F407ZG
	bool "STM32F407ZG"
	select STM32_STM32F4XXX
	select STM32_STM32F407

config ARCH_CHIP_STM32F407IE
	bool "STM32F407IE"
	select STM32_STM32F4XXX
	select STM32_STM32F407

config ARCH_CHIP_STM32F407IG
	bool "STM32F407IG"
	select STM32_STM32F4XXX
	select STM32_STM32F407

config ARCH_CHIP_STM32F427V
	bool "STM32F427V"
	select STM32_STM32F4XXX
	select STM32_STM32F427

config ARCH_CHIP_STM32F427Z
	bool "STM32F427Z"
	select STM32_STM32F4XXX
	select STM32_STM32F427

config ARCH_CHIP_STM32F427I
	bool "STM32F427I"
	select STM32_STM32F4XXX
	select STM32_STM32F427

config ARCH_CHIP_STM32F429V
	bool "STM32F429V"
	select STM32_STM32F4XXX
	select STM32_STM32F429

config ARCH_CHIP_STM32F429Z
	bool "STM32F429Z"
	select STM32_STM32F4XXX
	select STM32_STM32F429

config ARCH_CHIP_STM32F429I
	bool "STM32F429I"
	select STM32_STM32F4XXX
	select STM32_STM32F429

config ARCH_CHIP_STM32F429B
	bool "STM32F429B"
	select STM32_STM32F4XXX
	select STM32_STM32F429

config ARCH_CHIP_STM32F429N
	bool "STM32F429N"
	select STM32_STM32F4XXX
	select STM32_STM32F429

config ARCH_CHIP_STM32F446M
	bool "STM32F446M"
	select STM32_STM32F4XXX
	select STM32_STM32F446

config ARCH_CHIP_STM32F446R
	bool "STM32F446R"
	select STM32_STM32F4XXX
	select STM32_STM32F446

config ARCH_CHIP_STM32F446V
	bool "STM32F446V"
	select STM32_STM32F4XXX
	select STM32_STM32F446

config ARCH_CHIP_STM32F446Z
	bool "STM32F446Z"
	select STM32_STM32F4XXX
	select STM32_STM32F446

config ARCH_CHIP_STM32F469A
	bool "STM32F469A"
	select STM32_STM32F4XXX
	select STM32_STM32F469

config ARCH_CHIP_STM32F469I
	bool "STM32F469I"
	select STM32_STM32F4XXX
	select STM32_STM32F469
	select STM32_HAVE_ETHMAC

config ARCH_CHIP_STM32F469B
	bool "STM32F469B"
	select STM32_STM32F4XXX
	select STM32_STM32F469
	select STM32_HAVE_ETHMAC

config ARCH_CHIP_STM32F469N
	bool "STM32F469N"
	select STM32_STM32F4XXX
	select STM32_STM32F469
	select STM32_HAVE_ETHMAC

config ARCH_CHIP_STM32G431K
	bool "STM32G431K"
	select STM32_STM32G43XX
	select STM32_STM32G4XXK
	select STM32_STM32G431K

config ARCH_CHIP_STM32G431C
	bool "STM32G431C"
	select STM32_STM32G43XX
	select STM32_STM32G4XXC
	select STM32_STM32G431C

config ARCH_CHIP_STM32G431R
	bool "STM32G431R"
	select STM32_STM32G43XX
	select STM32_STM32G4XXR
	select STM32_STM32G431R

config ARCH_CHIP_STM32G431M
	bool "STM32G431M"
	select STM32_STM32G43XX
	select STM32_STM32G4XXM
	select STM32_STM32G431M

config ARCH_CHIP_STM32G431V
	bool "STM32G431V"
	select STM32_STM32G43XX
	select STM32_STM32G4XXV
	select STM32_STM32G431V

config ARCH_CHIP_STM32G474C
	bool "STM32G474C"
	select STM32_STM32G47XX
	select STM32_STM32G4XXC
	select STM32_STM32G474C

config ARCH_CHIP_STM32G474M
	bool "STM32G474M"
	select STM32_STM32G47XX
	select STM32_STM32G4XXM
	select STM32_STM32G474M

config ARCH_CHIP_STM32G474R
	bool "STM32G474R"
	select STM32_STM32G47XX
	select STM32_STM32G4XXR
	select STM32_STM32G474R
	select STM32_HAVE_USBFS

config ARCH_CHIP_STM32G474Q
	bool "STM32G474Q"
	select STM32_STM32G47XX
	select STM32_STM32G4XXQ
	select STM32_STM32G474Q

config ARCH_CHIP_STM32G474V
	bool "STM32G474V"
	select STM32_STM32G47XX
	select STM32_STM32G4XXV
	select STM32_STM32G474V

endchoice

choice
	prompt "Override Flash Size Designator"
	default STM32_FLASH_CONFIG_DEFAULT
	depends on ARCH_CHIP_STM32
	---help---
		STM32F/STM32G/STM32L series parts numbering (sans the package type)
		ends with a number or letter that designates the FLASH size.

				Designator  Size in KiB
				   4   	16
				   6   	32
				   8   	64
				   B   	128
				   Z   	192
				   C   	256
				   D   	384
				   E   	512
				   F   	768
				   G   	1024
				   I   	2048

		This configuration option defaults to using the configuration based
		on that designator or the default smaller size if there is no last
		character designator is present in the STM32 Chip Selection.

		Examples:
		   If the STM32F407VE is chosen, the Flash configuration would be
		   'E', if a variant of the part with a 2048 KiB Flash is released
		   in the future one could simply select the 'I' designator here.

		   If an STM32F42xxx or  Series parts is chosen the default Flash
		   configuration will be 'G' and can be set herein to 'I' to choose
		   the larger FLASH part.

config STM32_FLASH_CONFIG_DEFAULT
			bool "Default"

config STM32_FLASH_CONFIG_4
			bool "4 16KiB"

config STM32_FLASH_CONFIG_6
			bool "6 32KiB"

config STM32_FLASH_CONFIG_8
			bool "8 64KiB"

config STM32_FLASH_CONFIG_B
			bool "B 128KiB"

config STM32_FLASH_CONFIG_Z
			bool "Z 192KiB"

config STM32_FLASH_CONFIG_C
			bool "C 256KiB"

config STM32_FLASH_CONFIG_D
			bool "D 384KiB"

config STM32_FLASH_CONFIG_E
			bool "E 512KiB"

config STM32_FLASH_CONFIG_F
			bool "F 768KiB"

config STM32_FLASH_CONFIG_G
			bool "G 1024KiB"

config STM32_FLASH_CONFIG_I
			bool "I 2048KiB"

endchoice

# This is really 15XX/16XX, but we treat the two the same.
config STM32_STM32L15XX
	bool
	default n
	select ARCH_CORTEXM3
	select STM32_ENERGYLITE
	select STM32_HAVE_USBDEV
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3
	select STM32_HAVE_TIM3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM9
	select STM32_HAVE_TIM10
	select STM32_HAVE_TIM11
	select STM32_HAVE_ADC2
	select STM32_HAVE_USART3
	select STM32_HAVE_RTC_SUBSECONDS if !STM32_LOWDENSITY
	select STM32_HAVE_IP_DBGMCU_V2
	select STM32_HAVE_IP_TIMERS_V1
	select STM32_HAVE_IP_ADC_V1
	select STM32_HAVE_IP_DAC_V1
	select STM32_HAVE_IP_DMA_V1
	select STM32_HAVE_IP_I2C_V1

config STM32_ENERGYLITE
	bool
	default n
	select STM32_HAVE_TIM6
	select STM32_HAVE_TIM7

config STM32_STM32F10XX
	bool
	default n
	select ARCH_CORTEXM3
	select STM32_HAVE_SPI2 if STM32_HIGHDENSITY || STM32_MEDIUMDENSITY
	select STM32_HAVE_SPI3 if STM32_HIGHDENSITY || STM32_MEDIUMDENSITY
	select STM32_HAVE_RTC_COUNTER
	select STM32_HAVE_TIM3
	select STM32_HAVE_IP_DBGMCU_V1
	select STM32_HAVE_IP_TIMERS_V1
	select STM32_HAVE_IP_ADC_V1_BASIC
	select STM32_HAVE_IP_DAC_V1
	select STM32_HAVE_IP_DMA_V1
	select STM32_HAVE_IP_I2C_V1

config STM32_VALUELINE
	bool
	default n
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM5
	select STM32_HAVE_TIM6
	select STM32_HAVE_TIM7
	select STM32_HAVE_TIM12
	select STM32_HAVE_TIM13
	select STM32_HAVE_TIM14
	select STM32_HAVE_TIM15
	select STM32_HAVE_TIM16
	select STM32_HAVE_TIM17
	select STM32_HAVE_SPI2 if STM32_HIGHDENSITY
	select STM32_HAVE_SPI3 if STM32_HIGHDENSITY

config STM32_CONNECTIVITYLINE
	bool
	default n
	select STM32_HAVE_OTGFS
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM5
	select STM32_HAVE_TIM6
	select STM32_HAVE_TIM7
	select STM32_HAVE_ADC2
	select STM32_HAVE_CAN1
	select STM32_HAVE_CAN2
	select STM32_HAVE_ETHMAC
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3

config STM32_PERFORMANCELINE
	bool
	default n
	select STM32_HAVE_USBDEV
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM5
	select STM32_HAVE_TIM6
	select STM32_HAVE_TIM7
	select STM32_HAVE_TIM8
	select STM32_HAVE_ADC2
	select STM32_HAVE_CAN1

config STM32_USBACCESSLINE
	bool
	default n
	select STM32_HAVE_USBDEV
	select STM32_HAVE_FSMC
	select STM32_HAVE_USART3
	select STM32_HAVE_SPI2

config STM32_MEDIUMPLUSDENSITY
	bool
	default n

config STM32_HIGHDENSITY
	bool
	default n
	select STM32_HAVE_FSMC
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM5
	select STM32_HAVE_TIM6
	select STM32_HAVE_TIM7
	select STM32_HAVE_TIM8
	select STM32_HAVE_ADC2
	select STM32_HAVE_ADC3
	select STM32_HAVE_CAN1

config STM32_MEDIUMDENSITY
	bool
	default n
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM5
	select STM32_HAVE_TIM6
	select STM32_HAVE_TIM7
	select STM32_HAVE_TIM8
	select STM32_HAVE_ADC2
	select STM32_HAVE_ADC3
	select STM32_HAVE_CAN1

config STM32_LOWDENSITY
	bool
	default n
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM5
	select STM32_HAVE_TIM6
	select STM32_HAVE_TIM7
	select STM32_HAVE_TIM8
	select STM32_HAVE_ADC2
	select STM32_HAVE_CAN1 if !STM32_VALUELINE

config STM32_STM32F20XX
	bool
	default n
	select ARCH_CORTEXM3
	select STM32_HAVE_FLASH_ICACHE
	select STM32_HAVE_FLASH_DCACHE
	select STM32_HAVE_CRYP
	select STM32_HAVE_OTGFS
	select STM32_HAVE_OTGHS
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5
	select STM32_HAVE_USART6
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM5
	select STM32_HAVE_TIM6
	select STM32_HAVE_TIM7
	select STM32_HAVE_TIM8
	select STM32_HAVE_TIM9
	select STM32_HAVE_TIM10
	select STM32_HAVE_TIM11
	select STM32_HAVE_TIM12
	select STM32_HAVE_TIM13
	select STM32_HAVE_TIM14
	select STM32_HAVE_ADC2
	select STM32_HAVE_ADC3
	select STM32_HAVE_DAC1
	select STM32_HAVE_I2C2
	select STM32_HAVE_I2C3
	select STM32_HAVE_CAN1
	select STM32_HAVE_CAN2
	select STM32_HAVE_RNG
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3
	select STM32_HAVE_IOCOMPENSATION
	select STM32_HAVE_IP_DBGMCU_V2
	select STM32_HAVE_IP_TIMERS_V1
	select STM32_HAVE_IP_ADC_V1
	select STM32_HAVE_IP_DAC_V1
	select STM32_HAVE_IP_DMA_V2
	select STM32_HAVE_IP_I2C_V1

config STM32_STM32F205
	bool
	default n

config STM32_STM32F207
	bool
	default n
	select STM32_HAVE_FSMC
	select STM32_HAVE_ETHMAC

config STM32_STM32F30XX
	bool
	default n
	select ARCH_CORTEXM4
	select ARCH_HAVE_FPU
	select STM32_HAVE_ADC1_DMA
	select STM32_HAVE_CAN1
	select STM32_HAVE_DAC1
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM3
	select STM32_HAVE_TIM6
	select STM32_HAVE_TIM15
	select STM32_HAVE_TIM16
	select STM32_HAVE_TIM17
	select STM32_HAVE_TSC
	select STM32_HAVE_IP_DBGMCU_V2
	select STM32_HAVE_IP_TIMERS_V2
	select STM32_HAVE_IP_ADC_V2
	select STM32_HAVE_IP_DAC_V1
	select STM32_HAVE_IP_DMA_V1
	select STM32_HAVE_IP_I2C_V2

config STM32_STM32F302
	bool
	default n
	select STM32_HAVE_ADC2
	select STM32_HAVE_ADC2_DMA
	select STM32_HAVE_I2C2
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3
	select STM32_HAVE_TIM4
	select STM32_HAVE_USBDEV

config STM32_STM32F303
	bool
	default n
	select STM32_HAVE_ADC2
	select STM32_HAVE_ADC2_DMA
	select STM32_HAVE_CCM
	select STM32_HAVE_TIM7

config STM32_STM32F33XX
	bool
	default n
	select ARCH_CORTEXM4
	select ARCH_HAVE_FPU
	select STM32_HAVE_HRTIM1
	select STM32_HAVE_COMP2
	select STM32_HAVE_COMP4
	select STM32_HAVE_COMP6
	select STM32_HAVE_OPAMP2
	select STM32_HAVE_CCM
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM6
	select STM32_HAVE_TIM7
	select STM32_HAVE_TIM15
	select STM32_HAVE_TIM16
	select STM32_HAVE_TIM17
	select STM32_HAVE_TSC
	select STM32_HAVE_ADC2
	select STM32_HAVE_ADC1_DMA
	select STM32_HAVE_ADC2_DMA
	select STM32_HAVE_CAN1
	select STM32_HAVE_DAC1
	select STM32_HAVE_DAC2
	select STM32_HAVE_USART3
	select STM32_HAVE_IP_DBGMCU_V2
	select STM32_HAVE_IP_TIMERS_V2
	select STM32_HAVE_IP_ADC_V2
	select STM32_HAVE_IP_COMP_V1
	select STM32_HAVE_IP_DAC_V1
	select STM32_HAVE_IP_DMA_V1
	select STM32_HAVE_IP_I2C_V2

config STM32_STM32F37XX
	bool
	default n
	select ARCH_CORTEXM4
	select ARCH_HAVE_FPU
	select STM32_HAVE_USBDEV
	select STM32_HAVE_TIM3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM5
	select STM32_HAVE_TIM6
	select STM32_HAVE_TIM7
	select STM32_HAVE_TIM15
	select STM32_HAVE_TIM16
	select STM32_HAVE_TIM17
	select STM32_HAVE_TSC
	select STM32_HAVE_SDADC1
	select STM32_HAVE_SDADC2
	select STM32_HAVE_SDADC3
	select STM32_HAVE_CAN1
	select STM32_HAVE_DAC1
	select STM32_HAVE_DAC2
	select STM32_HAVE_I2C2
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3
	select STM32_HAVE_USART3
	select STM32_HAVE_IP_TIMERS_V1
	select STM32_HAVE_IP_ADC_V1_BASIC
	select STM32_HAVE_IP_DAC_V1
	select STM32_HAVE_IP_DMA_V1
	select STM32_HAVE_IP_I2C_V2

config STM32_STM32F4XXX
	bool
	default n
	select ARCH_CORTEXM4
	select ARCH_HAVE_FPU
	select STM32_HAVE_FLASH_ICACHE
	select STM32_HAVE_FLASH_DCACHE
	select STM32_HAVE_CRYP
	select STM32_HAVE_SPI2
	select STM32_HAVE_I2C2
	select STM32_HAVE_IOCOMPENSATION
	select STM32_HAVE_IP_DBGMCU_V2
	select STM32_HAVE_IP_TIMERS_V1
	select STM32_HAVE_IP_ADC_V1
	select STM32_HAVE_IP_DAC_V1
	select STM32_HAVE_IP_DMA_V2
	select STM32_HAVE_IP_I2C_V1

config STM32_STM32F401xBC
	bool
	default n
	select STM32_STM32F401

config STM32_STM32F401xDE
	bool
	default n
	select STM32_STM32F401

config STM32_STM32F401
	bool
	default n
	select ARCH_CORTEXM4
	select STM32_STM32F4XXX
	select STM32_HAVE_USART6
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM5
	select STM32_HAVE_TIM9
	select STM32_HAVE_TIM10
	select STM32_HAVE_TIM11
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3
	select STM32_HAVE_I2S3
	select STM32_HAVE_I2C3
	select STM32_HAVE_OTGFS

config STM32_STM32F410
	bool
	default n
	select STM32_HAVE_USART6
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM5
	select STM32_HAVE_TIM6
	select STM32_HAVE_TIM9
	select STM32_HAVE_TIM11
	select STM32_HAVE_SPI5
	select STM32_HAVE_DAC1

config STM32_STM32F411
	bool
	default n
	select STM32_HAVE_USART6
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM5
	select STM32_HAVE_TIM9
	select STM32_HAVE_TIM10
	select STM32_HAVE_TIM11
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3
	select STM32_HAVE_SPI4
	select STM32_HAVE_SPI5
	select STM32_HAVE_I2S3
	select STM32_HAVE_I2C3
	select STM32_HAVE_OTGFS

config STM32_STM32F412
	bool
	default n
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM2
	select STM32_HAVE_TIM3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM5
	select STM32_HAVE_TIM8
	select STM32_HAVE_TIM9
	select STM32_HAVE_TIM12
	select STM32_HAVE_TIM13
	select STM32_HAVE_TIM14
	select STM32_HAVE_USART2
	select STM32_HAVE_USART6
	select STM32_HAVE_I2C1
	select STM32_HAVE_I2C2
	select STM32_HAVE_I2C3
	select STM32_HAVE_SPI1
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3
	select STM32_HAVE_CAN1
	select STM32_HAVE_CAN2
	select STM32_HAVE_OTGFS
	select STM32_HAVE_I2SPLL

config STM32_STM32F405
	bool
	default n
	select STM32_HAVE_FSMC
	select STM32_HAVE_CCM
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5
	select STM32_HAVE_USART6
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM5
	select STM32_HAVE_TIM6
	select STM32_HAVE_TIM7
	select STM32_HAVE_TIM8
	select STM32_HAVE_TIM9
	select STM32_HAVE_TIM10
	select STM32_HAVE_TIM11
	select STM32_HAVE_TIM12
	select STM32_HAVE_TIM13
	select STM32_HAVE_TIM14
	select STM32_HAVE_ADC2
	select STM32_HAVE_ADC3
	select STM32_HAVE_CAN1
	select STM32_HAVE_CAN2
	select STM32_HAVE_DAC1
	select STM32_HAVE_DAC2
	select STM32_HAVE_SPI3
	select STM32_HAVE_I2S3
	select STM32_HAVE_I2C3
	select STM32_HAVE_RNG
	select STM32_HAVE_OTGFS

config STM32_STM32F407
	bool
	default n
	select STM32_HAVE_FSMC
	select STM32_HAVE_CCM
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5
	select STM32_HAVE_USART6
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM2
	select STM32_HAVE_TIM3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM5
	select STM32_HAVE_TIM6
	select STM32_HAVE_TIM7
	select STM32_HAVE_TIM8
	select STM32_HAVE_TIM9
	select STM32_HAVE_TIM10
	select STM32_HAVE_TIM11
	select STM32_HAVE_TIM12
	select STM32_HAVE_TIM13
	select STM32_HAVE_TIM14
	select STM32_HAVE_ADC2
	select STM32_HAVE_ADC3
	select STM32_HAVE_CAN1
	select STM32_HAVE_CAN2
	select STM32_HAVE_DAC1
	select STM32_HAVE_SPI3
	select STM32_HAVE_I2S3
	select STM32_HAVE_I2C3
	select STM32_HAVE_RNG
	select STM32_HAVE_ETHMAC
	select STM32_HAVE_OTGFS

# This is really 427/437, but we treat the two the same.

config STM32_STM32F427
	bool
	default n
	select STM32_HAVE_OVERDRIVE
	select STM32_HAVE_FMC
	select STM32_HAVE_CCM
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5
	select STM32_HAVE_USART6
	select STM32_HAVE_UART7
	select STM32_HAVE_UART8
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM5
	select STM32_HAVE_TIM6
	select STM32_HAVE_TIM7
	select STM32_HAVE_TIM8
	select STM32_HAVE_TIM9
	select STM32_HAVE_TIM10
	select STM32_HAVE_TIM11
	select STM32_HAVE_TIM12
	select STM32_HAVE_TIM13
	select STM32_HAVE_TIM14
	select STM32_HAVE_ADC2
	select STM32_HAVE_ADC3
	select STM32_HAVE_CAN1
	select STM32_HAVE_CAN2
	select STM32_HAVE_DAC1
	select STM32_HAVE_RNG
	select STM32_HAVE_ETHMAC
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3
	select STM32_HAVE_SPI4
	select STM32_HAVE_SPI5
	select STM32_HAVE_I2S3
	select STM32_HAVE_I2C3
	select STM32_HAVE_OTGFS
	select STM32_HAVE_SPI6
	select STM32_HAVE_I2SPLL

# This is really 429/439, but we treat the two the same.

config STM32_STM32F429
	bool
	default n
	select STM32_HAVE_OVERDRIVE
	select STM32_HAVE_FMC
	select STM32_HAVE_LTDC
	select STM32_HAVE_CCM
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5
	select STM32_HAVE_USART6
	select STM32_HAVE_UART7
	select STM32_HAVE_UART8
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM5
	select STM32_HAVE_TIM6
	select STM32_HAVE_TIM7
	select STM32_HAVE_TIM8
	select STM32_HAVE_TIM9
	select STM32_HAVE_TIM10
	select STM32_HAVE_TIM11
	select STM32_HAVE_TIM12
	select STM32_HAVE_TIM13
	select STM32_HAVE_TIM14
	select STM32_HAVE_ADC2
	select STM32_HAVE_ADC3
	select STM32_HAVE_CAN1
	select STM32_HAVE_CAN2
	select STM32_HAVE_DAC1
	select STM32_HAVE_RNG
	select STM32_HAVE_ETHMAC
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3
	select STM32_HAVE_I2S3
	select STM32_HAVE_SPI4
	select STM32_HAVE_SPI5
	select STM32_HAVE_SPI6
	select STM32_HAVE_I2S3
	select STM32_HAVE_I2C3
	select STM32_HAVE_OTGFS

config STM32_STM32F446
	bool
	default n
	select STM32_HAVE_OVERDRIVE
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5
	select STM32_HAVE_USART6
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM2
	select STM32_HAVE_TIM3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM5
	select STM32_HAVE_TIM6
	select STM32_HAVE_TIM7
	select STM32_HAVE_TIM8
	select STM32_HAVE_TIM9
	select STM32_HAVE_TIM10
	select STM32_HAVE_TIM11
	select STM32_HAVE_TIM12
	select STM32_HAVE_TIM13
	select STM32_HAVE_TIM14
	select STM32_HAVE_ADC2
	select STM32_HAVE_ADC3
	select STM32_HAVE_CAN1
	select STM32_HAVE_CAN2
	select STM32_HAVE_DAC1
	select STM32_HAVE_SPI3
	select STM32_HAVE_SPI4
	select STM32_HAVE_I2S3
	select STM32_HAVE_I2C3
	select STM32_HAVE_OTGFS
	select STM32_HAVE_SAIPLL
	select STM32_HAVE_I2SPLL

# This is really 469/479, but we treat the two the same.

config STM32_STM32F469
	bool
	default n
	select STM32_HAVE_OVERDRIVE
	select STM32_HAVE_FMC
	select STM32_HAVE_LTDC
	select STM32_HAVE_CCM
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5
	select STM32_HAVE_USART6
	select STM32_HAVE_UART7
	select STM32_HAVE_UART8
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM2
	select STM32_HAVE_TIM3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM5
	select STM32_HAVE_TIM6
	select STM32_HAVE_TIM7
	select STM32_HAVE_TIM8
	select STM32_HAVE_TIM9
	select STM32_HAVE_TIM10
	select STM32_HAVE_TIM11
	select STM32_HAVE_TIM12
	select STM32_HAVE_TIM13
	select STM32_HAVE_TIM14
	select STM32_HAVE_ADC2
	select STM32_HAVE_ADC3
	select STM32_HAVE_CAN1
	select STM32_HAVE_CAN2
	select STM32_HAVE_DAC1
	select STM32_HAVE_RNG
	select STM32_HAVE_SPI3
	select STM32_HAVE_SPI4
	select STM32_HAVE_SPI5
	select STM32_HAVE_SPI6
	select STM32_HAVE_OTGFS
	select STM32_HAVE_SAIPLL
	select STM32_HAVE_I2SPLL
	select STM32_HAVE_I2S3
	select STM32_HAVE_I2C3

config STM32_STM32G4XXX
	bool
	default n
	select ARCH_CORTEXM4
	select ARCH_HAVE_FPU
	select STM32_HAVE_DMAMUX
	select STM32_HAVE_IP_DBGMCU_V3
	select STM32_HAVE_IP_ADC_V2
	select STM32_HAVE_IP_COMP_V2
	select STM32_HAVE_IP_DAC_V2
	select STM32_HAVE_IP_DMA_V1
	select STM32_HAVE_IP_I2C_V2
	select STM32_HAVE_IP_TIMERS_V3

config STM32_STM32G4_CAT2
	bool
	default n

config STM32_STM32G4_CAT3
	bool
	default n

config STM32_STM32G4_CAT4
	bool
	default n

config STM32_STM32G4XXK
	bool
	default n

config STM32_STM32G4XXC
	bool
	default n

config STM32_STM32G4XXR
	bool
	default n

config STM32_STM32G4XXM
	bool
	default n

config STM32_STM32G4XXV
	bool
	default n

config STM32_STM32G4XXP
	bool
	default n

config STM32_STM32G4XXQ
	bool
	default n

config STM32_STM32G43XX
	bool
	default n
	select STM32_STM32G4XXX
	select STM32_STM32G4_CAT2
	select STM32_HAVE_ADC2
	select STM32_HAVE_CCM
	select STM32_HAVE_COMP1
	select STM32_HAVE_COMP2
	select STM32_HAVE_COMP3
	select STM32_HAVE_COMP4
	select STM32_HAVE_CORDIC
	select STM32_HAVE_CRS
	select STM32_HAVE_DAC1
	select STM32_HAVE_DAC3
	select STM32_HAVE_FMAC
	select STM32_HAVE_FDCAN1
	select STM32_HAVE_I2C2
	select STM32_HAVE_I2C3
	select STM32_HAVE_LPTIM1
	select STM32_HAVE_LPUART1
	select STM32_HAVE_OPAMP1
	select STM32_HAVE_OPAMP2
	select STM32_HAVE_OPAMP3
	select STM32_HAVE_RNG
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM15
	select STM32_HAVE_TIM16
	select STM32_HAVE_TIM17
	select STM32_HAVE_TIM2
	select STM32_HAVE_TIM3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM8
	select STM32_HAVE_UCPD
	select STM32_HAVE_USBDEV

config STM32_STM32G431K
	bool
	default n

config STM32_STM32G431C
	bool
	default n
	select STM32_HAVE_USART3

config STM32_STM32G431R
	bool
	default n
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4

config STM32_STM32G431M
	bool
	default n
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4

config STM32_STM32G431V
	bool
	default n
	select STM32_HAVE_USART3
	select STM32_HAVE_UART4

config STM32_STM32G47XX
	bool
	default n
	select STM32_STM32G4XXX
	select STM32_STM32G4_CAT3
	select STM32_HAVE_ADC2
	select STM32_HAVE_ADC3
	select STM32_HAVE_ADC4
	select STM32_HAVE_ADC5
	select STM32_HAVE_CCM
	select STM32_HAVE_COMP1
	select STM32_HAVE_COMP2
	select STM32_HAVE_COMP3
	select STM32_HAVE_COMP4
	select STM32_HAVE_COMP5
	select STM32_HAVE_COMP6
	select STM32_HAVE_COMP7
	select STM32_HAVE_CORDIC
	select STM32_HAVE_CRS
	select STM32_HAVE_DAC1
	select STM32_HAVE_DAC2
	select STM32_HAVE_DAC3
	select STM32_HAVE_DAC4
	select STM32_HAVE_DMA1_CHAN8
	select STM32_HAVE_DMA2_CHAN678
	select STM32_HAVE_FSMC
	select STM32_HAVE_FMAC
	select STM32_HAVE_FDCAN1
	select STM32_HAVE_FDCAN2
	select STM32_HAVE_HRTIM1
	select STM32_HAVE_I2C2
	select STM32_HAVE_I2C3
	select STM32_HAVE_I2C4
	select STM32_HAVE_I2S3
	select STM32_HAVE_LPTIM1
	select STM32_HAVE_LPUART1
	select STM32_HAVE_OPAMP1
	select STM32_HAVE_OPAMP2
	select STM32_HAVE_OPAMP3
	select STM32_HAVE_OPAMP4
	select STM32_HAVE_OPAMP5
	select STM32_HAVE_OPAMP6
	select STM32_HAVE_QSPI
	select STM32_HAVE_RNG
	select STM32_HAVE_SPI2
	select STM32_HAVE_SPI3
	select STM32_HAVE_TIM1
	select STM32_HAVE_TIM15
	select STM32_HAVE_TIM16
	select STM32_HAVE_TIM17
	select STM32_HAVE_TIM2
	select STM32_HAVE_TIM20
	select STM32_HAVE_TIM3
	select STM32_HAVE_TIM4
	select STM32_HAVE_TIM5
	select STM32_HAVE_TIM8
	select STM32_HAVE_USART3
	select STM32_HAVE_UCPD
	select STM32_HAVE_USBDEV

config STM32_STM32G474C
	bool
	default n
	select STM32_HAVE_FDCAN3

config STM32_STM32G474M
	bool
	default n
	select STM32_HAVE_FDCAN3
	select STM32_HAVE_SPI4
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5

config STM32_STM32G474R
	bool
	default n
	select STM32_HAVE_FDCAN3
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5

config STM32_STM32G474Q
	bool
	default n
	select STM32_HAVE_FDCAN3
	select STM32_HAVE_FMC
	select STM32_HAVE_SPI4
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5

config STM32_STM32G474V
	bool
	default n
	select STM32_HAVE_FDCAN3
	select STM32_HAVE_FMC
	select STM32_HAVE_SPI4
	select STM32_HAVE_UART4
	select STM32_HAVE_UART5

config STM32_DFU
	bool "DFU bootloader"
	default n
	depends on !STM32_VALUELINE
	---help---
		Configure and position code for use with the STMicro DFU bootloader.  Do
		not select this option if you will load code using JTAG/SWM.

menu "STM32 Peripheral Support"

# These "hidden" settings determine whether a peripheral option is available
# for the selected MCU

config STM32_HAVE_FLASH_ICACHE
	bool
	default n

config STM32_HAVE_FLASH_DCACHE
	bool
	default n

config STM32_HAVE_OVERDRIVE
	bool
	default n

config STM32_HAVE_AES
	bool
	default n

config STM32_HAVE_CRYP
	bool
	default n

config STM32_HAVE_CCM
	bool
	default n

config STM32_HAVE_DMA1_CHAN8
	bool
	default n

config STM32_HAVE_DMA2_CHAN678
	bool
	default n

config STM32_HAVE_DMAMUX
	bool
	default n

config STM32_HAVE_UCPD
	bool
	default n

config STM32_HAVE_USBDEV
	bool
	default n

config STM32_HAVE_USBFS
	bool
	default n

config STM32_HAVE_OTGFS
	bool
	default n

config STM32_HAVE_FMC
	bool
	default n

config STM32_HAVE_FMAC
	bool
	default n

config STM32_HAVE_FSMC
	bool
	default n

config STM32_HAVE_FDCAN1
	bool
	default n

config STM32_HAVE_FDCAN2
	bool
	default n

config STM32_HAVE_FDCAN3
	bool
	default n

config STM32_HAVE_IOCOMPENSATION
	bool
	default n

config STM32_HAVE_HRTIM1
	bool
	default n

config STM32_HAVE_LTDC
	bool
	default n

config STM32_HAVE_USART3
	bool
	default n

config STM32_HAVE_UART4
	bool
	default n

config STM32_HAVE_UART5
	bool
	default n

config STM32_HAVE_USART6
	bool
	default n

config STM32_HAVE_UART7
	bool
	default n

config STM32_HAVE_UART8
	bool
	default n

config STM32_HAVE_TIM1
	bool
	default n

config STM32_HAVE_TIM2
	bool
	default n

config STM32_HAVE_TIM3
	bool
	default n

config STM32_HAVE_TIM4
	bool
	default n

config STM32_HAVE_TIM5
	bool
	default n

config STM32_HAVE_TIM6
	bool
	default n

config STM32_HAVE_TIM7
	bool
	default n

config STM32_HAVE_TIM8
	bool
	default n

config STM32_HAVE_TIM9
	bool
	default n

config STM32_HAVE_TIM10
	bool
	default n

config STM32_HAVE_TIM11
	bool
	default n

config STM32_HAVE_TIM12
	bool
	default n

config STM32_HAVE_TIM13
	bool
	default n

config STM32_HAVE_TIM14
	bool
	default n

config STM32_HAVE_TIM15
	bool
	default n

config STM32_HAVE_TIM16
	bool
	default n

config STM32_HAVE_TIM17
	bool
	default n

config STM32_HAVE_TIM18
	bool
	default n

config STM32_HAVE_TIM19
	bool
	default n

config STM32_HAVE_TIM20
	bool
	default n

config STM32_HAVE_TSC
	bool
	default n

config STM32_HAVE_ADC1
	bool
	default y

config STM32_HAVE_ADC2
	bool
	default n

config STM32_HAVE_ADC3
	bool
	default n

config STM32_HAVE_ADC4
	bool
	default n

config STM32_HAVE_ADC5
	bool
	default n

config STM32_HAVE_ADC1_DMA
	bool
	default n

config STM32_HAVE_ADC2_DMA
	bool
	default n

config STM32_HAVE_ADC3_DMA
	bool
	default n

config STM32_HAVE_ADC4_DMA
	bool
	default n

config STM32_HAVE_ADC5_DMA
	bool
	default n

config STM32_HAVE_SDADC1
	bool
	default n

config STM32_HAVE_SDADC2
	bool
	default n

config STM32_HAVE_SDADC3
	bool
	default n

config STM32_HAVE_SDADC1_DMA
	bool
	default n

config STM32_HAVE_SDADC2_DMA
	bool
	default n

config STM32_HAVE_SDADC3_DMA
	bool
	default n

config STM32_HAVE_CAN1
	bool
	default n

config STM32_HAVE_CAN2
	bool
	default n

config STM32_HAVE_COMP1
	bool
	default n

config STM32_HAVE_COMP2
	bool
	default n

config STM32_HAVE_COMP3
	bool
	default n

config STM32_HAVE_COMP4
	bool
	default n

config STM32_HAVE_COMP5
	bool
	default n

config STM32_HAVE_COMP6
	bool
	default n

config STM32_HAVE_COMP7
	bool
	default n

config STM32_HAVE_CORDIC
	bool
	default n

config STM32_HAVE_CRS
	bool
	default n

config STM32_HAVE_DAC1
	bool
	default n

config STM32_HAVE_DAC2
	bool
	default n

config STM32_HAVE_DAC3
	bool
	default n

config STM32_HAVE_DAC4
	bool
	default n

config STM32_HAVE_QSPI
	bool
	default n

config STM32_HAVE_RNG
	bool
	default n

config STM32_HAVE_ETHMAC
	bool
	default n

config STM32_HAVE_I2C2
	bool
	default n

config STM32_HAVE_I2C3
	bool
	default n

config STM32_HAVE_I2C4
	bool
	default n

config STM32_HAVE_LPTIM1
	bool
	default n

config STM32_HAVE_LPUART1
	bool
	default n

config STM32_HAVE_SPI2
	bool
	default n

config STM32_HAVE_SPI3
	bool
	default n

config STM32_HAVE_I2S3
	bool
	default n

config STM32_HAVE_SPI4
	bool
	default n

config STM32_HAVE_SPI5
	bool
	default n

config STM32_HAVE_SPI6
	bool
	default n

config STM32_HAVE_SAIPLL
	bool
	default n

config STM32_HAVE_I2SPLL
	bool
	default n

config STM32_HAVE_OPAMP1
	bool
	default n

config STM32_HAVE_OPAMP2
	bool
	default n

config STM32_HAVE_OPAMP3
	bool
	default n

config STM32_HAVE_OPAMP4
	bool
	default n

config STM32_HAVE_OPAMP5
	bool
	default n

config STM32_HAVE_OPAMP6
	bool
	default n

# These are STM32 peripherals IP blocks

config STM32_HAVE_IP_DBGMCU_V1
	bool
	default n

config STM32_HAVE_IP_DBGMCU_V2
	bool
	default n

config STM32_HAVE_IP_DBGMCU_V3
	bool
	default n

config STM32_HAVE_IP_I2C_V1
	bool
	default n

config STM32_HAVE_IP_I2C_V2
	bool
	default n

config STM32_HAVE_IP_DMA_V1
	bool
	default n

config STM32_HAVE_IP_DMA_V2
	bool
	default n

config STM32_HAVE_IP_TIMERS_V1
	bool
	default n

config STM32_HAVE_IP_TIMERS_V2
	bool
	default n

config STM32_HAVE_IP_TIMERS_V3
	bool
	default n

config STM32_HAVE_IP_ADC_V1
	bool
	default n

config STM32_HAVE_IP_ADC_V1_BASIC
	bool
	default n
	select STM32_HAVE_IP_ADC_V1

config STM32_HAVE_IP_ADC_V2
	bool
	default n

config STM32_HAVE_IP_ADC_V2_BASIC
	bool
	default n
	select STM32_HAVE_IP_ADC_V2

config STM32_HAVE_IP_COMP_V1
	bool
	default n

config STM32_HAVE_IP_COMP_V2
	bool
	default n

config STM32_HAVE_IP_DAC_V1
	bool
	default n

config STM32_HAVE_IP_DAC_V2
	bool
	default n

# These are the peripheral selections proper

config STM32_ADC1
	bool "ADC1"
	default n
	select STM32_ADC
	select STM32_HAVE_ADC1_DMA if STM32_STM32F10XX && STM32_DMA1
	select STM32_HAVE_ADC1_DMA if STM32_STM32F37XX && STM32_DMA1
	select STM32_HAVE_ADC1_DMA if !STM32_STM32F10XX && STM32_DMA2
	select STM32_HAVE_ADC1_DMA if STM32_DMAMUX

config STM32_ADC2
	bool "ADC2"
	default n
	select STM32_ADC
	depends on STM32_HAVE_ADC2
	select STM32_HAVE_ADC2_DMA if STM32_DMA2
	select STM32_HAVE_ADC2_DMA if STM32_DMAMUX

config STM32_ADC3
	bool "ADC3"
	default n
	select STM32_ADC
	depends on STM32_HAVE_ADC3
	select STM32_HAVE_ADC3_DMA if STM32_DMA2
	select STM32_HAVE_ADC3_DMA if STM32_DMAMUX

config STM32_ADC4
	bool "ADC4"
	default n
	select STM32_ADC
	depends on STM32_HAVE_ADC4
	select STM32_HAVE_ADC4_DMA if STM32_DMA2
	select STM32_HAVE_ADC4_DMA if STM32_DMAMUX

config STM32_ADC5
	bool "ADC5"
	default n
	select STM32_ADC
	depends on STM32_HAVE_ADC5
	select STM32_HAVE_ADC5_DMA if STM32_DMA2
	select STM32_HAVE_ADC5_DMA if STM32_DMAMUX

config STM32_SDADC1
	bool "SDADC1"
	default n
	select STM32_SDADC
	depends on STM32_HAVE_SDADC1
	select STM32_HAVE_SDADC1_DMA if STM32_DMA2

config STM32_SDADC2
	bool "SDADC2"
	default n
	select STM32_SDADC
	depends on STM32_HAVE_SDADC2
	select STM32_HAVE_SDADC2_DMA if STM32_DMA2

config STM32_SDADC3
	bool "SDADC3"
	default n
	select STM32_SDADC
	depends on STM32_HAVE_SDADC3
	select STM32_HAVE_SDADC3_DMA if STM32_DMA2

config STM32_COMP1
	bool "COMP1"
	default n
	select STM32_COMP
	depends on STM32_HAVE_COMP1

config STM32_COMP2
	bool "COMP2"
	default n
	select STM32_COMP
	depends on STM32_HAVE_COMP2

config STM32_COMP3
	bool "COMP3"
	default n
	select STM32_COMP
	depends on STM32_HAVE_COMP3

config STM32_COMP4
	bool "COMP4"
	default n
	select STM32_COMP
	depends on STM32_HAVE_COMP4

config STM32_COMP5
	bool "COMP5"
	default n
	select STM32_COMP
	depends on STM32_HAVE_COMP5

config STM32_COMP6
	bool "COMP6"
	default n
	select STM32_COMP
	depends on STM32_HAVE_COMP6

config STM32_COMP7
	bool "COMP7"
	default n
	select STM32_COMP
	depends on STM32_HAVE_COMP7

config STM32_CORDIC
	bool "CORDIC Accelerator"
	default n
	depends on STM32_HAVE_CORDIC
	select MATH_CORDIC_USE_Q31

config STM32_BKP
	bool "BKP"
	default n
	depends on STM32_STM32F10XX

config STM32_BKPSRAM
	bool "Enable BKP RAM Domain"
	default n
	depends on STM32_STM32F20XX || STM32_STM32F4XXX

config STM32_CAN1
	bool "CAN1"
	default n
	select STM32_CAN
	depends on STM32_HAVE_CAN1

config STM32_CAN2
	bool "CAN2"
	default n
	select STM32_CAN
	depends on STM32_HAVE_CAN2

config STM32_CCMDATARAM
	bool "CMD/DATA RAM"
	default n
	depends on STM32_STM32F4XXX

config STM32_AES
	bool "128-bit AES"
	default n
	depends on STM32_HAVE_AES
	select CRYPTO_AES192_DISABLE if CRYPTO_ALGTEST
	select CRYPTO_AES256_DISABLE if CRYPTO_ALGTEST

config STM32_CEC
	bool "CEC"
	default n
	depends on STM32_VALUELINE

config STM32_CRC
	bool "CRC"
	default n

config STM32_CRS
	bool "CRS (Clock Recovery System)"
	default n
	depends on STM32_HAVE_CRS

config STM32_CRYP
	bool "CRYP"
	default n
	depends on STM32_HAVE_CRYP

config STM32_DMA1
	bool "DMA1"
	default n
	select STM32_DMA
	select ARCH_DMA

config STM32_DMA2
	bool "DMA2"
	default n
	select STM32_DMA
	select ARCH_DMA
	depends on !STM32_VALUELINE || (STM32_VALUELINE && STM32_HIGHDENSITY)

config STM32_DMAMUX1
	bool "DMAMUX1"
	default n
	depends on STM32_HAVE_DMAMUX
	select STM32_DMAMUX

config STM32_DAC1
	bool "DAC1"
	default n
	depends on STM32_HAVE_DAC1
	select STM32_DAC

if STM32_DAC1

config STM32_DAC1CH1
	bool "DAC1CH1"
	default n

config STM32_DAC1CH2
	bool "DAC1CH2"
	default n

endif #STM32_DAC1

config STM32_DAC2
	bool "DAC2"
	default n
	depends on STM32_HAVE_DAC2
	select STM32_DAC

if STM32_DAC2

config STM32_DAC2CH1
	bool "DAC2CH1"
	default n

endif #STM32_DAC2

config STM32_DAC3
	bool "DAC3"
	default n
	depends on STM32_HAVE_DAC3
	select STM32_DAC

if STM32_DAC3

config STM32_DAC3CH1
	bool "DAC3CH1 Internal"
	default n

config STM32_DAC3CH2
	bool "DAC3CH2 Internal"
	default n

endif #STM32_DAC3

config STM32_DAC4
	bool "DAC4"
	default n
	depends on STM32_HAVE_DAC4
	select STM32_DAC

if STM32_DAC4

config STM32_DAC4CH1
	bool "DAC4CH1 Internal"
	default n

config STM32_DAC4CH2
	bool "DAC4CH2 Internal"
	default n

endif #STM32_DAC4

config STM32_DCMI
	bool "DCMI"
	default n
	depends on STM32_STM32F20XX || STM32_STM32F4XXX

config STM32_ETHMAC
	bool "Ethernet MAC"
	default n
	depends on STM32_HAVE_ETHMAC
	select NETDEVICES
	select ARCH_HAVE_PHY

config STM32_FDCAN1
	bool "FDCAN1"
	default n
	depends on STM32_HAVE_FDCAN1
	select STM32_FDCAN

config STM32_FDCAN2
	bool "FDCAN2"
	default n
	depends on STM32_HAVE_FDCAN2
	select STM32_FDCAN

config STM32_FDCAN3
	bool "FDCAN3"
	default n
	depends on STM32_HAVE_FDCAN3
	select STM32_FDCAN

config STM32_FSMC
	bool "FSMC"
	default n
	depends on STM32_HAVE_FSMC

config STM32_FMC
	bool "FMC"
	default n
	depends on STM32_HAVE_FMC

config STM32_FMAC
	bool "FMAC (Filter Math Accelerator)"
	default n
	depends on STM32_HAVE_FMAC

config STM32_HASH
	bool "HASH"
	default n
	depends on STM32_STM32F20XX || STM32_STM32F4XXX

config STM32_HRTIM
	bool
	default n

config STM32_HRTIM1
	bool "HRTIM1"
	default n
	depends on STM32_HAVE_HRTIM1
	select STM32_HRTIM

if STM32_HRTIM1

config STM32_HRTIM_MASTER
	bool "HRTIM MASTER"
	default n
	---help---
		Enable HRTIM Master Timer

config STM32_HRTIM_TIMA
	bool "HRTIM TIMA"
	default n
	---help---
		Enable HRTIM Timer A

config STM32_HRTIM_TIMB
	bool "HRTIM TIMB"
	default n
	---help---
		Enable HRTIM Timer B

config STM32_HRTIM_TIMC
	bool "HRTIM TIMC"
	default n
	---help---
		Enable HRTIM Timer C

config STM32_HRTIM_TIMD
	bool "HRTIM TIMD"
	default n
	---help---
		Enable HRTIM Timer D

config STM32_HRTIM_TIME
	bool "HRTIM TIME"
	default n
	---help---
		Enable HRTIM Timer E

endif # STM32_HRTIM

config STM32_I2C1
	bool "I2C1"
	default n
	select STM32_I2C

config STM32_I2C2
	bool "I2C2"
	default n
	depends on STM32_HAVE_I2C2
	select STM32_I2C

config STM32_I2C3
	bool "I2C3"
	default n
	depends on STM32_HAVE_I2C3
	select STM32_I2C

config STM32_LPTIM1
	bool "LPTIM1"
	default n
	depends on STM32_HAVE_LPTIM1

config STM32_LPUART1
	bool "LPUART1"
	default n
	depends on STM32_HAVE_LPUART1

config STM32_LTDC
	bool "LTDC"
	default n
	select FB
	depends on STM32_HAVE_LTDC
	---help---
		The STM32 LTDC is an LCD-TFT Display Controller available on
		the STM32F429 and STM32F439 devices.  It is a standard parallel
		video interface (HSYNC, VSYNC, etc.) for controlling TFT
		LCD displays.

config STM32_DMA2D
	bool "DMA2D"
	default n
	select FB
	select FB_OVERLAY
	depends on STM32_STM32F429
	---help---
		The STM32 DMA2D is an Chrom-Art Accelerator for image manipulation
		available on the STM32F429 and STM32F439 devices.

config STM32_OPAMP1
	bool "OPAMP1"
	default n
	select STM32_OPAMP
	depends on STM32_HAVE_OPAMP1

config STM32_OPAMP2
	bool "OPAMP2"
	default n
	select STM32_OPAMP
	depends on STM32_HAVE_OPAMP2

config STM32_OPAMP3
	bool "OPAMP3"
	default n
	select STM32_OPAMP
	depends on STM32_HAVE_OPAMP3

config STM32_OPAMP4
	bool "OPAMP4"
	default n
	select STM32_OPAMP
	depends on STM32_HAVE_OPAMP4

config STM32_RTC
	bool "RTC"
	default n
	select RTC

config STM32_OTGFS
	bool "OTG FS"
	default n
	depends on STM32_HAVE_OTGFS
	select USBHOST_HAVE_ASYNCH if STM32_USBHOST

config STM32_OTGHS
	bool "OTG HS"
	default n
	depends on STM32_STM32F20XX || STM32_STM32F4XXX
	select USBHOST_HAVE_ASYNCH if STM32_USBHOST

config STM32_PWR
	bool "PWR"
	default n

config STM32_QSPI
	bool "QSPI (QUADSPI)"
	depends on STM32_HAVE_QSPI
	default n

config STM32_RNG
	bool "RNG"
	default n
	depends on STM32_HAVE_RNG
	select ARCH_HAVE_RNG

# REVISIT:  There are some lower-end STM32F401 parts without SDIO too. Others?

config STM32_SDIO
	bool "SDIO"
	default n
	depends on !STM32_CONNECTIVITYLINE && !STM32_VALUELINE
	select ARCH_HAVE_SDIO
	select ARCH_HAVE_SDIOWAIT_WRCOMPLETE
	select ARCH_HAVE_SDIO_PREFLIGHT

config STM32_SPI1
	bool "SPI1"
	default n
	select SPI
	select STM32_SPI

config STM32_SPI2
	bool "SPI2"
	default n
	depends on STM32_HAVE_SPI2
	select SPI
	select STM32_SPI

config STM32_SPI3
	bool "SPI3"
	default n
	depends on STM32_HAVE_SPI3
	select SPI
	select STM32_SPI

config STM32_I2S3
	bool "I2S3"
	default n
	depends on STM32_HAVE_I2S3
	select I2S
	select STM32_I2S

config STM32_SPI4
	bool "SPI4"
	default n
	depends on STM32_HAVE_SPI4
	select SPI
	select STM32_SPI

config STM32_SPI5
	bool "SPI5"
	default n
	depends on STM32_HAVE_SPI5
	select SPI
	select STM32_SPI

config STM32_SPI6
	bool "SPI6"
	default n
	depends on STM32_HAVE_SPI6
	select SPI
	select STM32_SPI

config STM32_SYSCFG
	bool "SYSCFG"
	default y
	depends on STM32_STM32L15XX || STM32_STM32F30XX || STM32_STM32F33XX || STM32_STM32F37XX || STM32_STM32F20XX || STM32_STM32F4XXX || STM32_STM32G4XXX || STM32_CONNECTIVITYLINE

config STM32_TIM1
	bool "TIM1"
	default n
	depends on STM32_HAVE_TIM1
	select STM32_TIM

config STM32_TIM2
	bool "TIM2"
	default n
	select STM32_TIM

config STM32_TIM3
	bool "TIM3"
	default n
	depends on STM32_HAVE_TIM3
	select STM32_TIM

config STM32_TIM4
	bool "TIM4"
	default n
	depends on STM32_HAVE_TIM4
	select STM32_TIM

config STM32_TIM5
	bool "TIM5"
	default n
	depends on STM32_HAVE_TIM5
	select STM32_TIM

config STM32_TIM6
	bool "TIM6"
	default n
	depends on STM32_HAVE_TIM6
	select STM32_TIM

config STM32_TIM7
	bool "TIM7"
	default n
	depends on STM32_HAVE_TIM7
	select STM32_TIM

config STM32_TIM8
	bool "TIM8"
	default n
	depends on STM32_HAVE_TIM8
	select STM32_TIM

config STM32_TIM9
	bool "TIM9"
	default n
	depends on STM32_HAVE_TIM9
	select STM32_TIM

config STM32_TIM10
	bool "TIM10"
	default n
	depends on STM32_HAVE_TIM10
	select STM32_TIM

config STM32_TIM11
	bool "TIM11"
	default n
	depends on STM32_HAVE_TIM11
	select STM32_TIM

config STM32_TIM12
	bool "TIM12"
	default n
	depends on STM32_HAVE_TIM12
	select STM32_TIM

config STM32_TIM13
	bool "TIM13"
	default n
	depends on STM32_HAVE_TIM13
	select STM32_TIM

config STM32_TIM14
	bool "TIM14"
	default n
	depends on STM32_HAVE_TIM14
	select STM32_TIM

config STM32_TIM15
	bool "TIM15"
	default n
	depends on STM32_HAVE_TIM15
	select STM32_TIM

config STM32_TIM16
	bool "TIM16"
	default n
	depends on STM32_HAVE_TIM16
	select STM32_TIM

config STM32_TIM17
	bool "TIM17"
	default n
	depends on STM32_HAVE_TIM17
	select STM32_TIM

config STM32_TSC
	bool "TSC"
	default n
	depends on STM32_HAVE_TSC

config STM32_USART1
	bool "USART1"
	default n
	select STM32_USART

config STM32_USART2
	bool "USART2"
	default n
	select STM32_USART

config STM32_USART3
	bool "USART3"
	default n
	depends on STM32_HAVE_USART3
	select STM32_USART

config STM32_UART4
	bool "UART4"
	default n
	depends on STM32_HAVE_UART4
	select STM32_USART

config STM32_UART5
	bool "UART5"
	default n
	depends on STM32_HAVE_UART5
	select STM32_USART

config STM32_USART6
	bool "USART6"
	default n
	depends on STM32_HAVE_USART6
	select STM32_USART

config STM32_UART7
	bool "UART7"
	default n
	depends on STM32_HAVE_UART7
	select STM32_USART

config STM32_UART8
	bool "UART8"
	default n
	depends on STM32_HAVE_UART8
	select STM32_USART

config STM32_USB
	bool "USB Device"
	default n
	depends on STM32_HAVE_USBDEV
	select USBDEV

config STM32_USBFS
	bool "USB Full Speed Device"
	default n
	depends on STM32_HAVE_USBFS
	select USBDEV

config STM32_UCPD
	bool "UCPD (USB Type C Power Delivery)"
	default n
	depends on STM32_HAVE_UCPD
	select USBDEV

config STM32_LCD
	bool "Segment LCD"
	default n
	depends on STM32_STM32L15XX

#
# STM32 LCD Clock Selection
#

if STM32_LCD

choice
	prompt "Segment LCD Clock Source"
	default LCD_LSECLOCK

config LCD_LSICLOCK
	bool "Internal Low Speed Clock"

config LCD_LSECLOCK
	bool "External Low Speed Clock"

config LCD_HSECLOCK
	bool "External High Speed Clock"

endchoice
endif # STM32_LCD

config STM32_IWDG
	bool "IWDG"
	default n
	select WATCHDOG

config STM32_WWDG
	bool "WWDG"
	default n
	select WATCHDOG

endmenu

config STM32_ADC
	bool
	default n

config STM32_SDADC
	bool
	default n

config STM32_DAC
	bool
	default n

config STM32_DMA
	bool
	default n

config STM32_DMAMUX
	bool
	default n

config STM32_SPI
	bool
	default n

config STM32_SPI_DMA
	bool
	default n

config STM32_I2S
	bool
	default n
	select STM32_SPI_DMA

config STM32_I2C
	bool
	default n

config STM32_CAN
	bool
	default n

config STM32_FDCAN
	bool
	default n

config STM32_TIM
	bool
	default n

config STM32_PWM
	bool
	default n
	select ARCH_HAVE_PWM_PULSECOUNT

config STM32_CAP
	bool
	default n

config STM32_COMP
	bool
	default n
	depends on STM32_STM32L15XX || STM32_STM32F33XX || STM32_STM32G4XXX

config STM32_OPAMP
	bool
	default n

config STM32_NOEXT_VECTORS
	bool "Disable the ARMv7-M EXT vectors"
	default n
	---help---
		Sometimes you may not need any Vector support beyond SysTick
		and wish to save memory. This applies only to ARMv7-M architectures.

config STM32_USE_LEGACY_PINMAP
	bool "Use the legacy pinmap with GPIO_SPEED_xxx included."
	default y
	---help---
		In the past, pinmap files included GPIO_SPEED_xxxMhz. These speed
		settings should have come from the board.h as it describes the wiring
		of the SoC to the board. The speed is really slew rate control and
		therefore is related to the layout and can only be properly set
		in board.h.

		STM32_USE_LEGACY_PINMAP is provided, to allow lazy migration to
		using pinmaps without speeds. The work required to do this can be aided
		by running tools/stm32_pinmap_tool.py. The tools will take a board.h
		file and a legacy pinmap and output the required changes that one needs
		to make to a board.h file.

		Eventually, STM32_USE_LEGACY_PINMAP will be deprecated and the legacy
		pinmaps removed from NuttX. Any new boards added should set
		STM32_USE_LEGACY_PINMAP=n and fully define the pins in board.h

config STM32_SYSCFG_IOCOMPENSATION
	bool "SYSCFG I/O Compensation"
	default n
	depends on STM32_HAVE_IOCOMPENSATION
	---help---
		By default the I/O compensation cell is not used. However when the I/O
		output buffer speed is configured in 50 MHz or 100 MHz mode, it is
		recommended to use the compensation cell for slew rate control on I/O
		tf(IO)out)/tr(IO)out commutation to reduce the I/O noise on power supply.

		The I/O compensation cell can be used only when the supply voltage ranges
		from 2.4 to 3.6 V.

menu "Alternate Pin Mapping"
	depends on STM32_STM32F10XX || STM32_CONNECTIVITYLINE

choice
	prompt "CAN1 Alternate Pin Mappings"
	depends on STM32_STM32F10XX && STM32_CAN1
	default STM32_CAN1_NO_REMAP

config STM32_CAN1_NO_REMAP
	bool "No pin remapping"

config STM32_CAN1_REMAP1
	bool "CAN1 alternate pin remapping #1"

config STM32_CAN1_REMAP2
	bool "CAN1 alternate pin remapping #2"

endchoice

config STM32_CAN2_REMAP
	bool "CAN2 Alternate Pin Mapping"
	default n
	depends on STM32_CONNECTIVITYLINE && STM32_CAN2

config STM32_CEC_REMAP
	bool "CEC Alternate Pin Mapping"
	default n
	depends on STM32_STM32F10XX && STM32_CEC

config STM32_ETH_REMAP
	bool "Ethernet Alternate Pin Mapping"
	default n
	depends on STM32_CONNECTIVITYLINE && STM32_ETHMAC

config STM32_I2C1_REMAP
	bool "I2C1 Alternate Pin Mapping"
	default n
	depends on STM32_STM32F10XX && STM32_I2C1

config STM32_SPI1_REMAP
	bool "SPI1 Alternate Pin Mapping"
	default n
	depends on STM32_STM32F10XX && STM32_SPI1

config STM32_SPI3_REMAP
	bool "SPI3 Alternate Pin Mapping"
	default n
	depends on STM32_STM32F10XX && STM32_SPI3 && !STM32_VALUELINE

config STM32_I2S3_REMAP
	bool "I2S3 Alternate Pin Mapping"
	default n
	depends on STM32_STM32F10XX && STM32_I2S3 && !STM32_VALUELINE

choice
	prompt "TIM1 Alternate Pin Mappings"
	depends on STM32_STM32F10XX && STM32_TIM1
	default STM32_TIM1_NO_REMAP

config STM32_TIM1_NO_REMAP
	bool "No pin remapping"

config STM32_TIM1_FULL_REMAP
	bool "Full pin remapping"

config STM32_TIM1_PARTIAL_REMAP
	bool "Partial pin remapping"

endchoice

choice
	prompt "TIM2 Alternate Pin Mappings"
	depends on STM32_STM32F10XX && STM32_TIM2
	default STM32_TIM2_NO_REMAP

config STM32_TIM2_NO_REMAP
	bool "No pin remapping"

config STM32_TIM2_FULL_REMAP
	bool "Full pin remapping"

config STM32_TIM2_PARTIAL_REMAP_1
	bool "Partial pin remapping #1"

config STM32_TIM2_PARTIAL_REMAP_2
	bool "Partial pin remapping #2"

endchoice

choice
	prompt "TIM3 Alternate Pin Mappings"
	depends on STM32_STM32F10XX && STM32_TIM3
	default STM32_TIM3_NO_REMAP

config STM32_TIM3_NO_REMAP
	bool "No pin remapping"

config STM32_TIM3_FULL_REMAP
	bool "Full pin remapping"

config STM32_TIM3_PARTIAL_REMAP
	bool "Partial pin remapping"

endchoice

config STM32_TIM4_REMAP
	bool "TIM4 Alternate Pin Mapping"
	default n
	depends on STM32_STM32F10XX && STM32_TIM4

config STM32_TIM9_REMAP
	bool "TIM9 Alternate Pin Mapping"
	default n
	depends on STM32_STM32F10XX && STM32_TIM9

config STM32_TIM10_REMAP
	bool "TIM10 Alternate Pin Mapping"
	default n
	depends on STM32_STM32F10XX && STM32_TIM10

config STM32_TIM11_REMAP
	bool "TIM11 Alternate Pin Mapping"
	default n
	depends on STM32_STM32F10XX && STM32_TIM11

config STM32_TIM12_REMAP
	bool "TIM12 Alternate Pin Mapping"
	default n
	depends on STM32_STM32F10XX && STM32_TIM12

config STM32_TIM13_REMAP
	bool "TIM13 Alternate Pin Mapping"
	default n
	depends on STM32_STM32F10XX && STM32_TIM13

config STM32_TIM14_REMAP
	bool "TIM14 Alternate Pin Mapping"
	default n
	depends on STM32_STM32F10XX && STM32_TIM14

config STM32_TIM15_REMAP
	bool "TIM15 Alternate Pin Mapping"
	default n
	depends on STM32_STM32F10XX && STM32_TIM15

config STM32_TIM16_REMAP
	bool "TIM16 Alternate Pin Mapping"
	default n
	depends on STM32_STM32F10XX && STM32_TIM16

config STM32_TIM17_REMAP
	bool "TIM17 Alternate Pin Mapping"
	default n
	depends on STM32_STM32F10XX && STM32_TIM17

config STM32_USART1_REMAP
	bool "USART1 Alternate Pin Mapping"
	default n
	depends on STM32_STM32F10XX && STM32_USART1

config STM32_USART2_REMAP
	bool "USART2 Alternate Pin Mapping"
	default n
	depends on STM32_STM32F10XX && STM32_USART2

choice
	prompt "USART3 Alternate Pin Mappings"
	depends on STM32_STM32F10XX && STM32_USART3
	default STM32_USART3_NO_REMAP

config STM32_USART3_NO_REMAP
	bool "No pin remapping"

config STM32_USART3_FULL_REMAP
	bool "Full pin remapping"

config STM32_USART3_PARTIAL_REMAP
	bool "Partial pin remapping"

endchoice

endmenu

config STM32_FLASH_ICACHE
	bool "Enable FLASH Instruction Cache"
	default y
	depends on STM32_HAVE_FLASH_ICACHE
	---help---
		Enable the FLASH instruction cache.

config STM32_FLASH_DCACHE
	bool "Enable FLASH Data Cache"
	default y
	depends on STM32_HAVE_FLASH_DCACHE
	---help---
		Enable the FLASH data cache.

config STM32_FLASH_WORKAROUND_DATA_CACHE_CORRUPTION_ON_RWW
	bool "Workaround for FLASH data cache corruption"
	default n
	depends on (STM32_STM32F20XX || STM32_STM32F4XXX) && STM32_FLASH_DCACHE
	---help---
		Enable the workaround to fix flash data cache corruption when reading
		from one flash bank while writing on other flash bank.  See your STM32
		errata to check if your STM32 is affected by this problem.

config STM32_FLASH_PREFETCH
	bool "Enable FLASH Pre-fetch"
	default y if STM32_STM32F427 || STM32_STM32F429 || STM32_STM32F446
	default n
	depends on STM32_STM32F20XX || STM32_STM32F4XXX
	---help---
		Enable FLASH prefetch in F2 and F4 parts (FLASH pre-fetch is always enabled
		on F1 parts).  Some early revisions of F4 parts do not support FLASH pre-fetch
		properly and enabling this option may interfere with ADC accuracy.

choice
	prompt "JTAG Configuration"
	default STM32_JTAG_DISABLE
	---help---
		JTAG Enable settings (by default JTAG-DP and SW-DP are disabled)

config STM32_JTAG_DISABLE
	bool "Disable all JTAG clocking"

config STM32_JTAG_FULL_ENABLE
	bool "Enable full SWJ (JTAG-DP + SW-DP)"

config STM32_JTAG_NOJNTRST_ENABLE
	bool "Enable full SWJ (JTAG-DP + SW-DP) but without JNTRST"

config STM32_JTAG_SW_ENABLE
	bool "Set JTAG-DP disabled and SW-DP enabled"

endchoice

config STM32_DISABLE_IDLE_SLEEP_DURING_DEBUG
	bool "Disable IDLE Sleep (WFI) in debug mode"
	default n
	---help---
		In debug configuration, disables the WFI instruction in the IDLE loop
		to prevent the JTAG from disconnecting.  With some JTAG debuggers, such
		as the ST-LINK2 with OpenOCD, if the ARM is put to sleep via the WFI
		instruction, the debugger will disconnect, terminating the debug session.

config STM32_FORCEPOWER
	bool "Force power"
	default n
	---help---
		Timer and I2C devices may need to the following to force power to be applied
		unconditionally at power up.  (Otherwise, the device is powered when it is
		initialized).

config ARCH_BOARD_STM32_CUSTOM_CLOCKCONFIG
	bool "Custom clock configuration"
	default n
	---help---
		Enables special, board-specific STM32 clock configuration.

config STM32_SAIPLL
	bool "SAIPLL"
	default n
	depends on STM32_HAVE_SAIPLL
	---help---
		The STM32F446 has a separate PLL for the SAI block.
		Set this true and provide configuration parameters in
		board.h to use this PLL.

config STM32_I2SPLL
	bool "I2SPLL"
	default n
	depends on STM32_HAVE_I2SPLL
	---help---
		The STM32F446 has a separate PLL for the I2S block.
		Set this true and provide configuration parameters in
		board.h to use this PLL.

config STM32_CCMEXCLUDE
	bool "Exclude CCM SRAM from the heap"
	default y if  ARCH_DMA || LIBC_ARCH_ELF
	depends on STM32_HAVE_CCM
	---help---
		Exclude CCM SRAM from the HEAP because (1) it cannot be used for DMA
		and (2) it appears to be impossible to execute ELF modules from CCM
		RAM.

config STM32_CCM_PROCFS
	bool "CCM PROCFS support"
	default n
	depends on !DISABLE_MOUNTPOINT && FS_PROCFS && FS_PROCFS_REGISTER
	---help---
		Select to build in support for /proc/ccm.  Reading from /proc/ccm
		will provide statistics about CCM memory use similar to what you
		would get from mallinfo() for the user heap.

config STM32_DMACAPABLE
	bool "Workaround non-DMA capable memory"
	depends on ARCH_DMA
	default y if STM32_STM32F4XXX && !STM32_CCMEXCLUDE
	default n if !STM32_STM32F4XXX || STM32_CCMEXCLUDE
	---help---
		This option enables the DMA interface stm32_dmacapable that can be
		used to check if it is possible to do DMA from the selected address.
		Drivers then may use this information to determine if they should
		attempt the DMA or fall back to a different transfer method.

config STM32_EXTERNAL_RAM
	bool "External RAM on FSMC/FMC"
	default n
	depends on STM32_FSMC || STM32_FMC
	select ARCH_HAVE_HEAP2
	---help---
		In addition to internal SRAM, external RAM may be available through the FSMC/FMC.

config STM32_TICKLESS_SYSTICK
	bool "Tickless via SysTick"
	default n
	depends on SCHED_TICKLESS
	---help---
		Use SysTick as Tickless clock.

menu "Timer Configuration"
	depends on STM32_TIM

if SCHED_TICKLESS

config STM32_TICKLESS_TIMER
	int "Tickless hardware timer"
	default 2
	range 1 14
	depends on !STM32_TICKLESS_SYSTICK
	---help---
		If the Tickless OS feature is enabled, then one clock must be
		assigned to provided the timer needed by the OS.

config STM32_TICKLESS_CHANNEL
	int "Tickless timer channel"
	default 1
	range 1 4
	---help---
		If the Tickless OS feature is enabled, the one clock must be
		assigned to provided the free-running timer needed by the OS
		and one channel on that clock is needed to handle intervals.

endif # SCHED_TICKLESS

config STM32_ONESHOT
	bool "TIM one-shot wrapper"
	default n
	---help---
		Enable a wrapper around the low level timer/counter functions to
		support one-shot timer.

config STM32_FREERUN
	bool "TIM free-running wrapper"
	default n
	---help---
		Enable a wrapper around the low level timer/counter functions to
		support a free-running timer.

config STM32_ONESHOT_MAXTIMERS
	int "Maximum number of oneshot timers"
	default 1
	range 1 8
	depends on STM32_ONESHOT
	---help---
		Determines the maximum number of oneshot timers that can be
		supported.  This setting pre-allocates some minimal support for each
		of the timers and places an upper limit on the number of oneshot
		timers that you can use.

config STM32_PWM_LL_OPS
	bool "PWM low-level operations"
	default n
	---help---
		Enable low-level PWM ops.

config STM32_TIM1_PWM
	bool "TIM1 PWM"
	default n
	depends on STM32_TIM1
	select STM32_PWM
	---help---
		Reserve timer 1 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32_TIM1
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32_TIM1_PWM

config STM32_TIM1_MODE
	int "TIM1 Mode"
	default 0
	range 0 4
	---help---
		Specifies the timer mode.

config STM32_TIM1_LOCK
	int "TIM1 Lock Level Configuration"
	default 0
	range 0 3
	---help---
		Timer 1 lock level configuration

config STM32_TIM1_TDTS
	int "TIM1 t_DTS Division"
	default 0
	range 0 2
	---help---
		Timer 1 dead-time and sampling clock (t_DTS) division

config STM32_TIM1_DEADTIME
	int "TIM1 Initial Dead-time"
	default 0
	range 0 255
	---help---
		Timer 1 initial dead-time

if STM32_PWM_MULTICHAN

config STM32_TIM1_CHANNEL1
	bool "TIM1 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32_TIM1_CHANNEL1

config STM32_TIM1_CH1MODE
	int "TIM1 Channel 1 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM1_CH1OUT
	bool "TIM1 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32_TIM1_CH1NOUT
	bool "TIM1 Channel 1 Complementary Output"
	default n
	---help---
		Enables channel 1 Complementary Output.

endif # STM32_TIM1_CHANNEL1

config STM32_TIM1_CHANNEL2
	bool "TIM1 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32_TIM1_CHANNEL2

config STM32_TIM1_CH2MODE
	int "TIM1 Channel 2 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM1_CH2OUT
	bool "TIM1 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

config STM32_TIM1_CH2NOUT
	bool "TIM1 Channel 2 Complementary Output"
	default n
	---help---
		Enables channel 2 Complementary Output.

endif # STM32_TIM1_CHANNEL2

config STM32_TIM1_CHANNEL3
	bool "TIM1 Channel 3"
	default n
	---help---
		Enables channel 3.

if STM32_TIM1_CHANNEL3

config STM32_TIM1_CH3MODE
	int "TIM1 Channel 3 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM1_CH3OUT
	bool "TIM1 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

config STM32_TIM1_CH3NOUT
	bool "TIM1 Channel 3 Complementary Output"
	default n
	---help---
		Enables channel 3 Complementary Output.

endif # STM32_TIM1_CHANNEL3

config STM32_TIM1_CHANNEL4
	bool "TIM1 Channel 4"
	default n
	---help---
		Enables channel 4.

if STM32_TIM1_CHANNEL4

config STM32_TIM1_CH4MODE
	int "TIM1 Channel 4 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM1_CH4OUT
	bool "TIM1 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32_TIM1_CHANNEL4

config STM32_TIM1_CHANNEL5
	bool "TIM1 Channel 5 (internal)"
	default n
	depends on STM32_HAVE_IP_TIMERS_V2
	---help---
		Enables channel 5 (not available externally)

if STM32_TIM1_CHANNEL5

config STM32_TIM1_CH5MODE
	int "TIM1 Channel 5 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM1_CH5OUT
	bool "TIM1 Channel 5 Output"
	default n
	---help---
		Enables channel 5 output.

endif # STM32_TIM1_CHANNEL5

config STM32_TIM1_CHANNEL6
	bool "TIM1 Channel 6 (internal)"
	default n
	depends on STM32_HAVE_IP_TIMERS_V2
	---help---
		Enables channel 6 (not available externally)

if STM32_TIM1_CHANNEL6

config STM32_TIM1_CH6MODE
	int "TIM1 Channel 6 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM1_CH6OUT
	bool "TIM1 Channel 6 Output"
	default n
	---help---
		Enables channel 6 output.

endif # STM32_TIM1_CHANNEL6

endif # STM32_PWM_MULTICHAN

if !STM32_PWM_MULTICHAN

config STM32_TIM1_CHANNEL
	int "TIM1 PWM Output Channel"
	default 1
	range 1 4
	---help---
		If TIM1 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

if STM32_TIM1_CHANNEL = 1

config STM32_TIM1_CH1OUT
	bool "TIM1 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32_TIM1_CH1NOUT
	bool "TIM1 Channel 1 Complementary Output"
	default n
	---help---
		Enables channel 1 Complementary Output.

endif # STM32_TIM1_CHANNEL = 1

if STM32_TIM1_CHANNEL = 2

config STM32_TIM1_CH2OUT
	bool "TIM1 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

config STM32_TIM1_CH2NOUT
	bool "TIM1 Channel 2 Complementary Output"
	default n
	---help---
		Enables channel 2 Complementary Output.

endif # STM32_TIM1_CHANNEL = 2

if STM32_TIM1_CHANNEL = 3

config STM32_TIM1_CH3OUT
	bool "TIM1 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

config STM32_TIM1_CH3NOUT
	bool "TIM1 Channel 3 Complementary Output"
	default n
	---help---
		Enables channel 3 Complementary Output.

endif # STM32_TIM1_CHANNEL = 3

if STM32_TIM1_CHANNEL = 4

config STM32_TIM1_CH4OUT
	bool "TIM1 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32_TIM1_CHANNEL = 4

config STM32_TIM1_CHMODE
	int "TIM1 Channel Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32_PWM_MULTICHAN

endif # STM32_TIM1_PWM

config STM32_TIM2_PWM
	bool "TIM2 PWM"
	default n
	depends on STM32_TIM2
	select STM32_PWM
	---help---
		Reserve timer 2 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32_TIM2
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32_TIM2_PWM

config STM32_TIM2_MODE
	int "TIM2 Mode"
	default 0
	range 0 4
	---help---
		Specifies the timer mode.

if STM32_PWM_MULTICHAN

config STM32_TIM2_CHANNEL1
	bool "TIM2 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32_TIM2_CHANNEL1

config STM32_TIM2_CH1MODE
	int "TIM2 Channel 1 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM2_CH1OUT
	bool "TIM2 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM2_CHANNEL1

config STM32_TIM2_CHANNEL2
	bool "TIM2 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32_TIM2_CHANNEL2

config STM32_TIM2_CH2MODE
	int "TIM2 Channel 2 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM2_CH2OUT
	bool "TIM2 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32_TIM2_CHANNEL2

config STM32_TIM2_CHANNEL3
	bool "TIM2 Channel 3"
	default n
	---help---
		Enables channel 3.

if STM32_TIM2_CHANNEL3

config STM32_TIM2_CH3MODE
	int "TIM2 Channel 3 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM2_CH3OUT
	bool "TIM2 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32_TIM2_CHANNEL3

config STM32_TIM2_CHANNEL4
	bool "TIM2 Channel 4"
	default n
	---help---
		Enables channel 4.

if STM32_TIM2_CHANNEL4

config STM32_TIM2_CH4MODE
	int "TIM2 Channel 4 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM2_CH4OUT
	bool "TIM2 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32_TIM2_CHANNEL4

endif # STM32_PWM_MULTICHAN

if !STM32_PWM_MULTICHAN

config STM32_TIM2_CHANNEL
	int "TIM2 PWM Output Channel"
	default 1
	range 1 4
	---help---
		If TIM2 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

if STM32_TIM2_CHANNEL = 1

config STM32_TIM2_CH1OUT
	bool "TIM2 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM2_CHANNEL = 1

if STM32_TIM2_CHANNEL = 2

config STM32_TIM2_CH2OUT
	bool "TIM2 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32_TIM2_CHANNEL = 2

if STM32_TIM2_CHANNEL = 3

config STM32_TIM2_CH3OUT
	bool "TIM2 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32_TIM2_CHANNEL = 3

if STM32_TIM2_CHANNEL = 4

config STM32_TIM2_CH4OUT
	bool "TIM2 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32_TIM2_CHANNEL = 4

config STM32_TIM2_CHMODE
	int "TIM2 Channel Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32_PWM_MULTICHAN

endif # STM32_TIM2_PWM

config STM32_TIM3_PWM
	bool "TIM3 PWM"
	default n
	depends on STM32_TIM3
	select STM32_PWM
	---help---
		Reserve timer 3 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32_TIM3
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32_TIM3_PWM

config STM32_TIM3_MODE
	int "TIM3 Mode"
	default 0
	range 0 4
	---help---
		Specifies the timer mode.

if STM32_PWM_MULTICHAN

config STM32_TIM3_CHANNEL1
	bool "TIM3 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32_TIM3_CHANNEL1

config STM32_TIM3_CH1MODE
	int "TIM3 Channel 1 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM3_CH1OUT
	bool "TIM3 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM3_CHANNEL1

config STM32_TIM3_CHANNEL2
	bool "TIM3 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32_TIM3_CHANNEL2

config STM32_TIM3_CH2MODE
	int "TIM3 Channel 2 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM3_CH2OUT
	bool "TIM3 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32_TIM3_CHANNEL2

config STM32_TIM3_CHANNEL3
	bool "TIM3 Channel 3"
	default n
	---help---
		Enables channel 3.

if STM32_TIM3_CHANNEL3

config STM32_TIM3_CH3MODE
	int "TIM3 Channel 3 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM3_CH3OUT
	bool "TIM3 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32_TIM3_CHANNEL3

config STM32_TIM3_CHANNEL4
	bool "TIM3 Channel 4"
	default n
	---help---
		Enables channel 4.

if STM32_TIM3_CHANNEL4

config STM32_TIM3_CH4MODE
	int "TIM3 Channel 4 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM3_CH4OUT
	bool "TIM3 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32_TIM3_CHANNEL4

endif # STM32_PWM_MULTICHAN

if !STM32_PWM_MULTICHAN

config STM32_TIM3_CHANNEL
	int "TIM3 PWM Output Channel"
	default 1
	range 1 4
	---help---
		If TIM3 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

if STM32_TIM3_CHANNEL = 1

config STM32_TIM3_CH1OUT
	bool "TIM3 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM3_CHANNEL = 1

if STM32_TIM3_CHANNEL = 2

config STM32_TIM3_CH2OUT
	bool "TIM3 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32_TIM3_CHANNEL = 2

if STM32_TIM3_CHANNEL = 3

config STM32_TIM3_CH3OUT
	bool "TIM3 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32_TIM3_CHANNEL = 3

if STM32_TIM3_CHANNEL = 4

config STM32_TIM3_CH4OUT
	bool "TIM3 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32_TIM3_CHANNEL = 4

config STM32_TIM3_CHMODE
	int "TIM3 Channel Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32_PWM_MULTICHAN

endif # STM32_TIM3_PWM

config STM32_TIM4_PWM
	bool "TIM4 PWM"
	default n
	depends on STM32_TIM4
	select STM32_PWM
	---help---
		Reserve timer 4 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32_TIM4
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32_TIM4_PWM

config STM32_TIM4_MODE
	int "TIM4 Mode"
	default 0
	range 0 4
	---help---
		Specifies the timer mode.

if STM32_PWM_MULTICHAN

config STM32_TIM4_CHANNEL1
	bool "TIM4 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32_TIM4_CHANNEL1

config STM32_TIM4_CH1MODE
	int "TIM4 Channel 1 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM4_CH1OUT
	bool "TIM4 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM4_CHANNEL1

config STM32_TIM4_CHANNEL2
	bool "TIM4 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32_TIM4_CHANNEL2

config STM32_TIM4_CH2MODE
	int "TIM4 Channel 2 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM4_CH2OUT
	bool "TIM4 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32_TIM4_CHANNEL2

config STM32_TIM4_CHANNEL3
	bool "TIM4 Channel 3"
	default n
	---help---
		Enables channel 3.

if STM32_TIM4_CHANNEL3

config STM32_TIM4_CH3MODE
	int "TIM4 Channel 3 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM4_CH3OUT
	bool "TIM4 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32_TIM4_CHANNEL3

config STM32_TIM4_CHANNEL4
	bool "TIM4 Channel 4"
	default n
	---help---
		Enables channel 4.

if STM32_TIM4_CHANNEL4

config STM32_TIM4_CH4MODE
	int "TIM4 Channel 4 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM4_CH4OUT
	bool "TIM4 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32_TIM4_CHANNEL4

endif # STM32_PWM_MULTICHAN

if !STM32_PWM_MULTICHAN

config STM32_TIM4_CHANNEL
	int "TIM4 PWM Output Channel"
	default 1
	range 1 4
	---help---
		If TIM4 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

if STM32_TIM4_CHANNEL = 1

config STM32_TIM4_CH1OUT
	bool "TIM4 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM4_CHANNEL = 1

if STM32_TIM4_CHANNEL = 2

config STM32_TIM4_CH2OUT
	bool "TIM4 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32_TIM4_CHANNEL = 2

if STM32_TIM4_CHANNEL = 3

config STM32_TIM4_CH3OUT
	bool "TIM4 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32_TIM4_CHANNEL = 3

if STM32_TIM4_CHANNEL = 4

config STM32_TIM4_CH4OUT
	bool "TIM4 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32_TIM4_CHANNEL = 4

config STM32_TIM4_CHMODE
	int "TIM4 Channel Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32_PWM_MULTICHAN

endif # STM32_TIM4_PWM

config STM32_TIM5_PWM
	bool "TIM5 PWM"
	default n
	depends on STM32_TIM5
	select STM32_PWM
	---help---
		Reserve timer 5 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32_TIM5
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32_TIM5_PWM

config STM32_TIM5_MODE
	int "TIM5 Mode"
	default 0
	range 0 4
	---help---
		Specifies the timer mode.

if STM32_PWM_MULTICHAN

config STM32_TIM5_CHANNEL1
	bool "TIM5 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32_TIM5_CHANNEL1

config STM32_TIM5_CH1MODE
	int "TIM5 Channel 1 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM5_CH1OUT
	bool "TIM5 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM5_CHANNEL1

config STM32_TIM5_CHANNEL2
	bool "TIM5 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32_TIM5_CHANNEL2

config STM32_TIM5_CH2MODE
	int "TIM5 Channel 2 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM5_CH2OUT
	bool "TIM5 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32_TIM5_CHANNEL2

config STM32_TIM5_CHANNEL3
	bool "TIM5 Channel 3"
	default n
	---help---
		Enables channel 3.

if STM32_TIM5_CHANNEL3

config STM32_TIM5_CH3MODE
	int "TIM5 Channel 3 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM5_CH3OUT
	bool "TIM5 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32_TIM5_CHANNEL3

config STM32_TIM5_CHANNEL4
	bool "TIM5 Channel 4"
	default n
	---help---
		Enables channel 4.

if STM32_TIM5_CHANNEL4

config STM32_TIM5_CH4MODE
	int "TIM5 Channel 4 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM5_CH4OUT
	bool "TIM5 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32_TIM5_CHANNEL4

endif # STM32_PWM_MULTICHAN

if !STM32_PWM_MULTICHAN

config STM32_TIM5_CHANNEL
	int "TIM5 PWM Output Channel"
	default 1
	range 1 4
	---help---
		If TIM5 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

if STM32_TIM5_CHANNEL = 1

config STM32_TIM5_CH1OUT
	bool "TIM5 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM5_CHANNEL = 1

if STM32_TIM5_CHANNEL = 2

config STM32_TIM5_CH2OUT
	bool "TIM5 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32_TIM5_CHANNEL = 2

if STM32_TIM5_CHANNEL = 3

config STM32_TIM5_CH3OUT
	bool "TIM5 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

endif # STM32_TIM5_CHANNEL = 3

if STM32_TIM5_CHANNEL = 4

config STM32_TIM5_CH4OUT
	bool "TIM5 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32_TIM5_CHANNEL = 4

config STM32_TIM5_CHMODE
	int "TIM5 Channel Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32_PWM_MULTICHAN

endif # STM32_TIM5_PWM

config STM32_TIM8_PWM
	bool "TIM8 PWM"
	default n
	depends on STM32_TIM8
	select STM32_PWM
	---help---
		Reserve timer 8 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32_TIM8
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32_TIM8_PWM

config STM32_TIM8_MODE
	int "TIM8 Mode"
	default 0
	range 0 4
	---help---
		Specifies the timer mode.

config STM32_TIM8_LOCK
	int "TIM8 Lock Level Configuration"
	default 0
	range 0 3
	---help---
		Timer 8 lock level configuration

config STM32_TIM8_DEADTIME
	int "TIM8 Initial Dead-time"
	default 0
	range 0 255
	---help---
		Timer 8 initial dead-time

config STM32_TIM8_TDTS
	int "TIM8 t_DTS Division"
	default 0
	range 0 2
	---help---
		Timer 8 dead-time and sampling clock (t_DTS) division

if STM32_PWM_MULTICHAN

config STM32_TIM8_CHANNEL1
	bool "TIM8 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32_TIM8_CHANNEL1

config STM32_TIM8_CH1MODE
	int "TIM8 Channel 1 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM8_CH1OUT
	bool "TIM8 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32_TIM8_CH1NOUT
	bool "TIM8 Channel 1 Complementary Output"
	default n
	---help---
		Enables channel 1 Complementary Output.

endif # STM32_TIM8_CHANNEL1

config STM32_TIM8_CHANNEL2
	bool "TIM8 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32_TIM8_CHANNEL2

config STM32_TIM8_CH2MODE
	int "TIM8 Channel 2 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM8_CH2OUT
	bool "TIM8 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

config STM32_TIM8_CH2NOUT
	bool "TIM8 Channel 2 Complementary Output"
	default n
	---help---
		Enables channel 2 Complementary Output.

endif # STM32_TIM8_CHANNEL2

config STM32_TIM8_CHANNEL3
	bool "TIM8 Channel 3"
	default n
	---help---
		Enables channel 3.

if STM32_TIM8_CHANNEL3

config STM32_TIM8_CH3MODE
	int "TIM8 Channel 3 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM8_CH3OUT
	bool "TIM8 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

config STM32_TIM8_CH3NOUT
	bool "TIM8 Channel 3 Complementary Output"
	default n
	---help---
		Enables channel 3 Complementary Output.

endif # STM32_TIM8_CHANNEL3

config STM32_TIM8_CHANNEL4
	bool "TIM8 Channel 4"
	default n
	---help---
		Enables channel 4.

if STM32_TIM8_CHANNEL4

config STM32_TIM8_CH4MODE
	int "TIM8 Channel 4 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM8_CH4OUT
	bool "TIM8 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32_TIM8_CHANNEL4

config STM32_TIM8_CHANNEL5
	bool "TIM8 Channel 5 (internal)"
	default n
	depends on STM32_HAVE_IP_TIMERS_V2
	---help---
		Enables channel 5 (not available externally)

if STM32_TIM8_CHANNEL5

config STM32_TIM8_CH5MODE
	int "TIM8 Channel 5 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM8_CH5OUT
	bool "TIM8 Channel 5 Output"
	default n
	---help---
		Enables channel 5 output.

endif # STM32_TIM8_CHANNEL5

config STM32_TIM8_CHANNEL6
	bool "TIM8 Channel 6 (internal)"
	default n
	depends on STM32_HAVE_IP_TIMERS_V2
	---help---
		Enables channel 6 (not available externally)

if STM32_TIM8_CHANNEL6

config STM32_TIM8_CH6MODE
	int "TIM8 Channel 6 Mode"
	default 6
	range 0 11
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM8_CH6OUT
	bool "TIM8 Channel 6 Output"
	default n
	---help---
		Enables channel 6 output.

endif # STM32_TIM8_CHANNEL6

endif # STM32_PWM_MULTICHAN

if !STM32_PWM_MULTICHAN

config STM32_TIM8_CHANNEL
	int "TIM8 PWM Output Channel"
	default 1
	range 1 4
	---help---
		If TIM8 is enabled for PWM usage, you also need specifies the timer output
		channel {1,..,4}

if STM32_TIM8_CHANNEL = 1

config STM32_TIM8_CH1OUT
	bool "TIM8 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32_TIM8_CH1NOUT
	bool "TIM8 Channel 1 Complementary Output"
	default n
	---help---
		Enables channel 1 Complementary Output.

endif # STM32_TIM8_CHANNEL = 1

if STM32_TIM8_CHANNEL = 2

config STM32_TIM8_CH2OUT
	bool "TIM8 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

config STM32_TIM8_CH2NOUT
	bool "TIM8 Channel 2 Complementary Output"
	default n
	---help---
		Enables channel 2 Complementary Output.

endif # STM32_TIM8_CHANNEL = 2

if STM32_TIM8_CHANNEL = 3

config STM32_TIM8_CH3OUT
	bool "TIM8 Channel 3 Output"
	default n
	---help---
		Enables channel 3 output.

config STM32_TIM8_CH3NOUT
	bool "TIM8 Channel 3 Complementary Output"
	default n
	---help---
		Enables channel 3 Complementary Output.

endif # STM32_TIM8_CHANNEL = 3

if STM32_TIM8_CHANNEL = 4

config STM32_TIM8_CH4OUT
	bool "TIM8 Channel 4 Output"
	default n
	---help---
		Enables channel 4 output.

endif # STM32_TIM8_CHANNEL = 4

config STM32_TIM8_CHMODE
	int "TIM8 Channel Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32_PWM_MULTICHAN

endif # STM32_TIM8_PWM

config STM32_TIM9_PWM
	bool "TIM9 PWM"
	default n
	depends on STM32_TIM9
	select STM32_PWM
	---help---
		Reserve timer 9 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32_TIM9
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32_TIM9_PWM

if STM32_PWM_MULTICHAN

config STM32_TIM9_CHANNEL1
	bool "TIM9 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32_TIM9_CHANNEL1

config STM32_TIM9_CH1MODE
	int "TIM9 Channel 1 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM9_CH1OUT
	bool "TIM9 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM9_CHANNEL1

config STM32_TIM9_CHANNEL2
	bool "TIM9 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32_TIM9_CHANNEL2

config STM32_TIM9_CH2MODE
	int "TIM9 Channel 2 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM9_CH2OUT
	bool "TIM9 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32_TIM9_CHANNEL2

endif # STM32_PWM_MULTICHAN

if !STM32_PWM_MULTICHAN

config STM32_TIM9_CHANNEL
	int "TIM9 PWM Output Channel"
	default 1
	range 1 2
	---help---
		If TIM9 is enabled for PWM usage, you also need specifies the timer output
		channel {1,2}

if STM32_TIM9_CHANNEL = 1

config STM32_TIM9_CH1OUT
	bool "TIM9 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM9_CHANNEL = 1

if STM32_TIM9_CHANNEL = 2

config STM32_TIM9_CH2OUT
	bool "TIM9 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32_TIM9_CHANNEL = 2

config STM32_TIM9_CHMODE
	int "TIM9 Channel Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32_PWM_MULTICHAN

endif # STM32_TIM9_PWM

config STM32_TIM10_PWM
	bool "TIM10 PWM"
	default n
	depends on STM32_TIM10
	select STM32_PWM
	---help---
		Reserve timer 10 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32_TIM10
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32_TIM10_PWM

if STM32_PWM_MULTICHAN

config STM32_TIM10_CHANNEL1
	bool "TIM10 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32_TIM10_CHANNEL1

config STM32_TIM10_CH1MODE
	int "TIM10 Channel 1 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM10_CH1OUT
	bool "TIM10 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM10_CHANNEL1

endif # STM32_PWM_MULTICHAN

if !STM32_PWM_MULTICHAN

config STM32_TIM10_CHANNEL
	int "TIM10 PWM Output Channel"
	default 1
	range 1 1
	---help---
		If TIM10 is enabled for PWM usage, you also need specifies the timer output
		channel {1}

if STM32_TIM10_CHANNEL = 1

config STM32_TIM10_CH1OUT
	bool "TIM10 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM10_CHANNEL = 1

config STM32_TIM10_CHMODE
	int "TIM10 Channel Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32_PWM_MULTICHAN

endif # STM32_TIM10_PWM

config STM32_TIM11_PWM
	bool "TIM11 PWM"
	default n
	depends on STM32_TIM11
	select STM32_PWM
	---help---
		Reserve timer 11 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32_TIM11
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32_TIM11_PWM

if STM32_PWM_MULTICHAN

config STM32_TIM11_CHANNEL1
	bool "TIM11 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32_TIM11_CHANNEL1

config STM32_TIM11_CH1MODE
	int "TIM11 Channel 1 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM11_CH1OUT
	bool "TIM11 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM11_CHANNEL1

endif # STM32_PWM_MULTICHAN

if !STM32_PWM_MULTICHAN

config STM32_TIM11_CHANNEL
	int "TIM11 PWM Output Channel"
	default 1
	range 1 1
	---help---
		If TIM11 is enabled for PWM usage, you also need specifies the timer output
		channel {1}

if STM32_TIM11_CHANNEL = 1

config STM32_TIM11_CH1OUT
	bool "TIM11 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM11_CHANNEL = 1

config STM32_TIM11_CHMODE
	int "TIM11 Channel Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32_PWM_MULTICHAN

endif # STM32_TIM11_PWM

config STM32_TIM12_PWM
	bool "TIM12 PWM"
	default n
	depends on STM32_TIM12
	select STM32_PWM
	---help---
		Reserve timer 12 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32_TIM12
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32_TIM12_PWM

if STM32_PWM_MULTICHAN

config STM32_TIM12_CHANNEL1
	bool "TIM12 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32_TIM12_CHANNEL1

config STM32_TIM12_CH1MODE
	int "TIM12 Channel 1 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM12_CH1OUT
	bool "TIM12 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM12_CHANNEL1

config STM32_TIM12_CHANNEL2
	bool "TIM12 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32_TIM12_CHANNEL2

config STM32_TIM12_CH2MODE
	int "TIM12 Channel 2 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM12_CH2OUT
	bool "TIM12 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32_TIM12_CHANNEL2

endif # STM32_PWM_MULTICHAN

if !STM32_PWM_MULTICHAN

config STM32_TIM12_CHANNEL
	int "TIM12 PWM Output Channel"
	default 1
	range 1 2
	---help---
		If TIM12 is enabled for PWM usage, you also need specifies the timer output
		channel {1,2}

if STM32_TIM12_CHANNEL = 1

config STM32_TIM12_CH1OUT
	bool "TIM12 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM12_CHANNEL = 1

if STM32_TIM12_CHANNEL = 2

config STM32_TIM12_CH2OUT
	bool "TIM12 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32_TIM12_CHANNEL = 2

config STM32_TIM12_CHMODE
	int "TIM12 Channel Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32_PWM_MULTICHAN

endif # STM32_TIM12_PWM

config STM32_TIM13_PWM
	bool "TIM13 PWM"
	default n
	depends on STM32_TIM13
	select STM32_PWM
	---help---
		Reserve timer 13 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32_TIM13
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32_TIM13_PWM

if STM32_PWM_MULTICHAN

config STM32_TIM13_CHANNEL1
	bool "TIM13 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32_TIM13_CHANNEL1

config STM32_TIM13_CH1MODE
	int "TIM13 Channel 1 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM13_CH1OUT
	bool "TIM13 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM13_CHANNEL1

endif # STM32_PWM_MULTICHAN

if !STM32_PWM_MULTICHAN

config STM32_TIM13_CHANNEL
	int "TIM13 PWM Output Channel"
	default 1
	range 1 1
	---help---
		If TIM13 is enabled for PWM usage, you also need specifies the timer output
		channel {1}

if STM32_TIM13_CHANNEL = 1

config STM32_TIM13_CH1OUT
	bool "TIM13 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM13_CHANNEL = 1

config STM32_TIM13_CHMODE
	int "TIM13 Channel Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32_PWM_MULTICHAN

endif # STM32_TIM13_PWM

config STM32_TIM14_PWM
	bool "TIM14 PWM"
	default n
	depends on STM32_TIM14
	select STM32_PWM
	---help---
		Reserve timer 14 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32_TIM14
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32_TIM14_PWM

if STM32_PWM_MULTICHAN

config STM32_TIM14_CHANNEL1
	bool "TIM14 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32_TIM14_CHANNEL1

config STM32_TIM14_CH1MODE
	int "TIM14 Channel 1 Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM14_CH1OUT
	bool "TIM14 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM14_CHANNEL1

endif # STM32_PWM_MULTICHAN

if !STM32_PWM_MULTICHAN

config STM32_TIM14_CHANNEL
	int "TIM14 PWM Output Channel"
	default 1
	range 1 1
	---help---
		If TIM14 is enabled for PWM usage, you also need specifies the timer output
		channel {1}

if STM32_TIM14_CHANNEL = 1

config STM32_TIM14_CH1OUT
	bool "TIM14 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM14_CHANNEL = 1

config STM32_TIM14_CHMODE
	int "TIM14 Channel Mode"
	default 6
	range 0 11 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32_PWM_MULTICHAN

endif # STM32_TIM14_PWM

config STM32_TIM15_PWM
	bool "TIM15 PWM"
	default n
	depends on STM32_TIM15
	select STM32_PWM
	---help---
		Reserve timer 15 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32_TIM15
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32_TIM15_PWM

config STM32_TIM15_LOCK
	int "TIM15 Lock Level Configuration"
	default 0
	range 0 3
	---help---
		Timer 15 lock level configuration

config STM32_TIM15_TDTS
	int "TIM15 t_DTS Division"
	default 0
	range 0 2
	---help---
		Timer 15 dead-time and sampling clock (t_DTS) division

config STM32_TIM15_DEADTIME
	int "TIM15 Initial Dead-time"
	default 0
	range 0 255
	---help---
		Timer 15 initial dead-time

if STM32_PWM_MULTICHAN

config STM32_TIM15_CHANNEL1
	bool "TIM15 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32_TIM15_CHANNEL1

config STM32_TIM15_CH1MODE
	int "TIM15 Channel 1 Mode"
	default 6
	range 0 9 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM15_CH1OUT
	bool "TIM15 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32_TIM15_CH1NOUT
	bool "TIM15 Channel 1 Complementary Output"
	default n
	---help---
		Enables channel 1 Complementary Output.

endif # STM32_TIM15_CHANNEL1

config STM32_TIM15_CHANNEL2
	bool "TIM15 Channel 2"
	default n
	---help---
		Enables channel 2.

if STM32_TIM15_CHANNEL2

config STM32_TIM15_CH2MODE
	int "TIM15 Channel 2 Mode"
	default 6
	range 0 9 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM15_CH2OUT
	bool "TIM15 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

endif # STM32_TIM15_CHANNEL2

endif # STM32_PWM_MULTICHAN

if !STM32_PWM_MULTICHAN

config STM32_TIM15_CHANNEL
	int "TIM15 PWM Output Channel"
	default 1
	range 1 2
	---help---
		If TIM15 is enabled for PWM usage, you also need specifies the timer output
		channel {1,2}

if STM32_TIM15_CHANNEL = 1

config STM32_TIM15_CH1OUT
	bool "TIM15 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

config STM32_TIM15_CH1NOUT
	bool "TIM15 Channel 1 Complementary Output"
	default n
	---help---
		Enables channel 1 Complementary Output.

endif # STM32_TIM15_CHANNEL = 1

if STM32_TIM15_CHANNEL = 2

config STM32_TIM15_CH2OUT
	bool "TIM15 Channel 2 Output"
	default n
	---help---
		Enables channel 2 output.

config STM32_TIM15_CH2NOUT
	bool "TIM15 Channel 2 Complementary Output"
	default n
	---help---
		Enables channel 2 Complementary Output.

endif # STM32_TIM15_CHANNEL = 2

config STM32_TIM15_CHMODE
	int "TIM15 Channel Mode"
	default 6
	range 0 9 if STM32_HAVE_IP_TIMERS_V2
	range 0 7 if !STM32_HAVE_IP_TIMERS_V2
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32_PWM_MULTICHAN

endif # STM32_TIM15_PWM

config STM32_TIM16_PWM
	bool "TIM16 PWM"
	default n
	depends on STM32_TIM16
	select STM32_PWM
	---help---
		Reserve timer 16 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32_TIM16
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32_TIM16_PWM

config STM32_TIM16_LOCK
	int "TIM16 Lock Level Configuration"
	default 0
	range 0 3
	---help---
		Timer 16 lock level configuration

config STM32_TIM16_TDTS
	int "TIM16 t_DTS division"
	default 0
	range 0 2
	---help---
		Timer 16 dead-time and sampling clock (t_DTS) division

config STM32_TIM16_DEADTIME
	int "TIM16 Initial Dead-time"
	default 0
	range 0 255
	---help---
		Timer 16 initial dead-time

if STM32_PWM_MULTICHAN

config STM32_TIM16_CHANNEL1
	bool "TIM16 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32_TIM16_CHANNEL1

config STM32_TIM16_CH1MODE
	int "TIM16 Channel 1 Mode"
	default 6
	range 0 7
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM16_CH1OUT
	bool "TIM16 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM16_CHANNEL1

endif # STM32_PWM_MULTICHAN

if !STM32_PWM_MULTICHAN

config STM32_TIM16_CHANNEL
	int "TIM16 PWM Output Channel"
	default 1
	range 1 1
	---help---
		If TIM16 is enabled for PWM usage, you also need specifies the timer output
		channel {1}

if STM32_TIM16_CHANNEL = 1

config STM32_TIM16_CH1OUT
	bool "TIM16 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM16_CHANNEL = 1

config STM32_TIM16_CHMODE
	int "TIM16 Channel Mode"
	default 6
	range 0 7
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32_PWM_MULTICHAN

endif # STM32_TIM16_PWM

config STM32_TIM17_PWM
	bool "TIM17 PWM"
	default n
	depends on STM32_TIM17
	select STM32_PWM
	---help---
		Reserve timer 17 for use by PWM

		Timer devices may be used for different purposes.  One special purpose is
		to generate modulated outputs for such things as motor control.  If STM32_TIM17
		is defined then THIS following may also be defined to indicate that
		the timer is intended to be used for pulsed output modulation.

if STM32_TIM17_PWM

config STM32_TIM17_LOCK
	int "TIM17 Lock Level Configuration"
	default 0
	range 0 3
	---help---
		Timer 17 lock level configuration

config STM32_TIM17_TDTS
	int "TIM17 t_DTS Division"
	default 0
	range 0 2
	---help---
		Timer 17 dead-time and sampling clock (t_DTS) division

config STM32_TIM17_DEADTIME
	int "TIM17 Initial Dead-time"
	default 0
	range 0 255
	---help---
		Timer 17 initial dead-time

if STM32_PWM_MULTICHAN

config STM32_TIM17_CHANNEL1
	bool "TIM17 Channel 1"
	default n
	---help---
		Enables channel 1.

if STM32_TIM17_CHANNEL1

config STM32_TIM17_CH1MODE
	int "TIM17 Channel 1 Mode"
	default 6
	range 0 7
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

config STM32_TIM17_CH1OUT
	bool "TIM17 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM17_CHANNEL1

endif # STM32_PWM_MULTICHAN

if !STM32_PWM_MULTICHAN

config STM32_TIM17_CHANNEL
	int "TIM17 PWM Output Channel"
	default 1
	range 1 1
	---help---
		If TIM17 is enabled for PWM usage, you also need specifies the timer output
		channel {1}

if STM32_TIM17_CHANNEL = 1

config STM32_TIM17_CH1OUT
	bool "TIM17 Channel 1 Output"
	default n
	---help---
		Enables channel 1 output.

endif # STM32_TIM17_CHANNEL = 1

config STM32_TIM17_CHMODE
	int "TIM17 Channel Mode"
	default 6
	range 0 7
	---help---
		Specifies the channel mode. See enum stm32_pwm_chanmode_e in stm32_pwm.h.

endif # !STM32_PWM_MULTICHAN

endif # STM32_TIM17_PWM

config STM32_PWM_MULTICHAN
	bool "PWM Multiple Output Channels"
	default n
	depends on STM32_PWM
	select ARCH_HAVE_PWM_MULTICHAN
	---help---
		Specifies that the PWM driver supports multiple output
		channels per timer.

config STM32_PWM_TRGO
	bool "TIM PWM TRGO support"
	default n
	depends on STM32_PWM
	---help---
		Enable TRGO support for PWM driver

config STM32_TIM1_ADC
	bool "TIM1 ADC"
	default n
	depends on STM32_TIM1 && STM32_ADC
	---help---
		Reserve timer 1 for use by ADC

		Timer devices may be used for different purposes.  If STM32_TIM1 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for ADC conversion. Note that ADC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the ADC, but then you also have to configure which ADC
		channel it is assigned to.

choice
	prompt "Select TIM1 ADC channel"
	default STM32_TIM1_ADC1
	depends on STM32_TIM1_ADC

config STM32_TIM1_ADC1
	bool "TIM1 ADC channel 1"
	depends on STM32_ADC1
	select STM32_HAVE_ADC1_TIMER
	---help---
		Reserve TIM1 to trigger ADC1

config STM32_TIM1_ADC2
	bool "TIM1 ADC channel 2"
	depends on STM32_ADC2
	select STM32_HAVE_ADC2_TIMER
	---help---
		Reserve TIM1 to trigger ADC2

config STM32_TIM1_ADC3
	bool "TIM1 ADC channel 3"
	depends on STM32_ADC3
	select STM32_HAVE_ADC3_TIMER
	---help---
		Reserve TIM1 to trigger ADC3

endchoice

config STM32_TIM2_ADC
	bool "TIM2 ADC"
	default n
	depends on STM32_TIM2 && STM32_ADC
	---help---
		Reserve timer 1 for use by ADC

		Timer devices may be used for different purposes.  If STM32_TIM2 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for ADC conversion. Note that ADC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the ADC, but then you also have to configure which ADC
		channel it is assigned to.

choice
	prompt "Select TIM2 ADC channel"
	default STM32_TIM2_ADC1
	depends on STM32_TIM2_ADC

config STM32_TIM2_ADC1
	bool "TIM2 ADC channel 1"
	depends on STM32_ADC1
	select STM32_HAVE_ADC1_TIMER
	---help---
		Reserve TIM2 to trigger ADC1

config STM32_TIM2_ADC2
	bool "TIM2 ADC channel 2"
	depends on STM32_ADC2
	select STM32_HAVE_ADC2_TIMER
	---help---
		Reserve TIM2 to trigger ADC2

config STM32_TIM2_ADC3
	bool "TIM2 ADC channel 3"
	depends on STM32_ADC3
	select STM32_HAVE_ADC3_TIMER
	---help---
		Reserve TIM2 to trigger ADC3

endchoice

config STM32_TIM3_ADC
	bool "TIM3 ADC"
	default n
	depends on STM32_TIM3 && STM32_ADC
	---help---
		Reserve timer 1 for use by ADC

		Timer devices may be used for different purposes.  If STM32_TIM3 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for ADC conversion. Note that ADC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the ADC, but then you also have to configure which ADC
		channel it is assigned to.

choice
	prompt "Select TIM3 ADC channel"
	default STM32_TIM3_ADC1
	depends on STM32_TIM3_ADC

config STM32_TIM3_ADC1
	bool "TIM3 ADC channel 1"
	depends on STM32_ADC1
	select STM32_HAVE_ADC1_TIMER
	---help---
		Reserve TIM3 to trigger ADC1

config STM32_TIM3_ADC2
	bool "TIM3 ADC channel 2"
	depends on STM32_ADC2
	select STM32_HAVE_ADC2_TIMER
	---help---
		Reserve TIM3 to trigger ADC2

config STM32_TIM3_ADC3
	bool "TIM3 ADC channel 3"
	depends on STM32_ADC3
	select STM32_HAVE_ADC3_TIMER
	---help---
		Reserve TIM3 to trigger ADC3

endchoice

config STM32_TIM4_ADC
	bool "TIM4 ADC"
	default n
	depends on STM32_TIM4 && STM32_ADC
	---help---
		Reserve timer 1 for use by ADC

		Timer devices may be used for different purposes.  If STM32_TIM4 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for ADC conversion. Note that ADC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the ADC, but then you also have to configure which ADC
		channel it is assigned to.

choice
	prompt "Select TIM4 ADC channel"
	default STM32_TIM4_ADC1
	depends on STM32_TIM4_ADC

config STM32_TIM4_ADC1
	bool "TIM4 ADC channel 1"
	depends on STM32_ADC1
	select STM32_HAVE_ADC1_TIMER
	---help---
		Reserve TIM4 to trigger ADC1

config STM32_TIM4_ADC2
	bool "TIM4 ADC channel 2"
	depends on STM32_ADC2
	select STM32_HAVE_ADC2_TIMER
	---help---
		Reserve TIM4 to trigger ADC2

config STM32_TIM4_ADC3
	bool "TIM4 ADC channel 3"
	depends on STM32_ADC3
	select STM32_HAVE_ADC3_TIMER
	---help---
		Reserve TIM4 to trigger ADC3

endchoice

config STM32_TIM5_ADC
	bool "TIM5 ADC"
	default n
	depends on STM32_TIM5 && STM32_ADC
	---help---
		Reserve timer 1 for use by ADC

		Timer devices may be used for different purposes.  If STM32_TIM5 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for ADC conversion. Note that ADC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the ADC, but then you also have to configure which ADC
		channel it is assigned to.

choice
	prompt "Select TIM5 ADC channel"
	default STM32_TIM5_ADC1
	depends on STM32_TIM5_ADC

config STM32_TIM5_ADC1
	bool "TIM5 ADC channel 1"
	depends on STM32_ADC1
	select STM32_HAVE_ADC1_TIMER
	---help---
		Reserve TIM5 to trigger ADC1

config STM32_TIM5_ADC2
	bool "TIM5 ADC channel 2"
	depends on STM32_ADC2
	select STM32_HAVE_ADC2_TIMER
	---help---
		Reserve TIM5 to trigger ADC2

config STM32_TIM5_ADC3
	bool "TIM5 ADC channel 3"
	depends on STM32_ADC3
	select STM32_HAVE_ADC3_TIMER
	---help---
		Reserve TIM5 to trigger ADC3

endchoice

config STM32_TIM8_ADC
	bool "TIM8 ADC"
	default n
	depends on STM32_TIM8 && STM32_ADC
	---help---
		Reserve timer 1 for use by ADC

		Timer devices may be used for different purposes.  If STM32_TIM8 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for ADC conversion. Note that ADC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the ADC, but then you also have to configure which ADC
		channel it is assigned to.

choice
	prompt "Select TIM8 ADC channel"
	default STM32_TIM8_ADC1
	depends on STM32_TIM8_ADC

config STM32_TIM8_ADC1
	bool "TIM8 ADC channel 1"
	depends on STM32_ADC1
	select STM32_HAVE_ADC1_TIMER
	---help---
		Reserve TIM8 to trigger ADC1

config STM32_TIM8_ADC2
	bool "TIM8 ADC channel 2"
	depends on STM32_ADC2
	select STM32_HAVE_ADC2_TIMER
	---help---
		Reserve TIM8 to trigger ADC2

config STM32_TIM8_ADC3
	bool "TIM8 ADC channel 3"
	depends on STM32_ADC3
	select STM32_HAVE_ADC3_TIMER
	---help---
		Reserve TIM8 to trigger ADC3

endchoice

config STM32_HAVE_ADC1_TIMER
	bool

config STM32_HAVE_ADC2_TIMER
	bool

config STM32_HAVE_ADC3_TIMER
	bool

config STM32_HAVE_ADC4_TIMER
	bool

config STM32_HAVE_ADC5_TIMER
	bool

config STM32_ADC1_SAMPLE_FREQUENCY
	int "ADC1 Sampling Frequency"
	default 100
	depends on STM32_HAVE_ADC1_TIMER
	---help---
		ADC1 sampling frequency.  Default:  100Hz

config STM32_ADC1_TIMTRIG
	int "ADC1 Timer Trigger"
	default 0
	range 0 4
	depends on STM32_HAVE_ADC1_TIMER
	---help---
		Values 0:CC1 1:CC2 2:CC3 3:CC4 4:TRGO

config STM32_ADC2_SAMPLE_FREQUENCY
	int "ADC2 Sampling Frequency"
	default 100
	depends on STM32_HAVE_ADC2_TIMER
	---help---
		ADC2 sampling frequency.  Default:  100Hz

config STM32_ADC2_TIMTRIG
	int "ADC2 Timer Trigger"
	default 0
	range 0 4
	depends on STM32_HAVE_ADC2_TIMER
	---help---
		Values 0:CC1 1:CC2 2:CC3 3:CC4 4:TRGO

config STM32_ADC3_SAMPLE_FREQUENCY
	int "ADC3 Sampling Frequency"
	default 100
	depends on STM32_HAVE_ADC3_TIMER
	---help---
		ADC3 sampling frequency.  Default:  100Hz

config STM32_ADC3_TIMTRIG
	int "ADC3 Timer Trigger"
	default 0
	range 0 4
	depends on STM32_HAVE_ADC3_TIMER
	---help---
		Values 0:CC1 1:CC2 2:CC3 3:CC4 4:TRGO

config STM32_TIM1_DAC
	bool "TIM1 DAC"
	default n
	depends on STM32_TIM1 && STM32_DAC
	---help---
		Reserve timer 1 for use by DAC

		Timer devices may be used for different purposes.  If STM32_TIM1 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM1 DAC channel"
	default STM32_TIM1_DAC1
	depends on STM32_TIM1_DAC

config STM32_TIM1_DAC1
	bool "TIM1 DAC channel 1"
	---help---
		Reserve TIM1 to trigger DAC1

config STM32_TIM1_DAC2
	bool "TIM1 DAC channel 2"
	---help---
		Reserve TIM1 to trigger DAC2

endchoice

config STM32_TIM2_DAC
	bool "TIM2 DAC"
	default n
	depends on STM32_TIM2 && STM32_DAC
	---help---
		Reserve timer 2 for use by DAC

		Timer devices may be used for different purposes.  If STM32_TIM2 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM2 DAC channel"
	default STM32_TIM2_DAC1
	depends on STM32_TIM2_DAC

config STM32_TIM2_DAC1
	bool "TIM2 DAC channel 1"
	---help---
		Reserve TIM2 to trigger DAC1

config STM32_TIM2_DAC2
	bool "TIM2 DAC channel 2"
	---help---
		Reserve TIM2 to trigger DAC2

endchoice

config STM32_TIM3_DAC
	bool "TIM3 DAC"
	default n
	depends on STM32_TIM3 && STM32_DAC
	---help---
		Reserve timer 3 for use by DAC

		Timer devices may be used for different purposes.  If STM32_TIM3 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM3 DAC channel"
	default STM32_TIM3_DAC1
	depends on STM32_TIM3_DAC

config STM32_TIM3_DAC1
	bool "TIM3 DAC channel 1"
	---help---
		Reserve TIM3 to trigger DAC1

config STM32_TIM3_DAC2
	bool "TIM3 DAC channel 2"
	---help---
		Reserve TIM3 to trigger DAC2

endchoice

config STM32_TIM4_DAC
	bool "TIM4 DAC"
	default n
	depends on STM32_TIM4 && STM32_DAC
	---help---
		Reserve timer 4 for use by DAC

		Timer devices may be used for different purposes.  If STM32_TIM4 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM4 DAC channel"
	default STM32_TIM4_DAC1
	depends on STM32_TIM4_DAC

config STM32_TIM4_DAC1
	bool "TIM4 DAC channel 1"
	---help---
		Reserve TIM4 to trigger DAC1

config STM32_TIM4_DAC2
	bool "TIM4 DAC channel 2"
	---help---
		Reserve TIM4 to trigger DAC2

endchoice

config STM32_TIM5_DAC
	bool "TIM5 DAC"
	default n
	depends on STM32_TIM5 && STM32_DAC
	---help---
		Reserve timer 5 for use by DAC

		Timer devices may be used for different purposes.  If STM32_TIM5 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM5 DAC channel"
	default STM32_TIM5_DAC1
	depends on STM32_TIM5_DAC

config STM32_TIM5_DAC1
	bool "TIM5 DAC channel 1"
	---help---
		Reserve TIM5 to trigger DAC1

config STM32_TIM5_DAC2
	bool "TIM5 DAC channel 2"
	---help---
		Reserve TIM5 to trigger DAC2

endchoice

config STM32_TIM6_DAC
	bool "TIM6 DAC"
	default n
	depends on STM32_TIM6 && STM32_DAC
	---help---
		Reserve timer 6 for use by DAC

		Timer devices may be used for different purposes.  If STM32_TIM6 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM6 DAC channel"
	default STM32_TIM6_DAC1
	depends on STM32_TIM6_DAC

config STM32_TIM6_DAC1
	bool "TIM6 DAC channel 1"
	---help---
		Reserve TIM6 to trigger DAC1

config STM32_TIM6_DAC2
	bool "TIM6 DAC channel 2"
	---help---
		Reserve TIM6 to trigger DAC2

endchoice

config STM32_TIM7_DAC
	bool "TIM7 DAC"
	default n
	depends on STM32_TIM7 && STM32_DAC
	---help---
		Reserve timer 7 for use by DAC

		Timer devices may be used for different purposes.  If STM32_TIM7 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM7 DAC channel"
	default STM32_TIM7_DAC1
	depends on STM32_TIM7_DAC

config STM32_TIM7_DAC1
	bool "TIM7 DAC channel 1"
	---help---
		Reserve TIM7 to trigger DAC1

config STM32_TIM7_DAC2
	bool "TIM7 DAC channel 2"
	---help---
		Reserve TIM7 to trigger DAC2

endchoice

config STM32_TIM8_DAC
	bool "TIM8 DAC"
	default n
	depends on STM32_TIM8 && STM32_DAC
	---help---
		Reserve timer 8 for use by DAC

		Timer devices may be used for different purposes.  If STM32_TIM8 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM8 DAC channel"
	default STM32_TIM8_DAC1
	depends on STM32_TIM8_DAC

config STM32_TIM8_DAC1
	bool "TIM8 DAC channel 1"
	---help---
		Reserve TIM8 to trigger DAC1

config STM32_TIM8_DAC2
	bool "TIM8 DAC channel 2"
	---help---
		Reserve TIM8 to trigger DAC2

endchoice

config STM32_TIM9_DAC
	bool "TIM9 DAC"
	default n
	depends on STM32_TIM9 && STM32_DAC
	---help---
		Reserve timer 9 for use by DAC

		Timer devices may be used for different purposes.  If STM32_TIM9 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM9 DAC channel"
	default STM32_TIM9_DAC1
	depends on STM32_TIM9_DAC

config STM32_TIM9_DAC1
	bool "TIM9 DAC channel 1"
	---help---
		Reserve TIM9 to trigger DAC1

config STM32_TIM9_DAC2
	bool "TIM9 DAC channel 2"
	---help---
		Reserve TIM9 to trigger DAC2

endchoice

config STM32_TIM10_DAC
	bool "TIM10 DAC"
	default n
	depends on STM32_TIM10 && STM32_DAC
	---help---
		Reserve timer 10 for use by DAC

		Timer devices may be used for different purposes.  If STM32_TIM10 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM10 DAC channel"
	default STM32_TIM10_DAC1
	depends on STM32_TIM10_DAC

config STM32_TIM10_DAC1
	bool "TIM10 DAC channel 1"
	---help---
		Reserve TIM10 to trigger DAC1

config STM32_TIM10_DAC2
	bool "TIM10 DAC channel 2"
	---help---
		Reserve TIM10 to trigger DAC2

endchoice

config STM32_TIM11_DAC
	bool "TIM11 DAC"
	default n
	depends on STM32_TIM11 && STM32_DAC
	---help---
		Reserve timer 11 for use by DAC

		Timer devices may be used for different purposes.  If STM32_TIM11 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM11 DAC channel"
	default STM32_TIM11_DAC1
	depends on STM32_TIM11_DAC

config STM32_TIM11_DAC1
	bool "TIM11 DAC channel 1"
	---help---
		Reserve TIM11 to trigger DAC1

config STM32_TIM11_DAC2
	bool "TIM11 DAC channel 2"
	---help---
		Reserve TIM11 to trigger DAC2

endchoice

config STM32_TIM12_DAC
	bool "TIM12 DAC"
	default n
	depends on STM32_TIM12 && STM32_DAC
	---help---
		Reserve timer 12 for use by DAC

		Timer devices may be used for different purposes.  If STM32_TIM12 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM12 DAC channel"
	default STM32_TIM12_DAC1
	depends on STM32_TIM12_DAC

config STM32_TIM12_DAC1
	bool "TIM12 DAC channel 1"
	---help---
		Reserve TIM12 to trigger DAC1

config STM32_TIM12_DAC2
	bool "TIM12 DAC channel 2"
	---help---
		Reserve TIM12 to trigger DAC2

endchoice

config STM32_TIM13_DAC
	bool "TIM13 DAC"
	default n
	depends on STM32_TIM13 && STM32_DAC
	---help---
		Reserve timer 13 for use by DAC

		Timer devices may be used for different purposes.  If STM32_TIM13 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM13 DAC channel"
	default STM32_TIM13_DAC1
	depends on STM32_TIM13_DAC

config STM32_TIM13_DAC1
	bool "TIM13 DAC channel 1"
	---help---
		Reserve TIM13 to trigger DAC1

config STM32_TIM13_DAC2
	bool "TIM13 DAC channel 2"
	---help---
		Reserve TIM13 to trigger DAC2

endchoice

config STM32_TIM14_DAC
	bool "TIM14 DAC"
	default n
	depends on STM32_TIM14 && STM32_DAC
	---help---
		Reserve timer 14 for use by DAC

		Timer devices may be used for different purposes.  If STM32_TIM14 is
		defined then the following may also be defined to indicate that the
		timer is intended to be used for DAC conversion. Note that DAC usage
		requires two definition:  Not only do you have to assign the timer
		for used by the DAC, but then you also have to configure which DAC
		channel it is assigned to.

choice
	prompt "Select TIM14 DAC channel"
	default STM32_TIM14_DAC1
	depends on STM32_TIM14_DAC

config STM32_TIM14_DAC1
	bool "TIM14 DAC channel 1"
	---help---
		Reserve TIM14 to trigger DAC1

config STM32_TIM14_DAC2
	bool "TIM14 DAC channel 2"
	---help---
		Reserve TIM14 to trigger DAC2

endchoice

config STM32_TIM1_CAP
	bool "TIM1 Capture"
	default n
	depends on STM32_TIM1
	select STM32_CAP
	---help---
		Reserve timer 1 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

if STM32_TIM1_CAP

config STM32_TIM1_CHANNEL
	int "TIM1 Capture Input Channel"
	default 1
	range 1 4
	---help---
		If TIM1 is enabled for capture usage, you also need specifies the timer input
		channel {1,..,4}

config STM32_TIM1_CLOCK
	int "TIM1 work frequence for capture"
	default 1000000
	---help---
		This clock frequence limiting the count rate at the expense of resolution.

endif # STM32_TIM1_CAP

config STM32_TIM2_CAP
	bool "TIM2 Capture"
	default n
	depends on STM32_TIM2
	select STM32_CAP
	---help---
		Reserve timer 2 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

if STM32_TIM2_CAP

config STM32_TIM2_CHANNEL
	int "TIM2 Capture Input Channel"
	default 1
	range 1 4
	---help---
		If TIM2 is enabled for capture usage, you also need specifies the timer input
		channel {1,..,4}

config STM32_TIM2_CLOCK
	int "TIM2 work frequence for capture"
	default 1000000
	---help---
		This clock frequence limiting the count rate at the expense of resolution.

endif # STM32_TIM2_CAP

config STM32_TIM3_CAP
	bool "TIM3 Capture"
	default n
	depends on STM32_TIM3
	select STM32_CAP
	---help---
		Reserve timer 3 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

if STM32_TIM3_CAP

config STM32_TIM3_CHANNEL
	int "TIM3 Capture Input Channel"
	default 1
	range 1 4
	---help---
		If TIM3 is enabled for capture usage, you also need specifies the timer input
		channel {1,..,4}

config STM32_TIM3_CLOCK
	int "TIM3 work frequence for capture"
	default 1000000
	---help---
		This clock frequence limiting the count rate at the expense of resolution.

endif # STM32_TIM3_CAP

config STM32_TIM4_CAP
	bool "TIM4 Capture"
	default n
	depends on STM32_TIM4
	select STM32_CAP
	---help---
		Reserve timer 4 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

if STM32_TIM4_CAP

config STM32_TIM4_CHANNEL
	int "TIM4 Capture Input Channel"
	default 1
	range 1 4
	---help---
		If TIM4 is enabled for capture usage, you also need specifies the timer input
		channel {1,..,4}

config STM32_TIM4_CLOCK
	int "TIM4 work frequence for capture"
	default 1000000
	---help---
		This clock frequence limiting the count rate at the expense of resolution.

endif # STM32_TIM4_CAP

config STM32_TIM5_CAP
	bool "TIM5 Capture"
	default n
	depends on STM32_TIM5
	select STM32_CAP
	---help---
		Reserve timer 5 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

if STM32_TIM5_CAP

config STM32_TIM5_CHANNEL
	int "TIM5 Capture Input Channel"
	default 1
	range 1 4
	---help---
		If TIM5 is enabled for capture usage, you also need specifies the timer input
		channel {1,..,4}

config STM32_TIM5_CLOCK
	int "TIM5 work frequence for capture"
	default 1000000
	---help---
		This clock frequence limiting the count rate at the expense of resolution.

endif # STM32_TIM5_CAP

config STM32_TIM8_CAP
	bool "TIM8 Capture"
	default n
	depends on STM32_TIM8
	select STM32_CAP
	---help---
		Reserve timer 8 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

if STM32_TIM8_CAP

config STM32_TIM8_CHANNEL
	int "TIM8 Capture Input Channel"
	default 1
	range 1 4
	---help---
		If TIM8 is enabled for capture usage, you also need specifies the timer input
		channel {1,..,4}

config STM32_TIM8_CLOCK
	int "TIM8 work frequence for capture"
	default 1000000
	---help---
		This clock frequence limiting the count rate at the expense of resolution.

endif # STM32_TIM8_CAP

config STM32_TIM9_CAP
	bool "TIM9 Capture"
	default n
	depends on STM32_TIM9
	select STM32_CAP
	---help---
		Reserve timer 9 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

if STM32_TIM9_CAP

config STM32_TIM9_CHANNEL
	int "TIM9 Capture Input Channel"
	default 1
	range 1 4
	---help---
		If TIM9 is enabled for capture usage, you also need specifies the timer input
		channel {1,..,4}

config STM32_TIM9_CLOCK
	int "TIM9 work frequence for capture"
	default 1000000
	---help---
		This clock frequence limiting the count rate at the expense of resolution.

endif # STM32_TIM9_CAP

config STM32_TIM10_CAP
	bool "TIM10 Capture"
	default n
	depends on STM32_TIM10
	select STM32_CAP
	---help---
		Reserve timer 10 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

if STM32_TIM10_CAP

config STM32_TIM10_CHANNEL
	int "TIM10 Capture Input Channel"
	default 1
	range 1 4
	---help---
		If TIM10 is enabled for capture usage, you also need specifies the timer input
		channel {1,..,4}

config STM32_TIM10_CLOCK
	int "TIM10 work frequence for capture"
	default 1000000
	---help---
		This clock frequence limiting the count rate at the expense of resolution.

endif # STM32_TIM10_CAP

config STM32_TIM11_CAP
	bool "TIM11 Capture"
	default n
	depends on STM32_TIM11
	select STM32_CAP
	---help---
		Reserve timer 11 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

if STM32_TIM11_CAP

config STM32_TIM11_CHANNEL
	int "TIM11 Capture Input Channel"
	default 1
	range 1 4
	---help---
		If TIM11 is enabled for capture usage, you also need specifies the timer input
		channel {1,..,4}

config STM32_TIM11_CLOCK
	int "TIM11 work frequence for capture"
	default 1000000
	---help---
		This clock frequence limiting the count rate at the expense of resolution.

endif # STM32_TIM11_CAP

config STM32_TIM12_CAP
	bool "TIM12 Capture"
	default n
	depends on STM32_TIM12
	select STM32_CAP
	---help---
		Reserve timer 12 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

if STM32_TIM12_CAP

config STM32_TIM12_CHANNEL
	int "TIM12 Capture Input Channel"
	default 1
	range 1 4
	---help---
		If TIM12 is enabled for capture usage, you also need specifies the timer input
		channel {1,..,4}

config STM32_TIM12_CLOCK
	int "TIM12 work frequence for capture"
	default 1000000
	---help---
		This clock frequence limiting the count rate at the expense of resolution.

endif # STM32_TIM12_CAP

config STM32_TIM13_CAP
	bool "TIM13 Capture"
	default n
	depends on STM32_TIM13
	select STM32_CAP
	---help---
		Reserve timer 13 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

if STM32_TIM13_CAP

config STM32_TIM13_CHANNEL
	int "TIM13 Capture Input Channel"
	default 1
	range 1 4
	---help---
		If TIM13 is enabled for capture usage, you also need specifies the timer input
		channel {1,..,4}

config STM32_TIM13_CLOCK
	int "TIM13 work frequence for capture"
	default 1000000
	---help---
		This clock frequence limiting the count rate at the expense of resolution.

endif # STM32_TIM13_CAP

config STM32_TIM14_CAP
	bool "TIM14 Capture"
	default n
	depends on STM32_TIM14
	select STM32_CAP
	---help---
		Reserve timer 14 for use by Capture

		Timer devices may be used for different purposes.  One special purpose is
		to capture input.

if STM32_TIM14_CAP

config STM32_TIM14_CHANNEL
	int "TIM14 Capture Input Channel"
	default 1
	range 1 4
	---help---
		If TIM14 is enabled for capture usage, you also need specifies the timer input
		channel {1,..,4}

config STM32_TIM14_CLOCK
	int "TIM14 work frequence for capture"
	default 1000000
	---help---
		This clock frequence limiting the count rate at the expense of resolution.

endif # STM32_TIM14_CAP

menu "STM32 TIMx Outputs Configuration"

config STM32_TIM1_CH1POL
	int "TIM1 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM1_CH1OUT
	---help---
		TIM1 Channel 1 output polarity

config STM32_TIM1_CH1IDLE
	int "TIM1 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM1_CH1OUT
	---help---
		TIM1 Channel 1 output IDLE

config STM32_TIM1_CH1NPOL
	int "TIM1 Channel 1 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM1_CH1NOUT
	---help---
		TIM1 Channel 1 Complementary Output polarity

config STM32_TIM1_CH1NIDLE
	int "TIM1 Channel 1 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM1_CH1NOUT
	---help---
		TIM1 Channel 1 Complementary Output IDLE

config STM32_TIM1_CH2POL
	int "TIM1 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM1_CH2OUT
	---help---
		TIM1 Channel 2 output polarity

config STM32_TIM1_CH2IDLE
	int "TIM1 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM1_CH2OUT
	---help---
		TIM1 Channel 2 output IDLE

config STM32_TIM1_CH2NPOL
	int "TIM1 Channel 2 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM1_CH2NOUT
	---help---
		TIM1 Channel 2 Complementary Output polarity

config STM32_TIM1_CH2NIDLE
	int "TIM1 Channel 2 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM1_CH2NOUT
	---help---
		TIM1 Channel 2 Complementary Output IDLE

config STM32_TIM1_CH3POL
	int "TIM1 Channel 3 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM1_CH3OUT
	---help---
		TIM1 Channel 3 output polarity

config STM32_TIM1_CH3IDLE
	int "TIM1 Channel 3 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM1_CH3OUT
	---help---
		TIM1 Channel 3 output IDLE

config STM32_TIM1_CH3NPOL
	int "TIM1 Channel 3 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM1_CH3NOUT
	---help---
		TIM1 Channel 3 Complementary Output polarity

config STM32_TIM1_CH3NIDLE
	int "TIM1 Channel 3 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM1_CH3NOUT
	---help---
		TIM1 Channel 3 Complementary Output IDLE

config STM32_TIM1_CH4POL
	int "TIM1 Channel 4 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM1_CH4OUT
	---help---
		TIM1 Channel 4 output polarity

config STM32_TIM1_CH4IDLE
	int "TIM1 Channel 4 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM1_CH4OUT
	---help---
		TIM1 Channel 4 output IDLE

config STM32_TIM1_CH5POL
	int "TIM1 Channel 5 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM1_CH5OUT
	---help---
		TIM1 Channel 5 output polarity

config STM32_TIM1_CH5IDLE
	int "TIM1 Channel 5 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM1_CH5OUT
	---help---
		TIM1 Channel 5 output IDLE

config STM32_TIM1_CH6POL
	int "TIM1 Channel 6 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM1_CH6OUT
	---help---
		TIM1 Channel 6 output polarity

config STM32_TIM1_CH6IDLE
	int "TIM1 Channel 6 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM1_CH6OUT
	---help---
		TIM1 Channel 6 output IDLE

config STM32_TIM2_CH1POL
	int "TIM2 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM2_CH1OUT
	---help---
		TIM2 Channel 1 output polarity

config STM32_TIM2_CH1IDLE
	int "TIM2 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM2_CH1OUT
	---help---
		TIM2 Channel 1 output IDLE

config STM32_TIM2_CH2POL
	int "TIM2 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM2_CH2OUT
	---help---
		TIM2 Channel 2 output polarity

config STM32_TIM2_CH2IDLE
	int "TIM2 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM2_CH2OUT
	---help---
		TIM2 Channel 2 output IDLE

config STM32_TIM2_CH3POL
	int "TIM2 Channel 3 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM2_CH3OUT
	---help---
		TIM2 Channel 3 output polarity

config STM32_TIM2_CH3IDLE
	int "TIM2 Channel 3 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM2_CH3OUT
	---help---
		TIM2 Channel 3 output IDLE

config STM32_TIM2_CH4POL
	int "TIM2 Channel 4 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM2_CH4OUT
	---help---
		TIM2 Channel 4 output polarity

config STM32_TIM2_CH4IDLE
	int "TIM2 Channel 4 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM2_CH4OUT
	---help---
		TIM2 Channel 4 output IDLE

config STM32_TIM3_CH1POL
	int "TIM3 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM3_CH1OUT
	---help---
		TIM3 Channel 1 output polarity

config STM32_TIM3_CH1IDLE
	int "TIM3 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM3_CH1OUT
	---help---
		TIM3 Channel 1 output IDLE

config STM32_TIM3_CH2POL
	int "TIM3 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM3_CH2OUT
	---help---
		TIM3 Channel 2 output polarity

config STM32_TIM3_CH2IDLE
	int "TIM3 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM3_CH2OUT
	---help---
		TIM3 Channel 2 output IDLE

config STM32_TIM3_CH3POL
	int "TIM3 Channel 3 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM3_CH3OUT
	---help---
		TIM3 Channel 3 output polarity

config STM32_TIM3_CH3IDLE
	int "TIM3 Channel 3 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM3_CH3OUT
	---help---
		TIM3 Channel 3 output IDLE

config STM32_TIM3_CH4POL
	int "TIM3 Channel 4 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM3_CH4OUT
	---help---
		TIM3 Channel 4 output polarity

config STM32_TIM3_CH4IDLE
	int "TIM3 Channel 4 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM3_CH4OUT
	---help---
		TIM3 Channel 4 output IDLE

config STM32_TIM4_CH1POL
	int "TIM4 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM4_CH1OUT
	---help---
		TIM4 Channel 1 output polarity

config STM32_TIM4_CH1IDLE
	int "TIM4 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM4_CH1OUT
	---help---
		TIM4 Channel 1 output IDLE

config STM32_TIM4_CH2POL
	int "TIM4 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM4_CH2OUT
	---help---
		TIM4 Channel 2 output polarity

config STM32_TIM4_CH2IDLE
	int "TIM4 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM4_CH2OUT
	---help---
		TIM4 Channel 2 output IDLE

config STM32_TIM4_CH3POL
	int "TIM4 Channel 3 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM4_CH3OUT
	---help---
		TIM4 Channel 3 output polarity

config STM32_TIM4_CH3IDLE
	int "TIM4 Channel 3 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM4_CH3OUT
	---help---
		TIM4 Channel 3 output IDLE

config STM32_TIM4_CH4POL
	int "TIM4 Channel 4 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM4_CH4OUT
	---help---
		TIM4 Channel 4 output polarity

config STM32_TIM4_CH4IDLE
	int "TIM4 Channel 4 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM4_CH4OUT
	---help---
		TIM4 Channel 4 output IDLE

config STM32_TIM5_CH1POL
	int "TIM5 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM5_CH1OUT
	---help---
		TIM5 Channel 1 output polarity

config STM32_TIM5_CH1IDLE
	int "TIM5 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM5_CH1OUT
	---help---
		TIM5 Channel 1 output IDLE

config STM32_TIM5_CH2POL
	int "TIM5 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM5_CH2OUT
	---help---
		TIM5 Channel 2 output polarity

config STM32_TIM5_CH2IDLE
	int "TIM5 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM5_CH2OUT
	---help---
		TIM5 Channel 2 output IDLE

config STM32_TIM5_CH3POL
	int "TIM5 Channel 3 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM5_CH3OUT
	---help---
		TIM5 Channel 3 output polarity

config STM32_TIM5_CH3IDLE
	int "TIM5 Channel 3 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM5_CH3OUT
	---help---
		TIM5 Channel 3 output IDLE

config STM32_TIM5_CH4POL
	int "TIM5 Channel 4 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM5_CH4OUT
	---help---
		TIM5 Channel 4 output polarity

config STM32_TIM5_CH4IDLE
	int "TIM5 Channel 4 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM5_CH4OUT
	---help---
		TIM5 Channel 4 output IDLE

config STM32_TIM8_CH1POL
	int "TIM8 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM8_CH1OUT
	---help---
		TIM8 Channel 1 output polarity

config STM32_TIM8_CH1IDLE
	int "TIM8 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM8_CH1OUT
	---help---
		TIM8 Channel 1 output IDLE

config STM32_TIM8_CH1NPOL
	int "TIM8 Channel 1 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM8_CH1NOUT
	---help---
		TIM8 Channel 1 Complementary Output polarity

config STM32_TIM8_CH1NIDLE
	int "TIM8 Channel 1 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM8_CH1NOUT
	---help---
		TIM8 Channel 1 Complementary Output IDLE

config STM32_TIM8_CH2POL
	int "TIM8 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM8_CH2OUT
	---help---
		TIM8 Channel 2 output polarity

config STM32_TIM8_CH2IDLE
	int "TIM8 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM8_CH2OUT
	---help---
		TIM8 Channel 2 output IDLE

config STM32_TIM8_CH2NPOL
	int "TIM8 Channel 2 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM8_CH2NOUT
	---help---
		TIM8 Channel 2 Complementary Output polarity

config STM32_TIM8_CH2NIDLE
	int "TIM8 Channel 2 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM8_CH2NOUT
	---help---
		TIM8 Channel 2 Complementary Output IDLE

config STM32_TIM8_CH3POL
	int "TIM8 Channel 3 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM8_CH3OUT
	---help---
		TIM8 Channel 3 output polarity

config STM32_TIM8_CH3IDLE
	int "TIM8 Channel 3 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM8_CH3OUT
	---help---
		TIM8 Channel 3 output IDLE

config STM32_TIM8_CH3NPOL
	int "TIM8 Channel 3 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM8_CH3NOUT
	---help---
		TIM8 Channel 3 Complementary Output polarity

config STM32_TIM8_CH3NIDLE
	int "TIM8 Channel 3 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM8_CH3NOUT
	---help---
		TIM8 Channel 3 Complementary Output IDLE

config STM32_TIM8_CH4POL
	int "TIM8 Channel 4 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM8_CH4OUT
	---help---
		TIM8 Channel 4 output polarity

config STM32_TIM8_CH4IDLE
	int "TIM8 Channel 4 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM8_CH4OUT
	---help---
		TIM8 Channel 4 output IDLE

config STM32_TIM8_CH5POL
	int "TIM8 Channel 5 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM8_CH5OUT
	---help---
		TIM8 Channel 5 output polarity

config STM32_TIM8_CH5IDLE
	int "TIM8 Channel 5 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM8_CH5OUT
	---help---
		TIM8 Channel 5 output IDLE

config STM32_TIM8_CH6POL
	int "TIM8 Channel 6 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM8_CH6OUT
	---help---
		TIM8 Channel 6 output polarity

config STM32_TIM8_CH6IDLE
	int "TIM8 Channel 6 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM8_CH6OUT
	---help---
		TIM8 Channel 6 output IDLE

config STM32_TIM9_CH1POL
	int "TIM9 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM9_CH1OUT
	---help---
		TIM9 Channel 1 output polarity

config STM32_TIM9_CH1IDLE
	int "TIM9 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM9_CH1OUT
	---help---
		TIM9 Channel 1 output IDLE

config STM32_TIM9_CH2POL
	int "TIM9 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM9_CH2OUT
	---help---
		TIM9 Channel 2 output polarity

config STM32_TIM9_CH2IDLE
	int "TIM9 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM9_CH2OUT
	---help---
		TIM9 Channel 2 output IDLE

config STM32_TIM10_CH1POL
	int "TIM10 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM10_CH1OUT
	---help---
		TIM10 Channel 1 output polarity

config STM32_TIM10_CH1IDLE
	int "TIM10 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM10_CH1OUT
	---help---
		TIM10 Channel 1 output IDLE

config STM32_TIM11_CH1POL
	int "TIM11 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM11_CH1OUT
	---help---
		TIM11 Channel 1 output polarity

config STM32_TIM11_CH1IDLE
	int "TIM11 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM11_CH1OUT
	---help---
		TIM11 Channel 1 output IDLE

config STM32_TIM12_CH1POL
	int "TIM12 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM12_CH1OUT
	---help---
		TIM12 Channel 1 output polarity

config STM32_TIM12_CH1IDLE
	int "TIM12 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM12_CH1OUT
	---help---
		TIM12 Channel 1 output IDLE

config STM32_TIM12_CH2POL
	int "TIM12 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM12_CH2OUT
	---help---
		TIM12 Channel 2 output polarity

config STM32_TIM12_CH2IDLE
	int "TIM12 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM12_CH2OUT
	---help---
		TIM12 Channel 2 output IDLE

config STM32_TIM13_CH1POL
	int "TIM13 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM13_CH1OUT
	---help---
		TIM13 Channel 1 output polarity

config STM32_TIM13_CH1IDLE
	int "TIM13 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM13_CH1OUT
	---help---
		TIM13 Channel 1 output IDLE

config STM32_TIM14_CH1POL
	int "TIM14 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM14_CH1OUT
	---help---
		TIM14 Channel 1 output polarity

config STM32_TIM14_CH1IDLE
	int "TIM14 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM14_CH1OUT
	---help---
		TIM14 Channel 1 output IDLE

config STM32_TIM15_CH1POL
	int "TIM15 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM15_CH1OUT
	---help---
		TIM15 Channel 1 output polarity

config STM32_TIM15_CH1IDLE
	int "TIM15 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM15_CH1OUT
	---help---
		TIM15 Channel 1 output IDLE

config STM32_TIM15_CH1NPOL
	int "TIM15 Channel 1 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM15_CH1NOUT
	---help---
		TIM15 Channel 1 Complementary Output polarity

config STM32_TIM15_CH1NIDLE
	int "TIM15 Channel 1 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM15_CH1NOUT
	---help---
		TIM15 Channel 1 Complementary Output IDLE

config STM32_TIM15_CH2POL
	int "TIM15 Channel 2 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM15_CH2OUT
	---help---
		TIM15 Channel 2 output polarity

config STM32_TIM15_CH2IDLE
	int "TIM15 Channel 2 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM15_CH2OUT
	---help---
		TIM15 Channel 2 output IDLE

config STM32_TIM15_CH2NPOL
	int "TIM15 Channel 2 Complementary Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM15_CH2NOUT
	---help---
		TIM15 Channel 2 Complementary Output polarity

config STM32_TIM15_CH2NIDLE
	int "TIM15 Channel 2 Complementary Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM15_CH2NOUT
	---help---
		TIM15 Channel 2 Complementary Output IDLE

config STM32_TIM16_CH1POL
	int "TIM16 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM16_CH1OUT
	---help---
		TIM16 Channel 1 output polarity

config STM32_TIM16_CH1IDLE
	int "TIM16 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM16_CH1OUT
	---help---
		TIM16 Channel 1 output IDLE

config STM32_TIM17_CH1POL
	int "TIM17 Channel 1 Output polarity"
	default 0
	range 0 1
	depends on STM32_TIM17_CH1OUT
	---help---
		TIM17 Channel 1 output polarity

config STM32_TIM17_CH1IDLE
	int "TIM17 Channel 1 Output IDLE"
	default 0
	range 0 1
	depends on STM32_TIM17_CH1OUT
	---help---
		TIM17 Channel 1 output IDLE

endmenu #STM32 TIMx Outputs Configuration

endmenu # Timer Configuration

menu "HRTIM Configuration"
	depends on STM32_HRTIM

config STM32_HRTIM_DISABLE_CHARDRV
	bool "HRTIM Disable Character Driver"
	default n
	---help---
		In most cases we do not need HRTIM Character Driver, so we can disable it
		and save some memory.

config STM32_HRTIM_NO_ENABLE_TIMERS
	bool "Do not enable HRTIM timers at startup"
	default n
	---help---
		Do not enable HRTIM timers at startup

menuconfig STM32_HRTIM_ADC
	bool "HRTIM ADC Triggering"
	default n
	---help---
		Enable HRTIM ADC Triggering support.

if STM32_HRTIM_ADC

config STM32_HRTIM_ADC1_TRG1
	bool "HRTIM ADC1 Trigger 1"
	default n

config STM32_HRTIM_ADC1_TRG2
	bool "HRTIM ADC1 Trigger 2"
	default n

config STM32_HRTIM_ADC1_TRG3
	bool "HRTIM ADC1 Trigger 3"
	default n

config STM32_HRTIM_ADC1_TRG4
	bool "HRTIM ADC1 Trigger 4"
	default n

config STM32_HRTIM_ADC2_TRG1
	bool "HRTIM ADC2 Trigger 1"
	default n

config STM32_HRTIM_ADC2_TRG2
	bool "HRTIM ADC2 Trigger 2"
	default n

config STM32_HRTIM_ADC2_TRG3
	bool "HRTIM ADC2 Trigger 3"
	default n

config STM32_HRTIM_ADC2_TRG4
	bool "HRTIM ADC2 Trigger 4"
	default n

endif # STM32_HRTIM_ADC

config STM32_HRTIM_DAC
	bool "HRTIM DAC Triggering"
	default n
	---help---
		Enable HRTIM DAC Triggering support.

config STM32_HRTIM_PWM
	bool "HRTIM PWM Outputs"
	default n
	---help---
		Enable HRTIM PWM Outputs support.

config STM32_HRTIM_CAP
	bool "HRTIM Capture"
	default n
	---help---
		Enable HRTIM Capture support.

config STM32_HRTIM_INTERRUPTS
	bool "HRTIM Interrupts"
	default n
	---help---
		Enable HRTIM Interrupts support.

config STM32_HRTIM_BURST
	bool "HRTIM Burst Mode"
	depends on STM32_HRTIM_PWM
	default n
	---help---
		Enable HRTIM Burst Mode support for PWM outputs.

config STM32_HRTIM_DEADTIME
	bool "HRTIM Dead-time"
	depends on STM32_HRTIM_PWM
	default n
	---help---
		Enable HRTIM Deadtime support for PWM outputs.

config STM32_HRTIM_PUSHPULL
	bool "HRTIM Push-Pull Mode"
	depends on STM32_HRTIM_PWM
	default n
	---help---
		Enable HRTIM Push-Pull Mode support for PWM outputs.

config STM32_HRTIM_CHOPPER
	bool "HRTIM Chopper"
	depends on STM32_HRTIM_PWM
	default n
	---help---
		Enable HRTIM Chopper Mode for PWM outputs.

config STM32_HRTIM_DMA
	bool "HRTIM DMA"
	default n

config STM32_HRTIM_DMABURST
	bool "HRTIM DMA Burst"
	default n

config STM32_HRTIM_AUTODELAY
	bool "HRTIM Autodelay"
	depends on STM32_HRTIM_PWM
	default n

menuconfig STM32_HRTIM_EVENTS
	bool "HRTIM Events Configuration"
	default n
	---help---
		Enable HRTIM Events support.

if STM32_HRTIM_EVENTS

config STM32_HRTIM_EEV1
	bool "HRTIM EEV1"
	default n

config STM32_HRTIM_EEV2
	bool "HRTIM EEV2"
	default n

config STM32_HRTIM_EEV3
	bool "HRTIM EEV3"
	default n

config STM32_HRTIM_EEV4
	bool "HRTIM EEV4"
	default n

config STM32_HRTIM_EEV5
	bool "HRTIM EEV5"
	default n

config STM32_HRTIM_EEV6
	bool "HRTIM EEV6"
	default n

config STM32_HRTIM_EEV7
	bool "HRTIM EEV7"
	default n

config STM32_HRTIM_EEV8
	bool "HRTIM EEV8"
	default n

config STM32_HRTIM_EEV9
	bool "HRTIM EEV9"
	default n

config STM32_HRTIM_EEV10
	bool "HRTIM EEV10"
	default n

endif # STM32_HRTIM_EVENTS

menuconfig STM32_HRTIM_FAULTS
	bool "HRTIM Faults Configuration"
	default n
	---help---
		Enable HRTIM Faults support.

if STM32_HRTIM_FAULTS

config STM32_HRTIM_FAULT1
	bool "HRTIM Fault 1"
	default n

config STM32_HRTIM_FAULT2
	bool "HRTIM Fault 2"
	default n

config STM32_HRTIM_FAULT3
	bool "HRTIM Fault 3"
	default n

config STM32_HRTIM_FAULT4
	bool "HRTIM Fault 4"
	default n

endif # STM32_HRTIM_FAULTS

config STM32_HRTIM_CLK_FROM_PLL
	bool "HRTIM Clock from PLL"
	default n
	---help---
		Set PLL as the clock source for HRTIM.
		This configuration requires the following conditions:
		  1) system clock is PLL,
			2) SYSCLK and PCLK2 ratio must be 1 o 2.

menu "HRTIM Master Configuration"
	depends on STM32_HRTIM_MASTER

config STM32_HRTIM_MASTER_DAC
	bool "HRTIM Master DAC Triggering"
	default n
	depends on STM32_HRTIM_DAC

config STM32_HRTIM_MASTER_DMA
	bool "HRTIM MASTER DMA"
	default n
	depends on STM32_HRTIM_DMA

config STM32_HRTIM_MASTER_IRQ
	bool "HRTIM MASTER Interrupts"
	default n
	depends on STM32_HRTIM_INTERRUPTS

endmenu # "HRTIM Master Configuration"

menu "HRTIM Timer A Configuration"
	depends on STM32_HRTIM_TIMA

config STM32_HRTIM_TIMA_CAP
	bool "HRTIM TIMA Capture"
	default n
	depends on STM32_HRTIM_CAPTURE

config STM32_HRTIM_TIMA_DAC
	bool "HRTIM TIMA DAC Triggering"
	default n
	depends on STM32_HRTIM_DAC

config STM32_HRTIM_TIMA_DMA
	bool "HRTIM TIMA DMA"
	default n
	depends on STM32_HRTIM_DMA

config STM32_HRTIM_TIMA_IRQ
	bool "HRTIM TIMA Interrupts"
	default n
	depends on STM32_HRTIM_INTERRUPTS

config STM32_HRTIM_TIMA_PWM
	bool "HRTIM TIMA PWM Outputs"
	default n
	depends on STM32_HRTIM_PWM

config STM32_HRTIM_TIMA_PWM_CH1
	bool "HRTIM TIMA PWM Output 1"
	default n
	depends on STM32_HRTIM_TIMA_PWM

config STM32_HRTIM_TIMA_PWM_CH2
	bool "HRTIM TIMA PWM Output 2"
	default n
	depends on STM32_HRTIM_TIMA_PWM

config STM32_HRTIM_TIMA_BURST
	bool "HRTIM TIMA Burst"
	default n
	depends on (STM32_HRTIM_BURST && STM32_HRTIM_TIMA_PWM)

config STM32_HRTIM_TIMA_BURST_CH1
	bool "HRTIM TIMA Output 1 Burst Mode"
	default n
	depends on (STM32_HRTIM_TIMA_BURST && STM32_HRTIM_TIMA_PWM_CH1)

config STM32_HRTIM_TIMA_BURST_CH2
	bool "HRTIM TIMA Output 2 Burst Mode"
	default n
	depends on (STM32_HRTIM_TIMA_BURST && STM32_HRTIM_TIMA_PWM_CH2)

config STM32_HRTIM_TIMA_CHOP
	bool "HRTIM TIMA PWM Chopper"
	default n
	depends on (STM32_HRTIM_CHOPPER && STM32_HRTIM_TIMA_PWM)

config STM32_HRTIM_TIMA_DT
	bool "HRTIM TIMA PWM Dead-time"
	default n
	depends on (STM32_HRTIM_DEADTIME && STM32_HRTIM_TIMA_PWM)

config STM32_HRTIM_TIMA_PSHPLL
	bool "HRTIM TIMA PWM Push-pull mode"
	default n
	depends on (STM32_HRTIM_PUSHPULL && STM32_HRTIM_TIMA_PWM)

endmenu # "HRTIM Timer A Configuration"

menu "HRTIM Timer B Configuration"
	depends on STM32_HRTIM_TIMB

config STM32_HRTIM_TIMB_CAP
	bool "HRTIM TIMB Capture"
	default n
	depends on STM32_HRTIM_CAPTURE

config STM32_HRTIM_TIMB_DAC
	bool "HRTIM TIMB DAC Triggering"
	default n
	depends on STM32_HRTIM_DAC

config STM32_HRTIM_TIMB_DMA
	bool "HRTIM TIMB DMA"
	default n
	depends on STM32_HRTIM_DMA

config STM32_HRTIM_TIMB_IRQ
	bool "HRTIM TIMB Interrupts"
	default n
	depends on STM32_HRTIM_INTERRUPTS

config STM32_HRTIM_TIMB_PWM
	bool "HRTIM TIMB PWM Outputs"
	default n
	depends on STM32_HRTIM_PWM

config STM32_HRTIM_TIMB_PWM_CH1
	bool "HRTIM TIMB PWM Output 1"
	default n
	depends on STM32_HRTIM_TIMB_PWM

config STM32_HRTIM_TIMB_PWM_CH2
	bool "HRTIM TIMB PWM Output 2"
	default n
	depends on STM32_HRTIM_TIMB_PWM

config STM32_HRTIM_TIMB_BURST
	bool "HRTIM TIMB Burst"
	default n
	depends on (STM32_HRTIM_BURST && STM32_HRTIM_TIMB_PWM)

config STM32_HRTIM_TIMB_BURST_CH1
	bool "HRTIM TIMB Output 1 Burst Mode"
	default n
	depends on (STM32_HRTIM_TIMB_BURST && STM32_HRTIM_TIMB_PWM_CH1)

config STM32_HRTIM_TIMB_BURST_CH2
	bool "HRTIM TIMB Output 2 Burst Mode"
	default n
	depends on (STM32_HRTIM_TIMB_BURST && STM32_HRTIM_TIMB_PWM_CH2)

config STM32_HRTIM_TIMB_CHOP
	bool "HRTIM TIMB PWM Chopper"
	default n
	depends on (STM32_HRTIM_CHOPPER && STM32_HRTIM_TIMB_PWM)

config STM32_HRTIM_TIMB_DT
	bool "HRTIM TIMB PWM Dead-time"
	default n
	depends on (STM32_HRTIM_DEADTIME && STM32_HRTIM_TIMB_PWM)

config STM32_HRTIM_TIMB_PSHPLL
	bool "HRTIM TIMB PWM Push-pull mode"
	default n
	depends on (STM32_HRTIM_PUSHPULL && STM32_HRTIM_TIMB_PWM)

endmenu # "HRTIM Timer B Configuration"

menu "HRTIM Timer C Configuration"
	depends on STM32_HRTIM_TIMC

config STM32_HRTIM_TIMC_CAP
	bool "HRTIM TIMC Capture"
	default n
	depends on STM32_HRTIM_CAPTURE

config STM32_HRTIM_TIMC_DAC
	bool "HRTIM TIMC DAC Triggering"
	default n
	depends on STM32_HRTIM_DAC

config STM32_HRTIM_TIMC_DMA
	bool "HRTIM TIMC DMA"
	default n
	depends on STM32_HRTIM_DMA

config STM32_HRTIM_TIMC_IRQ
	bool "HRTIM TIMC Interrupts"
	default n
	depends on STM32_HRTIM_INTERRUPTS

config STM32_HRTIM_TIMC_PWM
	bool "HRTIM TIMC PWM Outputs"
	default n
	depends on STM32_HRTIM_PWM

config STM32_HRTIM_TIMC_PWM_CH1
	bool "HRTIM TIMC PWM Output 1"
	default n
	depends on STM32_HRTIM_TIMC_PWM

config STM32_HRTIM_TIMC_PWM_CH2
	bool "HRTIM TIMC PWM Output 2"
	default n
	depends on STM32_HRTIM_TIMC_PWM

config STM32_HRTIM_TIMC_BURST
	bool "HRTIM TIMC Burst"
	default n
	depends on (STM32_HRTIM_BURST && STM32_HRTIM_TIMC_PWM)

config STM32_HRTIM_TIMC_BURST_CH1
	bool "HRTIM TIMC Output 1 Burst Mode"
	default n
	depends on (STM32_HRTIM_TIMC_BURST && STM32_HRTIM_TIMC_PWM_CH1)

config STM32_HRTIM_TIMC_BURST_CH2
	bool "HRTIM TIMC Output 2 Burst Mode"
	default n
	depends on (STM32_HRTIM_TIMC_BURST && STM32_HRTIM_TIMC_PWM_CH2)

config STM32_HRTIM_TIMC_CHOP
	bool "HRTIM TIMC PWM Chopper"
	default n
	depends on (STM32_HRTIM_CHOPPER && STM32_HRTIM_TIMC_PWM)

config STM32_HRTIM_TIMC_DT
	bool "HRTIM TIMC PWM Dead-time"
	default n
	depends on (STM32_HRTIM_DEADTIME && STM32_HRTIM_TIMC_PWM)

config STM32_HRTIM_TIMC_PSHPLL
	bool "HRTIM TIMC PWM Push-pull mode"
	default n
	depends on (STM32_HRTIM_PUSHPULL && STM32_HRTIM_TIMC_PWM)

endmenu # "HRTIM Timer C Configuration"

menu "HRTIM Timer D Configuration"
	depends on STM32_HRTIM_TIMD

config STM32_HRTIM_TIMD_CAP
	bool "HRTIM TIMD Capture"
	default n
	depends on STM32_HRTIM_CAPTURE

config STM32_HRTIM_TIMD_DAC
	bool "HRTIM TIMD DAC Triggering"
	default n
	depends on STM32_HRTIM_DAC

config STM32_HRTIM_TIMD_DMA
	bool "HRTIM TIMD DMA"
	default n
	depends on STM32_HRTIM_DMA

config STM32_HRTIM_TIMD_IRQ
	bool "HRTIM TIMD Interrupts"
	default n
	depends on STM32_HRTIM_INTERRUPTS

config STM32_HRTIM_TIMD_PWM
	bool "HRTIM TIMD PWM Outputs"
	default n
	depends on STM32_HRTIM_PWM

config STM32_HRTIM_TIMD_PWM_CH1
	bool "HRTIM TIMD PWM Output 1"
	default n
	depends on STM32_HRTIM_TIMD_PWM

config STM32_HRTIM_TIMD_PWM_CH2
	bool "HRTIM TIMD PWM Output 2"
	default n
	depends on STM32_HRTIM_TIMD_PWM

config STM32_HRTIM_TIMD_BURST
	bool "HRTIM TIMD Burst"
	default n
	depends on (STM32_HRTIM_BURST && STM32_HRTIM_TIMD_PWM)

config STM32_HRTIM_TIMD_BURST_CH1
	bool "HRTIM TIMD Output 1 Burst Mode"
	default n
	depends on (STM32_HRTIM_TIMD_BURST && STM32_HRTIM_TIMD_PWM_CH1)

config STM32_HRTIM_TIMD_BURST_CH2
	bool "HRTIM TIMD Output 2 Burst Mode"
	default n
	depends on (STM32_HRTIM_TIMD_BURST && STM32_HRTIM_TIMD_PWM_CH2)

config STM32_HRTIM_TIMD_CHOP
	bool "HRTIM TIMD PWM Chopper"
	default n
	depends on (STM32_HRTIM_CHOPPER && STM32_HRTIM_TIMD_PWM)

config STM32_HRTIM_TIMD_DT
	bool "HRTIM TIMD PWM Dead-time"
	default n
	depends on (STM32_HRTIM_DEADTIME && STM32_HRTIM_TIMD_PWM)

config STM32_HRTIM_TIMD_PSHPLL
	bool "HRTIM TIMD PWM Push-pull mode"
	default n
	depends on (STM32_HRTIM_PUSHPULL && STM32_HRTIM_TIMD_PWM)

endmenu # "HRTIM Timer D Configuration"

menu "HRTIM Timer E Configuration"
	depends on STM32_HRTIM_TIME

config STM32_HRTIM_TIME_CAP
	bool "HRTIM TIME Capture"
	default n
	depends on STM32_HRTIM_CAPTURE

config STM32_HRTIM_TIME_DAC
	bool "HRTIM TIME DAC Triggering"
	default n
	depends on STM32_HRTIM_DAC

config STM32_HRTIM_TIME_DMA
	bool "HRTIM TIME DMA"
	default n
	depends on STM32_HRTIM_DMA

config STM32_HRTIM_TIME_IRQ
	bool "HRTIM TIME Interrupts"
	default n
	depends on STM32_HRTIM_INTERRUPTS

config STM32_HRTIM_TIME_PWM
	bool "HRTIM TIME PWM Outputs"
	default n
	depends on STM32_HRTIM_PWM

config STM32_HRTIM_TIME_PWM_CH1
	bool "HRTIM TIME PWM Output 1"
	default n
	depends on STM32_HRTIM_TIME_PWM

config STM32_HRTIM_TIME_PWM_CH2
	bool "HRTIM TIME PWM Output 2"
	default n
	depends on STM32_HRTIM_TIME_PWM

config STM32_HRTIM_TIME_BURST
	bool "HRTIM TIME Burst"
	default n
	depends on (STM32_HRTIM_BURST && STM32_HRTIM_TIME_PWM)

config STM32_HRTIM_TIME_BURST_CH1
	bool "HRTIM TIME Output 1 Burst Mode"
	default n
	depends on (STM32_HRTIM_TIME_BURST && STM32_HRTIM_TIME_PWM_CH1)

config STM32_HRTIM_TIME_BURST_CH2
	bool "HRTIM TIME Output 2 Burst Mode"
	default n
	depends on (STM32_HRTIM_TIME_BURST && STM32_HRTIM_TIME_PWM_CH2)

config STM32_HRTIM_TIME_CHOP
	bool "HRTIM TIME PWM Chopper"
	default n
	depends on (STM32_HRTIM_CHOPPER && STM32_HRTIM_TIME_PWM)

config STM32_HRTIM_TIME_DT
	bool "HRTIM TIME PWM Dead-time"
	default n
	depends on (STM32_HRTIM_DEADTIME && STM32_HRTIM_TIME_PWM)

config STM32_HRTIM_TIME_PSHPLL
	bool "HRTIM TIME PWM Push-pull mode"
	default n
	depends on (STM32_HRTIM_PUSHPULL && STM32_HRTIM_TIME_PWM)

endmenu # "HRTIM Timer E Configuration"

endmenu # "HRTIM Configuration"

menu "ADC Configuration"
	depends on STM32_ADC

config STM32_ADC1_RESOLUTION
	int "ADC1 resolution"
	depends on STM32_ADC1 && !STM32_HAVE_IP_ADC_V1_BASIC
	default 0
	range 0 3
	---help---
		ADC1 resolution. 0 - 12 bit, 1 - 10 bit, 2 - 8 bit, 3 - 6 bit

config STM32_ADC2_RESOLUTION
	int "ADC2 resolution"
	depends on STM32_ADC2 && !STM32_HAVE_IP_ADC_V1_BASIC
	default 0
	range 0 3
	---help---
		ADC2 resolution. 0 - 12 bit, 1 - 10 bit, 2 - 8 bit, 3 - 6 bit

config STM32_ADC3_RESOLUTION
	int "ADC3 resolution"
	depends on STM32_ADC3 && !STM32_HAVE_IP_ADC_V1_BASIC
	default 0
	range 0 3
	---help---
		ADC3 resolution. 0 - 12 bit, 1 - 10 bit, 2 - 8 bit, 3 - 6 bit

config STM32_ADC4_RESOLUTION
	int "ADC4 resolution"
	depends on STM32_ADC4 && !STM32_HAVE_IP_ADC_V1_BASIC
	default 0
	range 0 3
	---help---
		ADC4 resolution. 0 - 12 bit, 1 - 10 bit, 2 - 8 bit, 3 - 6 bit

config STM32_ADC5_RESOLUTION
	int "ADC5 resolution"
	depends on STM32_ADC5 && !STM32_HAVE_IP_ADC_V1_BASIC
	default 0
	range 0 3
	---help---
		ADC5 resolution. 0 - 12 bit, 1 - 10 bit, 2 - 8 bit, 3 - 6 bit

config STM32_ADC_MAX_SAMPLES
	int "The maximum number of channels that can be sampled"
	default 16
	---help---
		The maximum number of samples which can be handled without
		overrun depends on various factors. This is the user's
		responsibility to correctly select this value.
		Since the interface to update the sampling time is available
		for all supported devices, the user can change the default
		values in the board initialization logic and avoid ADC overrun.

config STM32_ADC_NO_STARTUP_CONV
	bool "Do not start conversion when opening ADC device"
	default n
	---help---
		Do not start conversion when opening ADC device.

config STM32_ADC_NOIRQ
	bool "Do not use default ADC interrupts"
	default n
	---help---
		Do not use default ADC interrupts handlers.

config STM32_ADC_LL_OPS
	bool "ADC low-level operations"
	default n
	---help---
		Enable low-level ADC ops.

config STM32_ADC_CHANGE_SAMPLETIME
	bool "ADC sample time configuration"
	default n
	depends on STM32_ADC_LL_OPS
	---help---
		Enable ADC sample time configuration (SMPRx registers).

config STM32_ADC1_DMA
	bool "ADC1 DMA"
	depends on STM32_ADC1 && STM32_HAVE_ADC1_DMA
	default n
	---help---
		If DMA is selected, then the ADC may be configured to support
		DMA transfer, which is necessary if multiple channels are read
		or if very high trigger frequencies are used.

config STM32_ADC1_SCAN
	bool "ADC1 scan mode"
	depends on STM32_ADC1 && STM32_HAVE_IP_ADC_V1
	default y if STM32_ADC1_DMA
	default n

config STM32_ADC1_DMA_CFG
	int "ADC1 DMA configuration"
	depends on STM32_ADC1_DMA && !STM32_HAVE_IP_ADC_V1_BASIC
	range 0 1
	default 0
	---help---
		0 - ADC1 DMA in One Shot Mode, 1 - ADC1 DMA in Circular Mode

config STM32_ADC1_ANIOC_TRIGGER
	int "ADC1 software trigger (ANIOC_TRIGGER) configuration"
	depends on STM32_ADC1
	range 1 3
	default 3
	---help---
		1 - ANIOC_TRIGGER only starts regular conversion
		2 - ANIOC_TRIGGER only starts injected conversion
		3 - ANIOC_TRIGGER starts both regular and injected conversions

config STM32_ADC2_DMA
	bool "ADC2 DMA"
	depends on STM32_ADC2 && STM32_HAVE_ADC2_DMA
	default n
	---help---
		If DMA is selected, then the ADC may be configured to support
		DMA transfer, which is necessary if multiple channels are read
		or if very high trigger frequencies are used.

config STM32_ADC2_SCAN
	bool "ADC2 scan mode"
	depends on STM32_ADC2 && STM32_HAVE_IP_ADC_V1
	default y if STM32_ADC2_DMA
	default n

config STM32_ADC2_DMA_CFG
	int "ADC2 DMA configuration"
	depends on STM32_ADC2_DMA && !STM32_HAVE_IP_ADC_V1_BASIC
	range 0 1
	default 0
	---help---
		0 - ADC2 DMA in One Shot Mode, 1 - ADC2 DMA in Circular Mode

config STM32_ADC2_ANIOC_TRIGGER
	int "ADC2 software trigger (ANIOC_TRIGGER) configuration"
	depends on STM32_ADC2
	range 1 3
	default 3
	---help---
		1 - ANIOC_TRIGGER only starts regular conversion
		2 - ANIOC_TRIGGER only starts injected conversion
		3 - ANIOC_TRIGGER starts both regular and injected conversions

config STM32_ADC3_DMA
	bool "ADC3 DMA"
	depends on STM32_ADC3 && STM32_HAVE_ADC3_DMA
	default n
	---help---
		If DMA is selected, then the ADC may be configured to support
		DMA transfer, which is necessary if multiple channels are read
		or if very high trigger frequencies are used.

config STM32_ADC3_SCAN
	bool "ADC3 scan mode"
	depends on STM32_ADC3 && STM32_HAVE_IP_ADC_V1
	default y if STM32_ADC3_DMA
	default n

config STM32_ADC3_DMA_CFG
	int "ADC3 DMA configuration"
	depends on STM32_ADC3_DMA && !STM32_HAVE_IP_ADC_V1_BASIC
	range 0 1
	default 0
	---help---
		0 - ADC3 DMA in One Shot Mode, 1 - ADC3 DMA in Circular Mode

config STM32_ADC3_ANIOC_TRIGGER
	int "ADC3 software trigger (ANIOC_TRIGGER) configuration"
	depends on STM32_ADC3
	range 1 3
	default 3
	---help---
		1 - ANIOC_TRIGGER only starts regular conversion
		2 - ANIOC_TRIGGER only starts injected conversion
		3 - ANIOC_TRIGGER starts both regular and injected conversions

config STM32_ADC4_DMA
	bool "ADC4 DMA"
	depends on STM32_ADC4 && STM32_HAVE_ADC4_DMA
	default n
	---help---
		If DMA is selected, then the ADC may be configured to support
		DMA transfer, which is necessary if multiple channels are read
		or if very high trigger frequencies are used.

config STM32_ADC4_DMA_CFG
	int "ADC4 DMA configuration"
	depends on STM32_ADC4_DMA && !STM32_HAVE_IP_ADC_V1_BASIC
	range 0 1
	default 0
	---help---
		0 - ADC4 DMA in One Shot Mode, 1 - ADC4 DMA in Circular Mode

config STM32_ADC4_ANIOC_TRIGGER
	int "ADC4 software trigger (ANIOC_TRIGGER) configuration"
	depends on STM32_ADC4
	range 1 3
	default 3
	---help---
		1 - ANIOC_TRIGGER only starts regular conversion
		2 - ANIOC_TRIGGER only starts injected conversion
		3 - ANIOC_TRIGGER starts both regular and injected conversions

config STM32_ADC5_DMA
	bool "ADC5 DMA"
	depends on STM32_ADC5 && STM32_HAVE_ADC5_DMA
	default n
	---help---
		If DMA is selected, then the ADC may be configured to support
		DMA transfer, which is necessary if multiple channels are read
		or if very high trigger frequencies are used.

config STM32_ADC5_DMA_CFG
	int "ADC5 DMA configuration"
	depends on STM32_ADC5_DMA && !STM32_HAVE_IP_ADC_V1_BASIC
	range 0 1
	default 0
	---help---
		0 - ADC5 DMA in One Shot Mode, 1 - ADC5 DMA in Circular Mode

config STM32_ADC1_INJECTED_CHAN
	int "ADC1 injected channels"
	depends on STM32_ADC1
	range 0 4
	default 0
	---help---
		Support for ADC1 injected channels.

config STM32_ADC2_INJECTED_CHAN
	int "ADC2 injected channels"
	depends on STM32_ADC2
	range 0 4
	default 0
	---help---
		Support for ADC2 injected channels.

config STM32_ADC3_INJECTED_CHAN
	int "ADC3 injected channels"
	depends on STM32_ADC3
	range 0 4
	default 0
	---help---
		Support for ADC3 injected channels.

config STM32_ADC4_INJECTED_CHAN
	int "ADC4 injected channels"
	depends on STM32_ADC4
	range 0 4
	default 0
	---help---
		Support for ADC4 injected channels.

config STM32_ADC5_INJECTED_CHAN
	int "ADC5 injected channels"
	depends on STM32_ADC5
	range 0 4
	default 0
	---help---
		Support for ADC5 injected channels.

config STM32_ADC1_EXTSEL
	bool "ADC1 external trigger for regular group"
	depends on STM32_ADC1 && !STM32_HAVE_ADC1_TIMER
	default n
	---help---
		Enable EXTSEL for ADC1.

config STM32_ADC2_EXTSEL
	bool "ADC2 external trigger for regular group"
	depends on STM32_ADC2 && !STM32_HAVE_ADC2_TIMER
	default n
	---help---
		Enable EXTSEL for ADC2.

config STM32_ADC3_EXTSEL
	bool "ADC3 external trigger for regular group"
	depends on STM32_ADC3 && !STM32_HAVE_ADC3_TIMER
	default n
	---help---
		Enable EXTSEL for ADC3.

config STM32_ADC4_EXTSEL
	bool "ADC4 external trigger for regular group"
	depends on STM32_ADC4 && !STM32_HAVE_ADC4_TIMER
	default n
	---help---
		Enable EXTSEL for ADC4.

config STM32_ADC5_EXTSEL
	bool "ADC5 external trigger for regular group"
	depends on STM32_ADC5 && !STM32_HAVE_ADC5_TIMER
	default n
	---help---
		Enable EXTSEL for ADC5.

config STM32_ADC1_JEXTSEL
	bool "ADC1 external trigger for injected group"
	depends on STM32_ADC1
	default n
	---help---
		Enable JEXTSEL for ADC1.

config STM32_ADC2_JEXTSEL
	bool "ADC2 external trigger for injected group"
	depends on STM32_ADC2
	default n
	---help---
		Enable JEXTSEL for ADC2.

config STM32_ADC3_JEXTSEL
	bool "ADC3 external trigger for injected group"
	depends on STM32_ADC3
	default n
	---help---
		Enable JEXTSEL for ADC3.

config STM32_ADC4_JEXTSEL
	bool "ADC4 external trigger for injected group"
	depends on STM32_ADC4
	default n
	---help---
		Enable JEXTSEL for ADC4.

config STM32_ADC5_JEXTSEL
	bool "ADC5 external trigger for injected group"
	depends on STM32_ADC5
	default n
	---help---
		Enable JEXTSEL for ADC5.

endmenu

menu "COMP Configuration"
	depends on STM32_COMP && STM32_HAVE_IP_COMP_V2

config STM32_COMP1_OUT
	bool "COMP1 GPIO Output"
	depends on STM32_COMP1
	default n
	---help---
		Enables COMP1 output.

config STM32_COMP1_INM
	int "COMP1 inverting input assignment"
	depends on STM32_COMP1
	range 0 7
	default 0
	---help---
		Selects COMP1 inverting input pin.

config STM32_COMP1_INP
	int "COMP1 non-inverting input assignment"
	depends on STM32_COMP1
	range 0 1
	default 0
	---help---
		Selects COMP1 non-inverting input pin.

config STM32_COMP1_POL
	int "COMP1 polarity"
	depends on STM32_COMP1
	range 0 1
	default 0
	---help---
		Selects COMP1 output polarity.

config STM32_COMP1_HYST
	int "COMP1 hysteresis"
	depends on STM32_STM32G4XXX && STM32_COMP1
	range 0 7
	default 0
	---help---
		Selects the hysteresis of the COMP1.

config STM32_COMP1_BLANKSEL
	int "COMP1 blanking signal select"
	depends on STM32_COMP1
	range 0 7
	default 0
	---help---
		Selects the blanking signal for comparator COMP1.

config STM32_COMP1_LOCK
	int "COMP1 COMP_CxCSR register lock"
	depends on STM32_COMP1
	range 0 1
	default 0
	---help---
		Locks COMP_CxCSR register.
		  0 - Unlock 1 - Lock

config STM32_COMP2_OUT
	bool "COMP2 GPIO Output"
	depends on STM32_COMP2
	default n
	---help---
		Enables COMP2 output.

config STM32_COMP2_INM
	int "COMP2 inverting input assignment"
	depends on STM32_COMP2
	range 0 7
	default 0
	---help---
		Selects COMP2 inverting input pin.

config STM32_COMP2_INP
	int "COMP2 non-inverting input assignment"
	depends on STM32_COMP2
	range 0 1
	default 0
	---help---
		Selects COMP2 non-inverting input pin.

config STM32_COMP2_POL
	int "COMP2 polarity"
	depends on STM32_COMP2
	range 0 1
	default 0
	---help---
		Selects COMP2 output polarity.

config STM32_COMP2_HYST
	int "COMP2 hysteresis"
	depends on STM32_STM32G4XXX && STM32_COMP2
	range 0 7
	default 0
	---help---
		Selects the hysteresis of the COMP2.

config STM32_COMP2_BLANKSEL
	int "COMP2 blanking signal select"
	depends on STM32_COMP2
	range 0 7
	default 0
	---help---
		Selects the blanking signal for comparator COMP2.

config STM32_COMP2_LOCK
	int "COMP2 COMP_CxCSR register lock"
	depends on STM32_COMP2
	range 0 1
	default 0
	---help---
		Locks COMP_CxCSR register.
		  0 - Unlock 1 - Lock

config STM32_COMP3_OUT
	bool "COMP3 GPIO Output"
	depends on STM32_COMP3
	default n
	---help---
		Enables COMP3 output.

config STM32_COMP3_INM
	int "COMP3 inverting input assignment"
	depends on STM32_COMP3
	range 0 7
	default 0
	---help---
		Selects COMP3 inverting input pin.

config STM32_COMP3_INP
	int "COMP3 non-inverting input assignment"
	depends on STM32_COMP3
	range 0 1
	default 0
	---help---
		Selects COMP3 non-inverting input pin.

config STM32_COMP3_POL
	int "COMP3 polarity"
	depends on STM32_COMP3
	range 0 1
	default 0
	---help---
		Selects COMP3 output polarity.

config STM32_COMP3_HYST
	int "COMP3 hysteresis"
	depends on STM32_STM32G4XXX && STM32_COMP3
	range 0 7
	default 0
	---help---
		Selects the hysteresis of the COMP3.

config STM32_COMP3_BLANKSEL
	int "COMP3 blanking signal select"
	depends on STM32_COMP3
	range 0 7
	default 0
	---help---
		Selects the blanking signal for comparator COMP3.

config STM32_COMP3_LOCK
	int "COMP3 COMP_CxCSR register lock"
	depends on STM32_COMP3
	range 0 1
	default 0
	---help---
		Locks COMP_CxCSR register.
		  0 - Unlock 1 - Lock

config STM32_COMP4_OUT
	bool "COMP4 GPIO Output"
	depends on STM32_COMP4
	default n
	---help---
		Enables COMP4 output.

config STM32_COMP4_INM
	int "COMP4 inverting input assignment"
	depends on STM32_COMP4
	range 0 7
	default 0
	---help---
		Selects COMP4 inverting input pin.

config STM32_COMP4_INP
	int "COMP4 non-inverting input assignment"
	depends on STM32_COMP4
	range 0 1
	default 0
	---help---
		Selects COMP4 non-inverting input pin.

config STM32_COMP4_POL
	int "COMP4 polarity"
	depends on STM32_COMP4
	range 0 1
	default 0
	---help---
		Selects COMP4 output polarity.

config STM32_COMP4_HYST
	int "COMP4 hysteresis"
	depends on STM32_STM32G4XXX && STM32_COMP4
	range 0 7
	default 0
	---help---
		Selects the hysteresis of the COMP4.

config STM32_COMP4_BLANKSEL
	int "COMP4 blanking signal select"
	depends on STM32_COMP4
	range 0 7
	default 0
	---help---
		Selects the blanking signal for comparator COMP4.

config STM32_COMP4_LOCK
	int "COMP4 COMP_CxCSR register lock"
	depends on STM32_COMP4
	range 0 1
	default 0
	---help---
		Locks COMP_CxCSR register.
		  0 - Unlock 1 - Lock

config STM32_COMP5_OUT
	bool "COMP5 GPIO Output"
	depends on STM32_COMP5
	default n
	---help---
		Enables COMP5 output.

config STM32_COMP5_INM
	int "COMP5 inverting input assignment"
	depends on STM32_COMP5
	range 0 7
	default 0
	---help---
		Selects COMP5 inverting input pin.

config STM32_COMP5_INP
	int "COMP5 non-inverting input assignment"
	depends on STM32_COMP5
	range 0 1
	default 0
	---help---
		Selects COMP5 non-inverting input pin.

config STM32_COMP5_POL
	int "COMP5 polarity"
	depends on STM32_COMP5
	range 0 1
	default 0
	---help---
		Selects COMP5 output polarity.

config STM32_COMP5_HYST
	int "COMP5 hysteresis"
	depends on STM32_STM32G4XXX && STM32_COMP5
	range 0 7
	default 0
	---help---
		Selects the hysteresis of the COMP5.

config STM32_COMP5_BLANKSEL
	int "COMP5 blanking signal select"
	depends on STM32_COMP5
	range 0 7
	default 0
	---help---
		Selects the blanking signal for comparator COMP5.

config STM32_COMP5_LOCK
	int "COMP5 COMP_CxCSR register lock"
	depends on STM32_COMP5
	range 0 1
	default 0
	---help---
		Locks COMP_CxCSR register.
		  0 - Unlock 1 - Lock

config STM32_COMP6_OUT
	bool "COMP6 GPIO Output"
	depends on STM32_COMP6
	default n
	---help---
		Enables COMP6 output.

config STM32_COMP6_INM
	int "COMP6 inverting input assignment"
	depends on STM32_COMP6
	range 0 7
	default 0
	---help---
		Selects COMP6 inverting input pin.

config STM32_COMP6_INP
	int "COMP6 non-inverting input assignment"
	depends on STM32_COMP6
	range 0 1
	default 0
	---help---
		Selects COMP6 non-inverting input pin.

config STM32_COMP6_POL
	int "COMP6 polarity"
	depends on STM32_COMP6
	range 0 1
	default 0
	---help---
		Selects COMP6 output polarity.

config STM32_COMP6_HYST
	int "COMP6 hysteresis"
	depends on STM32_STM32G4XXX && STM32_COMP6
	range 0 7
	default 0
	---help---
		Selects the hysteresis of the COMP6.

config STM32_COMP6_BLANKSEL
	int "COMP6 blanking signal select"
	depends on STM32_COMP6
	range 0 7
	default 0
	---help---
		Selects the blanking signal for comparator COMP6.

config STM32_COMP6_LOCK
	int "COMP6 COMP_CxCSR register lock"
	depends on STM32_COMP6
	range 0 1
	default 0
	---help---
		Locks COMP_CxCSR register.
		  0 - Unlock 1 - Lock

config STM32_COMP7_OUT
	bool "COMP7 GPIO Output"
	depends on STM32_COMP7
	default n
	---help---
		Enables COMP7 output.

config STM32_COMP7_INM
	int "COMP7 inverting input assignment"
	depends on STM32_COMP7
	range 0 7
	default 0
	---help---
		Selects COMP7 inverting input pin.

config STM32_COMP7_INP
	int "COMP7 non-inverting input assignment"
	depends on STM32_COMP7
	range 0 1
	default 0
	---help---
		Selects COMP7 non-inverting input pin.

config STM32_COMP7_POL
	int "COMP7 polarity"
	depends on STM32_COMP7
	range 0 1
	default 0
	---help---
		Selects COMP7 output polarity.

config STM32_COMP7_HYST
	int "COMP7 hysteresis"
	depends on STM32_STM32G4XXX && STM32_COMP7
	range 0 7
	default 0
	---help---
		Selects the hysteresis of the COMP7.

config STM32_COMP7_BLANKSEL
	int "COMP7 blanking signal select"
	depends on STM32_COMP7
	range 0 7
	default 0
	---help---
		Selects the blanking signal for comparator COMP7.

config STM32_COMP7_LOCK
	int "COMP7 COMP_CxCSR register lock"
	depends on STM32_COMP7
	range 0 1
	default 0
	---help---
		Locks COMP_CxCSR register.
		  0 - Unlock 1 - Lock

endmenu

menu "SDADC Configuration"
	depends on STM32_SDADC

config STM32_SDADC1_DMA
	bool "SDADC1 DMA"
	depends on STM32_SDADC1 && STM32_HAVE_SDADC1_DMA
	default n
	---help---
		If DMA is selected, then the SDADC may be configured to support
		DMA transfer, which is advisable if multiple channels are read
		or if very high trigger frequencies are used.

config STM32_SDADC2_DMA
	bool "SDADC2 DMA"
	depends on STM32_SDADC2 && STM32_HAVE_SDADC2_DMA
	default n
	---help---
		If DMA is selected, then the SDADC may be configured to support
		DMA transfer, which is advisable if multiple channels are read
		or if very high trigger frequencies are used.

config STM32_SDADC3_DMA
	bool "SDADC3 DMA"
	depends on STM32_SDADC3 && STM32_HAVE_SDADC3_DMA
	default n
	---help---
		If DMA is selected, then the SDADC may be configured to support
		DMA transfer, which is advisable if multiple channels are read
		or if very high trigger frequencies are used.

endmenu

menu "DAC Configuration"
	depends on STM32_DAC1 || STM32_DAC2 || STM32_DAC3 || STM32_DAC4

config STM32_DAC1CH1_MODE
	int "DAC1CH1 channel mode"
	depends on STM32_DAC1CH1 && STM32_HAVE_IP_DAC_V2
	default 0
	range 0 7
	---help---
		– DAC channel in Normal mode
			0: DAC channel is connected to external pin with Buffer enabled
			1: DAC channel is connected to external pin and to on chip peripherals with buffer enabled
			2: DAC channel2 is connected to external pin with buffer disabled
			3: DAC channel is connected to on chip peripherals with Buffer disabled
		- DAC channel in Sample and hold mode
			4: DAC channel is connected to external pin with Buffer enabled
			5: DAC channel is connected to external pin and to on chip peripherals with Buffer enabled
			6: DAC channel is connected to external pin and to on chip peripherals with Buffer disabled
			7: DAC channel is connected to on chip peripherals with Buffer disabled

config STM32_DAC1CH1_DMA
	bool "DAC1CH1 DMA"
	depends on STM32_DAC1CH1
	default n
	---help---
		If DMA is selected, then a timer and output frequency must also be
		provided to support the DMA transfer.  The DMA transfer could be
		supported by and EXTI trigger, but this feature is not currently
		supported by the driver.

if STM32_DAC1CH1_DMA

config STM32_DAC1CH1_DMA_BUFFER_SIZE
	int "DAC1CH1 DMA buffer size"
	default 256

config STM32_DAC1CH1_DMA_EXTERNAL
	bool "DAC1CH1 DMA External Trigger"
	default n

if STM32_HRTIM_DAC

config STM32_DAC1CH1_HRTIM_TRG1
	bool "DAC1CH1 HRTIM Trigger 1"
	default n

config STM32_DAC1CH1_HRTIM_TRG2
	bool "DAC1CH1 HRTIM Trigger 2"
	default n

endif # STM32_HRTIM_DAC

config STM32_DAC1CH1_TIMER
	int "DAC1CH1 timer"
	depends on !STM32_DAC1CH1_DMA_EXTERNAL
	range 2 8

config STM32_DAC1CH1_TIMER_FREQUENCY
	int "DAC1CH1 timer frequency"
	depends on !STM32_DAC1CH1_DMA_EXTERNAL
	default 0

endif

config STM32_DAC1CH2_MODE
	int "DAC1CH2 channel mode"
	depends on STM32_DAC1CH2 && STM32_HAVE_IP_DAC_V2
	default 0
	range 0 7
	---help---
		– DAC channel in Normal mode
			0: DAC channel is connected to external pin with Buffer enabled
			1: DAC channel is connected to external pin and to on chip peripherals with buffer enabled
			2: DAC channel2 is connected to external pin with buffer disabled
			3: DAC channel is connected to on chip peripherals with Buffer disabled
		- DAC channel in Sample and hold mode
			4: DAC channel is connected to external pin with Buffer enabled
			5: DAC channel is connected to external pin and to on chip peripherals with Buffer enabled
			6: DAC channel is connected to external pin and to on chip peripherals with Buffer disabled
			7: DAC channel is connected to on chip peripherals with Buffer disabled

config STM32_DAC1CH2_DMA
	bool "DAC1CH2 DMA"
	depends on STM32_DAC1CH2
	default n
	---help---
		If DMA is selected, then a timer and output frequency must also be
		provided to support the DMA transfer.  The DMA transfer could be
		supported by and EXTI trigger, but this feature is not currently
		supported by the driver.

if STM32_DAC1CH2_DMA

config STM32_DAC1CH2_DMA_BUFFER_SIZE
	int "DAC1CH2 DMA buffer size"
	default 256

config STM32_DAC1CH2_DMA_EXTERNAL
	bool "DAC1CH2 DMA External Trigger"
	default n

if STM32_HRTIM_DAC

config STM32_DAC1CH2_HRTIM_TRG1
	bool "DAC1CH2 HRTIM Trigger 1"
	default n

config STM32_DAC1CH2_HRTIM_TRG2
	bool "DAC1CH2 HRTIM Trigger 2"
	default n

endif # STM32_HRTIM_DAC

config STM32_DAC1CH2_TIMER
	int "DAC1CH2 timer"
	depends on !STM32_DAC1CH2_DMA_EXTERNAL
	range 2 8

config STM32_DAC1CH2_TIMER_FREQUENCY
	int "DAC1CH2 timer frequency"
	depends on !STM32_DAC1CH2_DMA_EXTERNAL
	default 0

endif

config STM32_DAC2CH1_MODE
	int "DAC2CH1 channel mode"
	depends on STM32_DAC2CH1 && STM32_HAVE_IP_DAC_V2
	default 0
	range 0 7
	---help---
		– DAC channel in Normal mode
			0: DAC channel is connected to external pin with Buffer enabled
			1: DAC channel is connected to external pin and to on chip peripherals with buffer enabled
			2: DAC channel2 is connected to external pin with buffer disabled
			3: DAC channel is connected to on chip peripherals with Buffer disabled
		- DAC channel in Sample and hold mode
			4: DAC channel is connected to external pin with Buffer enabled
			5: DAC channel is connected to external pin and to on chip peripherals with Buffer enabled
			6: DAC channel is connected to external pin and to on chip peripherals with Buffer disabled
			7: DAC channel is connected to on chip peripherals with Buffer disabled

config STM32_DAC2CH1_DMA
	bool "DAC2CH1 DMA"
	depends on STM32_DAC2CH1
	default n
	---help---
		If DMA is selected, then a timer and output frequency must also be
		provided to support the DMA transfer.  The DMA transfer could be
		supported by and EXTI trigger, but this feature is not currently
		supported by the driver.

if STM32_DAC2CH1_DMA

config STM32_DAC2CH1_DMA_BUFFER_SIZE
	int "DAC2CH1 DMA buffer size"
	default 256

config STM32_DAC2CH1_DMA_EXTERNAL
	bool "DAC2CH1 DMA External Trigger"
	default n

if STM32_HRTIM_DAC

config STM32_DAC2CH1_HRTIM_TRG3
	bool "DAC2CH1 HRTIM Trigger 3"
	default n

endif # STM32_HRTIM_DAC

config STM32_DAC2CH1_TIMER
	int "DAC2CH1 timer"
	depends on !STM32_DAC2CH1_DMA_EXTERNAL
	default 0
	range 2 8

config STM32_DAC2CH1_TIMER_FREQUENCY
	int "DAC2CH1 timer frequency"
	depends on !STM32_DAC2CH1_DMA_EXTERNAL
	default 0

endif

config STM32_DAC3CH1_MODE
	int "DAC3CH1 channel mode"
	depends on STM32_DAC3CH1 && STM32_HAVE_IP_DAC_V2
	default 0
	range 0 7
	---help---
		– DAC channel in Normal mode
			0: DAC channel is connected to external pin with Buffer enabled
			1: DAC channel is connected to external pin and to on chip peripherals with buffer enabled
			2: DAC channel is connected to external pin with buffer disabled
			3: DAC channel is connected to on chip peripherals with Buffer disabled
		- DAC channel in Sample and hold mode
			4: DAC channel is connected to external pin with Buffer enabled
			5: DAC channel is connected to external pin and to on chip peripherals with Buffer enabled
			6: DAC channel is connected to external pin and to on chip peripherals with Buffer disabled
			7: DAC channel is connected to on chip peripherals with Buffer disabled

config STM32_DAC3CH1_DMA
	bool "DAC3CH1 DMA"
	depends on STM32_DAC3CH1
	default n
	---help---
		If DMA is selected, then a timer and output frequency must also be
		provided to support the DMA transfer. The DMA transfer could be
		supported by an EXTI trigger, but this feature is not currently
		supported by the driver.

if STM32_DAC3CH1_DMA

config STM32_DAC3CH1_DMA_BUFFER_SIZE
	int "DAC3CH1 DMA buffer size"
	default 256

config STM32_DAC3CH1_DMA_EXTERNAL
	bool "DAC3CH1 DMA External Trigger"
	default n

if STM32_HRTIM_DAC

config STM32_DAC3CH1_HRTIM_TRG3
	bool "DAC3CH1 HRTIM Trigger 3"
	default n

endif # STM32_HRTIM_DAC

config STM32_DAC3CH1_TIMER
	int "DAC3CH1 timer"
	depends on !STM32_DAC3CH1_DMA_EXTERNAL
	default 0
	range 2 8

config STM32_DAC3CH1_TIMER_FREQUENCY
	int "DAC3CH1 timer frequency"
	depends on !STM32_DAC3CH1_DMA_EXTERNAL
	default 0

endif

config STM32_DAC3CH2_MODE
	int "DAC3CH2 channel mode"
	depends on STM32_DAC3CH2 && STM32_HAVE_IP_DAC_V2
	default 0
	range 0 7
	---help---
		– DAC channel in Normal mode
			0: DAC channel is connected to external pin with Buffer enabled
			1: DAC channel is connected to external pin and to on chip peripherals with buffer enabled
			2: DAC channel2 is connected to external pin with buffer disabled
			3: DAC channel is connected to on chip peripherals with Buffer disabled
		- DAC channel in Sample and hold mode
			4: DAC channel is connected to external pin with Buffer enabled
			5: DAC channel is connected to external pin and to on chip peripherals with Buffer enabled
			6: DAC channel is connected to external pin and to on chip peripherals with Buffer disabled
			7: DAC channel is connected to on chip peripherals with Buffer disabled

config STM32_DAC3CH2_DMA
	bool "DAC3CH2 DMA"
	depends on STM32_DAC3CH2
	default n
	---help---
		If DMA is selected, then a timer and output frequency must also be
		provided to support the DMA transfer. The DMA transfer could be
		supported by an EXTI trigger, but this feature is not currently
		supported by the driver.

if STM32_DAC3CH2_DMA

config STM32_DAC3CH2_DMA_BUFFER_SIZE
	int "DAC3CH2 DMA buffer size"
	default 256

config STM32_DAC3CH2_DMA_EXTERNAL
	bool "DAC3CH1 DMA External Trigger"
	default n

if STM32_HRTIM_DAC

config STM32_DAC3CH2_HRTIM_TRG3
	bool "DAC3CH2 HRTIM Trigger 3"
	default n

endif # STM32_HRTIM_DAC

config STM32_DAC3CH2_TIMER
	int "DAC3CH2 timer"
	depends on !STM32_DAC3CH2_DMA_EXTERNAL
	default 0
	range 2 8

config STM32_DAC3CH2_TIMER_FREQUENCY
	int "DAC3CH2 timer frequency"
	depends on !STM32_DAC3CH2_DMA_EXTERNAL
	default 0

endif

endmenu

config STM32_USART
	bool
	default n

config STM32_USART_RXDMA
	bool
	default n

config STM32_SERIALDRIVER
	bool
	default n

config STM32_1WIREDRIVER
	bool
	default n

config STM32_HCIUART
	bool
	default n

config STM32_HCIUART_RXDMA
	bool
	default n

menu "U[S]ART Configuration"
	depends on STM32_USART

comment "U[S]ART Device Configuration"

choice
	prompt "USART1 Driver Configuration"
	default STM32_USART1_SERIALDRIVER
	depends on STM32_USART1

config STM32_USART1_SERIALDRIVER
	bool "Standard serial driver"
	select USART1_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS
	select STM32_SERIALDRIVER

config STM32_USART1_1WIREDRIVER
	bool "1-Wire driver"
	select STM32_1WIREDRIVER

config STM32_USART1_HCIUART
	bool "Bluetooth HCI-UART"
	select STM32_HCIUART
	depends on WIRELESS_BLUETOOTH

endchoice # USART1 Driver Configuration

if STM32_USART1_SERIALDRIVER

config USART1_RS485
	bool "RS-485 on USART1"
	default n
	---help---
		Enable RS-485 interface on USART1. Your board config will have to
		provide GPIO_USART1_RS485_DIR pin definition. Currently it cannot be
		used with USART1_RXDMA.

config USART1_RS485_DIR_POLARITY
	int "USART1 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on USART1_RS485
	---help---
		Polarity of DIR pin for RS-485 on USART1. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config USART1_RXDMA
	bool "USART1 Rx DMA"
	default n
	depends on (((STM32_STM32F10XX || STM32_STM32L15XX) && STM32_DMA1) || (!STM32_STM32F10XX && STM32_DMA2))
	select STM32_USART_RXDMA
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

config USART1_TXDMA
	bool "USART1 Tx DMA"
	default n
	depends on (((STM32_STM32F10XX || STM32_STM32L15XX) && STM32_DMA1) || (!STM32_STM32F10XX && STM32_DMA2))
	select STM32_USART_TXDMA
	---help---
		In high data rate usage, Tx DMA may reduce CPU load

endif # STM32_USART1_SERIALDRIVER

if STM32_USART1_HCIUART

config STM32_HCIUART1_RXBUFSIZE
	int "HCI UART1 Rx buffer size"
	default 80
	---help---
		Characters are buffered as they are received. This specifies
		the size of the receive buffer.  Ideally this should be at least
		the size of the largest frame that can be received

config STM32_HCIUART1_TXBUFSIZE
	int "HCI UART1 Transmit buffer size"
	default 80
	---help---
		Characters are buffered before being sent.  This specifies
		the size of the transmit buffer.  Ideally this should be at least
		the size of the largest frame that can be sent

config STM32_HCIUART1_BAUD
	int "HCI UART1 initial BAUD rate"
	default 115200
	---help---
		The configured initial BAUD of the HCIR USART used during bringup.
		In most cases this initial rate can be increased by the upper half
		HCI UART driver using vendor-specifi HCI UART commands.

config STM32_HCIUART1_RXDMA
	bool "HCI UART1 Rx DMA"
	default n
	depends on (((STM32_STM32F10XX || STM32_STM32L15XX) && STM32_DMA1) || (!STM32_STM32F10XX && STM32_DMA2))
	select STM32_HCIUART_RXDMA
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

endif # STM32_USART1_HCIUART

choice
	prompt "USART2 Driver Configuration"
	default STM32_USART2_SERIALDRIVER
	depends on STM32_USART2

config STM32_USART2_SERIALDRIVER
	bool "Standard serial driver"
	select USART2_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS
	select STM32_SERIALDRIVER

config STM32_USART2_1WIREDRIVER
	bool "1-Wire driver"
	select STM32_1WIREDRIVER

config STM32_USART2_HCIUART
	bool "Bluetooth HCI-UART"
	select STM32_HCIUART
	depends on WIRELESS_BLUETOOTH

endchoice # USART2 Driver Configuration

if STM32_USART2_SERIALDRIVER

config USART2_RS485
	bool "RS-485 on USART2"
	default n
	---help---
		Enable RS-485 interface on USART2. Your board config will have to
		provide GPIO_USART2_RS485_DIR pin definition. Currently it cannot be
		used with USART2_RXDMA.

config USART2_RS485_DIR_POLARITY
	int "USART2 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on USART2_RS485
	---help---
		Polarity of DIR pin for RS-485 on USART2. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config USART2_RXDMA
	bool "USART2 Rx DMA"
	default n
	depends on STM32_DMA1
	select STM32_USART_RXDMA
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

config USART2_TXDMA
	bool "USART2 Tx DMA"
	default n
	depends on STM32_DMA1
	select STM32_USART_TXDMA
	---help---
		In high data rate usage, Tx DMA may reduce CPU load

endif # STM32_USART2_SERIALDRIVER

if STM32_USART2_HCIUART

config STM32_HCIUART2_RXBUFSIZE
	int "HCI UART2 Rx buffer size"
	default 80
	---help---
		Characters are buffered as they are received. This specifies
		the size of the receive buffer.  Ideally this should be at least
		the size of the largest frame that can be received

config STM32_HCIUART2_TXBUFSIZE
	int "HCI UART2 Transmit buffer size"
	default 80
	---help---
		Characters are buffered before being sent.  This specifies
		the size of the transmit buffer.  Ideally this should be at least
		the size of the largest frame that can be sent

config STM32_HCIUART2_BAUD
	int "HCI UART2 initial BAUD rate"
	default 115200
	---help---
		The configured initial BAUD of the HCIR USART used during bringup.
		In most cases this initial rate can be increased by the upper half
		HCI UART driver using vendor-specifi HCI UART commands.

config STM32_HCIUART2_RXDMA
	bool "HCI UART2 Rx DMA"
	default n
	depends on STM32_DMA1
	select STM32_HCIUART_RXDMA
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

endif # STM32_USART2_HCIUART

choice
	prompt "USART3 Driver Configuration"
	default STM32_USART3_SERIALDRIVER
	depends on STM32_USART3

config STM32_USART3_SERIALDRIVER
	bool "Standard serial driver"
	select USART3_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS
	select STM32_SERIALDRIVER

config STM32_USART3_1WIREDRIVER
	bool "1-Wire driver"
	select STM32_1WIREDRIVER

config STM32_USART3_HCIUART
	bool "Bluetooth HCI-UART"
	select STM32_HCIUART
	depends on WIRELESS_BLUETOOTH

endchoice # USART3 Driver Configuration

if STM32_USART3_SERIALDRIVER

config USART3_RS485
	bool "RS-485 on USART3"
	default n
	---help---
		Enable RS-485 interface on USART3. Your board config will have to
		provide GPIO_USART3_RS485_DIR pin definition. Currently it cannot be
		used with USART3_RXDMA.

config USART3_RS485_DIR_POLARITY
	int "USART3 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on USART3_RS485
	---help---
		Polarity of DIR pin for RS-485 on USART3. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config USART3_RXDMA
	bool "USART3 Rx DMA"
	default n
	depends on STM32_DMA1
	select STM32_USART_RXDMA
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

config USART3_TXDMA
	bool "USART3 Tx DMA"
	default n
	depends on STM32_DMA1
	select STM32_USART_TXDMA
	---help---
		In high data rate usage, Tx DMA may reduce CPU load

endif # STM32_USART3_SERIALDRIVER

if STM32_USART3_HCIUART

config STM32_HCIUART3_RXBUFSIZE
	int "HCI UART3 Rx buffer size"
	default 80
	---help---
		Characters are buffered as they are received. This specifies
		the size of the receive buffer.  Ideally this should be at least
		the size of the largest frame that can be received

config STM32_HCIUART3_TXBUFSIZE
	int "HCI UART3 Transmit buffer size"
	default 80
	---help---
		Characters are buffered before being sent.  This specifies
		the size of the transmit buffer.  Ideally this should be at least
		the size of the largest frame that can be sent

config STM32_HCIUART3_BAUD
	int "HCI UART3 initial BAUD rate"
	default 115200
	---help---
		The configured initial BAUD of the HCIR USART used during bringup.
		In most cases this initial rate can be increased by the upper half
		HCI UART driver using vendor-specifi HCI UART commands.

config STM32_HCIUART3_RXDMA
	bool "HCI UART3 Rx DMA"
	default n
	depends on STM32_DMA1
	select STM32_HCIUART_RXDMA
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

endif # STM32_USART3_HCIUART

choice
	prompt "UART4 Driver Configuration"
	default STM32_UART4_SERIALDRIVER
	depends on STM32_UART4

config STM32_UART4_SERIALDRIVER
	bool "Standard serial driver"
	select UART4_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS
	select STM32_SERIALDRIVER

config STM32_UART4_1WIREDRIVER
	bool "1-Wire driver"
	select STM32_1WIREDRIVER

endchoice # UART1 Driver Configuration

if STM32_UART4_SERIALDRIVER

config UART4_RS485
	bool "RS-485 on UART4"
	default n
	---help---
		Enable RS-485 interface on UART4. Your board config will have to
		provide GPIO_UART4_RS485_DIR pin definition. Currently it cannot be
		used with UART4_RXDMA.

config UART4_RS485_DIR_POLARITY
	int "UART4 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on UART4_RS485
	---help---
		Polarity of DIR pin for RS-485 on UART4. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config UART4_RXDMA
	bool "UART4 Rx DMA"
	default n
	depends on STM32_DMA1
	select STM32_USART_RXDMA
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

config UART4_TXDMA
	bool "UART4 Tx DMA"
	default n
	depends on STM32_DMA1
	select STM32_USART_TXDMA
	---help---
		In high data rate usage, Tx DMA may reduce CPU load

endif # STM32_UART4_SERIALDRIVER

choice
	prompt "UART5 Driver Configuration"
	default STM32_UART5_SERIALDRIVER
	depends on STM32_UART5

config STM32_UART5_SERIALDRIVER
	bool "Standard serial driver"
	select UART5_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS
	select STM32_SERIALDRIVER

config STM32_UART5_1WIREDRIVER
	bool "1-Wire driver"
	select STM32_1WIREDRIVER

endchoice # UART5 Driver Configuration

if STM32_UART5_SERIALDRIVER

config UART5_RS485
	bool "RS-485 on UART5"
	default n
	---help---
		Enable RS-485 interface on UART5. Your board config will have to
		provide GPIO_UART5_RS485_DIR pin definition. Currently it cannot be
		used with UART5_RXDMA.

config UART5_RS485_DIR_POLARITY
	int "UART5 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on UART5_RS485
	---help---
		Polarity of DIR pin for RS-485 on UART5. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config UART5_RXDMA
	bool "UART5 Rx DMA"
	default n
	depends on STM32_DMA1
	select STM32_USART_RXDMA
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

config UART5_TXDMA
	bool "UART5 Tx DMA"
	default n
	depends on STM32_DMA1
	select STM32_USART_TXDMA
	---help---
		In high data rate usage, Tx DMA may reduce CPU load

endif # STM32_UART5_SERIALDRIVER

choice
	prompt "USART6 Driver Configuration"
	default STM32_USART6_SERIALDRIVER
	depends on STM32_USART6

config STM32_USART6_SERIALDRIVER
	bool "Standard serial driver"
	select USART6_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS
	select STM32_SERIALDRIVER

config STM32_USART6_1WIREDRIVER
	bool "1-Wire driver"
	select STM32_1WIREDRIVER

config STM32_USART6_HCIUART
	bool "Bluetooth HCI-UART"
	select STM32_HCIUART
	depends on WIRELESS_BLUETOOTH

endchoice # USART6 Driver Configuration

if STM32_USART6_SERIALDRIVER

config USART6_RS485
	bool "RS-485 on USART6"
	default n
	---help---
		Enable RS-485 interface on USART6. Your board config will have to
		provide GPIO_USART6_RS485_DIR pin definition. Currently it cannot be
		used with USART6_RXDMA.

config USART6_RS485_DIR_POLARITY
	int "USART6 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on USART6_RS485
	---help---
		Polarity of DIR pin for RS-485 on USART6. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config USART6_RXDMA
	bool "USART6 Rx DMA"
	default n
	depends on STM32_DMA2
	select STM32_USART_RXDMA
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

config USART6_TXDMA
	bool "USART6 Tx DMA"
	default n
	depends on STM32_DMA2
	select STM32_USART_TXDMA
	---help---
		In high data rate usage, Tx DMA may reduce CPU load

endif # STM32_USART6_SERIALDRIVER

if STM32_USART6_HCIUART

config STM32_HCIUART6_RXBUFSIZE
	int "HCI UART6 Rx buffer size"
	default 80
	---help---
		Characters are buffered as they are received. This specifies
		the size of the receive buffer.  Ideally this should be at least
		the size of the largest frame that can be received

config STM32_HCIUART6_TXBUFSIZE
	int "HCI UART6 Transmit buffer size"
	default 80
	---help---
		Characters are buffered before being sent.  This specifies
		the size of the transmit buffer.  Ideally this should be at least
		the size of the largest frame that can be sent

config STM32_HCIUART6_BAUD
	int "HCI UART6 initial BAUD rate"
	default 115200
	---help---
		The configured initial BAUD of the HCIR USART used during bringup.
		In most cases this initial rate can be increased by the upper half
		HCI UART driver using vendor-specifi HCI UART commands.

config STM32_HCIUART6_RXDMA
	bool "HCI UART6 Rx DMA"
	default n
	depends on STM32_DMA1
	select STM32_HCIUART_RXDMA
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

endif # STM32_USART6_HCIUART

choice
	prompt "UART7 Driver Configuration"
	default STM32_UART7_SERIALDRIVER
	depends on STM32_UART7

config STM32_UART7_SERIALDRIVER
	bool "Standard serial driver"
	select UART7_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS
	select STM32_SERIALDRIVER

config STM32_UART7_1WIREDRIVER
	bool "1-Wire driver"
	select STM32_1WIREDRIVER

config STM32_UART7_HCIUART
	bool "Bluetooth HCI-UART"
	select STM32_HCIUART
	depends on WIRELESS_BLUETOOTH

endchoice # UART7 Driver Configuration

if STM32_UART7_SERIALDRIVER

config UART7_RS485
	bool "RS-485 on UART7"
	default n
	---help---
		Enable RS-485 interface on UART7. Your board config will have to
		provide GPIO_UART7_RS485_DIR pin definition. Currently it cannot be
		used with UART7_RXDMA.

config UART7_RS485_DIR_POLARITY
	int "UART7 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on UART7_RS485
	---help---
		Polarity of DIR pin for RS-485 on UART7. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config UART7_RXDMA
	bool "UART7 Rx DMA"
	default n
	depends on STM32_DMA1
	select STM32_USART_RXDMA
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

config UART7_TXDMA
	bool "UART7 Tx DMA"
	default n
	depends on STM32_DMA1
	select STM32_USART_TXDMA
	---help---
		In high data rate usage, Tx DMA may reduce CPU load

endif # STM32_UART7_SERIALDRIVER

if STM32_UART7_HCIUART

config STM32_HCIUART7_RXBUFSIZE
	int "HCI UART7 Rx buffer size"
	default 80
	---help---
		Characters are buffered as they are received. This specifies
		the size of the receive buffer.  Ideally this should be at least
		the size of the largest frame that can be received

config STM32_HCIUART7_TXBUFSIZE
	int "HCI UART7 Transmit buffer size"
	default 80
	---help---
		Characters are buffered before being sent.  This specifies
		the size of the transmit buffer.  Ideally this should be at least
		the size of the largest frame that can be sent

config STM32_HCIUART7_BAUD
	int "HCI UART7 initial BAUD rate"
	default 115200
	---help---
		The configured initial BAUD of the HCIR USART used during bringup.
		In most cases this initial rate can be increased by the upper half
		HCI UART driver using vendor-specifi HCI UART commands.

config STM32_HCIUART7_RXDMA
	bool "HCI UART7 Rx DMA"
	default n
	depends on STM32_DMA2
	select STM32_HCIUART_RXDMA
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

endif # STM32_UART7_HCIUART

choice
	prompt "UART8 Driver Configuration"
	default STM32_UART8_SERIALDRIVER
	depends on STM32_UART8

config STM32_UART8_SERIALDRIVER
	bool "Standard serial driver"
	select UART8_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS
	select STM32_SERIALDRIVER

config STM32_UART8_1WIREDRIVER
	bool "1-Wire driver"
	select STM32_1WIREDRIVER

config STM32_UART8_HCIUART
	bool "Bluetooth HCI-UART"
	select STM32_HCIUART
	depends on WIRELESS_BLUETOOTH

endchoice # UART8 Driver Configuration

if STM32_UART8_SERIALDRIVER

config UART8_RS485
	bool "RS-485 on UART8"
	default n
	---help---
		Enable RS-485 interface on UART8. Your board config will have to
		provide GPIO_UART8_RS485_DIR pin definition. Currently it cannot be
		used with UART8_RXDMA.

config UART8_RS485_DIR_POLARITY
	int "UART8 RS-485 DIR pin polarity"
	default 1
	range 0 1
	depends on UART8_RS485
	---help---
		Polarity of DIR pin for RS-485 on UART8. Set to state on DIR pin which
		enables TX (0 - low / nTXEN, 1 - high / TXEN).

config UART8_RXDMA
	bool "UART8 Rx DMA"
	default n
	depends on STM32_DMA1
	select STM32_USART_RXDMA
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

config UART8_TXDMA
	bool "UART8 Tx DMA"
	default n
	depends on STM32_DMA1
	select STM32_USART_TXDMA
	---help---
		In high data rate usage, Tx DMA may reduce CPU load

endif # STM32_UART8_SERIALDRIVER

if STM32_UART8_HCIUART

config STM32_HCIUART8_RXBUFSIZE
	int "HCI UART8 Rx buffer size"
	default 80
	---help---
		Characters are buffered as they are received. This specifies
		the size of the receive buffer.  Ideally this should be at least
		the size of the largest frame that can be received

config STM32_HCIUART8_TXBUFSIZE
	int "HCI UART8 Transmit buffer size"
	default 80
	---help---
		Characters are buffered before being sent.  This specifies
		the size of the transmit buffer.  Ideally this should be at least
		the size of the largest frame that can be sent

config STM32_HCIUART8_BAUD
	int "HCI UART8 initial BAUD rate"
	default 115200
	---help---
		The configured initial BAUD of the HCIR USART used during bringup.
		In most cases this initial rate can be increased by the upper half
		HCI UART driver using vendor-specifi HCI UART commands.

config STM32_HCIUART8_RXDMA
	bool "HCI UART8 Rx DMA"
	default n
	depends on STM32_DMA2
	select STM32_HCIUART_RXDMA
	---help---
		In high data rate usage, Rx DMA may eliminate Rx overrun errors

endif # STM32_UART8_HCIUART

menu "Serial Driver Configuration"
	depends on STM32_SERIALDRIVER

config STM32_SERIAL_RXDMA_BUFFER_SIZE
	int "Rx DMA buffer size"
	default 32
	range 32 4096
	depends on STM32_USART_RXDMA
	---help---
		The DMA buffer size when using RX DMA to emulate a FIFO.

		When streaming data, the generic serial layer will be called
		every time the FIFO receives half or this number of bytes.

		Value given here will be rounded up to next multiple of 4 bytes.

config STM32_SERIAL_DISABLE_REORDERING
	bool "Disable reordering of ttySx devices."
	default n
	---help---
		NuttX per default reorders the serial ports (/dev/ttySx) so that the
		console is always on /dev/ttyS0. If more than one UART is in use this
		can, however, have the side-effect that all port mappings
		(hardware USART1 -> /dev/ttyS0) change if the console is moved to another
		UART. This is in particular relevant if a project uses the USB console
		in some boards and a serial console in other boards, but does not
		want the side effect of having all serial port names change when just
		the console is moved from serial to USB.

config STM32_FLOWCONTROL_BROKEN
	bool "Use Software UART RTS flow control"
	default n
	---help---
		Enable UART RTS flow control using Software. Because STM
		Current STM32 have broken HW based RTS behavior (they assert
		nRTS after every byte received)  Enable this setting workaround
		this issue by using software based management of RTS

config STM32_USART_BREAKS
	bool "Add TIOxSBRK to support sending Breaks"
	default n
	---help---
		Add TIOCxBRK routines to send a line break per the STM32 manual, the
		break will be a pulse based on the value M. This is not a BSD compatible
		break.

config STM32_SERIALBRK_BSDCOMPAT
	bool "Use GPIO To send Break"
	depends on STM32_USART_BREAKS
	default n
	---help---
		Enable using GPIO on the TX pin to send a BSD compatible break:
		TIOCSBRK will start the break and TIOCCBRK will end the break.
		The current STM32 U[S]ARTS have no way to leave the break (TX=LOW)
		on because the SW starts the break and then the HW automatically clears
		the break. This makes it is difficult to sent a long break.

config STM32_USART_SINGLEWIRE
	bool "Single Wire Support"
	default n
	depends on STM32_USART
	---help---
		Enable single wire UART support.  The option enables support for the
		TIOCSSINGLEWIRE ioctl in the STM32 serial driver.

endmenu # Serial Driver Configuration

menu "HCI UART Driver Configuration"
	depends on STM32_SERIALDRIVER

config STM32_HCIUART_RXDMA_BUFSIZE
	int "Rx DMA buffer size"
	default 32
	range 32 4096
	depends on STM32_HCIUART_RXDMA
	---help---
		The DMA buffer size when using RX DMA to emulate a FIFO.

		When streaming data, the generic serial layer will be called
		every time the FIFO receives half or this number of bytes.

		Value given here will be rounded up to next multiple of 4 bytes.

config STM32_HCIUART_RXDMAPRIO
	hex "HCI UART DMA priority"
	default 0x00001000 if STM32_STM32F10XX
	default 0x00010000 if !STM32_STM32F10XX
	depends on STM32_HCIUART_RXDMA
	---help---
		Select HCI UART DMA priority.

		For STM32 F1 family, options are: 0x00000000 low, 0x00001000 medium,
		0x00002000 high, 0x00003000 very high.  Default: medium.

		For other STM32's, options are: 0x00000000 low, 0x00010000 medium,
		0x00020000 high, 0x00030000 very high.  Default: medium.

config STM32_HCIUART_SW_RXFLOW
	bool "Use Software UART RTS flow control"
	default n
	---help---
		Enable UART RTS flow control using Software.  Current STM32 have
		broken HW based RTS behavior (they assert nRTS after every byte
		received)  Enable this setting workaround this issue by using
		software based management of RTS

		If HCI UART DMA is enabled, this is probably the better selection
		as well. In that case, the Rx DMA buffer will avoid Rx overrun due
		to short, bursty activity.  Software RTS management will probably
		result in overall better throughput and should still avoid Rx data
		overrun conditions.

config STM32_HCIUART_UPPER_WATERMARK
	int "RTS flow control upper watermark (%)"
	default 75
	range 2 100
	depends on STM32_HCIUART_SW_RXFLOW
	---help---
		If software RTS flow control is enable, then RTS will be asserted
		when this amount of Rx data has been buffered.  The amount is
		expressed as a percentage of the Rx buffer size.

config STM32_HCIUART_LOWER_WATERMARK
	int "RTS flow control lower watermark (%)"
	default 25
	range 1 99
	depends on STM32_HCIUART_SW_RXFLOW
	---help---
		If software RTS flow control is enable, then RTS will be de-asserted
		when there is less than this amount ofdata in the Rx buffere.  The
		amount is expressed as a percentage of the Rx buffer size.

endmenu # HCI UART Driver Configuration

if PM

config STM32_PM_SERIAL_ACTIVITY
	int "PM serial activity"
	default 10
	---help---
		PM activity reported to power management logic on every serial
		interrupt.

endif

endmenu # U[S]ART Configuration

menu "SPI Configuration"
	depends on STM32_SPI

config STM32_SPI_INTERRUPTS
	bool "Interrupt driver SPI"
	default n
	---help---
		Select to enable interrupt driven SPI support. Non-interrupt-driven,
		poll-waiting is recommended if the interrupt rate would be to high in
		the interrupt driven case.

config STM32_SPI1_DMA
	bool "SPI1 DMA"
	default n
	depends on STM32_SPI1 && !STM32_SPI_INTERRUPT
	select STM32_SPI_DMA
	---help---
		Use DMA to improve SPI1 transfer performance.  Cannot be used with STM32_SPI_INTERRUPT.

config STM32_SPI1_DMA_BUFFER
	int "SPI1 DMA buffer size"
	default 0
	depends on STM32_SPI1_DMA
	---help---
		Add a properly aligned DMA buffer for RX and TX DMA for SPI1.

config STM32_SPI_DMATHRESHOLD
	int "SPI DMA threshold"
	default 4
	depends on STM32_SPI_DMA
	---help---
		When SPI DMA is enabled, small DMA transfers will still be performed
		by polling logic.  But we need a threshold value to determine what
		is small.

config STM32_SPI2_DMA
	bool "SPI2 DMA"
	default n
	depends on STM32_SPI2 && !STM32_SPI_INTERRUPT
	select STM32_SPI_DMA
	---help---
		Use DMA to improve SPI2 transfer performance.  Cannot be used with STM32_SPI_INTERRUPT.

config STM32_SPI2_DMA_BUFFER
	int "SPI2 DMA buffer size"
	default 0
	depends on STM32_SPI2_DMA
	---help---
		Add a properly aligned DMA buffer for RX and TX DMA for SPI2.

config STM32_SPI3_DMA
	bool "SPI3 DMA"
	default n
	depends on STM32_SPI3 && !STM32_SPI_INTERRUPT
	select STM32_SPI_DMA
	---help---
		Use DMA to improve SPI3 transfer performance.  Cannot be used with STM32_SPI_INTERRUPT.

config STM32_SPI3_DMA_BUFFER
	int "SPI3 DMA buffer size"
	default 0
	depends on STM32_SPI3_DMA
	---help---
		Add a properly aligned DMA buffer for RX and TX DMA for SPI3.

config STM32_SPI4_DMA
	bool "SPI4 DMA"
	default n
	depends on STM32_SPI4 && !STM32_SPI_INTERRUPT
	select STM32_SPI_DMA
	---help---
		Use DMA to improve SPI4 transfer performance.  Cannot be used with STM32_SPI_INTERRUPT.

config STM32_SPI4_DMA_BUFFER
	int "SPI4 DMA buffer size"
	default 0
	depends on STM32_SPI4_DMA
	---help---
		Add a properly aligned DMA buffer for RX and TX DMA for SPI4.

config STM32_SPI5_DMA
	bool "SPI5 DMA"
	default n
	depends on STM32_SPI5 && !STM32_SPI_INTERRUPT
	select STM32_SPI_DMA
	---help---
		Use DMA to improve SPI5 transfer performance.  Cannot be used with STM32_SPI_INTERRUPT.

config STM32_SPI5_DMA_BUFFER
	int "SPI5 DMA buffer size"
	default 0
	depends on STM32_SPI5_DMA
	---help---
		Add a properly aligned DMA buffer for RX and TX DMA for SPI5.

config STM32_SPI6_DMA
	bool "SPI6 DMA"
	default n
	depends on STM32_SPI6 && !STM32_SPI_INTERRUPT
	select STM32_SPI_DMA
	---help---
		Use DMA to improve SPI6 transfer performance.  Cannot be used with STM32_SPI_INTERRUPT.

config STM32_SPI5_DMA_BUFFER
	int "SPI5 DMA buffer size"
	default 0
	depends on STM32_SPI5_DMA
	---help---
		Add a properly aligned DMA buffer for RX and TX DMA for SPI6.

endmenu # SPI Configuration

menu "I2S Configuration"
	depends on STM32_I2S3

config STM32_I2S_MCK
	bool "I2S_MCK"
	default n
	---help---
		TBD.

config STM32_I2S_MAXINFLIGHT
	int "I2S queue size"
	default 16
	---help---
		This is the total number of transfers, both RX and TX, that can be
		enqueue before the caller is required to wait.  This setting
		determines the number certain queue data structures that will be
		pre-allocated.

comment "I2S3 Configuration"

config STM32_I2S3_DATALEN
	int "Data width (bits)"
	default 16
	---help---
		Data width in bits.  This is a default value and may be change
		via the I2S interface

#if STM32_I2S
config STM32_I2S3_RX
	bool "Enable I2S receiver"
	default n
	---help---
		Enable I2S receipt logic

config STM32_I2S3_TX
	bool "Enable I2S transmitter"
	default n
	---help---
		Enable I2S transmission logic

config STM32_I2S_DMADEBUG
	bool "I2S DMA transfer debug"
	depends on DEBUG_DMA
	default n
	---help---
		Enable special debug instrumentation analyze I2S DMA data transfers.
		This logic is as non-invasive as possible:  It samples DMA
		registers at key points in the data transfer and then dumps all of
		the registers at the end of the transfer.

config STM32_I2S_REGDEBUG
	bool "SSC Register level debug"
	depends on DEBUG
	default n
	---help---
		Output detailed register-level SSC device debug information.
		Very invasive! Requires also DEBUG.

endmenu # I2S Configuration

menu "I2C Configuration"
	depends on STM32_I2C

config STM32_I2C_ALT
	bool "Alternate I2C implementation"
	default n if !STM32_PERFORMANCELINE
	default y if STM32_PERFORMANCELINE
	depends on !STM32_STM32F30XX
	---help---
		This selection enables an alternative I2C driver.  This alternate
		driver implements some rather complex workarounds for errata against
		the STM32 F103 "Performance Line".  This selection is an option
		because: (1) It has not yet been fully verified and (2) It is not
		certain that he scope of this workaround is needed only for the F103.

config STM32_I2C_DYNTIMEO
	bool "Use dynamic timeouts"
	default n
	depends on STM32_I2C

config STM32_I2C_DYNTIMEO_USECPERBYTE
	int "Timeout Microseconds per Byte"
	default 500
	depends on STM32_I2C_DYNTIMEO

config STM32_I2C_DYNTIMEO_STARTSTOP
	int "Timeout for Start/Stop (Milliseconds)"
	default 1000
	depends on STM32_I2C_DYNTIMEO

config STM32_I2CTIMEOSEC
	int "Timeout seconds"
	default 0
	depends on STM32_I2C

config STM32_I2CTIMEOMS
	int "Timeout Milliseconds"
	default 500
	depends on STM32_I2C && !STM32_I2C_DYNTIMEO

config STM32_I2CTIMEOTICKS
	int "Timeout for Done and Stop (ticks)"
	default 500
	depends on STM32_I2C && !STM32_I2C_DYNTIMEO

config STM32_I2C_DUTY16_9
	bool "Frequency with Tlow/Thigh = 16/9"
	default n
	depends on STM32_I2C

config STM32_I2C_DMA
	bool "I2C DMA Support"
	default n
	depends on STM32_I2C && STM32_STM32F4XXX && STM32_DMA1 && !I2C_POLLED
	---help---
		This option enables the DMA for I2C transfers.
		Note: The user can define CONFIG_I2C_DMAPRIO: a custom priority value for the
		I2C dma streams, else the default priority level is set to medium.

endmenu

menu "SDIO Configuration"
	depends on STM32_SDIO

config STM32_SDIO_CARD
	bool "SDIO Card support"
	default n
	---help---
		Build in additional support needed only for SDIO cards (vs. SD
		memory cards)

config STM32_SDIO_PULLUP
	bool "Enable internal Pull-Ups"
	default n
	---help---
		If you are using an external SDCard module that does not have the
		pull-up resistors for the SDIO interface (like the Gadgeteer SD Card
		Module) then enable this option to activate the internal pull-up
		resistors.

config STM32_SDIO_DMA
	bool "Support DMA data transfers"
	default y if STM32_DMA2
	default n if !STM32_DMA2
	select SDIO_DMA
	depends on STM32_DMA2
	---help---
		Support DMA data transfers.  Requires STM32_SDIO and config STM32_DMA2.

config STM32_SDIO_DMAPRIO
	hex "SDIO DMA priority"
	default 0x00001000 if STM32_STM32F10XX
	default 0x00010000 if !STM32_STM32F10XX
	---help---
		Select SDIO DMA priority.

		For STM32 F1 family, options are: 0x00000000 low, 0x00001000 medium,
		0x00002000 high, 0x00003000 very high.  Default: medium.

		For other STM32's, options are: 0x00000000 low, 0x00010000 medium,
		0x00020000 high, 0x00030000 very high.  Default: medium.

config STM32_SDIO_WIDTH_D1_ONLY
	bool "Use D1 only"
	default n
	---help---
		Select 1-bit transfer mode.  Default: 4-bit transfer mode.

endmenu

if STM32_BKPSRAM

config STM32_BBSRAM
	bool "BBSRAM File Support"
	default n

config STM32_BBSRAM_FILES
	int "Max Files to support in BBSRAM"
	default 4

config STM32_SAVE_CRASHDUMP
	bool "Enable Saving Panic to BBSRAM"
	default n

endif # STM32_BKPSRAM

config STM32_HAVE_RTC_COUNTER
	bool
	default n

config STM32_HAVE_RTC_SUBSECONDS
	bool
	select ARCH_HAVE_RTC_SUBSECONDS
	default n

menu "RTC Configuration"
	depends on STM32_RTC

config STM32_RTC_MAGIC_REG
	int "BKP register"
	default 0
	range  0 19
	depends on !STM32_HAVE_RTC_COUNTER
	---help---
		The BKP register used to store/check the Magic value to determine if
		RTC is already setup

config STM32_RTC_MAGIC
	hex "RTC Magic 1"
	default 0xfacefeed
	depends on !STM32_HAVE_RTC_COUNTER
	---help---
		Value used as Magic to determine if the RTC is already setup

config STM32_RTC_MAGIC_TIME_SET
	hex "RTC Magic 2"
	default 0xf00dface
	depends on !STM32_HAVE_RTC_COUNTER
	---help---
		Value used as Magic to determine if the RTC has been setup and has
		time set

choice
	prompt "RTC clock source"
	default STM32_RTC_LSECLOCK

config STM32_RTC_LSECLOCK
	bool "LSE clock"
	---help---
		Drive the RTC with the LSE clock

config STM32_RTC_LSICLOCK
	bool "LSI clock"
	---help---
		Drive the RTC with the LSI clock

config STM32_RTC_HSECLOCK
	bool "HSE clock"
	---help---
		Drive the RTC with the HSE clock, divided down to 1MHz.

endchoice # RTC clock source
endmenu # RTC configuration

menu "Ethernet MAC configuration"
	depends on STM32_ETHMAC

config STM32_PHYADDR
	int "PHY address"
	default 1
	---help---
		The 5-bit address of the PHY on the board.  Default: 1

config STM32_PHYINIT
	bool "Board-specific PHY Initialization"
	default n
	---help---
		Some boards require specialized initialization of the PHY before it can be used.
		This may include such things as configuring GPIOs, resetting the PHY, etc.  If
		STM32_PHYINIT is defined in the configuration then the board specific logic must
		provide stm32_phyinitialize();  The STM32 Ethernet driver will call this function
		one time before it first uses the PHY.

config STM32_MII
	bool "Use MII interface"
	default n
	---help---
		Support Ethernet MII interface.

choice
	prompt "MII clock configuration"
	default STM32_MII_MCO if STM32_STM32F10XX
	default STM32_MII_MCO1 if STM32_STM32F20XX || STM32_STM32F4XXX
	depends on STM32_MII

config STM32_MII_MCO
	bool "Use MC0 as MII clock"
	depends on STM32_STM32F10XX
	---help---
		Use MCO to clock the MII interface.  Default:  Use MC0

config STM32_MII_MCO1
	bool "Use MC01 as MII clock"
	depends on (STM32_STM32F20XX || STM32_STM32F4XXX)
	---help---
		Use MCO1 to clock the MII interface.  Default:  Use MC01

config STM32_MII_MCO2
	bool "Use MC02 as MII clock"
	depends on (STM32_STM32F20XX || STM32_STM32F4XXX)
	---help---
		Use MCO2 to clock the MII interface.  Default:  Use MC01

config STM32_MII_EXTCLK
	bool "External MII clock"
	---help---
		Clocking is provided by external logic.  Don't use MCO for MII
		clock.  Default:  Use MC0[1]

endchoice

config STM32_AUTONEG
	bool "Use autonegotiation"
	default y
	---help---
		Use PHY autonegotiation to determine speed and mode

config STM32_ETHFD
	bool "Full duplex"
	default n
	depends on !STM32_AUTONEG
	---help---
		If STM32_AUTONEG is not defined, then this may be defined to select full duplex
		mode. Default: half-duplex

config STM32_ETH100MBPS
	bool "100 Mbps"
	default n
	depends on !STM32_AUTONEG
	---help---
		If STM32_AUTONEG is not defined, then this may be defined to select 100 MBps
		speed.  Default: 10 Mbps

config STM32_PHYSR
	int "PHY Status Register Address (decimal)"
	depends on STM32_AUTONEG
	---help---
		This must be provided if STM32_AUTONEG is defined.  The PHY status register
		address may diff from PHY to PHY.  This configuration sets the address of
		the PHY status register.

config STM32_PHYSR_ALTCONFIG
	bool "PHY Status Alternate Bit Layout"
	default n
	depends on STM32_AUTONEG
	---help---
		Different PHYs present speed and mode information in different ways.  Some
		will present separate information for speed and mode (this is the default).
		Those PHYs, for example, may provide a 10/100 Mbps indication and a separate
		full/half duplex indication. This options selects an alternative representation
		where speed and mode information are combined.  This might mean, for example,
		separate bits for 10HD, 100HD, 10FD and 100FD.

config STM32_PHYSR_SPEED
	hex "PHY Speed Mask"
	depends on STM32_AUTONEG && !STM32_PHYSR_ALTCONFIG
	---help---
		This must be provided if STM32_AUTONEG is defined.  This provides bit mask
		for isolating the 10 or 100MBps speed indication.

config STM32_PHYSR_100MBPS
	hex "PHY 100Mbps Speed Value"
	depends on STM32_AUTONEG && !STM32_PHYSR_ALTCONFIG
	---help---
		This must be provided if STM32_AUTONEG is defined.  This provides the value
		of the speed bit(s) indicating 100MBps speed.

config STM32_PHYSR_MODE
	hex "PHY Mode Mask"
	depends on STM32_AUTONEG && !STM32_PHYSR_ALTCONFIG
	---help---
		This must be provided if STM32_AUTONEG is defined.  This provide bit mask
		for isolating the full or half duplex mode bits.

config STM32_PHYSR_FULLDUPLEX
	hex "PHY Full Duplex Mode Value"
	depends on STM32_AUTONEG && !STM32_PHYSR_ALTCONFIG
	---help---
		This must be provided if STM32_AUTONEG is defined.  This provides the
		value of the mode bits indicating full duplex mode.

config STM32_PHYSR_ALTMODE
	hex "PHY Mode Mask"
	depends on STM32_AUTONEG && STM32_PHYSR_ALTCONFIG
	---help---
		This must be provided if STM32_AUTONEG is defined.  This provide bit mask
		for isolating the speed and full/half duplex mode bits.

config STM32_PHYSR_10HD
	hex "10MBase-T Half Duplex Value"
	depends on STM32_AUTONEG && STM32_PHYSR_ALTCONFIG
	---help---
		This must be provided if STM32_AUTONEG is defined.  This is the value
		under the bit mask that represents the 10Mbps, half duplex setting.

config STM32_PHYSR_100HD
	hex "100Base-T Half Duplex Value"
	depends on STM32_AUTONEG && STM32_PHYSR_ALTCONFIG
	---help---
		This must be provided if STM32_AUTONEG is defined.  This is the value
		under the bit mask that represents the 100Mbps, half duplex setting.

config STM32_PHYSR_10FD
	hex "10Base-T Full Duplex Value"
	depends on STM32_AUTONEG && STM32_PHYSR_ALTCONFIG
	---help---
		This must be provided if STM32_AUTONEG is defined.  This is the value
		under the bit mask that represents the 10Mbps, full duplex setting.

config STM32_PHYSR_100FD
	hex "100Base-T Full Duplex Value"
	depends on STM32_AUTONEG && STM32_PHYSR_ALTCONFIG
	---help---
		This must be provided if STM32_AUTONEG is defined.  This is the value
		under the bit mask that represents the 100Mbps, full duplex setting.

config STM32_ETH_PTP
	bool "Precision Time Protocol (PTP)"
	default n
	---help---
		Precision Time Protocol (PTP).  Not supported but some hooks are indicated
		with this condition.

config STM32_RMII
	bool
	default y if !STM32_MII

choice
	prompt "RMII clock configuration"
	default STM32_RMII_MCO if STM32_STM32F10XX
	default STM32_RMII_MCO1 if STM32_STM32F20XX || STM32_STM32F4XXX
	depends on STM32_RMII

config STM32_RMII_MCO
	bool "Use MC0 as RMII clock"
	depends on STM32_STM32F10XX
	---help---
		Use MCO to clock the RMII interface.  Default:  Use MC0

config STM32_RMII_MCO1
	bool "Use MC01 as RMII clock"
	depends on (STM32_STM32F20XX || STM32_STM32F4XXX)
	---help---
		Use MCO1 to clock the RMII interface.  Default:  Use MC01

config STM32_RMII_MCO2
	bool "Use MC02 as RMII clock"
	depends on (STM32_STM32F20XX || STM32_STM32F4XXX)
	---help---
		Use MCO2 to clock the RMII interface.  Default:  Use MC01

config STM32_RMII_EXTCLK
	bool "External RMII clock"
	---help---
		Clocking is provided by external logic.  Don't use MCO for RMII
		clock.  Default:  Use MC0[1]

endchoice

config STM32_ETHMAC_REGDEBUG
	bool "Register-Level Debug"
	default n
	depends on DEBUG_NET_INFO
	---help---
		Enable very low-level register access debug.  Depends on CONFIG_DEBUG_FEATURES.

endmenu # Ethernet MAC configuration

config STM32_USBHOST
	bool "Enable USB Host Support"
	depends on STM32_OTGFS || STM32_OTGHS
	default n
	select USBHOST

menu "USB FS Host Configuration"
	depends on STM32_OTGFS && STM32_USBHOST

config STM32_OTGFS_RXFIFO_SIZE
	int "Rx Packet Size"
	default 128
	---help---
		Size of the RX FIFO in 32-bit words. Default 128 (512 bytes)

config STM32_OTGFS_NPTXFIFO_SIZE
	int "Non-periodic Tx FIFO Size"
	default 96
	---help---
		Size of the non-periodic Tx FIFO in 32-bit words.  Default 96 (384 bytes)

config STM32_OTGFS_PTXFIFO_SIZE
	int "Periodic Tx FIFO size"
	default 128
	---help---
		Size of the periodic Tx FIFO in 32-bit words.  Default 96 (384 bytes)

config STM32_OTGFS_DESCSIZE
	int "Descriptor Size"
	default 128
	---help---
		Maximum size to allocate for descriptor memory descriptor.  Default: 128

config STM32_OTGFS_SOFINTR
	bool "Enable SOF interrupts"
	default n
	---help---
		Enable SOF interrupts.  Why would you ever want to do that?

config STM32_OTGFS_VBUS_CONTROL
	bool "Enable VBus Control"
	default y
	---help---
		Enable VBus control.  Used when the board has VBus sensing and
		a power switch for the OTG FS USB port.  Disable this config
		if the board lacks this USB VBus control circuitry.

endmenu

menu "USB HS Host Configuration"
	depends on STM32_OTGHS && STM32_USBHOST

config STM32_OTGHS_RXFIFO_SIZE
	int "Rx Packet Size"
	default 128
	---help---
		Size of the RX FIFO in 32-bit words. Default 128 (512 bytes)

config STM32_OTGHS_NPTXFIFO_SIZE
	int "Non-periodic Tx FIFO Size"
	default 96
	---help---
		Size of the non-periodic Tx FIFO in 32-bit words.  Default 96 (384 bytes)

config STM32_OTGHS_PTXFIFO_SIZE
	int "Periodic Tx FIFO size"
	default 128
	---help---
		Size of the periodic Tx FIFO in 32-bit words.  Default 96 (384 bytes)

config STM32_OTGHS_DESCSIZE
	int "Descriptor Size"
	default 128
	---help---
		Maximum size to allocate for descriptor memory descriptor.  Default: 128

config STM32_OTGHS_SOFINTR
	bool "Enable SOF interrupts"
	default n
	---help---
		Enable SOF interrupts.  Why would you ever want to do that?

config STM32_OTGHS_VBUS_CONTROL
	bool "Enable VBus Control"
	default y
	---help---
		Enable VBus control.  Used when the board has VBus sensing and
		a power switch for the OTG HS USB port.  Disable this config
		if the board lacks this USB VBus control circuitry.

endmenu

menu "USB Host Debug Configuration"
	depends on STM32_USBHOST

config STM32_USBHOST_REGDEBUG
	bool "Register-Level Debug"
	default n
	depends on STM32_USBHOST && DEBUG_USB_INFO
	---help---
		Enable very low-level register access debug.

config STM32_USBHOST_PKTDUMP
	bool "Packet Dump Debug"
	default n
	depends on STM32_USBHOST && DEBUG_USB_INFO
	---help---
		Dump all incoming and outgoing USB packets.

endmenu

comment "USB Device Configuration"

menu "USB Full Speed Debug Configuration"
	depends on STM32_USBFS

config STM32_USBFS_REGDEBUG
	bool "Register-Level Debug"
	default n
	depends on STM32_USBFS && DEBUG_USB_INFO
	---help---
		Enable very low-level register access debug.

endmenu

menu "OTG Configuration"
	depends on STM32_OTGFS

config OTG_ID_GPIO_DISABLE
	bool "Disable the use of GPIO_OTG_ID pin."
	default n
	---help---
		Disables/Enables the use of GPIO_OTG_ID pin. This allows non OTG use
		cases to reuse this GPIO pin and ensure it is not set incorrectlty
		during OS boot.

endmenu

config STM32_USB_ITRMP
	bool "Re-map USB interrupt"
	default n if !STM32_CAN1
	default y if STM32_CAN1
	depends on STM32_USB && STM32_STM32F30XX
	---help---
		The legacy USB in the F1 series shared interrupt lines with USB
		device and CAN1.  In the F3 series, a hardware options was added to
		either retain the legacy F1 behavior or to map the USB interrupts to
		their own dedicated vectors.  The option is available only for the
		F3 family and selects the use of the dedicated USB interrupts.

menu "CAN driver configuration"
	depends on STM32_CAN

choice
	prompt "CAN character driver or SocketCAN support"
	default STM32_CAN_CHARDRIVER

config STM32_CAN_CHARDRIVER
	bool "STM32 CAN character driver support"
	select ARCH_HAVE_CAN_ERRORS
	select CAN

config STM32_CAN_SOCKET
	bool "STM32 CAN SocketCAN support"
	select NET_CAN_HAVE_ERRORS

endchoice # CAN character driver or SocketCAN support

config STM32_CAN1_BAUD
	int "CAN1 BAUD"
	default 250000
	depends on STM32_CAN1
	---help---
		CAN1 BAUD rate.  Required if CONFIG_STM32_CAN1 is defined.

config STM32_CAN2_BAUD
	int "CAN2 BAUD"
	default 250000
	depends on STM32_CAN2
	---help---
		CAN2 BAUD rate.  Required if CONFIG_STM32_CAN2 is defined.

config STM32_CAN_TSEG1
	int "TSEG1 quanta"
	default 6
	---help---
		The number of CAN time quanta in segment 1. Default: 6

config STM32_CAN_TSEG2
	int "TSEG2 quanta"
	default 7
	---help---
		The number of CAN time quanta in segment 2. Default: 7

config STM32_CAN_REGDEBUG
	bool "CAN Register level debug"
	depends on DEBUG_CAN_INFO
	default n
	---help---
		Output detailed register-level CAN device debug information.
		Requires also CONFIG_DEBUG_CAN_INFO.

endmenu # "CAN driver configuration"

menu "FDCAN driver configuration"
	depends on STM32_FDCAN

choice
	prompt "FDCAN character driver or SocketCAN support"
	default STM32_FDCAN_CHARDRIVER

config STM32_FDCAN_CHARDRIVER
	bool "STM32 FDCAN character driver support"
	select ARCH_HAVE_CAN_ERRORS
	select CAN

config STM32_FDCAN_SOCKET
	bool "STM32 FDCAN SocketCAN support"
	select NET_CAN_HAVE_ERRORS
	select NET_CAN_HAVE_CANFD

endchoice # FDCAN character driver or SocketCAN support

config STM32_FDCAN_REGDEBUG
	bool "CAN Register level debug"
	depends on DEBUG_CAN_INFO
	default n
	---help---
		Output detailed register-level CAN device debug information.
		Requires also CONFIG_DEBUG_CAN_INFO.

config STM32_FDCAN_QUEUE_MODE
	bool "FDCAN QUEUE mode (vs FIFO mode)"
	default n

menu "FDCAN1 device driver options"
	depends on STM32_FDCAN1

choice
	prompt "FDCAN1 frame format"
	default STM32_FDCAN1_ISO11898_1

config STM32_FDCAN1_ISO11898_1
	bool "ISO11898-1"
	---help---
		Enable ISO11898-1 frame format

config STM32_FDCAN1_NONISO_FORMAT
	bool "Non ISO"
	---help---
		Enable Non ISO, Bosch CAN FD Specification V1.0

endchoice # FDCAN1 frame format

choice
	prompt "FDCAN1 mode"
	default STM32_FDCAN1_CLASSIC

config STM32_FDCAN1_CLASSIC
	bool "Classic CAN"
	---help---
		Enable Clasic CAN mode

config STM32_FDCAN1_FD
	bool "CAN FD"
	depends on CAN_FD || NET_CAN_CANFD
	---help---
		Enable CAN FD mode

config STM32_FDCAN1_FD_BRS
	bool "CAN FD with fast bit rate switching"
	depends on CAN_FD || NET_CAN_CANFD
	---help---
		Enable CAN FD mode with fast bit rate switching mode.

endchoice # FDCAN1 mode

config STM32_FDCAN1_LOOPBACK
	bool "Enable FDCAN1 loopback mode"
	default n
	---help---
		Enable the FDCAN1 local loopback mode for testing purposes.

comment "Nominal Bit Timing"

config STM32_FDCAN1_BITRATE
	int "FDCAN bitrate"
	default 500000
	range 0 1000000
	---help---
		FDCAN1 bitrate in bits per second.  Required if STM32_FDCAN1 is defined.

config STM32_FDCAN1_NTSEG1
	int "FDCAN1 NTSEG1 (PropSeg + PhaseSeg1)"
	default 6
	range 1 256 if STM32_STM32G4XXX
	---help---
		The length of the bit time is Tquanta * (SyncSeg + PropSeg + PhaseSeg1 + PhaseSeg2).

config STM32_FDCAN1_NTSEG2
	int "FDCAN1 NTSEG2 (PhaseSeg2)"
	default 7
	range 1 128 if STM32_STM32G4XXX
	---help---
		The length of the bit time is Tquanta * (SyncSeg + PropSeg + PhaseSeg1 + PhaseSeg2).

config STM32_FDCAN1_NSJW
	int "FDCAN1 synchronization jump width"
	default 1
	range 1 128 if STM32_STM32G4XXX
	---help---
		The length of the bit time is Tquanta * (SyncSeg + PropSeg + PhaseSeg1 + PhaseSeg2).

comment "Data Bit Timing"
	depends on CAN_FD && STM32_FDCAN1_FD_BRS

config STM32_FDCAN1_DBITRATE
	int "FDCAN1 data bitrate"
	default 2000000
	depends on CAN_FD && STM32_FDCAN1_FD_BRS
	---help---
		FDCAN1 bitrate in bits per second.  Required if operating in FD mode with bit rate switching (BRS).

config STM32_FDCAN1_DTSEG1
	int "FDCAN1 DTSEG1 (PropSeg + PhaseSeg1 of data phase)"
	default 4
	range 1 31 if STM32_STM32G4XXX
	depends on CAN_FD && STM32_FDCAN1_FD_BRS
	---help---
		The length of the bit time is Tquanta * (SyncSeg + PropSeg + PhaseSeg1 + PhaseSeg2).

config STM32_FDCAN1_DTSEG2
	int "FDCAN1 DTSEG2 (PhaseSeg2 of data phase)"
	default 4
	range 1 15 if STM32_STM32G4XXX
	depends on CAN_FD && STM32_FDCAN1_FD_BRS
	---help---
		The length of the bit time is Tquanta * (SyncSeg + PropSeg + PhaseSeg1 + PhaseSeg2).

config STM32_FDCAN1_DSJW
	int "FDCAN1 fast synchronization jump width"
	default 2
	range 1 15 if STM32_STM32G4XXX
	depends on CAN_FD && STM32_FDCAN1_FD_BRS
	---help---
		The duration of a synchronization jump is Tcan_clk x DSJW.

endmenu # FDCAN1 device driver options

menu "FDCAN2 device driver options"
	depends on STM32_FDCAN2

choice
	prompt "FDCAN2 frame format"
	default STM32_FDCAN2_ISO11898_1

config STM32_FDCAN2_ISO11898_1
	bool "ISO11898-1"
	---help---
		Enable ISO11898-1 frame format

config STM32_FDCAN2_NONISO_FORMAT
	bool "Non ISO"
	---help---
		Enable Non ISO, Bosch CAN FD Specification V1.0

endchoice # FDCAN2 frame format

choice
	prompt "FDCAN2 mode"
	default STM32_FDCAN2_CLASSIC

config STM32_FDCAN2_CLASSIC
	bool "Classic CAN"
	---help---
		Enable Clasic CAN mode

config STM32_FDCAN2_FD
	bool "CAN FD"
	depends on CAN_FD || NET_CAN_CANFD
	---help---
		Enable CAN FD mode

config STM32_FDCAN2_FD_BRS
	bool "CAN FD with fast bit rate switching"
	depends on CAN_FD || NET_CAN_CANFD
	---help---
		Enable CAN FD mode with fast bit rate switching mode.

endchoice # FDCAN2 mode

config STM32_FDCAN2_LOOPBACK
	bool "Enable FDCAN2 loopback mode"
	default n
	---help---
		Enable the FDCAN2 local loopback mode for testing purposes.

comment "Nominal Bit Timing"

config STM32_FDCAN2_BITRATE
	int "FDCAN bitrate"
	default 500000
	range 0 1000000
	---help---
		FDCAN2 bitrate in bits per second.  Required if STM32_FDCAN2 is defined.

config STM32_FDCAN2_NTSEG1
	int "FDCAN2 NTSEG1 (PropSeg + PhaseSeg1)"
	default 6
	range 1 256 if STM32_STM32G4XXX
	---help---
		The length of the bit time is Tquanta * (SyncSeg + PropSeg + PhaseSeg1 + PhaseSeg2).

config STM32_FDCAN2_NTSEG2
	int "FDCAN2 NTSEG2 (PhaseSeg2)"
	default 7
	range 1 128 if STM32_STM32G4XXX
	---help---
		The length of the bit time is Tquanta * (SyncSeg + PropSeg + PhaseSeg1 + PhaseSeg2).

config STM32_FDCAN2_NSJW
	int "FDCAN2 synchronization jump width"
	default 1
	range 1 128 if STM32_STM32G4XXX
	---help---
		The length of the bit time is Tquanta * (SyncSeg + PropSeg + PhaseSeg1 + PhaseSeg2).

comment "Data Bit Timing"
	depends on CAN_FD && STM32_FDCAN2_FD_BRS

config STM32_FDCAN2_DBITRATE
	int "FDCAN2 data bitrate"
	default 2000000
	depends on CAN_FD && STM32_FDCAN2_FD_BRS
	---help---
		FDCAN2 bitrate in bits per second.  Required if operating in FD mode with bit rate switching (BRS).

config STM32_FDCAN2_DTSEG1
	int "FDCAN2 DTSEG1 (PropSeg + PhaseSeg1 of data phase)"
	default 4
	range 1 31 if STM32_STM32G4XXX
	depends on CAN_FD && STM32_FDCAN2_FD_BRS
	---help---
		The length of the bit time is Tquanta * (SyncSeg + PropSeg + PhaseSeg1 + PhaseSeg2).

config STM32_FDCAN2_DTSEG2
	int "FDCAN2 DTSEG2 (PhaseSeg2 of data phase)"
	default 4
	range 1 15 if STM32_STM32G4XXX
	depends on CAN_FD && STM32_FDCAN2_FD_BRS
	---help---
		The length of the bit time is Tquanta * (SyncSeg + PropSeg + PhaseSeg1 + PhaseSeg2).

config STM32_FDCAN2_DSJW
	int "FDCAN2 fast synchronization jump width"
	default 2
	range 1 15 if STM32_STM32G4XXX
	depends on CAN_FD && STM32_FDCAN2_FD_BRS
	---help---
		The duration of a synchronization jump is Tcan_clk x DSJW.

endmenu # FDCAN2 device driver options

menu "FDCAN3 device driver options"
	depends on STM32_FDCAN3

choice
	prompt "FDCAN3 frame format"
	default STM32_FDCAN3_ISO11898_1

config STM32_FDCAN3_ISO11898_1
	bool "ISO11898-1"
	---help---
		Enable ISO11898-1 frame format

config STM32_FDCAN3_NONISO_FORMAT
	bool "Non ISO"
	---help---
		Enable Non ISO, Bosch CAN FD Specification V1.0

endchoice # FDCAN3 frame format

choice
	prompt "FDCAN3 mode"
	default STM32_FDCAN3_CLASSIC

config STM32_FDCAN3_CLASSIC
	bool "Classic CAN"
	---help---
		Enable Clasic CAN mode

config STM32_FDCAN3_FD
	bool "CAN FD"
	depends on CAN_FD || NET_CAN_CANFD
	---help---
		Enable CAN FD mode

config STM32_FDCAN3_FD_BRS
	bool "CAN FD with fast bit rate switching"
	depends on CAN_FD || NET_CAN_CANFD
	---help---
		Enable CAN FD mode with fast bit rate switching mode.

endchoice # FDCAN3 mode

config STM32_FDCAN3_LOOPBACK
	bool "Enable FDCAN3 loopback mode"
	default n
	---help---
		Enable the FDCAN3 local loopback mode for testing purposes.

comment "Nominal Bit Timing"

config STM32_FDCAN3_BITRATE
	int "FDCAN bitrate"
	default 500000
	range 0 1000000
	---help---
		FDCAN3 bitrate in bits per second.  Required if STM32_FDCAN3 is defined.

config STM32_FDCAN3_NTSEG1
	int "FDCAN3 NTSEG1 (PropSeg + PhaseSeg1)"
	default 6
	range 1 256 if STM32_STM32G4XXX
	---help---
		The length of the bit time is Tquanta * (SyncSeg + PropSeg + PhaseSeg1 + PhaseSeg2).

config STM32_FDCAN3_NTSEG2
	int "FDCAN3 NTSEG2 (PhaseSeg2)"
	default 7
	range 1 128 if STM32_STM32G4XXX
	---help---
		The length of the bit time is Tquanta * (SyncSeg + PropSeg + PhaseSeg1 + PhaseSeg2).

config STM32_FDCAN3_NSJW
	int "FDCAN3 synchronization jump width"
	default 1
	range 1 128 if STM32_STM32G4XXX
	---help---
		The length of the bit time is Tquanta * (SyncSeg + PropSeg + PhaseSeg1 + PhaseSeg2).

comment "Data Bit Timing"
	depends on CAN_FD && STM32_FDCAN3_FD_BRS

config STM32_FDCAN3_DBITRATE
	int "FDCAN3 data bitrate"
	default 2000000
	depends on CAN_FD && STM32_FDCAN3_FD_BRS
	---help---
		FDCAN3 bitrate in bits per second.  Required if operating in FD mode with bit rate switching (BRS).

config STM32_FDCAN3_DTSEG1
	int "FDCAN3 DTSEG1 (PropSeg + PhaseSeg1 of data phase)"
	default 4
	range 1 31 if STM32_STM32G4XXX
	depends on CAN_FD && STM32_FDCAN3_FD_BRS
	---help---
		The length of the bit time is Tquanta * (SyncSeg + PropSeg + PhaseSeg1 + PhaseSeg2).

config STM32_FDCAN3_DTSEG2
	int "FDCAN3 DTSEG2 (PhaseSeg2 of data phase)"
	default 4
	range 1 15 if STM32_STM32G4XXX
	depends on CAN_FD && STM32_FDCAN3_FD_BRS
	---help---
		The length of the bit time is Tquanta * (SyncSeg + PropSeg + PhaseSeg1 + PhaseSeg2).

config STM32_FDCAN3_DSJW
	int "FDCAN3 fast synchronization jump width"
	default 2
	range 1 15 if STM32_STM32G4XXX
	depends on CAN_FD && STM32_FDCAN3_FD_BRS
	---help---
		The duration of a synchronization jump is Tcan_clk x DSJW.

endmenu # FDCAN3 device driver options

endmenu  # "FDCAN driver configuration"

if STM32_LTDC

menu "LTDC Configuration"

config STM32_LTDC_BACKLIGHT
	bool "Backlight support"
	default y

config STM32_LTDC_DEFBACKLIGHT
	hex "Default backlight level"
	default 0xf0

config STM32_LTDC_BACKCOLOR
	hex "Background color"
	default 0x0
	---help---
		This is the background color that will be used as the LTDC
		background layer color.  It is an RGB888 format value.

config STM32_LTDC_DITHER
	bool "Dither support"
	default n

config STM32_LTDC_DITHER_RED
	depends on STM32_LTDC_DITHER
	int "Dither red width"
	range 0 7
	default 2
	---help---
		This is the dither red width.

config STM32_LTDC_DITHER_GREEN
	depends on STM32_LTDC_DITHER
	int "Dither green width"
	range 0 7
	default 2
	---help---
		This is the dither green width.

config STM32_LTDC_DITHER_BLUE
	depends on STM32_LTDC_DITHER
	int "Dither blue width"
	range 0 7
	default 2
	---help---
		This is the dither blue width.

config STM32_LTDC_FB_BASE
	hex "Framebuffer memory start address"
	default 0
	---help---
		If you are using the LTDC, then you must provide the address
		of the start of the framebuffer.  This address will typically
		be in the SRAM or SDRAM memory region of the FSMC/FMC.

config STM32_LTDC_FB_SIZE
	int "Framebuffer memory size (bytes)"
	default 0
	---help---
		Must be the whole size of the active LTDC layer.

config STM32_LTDC_L1_CHROMAKEYEN
	bool "Enable chromakey support for layer 1"
	default y

config STM32_LTDC_L1_CHROMAKEY
	hex "Layer L1 initial chroma key"
	default 0x00000000

config STM32_LTDC_L1_COLOR
	hex "Layer L1 default color"
	default 0x00000000

choice
	prompt "Layer 1 color format"
	default STM32_LTDC_L1_RGB565

config STM32_LTDC_L1_L8
	bool "8 bpp L8 (8-bit CLUT)"
	depends on STM32_FB_CMAP

config STM32_LTDC_L1_AL44
	bool "8 bpp AL44 (4-bit alpha + 4-bit CLUT)"
	depends on STM32_FB_CMAP

config STM32_LTDC_L1_AL88
	bool "16 bpp AL88 (8-bit alpha + 8-bit CLUT)"
	depends on STM32_FB_CMAP

config STM32_LTDC_L1_RGB565
	bool "16 bpp RGB 565"
	depends on !STM32_FB_CMAP

config STM32_LTDC_L1_ARGB4444
	bool "16 bpp ARGB 4444"
	depends on !STM32_FB_CMAP

config STM32_LTDC_L1_ARGB1555
	bool "16 bpp ARGB 1555"
	depends on !STM32_FB_CMAP

config STM32_LTDC_L1_RGB888
	bool "24 bpp RGB 888"
	depends on !STM32_FB_CMAP

config STM32_LTDC_L1_ARGB8888
	bool "32 bpp ARGB 8888"
	depends on !STM32_FB_CMAP

endchoice # Layer 1 color format

config STM32_LTDC_L2
	bool "Enable Layer 2 support"
	default y

if STM32_LTDC_L2

config STM32_LTDC_L2_COLOR
	hex "Layer L2 default color"
	default 0x00000000

config STM32_LTDC_L2_CHROMAKEYEN
	bool "Enable chromakey support for layer 2"
	default y

config STM32_LTDC_L2_CHROMAKEY
	hex "Layer L2 initial chroma key"
	default 0x00000000

choice
	prompt "Layer 2 (top layer) color format"
	default STM32_LTDC_L2_RGB565

config STM32_LTDC_L2_L8
	depends on STM32_LTDC_L1_L8
	bool "8 bpp L8 (8-bit CLUT)"

config STM32_LTDC_L2_AL44
	depends on STM32_LTDC_L1_AL44
	bool "8 bpp AL44 (4-bit alpha + 4-bit CLUT)"

config STM32_LTDC_L2_AL88
	depends on STM32_LTDC_L1_AL88
	bool "16 bpp AL88 (8-bit alpha + 8-bit CLUT)"

config STM32_LTDC_L2_RGB565
	depends on STM32_LTDC_L1_RGB565
	bool "16 bpp RGB 565"

config STM32_LTDC_L2_ARGB4444
	depends on STM32_LTDC_L1_ARGB4444
	bool "16 bpp ARGB 4444"

config STM32_LTDC_L2_ARGB1555
	depends on STM32_LTDC_L1_ARGB1555
	bool "16 bpp ARGB 1555"

config STM32_LTDC_L2_RGB888
	depends on STM32_LTDC_L1_RGB888
	bool "24 bpp RGB 888"

config STM32_LTDC_L2_ARGB8888
	depends on STM32_LTDC_L1_ARGB8888
	bool "32 bpp ARGB 8888"

endchoice # Layer 2 color format

endif # STM32_LTDC_L2

config STM32_FB_CMAP
	bool "Color map support"
	default y
	select FB_CMAP
	---help---
		Enabling color map support is necessary for LTDC L8 format.

config STM32_FB_TRANSPARENCY
	bool "Transparency color map support"
	default y
	depends on STM32_FB_CMAP
	select FB_TRANSPARENCY
	---help---
		Enabling transparency color map support is necessary for LTDC L8 format.

config STM32_LTDC_REGDEBUG
	bool "LTDC Register level debug"
	depends on DEBUG_INFO && DEBUG_LCD
	default n
	---help---
		Output detailed register-level LTDC device debug information.
endmenu

endif # STM32_LTDC

if STM32_DMA2D

menu "DMA2D Configuration"

config STM32_DMA2D_NLAYERS
	int "Number DMA2D overlays"
	default 1
	range 1 256
	---help---
		Number of supported DMA2D layer.

config STM32_DMA2D_LAYER_SHARED
	bool "Overlays shared memory region"
	default n
	---help---
		Several overlays can share the same memory region.
		Setup a whole memory area (usually multiple size of the visible screen)
		allows image preprocessing before they become visible by blit operation.

config STM32_DMA2D_LAYER_PPLINE
	int "Pixel per line"
	default 1
	range 1 65535
	---help---
		If you are using the DMA2D, then you must provide the pixel per line or
		width of the overlay.

config STM32_DMA2D_FB_BASE
	hex "Framebuffer memory start address"
	default 0
	---help---
		If you are using the DMA2D, then you must provide the address
		of the start of the DMA2D overlays framebuffer. This address will typically
		be in the SRAM or SDRAM memory region of the FSMC/FMC.

config STM32_DMA2D_FB_SIZE
	int "Framebuffer memory size (bytes)"
	default 0
	---help---
		Must be the whole size of all DMA2D overlays.

menu "Supported pixel format"

config STM32_DMA2D_L8
	depends on STM32_FB_CMAP && STM32_LTDC_L1_L8
	bool "8 bpp L8 (8-bit CLUT)"
	default y

config STM32_DMA2D_AL44
	depends on STM32_FB_CMAP && STM32_LTDC_L1_AL44
	bool "8 bpp AL44 (4-bit alpha + 4-bit CLUT)"
	default y

config STM32_DMA2D_AL88
	depends on STM32_FB_CMAP && STM32_LTDC_L1_AL88
	bool "16 bpp AL88 (8-bit alpha + 8-bit CLUT)"
	default y

config STM32_DMA2D_RGB565
	bool "16 bpp RGB 565"
	depends on STM32_LTDC_L1_RGB565
	default y

config STM32_DMA2D_ARGB4444
	bool "16 bpp ARGB 4444"
	depends on STM32_LTDC_L1_ARGB4444
	default y

config STM32_DMA2D_ARGB1555
	bool "16 bpp ARGB 1555"
	depends on STM32_LTDC_L1_ARGB15555
	default y

config STM32_DMA2D_RGB888
	bool "24 bpp RGB 888"
	depends on STM32_LTDC_L1_RGB888
	default y

config STM32_DMA2D_ARGB8888
	bool "32 bpp ARGB 8888"
	depends on STM32_LTDC_L1_ARGB8888
	default y

endmenu

config STM32_DMA2D_REGDEBUG
	bool "DMA2D Register level debug"
	depends on DEBUG_INFO && DEBUG_LCD
	default n
	---help---
		Output detailed register-level DMA2D device debug information.

endmenu
endif # STM32_DMA2D

menu "STM32 QEncoder Driver"
	depends on SENSORS_QENCODER
	depends on STM32_TIM1 || STM32_TIM2 || STM32_TIM3 || STM32_TIM4 || STM32_TIM5 || STM32_TIM8

config STM32_QENCODER_DISABLE_EXTEND16BTIMERS
	bool "Disable QEncoder timers extension from 16-bit to 32-bit"
	default n

config STM32_QENCODER_INDEX_PIN
	bool "Enable QEncoder timers support for index pin"
	default n

config STM32_TIM1_QE
	bool "TIM1 QE"
	default n
	depends on STM32_TIM1
	---help---
		Reserve TIM1 for use by QEncoder.

if STM32_TIM1_QE

config STM32_TIM1_QEPSC
	int "TIM1 QE pulse prescaler"
	default 1
	---help---
		This prescaler divides the number of recorded encoder pulses,
		limiting the count rate at the expense of resolution.

endif

config STM32_TIM2_QE
	bool "TIM2 QE"
	default n
	depends on STM32_TIM2
	---help---
		Reserve TIM2 for use by QEncoder.

if STM32_TIM2_QE

config STM32_TIM2_QEPSC
	int "TIM2 QE pulse prescaler"
	default 1
	---help---
		This prescaler divides the number of recorded encoder pulses,
		limiting the count rate at the expense of resolution.

endif

config STM32_TIM3_QE
	bool "TIM3 QE"
	default n
	depends on STM32_TIM3
	---help---
		Reserve TIM3 for use by QEncoder.

if STM32_TIM3_QE

config STM32_TIM3_QEPSC
	int "TIM3 QE pulse prescaler"
	default 1
	---help---
		This prescaler divides the number of recorded encoder pulses,
		limiting the count rate at the expense of resolution.

endif

config STM32_TIM4_QE
	bool "TIM4 QE"
	default n
	depends on STM32_TIM4
	---help---
		Reserve TIM4 for use by QEncoder.

if STM32_TIM4_QE

config STM32_TIM4_QEPSC
	int "TIM4 QE pulse prescaler"
	default 1
	---help---
		This prescaler divides the number of recorded encoder pulses,
		limiting the count rate at the expense of resolution.

endif

config STM32_TIM5_QE
	bool "TIM5 QE"
	default n
	depends on STM32_TIM5
	---help---
		Reserve TIM5 for use by QEncoder.

if STM32_TIM5_QE

config STM32_TIM5_QEPSC
	int "TIM5 QE pulse prescaler"
	default 1
	---help---
		This prescaler divides the number of recorded encoder pulses,
		limiting the count rate at the expense of resolution.

endif

config STM32_TIM8_QE
	bool "TIM8 QE"
	default n
	depends on STM32_TIM8
	---help---
		Reserve TIM8 for use by QEncoder.

if STM32_TIM8_QE

config STM32_TIM8_QEPSC
	int "TIM8 QE pulse prescaler"
	default 1
	---help---
		This prescaler divides the number of recorded encoder pulses,
		limiting the count rate at the expense of resolution.

endif

config STM32_QENCODER_FILTER
	bool "Enable filtering on STM32 QEncoder input"
	default y

choice
	depends on STM32_QENCODER_FILTER
	prompt "Input channel sampling frequency"
	default STM32_QENCODER_SAMPLE_FDTS_4

config STM32_QENCODER_SAMPLE_FDTS
	bool "fDTS"

config STM32_QENCODER_SAMPLE_CKINT
	bool "fCK_INT"

config STM32_QENCODER_SAMPLE_FDTS_2
	bool "fDTS/2"

config STM32_QENCODER_SAMPLE_FDTS_4
	bool "fDTS/4"

config STM32_QENCODER_SAMPLE_FDTS_8
	bool "fDTS/8"

config STM32_QENCODER_SAMPLE_FDTS_16
	bool "fDTS/16"

config STM32_QENCODER_SAMPLE_FDTS_32
	bool "fDTS/32"

endchoice

choice
	depends on STM32_QENCODER_FILTER
	prompt "Input channel event count"
	default STM32_QENCODER_SAMPLE_EVENT_6

config STM32_QENCODER_SAMPLE_EVENT_1
	depends on STM32_QENCODER_SAMPLE_FDTS
	bool "1"

config STM32_QENCODER_SAMPLE_EVENT_2
	depends on STM32_QENCODER_SAMPLE_CKINT
	bool "2"

config STM32_QENCODER_SAMPLE_EVENT_4
	depends on STM32_QENCODER_SAMPLE_CKINT
	bool "4"

config STM32_QENCODER_SAMPLE_EVENT_5
	depends on STM32_QENCODER_SAMPLE_FDTS_16 || STM32_QENCODER_SAMPLE_FDTS_32
	bool "5"

config STM32_QENCODER_SAMPLE_EVENT_6
	depends on !STM32_QENCODER_SAMPLE_FDTS && !STM32_QENCODER_SAMPLE_CKINT
	bool "6"

config STM32_QENCODER_SAMPLE_EVENT_8
	depends on !STM32_QENCODER_SAMPLE_FDTS
	bool "8"

endchoice

endmenu

menuconfig STM32_FOC
	bool "STM32 lower-half FOC support"
	default n
	select ARCH_IRQPRIO
	select STM32_PWM_MULTICHAN
	select STM32_PWM_LL_OPS
	select STM32_ADC_LL_OPS
	select STM32_ADC_CHANGE_SAMPLETIME
	select STM32_ADC_NO_STARTUP_CONV
	select STM32_ADC_FORCE_SCAN if STM32_HAVE_IP_ADC_V1

if STM32_FOC

config STM32_FOC_FOC0
	bool "FOC0 device (TIM1 for PWM modulation)"
	default n
	depends on STM32_HAVE_TIM1
	select STM32_FOC_USE_TIM1
	---help---
		Enable support for FOC0 device that uses TIM1 for PWM modulation

config STM32_FOC_FOC1
	bool "FOC1 device (TIM8 for PWM modulation)"
	default n
	depends on STM32_HAVE_TIM8
	select STM32_FOC_USE_TIM8
	---help---
		Enable support for FOC1 device that uses TIM8 for PWM modulation

choice
	prompt "FOC ADC trigger selection"
	default STM32_FOC_ADC_TRGO

config STM32_FOC_ADC_CCR4
	bool "FOC uses CCR4 as ADC trigger"
	---help---
		This option uses the software frequency prescaler and is
		not possible for 4-phase output.

config STM32_FOC_ADC_TRGO
	bool "FOC uses TRGO as ADC trigger"
	depends on STM32_HAVE_IP_ADC_V2 || (STM32_HAVE_IP_ADC_V1 && !STM32_FOC_FOC1)
	select STM32_PWM_TRGO
	---help---
		This option allows you to use higher PWM frequency and works for 4-phase output.
		It is not possible for ADC IPv1 if FOC1 enabled (no T8TRGO in JEXTSEL).

endchoice # "FOC ADC trigger selection"

if STM32_FOC_FOC0

choice
	prompt "FOC0 device ADC selection"
	default STM32_FOC_FOC0_ADC1

config STM32_FOC_FOC0_ADC1
	bool "FOC0 uses ADC1"
	depends on STM32_HAVE_ADC1
	select STM32_FOC_USE_ADC1

config STM32_FOC_FOC0_ADC2
	bool "FOC0 uses ADC2"
	depends on STM32_HAVE_ADC2
	select STM32_FOC_USE_ADC2

config STM32_FOC_FOC0_ADC3
	bool "FOC0 uses ADC3"
	depends on STM32_HAVE_ADC3
	select STM32_FOC_USE_ADC3

config STM32_FOC_FOC0_ADC4
	bool "FOC0 uses ADC4"
	depends on STM32_HAVE_ADC4
	select STM32_FOC_USE_ADC4

endchoice # "FOC0 device ADC selection"

endif # STM32_FOC_FOC0

if STM32_FOC_FOC1

choice
	prompt "FOC1 device ADC selection"
	default STM32_FOC_FOC1_ADC2

config STM32_FOC_FOC1_ADC1
	bool "FOC1 uses ADC1"
	depends on STM32_HAVE_ADC1
	select STM32_FOC_USE_ADC1

config STM32_FOC_FOC1_ADC2
	bool "FOC1 uses ADC2"
	depends on STM32_HAVE_ADC2
	select STM32_FOC_USE_ADC2

config STM32_FOC_FOC1_ADC3
	bool "FOC1 uses ADC3"
	depends on STM32_HAVE_ADC3
	select STM32_FOC_USE_ADC3

config STM32_FOC_FOC1_ADC4
	bool "FOC1 uses ADC4"
	depends on STM32_HAVE_ADC4
	select STM32_FOC_USE_ADC4

endchoice # "FOC0 device ADC selection"

endif # STM32_FOC_FOC1

config STM32_FOC_HAS_PWM_COMPLEMENTARY
	bool "FOC PWM has complementary outputs"
	default n
	---help---
		Enable complementary outputs for the FOC PWM (sometimes called 6-PWM mode)

# hidden variables and automatic configuration

config STM32_FOC_USE_TIM1
	bool
	default n
	select STM32_TIM1
	select STM32_TIM1_PWM
	select STM32_TIM1_CHANNEL1
	select STM32_TIM1_CHANNEL2
	select STM32_TIM1_CHANNEL3
	select STM32_TIM1_CHANNEL4 if STM32_FOC_ADC_CCR4
	select STM32_TIM1_CH1OUT
	select STM32_TIM1_CH2OUT
	select STM32_TIM1_CH3OUT
	select STM32_TIM1_CH4OUT if STM32_FOC_ADC_CCR4
	select STM32_TIM1_CH1NOUT if STM32_FOC_HAS_PWM_COMPLEMENTARY
	select STM32_TIM1_CH2NOUT if STM32_FOC_HAS_PWM_COMPLEMENTARY
	select STM32_TIM1_CH3NOUT if STM32_FOC_HAS_PWM_COMPLEMENTARY
	---help---
		The TIM1 generates PWM for the FOC

config STM32_FOC_USE_TIM8
	bool
	default n
	select STM32_TIM8
	select STM32_TIM8_PWM
	select STM32_TIM8_CHANNEL1
	select STM32_TIM8_CHANNEL2
	select STM32_TIM8_CHANNEL3
	select STM32_TIM8_CHANNEL4 if STM32_FOC_ADC_CCR4
	select STM32_TIM8_CH1OUT
	select STM32_TIM8_CH2OUT
	select STM32_TIM8_CH3OUT
	select STM32_TIM8_CH4OUT if STM32_FOC_ADC_CCR4
	select STM32_TIM8_CH1NOUT if STM32_FOC_HAS_PWM_COMPLEMENTARY
	select STM32_TIM8_CH2NOUT if STM32_FOC_HAS_PWM_COMPLEMENTARY
	select STM32_TIM8_CH3NOUT if STM32_FOC_HAS_PWM_COMPLEMENTARY
	---help---
		The TIM8 generates PWM for the FOC

config STM32_FOC_USE_ADC1
	bool
	default n
	select STM32_ADC1
	select STM32_ADC1_SCAN if STM32_HAVE_IP_ADC_V1
	select STM32_ADC1_JEXTSEL

config STM32_FOC_USE_ADC2
	bool
	default n
	select STM32_ADC2
	select STM32_ADC2_SCAN if STM32_HAVE_IP_ADC_V1
	select STM32_ADC2_JEXTSEL

config STM32_FOC_USE_ADC3
	bool
	default n
	select STM32_ADC3
	select STM32_ADC3_SCAN if STM32_HAVE_IP_ADC_V1
	select STM32_ADC3_JEXTSEL

config STM32_FOC_USE_ADC4
	bool
	default n
	select STM32_ADC4
	select STM32_ADC3_JEXTSEL

config STM32_FOC_G4_ADCCHAN0_WORKAROUND
	bool "FOC G4 ADC channel 0 unwanted conversion workaround"
	default n
	---help---
		Some STM32G4 family chips have an issue that causes unwanted ADC channel 0
		conversion when a regular conversion is interrupted by an injected conversion.
		This FOC implementation uses injected conversion to sample phase currents
		and allows user to use regular conversion as an auxiliary analog conversion.
		In this case, there is a certain probability that regular conversion will be
		interrupted by an injected conversion that will lead to an incorrect reading
		of phase currents.

		This workaround inserts a dummy conversion at the beginning of the injected
		sequence. For more details look at the chip errata documents.

endif #STM32_FOC
