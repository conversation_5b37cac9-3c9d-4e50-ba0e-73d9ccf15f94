############################################################################
# arch/arm/src/stm32/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

include armv7-m/Make.defs

CHIP_CSRCS  = stm32_allocateheap.c stm32_start.c stm32_rcc.c stm32_lse.c
CHIP_CSRCS += stm32_lsi.c stm32_gpio.c stm32_exti_gpio.c stm32_flash.c
CHIP_CSRCS += stm32_irq.c stm32_lowputc.c
CHIP_CSRCS += stm32_serial.c stm32_spi.c stm32_i2s.c stm32_sdio.c stm32_tim.c
CHIP_CSRCS += stm32_waste.c stm32_ccm.c stm32_uid.c stm32_capture.c
CHIP_CSRCS += stm32_dfumode.c

ifeq ($(CONFIG_STM32_DMA),y)
CHIP_CSRCS += stm32_dma.c
endif

ifeq ($(CONFIG_TIMER),y)
CHIP_CSRCS += stm32_tim_lowerhalf.c
endif

ifdef CONFIG_STM32_TICKLESS_TIMER
CHIP_CSRCS += stm32_tickless.c
else
CHIP_CSRCS += stm32_timerisr.c
endif

ifeq ($(CONFIG_STM32_ONESHOT),y)
CHIP_CSRCS += stm32_oneshot.c stm32_oneshot_lowerhalf.c
endif

ifeq ($(CONFIG_STM32_FREERUN),y)
CHIP_CSRCS += stm32_freerun.c
endif

ifeq ($(CONFIG_BUILD_PROTECTED),y)
CHIP_CSRCS += stm32_userspace.c stm32_mpuinit.c
endif

ifeq ($(CONFIG_STM32_HAVE_IP_I2C_V1),y)
ifeq ($(CONFIG_STM32_I2C_ALT),y)
CHIP_CSRCS += stm32_i2c_alt.c
else ifeq ($(CONFIG_STM32_STM32F4XXX),y)
CHIP_CSRCS += stm32f40xxx_i2c.c
else
CHIP_CSRCS += stm32_i2c.c
endif
else ifeq ($(CONFIG_STM32_HAVE_IP_I2C_V2),y)
CHIP_CSRCS += stm32_i2c_v2.c
endif

ifeq ($(CONFIG_USBDEV),y)
ifeq ($(CONFIG_STM32_USB),y)
CHIP_CSRCS += stm32_usbdev.c
endif
ifeq ($(CONFIG_STM32_USBFS),y)
CHIP_CSRCS += stm32_usbfs.c
endif
ifeq ($(CONFIG_STM32_OTGFS),y)
CHIP_CSRCS += stm32_otgfsdev.c
endif
ifeq ($(CONFIG_STM32_OTGHS),y)
CHIP_CSRCS += stm32_otghsdev.c
endif
endif

ifeq ($(CONFIG_STM32_USBHOST),y)
ifeq ($(CONFIG_STM32_OTGFS),y)
CHIP_CSRCS += stm32_otgfshost.c
endif
ifeq ($(CONFIG_STM32_OTGHS),y)
CHIP_CSRCS += stm32_otghshost.c
endif
ifeq ($(CONFIG_USBHOST_TRACE),y)
CHIP_CSRCS += stm32_usbhost.c
else
ifeq ($(CONFIG_DEBUG_USB),y)
CHIP_CSRCS += stm32_usbhost.c
endif
endif
endif

ifneq ($(CONFIG_ARCH_IDLE_CUSTOM),y)
CHIP_CSRCS += stm32_idle.c
endif

CHIP_CSRCS += stm32_pmstop.c stm32_pmstandby.c stm32_pmsleep.c

ifneq ($(CONFIG_ARCH_CUSTOM_PMINIT),y)
CHIP_CSRCS += stm32_pminitialize.c
endif

ifeq ($(CONFIG_STM32_ETHMAC),y)
CHIP_CSRCS += stm32_eth.c
endif

ifeq ($(CONFIG_STM32_PWR),y)
CHIP_CSRCS += stm32_pwr.c stm32_exti_pwr.c
endif

ifeq ($(CONFIG_STM32_RTC),y)
CHIP_CSRCS += stm32_rtc.c
ifeq ($(CONFIG_RTC_ALARM),y)
CHIP_CSRCS += stm32_exti_alarm.c
endif
ifeq ($(CONFIG_RTC_PERIODIC),y)
CHIP_CSRCS += stm32_exti_wakeup.c
endif
ifeq ($(CONFIG_RTC_DRIVER),y)
CHIP_CSRCS += stm32_rtc_lowerhalf.c
endif
endif

ifeq ($(CONFIG_STM32_ADC),y)
CHIP_CSRCS += stm32_adc.c
endif

ifeq ($(CONFIG_STM32_SDADC),y)
CHIP_CSRCS += stm32_sdadc.c
endif

ifeq ($(CONFIG_STM32_DAC),y)
CHIP_CSRCS += stm32_dac.c
endif

ifeq ($(CONFIG_STM32_COMP),y)
CHIP_CSRCS += stm32_comp.c
endif

ifeq ($(CONFIG_STM32_OPAMP),y)
CHIP_CSRCS += stm32_opamp.c
endif

ifeq ($(CONFIG_STM32_HRTIM),y)
CHIP_CSRCS += stm32_hrtim.c
endif

ifeq ($(CONFIG_STM32_1WIREDRIVER),y)
CHIP_CSRCS += stm32_1wire.c
endif

ifeq ($(CONFIG_STM32_HCIUART),y)
CHIP_CSRCS += stm32_hciuart.c
endif

ifeq ($(CONFIG_STM32_RNG),y)
CHIP_CSRCS += stm32_rng.c
endif

ifeq ($(CONFIG_STM32_LTDC),y)
CHIP_CSRCS += stm32_ltdc.c
endif

ifeq ($(CONFIG_STM32_DMA2D),y)
CHIP_CSRCS += stm32_dma2d.c
endif

ifeq ($(CONFIG_STM32_PWM),y)
CHIP_CSRCS += stm32_pwm.c
endif

ifeq ($(CONFIG_STM32_CAP),y)
CHIP_CSRCS += stm32_capture_lowerhalf.c
endif

ifeq ($(CONFIG_SENSORS_QENCODER),y)
CHIP_CSRCS += stm32_qencoder.c
endif

ifeq ($(CONFIG_SENSORS_HALL3PHASE),y)
CHIP_CSRCS += stm32_hall3ph.c
endif

ifeq ($(CONFIG_STM32_CAN),y)
ifeq ($(CONFIG_STM32_CAN_CHARDRIVER),y)
CHIP_CSRCS += stm32_can.c
endif
ifeq ($(CONFIG_STM32_CAN_SOCKET),y)
CHIP_CSRCS += stm32_can_sock.c
endif
endif

ifeq ($(CONFIG_STM32_FDCAN),y)
ifeq ($(CONFIG_STM32_FDCAN_CHARDRIVER),y)
CHIP_CSRCS += stm32_fdcan.c
endif
ifeq ($(CONFIG_STM32_FDCAN_SOCKET),y)
CHIP_CSRCS += stm32_fdcan_sock.c
endif
endif

ifeq ($(CONFIG_STM32_IWDG),y)
CHIP_CSRCS += stm32_iwdg.c
endif

ifeq ($(CONFIG_STM32_WWDG),y)
CHIP_CSRCS += stm32_wwdg.c
endif

ifeq ($(CONFIG_DEBUG_FEATURES),y)
CHIP_CSRCS += stm32_dumpgpio.c
endif

ifeq ($(CONFIG_STM32_AES),y)
CHIP_CSRCS += stm32_aes.c
endif

ifeq ($(CONFIG_STM32_BBSRAM),y)
CHIP_CSRCS += stm32_bbsram.c
endif

ifeq ($(CONFIG_STM32_FMC),y)
CHIP_CSRCS += stm32_fmc.c
endif

ifeq ($(CONFIG_STM32_FSMC),y)
CHIP_CSRCS += stm32_fsmc.c
endif

ifeq ($(CONFIG_STM32_FOC),y)
CHIP_CSRCS += stm32_foc.c
endif

ifeq ($(CONFIG_STM32_CORDIC),y)
CHIP_CSRCS += stm32_cordic.c
endif
