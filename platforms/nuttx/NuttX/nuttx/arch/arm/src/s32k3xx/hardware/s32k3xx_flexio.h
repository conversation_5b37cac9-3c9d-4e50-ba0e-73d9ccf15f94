/****************************************************************************
 * arch/arm/src/s32k3xx/hardware/s32k3xx_flexio.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/* Copyright 2022 NXP */

#ifndef __ARCH_ARM_SRC_S32K3XX_HARDWARE_S32K3XX_FLEXIO_H
#define __ARCH_ARM_SRC_S32K3XX_HARDWARE_S32K3XX_FLEXIO_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <hardware/s32k3xx_memorymap.h>

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* FlexIO Register Offsets **************************************************/

#define S32K3XX_FLEXIO_VERID_OFFSET          (0x0000) /* Version ID Register (VERID) */
#define S32K3XX_FLEXIO_PARAM_OFFSET          (0x0004) /* Parameter Register (PARAM) */
#define S32K3XX_FLEXIO_CTRL_OFFSET           (0x0008) /* FlexIO Control Register (CTRL) */
#define S32K3XX_FLEXIO_PIN_OFFSET            (0x000c) /* Pin State Register (PIN) */
#define S32K3XX_FLEXIO_SHIFTSTAT_OFFSET      (0x0010) /* Shifter Status Register (SHIFTSTAT) */
#define S32K3XX_FLEXIO_SHIFTERR_OFFSET       (0x0014) /* Shifter Error Register (SHIFTERR) */
#define S32K3XX_FLEXIO_TIMSTAT_OFFSET        (0x0018) /* Timer Status Register (TIMSTAT) */
#define S32K3XX_FLEXIO_SHIFTSIEN_OFFSET      (0x0020) /* Shifter Status Interrupt Enable (SHIFTSIEN) */
#define S32K3XX_FLEXIO_SHIFTEIEN_OFFSET      (0x0024) /* Shifter Error Interrupt Enable (SHIFTEIEN) */
#define S32K3XX_FLEXIO_TIMIEN_OFFSET         (0x0028) /* Timer Interrupt Enable Register (TIMIEN) */
#define S32K3XX_FLEXIO_SHIFTSDEN_OFFSET      (0x0030) /* Shifter Status DMA Enable (SHIFTSDEN) */
#define S32K3XX_FLEXIO_TIMERSDEN_OFFSET      (0x0038) /* Timer Status DMA Enable (TIMERSDEN) */
#define S32K3XX_FLEXIO_SHIFTSTATE_OFFSET     (0x0040) /* Shifter State Register (SHIFTSTATE) */
#define S32K3XX_FLEXIO_TRGSTAT_OFFSET        (0x0048) /* Trigger Status Register (TRGSTAT) */
#define S32K3XX_FLEXIO_TRIGIEN_OFFSET        (0x004c) /* External Trigger Interrupt Enable Register (TRIGIEN) */
#define S32K3XX_FLEXIO_PINSTAT_OFFSET        (0x0050) /* Pin Status Register (PINSTAT) */
#define S32K3XX_FLEXIO_PINIEN_OFFSET         (0x0054) /* Pin Interrupt Enable Register (PINIEN) */
#define S32K3XX_FLEXIO_PINREN_OFFSET         (0x0058) /* Pin Rising Edge Enable Register (PINREN) */
#define S32K3XX_FLEXIO_PINFEN_OFFSET         (0x005c) /* Pin Falling Edge Enable Register (PINFEN) */
#define S32K3XX_FLEXIO_PINOUTD_OFFSET        (0x0060) /* Pin Output Data Register (PINOUTD) */
#define S32K3XX_FLEXIO_PINOUTE_OFFSET        (0x0064) /* Pin Output Enable Register (PINOUTE) */
#define S32K3XX_FLEXIO_PINOUTDIS_OFFSET      (0x0068) /* Pin Output Disable Register (PINOUTDIS) */
#define S32K3XX_FLEXIO_PINOUTCLR_OFFSET      (0x006c) /* Pin Output Clear Register (PINOUTCLR) */
#define S32K3XX_FLEXIO_PINOUTSET_OFFSET      (0x0070) /* Pin Output Set Register (PINOUTSET) */
#define S32K3XX_FLEXIO_PINOUTTOG_OFFSET      (0x0074) /* Pin Output Toggle Register (PINOUTTOG) */
#define S32K3XX_FLEXIO_SHIFTCTL0_OFFSET      (0x0080) /* Shifter Control 0 Register (SHIFTCTL0) */
#define S32K3XX_FLEXIO_SHIFTCTL1_OFFSET      (0x0084) /* Shifter Control 1 Register (SHIFTCTL1) */
#define S32K3XX_FLEXIO_SHIFTCTL2_OFFSET      (0x0088) /* Shifter Control 2 Register (SHIFTCTL2) */
#define S32K3XX_FLEXIO_SHIFTCTL3_OFFSET      (0x008c) /* Shifter Control 3 Register (SHIFTCTL3) */
#define S32K3XX_FLEXIO_SHIFTCTL4_OFFSET      (0x0090) /* Shifter Control 4 Register (SHIFTCTL4) */
#define S32K3XX_FLEXIO_SHIFTCTL5_OFFSET      (0x0094) /* Shifter Control 5 Register (SHIFTCTL5) */
#define S32K3XX_FLEXIO_SHIFTCTL6_OFFSET      (0x0098) /* Shifter Control 6 Register (SHIFTCTL6) */
#define S32K3XX_FLEXIO_SHIFTCTL7_OFFSET      (0x009c) /* Shifter Control 7 Register (SHIFTCTL7) */
#define S32K3XX_FLEXIO_SHIFTCFG0_OFFSET      (0x0100) /* Shifter Configuration 0 Register (SHIFTCFG0) */
#define S32K3XX_FLEXIO_SHIFTCFG1_OFFSET      (0x0104) /* Shifter Configuration 1 Register (SHIFTCFG1) */
#define S32K3XX_FLEXIO_SHIFTCFG2_OFFSET      (0x0108) /* Shifter Configuration 2 Register (SHIFTCFG2) */
#define S32K3XX_FLEXIO_SHIFTCFG3_OFFSET      (0x010c) /* Shifter Configuration 3 Register (SHIFTCFG3) */
#define S32K3XX_FLEXIO_SHIFTCFG4_OFFSET      (0x0110) /* Shifter Configuration 4 Register (SHIFTCFG4) */
#define S32K3XX_FLEXIO_SHIFTCFG5_OFFSET      (0x0114) /* Shifter Configuration 5 Register (SHIFTCFG5) */
#define S32K3XX_FLEXIO_SHIFTCFG6_OFFSET      (0x0118) /* Shifter Configuration 6 Register (SHIFTCFG6) */
#define S32K3XX_FLEXIO_SHIFTCFG7_OFFSET      (0x011c) /* Shifter Configuration 7 Register (SHIFTCFG7) */
#define S32K3XX_FLEXIO_SHIFTBUF0_OFFSET      (0x0200) /* Shifter Buffer 0 Register (SHIFTBUF0) */
#define S32K3XX_FLEXIO_SHIFTBUF1_OFFSET      (0x0204) /* Shifter Buffer 1 Register (SHIFTBUF1) */
#define S32K3XX_FLEXIO_SHIFTBUF2_OFFSET      (0x0208) /* Shifter Buffer 2 Register (SHIFTBUF2) */
#define S32K3XX_FLEXIO_SHIFTBUF3_OFFSET      (0x020c) /* Shifter Buffer 3 Register (SHIFTBUF3) */
#define S32K3XX_FLEXIO_SHIFTBUF4_OFFSET      (0x0210) /* Shifter Buffer 4 Register (SHIFTBUF4) */
#define S32K3XX_FLEXIO_SHIFTBUF5_OFFSET      (0x0214) /* Shifter Buffer 5 Register (SHIFTBUF5) */
#define S32K3XX_FLEXIO_SHIFTBUF6_OFFSET      (0x0218) /* Shifter Buffer 6 Register (SHIFTBUF6) */
#define S32K3XX_FLEXIO_SHIFTBUF7_OFFSET      (0x021c) /* Shifter Buffer 7 Register (SHIFTBUF7) */
#define S32K3XX_FLEXIO_SHIFTBUFBIS0_OFFSET   (0x0280) /* Shifter Buffer 0 Bit Swapped Register (SHIFTBUFBIS0) */
#define S32K3XX_FLEXIO_SHIFTBUFBIS1_OFFSET   (0x0284) /* Shifter Buffer 1 Bit Swapped Register (SHIFTBUFBIS1) */
#define S32K3XX_FLEXIO_SHIFTBUFBIS2_OFFSET   (0x0288) /* Shifter Buffer 2 Bit Swapped Register (SHIFTBUFBIS2) */
#define S32K3XX_FLEXIO_SHIFTBUFBIS3_OFFSET   (0x028c) /* Shifter Buffer 3 Bit Swapped Register (SHIFTBUFBIS3) */
#define S32K3XX_FLEXIO_SHIFTBUFBIS4_OFFSET   (0x0290) /* Shifter Buffer 4 Bit Swapped Register (SHIFTBUFBIS4) */
#define S32K3XX_FLEXIO_SHIFTBUFBIS5_OFFSET   (0x0294) /* Shifter Buffer 5 Bit Swapped Register (SHIFTBUFBIS5) */
#define S32K3XX_FLEXIO_SHIFTBUFBIS6_OFFSET   (0x0298) /* Shifter Buffer 6 Bit Swapped Register (SHIFTBUFBIS6) */
#define S32K3XX_FLEXIO_SHIFTBUFBIS7_OFFSET   (0x029c) /* Shifter Buffer 7 Bit Swapped Register (SHIFTBUFBIS7) */
#define S32K3XX_FLEXIO_SHIFTBUFBYS0_OFFSET   (0x0300) /* Shifter Buffer 0 Byte Swapped Register (SHIFTBUFBYS0) */
#define S32K3XX_FLEXIO_SHIFTBUFBYS1_OFFSET   (0x0304) /* Shifter Buffer 1 Byte Swapped Register (SHIFTBUFBYS1) */
#define S32K3XX_FLEXIO_SHIFTBUFBYS2_OFFSET   (0x0308) /* Shifter Buffer 2 Byte Swapped Register (SHIFTBUFBYS2) */
#define S32K3XX_FLEXIO_SHIFTBUFBYS3_OFFSET   (0x030c) /* Shifter Buffer 3 Byte Swapped Register (SHIFTBUFBYS3) */
#define S32K3XX_FLEXIO_SHIFTBUFBYS4_OFFSET   (0x0310) /* Shifter Buffer 4 Byte Swapped Register (SHIFTBUFBYS4) */
#define S32K3XX_FLEXIO_SHIFTBUFBYS5_OFFSET   (0x0314) /* Shifter Buffer 5 Byte Swapped Register (SHIFTBUFBYS5) */
#define S32K3XX_FLEXIO_SHIFTBUFBYS6_OFFSET   (0x0318) /* Shifter Buffer 6 Byte Swapped Register (SHIFTBUFBYS6) */
#define S32K3XX_FLEXIO_SHIFTBUFBYS7_OFFSET   (0x031c) /* Shifter Buffer 7 Byte Swapped Register (SHIFTBUFBYS7) */
#define S32K3XX_FLEXIO_SHIFTBUFBBS0_OFFSET   (0x0380) /* Shifter Buffer 0 Bit Byte Swapped Register (SHIFTBUFBBS0) */
#define S32K3XX_FLEXIO_SHIFTBUFBBS1_OFFSET   (0x0384) /* Shifter Buffer 1 Bit Byte Swapped Register (SHIFTBUFBBS1) */
#define S32K3XX_FLEXIO_SHIFTBUFBBS2_OFFSET   (0x0388) /* Shifter Buffer 2 Bit Byte Swapped Register (SHIFTBUFBBS2) */
#define S32K3XX_FLEXIO_SHIFTBUFBBS3_OFFSET   (0x038c) /* Shifter Buffer 3 Bit Byte Swapped Register (SHIFTBUFBBS3) */
#define S32K3XX_FLEXIO_SHIFTBUFBBS4_OFFSET   (0x0390) /* Shifter Buffer 4 Bit Byte Swapped Register (SHIFTBUFBBS4) */
#define S32K3XX_FLEXIO_SHIFTBUFBBS5_OFFSET   (0x0394) /* Shifter Buffer 5 Bit Byte Swapped Register (SHIFTBUFBBS5) */
#define S32K3XX_FLEXIO_SHIFTBUFBBS6_OFFSET   (0x0398) /* Shifter Buffer 6 Bit Byte Swapped Register (SHIFTBUFBBS6) */
#define S32K3XX_FLEXIO_SHIFTBUFBBS7_OFFSET   (0x039c) /* Shifter Buffer 7 Bit Byte Swapped Register (SHIFTBUFBBS7) */
#define S32K3XX_FLEXIO_TIMCTL0_OFFSET        (0x0400) /* Timer Control 0 Register (TIMCTL0) */
#define S32K3XX_FLEXIO_TIMCTL1_OFFSET        (0x0404) /* Timer Control 1 Register (TIMCTL1) */
#define S32K3XX_FLEXIO_TIMCTL2_OFFSET        (0x0408) /* Timer Control 2 Register (TIMCTL2) */
#define S32K3XX_FLEXIO_TIMCTL3_OFFSET        (0x040c) /* Timer Control 3 Register (TIMCTL3) */
#define S32K3XX_FLEXIO_TIMCTL4_OFFSET        (0x0410) /* Timer Control 4 Register (TIMCTL4) */
#define S32K3XX_FLEXIO_TIMCTL5_OFFSET        (0x0414) /* Timer Control 5 Register (TIMCTL5) */
#define S32K3XX_FLEXIO_TIMCTL6_OFFSET        (0x0418) /* Timer Control 6 Register (TIMCTL6) */
#define S32K3XX_FLEXIO_TIMCTL7_OFFSET        (0x041c) /* Timer Control 7 Register (TIMCTL7) */
#define S32K3XX_FLEXIO_TIMCFG0_OFFSET        (0x0480) /* Timer Configuration 0 Register (TIMCFG0) */
#define S32K3XX_FLEXIO_TIMCFG1_OFFSET        (0x0484) /* Timer Configuration 1 Register (TIMCFG1) */
#define S32K3XX_FLEXIO_TIMCFG2_OFFSET        (0x0488) /* Timer Configuration 2 Register (TIMCFG2) */
#define S32K3XX_FLEXIO_TIMCFG3_OFFSET        (0x048c) /* Timer Configuration 3 Register (TIMCFG3) */
#define S32K3XX_FLEXIO_TIMCFG4_OFFSET        (0x0490) /* Timer Configuration 4 Register (TIMCFG4) */
#define S32K3XX_FLEXIO_TIMCFG5_OFFSET        (0x0494) /* Timer Configuration 5 Register (TIMCFG5) */
#define S32K3XX_FLEXIO_TIMCFG6_OFFSET        (0x0498) /* Timer Configuration 6 Register (TIMCFG6) */
#define S32K3XX_FLEXIO_TIMCFG7_OFFSET        (0x049c) /* Timer Configuration 7 Register (TIMCFG7) */
#define S32K3XX_FLEXIO_TIMCMP0_OFFSET        (0x0500) /* Timer Compare 0 Register (TIMCMP0) */
#define S32K3XX_FLEXIO_TIMCMP1_OFFSET        (0x0504) /* Timer Compare 1 Register (TIMCMP1) */
#define S32K3XX_FLEXIO_TIMCMP2_OFFSET        (0x0508) /* Timer Compare 2 Register (TIMCMP2) */
#define S32K3XX_FLEXIO_TIMCMP3_OFFSET        (0x050c) /* Timer Compare 3 Register (TIMCMP3) */
#define S32K3XX_FLEXIO_TIMCMP4_OFFSET        (0x0510) /* Timer Compare 4 Register (TIMCMP4) */
#define S32K3XX_FLEXIO_TIMCMP5_OFFSET        (0x0514) /* Timer Compare 5 Register (TIMCMP5) */
#define S32K3XX_FLEXIO_TIMCMP6_OFFSET        (0x0518) /* Timer Compare 6 Register (TIMCMP6) */
#define S32K3XX_FLEXIO_TIMCMP7_OFFSET        (0x051c) /* Timer Compare 7 Register (TIMCMP7) */
#define S32K3XX_FLEXIO_SHIFTBUFNBS0_OFFSET   (0x0680) /* Shifter Buffer 0 Nibble Byte Swapped Register (SHIFTBUFNBS0) */
#define S32K3XX_FLEXIO_SHIFTBUFNBS1_OFFSET   (0x0684) /* Shifter Buffer 1 Nibble Byte Swapped Register (SHIFTBUFNBS1) */
#define S32K3XX_FLEXIO_SHIFTBUFNBS2_OFFSET   (0x0688) /* Shifter Buffer 2 Nibble Byte Swapped Register (SHIFTBUFNBS2) */
#define S32K3XX_FLEXIO_SHIFTBUFNBS3_OFFSET   (0x068c) /* Shifter Buffer 3 Nibble Byte Swapped Register (SHIFTBUFNBS3) */
#define S32K3XX_FLEXIO_SHIFTBUFNBS4_OFFSET   (0x0690) /* Shifter Buffer 4 Nibble Byte Swapped Register (SHIFTBUFNBS4) */
#define S32K3XX_FLEXIO_SHIFTBUFNBS5_OFFSET   (0x0694) /* Shifter Buffer 5 Nibble Byte Swapped Register (SHIFTBUFNBS5) */
#define S32K3XX_FLEXIO_SHIFTBUFNBS6_OFFSET   (0x0698) /* Shifter Buffer 6 Nibble Byte Swapped Register (SHIFTBUFNBS6) */
#define S32K3XX_FLEXIO_SHIFTBUFNBS7_OFFSET   (0x069c) /* Shifter Buffer 7 Nibble Byte Swapped Register (SHIFTBUFNBS7) */
#define S32K3XX_FLEXIO_SHIFTBUFHWS0_OFFSET   (0x0700) /* Shifter Buffer 0 Half Word Swapped Register (SHIFTBUFHWS0) */
#define S32K3XX_FLEXIO_SHIFTBUFHWS1_OFFSET   (0x0704) /* Shifter Buffer 1 Half Word Swapped Register (SHIFTBUFHWS1) */
#define S32K3XX_FLEXIO_SHIFTBUFHWS2_OFFSET   (0x0708) /* Shifter Buffer 2 Half Word Swapped Register (SHIFTBUFHWS2) */
#define S32K3XX_FLEXIO_SHIFTBUFHWS3_OFFSET   (0x071c) /* Shifter Buffer 3 Half Word Swapped Register (SHIFTBUFHWS3) */
#define S32K3XX_FLEXIO_SHIFTBUFHWS4_OFFSET   (0x0710) /* Shifter Buffer 4 Half Word Swapped Register (SHIFTBUFHWS4) */
#define S32K3XX_FLEXIO_SHIFTBUFHWS5_OFFSET   (0x0714) /* Shifter Buffer 5 Half Word Swapped Register (SHIFTBUFHWS5) */
#define S32K3XX_FLEXIO_SHIFTBUFHWS6_OFFSET   (0x0718) /* Shifter Buffer 6 Half Word Swapped Register (SHIFTBUFHWS6) */
#define S32K3XX_FLEXIO_SHIFTBUFHWS7_OFFSET   (0x071c) /* Shifter Buffer 7 Half Word Swapped Register (SHIFTBUFHWS7) */
#define S32K3XX_FLEXIO_SHIFTBUFNIS0_OFFSET   (0x0780) /* Shifter Buffer 0 Nibble Swapped Register (SHIFTBUFNIS0) */
#define S32K3XX_FLEXIO_SHIFTBUFNIS1_OFFSET   (0x0784) /* Shifter Buffer 1 Nibble Swapped Register (SHIFTBUFNIS1) */
#define S32K3XX_FLEXIO_SHIFTBUFNIS2_OFFSET   (0x0788) /* Shifter Buffer 2 Nibble Swapped Register (SHIFTBUFNIS2) */
#define S32K3XX_FLEXIO_SHIFTBUFNIS3_OFFSET   (0x078c) /* Shifter Buffer 3 Nibble Swapped Register (SHIFTBUFNIS3) */
#define S32K3XX_FLEXIO_SHIFTBUFNIS4_OFFSET   (0x0790) /* Shifter Buffer 4 Nibble Swapped Register (SHIFTBUFNIS4) */
#define S32K3XX_FLEXIO_SHIFTBUFNIS5_OFFSET   (0x0794) /* Shifter Buffer 5 Nibble Swapped Register (SHIFTBUFNIS5) */
#define S32K3XX_FLEXIO_SHIFTBUFNIS6_OFFSET   (0x0798) /* Shifter Buffer 6 Nibble Swapped Register (SHIFTBUFNIS6) */
#define S32K3XX_FLEXIO_SHIFTBUFNIS7_OFFSET   (0x079c) /* Shifter Buffer 7 Nibble Swapped Register (SHIFTBUFNIS7) */
#define S32K3XX_FLEXIO_SHIFTBUFOES0_OFFSET   (0x0800) /* Shifter Buffer 0 Odd Even Swapped Register (SHIFTBUFOES0) */
#define S32K3XX_FLEXIO_SHIFTBUFOES1_OFFSET   (0x0804) /* Shifter Buffer 1 Odd Even Swapped Register (SHIFTBUFOES1) */
#define S32K3XX_FLEXIO_SHIFTBUFOES2_OFFSET   (0x0808) /* Shifter Buffer 2 Odd Even Swapped Register (SHIFTBUFOES2) */
#define S32K3XX_FLEXIO_SHIFTBUFOES3_OFFSET   (0x080c) /* Shifter Buffer 3 Odd Even Swapped Register (SHIFTBUFOES3) */
#define S32K3XX_FLEXIO_SHIFTBUFOES4_OFFSET   (0x0810) /* Shifter Buffer 4 Odd Even Swapped Register (SHIFTBUFOES4) */
#define S32K3XX_FLEXIO_SHIFTBUFOES5_OFFSET   (0x0814) /* Shifter Buffer 5 Odd Even Swapped Register (SHIFTBUFOES5) */
#define S32K3XX_FLEXIO_SHIFTBUFOES6_OFFSET   (0x0818) /* Shifter Buffer 6 Odd Even Swapped Register (SHIFTBUFOES6) */
#define S32K3XX_FLEXIO_SHIFTBUFOES7_OFFSET   (0x081c) /* Shifter Buffer 7 Odd Even Swapped Register (SHIFTBUFOES7) */
#define S32K3XX_FLEXIO_SHIFTBUFEOS0_OFFSET   (0x0880) /* Shifter Buffer 0 Even Odd Swapped Register (SHIFTBUFEOS0) */
#define S32K3XX_FLEXIO_SHIFTBUFEOS1_OFFSET   (0x0884) /* Shifter Buffer 1 Even Odd Swapped Register (SHIFTBUFEOS1) */
#define S32K3XX_FLEXIO_SHIFTBUFEOS2_OFFSET   (0x0888) /* Shifter Buffer 2 Even Odd Swapped Register (SHIFTBUFEOS2) */
#define S32K3XX_FLEXIO_SHIFTBUFEOS3_OFFSET   (0x088c) /* Shifter Buffer 3 Even Odd Swapped Register (SHIFTBUFEOS3) */
#define S32K3XX_FLEXIO_SHIFTBUFEOS4_OFFSET   (0x0890) /* Shifter Buffer 4 Even Odd Swapped Register (SHIFTBUFEOS4) */
#define S32K3XX_FLEXIO_SHIFTBUFEOS5_OFFSET   (0x0894) /* Shifter Buffer 5 Even Odd Swapped Register (SHIFTBUFEOS5) */
#define S32K3XX_FLEXIO_SHIFTBUFEOS6_OFFSET   (0x0898) /* Shifter Buffer 6 Even Odd Swapped Register (SHIFTBUFEOS6) */
#define S32K3XX_FLEXIO_SHIFTBUFEOS7_OFFSET   (0x089c) /* Shifter Buffer 7 Even Odd Swapped Register (SHIFTBUFEOS7) */
#define S32K3XX_FLEXIO_SHIFTBUFHBS0_OFFSET   (0x0900) /* Shifter Buffer 0 Halfword Byte Swapped Register (SHIFTBUFHBS0) */
#define S32K3XX_FLEXIO_SHIFTBUFHBS1_OFFSET   (0x0904) /* Shifter Buffer 1 Halfword Byte Swapped Register (SHIFTBUFHBS1) */
#define S32K3XX_FLEXIO_SHIFTBUFHBS2_OFFSET   (0x0908) /* Shifter Buffer 2 Halfword Byte Swapped Register (SHIFTBUFHBS2) */
#define S32K3XX_FLEXIO_SHIFTBUFHBS3_OFFSET   (0x090c) /* Shifter Buffer 3 Halfword Byte Swapped Register (SHIFTBUFHBS3) */
#define S32K3XX_FLEXIO_SHIFTBUFHBS4_OFFSET   (0x0910) /* Shifter Buffer 4 Halfword Byte Swapped Register (SHIFTBUFHBS4) */
#define S32K3XX_FLEXIO_SHIFTBUFHBS5_OFFSET   (0x0914) /* Shifter Buffer 5 Halfword Byte Swapped Register (SHIFTBUFHBS5) */
#define S32K3XX_FLEXIO_SHIFTBUFHBS6_OFFSET   (0x0918) /* Shifter Buffer 6 Halfword Byte Swapped Register (SHIFTBUFHBS6) */
#define S32K3XX_FLEXIO_SHIFTBUFHBS7_OFFSET   (0x091c) /* Shifter Buffer 7 Halfword Byte Swapped Register (SHIFTBUFHBS7) */

#define S32K3XX_FLEXIO_SHIFTCTL_OFFSET(n)    (0x0080 + ((n) << 2)) /* Shifter Control n Register (SHIFTCTLn) */
#define S32K3XX_FLEXIO_SHIFTCFG_OFFSET(n)    (0x0100 + ((n) << 2)) /* Shifter Configuration n Register (SHIFTCFGn) */
#define S32K3XX_FLEXIO_SHIFTBUF_OFFSET(n)    (0x0200 + ((n) << 2)) /* Shifter Buffer n Register (SHIFTBUFn) */
#define S32K3XX_FLEXIO_SHIFTBUFBIS_OFFSET(n) (0x0280 + ((n) << 2)) /* Shifter Buffer n Bit Swapped Register (SHIFTBUFBISn) */
#define S32K3XX_FLEXIO_SHIFTBUFBYS_OFFSET(n) (0x0300 + ((n) << 2)) /* Shifter Buffer n Byte Swapped Register (SHIFTBUFBYSn) */
#define S32K3XX_FLEXIO_SHIFTBUFBBS_OFFSET(n) (0x0380 + ((n) << 2)) /* Shifter Buffer n Bit Byte Swapped Register (SHIFTBUFBBSn) */
#define S32K3XX_FLEXIO_TIMCTL_OFFSET(n)      (0x0400 + ((n) << 2)) /* Timer Control n Register (TIMCTLn) */
#define S32K3XX_FLEXIO_TIMCFG_OFFSET(n)      (0x0480 + ((n) << 2)) /* Timer Configuration n Register (TIMCFGn) */
#define S32K3XX_FLEXIO_TIMCMP_OFFSET(n)      (0x0500 + ((n) << 2)) /* Timer Compare n Register (TIMCMPn) */
#define S32K3XX_FLEXIO_SHIFTBUFNBS_OFFSET(n) (0x0680 + ((n) << 2)) /* Shifter Buffer n Nibble Byte Swapped Register (SHIFTBUFNBSn) */
#define S32K3XX_FLEXIO_SHIFTBUFHWS_OFFSET(n) (0x0700 + ((n) << 2)) /* Shifter Buffer n Half Word Swapped Register (SHIFTBUFHWSn) */
#define S32K3XX_FLEXIO_SHIFTBUFNIS_OFFSET(n) (0x0780 + ((n) << 2)) /* Shifter Buffer n Nibble Swapped Register (SHIFTBUFNISn) */
#define S32K3XX_FLEXIO_SHIFTBUFOES_OFFSET(n) (0x0800 + ((n) << 2)) /* Shifter Buffer n Odd Even Swapped Register (SHIFTBUFOESn) */
#define S32K3XX_FLEXIO_SHIFTBUFEOS_OFFSET(n) (0x0880 + ((n) << 2)) /* Shifter Buffer n Even Odd Swapped Register (SHIFTBUFEOSn) */
#define S32K3XX_FLEXIO_SHIFTBUFHBS_OFFSET(n) (0x0900 + ((n) << 2)) /* Shifter Buffer n Halfword Byte Swapped Register (SHIFTBUFHBSn) */

/* FlexIO Register Addresses ************************************************/

#define S32K3XX_FLEXIO_VERID                 (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_VERID_OFFSET)
#define S32K3XX_FLEXIO_PARAM                 (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_PARAM_OFFSET)
#define S32K3XX_FLEXIO_CTRL                  (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_CTRL_OFFSET)
#define S32K3XX_FLEXIO_PIN                   (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_PIN_OFFSET)
#define S32K3XX_FLEXIO_SHIFTSTAT             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTSTAT_OFFSET)
#define S32K3XX_FLEXIO_SHIFTERR              (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTERR_OFFSET)
#define S32K3XX_FLEXIO_TIMSTAT               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMSTAT_OFFSET)
#define S32K3XX_FLEXIO_SHIFTSIEN             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTSIEN_OFFSET)
#define S32K3XX_FLEXIO_SHIFTEIEN             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTEIEN_OFFSET)
#define S32K3XX_FLEXIO_TIMIEN                (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMIEN_OFFSET)
#define S32K3XX_FLEXIO_SHIFTSDEN             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTSDEN_OFFSET)
#define S32K3XX_FLEXIO_TIMERSDEN             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMERSDEN_OFFSET)
#define S32K3XX_FLEXIO_SHIFTSTATE            (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTSTATE_OFFSET)
#define S32K3XX_FLEXIO_TRGSTAT               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TRGSTAT_OFFSET)
#define S32K3XX_FLEXIO_TRIGIEN               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TRIGIEN_OFFSET)
#define S32K3XX_FLEXIO_PINSTAT               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_PINSTAT_OFFSET)
#define S32K3XX_FLEXIO_PINIEN                (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_PINIEN_OFFSET)
#define S32K3XX_FLEXIO_PINREN                (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_PINREN_OFFSET)
#define S32K3XX_FLEXIO_PINFEN                (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_PINFEN_OFFSET)
#define S32K3XX_FLEXIO_PINOUTD               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_PINOUTD_OFFSET)
#define S32K3XX_FLEXIO_PINOUTE               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_PINOUTE_OFFSET)
#define S32K3XX_FLEXIO_PINOUTDIS             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_PINOUTDIS_OFFSET)
#define S32K3XX_FLEXIO_PINOUTCLR             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_PINOUTCLR_OFFSET)
#define S32K3XX_FLEXIO_PINOUTSET             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_PINOUTSET_OFFSET)
#define S32K3XX_FLEXIO_PINOUTTOG             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_PINOUTTOG_OFFSET)
#define S32K3XX_FLEXIO_SHIFTCTL0             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTCTL0_OFFSET)
#define S32K3XX_FLEXIO_SHIFTCTL1             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTCTL1_OFFSET)
#define S32K3XX_FLEXIO_SHIFTCTL2             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTCTL2_OFFSET)
#define S32K3XX_FLEXIO_SHIFTCTL3             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTCTL3_OFFSET)
#define S32K3XX_FLEXIO_SHIFTCTL4             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTCTL4_OFFSET)
#define S32K3XX_FLEXIO_SHIFTCTL5             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTCTL5_OFFSET)
#define S32K3XX_FLEXIO_SHIFTCTL6             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTCTL6_OFFSET)
#define S32K3XX_FLEXIO_SHIFTCTL7             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTCTL7_OFFSET)
#define S32K3XX_FLEXIO_SHIFTCFG0             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTCFG0_OFFSET)
#define S32K3XX_FLEXIO_SHIFTCFG1             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTCFG1_OFFSET)
#define S32K3XX_FLEXIO_SHIFTCFG2             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTCFG2_OFFSET)
#define S32K3XX_FLEXIO_SHIFTCFG3             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTCFG3_OFFSET)
#define S32K3XX_FLEXIO_SHIFTCFG4             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTCFG4_OFFSET)
#define S32K3XX_FLEXIO_SHIFTCFG5             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTCFG5_OFFSET)
#define S32K3XX_FLEXIO_SHIFTCFG6             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTCFG6_OFFSET)
#define S32K3XX_FLEXIO_SHIFTCFG7             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTCFG7_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUF0             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUF0_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUF1             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUF1_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUF2             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUF2_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUF3             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUF3_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUF4             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUF4_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUF5             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUF5_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUF6             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUF6_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUF7             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUF7_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBIS0          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBIS0_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBIS1          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBIS1_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBIS2          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBIS2_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBIS3          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBIS3_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBIS4          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBIS4_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBIS5          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBIS5_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBIS6          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBIS6_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBIS7          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBIS7_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBYS0          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBYS0_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBYS1          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBYS1_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBYS2          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBYS2_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBYS3          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBYS3_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBYS4          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBYS4_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBYS5          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBYS5_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBYS6          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBYS6_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBYS7          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBYS7_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBBS0          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBBS0_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBBS1          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBBS1_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBBS2          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBBS2_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBBS3          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBBS3_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBBS4          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBBS4_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBBS5          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBBS5_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBBS6          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBBS6_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFBBS7          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBBS7_OFFSET)
#define S32K3XX_FLEXIO_TIMCTL0               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCTL0_OFFSET)
#define S32K3XX_FLEXIO_TIMCTL1               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCTL1_OFFSET)
#define S32K3XX_FLEXIO_TIMCTL2               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCTL2_OFFSET)
#define S32K3XX_FLEXIO_TIMCTL3               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCTL3_OFFSET)
#define S32K3XX_FLEXIO_TIMCTL4               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCTL4_OFFSET)
#define S32K3XX_FLEXIO_TIMCTL5               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCTL5_OFFSET)
#define S32K3XX_FLEXIO_TIMCTL6               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCTL6_OFFSET)
#define S32K3XX_FLEXIO_TIMCTL7               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCTL7_OFFSET)
#define S32K3XX_FLEXIO_TIMCFG0               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCFG0_OFFSET)
#define S32K3XX_FLEXIO_TIMCFG1               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCFG1_OFFSET)
#define S32K3XX_FLEXIO_TIMCFG2               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCFG2_OFFSET)
#define S32K3XX_FLEXIO_TIMCFG3               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCFG3_OFFSET)
#define S32K3XX_FLEXIO_TIMCFG4               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCFG4_OFFSET)
#define S32K3XX_FLEXIO_TIMCFG5               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCFG5_OFFSET)
#define S32K3XX_FLEXIO_TIMCFG6               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCFG6_OFFSET)
#define S32K3XX_FLEXIO_TIMCFG7               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCFG7_OFFSET)
#define S32K3XX_FLEXIO_TIMCMP0               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCMP0_OFFSET)
#define S32K3XX_FLEXIO_TIMCMP1               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCMP1_OFFSET)
#define S32K3XX_FLEXIO_TIMCMP2               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCMP2_OFFSET)
#define S32K3XX_FLEXIO_TIMCMP3               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCMP3_OFFSET)
#define S32K3XX_FLEXIO_TIMCMP4               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCMP4_OFFSET)
#define S32K3XX_FLEXIO_TIMCMP5               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCMP5_OFFSET)
#define S32K3XX_FLEXIO_TIMCMP6               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCMP6_OFFSET)
#define S32K3XX_FLEXIO_TIMCMP7               (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCMP7_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFNBS0          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFNBS0_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFNBS1          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFNBS1_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFNBS2          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFNBS2_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFNBS3          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFNBS3_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFNBS4          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFNBS4_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFNBS5          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFNBS5_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFNBS6          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFNBS6_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFNBS7          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFNBS7_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFHWS0          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFHWS0_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFHWS1          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFHWS1_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFHWS2          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFHWS2_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFHWS3          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFHWS3_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFHWS4          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFHWS4_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFHWS5          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFHWS5_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFHWS6          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFHWS6_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFHWS7          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFHWS7_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFNIS0          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFNIS0_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFNIS1          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFNIS1_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFNIS2          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFNIS2_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFNIS3          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFNIS3_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFNIS4          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFNIS4_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFNIS5          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFNIS5_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFNIS6          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFNIS6_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFNIS7          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFNIS7_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFOES0          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFOES0_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFOES1          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFOES1_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFOES2          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFOES2_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFOES3          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFOES3_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFOES4          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFOES4_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFOES5          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFOES5_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFOES6          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFOES6_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFOES7          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFOES7_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFEOS0          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFEOS0_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFEOS1          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFEOS1_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFEOS2          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFEOS2_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFEOS3          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFEOS3_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFEOS4          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFEOS4_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFEOS5          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFEOS5_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFEOS6          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFEOS6_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFEOS7          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFEOS7_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFHBS0          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFHBS0_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFHBS1          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFHBS1_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFHBS2          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFHBS2_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFHBS3          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFHBS3_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFHBS4          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFHBS4_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFHBS5          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFHBS5_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFHBS6          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFHBS6_OFFSET)
#define S32K3XX_FLEXIO_SHIFTBUFHBS7          (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFHBS7_OFFSET)

#define S32K3XX_FLEXIO_SHIFTCTL(n)           (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTCTL_OFFSET(n))
#define S32K3XX_FLEXIO_SHIFTCFG(n)           (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTCFG_OFFSET(n))
#define S32K3XX_FLEXIO_SHIFTBUF(n)           (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUF_OFFSET(n))
#define S32K3XX_FLEXIO_SHIFTBUFBIS(n)        (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBIS_OFFSET(n))
#define S32K3XX_FLEXIO_SHIFTBUFBYS(n)        (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBYS_OFFSET(n))
#define S32K3XX_FLEXIO_SHIFTBUFBBS(n)        (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFBBS_OFFSET(n))
#define S32K3XX_FLEXIO_TIMCTL(n)             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCTL_OFFSET(n))
#define S32K3XX_FLEXIO_TIMCFG(n)             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCFG_OFFSET(n))
#define S32K3XX_FLEXIO_TIMCMP(n)             (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_TIMCMP_OFFSET(n))
#define S32K3XX_FLEXIO_SHIFTBUFNBS(n)        (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFNBS_OFFSET(n))
#define S32K3XX_FLEXIO_SHIFTBUFHWS(n)        (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFHWS_OFFSET(n))
#define S32K3XX_FLEXIO_SHIFTBUFNIS(n)        (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFNIS_OFFSET(n))
#define S32K3XX_FLEXIO_SHIFTBUFOES(n)        (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFOES_OFFSET(n))
#define S32K3XX_FLEXIO_SHIFTBUFEOS(n)        (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFEOS_OFFSET(n))
#define S32K3XX_FLEXIO_SHIFTBUFHBS(n)        (S32K3XX_FLEXIO_BASE + S32K3XX_FLEXIO_SHIFTBUFHBS_OFFSET(n))

/* Register Bit Definitions *************************************************/

/* Version ID Register (VERID) */

#define FLEXIO_VERID_FEATURE_SHIFT        (0)       /* Bits 0-15: Feature Specification Number (FEATURE) */
#define FLEXIO_VERID_FEATURE_MASK         (0xffff << FLEXIO_VERID_FEATURE_SHIFT)
#define FLEXIO_VERID_MINOR_SHIFT          (16)      /* Bits 16-23: Minor Version Number (MINOR) */
#define FLEXIO_VERID_MINOR_MASK           (0xff << FLEXIO_VERID_MINOR_SHIFT)
#define FLEXIO_VERID_MAJOR_SHIFT          (24)      /* Bits 24-31: Major Version Number (MAJOR) */
#define FLEXIO_VERID_MAJOR_MASK           (0xff << FLEXIO_VERID_MAJOR_SHIFT)

/* Parameter Register (PARAM) */

#define FLEXIO_PARAM_SHIFTER_SHIFT        (0)       /* Bits 0-7: Shifter Number (SHIFTER) */
#define FLEXIO_PARAM_SHIFTER_MASK         (0xff << FLEXIO_PARAM_SHIFTER_SHIFT)
#define FLEXIO_PARAM_TIMER_SHIFT          (8)       /* Bits 8-15: Timer Number (TIMER) */
#define FLEXIO_PARAM_TIMER_MASK           (0xff << FLEXIO_PARAM_TIMER_SHIFT)
#define FLEXIO_PARAM_PIN_SHIFT            (16)      /* Bits 16-23: Pin Number (PIN) */
#define FLEXIO_PARAM_PIN_MASK             (0xff << FLEXIO_PARAM_PIN_SHIFT)
#define FLEXIO_PARAM_TRIGGER_SHIFT        (24)      /* Bits 24-31: Trigger Number (TRIGGER) */
#define FLEXIO_PARAM_TRIGGER_MASK         (0xff << FLEXIO_PARAM_TRIGGER_SHIFT)

/* FlexIO Control Register (CTRL) */

#define FLEXIO_CTRL_FLEXEN                (1 << 0)  /* Bit 0: FlexIO Enable (FLEXEN) */
#  define FLEXIO_CTRL_FLEXEN_DIS          (0 << 0)  /*        FlexIO module is disabled */
#  define FLEXIO_CTRL_FLEXEN_ENA          (1 << 0)  /*        FlexIO module is enabled */
#define FLEXIO_CTRL_SWRST                 (1 << 1)  /* Bit 1: Software Reset (SWRST) */
#  define FLEXIO_CTRL_SWRST_DIS           (0 << 1)  /*        Software reset is disabled */
#  define FLEXIO_CTRL_SWRST_ENA           (1 << 1)  /*        Software reset is enabled */
#define FLEXIO_CTRL_FASTACC               (1 << 2)  /* Bit 2: Fast Access (FASTACC) */
#  define FLEXIO_CTRL_FASTACC_NORMAL      (0 << 2)  /*        Configures for normal register accesses to FlexIO */
#  define FLEXIO_CTRL_FASTACC_FAST        (1 << 2)  /*        Configures for fast register accesses to FlexIO */
                                                    /* Bits 3-29: Reserved */
#define FLEXIO_CTRL_DBGE                  (1 << 30) /* Bit 30: Debug Enable (DBGE) */
#  define FLEXIO_CTRL_DBGE_DIS            (0 << 30) /*         FlexIO is disabled in debug modes */
#  define FLEXIO_CTRL_DBGE_ENA            (1 << 30) /*         FlexIO is enabled in debug modes */
                                                    /* Bit 31: Reserved */

/* Pin State Register (PIN) */

#define FLEXIO_PIN_PDI_SHIFT              (0)       /* Bits 0-31: Pin Data Input (PDI) */
#define FLEXIO_PIN_PDI_MASK               (0xffffffff << FLEXIO_PIN_PDI_SHIFT)

/* Shifter Status Register (SHIFTSTAT) */

#define FLEXIO_SHIFTSTAT_SSF_SHIFT        (0)       /* Bits 0-7: Shifter Status Flag (SSF) */
#define FLEXIO_SHIFTSTAT_SSF_MASK         (0xff << FLEXIO_SHIFTSTAT_SSF_SHIFT)
                                                    /* Bits 8-31: Reserved */

/* Shifter Error Register (SHIFTERR) */

#define FLEXIO_SHIFTERR_SEF_SHIFT         (0)       /* Bits 0-7: Shifter Error Flags (SEF) */
#define FLEXIO_SHIFTERR_SEF_MASK          (0xff << FLEXIO_SHIFTERR_SEF_SHIFT)
                                                    /* Bits 8-31: Reserved */

/* Timer Status Register (TIMSTAT) */

#define FLEXIO_TIMSTAT_TSF_SHIFT          (0)       /* Bits 0-7: Timer Status Flags (TSF) */
#define FLEXIO_TIMSTAT_TSF_MASK           (0xff << FLEXIO_TIMSTAT_TSF_SHIFT)
                                                    /* Bits 8-31: Reserved */

/* Shifter Status Interrupt Enable (SHIFTSIEN) */

#define FLEXIO_SHIFTSIEN_SSIE_SHIFT       (0)       /* Bits 0-7: Shifter Status Interrupt Enable (SSIE) */
#define FLEXIO_SHIFTSIEN_SSIE_MASK        (0xff << FLEXIO_SHIFTSIEN_SSIE_SHIFT)
                                                    /* Bits 8-31: Reserved */

/* Shifter Error Interrupt Enable (SHIFTEIEN) */

#define FLEXIO_SHIFTEIEN_SEIE_SHIFT       (0)       /* Bits 0-7: Shifter Error Interrupt Enable (SEIE) */
#define FLEXIO_SHIFTEIEN_SEIE_MASK        (0xff << FLEXIO_SHIFTEIEN_SEIE_SHIFT)
                                                    /* Bits 8-31: Reserved */

/* Timer Interrupt Enable Register (TIMIEN) */

#define FLEXIO_TIMIEN_TEIE_SHIFT          (0)       /* Bits 0-7: Timer Status Interrupt Enable (TEIE) */
#define FLEXIO_TIMIEN_TEIE_MASK           (0xff << FLEXIO_TIMIEN_TEIE_SHIFT)
                                                    /* Bits 8-31: Reserved */

/* Shifter Status DMA Enable (SHIFTSDEN) */

#define FLEXIO_SHIFTSDEN_SSDE_SHIFT       (0)       /* Bits 0-7: Shifter Status DMA Enable (SSDE) */
#define FLEXIO_SHIFTSDEN_SSDE_MASK        (0xff << FLEXIO_SHIFTSDEN_SSDE_SHIFT)
                                                    /* Bits 8-31: Reserved */

/* Timer Status DMA Enable (TIMERSDEN) */

#define FLEXIO_TIMERSDEN_TSDE_SHIFT       (0)       /* Bits 0-7: Timer Status DMA Enable (TSDE) */
#define FLEXIO_TIMERSDEN_TSDE_MASK        (0xff << FLEXIO_TIMERSDEN_TSDE_SHIFT)
                                                    /* Bits 8-31: Reserved */

/* Shifter State Register (SHIFTSTATE) */

#define FLEXIO_SHIFTSTATE_STATE_SHIFT     (0)       /* Bits 0-2: Current State Pointer (STATE) */
#define FLEXIO_SHIFTSTATE_STATE_MASK      (0x07 << FLEXIO_SHIFTSTATE_STATE_SHIFT)
                                                    /* Bits 3-31: Reserved */

/* Trigger Status Register (TRGSTAT) */

#define FLEXIO_TRGSTAT_ETSF_SHIFT         (0)       /* Bits 0-3: External Trigger Status Flags (ETSF) */
#define FLEXIO_TRGSTAT_ETSF_MASK          (0x0f << FLEXIO_TRGSTAT_ETSF_SHIFT)
                                                    /* Bits 4-31: Reserved */

/* External Trigger Interrupt Enable Register (TRIGIEN) */

#define FLEXIO_TRIGIEN_TRIE_SHIFT         (0)       /* Bits 0-3: External Trigger Interrupt Enable (TRIE) */
#define FLEXIO_TRIGIEN_TRIE_MASK          (0x0f << FLEXIO_TRIGIEN_TRIE_SHIFT)
                                                    /* Bits 4-31: Reserved */

/* Pin Status Register (PINSTAT) */

#define FLEXIO_PINSTAT_PSF_SHIFT          (0)       /* Bits 0-31: Pin Status Flags (PSF) */
#define FLEXIO_PINSTAT_PSF_MASK           (0xffffffff << FLEXIO_PINSTAT_PSF_SHIFT)

/* Pin Interrupt Enable Register (PINIEN) */

#define FLEXIO_PINIEN_PSIE_SHIFT          (0)       /* Bits 0-31: Pin Status Interrupt Enable (PSIE) */
#define FLEXIO_PINIEN_PSIE_MASK           (0xffffffff << FLEXIO_PINIEN_PSIE_SHIFT)

/* Pin Rising Edge Enable Register (PINREN) */

#define FLEXIO_PINREN_PRE_SHIFT           (0)       /* Bits 0-31: Pin Rising Edge (PRE) */
#define FLEXIO_PINREN_PRE_MASK            (0xffffffff << FLEXIO_PINREN_PRE_SHIFT)

/* Pin Falling Edge Enable Register (PINFEN) */

#define FLEXIO_PINFEN_PFE_SHIFT           (0)       /* Bits 0-31: Pin Falling Edge (PFE) */
#define FLEXIO_PINFEN_PFE_MASK            (0xffffffff << FLEXIO_PINFEN_PFE_SHIFT)

/* Pin Output Data Register (PINOUTD) */

#define FLEXIO_PINOUTD_OUTD_SHIFT         (0)       /* Bits 0-31: Output Data (OUTD) */
#define FLEXIO_PINOUTD_OUTD_MASK          (0xffffffff << FLEXIO_PINOUTD_OUTD_SHIFT)

/* Pin Output Enable Register (PINOUTE) */

#define FLEXIO_PINOUTE_OUTE_SHIFT         (0)       /* Bits 0-31: Output Enable (OUTE) */
#define FLEXIO_PINOUTE_OUTE_MASK          (0xffffffff << FLEXIO_PINOUTE_OUTE_SHIFT)

/* Pin Output Disable Register (PINOUTDIS) */

#define FLEXIO_PINOUTDIS_OUTDIS_SHIFT     (0)       /* Bits 0-31: Output Disable (OUTDIS) */
#define FLEXIO_PINOUTDIS_OUTDIS_MASK      (0xffffffff << FLEXIO_PINOUTDIS_OUTDIS_SHIFT)

/* Pin Output Clear Register (PINOUTCLR) */

#define FLEXIO_PINOUTCLR_OUTCLR_SHIFT     (0)       /* Bits 0-31: Output Clear (OUTCLR) */
#define FLEXIO_PINOUTCLR_OUTCLR_MASK      (0xffffffff << FLEXIO_PINOUTCLR_OUTCLR_SHIFT)

/* Pin Output Set Register (PINOUTSET) */

#define FLEXIO_PINOUTSET_OUTSET_SHIFT     (0)       /* Bits 0-31: Output Set (OUTSET) */
#define FLEXIO_PINOUTSET_OUTSET_MASK      (0xffffffff << FLEXIO_PINOUTSET_OUTSET_SHIFT)

/* Pin Output Toggle Register (PINOUTTOG) */

#define FLEXIO_PINOUTTOG_OUTTOG_SHIFT     (0)       /* Bits 0-31: Output Toggle (OUTTOG) */
#define FLEXIO_PINOUTTOG_OUTTOG_MASK      (0xffffffff << FLEXIO_PINOUTTOG_OUTTOG_SHIFT)

/* Shifter Control n Register (SHIFTCTLn) */

#define FLEXIO_SHIFTCTL_SMOD_SHIFT        (0)       /* Bits 0-2: Shifter Mode (SMOD) */
#define FLEXIO_SHIFTCTL_SMOD_MASK         (0x07 << FLEXIO_SHIFTCTL_SMOD_SHIFT)
#  define FLEXIO_SHIFTCTL_SMOD_DIS        (0x00 << FLEXIO_SHIFTCTL_SMOD_SHIFT) /* Disabled */
#  define FLEXIO_SHIFTCTL_SMOD_RX         (0x01 << FLEXIO_SHIFTCTL_SMOD_SHIFT) /* Receive mode */
#  define FLEXIO_SHIFTCTL_SMOD_TX         (0x02 << FLEXIO_SHIFTCTL_SMOD_SHIFT) /* Transmit mode */
#  define FLEXIO_SHIFTCTL_SMOD_STORE      (0x04 << FLEXIO_SHIFTCTL_SMOD_SHIFT) /* Match Store mode */
#  define FLEXIO_SHIFTCTL_SMOD_CONT       (0x05 << FLEXIO_SHIFTCTL_SMOD_SHIFT) /* Match Continuous mode */
#  define FLEXIO_SHIFTCTL_SMOD_STATE      (0x06 << FLEXIO_SHIFTCTL_SMOD_SHIFT) /* State mode */
#  define FLEXIO_SHIFTCTL_SMOD_LOGIC      (0x07 << FLEXIO_SHIFTCTL_SMOD_SHIFT) /* Logic mode */

                                                    /* Bits 3-6: Reserved */
#define FLEXIO_SHIFTCTL_PINPOL            (1 << 7)  /* Bit 7: Shifter Pin Polarity (PINPOL) */
#  define FLEXIO_SHIFTCTL_PINPOL_HI       (0 << 7)  /*        Pin is active high */
#  define FLEXIO_SHIFTCTL_PINPOL_LO       (1 << 7)  /*        Pin is active low */
#define FLEXIO_SHIFTCTL_PINSEL_SHIFT      (8)       /* Bits 8-12: Shifter Pin Select (PINSEL) */
#define FLEXIO_SHIFTCTL_PINSEL_MASK       (0x1f << FLEXIO_SHIFTCTL_PINSEL_SHIFT)
#  define FLEXIO_SHIFTCTL_PINSEL(n)       (((n) << FLEXIO_SHIFTCTL_PINSEL_SHIFT) & FLEXIO_SHIFTCTL_PINSEL_MASK)
                                                    /* Bits 13-15: Reserved */
#define FLEXIO_SHIFTCTL_PINCFG_SHIFT      (16)      /* Bits 16-17: Shifter Pin Configuration (PINCFG) */
#define FLEXIO_SHIFTCTL_PINCFG_MASK       (0x03 << FLEXIO_SHIFTCTL_PINCFG_SHIFT)
#  define FLEXIO_SHIFTCTL_PINCFG_DIS      (0x00 << FLEXIO_SHIFTCTL_PINCFG_SHIFT) /* Shifter pin output disabled */
#  define FLEXIO_SHIFTCTL_PINCFG_OD       (0x01 << FLEXIO_SHIFTCTL_PINCFG_SHIFT) /* Shifter pin open drain or bidirectional output enable */
#  define FLEXIO_SHIFTCTL_PINCFG_BID      (0x02 << FLEXIO_SHIFTCTL_PINCFG_SHIFT) /* Shifter pin bidirectional output data */
#  define FLEXIO_SHIFTCTL_PINCFG_OUT      (0x03 << FLEXIO_SHIFTCTL_PINCFG_SHIFT) /* Shifter pin output */

                                                    /* Bits 18-22: Reserved */
#define FLEXIO_SHIFTCTL_TIMPOL            (1 << 23) /* Bit 23: Timer Polarity (TIMPOL) */
#  define FLEXIO_SHIFTCTL_TIMPOL_PE       (0 << 23) /*         Shift on posedge of Shift clock */
#  define FLEXIO_SHIFTCTL_TIMPOL_NE       (1 << 23) /*         Shift on negedge of Shift clock */
#define FLEXIO_SHIFTCTL_TIMSEL_SHIFT      (24)      /* Bits 24-26: Timer Select (TIMSEL) */
#define FLEXIO_SHIFTCTL_TIMSEL_MASK       (0x07 << FLEXIO_SHIFTCTL_TIMSEL_SHIFT)
#  define FLEXIO_SHIFTCTL_TIMSEL(n)       (((n) << FLEXIO_SHIFTCTL_TIMSEL_SHIFT) & FLEXIO_SHIFTCTL_TIMSEL_MASK)
                                                    /* Bits 27-31: Reserved */

/* Shifter Configuration n Register (SHIFTCFGn) */

#define FLEXIO_SHIFTCFG_SSTART_SHIFT      (0)       /* Bits 0-1: Shifter Start bit (SSTART) */
#define FLEXIO_SHIFTCFG_SSTART_MASK       (0x03 << FLEXIO_SHIFTCFG_SSTART_SHIFT)
#  define FLEXIO_SHIFTCFG_SSTART_DIS      (0x00 << FLEXIO_SHIFTCFG_SSTART_SHIFT) /* Start bit disabled for transmitter/receiver/match store, transmitter loads data on enable */
#  define FLEXIO_SHIFTCFG_SSTART_DIS_SH   (0x01 << FLEXIO_SHIFTCFG_SSTART_SHIFT) /* Start bit disabled for transmitter/receiver/match store, transmitter loads data on first shift */
#  define FLEXIO_SHIFTCFG_SSTART_ZERO     (0x02 << FLEXIO_SHIFTCFG_SSTART_SHIFT) /* Transmitter outputs start bit value 0 before loading data on first shift, receiver/match store sets error flag if start bit is not 0 */
#  define FLEXIO_SHIFTCFG_SSTART_ONE      (0x03 << FLEXIO_SHIFTCFG_SSTART_SHIFT) /* Transmitter outputs start bit value 1 before loading data on first shift, receiver/match store sets error flag if start bit is not 1 */

                                                    /* Bits 2-3: Reserved */
#define FLEXIO_SHIFTCFG_SSTOP_SHIFT       (4)       /* Bits 4-5: Shifter Stop bit (SSTOP) */
#define FLEXIO_SHIFTCFG_SSTOP_MASK        (0x03 << FLEXIO_SHIFTCFG_SSTOP_SHIFT)
#  define FLEXIO_SHIFTCFG_SSTOP_DIS       (0x00 << FLEXIO_SHIFTCFG_SSTOP_SHIFT) /* Stop bit disabled for transmitter/receiver/match store */
#  define FLEXIO_SHIFTCFG_SSTOP_ZERO      (0x02 << FLEXIO_SHIFTCFG_SSTOP_SHIFT) /* Transmitter outputs stop bit value 0 on store, receiver/match store sets error flag if stop bit is not 0 */
#  define FLEXIO_SHIFTCFG_SSTOP_ONE       (0x03 << FLEXIO_SHIFTCFG_SSTOP_SHIFT) /* Transmitter outputs stop bit value 1 on store, receiver/match store sets error flag if stop bit is not 1 */

                                                    /* Bits 6-7: Reserved */
#define FLEXIO_SHIFTCFG_INSRC             (1 << 8)  /* Bit 8: Input Source (INSRC) */
#  define FLEXIO_SHIFTCFG_INSRC_PIN       (0 << 8)  /*        Pin */
#  define FLEXIO_SHIFTCFG_INSRC_SHIFTER   (1 << 8)  /*        Shifter N+1 Output */
#define FLEXIO_SHIFTCFG_LATST             (1 << 9)  /* Bit 9: Late Store (LATST) */
#  define FLEXIO_SHIFTCFG_LATST_PRE       (0 << 9)  /*        Shift register stores the pre-shift register state */
#  define FLEXIO_SHIFTCFG_LATST_POST      (1 << 9)  /*        Shift register stores the post-shift register state */
                                                    /* Bits 10-11: Reserved */
#define FLEXIO_SHIFTCFG_SSIZE             (1 << 12) /* Bit 12: Shifter Size (SSIZE) */
#  define FLEXIO_SHIFTCFG_SSIZE_32        (0 << 12) /*         Shift register is 32-bit */
#  define FLEXIO_SHIFTCFG_SSIZE_24        (1 << 12) /*         Shift register is 24-bit */
                                                    /* Bits 13-15: Reserved */
#define FLEXIO_SHIFTCFG_PWIDTH_SHIFT      (16)      /* Bits 16-20: Parallel Width (PWIDTH) */
#define FLEXIO_SHIFTCFG_PWIDTH_MASK       (0x1f << FLEXIO_SHIFTCFG_PWIDTH_SHIFT)
#  define FLEXIO_SHIFTCFG_PWIDTH_1        (0x00 << FLEXIO_SHIFTCFG_PWIDTH_SHIFT) /* 1-bit shift */
#  define FLEXIO_SHIFTCFG_PWIDTH_2        (0x01 << FLEXIO_SHIFTCFG_PWIDTH_SHIFT) /* 2-bit shift */
#  define FLEXIO_SHIFTCFG_PWIDTH_4        (0x02 << FLEXIO_SHIFTCFG_PWIDTH_SHIFT) /* 4-bit shift */
#  define FLEXIO_SHIFTCFG_PWIDTH_8        (0x04 << FLEXIO_SHIFTCFG_PWIDTH_SHIFT) /* 8-bit shift */
#  define FLEXIO_SHIFTCFG_PWIDTH_16       (0x08 << FLEXIO_SHIFTCFG_PWIDTH_SHIFT) /* 16-bit shift */
#  define FLEXIO_SHIFTCFG_PWIDTH_32       (0x10 << FLEXIO_SHIFTCFG_PWIDTH_SHIFT) /* 32-bit shift */

                                                    /* Bits 21-31: Reserved */

/* Shifter Buffer n Register (SHIFTBUFn) */

#define FLEXIO_SHIFTBUF_SHIFT             (0)       /* Bits 0-31: Shift Buffer (SHIFTBUF) */
#define FLEXIO_SHIFTBUF_MASK              (0xffffffff << FLEXIO_SHIFTBUF_SHIFT)

/* Shifter Buffer n Bit Swapped Register (SHIFTBUFBISn) */

#define FLEXIO_SHIFTBUFBIS_SHIFT          (0)       /* Bits 0-31: Shift Buffer (bit swapped) (SHIFTBUFBIS) */
#define FLEXIO_SHIFTBUFBIS_MASK           (0xffffffff << FLEXIO_SHIFTBUFBIS_SHIFT)

/* Shifter Buffer n Byte Swapped Register (SHIFTBUFBYSn) */

#define FLEXIO_SHIFTBUFBYS_SHIFT          (0)       /* Bits 0-31: Shift Buffer (byte swapped) (SHIFTBUFBYS) */
#define FLEXIO_SHIFTBUFBYS_MASK           (0xffffffff << FLEXIO_SHIFTBUFBYS_SHIFT)

/* Shifter Buffer n Byte Swapped Register (SHIFTBUFBBSn) */

#define FLEXIO_SHIFTBUFBBS_SHIFT          (0)       /* Bits 0-31: Shift Buffer (bit and byte swapped) (SHIFTBUFBBS) */
#define FLEXIO_SHIFTBUFBBS_MASK           (0xffffffff << FLEXIO_SHIFTBUFBBS_SHIFT)

/* Timer Control n Register (TIMCTLn) */

#define FLEXIO_TIMCTL_TIMOD_SHIFT         (0)       /* Bits 0-2: Timer Mode (TIMOD) */
#define FLEXIO_TIMCTL_TIMOD_MASK          (0x07 << FLEXIO_TIMCTL_TIMOD_SHIFT)
#  define FLEXIO_TIMCTL_TIMOD_DIS         (0x00 << FLEXIO_TIMCTL_TIMOD_SHIFT) /* Timer Disabled */
#  define FLEXIO_TIMCTL_TIMOD_8BBAUD      (0x01 << FLEXIO_TIMCTL_TIMOD_SHIFT) /* Dual 8-bit counters baud mode */
#  define FLEXIO_TIMCTL_TIMOD_8BPWMHI     (0x02 << FLEXIO_TIMCTL_TIMOD_SHIFT) /* Dual 8-bit counters PWM high mode */
#  define FLEXIO_TIMCTL_TIMOD_16BCNT      (0x03 << FLEXIO_TIMCTL_TIMOD_SHIFT) /* Single 16-bit counter mode */
#  define FLEXIO_TIMCTL_TIMOD_16BDIS      (0x04 << FLEXIO_TIMCTL_TIMOD_SHIFT) /* Single 16-bit counter disable mode */
#  define FLEXIO_TIMCTL_TIMOD_8BWORD      (0x05 << FLEXIO_TIMCTL_TIMOD_SHIFT) /* Dual 8-bit counters word mode */
#  define FLEXIO_TIMCTL_TIMOD_8BPWMLO     (0x06 << FLEXIO_TIMCTL_TIMOD_SHIFT) /* Dual 8-bit counters PWM low mode */
#  define FLEXIO_TIMCTL_TIMOD_16BINCAP    (0x07 << FLEXIO_TIMCTL_TIMOD_SHIFT) /* Single 16-bit input capture mode */

                                                    /* Bits 3-4: Reserved */
#define FLEXIO_TIMCTL_ONETIM              (1 << 5)  /* Bit 5: Timer One Time Operation (ONETIM) */
#define FLEXIO_TIMCTL_PININS              (1 << 6)  /* Bit 6: Timer Pin Input Select (PININS) */
#  define FLEXIO_TIMCTL_PININS_PINSEL     (0 << 6)  /*        Timer pin input and output are selected by PINSEL */
#  define FLEXIO_TIMCTL_PININS_PLUS1      (1 << 6)  /*        Timer pin input is selected by PINSEL+1, timer pin output remains selected by PINSEL */
#define FLEXIO_TIMCTL_PINPOL              (1 << 7)  /* Bit 7: Timer Pin Polarity (PINPOL) */
#  define FLEXIO_TIMCTL_PINPOL_HI         (0 << 7)  /*        Pin is active high */
#  define FLEXIO_TIMCTL_PINPOL_LO         (1 << 7)  /*        Pin is active low */
#define FLEXIO_TIMCTL_PINSEL_SHIFT        (8)       /* Bits 8-12: Timer Pin Select (PINSEL) */ 
#define FLEXIO_TIMCTL_PINSEL_MASK         (0x1f << FLEXIO_TIMCTL_PINSEL_SHIFT)
#  define FLEXIO_TIMCTL_PINSEL(n)         (((n) << FLEXIO_TIMCTL_PINSEL_SHIFT) & FLEXIO_TIMCTL_PINSEL_MASK)
                                                    /* Bits 13-15: Reserved */
#define FLEXIO_TIMCTL_PINCFG_SHIFT        (16)      /* Bits 16-17: Timer Pin Configuration (PINCFG) */
#define FLEXIO_TIMCTL_PINCFG_MASK         (0x03 << FLEXIO_TIMCTL_PINCFG_SHIFT)
#  define FLEXIO_TIMCTL_PINCFG_DIS        (0x00 << FLEXIO_TIMCTL_PINCFG_SHIFT) /* Timer pin output disabled */
#  define FLEXIO_TIMCTL_PINCFG_OD         (0x01 << FLEXIO_TIMCTL_PINCFG_SHIFT) /* Timer pin open drain or bidirectional output enable */
#  define FLEXIO_TIMCTL_PINCFG_BID        (0x02 << FLEXIO_TIMCTL_PINCFG_SHIFT) /* Timer pin bidirectional output data */
#  define FLEXIO_TIMCTL_PINCFG_OUT        (0x03 << FLEXIO_TIMCTL_PINCFG_SHIFT) /* Timer pin output */

                                                    /* Bits 18-21: Reserved */
#define FLEXIO_TIMCTL_TRGSRC              (1 << 22) /* Bit 22: Trigger Source (TRGSRC) */
#  define FLEXIO_TIMCTL_TRGSRC_EXT        (0 << 22) /*         External trigger selected */
#  define FLEXIO_TIMCTL_TRGSRC_INT        (1 << 22) /*         Internal trigger selected */
#define FLEXIO_TIMCTL_TRGPOL              (1 << 23) /* Bit 23: Trigger Polarity (TRGPOL) */
#  define FLEXIO_TIMCTL_TRGPOL_HI         (0 << 23) /*         Trigger active high */
#  define FLEXIO_TIMCTL_TRGPOL_LO         (1 << 23) /*         Trigger active low */
#define FLEXIO_TIMCTL_TRGSEL_SHIFT        (24)      /* Bits 24-29: Trigger Select (TRGSEL) */
#define FLEXIO_TIMCTL_TRGSEL_MASK         (0x3f << FLEXIO_TIMCTL_TRGSEL_SHIFT)
#  define FLEXIO_TIMCTL_TRGSEL_EXT(n)     (((n) << FLEXIO_TIMCTL_TRGSEL_SHIFT) & FLEXIO_TIMCTL_TRGSEL_MASK)       /* External trigger n input */
#  define FLEXIO_TIMCTL_TRGSEL_PIN(n)     (((2*(n)) << FLEXIO_TIMCTL_TRGSEL_SHIFT) & FLEXIO_TIMCTL_TRGSEL_MASK)   /* Pin n input */
#  define FLEXIO_TIMCTL_TRGSEL_SHIFTER(n) (((4*(n)+1) << FLEXIO_TIMCTL_TRGSEL_SHIFT) & FLEXIO_TIMCTL_TRGSEL_MASK) /* Shifter n status flag */
#  define FLEXIO_TIMCTL_TRGSEL_TIMER(n)   (((4*(n)+3) << FLEXIO_TIMCTL_TRGSEL_SHIFT) & FLEXIO_TIMCTL_TRGSEL_MASK) /* Timer n trigger output */

                                                    /* Bits 30-31: Reserved */

/* Timer Configuration n Register (TIMCFGn) */

                                                    /* Bit 0: Reserved */
#define FLEXIO_TIMCFG_TSTART              (1 << 1)  /* Bit 1: Timer Start Bit (TSTART) */
#  define FLEXIO_TIMCFG_TSTART_DIS        (0 << 1)  /*        Start bit disabled */
#  define FLEXIO_TIMCFG_TSTART_ENA        (1 << 1)  /*        Start bit enabled */
                                                    /* Bits 2-3: Reserved */
#define FLEXIO_TIMCFG_TSTOP_SHIFT         (4)       /* Bits 4-5: Timer Stop Bit (TSTOP) */ 
#define FLEXIO_TIMCFG_TSTOP_MASK          (0x03 << FLEXIO_TIMCFG_TSTOP_SHIFT)
#  define FLEXIO_TIMCFG_TSTOP_DIS         (0x00 << FLEXIO_TIMCFG_TSTOP_SHIFT) /* Stop bit disabled */
#  define FLEXIO_TIMCFG_TSTOP_TIMCMP      (0x01 << FLEXIO_TIMCFG_TSTOP_SHIFT) /* Stop bit is enabled on timer compare */
#  define FLEXIO_TIMCFG_TSTOP_TIMDIS      (0x02 << FLEXIO_TIMCFG_TSTOP_SHIFT) /* Stop bit is enabled on timer disable */
#  define FLEXIO_TIMCFG_TSTOP_BOTH        (0x03 << FLEXIO_TIMCFG_TSTOP_SHIFT) /* Stop bit is enabled on timer compare and timer disable */

                                                    /* Bits 6-7: Reserved */
#define FLEXIO_TIMCFG_TIMENA_SHIFT        (8)       /* Bits 8-10: Timer Enable (TIMENA) */ 
#define FLEXIO_TIMCFG_TIMENA_MASK         (0x07 << FLEXIO_TIMCFG_TIMENA_SHIFT)
#  define FLEXIO_TIMCFG_TIMENA_ALWAYS     (0x00 << FLEXIO_TIMCFG_TIMENA_SHIFT) /* Timer always enabled */
#  define FLEXIO_TIMCFG_TIMENA_TIMENA     (0x01 << FLEXIO_TIMCFG_TIMENA_SHIFT) /* Timer enabled on Timer N-1 enable */
#  define FLEXIO_TIMCFG_TIMENA_TRGHI      (0x02 << FLEXIO_TIMCFG_TIMENA_SHIFT) /* Timer enabled on Trigger high */
#  define FLEXIO_TIMCFG_TIMENA_TRGHIPIN   (0x03 << FLEXIO_TIMCFG_TIMENA_SHIFT) /* Timer enabled on Trigger high and Pin high */
#  define FLEXIO_TIMCFG_TIMENA_PINRIS     (0x04 << FLEXIO_TIMCFG_TIMENA_SHIFT) /* Timer enabled on Pin rising edge */
#  define FLEXIO_TIMCFG_TIMENA_PINTRG     (0x05 << FLEXIO_TIMCFG_TIMENA_SHIFT) /* Timer enabled on Pin rising edge and Trigger high */
#  define FLEXIO_TIMCFG_TIMENA_TRGRIS     (0x06 << FLEXIO_TIMCFG_TIMENA_SHIFT) /* Timer enabled on Trigger rising edge */
#  define FLEXIO_TIMCFG_TIMENA_TRGBOTH    (0x07 << FLEXIO_TIMCFG_TIMENA_SHIFT) /* Timer enabled on Trigger rising or falling edge */

                                                    /* Bit 11: Reserved */
#define FLEXIO_TIMCFG_TIMDIS_SHIFT        (12)      /* Bits 12-14: Timer Disable (TIMDIS) */
#define FLEXIO_TIMCFG_TIMDIS_MASK         (0x07 << FLEXIO_TIMCFG_TIMDIS_SHIFT)
#  define FLEXIO_TIMCFG_TIMDIS_NEVER      (0x00 << FLEXIO_TIMCFG_TIMDIS_SHIFT) /* Timer never disabled */
#  define FLEXIO_TIMCFG_TIMDIS_TIMDIS     (0x01 << FLEXIO_TIMCFG_TIMDIS_SHIFT) /* Timer disabled on Timer N-1 disable */
#  define FLEXIO_TIMCFG_TIMDIS_TIMCMP     (0x02 << FLEXIO_TIMCFG_TIMDIS_SHIFT) /* Timer disabled on Timer compare (upper 8-bits match and decrement) */
#  define FLEXIO_TIMCFG_TIMDIS_CMPTRGLO   (0x03 << FLEXIO_TIMCFG_TIMDIS_SHIFT) /* Timer disabled on Timer compare (upper 8-bits match and decrement) and Trigger Low */
#  define FLEXIO_TIMCFG_TIMDIS_PIN        (0x04 << FLEXIO_TIMCFG_TIMDIS_SHIFT) /* Timer disabled on Pin rising or falling edge */
#  define FLEXIO_TIMCFG_TIMDIS_PINTRGHI   (0x05 << FLEXIO_TIMCFG_TIMDIS_SHIFT) /* Timer disabled on Pin rising or falling edge provided Trigger is high */
#  define FLEXIO_TIMCFG_TIMDIS_TRG        (0x06 << FLEXIO_TIMCFG_TIMDIS_SHIFT) /* Timer disabled on Trigger falling edge */

                                                    /* Bit 15: Reserved */
#define FLEXIO_TIMCFG_TIMRST_SHIFT        (16)      /* Bits 16-18: Timer Reset (TIMRST) */
#define FLEXIO_TIMCFG_TIMRST_MASK         (0x07 << FLEXIO_TIMCFG_TIMRST_SHIFT)
#  define FLEXIO_TIMCFG_TIMRST_NEVER      (0x00 << FLEXIO_TIMCFG_TIMRST_SHIFT) /* Timer never reset */
#  define FLEXIO_TIMCFG_TIMRST_OUTHI      (0x01 << FLEXIO_TIMCFG_TIMRST_SHIFT) /* Timer reset on Timer Output high */
#  define FLEXIO_TIMCFG_TIMRST_PINOUT     (0x02 << FLEXIO_TIMCFG_TIMRST_SHIFT) /* Timer reset on Timer Pin equal to Timer Output */
#  define FLEXIO_TIMCFG_TIMRST_TRGOUT     (0x03 << FLEXIO_TIMCFG_TIMRST_SHIFT) /* Timer reset on Timer Trigger equal to Timer Output */
#  define FLEXIO_TIMCFG_TIMRST_PINRIS     (0x04 << FLEXIO_TIMCFG_TIMRST_SHIFT) /* Timer reset on Timer Pin rising edge */
#  define FLEXIO_TIMCFG_TIMRST_TRGRIS     (0x06 << FLEXIO_TIMCFG_TIMRST_SHIFT) /* Timer reset on Trigger rising edge */
#  define FLEXIO_TIMCFG_TIMRST_TRGBOTH    (0x07 << FLEXIO_TIMCFG_TIMRST_SHIFT) /* Timer reset on Trigger rising or falling edge */

                                                    /* Bit 19: Reserved */
#define FLEXIO_TIMCFG_TIMDEC_SHIFT             (20) /* Bits 20-22: Timer Decrement (TIMDEC) */ 
#define FLEXIO_TIMCFG_TIMDEC_MASK              (0x07 << FLEXIO_TIMCFG_TIMDEC_SHIFT)
#  define FLEXIO_TIMCFG_TIMDEC_CLKTIMOUT       (0x00 << FLEXIO_TIMCFG_TIMDEC_SHIFT) /* Decrement counter on FlexIO clock, Shift clock equals Timer output */
#  define FLEXIO_TIMCFG_TIMDEC_TRGINBOTHTIMOUT (0x01 << FLEXIO_TIMCFG_TIMDEC_SHIFT) /* Decrement counter on Trigger input (both edges), Shift clock equals Timer output */
#  define FLEXIO_TIMCFG_TIMDEC_PINBOTHPIN      (0x02 << FLEXIO_TIMCFG_TIMDEC_SHIFT) /* Decrement counter on Pin input (both edges), Shift clock equals Pin input */
#  define FLEXIO_TIMCFG_TIMDEC_TRGINBOTHTRGIN  (0x03 << FLEXIO_TIMCFG_TIMDEC_SHIFT) /* Decrement counter on Trigger input (both edges), Shift clock equals Trigger input */
#  define FLEXIO_TIMCFG_TIMDEC_CLKDIV16TIMOUT  (0x04 << FLEXIO_TIMCFG_TIMDEC_SHIFT) /* Decrement counter on FlexIO clock divided by 16, Shift clock equals Timer output */
#  define FLEXIO_TIMCFG_TIMDEC_CLKDIV256TIMOUT (0x05 << FLEXIO_TIMCFG_TIMDEC_SHIFT) /* Decrement counter on FlexIO clock divided by 256, Shift clock equals Timer output */
#  define FLEXIO_TIMCFG_TIMDEC_PINRISPIN       (0x06 << FLEXIO_TIMCFG_TIMDEC_SHIFT) /* Decrement counter on Pin input (rising edge), Shift clock equals Pin input */
#  define FLEXIO_TIMCFG_TIMDEC_TRGINRISTRGIN   (0x07 << FLEXIO_TIMCFG_TIMDEC_SHIFT) /* Decrement counter on Trigger input (rising edge), Shift clock equals Trigger input */

                                                    /* Bit 23: Reserved */
#define FLEXIO_TIMCFG_TIMOUT_SHIFT        (24)      /* Bits 24-25: Timer Output (TIMOUT) */ 
#define FLEXIO_TIMCFG_TIMOUT_MASK         (0x03 << FLEXIO_TIMCFG_TIMOUT_SHIFT)
#  define FLEXIO_TIMCFG_TIMOUT_ONE        (0x00 << FLEXIO_TIMCFG_TIMOUT_SHIFT) /* Timer output is logic one when enabled and is not affected by timer reset */
#  define FLEXIO_TIMCFG_TIMOUT_ZERO       (0x01 << FLEXIO_TIMCFG_TIMOUT_SHIFT) /* Timer output is logic zero when enabled and is not affected by timer reset */
#  define FLEXIO_TIMCFG_TIMOUT_ONERST     (0x02 << FLEXIO_TIMCFG_TIMOUT_SHIFT) /* Timer output is logic one when enabled and on timer reset */
#  define FLEXIO_TIMCFG_TIMOUT_ZERORST    (0x03 << FLEXIO_TIMCFG_TIMOUT_SHIFT) /* Timer output is logic zero when enabled and on timer reset */

                                                    /* Bits 26-31: Reserved */

/* Timer Compare n Register (TIMCMPn) */

#define FLEXIO_TIMCMP_CMP_SHIFT           (0)       /* Bits 0-15: Timer Compare Value (CMP) */
#define FLEXIO_TIMCMP_CMP_MASK            (0xffff << FLEXIO_TIMCMP_CMP_SHIFT)
                                                    /* Bits 16-31: Reserved */

/* Shifter Buffer n Nibble Byte Swapped Register (SHIFTBUFNBSn) */

#define FLEXIO_SHIFTBUFNBS_SHIFT          (0)       /* Bits 0-31: Shift Buffer (nibble swapped within each byte) (SHIFTBUFNBS) */
#define FLEXIO_SHIFTBUFNBS_MASK           (0xffffffff << FLEXIO_SHIFTBUFNBS_SHIFT)

/* Shifter Buffer n Half Word Swapped Register (SHIFTBUFHWSn) */

#define FLEXIO_SHIFTBUFHWS_SHIFT          (0)       /* Bits 0-31: Shift Buffer (half word swapped) (SHIFTBUFHWS) */
#define FLEXIO_SHIFTBUFHWS_MASK           (0xffffffff << FLEXIO_SHIFTBUFHWS_SHIFT)

/* Shifter Buffer n Nibble Swapped Register (SHIFTBUFNISn) */

#define FLEXIO_SHIFTBUFNIS_SHIFT          (0)       /* Bits 0-31: Shift Buffer (nibble swapped) (SHIFTBUFNIS) */
#define FLEXIO_SHIFTBUFNIS_MASK           (0xffffffff << FLEXIO_SHIFTBUFNIS_SHIFT)

/* Shifter Buffer n Odd Even Swapped Register (SHIFTBUFOESn) */

#define FLEXIO_SHIFTBUFOES_SHIFT          (0)       /* Bits 0-31: Shift Buffer (odd and even bits partitioned) (SHIFTBUFOES) */
#define FLEXIO_SHIFTBUFOES_MASK           (0xffffffff << FLEXIO_SHIFTBUFOES_SHIFT)

/* Shifter Buffer n Even Odd Swapped Register (SHIFTBUFEOSn) */

#define FLEXIO_SHIFTBUFEOS_SHIFT          (0)       /* Bits 0-31: Shift Buffer (even and odd bits partitioned) (SHIFTBUFEOS) */
#define FLEXIO_SHIFTBUFEOS_MASK           (0xffffffff << FLEXIO_SHIFTBUFEOS_SHIFT)

/* Shifter Buffer n Halfword Byte Swapped Register (SHIFTBUFHBSn) */

#define FLEXIO_SHIFTBUFHBS_SHIFT          (0)       /* Bits 0-31: Shift Buffer (halfword byte swapped) (SHIFTBUFHBS) */
#define FLEXIO_SHIFTBUFHBS_MASK           (0xffffffff << FLEXIO_SHIFTBUFHBS_SHIFT)

#endif /* __ARCH_ARM_SRC_S32K3XX_HARDWARE_S32K3XX_FLEXIO_H */
