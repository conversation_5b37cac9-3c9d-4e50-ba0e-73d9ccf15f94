/****************************************************************************
 * arch/arm/src/s32k3xx/hardware/s32k3xx_emac.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/* Copyright 2022 NXP */

#ifndef __ARCH_ARM_SRC_S32K3XX_HARDWARE_S32K3XX_EMAC_H
#define __ARCH_ARM_SRC_S32K3XX_HARDWARE_S32K3XX_EMAC_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <hardware/s32k3xx_memorymap.h>

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* EMAC Register Offsets ****************************************************/

#define S32K3XX_EMAC_MAC_CONFIGURATION_OFFSET                      (0x0000)
#define S32K3XX_EMAC_MAC_EXT_CONFIGURATION_OFFSET                  (0x0004)
#define S32K3XX_EMAC_MAC_PACKET_FILTER_OFFSET                      (0x0008)
#define S32K3XX_EMAC_MAC_WATCHDOG_TIMEOUT_OFFSET                   (0x000c)
#define S32K3XX_EMAC_MAC_HASH_TABLE_REG0_OFFSET                    (0x0010)
#define S32K3XX_EMAC_MAC_HASH_TABLE_REG1_OFFSET                    (0x0014)
#define S32K3XX_EMAC_MAC_VLAN_TAG_OFFSET                           (0x0050)
#define S32K3XX_EMAC_MAC_VLAN_TAG_CTRL_OFFSET                      (0x0050)
#define S32K3XX_EMAC_MAC_VLAN_TAG_DATA_OFFSET                      (0x0054)
#define S32K3XX_EMAC_MAC_VLAN_TAG_FILTER0_OFFSET                   (0x0054)
#define S32K3XX_EMAC_MAC_VLAN_TAG_FILTER1_OFFSET                   (0x0054)
#define S32K3XX_EMAC_MAC_VLAN_TAG_FILTER2_OFFSET                   (0x0054)
#define S32K3XX_EMAC_MAC_VLAN_TAG_FILTER3_OFFSET                   (0x0054)
#define S32K3XX_EMAC_MAC_VLAN_HASH_TABLE_OFFSET                    (0x0058)
#define S32K3XX_EMAC_MAC_VLAN_INCL_OFFSET                          (0x0060)
#define S32K3XX_EMAC_MAC_INNER_VLAN_INCL_OFFSET                    (0x0064)
#define S32K3XX_EMAC_MAC_Q0_TX_FLOW_CTRL_OFFSET                    (0x0070)
#define S32K3XX_EMAC_MAC_RX_FLOW_CTRL_OFFSET                       (0x0090)
#define S32K3XX_EMAC_MAC_RXQ_CTRL4_OFFSET                          (0x0094)
#define S32K3XX_EMAC_MAC_RXQ_CTRL0_OFFSET                          (0x00a0)
#define S32K3XX_EMAC_MAC_RXQ_CTRL1_OFFSET                          (0x00a4)
#define S32K3XX_EMAC_MAC_RXQ_CTRL2_OFFSET                          (0x00a8)
#define S32K3XX_EMAC_MAC_INTERRUPT_STATUS_OFFSET                   (0x00b0)
#define S32K3XX_EMAC_MAC_INTERRUPT_ENABLE_OFFSET                   (0x00b4)
#define S32K3XX_EMAC_MAC_RX_TX_STATUS_OFFSET                       (0x00b8)
#define S32K3XX_EMAC_MAC_VERSION_OFFSET                            (0x0110)
#define S32K3XX_EMAC_MAC_DEBUG_OFFSET                              (0x0114)
#define S32K3XX_EMAC_MAC_HW_FEATURE0_OFFSET                        (0x011c)
#define S32K3XX_EMAC_MAC_HW_FEATURE1_OFFSET                        (0x0120)
#define S32K3XX_EMAC_MAC_HW_FEATURE2_OFFSET                        (0x0124)
#define S32K3XX_EMAC_MAC_HW_FEATURE3_OFFSET                        (0x0128)
#define S32K3XX_EMAC_MAC_DPP_FSM_INTERRUPT_STATUS_OFFSET           (0x0140)
#define S32K3XX_EMAC_MAC_FSM_CONTROL_OFFSET                        (0x0148)
#define S32K3XX_EMAC_MAC_FSM_ACT_TIMER_OFFSET                      (0x014c)
#define S32K3XX_EMAC_SCS_REG1_OFFSET                               (0x0150)
#define S32K3XX_EMAC_MAC_MDIO_ADDRESS_OFFSET                       (0x0200)
#define S32K3XX_EMAC_MAC_MDIO_DATA_OFFSET                          (0x0204)
#define S32K3XX_EMAC_MAC_CSR_SW_CTRL_OFFSET                        (0x0230)
#define S32K3XX_EMAC_MAC_FPE_CTRL_STS_OFFSET                       (0x0234)
#define S32K3XX_EMAC_MAC_PRESN_TIME_NS_OFFSET                      (0x0240)
#define S32K3XX_EMAC_MAC_PRESN_TIME_UPDT_OFFSET                    (0x0244)
#define S32K3XX_EMAC_MAC_ADDRESS0_HIGH_OFFSET                      (0x0300)
#define S32K3XX_EMAC_MAC_ADDRESS0_LOW_OFFSET                       (0x0304)
#define S32K3XX_EMAC_MAC_ADDRESS1_HIGH_OFFSET                      (0x0308)
#define S32K3XX_EMAC_MAC_ADDRESS1_LOW_OFFSET                       (0x030c)
#define S32K3XX_EMAC_MAC_ADDRESS2_HIGH_OFFSET                      (0x0310)
#define S32K3XX_EMAC_MAC_ADDRESS2_LOW_OFFSET                       (0x0314)
#define S32K3XX_EMAC_MMC_CONTROL_OFFSET                            (0x0700)
#define S32K3XX_EMAC_MMC_RX_INTERRUPT_OFFSET                       (0x0704)
#define S32K3XX_EMAC_MMC_TX_INTERRUPT_OFFSET                       (0x0708)
#define S32K3XX_EMAC_MMC_RX_INTERRUPT_MASK_OFFSET                  (0x070c)
#define S32K3XX_EMAC_MMC_TX_INTERRUPT_MASK_OFFSET                  (0x0710)
#define S32K3XX_EMAC_TX_OCTET_COUNT_GOOD_BAD_OFFSET                (0x0714)
#define S32K3XX_EMAC_TX_PACKET_COUNT_GOOD_BAD_OFFSET               (0x0718)
#define S32K3XX_EMAC_TX_BROADCAST_PACKETS_GOOD_OFFSET              (0x071c)
#define S32K3XX_EMAC_TX_MULTICAST_PACKETS_GOOD_OFFSET              (0x0720)
#define S32K3XX_EMAC_TX_64OCTETS_PACKETS_GOOD_BAD_OFFSET           (0x0724)
#define S32K3XX_EMAC_TX_65TO127OCTETS_PACKETS_GOOD_BAD_OFFSET      (0x0728)
#define S32K3XX_EMAC_TX_128TO255OCTETS_PACKETS_GOOD_BAD_OFFSET     (0x072c)
#define S32K3XX_EMAC_TX_256TO511OCTETS_PACKETS_GOOD_BAD_OFFSET     (0x0730)
#define S32K3XX_EMAC_TX_512TO1023OCTETS_PACKETS_GOOD_BAD_OFFSET    (0x0734)
#define S32K3XX_EMAC_TX_1024TOMAXOCTETS_PACKETS_GOOD_BAD_OFFSET    (0x0738)
#define S32K3XX_EMAC_TX_UNICAST_PACKETS_GOOD_BAD_OFFSET            (0x073c)
#define S32K3XX_EMAC_TX_MULTICAST_PACKETS_GOOD_BAD_OFFSET          (0x0740)
#define S32K3XX_EMAC_TX_BROADCAST_PACKETS_GOOD_BAD_OFFSET          (0x0744)
#define S32K3XX_EMAC_TX_UNDERFLOW_ERROR_PACKETS_OFFSET             (0x0748)
#define S32K3XX_EMAC_TX_SINGLE_COLLISION_GOOD_PACKETS_OFFSET       (0x074c)
#define S32K3XX_EMAC_TX_MULTIPLE_COLLISION_GOOD_PACKETS_OFFSET     (0x0750)
#define S32K3XX_EMAC_TX_DEFERRED_PACKETS_OFFSET                    (0x0754)
#define S32K3XX_EMAC_TX_LATE_COLLISION_PACKETS_OFFSET              (0x0758)
#define S32K3XX_EMAC_TX_EXCESSIVE_COLLISION_PACKETS_OFFSET         (0x075c)
#define S32K3XX_EMAC_TX_CARRIER_ERROR_PACKETS_OFFSET               (0x0760)
#define S32K3XX_EMAC_TX_OCTET_COUNT_GOOD_OFFSET                    (0x0764)
#define S32K3XX_EMAC_TX_PACKET_COUNT_GOOD_OFFSET                   (0x0768)
#define S32K3XX_EMAC_TX_EXCESSIVE_DEFERRAL_ERROR_OFFSET            (0x076c)
#define S32K3XX_EMAC_TX_PAUSE_PACKETS_OFFSET                       (0x0770)
#define S32K3XX_EMAC_TX_VLAN_PACKETS_GOOD_OFFSET                   (0x0774)
#define S32K3XX_EMAC_TX_OSIZE_PACKETS_GOOD_OFFSET                  (0x0778)
#define S32K3XX_EMAC_RX_PACKETS_COUNT_GOOD_BAD_OFFSET              (0x0780)
#define S32K3XX_EMAC_RX_OCTET_COUNT_GOOD_BAD_OFFSET                (0x0784)
#define S32K3XX_EMAC_RX_OCTET_COUNT_GOOD_OFFSET                    (0x0788)
#define S32K3XX_EMAC_RX_BROADCAST_PACKETS_GOOD_OFFSET              (0x078c)
#define S32K3XX_EMAC_RX_MULTICAST_PACKETS_GOOD_OFFSET              (0x0790)
#define S32K3XX_EMAC_RX_CRC_ERROR_PACKETS_OFFSET                   (0x0794)
#define S32K3XX_EMAC_RX_ALIGNMENT_ERROR_PACKETS_OFFSET             (0x0798)
#define S32K3XX_EMAC_RX_RUNT_ERROR_PACKETS_OFFSET                  (0x079c)
#define S32K3XX_EMAC_RX_JABBER_ERROR_PACKETS_OFFSET                (0x07a0)
#define S32K3XX_EMAC_RX_UNDERSIZE_PACKETS_GOOD_OFFSET              (0x07a4)
#define S32K3XX_EMAC_RX_OVERSIZE_PACKETS_GOOD_OFFSET               (0x07a8)
#define S32K3XX_EMAC_RX_64OCTETS_PACKETS_GOOD_BAD_OFFSET           (0x07ac)
#define S32K3XX_EMAC_RX_65TO127OCTETS_PACKETS_GOOD_BAD_OFFSET      (0x07b0)
#define S32K3XX_EMAC_RX_128TO255OCTETS_PACKETS_GOOD_BAD_OFFSET     (0x07b4)
#define S32K3XX_EMAC_RX_256TO511OCTETS_PACKETS_GOOD_BAD_OFFSET     (0x07b8)
#define S32K3XX_EMAC_RX_512TO1023OCTETS_PACKETS_GOOD_BAD_OFFSET    (0x07bc)
#define S32K3XX_EMAC_RX_1024TOMAXOCTETS_PACKETS_GOOD_BAD_OFFSET    (0x07c0)
#define S32K3XX_EMAC_RX_UNICAST_PACKETS_GOOD_OFFSET                (0x07c4)
#define S32K3XX_EMAC_RX_LENGTH_ERROR_PACKETS_OFFSET                (0x07c8)
#define S32K3XX_EMAC_RX_OUT_OF_RANGE_TYPE_PACKETS_OFFSET           (0x07cc)
#define S32K3XX_EMAC_RX_PAUSE_PACKETS_OFFSET                       (0x07d0)
#define S32K3XX_EMAC_RX_FIFO_OVERFLOW_PACKETS_OFFSET               (0x07d4)
#define S32K3XX_EMAC_RX_VLAN_PACKETS_GOOD_BAD_OFFSET               (0x07d8)
#define S32K3XX_EMAC_RX_WATCHDOG_ERROR_PACKETS_OFFSET              (0x07dc)
#define S32K3XX_EMAC_RX_RECEIVE_ERROR_PACKETS_OFFSET               (0x07e0)
#define S32K3XX_EMAC_RX_CONTROL_PACKETS_GOOD_OFFSET                (0x07e4)
#define S32K3XX_EMAC_MMC_FPE_TX_INTERRUPT_OFFSET                   (0x08a0)
#define S32K3XX_EMAC_MMC_FPE_TX_INTERRUPT_MASK_OFFSET              (0x08a4)
#define S32K3XX_EMAC_MMC_TX_FPE_FRAGMENT_CNTR_OFFSET               (0x08a8)
#define S32K3XX_EMAC_MMC_TX_HOLD_REQ_CNTR_OFFSET                   (0x08ac)
#define S32K3XX_EMAC_MMC_FPE_RX_INTERRUPT_OFFSET                   (0x08c0)
#define S32K3XX_EMAC_MMC_FPE_RX_INTERRUPT_MASK_OFFSET              (0x08c4)
#define S32K3XX_EMAC_MMC_RX_PACKET_ASSEMBLY_ERR_CNTR_OFFSET        (0x08c8)
#define S32K3XX_EMAC_MMC_RX_PACKET_SMD_ERR_CNTR_OFFSET             (0x08cc)
#define S32K3XX_EMAC_MMC_RX_PACKET_ASSEMBLY_OK_CNTR_OFFSET         (0x08d0)
#define S32K3XX_EMAC_MMC_RX_FPE_FRAGMENT_CNTR_OFFSET               (0x08d4)
#define S32K3XX_EMAC_MAC_L3_L4_CONTROL0_OFFSET                     (0x0900)
#define S32K3XX_EMAC_MAC_LAYER4_ADDRESS0_OFFSET                    (0x0904)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR0_REG0_OFFSET                  (0x0910)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR1_REG0_OFFSET                  (0x0914)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR2_REG0_OFFSET                  (0x0918)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR3_REG0_OFFSET                  (0x091c)
#define S32K3XX_EMAC_MAC_L3_L4_CONTROL1_OFFSET                     (0x0930)
#define S32K3XX_EMAC_MAC_LAYER4_ADDRESS1_OFFSET                    (0x0934)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR0_REG1_OFFSET                  (0x0940)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR1_REG1_OFFSET                  (0x0944)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR2_REG1_OFFSET                  (0x0948)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR3_REG1_OFFSET                  (0x094c)
#define S32K3XX_EMAC_MAC_L3_L4_CONTROL2_OFFSET                     (0x0960)
#define S32K3XX_EMAC_MAC_LAYER4_ADDRESS2_OFFSET                    (0x0964)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR0_REG2_OFFSET                  (0x0970)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR1_REG2_OFFSET                  (0x0974)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR2_REG2_OFFSET                  (0x0978)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR3_REG2_OFFSET                  (0x097c)
#define S32K3XX_EMAC_MAC_L3_L4_CONTROL3_OFFSET                     (0x0990)
#define S32K3XX_EMAC_MAC_LAYER4_ADDRESS3_OFFSET                    (0x0994)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR0_REG3_OFFSET                  (0x09a0)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR1_REG3_OFFSET                  (0x09a4)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR2_REG3_OFFSET                  (0x09a8)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR3_REG3_OFFSET                  (0x09ac)
#define S32K3XX_EMAC_MAC_TIMESTAMP_CONTROL_OFFSET                  (0x0b00)
#define S32K3XX_EMAC_MAC_SUB_SECOND_INCREMENT_OFFSET               (0x0b04)
#define S32K3XX_EMAC_MAC_SYSTEM_TIME_SECONDS_OFFSET                (0x0b08)
#define S32K3XX_EMAC_MAC_SYSTEM_TIME_NANOSECONDS_OFFSET            (0x0b0c)
#define S32K3XX_EMAC_MAC_SYSTEM_TIME_SECONDS_UPDATE_OFFSET         (0x0b10)
#define S32K3XX_EMAC_MAC_SYSTEM_TIME_NANOSECONDS_UPDATE_OFFSET     (0x0b14)
#define S32K3XX_EMAC_MAC_TIMESTAMP_ADDEND_OFFSET                   (0x0b18)
#define S32K3XX_EMAC_MAC_SYSTEM_TIME_HIGHER_WORD_SECONDS_OFFSET    (0x0b1c)
#define S32K3XX_EMAC_MAC_TIMESTAMP_STATUS_OFFSET                   (0x0b20)
#define S32K3XX_EMAC_MAC_TX_TIMESTAMP_STATUS_NANOSECONDS_OFFSET    (0x0b30)
#define S32K3XX_EMAC_MAC_TX_TIMESTAMP_STATUS_SECONDS_OFFSET        (0x0b34)
#define S32K3XX_EMAC_MAC_TIMESTAMP_INGRESS_ASYM_CORR_OFFSET        (0x0b50)
#define S32K3XX_EMAC_MAC_TIMESTAMP_EGRESS_ASYM_CORR_OFFSET         (0x0b54)
#define S32K3XX_EMAC_MAC_TIMESTAMP_INGRESS_CORR_NANOSECOND_OFFSET  (0x0b58)
#define S32K3XX_EMAC_MAC_TIMESTAMP_EGRESS_CORR_NANOSECOND_OFFSET   (0x0b5c)
#define S32K3XX_EMAC_MAC_TIMESTAMP_INGRESS_CORR_SUBNANOSEC_OFFSET  (0x0b60)
#define S32K3XX_EMAC_MAC_TIMESTAMP_EGRESS_CORR_SUBNANOSEC_OFFSET   (0x0b64)
#define S32K3XX_EMAC_MAC_TIMESTAMP_INGRESS_LATENCY_OFFSET          (0x0b68)
#define S32K3XX_EMAC_MAC_TIMESTAMP_EGRESS_LATENCY_OFFSET           (0x0b6c)
#define S32K3XX_EMAC_MAC_PPS_CONTROL_OFFSET                        (0x0b70)
#define S32K3XX_EMAC_MAC_PPS0_TARGET_TIME_SECONDS_OFFSET           (0x0b80)
#define S32K3XX_EMAC_MAC_PPS0_TARGET_TIME_NANOSECONDS_OFFSET       (0x0b84)
#define S32K3XX_EMAC_MAC_PPS0_INTERVAL_OFFSET                      (0x0b88)
#define S32K3XX_EMAC_MAC_PPS0_WIDTH_OFFSET                         (0x0b8c)
#define S32K3XX_EMAC_MAC_PPS1_TARGET_TIME_SECONDS_OFFSET           (0x0b90)
#define S32K3XX_EMAC_MAC_PPS1_TARGET_TIME_NANOSECONDS_OFFSET       (0x0b94)
#define S32K3XX_EMAC_MAC_PPS1_INTERVAL_OFFSET                      (0x0b98)
#define S32K3XX_EMAC_MAC_PPS1_WIDTH_OFFSET                         (0x0b9c)
#define S32K3XX_EMAC_MAC_PPS2_TARGET_TIME_SECONDS_OFFSET           (0x0ba0)
#define S32K3XX_EMAC_MAC_PPS2_TARGET_TIME_NANOSECONDS_OFFSET       (0x0ba4)
#define S32K3XX_EMAC_MAC_PPS2_INTERVAL_OFFSET                      (0x0ba8)
#define S32K3XX_EMAC_MAC_PPS2_WIDTH_OFFSET                         (0x0bac)
#define S32K3XX_EMAC_MAC_PPS3_TARGET_TIME_SECONDS_OFFSET           (0x0bb0)
#define S32K3XX_EMAC_MAC_PPS3_TARGET_TIME_NANOSECONDS_OFFSET       (0x0bb4)
#define S32K3XX_EMAC_MAC_PPS3_INTERVAL_OFFSET                      (0x0bb8)
#define S32K3XX_EMAC_MAC_PPS3_WIDTH_OFFSET                         (0x0bbc)
#define S32K3XX_EMAC_MTL_OPERATION_MODE_OFFSET                     (0x0c00)
#define S32K3XX_EMAC_MTL_DBG_CTL_OFFSET                            (0x0c08)
#define S32K3XX_EMAC_MTL_DBG_STS_OFFSET                            (0x0c0c)
#define S32K3XX_EMAC_MTL_FIFO_DEBUG_DATA_OFFSET                    (0x0c10)
#define S32K3XX_EMAC_MTL_INTERRUPT_STATUS_OFFSET                   (0x0c20)
#define S32K3XX_EMAC_MTL_RXQ_DMA_MAP0_OFFSET                       (0x0c30)
#define S32K3XX_EMAC_MTL_TBS_CTRL_OFFSET                           (0x0c40)
#define S32K3XX_EMAC_MTL_EST_CONTROL_OFFSET                        (0x0c50)
#define S32K3XX_EMAC_MTL_EST_STATUS_OFFSET                         (0x0c58)
#define S32K3XX_EMAC_MTL_EST_SCH_ERROR_OFFSET                      (0x0c60)
#define S32K3XX_EMAC_MTL_EST_FRM_SIZE_ERROR_OFFSET                 (0x0c64)
#define S32K3XX_EMAC_MTL_EST_FRM_SIZE_CAPTURE_OFFSET               (0x0c68)
#define S32K3XX_EMAC_MTL_EST_INTR_ENABLE_OFFSET                    (0x0c70)
#define S32K3XX_EMAC_MTL_EST_GCL_CONTROL_OFFSET                    (0x0c80)
#define S32K3XX_EMAC_MTL_EST_GCL_DATA_OFFSET                       (0x0c84)
#define S32K3XX_EMAC_MTL_FPE_CTRL_STS_OFFSET                       (0x0c90)
#define S32K3XX_EMAC_MTL_FPE_ADVANCE_OFFSET                        (0x0c94)
#define S32K3XX_EMAC_MTL_RXP_CONTROL_STATUS_OFFSET                 (0x0ca0)
#define S32K3XX_EMAC_MTL_RXP_INTERRUPT_CONTROL_STATUS_OFFSET       (0x0ca4)
#define S32K3XX_EMAC_MTL_RXP_DROP_CNT_OFFSET                       (0x0ca8)
#define S32K3XX_EMAC_MTL_RXP_ERROR_CNT_OFFSET                      (0x0cac)
#define S32K3XX_EMAC_MTL_RXP_INDIRECT_ACC_CONTROL_STATUS_OFFSET    (0x0cb0)
#define S32K3XX_EMAC_MTL_RXP_INDIRECT_ACC_DATA_OFFSET              (0x0cb4)
#define S32K3XX_EMAC_MTL_ECC_CONTROL_OFFSET                        (0x0cc0)
#define S32K3XX_EMAC_MTL_SAFETY_INTERRUPT_STATUS_OFFSET            (0x0cc4)
#define S32K3XX_EMAC_MTL_ECC_INTERRUPT_ENABLE_OFFSET               (0x0cc8)
#define S32K3XX_EMAC_MTL_ECC_INTERRUPT_STATUS_OFFSET               (0x0ccc)
#define S32K3XX_EMAC_MTL_ECC_ERR_STS_RCTL_OFFSET                   (0x0cd0)
#define S32K3XX_EMAC_MTL_ECC_ERR_ADDR_STATUS_OFFSET                (0x0cd4)
#define S32K3XX_EMAC_MTL_ECC_ERR_CNTR_STATUS_OFFSET                (0x0cd8)
#define S32K3XX_EMAC_MTL_DPP_CONTROL_OFFSET                        (0x0ce0)
#define S32K3XX_EMAC_MTL_TXQ0_OPERATION_MODE_OFFSET                (0x0d00)
#define S32K3XX_EMAC_MTL_TXQ0_UNDERFLOW_OFFSET                     (0x0d04)
#define S32K3XX_EMAC_MTL_TXQ0_DEBUG_OFFSET                         (0x0d08)
#define S32K3XX_EMAC_MTL_TXQ0_ETS_STATUS_OFFSET                    (0x0d14)
#define S32K3XX_EMAC_MTL_TXQ0_QUANTUM_WEIGHT_OFFSET                (0x0d18)
#define S32K3XX_EMAC_MTL_Q0_INTERRUPT_CONTROL_STATUS_OFFSET        (0x0d2c)
#define S32K3XX_EMAC_MTL_RXQ0_OPERATION_MODE_OFFSET                (0x0d30)
#define S32K3XX_EMAC_MTL_RXQ0_MISSED_PACKET_OVERFLOW_CNT_OFFSET    (0x0d34)
#define S32K3XX_EMAC_MTL_RXQ0_DEBUG_OFFSET                         (0x0d38)
#define S32K3XX_EMAC_MTL_RXQ0_CONTROL_OFFSET                       (0x0d3c)
#define S32K3XX_EMAC_MTL_TXQ1_OPERATION_MODE_OFFSET                (0x0d40)
#define S32K3XX_EMAC_MTL_TXQ1_UNDERFLOW_OFFSET                     (0x0d44)
#define S32K3XX_EMAC_MTL_TXQ1_DEBUG_OFFSET                         (0x0d48)
#define S32K3XX_EMAC_MTL_TXQ1_ETS_CONTROL_OFFSET                   (0x0d50)
#define S32K3XX_EMAC_MTL_TXQ1_ETS_STATUS_OFFSET                    (0x0d54)
#define S32K3XX_EMAC_MTL_TXQ1_QUANTUM_WEIGHT_OFFSET                (0x0d58)
#define S32K3XX_EMAC_MTL_TXQ1_SENDSLOPECREDIT_OFFSET               (0x0d5c)
#define S32K3XX_EMAC_MTL_TXQ1_HICREDIT_OFFSET                      (0x0d60)
#define S32K3XX_EMAC_MTL_TXQ1_LOCREDIT_OFFSET                      (0x0d64)
#define S32K3XX_EMAC_MTL_Q1_INTERRUPT_CONTROL_STATUS_OFFSET        (0x0d6c)
#define S32K3XX_EMAC_MTL_RXQ1_OPERATION_MODE_OFFSET                (0x0d70)
#define S32K3XX_EMAC_MTL_RXQ1_MISSED_PACKET_OVERFLOW_CNT_OFFSET    (0x0d74)
#define S32K3XX_EMAC_MTL_RXQ1_DEBUG_OFFSET                         (0x0d78)
#define S32K3XX_EMAC_MTL_RXQ1_CONTROL_OFFSET                       (0x0d7c)
#define S32K3XX_EMAC_DMA_MODE_OFFSET                               (0x1000)
#define S32K3XX_EMAC_DMA_SYSBUS_MODE_OFFSET                        (0x1004)
#define S32K3XX_EMAC_DMA_INTERRUPT_STATUS_OFFSET                   (0x1008)
#define S32K3XX_EMAC_DMA_DEBUG_STATUS0_OFFSET                      (0x100c)
#define S32K3XX_EMAC_DMA_TBS_CTRL_OFFSET                           (0x1050)
#define S32K3XX_EMAC_DMA_SAFETY_INTERRUPT_STATUS_OFFSET            (0x1080)
#define S32K3XX_EMAC_DMA_CH0_CONTROL_OFFSET                        (0x1100)
#define S32K3XX_EMAC_DMA_CH0_TX_CONTROL_OFFSET                     (0x1104)
#define S32K3XX_EMAC_DMA_CH0_RX_CONTROL_OFFSET                     (0x1108)
#define S32K3XX_EMAC_DMA_CH0_TXDESC_LIST_ADDRESS_OFFSET            (0x1114)
#define S32K3XX_EMAC_DMA_CH0_RXDESC_LIST_ADDRESS_OFFSET            (0x111c)
#define S32K3XX_EMAC_DMA_CH0_TXDESC_TAIL_POINTER_OFFSET            (0x1120)
#define S32K3XX_EMAC_DMA_CH0_RXDESC_TAIL_POINTER_OFFSET            (0x1128)
#define S32K3XX_EMAC_DMA_CH0_TXDESC_RING_LENGTH_OFFSET             (0x112c)
#define S32K3XX_EMAC_DMA_CH0_RXDESC_RING_LENGTH_OFFSET             (0x1130)
#define S32K3XX_EMAC_DMA_CH0_INTERRUPT_ENABLE_OFFSET               (0x1134)
#define S32K3XX_EMAC_DMA_CH0_RX_INTERRUPT_WATCHDOG_TIMER_OFFSET    (0x1138)
#define S32K3XX_EMAC_DMA_CH0_SLOT_FUNCTION_CONTROL_STATUS_OFFSET   (0x113c)
#define S32K3XX_EMAC_DMA_CH0_CURRENT_APP_TXDESC_OFFSET             (0x1144)
#define S32K3XX_EMAC_DMA_CH0_CURRENT_APP_RXDESC_OFFSET             (0x114c)
#define S32K3XX_EMAC_DMA_CH0_CURRENT_APP_TXBUFFER_OFFSET           (0x1154)
#define S32K3XX_EMAC_DMA_CH0_CURRENT_APP_RXBUFFER_OFFSET           (0x115c)
#define S32K3XX_EMAC_DMA_CH0_STATUS_OFFSET                         (0x1160)
#define S32K3XX_EMAC_DMA_CH0_MISS_FRAME_CNT_OFFSET                 (0x1164)
#define S32K3XX_EMAC_DMA_CH0_RXP_ACCEPT_CNT_OFFSET                 (0x1168)
#define S32K3XX_EMAC_DMA_CH0_RX_ERI_CNT_OFFSET                     (0x116c)
#define S32K3XX_EMAC_DMA_CH1_CONTROL_OFFSET                        (0x1180)
#define S32K3XX_EMAC_DMA_CH1_TX_CONTROL_OFFSET                     (0x1184)
#define S32K3XX_EMAC_DMA_CH1_RX_CONTROL_OFFSET                     (0x1188)
#define S32K3XX_EMAC_DMA_CH1_TXDESC_LIST_ADDRESS_OFFSET            (0x1194)
#define S32K3XX_EMAC_DMA_CH1_RXDESC_LIST_ADDRESS_OFFSET            (0x119c)
#define S32K3XX_EMAC_DMA_CH1_TXDESC_TAIL_POINTER_OFFSET            (0x11a0)
#define S32K3XX_EMAC_DMA_CH1_RXDESC_TAIL_POINTER_OFFSET            (0x11a8)
#define S32K3XX_EMAC_DMA_CH1_TXDESC_RING_LENGTH_OFFSET             (0x11ac)
#define S32K3XX_EMAC_DMA_CH1_RXDESC_RING_LENGTH_OFFSET             (0x11b0)
#define S32K3XX_EMAC_DMA_CH1_INTERRUPT_ENABLE_OFFSET               (0x11b4)
#define S32K3XX_EMAC_DMA_CH1_RX_INTERRUPT_WATCHDOG_TIMER_OFFSET    (0x11b8)
#define S32K3XX_EMAC_DMA_CH1_SLOT_FUNCTION_CONTROL_STATUS_OFFSET   (0x11bc)
#define S32K3XX_EMAC_DMA_CH1_CURRENT_APP_TXDESC_OFFSET             (0x11c4)
#define S32K3XX_EMAC_DMA_CH1_CURRENT_APP_RXDESC_OFFSET             (0x11cc)
#define S32K3XX_EMAC_DMA_CH1_CURRENT_APP_TXBUFFER_OFFSET           (0x11d4)
#define S32K3XX_EMAC_DMA_CH1_CURRENT_APP_RXBUFFER_OFFSET           (0x11dc)
#define S32K3XX_EMAC_DMA_CH1_STATUS_OFFSET                         (0x11e0)
#define S32K3XX_EMAC_DMA_CH1_MISS_FRAME_CNT_OFFSET                 (0x11e4)
#define S32K3XX_EMAC_DMA_CH1_RXP_ACCEPT_CNT_OFFSET                 (0x11e8)
#define S32K3XX_EMAC_DMA_CH1_RX_ERI_CNT_OFFSET                     (0x11ec)

/* EMAC Register Addresses **************************************************/

#define S32K3XX_EMAC_MAC_CONFIGURATION                      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_CONFIGURATION_OFFSET)
#define S32K3XX_EMAC_MAC_EXT_CONFIGURATION                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_EXT_CONFIGURATION_OFFSET)
#define S32K3XX_EMAC_MAC_PACKET_FILTER                      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PACKET_FILTER_OFFSET)
#define S32K3XX_EMAC_MAC_WATCHDOG_TIMEOUT                   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_WATCHDOG_TIMEOUT_OFFSET)
#define S32K3XX_EMAC_MAC_HASH_TABLE_REG0                    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_HASH_TABLE_REG0_OFFSET)
#define S32K3XX_EMAC_MAC_HASH_TABLE_REG1                    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_HASH_TABLE_REG1_OFFSET)
#define S32K3XX_EMAC_MAC_VLAN_TAG                           (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_VLAN_TAG_OFFSET)
#define S32K3XX_EMAC_MAC_VLAN_TAG_CTRL                      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_VLAN_TAG_CTRL_OFFSET)
#define S32K3XX_EMAC_MAC_VLAN_TAG_DATA                      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_VLAN_TAG_DATA_OFFSET)
#define S32K3XX_EMAC_MAC_VLAN_TAG_FILTER0                   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_VLAN_TAG_FILTER0_OFFSET)
#define S32K3XX_EMAC_MAC_VLAN_TAG_FILTER1                   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_VLAN_TAG_FILTER1_OFFSET)
#define S32K3XX_EMAC_MAC_VLAN_TAG_FILTER2                   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_VLAN_TAG_FILTER2_OFFSET)
#define S32K3XX_EMAC_MAC_VLAN_TAG_FILTER3                   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_VLAN_TAG_FILTER3_OFFSET)
#define S32K3XX_EMAC_MAC_VLAN_HASH_TABLE                    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_VLAN_HASH_TABLE_OFFSET)
#define S32K3XX_EMAC_MAC_VLAN_INCL                          (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_VLAN_INCL_OFFSET)
#define S32K3XX_EMAC_MAC_INNER_VLAN_INCL                    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_INNER_VLAN_INCL_OFFSET)
#define S32K3XX_EMAC_MAC_Q0_TX_FLOW_CTRL                    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_Q0_TX_FLOW_CTRL_OFFSET)
#define S32K3XX_EMAC_MAC_RX_FLOW_CTRL                       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_RX_FLOW_CTRL_OFFSET)
#define S32K3XX_EMAC_MAC_RXQ_CTRL4                          (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_RXQ_CTRL4_OFFSET)
#define S32K3XX_EMAC_MAC_RXQ_CTRL0                          (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_RXQ_CTRL0_OFFSET)
#define S32K3XX_EMAC_MAC_RXQ_CTRL1                          (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_RXQ_CTRL1_OFFSET)
#define S32K3XX_EMAC_MAC_RXQ_CTRL2                          (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_RXQ_CTRL2_OFFSET)
#define S32K3XX_EMAC_MAC_INTERRUPT_STATUS                   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_INTERRUPT_STATUS_OFFSET)
#define S32K3XX_EMAC_MAC_INTERRUPT_ENABLE                   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_INTERRUPT_ENABLE_OFFSET)
#define S32K3XX_EMAC_MAC_RX_TX_STATUS                       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_RX_TX_STATUS_OFFSET)
#define S32K3XX_EMAC_MAC_VERSION                            (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_VERSION_OFFSET)
#define S32K3XX_EMAC_MAC_DEBUG                              (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_DEBUG_OFFSET)
#define S32K3XX_EMAC_MAC_HW_FEATURE0                        (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_HW_FEATURE0_OFFSET)
#define S32K3XX_EMAC_MAC_HW_FEATURE1                        (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_HW_FEATURE1_OFFSET)
#define S32K3XX_EMAC_MAC_HW_FEATURE2                        (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_HW_FEATURE2_OFFSET)
#define S32K3XX_EMAC_MAC_HW_FEATURE3                        (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_HW_FEATURE3_OFFSET)
#define S32K3XX_EMAC_MAC_DPP_FSM_INTERRUPT_STATUS           (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_DPP_FSM_INTERRUPT_STATUS_OFFSET)
#define S32K3XX_EMAC_MAC_FSM_CONTROL                        (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_FSM_CONTROL_OFFSET)
#define S32K3XX_EMAC_MAC_FSM_ACT_TIMER                      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_FSM_ACT_TIMER_OFFSET)
#define S32K3XX_EMAC_SCS_REG1                               (S32K3XX_EMAC_BASE + S32K3XX_EMAC_SCS_REG1_OFFSET)
#define S32K3XX_EMAC_MAC_MDIO_ADDRESS                       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_MDIO_ADDRESS_OFFSET)
#define S32K3XX_EMAC_MAC_MDIO_DATA                          (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_MDIO_DATA_OFFSET)
#define S32K3XX_EMAC_MAC_CSR_SW_CTRL                        (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_CSR_SW_CTRL_OFFSET)
#define S32K3XX_EMAC_MAC_FPE_CTRL_STS                       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_FPE_CTRL_STS_OFFSET)
#define S32K3XX_EMAC_MAC_PRESN_TIME_NS                      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PRESN_TIME_NS_OFFSET)
#define S32K3XX_EMAC_MAC_PRESN_TIME_UPDT                    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PRESN_TIME_UPDT_OFFSET)
#define S32K3XX_EMAC_MAC_ADDRESS0_HIGH                      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_ADDRESS0_HIGH_OFFSET)
#define S32K3XX_EMAC_MAC_ADDRESS0_LOW                       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_ADDRESS0_LOW_OFFSET)
#define S32K3XX_EMAC_MAC_ADDRESS1_HIGH                      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_ADDRESS1_HIGH_OFFSET)
#define S32K3XX_EMAC_MAC_ADDRESS1_LOW                       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_ADDRESS1_LOW_OFFSET)
#define S32K3XX_EMAC_MAC_ADDRESS2_HIGH                      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_ADDRESS2_HIGH_OFFSET)
#define S32K3XX_EMAC_MAC_ADDRESS2_LOW                       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_ADDRESS2_LOW_OFFSET)
#define S32K3XX_EMAC_MMC_CONTROL                            (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MMC_CONTROL_OFFSET)
#define S32K3XX_EMAC_MMC_RX_INTERRUPT                       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MMC_RX_INTERRUPT_OFFSET)
#define S32K3XX_EMAC_MMC_TX_INTERRUPT                       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MMC_TX_INTERRUPT_OFFSET)
#define S32K3XX_EMAC_MMC_RX_INTERRUPT_MASK                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MMC_RX_INTERRUPT_MASK_OFFSET)
#define S32K3XX_EMAC_MMC_TX_INTERRUPT_MASK                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MMC_TX_INTERRUPT_MASK_OFFSET)
#define S32K3XX_EMAC_TX_OCTET_COUNT_GOOD_BAD                (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_OCTET_COUNT_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_TX_PACKET_COUNT_GOOD_BAD               (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_PACKET_COUNT_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_TX_BROADCAST_PACKETS_GOOD              (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_BROADCAST_PACKETS_GOOD_OFFSET)
#define S32K3XX_EMAC_TX_MULTICAST_PACKETS_GOOD              (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_MULTICAST_PACKETS_GOOD_OFFSET)
#define S32K3XX_EMAC_TX_64OCTETS_PACKETS_GOOD_BAD           (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_64OCTETS_PACKETS_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_TX_65TO127OCTETS_PACKETS_GOOD_BAD      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_65TO127OCTETS_PACKETS_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_TX_128TO255OCTETS_PACKETS_GOOD_BAD     (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_128TO255OCTETS_PACKETS_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_TX_256TO511OCTETS_PACKETS_GOOD_BAD     (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_256TO511OCTETS_PACKETS_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_TX_512TO1023OCTETS_PACKETS_GOOD_BAD    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_512TO1023OCTETS_PACKETS_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_TX_1024TOMAXOCTETS_PACKETS_GOOD_BAD    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_1024TOMAXOCTETS_PACKETS_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_TX_UNICAST_PACKETS_GOOD_BAD            (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_UNICAST_PACKETS_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_TX_MULTICAST_PACKETS_GOOD_BAD          (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_MULTICAST_PACKETS_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_TX_BROADCAST_PACKETS_GOOD_BAD          (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_BROADCAST_PACKETS_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_TX_UNDERFLOW_ERROR_PACKETS             (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_UNDERFLOW_ERROR_PACKETS_OFFSET)
#define S32K3XX_EMAC_TX_SINGLE_COLLISION_GOOD_PACKETS       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_SINGLE_COLLISION_GOOD_PACKETS_OFFSET)
#define S32K3XX_EMAC_TX_MULTIPLE_COLLISION_GOOD_PACKETS     (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_MULTIPLE_COLLISION_GOOD_PACKETS_OFFSET)
#define S32K3XX_EMAC_TX_DEFERRED_PACKETS                    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_DEFERRED_PACKETS_OFFSET)
#define S32K3XX_EMAC_TX_LATE_COLLISION_PACKETS              (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_LATE_COLLISION_PACKETS_OFFSET)
#define S32K3XX_EMAC_TX_EXCESSIVE_COLLISION_PACKETS         (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_EXCESSIVE_COLLISION_PACKETS_OFFSET)
#define S32K3XX_EMAC_TX_CARRIER_ERROR_PACKETS               (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_CARRIER_ERROR_PACKETS_OFFSET)
#define S32K3XX_EMAC_TX_OCTET_COUNT_GOOD                    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_OCTET_COUNT_GOOD_OFFSET)
#define S32K3XX_EMAC_TX_PACKET_COUNT_GOOD                   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_PACKET_COUNT_GOOD_OFFSET)
#define S32K3XX_EMAC_TX_EXCESSIVE_DEFERRAL_ERROR            (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_EXCESSIVE_DEFERRAL_ERROR_OFFSET)
#define S32K3XX_EMAC_TX_PAUSE_PACKETS                       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_PAUSE_PACKETS_OFFSET)
#define S32K3XX_EMAC_TX_VLAN_PACKETS_GOOD                   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_VLAN_PACKETS_GOOD_OFFSET)
#define S32K3XX_EMAC_TX_OSIZE_PACKETS_GOOD                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_TX_OSIZE_PACKETS_GOOD_OFFSET)
#define S32K3XX_EMAC_RX_PACKETS_COUNT_GOOD_BAD              (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_PACKETS_COUNT_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_RX_OCTET_COUNT_GOOD_BAD                (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_OCTET_COUNT_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_RX_OCTET_COUNT_GOOD                    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_OCTET_COUNT_GOOD_OFFSET)
#define S32K3XX_EMAC_RX_BROADCAST_PACKETS_GOOD              (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_BROADCAST_PACKETS_GOOD_OFFSET)
#define S32K3XX_EMAC_RX_MULTICAST_PACKETS_GOOD              (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_MULTICAST_PACKETS_GOOD_OFFSET)
#define S32K3XX_EMAC_RX_CRC_ERROR_PACKETS                   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_CRC_ERROR_PACKETS_OFFSET)
#define S32K3XX_EMAC_RX_ALIGNMENT_ERROR_PACKETS             (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_ALIGNMENT_ERROR_PACKETS_OFFSET)
#define S32K3XX_EMAC_RX_RUNT_ERROR_PACKETS                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_RUNT_ERROR_PACKETS_OFFSET)
#define S32K3XX_EMAC_RX_JABBER_ERROR_PACKETS                (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_JABBER_ERROR_PACKETS_OFFSET)
#define S32K3XX_EMAC_RX_UNDERSIZE_PACKETS_GOOD              (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_UNDERSIZE_PACKETS_GOOD_OFFSET)
#define S32K3XX_EMAC_RX_OVERSIZE_PACKETS_GOOD               (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_OVERSIZE_PACKETS_GOOD_OFFSET)
#define S32K3XX_EMAC_RX_64OCTETS_PACKETS_GOOD_BAD           (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_64OCTETS_PACKETS_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_RX_65TO127OCTETS_PACKETS_GOOD_BAD      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_65TO127OCTETS_PACKETS_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_RX_128TO255OCTETS_PACKETS_GOOD_BAD     (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_128TO255OCTETS_PACKETS_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_RX_256TO511OCTETS_PACKETS_GOOD_BAD     (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_256TO511OCTETS_PACKETS_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_RX_512TO1023OCTETS_PACKETS_GOOD_BAD    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_512TO1023OCTETS_PACKETS_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_RX_1024TOMAXOCTETS_PACKETS_GOOD_BAD    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_1024TOMAXOCTETS_PACKETS_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_RX_UNICAST_PACKETS_GOOD                (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_UNICAST_PACKETS_GOOD_OFFSET)
#define S32K3XX_EMAC_RX_LENGTH_ERROR_PACKETS                (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_LENGTH_ERROR_PACKETS_OFFSET)
#define S32K3XX_EMAC_RX_OUT_OF_RANGE_TYPE_PACKETS           (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_OUT_OF_RANGE_TYPE_PACKETS_OFFSET)
#define S32K3XX_EMAC_RX_PAUSE_PACKETS                       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_PAUSE_PACKETS_OFFSET)
#define S32K3XX_EMAC_RX_FIFO_OVERFLOW_PACKETS               (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_FIFO_OVERFLOW_PACKETS_OFFSET)
#define S32K3XX_EMAC_RX_VLAN_PACKETS_GOOD_BAD               (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_VLAN_PACKETS_GOOD_BAD_OFFSET)
#define S32K3XX_EMAC_RX_WATCHDOG_ERROR_PACKETS              (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_WATCHDOG_ERROR_PACKETS_OFFSET)
#define S32K3XX_EMAC_RX_RECEIVE_ERROR_PACKETS               (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_RECEIVE_ERROR_PACKETS_OFFSET)
#define S32K3XX_EMAC_RX_CONTROL_PACKETS_GOOD                (S32K3XX_EMAC_BASE + S32K3XX_EMAC_RX_CONTROL_PACKETS_GOOD_OFFSET)
#define S32K3XX_EMAC_MMC_FPE_TX_INTERRUPT                   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MMC_FPE_TX_INTERRUPT_OFFSET)
#define S32K3XX_EMAC_MMC_FPE_TX_INTERRUPT_MASK              (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MMC_FPE_TX_INTERRUPT_MASK_OFFSET)
#define S32K3XX_EMAC_MMC_TX_FPE_FRAGMENT_CNTR               (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MMC_TX_FPE_FRAGMENT_CNTR_OFFSET)
#define S32K3XX_EMAC_MMC_TX_HOLD_REQ_CNTR                   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MMC_TX_HOLD_REQ_CNTR_OFFSET)
#define S32K3XX_EMAC_MMC_FPE_RX_INTERRUPT                   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MMC_FPE_RX_INTERRUPT_OFFSET)
#define S32K3XX_EMAC_MMC_FPE_RX_INTERRUPT_MASK              (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MMC_FPE_RX_INTERRUPT_MASK_OFFSET)
#define S32K3XX_EMAC_MMC_RX_PACKET_ASSEMBLY_ERR_CNTR        (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MMC_RX_PACKET_ASSEMBLY_ERR_CNTR_OFFSET)
#define S32K3XX_EMAC_MMC_RX_PACKET_SMD_ERR_CNTR             (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MMC_RX_PACKET_SMD_ERR_CNTR_OFFSET)
#define S32K3XX_EMAC_MMC_RX_PACKET_ASSEMBLY_OK_CNTR         (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MMC_RX_PACKET_ASSEMBLY_OK_CNTR_OFFSET)
#define S32K3XX_EMAC_MMC_RX_FPE_FRAGMENT_CNTR               (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MMC_RX_FPE_FRAGMENT_CNTR_OFFSET)
#define S32K3XX_EMAC_MAC_L3_L4_CONTROL0                     (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_L3_L4_CONTROL0_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER4_ADDRESS0                    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER4_ADDRESS0_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR0_REG0                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER3_ADDR0_REG0_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR1_REG0                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER3_ADDR1_REG0_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR2_REG0                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER3_ADDR2_REG0_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR3_REG0                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER3_ADDR3_REG0_OFFSET)
#define S32K3XX_EMAC_MAC_L3_L4_CONTROL1                     (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_L3_L4_CONTROL1_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER4_ADDRESS1                    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER4_ADDRESS1_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR0_REG1                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER3_ADDR0_REG1_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR1_REG1                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER3_ADDR1_REG1_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR2_REG1                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER3_ADDR2_REG1_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR3_REG1                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER3_ADDR3_REG1_OFFSET)
#define S32K3XX_EMAC_MAC_L3_L4_CONTROL2                     (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_L3_L4_CONTROL2_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER4_ADDRESS2                    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER4_ADDRESS2_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR0_REG2                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER3_ADDR0_REG2_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR1_REG2                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER3_ADDR1_REG2_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR2_REG2                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER3_ADDR2_REG2_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR3_REG2                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER3_ADDR3_REG2_OFFSET)
#define S32K3XX_EMAC_MAC_L3_L4_CONTROL3                     (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_L3_L4_CONTROL3_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER4_ADDRESS3                    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER4_ADDRESS3_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR0_REG3                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER3_ADDR0_REG3_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR1_REG3                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER3_ADDR1_REG3_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR2_REG3                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER3_ADDR2_REG3_OFFSET)
#define S32K3XX_EMAC_MAC_LAYER3_ADDR3_REG3                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_LAYER3_ADDR3_REG3_OFFSET)
#define S32K3XX_EMAC_MAC_TIMESTAMP_CONTROL                  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_TIMESTAMP_CONTROL_OFFSET)
#define S32K3XX_EMAC_MAC_SUB_SECOND_INCREMENT               (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_SUB_SECOND_INCREMENT_OFFSET)
#define S32K3XX_EMAC_MAC_SYSTEM_TIME_SECONDS                (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_SYSTEM_TIME_SECONDS_OFFSET)
#define S32K3XX_EMAC_MAC_SYSTEM_TIME_NANOSECONDS            (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_SYSTEM_TIME_NANOSECONDS_OFFSET)
#define S32K3XX_EMAC_MAC_SYSTEM_TIME_SECONDS_UPDATE         (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_SYSTEM_TIME_SECONDS_UPDATE_OFFSET)
#define S32K3XX_EMAC_MAC_SYSTEM_TIME_NANOSECONDS_UPDATE     (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_SYSTEM_TIME_NANOSECONDS_UPDATE_OFFSET)
#define S32K3XX_EMAC_MAC_TIMESTAMP_ADDEND                   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_TIMESTAMP_ADDEND_OFFSET)
#define S32K3XX_EMAC_MAC_SYSTEM_TIME_HIGHER_WORD_SECONDS    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_SYSTEM_TIME_HIGHER_WORD_SECONDS_OFFSET)
#define S32K3XX_EMAC_MAC_TIMESTAMP_STATUS                   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_TIMESTAMP_STATUS_OFFSET)
#define S32K3XX_EMAC_MAC_TX_TIMESTAMP_STATUS_NANOSECONDS    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_TX_TIMESTAMP_STATUS_NANOSECONDS_OFFSET)
#define S32K3XX_EMAC_MAC_TX_TIMESTAMP_STATUS_SECONDS        (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_TX_TIMESTAMP_STATUS_SECONDS_OFFSET)
#define S32K3XX_EMAC_MAC_TIMESTAMP_INGRESS_ASYM_CORR        (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_TIMESTAMP_INGRESS_ASYM_CORR_OFFSET)
#define S32K3XX_EMAC_MAC_TIMESTAMP_EGRESS_ASYM_CORR         (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_TIMESTAMP_EGRESS_ASYM_CORR_OFFSET)
#define S32K3XX_EMAC_MAC_TIMESTAMP_INGRESS_CORR_NANOSECOND  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_TIMESTAMP_INGRESS_CORR_NANOSECOND_OFFSET)
#define S32K3XX_EMAC_MAC_TIMESTAMP_EGRESS_CORR_NANOSECOND   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_TIMESTAMP_EGRESS_CORR_NANOSECOND_OFFSET)
#define S32K3XX_EMAC_MAC_TIMESTAMP_INGRESS_CORR_SUBNANOSEC  (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_TIMESTAMP_INGRESS_CORR_SUBNANOSEC_OFFSET)
#define S32K3XX_EMAC_MAC_TIMESTAMP_EGRESS_CORR_SUBNANOSEC   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_TIMESTAMP_EGRESS_CORR_SUBNANOSEC_OFFSET)
#define S32K3XX_EMAC_MAC_TIMESTAMP_INGRESS_LATENCY          (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_TIMESTAMP_INGRESS_LATENCY_OFFSET)
#define S32K3XX_EMAC_MAC_TIMESTAMP_EGRESS_LATENCY           (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_TIMESTAMP_EGRESS_LATENCY_OFFSET)
#define S32K3XX_EMAC_MAC_PPS_CONTROL                        (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PPS_CONTROL_OFFSET)
#define S32K3XX_EMAC_MAC_PPS0_TARGET_TIME_SECONDS           (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PPS0_TARGET_TIME_SECONDS_OFFSET)
#define S32K3XX_EMAC_MAC_PPS0_TARGET_TIME_NANOSECONDS       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PPS0_TARGET_TIME_NANOSECONDS_OFFSET)
#define S32K3XX_EMAC_MAC_PPS0_INTERVAL                      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PPS0_INTERVAL_OFFSET)
#define S32K3XX_EMAC_MAC_PPS0_WIDTH                         (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PPS0_WIDTH_OFFSET)
#define S32K3XX_EMAC_MAC_PPS1_TARGET_TIME_SECONDS           (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PPS1_TARGET_TIME_SECONDS_OFFSET)
#define S32K3XX_EMAC_MAC_PPS1_TARGET_TIME_NANOSECONDS       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PPS1_TARGET_TIME_NANOSECONDS_OFFSET)
#define S32K3XX_EMAC_MAC_PPS1_INTERVAL                      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PPS1_INTERVAL_OFFSET)
#define S32K3XX_EMAC_MAC_PPS1_WIDTH                         (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PPS1_WIDTH_OFFSET)
#define S32K3XX_EMAC_MAC_PPS2_TARGET_TIME_SECONDS           (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PPS2_TARGET_TIME_SECONDS_OFFSET)
#define S32K3XX_EMAC_MAC_PPS2_TARGET_TIME_NANOSECONDS       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PPS2_TARGET_TIME_NANOSECONDS_OFFSET)
#define S32K3XX_EMAC_MAC_PPS2_INTERVAL                      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PPS2_INTERVAL_OFFSET)
#define S32K3XX_EMAC_MAC_PPS2_WIDTH                         (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PPS2_WIDTH_OFFSET)
#define S32K3XX_EMAC_MAC_PPS3_TARGET_TIME_SECONDS           (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PPS3_TARGET_TIME_SECONDS_OFFSET)
#define S32K3XX_EMAC_MAC_PPS3_TARGET_TIME_NANOSECONDS       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PPS3_TARGET_TIME_NANOSECONDS_OFFSET)
#define S32K3XX_EMAC_MAC_PPS3_INTERVAL                      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PPS3_INTERVAL_OFFSET)
#define S32K3XX_EMAC_MAC_PPS3_WIDTH                         (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MAC_PPS3_WIDTH_OFFSET)
#define S32K3XX_EMAC_MTL_OPERATION_MODE                     (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_OPERATION_MODE_OFFSET)
#define S32K3XX_EMAC_MTL_DBG_CTL                            (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_DBG_CTL_OFFSET)
#define S32K3XX_EMAC_MTL_DBG_STS                            (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_DBG_STS_OFFSET)
#define S32K3XX_EMAC_MTL_FIFO_DEBUG_DATA                    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_FIFO_DEBUG_DATA_OFFSET)
#define S32K3XX_EMAC_MTL_INTERRUPT_STATUS                   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_INTERRUPT_STATUS_OFFSET)
#define S32K3XX_EMAC_MTL_RXQ_DMA_MAP0                       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_RXQ_DMA_MAP0_OFFSET)
#define S32K3XX_EMAC_MTL_TBS_CTRL                           (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_TBS_CTRL_OFFSET)
#define S32K3XX_EMAC_MTL_EST_CONTROL                        (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_EST_CONTROL_OFFSET)
#define S32K3XX_EMAC_MTL_EST_STATUS                         (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_EST_STATUS_OFFSET)
#define S32K3XX_EMAC_MTL_EST_SCH_ERROR                      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_EST_SCH_ERROR_OFFSET)
#define S32K3XX_EMAC_MTL_EST_FRM_SIZE_ERROR                 (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_EST_FRM_SIZE_ERROR_OFFSET)
#define S32K3XX_EMAC_MTL_EST_FRM_SIZE_CAPTURE               (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_EST_FRM_SIZE_CAPTURE_OFFSET)
#define S32K3XX_EMAC_MTL_EST_INTR_ENABLE                    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_EST_INTR_ENABLE_OFFSET)
#define S32K3XX_EMAC_MTL_EST_GCL_CONTROL                    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_EST_GCL_CONTROL_OFFSET)
#define S32K3XX_EMAC_MTL_EST_GCL_DATA                       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_EST_GCL_DATA_OFFSET)
#define S32K3XX_EMAC_MTL_FPE_CTRL_STS                       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_FPE_CTRL_STS_OFFSET)
#define S32K3XX_EMAC_MTL_FPE_ADVANCE                        (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_FPE_ADVANCE_OFFSET)
#define S32K3XX_EMAC_MTL_RXP_CONTROL_STATUS                 (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_RXP_CONTROL_STATUS_OFFSET)
#define S32K3XX_EMAC_MTL_RXP_INTERRUPT_CONTROL_STATUS       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_RXP_INTERRUPT_CONTROL_STATUS_OFFSET)
#define S32K3XX_EMAC_MTL_RXP_DROP_CNT                       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_RXP_DROP_CNT_OFFSET)
#define S32K3XX_EMAC_MTL_RXP_ERROR_CNT                      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_RXP_ERROR_CNT_OFFSET)
#define S32K3XX_EMAC_MTL_RXP_INDIRECT_ACC_CONTROL_STATUS    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_RXP_INDIRECT_ACC_CONTROL_STATUS_OFFSET)
#define S32K3XX_EMAC_MTL_RXP_INDIRECT_ACC_DATA              (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_RXP_INDIRECT_ACC_DATA_OFFSET)
#define S32K3XX_EMAC_MTL_ECC_CONTROL                        (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_ECC_CONTROL_OFFSET)
#define S32K3XX_EMAC_MTL_SAFETY_INTERRUPT_STATUS            (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_SAFETY_INTERRUPT_STATUS_OFFSET)
#define S32K3XX_EMAC_MTL_ECC_INTERRUPT_ENABLE               (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_ECC_INTERRUPT_ENABLE_OFFSET)
#define S32K3XX_EMAC_MTL_ECC_INTERRUPT_STATUS               (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_ECC_INTERRUPT_STATUS_OFFSET)
#define S32K3XX_EMAC_MTL_ECC_ERR_STS_RCTL                   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_ECC_ERR_STS_RCTL_OFFSET)
#define S32K3XX_EMAC_MTL_ECC_ERR_ADDR_STATUS                (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_ECC_ERR_ADDR_STATUS_OFFSET)
#define S32K3XX_EMAC_MTL_ECC_ERR_CNTR_STATUS                (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_ECC_ERR_CNTR_STATUS_OFFSET)
#define S32K3XX_EMAC_MTL_DPP_CONTROL                        (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_DPP_CONTROL_OFFSET)
#define S32K3XX_EMAC_MTL_TXQ0_OPERATION_MODE                (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_TXQ0_OPERATION_MODE_OFFSET)
#define S32K3XX_EMAC_MTL_TXQ0_UNDERFLOW                     (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_TXQ0_UNDERFLOW_OFFSET)
#define S32K3XX_EMAC_MTL_TXQ0_DEBUG                         (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_TXQ0_DEBUG_OFFSET)
#define S32K3XX_EMAC_MTL_TXQ0_ETS_STATUS                    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_TXQ0_ETS_STATUS_OFFSET)
#define S32K3XX_EMAC_MTL_TXQ0_QUANTUM_WEIGHT                (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_TXQ0_QUANTUM_WEIGHT_OFFSET)
#define S32K3XX_EMAC_MTL_Q0_INTERRUPT_CONTROL_STATUS        (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_Q0_INTERRUPT_CONTROL_STATUS_OFFSET)
#define S32K3XX_EMAC_MTL_RXQ0_OPERATION_MODE                (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_RXQ0_OPERATION_MODE_OFFSET)
#define S32K3XX_EMAC_MTL_RXQ0_MISSED_PACKET_OVERFLOW_CNT    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_RXQ0_MISSED_PACKET_OVERFLOW_CNT_OFFSET)
#define S32K3XX_EMAC_MTL_RXQ0_DEBUG                         (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_RXQ0_DEBUG_OFFSET)
#define S32K3XX_EMAC_MTL_RXQ0_CONTROL                       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_RXQ0_CONTROL_OFFSET)
#define S32K3XX_EMAC_MTL_TXQ1_OPERATION_MODE                (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_TXQ1_OPERATION_MODE_OFFSET)
#define S32K3XX_EMAC_MTL_TXQ1_UNDERFLOW                     (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_TXQ1_UNDERFLOW_OFFSET)
#define S32K3XX_EMAC_MTL_TXQ1_DEBUG                         (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_TXQ1_DEBUG_OFFSET)
#define S32K3XX_EMAC_MTL_TXQ1_ETS_CONTROL                   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_TXQ1_ETS_CONTROL_OFFSET)
#define S32K3XX_EMAC_MTL_TXQ1_ETS_STATUS                    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_TXQ1_ETS_STATUS_OFFSET)
#define S32K3XX_EMAC_MTL_TXQ1_QUANTUM_WEIGHT                (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_TXQ1_QUANTUM_WEIGHT_OFFSET)
#define S32K3XX_EMAC_MTL_TXQ1_SENDSLOPECREDIT               (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_TXQ1_SENDSLOPECREDIT_OFFSET)
#define S32K3XX_EMAC_MTL_TXQ1_HICREDIT                      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_TXQ1_HICREDIT_OFFSET)
#define S32K3XX_EMAC_MTL_TXQ1_LOCREDIT                      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_TXQ1_LOCREDIT_OFFSET)
#define S32K3XX_EMAC_MTL_Q1_INTERRUPT_CONTROL_STATUS        (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_Q1_INTERRUPT_CONTROL_STATUS_OFFSET)
#define S32K3XX_EMAC_MTL_RXQ1_OPERATION_MODE                (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_RXQ1_OPERATION_MODE_OFFSET)
#define S32K3XX_EMAC_MTL_RXQ1_MISSED_PACKET_OVERFLOW_CNT    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_RXQ1_MISSED_PACKET_OVERFLOW_CNT_OFFSET)
#define S32K3XX_EMAC_MTL_RXQ1_DEBUG                         (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_RXQ1_DEBUG_OFFSET)
#define S32K3XX_EMAC_MTL_RXQ1_CONTROL                       (S32K3XX_EMAC_BASE + S32K3XX_EMAC_MTL_RXQ1_CONTROL_OFFSET)
#define S32K3XX_EMAC_DMA_MODE                               (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_MODE_OFFSET)
#define S32K3XX_EMAC_DMA_SYSBUS_MODE                        (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_SYSBUS_MODE_OFFSET)
#define S32K3XX_EMAC_DMA_INTERRUPT_STATUS                   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_INTERRUPT_STATUS_OFFSET)
#define S32K3XX_EMAC_DMA_DEBUG_STATUS0                      (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_DEBUG_STATUS0_OFFSET)
#define S32K3XX_EMAC_DMA_TBS_CTRL                           (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_TBS_CTRL_OFFSET)
#define S32K3XX_EMAC_DMA_SAFETY_INTERRUPT_STATUS            (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_SAFETY_INTERRUPT_STATUS_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_CONTROL                        (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_CONTROL_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_TX_CONTROL                     (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_TX_CONTROL_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_RX_CONTROL                     (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_RX_CONTROL_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_TXDESC_LIST_ADDRESS            (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_TXDESC_LIST_ADDRESS_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_RXDESC_LIST_ADDRESS            (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_RXDESC_LIST_ADDRESS_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_TXDESC_TAIL_POINTER            (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_TXDESC_TAIL_POINTER_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_RXDESC_TAIL_POINTER            (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_RXDESC_TAIL_POINTER_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_TXDESC_RING_LENGTH             (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_TXDESC_RING_LENGTH_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_RXDESC_RING_LENGTH             (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_RXDESC_RING_LENGTH_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_INTERRUPT_ENABLE               (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_INTERRUPT_ENABLE_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_RX_INTERRUPT_WATCHDOG_TIMER    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_RX_INTERRUPT_WATCHDOG_TIMER_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_SLOT_FUNCTION_CONTROL_STATUS   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_SLOT_FUNCTION_CONTROL_STATUS_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_CURRENT_APP_TXDESC             (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_CURRENT_APP_TXDESC_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_CURRENT_APP_RXDESC             (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_CURRENT_APP_RXDESC_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_CURRENT_APP_TXBUFFER           (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_CURRENT_APP_TXBUFFER_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_CURRENT_APP_RXBUFFER           (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_CURRENT_APP_RXBUFFER_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_STATUS                         (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_STATUS_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_MISS_FRAME_CNT                 (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_MISS_FRAME_CNT_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_RXP_ACCEPT_CNT                 (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_RXP_ACCEPT_CNT_OFFSET)
#define S32K3XX_EMAC_DMA_CH0_RX_ERI_CNT                     (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH0_RX_ERI_CNT_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_CONTROL                        (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_CONTROL_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_TX_CONTROL                     (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_TX_CONTROL_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_RX_CONTROL                     (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_RX_CONTROL_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_TXDESC_LIST_ADDRESS            (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_TXDESC_LIST_ADDRESS_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_RXDESC_LIST_ADDRESS            (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_RXDESC_LIST_ADDRESS_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_TXDESC_TAIL_POINTER            (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_TXDESC_TAIL_POINTER_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_RXDESC_TAIL_POINTER            (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_RXDESC_TAIL_POINTER_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_TXDESC_RING_LENGTH             (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_TXDESC_RING_LENGTH_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_RXDESC_RING_LENGTH             (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_RXDESC_RING_LENGTH_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_INTERRUPT_ENABLE               (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_INTERRUPT_ENABLE_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_RX_INTERRUPT_WATCHDOG_TIMER    (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_RX_INTERRUPT_WATCHDOG_TIMER_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_SLOT_FUNCTION_CONTROL_STATUS   (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_SLOT_FUNCTION_CONTROL_STATUS_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_CURRENT_APP_TXDESC             (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_CURRENT_APP_TXDESC_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_CURRENT_APP_RXDESC             (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_CURRENT_APP_RXDESC_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_CURRENT_APP_TXBUFFER           (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_CURRENT_APP_TXBUFFER_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_CURRENT_APP_RXBUFFER           (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_CURRENT_APP_RXBUFFER_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_STATUS                         (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_STATUS_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_MISS_FRAME_CNT                 (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_MISS_FRAME_CNT_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_RXP_ACCEPT_CNT                 (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_RXP_ACCEPT_CNT_OFFSET)
#define S32K3XX_EMAC_DMA_CH1_RX_ERI_CNT                     (S32K3XX_EMAC_BASE + S32K3XX_EMAC_DMA_CH1_RX_ERI_CNT_OFFSET)

/* MAC Configuration (MAC_CONFIGURATION) */
#define EMAC_MAC_CONFIGURATION_RE            (1 << 0) /* Bit 0: Receiver Enable */
#define EMAC_MAC_CONFIGURATION_TE            (1 << 1) /* Bit 1: Transmitter Enable */
#define EMAC_MAC_CONFIGURATION_PRELEN_SHIFT  (2)      /* Bits 2-4: Preamble Length for Transmit Packets */
#define EMAC_MAC_CONFIGURATION_PRELEN_MASK   (0x3 << EMAC_MAC_CONFIGURATION_PRELEN_SHIFT)
#define EMAC_MAC_CONFIGURATION_PRELEN(n)     (((n) << EMAC_MAC_CONFIGURATION_PRELEN_SHIFT) & EMAC_MAC_CONFIGURATION_PRELEN_MASK)
#define EMAC_MAC_CONFIGURATION_DC            (1 << 4) /* Bit 4: Deferral Check */
#define EMAC_MAC_CONFIGURATION_BL_SHIFT      (5)      /* Bits 5-7: Back-Off Limit */
#define EMAC_MAC_CONFIGURATION_BL_MASK       (0x3 << EMAC_MAC_CONFIGURATION_BL_SHIFT)
#define EMAC_MAC_CONFIGURATION_BL(n)         (((n) << EMAC_MAC_CONFIGURATION_BL_SHIFT) & EMAC_MAC_CONFIGURATION_BL_MASK)
#define EMAC_MAC_CONFIGURATION_DR            (1 << 8)  /* Bit 8: Disable Retry */
#define EMAC_MAC_CONFIGURATION_DCRS          (1 << 9)  /* Bit 9: Disable Carrier Sense During Transmission */
#define EMAC_MAC_CONFIGURATION_DO            (1 << 10) /* Bit 10: Disable Receive Own */
#define EMAC_MAC_CONFIGURATION_ECRSFD        (1 << 11) /* Bit 11: Enable Carrier Sense In Full-Duplex Mode */
#define EMAC_MAC_CONFIGURATION_LM            (1 << 12) /* Bit 12: Loopback Mode */
#define EMAC_MAC_CONFIGURATION_DM            (1 << 13) /* Bit 13: Duplex Mode */
#define EMAC_MAC_CONFIGURATION_FES           (1 << 14) /* Bit 14: Speed */
#define EMAC_MAC_CONFIGURATION_PS            (1 << 15) /* Bit 15: Port Select */
#define EMAC_MAC_CONFIGURATION_JE            (1 << 16) /* Bit 16: Jumbo Packet Enable */
#define EMAC_MAC_CONFIGURATION_JD            (1 << 17) /* Bit 17: Jabber Disable */
#define EMAC_MAC_CONFIGURATION_WD            (1 << 19) /* Bit 19: Watchdog Disable */
#define EMAC_MAC_CONFIGURATION_ACS           (1 << 20) /* Bit 20: Automatic Pad Or CRC Stripping */
#define EMAC_MAC_CONFIGURATION_CST           (1 << 21) /* Bit 21: CRC Stripping For Type Packets */
#define EMAC_MAC_CONFIGURATION_S2KP          (1 << 22) /* Bit 22: IEEE 802.3 Support For 2K Packets */
#define EMAC_MAC_CONFIGURATION_GPSLCE        (1 << 23) /* Bit 23: Giant Packet Size Limit Control Enable */
#define EMAC_MAC_CONFIGURATION_IPG_SHIFT     (24)      /* Bits 24-27: Inter-Packet Gap */
#define EMAC_MAC_CONFIGURATION_IPG_MASK      (0x7 << EMAC_MAC_CONFIGURATION_IPG_SHIFT)
#define EMAC_MAC_CONFIGURATION_IPG(n)        (((n) << EMAC_MAC_CONFIGURATION_IPG_SHIFT) & EMAC_MAC_CONFIGURATION_IPG_MASK)
#define EMAC_MAC_CONFIGURATION_IPC           (1 << 27) /* Bit 27: Checksum Offload */
#define EMAC_MAC_CONFIGURATION_SARC_SHIFT    (28)      /* Bits 28-31: Source Address Insertion Or Replacement Control */
#define EMAC_MAC_CONFIGURATION_SARC_MASK     (0x7 << EMAC_MAC_CONFIGURATION_SARC_SHIFT)
#define EMAC_MAC_CONFIGURATION_SARC(n)       (((n) << EMAC_MAC_CONFIGURATION_SARC_SHIFT) & EMAC_MAC_CONFIGURATION_SARC_MASK)

/* MAC Extended Configuration (MAC_EXT_CONFIGURATION) */
#define EMAC_MAC_EXT_CONFIGURATION_GPSL_SHIFT  (0) /* Bits 0-14: Giant Packet Size Limit */
#define EMAC_MAC_EXT_CONFIGURATION_GPSL_MASK   (0x3fff << EMAC_MAC_EXT_CONFIGURATION_GPSL_SHIFT)
#define EMAC_MAC_EXT_CONFIGURATION_GPSL(n)     (((n) << EMAC_MAC_EXT_CONFIGURATION_GPSL_SHIFT) & EMAC_MAC_EXT_CONFIGURATION_GPSL_MASK)
#define EMAC_MAC_EXT_CONFIGURATION_DCRCC       (1 << 16) /* Bit 16: Disable CRC Checking For Received Packets */
#define EMAC_MAC_EXT_CONFIGURATION_SPEN        (1 << 17) /* Bit 17: Slow Protocol Detection Enable */
#define EMAC_MAC_EXT_CONFIGURATION_USP         (1 << 18) /* Bit 18: Unicast Slow Protocol Packet Detect */
#define EMAC_MAC_EXT_CONFIGURATION_PDC         (1 << 19) /* Bit 19: Packet Duplication Control */
#define EMAC_MAC_EXT_CONFIGURATION_EIPGEN      (1 << 24) /* Bit 24: Extended Inter-Packet Gap Enable */
#define EMAC_MAC_EXT_CONFIGURATION_EIPG_SHIFT  (25)      /* Bits 25-30: Extended Inter-Packet Gap */
#define EMAC_MAC_EXT_CONFIGURATION_EIPG_MASK   (0x1f << EMAC_MAC_EXT_CONFIGURATION_EIPG_SHIFT)
#define EMAC_MAC_EXT_CONFIGURATION_EIPG(n)     (((n) << EMAC_MAC_EXT_CONFIGURATION_EIPG_SHIFT) & EMAC_MAC_EXT_CONFIGURATION_EIPG_MASK)

/* MAC Packet Filter (MAC_PACKET_FILTER) */
#define EMAC_MAC_PACKET_FILTER_PR         (1 << 0) /* Bit 0: Promiscuous Mode */
#define EMAC_MAC_PACKET_FILTER_HUC        (1 << 1) /* Bit 1: Hash Unicast */
#define EMAC_MAC_PACKET_FILTER_HMC        (1 << 2) /* Bit 2: Hash Multicast */
#define EMAC_MAC_PACKET_FILTER_DAIF       (1 << 3) /* Bit 3: DA Inverse Filtering */
#define EMAC_MAC_PACKET_FILTER_PM         (1 << 4) /* Bit 4: Pass All Multicast */
#define EMAC_MAC_PACKET_FILTER_DBF        (1 << 5) /* Bit 5: Disable Broadcast Packets */
#define EMAC_MAC_PACKET_FILTER_PCF_SHIFT  (6)      /* Bits 6-8: Pass Control Packets */
#define EMAC_MAC_PACKET_FILTER_PCF_MASK   (0x3 << EMAC_MAC_PACKET_FILTER_PCF_SHIFT)
#define EMAC_MAC_PACKET_FILTER_PCF(n)     (((n) << EMAC_MAC_PACKET_FILTER_PCF_SHIFT) & EMAC_MAC_PACKET_FILTER_PCF_MASK)
#define EMAC_MAC_PACKET_FILTER_SAIF       (1 << 8)  /* Bit 8: SA Inverse Filtering */
#define EMAC_MAC_PACKET_FILTER_SAF        (1 << 9)  /* Bit 9: Source Address Filter Enable */
#define EMAC_MAC_PACKET_FILTER_HPF        (1 << 10) /* Bit 10: Hash Or Perfect Filter */
#define EMAC_MAC_PACKET_FILTER_VTFE       (1 << 16) /* Bit 16: VLAN Tag Filter Enable */
#define EMAC_MAC_PACKET_FILTER_IPFE       (1 << 20) /* Bit 20: Layer 3 and Layer 4 Filter Enable */
#define EMAC_MAC_PACKET_FILTER_DNTU       (1 << 21) /* Bit 21: Drop Non-TCP/UDP Over IP Packets */
#define EMAC_MAC_PACKET_FILTER_RA         (1 << 31) /* Bit 31: Receive All */

/* MAC Watchdog Timeout (MAC_WATCHDOG_TIMEOUT) */
#define EMAC_MAC_WATCHDOG_TIMEOUT_WTO_SHIFT  (0) /* Bits 0-4: Watchdog Timeout */
#define EMAC_MAC_WATCHDOG_TIMEOUT_WTO_MASK   (0xf << EMAC_MAC_WATCHDOG_TIMEOUT_WTO_SHIFT)
#define EMAC_MAC_WATCHDOG_TIMEOUT_WTO(n)     (((n) << EMAC_MAC_WATCHDOG_TIMEOUT_WTO_SHIFT) & EMAC_MAC_WATCHDOG_TIMEOUT_WTO_MASK)
#define EMAC_MAC_WATCHDOG_TIMEOUT_PWE        (1 << 8) /* Bit 8: Programmable Watchdog Enable */

/* MAC Hash Table First 32 Bits (MAC_HASH_TABLE_REG0) */
#define EMAC_MAC_HASH_TABLE_REG0_HT31T0_SHIFT  (0) /* Bits 0-32: MAC Hash Table First 32 Bits */
#define EMAC_MAC_HASH_TABLE_REG0_HT31T0_MASK   (0xffffffff << EMAC_MAC_HASH_TABLE_REG0_HT31T0_SHIFT)
#define EMAC_MAC_HASH_TABLE_REG0_HT31T0(n)     (((n) << EMAC_MAC_HASH_TABLE_REG0_HT31T0_SHIFT) & EMAC_MAC_HASH_TABLE_REG0_HT31T0_MASK)

/* MAC Hash Table Second 32 Bits (MAC_HASH_TABLE_REG1) */
#define EMAC_MAC_HASH_TABLE_REG1_HT63T32_SHIFT  (0) /* Bits 0-32: MAC Hash Table Second 32 Bits */
#define EMAC_MAC_HASH_TABLE_REG1_HT63T32_MASK   (0xffffffff << EMAC_MAC_HASH_TABLE_REG1_HT63T32_SHIFT)
#define EMAC_MAC_HASH_TABLE_REG1_HT63T32(n)     (((n) << EMAC_MAC_HASH_TABLE_REG1_HT63T32_SHIFT) & EMAC_MAC_HASH_TABLE_REG1_HT63T32_MASK)

/* MAC VLAN Tag (MAC_VLAN_TAG) */
#define EMAC_MAC_VLAN_TAG_VL_SHIFT     (0) /* Bits 0-16: VLAN Tag Identifier for Receive Packets */
#define EMAC_MAC_VLAN_TAG_VL_MASK      (0xffff << EMAC_MAC_VLAN_TAG_VL_SHIFT)
#define EMAC_MAC_VLAN_TAG_VL(n)        (((n) << EMAC_MAC_VLAN_TAG_VL_SHIFT) & EMAC_MAC_VLAN_TAG_VL_MASK)
#define EMAC_MAC_VLAN_TAG_ETV          (1 << 16) /* Bit 16: Enable Tag For VLAN */
#define EMAC_MAC_VLAN_TAG_VTIM         (1 << 17) /* Bit 17: VLAN Tag Inverse Match Enable */
#define EMAC_MAC_VLAN_TAG_ESVL         (1 << 18) /* Bit 18: Enable S-VLAN */
#define EMAC_MAC_VLAN_TAG_ERSVLM       (1 << 19) /* Bit 19: Enable Receive S-VLAN Match */
#define EMAC_MAC_VLAN_TAG_DOVLTC       (1 << 20) /* Bit 20: Disable VLAN Type Check */
#define EMAC_MAC_VLAN_TAG_EVLS_SHIFT   (21)      /* Bits 21-23: Enable VLAN Tag Stripping */
#define EMAC_MAC_VLAN_TAG_EVLS_MASK    (0x3 << EMAC_MAC_VLAN_TAG_EVLS_SHIFT)
#define EMAC_MAC_VLAN_TAG_EVLS(n)      (((n) << EMAC_MAC_VLAN_TAG_EVLS_SHIFT) & EMAC_MAC_VLAN_TAG_EVLS_MASK)
#define EMAC_MAC_VLAN_TAG_EVLRXS       (1 << 24) /* Bit 24: Enable VLAN Tag In Receive Status */
#define EMAC_MAC_VLAN_TAG_VTHM         (1 << 25) /* Bit 25: VLAN Tag Hash Table Match */
#define EMAC_MAC_VLAN_TAG_EDVLP        (1 << 26) /* Bit 26: Enable Double VLAN Processing */
#define EMAC_MAC_VLAN_TAG_ERIVLT       (1 << 27) /* Bit 27: Enable Inner VLAN Tag Comparison */
#define EMAC_MAC_VLAN_TAG_EIVLS_SHIFT  (28)      /* Bits 28-30: Enable Inner VLAN Tag Stripping */
#define EMAC_MAC_VLAN_TAG_EIVLS_MASK   (0x3 << EMAC_MAC_VLAN_TAG_EIVLS_SHIFT)
#define EMAC_MAC_VLAN_TAG_EIVLS(n)     (((n) << EMAC_MAC_VLAN_TAG_EIVLS_SHIFT) & EMAC_MAC_VLAN_TAG_EIVLS_MASK)
#define EMAC_MAC_VLAN_TAG_EIVLRXS      (1 << 31) /* Bit 31: Enable Inner VLAN Tag In Receive Status */

/* MAC VLAN Tag Control (MAC_VLAN_TAG_CTRL) */
#define EMAC_MAC_VLAN_TAG_CTRL_OB           (1 << 0) /* Bit 0: Operation Busy */
#define EMAC_MAC_VLAN_TAG_CTRL_CT           (1 << 1) /* Bit 1: Command Type */
#define EMAC_MAC_VLAN_TAG_CTRL_OFS_SHIFT    (2)      /* Bits 2-4: Offset */
#define EMAC_MAC_VLAN_TAG_CTRL_OFS_MASK     (0x3 << EMAC_MAC_VLAN_TAG_CTRL_OFS_SHIFT)
#define EMAC_MAC_VLAN_TAG_CTRL_OFS(n)       (((n) << EMAC_MAC_VLAN_TAG_CTRL_OFS_SHIFT) & EMAC_MAC_VLAN_TAG_CTRL_OFS_MASK)
#define EMAC_MAC_VLAN_TAG_CTRL_ETV          (1 << 16) /* Bit 16: Enable Tag For VLAN */
#define EMAC_MAC_VLAN_TAG_CTRL_VTIM         (1 << 17) /* Bit 17: VLAN Tag Inverse Match Enable */
#define EMAC_MAC_VLAN_TAG_CTRL_ESVL         (1 << 18) /* Bit 18: Enable S-VLAN */
#define EMAC_MAC_VLAN_TAG_CTRL_ERSVLM       (1 << 19) /* Bit 19: Enable Receive S-VLAN Match */
#define EMAC_MAC_VLAN_TAG_CTRL_DOVLTC       (1 << 20) /* Bit 20: Disable VLAN Type Check */
#define EMAC_MAC_VLAN_TAG_CTRL_EVLS_SHIFT   (21)      /* Bits 21-23: Enable VLAN Tag Stripping */
#define EMAC_MAC_VLAN_TAG_CTRL_EVLS_MASK    (0x3 << EMAC_MAC_VLAN_TAG_CTRL_EVLS_SHIFT)
#define EMAC_MAC_VLAN_TAG_CTRL_EVLS(n)      (((n) << EMAC_MAC_VLAN_TAG_CTRL_EVLS_SHIFT) & EMAC_MAC_VLAN_TAG_CTRL_EVLS_MASK)
#define EMAC_MAC_VLAN_TAG_CTRL_EVLRXS       (1 << 24) /* Bit 24: Enable VLAN Tag In Receive Status */
#define EMAC_MAC_VLAN_TAG_CTRL_VTHM         (1 << 25) /* Bit 25: VLAN Tag Hash Table Match */
#define EMAC_MAC_VLAN_TAG_CTRL_EDVLP        (1 << 26) /* Bit 26: Enable Double VLAN Processing */
#define EMAC_MAC_VLAN_TAG_CTRL_ERIVLT       (1 << 27) /* Bit 27: Enable Inner VLAN Tag Comparison */
#define EMAC_MAC_VLAN_TAG_CTRL_EIVLS_SHIFT  (28)      /* Bits 28-30: Enable Inner VLAN Tag Stripping */
#define EMAC_MAC_VLAN_TAG_CTRL_EIVLS_MASK   (0x3 << EMAC_MAC_VLAN_TAG_CTRL_EIVLS_SHIFT)
#define EMAC_MAC_VLAN_TAG_CTRL_EIVLS(n)     (((n) << EMAC_MAC_VLAN_TAG_CTRL_EIVLS_SHIFT) & EMAC_MAC_VLAN_TAG_CTRL_EIVLS_MASK)
#define EMAC_MAC_VLAN_TAG_CTRL_EIVLRXS      (1 << 31) /* Bit 31: Enable Inner VLAN Tag In Receive Status */

/* MAC VLAN Tag Data (MAC_VLAN_TAG_DATA) */
#define EMAC_MAC_VLAN_TAG_DATA_VID_SHIFT  (0) /* Bits 0-16: VLAN Tag ID */
#define EMAC_MAC_VLAN_TAG_DATA_VID_MASK   (0xffff << EMAC_MAC_VLAN_TAG_DATA_VID_SHIFT)
#define EMAC_MAC_VLAN_TAG_DATA_VID(n)     (((n) << EMAC_MAC_VLAN_TAG_DATA_VID_SHIFT) & EMAC_MAC_VLAN_TAG_DATA_VID_MASK)
#define EMAC_MAC_VLAN_TAG_DATA_VEN        (1 << 16) /* Bit 16: VLAN Tag Enable */
#define EMAC_MAC_VLAN_TAG_DATA_ETV        (1 << 17) /* Bit 17: VLAN Comparison */
#define EMAC_MAC_VLAN_TAG_DATA_DOVLTC     (1 << 18) /* Bit 18: Disable VLAN Type Comparison */
#define EMAC_MAC_VLAN_TAG_DATA_ERSVLM     (1 << 19) /* Bit 19: Enable S-VLAN Match */
#define EMAC_MAC_VLAN_TAG_DATA_ERIVLT     (1 << 20) /* Bit 20: Enable Inner VLAN Tag */
#define EMAC_MAC_VLAN_TAG_DATA_DMACHEN    (1 << 24) /* Bit 24: DMA Channel Number Enable */
#define EMAC_MAC_VLAN_TAG_DATA_DMACHN     (1 << 25) /* Bit 25: DMA Channel Number */

/* MAC VLAN Tag Filter 0 (MAC_VLAN_TAG_FILTER0) */
#define EMAC_MAC_VLAN_TAG_FILTER0_VID_SHIFT  (0) /* Bits 0-16: VLAN Tag ID */
#define EMAC_MAC_VLAN_TAG_FILTER0_VID_MASK   (0xffff << EMAC_MAC_VLAN_TAG_FILTER0_VID_SHIFT)
#define EMAC_MAC_VLAN_TAG_FILTER0_VID(n)     (((n) << EMAC_MAC_VLAN_TAG_FILTER0_VID_SHIFT) & EMAC_MAC_VLAN_TAG_FILTER0_VID_MASK)
#define EMAC_MAC_VLAN_TAG_FILTER0_VEN        (1 << 16) /* Bit 16: VLAN Tag Enable */
#define EMAC_MAC_VLAN_TAG_FILTER0_ETV        (1 << 17) /* Bit 17: VLAN Comparison */
#define EMAC_MAC_VLAN_TAG_FILTER0_DOVLTC     (1 << 18) /* Bit 18: Disable VLAN Type Comparison */
#define EMAC_MAC_VLAN_TAG_FILTER0_ERSVLM     (1 << 19) /* Bit 19: Enable S-VLAN Match */
#define EMAC_MAC_VLAN_TAG_FILTER0_ERIVLT     (1 << 20) /* Bit 20: Enable Inner VLAN Tag */
#define EMAC_MAC_VLAN_TAG_FILTER0_DMACHEN    (1 << 24) /* Bit 24: DMA Channel Number Enable */
#define EMAC_MAC_VLAN_TAG_FILTER0_DMACHN     (1 << 25) /* Bit 25: DMA Channel Number */

/* MAC VLAN Tag Filter 1 (MAC_VLAN_TAG_FILTER1) */
#define EMAC_MAC_VLAN_TAG_FILTER1_VID_SHIFT  (0) /* Bits 0-16: VLAN Tag ID */
#define EMAC_MAC_VLAN_TAG_FILTER1_VID_MASK   (0xffff << EMAC_MAC_VLAN_TAG_FILTER1_VID_SHIFT)
#define EMAC_MAC_VLAN_TAG_FILTER1_VID(n)     (((n) << EMAC_MAC_VLAN_TAG_FILTER1_VID_SHIFT) & EMAC_MAC_VLAN_TAG_FILTER1_VID_MASK)
#define EMAC_MAC_VLAN_TAG_FILTER1_VEN        (1 << 16) /* Bit 16: VLAN Tag Enable */
#define EMAC_MAC_VLAN_TAG_FILTER1_ETV        (1 << 17) /* Bit 17: VLAN Comparison */
#define EMAC_MAC_VLAN_TAG_FILTER1_DOVLTC     (1 << 18) /* Bit 18: Disable VLAN Type Comparison */
#define EMAC_MAC_VLAN_TAG_FILTER1_ERSVLM     (1 << 19) /* Bit 19: Enable S-VLAN Match */
#define EMAC_MAC_VLAN_TAG_FILTER1_ERIVLT     (1 << 20) /* Bit 20: Enable Inner VLAN Tag */
#define EMAC_MAC_VLAN_TAG_FILTER1_DMACHEN    (1 << 24) /* Bit 24: DMA Channel Number Enable */
#define EMAC_MAC_VLAN_TAG_FILTER1_DMACHN     (1 << 25) /* Bit 25: DMA Channel Number */

/* MAC VLAN Tag Filter 2 (MAC_VLAN_TAG_FILTER2) */
#define EMAC_MAC_VLAN_TAG_FILTER2_VID_SHIFT  (0) /* Bits 0-16: VLAN Tag ID */
#define EMAC_MAC_VLAN_TAG_FILTER2_VID_MASK   (0xffff << EMAC_MAC_VLAN_TAG_FILTER2_VID_SHIFT)
#define EMAC_MAC_VLAN_TAG_FILTER2_VID(n)     (((n) << EMAC_MAC_VLAN_TAG_FILTER2_VID_SHIFT) & EMAC_MAC_VLAN_TAG_FILTER2_VID_MASK)
#define EMAC_MAC_VLAN_TAG_FILTER2_VEN        (1 << 16) /* Bit 16: VLAN Tag Enable */
#define EMAC_MAC_VLAN_TAG_FILTER2_ETV        (1 << 17) /* Bit 17: VLAN Comparison */
#define EMAC_MAC_VLAN_TAG_FILTER2_DOVLTC     (1 << 18) /* Bit 18: Disable VLAN Type Comparison */
#define EMAC_MAC_VLAN_TAG_FILTER2_ERSVLM     (1 << 19) /* Bit 19: Enable S-VLAN Match */
#define EMAC_MAC_VLAN_TAG_FILTER2_ERIVLT     (1 << 20) /* Bit 20: Enable Inner VLAN Tag */
#define EMAC_MAC_VLAN_TAG_FILTER2_DMACHEN    (1 << 24) /* Bit 24: DMA Channel Number Enable */
#define EMAC_MAC_VLAN_TAG_FILTER2_DMACHN     (1 << 25) /* Bit 25: DMA Channel Number */

/* MAC VLAN Tag Filter 3 (MAC_VLAN_TAG_FILTER3) */
#define EMAC_MAC_VLAN_TAG_FILTER3_VID_SHIFT  (0) /* Bits 0-16: VLAN Tag ID */
#define EMAC_MAC_VLAN_TAG_FILTER3_VID_MASK   (0xffff << EMAC_MAC_VLAN_TAG_FILTER3_VID_SHIFT)
#define EMAC_MAC_VLAN_TAG_FILTER3_VID(n)     (((n) << EMAC_MAC_VLAN_TAG_FILTER3_VID_SHIFT) & EMAC_MAC_VLAN_TAG_FILTER3_VID_MASK)
#define EMAC_MAC_VLAN_TAG_FILTER3_VEN        (1 << 16) /* Bit 16: VLAN Tag Enable */
#define EMAC_MAC_VLAN_TAG_FILTER3_ETV        (1 << 17) /* Bit 17: VLAN Comparison */
#define EMAC_MAC_VLAN_TAG_FILTER3_DOVLTC     (1 << 18) /* Bit 18: Disable VLAN Type Comparison */
#define EMAC_MAC_VLAN_TAG_FILTER3_ERSVLM     (1 << 19) /* Bit 19: Enable S-VLAN Match */
#define EMAC_MAC_VLAN_TAG_FILTER3_ERIVLT     (1 << 20) /* Bit 20: Enable Inner VLAN Tag */
#define EMAC_MAC_VLAN_TAG_FILTER3_DMACHEN    (1 << 24) /* Bit 24: DMA Channel Number Enable */
#define EMAC_MAC_VLAN_TAG_FILTER3_DMACHN     (1 << 25) /* Bit 25: DMA Channel Number */

/* MAC VLAN Hash Table (MAC_VLAN_HASH_TABLE) */
#define EMAC_MAC_VLAN_HASH_TABLE_VLHT_SHIFT  (0) /* Bits 0-16: VLAN Hash Table */
#define EMAC_MAC_VLAN_HASH_TABLE_VLHT_MASK   (0xffff << EMAC_MAC_VLAN_HASH_TABLE_VLHT_SHIFT)
#define EMAC_MAC_VLAN_HASH_TABLE_VLHT(n)     (((n) << EMAC_MAC_VLAN_HASH_TABLE_VLHT_SHIFT) & EMAC_MAC_VLAN_HASH_TABLE_VLHT_MASK)

/* MAC VLAN Inclusion Or Replacement (MAC_VLAN_INCL) */
#define EMAC_MAC_VLAN_INCL_VLT_SHIFT  (0) /* Bits 0-16: VLAN Tag For Transmit Packets */
#define EMAC_MAC_VLAN_INCL_VLT_MASK   (0xffff << EMAC_MAC_VLAN_INCL_VLT_SHIFT)
#define EMAC_MAC_VLAN_INCL_VLT(n)     (((n) << EMAC_MAC_VLAN_INCL_VLT_SHIFT) & EMAC_MAC_VLAN_INCL_VLT_MASK)
#define EMAC_MAC_VLAN_INCL_VLC_SHIFT  (16) /* Bits 16-18: VLAN Tag Control */
#define EMAC_MAC_VLAN_INCL_VLC_MASK   (0x3 << EMAC_MAC_VLAN_INCL_VLC_SHIFT)
#define EMAC_MAC_VLAN_INCL_VLC(n)     (((n) << EMAC_MAC_VLAN_INCL_VLC_SHIFT) & EMAC_MAC_VLAN_INCL_VLC_MASK)
#define EMAC_MAC_VLAN_INCL_VLP        (1 << 18) /* Bit 18: VLAN Priority Control */
#define EMAC_MAC_VLAN_INCL_CSVL       (1 << 19) /* Bit 19: C-VLAN Or S-VLAN */
#define EMAC_MAC_VLAN_INCL_VLTI       (1 << 20) /* Bit 20: VLAN Tag Input */
#define EMAC_MAC_VLAN_INCL_CBTI       (1 << 21) /* Bit 21: Channel-Based Tag Insertion */
#define EMAC_MAC_VLAN_INCL_ADDR       (1 << 24) /* Bit 24: Address */
#define EMAC_MAC_VLAN_INCL_RDWR       (1 << 30) /* Bit 30: Read Write Control */
#define EMAC_MAC_VLAN_INCL_BUSY       (1 << 31) /* Bit 31: Busy */

/* Inner VLAN Tag Inclusion Or Replacement (MAC_INNER_VLAN_INCL) */
#define EMAC_MAC_INNER_VLAN_INCL_VLT_SHIFT  (0) /* Bits 0-16: VLAN Tag For Transmit Packets */
#define EMAC_MAC_INNER_VLAN_INCL_VLT_MASK   (0xffff << EMAC_MAC_INNER_VLAN_INCL_VLT_SHIFT)
#define EMAC_MAC_INNER_VLAN_INCL_VLT(n)     (((n) << EMAC_MAC_INNER_VLAN_INCL_VLT_SHIFT) & EMAC_MAC_INNER_VLAN_INCL_VLT_MASK)
#define EMAC_MAC_INNER_VLAN_INCL_VLC_SHIFT  (16) /* Bits 16-18: VLAN Tag Control in Transmit Packets */
#define EMAC_MAC_INNER_VLAN_INCL_VLC_MASK   (0x3 << EMAC_MAC_INNER_VLAN_INCL_VLC_SHIFT)
#define EMAC_MAC_INNER_VLAN_INCL_VLC(n)     (((n) << EMAC_MAC_INNER_VLAN_INCL_VLC_SHIFT) & EMAC_MAC_INNER_VLAN_INCL_VLC_MASK)
#define EMAC_MAC_INNER_VLAN_INCL_VLP        (1 << 18) /* Bit 18: VLAN Priority Control */
#define EMAC_MAC_INNER_VLAN_INCL_CSVL       (1 << 19) /* Bit 19: C-VLAN Or S-VLAN */
#define EMAC_MAC_INNER_VLAN_INCL_VLTI       (1 << 20) /* Bit 20: VLAN Tag Input */

/* MAC Q0 Tx Flow Control (MAC_Q0_TX_FLOW_CTRL) */
#define EMAC_MAC_Q0_TX_FLOW_CTRL_FCB_BPA    (1 << 0) /* Bit 0: Flow Control Busy Or Backpressure Activate */
#define EMAC_MAC_Q0_TX_FLOW_CTRL_TFE        (1 << 1) /* Bit 1: Transmit Flow Control Enable */
#define EMAC_MAC_Q0_TX_FLOW_CTRL_PLT_SHIFT  (4)      /* Bits 4-7: Pause Low Threshold */
#define EMAC_MAC_Q0_TX_FLOW_CTRL_PLT_MASK   (0x7 << EMAC_MAC_Q0_TX_FLOW_CTRL_PLT_SHIFT)
#define EMAC_MAC_Q0_TX_FLOW_CTRL_PLT(n)     (((n) << EMAC_MAC_Q0_TX_FLOW_CTRL_PLT_SHIFT) & EMAC_MAC_Q0_TX_FLOW_CTRL_PLT_MASK)
#define EMAC_MAC_Q0_TX_FLOW_CTRL_DZPQ       (1 << 7) /* Bit 7: Disable Zero-Quanta Pause */
#define EMAC_MAC_Q0_TX_FLOW_CTRL_PT_SHIFT   (16)     /* Bits 16-32: Pause Time */
#define EMAC_MAC_Q0_TX_FLOW_CTRL_PT_MASK    (0xffff << EMAC_MAC_Q0_TX_FLOW_CTRL_PT_SHIFT)
#define EMAC_MAC_Q0_TX_FLOW_CTRL_PT(n)      (((n) << EMAC_MAC_Q0_TX_FLOW_CTRL_PT_SHIFT) & EMAC_MAC_Q0_TX_FLOW_CTRL_PT_MASK)

/* MAC Receive Flow Control (MAC_RX_FLOW_CTRL) */
#define EMAC_MAC_RX_FLOW_CTRL_RFE  (1 << 0) /* Bit 0: Receive Flow Control Enable */
#define EMAC_MAC_RX_FLOW_CTRL_UP   (1 << 1) /* Bit 1: Unicast Pause Packet Detect */

/* MAC RxQ Control 4 (MAC_RXQ_CTRL4) */
#define EMAC_MAC_RXQ_CTRL4_UFFQE  (1 << 0)  /* Bit 0: Unicast Address Filter Fail Packets Queuing Enable */
#define EMAC_MAC_RXQ_CTRL4_UFFQ   (1 << 1)  /* Bit 1: Unicast Address Filter Fail Packets Queue */
#define EMAC_MAC_RXQ_CTRL4_MFFQE  (1 << 8)  /* Bit 8: Multicast Address Filter Fail Packets Queuing Enable */
#define EMAC_MAC_RXQ_CTRL4_MFFQ   (1 << 9)  /* Bit 9: Multicast Address Filter Fail Packets Queue */
#define EMAC_MAC_RXQ_CTRL4_VFFQE  (1 << 16) /* Bit 16: VLAN Tag Filter Fail Packets Queuing Enable */
#define EMAC_MAC_RXQ_CTRL4_VFFQ   (1 << 17) /* Bit 17: VLAN Tag Filter Fail Packets Queue */

/* MAC RxQ Control 0 (MAC_RXQ_CTRL0) */
#define EMAC_MAC_RXQ_CTRL0_RXQ0EN_SHIFT   (0) /* Bits 0-2: Receive Queue 0 Enable */
#define EMAC_MAC_RXQ_CTRL0_RXQ0EN_MASK    (0x3 << EMAC_MAC_RXQ_CTRL0_RXQ0EN_SHIFT)
#define EMAC_MAC_RXQ_CTRL0_RXQ0EN(n)      (((n) << EMAC_MAC_RXQ_CTRL0_RXQ0EN_SHIFT) & EMAC_MAC_RXQ_CTRL0_RXQ0EN_MASK)
#define EMAC_MAC_RXQ_CTRL0_RXQ0EN_DISABLE EMAC_MAC_RXQ_CTRL0_RXQ0EN(0)
#define EMAC_MAC_RXQ_CTRL0_RXQ0EN_AVB     EMAC_MAC_RXQ_CTRL0_RXQ0EN(0x1)
#define EMAC_MAC_RXQ_CTRL0_RXQ0EN_DCB_GEN EMAC_MAC_RXQ_CTRL0_RXQ0EN(0x2)
#define EMAC_MAC_RXQ_CTRL0_RXQ1EN_SHIFT   (2) /* Bits 2-4: Receive Queue 1 Enable */
#define EMAC_MAC_RXQ_CTRL0_RXQ1EN_MASK    (0x3 << EMAC_MAC_RXQ_CTRL0_RXQ1EN_SHIFT)
#define EMAC_MAC_RXQ_CTRL0_RXQ1EN(n)      (((n) << EMAC_MAC_RXQ_CTRL0_RXQ1EN_SHIFT) & EMAC_MAC_RXQ_CTRL0_RXQ1EN_MASK)
#define EMAC_MAC_RXQ_CTRL0_RXQ1EN_DISABLE EMAC_MAC_RXQ_CTRL0_RXQ1EN(0)
#define EMAC_MAC_RXQ_CTRL0_RXQ1EN_AVB     EMAC_MAC_RXQ_CTRL0_RXQ1EN(0x1)
#define EMAC_MAC_RXQ_CTRL0_RXQ1EN_DCB_GEN EMAC_MAC_RXQ_CTRL0_RXQ1EN(0x2)

/* Receive Queue Control 1 (MAC_RXQ_CTRL1) */
#define EMAC_MAC_RXQ_CTRL1_AVCPQ_SHIFT  (0) /* Bits 0-3: AV Untagged Control Packets Queue */
#define EMAC_MAC_RXQ_CTRL1_AVCPQ_MASK   (0x7 << EMAC_MAC_RXQ_CTRL1_AVCPQ_SHIFT)
#define EMAC_MAC_RXQ_CTRL1_AVCPQ(n)     (((n) << EMAC_MAC_RXQ_CTRL1_AVCPQ_SHIFT) & EMAC_MAC_RXQ_CTRL1_AVCPQ_MASK)
#define EMAC_MAC_RXQ_CTRL1_PTPQ_SHIFT   (4) /* Bits 4-7: PTP Packets Queue */
#define EMAC_MAC_RXQ_CTRL1_PTPQ_MASK    (0x7 << EMAC_MAC_RXQ_CTRL1_PTPQ_SHIFT)
#define EMAC_MAC_RXQ_CTRL1_PTPQ(n)      (((n) << EMAC_MAC_RXQ_CTRL1_PTPQ_SHIFT) & EMAC_MAC_RXQ_CTRL1_PTPQ_MASK)
#define EMAC_MAC_RXQ_CTRL1_UPQ_SHIFT    (12) /* Bits 12-15: Untagged Packet Queue */
#define EMAC_MAC_RXQ_CTRL1_UPQ_MASK     (0x7 << EMAC_MAC_RXQ_CTRL1_UPQ_SHIFT)
#define EMAC_MAC_RXQ_CTRL1_UPQ(n)       (((n) << EMAC_MAC_RXQ_CTRL1_UPQ_SHIFT) & EMAC_MAC_RXQ_CTRL1_UPQ_MASK)
#define EMAC_MAC_RXQ_CTRL1_MCBCQ_SHIFT  (16) /* Bits 16-19: Multicast And Broadcast Queue */
#define EMAC_MAC_RXQ_CTRL1_MCBCQ_MASK   (0x7 << EMAC_MAC_RXQ_CTRL1_MCBCQ_SHIFT)
#define EMAC_MAC_RXQ_CTRL1_MCBCQ(n)     (((n) << EMAC_MAC_RXQ_CTRL1_MCBCQ_SHIFT) & EMAC_MAC_RXQ_CTRL1_MCBCQ_MASK)
#define EMAC_MAC_RXQ_CTRL1_MCBCQEN      (1 << 20) /* Bit 20: Multicast And Broadcast Queue Enable */
#define EMAC_MAC_RXQ_CTRL1_TACPQE       (1 << 21) /* Bit 21: Tagged AV Control Packets Queuing Enable */
#define EMAC_MAC_RXQ_CTRL1_TPQC_SHIFT   (22)      /* Bits 22-24: Tagged PTP Over Ethernet Packets Queuing Control */
#define EMAC_MAC_RXQ_CTRL1_TPQC_MASK    (0x3 << EMAC_MAC_RXQ_CTRL1_TPQC_SHIFT)
#define EMAC_MAC_RXQ_CTRL1_TPQC(n)      (((n) << EMAC_MAC_RXQ_CTRL1_TPQC_SHIFT) & EMAC_MAC_RXQ_CTRL1_TPQC_MASK)
#define EMAC_MAC_RXQ_CTRL1_FPRQ_SHIFT   (24) /* Bits 24-27: Frame Preemption Residue Queue */
#define EMAC_MAC_RXQ_CTRL1_FPRQ_MASK    (0x7 << EMAC_MAC_RXQ_CTRL1_FPRQ_SHIFT)
#define EMAC_MAC_RXQ_CTRL1_FPRQ(n)      (((n) << EMAC_MAC_RXQ_CTRL1_FPRQ_SHIFT) & EMAC_MAC_RXQ_CTRL1_FPRQ_MASK)

/* MAC RxQ Control 2 (MAC_RXQ_CTRL2) */
#define EMAC_MAC_RXQ_CTRL2_PSRQ0_SHIFT  (0) /* Bits 0-8: Priorities Selected In Receive Queue 0 */
#define EMAC_MAC_RXQ_CTRL2_PSRQ0_MASK   (0xff << EMAC_MAC_RXQ_CTRL2_PSRQ0_SHIFT)
#define EMAC_MAC_RXQ_CTRL2_PSRQ0(n)     (((n) << EMAC_MAC_RXQ_CTRL2_PSRQ0_SHIFT) & EMAC_MAC_RXQ_CTRL2_PSRQ0_MASK)
#define EMAC_MAC_RXQ_CTRL2_PSRQ1_SHIFT  (8) /* Bits 8-16: Priorities Selected In Receive Queue 1 */
#define EMAC_MAC_RXQ_CTRL2_PSRQ1_MASK   (0xff << EMAC_MAC_RXQ_CTRL2_PSRQ1_SHIFT)
#define EMAC_MAC_RXQ_CTRL2_PSRQ1(n)     (((n) << EMAC_MAC_RXQ_CTRL2_PSRQ1_SHIFT) & EMAC_MAC_RXQ_CTRL2_PSRQ1_MASK)

/* MAC Interrupt Status (MAC_INTERRUPT_STATUS) */
#define EMAC_MAC_INTERRUPT_STATUS_PHYIS    (1 << 3)  /* Bit 3: PHY Interrupt */
#define EMAC_MAC_INTERRUPT_STATUS_MMCIS    (1 << 8)  /* Bit 8: MMC Interrupt Status */
#define EMAC_MAC_INTERRUPT_STATUS_MMCRXIS  (1 << 9)  /* Bit 9: MMC Receive Interrupt Status */
#define EMAC_MAC_INTERRUPT_STATUS_MMCTXIS  (1 << 10) /* Bit 10: MMC Transmit Interrupt Status */
#define EMAC_MAC_INTERRUPT_STATUS_TSIS     (1 << 12) /* Bit 12: Timestamp Interrupt Status */
#define EMAC_MAC_INTERRUPT_STATUS_TXSTSIS  (1 << 13) /* Bit 13: Transmit Status Interrupt */
#define EMAC_MAC_INTERRUPT_STATUS_RXSTSIS  (1 << 14) /* Bit 14: Receive Status Interrupt */
#define EMAC_MAC_INTERRUPT_STATUS_FPEIS    (1 << 17) /* Bit 17: Frame Preemption Interrupt Status */
#define EMAC_MAC_INTERRUPT_STATUS_MDIOIS   (1 << 18) /* Bit 18: MDIO Interrupt Status */
#define EMAC_MAC_INTERRUPT_STATUS_MFTIS    (1 << 19) /* Bit 19: MMC FPE Transmit Interrupt Status */
#define EMAC_MAC_INTERRUPT_STATUS_MFRIS    (1 << 20) /* Bit 20: MMC FPE Receive Interrupt Status */

/* MAC Interrupt Enable (MAC_INTERRUPT_ENABLE) */
#define EMAC_MAC_INTERRUPT_ENABLE_PHYIE    (1 << 3)  /* Bit 3: PHY Interrupt Enable */
#define EMAC_MAC_INTERRUPT_ENABLE_TSIE     (1 << 12) /* Bit 12: Timestamp Interrupt Enable */
#define EMAC_MAC_INTERRUPT_ENABLE_TXSTSIE  (1 << 13) /* Bit 13: Transmit Status Interrupt Enable */
#define EMAC_MAC_INTERRUPT_ENABLE_RXSTSIE  (1 << 14) /* Bit 14: Receive Status Interrupt Enable */
#define EMAC_MAC_INTERRUPT_ENABLE_FPEIE    (1 << 17) /* Bit 17: Frame Preemption Interrupt Enable */
#define EMAC_MAC_INTERRUPT_ENABLE_MDIOIE   (1 << 18) /* Bit 18: MDIO Interrupt Enable */

/* MAC Rx Transmit Status (MAC_RX_TX_STATUS) */
#define EMAC_MAC_RX_TX_STATUS_TJT    (1 << 0) /* Bit 0: Transmit Jabber Timeout */
#define EMAC_MAC_RX_TX_STATUS_NCARR  (1 << 1) /* Bit 1: No Carrier */
#define EMAC_MAC_RX_TX_STATUS_LCARR  (1 << 2) /* Bit 2: Loss of Carrier */
#define EMAC_MAC_RX_TX_STATUS_EXDEF  (1 << 3) /* Bit 3: Excessive Deferral */
#define EMAC_MAC_RX_TX_STATUS_LCOL   (1 << 4) /* Bit 4: Late Collision */
#define EMAC_MAC_RX_TX_STATUS_EXCOL  (1 << 5) /* Bit 5: Excessive Collisions */
#define EMAC_MAC_RX_TX_STATUS_RWT    (1 << 8) /* Bit 8: Receive Watchdog Timeout */

/* MAC Version (MAC_VERSION) */
#define EMAC_MAC_VERSION_IPVER_SHIFT   (0) /* Bits 0-8: IP Version */
#define EMAC_MAC_VERSION_IPVER_MASK    (0xff << EMAC_MAC_VERSION_IPVER_SHIFT)
#define EMAC_MAC_VERSION_IPVER(n)      (((n) << EMAC_MAC_VERSION_IPVER_SHIFT) & EMAC_MAC_VERSION_IPVER_MASK)
#define EMAC_MAC_VERSION_CFGVER_SHIFT  (8) /* Bits 8-16: IP Configuration Version */
#define EMAC_MAC_VERSION_CFGVER_MASK   (0xff << EMAC_MAC_VERSION_CFGVER_SHIFT)
#define EMAC_MAC_VERSION_CFGVER(n)     (((n) << EMAC_MAC_VERSION_CFGVER_SHIFT) & EMAC_MAC_VERSION_CFGVER_MASK)

/* MAC Debug (MAC_DEBUG) */
#define EMAC_MAC_DEBUG_RPESTS          (1 << 0) /* Bit 0: Receive Protocol Engine Status */
#define EMAC_MAC_DEBUG_RFCFCSTS_SHIFT  (1)      /* Bits 1-3: MAC Receive Packet Controller FIFO Status */
#define EMAC_MAC_DEBUG_RFCFCSTS_MASK   (0x3 << EMAC_MAC_DEBUG_RFCFCSTS_SHIFT)
#define EMAC_MAC_DEBUG_RFCFCSTS(n)     (((n) << EMAC_MAC_DEBUG_RFCFCSTS_SHIFT) & EMAC_MAC_DEBUG_RFCFCSTS_MASK)
#define EMAC_MAC_DEBUG_TPESTS          (1 << 16) /* Bit 16: MAC GMII Or MII Transmit Protocol Engine Status */
#define EMAC_MAC_DEBUG_TFCSTS_SHIFT    (17)      /* Bits 17-19: MAC Transmit Packet Controller Status */
#define EMAC_MAC_DEBUG_TFCSTS_MASK     (0x3 << EMAC_MAC_DEBUG_TFCSTS_SHIFT)
#define EMAC_MAC_DEBUG_TFCSTS(n)       (((n) << EMAC_MAC_DEBUG_TFCSTS_SHIFT) & EMAC_MAC_DEBUG_TFCSTS_MASK)

/* MAC Hardware Feature 0 (MAC_HW_FEATURE0) */
#define EMAC_MAC_HW_FEATURE0_MIISEL              (1 << 0)  /* Bit 0: 10 or 100 Mbit/s Support Feature */
#define EMAC_MAC_HW_FEATURE0_GMIISEL             (1 << 1)  /* Bit 1: 1000 Mbit/s Support Feature */
#define EMAC_MAC_HW_FEATURE0_HDSEL               (1 << 2)  /* Bit 2: Half-Duplex Support Feature */
#define EMAC_MAC_HW_FEATURE0_PCSSEL              (1 << 3)  /* Bit 3: PCS Select */
#define EMAC_MAC_HW_FEATURE0_VLHASH              (1 << 4)  /* Bit 4: VLAN Hash Filter Feature */
#define EMAC_MAC_HW_FEATURE0_SMASEL              (1 << 5)  /* Bit 5: SMA (MDIO) Interface Feature */
#define EMAC_MAC_HW_FEATURE0_RWKSEL              (1 << 6)  /* Bit 6: PMT Remote Wake-Up Packet Feature */
#define EMAC_MAC_HW_FEATURE0_MGKSEL              (1 << 7)  /* Bit 7: PMT Magic Packet Feature */
#define EMAC_MAC_HW_FEATURE0_MMCSEL              (1 << 8)  /* Bit 8: MAC Management Counters (MMC) Feature */
#define EMAC_MAC_HW_FEATURE0_ARPOFFSEL           (1 << 9)  /* Bit 9: ARP Offload Feature */
#define EMAC_MAC_HW_FEATURE0_TSSEL               (1 << 12) /* Bit 12: IEEE 1588-2008 Timestamp Feature */
#define EMAC_MAC_HW_FEATURE0_EEESEL              (1 << 13) /* Bit 13: Energy Efficient Ethernet (EEE) Feature */
#define EMAC_MAC_HW_FEATURE0_TXCOESEL            (1 << 14) /* Bit 14: Transmit Checksum Offload Feature */
#define EMAC_MAC_HW_FEATURE0_RXCOESEL            (1 << 16) /* Bit 16: Receive Checksum Offload Feature */
#define EMAC_MAC_HW_FEATURE0_ADDMACADRSEL_SHIFT  (18)      /* Bits 18-23: MAC Addresses 1-31 */
#define EMAC_MAC_HW_FEATURE0_ADDMACADRSEL_MASK   (0x1f << EMAC_MAC_HW_FEATURE0_ADDMACADRSEL_SHIFT)
#define EMAC_MAC_HW_FEATURE0_ADDMACADRSEL(n)     (((n) << EMAC_MAC_HW_FEATURE0_ADDMACADRSEL_SHIFT) & EMAC_MAC_HW_FEATURE0_ADDMACADRSEL_MASK)
#define EMAC_MAC_HW_FEATURE0_MACADR32SEL         (1 << 23) /* Bit 23: MAC Addresses 32-63 */
#define EMAC_MAC_HW_FEATURE0_MACADR64SEL         (1 << 24) /* Bit 24: MAC Addresses 64-127 */
#define EMAC_MAC_HW_FEATURE0_TSSTSSEL_SHIFT      (25)      /* Bits 25-27: Timestamp System Time Source Feature */
#define EMAC_MAC_HW_FEATURE0_TSSTSSEL_MASK       (0x3 << EMAC_MAC_HW_FEATURE0_TSSTSSEL_SHIFT)
#define EMAC_MAC_HW_FEATURE0_TSSTSSEL(n)         (((n) << EMAC_MAC_HW_FEATURE0_TSSTSSEL_SHIFT) & EMAC_MAC_HW_FEATURE0_TSSTSSEL_MASK)
#define EMAC_MAC_HW_FEATURE0_SAVLANINS           (1 << 27) /* Bit 27: SA or VLAN Insertion Feature */
#define EMAC_MAC_HW_FEATURE0_ACTPHYSEL_SHIFT     (28)      /* Bits 28-31: Active PHY Feature */
#define EMAC_MAC_HW_FEATURE0_ACTPHYSEL_MASK      (0x7 << EMAC_MAC_HW_FEATURE0_ACTPHYSEL_SHIFT)
#define EMAC_MAC_HW_FEATURE0_ACTPHYSEL(n)        (((n) << EMAC_MAC_HW_FEATURE0_ACTPHYSEL_SHIFT) & EMAC_MAC_HW_FEATURE0_ACTPHYSEL_MASK)

/* MAC Hardware Feature 1 (MAC_HW_FEATURE1) */
#define EMAC_MAC_HW_FEATURE1_RXFIFOSIZE_SHIFT  (0) /* Bits 0-5: MTL Receive FIFO Size Feature */
#define EMAC_MAC_HW_FEATURE1_RXFIFOSIZE_MASK   (0x1f << EMAC_MAC_HW_FEATURE1_RXFIFOSIZE_SHIFT)
#define EMAC_MAC_HW_FEATURE1_RXFIFOSIZE(n)     (((n) << EMAC_MAC_HW_FEATURE1_RXFIFOSIZE_SHIFT) & EMAC_MAC_HW_FEATURE1_RXFIFOSIZE_MASK)
#define EMAC_MAC_HW_FEATURE1_SPRAM             (1 << 5) /* Bit 5: Single Port RAM Feature */
#define EMAC_MAC_HW_FEATURE1_TXFIFOSIZE_SHIFT  (6)      /* Bits 6-11: MTL Transmit FIFO Size Feature */
#define EMAC_MAC_HW_FEATURE1_TXFIFOSIZE_MASK   (0x1f << EMAC_MAC_HW_FEATURE1_TXFIFOSIZE_SHIFT)
#define EMAC_MAC_HW_FEATURE1_TXFIFOSIZE(n)     (((n) << EMAC_MAC_HW_FEATURE1_TXFIFOSIZE_SHIFT) & EMAC_MAC_HW_FEATURE1_TXFIFOSIZE_MASK)
#define EMAC_MAC_HW_FEATURE1_OSTEN             (1 << 11) /* Bit 11: One-Step Timestamping Enable Feature */
#define EMAC_MAC_HW_FEATURE1_PTOEN             (1 << 12) /* Bit 12: PTP Offload Enable Feature */
#define EMAC_MAC_HW_FEATURE1_ADVTHWORD         (1 << 13) /* Bit 13: IEEE 1588 High-Word Feature */
#define EMAC_MAC_HW_FEATURE1_ADDR64_SHIFT      (14)      /* Bits 14-16: Address Width Feature */
#define EMAC_MAC_HW_FEATURE1_ADDR64_MASK       (0x3 << EMAC_MAC_HW_FEATURE1_ADDR64_SHIFT)
#define EMAC_MAC_HW_FEATURE1_ADDR64(n)         (((n) << EMAC_MAC_HW_FEATURE1_ADDR64_SHIFT) & EMAC_MAC_HW_FEATURE1_ADDR64_MASK)
#define EMAC_MAC_HW_FEATURE1_DCBEN             (1 << 16) /* Bit 16: DCB Enable Feature */
#define EMAC_MAC_HW_FEATURE1_SPHEN             (1 << 17) /* Bit 17: Split Header Enable Feature */
#define EMAC_MAC_HW_FEATURE1_TSOEN             (1 << 18) /* Bit 18: TCP Segmentation Offload Enable Feature */
#define EMAC_MAC_HW_FEATURE1_DBGMEMA           (1 << 19) /* Bit 19: DMA Debug Registers Enable Feature */
#define EMAC_MAC_HW_FEATURE1_AVSEL             (1 << 20) /* Bit 20: AV Feature */
#define EMAC_MAC_HW_FEATURE1_RAVSEL            (1 << 21) /* Bit 21: Receive Side-Only AV Feature */
#define EMAC_MAC_HW_FEATURE1_POUOST            (1 << 23) /* Bit 23: One Step For PTP Over UDP/IP Feature */
#define EMAC_MAC_HW_FEATURE1_HASHTBLSZ_SHIFT   (24)      /* Bits 24-26: Hash Table Size */
#define EMAC_MAC_HW_FEATURE1_HASHTBLSZ_MASK    (0x3 << EMAC_MAC_HW_FEATURE1_HASHTBLSZ_SHIFT)
#define EMAC_MAC_HW_FEATURE1_HASHTBLSZ(n)      (((n) << EMAC_MAC_HW_FEATURE1_HASHTBLSZ_SHIFT) & EMAC_MAC_HW_FEATURE1_HASHTBLSZ_MASK)
#define EMAC_MAC_HW_FEATURE1_L3L4FNUM_SHIFT    (27) /* Bits 27-31: L3 Or L4 Filter Number */
#define EMAC_MAC_HW_FEATURE1_L3L4FNUM_MASK     (0xf << EMAC_MAC_HW_FEATURE1_L3L4FNUM_SHIFT)
#define EMAC_MAC_HW_FEATURE1_L3L4FNUM(n)       (((n) << EMAC_MAC_HW_FEATURE1_L3L4FNUM_SHIFT) & EMAC_MAC_HW_FEATURE1_L3L4FNUM_MASK)

/* MAC Hardware Feature 2 (MAC_HW_FEATURE2) */
#define EMAC_MAC_HW_FEATURE2_RXQCNT_SHIFT      (0) /* Bits 0-4: Number Of MTL Receive Queues */
#define EMAC_MAC_HW_FEATURE2_RXQCNT_MASK       (0xf << EMAC_MAC_HW_FEATURE2_RXQCNT_SHIFT)
#define EMAC_MAC_HW_FEATURE2_RXQCNT(n)         (((n) << EMAC_MAC_HW_FEATURE2_RXQCNT_SHIFT) & EMAC_MAC_HW_FEATURE2_RXQCNT_MASK)
#define EMAC_MAC_HW_FEATURE2_TXQCNT_SHIFT      (6) /* Bits 6-10: Number Of MTL Transmit Queues */
#define EMAC_MAC_HW_FEATURE2_TXQCNT_MASK       (0xf << EMAC_MAC_HW_FEATURE2_TXQCNT_SHIFT)
#define EMAC_MAC_HW_FEATURE2_TXQCNT(n)         (((n) << EMAC_MAC_HW_FEATURE2_TXQCNT_SHIFT) & EMAC_MAC_HW_FEATURE2_TXQCNT_MASK)
#define EMAC_MAC_HW_FEATURE2_RXCHCNT_SHIFT     (12) /* Bits 12-16: Number Of DMA Receive Channels */
#define EMAC_MAC_HW_FEATURE2_RXCHCNT_MASK      (0xf << EMAC_MAC_HW_FEATURE2_RXCHCNT_SHIFT)
#define EMAC_MAC_HW_FEATURE2_RXCHCNT(n)        (((n) << EMAC_MAC_HW_FEATURE2_RXCHCNT_SHIFT) & EMAC_MAC_HW_FEATURE2_RXCHCNT_MASK)
#define EMAC_MAC_HW_FEATURE2_TXCHCNT_SHIFT     (18) /* Bits 18-22: Number Of DMA Transmit Channels */
#define EMAC_MAC_HW_FEATURE2_TXCHCNT_MASK      (0xf << EMAC_MAC_HW_FEATURE2_TXCHCNT_SHIFT)
#define EMAC_MAC_HW_FEATURE2_TXCHCNT(n)        (((n) << EMAC_MAC_HW_FEATURE2_TXCHCNT_SHIFT) & EMAC_MAC_HW_FEATURE2_TXCHCNT_MASK)
#define EMAC_MAC_HW_FEATURE2_PPSOUTNUM_SHIFT   (24) /* Bits 24-27: Number Of PPS Outputs */
#define EMAC_MAC_HW_FEATURE2_PPSOUTNUM_MASK    (0x7 << EMAC_MAC_HW_FEATURE2_PPSOUTNUM_SHIFT)
#define EMAC_MAC_HW_FEATURE2_PPSOUTNUM(n)      (((n) << EMAC_MAC_HW_FEATURE2_PPSOUTNUM_SHIFT) & EMAC_MAC_HW_FEATURE2_PPSOUTNUM_MASK)
#define EMAC_MAC_HW_FEATURE2_AUXSNAPNUM_SHIFT  (28) /* Bits 28-31: Number Of Auxiliary Snapshot Inputs */
#define EMAC_MAC_HW_FEATURE2_AUXSNAPNUM_MASK   (0x7 << EMAC_MAC_HW_FEATURE2_AUXSNAPNUM_SHIFT)
#define EMAC_MAC_HW_FEATURE2_AUXSNAPNUM(n)     (((n) << EMAC_MAC_HW_FEATURE2_AUXSNAPNUM_SHIFT) & EMAC_MAC_HW_FEATURE2_AUXSNAPNUM_MASK)

/* MAC Hardware Feature 3 (MAC_HW_FEATURE3) */
#define EMAC_MAC_HW_FEATURE3_NRVF_SHIFT    (0) /* Bits 0-3: Number Of Extended VLAN Tag Filters Indicates the number of selected extended VLAN tag filters. */
#define EMAC_MAC_HW_FEATURE3_NRVF_MASK     (0x7 << EMAC_MAC_HW_FEATURE3_NRVF_SHIFT)
#define EMAC_MAC_HW_FEATURE3_NRVF(n)       (((n) << EMAC_MAC_HW_FEATURE3_NRVF_SHIFT) & EMAC_MAC_HW_FEATURE3_NRVF_MASK)
#define EMAC_MAC_HW_FEATURE3_CBTISEL       (1 << 4)  /* Bit 4: Queue/Channel Based VLAN Tag Insertion On Transmit Feature */
#define EMAC_MAC_HW_FEATURE3_DVLAN         (1 << 5)  /* Bit 5: Double VLAN Tag Processing Feature */
#define EMAC_MAC_HW_FEATURE3_PDUPSEL       (1 << 9)  /* Bit 9: Broadcast/Multicast Packet Duplication Feature */
#define EMAC_MAC_HW_FEATURE3_FRPSEL        (1 << 10) /* Bit 10: Flexible Receive Parser Feature */
#define EMAC_MAC_HW_FEATURE3_FRPBS_SHIFT   (11)      /* Bits 11-13: Flexible Receive Parser Buffer Size */
#define EMAC_MAC_HW_FEATURE3_FRPBS_MASK    (0x3 << EMAC_MAC_HW_FEATURE3_FRPBS_SHIFT)
#define EMAC_MAC_HW_FEATURE3_FRPBS(n)      (((n) << EMAC_MAC_HW_FEATURE3_FRPBS_SHIFT) & EMAC_MAC_HW_FEATURE3_FRPBS_MASK)
#define EMAC_MAC_HW_FEATURE3_FRPES_SHIFT   (13) /* Bits 13-15: Flexible Receive Parser Table Entry Size */
#define EMAC_MAC_HW_FEATURE3_FRPES_MASK    (0x3 << EMAC_MAC_HW_FEATURE3_FRPES_SHIFT)
#define EMAC_MAC_HW_FEATURE3_FRPES(n)      (((n) << EMAC_MAC_HW_FEATURE3_FRPES_SHIFT) & EMAC_MAC_HW_FEATURE3_FRPES_MASK)
#define EMAC_MAC_HW_FEATURE3_ESTSEL        (1 << 16) /* Bit 16: Enhancements To Scheduling Traffic Feature */
#define EMAC_MAC_HW_FEATURE3_ESTDEP_SHIFT  (17)      /* Bits 17-20: Depth Of Gate Control List */
#define EMAC_MAC_HW_FEATURE3_ESTDEP_MASK   (0x7 << EMAC_MAC_HW_FEATURE3_ESTDEP_SHIFT)
#define EMAC_MAC_HW_FEATURE3_ESTDEP(n)     (((n) << EMAC_MAC_HW_FEATURE3_ESTDEP_SHIFT) & EMAC_MAC_HW_FEATURE3_ESTDEP_MASK)
#define EMAC_MAC_HW_FEATURE3_ESTWID_SHIFT  (20) /* Bits 20-22: Estimated Time Interval Width */
#define EMAC_MAC_HW_FEATURE3_ESTWID_MASK   (0x3 << EMAC_MAC_HW_FEATURE3_ESTWID_SHIFT)
#define EMAC_MAC_HW_FEATURE3_ESTWID(n)     (((n) << EMAC_MAC_HW_FEATURE3_ESTWID_SHIFT) & EMAC_MAC_HW_FEATURE3_ESTWID_MASK)
#define EMAC_MAC_HW_FEATURE3_FPESEL        (1 << 26) /* Bit 26: Frame Preemption Feature */
#define EMAC_MAC_HW_FEATURE3_TBSSEL        (1 << 27) /* Bit 27: Time-Based Scheduling Feature */
#define EMAC_MAC_HW_FEATURE3_ASP_SHIFT     (28)      /* Bits 28-30: Automotive Safety Package */
#define EMAC_MAC_HW_FEATURE3_ASP_MASK      (0x3 << EMAC_MAC_HW_FEATURE3_ASP_SHIFT)
#define EMAC_MAC_HW_FEATURE3_ASP(n)        (((n) << EMAC_MAC_HW_FEATURE3_ASP_SHIFT) & EMAC_MAC_HW_FEATURE3_ASP_MASK)

/* MAC DPP FSM Interrupt Status (MAC_DPP_FSM_INTERRUPT_STATUS) */
#define EMAC_MAC_DPP_FSM_INTERRUPT_STATUS_RDPES   (1 << 2)  /* Bit 2: Read Descriptor Parity Checker Error Status */
#define EMAC_MAC_DPP_FSM_INTERRUPT_STATUS_MPES    (1 << 3)  /* Bit 3: MTL Data Path Parity Checker Error Status */
#define EMAC_MAC_DPP_FSM_INTERRUPT_STATUS_MTSPES  (1 << 4)  /* Bit 4: MTL Transmit Status Data Path Parity Checker Error Status */
#define EMAC_MAC_DPP_FSM_INTERRUPT_STATUS_ARPES   (1 << 5)  /* Bit 5: Application Receive Interface Data Path Parity Error Status */
#define EMAC_MAC_DPP_FSM_INTERRUPT_STATUS_TTES    (1 << 8)  /* Bit 8: Transmit FSM Timeout Error Status */
#define EMAC_MAC_DPP_FSM_INTERRUPT_STATUS_RTES    (1 << 9)  /* Bit 9: Receive FSM Timeout Error Status */
#define EMAC_MAC_DPP_FSM_INTERRUPT_STATUS_ATES    (1 << 11) /* Bit 11: APP FSM Timeout Error Status */
#define EMAC_MAC_DPP_FSM_INTERRUPT_STATUS_PTES    (1 << 12) /* Bit 12: PTP FSM Timeout Error Status */
#define EMAC_MAC_DPP_FSM_INTERRUPT_STATUS_MSTTES  (1 << 16) /* Bit 16: Master Read Or Write Timeout Error Status */
#define EMAC_MAC_DPP_FSM_INTERRUPT_STATUS_FSMPES  (1 << 24) /* Bit 24: FSM State Parity Error Status */

/* MAC FSM Control (MAC_FSM_CONTROL) */
#define EMAC_MAC_FSM_CONTROL_TMOUTEN  (1 << 0)  /* Bit 0: Time Out Enable */
#define EMAC_MAC_FSM_CONTROL_PRTYEN   (1 << 1)  /* Bit 1: Parity Enable */
#define EMAC_MAC_FSM_CONTROL_TTEIN    (1 << 8)  /* Bit 8: Transmit FSM Timeout Error Injection */
#define EMAC_MAC_FSM_CONTROL_RTEIN    (1 << 9)  /* Bit 9: Receive FSM Timeout Error Injection */
#define EMAC_MAC_FSM_CONTROL_ATEIN    (1 << 11) /* Bit 11: APP FSM Timeout Error Injection */
#define EMAC_MAC_FSM_CONTROL_PTEIN    (1 << 12) /* Bit 12: PTP FSM Timeout Error Injection */
#define EMAC_MAC_FSM_CONTROL_TPEIN    (1 << 16) /* Bit 16: Transmit FSM Parity Error Injection */
#define EMAC_MAC_FSM_CONTROL_RPEIN    (1 << 17) /* Bit 17: Receive FSM Parity Error Injection */
#define EMAC_MAC_FSM_CONTROL_APEIN    (1 << 19) /* Bit 19: APP FSM Parity Error Injection */
#define EMAC_MAC_FSM_CONTROL_PPEIN    (1 << 20) /* Bit 20: PTP FSM Parity Error Injection */
#define EMAC_MAC_FSM_CONTROL_TLGRNML  (1 << 24) /* Bit 24: Transmit Large Or Normal Mode Select */
#define EMAC_MAC_FSM_CONTROL_RLGRNML  (1 << 25) /* Bit 25: Receive Large Or Normal Mode Select */
#define EMAC_MAC_FSM_CONTROL_ALGRNML  (1 << 27) /* Bit 27: APP Large Or Normal Mode Select */
#define EMAC_MAC_FSM_CONTROL_PLGRNML  (1 << 28) /* Bit 28: PTP Large Or Normal Mode Select */

/* MAC FSM ACT Timer (MAC_FSM_ACT_TIMER) */
#define EMAC_MAC_FSM_ACT_TIMER_TMR_SHIFT     (0) /* Bits 0-10: CSR Clocks For 1 us Tic */
#define EMAC_MAC_FSM_ACT_TIMER_TMR_MASK      (0x3ff << EMAC_MAC_FSM_ACT_TIMER_TMR_SHIFT)
#define EMAC_MAC_FSM_ACT_TIMER_TMR(n)        (((n) << EMAC_MAC_FSM_ACT_TIMER_TMR_SHIFT) & EMAC_MAC_FSM_ACT_TIMER_TMR_MASK)
#define EMAC_MAC_FSM_ACT_TIMER_NTMRMD_SHIFT  (16) /* Bits 16-20: Normal Mode Timeout Value */
#define EMAC_MAC_FSM_ACT_TIMER_NTMRMD_MASK   (0xf << EMAC_MAC_FSM_ACT_TIMER_NTMRMD_SHIFT)
#define EMAC_MAC_FSM_ACT_TIMER_NTMRMD(n)     (((n) << EMAC_MAC_FSM_ACT_TIMER_NTMRMD_SHIFT) & EMAC_MAC_FSM_ACT_TIMER_NTMRMD_MASK)
#define EMAC_MAC_FSM_ACT_TIMER_LTMRMD_SHIFT  (20) /* Bits 20-24: Large Mode Timeout Value */
#define EMAC_MAC_FSM_ACT_TIMER_LTMRMD_MASK   (0xf << EMAC_MAC_FSM_ACT_TIMER_LTMRMD_SHIFT)
#define EMAC_MAC_FSM_ACT_TIMER_LTMRMD(n)     (((n) << EMAC_MAC_FSM_ACT_TIMER_LTMRMD_SHIFT) & EMAC_MAC_FSM_ACT_TIMER_LTMRMD_MASK)

/* SCS_REG 1 (SCS_REG1) */
#define EMAC_SCS_REG1_MAC_SCS1_SHIFT  (0) /* Bits 0-32: MAC SCS 1 */
#define EMAC_SCS_REG1_MAC_SCS1_MASK   (0xffffffff << EMAC_SCS_REG1_MAC_SCS1_SHIFT)
#define EMAC_SCS_REG1_MAC_SCS1(n)     (((n) << EMAC_SCS_REG1_MAC_SCS1_SHIFT) & EMAC_SCS_REG1_MAC_SCS1_MASK)

/* MAC MDIO Address (MAC_MDIO_ADDRESS) */
#define EMAC_MAC_MDIO_ADDRESS_GB         (1 << 0) /* Bit 0: GMII Busy */
#define EMAC_MAC_MDIO_ADDRESS_C45E       (1 << 1) /* Bit 1: Clause 45 PHY Enable */
#define EMAC_MAC_MDIO_ADDRESS_GOC_0      (1 << 2) /* Bit 2: GMII Operation Command 0 */
#define EMAC_MAC_MDIO_ADDRESS_GOC_1      (1 << 3) /* Bit 3: GMII Operation Command 1 */
#define EMAC_MAC_MDIO_ADDRESS_SKAP       (1 << 4) /* Bit 4: Skip Address Packet */
#define EMAC_MAC_MDIO_ADDRESS_CR_SHIFT   (8)      /* Bits 8-12: CSR Clock Range */
#define EMAC_MAC_MDIO_ADDRESS_CR_MASK    (0xf << EMAC_MAC_MDIO_ADDRESS_CR_SHIFT)
#define EMAC_MAC_MDIO_ADDRESS_CR(n)      (((n) << EMAC_MAC_MDIO_ADDRESS_CR_SHIFT) & EMAC_MAC_MDIO_ADDRESS_CR_MASK)
#define EMAC_MAC_MDIO_ADDRESS_NTC_SHIFT  (12) /* Bits 12-15: Number Of Trailing Clocks */
#define EMAC_MAC_MDIO_ADDRESS_NTC_MASK   (0x7 << EMAC_MAC_MDIO_ADDRESS_NTC_SHIFT)
#define EMAC_MAC_MDIO_ADDRESS_NTC(n)     (((n) << EMAC_MAC_MDIO_ADDRESS_NTC_SHIFT) & EMAC_MAC_MDIO_ADDRESS_NTC_MASK)
#define EMAC_MAC_MDIO_ADDRESS_RDA_SHIFT  (16) /* Bits 16-21: Register Or Device Address */
#define EMAC_MAC_MDIO_ADDRESS_RDA_MASK   (0x1f << EMAC_MAC_MDIO_ADDRESS_RDA_SHIFT)
#define EMAC_MAC_MDIO_ADDRESS_RDA(n)     (((n) << EMAC_MAC_MDIO_ADDRESS_RDA_SHIFT) & EMAC_MAC_MDIO_ADDRESS_RDA_MASK)
#define EMAC_MAC_MDIO_ADDRESS_PA_SHIFT   (21) /* Bits 21-26: Physical Layer Address */
#define EMAC_MAC_MDIO_ADDRESS_PA_MASK    (0x1f << EMAC_MAC_MDIO_ADDRESS_PA_SHIFT)
#define EMAC_MAC_MDIO_ADDRESS_PA(n)      (((n) << EMAC_MAC_MDIO_ADDRESS_PA_SHIFT) & EMAC_MAC_MDIO_ADDRESS_PA_MASK)
#define EMAC_MAC_MDIO_ADDRESS_BTB        (1 << 26) /* Bit 26: Back-To-Back Transactions */
#define EMAC_MAC_MDIO_ADDRESS_PSE        (1 << 27) /* Bit 27: Preamble Suppression Enable */

/* MAC MDIO Data (MAC_MDIO_DATA) */
#define EMAC_MAC_MDIO_DATA_GD_SHIFT  (0) /* Bits 0-16: GMII Data */
#define EMAC_MAC_MDIO_DATA_GD_MASK   (0xffff << EMAC_MAC_MDIO_DATA_GD_SHIFT)
#define EMAC_MAC_MDIO_DATA_GD(n)     (((n) << EMAC_MAC_MDIO_DATA_GD_SHIFT) & EMAC_MAC_MDIO_DATA_GD_MASK)
#define EMAC_MAC_MDIO_DATA_RA_SHIFT  (16) /* Bits 16-32: Register Address */
#define EMAC_MAC_MDIO_DATA_RA_MASK   (0xffff << EMAC_MAC_MDIO_DATA_RA_SHIFT)
#define EMAC_MAC_MDIO_DATA_RA(n)     (((n) << EMAC_MAC_MDIO_DATA_RA_SHIFT) & EMAC_MAC_MDIO_DATA_RA_MASK)

/* MAC CSR Software Control (MAC_CSR_SW_CTRL) */
#define EMAC_MAC_CSR_SW_CTRL_RCWE  (1 << 0) /* Bit 0: Enable Register Write 1 To Clear (W1C) */
#define EMAC_MAC_CSR_SW_CTRL_SEEN  (1 << 8) /* Bit 8: Slave Error Response Enable */

/* MAC FPE Control STS (MAC_FPE_CTRL_STS) */
#define EMAC_MAC_FPE_CTRL_STS_EFPE      (1 << 0)  /* Bit 0: Enable Transmit Frame Preemption */
#define EMAC_MAC_FPE_CTRL_STS_SVER      (1 << 1)  /* Bit 1: Send Verify mPacket */
#define EMAC_MAC_FPE_CTRL_STS_SRSP      (1 << 2)  /* Bit 2: Send Respond mPacket */
#define EMAC_MAC_FPE_CTRL_STS_S1_SET_0  (1 << 3)  /* Bit 3: S1 SET 0 */
#define EMAC_MAC_FPE_CTRL_STS_RVER      (1 << 16) /* Bit 16: Received Verify Frame */
#define EMAC_MAC_FPE_CTRL_STS_RRSP      (1 << 17) /* Bit 17: Received Respond Frame */
#define EMAC_MAC_FPE_CTRL_STS_TVER      (1 << 18) /* Bit 18: Transmitted Verify Frame */
#define EMAC_MAC_FPE_CTRL_STS_TRSP      (1 << 19) /* Bit 19: Transmitted Respond Frame */

/* MAC Presentation Time (MAC_PRESN_TIME_NS) */
#define EMAC_MAC_PRESN_TIME_NS_MPTN_SHIFT  (0) /* Bits 0-32: MAC 1722 Presentation Time (In Nanoseconds) */
#define EMAC_MAC_PRESN_TIME_NS_MPTN_MASK   (0xffffffff << EMAC_MAC_PRESN_TIME_NS_MPTN_SHIFT)
#define EMAC_MAC_PRESN_TIME_NS_MPTN(n)     (((n) << EMAC_MAC_PRESN_TIME_NS_MPTN_SHIFT) & EMAC_MAC_PRESN_TIME_NS_MPTN_MASK)

/* MAC Presentation Time Update (MAC_PRESN_TIME_UPDT) */
#define EMAC_MAC_PRESN_TIME_UPDT_MPTU_SHIFT  (0) /* Bits 0-32: MAC 1722 Presentation Time Update */
#define EMAC_MAC_PRESN_TIME_UPDT_MPTU_MASK   (0xffffffff << EMAC_MAC_PRESN_TIME_UPDT_MPTU_SHIFT)
#define EMAC_MAC_PRESN_TIME_UPDT_MPTU(n)     (((n) << EMAC_MAC_PRESN_TIME_UPDT_MPTU_SHIFT) & EMAC_MAC_PRESN_TIME_UPDT_MPTU_MASK)

/* MAC Address 0 High (MAC_ADDRESS0_HIGH) */
#define EMAC_MAC_ADDRESS0_HIGH_ADDRHI_SHIFT  (0) /* Bits 0-16: MAC Address 0 [47:32] */
#define EMAC_MAC_ADDRESS0_HIGH_ADDRHI_MASK   (0xffff << EMAC_MAC_ADDRESS0_HIGH_ADDRHI_SHIFT)
#define EMAC_MAC_ADDRESS0_HIGH_ADDRHI(n)     (((n) << EMAC_MAC_ADDRESS0_HIGH_ADDRHI_SHIFT) & EMAC_MAC_ADDRESS0_HIGH_ADDRHI_MASK)
#define EMAC_MAC_ADDRESS0_HIGH_DCS_SHIFT     (16) /* Bits 16-18: DMA Channel Select */
#define EMAC_MAC_ADDRESS0_HIGH_DCS_MASK      (0x3 << EMAC_MAC_ADDRESS0_HIGH_DCS_SHIFT)
#define EMAC_MAC_ADDRESS0_HIGH_DCS(n)        (((n) << EMAC_MAC_ADDRESS0_HIGH_DCS_SHIFT) & EMAC_MAC_ADDRESS0_HIGH_DCS_MASK)
#define EMAC_MAC_ADDRESS0_HIGH_AE            (1 << 31) /* Bit 31: Address Enable */

/* MAC Address 0 Low (MAC_ADDRESS0_LOW) */
#define EMAC_MAC_ADDRESS0_LOW_ADDRLO_SHIFT  (0) /* Bits 0-32: MAC Address 0 [31:0] */
#define EMAC_MAC_ADDRESS0_LOW_ADDRLO_MASK   (0xffffffff << EMAC_MAC_ADDRESS0_LOW_ADDRLO_SHIFT)
#define EMAC_MAC_ADDRESS0_LOW_ADDRLO(n)     (((n) << EMAC_MAC_ADDRESS0_LOW_ADDRLO_SHIFT) & EMAC_MAC_ADDRESS0_LOW_ADDRLO_MASK)

/* MAC Address 1 High (MAC_ADDRESS1_HIGH) */
#define EMAC_MAC_ADDRESS1_HIGH_ADDRHI_SHIFT  (0) /* Bits 0-16: MAC Address 1 [47:32] */
#define EMAC_MAC_ADDRESS1_HIGH_ADDRHI_MASK   (0xffff << EMAC_MAC_ADDRESS1_HIGH_ADDRHI_SHIFT)
#define EMAC_MAC_ADDRESS1_HIGH_ADDRHI(n)     (((n) << EMAC_MAC_ADDRESS1_HIGH_ADDRHI_SHIFT) & EMAC_MAC_ADDRESS1_HIGH_ADDRHI_MASK)
#define EMAC_MAC_ADDRESS1_HIGH_DCS_SHIFT     (16) /* Bits 16-18: DMA Channel Select */
#define EMAC_MAC_ADDRESS1_HIGH_DCS_MASK      (0x3 << EMAC_MAC_ADDRESS1_HIGH_DCS_SHIFT)
#define EMAC_MAC_ADDRESS1_HIGH_DCS(n)        (((n) << EMAC_MAC_ADDRESS1_HIGH_DCS_SHIFT) & EMAC_MAC_ADDRESS1_HIGH_DCS_MASK)
#define EMAC_MAC_ADDRESS1_HIGH_MBC_SHIFT     (24) /* Bits 24-30: Mask Byte Control */
#define EMAC_MAC_ADDRESS1_HIGH_MBC_MASK      (0x3f << EMAC_MAC_ADDRESS1_HIGH_MBC_SHIFT)
#define EMAC_MAC_ADDRESS1_HIGH_MBC(n)        (((n) << EMAC_MAC_ADDRESS1_HIGH_MBC_SHIFT) & EMAC_MAC_ADDRESS1_HIGH_MBC_MASK)
#define EMAC_MAC_ADDRESS1_HIGH_SA            (1 << 30) /* Bit 30: Source Address */
#define EMAC_MAC_ADDRESS1_HIGH_AE            (1 << 31) /* Bit 31: Address Enable */

/* MAC Address 1 Low (MAC_ADDRESS1_LOW) */
#define EMAC_MAC_ADDRESS1_LOW_ADDRLO_SHIFT  (0) /* Bits 0-32: MAC Address 1 [31:0] */
#define EMAC_MAC_ADDRESS1_LOW_ADDRLO_MASK   (0xffffffff << EMAC_MAC_ADDRESS1_LOW_ADDRLO_SHIFT)
#define EMAC_MAC_ADDRESS1_LOW_ADDRLO(n)     (((n) << EMAC_MAC_ADDRESS1_LOW_ADDRLO_SHIFT) & EMAC_MAC_ADDRESS1_LOW_ADDRLO_MASK)

/* MAC Address 2 High (MAC_ADDRESS2_HIGH) */
#define EMAC_MAC_ADDRESS2_HIGH_ADDRHI_SHIFT  (0) /* Bits 0-16: MAC Address 1 [47:32] */
#define EMAC_MAC_ADDRESS2_HIGH_ADDRHI_MASK   (0xffff << EMAC_MAC_ADDRESS2_HIGH_ADDRHI_SHIFT)
#define EMAC_MAC_ADDRESS2_HIGH_ADDRHI(n)     (((n) << EMAC_MAC_ADDRESS2_HIGH_ADDRHI_SHIFT) & EMAC_MAC_ADDRESS2_HIGH_ADDRHI_MASK)
#define EMAC_MAC_ADDRESS2_HIGH_DCS_SHIFT     (16) /* Bits 16-18: DMA Channel Select */
#define EMAC_MAC_ADDRESS2_HIGH_DCS_MASK      (0x3 << EMAC_MAC_ADDRESS2_HIGH_DCS_SHIFT)
#define EMAC_MAC_ADDRESS2_HIGH_DCS(n)        (((n) << EMAC_MAC_ADDRESS2_HIGH_DCS_SHIFT) & EMAC_MAC_ADDRESS2_HIGH_DCS_MASK)
#define EMAC_MAC_ADDRESS2_HIGH_MBC_SHIFT     (24) /* Bits 24-30: Mask Byte Control */
#define EMAC_MAC_ADDRESS2_HIGH_MBC_MASK      (0x3f << EMAC_MAC_ADDRESS2_HIGH_MBC_SHIFT)
#define EMAC_MAC_ADDRESS2_HIGH_MBC(n)        (((n) << EMAC_MAC_ADDRESS2_HIGH_MBC_SHIFT) & EMAC_MAC_ADDRESS2_HIGH_MBC_MASK)
#define EMAC_MAC_ADDRESS2_HIGH_SA            (1 << 30) /* Bit 30: Source Address */
#define EMAC_MAC_ADDRESS2_HIGH_AE            (1 << 31) /* Bit 31: Address Enable */

/* MAC Address 2 Low (MAC_ADDRESS2_LOW) */
#define EMAC_MAC_ADDRESS2_LOW_ADDRLO_SHIFT  (0) /* Bits 0-32: MAC Address 1 [31:0] */
#define EMAC_MAC_ADDRESS2_LOW_ADDRLO_MASK   (0xffffffff << EMAC_MAC_ADDRESS2_LOW_ADDRLO_SHIFT)
#define EMAC_MAC_ADDRESS2_LOW_ADDRLO(n)     (((n) << EMAC_MAC_ADDRESS2_LOW_ADDRLO_SHIFT) & EMAC_MAC_ADDRESS2_LOW_ADDRLO_MASK)

/* MMC Control (MMC_CONTROL) */
#define EMAC_MMC_CONTROL_CNTRST      (1 << 0) /* Bit 0: Counters Reset */
#define EMAC_MMC_CONTROL_CNTSTOPRO   (1 << 1) /* Bit 1: Counter Stop Rollover */
#define EMAC_MMC_CONTROL_RSTONRD     (1 << 2) /* Bit 2: Reset On Read */
#define EMAC_MMC_CONTROL_CNTFREEZ    (1 << 3) /* Bit 3: MMC Counter Freeze */
#define EMAC_MMC_CONTROL_CNTPRST     (1 << 4) /* Bit 4: Counters Preset */
#define EMAC_MMC_CONTROL_CNTPRSTLVL  (1 << 5) /* Bit 5: Full-Half Preset */
#define EMAC_MMC_CONTROL_UCDBC       (1 << 8) /* Bit 8: Update MMC Counters For Dropped Broadcast Packets */

/* MMC Receive Interrupt (MMC_RX_INTERRUPT) */
#define EMAC_MMC_RX_INTERRUPT_RXGBPKTIS           (1 << 0)  /* Bit 0: MMC Receive Good Bad Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RXGBOCTIS           (1 << 1)  /* Bit 1: MMC Receive Good Bad Octet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RXGOCTIS            (1 << 2)  /* Bit 2: MMC Receive Good Octet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RXBCGPIS            (1 << 3)  /* Bit 3: MMC Receive Broadcast Good Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RXMCGPIS            (1 << 4)  /* Bit 4: MMC Receive Multicast Good Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RXCRCERPIS          (1 << 5)  /* Bit 5: MMC Receive CRC Error Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RXALGNERPIS         (1 << 6)  /* Bit 6: MMC Receive Alignment Error Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RXRUNTPIS           (1 << 7)  /* Bit 7: MMC Receive Runt Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RXJABERPIS          (1 << 8)  /* Bit 8: MMC Receive Jabber Error Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RXUSIZEGPIS         (1 << 9)  /* Bit 9: MMC Receive Undersize Good Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RXOSIZEGPIS         (1 << 10) /* Bit 10: MMC Receive Oversize Good Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RX64OCTGBPIS        (1 << 11) /* Bit 11: MMC Receive 64 Octet Good Bad Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RX65T127OCTGBPIS    (1 << 12) /* Bit 12: MMC Receive 65 To 127 Octet Good Bad Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RX128T255OCTGBPIS   (1 << 13) /* Bit 13: MMC Receive 128 To 255 Octet Good Bad Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RX256T511OCTGBPIS   (1 << 14) /* Bit 14: MMC Receive 256 To 511 Octet Good Bad Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RX512T1023OCTGBPIS  (1 << 15) /* Bit 15: MMC Receive 512 To 1023 Octet Good Bad Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RX1024TMAXOCTGBPIS  (1 << 16) /* Bit 16: MMC Receive 1024 To Maximum Octet Good Bad Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RXUCGPIS            (1 << 17) /* Bit 17: MMC Receive Unicast Good Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RXLENERPIS          (1 << 18) /* Bit 18: MMC Receive Length Error Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RXORANGEPIS         (1 << 19) /* Bit 19: MMC Receive Out-Of-Range Error Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RXPAUSPIS           (1 << 20) /* Bit 20: MMC Receive Pause Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RXFOVPIS            (1 << 21) /* Bit 21: MMC Receive FIFO Overflow Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RXVLANGBPIS         (1 << 22) /* Bit 22: MMC Receive VLAN Good Bad Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RXWDOGPIS           (1 << 23) /* Bit 23: MMC Receive Watchdog Error Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RXRCVERRPIS         (1 << 24) /* Bit 24: MMC Receive Error Packet Counter Interrupt Status */
#define EMAC_MMC_RX_INTERRUPT_RXCTRLPIS           (1 << 25) /* Bit 25: MMC Receive Control Packet Counter Interrupt Status */

/* MMC Transmit Interrupt (MMC_TX_INTERRUPT) */
#define EMAC_MMC_TX_INTERRUPT_TXGBOCTIS           (1 << 0)  /* Bit 0: MMC Transmit Good Bad Octet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TXGBPKTIS           (1 << 1)  /* Bit 1: MMC Transmit Good Bad Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TXBCGPIS            (1 << 2)  /* Bit 2: MMC Transmit Broadcast Good Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TXMCGPIS            (1 << 3)  /* Bit 3: MMC Transmit Multicast Good Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TX64OCTGBPIS        (1 << 4)  /* Bit 4: MMC Transmit 64-Octet Good Bad Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TX65T127OCTGBPIS    (1 << 5)  /* Bit 5: MMC Transmit 65 To 127 Octet Good Bad Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TX128T255OCTGBPIS   (1 << 6)  /* Bit 6: MMC Transmit 128 To 255 Octet Good Bad Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TX256T511OCTGBPIS   (1 << 7)  /* Bit 7: MMC Transmit 256 To 511 Octet Good Bad Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TX512T1023OCTGBPIS  (1 << 8)  /* Bit 8: MMC Transmit 512 To 1023 Octet Good Bad Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TX1024TMAXOCTGBPIS  (1 << 9)  /* Bit 9: MMC Transmit 1024 To Maximum Octet Good Bad Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TXUCGBPIS           (1 << 10) /* Bit 10: MMC Transmit Unicast Good Bad Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TXMCGBPIS           (1 << 11) /* Bit 11: MMC Transmit Multicast Good Bad Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TXBCGBPIS           (1 << 12) /* Bit 12: MMC Transmit Broadcast Good Bad Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TXUFLOWERPIS        (1 << 13) /* Bit 13: MMC Transmit Underflow Error Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TXSCOLGPIS          (1 << 14) /* Bit 14: MMC Transmit Single Collision Good Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TXMCOLGPIS          (1 << 15) /* Bit 15: MMC Transmit Multiple Collision Good Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TXDEFPIS            (1 << 16) /* Bit 16: MMC Transmit Deferred Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TXLATCOLPIS         (1 << 17) /* Bit 17: MMC Transmit Late Collision Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TXEXCOLPIS          (1 << 18) /* Bit 18: MMC Transmit Excessive Collision Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TXCARERPIS          (1 << 19) /* Bit 19: MMC Transmit Carrier Error Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TXGOCTIS            (1 << 20) /* Bit 20: MMC Transmit Good Octet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TXGPKTIS            (1 << 21) /* Bit 21: MMC Transmit Good Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TXEXDEFPIS          (1 << 22) /* Bit 22: MMC Transmit Excessive Deferral Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TXPAUSPIS           (1 << 23) /* Bit 23: MMC Transmit Pause Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TXVLANGPIS          (1 << 24) /* Bit 24: MMC Transmit VLAN Good Packet Counter Interrupt Status */
#define EMAC_MMC_TX_INTERRUPT_TXOSIZEGPIS         (1 << 25) /* Bit 25: MMC Transmit Oversize Good Packet Counter Interrupt Status */

/* MMC Receive Interrupt Mask (MMC_RX_INTERRUPT_MASK) */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXGBPKTIM           (1 << 0)  /* Bit 0: MMC Receive Good Bad Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXGBOCTIM           (1 << 1)  /* Bit 1: MMC Receive Good Bad Octet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXGOCTIM            (1 << 2)  /* Bit 2: MMC Receive Good Octet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXBCGPIM            (1 << 3)  /* Bit 3: MMC Receive Broadcast Good Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXMCGPIM            (1 << 4)  /* Bit 4: MMC Receive Multicast Good Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXCRCERPIM          (1 << 5)  /* Bit 5: MMC Receive CRC Error Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXALGNERPIM         (1 << 6)  /* Bit 6: MMC Receive Alignment Error Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXRUNTPIM           (1 << 7)  /* Bit 7: MMC Receive Runt Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXJABERPIM          (1 << 8)  /* Bit 8: MMC Receive Jabber Error Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXUSIZEGPIM         (1 << 9)  /* Bit 9: MMC Receive Undersize Good Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXOSIZEGPIM         (1 << 10) /* Bit 10: MMC Receive Oversize Good Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RX64OCTGBPIM        (1 << 11) /* Bit 11: MMC Receive 64-Octet Good Bad Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RX65T127OCTGBPIM    (1 << 12) /* Bit 12: MMC Receive 65 To 127 Octet Good Bad Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RX128T255OCTGBPIM   (1 << 13) /* Bit 13: MMC Receive 128 To 255 Octet Good Bad Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RX256T511OCTGBPIM   (1 << 14) /* Bit 14: MMC Receive 256 To 511 Octet Good Bad Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RX512T1023OCTGBPIM  (1 << 15) /* Bit 15: MMC Receive 512 To 1023 Octet Good Bad Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RX1024TMAXOCTGBPIM  (1 << 16) /* Bit 16: MMC Receive 1024 To Maximum Octet Good Bad Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXUCGPIM            (1 << 17) /* Bit 17: MMC Receive Unicast Good Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXLENERPIM          (1 << 18) /* Bit 18: MMC Receive Length Error Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXORANGEPIM         (1 << 19) /* Bit 19: MMC Receive Out-Of-Range Error Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXPAUSPIM           (1 << 20) /* Bit 20: MMC Receive Pause Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXFOVPIM            (1 << 21) /* Bit 21: MMC Receive FIFO Overflow Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXVLANGBPIM         (1 << 22) /* Bit 22: MMC Receive VLAN Good Bad Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXWDOGPIM           (1 << 23) /* Bit 23: MMC Receive Watchdog Error Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXRCVERRPIM         (1 << 24) /* Bit 24: MMC Receive Error Packet Counter Interrupt Mask */
#define EMAC_MMC_RX_INTERRUPT_MASK_RXCTRLPIM           (1 << 25) /* Bit 25: MMC Receive Control Packet Counter Interrupt Mask */

/* MMC Transmit Interrupt Mask (MMC_TX_INTERRUPT_MASK) */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXGBOCTIM           (1 << 0)  /* Bit 0: MMC Transmit Good Bad Octet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXGBPKTIM           (1 << 1)  /* Bit 1: MMC Transmit Good Bad Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXBCGPIM            (1 << 2)  /* Bit 2: MMC Transmit Broadcast Good Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXMCGPIM            (1 << 3)  /* Bit 3: MMC Transmit Multicast Good Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TX64OCTGBPIM        (1 << 4)  /* Bit 4: MMC Transmit 64-Octet Good Bad Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TX65T127OCTGBPIM    (1 << 5)  /* Bit 5: MMC Transmit 65 To 127 Octet Good Bad Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TX128T255OCTGBPIM   (1 << 6)  /* Bit 6: MMC Transmit 128 To 255 Octet Good Bad Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TX256T511OCTGBPIM   (1 << 7)  /* Bit 7: MMC Transmit 256 To 511 Octet Good Bad Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TX512T1023OCTGBPIM  (1 << 8)  /* Bit 8: MMC Transmit 512 To 1023 Octet Good Bad Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TX1024TMAXOCTGBPIM  (1 << 9)  /* Bit 9: MMC Transmit 1024 To Maximum Octet Good Bad Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXUCGBPIM           (1 << 10) /* Bit 10: MMC Transmit Unicast Good Bad Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXMCGBPIM           (1 << 11) /* Bit 11: MMC Transmit Multicast Good Bad Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXBCGBPIM           (1 << 12) /* Bit 12: MMC Transmit Broadcast Good Bad Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXUFLOWERPIM        (1 << 13) /* Bit 13: MMC Transmit Underflow Error Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXSCOLGPIM          (1 << 14) /* Bit 14: MMC Transmit Single Collision Good Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXMCOLGPIM          (1 << 15) /* Bit 15: MMC Transmit Multiple Collision Good Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXDEFPIM            (1 << 16) /* Bit 16: MMC Transmit Deferred Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXLATCOLPIM         (1 << 17) /* Bit 17: MMC Transmit Late Collision Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXEXCOLPIM          (1 << 18) /* Bit 18: MMC Transmit Excessive Collision Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXCARERPIM          (1 << 19) /* Bit 19: MMC Transmit Carrier Error Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXGOCTIM            (1 << 20) /* Bit 20: MMC Transmit Good Octet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXGPKTIM            (1 << 21) /* Bit 21: MMC Transmit Good Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXEXDEFPIM          (1 << 22) /* Bit 22: MMC Transmit Excessive Deferral Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXPAUSPIM           (1 << 23) /* Bit 23: MMC Transmit Pause Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXVLANGPIM          (1 << 24) /* Bit 24: MMC Transmit VLAN Good Packet Counter Interrupt Mask */
#define EMAC_MMC_TX_INTERRUPT_MASK_TXOSIZEGPIM         (1 << 25) /* Bit 25: MMC Transmit Oversize Good Packet Counter Interrupt Mask */

/* Transmit Octet Count Good Bad (TX_OCTET_COUNT_GOOD_BAD) */
#define EMAC_TX_OCTET_COUNT_GOOD_BAD_TXOCTGB_SHIFT  (0) /* Bits 0-32: Transmit Octet Count Good Bad */
#define EMAC_TX_OCTET_COUNT_GOOD_BAD_TXOCTGB_MASK   (0xffffffff << EMAC_TX_OCTET_COUNT_GOOD_BAD_TXOCTGB_SHIFT)
#define EMAC_TX_OCTET_COUNT_GOOD_BAD_TXOCTGB(n)     (((n) << EMAC_TX_OCTET_COUNT_GOOD_BAD_TXOCTGB_SHIFT) & EMAC_TX_OCTET_COUNT_GOOD_BAD_TXOCTGB_MASK)

/* Transmit Packet Count Good Bad (TX_PACKET_COUNT_GOOD_BAD) */
#define EMAC_TX_PACKET_COUNT_GOOD_BAD_TXPKTGB_SHIFT  (0) /* Bits 0-32: Transmit Packet Count Good Bad */
#define EMAC_TX_PACKET_COUNT_GOOD_BAD_TXPKTGB_MASK   (0xffffffff << EMAC_TX_PACKET_COUNT_GOOD_BAD_TXPKTGB_SHIFT)
#define EMAC_TX_PACKET_COUNT_GOOD_BAD_TXPKTGB(n)     (((n) << EMAC_TX_PACKET_COUNT_GOOD_BAD_TXPKTGB_SHIFT) & EMAC_TX_PACKET_COUNT_GOOD_BAD_TXPKTGB_MASK)

/* Transmit Broadcast Packets Good (TX_BROADCAST_PACKETS_GOOD) */
#define EMAC_TX_BROADCAST_PACKETS_GOOD_TXBCASTG_SHIFT  (0) /* Bits 0-32: Transmit Broadcast Packets Good */
#define EMAC_TX_BROADCAST_PACKETS_GOOD_TXBCASTG_MASK   (0xffffffff << EMAC_TX_BROADCAST_PACKETS_GOOD_TXBCASTG_SHIFT)
#define EMAC_TX_BROADCAST_PACKETS_GOOD_TXBCASTG(n)     (((n) << EMAC_TX_BROADCAST_PACKETS_GOOD_TXBCASTG_SHIFT) & EMAC_TX_BROADCAST_PACKETS_GOOD_TXBCASTG_MASK)

/* Transmit Multicast Packets Good (TX_MULTICAST_PACKETS_GOOD) */
#define EMAC_TX_MULTICAST_PACKETS_GOOD_TXMCASTG_SHIFT  (0) /* Bits 0-32: Transmit Multicast Packets Good */
#define EMAC_TX_MULTICAST_PACKETS_GOOD_TXMCASTG_MASK   (0xffffffff << EMAC_TX_MULTICAST_PACKETS_GOOD_TXMCASTG_SHIFT)
#define EMAC_TX_MULTICAST_PACKETS_GOOD_TXMCASTG(n)     (((n) << EMAC_TX_MULTICAST_PACKETS_GOOD_TXMCASTG_SHIFT) & EMAC_TX_MULTICAST_PACKETS_GOOD_TXMCASTG_MASK)

/* Transmit 64-Octet Packets Good Bad (TX_64OCTETS_PACKETS_GOOD_BAD) */
#define EMAC_TX_64OCTETS_PACKETS_GOOD_BAD_TX64OCTGB_SHIFT  (0) /* Bits 0-32: Transmit 64-Octet Packets Good Bad */
#define EMAC_TX_64OCTETS_PACKETS_GOOD_BAD_TX64OCTGB_MASK   (0xffffffff << EMAC_TX_64OCTETS_PACKETS_GOOD_BAD_TX64OCTGB_SHIFT)
#define EMAC_TX_64OCTETS_PACKETS_GOOD_BAD_TX64OCTGB(n)     (((n) << EMAC_TX_64OCTETS_PACKETS_GOOD_BAD_TX64OCTGB_SHIFT) & EMAC_TX_64OCTETS_PACKETS_GOOD_BAD_TX64OCTGB_MASK)

/* Transmit 65 To 127 Octet Packets Good Bad
 * (TX_65TO127OCTETS_PACKETS_GOOD_BAD)
 */

#define EMAC_TX_65TO127OCTETS_PACKETS_GOOD_BAD_TX65_127OCTGB_SHIFT  (0) /* Bits 0-32: Transmit 65 To 127 Octet Packets Good Bad */
#define EMAC_TX_65TO127OCTETS_PACKETS_GOOD_BAD_TX65_127OCTGB_MASK   (0xffffffff << EMAC_TX_65TO127OCTETS_PACKETS_GOOD_BAD_TX65_127OCTGB_SHIFT)
#define EMAC_TX_65TO127OCTETS_PACKETS_GOOD_BAD_TX65_127OCTGB(n)     (((n) << EMAC_TX_65TO127OCTETS_PACKETS_GOOD_BAD_TX65_127OCTGB_SHIFT) & EMAC_TX_65TO127OCTETS_PACKETS_GOOD_BAD_TX65_127OCTGB_MASK)

/* Transmit 128 To 255 Octet Packets Good Bad
 * (TX_128TO255OCTETS_PACKETS_GOOD_BAD)
 */

#define EMAC_TX_128TO255OCTETS_PACKETS_GOOD_BAD_TX128_255OCTGB_SHIFT  (0) /* Bits 0-32: Transmit 128 To 255 Octet Packets Good Bad */
#define EMAC_TX_128TO255OCTETS_PACKETS_GOOD_BAD_TX128_255OCTGB_MASK   (0xffffffff << EMAC_TX_128TO255OCTETS_PACKETS_GOOD_BAD_TX128_255OCTGB_SHIFT)
#define EMAC_TX_128TO255OCTETS_PACKETS_GOOD_BAD_TX128_255OCTGB(n)     (((n) << EMAC_TX_128TO255OCTETS_PACKETS_GOOD_BAD_TX128_255OCTGB_SHIFT) & EMAC_TX_128TO255OCTETS_PACKETS_GOOD_BAD_TX128_255OCTGB_MASK)

/* Transmit 256 To 511 Octet Packets Good Bad
 * (TX_256TO511OCTETS_PACKETS_GOOD_BAD)
 */

#define EMAC_TX_256TO511OCTETS_PACKETS_GOOD_BAD_TX256_511OCTGB_SHIFT  (0) /* Bits 0-32: Transmit 256 To 511 Octet Packets Good Bad */
#define EMAC_TX_256TO511OCTETS_PACKETS_GOOD_BAD_TX256_511OCTGB_MASK   (0xffffffff << EMAC_TX_256TO511OCTETS_PACKETS_GOOD_BAD_TX256_511OCTGB_SHIFT)
#define EMAC_TX_256TO511OCTETS_PACKETS_GOOD_BAD_TX256_511OCTGB(n)     (((n) << EMAC_TX_256TO511OCTETS_PACKETS_GOOD_BAD_TX256_511OCTGB_SHIFT) & EMAC_TX_256TO511OCTETS_PACKETS_GOOD_BAD_TX256_511OCTGB_MASK)

/* Transmit 512 To 1023 Octet Packets Good Bad
 * (TX_512TO1023OCTETS_PACKETS_GOOD_BAD)
 */

#define EMAC_TX_512TO1023OCTETS_PACKETS_GOOD_BAD_TX512_1023OCTGB_SHIFT  (0) /* Bits 0-32: Transmit 512 To 1023 Octet Packets Good Bad */
#define EMAC_TX_512TO1023OCTETS_PACKETS_GOOD_BAD_TX512_1023OCTGB_MASK   (0xffffffff << EMAC_TX_512TO1023OCTETS_PACKETS_GOOD_BAD_TX512_1023OCTGB_SHIFT)
#define EMAC_TX_512TO1023OCTETS_PACKETS_GOOD_BAD_TX512_1023OCTGB(n)     (((n) << EMAC_TX_512TO1023OCTETS_PACKETS_GOOD_BAD_TX512_1023OCTGB_SHIFT) & EMAC_TX_512TO1023OCTETS_PACKETS_GOOD_BAD_TX512_1023OCTGB_MASK)

/* Transmit 1024 To Max Octet Packets Good Bad
 * (TX_1024TOMAXOCTETS_PACKETS_GOOD_BAD)
 */

#define EMAC_TX_1024TOMAXOCTETS_PACKETS_GOOD_BAD_TX1024_MAXOCTGB_SHIFT  (0) /* Bits 0-32: Transmit 1024 To Max Octet Packets Good Bad */
#define EMAC_TX_1024TOMAXOCTETS_PACKETS_GOOD_BAD_TX1024_MAXOCTGB_MASK   (0xffffffff << EMAC_TX_1024TOMAXOCTETS_PACKETS_GOOD_BAD_TX1024_MAXOCTGB_SHIFT)
#define EMAC_TX_1024TOMAXOCTETS_PACKETS_GOOD_BAD_TX1024_MAXOCTGB(n)     (((n) << EMAC_TX_1024TOMAXOCTETS_PACKETS_GOOD_BAD_TX1024_MAXOCTGB_SHIFT) & EMAC_TX_1024TOMAXOCTETS_PACKETS_GOOD_BAD_TX1024_MAXOCTGB_MASK)

/* Transmit Unicast Packets Good Bad
 * (TX_UNICAST_PACKETS_GOOD_BAD)
 */

#define EMAC_TX_UNICAST_PACKETS_GOOD_BAD_TXUCASTGB_SHIFT  (0) /* Bits 0-32: Transmit Unicast Packets Good Bad */
#define EMAC_TX_UNICAST_PACKETS_GOOD_BAD_TXUCASTGB_MASK   (0xffffffff << EMAC_TX_UNICAST_PACKETS_GOOD_BAD_TXUCASTGB_SHIFT)
#define EMAC_TX_UNICAST_PACKETS_GOOD_BAD_TXUCASTGB(n)     (((n) << EMAC_TX_UNICAST_PACKETS_GOOD_BAD_TXUCASTGB_SHIFT) & EMAC_TX_UNICAST_PACKETS_GOOD_BAD_TXUCASTGB_MASK)

/* Transmit Multicast Packets Good Bad (TX_MULTICAST_PACKETS_GOOD_BAD) */
#define EMAC_TX_MULTICAST_PACKETS_GOOD_BAD_TXMCASTGB_SHIFT  (0) /* Bits 0-32: Transmit Multicast Packets Good Bad */
#define EMAC_TX_MULTICAST_PACKETS_GOOD_BAD_TXMCASTGB_MASK   (0xffffffff << EMAC_TX_MULTICAST_PACKETS_GOOD_BAD_TXMCASTGB_SHIFT)
#define EMAC_TX_MULTICAST_PACKETS_GOOD_BAD_TXMCASTGB(n)     (((n) << EMAC_TX_MULTICAST_PACKETS_GOOD_BAD_TXMCASTGB_SHIFT) & EMAC_TX_MULTICAST_PACKETS_GOOD_BAD_TXMCASTGB_MASK)

/* Transmit Broadcast Packets Good Bad (TX_BROADCAST_PACKETS_GOOD_BAD) */
#define EMAC_TX_BROADCAST_PACKETS_GOOD_BAD_TXBCASTGB_SHIFT  (0) /* Bits 0-32: Transmit Broadcast Packets Good Bad */
#define EMAC_TX_BROADCAST_PACKETS_GOOD_BAD_TXBCASTGB_MASK   (0xffffffff << EMAC_TX_BROADCAST_PACKETS_GOOD_BAD_TXBCASTGB_SHIFT)
#define EMAC_TX_BROADCAST_PACKETS_GOOD_BAD_TXBCASTGB(n)     (((n) << EMAC_TX_BROADCAST_PACKETS_GOOD_BAD_TXBCASTGB_SHIFT) & EMAC_TX_BROADCAST_PACKETS_GOOD_BAD_TXBCASTGB_MASK)

/* Transmit Underflow Error Packets (TX_UNDERFLOW_ERROR_PACKETS) */
#define EMAC_TX_UNDERFLOW_ERROR_PACKETS_TXUNDRFLW_SHIFT  (0) /* Bits 0-32: Transmit Underflow Error Packets */
#define EMAC_TX_UNDERFLOW_ERROR_PACKETS_TXUNDRFLW_MASK   (0xffffffff << EMAC_TX_UNDERFLOW_ERROR_PACKETS_TXUNDRFLW_SHIFT)
#define EMAC_TX_UNDERFLOW_ERROR_PACKETS_TXUNDRFLW(n)     (((n) << EMAC_TX_UNDERFLOW_ERROR_PACKETS_TXUNDRFLW_SHIFT) & EMAC_TX_UNDERFLOW_ERROR_PACKETS_TXUNDRFLW_MASK)

/* Transmit Single Collision Good Packets
 * (TX_SINGLE_COLLISION_GOOD_PACKETS)
 */

#define EMAC_TX_SINGLE_COLLISION_GOOD_PACKETS_TXSNGLCOLG_SHIFT  (0) /* Bits 0-32: Transmit Single Collision Good Packets */
#define EMAC_TX_SINGLE_COLLISION_GOOD_PACKETS_TXSNGLCOLG_MASK   (0xffffffff << EMAC_TX_SINGLE_COLLISION_GOOD_PACKETS_TXSNGLCOLG_SHIFT)
#define EMAC_TX_SINGLE_COLLISION_GOOD_PACKETS_TXSNGLCOLG(n)     (((n) << EMAC_TX_SINGLE_COLLISION_GOOD_PACKETS_TXSNGLCOLG_SHIFT) & EMAC_TX_SINGLE_COLLISION_GOOD_PACKETS_TXSNGLCOLG_MASK)

/* Transmit Multiple Collision Good Packets
 * (TX_MULTIPLE_COLLISION_GOOD_PACKETS)
 */

#define EMAC_TX_MULTIPLE_COLLISION_GOOD_PACKETS_TXMULTCOLG_SHIFT  (0) /* Bits 0-32: Transmit Multiple Collision Good Packets */
#define EMAC_TX_MULTIPLE_COLLISION_GOOD_PACKETS_TXMULTCOLG_MASK   (0xffffffff << EMAC_TX_MULTIPLE_COLLISION_GOOD_PACKETS_TXMULTCOLG_SHIFT)
#define EMAC_TX_MULTIPLE_COLLISION_GOOD_PACKETS_TXMULTCOLG(n)     (((n) << EMAC_TX_MULTIPLE_COLLISION_GOOD_PACKETS_TXMULTCOLG_SHIFT) & EMAC_TX_MULTIPLE_COLLISION_GOOD_PACKETS_TXMULTCOLG_MASK)

/* Transmit Deferred Packets (TX_DEFERRED_PACKETS) */
#define EMAC_TX_DEFERRED_PACKETS_TXDEFRD_SHIFT  (0) /* Bits 0-32: Transmit Deferred Packets */
#define EMAC_TX_DEFERRED_PACKETS_TXDEFRD_MASK   (0xffffffff << EMAC_TX_DEFERRED_PACKETS_TXDEFRD_SHIFT)
#define EMAC_TX_DEFERRED_PACKETS_TXDEFRD(n)     (((n) << EMAC_TX_DEFERRED_PACKETS_TXDEFRD_SHIFT) & EMAC_TX_DEFERRED_PACKETS_TXDEFRD_MASK)

/* Transmit Late Collision Packets (TX_LATE_COLLISION_PACKETS) */
#define EMAC_TX_LATE_COLLISION_PACKETS_TXLATECOL_SHIFT  (0) /* Bits 0-32: Transmit Late Collision Packets */
#define EMAC_TX_LATE_COLLISION_PACKETS_TXLATECOL_MASK   (0xffffffff << EMAC_TX_LATE_COLLISION_PACKETS_TXLATECOL_SHIFT)
#define EMAC_TX_LATE_COLLISION_PACKETS_TXLATECOL(n)     (((n) << EMAC_TX_LATE_COLLISION_PACKETS_TXLATECOL_SHIFT) & EMAC_TX_LATE_COLLISION_PACKETS_TXLATECOL_MASK)

/* Transmit Excessive Collision Packets (TX_EXCESSIVE_COLLISION_PACKETS) */
#define EMAC_TX_EXCESSIVE_COLLISION_PACKETS_TXEXSCOL_SHIFT  (0) /* Bits 0-32: Transmit Excessive Collision Packets */
#define EMAC_TX_EXCESSIVE_COLLISION_PACKETS_TXEXSCOL_MASK   (0xffffffff << EMAC_TX_EXCESSIVE_COLLISION_PACKETS_TXEXSCOL_SHIFT)
#define EMAC_TX_EXCESSIVE_COLLISION_PACKETS_TXEXSCOL(n)     (((n) << EMAC_TX_EXCESSIVE_COLLISION_PACKETS_TXEXSCOL_SHIFT) & EMAC_TX_EXCESSIVE_COLLISION_PACKETS_TXEXSCOL_MASK)

/* Transmit Carrier Error Packets (TX_CARRIER_ERROR_PACKETS) */
#define EMAC_TX_CARRIER_ERROR_PACKETS_TXCARR_SHIFT  (0) /* Bits 0-32: Transmit Carrier Error Packets */
#define EMAC_TX_CARRIER_ERROR_PACKETS_TXCARR_MASK   (0xffffffff << EMAC_TX_CARRIER_ERROR_PACKETS_TXCARR_SHIFT)
#define EMAC_TX_CARRIER_ERROR_PACKETS_TXCARR(n)     (((n) << EMAC_TX_CARRIER_ERROR_PACKETS_TXCARR_SHIFT) & EMAC_TX_CARRIER_ERROR_PACKETS_TXCARR_MASK)

/* Transmit Octet Count Good (TX_OCTET_COUNT_GOOD) */
#define EMAC_TX_OCTET_COUNT_GOOD_TXOCTG_SHIFT  (0) /* Bits 0-32: Transmit Octet Count Good */
#define EMAC_TX_OCTET_COUNT_GOOD_TXOCTG_MASK   (0xffffffff << EMAC_TX_OCTET_COUNT_GOOD_TXOCTG_SHIFT)
#define EMAC_TX_OCTET_COUNT_GOOD_TXOCTG(n)     (((n) << EMAC_TX_OCTET_COUNT_GOOD_TXOCTG_SHIFT) & EMAC_TX_OCTET_COUNT_GOOD_TXOCTG_MASK)

/* Transmit Packet Count Good (TX_PACKET_COUNT_GOOD) */
#define EMAC_TX_PACKET_COUNT_GOOD_TXPKTG_SHIFT  (0) /* Bits 0-32: Transmit Packet Count Good */
#define EMAC_TX_PACKET_COUNT_GOOD_TXPKTG_MASK   (0xffffffff << EMAC_TX_PACKET_COUNT_GOOD_TXPKTG_SHIFT)
#define EMAC_TX_PACKET_COUNT_GOOD_TXPKTG(n)     (((n) << EMAC_TX_PACKET_COUNT_GOOD_TXPKTG_SHIFT) & EMAC_TX_PACKET_COUNT_GOOD_TXPKTG_MASK)

/* Transmit Excessive Deferral Error (TX_EXCESSIVE_DEFERRAL_ERROR) */
#define EMAC_TX_EXCESSIVE_DEFERRAL_ERROR_TXEXSDEF_SHIFT  (0) /* Bits 0-32: Transmit Excessive Deferral Error */
#define EMAC_TX_EXCESSIVE_DEFERRAL_ERROR_TXEXSDEF_MASK   (0xffffffff << EMAC_TX_EXCESSIVE_DEFERRAL_ERROR_TXEXSDEF_SHIFT)
#define EMAC_TX_EXCESSIVE_DEFERRAL_ERROR_TXEXSDEF(n)     (((n) << EMAC_TX_EXCESSIVE_DEFERRAL_ERROR_TXEXSDEF_SHIFT) & EMAC_TX_EXCESSIVE_DEFERRAL_ERROR_TXEXSDEF_MASK)

/* Transmit Pause Packets (TX_PAUSE_PACKETS) */
#define EMAC_TX_PAUSE_PACKETS_TXPAUSE_SHIFT  (0) /* Bits 0-32: Transmit Pause Packets */
#define EMAC_TX_PAUSE_PACKETS_TXPAUSE_MASK   (0xffffffff << EMAC_TX_PAUSE_PACKETS_TXPAUSE_SHIFT)
#define EMAC_TX_PAUSE_PACKETS_TXPAUSE(n)     (((n) << EMAC_TX_PAUSE_PACKETS_TXPAUSE_SHIFT) & EMAC_TX_PAUSE_PACKETS_TXPAUSE_MASK)

/* Transmit VLAN Packets Good (TX_VLAN_PACKETS_GOOD) */
#define EMAC_TX_VLAN_PACKETS_GOOD_TXVLANG_SHIFT  (0) /* Bits 0-32: Transmit VLAN Packets Good */
#define EMAC_TX_VLAN_PACKETS_GOOD_TXVLANG_MASK   (0xffffffff << EMAC_TX_VLAN_PACKETS_GOOD_TXVLANG_SHIFT)
#define EMAC_TX_VLAN_PACKETS_GOOD_TXVLANG(n)     (((n) << EMAC_TX_VLAN_PACKETS_GOOD_TXVLANG_SHIFT) & EMAC_TX_VLAN_PACKETS_GOOD_TXVLANG_MASK)

/* Transmit O Size Packets Good (TX_OSIZE_PACKETS_GOOD) */
#define EMAC_TX_OSIZE_PACKETS_GOOD_TXOSIZG_SHIFT  (0) /* Bits 0-32: Transmit O Size Packets Good */
#define EMAC_TX_OSIZE_PACKETS_GOOD_TXOSIZG_MASK   (0xffffffff << EMAC_TX_OSIZE_PACKETS_GOOD_TXOSIZG_SHIFT)
#define EMAC_TX_OSIZE_PACKETS_GOOD_TXOSIZG(n)     (((n) << EMAC_TX_OSIZE_PACKETS_GOOD_TXOSIZG_SHIFT) & EMAC_TX_OSIZE_PACKETS_GOOD_TXOSIZG_MASK)

/* Receive Packets Count Good Bad (RX_PACKETS_COUNT_GOOD_BAD) */
#define EMAC_RX_PACKETS_COUNT_GOOD_BAD_RXPKTGB_SHIFT  (0) /* Bits 0-32: Receive Packets Count Good Bad */
#define EMAC_RX_PACKETS_COUNT_GOOD_BAD_RXPKTGB_MASK   (0xffffffff << EMAC_RX_PACKETS_COUNT_GOOD_BAD_RXPKTGB_SHIFT)
#define EMAC_RX_PACKETS_COUNT_GOOD_BAD_RXPKTGB(n)     (((n) << EMAC_RX_PACKETS_COUNT_GOOD_BAD_RXPKTGB_SHIFT) & EMAC_RX_PACKETS_COUNT_GOOD_BAD_RXPKTGB_MASK)

/* Receive Octet Count Good Bad (RX_OCTET_COUNT_GOOD_BAD) */
#define EMAC_RX_OCTET_COUNT_GOOD_BAD_RXOCTGB_SHIFT  (0) /* Bits 0-32: Receive Octet Count Good Bad */
#define EMAC_RX_OCTET_COUNT_GOOD_BAD_RXOCTGB_MASK   (0xffffffff << EMAC_RX_OCTET_COUNT_GOOD_BAD_RXOCTGB_SHIFT)
#define EMAC_RX_OCTET_COUNT_GOOD_BAD_RXOCTGB(n)     (((n) << EMAC_RX_OCTET_COUNT_GOOD_BAD_RXOCTGB_SHIFT) & EMAC_RX_OCTET_COUNT_GOOD_BAD_RXOCTGB_MASK)

/* Receive Octet Count Good (RX_OCTET_COUNT_GOOD) */
#define EMAC_RX_OCTET_COUNT_GOOD_RXOCTG_SHIFT  (0) /* Bits 0-32: Receive Octet Count Good */
#define EMAC_RX_OCTET_COUNT_GOOD_RXOCTG_MASK   (0xffffffff << EMAC_RX_OCTET_COUNT_GOOD_RXOCTG_SHIFT)
#define EMAC_RX_OCTET_COUNT_GOOD_RXOCTG(n)     (((n) << EMAC_RX_OCTET_COUNT_GOOD_RXOCTG_SHIFT) & EMAC_RX_OCTET_COUNT_GOOD_RXOCTG_MASK)

/* Receive Broadcast Packets Good (RX_BROADCAST_PACKETS_GOOD) */
#define EMAC_RX_BROADCAST_PACKETS_GOOD_RXBCASTG_SHIFT  (0) /* Bits 0-32: Receive Broadcast Packets Good */
#define EMAC_RX_BROADCAST_PACKETS_GOOD_RXBCASTG_MASK   (0xffffffff << EMAC_RX_BROADCAST_PACKETS_GOOD_RXBCASTG_SHIFT)
#define EMAC_RX_BROADCAST_PACKETS_GOOD_RXBCASTG(n)     (((n) << EMAC_RX_BROADCAST_PACKETS_GOOD_RXBCASTG_SHIFT) & EMAC_RX_BROADCAST_PACKETS_GOOD_RXBCASTG_MASK)

/* Receive Multicast Packets Good (RX_MULTICAST_PACKETS_GOOD) */
#define EMAC_RX_MULTICAST_PACKETS_GOOD_RXMCASTG_SHIFT  (0) /* Bits 0-32: Receive Multicast Packets Good */
#define EMAC_RX_MULTICAST_PACKETS_GOOD_RXMCASTG_MASK   (0xffffffff << EMAC_RX_MULTICAST_PACKETS_GOOD_RXMCASTG_SHIFT)
#define EMAC_RX_MULTICAST_PACKETS_GOOD_RXMCASTG(n)     (((n) << EMAC_RX_MULTICAST_PACKETS_GOOD_RXMCASTG_SHIFT) & EMAC_RX_MULTICAST_PACKETS_GOOD_RXMCASTG_MASK)

/* Receive CRC Error Packets (RX_CRC_ERROR_PACKETS) */
#define EMAC_RX_CRC_ERROR_PACKETS_RXCRCERR_SHIFT  (0) /* Bits 0-32: Receive CRC Error Packets */
#define EMAC_RX_CRC_ERROR_PACKETS_RXCRCERR_MASK   (0xffffffff << EMAC_RX_CRC_ERROR_PACKETS_RXCRCERR_SHIFT)
#define EMAC_RX_CRC_ERROR_PACKETS_RXCRCERR(n)     (((n) << EMAC_RX_CRC_ERROR_PACKETS_RXCRCERR_SHIFT) & EMAC_RX_CRC_ERROR_PACKETS_RXCRCERR_MASK)

/* Receive Alignment Error Packets (RX_ALIGNMENT_ERROR_PACKETS) */
#define EMAC_RX_ALIGNMENT_ERROR_PACKETS_RXALGNERR_SHIFT  (0) /* Bits 0-32: Receive Alignment Error Packets */
#define EMAC_RX_ALIGNMENT_ERROR_PACKETS_RXALGNERR_MASK   (0xffffffff << EMAC_RX_ALIGNMENT_ERROR_PACKETS_RXALGNERR_SHIFT)
#define EMAC_RX_ALIGNMENT_ERROR_PACKETS_RXALGNERR(n)     (((n) << EMAC_RX_ALIGNMENT_ERROR_PACKETS_RXALGNERR_SHIFT) & EMAC_RX_ALIGNMENT_ERROR_PACKETS_RXALGNERR_MASK)

/* Receive Runt Error Packets (RX_RUNT_ERROR_PACKETS) */
#define EMAC_RX_RUNT_ERROR_PACKETS_RXRUNTERR_SHIFT  (0) /* Bits 0-32: Receive Runt Error Packets */
#define EMAC_RX_RUNT_ERROR_PACKETS_RXRUNTERR_MASK   (0xffffffff << EMAC_RX_RUNT_ERROR_PACKETS_RXRUNTERR_SHIFT)
#define EMAC_RX_RUNT_ERROR_PACKETS_RXRUNTERR(n)     (((n) << EMAC_RX_RUNT_ERROR_PACKETS_RXRUNTERR_SHIFT) & EMAC_RX_RUNT_ERROR_PACKETS_RXRUNTERR_MASK)

/* Receive Jabber Error Packets (RX_JABBER_ERROR_PACKETS) */
#define EMAC_RX_JABBER_ERROR_PACKETS_RXJABERR_SHIFT  (0) /* Bits 0-32: Receive Jabber Error Packets */
#define EMAC_RX_JABBER_ERROR_PACKETS_RXJABERR_MASK   (0xffffffff << EMAC_RX_JABBER_ERROR_PACKETS_RXJABERR_SHIFT)
#define EMAC_RX_JABBER_ERROR_PACKETS_RXJABERR(n)     (((n) << EMAC_RX_JABBER_ERROR_PACKETS_RXJABERR_SHIFT) & EMAC_RX_JABBER_ERROR_PACKETS_RXJABERR_MASK)

/* Receive Undersize Packets Good (RX_UNDERSIZE_PACKETS_GOOD) */
#define EMAC_RX_UNDERSIZE_PACKETS_GOOD_RXUNDERSZG_SHIFT  (0) /* Bits 0-32: Receive Undersize Packets Good */
#define EMAC_RX_UNDERSIZE_PACKETS_GOOD_RXUNDERSZG_MASK   (0xffffffff << EMAC_RX_UNDERSIZE_PACKETS_GOOD_RXUNDERSZG_SHIFT)
#define EMAC_RX_UNDERSIZE_PACKETS_GOOD_RXUNDERSZG(n)     (((n) << EMAC_RX_UNDERSIZE_PACKETS_GOOD_RXUNDERSZG_SHIFT) & EMAC_RX_UNDERSIZE_PACKETS_GOOD_RXUNDERSZG_MASK)

/* Receive Oversize Packets Good (RX_OVERSIZE_PACKETS_GOOD) */
#define EMAC_RX_OVERSIZE_PACKETS_GOOD_RXOVERSZG_SHIFT  (0) /* Bits 0-32: Receive Oversize Packets Good */
#define EMAC_RX_OVERSIZE_PACKETS_GOOD_RXOVERSZG_MASK   (0xffffffff << EMAC_RX_OVERSIZE_PACKETS_GOOD_RXOVERSZG_SHIFT)
#define EMAC_RX_OVERSIZE_PACKETS_GOOD_RXOVERSZG(n)     (((n) << EMAC_RX_OVERSIZE_PACKETS_GOOD_RXOVERSZG_SHIFT) & EMAC_RX_OVERSIZE_PACKETS_GOOD_RXOVERSZG_MASK)

/* Receive 64 Octets Packets Good Bad (RX_64OCTETS_PACKETS_GOOD_BAD) */
#define EMAC_RX_64OCTETS_PACKETS_GOOD_BAD_RX64OCTGB_SHIFT  (0) /* Bits 0-32: Receive 64 Octets Packets Good Bad */
#define EMAC_RX_64OCTETS_PACKETS_GOOD_BAD_RX64OCTGB_MASK   (0xffffffff << EMAC_RX_64OCTETS_PACKETS_GOOD_BAD_RX64OCTGB_SHIFT)
#define EMAC_RX_64OCTETS_PACKETS_GOOD_BAD_RX64OCTGB(n)     (((n) << EMAC_RX_64OCTETS_PACKETS_GOOD_BAD_RX64OCTGB_SHIFT) & EMAC_RX_64OCTETS_PACKETS_GOOD_BAD_RX64OCTGB_MASK)

/* Receive 65-127 Octets Packets Good Bad
 * (RX_65TO127OCTETS_PACKETS_GOOD_BAD)
 */

#define EMAC_RX_65TO127OCTETS_PACKETS_GOOD_BAD_RX65_127OCTGB_SHIFT  (0) /* Bits 0-32: Receive 65-127 Octets Packets Good Bad */
#define EMAC_RX_65TO127OCTETS_PACKETS_GOOD_BAD_RX65_127OCTGB_MASK   (0xffffffff << EMAC_RX_65TO127OCTETS_PACKETS_GOOD_BAD_RX65_127OCTGB_SHIFT)
#define EMAC_RX_65TO127OCTETS_PACKETS_GOOD_BAD_RX65_127OCTGB(n)     (((n) << EMAC_RX_65TO127OCTETS_PACKETS_GOOD_BAD_RX65_127OCTGB_SHIFT) & EMAC_RX_65TO127OCTETS_PACKETS_GOOD_BAD_RX65_127OCTGB_MASK)

/* Receive 128-255 Octets Packets Good Bad
 * (RX_128TO255OCTETS_PACKETS_GOOD_BAD)
 */

#define EMAC_RX_128TO255OCTETS_PACKETS_GOOD_BAD_RX128_255OCTGB_SHIFT  (0) /* Bits 0-32: Receive 128-255 Octets Packets Good Bad */
#define EMAC_RX_128TO255OCTETS_PACKETS_GOOD_BAD_RX128_255OCTGB_MASK   (0xffffffff << EMAC_RX_128TO255OCTETS_PACKETS_GOOD_BAD_RX128_255OCTGB_SHIFT)
#define EMAC_RX_128TO255OCTETS_PACKETS_GOOD_BAD_RX128_255OCTGB(n)     (((n) << EMAC_RX_128TO255OCTETS_PACKETS_GOOD_BAD_RX128_255OCTGB_SHIFT) & EMAC_RX_128TO255OCTETS_PACKETS_GOOD_BAD_RX128_255OCTGB_MASK)

/* Receive 256-511 Octets Packets Good Bad
 * (RX_256TO511OCTETS_PACKETS_GOOD_BAD)
 */

#define EMAC_RX_256TO511OCTETS_PACKETS_GOOD_BAD_RX256_511OCTGB_SHIFT  (0) /* Bits 0-32: Receive 256-511 Octets Packets Good Bad */
#define EMAC_RX_256TO511OCTETS_PACKETS_GOOD_BAD_RX256_511OCTGB_MASK   (0xffffffff << EMAC_RX_256TO511OCTETS_PACKETS_GOOD_BAD_RX256_511OCTGB_SHIFT)
#define EMAC_RX_256TO511OCTETS_PACKETS_GOOD_BAD_RX256_511OCTGB(n)     (((n) << EMAC_RX_256TO511OCTETS_PACKETS_GOOD_BAD_RX256_511OCTGB_SHIFT) & EMAC_RX_256TO511OCTETS_PACKETS_GOOD_BAD_RX256_511OCTGB_MASK)

/* Receive 512-1023 Octets Packets Good Bad
 * (RX_512TO1023OCTETS_PACKETS_GOOD_BAD)
 */

#define EMAC_RX_512TO1023OCTETS_PACKETS_GOOD_BAD_RX512_1023OCTGB_SHIFT  (0) /* Bits 0-32: Receive 512-1023 Octets Packets Good Bad */
#define EMAC_RX_512TO1023OCTETS_PACKETS_GOOD_BAD_RX512_1023OCTGB_MASK   (0xffffffff << EMAC_RX_512TO1023OCTETS_PACKETS_GOOD_BAD_RX512_1023OCTGB_SHIFT)
#define EMAC_RX_512TO1023OCTETS_PACKETS_GOOD_BAD_RX512_1023OCTGB(n)     (((n) << EMAC_RX_512TO1023OCTETS_PACKETS_GOOD_BAD_RX512_1023OCTGB_SHIFT) & EMAC_RX_512TO1023OCTETS_PACKETS_GOOD_BAD_RX512_1023OCTGB_MASK)

/* Receive 1024 To Max Octets Good Bad
 * (RX_1024TOMAXOCTETS_PACKETS_GOOD_BAD)
 */

#define EMAC_RX_1024TOMAXOCTETS_PACKETS_GOOD_BAD_RX1024_MAXOCTGB_SHIFT  (0) /* Bits 0-32: Receive 1024-Max Octets Good Bad */
#define EMAC_RX_1024TOMAXOCTETS_PACKETS_GOOD_BAD_RX1024_MAXOCTGB_MASK   (0xffffffff << EMAC_RX_1024TOMAXOCTETS_PACKETS_GOOD_BAD_RX1024_MAXOCTGB_SHIFT)
#define EMAC_RX_1024TOMAXOCTETS_PACKETS_GOOD_BAD_RX1024_MAXOCTGB(n)     (((n) << EMAC_RX_1024TOMAXOCTETS_PACKETS_GOOD_BAD_RX1024_MAXOCTGB_SHIFT) & EMAC_RX_1024TOMAXOCTETS_PACKETS_GOOD_BAD_RX1024_MAXOCTGB_MASK)

/* Receive Unicast Packets Good (RX_UNICAST_PACKETS_GOOD) */
#define EMAC_RX_UNICAST_PACKETS_GOOD_RXUCASTG_SHIFT  (0) /* Bits 0-32: Receive Unicast Packets Good */
#define EMAC_RX_UNICAST_PACKETS_GOOD_RXUCASTG_MASK   (0xffffffff << EMAC_RX_UNICAST_PACKETS_GOOD_RXUCASTG_SHIFT)
#define EMAC_RX_UNICAST_PACKETS_GOOD_RXUCASTG(n)     (((n) << EMAC_RX_UNICAST_PACKETS_GOOD_RXUCASTG_SHIFT) & EMAC_RX_UNICAST_PACKETS_GOOD_RXUCASTG_MASK)

/* Receive Length Error Packets (RX_LENGTH_ERROR_PACKETS) */
#define EMAC_RX_LENGTH_ERROR_PACKETS_RXLENERR_SHIFT  (0) /* Bits 0-32: Receive Length Error Packets */
#define EMAC_RX_LENGTH_ERROR_PACKETS_RXLENERR_MASK   (0xffffffff << EMAC_RX_LENGTH_ERROR_PACKETS_RXLENERR_SHIFT)
#define EMAC_RX_LENGTH_ERROR_PACKETS_RXLENERR(n)     (((n) << EMAC_RX_LENGTH_ERROR_PACKETS_RXLENERR_SHIFT) & EMAC_RX_LENGTH_ERROR_PACKETS_RXLENERR_MASK)

/* Receive Out of Range Type Packet (RX_OUT_OF_RANGE_TYPE_PACKETS) */
#define EMAC_RX_OUT_OF_RANGE_TYPE_PACKETS_RXOUTOFRNG_SHIFT  (0) /* Bits 0-32: Receive Out of Range Type Packet */
#define EMAC_RX_OUT_OF_RANGE_TYPE_PACKETS_RXOUTOFRNG_MASK   (0xffffffff << EMAC_RX_OUT_OF_RANGE_TYPE_PACKETS_RXOUTOFRNG_SHIFT)
#define EMAC_RX_OUT_OF_RANGE_TYPE_PACKETS_RXOUTOFRNG(n)     (((n) << EMAC_RX_OUT_OF_RANGE_TYPE_PACKETS_RXOUTOFRNG_SHIFT) & EMAC_RX_OUT_OF_RANGE_TYPE_PACKETS_RXOUTOFRNG_MASK)

/* Receive Pause Packets (RX_PAUSE_PACKETS) */
#define EMAC_RX_PAUSE_PACKETS_RXPAUSEPKT_SHIFT  (0) /* Bits 0-32: Receive Pause Packets */
#define EMAC_RX_PAUSE_PACKETS_RXPAUSEPKT_MASK   (0xffffffff << EMAC_RX_PAUSE_PACKETS_RXPAUSEPKT_SHIFT)
#define EMAC_RX_PAUSE_PACKETS_RXPAUSEPKT(n)     (((n) << EMAC_RX_PAUSE_PACKETS_RXPAUSEPKT_SHIFT) & EMAC_RX_PAUSE_PACKETS_RXPAUSEPKT_MASK)

/* Receive FIFO Overflow Packets (RX_FIFO_OVERFLOW_PACKETS) */
#define EMAC_RX_FIFO_OVERFLOW_PACKETS_RXFIFOOVFL_SHIFT  (0) /* Bits 0-32: Receive FIFO Overflow Packets */
#define EMAC_RX_FIFO_OVERFLOW_PACKETS_RXFIFOOVFL_MASK   (0xffffffff << EMAC_RX_FIFO_OVERFLOW_PACKETS_RXFIFOOVFL_SHIFT)
#define EMAC_RX_FIFO_OVERFLOW_PACKETS_RXFIFOOVFL(n)     (((n) << EMAC_RX_FIFO_OVERFLOW_PACKETS_RXFIFOOVFL_SHIFT) & EMAC_RX_FIFO_OVERFLOW_PACKETS_RXFIFOOVFL_MASK)

/* Receive VLAN Packets Good Bad (RX_VLAN_PACKETS_GOOD_BAD) */
#define EMAC_RX_VLAN_PACKETS_GOOD_BAD_RXVLANPKTGB_SHIFT  (0) /* Bits 0-32: Receive VLAN Packets Good Bad */
#define EMAC_RX_VLAN_PACKETS_GOOD_BAD_RXVLANPKTGB_MASK   (0xffffffff << EMAC_RX_VLAN_PACKETS_GOOD_BAD_RXVLANPKTGB_SHIFT)
#define EMAC_RX_VLAN_PACKETS_GOOD_BAD_RXVLANPKTGB(n)     (((n) << EMAC_RX_VLAN_PACKETS_GOOD_BAD_RXVLANPKTGB_SHIFT) & EMAC_RX_VLAN_PACKETS_GOOD_BAD_RXVLANPKTGB_MASK)

/* Receive Watchdog Error Packets (RX_WATCHDOG_ERROR_PACKETS) */
#define EMAC_RX_WATCHDOG_ERROR_PACKETS_RXWDGERR_SHIFT  (0) /* Bits 0-32: Receive Watchdog Error Packets */
#define EMAC_RX_WATCHDOG_ERROR_PACKETS_RXWDGERR_MASK   (0xffffffff << EMAC_RX_WATCHDOG_ERROR_PACKETS_RXWDGERR_SHIFT)
#define EMAC_RX_WATCHDOG_ERROR_PACKETS_RXWDGERR(n)     (((n) << EMAC_RX_WATCHDOG_ERROR_PACKETS_RXWDGERR_SHIFT) & EMAC_RX_WATCHDOG_ERROR_PACKETS_RXWDGERR_MASK)

/* Receive Receive Error Packets (RX_RECEIVE_ERROR_PACKETS) */
#define EMAC_RX_RECEIVE_ERROR_PACKETS_RXRCVERR_SHIFT  (0) /* Bits 0-32: Receive Receive Error Packets */
#define EMAC_RX_RECEIVE_ERROR_PACKETS_RXRCVERR_MASK   (0xffffffff << EMAC_RX_RECEIVE_ERROR_PACKETS_RXRCVERR_SHIFT)
#define EMAC_RX_RECEIVE_ERROR_PACKETS_RXRCVERR(n)     (((n) << EMAC_RX_RECEIVE_ERROR_PACKETS_RXRCVERR_SHIFT) & EMAC_RX_RECEIVE_ERROR_PACKETS_RXRCVERR_MASK)

/* Receive Control Packets Good (RX_CONTROL_PACKETS_GOOD) */
#define EMAC_RX_CONTROL_PACKETS_GOOD_RXCTRLG_SHIFT  (0) /* Bits 0-32: Receive Control Packets Good */
#define EMAC_RX_CONTROL_PACKETS_GOOD_RXCTRLG_MASK   (0xffffffff << EMAC_RX_CONTROL_PACKETS_GOOD_RXCTRLG_SHIFT)
#define EMAC_RX_CONTROL_PACKETS_GOOD_RXCTRLG(n)     (((n) << EMAC_RX_CONTROL_PACKETS_GOOD_RXCTRLG_SHIFT) & EMAC_RX_CONTROL_PACKETS_GOOD_RXCTRLG_MASK)

/* MMC Transmit FPE Fragment Counter Interrupt Status
 * (MMC_FPE_TX_INTERRUPT)
 */

#define EMAC_MMC_FPE_TX_INTERRUPT_FCIS   (1 << 0) /* Bit 0: MMC Transmit FPE Fragment Counter Interrupt Status */
#define EMAC_MMC_FPE_TX_INTERRUPT_HRCIS  (1 << 1) /* Bit 1: MMC Transmit Hold Request Counter Interrupt Status */

/* MMC FPE Transmit Interrupt Mask (MMC_FPE_TX_INTERRUPT_MASK) */
#define EMAC_MMC_FPE_TX_INTERRUPT_MASK_FCIM   (1 << 0) /* Bit 0: MMC Transmit Fragment Counter Interrupt Mask */
#define EMAC_MMC_FPE_TX_INTERRUPT_MASK_HRCIM  (1 << 1) /* Bit 1: MMC Transmit Hold Request Counter Interrupt Mask */

/* Transmit FPE Fragment Counter (MMC_TX_FPE_FRAGMENT_CNTR) */
#define EMAC_MMC_TX_FPE_FRAGMENT_CNTR_TXFFC_SHIFT  (0) /* Bits 0-32: Transmit FPE Fragment Counter */
#define EMAC_MMC_TX_FPE_FRAGMENT_CNTR_TXFFC_MASK   (0xffffffff << EMAC_MMC_TX_FPE_FRAGMENT_CNTR_TXFFC_SHIFT)
#define EMAC_MMC_TX_FPE_FRAGMENT_CNTR_TXFFC(n)     (((n) << EMAC_MMC_TX_FPE_FRAGMENT_CNTR_TXFFC_SHIFT) & EMAC_MMC_TX_FPE_FRAGMENT_CNTR_TXFFC_MASK)

/* Transmit Hold Request Counter (MMC_TX_HOLD_REQ_CNTR) */
#define EMAC_MMC_TX_HOLD_REQ_CNTR_TXHRC_SHIFT  (0) /* Bits 0-32: Transmit Hold Request Counter */
#define EMAC_MMC_TX_HOLD_REQ_CNTR_TXHRC_MASK   (0xffffffff << EMAC_MMC_TX_HOLD_REQ_CNTR_TXHRC_SHIFT)
#define EMAC_MMC_TX_HOLD_REQ_CNTR_TXHRC(n)     (((n) << EMAC_MMC_TX_HOLD_REQ_CNTR_TXHRC_SHIFT) & EMAC_MMC_TX_HOLD_REQ_CNTR_TXHRC_MASK)

/* MMC Receive Packet Assembly Error Counter Interrupt Status
 * (MMC_FPE_RX_INTERRUPT)
 */

#define EMAC_MMC_FPE_RX_INTERRUPT_PAECIS  (1 << 0) /* Bit 0: MMC Receive, transmit, Packet Assembly Error Counter Interrupt Status */
#define EMAC_MMC_FPE_RX_INTERRUPT_PSECIS  (1 << 1) /* Bit 1: MMC Receive, Transmit, Packet SMD Error Counter Interrupt Status */
#define EMAC_MMC_FPE_RX_INTERRUPT_PAOCIS  (1 << 2) /* Bit 2: MMC Receive Packet Assembly OK Counter Interrupt Status */
#define EMAC_MMC_FPE_RX_INTERRUPT_FCIS    (1 << 3) /* Bit 3: MMC Receive FPE Fragment Counter Interrupt Status */

/* MMC FPE Receive Interrupt Mask (MMC_FPE_RX_INTERRUPT_MASK) */
#define EMAC_MMC_FPE_RX_INTERRUPT_MASK_PAECIM  (1 << 0) /* Bit 0: MMC receive Packet Assembly Error Counter Interrupt Mask */
#define EMAC_MMC_FPE_RX_INTERRUPT_MASK_PSECIM  (1 << 1) /* Bit 1: MMC Receive Packet SMD Error Counter Interrupt Mask */
#define EMAC_MMC_FPE_RX_INTERRUPT_MASK_PAOCIM  (1 << 2) /* Bit 2: MMC Receive Packet Assembly OK Counter Interrupt Mask */
#define EMAC_MMC_FPE_RX_INTERRUPT_MASK_FCIM    (1 << 3) /* Bit 3: MMC Receive FPE Fragment Counter Interrupt Mask */

/* MMC Receive Packet Assembly Error Counter
 * (MMC_RX_PACKET_ASSEMBLY_ERR_CNTR)
 */

#define EMAC_MMC_RX_PACKET_ASSEMBLY_ERR_CNTR_PAEC_SHIFT  (0) /* Bits 0-32: Packet Assembly Error Counter */
#define EMAC_MMC_RX_PACKET_ASSEMBLY_ERR_CNTR_PAEC_MASK   (0xffffffff << EMAC_MMC_RX_PACKET_ASSEMBLY_ERR_CNTR_PAEC_SHIFT)
#define EMAC_MMC_RX_PACKET_ASSEMBLY_ERR_CNTR_PAEC(n)     (((n) << EMAC_MMC_RX_PACKET_ASSEMBLY_ERR_CNTR_PAEC_SHIFT) & EMAC_MMC_RX_PACKET_ASSEMBLY_ERR_CNTR_PAEC_MASK)

/* MMC Receive Packet SMD Error Counter (MMC_RX_PACKET_SMD_ERR_CNTR) */
#define EMAC_MMC_RX_PACKET_SMD_ERR_CNTR_PSEC_SHIFT  (0) /* Bits 0-32: Packet SMD Error Counter */
#define EMAC_MMC_RX_PACKET_SMD_ERR_CNTR_PSEC_MASK   (0xffffffff << EMAC_MMC_RX_PACKET_SMD_ERR_CNTR_PSEC_SHIFT)
#define EMAC_MMC_RX_PACKET_SMD_ERR_CNTR_PSEC(n)     (((n) << EMAC_MMC_RX_PACKET_SMD_ERR_CNTR_PSEC_SHIFT) & EMAC_MMC_RX_PACKET_SMD_ERR_CNTR_PSEC_MASK)

/* MMC Receive Packet Assembly OK Counter (MMC_RX_PACKET_ASSEMBLY_OK_CNTR) */
#define EMAC_MMC_RX_PACKET_ASSEMBLY_OK_CNTR_PAOC_SHIFT  (0) /* Bits 0-32: Packet Assembly OK Counter */
#define EMAC_MMC_RX_PACKET_ASSEMBLY_OK_CNTR_PAOC_MASK   (0xffffffff << EMAC_MMC_RX_PACKET_ASSEMBLY_OK_CNTR_PAOC_SHIFT)
#define EMAC_MMC_RX_PACKET_ASSEMBLY_OK_CNTR_PAOC(n)     (((n) << EMAC_MMC_RX_PACKET_ASSEMBLY_OK_CNTR_PAOC_SHIFT) & EMAC_MMC_RX_PACKET_ASSEMBLY_OK_CNTR_PAOC_MASK)

/* MMC Receive FPE Fragment Counter (MMC_RX_FPE_FRAGMENT_CNTR) */
#define EMAC_MMC_RX_FPE_FRAGMENT_CNTR_FFC_SHIFT  (0) /* Bits 0-32: FPE Fragment Counter */
#define EMAC_MMC_RX_FPE_FRAGMENT_CNTR_FFC_MASK   (0xffffffff << EMAC_MMC_RX_FPE_FRAGMENT_CNTR_FFC_SHIFT)
#define EMAC_MMC_RX_FPE_FRAGMENT_CNTR_FFC(n)     (((n) << EMAC_MMC_RX_FPE_FRAGMENT_CNTR_FFC_SHIFT) & EMAC_MMC_RX_FPE_FRAGMENT_CNTR_FFC_MASK)

/* MAC Layer 3 Layer 4 Control 0 (MAC_L3_L4_CONTROL0) */
#define EMAC_MAC_L3_L4_CONTROL0_L3PEN0         (1 << 0) /* Bit 0: Layer 3 Protocol Enable */
#define EMAC_MAC_L3_L4_CONTROL0_L3SAM0         (1 << 2) /* Bit 2: Layer 3 IP SA Match Enable */
#define EMAC_MAC_L3_L4_CONTROL0_L3SAIM0        (1 << 3) /* Bit 3: Layer 3 IP SA Inverse Match Enable */
#define EMAC_MAC_L3_L4_CONTROL0_L3DAM0         (1 << 4) /* Bit 4: Layer 3 IP DA Match Enable */
#define EMAC_MAC_L3_L4_CONTROL0_L3DAIM0        (1 << 5) /* Bit 5: Layer 3 IP DA Inverse Match Enable */
#define EMAC_MAC_L3_L4_CONTROL0_L3HSBM0_SHIFT  (6)      /* Bits 6-11: Layer 3 IP SA Higher Bits Match */
#define EMAC_MAC_L3_L4_CONTROL0_L3HSBM0_MASK   (0x1f << EMAC_MAC_L3_L4_CONTROL0_L3HSBM0_SHIFT)
#define EMAC_MAC_L3_L4_CONTROL0_L3HSBM0(n)     (((n) << EMAC_MAC_L3_L4_CONTROL0_L3HSBM0_SHIFT) & EMAC_MAC_L3_L4_CONTROL0_L3HSBM0_MASK)
#define EMAC_MAC_L3_L4_CONTROL0_L3HDBM0_SHIFT  (11) /* Bits 11-16: Layer 3 IP DA Higher Bits Match */
#define EMAC_MAC_L3_L4_CONTROL0_L3HDBM0_MASK   (0x1f << EMAC_MAC_L3_L4_CONTROL0_L3HDBM0_SHIFT)
#define EMAC_MAC_L3_L4_CONTROL0_L3HDBM0(n)     (((n) << EMAC_MAC_L3_L4_CONTROL0_L3HDBM0_SHIFT) & EMAC_MAC_L3_L4_CONTROL0_L3HDBM0_MASK)
#define EMAC_MAC_L3_L4_CONTROL0_L4PEN0         (1 << 16) /* Bit 16: Layer 4 Protocol Enable */
#define EMAC_MAC_L3_L4_CONTROL0_L4SPM0         (1 << 18) /* Bit 18: Layer 4 Source Port Match Enable */
#define EMAC_MAC_L3_L4_CONTROL0_L4SPIM0        (1 << 19) /* Bit 19: Layer 4 Source Port Inverse Match Enable */
#define EMAC_MAC_L3_L4_CONTROL0_L4DPM0         (1 << 20) /* Bit 20: Layer 4 Destination Port Match Enable */
#define EMAC_MAC_L3_L4_CONTROL0_L4DPIM0        (1 << 21) /* Bit 21: Layer 4 Destination Port Inverse Match Enable */
#define EMAC_MAC_L3_L4_CONTROL0_DMCHN0         (1 << 24) /* Bit 24: DMA Channel Number */
#define EMAC_MAC_L3_L4_CONTROL0_DMCHEN0        (1 << 28) /* Bit 28: DMA Channel Select Enable */

/* MAC Layer 4 Address 0 (MAC_LAYER4_ADDRESS0) */
#define EMAC_MAC_LAYER4_ADDRESS0_L4SP0_SHIFT  (0) /* Bits 0-16: Layer 4 Source Port Number */
#define EMAC_MAC_LAYER4_ADDRESS0_L4SP0_MASK   (0xffff << EMAC_MAC_LAYER4_ADDRESS0_L4SP0_SHIFT)
#define EMAC_MAC_LAYER4_ADDRESS0_L4SP0(n)     (((n) << EMAC_MAC_LAYER4_ADDRESS0_L4SP0_SHIFT) & EMAC_MAC_LAYER4_ADDRESS0_L4SP0_MASK)
#define EMAC_MAC_LAYER4_ADDRESS0_L4DP0_SHIFT  (16) /* Bits 16-32: Layer 4 Destination Port Number */
#define EMAC_MAC_LAYER4_ADDRESS0_L4DP0_MASK   (0xffff << EMAC_MAC_LAYER4_ADDRESS0_L4DP0_SHIFT)
#define EMAC_MAC_LAYER4_ADDRESS0_L4DP0(n)     (((n) << EMAC_MAC_LAYER4_ADDRESS0_L4DP0_SHIFT) & EMAC_MAC_LAYER4_ADDRESS0_L4DP0_MASK)

/* MAC Layer 3 Address 0 Reg 0 (MAC_LAYER3_ADDR0_REG0) */
#define EMAC_MAC_LAYER3_ADDR0_REG0_L3A00_SHIFT  (0) /* Bits 0-32: Layer 3 Address 0 */
#define EMAC_MAC_LAYER3_ADDR0_REG0_L3A00_MASK   (0xffffffff << EMAC_MAC_LAYER3_ADDR0_REG0_L3A00_SHIFT)
#define EMAC_MAC_LAYER3_ADDR0_REG0_L3A00(n)     (((n) << EMAC_MAC_LAYER3_ADDR0_REG0_L3A00_SHIFT) & EMAC_MAC_LAYER3_ADDR0_REG0_L3A00_MASK)

/* MAC Layer 3 Address 1 Reg 0 (MAC_LAYER3_ADDR1_REG0) */
#define EMAC_MAC_LAYER3_ADDR1_REG0_L3A10_SHIFT  (0) /* Bits 0-32: Layer 3 Address 1 */
#define EMAC_MAC_LAYER3_ADDR1_REG0_L3A10_MASK   (0xffffffff << EMAC_MAC_LAYER3_ADDR1_REG0_L3A10_SHIFT)
#define EMAC_MAC_LAYER3_ADDR1_REG0_L3A10(n)     (((n) << EMAC_MAC_LAYER3_ADDR1_REG0_L3A10_SHIFT) & EMAC_MAC_LAYER3_ADDR1_REG0_L3A10_MASK)

/* MAC Layer 3 Address 2 Reg 0 (MAC_LAYER3_ADDR2_REG0) */
#define EMAC_MAC_LAYER3_ADDR2_REG0_L3A20_SHIFT  (0) /* Bits 0-32: Layer 3 Address 2 */
#define EMAC_MAC_LAYER3_ADDR2_REG0_L3A20_MASK   (0xffffffff << EMAC_MAC_LAYER3_ADDR2_REG0_L3A20_SHIFT)
#define EMAC_MAC_LAYER3_ADDR2_REG0_L3A20(n)     (((n) << EMAC_MAC_LAYER3_ADDR2_REG0_L3A20_SHIFT) & EMAC_MAC_LAYER3_ADDR2_REG0_L3A20_MASK)

/* MAC Layer 3 Address 3 Reg 0 (MAC_LAYER3_ADDR3_REG0) */
#define EMAC_MAC_LAYER3_ADDR3_REG0_L3A30_SHIFT  (0) /* Bits 0-32: Layer 3 Address 3 */
#define EMAC_MAC_LAYER3_ADDR3_REG0_L3A30_MASK   (0xffffffff << EMAC_MAC_LAYER3_ADDR3_REG0_L3A30_SHIFT)
#define EMAC_MAC_LAYER3_ADDR3_REG0_L3A30(n)     (((n) << EMAC_MAC_LAYER3_ADDR3_REG0_L3A30_SHIFT) & EMAC_MAC_LAYER3_ADDR3_REG0_L3A30_MASK)

/* MAC L3 L4 Control 1 (MAC_L3_L4_CONTROL1) */
#define EMAC_MAC_L3_L4_CONTROL1_L3PEN1         (1 << 0) /* Bit 0: Layer 3 Protocol Enable 1 */
#define EMAC_MAC_L3_L4_CONTROL1_L3SAM1         (1 << 2) /* Bit 2: Layer 3 IP SA Match Enable 1 */
#define EMAC_MAC_L3_L4_CONTROL1_L3SAIM1        (1 << 3) /* Bit 3: Layer 3 IP SA Inverse Match Enable 1 */
#define EMAC_MAC_L3_L4_CONTROL1_L3DAM1         (1 << 4) /* Bit 4: Layer 3 IP DA Match Enable 1 */
#define EMAC_MAC_L3_L4_CONTROL1_L3DAIM1        (1 << 5) /* Bit 5: Layer 3 IP DA Inverse Match Enable 1 */
#define EMAC_MAC_L3_L4_CONTROL1_L3HSBM1_SHIFT  (6)      /* Bits 6-11: Layer 3 IP SA Higher Bits Match 1 */
#define EMAC_MAC_L3_L4_CONTROL1_L3HSBM1_MASK   (0x1f << EMAC_MAC_L3_L4_CONTROL1_L3HSBM1_SHIFT)
#define EMAC_MAC_L3_L4_CONTROL1_L3HSBM1(n)     (((n) << EMAC_MAC_L3_L4_CONTROL1_L3HSBM1_SHIFT) & EMAC_MAC_L3_L4_CONTROL1_L3HSBM1_MASK)
#define EMAC_MAC_L3_L4_CONTROL1_L3HDBM1_SHIFT  (11) /* Bits 11-16: Layer 3 IP DA Higher Bits Match 1 */
#define EMAC_MAC_L3_L4_CONTROL1_L3HDBM1_MASK   (0x1f << EMAC_MAC_L3_L4_CONTROL1_L3HDBM1_SHIFT)
#define EMAC_MAC_L3_L4_CONTROL1_L3HDBM1(n)     (((n) << EMAC_MAC_L3_L4_CONTROL1_L3HDBM1_SHIFT) & EMAC_MAC_L3_L4_CONTROL1_L3HDBM1_MASK)
#define EMAC_MAC_L3_L4_CONTROL1_L4PEN1         (1 << 16) /* Bit 16: Layer 4 Protocol Enable 1 */
#define EMAC_MAC_L3_L4_CONTROL1_L4SPM1         (1 << 18) /* Bit 18: Layer 4 Source Port Match Enable 1 */
#define EMAC_MAC_L3_L4_CONTROL1_L4SPIM1        (1 << 19) /* Bit 19: Layer 4 Source Port Inverse Match Enable 1 */
#define EMAC_MAC_L3_L4_CONTROL1_L4DPM1         (1 << 20) /* Bit 20: Layer 4 Destination Port Match Enable 1 */
#define EMAC_MAC_L3_L4_CONTROL1_L4DPIM1        (1 << 21) /* Bit 21: Layer 4 Destination Port Inverse Match Enable 1 */
#define EMAC_MAC_L3_L4_CONTROL1_DMCHN1         (1 << 24) /* Bit 24: DMA Channel Number 1 */
#define EMAC_MAC_L3_L4_CONTROL1_DMCHEN1        (1 << 28) /* Bit 28: DMA Channel Select Enable 1 */

/* MAC Layer 4 Address 1 (MAC_LAYER4_ADDRESS1) */
#define EMAC_MAC_LAYER4_ADDRESS1_L4SP1_SHIFT  (0) /* Bits 0-16: Layer 4 Source Port Number 1 */
#define EMAC_MAC_LAYER4_ADDRESS1_L4SP1_MASK   (0xffff << EMAC_MAC_LAYER4_ADDRESS1_L4SP1_SHIFT)
#define EMAC_MAC_LAYER4_ADDRESS1_L4SP1(n)     (((n) << EMAC_MAC_LAYER4_ADDRESS1_L4SP1_SHIFT) & EMAC_MAC_LAYER4_ADDRESS1_L4SP1_MASK)
#define EMAC_MAC_LAYER4_ADDRESS1_L4DP1_SHIFT  (16) /* Bits 16-32: Layer 4 Destination Port Number 1 */
#define EMAC_MAC_LAYER4_ADDRESS1_L4DP1_MASK   (0xffff << EMAC_MAC_LAYER4_ADDRESS1_L4DP1_SHIFT)
#define EMAC_MAC_LAYER4_ADDRESS1_L4DP1(n)     (((n) << EMAC_MAC_LAYER4_ADDRESS1_L4DP1_SHIFT) & EMAC_MAC_LAYER4_ADDRESS1_L4DP1_MASK)

/* MAC Layer 3 Address 0 Reg 1 (MAC_LAYER3_ADDR0_REG1) */
#define EMAC_MAC_LAYER3_ADDR0_REG1_L3A01_SHIFT  (0) /* Bits 0-32: Layer 3 Address 0 */
#define EMAC_MAC_LAYER3_ADDR0_REG1_L3A01_MASK   (0xffffffff << EMAC_MAC_LAYER3_ADDR0_REG1_L3A01_SHIFT)
#define EMAC_MAC_LAYER3_ADDR0_REG1_L3A01(n)     (((n) << EMAC_MAC_LAYER3_ADDR0_REG1_L3A01_SHIFT) & EMAC_MAC_LAYER3_ADDR0_REG1_L3A01_MASK)

/* MAC Layer 3 Address 1 Reg 1 (MAC_LAYER3_ADDR1_REG1) */
#define EMAC_MAC_LAYER3_ADDR1_REG1_L3A11_SHIFT  (0) /* Bits 0-32: Layer 3 Address 1 */
#define EMAC_MAC_LAYER3_ADDR1_REG1_L3A11_MASK   (0xffffffff << EMAC_MAC_LAYER3_ADDR1_REG1_L3A11_SHIFT)
#define EMAC_MAC_LAYER3_ADDR1_REG1_L3A11(n)     (((n) << EMAC_MAC_LAYER3_ADDR1_REG1_L3A11_SHIFT) & EMAC_MAC_LAYER3_ADDR1_REG1_L3A11_MASK)

/* MAC Layer 3 Address 2 Reg 1 (MAC_LAYER3_ADDR2_REG1) */
#define EMAC_MAC_LAYER3_ADDR2_REG1_L3A21_SHIFT  (0) /* Bits 0-32: Layer 3 Address 2 */
#define EMAC_MAC_LAYER3_ADDR2_REG1_L3A21_MASK   (0xffffffff << EMAC_MAC_LAYER3_ADDR2_REG1_L3A21_SHIFT)
#define EMAC_MAC_LAYER3_ADDR2_REG1_L3A21(n)     (((n) << EMAC_MAC_LAYER3_ADDR2_REG1_L3A21_SHIFT) & EMAC_MAC_LAYER3_ADDR2_REG1_L3A21_MASK)

/* MAC Layer 3 Address 3 Reg 1 (MAC_LAYER3_ADDR3_REG1) */
#define EMAC_MAC_LAYER3_ADDR3_REG1_L3A31_SHIFT  (0) /* Bits 0-32: Layer 3 Address 3 */
#define EMAC_MAC_LAYER3_ADDR3_REG1_L3A31_MASK   (0xffffffff << EMAC_MAC_LAYER3_ADDR3_REG1_L3A31_SHIFT)
#define EMAC_MAC_LAYER3_ADDR3_REG1_L3A31(n)     (((n) << EMAC_MAC_LAYER3_ADDR3_REG1_L3A31_SHIFT) & EMAC_MAC_LAYER3_ADDR3_REG1_L3A31_MASK)

/* MAC L3 L4 Control 2 (MAC_L3_L4_CONTROL2) */
#define EMAC_MAC_L3_L4_CONTROL2_L3PEN2         (1 << 0) /* Bit 0: Layer 3 Protocol Enable 2 */
#define EMAC_MAC_L3_L4_CONTROL2_L3SAM2         (1 << 2) /* Bit 2: Layer 3 IP SA Match Enable 2 */
#define EMAC_MAC_L3_L4_CONTROL2_L3SAIM2        (1 << 3) /* Bit 3: Layer 3 IP SA Inverse Match Enable 2 */
#define EMAC_MAC_L3_L4_CONTROL2_L3DAM2         (1 << 4) /* Bit 4: Layer 3 IP DA Match Enable 2 */
#define EMAC_MAC_L3_L4_CONTROL2_L3DAIM2        (1 << 5) /* Bit 5: Layer 3 IP DA Inverse Match Enable 2 */
#define EMAC_MAC_L3_L4_CONTROL2_L3HSBM2_SHIFT  (6)      /* Bits 6-11: Layer 3 IP SA Higher Bits Match 2 */
#define EMAC_MAC_L3_L4_CONTROL2_L3HSBM2_MASK   (0x1f << EMAC_MAC_L3_L4_CONTROL2_L3HSBM2_SHIFT)
#define EMAC_MAC_L3_L4_CONTROL2_L3HSBM2(n)     (((n) << EMAC_MAC_L3_L4_CONTROL2_L3HSBM2_SHIFT) & EMAC_MAC_L3_L4_CONTROL2_L3HSBM2_MASK)
#define EMAC_MAC_L3_L4_CONTROL2_L3HDBM2_SHIFT  (11) /* Bits 11-16: Layer 3 IP DA Higher Bits Match 2 */
#define EMAC_MAC_L3_L4_CONTROL2_L3HDBM2_MASK   (0x1f << EMAC_MAC_L3_L4_CONTROL2_L3HDBM2_SHIFT)
#define EMAC_MAC_L3_L4_CONTROL2_L3HDBM2(n)     (((n) << EMAC_MAC_L3_L4_CONTROL2_L3HDBM2_SHIFT) & EMAC_MAC_L3_L4_CONTROL2_L3HDBM2_MASK)
#define EMAC_MAC_L3_L4_CONTROL2_L4PEN2         (1 << 16) /* Bit 16: Layer 4 Protocol Enable 2 */
#define EMAC_MAC_L3_L4_CONTROL2_L4SPM2         (1 << 18) /* Bit 18: Layer 4 Source Port Match Enable 2 */
#define EMAC_MAC_L3_L4_CONTROL2_L4SPIM2        (1 << 19) /* Bit 19: Layer 4 Source Port Inverse Match Enable 2 */
#define EMAC_MAC_L3_L4_CONTROL2_L4DPM2         (1 << 20) /* Bit 20: Layer 4 Destination Port Match Enable 2 */
#define EMAC_MAC_L3_L4_CONTROL2_L4DPIM2        (1 << 21) /* Bit 21: Layer 4 Destination Port Inverse Match Enable 2 */
#define EMAC_MAC_L3_L4_CONTROL2_DMCHN2         (1 << 24) /* Bit 24: DMA Channel Number 2 */
#define EMAC_MAC_L3_L4_CONTROL2_DMCHEN2        (1 << 28) /* Bit 28: DMA Channel Select Enable 2 */

/* MAC Layer 4 Address 2 (MAC_LAYER4_ADDRESS2) */
#define EMAC_MAC_LAYER4_ADDRESS2_L4SP2_SHIFT  (0) /* Bits 0-16: Layer 4 Source Port Number 2 */
#define EMAC_MAC_LAYER4_ADDRESS2_L4SP2_MASK   (0xffff << EMAC_MAC_LAYER4_ADDRESS2_L4SP2_SHIFT)
#define EMAC_MAC_LAYER4_ADDRESS2_L4SP2(n)     (((n) << EMAC_MAC_LAYER4_ADDRESS2_L4SP2_SHIFT) & EMAC_MAC_LAYER4_ADDRESS2_L4SP2_MASK)
#define EMAC_MAC_LAYER4_ADDRESS2_L4DP2_SHIFT  (16) /* Bits 16-32: Layer 4 Destination Port Number 2 */
#define EMAC_MAC_LAYER4_ADDRESS2_L4DP2_MASK   (0xffff << EMAC_MAC_LAYER4_ADDRESS2_L4DP2_SHIFT)
#define EMAC_MAC_LAYER4_ADDRESS2_L4DP2(n)     (((n) << EMAC_MAC_LAYER4_ADDRESS2_L4DP2_SHIFT) & EMAC_MAC_LAYER4_ADDRESS2_L4DP2_MASK)

/* MAC Layer 3 Address 0 Reg 2 (MAC_LAYER3_ADDR0_REG2) */
#define EMAC_MAC_LAYER3_ADDR0_REG2_L3A02_SHIFT  (0) /* Bits 0-32: Layer 3 Address 0 */
#define EMAC_MAC_LAYER3_ADDR0_REG2_L3A02_MASK   (0xffffffff << EMAC_MAC_LAYER3_ADDR0_REG2_L3A02_SHIFT)
#define EMAC_MAC_LAYER3_ADDR0_REG2_L3A02(n)     (((n) << EMAC_MAC_LAYER3_ADDR0_REG2_L3A02_SHIFT) & EMAC_MAC_LAYER3_ADDR0_REG2_L3A02_MASK)

/* MAC Layer 3 Address 1 Reg 2 (MAC_LAYER3_ADDR1_REG2) */
#define EMAC_MAC_LAYER3_ADDR1_REG2_L3A12_SHIFT  (0) /* Bits 0-32: Layer 3 Address 1 */
#define EMAC_MAC_LAYER3_ADDR1_REG2_L3A12_MASK   (0xffffffff << EMAC_MAC_LAYER3_ADDR1_REG2_L3A12_SHIFT)
#define EMAC_MAC_LAYER3_ADDR1_REG2_L3A12(n)     (((n) << EMAC_MAC_LAYER3_ADDR1_REG2_L3A12_SHIFT) & EMAC_MAC_LAYER3_ADDR1_REG2_L3A12_MASK)

/* MAC Layer 3 Address 2 Reg 2 (MAC_LAYER3_ADDR2_REG2) */
#define EMAC_MAC_LAYER3_ADDR2_REG2_L3A22_SHIFT  (0) /* Bits 0-32: Layer 3 Address 2 */
#define EMAC_MAC_LAYER3_ADDR2_REG2_L3A22_MASK   (0xffffffff << EMAC_MAC_LAYER3_ADDR2_REG2_L3A22_SHIFT)
#define EMAC_MAC_LAYER3_ADDR2_REG2_L3A22(n)     (((n) << EMAC_MAC_LAYER3_ADDR2_REG2_L3A22_SHIFT) & EMAC_MAC_LAYER3_ADDR2_REG2_L3A22_MASK)

/* MAC Layer 3 Address 3 Reg 2 (MAC_LAYER3_ADDR3_REG2) */
#define EMAC_MAC_LAYER3_ADDR3_REG2_L3A32_SHIFT  (0) /* Bits 0-32: Layer 3 Address 3 */
#define EMAC_MAC_LAYER3_ADDR3_REG2_L3A32_MASK   (0xffffffff << EMAC_MAC_LAYER3_ADDR3_REG2_L3A32_SHIFT)
#define EMAC_MAC_LAYER3_ADDR3_REG2_L3A32(n)     (((n) << EMAC_MAC_LAYER3_ADDR3_REG2_L3A32_SHIFT) & EMAC_MAC_LAYER3_ADDR3_REG2_L3A32_MASK)

/* MAC L3 L4 Control 3 (MAC_L3_L4_CONTROL3) */
#define EMAC_MAC_L3_L4_CONTROL3_L3PEN3         (1 << 0) /* Bit 0: Layer 3 Protocol Enable 3 */
#define EMAC_MAC_L3_L4_CONTROL3_L3SAM3         (1 << 2) /* Bit 2: Layer 3 IP SA Match Enable 3 */
#define EMAC_MAC_L3_L4_CONTROL3_L3SAIM3        (1 << 3) /* Bit 3: Layer 3 IP SA Inverse Match Enable 3 */
#define EMAC_MAC_L3_L4_CONTROL3_L3DAM3         (1 << 4) /* Bit 4: Layer 3 IP DA Match Enable 3 */
#define EMAC_MAC_L3_L4_CONTROL3_L3DAIM3        (1 << 5) /* Bit 5: Layer 3 IP DA Inverse Match Enable 3 */
#define EMAC_MAC_L3_L4_CONTROL3_L3HSBM3_SHIFT  (6)      /* Bits 6-11: Layer 3 IP SA Higher Bits Match 3 */
#define EMAC_MAC_L3_L4_CONTROL3_L3HSBM3_MASK   (0x1f << EMAC_MAC_L3_L4_CONTROL3_L3HSBM3_SHIFT)
#define EMAC_MAC_L3_L4_CONTROL3_L3HSBM3(n)     (((n) << EMAC_MAC_L3_L4_CONTROL3_L3HSBM3_SHIFT) & EMAC_MAC_L3_L4_CONTROL3_L3HSBM3_MASK)
#define EMAC_MAC_L3_L4_CONTROL3_L3HDBM3_SHIFT  (11) /* Bits 11-16: Layer 3 IP DA Higher Bits Match 3 */
#define EMAC_MAC_L3_L4_CONTROL3_L3HDBM3_MASK   (0x1f << EMAC_MAC_L3_L4_CONTROL3_L3HDBM3_SHIFT)
#define EMAC_MAC_L3_L4_CONTROL3_L3HDBM3(n)     (((n) << EMAC_MAC_L3_L4_CONTROL3_L3HDBM3_SHIFT) & EMAC_MAC_L3_L4_CONTROL3_L3HDBM3_MASK)
#define EMAC_MAC_L3_L4_CONTROL3_L4PEN3         (1 << 16) /* Bit 16: Layer 4 Protocol Enable 3 */
#define EMAC_MAC_L3_L4_CONTROL3_L4SPM3         (1 << 18) /* Bit 18: Layer 4 Source Port Match Enable 3 */
#define EMAC_MAC_L3_L4_CONTROL3_L4SPIM3        (1 << 19) /* Bit 19: Layer 4 Source Port Inverse Match Enable 3 */
#define EMAC_MAC_L3_L4_CONTROL3_L4DPM3         (1 << 20) /* Bit 20: Layer 4 Destination Port Match Enable 3 */
#define EMAC_MAC_L3_L4_CONTROL3_L4DPIM3        (1 << 21) /* Bit 21: Layer 4 Destination Port Inverse Match Enable 3 */
#define EMAC_MAC_L3_L4_CONTROL3_DMCHN3         (1 << 24) /* Bit 24: DMA Channel Number 2 */
#define EMAC_MAC_L3_L4_CONTROL3_DMCHEN3        (1 << 28) /* Bit 28: DMA Channel Select Enable 3 */

/* MAC Layer 4 Address 3 (MAC_LAYER4_ADDRESS3) */
#define EMAC_MAC_LAYER4_ADDRESS3_L4SP3_SHIFT  (0) /* Bits 0-16: Layer 4 Source Port Number 3 */
#define EMAC_MAC_LAYER4_ADDRESS3_L4SP3_MASK   (0xffff << EMAC_MAC_LAYER4_ADDRESS3_L4SP3_SHIFT)
#define EMAC_MAC_LAYER4_ADDRESS3_L4SP3(n)     (((n) << EMAC_MAC_LAYER4_ADDRESS3_L4SP3_SHIFT) & EMAC_MAC_LAYER4_ADDRESS3_L4SP3_MASK)
#define EMAC_MAC_LAYER4_ADDRESS3_L4DP3_SHIFT  (16) /* Bits 16-32: Layer 4 Destination Port Number 3 */
#define EMAC_MAC_LAYER4_ADDRESS3_L4DP3_MASK   (0xffff << EMAC_MAC_LAYER4_ADDRESS3_L4DP3_SHIFT)
#define EMAC_MAC_LAYER4_ADDRESS3_L4DP3(n)     (((n) << EMAC_MAC_LAYER4_ADDRESS3_L4DP3_SHIFT) & EMAC_MAC_LAYER4_ADDRESS3_L4DP3_MASK)

/* MAC Layer 3 Address 0 Reg 3 (MAC_LAYER3_ADDR0_REG3) */
#define EMAC_MAC_LAYER3_ADDR0_REG3_L3A03_SHIFT  (0) /* Bits 0-32: Layer 3 Address 0 */
#define EMAC_MAC_LAYER3_ADDR0_REG3_L3A03_MASK   (0xffffffff << EMAC_MAC_LAYER3_ADDR0_REG3_L3A03_SHIFT)
#define EMAC_MAC_LAYER3_ADDR0_REG3_L3A03(n)     (((n) << EMAC_MAC_LAYER3_ADDR0_REG3_L3A03_SHIFT) & EMAC_MAC_LAYER3_ADDR0_REG3_L3A03_MASK)

/* MAC Layer 3 Address 1 Reg 3 (MAC_LAYER3_ADDR1_REG3) */
#define EMAC_MAC_LAYER3_ADDR1_REG3_L3A13_SHIFT  (0) /* Bits 0-32: Layer 3 Address 1 */
#define EMAC_MAC_LAYER3_ADDR1_REG3_L3A13_MASK   (0xffffffff << EMAC_MAC_LAYER3_ADDR1_REG3_L3A13_SHIFT)
#define EMAC_MAC_LAYER3_ADDR1_REG3_L3A13(n)     (((n) << EMAC_MAC_LAYER3_ADDR1_REG3_L3A13_SHIFT) & EMAC_MAC_LAYER3_ADDR1_REG3_L3A13_MASK)

/* MAC Layer 3 Address 2 Reg 3 (MAC_LAYER3_ADDR2_REG3) */
#define EMAC_MAC_LAYER3_ADDR2_REG3_L3A23_SHIFT  (0) /* Bits 0-32: Layer 3 Address 2 */
#define EMAC_MAC_LAYER3_ADDR2_REG3_L3A23_MASK   (0xffffffff << EMAC_MAC_LAYER3_ADDR2_REG3_L3A23_SHIFT)
#define EMAC_MAC_LAYER3_ADDR2_REG3_L3A23(n)     (((n) << EMAC_MAC_LAYER3_ADDR2_REG3_L3A23_SHIFT) & EMAC_MAC_LAYER3_ADDR2_REG3_L3A23_MASK)

/* MAC Layer 3 Address 3 Reg 3 (MAC_LAYER3_ADDR3_REG3) */
#define EMAC_MAC_LAYER3_ADDR3_REG3_L3A33_SHIFT  (0) /* Bits 0-32: Layer 3 Address 3 */
#define EMAC_MAC_LAYER3_ADDR3_REG3_L3A33_MASK   (0xffffffff << EMAC_MAC_LAYER3_ADDR3_REG3_L3A33_SHIFT)
#define EMAC_MAC_LAYER3_ADDR3_REG3_L3A33(n)     (((n) << EMAC_MAC_LAYER3_ADDR3_REG3_L3A33_SHIFT) & EMAC_MAC_LAYER3_ADDR3_REG3_L3A33_MASK)

/* MAC Timestamp Control (MAC_TIMESTAMP_CONTROL) */
#define EMAC_MAC_TIMESTAMP_CONTROL_TSENA             (1 << 0)  /* Bit 0: Timestamp Enable */
#define EMAC_MAC_TIMESTAMP_CONTROL_TSCFUPDT          (1 << 1)  /* Bit 1: Fine Or Coarse Timestamp Update */
#define EMAC_MAC_TIMESTAMP_CONTROL_TSINIT            (1 << 2)  /* Bit 2: Initialize Timestamp */
#define EMAC_MAC_TIMESTAMP_CONTROL_TSUPDT            (1 << 3)  /* Bit 3: Update Timestamp */
#define EMAC_MAC_TIMESTAMP_CONTROL_TSADDREG          (1 << 5)  /* Bit 5: Update Addend Register */
#define EMAC_MAC_TIMESTAMP_CONTROL_PTGE              (1 << 6)  /* Bit 6: Presentation Time Generation Enable */
#define EMAC_MAC_TIMESTAMP_CONTROL_TSENALL           (1 << 8)  /* Bit 8: Enable Timestamp For All Packets */
#define EMAC_MAC_TIMESTAMP_CONTROL_TSCTRLSSR         (1 << 9)  /* Bit 9: Timestamp Digital Or Binary Rollover Control */
#define EMAC_MAC_TIMESTAMP_CONTROL_TSVER2ENA         (1 << 10) /* Bit 10: Enable PTP Packet Processing For Version 2 Format */
#define EMAC_MAC_TIMESTAMP_CONTROL_TSIPENA           (1 << 11) /* Bit 11: Enable Processing Of PTP Over Ethernet Packets */
#define EMAC_MAC_TIMESTAMP_CONTROL_TSIPV6ENA         (1 << 12) /* Bit 12: Enable Processing Of PTP Packets Sent Over IPv6-UDP */
#define EMAC_MAC_TIMESTAMP_CONTROL_TSIPV4ENA         (1 << 13) /* Bit 13: Enable Processing Of PTP Packets Sent Over IPv4-UDP */
#define EMAC_MAC_TIMESTAMP_CONTROL_TSEVNTENA         (1 << 14) /* Bit 14: Enable Timestamp Snapshot For Event Messages */
#define EMAC_MAC_TIMESTAMP_CONTROL_TSMSTRENA         (1 << 15) /* Bit 15: Enable Snapshot For Messages Relevant To Master */
#define EMAC_MAC_TIMESTAMP_CONTROL_SNAPTYPSEL_SHIFT  (16)      /* Bits 16-18: Select PTP Packets For Taking Snapshots */
#define EMAC_MAC_TIMESTAMP_CONTROL_SNAPTYPSEL_MASK   (0x3 << EMAC_MAC_TIMESTAMP_CONTROL_SNAPTYPSEL_SHIFT)
#define EMAC_MAC_TIMESTAMP_CONTROL_SNAPTYPSEL(n)     (((n) << EMAC_MAC_TIMESTAMP_CONTROL_SNAPTYPSEL_SHIFT) & EMAC_MAC_TIMESTAMP_CONTROL_SNAPTYPSEL_MASK)
#define EMAC_MAC_TIMESTAMP_CONTROL_TSENMACADDR       (1 << 18) /* Bit 18: Enable MAC Address For PTP Packet Filtering */
#define EMAC_MAC_TIMESTAMP_CONTROL_ESTI              (1 << 20) /* Bit 20: External System Time Input */
#define EMAC_MAC_TIMESTAMP_CONTROL_TXTSSTSM          (1 << 24) /* Bit 24: Transmit Timestamp Status Mode */
#define EMAC_MAC_TIMESTAMP_CONTROL_AV8021ASMEN       (1 << 28) /* Bit 28: AV 802.1AS Mode Enable */

/* MAC Sub Second Increment (MAC_SUB_SECOND_INCREMENT) */
#define EMAC_MAC_SUB_SECOND_INCREMENT_SNSINC_SHIFT  (8) /* Bits 8-16: Sub-Nanosecond Increment Value */
#define EMAC_MAC_SUB_SECOND_INCREMENT_SNSINC_MASK   (0xff << EMAC_MAC_SUB_SECOND_INCREMENT_SNSINC_SHIFT)
#define EMAC_MAC_SUB_SECOND_INCREMENT_SNSINC(n)     (((n) << EMAC_MAC_SUB_SECOND_INCREMENT_SNSINC_SHIFT) & EMAC_MAC_SUB_SECOND_INCREMENT_SNSINC_MASK)
#define EMAC_MAC_SUB_SECOND_INCREMENT_SSINC_SHIFT   (16) /* Bits 16-24: Sub-Second Increment Value */
#define EMAC_MAC_SUB_SECOND_INCREMENT_SSINC_MASK    (0xff << EMAC_MAC_SUB_SECOND_INCREMENT_SSINC_SHIFT)
#define EMAC_MAC_SUB_SECOND_INCREMENT_SSINC(n)      (((n) << EMAC_MAC_SUB_SECOND_INCREMENT_SSINC_SHIFT) & EMAC_MAC_SUB_SECOND_INCREMENT_SSINC_MASK)

/* MAC System Time In Seconds (MAC_SYSTEM_TIME_SECONDS) */
#define EMAC_MAC_SYSTEM_TIME_SECONDS_TSS_SHIFT  (0) /* Bits 0-32: Timestamp Second */
#define EMAC_MAC_SYSTEM_TIME_SECONDS_TSS_MASK   (0xffffffff << EMAC_MAC_SYSTEM_TIME_SECONDS_TSS_SHIFT)
#define EMAC_MAC_SYSTEM_TIME_SECONDS_TSS(n)     (((n) << EMAC_MAC_SYSTEM_TIME_SECONDS_TSS_SHIFT) & EMAC_MAC_SYSTEM_TIME_SECONDS_TSS_MASK)

/* MAC System Time In Nanoseconds (MAC_SYSTEM_TIME_NANOSECONDS) */
#define EMAC_MAC_SYSTEM_TIME_NANOSECONDS_TSSS_SHIFT  (0) /* Bits 0-31: Timestamp Sub Seconds */
#define EMAC_MAC_SYSTEM_TIME_NANOSECONDS_TSSS_MASK   (0x7fffffff << EMAC_MAC_SYSTEM_TIME_NANOSECONDS_TSSS_SHIFT)
#define EMAC_MAC_SYSTEM_TIME_NANOSECONDS_TSSS(n)     (((n) << EMAC_MAC_SYSTEM_TIME_NANOSECONDS_TSSS_SHIFT) & EMAC_MAC_SYSTEM_TIME_NANOSECONDS_TSSS_MASK)

/* MAC System Time Seconds Update (MAC_SYSTEM_TIME_SECONDS_UPDATE) */
#define EMAC_MAC_SYSTEM_TIME_SECONDS_UPDATE_TSS_SHIFT  (0) /* Bits 0-32: Timestamp Seconds */
#define EMAC_MAC_SYSTEM_TIME_SECONDS_UPDATE_TSS_MASK   (0xffffffff << EMAC_MAC_SYSTEM_TIME_SECONDS_UPDATE_TSS_SHIFT)
#define EMAC_MAC_SYSTEM_TIME_SECONDS_UPDATE_TSS(n)     (((n) << EMAC_MAC_SYSTEM_TIME_SECONDS_UPDATE_TSS_SHIFT) & EMAC_MAC_SYSTEM_TIME_SECONDS_UPDATE_TSS_MASK)

/* MAC System Time Nanoseconds Update (MAC_SYSTEM_TIME_NANOSECONDS_UPDATE) */
#define EMAC_MAC_SYSTEM_TIME_NANOSECONDS_UPDATE_TSSS_SHIFT  (0) /* Bits 0-31: Timestamp Subseconds */
#define EMAC_MAC_SYSTEM_TIME_NANOSECONDS_UPDATE_TSSS_MASK   (0x7fffffff << EMAC_MAC_SYSTEM_TIME_NANOSECONDS_UPDATE_TSSS_SHIFT)
#define EMAC_MAC_SYSTEM_TIME_NANOSECONDS_UPDATE_TSSS(n)     (((n) << EMAC_MAC_SYSTEM_TIME_NANOSECONDS_UPDATE_TSSS_SHIFT) & EMAC_MAC_SYSTEM_TIME_NANOSECONDS_UPDATE_TSSS_MASK)
#define EMAC_MAC_SYSTEM_TIME_NANOSECONDS_UPDATE_ADDSUB      (1 << 31) /* Bit 31: Add Or Subtract Time */

/* MAC Timestamp Addend (MAC_TIMESTAMP_ADDEND) */
#define EMAC_MAC_TIMESTAMP_ADDEND_TSAR_SHIFT  (0) /* Bits 0-32: Timestamp Addend Register */
#define EMAC_MAC_TIMESTAMP_ADDEND_TSAR_MASK   (0xffffffff << EMAC_MAC_TIMESTAMP_ADDEND_TSAR_SHIFT)
#define EMAC_MAC_TIMESTAMP_ADDEND_TSAR(n)     (((n) << EMAC_MAC_TIMESTAMP_ADDEND_TSAR_SHIFT) & EMAC_MAC_TIMESTAMP_ADDEND_TSAR_MASK)

/* MAC System Time Higher Word In Seconds
 * (MAC_SYSTEM_TIME_HIGHER_WORD_SECONDS)
 */

#define EMAC_MAC_SYSTEM_TIME_HIGHER_WORD_SECONDS_TSHWR_SHIFT  (0) /* Bits 0-16: Timestamp Higher Word Register */
#define EMAC_MAC_SYSTEM_TIME_HIGHER_WORD_SECONDS_TSHWR_MASK   (0xffff << EMAC_MAC_SYSTEM_TIME_HIGHER_WORD_SECONDS_TSHWR_SHIFT)
#define EMAC_MAC_SYSTEM_TIME_HIGHER_WORD_SECONDS_TSHWR(n)     (((n) << EMAC_MAC_SYSTEM_TIME_HIGHER_WORD_SECONDS_TSHWR_SHIFT) & EMAC_MAC_SYSTEM_TIME_HIGHER_WORD_SECONDS_TSHWR_MASK)

/* MAC Timestamp Status (MAC_TIMESTAMP_STATUS) */
#define EMAC_MAC_TIMESTAMP_STATUS_TSSOVF      (1 << 0)  /* Bit 0: Timestamp Seconds Overflow */
#define EMAC_MAC_TIMESTAMP_STATUS_TSTARGT0    (1 << 1)  /* Bit 1: Timestamp Target Time Reached */
#define EMAC_MAC_TIMESTAMP_STATUS_TSTRGTERR0  (1 << 3)  /* Bit 3: Timestamp Target Time Error */
#define EMAC_MAC_TIMESTAMP_STATUS_TSTARGT1    (1 << 4)  /* Bit 4: Timestamp Target Time Reached For Target Time PPS1 */
#define EMAC_MAC_TIMESTAMP_STATUS_TSTRGTERR1  (1 << 5)  /* Bit 5: Timestamp Target Time Error */
#define EMAC_MAC_TIMESTAMP_STATUS_TSTARGT2    (1 << 6)  /* Bit 6: Timestamp Target Time Reached For Target Time PPS2 */
#define EMAC_MAC_TIMESTAMP_STATUS_TSTRGTERR2  (1 << 7)  /* Bit 7: Timestamp Target Time Error */
#define EMAC_MAC_TIMESTAMP_STATUS_TSTARGT3    (1 << 8)  /* Bit 8: Timestamp Target Time Reached For Target Time PPS3 */
#define EMAC_MAC_TIMESTAMP_STATUS_TSTRGTERR3  (1 << 9)  /* Bit 9: Timestamp Target Time Error */
#define EMAC_MAC_TIMESTAMP_STATUS_TXTSSIS     (1 << 15) /* Bit 15: Transmit Timestamp Status Interrupt Status */

/* MAC Transmit Timestamp Status In Nanoseconds
 * (MAC_TX_TIMESTAMP_STATUS_NANOSECONDS)
 */

#define EMAC_MAC_TX_TIMESTAMP_STATUS_NANOSECONDS_TXTSSLO_SHIFT  (0) /* Bits 0-31: Transmit Timestamp Status Low */
#define EMAC_MAC_TX_TIMESTAMP_STATUS_NANOSECONDS_TXTSSLO_MASK   (0x7fffffff << EMAC_MAC_TX_TIMESTAMP_STATUS_NANOSECONDS_TXTSSLO_SHIFT)
#define EMAC_MAC_TX_TIMESTAMP_STATUS_NANOSECONDS_TXTSSLO(n)     (((n) << EMAC_MAC_TX_TIMESTAMP_STATUS_NANOSECONDS_TXTSSLO_SHIFT) & EMAC_MAC_TX_TIMESTAMP_STATUS_NANOSECONDS_TXTSSLO_MASK)
#define EMAC_MAC_TX_TIMESTAMP_STATUS_NANOSECONDS_TXTSSMIS       (1 << 31) /* Bit 31: Transmit Timestamp Status Missed */

/* MAC Transmit Timestamp Status In Seconds
 * (MAC_TX_TIMESTAMP_STATUS_SECONDS)
 */

#define EMAC_MAC_TX_TIMESTAMP_STATUS_SECONDS_TXTSSHI_SHIFT  (0) /* Bits 0-32: Transmit Timestamp Status High */
#define EMAC_MAC_TX_TIMESTAMP_STATUS_SECONDS_TXTSSHI_MASK   (0xffffffff << EMAC_MAC_TX_TIMESTAMP_STATUS_SECONDS_TXTSSHI_SHIFT)
#define EMAC_MAC_TX_TIMESTAMP_STATUS_SECONDS_TXTSSHI(n)     (((n) << EMAC_MAC_TX_TIMESTAMP_STATUS_SECONDS_TXTSSHI_SHIFT) & EMAC_MAC_TX_TIMESTAMP_STATUS_SECONDS_TXTSSHI_MASK)

/* MAC Timestamp Ingress Asymmetry Correction
 * (MAC_TIMESTAMP_INGRESS_ASYM_CORR)
 */

#define EMAC_MAC_TIMESTAMP_INGRESS_ASYM_CORR_OSTIAC_SHIFT  (0) /* Bits 0-32: One-Step Timestamp Ingress Asymmetry Correction */
#define EMAC_MAC_TIMESTAMP_INGRESS_ASYM_CORR_OSTIAC_MASK   (0xffffffff << EMAC_MAC_TIMESTAMP_INGRESS_ASYM_CORR_OSTIAC_SHIFT)
#define EMAC_MAC_TIMESTAMP_INGRESS_ASYM_CORR_OSTIAC(n)     (((n) << EMAC_MAC_TIMESTAMP_INGRESS_ASYM_CORR_OSTIAC_SHIFT) & EMAC_MAC_TIMESTAMP_INGRESS_ASYM_CORR_OSTIAC_MASK)

/* MAC Timestamp Egress Asymmetry Correction
 * (MAC_TIMESTAMP_EGRESS_ASYM_CORR)
 */

#define EMAC_MAC_TIMESTAMP_EGRESS_ASYM_CORR_OSTEAC_SHIFT  (0) /* Bits 0-32: One-Step Timestamp Egress Asymmetry Correction */
#define EMAC_MAC_TIMESTAMP_EGRESS_ASYM_CORR_OSTEAC_MASK   (0xffffffff << EMAC_MAC_TIMESTAMP_EGRESS_ASYM_CORR_OSTEAC_SHIFT)
#define EMAC_MAC_TIMESTAMP_EGRESS_ASYM_CORR_OSTEAC(n)     (((n) << EMAC_MAC_TIMESTAMP_EGRESS_ASYM_CORR_OSTEAC_SHIFT) & EMAC_MAC_TIMESTAMP_EGRESS_ASYM_CORR_OSTEAC_MASK)

/* MAC Timestamp Ingress Correction In Nanoseconds
 * (MAC_TIMESTAMP_INGRESS_CORR_NANOSECOND)
 */

#define EMAC_MAC_TIMESTAMP_INGRESS_CORR_NANOSECOND_TSIC_SHIFT  (0) /* Bits 0-32: Timestamp Ingress Correction */
#define EMAC_MAC_TIMESTAMP_INGRESS_CORR_NANOSECOND_TSIC_MASK   (0xffffffff << EMAC_MAC_TIMESTAMP_INGRESS_CORR_NANOSECOND_TSIC_SHIFT)
#define EMAC_MAC_TIMESTAMP_INGRESS_CORR_NANOSECOND_TSIC(n)     (((n) << EMAC_MAC_TIMESTAMP_INGRESS_CORR_NANOSECOND_TSIC_SHIFT) & EMAC_MAC_TIMESTAMP_INGRESS_CORR_NANOSECOND_TSIC_MASK)

/* MAC Timestamp Egress Correction In Nanoseconds
 * (MAC_TIMESTAMP_EGRESS_CORR_NANOSECOND)
 */

#define EMAC_MAC_TIMESTAMP_EGRESS_CORR_NANOSECOND_TSEC_SHIFT  (0) /* Bits 0-32: Timestamp Egress Correction */
#define EMAC_MAC_TIMESTAMP_EGRESS_CORR_NANOSECOND_TSEC_MASK   (0xffffffff << EMAC_MAC_TIMESTAMP_EGRESS_CORR_NANOSECOND_TSEC_SHIFT)
#define EMAC_MAC_TIMESTAMP_EGRESS_CORR_NANOSECOND_TSEC(n)     (((n) << EMAC_MAC_TIMESTAMP_EGRESS_CORR_NANOSECOND_TSEC_SHIFT) & EMAC_MAC_TIMESTAMP_EGRESS_CORR_NANOSECOND_TSEC_MASK)

/* MAC Timestamp Ingress Correction In Subnanoseconds
 * (MAC_TIMESTAMP_INGRESS_CORR_SUBNANOSEC)
 */

#define EMAC_MAC_TIMESTAMP_INGRESS_CORR_SUBNANOSEC_TSICSNS_SHIFT  (8) /* Bits 8-16: Timestamp Ingress Correction In Sub-Nanoseconds */
#define EMAC_MAC_TIMESTAMP_INGRESS_CORR_SUBNANOSEC_TSICSNS_MASK   (0xff << EMAC_MAC_TIMESTAMP_INGRESS_CORR_SUBNANOSEC_TSICSNS_SHIFT)
#define EMAC_MAC_TIMESTAMP_INGRESS_CORR_SUBNANOSEC_TSICSNS(n)     (((n) << EMAC_MAC_TIMESTAMP_INGRESS_CORR_SUBNANOSEC_TSICSNS_SHIFT) & EMAC_MAC_TIMESTAMP_INGRESS_CORR_SUBNANOSEC_TSICSNS_MASK)

/* MAC Timestamp Engress Correction In Subnanoseconds
 * (MAC_TIMESTAMP_EGRESS_CORR_SUBNANOSEC)
 */

#define EMAC_MAC_TIMESTAMP_EGRESS_CORR_SUBNANOSEC_TSECSNS_SHIFT  (8) /* Bits 8-16: Timestamp Egress Correction In Sub-Nanoseconds */
#define EMAC_MAC_TIMESTAMP_EGRESS_CORR_SUBNANOSEC_TSECSNS_MASK   (0xff << EMAC_MAC_TIMESTAMP_EGRESS_CORR_SUBNANOSEC_TSECSNS_SHIFT)
#define EMAC_MAC_TIMESTAMP_EGRESS_CORR_SUBNANOSEC_TSECSNS(n)     (((n) << EMAC_MAC_TIMESTAMP_EGRESS_CORR_SUBNANOSEC_TSECSNS_SHIFT) & EMAC_MAC_TIMESTAMP_EGRESS_CORR_SUBNANOSEC_TSECSNS_MASK)

/* MAC Timestamp Ingress Latency (MAC_TIMESTAMP_INGRESS_LATENCY) */
#define EMAC_MAC_TIMESTAMP_INGRESS_LATENCY_ITLSNS_SHIFT  (8) /* Bits 8-16: Ingress Timestamp Latency In Nanoseconds */
#define EMAC_MAC_TIMESTAMP_INGRESS_LATENCY_ITLSNS_MASK   (0xff << EMAC_MAC_TIMESTAMP_INGRESS_LATENCY_ITLSNS_SHIFT)
#define EMAC_MAC_TIMESTAMP_INGRESS_LATENCY_ITLSNS(n)     (((n) << EMAC_MAC_TIMESTAMP_INGRESS_LATENCY_ITLSNS_SHIFT) & EMAC_MAC_TIMESTAMP_INGRESS_LATENCY_ITLSNS_MASK)
#define EMAC_MAC_TIMESTAMP_INGRESS_LATENCY_ITLNS_SHIFT   (16) /* Bits 16-28: Ingress Timestamp Latency In Sub-Nanoseconds */
#define EMAC_MAC_TIMESTAMP_INGRESS_LATENCY_ITLNS_MASK    (0xfff << EMAC_MAC_TIMESTAMP_INGRESS_LATENCY_ITLNS_SHIFT)
#define EMAC_MAC_TIMESTAMP_INGRESS_LATENCY_ITLNS(n)      (((n) << EMAC_MAC_TIMESTAMP_INGRESS_LATENCY_ITLNS_SHIFT) & EMAC_MAC_TIMESTAMP_INGRESS_LATENCY_ITLNS_MASK)

/* MAC Timestamp Egress Latecy (MAC_TIMESTAMP_EGRESS_LATENCY) */
#define EMAC_MAC_TIMESTAMP_EGRESS_LATENCY_ETLSNS_SHIFT  (8) /* Bits 8-16: Egress Timestamp Latency In Sub-Nanoseconds */
#define EMAC_MAC_TIMESTAMP_EGRESS_LATENCY_ETLSNS_MASK   (0xff << EMAC_MAC_TIMESTAMP_EGRESS_LATENCY_ETLSNS_SHIFT)
#define EMAC_MAC_TIMESTAMP_EGRESS_LATENCY_ETLSNS(n)     (((n) << EMAC_MAC_TIMESTAMP_EGRESS_LATENCY_ETLSNS_SHIFT) & EMAC_MAC_TIMESTAMP_EGRESS_LATENCY_ETLSNS_MASK)
#define EMAC_MAC_TIMESTAMP_EGRESS_LATENCY_ETLNS_SHIFT   (16) /* Bits 16-28: Egress Timestamp Latency In Nanoseconds */
#define EMAC_MAC_TIMESTAMP_EGRESS_LATENCY_ETLNS_MASK    (0xfff << EMAC_MAC_TIMESTAMP_EGRESS_LATENCY_ETLNS_SHIFT)
#define EMAC_MAC_TIMESTAMP_EGRESS_LATENCY_ETLNS(n)      (((n) << EMAC_MAC_TIMESTAMP_EGRESS_LATENCY_ETLNS_SHIFT) & EMAC_MAC_TIMESTAMP_EGRESS_LATENCY_ETLNS_MASK)

/* MAC PPS Control (MAC_PPS_CONTROL) */
#define EMAC_MAC_PPS_CONTROL_PPSCTRL_PPSCMD_SHIFT  (0) /* Bits 0-4: PPS Output Frequency Control */
#define EMAC_MAC_PPS_CONTROL_PPSCTRL_PPSCMD_MASK   (0xf << EMAC_MAC_PPS_CONTROL_PPSCTRL_PPSCMD_SHIFT)
#define EMAC_MAC_PPS_CONTROL_PPSCTRL_PPSCMD(n)     (((n) << EMAC_MAC_PPS_CONTROL_PPSCTRL_PPSCMD_SHIFT) & EMAC_MAC_PPS_CONTROL_PPSCTRL_PPSCMD_MASK)
#define EMAC_MAC_PPS_CONTROL_PPSEN0                (1 << 4) /* Bit 4: Flexible PPS Output Mode Enable 0 */
#define EMAC_MAC_PPS_CONTROL_TRGTMODSEL0_SHIFT     (5)      /* Bits 5-7: Target Time Register Mode For PPS0 Output */
#define EMAC_MAC_PPS_CONTROL_TRGTMODSEL0_MASK      (0x3 << EMAC_MAC_PPS_CONTROL_TRGTMODSEL0_SHIFT)
#define EMAC_MAC_PPS_CONTROL_TRGTMODSEL0(n)        (((n) << EMAC_MAC_PPS_CONTROL_TRGTMODSEL0_SHIFT) & EMAC_MAC_PPS_CONTROL_TRGTMODSEL0_MASK)
#define EMAC_MAC_PPS_CONTROL_MCGREN0               (1 << 7) /* Bit 7: MCGR Mode Enable For PPS0 Output */
#define EMAC_MAC_PPS_CONTROL_PPSCMD1_SHIFT         (8)      /* Bits 8-12: Flexible PPS1 Output Control */
#define EMAC_MAC_PPS_CONTROL_PPSCMD1_MASK          (0xf << EMAC_MAC_PPS_CONTROL_PPSCMD1_SHIFT)
#define EMAC_MAC_PPS_CONTROL_PPSCMD1(n)            (((n) << EMAC_MAC_PPS_CONTROL_PPSCMD1_SHIFT) & EMAC_MAC_PPS_CONTROL_PPSCMD1_MASK)
#define EMAC_MAC_PPS_CONTROL_TRGTMODSEL1_SHIFT     (13) /* Bits 13-15: Target Time Register Mode For PPS1 Output */
#define EMAC_MAC_PPS_CONTROL_TRGTMODSEL1_MASK      (0x3 << EMAC_MAC_PPS_CONTROL_TRGTMODSEL1_SHIFT)
#define EMAC_MAC_PPS_CONTROL_TRGTMODSEL1(n)        (((n) << EMAC_MAC_PPS_CONTROL_TRGTMODSEL1_SHIFT) & EMAC_MAC_PPS_CONTROL_TRGTMODSEL1_MASK)
#define EMAC_MAC_PPS_CONTROL_MCGREN1               (1 << 15) /* Bit 15: MCGR Mode Enable For PPS1 Output */
#define EMAC_MAC_PPS_CONTROL_PPSCMD2_SHIFT         (16)      /* Bits 16-20: Flexible PPS2 Output Control */
#define EMAC_MAC_PPS_CONTROL_PPSCMD2_MASK          (0xf << EMAC_MAC_PPS_CONTROL_PPSCMD2_SHIFT)
#define EMAC_MAC_PPS_CONTROL_PPSCMD2(n)            (((n) << EMAC_MAC_PPS_CONTROL_PPSCMD2_SHIFT) & EMAC_MAC_PPS_CONTROL_PPSCMD2_MASK)
#define EMAC_MAC_PPS_CONTROL_TRGTMODSEL2_SHIFT     (21) /* Bits 21-23: Target Time Register Mode For PPS2 Output */
#define EMAC_MAC_PPS_CONTROL_TRGTMODSEL2_MASK      (0x3 << EMAC_MAC_PPS_CONTROL_TRGTMODSEL2_SHIFT)
#define EMAC_MAC_PPS_CONTROL_TRGTMODSEL2(n)        (((n) << EMAC_MAC_PPS_CONTROL_TRGTMODSEL2_SHIFT) & EMAC_MAC_PPS_CONTROL_TRGTMODSEL2_MASK)
#define EMAC_MAC_PPS_CONTROL_MCGREN2               (1 << 23) /* Bit 23: MCGR Mode Enable For PPS2 Output */
#define EMAC_MAC_PPS_CONTROL_PPSCMD3_SHIFT         (24)      /* Bits 24-28: Flexible PPS3 Output Control */
#define EMAC_MAC_PPS_CONTROL_PPSCMD3_MASK          (0xf << EMAC_MAC_PPS_CONTROL_PPSCMD3_SHIFT)
#define EMAC_MAC_PPS_CONTROL_PPSCMD3(n)            (((n) << EMAC_MAC_PPS_CONTROL_PPSCMD3_SHIFT) & EMAC_MAC_PPS_CONTROL_PPSCMD3_MASK)
#define EMAC_MAC_PPS_CONTROL_TRGTMODSEL3_SHIFT     (29) /* Bits 29-31: Target Time Register Mode For PPS3 Output */
#define EMAC_MAC_PPS_CONTROL_TRGTMODSEL3_MASK      (0x3 << EMAC_MAC_PPS_CONTROL_TRGTMODSEL3_SHIFT)
#define EMAC_MAC_PPS_CONTROL_TRGTMODSEL3(n)        (((n) << EMAC_MAC_PPS_CONTROL_TRGTMODSEL3_SHIFT) & EMAC_MAC_PPS_CONTROL_TRGTMODSEL3_MASK)
#define EMAC_MAC_PPS_CONTROL_MCGREN3               (1 << 31) /* Bit 31: MCGR Mode Enable For PPS3 Output */

/* MAC PPS0 Target Time In Seconds (MAC_PPS0_TARGET_TIME_SECONDS) */
#define EMAC_MAC_PPS0_TARGET_TIME_SECONDS_TSTRH0_SHIFT  (0) /* Bits 0-32: PPS Target Time In Seconds Register */
#define EMAC_MAC_PPS0_TARGET_TIME_SECONDS_TSTRH0_MASK   (0xffffffff << EMAC_MAC_PPS0_TARGET_TIME_SECONDS_TSTRH0_SHIFT)
#define EMAC_MAC_PPS0_TARGET_TIME_SECONDS_TSTRH0(n)     (((n) << EMAC_MAC_PPS0_TARGET_TIME_SECONDS_TSTRH0_SHIFT) & EMAC_MAC_PPS0_TARGET_TIME_SECONDS_TSTRH0_MASK)

/* MAC PPS0 Target Time In Nanoseconds (MAC_PPS0_TARGET_TIME_NANOSECONDS) */
#define EMAC_MAC_PPS0_TARGET_TIME_NANOSECONDS_TTSL0_SHIFT  (0) /* Bits 0-31: Target Time Low For PPS0 */
#define EMAC_MAC_PPS0_TARGET_TIME_NANOSECONDS_TTSL0_MASK   (0x7fffffff << EMAC_MAC_PPS0_TARGET_TIME_NANOSECONDS_TTSL0_SHIFT)
#define EMAC_MAC_PPS0_TARGET_TIME_NANOSECONDS_TTSL0(n)     (((n) << EMAC_MAC_PPS0_TARGET_TIME_NANOSECONDS_TTSL0_SHIFT) & EMAC_MAC_PPS0_TARGET_TIME_NANOSECONDS_TTSL0_MASK)
#define EMAC_MAC_PPS0_TARGET_TIME_NANOSECONDS_TRGTBUSY0    (1 << 31) /* Bit 31: PPS Target Time Busy Status 0 */

/* MAC PPS0 Interval (MAC_PPS0_INTERVAL) */
#define EMAC_MAC_PPS0_INTERVAL_PPSINT0_SHIFT  (0) /* Bits 0-32: PPS Output Signal Interval 0 */
#define EMAC_MAC_PPS0_INTERVAL_PPSINT0_MASK   (0xffffffff << EMAC_MAC_PPS0_INTERVAL_PPSINT0_SHIFT)
#define EMAC_MAC_PPS0_INTERVAL_PPSINT0(n)     (((n) << EMAC_MAC_PPS0_INTERVAL_PPSINT0_SHIFT) & EMAC_MAC_PPS0_INTERVAL_PPSINT0_MASK)

/* MAC PPS0 Width (MAC_PPS0_WIDTH) */
#define EMAC_MAC_PPS0_WIDTH_PPSWIDTH0_SHIFT  (0) /* Bits 0-32: PPS Output Signal Width 0 */
#define EMAC_MAC_PPS0_WIDTH_PPSWIDTH0_MASK   (0xffffffff << EMAC_MAC_PPS0_WIDTH_PPSWIDTH0_SHIFT)
#define EMAC_MAC_PPS0_WIDTH_PPSWIDTH0(n)     (((n) << EMAC_MAC_PPS0_WIDTH_PPSWIDTH0_SHIFT) & EMAC_MAC_PPS0_WIDTH_PPSWIDTH0_MASK)

/* MAC PPS1 Target Time In Seconds (MAC_PPS1_TARGET_TIME_SECONDS) */
#define EMAC_MAC_PPS1_TARGET_TIME_SECONDS_TSTRH1_SHIFT  (0) /* Bits 0-32: PPS Target Time In Seconds 1 */
#define EMAC_MAC_PPS1_TARGET_TIME_SECONDS_TSTRH1_MASK   (0xffffffff << EMAC_MAC_PPS1_TARGET_TIME_SECONDS_TSTRH1_SHIFT)
#define EMAC_MAC_PPS1_TARGET_TIME_SECONDS_TSTRH1(n)     (((n) << EMAC_MAC_PPS1_TARGET_TIME_SECONDS_TSTRH1_SHIFT) & EMAC_MAC_PPS1_TARGET_TIME_SECONDS_TSTRH1_MASK)

/* MAC PPS1 Target Time In Nanoseconds (MAC_PPS1_TARGET_TIME_NANOSECONDS) */
#define EMAC_MAC_PPS1_TARGET_TIME_NANOSECONDS_TTSL1_SHIFT  (0) /* Bits 0-31: Target Time Low For PPS1 */
#define EMAC_MAC_PPS1_TARGET_TIME_NANOSECONDS_TTSL1_MASK   (0x7fffffff << EMAC_MAC_PPS1_TARGET_TIME_NANOSECONDS_TTSL1_SHIFT)
#define EMAC_MAC_PPS1_TARGET_TIME_NANOSECONDS_TTSL1(n)     (((n) << EMAC_MAC_PPS1_TARGET_TIME_NANOSECONDS_TTSL1_SHIFT) & EMAC_MAC_PPS1_TARGET_TIME_NANOSECONDS_TTSL1_MASK)
#define EMAC_MAC_PPS1_TARGET_TIME_NANOSECONDS_TRGTBUSY1    (1 << 31) /* Bit 31: PPS Target Time Busy Status 1 */

/* MAC PPS1 Interval (MAC_PPS1_INTERVAL) */
#define EMAC_MAC_PPS1_INTERVAL_PPSINT1_SHIFT  (0) /* Bits 0-32: PPS Output Signal Interval 1 */
#define EMAC_MAC_PPS1_INTERVAL_PPSINT1_MASK   (0xffffffff << EMAC_MAC_PPS1_INTERVAL_PPSINT1_SHIFT)
#define EMAC_MAC_PPS1_INTERVAL_PPSINT1(n)     (((n) << EMAC_MAC_PPS1_INTERVAL_PPSINT1_SHIFT) & EMAC_MAC_PPS1_INTERVAL_PPSINT1_MASK)

/* MAC PPS1 Width (MAC_PPS1_WIDTH) */
#define EMAC_MAC_PPS1_WIDTH_PPSWIDTH1_SHIFT  (0) /* Bits 0-32: PPS Output Signal Width 1 */
#define EMAC_MAC_PPS1_WIDTH_PPSWIDTH1_MASK   (0xffffffff << EMAC_MAC_PPS1_WIDTH_PPSWIDTH1_SHIFT)
#define EMAC_MAC_PPS1_WIDTH_PPSWIDTH1(n)     (((n) << EMAC_MAC_PPS1_WIDTH_PPSWIDTH1_SHIFT) & EMAC_MAC_PPS1_WIDTH_PPSWIDTH1_MASK)

/* MAC PPS2 Taget Time In Seconds (MAC_PPS2_TARGET_TIME_SECONDS) */
#define EMAC_MAC_PPS2_TARGET_TIME_SECONDS_TSTRH2_SHIFT  (0) /* Bits 0-32: PPS Target Time In Seconds 2 */
#define EMAC_MAC_PPS2_TARGET_TIME_SECONDS_TSTRH2_MASK   (0xffffffff << EMAC_MAC_PPS2_TARGET_TIME_SECONDS_TSTRH2_SHIFT)
#define EMAC_MAC_PPS2_TARGET_TIME_SECONDS_TSTRH2(n)     (((n) << EMAC_MAC_PPS2_TARGET_TIME_SECONDS_TSTRH2_SHIFT) & EMAC_MAC_PPS2_TARGET_TIME_SECONDS_TSTRH2_MASK)

/* MAC PPS2 Target Time In Nanoseconds (MAC_PPS2_TARGET_TIME_NANOSECONDS) */
#define EMAC_MAC_PPS2_TARGET_TIME_NANOSECONDS_TTSL2_SHIFT  (0) /* Bits 0-31: Target Time Low For PPS2 */
#define EMAC_MAC_PPS2_TARGET_TIME_NANOSECONDS_TTSL2_MASK   (0x7fffffff << EMAC_MAC_PPS2_TARGET_TIME_NANOSECONDS_TTSL2_SHIFT)
#define EMAC_MAC_PPS2_TARGET_TIME_NANOSECONDS_TTSL2(n)     (((n) << EMAC_MAC_PPS2_TARGET_TIME_NANOSECONDS_TTSL2_SHIFT) & EMAC_MAC_PPS2_TARGET_TIME_NANOSECONDS_TTSL2_MASK)
#define EMAC_MAC_PPS2_TARGET_TIME_NANOSECONDS_TRGTBUSY2    (1 << 31) /* Bit 31: PPS Target Time Busy Status 2 */

/* MAC PPS2 Interval (MAC_PPS2_INTERVAL) */
#define EMAC_MAC_PPS2_INTERVAL_PPSINT2_SHIFT  (0) /* Bits 0-32: PPS Output Signal Interval 2 */
#define EMAC_MAC_PPS2_INTERVAL_PPSINT2_MASK   (0xffffffff << EMAC_MAC_PPS2_INTERVAL_PPSINT2_SHIFT)
#define EMAC_MAC_PPS2_INTERVAL_PPSINT2(n)     (((n) << EMAC_MAC_PPS2_INTERVAL_PPSINT2_SHIFT) & EMAC_MAC_PPS2_INTERVAL_PPSINT2_MASK)

/* MAC PPS2 Width (MAC_PPS2_WIDTH) */
#define EMAC_MAC_PPS2_WIDTH_PPSWIDTH2_SHIFT  (0) /* Bits 0-32: PPS Output Signal Width 2 */
#define EMAC_MAC_PPS2_WIDTH_PPSWIDTH2_MASK   (0xffffffff << EMAC_MAC_PPS2_WIDTH_PPSWIDTH2_SHIFT)
#define EMAC_MAC_PPS2_WIDTH_PPSWIDTH2(n)     (((n) << EMAC_MAC_PPS2_WIDTH_PPSWIDTH2_SHIFT) & EMAC_MAC_PPS2_WIDTH_PPSWIDTH2_MASK)

/* MAC PPS3 Target Time In Seconds (MAC_PPS3_TARGET_TIME_SECONDS) */
#define EMAC_MAC_PPS3_TARGET_TIME_SECONDS_TSTRH3_SHIFT  (0) /* Bits 0-32: PPS Target Time In Seconds 3 */
#define EMAC_MAC_PPS3_TARGET_TIME_SECONDS_TSTRH3_MASK   (0xffffffff << EMAC_MAC_PPS3_TARGET_TIME_SECONDS_TSTRH3_SHIFT)
#define EMAC_MAC_PPS3_TARGET_TIME_SECONDS_TSTRH3(n)     (((n) << EMAC_MAC_PPS3_TARGET_TIME_SECONDS_TSTRH3_SHIFT) & EMAC_MAC_PPS3_TARGET_TIME_SECONDS_TSTRH3_MASK)

/* MAC PPS3 Target Time In Nanoseconds (MAC_PPS3_TARGET_TIME_NANOSECONDS) */
#define EMAC_MAC_PPS3_TARGET_TIME_NANOSECONDS_TTSL3_SHIFT  (0) /* Bits 0-31: Target Time Low For PPS3 */
#define EMAC_MAC_PPS3_TARGET_TIME_NANOSECONDS_TTSL3_MASK   (0x7fffffff << EMAC_MAC_PPS3_TARGET_TIME_NANOSECONDS_TTSL3_SHIFT)
#define EMAC_MAC_PPS3_TARGET_TIME_NANOSECONDS_TTSL3(n)     (((n) << EMAC_MAC_PPS3_TARGET_TIME_NANOSECONDS_TTSL3_SHIFT) & EMAC_MAC_PPS3_TARGET_TIME_NANOSECONDS_TTSL3_MASK)
#define EMAC_MAC_PPS3_TARGET_TIME_NANOSECONDS_TRGTBUSY3    (1 << 31) /* Bit 31: PPS Target Time Register Busy 3 */

/* MAC PPS3 Interval (MAC_PPS3_INTERVAL) */
#define EMAC_MAC_PPS3_INTERVAL_PPSINT3_SHIFT  (0) /* Bits 0-32: PPS Output Signal Interval */
#define EMAC_MAC_PPS3_INTERVAL_PPSINT3_MASK   (0xffffffff << EMAC_MAC_PPS3_INTERVAL_PPSINT3_SHIFT)
#define EMAC_MAC_PPS3_INTERVAL_PPSINT3(n)     (((n) << EMAC_MAC_PPS3_INTERVAL_PPSINT3_SHIFT) & EMAC_MAC_PPS3_INTERVAL_PPSINT3_MASK)

/* MAC PPS3 Width (MAC_PPS3_WIDTH) */
#define EMAC_MAC_PPS3_WIDTH_PPSWIDTH3_SHIFT  (0) /* Bits 0-32: PPS Output Signal Width 3 */
#define EMAC_MAC_PPS3_WIDTH_PPSWIDTH3_MASK   (0xffffffff << EMAC_MAC_PPS3_WIDTH_PPSWIDTH3_SHIFT)
#define EMAC_MAC_PPS3_WIDTH_PPSWIDTH3(n)     (((n) << EMAC_MAC_PPS3_WIDTH_PPSWIDTH3_SHIFT) & EMAC_MAC_PPS3_WIDTH_PPSWIDTH3_MASK)

/* MTL Operation Mode (MTL_OPERATION_MODE) */
#define EMAC_MTL_OPERATION_MODE_DTXSTS        (1 << 1) /* Bit 1: Drop Transmit Status */
#define EMAC_MTL_OPERATION_MODE_RAA           (1 << 2) /* Bit 2: Receive Arbitration Algorithm */
#define EMAC_MTL_OPERATION_MODE_SCHALG_SHIFT  (5)      /* Bits 5-7: Transmit Scheduling Algorithm */
#define EMAC_MTL_OPERATION_MODE_SCHALG_MASK   (0x3 << EMAC_MTL_OPERATION_MODE_SCHALG_SHIFT)
#define EMAC_MTL_OPERATION_MODE_SCHALG(n)     (((n) << EMAC_MTL_OPERATION_MODE_SCHALG_SHIFT) & EMAC_MTL_OPERATION_MODE_SCHALG_MASK)
#define EMAC_MTL_OPERATION_MODE_SCHALG_WRR    EMAC_MTL_OPERATION_MODE_SCHALG(0x0)
#define EMAC_MTL_OPERATION_MODE_SCHALG_WFQ    EMAC_MTL_OPERATION_MODE_SCHALG(0x1)
#define EMAC_MTL_OPERATION_MODE_SCHALG_DWRR   EMAC_MTL_OPERATION_MODE_SCHALG(0x2)
#define EMAC_MTL_OPERATION_MODE_SCHALG_SP     EMAC_MTL_OPERATION_MODE_SCHALG(0x3)
#define EMAC_MTL_OPERATION_MODE_CNTPRST       (1 << 8)  /* Bit 8: Counters Preset */
#define EMAC_MTL_OPERATION_MODE_CNTCLR        (1 << 9)  /* Bit 9: Counters Reset */
#define EMAC_MTL_OPERATION_MODE_FRPE          (1 << 15) /* Bit 15: Flexible Receive Parser Enable */

/* MTL Debug Control (MTL_DBG_CTL) */
#define EMAC_MTL_DBG_CTL_FDBGEN          (1 << 0) /* Bit 0: FIFO Debug Access Enable */
#define EMAC_MTL_DBG_CTL_DBGMOD          (1 << 1) /* Bit 1: Debug Mode Access to FIFO */
#define EMAC_MTL_DBG_CTL_BYTEEN_SHIFT    (2)      /* Bits 2-4: Byte Enables */
#define EMAC_MTL_DBG_CTL_BYTEEN_MASK     (0x3 << EMAC_MTL_DBG_CTL_BYTEEN_SHIFT)
#define EMAC_MTL_DBG_CTL_BYTEEN(n)       (((n) << EMAC_MTL_DBG_CTL_BYTEEN_SHIFT) & EMAC_MTL_DBG_CTL_BYTEEN_MASK)
#define EMAC_MTL_DBG_CTL_PKTSTATE_SHIFT  (5) /* Bits 5-7: Encoded Packet State */
#define EMAC_MTL_DBG_CTL_PKTSTATE_MASK   (0x3 << EMAC_MTL_DBG_CTL_PKTSTATE_SHIFT)
#define EMAC_MTL_DBG_CTL_PKTSTATE(n)     (((n) << EMAC_MTL_DBG_CTL_PKTSTATE_SHIFT) & EMAC_MTL_DBG_CTL_PKTSTATE_MASK)
#define EMAC_MTL_DBG_CTL_RSTALL          (1 << 8)  /* Bit 8: Reset All Pointers */
#define EMAC_MTL_DBG_CTL_RSTSEL          (1 << 9)  /* Bit 9: Reset Pointers Of Selected FIFO */
#define EMAC_MTL_DBG_CTL_FIFORDEN        (1 << 10) /* Bit 10: FIFO Read Enable */
#define EMAC_MTL_DBG_CTL_FIFOWREN        (1 << 11) /* Bit 11: FIFO Write Enable */
#define EMAC_MTL_DBG_CTL_FIFOSEL_SHIFT   (12)      /* Bits 12-14: FIFO Selected for Access */
#define EMAC_MTL_DBG_CTL_FIFOSEL_MASK    (0x3 << EMAC_MTL_DBG_CTL_FIFOSEL_SHIFT)
#define EMAC_MTL_DBG_CTL_FIFOSEL(n)      (((n) << EMAC_MTL_DBG_CTL_FIFOSEL_SHIFT) & EMAC_MTL_DBG_CTL_FIFOSEL_MASK)
#define EMAC_MTL_DBG_CTL_PKTIE           (1 << 14) /* Bit 14: Receive Packet Available Interrupt Status Enable */
#define EMAC_MTL_DBG_CTL_STSIE           (1 << 15) /* Bit 15: Transmit Status Available Interrupt Status Enable */
#define EMAC_MTL_DBG_CTL_EIEE            (1 << 16) /* Bit 16: ECC Inject Error Enable */
#define EMAC_MTL_DBG_CTL_EIEC_SHIFT      (17)      /* Bits 17-19: ECC Inject Error Control */
#define EMAC_MTL_DBG_CTL_EIEC_MASK       (0x3 << EMAC_MTL_DBG_CTL_EIEC_SHIFT)
#define EMAC_MTL_DBG_CTL_EIEC(n)         (((n) << EMAC_MTL_DBG_CTL_EIEC_SHIFT) & EMAC_MTL_DBG_CTL_EIEC_MASK)

/* MTL Debug Status (MTL_DBG_STS) */
#define EMAC_MTL_DBG_STS_FIFOBUSY        (1 << 0) /* Bit 0: FIFO Busy */
#define EMAC_MTL_DBG_STS_PKTSTATE_SHIFT  (1)      /* Bits 1-3: Encoded Packet State */
#define EMAC_MTL_DBG_STS_PKTSTATE_MASK   (0x3 << EMAC_MTL_DBG_STS_PKTSTATE_SHIFT)
#define EMAC_MTL_DBG_STS_PKTSTATE(n)     (((n) << EMAC_MTL_DBG_STS_PKTSTATE_SHIFT) & EMAC_MTL_DBG_STS_PKTSTATE_MASK)
#define EMAC_MTL_DBG_STS_BYTEEN_SHIFT    (3) /* Bits 3-5: Byte Enables */
#define EMAC_MTL_DBG_STS_BYTEEN_MASK     (0x3 << EMAC_MTL_DBG_STS_BYTEEN_SHIFT)
#define EMAC_MTL_DBG_STS_BYTEEN(n)       (((n) << EMAC_MTL_DBG_STS_BYTEEN_SHIFT) & EMAC_MTL_DBG_STS_BYTEEN_MASK)
#define EMAC_MTL_DBG_STS_PKTI            (1 << 8) /* Bit 8: Receive Packet Available Interrupt Status */
#define EMAC_MTL_DBG_STS_STSI            (1 << 9) /* Bit 9: Transmit Status Available Interrupt Status */
#define EMAC_MTL_DBG_STS_LOCR_SHIFT      (15)     /* Bits 15-32: Remaining Locations In FIFO */
#define EMAC_MTL_DBG_STS_LOCR_MASK       (0x1fFFF << EMAC_MTL_DBG_STS_LOCR_SHIFT)
#define EMAC_MTL_DBG_STS_LOCR(n)         (((n) << EMAC_MTL_DBG_STS_LOCR_SHIFT) & EMAC_MTL_DBG_STS_LOCR_MASK)

/* MTL FIFO Debug Data (MTL_FIFO_DEBUG_DATA) */
#define EMAC_MTL_FIFO_DEBUG_DATA_FDBGDATA_SHIFT  (0) /* Bits 0-32: FIFO Debug Data */
#define EMAC_MTL_FIFO_DEBUG_DATA_FDBGDATA_MASK   (0xffffffff << EMAC_MTL_FIFO_DEBUG_DATA_FDBGDATA_SHIFT)
#define EMAC_MTL_FIFO_DEBUG_DATA_FDBGDATA(n)     (((n) << EMAC_MTL_FIFO_DEBUG_DATA_FDBGDATA_SHIFT) & EMAC_MTL_FIFO_DEBUG_DATA_FDBGDATA_MASK)

/* MTL Interrupt Status (MTL_INTERRUPT_STATUS) */
#define EMAC_MTL_INTERRUPT_STATUS_Q0IS    (1 << 0)  /* Bit 0: Queue 0 Interrupt Status */
#define EMAC_MTL_INTERRUPT_STATUS_Q1IS    (1 << 1)  /* Bit 1: Queue 1 Interrupt Status */
#define EMAC_MTL_INTERRUPT_STATUS_DBGIS   (1 << 17) /* Bit 17: Debug Interrupt Status */
#define EMAC_MTL_INTERRUPT_STATUS_ESTIS   (1 << 18) /* Bit 18: EST (TAS- 802.1Qbv) Interrupt Status */
#define EMAC_MTL_INTERRUPT_STATUS_MTLPIS  (1 << 23) /* Bit 23: MTL Receive Parser Interrupt Status */

/* MTL Receive Queue DMA Map 0 (MTL_RXQ_DMA_MAP0) */
#define EMAC_MTL_RXQ_DMA_MAP0_Q0MDMACH  (1 << 0)  /* Bit 0: Queue 0 Mapped to DMA Channel */
#define EMAC_MTL_RXQ_DMA_MAP0_Q0DDMACH  (1 << 4)  /* Bit 4: Queue 0 Enabled for DA-based DMA Channel Selection */
#define EMAC_MTL_RXQ_DMA_MAP0_Q1MDMACH  (1 << 8)  /* Bit 8: Queue 1 Mapped to DMA Channel */
#define EMAC_MTL_RXQ_DMA_MAP0_Q1DDMACH  (1 << 12) /* Bit 12: Queue 1 Enabled for DA-based DMA Channel Selection */

/* MTL TBS Control (MTL_TBS_CTRL) */
#define EMAC_MTL_TBS_CTRL_ESTM         (1 << 0) /* Bit 0: EST offset Mode */
#define EMAC_MTL_TBS_CTRL_LEOV         (1 << 1) /* Bit 1: Launch Expiry Offset Valid */
#define EMAC_MTL_TBS_CTRL_LEGOS_SHIFT  (4)      /* Bits 4-7: Launch Expiry GSN Offset */
#define EMAC_MTL_TBS_CTRL_LEGOS_MASK   (0x7 << EMAC_MTL_TBS_CTRL_LEGOS_SHIFT)
#define EMAC_MTL_TBS_CTRL_LEGOS(n)     (((n) << EMAC_MTL_TBS_CTRL_LEGOS_SHIFT) & EMAC_MTL_TBS_CTRL_LEGOS_MASK)
#define EMAC_MTL_TBS_CTRL_LEOS_SHIFT   (8) /* Bits 8-32: Launch Expiry Offset */
#define EMAC_MTL_TBS_CTRL_LEOS_MASK    (0xffffff << EMAC_MTL_TBS_CTRL_LEOS_SHIFT)
#define EMAC_MTL_TBS_CTRL_LEOS(n)      (((n) << EMAC_MTL_TBS_CTRL_LEOS_SHIFT) & EMAC_MTL_TBS_CTRL_LEOS_MASK)

/* MTL EST Control (MTL_EST_CONTROL) */
#define EMAC_MTL_EST_CONTROL_EEST        (1 << 0) /* Bit 0: Enable EST */
#define EMAC_MTL_EST_CONTROL_SSWL        (1 << 1) /* Bit 1: Switch to Software Owned List */
#define EMAC_MTL_EST_CONTROL_DDBF        (1 << 4) /* Bit 4: Do not Drop Frames during Frame Size Error */
#define EMAC_MTL_EST_CONTROL_DFBS        (1 << 5) /* Bit 5: Drop Frames Causing Scheduling Error */
#define EMAC_MTL_EST_CONTROL_LCSE_SHIFT  (6)      /* Bits 6-8: Loop Count to Report Scheduling Error */
#define EMAC_MTL_EST_CONTROL_LCSE_MASK   (0x3 << EMAC_MTL_EST_CONTROL_LCSE_SHIFT)
#define EMAC_MTL_EST_CONTROL_LCSE(n)     (((n) << EMAC_MTL_EST_CONTROL_LCSE_SHIFT) & EMAC_MTL_EST_CONTROL_LCSE_MASK)
#define EMAC_MTL_EST_CONTROL_TILS_SHIFT  (8) /* Bits 8-11: Time Interval Left Shift Amount */
#define EMAC_MTL_EST_CONTROL_TILS_MASK   (0x7 << EMAC_MTL_EST_CONTROL_TILS_SHIFT)
#define EMAC_MTL_EST_CONTROL_TILS(n)     (((n) << EMAC_MTL_EST_CONTROL_TILS_SHIFT) & EMAC_MTL_EST_CONTROL_TILS_MASK)
#define EMAC_MTL_EST_CONTROL_CTOV_SHIFT  (12) /* Bits 12-24: Current Time Offset Value */
#define EMAC_MTL_EST_CONTROL_CTOV_MASK   (0xfff << EMAC_MTL_EST_CONTROL_CTOV_SHIFT)
#define EMAC_MTL_EST_CONTROL_CTOV(n)     (((n) << EMAC_MTL_EST_CONTROL_CTOV_SHIFT) & EMAC_MTL_EST_CONTROL_CTOV_MASK)
#define EMAC_MTL_EST_CONTROL_PTOV_SHIFT  (24) /* Bits 24-32: PTP Time Offset Value */
#define EMAC_MTL_EST_CONTROL_PTOV_MASK   (0xff << EMAC_MTL_EST_CONTROL_PTOV_SHIFT)
#define EMAC_MTL_EST_CONTROL_PTOV(n)     (((n) << EMAC_MTL_EST_CONTROL_PTOV_SHIFT) & EMAC_MTL_EST_CONTROL_PTOV_MASK)

/* MTL EST Status (MTL_EST_STATUS) */
#define EMAC_MTL_EST_STATUS_SWLC        (1 << 0) /* Bit 0: Switch to Software Owned List Complete */
#define EMAC_MTL_EST_STATUS_BTRE        (1 << 1) /* Bit 1: BTR Error */
#define EMAC_MTL_EST_STATUS_HLBF        (1 << 2) /* Bit 2: Head-Of-Line Blocking due to Frame Size Set when HOL Blocking is noticed on one or more Queues as a result of none of the Time Intervals of gate open in the GCL being greater than or equal to the duration needed for frame size (or frame fragment size when preemption is enabled) transmission */
#define EMAC_MTL_EST_STATUS_HLBS        (1 << 3) /* Bit 3: Head-Of-Line Blocking due to Scheduling Set when the frame is not able to win arbitration and get scheduled even after 4 iterations of the GCL */
#define EMAC_MTL_EST_STATUS_CGCE        (1 << 4) /* Bit 4: Constant Gate Control Error This error occurs when the list length (LLR) is 1 and the Cycle Time (CTR) is less than or equal to the programmed Time Interval (TI) value after the optional Left Shifting */
#define EMAC_MTL_EST_STATUS_SWOL        (1 << 7) /* Bit 7: S/W owned list When '0' indicates Gate control list number "0" is owned by software and when "1" indicates the Gate Control list "1" is owned by the software */
#define EMAC_MTL_EST_STATUS_BTRL_SHIFT  (8)      /* Bits 8-12: BTR Error Loop Count Provides the minimum count (N) for which the equation Current Time =< New BTR + ((n) * New Cycle Time) becomes true */
#define EMAC_MTL_EST_STATUS_BTRL_MASK   (0xf << EMAC_MTL_EST_STATUS_BTRL_SHIFT)
#define EMAC_MTL_EST_STATUS_BTRL(n)     (((n) << EMAC_MTL_EST_STATUS_BTRL_SHIFT) & EMAC_MTL_EST_STATUS_BTRL_MASK)
#define EMAC_MTL_EST_STATUS_CGSN_SHIFT  (16) /* Bits 16-20: Current GCL Slot Number Indicates the slot number of the GCL list */
#define EMAC_MTL_EST_STATUS_CGSN_MASK   (0xf << EMAC_MTL_EST_STATUS_CGSN_SHIFT)
#define EMAC_MTL_EST_STATUS_CGSN(n)     (((n) << EMAC_MTL_EST_STATUS_CGSN_SHIFT) & EMAC_MTL_EST_STATUS_CGSN_MASK)

/* MTL EST Scheduling Error (MTL_EST_SCH_ERROR) */
#define EMAC_MTL_EST_SCH_ERROR_SEQN_SHIFT  (0) /* Bits 0-2: Schedule Error Queue Number */
#define EMAC_MTL_EST_SCH_ERROR_SEQN_MASK   (0x3 << EMAC_MTL_EST_SCH_ERROR_SEQN_SHIFT)
#define EMAC_MTL_EST_SCH_ERROR_SEQN(n)     (((n) << EMAC_MTL_EST_SCH_ERROR_SEQN_SHIFT) & EMAC_MTL_EST_SCH_ERROR_SEQN_MASK)

/* MTL EST Frame Size Error (MTL_EST_FRM_SIZE_ERROR) */
#define EMAC_MTL_EST_FRM_SIZE_ERROR_FEQN_SHIFT  (0) /* Bits 0-2: Frame Size Error Queue Number */
#define EMAC_MTL_EST_FRM_SIZE_ERROR_FEQN_MASK   (0x3 << EMAC_MTL_EST_FRM_SIZE_ERROR_FEQN_SHIFT)
#define EMAC_MTL_EST_FRM_SIZE_ERROR_FEQN(n)     (((n) << EMAC_MTL_EST_FRM_SIZE_ERROR_FEQN_SHIFT) & EMAC_MTL_EST_FRM_SIZE_ERROR_FEQN_MASK)

/* MTL EST Frame Size Capture (MTL_EST_FRM_SIZE_CAPTURE) */
#define EMAC_MTL_EST_FRM_SIZE_CAPTURE_HBFS_SHIFT  (0) /* Bits 0-15: Frame Size of HLBF */
#define EMAC_MTL_EST_FRM_SIZE_CAPTURE_HBFS_MASK   (0x7ffF << EMAC_MTL_EST_FRM_SIZE_CAPTURE_HBFS_SHIFT)
#define EMAC_MTL_EST_FRM_SIZE_CAPTURE_HBFS(n)     (((n) << EMAC_MTL_EST_FRM_SIZE_CAPTURE_HBFS_SHIFT) & EMAC_MTL_EST_FRM_SIZE_CAPTURE_HBFS_MASK)
#define EMAC_MTL_EST_FRM_SIZE_CAPTURE_HBFQ        (1 << 16) /* Bit 16: Queue Number of HLBF */

/* MTL EST Interrupt Enable (MTL_EST_INTR_ENABLE) */
#define EMAC_MTL_EST_INTR_ENABLE_IECC  (1 << 0) /* Bit 0: Interrupt Enable for Switch List */
#define EMAC_MTL_EST_INTR_ENABLE_IEBE  (1 << 1) /* Bit 1: Interrupt Enable for BTR Error */
#define EMAC_MTL_EST_INTR_ENABLE_IEHF  (1 << 2) /* Bit 2: Interrupt Enable for HLBF */
#define EMAC_MTL_EST_INTR_ENABLE_IEHS  (1 << 3) /* Bit 3: Interrupt Enable for HLBS */
#define EMAC_MTL_EST_INTR_ENABLE_CGCE  (1 << 4) /* Bit 4: Interrupt Enable for CGCE */

/* MTL EST GCL Control (MTL_EST_GCL_CONTROL) */
#define EMAC_MTL_EST_GCL_CONTROL_SRWO           (1 << 0) /* Bit 0: Start Read/Write Operation */
#define EMAC_MTL_EST_GCL_CONTROL_R1W0           (1 << 1) /* Bit 1: Read '1', Write '0' */
#define EMAC_MTL_EST_GCL_CONTROL_GCRR           (1 << 2) /* Bit 2: Gate Control Related Registers */
#define EMAC_MTL_EST_GCL_CONTROL_DBGM           (1 << 4) /* Bit 4: Debug Mode */
#define EMAC_MTL_EST_GCL_CONTROL_DBGB           (1 << 5) /* Bit 5: Debug Mode Bank Select */
#define EMAC_MTL_EST_GCL_CONTROL_ADDR_SHIFT     (8)      /* Bits 8-16: Gate Control List Address: (GCLA when GCRR is "0") */
#define EMAC_MTL_EST_GCL_CONTROL_ADDR_MASK      (0xff << EMAC_MTL_EST_GCL_CONTROL_ADDR_SHIFT)
#define EMAC_MTL_EST_GCL_CONTROL_ADDR(n)        (((n) << EMAC_MTL_EST_GCL_CONTROL_ADDR_SHIFT) & EMAC_MTL_EST_GCL_CONTROL_ADDR_MASK)
#define EMAC_MTL_EST_GCL_CONTROL_ERR0           (1 << 20) /* Bit 20: If this field is 1, it indicates that when the software writes to GCL the last write operation was aborted and when MTL_EST_Control[SSWL] is 1, GCL registers are prohibited */
#define EMAC_MTL_EST_GCL_CONTROL_ESTEIEE        (1 << 21) /* Bit 21: EST ECC Inject Error Enable */
#define EMAC_MTL_EST_GCL_CONTROL_ESTEIEC_SHIFT  (22)      /* Bits 22-24: ECC Inject Error Control for EST Memory */
#define EMAC_MTL_EST_GCL_CONTROL_ESTEIEC_MASK   (0x3 << EMAC_MTL_EST_GCL_CONTROL_ESTEIEC_SHIFT)
#define EMAC_MTL_EST_GCL_CONTROL_ESTEIEC(n)     (((n) << EMAC_MTL_EST_GCL_CONTROL_ESTEIEC_SHIFT) & EMAC_MTL_EST_GCL_CONTROL_ESTEIEC_MASK)

/* MTL EST GCL Data (MTL_EST_GCL_DATA) */
#define EMAC_MTL_EST_GCL_DATA_GCD_SHIFT  (0) /* Bits 0-32: Gate Control Data */
#define EMAC_MTL_EST_GCL_DATA_GCD_MASK   (0xffffffff << EMAC_MTL_EST_GCL_DATA_GCD_SHIFT)
#define EMAC_MTL_EST_GCL_DATA_GCD(n)     (((n) << EMAC_MTL_EST_GCL_DATA_GCD_SHIFT) & EMAC_MTL_EST_GCL_DATA_GCD_MASK)

/* MTL FPE Control Status (MTL_FPE_CTRL_STS) */
#define EMAC_MTL_FPE_CTRL_STS_AFSZ_SHIFT  (0) /* Bits 0-2: Additional Fragment Size */
#define EMAC_MTL_FPE_CTRL_STS_AFSZ_MASK   (0x3 << EMAC_MTL_FPE_CTRL_STS_AFSZ_SHIFT)
#define EMAC_MTL_FPE_CTRL_STS_AFSZ(n)     (((n) << EMAC_MTL_FPE_CTRL_STS_AFSZ_SHIFT) & EMAC_MTL_FPE_CTRL_STS_AFSZ_MASK)
#define EMAC_MTL_FPE_CTRL_STS_PEC_SHIFT   (8) /* Bits 8-10: Preemption Classification */
#define EMAC_MTL_FPE_CTRL_STS_PEC_MASK    (0x3 << EMAC_MTL_FPE_CTRL_STS_PEC_SHIFT)
#define EMAC_MTL_FPE_CTRL_STS_PEC(n)      (((n) << EMAC_MTL_FPE_CTRL_STS_PEC_SHIFT) & EMAC_MTL_FPE_CTRL_STS_PEC_MASK)
#define EMAC_MTL_FPE_CTRL_STS_HRS         (1 << 28) /* Bit 28: Hold/Release Status */

/* MTL FPE Advance (MTL_FPE_ADVANCE) */
#define EMAC_MTL_FPE_ADVANCE_HADV_SHIFT  (0) /* Bits 0-16: Hold Advance */
#define EMAC_MTL_FPE_ADVANCE_HADV_MASK   (0xffff << EMAC_MTL_FPE_ADVANCE_HADV_SHIFT)
#define EMAC_MTL_FPE_ADVANCE_HADV(n)     (((n) << EMAC_MTL_FPE_ADVANCE_HADV_SHIFT) & EMAC_MTL_FPE_ADVANCE_HADV_MASK)
#define EMAC_MTL_FPE_ADVANCE_RADV_SHIFT  (16) /* Bits 16-32: Release Advance */
#define EMAC_MTL_FPE_ADVANCE_RADV_MASK   (0xffff << EMAC_MTL_FPE_ADVANCE_RADV_SHIFT)
#define EMAC_MTL_FPE_ADVANCE_RADV(n)     (((n) << EMAC_MTL_FPE_ADVANCE_RADV_SHIFT) & EMAC_MTL_FPE_ADVANCE_RADV_MASK)

/* MTL Rx Parser Control Status (MTL_RXP_CONTROL_STATUS) */
#define EMAC_MTL_RXP_CONTROL_STATUS_NVE_SHIFT  (0) /* Bits 0-6: Number Of Valid Entry Address Or Index In The Instruction Table */
#define EMAC_MTL_RXP_CONTROL_STATUS_NVE_MASK   (0x3f << EMAC_MTL_RXP_CONTROL_STATUS_NVE_SHIFT)
#define EMAC_MTL_RXP_CONTROL_STATUS_NVE(n)     (((n) << EMAC_MTL_RXP_CONTROL_STATUS_NVE_SHIFT) & EMAC_MTL_RXP_CONTROL_STATUS_NVE_MASK)
#define EMAC_MTL_RXP_CONTROL_STATUS_MTL_SCS1   (1 << 15) /* Bit 15: MTL_SCS1 */
#define EMAC_MTL_RXP_CONTROL_STATUS_NPE_SHIFT  (16)      /* Bits 16-22: Number of parsable entries in the Instruction table */
#define EMAC_MTL_RXP_CONTROL_STATUS_NPE_MASK   (0x3f << EMAC_MTL_RXP_CONTROL_STATUS_NPE_SHIFT)
#define EMAC_MTL_RXP_CONTROL_STATUS_NPE(n)     (((n) << EMAC_MTL_RXP_CONTROL_STATUS_NPE_SHIFT) & EMAC_MTL_RXP_CONTROL_STATUS_NPE_MASK)
#define EMAC_MTL_RXP_CONTROL_STATUS_RXPI       (1 << 31) /* Bit 31: RX Parser in Idle State */

/* MTL Rx Parser Interrupt Control Status
 * (MTL_RXP_INTERRUPT_CONTROL_STATUS)
 */

#define EMAC_MTL_RXP_INTERRUPT_CONTROL_STATUS_NVEOVIS  (1 << 0)  /* Bit 0: Number of Valid Entry Address/Index Overflow Interrupt Status */
#define EMAC_MTL_RXP_INTERRUPT_CONTROL_STATUS_NPEOVIS  (1 << 1)  /* Bit 1: Number of Parsable Entries Overflow Interrupt Status */
#define EMAC_MTL_RXP_INTERRUPT_CONTROL_STATUS_FOOVIS   (1 << 2)  /* Bit 2: Frame Offset Overflow Interrupt Status */
#define EMAC_MTL_RXP_INTERRUPT_CONTROL_STATUS_PDRFIS   (1 << 3)  /* Bit 3: Packet Dropped due to RF Interrupt Status */
#define EMAC_MTL_RXP_INTERRUPT_CONTROL_STATUS_NVEOVIE  (1 << 16) /* Bit 16: Number of Valid Entries Overflow Interrupt Enable */
#define EMAC_MTL_RXP_INTERRUPT_CONTROL_STATUS_NPEOVIE  (1 << 17) /* Bit 17: Number of Parsable Entries Overflow Interrupt Enable */
#define EMAC_MTL_RXP_INTERRUPT_CONTROL_STATUS_FOOVIE   (1 << 18) /* Bit 18: Frame Offset Overflow Interrupt Enable */
#define EMAC_MTL_RXP_INTERRUPT_CONTROL_STATUS_PDRFIE   (1 << 19) /* Bit 19: Packet Drop due to RF Interrupt Enable */

/* MTL Rx Parser Drop Count (MTL_RXP_DROP_CNT) */
#define EMAC_MTL_RXP_DROP_CNT_RXPDC_SHIFT  (0) /* Bits 0-31: Rx Parser Drop Count */
#define EMAC_MTL_RXP_DROP_CNT_RXPDC_MASK   (0x7fffffff << EMAC_MTL_RXP_DROP_CNT_RXPDC_SHIFT)
#define EMAC_MTL_RXP_DROP_CNT_RXPDC(n)     (((n) << EMAC_MTL_RXP_DROP_CNT_RXPDC_SHIFT) & EMAC_MTL_RXP_DROP_CNT_RXPDC_MASK)
#define EMAC_MTL_RXP_DROP_CNT_RXPDCOVF     (1 << 31) /* Bit 31: Rx Parser Drop Counter Overflow Bit */

/* MTL Rx Parser Error Count (MTL_RXP_ERROR_CNT) */
#define EMAC_MTL_RXP_ERROR_CNT_RXPEC_SHIFT  (0) /* Bits 0-31: Rx Parser Error Count */
#define EMAC_MTL_RXP_ERROR_CNT_RXPEC_MASK   (0x7fffffff << EMAC_MTL_RXP_ERROR_CNT_RXPEC_SHIFT)
#define EMAC_MTL_RXP_ERROR_CNT_RXPEC(n)     (((n) << EMAC_MTL_RXP_ERROR_CNT_RXPEC_SHIFT) & EMAC_MTL_RXP_ERROR_CNT_RXPEC_MASK)
#define EMAC_MTL_RXP_ERROR_CNT_RXPECOVF     (1 << 31) /* Bit 31: Rx Parser Error Counter Overflow Bit */

/* MTL Rx Parser Indirect Access Control Status
 * (MTL_RXP_INDIRECT_ACC_CONTROL_STATUS)
 */

#define EMAC_MTL_RXP_INDIRECT_ACC_CONTROL_STATUS_ADDR_SHIFT     (0) /* Bits 0-8: FRP Instruction Table Offset Address */
#define EMAC_MTL_RXP_INDIRECT_ACC_CONTROL_STATUS_ADDR_MASK      (0xff << EMAC_MTL_RXP_INDIRECT_ACC_CONTROL_STATUS_ADDR_SHIFT)
#define EMAC_MTL_RXP_INDIRECT_ACC_CONTROL_STATUS_ADDR(n)        (((n) << EMAC_MTL_RXP_INDIRECT_ACC_CONTROL_STATUS_ADDR_SHIFT) & EMAC_MTL_RXP_INDIRECT_ACC_CONTROL_STATUS_ADDR_MASK)
#define EMAC_MTL_RXP_INDIRECT_ACC_CONTROL_STATUS_WRRDN          (1 << 16) /* Bit 16: Read Write Control */
#define EMAC_MTL_RXP_INDIRECT_ACC_CONTROL_STATUS_RXPEIEE        (1 << 20) /* Bit 20: ECC Inject Error Enable for Rx Parser Memory */
#define EMAC_MTL_RXP_INDIRECT_ACC_CONTROL_STATUS_RXPEIEC_SHIFT  (21)      /* Bits 21-23: ECC Inject Error Control for Rx Parser Memory */
#define EMAC_MTL_RXP_INDIRECT_ACC_CONTROL_STATUS_RXPEIEC_MASK   (0x3 << EMAC_MTL_RXP_INDIRECT_ACC_CONTROL_STATUS_RXPEIEC_SHIFT)
#define EMAC_MTL_RXP_INDIRECT_ACC_CONTROL_STATUS_RXPEIEC(n)     (((n) << EMAC_MTL_RXP_INDIRECT_ACC_CONTROL_STATUS_RXPEIEC_SHIFT) & EMAC_MTL_RXP_INDIRECT_ACC_CONTROL_STATUS_RXPEIEC_MASK)
#define EMAC_MTL_RXP_INDIRECT_ACC_CONTROL_STATUS_STARTBUSY      (1 << 31) /* Bit 31: FRP Instruction Table Access Busy */

/* MTL Rx Parser Indirect Access Data (MTL_RXP_INDIRECT_ACC_DATA) */
#define EMAC_MTL_RXP_INDIRECT_ACC_DATA_DATA_SHIFT  (0) /* Bits 0-32: FRP Instruction Table Write/Read Data */
#define EMAC_MTL_RXP_INDIRECT_ACC_DATA_DATA_MASK   (0xffffffff << EMAC_MTL_RXP_INDIRECT_ACC_DATA_DATA_SHIFT)
#define EMAC_MTL_RXP_INDIRECT_ACC_DATA_DATA(n)     (((n) << EMAC_MTL_RXP_INDIRECT_ACC_DATA_DATA_SHIFT) & EMAC_MTL_RXP_INDIRECT_ACC_DATA_DATA_MASK)

/* MTL ECC Control (MTL_ECC_CONTROL) */
#define EMAC_MTL_ECC_CONTROL_MTXEE   (1 << 0) /* Bit 0: MTL Tx FIFO ECC Enable */
#define EMAC_MTL_ECC_CONTROL_MRXEE   (1 << 1) /* Bit 1: MTL Rx FIFO ECC Enable */
#define EMAC_MTL_ECC_CONTROL_MESTEE  (1 << 2) /* Bit 2: MTL EST ECC Enable */
#define EMAC_MTL_ECC_CONTROL_MRXPEE  (1 << 3) /* Bit 3: MTL Rx Parser ECC Enable */
#define EMAC_MTL_ECC_CONTROL_MEEAO   (1 << 8) /* Bit 8: MTL ECC Error Address Status Over-ride */

/* MTL Safety Interript Status (MTL_SAFETY_INTERRUPT_STATUS) */
#define EMAC_MTL_SAFETY_INTERRUPT_STATUS_MECIS  (1 << 0) /* Bit 0: MTL ECC Correctable Error Interrupt Status */
#define EMAC_MTL_SAFETY_INTERRUPT_STATUS_MEUIS  (1 << 1) /* Bit 1: MTL ECC Uncorrectable Error Interrupt Status */

/* MTL ECC Interrupt Enable (MTL_ECC_INTERRUPT_ENABLE) */
#define EMAC_MTL_ECC_INTERRUPT_ENABLE_TXCEIE  (1 << 0)  /* Bit 0: Tx Memory Correctable Error Interrupt Enable */
#define EMAC_MTL_ECC_INTERRUPT_ENABLE_RXCEIE  (1 << 4)  /* Bit 4: Rx Memory Correctable Error Interrupt Enable */
#define EMAC_MTL_ECC_INTERRUPT_ENABLE_ECEIE   (1 << 8)  /* Bit 8: EST Memory Correctable Error Interrupt Enable */
#define EMAC_MTL_ECC_INTERRUPT_ENABLE_RPCEIE  (1 << 12) /* Bit 12: Rx Parser Memory Correctable Error Interrupt Enable */

/* MTL ECC Interrupt Status (MTL_ECC_INTERRUPT_STATUS) */
#define EMAC_MTL_ECC_INTERRUPT_STATUS_TXCES  (1 << 0)  /* Bit 0: MTL Tx Memory Correctable Error Status */
#define EMAC_MTL_ECC_INTERRUPT_STATUS_TXAMS  (1 << 1)  /* Bit 1: MTL Tx Memory Address Mismatch Status */
#define EMAC_MTL_ECC_INTERRUPT_STATUS_TXUES  (1 << 2)  /* Bit 2: MTL Tx Memory Uncorrectable Error Status */
#define EMAC_MTL_ECC_INTERRUPT_STATUS_RXCES  (1 << 4)  /* Bit 4: MTL Rx memory Correctable Error Status */
#define EMAC_MTL_ECC_INTERRUPT_STATUS_RXAMS  (1 << 5)  /* Bit 5: MTL Rx Memory Address Mismatch Status */
#define EMAC_MTL_ECC_INTERRUPT_STATUS_RXUES  (1 << 6)  /* Bit 6: MTL Rx Memory Uncorrectable Error Status */
#define EMAC_MTL_ECC_INTERRUPT_STATUS_ECES   (1 << 8)  /* Bit 8: MTL EST Memory Correctable Error Status */
#define EMAC_MTL_ECC_INTERRUPT_STATUS_EAMS   (1 << 9)  /* Bit 9: MTL EST Memory Address Mismatch Status */
#define EMAC_MTL_ECC_INTERRUPT_STATUS_EUES   (1 << 10) /* Bit 10: MTL EST Memory Uncorrectable Error Status */
#define EMAC_MTL_ECC_INTERRUPT_STATUS_RPCES  (1 << 12) /* Bit 12: MTL Rx Parser Memory Correctable Error Status */
#define EMAC_MTL_ECC_INTERRUPT_STATUS_RPAMS  (1 << 13) /* Bit 13: MTL Rx Parser Memory Address Mismatch Status */
#define EMAC_MTL_ECC_INTERRUPT_STATUS_RPUES  (1 << 14) /* Bit 14: Rx Parser Memory Uncorrectable Error Status */

/* MTL ECC Error Status (MTL_ECC_ERR_STS_RCTL) */
#define EMAC_MTL_ECC_ERR_STS_RCTL_EESRE      (1 << 0) /* Bit 0: MTL ECC Error Status Read Enable */
#define EMAC_MTL_ECC_ERR_STS_RCTL_EMS_SHIFT  (1)      /* Bits 1-4: MTL ECC Memory Selection */
#define EMAC_MTL_ECC_ERR_STS_RCTL_EMS_MASK   (0x7 << EMAC_MTL_ECC_ERR_STS_RCTL_EMS_SHIFT)
#define EMAC_MTL_ECC_ERR_STS_RCTL_EMS(n)     (((n) << EMAC_MTL_ECC_ERR_STS_RCTL_EMS_SHIFT) & EMAC_MTL_ECC_ERR_STS_RCTL_EMS_MASK)
#define EMAC_MTL_ECC_ERR_STS_RCTL_CCES       (1 << 4) /* Bit 4: Clear Correctable Error Status */
#define EMAC_MTL_ECC_ERR_STS_RCTL_CUES       (1 << 5) /* Bit 5: Clear Uncorrectable Error Status */

/* MTL ECC Error Adress Status (MTL_ECC_ERR_ADDR_STATUS) */
#define EMAC_MTL_ECC_ERR_ADDR_STATUS_ECEAS_SHIFT  (0) /* Bits 0-16: MTL ECC Correctable Error Address Status */
#define EMAC_MTL_ECC_ERR_ADDR_STATUS_ECEAS_MASK   (0xffff << EMAC_MTL_ECC_ERR_ADDR_STATUS_ECEAS_SHIFT)
#define EMAC_MTL_ECC_ERR_ADDR_STATUS_ECEAS(n)     (((n) << EMAC_MTL_ECC_ERR_ADDR_STATUS_ECEAS_SHIFT) & EMAC_MTL_ECC_ERR_ADDR_STATUS_ECEAS_MASK)
#define EMAC_MTL_ECC_ERR_ADDR_STATUS_EUEAS_SHIFT  (16) /* Bits 16-32: MTL ECC Uncorrectable Error Address Status */
#define EMAC_MTL_ECC_ERR_ADDR_STATUS_EUEAS_MASK   (0xffff << EMAC_MTL_ECC_ERR_ADDR_STATUS_EUEAS_SHIFT)
#define EMAC_MTL_ECC_ERR_ADDR_STATUS_EUEAS(n)     (((n) << EMAC_MTL_ECC_ERR_ADDR_STATUS_EUEAS_SHIFT) & EMAC_MTL_ECC_ERR_ADDR_STATUS_EUEAS_MASK)

/* MTL ECC Error Control Status (MTL_ECC_ERR_CNTR_STATUS) */
#define EMAC_MTL_ECC_ERR_CNTR_STATUS_ECECS_SHIFT  (0) /* Bits 0-8: MTL ECC Correctable Error Counter Status Based on the EMS field of MTL_ECC_Err_Cntr_Rctl register, this field holds the respective memory's correctable error count value */
#define EMAC_MTL_ECC_ERR_CNTR_STATUS_ECECS_MASK   (0xff << EMAC_MTL_ECC_ERR_CNTR_STATUS_ECECS_SHIFT)
#define EMAC_MTL_ECC_ERR_CNTR_STATUS_ECECS(n)     (((n) << EMAC_MTL_ECC_ERR_CNTR_STATUS_ECECS_SHIFT) & EMAC_MTL_ECC_ERR_CNTR_STATUS_ECECS_MASK)
#define EMAC_MTL_ECC_ERR_CNTR_STATUS_EUECS_SHIFT  (16) /* Bits 16-20: MTL ECC Uncorrectable Error Counter Status Based on the EMS field of MTL_ECC_Err_Cntr_Rctl register, this field holds the respective memory's uncorrectable error count value */
#define EMAC_MTL_ECC_ERR_CNTR_STATUS_EUECS_MASK   (0xf << EMAC_MTL_ECC_ERR_CNTR_STATUS_EUECS_SHIFT)
#define EMAC_MTL_ECC_ERR_CNTR_STATUS_EUECS(n)     (((n) << EMAC_MTL_ECC_ERR_CNTR_STATUS_EUECS_SHIFT) & EMAC_MTL_ECC_ERR_CNTR_STATUS_EUECS_MASK)

/* MTL DPP Control (MTL_DPP_CONTROL) */
#define EMAC_MTL_DPP_CONTROL_EDPP    (1 << 0)  /* Bit 0: Enable Data path Parity Protection */
#define EMAC_MTL_DPP_CONTROL_OPE     (1 << 1)  /* Bit 1: Odd Parity Enable */
#define EMAC_MTL_DPP_CONTROL_IPEID   (1 << 4)  /* Bit 4: Insert Parity Error in Interface Data Parity Generator */
#define EMAC_MTL_DPP_CONTROL_IPEMC   (1 << 5)  /* Bit 5: Insert Parity Error in MTL Checksum Parity Generator */
#define EMAC_MTL_DPP_CONTROL_IPEMTS  (1 << 6)  /* Bit 6: Insert Parity Error in MTL Tx Status Parity Generator */
#define EMAC_MTL_DPP_CONTROL_IPEMRF  (1 << 7)  /* Bit 7: Insert Parity Error in MTL Rx FIFO Read Control Parity Generator */
#define EMAC_MTL_DPP_CONTROL_IPEDDC  (1 << 8)  /* Bit 8: Insert Parity Error in DMA DTX Control Word Parity Generator */
#define EMAC_MTL_DPP_CONTROL_IPETD   (1 << 10) /* Bit 10: Insert Parity error in Tx write-back Descriptor parity generator */
#define EMAC_MTL_DPP_CONTROL_IPERD   (1 << 11) /* Bit 11: Insert Parity error in Rx write-back Descriptor parity generator */

/* MTL Tx Queue 0 Operation Mode (MTL_TXQ0_OPERATION_MODE) */
#define EMAC_MTL_TXQ0_OPERATION_MODE_FTQ           (1 << 0) /* Bit 0: Flush Transmit Queue */
#define EMAC_MTL_TXQ0_OPERATION_MODE_TSF           (1 << 1) /* Bit 1: Transmit Store and Forward */
#define EMAC_MTL_TXQ0_OPERATION_MODE_TXQEN_SHIFT   (2)      /* Bits 2-4: Transmit Queue Enable */
#define EMAC_MTL_TXQ0_OPERATION_MODE_TXQEN_MASK    (0x3 << EMAC_MTL_TXQ0_OPERATION_MODE_TXQEN_SHIFT)
#define EMAC_MTL_TXQ0_OPERATION_MODE_TXQEN(n)      (((n) << EMAC_MTL_TXQ0_OPERATION_MODE_TXQEN_SHIFT) & EMAC_MTL_TXQ0_OPERATION_MODE_TXQEN_MASK)
#define EMAC_MTL_TXQ0_OPERATION_MODE_TXQEN_DISABLE EMAC_MTL_TXQ0_OPERATION_MODE_TXQEN(0)
#define EMAC_MTL_TXQ0_OPERATION_MODE_TXQEN_AVB     EMAC_MTL_TXQ0_OPERATION_MODE_TXQEN(0x1)
#define EMAC_MTL_TXQ0_OPERATION_MODE_TXQEN_DCB_GEN EMAC_MTL_TXQ0_OPERATION_MODE_TXQEN(0x2)
#define EMAC_MTL_TXQ0_OPERATION_MODE_TTC_SHIFT     (4) /* Bits 4-7: Transmit Threshold Control */
#define EMAC_MTL_TXQ0_OPERATION_MODE_TTC_MASK      (0x7 << EMAC_MTL_TXQ0_OPERATION_MODE_TTC_SHIFT)
#define EMAC_MTL_TXQ0_OPERATION_MODE_TTC(n)        (((n) << EMAC_MTL_TXQ0_OPERATION_MODE_TTC_SHIFT) & EMAC_MTL_TXQ0_OPERATION_MODE_TTC_MASK)
#define EMAC_MTL_TXQ0_OPERATION_MODE_TQS_SHIFT     (16) /* Bits 16-21: Transmit Queue Size */
#define EMAC_MTL_TXQ0_OPERATION_MODE_TQS_MASK      (0x1f << EMAC_MTL_TXQ0_OPERATION_MODE_TQS_SHIFT)
#define EMAC_MTL_TXQ0_OPERATION_MODE_TQS(n)        (((n) << EMAC_MTL_TXQ0_OPERATION_MODE_TQS_SHIFT) & EMAC_MTL_TXQ0_OPERATION_MODE_TQS_MASK)

/* MTL Tx Queue 0 Underflow (MTL_TXQ0_UNDERFLOW) */
#define EMAC_MTL_TXQ0_UNDERFLOW_UFFRMCNT_SHIFT  (0) /* Bits 0-11: Underflow Packet Counter */
#define EMAC_MTL_TXQ0_UNDERFLOW_UFFRMCNT_MASK   (0x7ff << EMAC_MTL_TXQ0_UNDERFLOW_UFFRMCNT_SHIFT)
#define EMAC_MTL_TXQ0_UNDERFLOW_UFFRMCNT(n)     (((n) << EMAC_MTL_TXQ0_UNDERFLOW_UFFRMCNT_SHIFT) & EMAC_MTL_TXQ0_UNDERFLOW_UFFRMCNT_MASK)
#define EMAC_MTL_TXQ0_UNDERFLOW_UFCNTOVF        (1 << 11) /* Bit 11: Overflow Bit for Underflow Packet Counter */

/* MTL Tx Queue 0 Debug (MTL_TXQ0_DEBUG) */
#define EMAC_MTL_TXQ0_DEBUG_TXQPAUSED      (1 << 0) /* Bit 0: Transmit Queue in Pause */
#define EMAC_MTL_TXQ0_DEBUG_TRCSTS_SHIFT   (1)      /* Bits 1-3: MTL Tx Queue Read Controller Status */
#define EMAC_MTL_TXQ0_DEBUG_TRCSTS_MASK    (0x3 << EMAC_MTL_TXQ0_DEBUG_TRCSTS_SHIFT)
#define EMAC_MTL_TXQ0_DEBUG_TRCSTS(n)      (((n) << EMAC_MTL_TXQ0_DEBUG_TRCSTS_SHIFT) & EMAC_MTL_TXQ0_DEBUG_TRCSTS_MASK)
#define EMAC_MTL_TXQ0_DEBUG_TWCSTS         (1 << 3) /* Bit 3: MTL Tx Queue Write Controller Status */
#define EMAC_MTL_TXQ0_DEBUG_TXQSTS         (1 << 4) /* Bit 4: MTL Tx Queue Not Empty Status */
#define EMAC_MTL_TXQ0_DEBUG_TXSTSFSTS      (1 << 5) /* Bit 5: MTL Tx Status FIFO Full Status */
#define EMAC_MTL_TXQ0_DEBUG_PTXQ_SHIFT     (16)     /* Bits 16-19: Number of Packets in the Transmit Queue */
#define EMAC_MTL_TXQ0_DEBUG_PTXQ_MASK      (0x7 << EMAC_MTL_TXQ0_DEBUG_PTXQ_SHIFT)
#define EMAC_MTL_TXQ0_DEBUG_PTXQ(n)        (((n) << EMAC_MTL_TXQ0_DEBUG_PTXQ_SHIFT) & EMAC_MTL_TXQ0_DEBUG_PTXQ_MASK)
#define EMAC_MTL_TXQ0_DEBUG_STXSTSF_SHIFT  (20) /* Bits 20-23: Number of Status Words in Tx Status FIFO of Queue */
#define EMAC_MTL_TXQ0_DEBUG_STXSTSF_MASK   (0x7 << EMAC_MTL_TXQ0_DEBUG_STXSTSF_SHIFT)
#define EMAC_MTL_TXQ0_DEBUG_STXSTSF(n)     (((n) << EMAC_MTL_TXQ0_DEBUG_STXSTSF_SHIFT) & EMAC_MTL_TXQ0_DEBUG_STXSTSF_MASK)

/* MTL Tx Queue 0 ETS Status (MTL_TXQ0_ETS_STATUS) */
#define EMAC_MTL_TXQ0_ETS_STATUS_ABS_SHIFT  (0) /* Bits 0-24: Average Bits per Slot */
#define EMAC_MTL_TXQ0_ETS_STATUS_ABS_MASK   (0xffffff << EMAC_MTL_TXQ0_ETS_STATUS_ABS_SHIFT)
#define EMAC_MTL_TXQ0_ETS_STATUS_ABS(n)     (((n) << EMAC_MTL_TXQ0_ETS_STATUS_ABS_SHIFT) & EMAC_MTL_TXQ0_ETS_STATUS_ABS_MASK)

/* MTL Tx Queue Quantum Weight (MTL_TXQ0_QUANTUM_WEIGHT) */
#define EMAC_MTL_TXQ0_QUANTUM_WEIGHT_ISCQW_SHIFT  (0) /* Bits 0-21: Quantum or Weights */
#define EMAC_MTL_TXQ0_QUANTUM_WEIGHT_ISCQW_MASK   (0x1fffff << EMAC_MTL_TXQ0_QUANTUM_WEIGHT_ISCQW_SHIFT)
#define EMAC_MTL_TXQ0_QUANTUM_WEIGHT_ISCQW(n)     (((n) << EMAC_MTL_TXQ0_QUANTUM_WEIGHT_ISCQW_SHIFT) & EMAC_MTL_TXQ0_QUANTUM_WEIGHT_ISCQW_MASK)

/* MTL Queue 0 Interrupt Control Status (MTL_Q0_INTERRUPT_CONTROL_STATUS) */
#define EMAC_MTL_Q0_INTERRUPT_CONTROL_STATUS_TXUNFIS  (1 << 0)  /* Bit 0: Transmit Queue Underflow Interrupt Status */
#define EMAC_MTL_Q0_INTERRUPT_CONTROL_STATUS_ABPSIS   (1 << 1)  /* Bit 1: Average Bits Per Slot Interrupt Status */
#define EMAC_MTL_Q0_INTERRUPT_CONTROL_STATUS_TXUIE    (1 << 8)  /* Bit 8: Transmit Queue Underflow Interrupt Enable */
#define EMAC_MTL_Q0_INTERRUPT_CONTROL_STATUS_ABPSIE   (1 << 9)  /* Bit 9: Average Bits Per Slot Interrupt Enable */
#define EMAC_MTL_Q0_INTERRUPT_CONTROL_STATUS_RXOVFIS  (1 << 16) /* Bit 16: Receive Queue Overflow Interrupt Status */
#define EMAC_MTL_Q0_INTERRUPT_CONTROL_STATUS_RXOIE    (1 << 24) /* Bit 24: Receive Queue Overflow Interrupt Enable */

/* MTL Rx Queue 0 Operation Mode (MTL_RXQ0_OPERATION_MODE) */
#define EMAC_MTL_RXQ0_OPERATION_MODE_RTC_SHIFT   (0) /* Bits 0-2: Receive Queue Threshold Control */
#define EMAC_MTL_RXQ0_OPERATION_MODE_RTC_MASK    (0x3 << EMAC_MTL_RXQ0_OPERATION_MODE_RTC_SHIFT)
#define EMAC_MTL_RXQ0_OPERATION_MODE_RTC(n)      (((n) << EMAC_MTL_RXQ0_OPERATION_MODE_RTC_SHIFT) & EMAC_MTL_RXQ0_OPERATION_MODE_RTC_MASK)
#define EMAC_MTL_RXQ0_OPERATION_MODE_FUP         (1 << 3) /* Bit 3: Forward Undersized Good Packets */
#define EMAC_MTL_RXQ0_OPERATION_MODE_FEP         (1 << 4) /* Bit 4: Forward Error Packets */
#define EMAC_MTL_RXQ0_OPERATION_MODE_RSF         (1 << 5) /* Bit 5: Receive Queue Store and Forward */
#define EMAC_MTL_RXQ0_OPERATION_MODE_DIS_TCP_EF  (1 << 6) /* Bit 6: Disable Dropping of TCP/IP Checksum Error Packets */
#define EMAC_MTL_RXQ0_OPERATION_MODE_EHFC        (1 << 7) /* Bit 7: Enable Hardware Flow Control */
#define EMAC_MTL_RXQ0_OPERATION_MODE_RFA_SHIFT   (8)      /* Bits 8-12: Threshold for Activating Flow Control (in half-duplex and full-duplex) */
#define EMAC_MTL_RXQ0_OPERATION_MODE_RFA_MASK    (0xf << EMAC_MTL_RXQ0_OPERATION_MODE_RFA_SHIFT)
#define EMAC_MTL_RXQ0_OPERATION_MODE_RFA(n)      (((n) << EMAC_MTL_RXQ0_OPERATION_MODE_RFA_SHIFT) & EMAC_MTL_RXQ0_OPERATION_MODE_RFA_MASK)
#define EMAC_MTL_RXQ0_OPERATION_MODE_RFD_SHIFT   (14) /* Bits 14-18: Threshold for Deactivating Flow Control (in half-duplex and full-duplex modes) */
#define EMAC_MTL_RXQ0_OPERATION_MODE_RFD_MASK    (0xf << EMAC_MTL_RXQ0_OPERATION_MODE_RFD_SHIFT)
#define EMAC_MTL_RXQ0_OPERATION_MODE_RFD(n)      (((n) << EMAC_MTL_RXQ0_OPERATION_MODE_RFD_SHIFT) & EMAC_MTL_RXQ0_OPERATION_MODE_RFD_MASK)
#define EMAC_MTL_RXQ0_OPERATION_MODE_RQS_SHIFT   (20) /* Bits 20-25: Receive Queue Size */
#define EMAC_MTL_RXQ0_OPERATION_MODE_RQS_MASK    (0x1f << EMAC_MTL_RXQ0_OPERATION_MODE_RQS_SHIFT)
#define EMAC_MTL_RXQ0_OPERATION_MODE_RQS(n)      (((n) << EMAC_MTL_RXQ0_OPERATION_MODE_RQS_SHIFT) & EMAC_MTL_RXQ0_OPERATION_MODE_RQS_MASK)

/* MTL Rx Queue Missed Packet Overflow Count
 * (MTL_RXQ0_MISSED_PACKET_OVERFLOW_CNT)
 */

#define EMAC_MTL_RXQ0_MISSED_PACKET_OVERFLOW_CNT_OVFPKTCNT_SHIFT  (0) /* Bits 0-11: Overflow Packet Counter */
#define EMAC_MTL_RXQ0_MISSED_PACKET_OVERFLOW_CNT_OVFPKTCNT_MASK   (0x7ff << EMAC_MTL_RXQ0_MISSED_PACKET_OVERFLOW_CNT_OVFPKTCNT_SHIFT)
#define EMAC_MTL_RXQ0_MISSED_PACKET_OVERFLOW_CNT_OVFPKTCNT(n)     (((n) << EMAC_MTL_RXQ0_MISSED_PACKET_OVERFLOW_CNT_OVFPKTCNT_SHIFT) & EMAC_MTL_RXQ0_MISSED_PACKET_OVERFLOW_CNT_OVFPKTCNT_MASK)
#define EMAC_MTL_RXQ0_MISSED_PACKET_OVERFLOW_CNT_OVFCNTOVF        (1 << 11) /* Bit 11: Overflow Counter Overflow Bit */
#define EMAC_MTL_RXQ0_MISSED_PACKET_OVERFLOW_CNT_MISPKTCNT_SHIFT  (16)      /* Bits 16-27: Missed Packet Counter */
#define EMAC_MTL_RXQ0_MISSED_PACKET_OVERFLOW_CNT_MISPKTCNT_MASK   (0x7ff << EMAC_MTL_RXQ0_MISSED_PACKET_OVERFLOW_CNT_MISPKTCNT_SHIFT)
#define EMAC_MTL_RXQ0_MISSED_PACKET_OVERFLOW_CNT_MISPKTCNT(n)     (((n) << EMAC_MTL_RXQ0_MISSED_PACKET_OVERFLOW_CNT_MISPKTCNT_SHIFT) & EMAC_MTL_RXQ0_MISSED_PACKET_OVERFLOW_CNT_MISPKTCNT_MASK)
#define EMAC_MTL_RXQ0_MISSED_PACKET_OVERFLOW_CNT_MISCNTOVF        (1 << 27) /* Bit 27: Missed Packet Counter Overflow Bit */

/* MTL Rx Queue 0 Debug (MTL_RXQ0_DEBUG) */
#define EMAC_MTL_RXQ0_DEBUG_RWCSTS        (1 << 0) /* Bit 0: MTL Rx Queue Write Controller Active Status */
#define EMAC_MTL_RXQ0_DEBUG_RRCSTS_SHIFT  (1)      /* Bits 1-3: MTL Rx Queue Read Controller State */
#define EMAC_MTL_RXQ0_DEBUG_RRCSTS_MASK   (0x3 << EMAC_MTL_RXQ0_DEBUG_RRCSTS_SHIFT)
#define EMAC_MTL_RXQ0_DEBUG_RRCSTS(n)     (((n) << EMAC_MTL_RXQ0_DEBUG_RRCSTS_SHIFT) & EMAC_MTL_RXQ0_DEBUG_RRCSTS_MASK)
#define EMAC_MTL_RXQ0_DEBUG_RXQSTS_SHIFT  (4) /* Bits 4-6: MTL Rx Queue Fill-Level Status */
#define EMAC_MTL_RXQ0_DEBUG_RXQSTS_MASK   (0x3 << EMAC_MTL_RXQ0_DEBUG_RXQSTS_SHIFT)
#define EMAC_MTL_RXQ0_DEBUG_RXQSTS(n)     (((n) << EMAC_MTL_RXQ0_DEBUG_RXQSTS_SHIFT) & EMAC_MTL_RXQ0_DEBUG_RXQSTS_MASK)
#define EMAC_MTL_RXQ0_DEBUG_PRXQ_SHIFT    (16) /* Bits 16-30: Number of Packets in Receive Queue */
#define EMAC_MTL_RXQ0_DEBUG_PRXQ_MASK     (0x3fff << EMAC_MTL_RXQ0_DEBUG_PRXQ_SHIFT)
#define EMAC_MTL_RXQ0_DEBUG_PRXQ(n)       (((n) << EMAC_MTL_RXQ0_DEBUG_PRXQ_SHIFT) & EMAC_MTL_RXQ0_DEBUG_PRXQ_MASK)

/* MTL Rx Queue 0 Control 0 (MTL_RXQ0_CONTROL) */
#define EMAC_MTL_RXQ0_CONTROL_RXQ_WEGT_SHIFT  (0) /* Bits 0-3: Receive Queue Weight */
#define EMAC_MTL_RXQ0_CONTROL_RXQ_WEGT_MASK   (0x7 << EMAC_MTL_RXQ0_CONTROL_RXQ_WEGT_SHIFT)
#define EMAC_MTL_RXQ0_CONTROL_RXQ_WEGT(n)     (((n) << EMAC_MTL_RXQ0_CONTROL_RXQ_WEGT_SHIFT) & EMAC_MTL_RXQ0_CONTROL_RXQ_WEGT_MASK)
#define EMAC_MTL_RXQ0_CONTROL_RXQ_FRM_ARBIT   (1 << 3) /* Bit 3: Receive Queue Packet Arbitration */

/* MTL Tx Queue 1 Operation Mode (MTL_TXQ1_OPERATION_MODE) */
#define EMAC_MTL_TXQ1_OPERATION_MODE_FTQ          (1 << 0) /* Bit 0: Flush Transmit Queue */
#define EMAC_MTL_TXQ1_OPERATION_MODE_TSF          (1 << 1) /* Bit 1: Transmit Store and Forward */
#define EMAC_MTL_TXQ1_OPERATION_MODE_TXQEN_SHIFT  (2)      /* Bits 2-4: Transmit Queue Enable */
#define EMAC_MTL_TXQ1_OPERATION_MODE_TXQEN_MASK   (0x3 << EMAC_MTL_TXQ1_OPERATION_MODE_TXQEN_SHIFT)
#define EMAC_MTL_TXQ1_OPERATION_MODE_TXQEN(n)     (((n) << EMAC_MTL_TXQ1_OPERATION_MODE_TXQEN_SHIFT) & EMAC_MTL_TXQ1_OPERATION_MODE_TXQEN_MASK)
#define EMAC_MTL_TXQ1_OPERATION_MODE_TTC_SHIFT    (4) /* Bits 4-7: Transmit Threshold Control */
#define EMAC_MTL_TXQ1_OPERATION_MODE_TTC_MASK     (0x7 << EMAC_MTL_TXQ1_OPERATION_MODE_TTC_SHIFT)
#define EMAC_MTL_TXQ1_OPERATION_MODE_TTC(n)       (((n) << EMAC_MTL_TXQ1_OPERATION_MODE_TTC_SHIFT) & EMAC_MTL_TXQ1_OPERATION_MODE_TTC_MASK)
#define EMAC_MTL_TXQ1_OPERATION_MODE_TQS_SHIFT    (16) /* Bits 16-21: Transmit Queue Size */
#define EMAC_MTL_TXQ1_OPERATION_MODE_TQS_MASK     (0x1f << EMAC_MTL_TXQ1_OPERATION_MODE_TQS_SHIFT)
#define EMAC_MTL_TXQ1_OPERATION_MODE_TQS(n)       (((n) << EMAC_MTL_TXQ1_OPERATION_MODE_TQS_SHIFT) & EMAC_MTL_TXQ1_OPERATION_MODE_TQS_MASK)

/* MTL Tx Queue 1 Underflow (MTL_TXQ1_UNDERFLOW) */
#define EMAC_MTL_TXQ1_UNDERFLOW_UFFRMCNT_SHIFT  (0) /* Bits 0-11: Underflow Packet Counter */
#define EMAC_MTL_TXQ1_UNDERFLOW_UFFRMCNT_MASK   (0x7ff << EMAC_MTL_TXQ1_UNDERFLOW_UFFRMCNT_SHIFT)
#define EMAC_MTL_TXQ1_UNDERFLOW_UFFRMCNT(n)     (((n) << EMAC_MTL_TXQ1_UNDERFLOW_UFFRMCNT_SHIFT) & EMAC_MTL_TXQ1_UNDERFLOW_UFFRMCNT_MASK)
#define EMAC_MTL_TXQ1_UNDERFLOW_UFCNTOVF        (1 << 11) /* Bit 11: Overflow Bit for Underflow Packet Counter */

/* MTL Tx Queue 1 Debug (MTL_TXQ1_DEBUG) */
#define EMAC_MTL_TXQ1_DEBUG_TXQPAUSED      (1 << 0) /* Bit 0: Transmit Queue in Pause */
#define EMAC_MTL_TXQ1_DEBUG_TRCSTS_SHIFT   (1)      /* Bits 1-3: MTL Tx Queue Read Controller Status */
#define EMAC_MTL_TXQ1_DEBUG_TRCSTS_MASK    (0x3 << EMAC_MTL_TXQ1_DEBUG_TRCSTS_SHIFT)
#define EMAC_MTL_TXQ1_DEBUG_TRCSTS(n)      (((n) << EMAC_MTL_TXQ1_DEBUG_TRCSTS_SHIFT) & EMAC_MTL_TXQ1_DEBUG_TRCSTS_MASK)
#define EMAC_MTL_TXQ1_DEBUG_TWCSTS         (1 << 3) /* Bit 3: MTL Tx Queue Write Controller Status */
#define EMAC_MTL_TXQ1_DEBUG_TXQSTS         (1 << 4) /* Bit 4: MTL Tx Queue Not Empty Status */
#define EMAC_MTL_TXQ1_DEBUG_TXSTSFSTS      (1 << 5) /* Bit 5: MTL Tx Status FIFO Full Status */
#define EMAC_MTL_TXQ1_DEBUG_PTXQ_SHIFT     (16)     /* Bits 16-19: Number of Packets in the Transmit Queue */
#define EMAC_MTL_TXQ1_DEBUG_PTXQ_MASK      (0x7 << EMAC_MTL_TXQ1_DEBUG_PTXQ_SHIFT)
#define EMAC_MTL_TXQ1_DEBUG_PTXQ(n)        (((n) << EMAC_MTL_TXQ1_DEBUG_PTXQ_SHIFT) & EMAC_MTL_TXQ1_DEBUG_PTXQ_MASK)
#define EMAC_MTL_TXQ1_DEBUG_STXSTSF_SHIFT  (20) /* Bits 20-23: Number of Status Words in Tx Status FIFO of Queue */
#define EMAC_MTL_TXQ1_DEBUG_STXSTSF_MASK   (0x7 << EMAC_MTL_TXQ1_DEBUG_STXSTSF_SHIFT)
#define EMAC_MTL_TXQ1_DEBUG_STXSTSF(n)     (((n) << EMAC_MTL_TXQ1_DEBUG_STXSTSF_SHIFT) & EMAC_MTL_TXQ1_DEBUG_STXSTSF_MASK)

/* MTL Tx Queue 1 ETS Control (MTL_TXQ1_ETS_CONTROL) */
#define EMAC_MTL_TXQ1_ETS_CONTROL_AVALG      (1 << 2) /* Bit 2: AV Algorithm */
#define EMAC_MTL_TXQ1_ETS_CONTROL_CC         (1 << 3) /* Bit 3: Credit Control */
#define EMAC_MTL_TXQ1_ETS_CONTROL_SLC_SHIFT  (4)      /* Bits 4-7: Slot Count */
#define EMAC_MTL_TXQ1_ETS_CONTROL_SLC_MASK   (0x7 << EMAC_MTL_TXQ1_ETS_CONTROL_SLC_SHIFT)
#define EMAC_MTL_TXQ1_ETS_CONTROL_SLC(n)     (((n) << EMAC_MTL_TXQ1_ETS_CONTROL_SLC_SHIFT) & EMAC_MTL_TXQ1_ETS_CONTROL_SLC_MASK)

/* MTL Tx Queue 1 ETS Status (MTL_TXQ1_ETS_STATUS) */
#define EMAC_MTL_TXQ1_ETS_STATUS_ABS_SHIFT  (0) /* Bits 0-24: Average Bits per Slot */
#define EMAC_MTL_TXQ1_ETS_STATUS_ABS_MASK   (0xffffff << EMAC_MTL_TXQ1_ETS_STATUS_ABS_SHIFT)
#define EMAC_MTL_TXQ1_ETS_STATUS_ABS(n)     (((n) << EMAC_MTL_TXQ1_ETS_STATUS_ABS_SHIFT) & EMAC_MTL_TXQ1_ETS_STATUS_ABS_MASK)

/* MTL Tx Queue 1 Quantum Weight (MTL_TXQ1_QUANTUM_WEIGHT) */
#define EMAC_MTL_TXQ1_QUANTUM_WEIGHT_ISCQW_SHIFT  (0) /* Bits 0-21: idleSlopeCredit, Quantum or Weights */
#define EMAC_MTL_TXQ1_QUANTUM_WEIGHT_ISCQW_MASK   (0x1fffff << EMAC_MTL_TXQ1_QUANTUM_WEIGHT_ISCQW_SHIFT)
#define EMAC_MTL_TXQ1_QUANTUM_WEIGHT_ISCQW(n)     (((n) << EMAC_MTL_TXQ1_QUANTUM_WEIGHT_ISCQW_SHIFT) & EMAC_MTL_TXQ1_QUANTUM_WEIGHT_ISCQW_MASK)

/* MTL Tx Queue 1 Sendslope Credit (MTL_TXQ1_SENDSLOPECREDIT) */
#define EMAC_MTL_TXQ1_SENDSLOPECREDIT_SSC_SHIFT  (0) /* Bits 0-14: sendSlopeCredit Value */
#define EMAC_MTL_TXQ1_SENDSLOPECREDIT_SSC_MASK   (0x3fff << EMAC_MTL_TXQ1_SENDSLOPECREDIT_SSC_SHIFT)
#define EMAC_MTL_TXQ1_SENDSLOPECREDIT_SSC(n)     (((n) << EMAC_MTL_TXQ1_SENDSLOPECREDIT_SSC_SHIFT) & EMAC_MTL_TXQ1_SENDSLOPECREDIT_SSC_MASK)

/* MTL Tx Queue 1 HiCredit (MTL_TXQ1_HICREDIT) */
#define EMAC_MTL_TXQ1_HICREDIT_HC_SHIFT  (0) /* Bits 0-29: hiCredit Value */
#define EMAC_MTL_TXQ1_HICREDIT_HC_MASK   (0x1fffffff << EMAC_MTL_TXQ1_HICREDIT_HC_SHIFT)
#define EMAC_MTL_TXQ1_HICREDIT_HC(n)     (((n) << EMAC_MTL_TXQ1_HICREDIT_HC_SHIFT) & EMAC_MTL_TXQ1_HICREDIT_HC_MASK)

/* MTL Tx Queue 1 LoCredit (MTL_TXQ1_LOCREDIT) */
#define EMAC_MTL_TXQ1_LOCREDIT_LC_SHIFT  (0) /* Bits 0-29: loCredit Value */
#define EMAC_MTL_TXQ1_LOCREDIT_LC_MASK   (0x1fffffff << EMAC_MTL_TXQ1_LOCREDIT_LC_SHIFT)
#define EMAC_MTL_TXQ1_LOCREDIT_LC(n)     (((n) << EMAC_MTL_TXQ1_LOCREDIT_LC_SHIFT) & EMAC_MTL_TXQ1_LOCREDIT_LC_MASK)

/* MTL Queue 1 Interrupt Control Status (MTL_Q1_INTERRUPT_CONTROL_STATUS) */
#define EMAC_MTL_Q1_INTERRUPT_CONTROL_STATUS_TXUNFIS  (1 << 0)  /* Bit 0: Transmit Queue Underflow Interrupt Status */
#define EMAC_MTL_Q1_INTERRUPT_CONTROL_STATUS_ABPSIS   (1 << 1)  /* Bit 1: Average Bits Per Slot Interrupt Status */
#define EMAC_MTL_Q1_INTERRUPT_CONTROL_STATUS_TXUIE    (1 << 8)  /* Bit 8: Transmit Queue Underflow Interrupt Enable */
#define EMAC_MTL_Q1_INTERRUPT_CONTROL_STATUS_ABPSIE   (1 << 9)  /* Bit 9: Average Bits Per Slot Interrupt Enable */
#define EMAC_MTL_Q1_INTERRUPT_CONTROL_STATUS_RXOVFIS  (1 << 16) /* Bit 16: Receive Queue Overflow Interrupt Status */
#define EMAC_MTL_Q1_INTERRUPT_CONTROL_STATUS_RXOIE    (1 << 24) /* Bit 24: Receive Queue Overflow Interrupt Enable */

/* MTL Rx Queue 1 Operation Mode (MTL_RXQ1_OPERATION_MODE) */
#define EMAC_MTL_RXQ1_OPERATION_MODE_RTC_SHIFT   (0) /* Bits 0-2: Receive Queue Threshold Control */
#define EMAC_MTL_RXQ1_OPERATION_MODE_RTC_MASK    (0x3 << EMAC_MTL_RXQ1_OPERATION_MODE_RTC_SHIFT)
#define EMAC_MTL_RXQ1_OPERATION_MODE_RTC(n)      (((n) << EMAC_MTL_RXQ1_OPERATION_MODE_RTC_SHIFT) & EMAC_MTL_RXQ1_OPERATION_MODE_RTC_MASK)
#define EMAC_MTL_RXQ1_OPERATION_MODE_FUP         (1 << 3) /* Bit 3: Forward Undersized Good Packets */
#define EMAC_MTL_RXQ1_OPERATION_MODE_FEP         (1 << 4) /* Bit 4: Forward Error Packets */
#define EMAC_MTL_RXQ1_OPERATION_MODE_RSF         (1 << 5) /* Bit 5: Receive Queue Store and Forward */
#define EMAC_MTL_RXQ1_OPERATION_MODE_DIS_TCP_EF  (1 << 6) /* Bit 6: Disable Dropping of TCP or IP Checksum Error Packets */
#define EMAC_MTL_RXQ1_OPERATION_MODE_EHFC        (1 << 7) /* Bit 7: Enable Hardware Flow Control */
#define EMAC_MTL_RXQ1_OPERATION_MODE_RFA_SHIFT   (8)      /* Bits 8-12: Threshold for Activating Flow Control (in half-duplex and full-duplex */
#define EMAC_MTL_RXQ1_OPERATION_MODE_RFA_MASK    (0xf << EMAC_MTL_RXQ1_OPERATION_MODE_RFA_SHIFT)
#define EMAC_MTL_RXQ1_OPERATION_MODE_RFA(n)      (((n) << EMAC_MTL_RXQ1_OPERATION_MODE_RFA_SHIFT) & EMAC_MTL_RXQ1_OPERATION_MODE_RFA_MASK)
#define EMAC_MTL_RXQ1_OPERATION_MODE_RFD_SHIFT   (14) /* Bits 14-18: Threshold for Deactivating Flow Control (in half-duplex and full-duplex modes) */
#define EMAC_MTL_RXQ1_OPERATION_MODE_RFD_MASK    (0xf << EMAC_MTL_RXQ1_OPERATION_MODE_RFD_SHIFT)
#define EMAC_MTL_RXQ1_OPERATION_MODE_RFD(n)      (((n) << EMAC_MTL_RXQ1_OPERATION_MODE_RFD_SHIFT) & EMAC_MTL_RXQ1_OPERATION_MODE_RFD_MASK)
#define EMAC_MTL_RXQ1_OPERATION_MODE_RQS_SHIFT   (20) /* Bits 20-25: Receive Queue Size */
#define EMAC_MTL_RXQ1_OPERATION_MODE_RQS_MASK    (0x1f << EMAC_MTL_RXQ1_OPERATION_MODE_RQS_SHIFT)
#define EMAC_MTL_RXQ1_OPERATION_MODE_RQS(n)      (((n) << EMAC_MTL_RXQ1_OPERATION_MODE_RQS_SHIFT) & EMAC_MTL_RXQ1_OPERATION_MODE_RQS_MASK)

/* MTL Rx Queue 1 Missed Packet Overflow Counter
 * (MTL_RXQ1_MISSED_PACKET_OVERFLOW_CNT)
 */

#define EMAC_MTL_RXQ1_MISSED_PACKET_OVERFLOW_CNT_OVFPKTCNT_SHIFT  (0) /* Bits 0-11: Overflow Packet Counter */
#define EMAC_MTL_RXQ1_MISSED_PACKET_OVERFLOW_CNT_OVFPKTCNT_MASK   (0x7ff << EMAC_MTL_RXQ1_MISSED_PACKET_OVERFLOW_CNT_OVFPKTCNT_SHIFT)
#define EMAC_MTL_RXQ1_MISSED_PACKET_OVERFLOW_CNT_OVFPKTCNT(n)     (((n) << EMAC_MTL_RXQ1_MISSED_PACKET_OVERFLOW_CNT_OVFPKTCNT_SHIFT) & EMAC_MTL_RXQ1_MISSED_PACKET_OVERFLOW_CNT_OVFPKTCNT_MASK)
#define EMAC_MTL_RXQ1_MISSED_PACKET_OVERFLOW_CNT_OVFCNTOVF        (1 << 11) /* Bit 11: Overflow Counter Overflow Bit */
#define EMAC_MTL_RXQ1_MISSED_PACKET_OVERFLOW_CNT_MISPKTCNT_SHIFT  (16)      /* Bits 16-27: Missed Packet Counter */
#define EMAC_MTL_RXQ1_MISSED_PACKET_OVERFLOW_CNT_MISPKTCNT_MASK   (0x7ff << EMAC_MTL_RXQ1_MISSED_PACKET_OVERFLOW_CNT_MISPKTCNT_SHIFT)
#define EMAC_MTL_RXQ1_MISSED_PACKET_OVERFLOW_CNT_MISPKTCNT(n)     (((n) << EMAC_MTL_RXQ1_MISSED_PACKET_OVERFLOW_CNT_MISPKTCNT_SHIFT) & EMAC_MTL_RXQ1_MISSED_PACKET_OVERFLOW_CNT_MISPKTCNT_MASK)
#define EMAC_MTL_RXQ1_MISSED_PACKET_OVERFLOW_CNT_MISCNTOVF        (1 << 27) /* Bit 27: Missed Packet Counter Overflow Bit */

/* MTL Rx Queue 1 Debug (MTL_RXQ1_DEBUG) */
#define EMAC_MTL_RXQ1_DEBUG_RWCSTS        (1 << 0) /* Bit 0: MTL Rx Queue Write Controller Active Status */
#define EMAC_MTL_RXQ1_DEBUG_RRCSTS_SHIFT  (1)      /* Bits 1-3: MTL Rx Queue Read Controller State */
#define EMAC_MTL_RXQ1_DEBUG_RRCSTS_MASK   (0x3 << EMAC_MTL_RXQ1_DEBUG_RRCSTS_SHIFT)
#define EMAC_MTL_RXQ1_DEBUG_RRCSTS(n)     (((n) << EMAC_MTL_RXQ1_DEBUG_RRCSTS_SHIFT) & EMAC_MTL_RXQ1_DEBUG_RRCSTS_MASK)
#define EMAC_MTL_RXQ1_DEBUG_RXQSTS_SHIFT  (4) /* Bits 4-6: MTL Rx Queue Fill-Level Status */
#define EMAC_MTL_RXQ1_DEBUG_RXQSTS_MASK   (0x3 << EMAC_MTL_RXQ1_DEBUG_RXQSTS_SHIFT)
#define EMAC_MTL_RXQ1_DEBUG_RXQSTS(n)     (((n) << EMAC_MTL_RXQ1_DEBUG_RXQSTS_SHIFT) & EMAC_MTL_RXQ1_DEBUG_RXQSTS_MASK)
#define EMAC_MTL_RXQ1_DEBUG_PRXQ_SHIFT    (16) /* Bits 16-30: Number of Packets in Receive Queue */
#define EMAC_MTL_RXQ1_DEBUG_PRXQ_MASK     (0x3fff << EMAC_MTL_RXQ1_DEBUG_PRXQ_SHIFT)
#define EMAC_MTL_RXQ1_DEBUG_PRXQ(n)       (((n) << EMAC_MTL_RXQ1_DEBUG_PRXQ_SHIFT) & EMAC_MTL_RXQ1_DEBUG_PRXQ_MASK)

/* MTL Rx Queue 1 Control (MTL_RXQ1_CONTROL) */
#define EMAC_MTL_RXQ1_CONTROL_RXQ_WEGT_SHIFT  (0) /* Bits 0-3: Receive Queue Weight */
#define EMAC_MTL_RXQ1_CONTROL_RXQ_WEGT_MASK   (0x7 << EMAC_MTL_RXQ1_CONTROL_RXQ_WEGT_SHIFT)
#define EMAC_MTL_RXQ1_CONTROL_RXQ_WEGT(n)     (((n) << EMAC_MTL_RXQ1_CONTROL_RXQ_WEGT_SHIFT) & EMAC_MTL_RXQ1_CONTROL_RXQ_WEGT_MASK)
#define EMAC_MTL_RXQ1_CONTROL_RXQ_FRM_ARBIT   (1 << 3) /* Bit 3: Receive Queue Packet Arbitration */

/* DMA Mode (DMA_MODE) */
#define EMAC_DMA_MODE_SWR         (1 << 0) /* Bit 0: Software Reset */
#define EMAC_DMA_MODE_DA          (1 << 1) /* Bit 1: DMA Tx or Rx Arbitration Scheme */
#define EMAC_DMA_MODE_TAA_SHIFT   (2)      /* Bits 2-5: Transmit Arbitration Algorithm */
#define EMAC_DMA_MODE_TAA_MASK    (0x7 << EMAC_DMA_MODE_TAA_SHIFT)
#define EMAC_DMA_MODE_TAA(n)      (((n) << EMAC_DMA_MODE_TAA_SHIFT) & EMAC_DMA_MODE_TAA_MASK)
#define EMAC_DMA_MODE_ARBC        (1 << 9)  /* Bit 9: Is reserved for NXP internal use */
#define EMAC_DMA_MODE_TXPR        (1 << 11) /* Bit 11: Transmit Priority */
#define EMAC_DMA_MODE_PR_SHIFT    (12)      /* Bits 12-15: Priority Ratio */
#define EMAC_DMA_MODE_PR_MASK     (0x7 << EMAC_DMA_MODE_PR_SHIFT)
#define EMAC_DMA_MODE_PR(n)       (((n) << EMAC_DMA_MODE_PR_SHIFT) & EMAC_DMA_MODE_PR_MASK)
#define EMAC_DMA_MODE_INTM_SHIFT  (16) /* Bits 16-18: Interrupt Mode */
#define EMAC_DMA_MODE_INTM_MASK   (0x3 << EMAC_DMA_MODE_INTM_SHIFT)
#define EMAC_DMA_MODE_INTM(n)     (((n) << EMAC_DMA_MODE_INTM_SHIFT) & EMAC_DMA_MODE_INTM_MASK)

/* DMA System Bus Mode (DMA_SYSBUS_MODE) */
#define EMAC_DMA_SYSBUS_MODE_FB   (1 << 0)  /* Bit 0: Fixed Burst Length */
#define EMAC_DMA_SYSBUS_MODE_AAL  (1 << 12) /* Bit 12: Address-Aligned Beats */
#define EMAC_DMA_SYSBUS_MODE_MB   (1 << 14) /* Bit 14: Mixed Burst */
#define EMAC_DMA_SYSBUS_MODE_RB   (1 << 15) /* Bit 15: Rebuild INCRx Burst */

/* DMA Interrupt Status (DMA_INTERRUPT_STATUS) */
#define EMAC_DMA_INTERRUPT_STATUS_DC0IS  (1 << 0)  /* Bit 0: DMA Channel 0 Interrupt Status */
#define EMAC_DMA_INTERRUPT_STATUS_DC1IS  (1 << 1)  /* Bit 1: DMA Channel 1 Interrupt Status */
#define EMAC_DMA_INTERRUPT_STATUS_MTLIS  (1 << 16) /* Bit 16: MTL Interrupt Status */
#define EMAC_DMA_INTERRUPT_STATUS_MACIS  (1 << 17) /* Bit 17: MAC Interrupt Status */

/* DMA Debug Status 0 (DMA_DEBUG_STATUS0) */
#define EMAC_DMA_DEBUG_STATUS0_AXWHSTS     (1 << 0) /* Bit 0: AHB Master Status */
#define EMAC_DMA_DEBUG_STATUS0_RPS0_SHIFT  (8)      /* Bits 8-12: DMA Channel 0 Receive Process State */
#define EMAC_DMA_DEBUG_STATUS0_RPS0_MASK   (0xf << EMAC_DMA_DEBUG_STATUS0_RPS0_SHIFT)
#define EMAC_DMA_DEBUG_STATUS0_RPS0(n)     (((n) << EMAC_DMA_DEBUG_STATUS0_RPS0_SHIFT) & EMAC_DMA_DEBUG_STATUS0_RPS0_MASK)
#define EMAC_DMA_DEBUG_STATUS0_TPS0_SHIFT  (12) /* Bits 12-16: DMA Channel 0 Transmit Process State */
#define EMAC_DMA_DEBUG_STATUS0_TPS0_MASK   (0xf << EMAC_DMA_DEBUG_STATUS0_TPS0_SHIFT)
#define EMAC_DMA_DEBUG_STATUS0_TPS0(n)     (((n) << EMAC_DMA_DEBUG_STATUS0_TPS0_SHIFT) & EMAC_DMA_DEBUG_STATUS0_TPS0_MASK)
#define EMAC_DMA_DEBUG_STATUS0_RPS1_SHIFT  (16) /* Bits 16-20: DMA Channel 1 Receive Process State */
#define EMAC_DMA_DEBUG_STATUS0_RPS1_MASK   (0xf << EMAC_DMA_DEBUG_STATUS0_RPS1_SHIFT)
#define EMAC_DMA_DEBUG_STATUS0_RPS1(n)     (((n) << EMAC_DMA_DEBUG_STATUS0_RPS1_SHIFT) & EMAC_DMA_DEBUG_STATUS0_RPS1_MASK)
#define EMAC_DMA_DEBUG_STATUS0_TPS1_SHIFT  (20) /* Bits 20-24: DMA Channel 1 Transmit Process State */
#define EMAC_DMA_DEBUG_STATUS0_TPS1_MASK   (0xf << EMAC_DMA_DEBUG_STATUS0_TPS1_SHIFT)
#define EMAC_DMA_DEBUG_STATUS0_TPS1(n)     (((n) << EMAC_DMA_DEBUG_STATUS0_TPS1_SHIFT) & EMAC_DMA_DEBUG_STATUS0_TPS1_MASK)

/* DMA TBS Control (DMA_TBS_CTRL) */
#define EMAC_DMA_TBS_CTRL_FTOV        (1 << 0) /* Bit 0: Fetch Time Offset Valid */
#define EMAC_DMA_TBS_CTRL_FGOS_SHIFT  (4)      /* Bits 4-7: Fetch GSN Offset */
#define EMAC_DMA_TBS_CTRL_FGOS_MASK   (0x7 << EMAC_DMA_TBS_CTRL_FGOS_SHIFT)
#define EMAC_DMA_TBS_CTRL_FGOS(n)     (((n) << EMAC_DMA_TBS_CTRL_FGOS_SHIFT) & EMAC_DMA_TBS_CTRL_FGOS_MASK)
#define EMAC_DMA_TBS_CTRL_FTOS_SHIFT  (8) /* Bits 8-32: Fetch Time Offset */
#define EMAC_DMA_TBS_CTRL_FTOS_MASK   (0xffffff << EMAC_DMA_TBS_CTRL_FTOS_SHIFT)
#define EMAC_DMA_TBS_CTRL_FTOS(n)     (((n) << EMAC_DMA_TBS_CTRL_FTOS_SHIFT) & EMAC_DMA_TBS_CTRL_FTOS_MASK)

/* DMA Safety Interrupt Status (DMA_SAFETY_INTERRUPT_STATUS) */
#define EMAC_DMA_SAFETY_INTERRUPT_STATUS_DECIS  (1 << 0)  /* Bit 0: DMA ECC Correctable Error Interrupt Status */
#define EMAC_DMA_SAFETY_INTERRUPT_STATUS_DEUIS  (1 << 1)  /* Bit 1: DMA ECC Uncorrectable Error Interrupt Status */
#define EMAC_DMA_SAFETY_INTERRUPT_STATUS_MSCIS  (1 << 28) /* Bit 28: MTL Safety Correctable Error Interrupt Status */
#define EMAC_DMA_SAFETY_INTERRUPT_STATUS_MSUIS  (1 << 29) /* Bit 29: MTL Safety Uncorrectable Error Interrupt Status */
#define EMAC_DMA_SAFETY_INTERRUPT_STATUS_MCSIS  (1 << 31) /* Bit 31: MAC Safety Uncorrectable Interrupt Status */

/* DMA Channel 0 Control (DMA_CH0_CONTROL) */
#define EMAC_DMA_CH0_CONTROL_PBLX8      (1 << 16) /* Bit 16: 8xPBL mode */
#define EMAC_DMA_CH0_CONTROL_DSL_SHIFT  (18)      /* Bits 18-21: Descriptor Skip Length */
#define EMAC_DMA_CH0_CONTROL_DSL_MASK   (0x7 << EMAC_DMA_CH0_CONTROL_DSL_SHIFT)
#define EMAC_DMA_CH0_CONTROL_DSL(n)     (((n) << EMAC_DMA_CH0_CONTROL_DSL_SHIFT) & EMAC_DMA_CH0_CONTROL_DSL_MASK)

/* DMA Channel Tx Control (DMA_CH0_TX_CONTROL) */
#define EMAC_DMA_CH0_TX_CONTROL_ST           (1 << 0) /* Bit 0: Start or Stop Transmission Command */
#define EMAC_DMA_CH0_TX_CONTROL_TCW_SHIFT    (1)      /* Bits 1-4: Transmit Channel Weight */
#define EMAC_DMA_CH0_TX_CONTROL_TCW_MASK     (0x7 << EMAC_DMA_CH0_TX_CONTROL_TCW_SHIFT)
#define EMAC_DMA_CH0_TX_CONTROL_TCW(n)       (((n) << EMAC_DMA_CH0_TX_CONTROL_TCW_SHIFT) & EMAC_DMA_CH0_TX_CONTROL_TCW_MASK)
#define EMAC_DMA_CH0_TX_CONTROL_OSF          (1 << 4) /* Bit 4: Operate on Second Packet */
#define EMAC_DMA_CH0_TX_CONTROL_TXPBL_SHIFT  (16)     /* Bits 16-22: Transmit Programmable Burst Length */
#define EMAC_DMA_CH0_TX_CONTROL_TXPBL_MASK   (0x3f << EMAC_DMA_CH0_TX_CONTROL_TXPBL_SHIFT)
#define EMAC_DMA_CH0_TX_CONTROL_TXPBL(n)     (((n) << EMAC_DMA_CH0_TX_CONTROL_TXPBL_SHIFT) & EMAC_DMA_CH0_TX_CONTROL_TXPBL_MASK)
#define EMAC_DMA_CH0_TX_CONTROL_ETIC         (1 << 22) /* Bit 22: Early Transmit Interrupt Control */
#define EMAC_DMA_CH0_TX_CONTROL_EDSE         (1 << 28) /* Bit 28: Enhanced Descriptor Enable */

/* DMA Channel Rx Control (DMA_CH0_RX_CONTROL) */
#define EMAC_DMA_CH0_RX_CONTROL_SR               (1 << 0) /* Bit 0: Start or Stop Receive */
#define EMAC_DMA_CH0_RX_CONTROL_RBSZ_X_0_SHIFT   (1)      /* Bits 1-3: Receive Buffer size Low */
#define EMAC_DMA_CH0_RX_CONTROL_RBSZ_X_0_MASK    (0x3 << EMAC_DMA_CH0_RX_CONTROL_RBSZ_X_0_SHIFT)
#define EMAC_DMA_CH0_RX_CONTROL_RBSZ_X_0(n)      (((n) << EMAC_DMA_CH0_RX_CONTROL_RBSZ_X_0_SHIFT) & EMAC_DMA_CH0_RX_CONTROL_RBSZ_X_0_MASK)
#define EMAC_DMA_CH0_RX_CONTROL_RBSZ_13_Y_SHIFT  (3) /* Bits 3-15: Receive Buffer size High */
#define EMAC_DMA_CH0_RX_CONTROL_RBSZ_13_Y_MASK   (0xfff << EMAC_DMA_CH0_RX_CONTROL_RBSZ_13_Y_SHIFT)
#define EMAC_DMA_CH0_RX_CONTROL_RBSZ_13_Y(n)     (((n) << EMAC_DMA_CH0_RX_CONTROL_RBSZ_13_Y_SHIFT) & EMAC_DMA_CH0_RX_CONTROL_RBSZ_13_Y_MASK)
#define EMAC_DMA_CH0_RX_CONTROL_RXPBL_SHIFT      (16) /* Bits 16-22: Receive Programmable Burst Length */
#define EMAC_DMA_CH0_RX_CONTROL_RXPBL_MASK       (0x3f << EMAC_DMA_CH0_RX_CONTROL_RXPBL_SHIFT)
#define EMAC_DMA_CH0_RX_CONTROL_RXPBL(n)         (((n) << EMAC_DMA_CH0_RX_CONTROL_RXPBL_SHIFT) & EMAC_DMA_CH0_RX_CONTROL_RXPBL_MASK)
#define EMAC_DMA_CH0_RX_CONTROL_ERIC             (1 << 22) /* Bit 22: Early Receive Interrupt Control */
#define EMAC_DMA_CH0_RX_CONTROL_RPF              (1 << 31) /* Bit 31: Rx Packet Flush */

/* DMA Channel 0 Tx Descriptor List Address (DMA_CH0_TXDESC_LIST_ADDRESS) */
#define EMAC_DMA_CH0_TXDESC_LIST_ADDRESS_TDESLA_SHIFT  (2) /* Bits 2-32: Start of Transmit List */
#define EMAC_DMA_CH0_TXDESC_LIST_ADDRESS_TDESLA_MASK   (0x3fffffff << EMAC_DMA_CH0_TXDESC_LIST_ADDRESS_TDESLA_SHIFT)
#define EMAC_DMA_CH0_TXDESC_LIST_ADDRESS_TDESLA(n)     (((n) << EMAC_DMA_CH0_TXDESC_LIST_ADDRESS_TDESLA_SHIFT) & EMAC_DMA_CH0_TXDESC_LIST_ADDRESS_TDESLA_MASK)

/* DMA Channel 0 Rx Descriptor List Address (DMA_CH0_RXDESC_LIST_ADDRESS) */
#define EMAC_DMA_CH0_RXDESC_LIST_ADDRESS_RDESLA_SHIFT  (2) /* Bits 2-32: Start of Receive List */
#define EMAC_DMA_CH0_RXDESC_LIST_ADDRESS_RDESLA_MASK   (0x3fffffff << EMAC_DMA_CH0_RXDESC_LIST_ADDRESS_RDESLA_SHIFT)
#define EMAC_DMA_CH0_RXDESC_LIST_ADDRESS_RDESLA(n)     (((n) << EMAC_DMA_CH0_RXDESC_LIST_ADDRESS_RDESLA_SHIFT) & EMAC_DMA_CH0_RXDESC_LIST_ADDRESS_RDESLA_MASK)

/* DMA Channel 0 Tx Descriptor Tail Pointer (DMA_CH0_TXDESC_TAIL_POINTER) */
#define EMAC_DMA_CH0_TXDESC_TAIL_POINTER_TDTP_SHIFT  (2) /* Bits 2-32: Transmit Descriptor Tail Pointer */
#define EMAC_DMA_CH0_TXDESC_TAIL_POINTER_TDTP_MASK   (0x3fffffff << EMAC_DMA_CH0_TXDESC_TAIL_POINTER_TDTP_SHIFT)
#define EMAC_DMA_CH0_TXDESC_TAIL_POINTER_TDTP(n)     (((n) << EMAC_DMA_CH0_TXDESC_TAIL_POINTER_TDTP_SHIFT) & EMAC_DMA_CH0_TXDESC_TAIL_POINTER_TDTP_MASK)

/* DMA Channeli 0 Rx Descriptor List Pointer (DMA_CH0_RXDESC_TAIL_POINTER) */
#define EMAC_DMA_CH0_RXDESC_TAIL_POINTER_RDTP_SHIFT  (2) /* Bits 2-32: Receive Descriptor Tail Pointer */
#define EMAC_DMA_CH0_RXDESC_TAIL_POINTER_RDTP_MASK   (0x3fffffff << EMAC_DMA_CH0_RXDESC_TAIL_POINTER_RDTP_SHIFT)
#define EMAC_DMA_CH0_RXDESC_TAIL_POINTER_RDTP(n)     (((n) << EMAC_DMA_CH0_RXDESC_TAIL_POINTER_RDTP_SHIFT) & EMAC_DMA_CH0_RXDESC_TAIL_POINTER_RDTP_MASK)

/* DMA Channel 0 Tx Descriptor Ring Length (DMA_CH0_TXDESC_RING_LENGTH) */
#define EMAC_DMA_CH0_TXDESC_RING_LENGTH_TDRL_SHIFT  (0) /* Bits 0-10: Transmit Descriptor Ring Length */
#define EMAC_DMA_CH0_TXDESC_RING_LENGTH_TDRL_MASK   (0x3ff << EMAC_DMA_CH0_TXDESC_RING_LENGTH_TDRL_SHIFT)
#define EMAC_DMA_CH0_TXDESC_RING_LENGTH_TDRL(n)     (((n) << EMAC_DMA_CH0_TXDESC_RING_LENGTH_TDRL_SHIFT) & EMAC_DMA_CH0_TXDESC_RING_LENGTH_TDRL_MASK)

/* DMA Channel 0 Rx Descriptor Ring Length (DMA_CH0_RXDESC_RING_LENGTH) */
#define EMAC_DMA_CH0_RXDESC_RING_LENGTH_RDRL_SHIFT  (0) /* Bits 0-10: Receive Descriptor Ring Length */
#define EMAC_DMA_CH0_RXDESC_RING_LENGTH_RDRL_MASK   (0x3ff << EMAC_DMA_CH0_RXDESC_RING_LENGTH_RDRL_SHIFT)
#define EMAC_DMA_CH0_RXDESC_RING_LENGTH_RDRL(n)     (((n) << EMAC_DMA_CH0_RXDESC_RING_LENGTH_RDRL_SHIFT) & EMAC_DMA_CH0_RXDESC_RING_LENGTH_RDRL_MASK)

/* DMA Channel 0 Interrupt Enable (DMA_CH0_INTERRUPT_ENABLE) */
#define EMAC_DMA_CH0_INTERRUPT_ENABLE_TIE   (1 << 0)  /* Bit 0: Transmit Interrupt Enable */
#define EMAC_DMA_CH0_INTERRUPT_ENABLE_TXSE  (1 << 1)  /* Bit 1: Transmit Stopped Enable */
#define EMAC_DMA_CH0_INTERRUPT_ENABLE_TBUE  (1 << 2)  /* Bit 2: Transmit Buffer Unavailable Enable */
#define EMAC_DMA_CH0_INTERRUPT_ENABLE_RIE   (1 << 6)  /* Bit 6: Receive Interrupt Enable */
#define EMAC_DMA_CH0_INTERRUPT_ENABLE_RBUE  (1 << 7)  /* Bit 7: Receive Buffer Unavailable Enable */
#define EMAC_DMA_CH0_INTERRUPT_ENABLE_RSE   (1 << 8)  /* Bit 8: Receive Stopped Enable */
#define EMAC_DMA_CH0_INTERRUPT_ENABLE_RWTE  (1 << 9)  /* Bit 9: Receive Watchdog Timeout Enable */
#define EMAC_DMA_CH0_INTERRUPT_ENABLE_ETIE  (1 << 10) /* Bit 10: Early Transmit Interrupt Enable */
#define EMAC_DMA_CH0_INTERRUPT_ENABLE_ERIE  (1 << 11) /* Bit 11: Early Receive Interrupt Enable */
#define EMAC_DMA_CH0_INTERRUPT_ENABLE_FBEE  (1 << 12) /* Bit 12: Fatal Bus Error Enable */
#define EMAC_DMA_CH0_INTERRUPT_ENABLE_CDEE  (1 << 13) /* Bit 13: Context Descriptor Error Enable */
#define EMAC_DMA_CH0_INTERRUPT_ENABLE_AIE   (1 << 14) /* Bit 14: Abnormal Interrupt Summary Enable */
#define EMAC_DMA_CH0_INTERRUPT_ENABLE_NIE   (1 << 15) /* Bit 15: Normal Interrupt Summary Enable */

/* DMA Channel 0 Rx Interrupt Watchdog Timer
 * (DMA_CH0_RX_INTERRUPT_WATCHDOG_TIMER)
 */

#define EMAC_DMA_CH0_RX_INTERRUPT_WATCHDOG_TIMER_RWT_SHIFT   (0) /* Bits 0-8: Receive Interrupt Watchdog Timer Count */
#define EMAC_DMA_CH0_RX_INTERRUPT_WATCHDOG_TIMER_RWT_MASK    (0xff << EMAC_DMA_CH0_RX_INTERRUPT_WATCHDOG_TIMER_RWT_SHIFT)
#define EMAC_DMA_CH0_RX_INTERRUPT_WATCHDOG_TIMER_RWT(n)      (((n) << EMAC_DMA_CH0_RX_INTERRUPT_WATCHDOG_TIMER_RWT_SHIFT) & EMAC_DMA_CH0_RX_INTERRUPT_WATCHDOG_TIMER_RWT_MASK)
#define EMAC_DMA_CH0_RX_INTERRUPT_WATCHDOG_TIMER_RWTU_SHIFT  (16) /* Bits 16-18: Receive Interrupt Watchdog Timer Count Units */
#define EMAC_DMA_CH0_RX_INTERRUPT_WATCHDOG_TIMER_RWTU_MASK   (0x3 << EMAC_DMA_CH0_RX_INTERRUPT_WATCHDOG_TIMER_RWTU_SHIFT)
#define EMAC_DMA_CH0_RX_INTERRUPT_WATCHDOG_TIMER_RWTU(n)     (((n) << EMAC_DMA_CH0_RX_INTERRUPT_WATCHDOG_TIMER_RWTU_SHIFT) & EMAC_DMA_CH0_RX_INTERRUPT_WATCHDOG_TIMER_RWTU_MASK)

/* DMA Channel 0 Slot Function Control Status
 * (DMA_CH0_SLOT_FUNCTION_CONTROL_STATUS)
 */

#define EMAC_DMA_CH0_SLOT_FUNCTION_CONTROL_STATUS_ESC        (1 << 0) /* Bit 0: Enable Slot Comparison */
#define EMAC_DMA_CH0_SLOT_FUNCTION_CONTROL_STATUS_ASC        (1 << 1) /* Bit 1: Advance Slot Check */
#define EMAC_DMA_CH0_SLOT_FUNCTION_CONTROL_STATUS_SIV_SHIFT  (4)      /* Bits 4-16: Slot Interval Value */
#define EMAC_DMA_CH0_SLOT_FUNCTION_CONTROL_STATUS_SIV_MASK   (0xfff << EMAC_DMA_CH0_SLOT_FUNCTION_CONTROL_STATUS_SIV_SHIFT)
#define EMAC_DMA_CH0_SLOT_FUNCTION_CONTROL_STATUS_SIV(n)     (((n) << EMAC_DMA_CH0_SLOT_FUNCTION_CONTROL_STATUS_SIV_SHIFT) & EMAC_DMA_CH0_SLOT_FUNCTION_CONTROL_STATUS_SIV_MASK)
#define EMAC_DMA_CH0_SLOT_FUNCTION_CONTROL_STATUS_RSN_SHIFT  (16) /* Bits 16-20: Reference Slot Number */
#define EMAC_DMA_CH0_SLOT_FUNCTION_CONTROL_STATUS_RSN_MASK   (0xf << EMAC_DMA_CH0_SLOT_FUNCTION_CONTROL_STATUS_RSN_SHIFT)
#define EMAC_DMA_CH0_SLOT_FUNCTION_CONTROL_STATUS_RSN(n)     (((n) << EMAC_DMA_CH0_SLOT_FUNCTION_CONTROL_STATUS_RSN_SHIFT) & EMAC_DMA_CH0_SLOT_FUNCTION_CONTROL_STATUS_RSN_MASK)

/* DMA Channel 0 Current Application Transmit Descriptor
 * (DMA_CH0_CURRENT_APP_TXDESC)
 */

#define EMAC_DMA_CH0_CURRENT_APP_TXDESC_CURTDESAPTR_SHIFT  (0) /* Bits 0-32: Application Transmit Descriptor Address Pointer */
#define EMAC_DMA_CH0_CURRENT_APP_TXDESC_CURTDESAPTR_MASK   (0xffffffff << EMAC_DMA_CH0_CURRENT_APP_TXDESC_CURTDESAPTR_SHIFT)
#define EMAC_DMA_CH0_CURRENT_APP_TXDESC_CURTDESAPTR(n)     (((n) << EMAC_DMA_CH0_CURRENT_APP_TXDESC_CURTDESAPTR_SHIFT) & EMAC_DMA_CH0_CURRENT_APP_TXDESC_CURTDESAPTR_MASK)

/* DMA Channel 0 Current Application Receive Descriptor
 * (DMA_CH0_CURRENT_APP_RXDESC)
 */

#define EMAC_DMA_CH0_CURRENT_APP_RXDESC_CURRDESAPTR_SHIFT  (0) /* Bits 0-32: Application Receive Descriptor Address Pointer */
#define EMAC_DMA_CH0_CURRENT_APP_RXDESC_CURRDESAPTR_MASK   (0xffffffff << EMAC_DMA_CH0_CURRENT_APP_RXDESC_CURRDESAPTR_SHIFT)
#define EMAC_DMA_CH0_CURRENT_APP_RXDESC_CURRDESAPTR(n)     (((n) << EMAC_DMA_CH0_CURRENT_APP_RXDESC_CURRDESAPTR_SHIFT) & EMAC_DMA_CH0_CURRENT_APP_RXDESC_CURRDESAPTR_MASK)

/* DMA Channel 0 Current Application Transmit Descriptor
 * (DMA_CH0_CURRENT_APP_TXBUFFER)
 */

#define EMAC_DMA_CH0_CURRENT_APP_TXBUFFER_CURTBUFAPTR_SHIFT  (0) /* Bits 0-32: Application Transmit Buffer Address Pointer */
#define EMAC_DMA_CH0_CURRENT_APP_TXBUFFER_CURTBUFAPTR_MASK   (0xffffffff << EMAC_DMA_CH0_CURRENT_APP_TXBUFFER_CURTBUFAPTR_SHIFT)
#define EMAC_DMA_CH0_CURRENT_APP_TXBUFFER_CURTBUFAPTR(n)     (((n) << EMAC_DMA_CH0_CURRENT_APP_TXBUFFER_CURTBUFAPTR_SHIFT) & EMAC_DMA_CH0_CURRENT_APP_TXBUFFER_CURTBUFAPTR_MASK)

/* DMA Channel 0 Current Application Receive Buffer
 * (DMA_CH0_CURRENT_APP_RXBUFFER)
 */

#define EMAC_DMA_CH0_CURRENT_APP_RXBUFFER_CURRBUFAPTR_SHIFT  (0) /* Bits 0-32: Application Receive Buffer Address Pointer */
#define EMAC_DMA_CH0_CURRENT_APP_RXBUFFER_CURRBUFAPTR_MASK   (0xffffffff << EMAC_DMA_CH0_CURRENT_APP_RXBUFFER_CURRBUFAPTR_SHIFT)
#define EMAC_DMA_CH0_CURRENT_APP_RXBUFFER_CURRBUFAPTR(n)     (((n) << EMAC_DMA_CH0_CURRENT_APP_RXBUFFER_CURRBUFAPTR_SHIFT) & EMAC_DMA_CH0_CURRENT_APP_RXBUFFER_CURRBUFAPTR_MASK)

/* DMA Channel 0 Status (DMA_CH0_STATUS) */
#define EMAC_DMA_CH0_STATUS_TI         (1 << 0)  /* Bit 0: Transmit Interrupt */
#define EMAC_DMA_CH0_STATUS_TPS        (1 << 1)  /* Bit 1: Transmit Process Stopped */
#define EMAC_DMA_CH0_STATUS_TBU        (1 << 2)  /* Bit 2: Transmit Buffer Unavailable */
#define EMAC_DMA_CH0_STATUS_RI         (1 << 6)  /* Bit 6: Receive Interrupt */
#define EMAC_DMA_CH0_STATUS_RBU        (1 << 7)  /* Bit 7: Receive Buffer Unavailable */
#define EMAC_DMA_CH0_STATUS_RPS        (1 << 8)  /* Bit 8: Receive Process Stopped */
#define EMAC_DMA_CH0_STATUS_RWT        (1 << 9)  /* Bit 9: Receive Watchdog Timeout */
#define EMAC_DMA_CH0_STATUS_ETI        (1 << 10) /* Bit 10: Early Transmit Interrupt */
#define EMAC_DMA_CH0_STATUS_ERI        (1 << 11) /* Bit 11: Early Receive Interrupt */
#define EMAC_DMA_CH0_STATUS_FBE        (1 << 12) /* Bit 12: Fatal Bus Error */
#define EMAC_DMA_CH0_STATUS_CDE        (1 << 13) /* Bit 13: Context Descriptor Error */
#define EMAC_DMA_CH0_STATUS_AIS        (1 << 14) /* Bit 14: Abnormal Interrupt Summary */
#define EMAC_DMA_CH0_STATUS_NIS        (1 << 15) /* Bit 15: Normal Interrupt Summary */
#define EMAC_DMA_CH0_STATUS_TEB_SHIFT  (16)      /* Bits 16-19: Tx DMA Error Bits */
#define EMAC_DMA_CH0_STATUS_TEB_MASK   (0x7 << EMAC_DMA_CH0_STATUS_TEB_SHIFT)
#define EMAC_DMA_CH0_STATUS_TEB(n)     (((n) << EMAC_DMA_CH0_STATUS_TEB_SHIFT) & EMAC_DMA_CH0_STATUS_TEB_MASK)
#define EMAC_DMA_CH0_STATUS_REB_SHIFT  (19) /* Bits 19-22: Rx DMA Error Bits */
#define EMAC_DMA_CH0_STATUS_REB_MASK   (0x7 << EMAC_DMA_CH0_STATUS_REB_SHIFT)
#define EMAC_DMA_CH0_STATUS_REB(n)     (((n) << EMAC_DMA_CH0_STATUS_REB_SHIFT) & EMAC_DMA_CH0_STATUS_REB_MASK)

/* DMA Channel 0 Miss Frame Counter (DMA_CH0_MISS_FRAME_CNT) */
#define EMAC_DMA_CH0_MISS_FRAME_CNT_MFC_SHIFT  (0) /* Bits 0-11: Dropped Packet Counters Indicates the number of packet counters that DMA drops either because of bus error or because of programing RPF field in DMA_CH${i}_Rx_Control register */
#define EMAC_DMA_CH0_MISS_FRAME_CNT_MFC_MASK   (0x7ff << EMAC_DMA_CH0_MISS_FRAME_CNT_MFC_SHIFT)
#define EMAC_DMA_CH0_MISS_FRAME_CNT_MFC(n)     (((n) << EMAC_DMA_CH0_MISS_FRAME_CNT_MFC_SHIFT) & EMAC_DMA_CH0_MISS_FRAME_CNT_MFC_MASK)
#define EMAC_DMA_CH0_MISS_FRAME_CNT_MFCO       (1 << 15) /* Bit 15: Overflow status of the MFC Counter */

/* DMA Channel 0 Rx Parser Accept Count (DMA_CH0_RXP_ACCEPT_CNT) */
#define EMAC_DMA_CH0_RXP_ACCEPT_CNT_RXPAC_SHIFT  (0) /* Bits 0-31: Rx Parser Accept Counter */
#define EMAC_DMA_CH0_RXP_ACCEPT_CNT_RXPAC_MASK   (0x7fffffff << EMAC_DMA_CH0_RXP_ACCEPT_CNT_RXPAC_SHIFT)
#define EMAC_DMA_CH0_RXP_ACCEPT_CNT_RXPAC(n)     (((n) << EMAC_DMA_CH0_RXP_ACCEPT_CNT_RXPAC_SHIFT) & EMAC_DMA_CH0_RXP_ACCEPT_CNT_RXPAC_MASK)
#define EMAC_DMA_CH0_RXP_ACCEPT_CNT_RXPACOF      (1 << 31) /* Bit 31: Rx Parser Accept Counter Overflow Bit */

/* DMA Channel 0 Rx ERI Count (DMA_CH0_RX_ERI_CNT) */
#define EMAC_DMA_CH0_RX_ERI_CNT_ECNT_SHIFT  (0) /* Bits 0-12: ERI Counter */
#define EMAC_DMA_CH0_RX_ERI_CNT_ECNT_MASK   (0xfff << EMAC_DMA_CH0_RX_ERI_CNT_ECNT_SHIFT)
#define EMAC_DMA_CH0_RX_ERI_CNT_ECNT(n)     (((n) << EMAC_DMA_CH0_RX_ERI_CNT_ECNT_SHIFT) & EMAC_DMA_CH0_RX_ERI_CNT_ECNT_MASK)

/* DMA Channel 1 Control (DMA_CH1_CONTROL) */
#define EMAC_DMA_CH1_CONTROL_PBLX8      (1 << 16) /* Bit 16: 8xPBL mode */
#define EMAC_DMA_CH1_CONTROL_DSL_SHIFT  (18)      /* Bits 18-21: Descriptor Skip Length */
#define EMAC_DMA_CH1_CONTROL_DSL_MASK   (0x7 << EMAC_DMA_CH1_CONTROL_DSL_SHIFT)
#define EMAC_DMA_CH1_CONTROL_DSL(n)     (((n) << EMAC_DMA_CH1_CONTROL_DSL_SHIFT) & EMAC_DMA_CH1_CONTROL_DSL_MASK)

/* DMA Channel 1 Tx Control (DMA_CH1_TX_CONTROL) */
#define EMAC_DMA_CH1_TX_CONTROL_ST           (1 << 0) /* Bit 0: Start or Stop Transmission Command */
#define EMAC_DMA_CH1_TX_CONTROL_TCW_SHIFT    (1)      /* Bits 1-4: Transmit Channel Weight */
#define EMAC_DMA_CH1_TX_CONTROL_TCW_MASK     (0x7 << EMAC_DMA_CH1_TX_CONTROL_TCW_SHIFT)
#define EMAC_DMA_CH1_TX_CONTROL_TCW(n)       (((n) << EMAC_DMA_CH1_TX_CONTROL_TCW_SHIFT) & EMAC_DMA_CH1_TX_CONTROL_TCW_MASK)
#define EMAC_DMA_CH1_TX_CONTROL_OSF          (1 << 4) /* Bit 4: Operate on Second Packet */
#define EMAC_DMA_CH1_TX_CONTROL_TXPBL_SHIFT  (16)     /* Bits 16-22: Transmit Programmable Burst Length */
#define EMAC_DMA_CH1_TX_CONTROL_TXPBL_MASK   (0x3f << EMAC_DMA_CH1_TX_CONTROL_TXPBL_SHIFT)
#define EMAC_DMA_CH1_TX_CONTROL_TXPBL(n)     (((n) << EMAC_DMA_CH1_TX_CONTROL_TXPBL_SHIFT) & EMAC_DMA_CH1_TX_CONTROL_TXPBL_MASK)
#define EMAC_DMA_CH1_TX_CONTROL_ETIC         (1 << 22) /* Bit 22: Early Transmit Interrupt Control */
#define EMAC_DMA_CH1_TX_CONTROL_EDSE         (1 << 28) /* Bit 28: Enhanced Descriptor Enable */

/* DMA Channel 1 Rx Control (DMA_CH1_RX_CONTROL) */
#define EMAC_DMA_CH1_RX_CONTROL_SR               (1 << 0)  /* Bit 0: Start or Stop Receive */
#define EMAC_DMA_CH1_RX_CONTROL_RBSZ_X_0_SHIFT   (1)       /* Bits 1-3: Receive Buffer size Low */
#define EMAC_DMA_CH1_RX_CONTROL_RBSZ_X_0_MASK    (0x3 << EMAC_DMA_CH1_RX_CONTROL_RBSZ_X_0_SHIFT)
#define EMAC_DMA_CH1_RX_CONTROL_RBSZ_X_0(n)      (((n) << EMAC_DMA_CH1_RX_CONTROL_RBSZ_X_0_SHIFT) & EMAC_DMA_CH1_RX_CONTROL_RBSZ_X_0_MASK)
#define EMAC_DMA_CH1_RX_CONTROL_RBSZ_13_Y_SHIFT  (3)       /* Bits 3-15: Receive Buffer size High */
#define EMAC_DMA_CH1_RX_CONTROL_RBSZ_13_Y_MASK   (0xfff << EMAC_DMA_CH1_RX_CONTROL_RBSZ_13_Y_SHIFT)
#define EMAC_DMA_CH1_RX_CONTROL_RBSZ_13_Y(n)     (((n) << EMAC_DMA_CH1_RX_CONTROL_RBSZ_13_Y_SHIFT) & EMAC_DMA_CH1_RX_CONTROL_RBSZ_13_Y_MASK)
#define EMAC_DMA_CH1_RX_CONTROL_RXPBL_SHIFT      (16)      /* Bits 16-22: Receive Programmable Burst Length */
#define EMAC_DMA_CH1_RX_CONTROL_RXPBL_MASK       (0x3f << EMAC_DMA_CH1_RX_CONTROL_RXPBL_SHIFT)
#define EMAC_DMA_CH1_RX_CONTROL_RXPBL(n)         (((n) << EMAC_DMA_CH1_RX_CONTROL_RXPBL_SHIFT) & EMAC_DMA_CH1_RX_CONTROL_RXPBL_MASK)
#define EMAC_DMA_CH1_RX_CONTROL_ERIC             (1 << 22) /* Bit 22: Early Receive Interrupt Control */
#define EMAC_DMA_CH1_RX_CONTROL_RPF              (1 << 31) /* Bit 31: Rx Packet Flush */

/* DMA Channel 1 Tx Descriptor List Address (DMA_CH1_TXDESC_LIST_ADDRESS) */
#define EMAC_DMA_CH1_TXDESC_LIST_ADDRESS_TDESLA_SHIFT  (2) /* Bits 2-32: Start of Transmit List */
#define EMAC_DMA_CH1_TXDESC_LIST_ADDRESS_TDESLA_MASK   (0x3fffffff << EMAC_DMA_CH1_TXDESC_LIST_ADDRESS_TDESLA_SHIFT)
#define EMAC_DMA_CH1_TXDESC_LIST_ADDRESS_TDESLA(n)     (((n) << EMAC_DMA_CH1_TXDESC_LIST_ADDRESS_TDESLA_SHIFT) & EMAC_DMA_CH1_TXDESC_LIST_ADDRESS_TDESLA_MASK)

/* DMA Channel 1 Rx Descriptor List Address (DMA_CH1_RXDESC_LIST_ADDRESS) */
#define EMAC_DMA_CH1_RXDESC_LIST_ADDRESS_RDESLA_SHIFT  (2) /* Bits 2-32: Start of Receive List */
#define EMAC_DMA_CH1_RXDESC_LIST_ADDRESS_RDESLA_MASK   (0x3fffffff << EMAC_DMA_CH1_RXDESC_LIST_ADDRESS_RDESLA_SHIFT)
#define EMAC_DMA_CH1_RXDESC_LIST_ADDRESS_RDESLA(n)     (((n) << EMAC_DMA_CH1_RXDESC_LIST_ADDRESS_RDESLA_SHIFT) & EMAC_DMA_CH1_RXDESC_LIST_ADDRESS_RDESLA_MASK)

/* DMA Channel 1 Tx Descriptor Tail Pointer (DMA_CH1_TXDESC_TAIL_POINTER) */
#define EMAC_DMA_CH1_TXDESC_TAIL_POINTER_TDTP_SHIFT  (2) /* Bits 2-32: Transmit Descriptor Tail Pointer */
#define EMAC_DMA_CH1_TXDESC_TAIL_POINTER_TDTP_MASK   (0x3fffffff << EMAC_DMA_CH1_TXDESC_TAIL_POINTER_TDTP_SHIFT)
#define EMAC_DMA_CH1_TXDESC_TAIL_POINTER_TDTP(n)     (((n) << EMAC_DMA_CH1_TXDESC_TAIL_POINTER_TDTP_SHIFT) & EMAC_DMA_CH1_TXDESC_TAIL_POINTER_TDTP_MASK)

/* DMA Channel 1 Rx Descriptor Tail Pointer (DMA_CH1_RXDESC_TAIL_POINTER) */
#define EMAC_DMA_CH1_RXDESC_TAIL_POINTER_RDTP_SHIFT  (2) /* Bits 2-32: Receive Descriptor Tail Pointer */
#define EMAC_DMA_CH1_RXDESC_TAIL_POINTER_RDTP_MASK   (0x3fffffff << EMAC_DMA_CH1_RXDESC_TAIL_POINTER_RDTP_SHIFT)
#define EMAC_DMA_CH1_RXDESC_TAIL_POINTER_RDTP(n)     (((n) << EMAC_DMA_CH1_RXDESC_TAIL_POINTER_RDTP_SHIFT) & EMAC_DMA_CH1_RXDESC_TAIL_POINTER_RDTP_MASK)

/* DMA Channel 1 Tx Descriptor Ring Length (DMA_CH1_TXDESC_RING_LENGTH) */
#define EMAC_DMA_CH1_TXDESC_RING_LENGTH_TDRL_SHIFT  (0) /* Bits 0-10: Transmit Descriptor Ring Length */
#define EMAC_DMA_CH1_TXDESC_RING_LENGTH_TDRL_MASK   (0x3ff << EMAC_DMA_CH1_TXDESC_RING_LENGTH_TDRL_SHIFT)
#define EMAC_DMA_CH1_TXDESC_RING_LENGTH_TDRL(n)     (((n) << EMAC_DMA_CH1_TXDESC_RING_LENGTH_TDRL_SHIFT) & EMAC_DMA_CH1_TXDESC_RING_LENGTH_TDRL_MASK)

/* DMA Channel 1 Rx Descriptor Ring Length (DMA_CH1_RXDESC_RING_LENGTH) */
#define EMAC_DMA_CH1_RXDESC_RING_LENGTH_RDRL_SHIFT  (0) /* Bits 0-10: Receive Descriptor Ring Length */
#define EMAC_DMA_CH1_RXDESC_RING_LENGTH_RDRL_MASK   (0x3ff << EMAC_DMA_CH1_RXDESC_RING_LENGTH_RDRL_SHIFT)
#define EMAC_DMA_CH1_RXDESC_RING_LENGTH_RDRL(n)     (((n) << EMAC_DMA_CH1_RXDESC_RING_LENGTH_RDRL_SHIFT) & EMAC_DMA_CH1_RXDESC_RING_LENGTH_RDRL_MASK)

/* DMA Channel 1 Interrupt Enable (DMA_CH1_INTERRUPT_ENABLE) */
#define EMAC_DMA_CH1_INTERRUPT_ENABLE_TIE   (1 << 0)  /* Bit 0: Transmit Interrupt Enable */
#define EMAC_DMA_CH1_INTERRUPT_ENABLE_TXSE  (1 << 1)  /* Bit 1: Transmit Stopped Enable */
#define EMAC_DMA_CH1_INTERRUPT_ENABLE_TBUE  (1 << 2)  /* Bit 2: Transmit Buffer Unavailable Enable */
#define EMAC_DMA_CH1_INTERRUPT_ENABLE_RIE   (1 << 6)  /* Bit 6: Receive Interrupt Enable */
#define EMAC_DMA_CH1_INTERRUPT_ENABLE_RBUE  (1 << 7)  /* Bit 7: Receive Buffer Unavailable Enable */
#define EMAC_DMA_CH1_INTERRUPT_ENABLE_RSE   (1 << 8)  /* Bit 8: Receive Stopped Enable */
#define EMAC_DMA_CH1_INTERRUPT_ENABLE_RWTE  (1 << 9)  /* Bit 9: Receive Watchdog Timeout Enable */
#define EMAC_DMA_CH1_INTERRUPT_ENABLE_ETIE  (1 << 10) /* Bit 10: Early Transmit Interrupt Enable */
#define EMAC_DMA_CH1_INTERRUPT_ENABLE_ERIE  (1 << 11) /* Bit 11: Early Receive Interrupt Enable */
#define EMAC_DMA_CH1_INTERRUPT_ENABLE_FBEE  (1 << 12) /* Bit 12: Fatal Bus Error Enable */
#define EMAC_DMA_CH1_INTERRUPT_ENABLE_CDEE  (1 << 13) /* Bit 13: Context Descriptor Error Enable */
#define EMAC_DMA_CH1_INTERRUPT_ENABLE_AIE   (1 << 14) /* Bit 14: Abnormal Interrupt Summary Enable */
#define EMAC_DMA_CH1_INTERRUPT_ENABLE_NIE   (1 << 15) /* Bit 15: Normal Interrupt Summary Enable */

/* DMA Channel 1 Rx Interrupt Watchdog Timer
 * (DMA_CH1_RX_INTERRUPT_WATCHDOG_TIMER)
 */

#define EMAC_DMA_CH1_RX_INTERRUPT_WATCHDOG_TIMER_RWT_SHIFT   (0) /* Bits 0-8: Receive Interrupt Watchdog Timer Count */
#define EMAC_DMA_CH1_RX_INTERRUPT_WATCHDOG_TIMER_RWT_MASK    (0xff << EMAC_DMA_CH1_RX_INTERRUPT_WATCHDOG_TIMER_RWT_SHIFT)
#define EMAC_DMA_CH1_RX_INTERRUPT_WATCHDOG_TIMER_RWT(n)      (((n) << EMAC_DMA_CH1_RX_INTERRUPT_WATCHDOG_TIMER_RWT_SHIFT) & EMAC_DMA_CH1_RX_INTERRUPT_WATCHDOG_TIMER_RWT_MASK)
#define EMAC_DMA_CH1_RX_INTERRUPT_WATCHDOG_TIMER_RWTU_SHIFT  (16) /* Bits 16-18: Receive Interrupt Watchdog Timer Count Units */
#define EMAC_DMA_CH1_RX_INTERRUPT_WATCHDOG_TIMER_RWTU_MASK   (0x3 << EMAC_DMA_CH1_RX_INTERRUPT_WATCHDOG_TIMER_RWTU_SHIFT)
#define EMAC_DMA_CH1_RX_INTERRUPT_WATCHDOG_TIMER_RWTU(n)     (((n) << EMAC_DMA_CH1_RX_INTERRUPT_WATCHDOG_TIMER_RWTU_SHIFT) & EMAC_DMA_CH1_RX_INTERRUPT_WATCHDOG_TIMER_RWTU_MASK)

/* DMA Channel 1 Slot Function Control Status
 * (DMA_CH1_SLOT_FUNCTION_CONTROL_STATUS)
 */

#define EMAC_DMA_CH1_SLOT_FUNCTION_CONTROL_STATUS_ESC        (1 << 0) /* Bit 0: Enable Slot Comparison */
#define EMAC_DMA_CH1_SLOT_FUNCTION_CONTROL_STATUS_ASC        (1 << 1) /* Bit 1: Advance Slot Check */
#define EMAC_DMA_CH1_SLOT_FUNCTION_CONTROL_STATUS_SIV_SHIFT  (4)      /* Bits 4-16: Slot Interval Value */
#define EMAC_DMA_CH1_SLOT_FUNCTION_CONTROL_STATUS_SIV_MASK   (0xfff << EMAC_DMA_CH1_SLOT_FUNCTION_CONTROL_STATUS_SIV_SHIFT)
#define EMAC_DMA_CH1_SLOT_FUNCTION_CONTROL_STATUS_SIV(n)     (((n) << EMAC_DMA_CH1_SLOT_FUNCTION_CONTROL_STATUS_SIV_SHIFT) & EMAC_DMA_CH1_SLOT_FUNCTION_CONTROL_STATUS_SIV_MASK)
#define EMAC_DMA_CH1_SLOT_FUNCTION_CONTROL_STATUS_RSN_SHIFT  (16)     /* Bits 16-20: Reference Slot Number */
#define EMAC_DMA_CH1_SLOT_FUNCTION_CONTROL_STATUS_RSN_MASK   (0xf << EMAC_DMA_CH1_SLOT_FUNCTION_CONTROL_STATUS_RSN_SHIFT)
#define EMAC_DMA_CH1_SLOT_FUNCTION_CONTROL_STATUS_RSN(n)     (((n) << EMAC_DMA_CH1_SLOT_FUNCTION_CONTROL_STATUS_RSN_SHIFT) & EMAC_DMA_CH1_SLOT_FUNCTION_CONTROL_STATUS_RSN_MASK)

/* DMA Channel 1 Current Application Transmit Descriptor
 * (DMA_CH1_CURRENT_APP_TXDESC)
 */

#define EMAC_DMA_CH1_CURRENT_APP_TXDESC_CURTDESAPTR_SHIFT  (0) /* Bits 0-32: Application Transmit Descriptor Address Pointer */
#define EMAC_DMA_CH1_CURRENT_APP_TXDESC_CURTDESAPTR_MASK   (0xffffffff << EMAC_DMA_CH1_CURRENT_APP_TXDESC_CURTDESAPTR_SHIFT)
#define EMAC_DMA_CH1_CURRENT_APP_TXDESC_CURTDESAPTR(n)     (((n) << EMAC_DMA_CH1_CURRENT_APP_TXDESC_CURTDESAPTR_SHIFT) & EMAC_DMA_CH1_CURRENT_APP_TXDESC_CURTDESAPTR_MASK)

/* DMA Channel 1 Current Application Receive Descriptor
 * (DMA_CH1_CURRENT_APP_RXDESC)
 */

#define EMAC_DMA_CH1_CURRENT_APP_RXDESC_CURRDESAPTR_SHIFT  (0) /* Bits 0-32: Application Receive Descriptor Address Pointer */
#define EMAC_DMA_CH1_CURRENT_APP_RXDESC_CURRDESAPTR_MASK   (0xffffffff << EMAC_DMA_CH1_CURRENT_APP_RXDESC_CURRDESAPTR_SHIFT)
#define EMAC_DMA_CH1_CURRENT_APP_RXDESC_CURRDESAPTR(n)     (((n) << EMAC_DMA_CH1_CURRENT_APP_RXDESC_CURRDESAPTR_SHIFT) & EMAC_DMA_CH1_CURRENT_APP_RXDESC_CURRDESAPTR_MASK)

/* DMA Channel 1 Current Application Transmit Buffer
 * (DMA_CH1_CURRENT_APP_TXBUFFER)
 */

#define EMAC_DMA_CH1_CURRENT_APP_TXBUFFER_CURTBUFAPTR_SHIFT  (0) /* Bits 0-32: Application Transmit Buffer Address Pointer */
#define EMAC_DMA_CH1_CURRENT_APP_TXBUFFER_CURTBUFAPTR_MASK   (0xffffffff << EMAC_DMA_CH1_CURRENT_APP_TXBUFFER_CURTBUFAPTR_SHIFT)
#define EMAC_DMA_CH1_CURRENT_APP_TXBUFFER_CURTBUFAPTR(n)     (((n) << EMAC_DMA_CH1_CURRENT_APP_TXBUFFER_CURTBUFAPTR_SHIFT) & EMAC_DMA_CH1_CURRENT_APP_TXBUFFER_CURTBUFAPTR_MASK)

/* DMA Channel 1 Current Application Receive Buffer
 * (DMA_CH1_CURRENT_APP_RXBUFFER)
 */

#define EMAC_DMA_CH1_CURRENT_APP_RXBUFFER_CURRBUFAPTR_SHIFT  (0) /* Bits 0-32: Application Receive Buffer Address Pointer */
#define EMAC_DMA_CH1_CURRENT_APP_RXBUFFER_CURRBUFAPTR_MASK   (0xffffffff << EMAC_DMA_CH1_CURRENT_APP_RXBUFFER_CURRBUFAPTR_SHIFT)
#define EMAC_DMA_CH1_CURRENT_APP_RXBUFFER_CURRBUFAPTR(n)     (((n) << EMAC_DMA_CH1_CURRENT_APP_RXBUFFER_CURRBUFAPTR_SHIFT) & EMAC_DMA_CH1_CURRENT_APP_RXBUFFER_CURRBUFAPTR_MASK)

/* DMA Channel 1 Status (DMA_CH1_STATUS) */
#define EMAC_DMA_CH1_STATUS_TI         (1 << 0)  /* Bit 0: Transmit Interrupt */
#define EMAC_DMA_CH1_STATUS_TPS        (1 << 1)  /* Bit 1: Transmit Process Stopped */
#define EMAC_DMA_CH1_STATUS_TBU        (1 << 2)  /* Bit 2: Transmit Buffer Unavailable */
#define EMAC_DMA_CH1_STATUS_RI         (1 << 6)  /* Bit 6: Receive Interrupt */
#define EMAC_DMA_CH1_STATUS_RBU        (1 << 7)  /* Bit 7: Receive Buffer Unavailable */
#define EMAC_DMA_CH1_STATUS_RPS        (1 << 8)  /* Bit 8: Receive Process Stopped */
#define EMAC_DMA_CH1_STATUS_RWT        (1 << 9)  /* Bit 9: Receive Watchdog Timeout */
#define EMAC_DMA_CH1_STATUS_ETI        (1 << 10) /* Bit 10: Early Transmit Interrupt */
#define EMAC_DMA_CH1_STATUS_ERI        (1 << 11) /* Bit 11: Early Receive Interrupt */
#define EMAC_DMA_CH1_STATUS_FBE        (1 << 12) /* Bit 12: Fatal Bus Error */
#define EMAC_DMA_CH1_STATUS_CDE        (1 << 13) /* Bit 13: Context Descriptor Error */
#define EMAC_DMA_CH1_STATUS_AIS        (1 << 14) /* Bit 14: Abnormal Interrupt Summary */
#define EMAC_DMA_CH1_STATUS_NIS        (1 << 15) /* Bit 15: Normal Interrupt Summary */
#define EMAC_DMA_CH1_STATUS_TEB_SHIFT  (16)      /* Bits 16-19: Tx DMA Error Bits */
#define EMAC_DMA_CH1_STATUS_TEB_MASK   (0x7 << EMAC_DMA_CH1_STATUS_TEB_SHIFT)
#define EMAC_DMA_CH1_STATUS_TEB(n)     (((n) << EMAC_DMA_CH1_STATUS_TEB_SHIFT) & EMAC_DMA_CH1_STATUS_TEB_MASK)
#define EMAC_DMA_CH1_STATUS_REB_SHIFT  (19)      /* Bits 19-22: Rx DMA Error Bits */
#define EMAC_DMA_CH1_STATUS_REB_MASK   (0x7 << EMAC_DMA_CH1_STATUS_REB_SHIFT)
#define EMAC_DMA_CH1_STATUS_REB(n)     (((n) << EMAC_DMA_CH1_STATUS_REB_SHIFT) & EMAC_DMA_CH1_STATUS_REB_MASK)

/* DMA Channel 1 Miss Frame Counter (DMA_CH1_MISS_FRAME_CNT) */
#define EMAC_DMA_CH1_MISS_FRAME_CNT_MFC_SHIFT  (0) /* Bits 0-11: Dropped Packet Counters */
#define EMAC_DMA_CH1_MISS_FRAME_CNT_MFC_MASK   (0x7ff << EMAC_DMA_CH1_MISS_FRAME_CNT_MFC_SHIFT)
#define EMAC_DMA_CH1_MISS_FRAME_CNT_MFC(n)     (((n) << EMAC_DMA_CH1_MISS_FRAME_CNT_MFC_SHIFT) & EMAC_DMA_CH1_MISS_FRAME_CNT_MFC_MASK)
#define EMAC_DMA_CH1_MISS_FRAME_CNT_MFCO       (1 << 15) /* Bit 15: Overflow status of the MFC Counter */

/* DMA Channel 1 Rx Parser Accept Count (DMA_CH1_RXP_ACCEPT_CNT) */
#define EMAC_DMA_CH1_RXP_ACCEPT_CNT_RXPAC_SHIFT  (0) /* Bits 0-31: Rx Parser Accept Counter */
#define EMAC_DMA_CH1_RXP_ACCEPT_CNT_RXPAC_MASK   (0x7fffffff << EMAC_DMA_CH1_RXP_ACCEPT_CNT_RXPAC_SHIFT)
#define EMAC_DMA_CH1_RXP_ACCEPT_CNT_RXPAC(n)     (((n) << EMAC_DMA_CH1_RXP_ACCEPT_CNT_RXPAC_SHIFT) & EMAC_DMA_CH1_RXP_ACCEPT_CNT_RXPAC_MASK)
#define EMAC_DMA_CH1_RXP_ACCEPT_CNT_RXPACOF      (1 << 31) /* Bit 31: Rx Parser Accept Counter Overflow Bit */

/* DMA Channel 1 Rx ERI Count (DMA_CH1_RX_ERI_CNT) */
#define EMAC_DMA_CH1_RX_ERI_CNT_ECNT_SHIFT  (0) /* Bits 0-12: ERI Counter */
#define EMAC_DMA_CH1_RX_ERI_CNT_ECNT_MASK   (0xfff << EMAC_DMA_CH1_RX_ERI_CNT_ECNT_SHIFT)
#define EMAC_DMA_CH1_RX_ERI_CNT_ECNT(n)     (((n) << EMAC_DMA_CH1_RX_ERI_CNT_ECNT_SHIFT) & EMAC_DMA_CH1_RX_ERI_CNT_ECNT_MASK)

#define EMAC_TDES3_CPC(x)        (((uint32_t)(((uint32_t)(x)) << 26U)) & 0x0C000000U)
#define EMAC_TDES3_CIC(x)        (((uint32_t)(((uint32_t)(x)) << 16U)) & 0x00030000U)
#define EMAC_TDES3_OWN_MASK      (0x80000000u)
#define EMAC_TDES3_FD_MASK       (0x20000000u)
#define EMAC_TDES3_LD_MASK       (0x10000000u)

#define EMAC_TDES2_IOC_MASK      (0x80000000u)
#define EMAC_TDES2_TTSE_MASK     (0x40000000u)
#define EMAC_TDES2_B1L_MASK      (0x00003fffu)

#define EMAC_RDES0_IVT_MASK      (0xffff0000u)
#define EMAC_RDES0_IVT_SHIFT     (16u)
#define EMAC_RDES0_OVT_MASK      (0x0000ffffu)

#define EMAC_RDES1_TSA_MASK      (0x00004000u)
#define EMAC_RDES1_IPCE_MASK     (0x00000080u)
#define EMAC_RDES1_IPV6_MASK     (0x00000020u)
#define EMAC_RDES1_IPV4_MASK     (0x00000010u)
#define EMAC_RDES1_IPHE_MASK     (0x00000008u)
#define EMAC_RDES1_PT_MASK       (0x00000007u)

#define EMAC_RDES3_PL_MASK       (0x00007fffu)
#define EMAC_RDES3_OWN_MASK      (0x80000000u)
#define EMAC_RDES3_INTE_MASK     (0x40000000u)
#define EMAC_RDES3_BUF1V_MASK    (0x01000000u)
#define EMAC_RDES3_RS1V_MASK     (0x04000000u)
#define EMAC_RDES3_RS0V_MASK     (0x02000000u)

#define EMAC_RDES3_CTXT_MASK     (0x40000000u)

#define EMAC_INFO1_CONSUMED_MASK (0x00000001u)
#define EMAC_INFO1_LOCKED_MASK   (0x10000000u)
#define EMAC_INFO1_LENGTH_MASK   (0x00003fffu)

/****************************************************************************
 * Public Types
 ****************************************************************************/

/* Buffer Descriptors *******************************************************/

struct eth_desc_s
{
    uint32_t des0;      /* Data length */
    uint32_t des1;      /* Data length */
    uint32_t des2;      /* Data length */
    uint32_t des3;      /* Data length */
};

#endif /* __ARCH_ARM_SRC_S32K3XX_HARDWARE_S32K3XX_EMAC_H */
