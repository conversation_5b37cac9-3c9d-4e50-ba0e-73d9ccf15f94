/****************************************************************************
 * arch/arm/src/s32k3xx/hardware/s32k344_pinmux.h
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/* Copyright 2022 NXP */

#ifndef __ARCH_ARM_SRC_S32K3XX_HARDWARE_S32K344_PINMUX_H
#define __ARCH_ARM_SRC_S32K3XX_HARDWARE_S32K344_PINMUX_H

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <s32k3xx_pin.h>

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* In most cases, there are alternative configurations for various pins.
 * Those alternative pins are labeled with a suffix like _1, _2, etc. in
 * order to distinguish them.  Logic in the board.h file must select the
 * correct pin configuration for the board by defining a pin
 * configuration (with no suffix) that maps to the correct alternative.
 *
 * WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!! WARNING!!!
 * Additional effort is required to select specific GPIO options such as
 * frequency, and pull-up/down!  Just the basics are defined for most pins
 * in the initial version of this file.
 */

/* Analog-to-Digital Converter (ADC)
 *
 * These are only outputs for external analog multiplexing.  Analog inputs
 * cannot be controlled by the pin multiplexing in the SIUL2 module.  Those
 * analog functions are available in parallel to the multiplexed digital
 * functionality.  See the S32K3xx reference manual for more information.
 */

#define PIN_ADC0_MA0_1              (PIN_PORTB | PIN3  | PIN_OUTPUT_ALT4)
#define PIN_ADC0_MA0_2              (PIN_PORTE | PIN2  | PIN_OUTPUT_ALT7)
#define PIN_ADC0_MA0_3              (PIN_PORTF | PIN9  | PIN_OUTPUT_ALT3)
#define PIN_ADC0_MA0_4              (PIN_PORTF | PIN12 | PIN_OUTPUT_ALT3)
#define PIN_ADC0_MA1_1              (PIN_PORTC | PIN14 | PIN_OUTPUT_ALT4)
#define PIN_ADC0_MA1_2              (PIN_PORTE | PIN6  | PIN_OUTPUT_ALT7)
#define PIN_ADC0_MA1_3              (PIN_PORTF | PIN10 | PIN_OUTPUT_ALT3)
#define PIN_ADC0_MA1_4              (PIN_PORTF | PIN13 | PIN_OUTPUT_ALT3)
#define PIN_ADC0_MA2_1              (PIN_PORTC | PIN6  | PIN_OUTPUT_ALT7)
#define PIN_ADC0_MA2_2              (PIN_PORTC | PIN15 | PIN_OUTPUT_ALT4)
#define PIN_ADC0_MA2_3              (PIN_PORTF | PIN11 | PIN_OUTPUT_ALT3)
#define PIN_ADC0_MA2_4              (PIN_PORTF | PIN19 | PIN_OUTPUT_ALT4)

#define PIN_ADC1_MA0_1              (PIN_PORTB | PIN2  | PIN_OUTPUT_ALT1)
#define PIN_ADC1_MA0_2              (PIN_PORTB | PIN23 | PIN_OUTPUT_ALT1)
#define PIN_ADC1_MA0_3              (PIN_PORTC | PIN27 | PIN_OUTPUT_ALT5)
#define PIN_ADC1_MA0_4              (PIN_PORTF | PIN16 | PIN_OUTPUT_ALT4)
#define PIN_ADC1_MA0_5              (PIN_PORTF | PIN29 | PIN_OUTPUT_ALT4)
#define PIN_ADC1_MA1_1              (PIN_PORTB | PIN24 | PIN_OUTPUT_ALT1)
#define PIN_ADC1_MA1_2              (PIN_PORTC | PIN13 | PIN_OUTPUT_ALT4)
#define PIN_ADC1_MA1_3              (PIN_PORTC | PIN21 | PIN_OUTPUT_ALT5)
#define PIN_ADC1_MA1_4              (PIN_PORTF | PIN17 | PIN_OUTPUT_ALT4)
#define PIN_ADC1_MA1_5              (PIN_PORTF | PIN30 | PIN_OUTPUT_ALT4)
#define PIN_ADC1_MA2_1              (PIN_PORTB | PIN28 | PIN_OUTPUT_ALT1)
#define PIN_ADC1_MA2_2              (PIN_PORTC | PIN12 | PIN_OUTPUT_ALT1)
#define PIN_ADC1_MA2_3              (PIN_PORTC | PIN20 | PIN_OUTPUT_ALT5)
#define PIN_ADC1_MA2_4              (PIN_PORTF | PIN18 | PIN_OUTPUT_ALT4)
#define PIN_ADC1_MA2_5              (PIN_PORTF | PIN31 | PIN_OUTPUT_ALT1)

/* FlexCAN */

#define PIN_CAN0_RX_1               (PIN_PORTA | PIN6  | PIN_INPUT_ALT2  | IMCR(512))
#define PIN_CAN0_RX_2               (PIN_PORTA | PIN28 | PIN_INPUT_ALT4  | IMCR(512))
#define PIN_CAN0_RX_3               (PIN_PORTB | PIN0  | PIN_INPUT_ALT3  | IMCR(512))
#define PIN_CAN0_RX_4               (PIN_PORTC | PIN2  | PIN_INPUT_ALT1  | IMCR(512))
#define PIN_CAN0_RX_5               (PIN_PORTF | PIN21 | PIN_INPUT_ALT5  | IMCR(512))
#define PIN_CAN0_TX_1               (PIN_PORTA | PIN7  | PIN_OUTPUT_ALT4)
#define PIN_CAN0_TX_2               (PIN_PORTA | PIN27 | PIN_OUTPUT_ALT5)
#define PIN_CAN0_TX_3               (PIN_PORTB | PIN1  | PIN_OUTPUT_ALT5)
#define PIN_CAN0_TX_4               (PIN_PORTC | PIN3  | PIN_OUTPUT_ALT3)
#define PIN_CAN0_TX_5               (PIN_PORTF | PIN20 | PIN_OUTPUT_ALT6)

#define PIN_CAN1_RX_1               (PIN_PORTA | PIN12 | PIN_INPUT_ALT2  | IMCR(513))
#define PIN_CAN1_RX_2               (PIN_PORTA | PIN22 | PIN_INPUT_ALT3  | IMCR(513))
#define PIN_CAN1_RX_3               (PIN_PORTB | PIN23 | PIN_INPUT_ALT4  | IMCR(513))
#define PIN_CAN1_RX_4               (PIN_PORTC | PIN9  | PIN_INPUT_ALT1  | IMCR(513))
#define PIN_CAN1_RX_5               (PIN_PORTF | PIN15 | PIN_INPUT_ALT5  | IMCR(513))
#define PIN_CAN1_TX_1               (PIN_PORTA | PIN11 | PIN_OUTPUT_ALT1)
#define PIN_CAN1_TX_2               (PIN_PORTA | PIN23 | PIN_OUTPUT_ALT1)
#define PIN_CAN1_TX_3               (PIN_PORTB | PIN22 | PIN_OUTPUT_ALT1)
#define PIN_CAN1_TX_4               (PIN_PORTC | PIN8  | PIN_OUTPUT_ALT3)
#define PIN_CAN1_TX_5               (PIN_PORTF | PIN14 | PIN_OUTPUT_ALT1)

#define PIN_CAN2_RX_1               (PIN_PORTC | PIN6  | PIN_INPUT_ALT6  | IMCR(514))
#define PIN_CAN2_RX_2               (PIN_PORTC | PIN14 | PIN_INPUT_ALT2  | IMCR(514))
#define PIN_CAN2_RX_3               (PIN_PORTC | PIN16 | PIN_INPUT_ALT1  | IMCR(514))
#define PIN_CAN2_RX_4               (PIN_PORTD | PIN19 | PIN_INPUT_ALT4  | IMCR(514))
#define PIN_CAN2_RX_5               (PIN_PORTE | PIN25 | PIN_INPUT_ALT3  | IMCR(514))
#define PIN_CAN2_RX_6               (PIN_PORTF | PIN26 | PIN_INPUT_ALT5  | IMCR(514))
#define PIN_CAN2_TX_1               (PIN_PORTC | PIN7  | PIN_OUTPUT_ALT7)
#define PIN_CAN2_TX_2               (PIN_PORTC | PIN15 | PIN_OUTPUT_ALT1)
#define PIN_CAN2_TX_3               (PIN_PORTC | PIN17 | PIN_OUTPUT_ALT3)
#define PIN_CAN2_TX_4               (PIN_PORTD | PIN18 | PIN_OUTPUT_ALT1)
#define PIN_CAN2_TX_5               (PIN_PORTE | PIN24 | PIN_OUTPUT_ALT3)
#define PIN_CAN2_TX_6               (PIN_PORTF | PIN25 | PIN_OUTPUT_ALT1)

#define PIN_CAN3_RX_1               (PIN_PORTC | PIN1  | PIN_INPUT_ALT2  | IMCR(515))
#define PIN_CAN3_RX_2               (PIN_PORTC | PIN29 | PIN_INPUT_ALT3  | IMCR(515))
#define PIN_CAN3_RX_3               (PIN_PORTD | PIN15 | PIN_INPUT_ALT1  | IMCR(515))
#define PIN_CAN3_RX_4               (PIN_PORTE | PIN29 | PIN_INPUT_ALT4  | IMCR(515))
#define PIN_CAN3_RX_5               (PIN_PORTF | PIN30 | PIN_INPUT_ALT5  | IMCR(515))
#define PIN_CAN3_TX_1               (PIN_PORTC | PIN0  | PIN_OUTPUT_ALT1)
#define PIN_CAN3_TX_2               (PIN_PORTC | PIN28 | PIN_OUTPUT_ALT1)
#define PIN_CAN3_TX_3               (PIN_PORTE | PIN9  | PIN_OUTPUT_ALT4)
#define PIN_CAN3_TX_4               (PIN_PORTE | PIN28 | PIN_OUTPUT_ALT1)
#define PIN_CAN3_TX_5               (PIN_PORTF | PIN29 | PIN_OUTPUT_ALT1)

#define PIN_CAN4_RX_1               (PIN_PORTB | PIN2  | PIN_INPUT_ALT2  | IMCR(516))
#define PIN_CAN4_RX_2               (PIN_PORTC | PIN31 | PIN_INPUT_ALT3  | IMCR(516))
#define PIN_CAN4_RX_3               (PIN_PORTE | PIN14 | PIN_INPUT_ALT1  | IMCR(516))
#define PIN_CAN4_RX_4               (PIN_PORTE | PIN31 | PIN_INPUT_ALT4  | IMCR(516))
#define PIN_CAN4_RX_5               (PIN_PORTG | PIN9  | PIN_INPUT_ALT5  | IMCR(516))
#define PIN_CAN4_TX_1               (PIN_PORTB | PIN3  | PIN_OUTPUT_ALT5)
#define PIN_CAN4_TX_2               (PIN_PORTC | PIN30 | PIN_OUTPUT_ALT1)
#define PIN_CAN4_TX_3               (PIN_PORTE | PIN3  | PIN_OUTPUT_ALT1)
#define PIN_CAN4_TX_4               (PIN_PORTE | PIN30 | PIN_OUTPUT_ALT1)
#define PIN_CAN4_TX_5               (PIN_PORTG | PIN8  | PIN_OUTPUT_ALT1)

#define PIN_CAN5_RX_1               (PIN_PORTC | PIN11 | PIN_INPUT_ALT2  | IMCR(517))
#define PIN_CAN5_RX_2               (PIN_PORTC | PIN26 | PIN_INPUT_ALT5  | IMCR(517))
#define PIN_CAN5_RX_3               (PIN_PORTD | PIN17 | PIN_INPUT_ALT1  | IMCR(517))
#define PIN_CAN5_RX_4               (PIN_PORTF | PIN5  | PIN_INPUT_ALT3  | IMCR(517))
#define PIN_CAN5_RX_5               (PIN_PORTG | PIN14 | PIN_INPUT_ALT4  | IMCR(517))
#define PIN_CAN5_TX_1               (PIN_PORTC | PIN10 | PIN_OUTPUT_ALT3)
#define PIN_CAN5_TX_2               (PIN_PORTC | PIN27 | PIN_OUTPUT_ALT1)
#define PIN_CAN5_TX_3               (PIN_PORTE | PIN12 | PIN_OUTPUT_ALT2)
#define PIN_CAN5_TX_4               (PIN_PORTF | PIN4  | PIN_OUTPUT_ALT1)
#define PIN_CAN5_TX_5               (PIN_PORTG | PIN13 | PIN_OUTPUT_ALT1)

/* Clock Output (CLKOUT) */

#define PIN_CLKOUT_RUN_1            (PIN_PORTB | PIN5  | PIN_OUTPUT_ALT5)
#define PIN_CLKOUT_RUN_2            (PIN_PORTD | PIN10 | PIN_OUTPUT_ALT6)
#define PIN_CLKOUT_RUN_3            (PIN_PORTD | PIN14 | PIN_OUTPUT_ALT7)
#define PIN_CLKOUT_STANDBY_1        (PIN_PORTA | PIN12 | PIN_OUTPUT_ALT3)
#define PIN_CLKOUT_STANDBY_2        (PIN_PORTE | PIN10 | PIN_OUTPUT_ALT5)

/* Comparator (CMP)
 *
 * These are only digital comparator outputs.  The analog comparator inputs
 * cannot be controlled by the pin multiplexing in the SIUL2 module.  Those
 * analog functions are available in parallel to the multiplexed digital
 * functionality.  See the S32K3xx reference manual for more information.
 */

#define PIN_CMP0_OUT_1              (PIN_PORTA | PIN4  | PIN_OUTPUT_ALT4)
#define PIN_CMP0_OUT_2              (PIN_PORTE | PIN3  | PIN_OUTPUT_ALT7)
#define PIN_CMP0_OUT_3              (PIN_PORTF | PIN14 | PIN_OUTPUT_ALT3)
#define PIN_CMP0_RRT_1              (PIN_PORTA | PIN11 | PIN_OUTPUT_ALT5)
#define PIN_CMP0_RRT_2              (PIN_PORTD | PIN14 | PIN_OUTPUT_ALT6)
#define PIN_CMP0_RRT_3              (PIN_PORTF | PIN15 | PIN_OUTPUT_ALT3)

#define PIN_CMP1_OUT_1              (PIN_PORTA | PIN12 | PIN_OUTPUT_ALT7)
#define PIN_CMP1_OUT_2              (PIN_PORTE | PIN30 | PIN_OUTPUT_ALT2)
#define PIN_CMP1_RRT_1              (PIN_PORTE | PIN15 | PIN_OUTPUT_ALT5)
#define PIN_CMP1_RRT_2              (PIN_PORTE | PIN31 | PIN_OUTPUT_ALT2)

#define PIN_CMP2_OUT_1              (PIN_PORTA | PIN9  | PIN_OUTPUT_ALT7)
#define PIN_CMP2_OUT_2              (PIN_PORTG | PIN4  | PIN_OUTPUT_ALT4)
#define PIN_CMP2_RRT_1              (PIN_PORTC | PIN5  | PIN_OUTPUT_ALT5)
#define PIN_CMP2_RRT_2              (PIN_PORTG | PIN5  | PIN_OUTPUT_ALT4)

/* External Interrupt (EIRQ) */

#define PIN_EIRQ0_1                 (PIN_PORTA | PIN0  | PIN_INPUT_ALT1  | IMCR(528))
#define PIN_EIRQ0_2                 (PIN_PORTA | PIN18 | PIN_INPUT_ALT2  | IMCR(528))
#define PIN_EIRQ0_3                 (PIN_PORTC | PIN0  | PIN_INPUT_ALT3  | IMCR(528))
#define PIN_EIRQ0_4                 (PIN_PORTE | PIN0  | PIN_INPUT_ALT4  | IMCR(528))
#define PIN_EIRQ0_5                 (PIN_PORTF | PIN0  | PIN_INPUT_ALT5  | IMCR(528))
#define PIN_EIRQ1_1                 (PIN_PORTA | PIN1  | PIN_INPUT_ALT1  | IMCR(529))
#define PIN_EIRQ1_2                 (PIN_PORTA | PIN19 | PIN_INPUT_ALT2  | IMCR(529))
#define PIN_EIRQ1_3                 (PIN_PORTC | PIN1  | PIN_INPUT_ALT3  | IMCR(529))
#define PIN_EIRQ1_4                 (PIN_PORTE | PIN1  | PIN_INPUT_ALT4  | IMCR(529))
#define PIN_EIRQ1_5                 (PIN_PORTF | PIN1  | PIN_INPUT_ALT5  | IMCR(529))
#define PIN_EIRQ2_1                 (PIN_PORTA | PIN2  | PIN_INPUT_ALT1  | IMCR(530))
#define PIN_EIRQ2_2                 (PIN_PORTA | PIN20 | PIN_INPUT_ALT2  | IMCR(530))
#define PIN_EIRQ2_3                 (PIN_PORTC | PIN2  | PIN_INPUT_ALT3  | IMCR(530))
#define PIN_EIRQ2_4                 (PIN_PORTE | PIN2  | PIN_INPUT_ALT4  | IMCR(530))
#define PIN_EIRQ2_5                 (PIN_PORTF | PIN2  | PIN_INPUT_ALT5  | IMCR(530))
#define PIN_EIRQ3_1                 (PIN_PORTA | PIN3  | PIN_INPUT_ALT1  | IMCR(531))
#define PIN_EIRQ3_2                 (PIN_PORTA | PIN21 | PIN_INPUT_ALT2  | IMCR(531))
#define PIN_EIRQ3_3                 (PIN_PORTC | PIN3  | PIN_INPUT_ALT3  | IMCR(531))
#define PIN_EIRQ3_4                 (PIN_PORTE | PIN3  | PIN_INPUT_ALT4  | IMCR(531))
#define PIN_EIRQ3_5                 (PIN_PORTF | PIN3  | PIN_INPUT_ALT5  | IMCR(531))
#define PIN_EIRQ4_1                 (PIN_PORTA | PIN4  | PIN_INPUT_ALT1  | IMCR(532))
#define PIN_EIRQ4_2                 (PIN_PORTA | PIN16 | PIN_INPUT_ALT2  | IMCR(532))
#define PIN_EIRQ4_3                 (PIN_PORTC | PIN4  | PIN_INPUT_ALT3  | IMCR(532))
#define PIN_EIRQ4_4                 (PIN_PORTE | PIN4  | PIN_INPUT_ALT4  | IMCR(532))
#define PIN_EIRQ4_5                 (PIN_PORTF | PIN4  | PIN_INPUT_ALT5  | IMCR(532))
#define PIN_EIRQ5_1                 (PIN_PORTA | PIN5  | PIN_INPUT_ALT1  | IMCR(533))
#define PIN_EIRQ5_2                 (PIN_PORTA | PIN25 | PIN_INPUT_ALT2  | IMCR(533))
#define PIN_EIRQ5_3                 (PIN_PORTC | PIN5  | PIN_INPUT_ALT3  | IMCR(533))
#define PIN_EIRQ5_4                 (PIN_PORTE | PIN5  | PIN_INPUT_ALT4  | IMCR(533))
#define PIN_EIRQ5_5                 (PIN_PORTF | PIN5  | PIN_INPUT_ALT5  | IMCR(533))
#define PIN_EIRQ6_1                 (PIN_PORTA | PIN6  | PIN_INPUT_ALT1  | IMCR(534))
#define PIN_EIRQ6_2                 (PIN_PORTA | PIN28 | PIN_INPUT_ALT2  | IMCR(534))
#define PIN_EIRQ6_3                 (PIN_PORTC | PIN6  | PIN_INPUT_ALT3  | IMCR(534))
#define PIN_EIRQ6_4                 (PIN_PORTE | PIN6  | PIN_INPUT_ALT4  | IMCR(534))
#define PIN_EIRQ6_5                 (PIN_PORTF | PIN6  | PIN_INPUT_ALT5  | IMCR(534))
#define PIN_EIRQ7_1                 (PIN_PORTA | PIN7  | PIN_INPUT_ALT1  | IMCR(535))
#define PIN_EIRQ7_2                 (PIN_PORTA | PIN30 | PIN_INPUT_ALT2  | IMCR(535))
#define PIN_EIRQ7_3                 (PIN_PORTC | PIN7  | PIN_INPUT_ALT3  | IMCR(535))
#define PIN_EIRQ7_4                 (PIN_PORTE | PIN8  | PIN_INPUT_ALT4  | IMCR(535))
#define PIN_EIRQ7_5                 (PIN_PORTF | PIN7  | PIN_INPUT_ALT5  | IMCR(535))
#define PIN_EIRQ8_1                 (PIN_PORTB | PIN0  | PIN_INPUT_ALT1  | IMCR(536))
#define PIN_EIRQ8_2                 (PIN_PORTB | PIN21 | PIN_INPUT_ALT2  | IMCR(536))
#define PIN_EIRQ8_3                 (PIN_PORTD | PIN0  | PIN_INPUT_ALT3  | IMCR(536))
#define PIN_EIRQ8_4                 (PIN_PORTE | PIN9  | PIN_INPUT_ALT4  | IMCR(536))
#define PIN_EIRQ8_5                 (PIN_PORTG | PIN0  | PIN_INPUT_ALT5  | IMCR(536))
#define PIN_EIRQ9_1                 (PIN_PORTB | PIN1  | PIN_INPUT_ALT1  | IMCR(537))
#define PIN_EIRQ9_2                 (PIN_PORTB | PIN22 | PIN_INPUT_ALT2  | IMCR(537))
#define PIN_EIRQ9_3                 (PIN_PORTD | PIN1  | PIN_INPUT_ALT3  | IMCR(537))
#define PIN_EIRQ9_4                 (PIN_PORTE | PIN10 | PIN_INPUT_ALT4  | IMCR(537))
#define PIN_EIRQ9_5                 (PIN_PORTG | PIN1  | PIN_INPUT_ALT5  | IMCR(537))
#define PIN_EIRQ10_1                (PIN_PORTB | PIN2  | PIN_INPUT_ALT1  | IMCR(538))
#define PIN_EIRQ10_2                (PIN_PORTB | PIN23 | PIN_INPUT_ALT2  | IMCR(538))
#define PIN_EIRQ10_3                (PIN_PORTD | PIN2  | PIN_INPUT_ALT3  | IMCR(538))
#define PIN_EIRQ10_4                (PIN_PORTE | PIN11 | PIN_INPUT_ALT4  | IMCR(538))
#define PIN_EIRQ10_5                (PIN_PORTG | PIN2  | PIN_INPUT_ALT5  | IMCR(538))
#define PIN_EIRQ11_1                (PIN_PORTB | PIN3  | PIN_INPUT_ALT1  | IMCR(539))
#define PIN_EIRQ11_2                (PIN_PORTB | PIN24 | PIN_INPUT_ALT2  | IMCR(539))
#define PIN_EIRQ11_3                (PIN_PORTD | PIN3  | PIN_INPUT_ALT3  | IMCR(539))
#define PIN_EIRQ11_4                (PIN_PORTE | PIN12 | PIN_INPUT_ALT4  | IMCR(539))
#define PIN_EIRQ11_5                (PIN_PORTG | PIN3  | PIN_INPUT_ALT5  | IMCR(539))
#define PIN_EIRQ12_1                (PIN_PORTB | PIN4  | PIN_INPUT_ALT1  | IMCR(540))
#define PIN_EIRQ12_2                (PIN_PORTB | PIN25 | PIN_INPUT_ALT2  | IMCR(540))
#define PIN_EIRQ12_3                (PIN_PORTD | PIN4  | PIN_INPUT_ALT3  | IMCR(540))
#define PIN_EIRQ12_4                (PIN_PORTE | PIN13 | PIN_INPUT_ALT4  | IMCR(540))
#define PIN_EIRQ12_5                (PIN_PORTG | PIN4  | PIN_INPUT_ALT5  | IMCR(540))
#define PIN_EIRQ13_1                (PIN_PORTB | PIN5  | PIN_INPUT_ALT1  | IMCR(541))
#define PIN_EIRQ13_2                (PIN_PORTB | PIN26 | PIN_INPUT_ALT2  | IMCR(541))
#define PIN_EIRQ13_3                (PIN_PORTD | PIN5  | PIN_INPUT_ALT3  | IMCR(541))
#define PIN_EIRQ13_4                (PIN_PORTE | PIN14 | PIN_INPUT_ALT4  | IMCR(541))
#define PIN_EIRQ13_5                (PIN_PORTG | PIN5  | PIN_INPUT_ALT5  | IMCR(541))
#define PIN_EIRQ14_1                (PIN_PORTB | PIN8  | PIN_INPUT_ALT1  | IMCR(542))
#define PIN_EIRQ14_2                (PIN_PORTB | PIN28 | PIN_INPUT_ALT2  | IMCR(542))
#define PIN_EIRQ14_3                (PIN_PORTD | PIN6  | PIN_INPUT_ALT3  | IMCR(542))
#define PIN_EIRQ14_4                (PIN_PORTE | PIN15 | PIN_INPUT_ALT4  | IMCR(542))
#define PIN_EIRQ14_5                (PIN_PORTG | PIN6  | PIN_INPUT_ALT5  | IMCR(542))
#define PIN_EIRQ15_1                (PIN_PORTB | PIN9  | PIN_INPUT_ALT1  | IMCR(543))
#define PIN_EIRQ15_2                (PIN_PORTB | PIN31 | PIN_INPUT_ALT2  | IMCR(543))
#define PIN_EIRQ15_3                (PIN_PORTD | PIN7  | PIN_INPUT_ALT3  | IMCR(543))
#define PIN_EIRQ15_4                (PIN_PORTE | PIN16 | PIN_INPUT_ALT4  | IMCR(543))
#define PIN_EIRQ15_5                (PIN_PORTG | PIN7  | PIN_INPUT_ALT5  | IMCR(543))
#define PIN_EIRQ16_1                (PIN_PORTA | PIN8  | PIN_INPUT_ALT1  | IMCR(544))
#define PIN_EIRQ16_2                (PIN_PORTC | PIN8  | PIN_INPUT_ALT2  | IMCR(544))
#define PIN_EIRQ16_3                (PIN_PORTC | PIN20 | PIN_INPUT_ALT3  | IMCR(544))
#define PIN_EIRQ16_4                (PIN_PORTF | PIN8  | PIN_INPUT_ALT4  | IMCR(544))
#define PIN_EIRQ17_1                (PIN_PORTA | PIN9  | PIN_INPUT_ALT1  | IMCR(545))
#define PIN_EIRQ17_2                (PIN_PORTC | PIN9  | PIN_INPUT_ALT2  | IMCR(545))
#define PIN_EIRQ17_3                (PIN_PORTC | PIN21 | PIN_INPUT_ALT3  | IMCR(545))
#define PIN_EIRQ17_4                (PIN_PORTF | PIN9  | PIN_INPUT_ALT4  | IMCR(545))
#define PIN_EIRQ18_1                (PIN_PORTA | PIN10 | PIN_INPUT_ALT1  | IMCR(546))
#define PIN_EIRQ18_2                (PIN_PORTC | PIN10 | PIN_INPUT_ALT2  | IMCR(546))
#define PIN_EIRQ18_3                (PIN_PORTC | PIN23 | PIN_INPUT_ALT3  | IMCR(546))
#define PIN_EIRQ18_4                (PIN_PORTF | PIN10 | PIN_INPUT_ALT4  | IMCR(546))
#define PIN_EIRQ19_1                (PIN_PORTA | PIN11 | PIN_INPUT_ALT1  | IMCR(547))
#define PIN_EIRQ19_2                (PIN_PORTC | PIN11 | PIN_INPUT_ALT2  | IMCR(547))
#define PIN_EIRQ19_3                (PIN_PORTC | PIN24 | PIN_INPUT_ALT3  | IMCR(547))
#define PIN_EIRQ19_4                (PIN_PORTF | PIN11 | PIN_INPUT_ALT4  | IMCR(547))
#define PIN_EIRQ20_1                (PIN_PORTA | PIN12 | PIN_INPUT_ALT1  | IMCR(548))
#define PIN_EIRQ20_2                (PIN_PORTC | PIN12 | PIN_INPUT_ALT2  | IMCR(548))
#define PIN_EIRQ20_3                (PIN_PORTC | PIN25 | PIN_INPUT_ALT3  | IMCR(548))
#define PIN_EIRQ20_4                (PIN_PORTF | PIN12 | PIN_INPUT_ALT4  | IMCR(548))
#define PIN_EIRQ21_1                (PIN_PORTA | PIN13 | PIN_INPUT_ALT1  | IMCR(549))
#define PIN_EIRQ21_2                (PIN_PORTC | PIN13 | PIN_INPUT_ALT2  | IMCR(549))
#define PIN_EIRQ21_3                (PIN_PORTC | PIN26 | PIN_INPUT_ALT3  | IMCR(549))
#define PIN_EIRQ21_4                (PIN_PORTF | PIN13 | PIN_INPUT_ALT4  | IMCR(549))
#define PIN_EIRQ22_1                (PIN_PORTA | PIN14 | PIN_INPUT_ALT1  | IMCR(550))
#define PIN_EIRQ22_2                (PIN_PORTC | PIN14 | PIN_INPUT_ALT2  | IMCR(550))
#define PIN_EIRQ22_3                (PIN_PORTC | PIN27 | PIN_INPUT_ALT3  | IMCR(550))
#define PIN_EIRQ22_4                (PIN_PORTF | PIN14 | PIN_INPUT_ALT4  | IMCR(550))
#define PIN_EIRQ23_1                (PIN_PORTA | PIN15 | PIN_INPUT_ALT1  | IMCR(551))
#define PIN_EIRQ23_2                (PIN_PORTC | PIN15 | PIN_INPUT_ALT2  | IMCR(551))
#define PIN_EIRQ23_3                (PIN_PORTC | PIN29 | PIN_INPUT_ALT3  | IMCR(551))
#define PIN_EIRQ23_4                (PIN_PORTF | PIN15 | PIN_INPUT_ALT4  | IMCR(551))
#define PIN_EIRQ24_1                (PIN_PORTB | PIN10 | PIN_INPUT_ALT1  | IMCR(552))
#define PIN_EIRQ24_2                (PIN_PORTD | PIN8  | PIN_INPUT_ALT2  | IMCR(552))
#define PIN_EIRQ24_3                (PIN_PORTD | PIN17 | PIN_INPUT_ALT3  | IMCR(552))
#define PIN_EIRQ24_4                (PIN_PORTG | PIN8  | PIN_INPUT_ALT4  | IMCR(552))
#define PIN_EIRQ25_1                (PIN_PORTB | PIN11 | PIN_INPUT_ALT1  | IMCR(553))
#define PIN_EIRQ25_2                (PIN_PORTD | PIN9  | PIN_INPUT_ALT2  | IMCR(553))
#define PIN_EIRQ25_3                (PIN_PORTD | PIN20 | PIN_INPUT_ALT3  | IMCR(553))
#define PIN_EIRQ25_4                (PIN_PORTG | PIN9  | PIN_INPUT_ALT4  | IMCR(553))
#define PIN_EIRQ26_1                (PIN_PORTB | PIN12 | PIN_INPUT_ALT1  | IMCR(554))
#define PIN_EIRQ26_2                (PIN_PORTD | PIN10 | PIN_INPUT_ALT2  | IMCR(554))
#define PIN_EIRQ26_3                (PIN_PORTD | PIN21 | PIN_INPUT_ALT3  | IMCR(554))
#define PIN_EIRQ26_4                (PIN_PORTG | PIN10 | PIN_INPUT_ALT4  | IMCR(554))
#define PIN_EIRQ27_1                (PIN_PORTB | PIN13 | PIN_INPUT_ALT1  | IMCR(555))
#define PIN_EIRQ27_2                (PIN_PORTD | PIN11 | PIN_INPUT_ALT2  | IMCR(555))
#define PIN_EIRQ27_3                (PIN_PORTD | PIN22 | PIN_INPUT_ALT3  | IMCR(555))
#define PIN_EIRQ27_4                (PIN_PORTG | PIN11 | PIN_INPUT_ALT4  | IMCR(555))
#define PIN_EIRQ28_1                (PIN_PORTB | PIN14 | PIN_INPUT_ALT1  | IMCR(556))
#define PIN_EIRQ28_2                (PIN_PORTD | PIN12 | PIN_INPUT_ALT2  | IMCR(556))
#define PIN_EIRQ28_3                (PIN_PORTD | PIN23 | PIN_INPUT_ALT3  | IMCR(556))
#define PIN_EIRQ28_4                (PIN_PORTG | PIN12 | PIN_INPUT_ALT4  | IMCR(556))
#define PIN_EIRQ29_1                (PIN_PORTB | PIN15 | PIN_INPUT_ALT1  | IMCR(557))
#define PIN_EIRQ29_2                (PIN_PORTD | PIN13 | PIN_INPUT_ALT2  | IMCR(557))
#define PIN_EIRQ29_3                (PIN_PORTD | PIN24 | PIN_INPUT_ALT3  | IMCR(557))
#define PIN_EIRQ29_4                (PIN_PORTG | PIN13 | PIN_INPUT_ALT4  | IMCR(557))
#define PIN_EIRQ30_1                (PIN_PORTB | PIN16 | PIN_INPUT_ALT1  | IMCR(558))
#define PIN_EIRQ30_2                (PIN_PORTD | PIN14 | PIN_INPUT_ALT3  | IMCR(558))
#define PIN_EIRQ30_3                (PIN_PORTD | PIN27 | PIN_INPUT_ALT2  | IMCR(558))
#define PIN_EIRQ30_4                (PIN_PORTG | PIN14 | PIN_INPUT_ALT4  | IMCR(558))
#define PIN_EIRQ31_1                (PIN_PORTB | PIN17 | PIN_INPUT_ALT1  | IMCR(559))
#define PIN_EIRQ31_2                (PIN_PORTD | PIN15 | PIN_INPUT_ALT2  | IMCR(559))
#define PIN_EIRQ31_3                (PIN_PORTD | PIN28 | PIN_INPUT_ALT3  | IMCR(559))
#define PIN_EIRQ31_4                (PIN_PORTG | PIN15 | PIN_INPUT_ALT4  | IMCR(559))

/* Ethernet MAC (EMAC) */

#define PIN_EMAC_MII_COL_1          (PIN_PORTB | PIN23 | PIN_INPUT_ALT1  | IMCR(801))
#define PIN_EMAC_MII_COL_2          (PIN_PORTC | PIN14 | PIN_INPUT_ALT2  | IMCR(801))
#define PIN_EMAC_MII_CRS_1          (PIN_PORTB | PIN22 | PIN_INPUT_ALT1  | IMCR(802))
#define PIN_EMAC_MII_CRS_2          (PIN_PORTC | PIN15 | PIN_INPUT_ALT2  | IMCR(802))
#define PIN_EMAC_MII_RMII_MDC_1     (PIN_PORTB | PIN5  | PIN_OUTPUT_ALT7)
#define PIN_EMAC_MII_RMII_MDC_2     (PIN_PORTD | PIN17 | PIN_OUTPUT_ALT3)
#define PIN_EMAC_MII_RMII_MDC_3     (PIN_PORTE | PIN8  | PIN_OUTPUT_ALT5)
#define PIN_EMAC_MII_RMII_MDIO_1    (PIN_PORTB | PIN4  | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT1  | IMCR(803))
#define PIN_EMAC_MII_RMII_MDIO_2    (PIN_PORTD | PIN16 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(803))
#define PIN_EMAC_MII_RMII_RX_DV_1   (PIN_PORTC | PIN15 | PIN_INPUT_ALT2  | IMCR(804))
#define PIN_EMAC_MII_RMII_RX_DV_2   (PIN_PORTC | PIN17 | PIN_INPUT_ALT1  | IMCR(804))
#define PIN_EMAC_MII_RMII_RX_ER_1   (PIN_PORTC | PIN14 | PIN_INPUT_ALT2  | IMCR(805))
#define PIN_EMAC_MII_RMII_RX_ER_2   (PIN_PORTC | PIN16 | PIN_INPUT_ALT1  | IMCR(805))
#define PIN_EMAC_MII_RMII_RXD0_1    (PIN_PORTC | PIN0  | PIN_INPUT_ALT2  | IMCR(806))
#define PIN_EMAC_MII_RMII_RXD0_2    (PIN_PORTC | PIN1  | PIN_INPUT_ALT1  | IMCR(806))
#define PIN_EMAC_MII_RMII_RXD0_3    (PIN_PORTD | PIN9  | PIN_INPUT_ALT3  | IMCR(806))
#define PIN_EMAC_MII_RMII_RXD1_1    (PIN_PORTC | PIN0  | PIN_INPUT_ALT1  | IMCR(807))
#define PIN_EMAC_MII_RMII_RXD1_2    (PIN_PORTC | PIN1  | PIN_INPUT_ALT2  | IMCR(807))
#define PIN_EMAC_MII_RMII_RXD1_3    (PIN_PORTD | PIN8  | PIN_INPUT_ALT3  | IMCR(807))
#define PIN_EMAC_MII_RMII_TX_CLK_1  (PIN_PORTC | PIN0  | PIN_INPUT_ALT4  | IMCR(808))
#define PIN_EMAC_MII_RMII_TX_CLK_2  (PIN_PORTD | PIN6  | PIN_INPUT_ALT2  | IMCR(808))
#define PIN_EMAC_MII_RMII_TX_CLK_3  (PIN_PORTD | PIN11 | PIN_INPUT_ALT1  | IMCR(808))
#define PIN_EMAC_MII_RMII_TX_CLK_4  (PIN_PORTD | PIN12 | PIN_INPUT_ALT3  | IMCR(808))
#define PIN_EMAC_MII_RMII_TX_EN_1   (PIN_PORTD | PIN11 | PIN_OUTPUT_ALT3)
#define PIN_EMAC_MII_RMII_TX_EN_2   (PIN_PORTD | PIN12 | PIN_OUTPUT_ALT5)
#define PIN_EMAC_MII_RMII_TX_EN_3   (PIN_PORTE | PIN9  | PIN_OUTPUT_ALT6)
#define PIN_EMAC_MII_RMII_TXD0_1    (PIN_PORTB | PIN5  | PIN_OUTPUT_ALT1)
#define PIN_EMAC_MII_RMII_TXD0_2    (PIN_PORTC | PIN2  | PIN_OUTPUT_ALT5)
#define PIN_EMAC_MII_RMII_TXD0_3    (PIN_PORTD | PIN7  | PIN_OUTPUT_ALT1)
#define PIN_EMAC_MII_RMII_TXD1_1    (PIN_PORTB | PIN4  | PIN_OUTPUT_ALT1)
#define PIN_EMAC_MII_RMII_TXD1_2    (PIN_PORTC | PIN2  | PIN_OUTPUT_ALT1)
#define PIN_EMAC_MII_RMII_TXD1_3    (PIN_PORTD | PIN7  | PIN_OUTPUT_ALT5)
#define PIN_EMAC_MII_RX_CLK_1       (PIN_PORTC | PIN1  | PIN_INPUT_ALT3  | IMCR(812))
#define PIN_EMAC_MII_RX_CLK_2       (PIN_PORTD | PIN5  | PIN_INPUT_ALT2  | IMCR(812))
#define PIN_EMAC_MII_RX_CLK_3       (PIN_PORTD | PIN10 | PIN_INPUT_ALT1  | IMCR(812))
#define PIN_EMAC_MII_RXD2_1         (PIN_PORTC | PIN15 | PIN_INPUT_ALT2  | IMCR(813))
#define PIN_EMAC_MII_RXD2_2         (PIN_PORTD | PIN9  | PIN_INPUT_ALT1  | IMCR(813))
#define PIN_EMAC_MII_RXD3_1         (PIN_PORTC | PIN14 | PIN_INPUT_ALT2  | IMCR(814))
#define PIN_EMAC_MII_RXD3_2         (PIN_PORTD | PIN8  | PIN_INPUT_ALT1  | IMCR(814))
#define PIN_EMAC_MII_TXD2_1         (PIN_PORTD | PIN5  | PIN_OUTPUT_ALT1)
#define PIN_EMAC_MII_TXD2_2         (PIN_PORTD | PIN6  | PIN_OUTPUT_ALT5)
#define PIN_EMAC_MII_TXD2_3         (PIN_PORTD | PIN11 | PIN_OUTPUT_ALT1)
#define PIN_EMAC_MII_TXD3_1         (PIN_PORTD | PIN5  | PIN_OUTPUT_ALT5)
#define PIN_EMAC_MII_TXD3_2         (PIN_PORTD | PIN6  | PIN_OUTPUT_ALT1)
#define PIN_EMAC_MII_TXD3_3         (PIN_PORTD | PIN10 | PIN_OUTPUT_ALT1)
#define PIN_EMAC_PPS0_1             (PIN_PORTA | PIN26 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT3  | IMCR(656))
#define PIN_EMAC_PPS0_2             (PIN_PORTD | PIN14 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT2  | IMCR(656))
#define PIN_EMAC_PPS0_3             (PIN_PORTE | PIN3  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(656))
#define PIN_EMAC_PPS1_1             (PIN_PORTA | PIN27 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(657))
#define PIN_EMAC_PPS1_2             (PIN_PORTD | PIN13 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT2  | IMCR(657))
#define PIN_EMAC_PPS1_3             (PIN_PORTE | PIN14 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(657))
#define PIN_EMAC_PPS2_1             (PIN_PORTA | PIN29 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(658))
#define PIN_EMAC_PPS2_2             (PIN_PORTD | PIN15 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT2  | IMCR(658))
#define PIN_EMAC_PPS2_3             (PIN_PORTD | PIN17 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(658))
#define PIN_EMAC_PPS3_1             (PIN_PORTB | PIN28 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT2  | IMCR(659))
#define PIN_EMAC_PPS3_2             (PIN_PORTE | PIN9  | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT1  | IMCR(659))
#define PIN_EMAC_PPS3_3             (PIN_PORTE | PIN12 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT3  | IMCR(659))

/* Enhanced Modular I/O Subsystem (eMIOS) */

#define PIN_EMIOS0_CH0_1            (PIN_PORTB | PIN12 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(560))
#define PIN_EMIOS0_CH0_2            (PIN_PORTC | PIN0  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(560))
#define PIN_EMIOS0_CH0_3            (PIN_PORTD | PIN15 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(560))
#define PIN_EMIOS0_CH1_1            (PIN_PORTB | PIN13 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(561))
#define PIN_EMIOS0_CH1_2            (PIN_PORTC | PIN1  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(561))
#define PIN_EMIOS0_CH1_3            (PIN_PORTD | PIN16 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(561))
#define PIN_EMIOS0_CH1_4            (PIN_PORTE | PIN11 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(561))
#define PIN_EMIOS0_CH2_1            (PIN_PORTB | PIN14 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(562))
#define PIN_EMIOS0_CH2_2            (PIN_PORTC | PIN2  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(562))
#define PIN_EMIOS0_CH2_3            (PIN_PORTD | PIN0  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(562))
#define PIN_EMIOS0_CH2_4            (PIN_PORTD | PIN5  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(562))
#define PIN_EMIOS0_CH3_1            (PIN_PORTB | PIN0  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT4  | IMCR(563))
#define PIN_EMIOS0_CH3_2            (PIN_PORTB | PIN15 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(563))
#define PIN_EMIOS0_CH3_3            (PIN_PORTC | PIN3  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(563))
#define PIN_EMIOS0_CH3_4            (PIN_PORTD | PIN1  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(563))
#define PIN_EMIOS0_CH3_5            (PIN_PORTE | PIN2  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT5  | IMCR(563))
#define PIN_EMIOS0_CH4_1            (PIN_PORTB | PIN4  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(564))
#define PIN_EMIOS0_CH4_2            (PIN_PORTB | PIN16 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(564))
#define PIN_EMIOS0_CH5_1            (PIN_PORTB | PIN5  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(565))
#define PIN_EMIOS0_CH5_2            (PIN_PORTB | PIN17 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(565))
#define PIN_EMIOS0_CH6_1            (PIN_PORTA | PIN17 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(566))
#define PIN_EMIOS0_CH6_2            (PIN_PORTC | PIN10 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT4  | IMCR(566))
#define PIN_EMIOS0_CH6_3            (PIN_PORTE | PIN8  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(566))
#define PIN_EMIOS0_CH7_1            (PIN_PORTB | PIN1  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(567))
#define PIN_EMIOS0_CH7_2            (PIN_PORTE | PIN7  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(567))
#define PIN_EMIOS0_CH7_3            (PIN_PORTE | PIN9  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(567))
#define PIN_EMIOS0_CH8_1            (PIN_PORTB | PIN2  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(568))
#define PIN_EMIOS0_CH8_2            (PIN_PORTC | PIN4  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(568))
#define PIN_EMIOS0_CH8_3            (PIN_PORTC | PIN22 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT3  | IMCR(568))
#define PIN_EMIOS0_CH9_1            (PIN_PORTA | PIN1  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(569))
#define PIN_EMIOS0_CH9_2            (PIN_PORTB | PIN3  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(569))
#define PIN_EMIOS0_CH10_1           (PIN_PORTA | PIN15 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(570))
#define PIN_EMIOS0_CH10_2           (PIN_PORTC | PIN14 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(570))
#define PIN_EMIOS0_CH11_1           (PIN_PORTA | PIN16 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(571))
#define PIN_EMIOS0_CH11_2           (PIN_PORTC | PIN15 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(571))
#define PIN_EMIOS0_CH12_1           (PIN_PORTA | PIN10 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(572))
#define PIN_EMIOS0_CH12_2           (PIN_PORTD | PIN8  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(572))
#define PIN_EMIOS0_CH13_1           (PIN_PORTA | PIN11 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(573))
#define PIN_EMIOS0_CH13_2           (PIN_PORTD | PIN9  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(573))
#define PIN_EMIOS0_CH14_1           (PIN_PORTA | PIN12 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(574))
#define PIN_EMIOS0_CH14_2           (PIN_PORTC | PIN0  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(574))
#define PIN_EMIOS0_CH15_1           (PIN_PORTA | PIN13 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(575))
#define PIN_EMIOS0_CH15_2           (PIN_PORTC | PIN1  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(575))
#define PIN_EMIOS0_CH16_1           (PIN_PORTC | PIN5  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(576))
#define PIN_EMIOS0_CH16_2           (PIN_PORTD | PIN0  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT1  | IMCR(576))
#define PIN_EMIOS0_CH16_3           (PIN_PORTD | PIN10 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(576))
#define PIN_EMIOS0_CH17_1           (PIN_PORTA | PIN0  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(577))
#define PIN_EMIOS0_CH17_2           (PIN_PORTD | PIN1  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT1  | IMCR(577))
#define PIN_EMIOS0_CH17_3           (PIN_PORTD | PIN11 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(577))
#define PIN_EMIOS0_CH18_1           (PIN_PORTD | PIN12 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(578))
#define PIN_EMIOS0_CH18_2           (PIN_PORTD | PIN17 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(578))
#define PIN_EMIOS0_CH18_3           (PIN_PORTE | PIN4  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(578))
#define PIN_EMIOS0_CH19_1           (PIN_PORTD | PIN5  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(579))
#define PIN_EMIOS0_CH19_2           (PIN_PORTE | PIN3  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(579))
#define PIN_EMIOS0_CH19_3           (PIN_PORTE | PIN5  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT1  | IMCR(579))
#define PIN_EMIOS0_CH19_4           (PIN_PORTE | PIN14 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT3  | IMCR(579))
#define PIN_EMIOS0_CH20_1           (PIN_PORTD | PIN13 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(580))
#define PIN_EMIOS0_CH20_2           (PIN_PORTE | PIN10 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(580))
#define PIN_EMIOS0_CH21_1           (PIN_PORTD | PIN14 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(581))
#define PIN_EMIOS0_CH21_2           (PIN_PORTE | PIN11 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(581))
#define PIN_EMIOS0_CH22_1           (PIN_PORTC | PIN12 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(582))
#define PIN_EMIOS0_CH22_2           (PIN_PORTE | PIN15 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT1  | IMCR(582))
#define PIN_EMIOS0_CH22_3           (PIN_PORTE | PIN19 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(582))
#define PIN_EMIOS0_CH23_1           (PIN_PORTC | PIN13 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(583))
#define PIN_EMIOS0_CH23_2           (PIN_PORTE | PIN16 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(583))

#define PIN_EMIOS1_CH0_1            (PIN_PORTA | PIN0  | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT3  | IMCR(592))
#define PIN_EMIOS1_CH0_2            (PIN_PORTA | PIN18 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(592))
#define PIN_EMIOS1_CH0_3            (PIN_PORTC | PIN10 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT6  | IMCR(592))
#define PIN_EMIOS1_CH0_4            (PIN_PORTC | PIN24 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT4  | IMCR(592))
#define PIN_EMIOS1_CH0_5            (PIN_PORTE | PIN20 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(592))
#define PIN_EMIOS1_CH0_6            (PIN_PORTF | PIN16 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT5  | IMCR(592))
#define PIN_EMIOS1_CH1_1            (PIN_PORTA | PIN11 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(593))
#define PIN_EMIOS1_CH1_2            (PIN_PORTA | PIN19 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(593))
#define PIN_EMIOS1_CH1_3            (PIN_PORTC | PIN11 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT7  | IMCR(593))
#define PIN_EMIOS1_CH1_4            (PIN_PORTC | PIN25 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT4  | IMCR(593))
#define PIN_EMIOS1_CH1_5            (PIN_PORTE | PIN21 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(593))
#define PIN_EMIOS1_CH1_6            (PIN_PORTF | PIN17 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT5  | IMCR(593))
#define PIN_EMIOS1_CH2_1            (PIN_PORTA | PIN12 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT4  | IMCR(594))
#define PIN_EMIOS1_CH2_2            (PIN_PORTA | PIN20 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(594))
#define PIN_EMIOS1_CH2_3            (PIN_PORTC | PIN12 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(594))
#define PIN_EMIOS1_CH2_4            (PIN_PORTE | PIN22 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(594))
#define PIN_EMIOS1_CH2_5            (PIN_PORTF | PIN18 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT5  | IMCR(594))
#define PIN_EMIOS1_CH3_1            (PIN_PORTA | PIN13 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT4  | IMCR(595))
#define PIN_EMIOS1_CH3_2            (PIN_PORTA | PIN21 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(595))
#define PIN_EMIOS1_CH3_3            (PIN_PORTC | PIN13 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT7  | IMCR(595))
#define PIN_EMIOS1_CH3_4            (PIN_PORTC | PIN26 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(595))
#define PIN_EMIOS1_CH3_5            (PIN_PORTE | PIN23 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(595))
#define PIN_EMIOS1_CH3_6            (PIN_PORTF | PIN19 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT5  | IMCR(595))
#define PIN_EMIOS1_CH4_1            (PIN_PORTA | PIN14 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT7  | IMCR(596))
#define PIN_EMIOS1_CH4_2            (PIN_PORTA | PIN22 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(596))
#define PIN_EMIOS1_CH4_3            (PIN_PORTC | PIN14 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT8  | IMCR(596))
#define PIN_EMIOS1_CH4_4            (PIN_PORTC | PIN27 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(596))
#define PIN_EMIOS1_CH4_5            (PIN_PORTE | PIN4  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(596))
#define PIN_EMIOS1_CH4_6            (PIN_PORTE | PIN24 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(596))
#define PIN_EMIOS1_CH4_7            (PIN_PORTF | PIN20 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT5  | IMCR(596))
#define PIN_EMIOS1_CH5_1            (PIN_PORTB | PIN1  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(597))
#define PIN_EMIOS1_CH5_2            (PIN_PORTE | PIN5  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(597))
#define PIN_EMIOS1_CH5_3            (PIN_PORTE | PIN12 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT5  | IMCR(597))
#define PIN_EMIOS1_CH5_4            (PIN_PORTE | PIN13 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(597))
#define PIN_EMIOS1_CH5_5            (PIN_PORTE | PIN25 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(597))
#define PIN_EMIOS1_CH5_6            (PIN_PORTF | PIN21 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT6  | IMCR(597))
#define PIN_EMIOS1_CH6_1            (PIN_PORTA | PIN23 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(598))
#define PIN_EMIOS1_CH6_2            (PIN_PORTB | PIN0  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(598))
#define PIN_EMIOS1_CH6_3            (PIN_PORTC | PIN6  | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT4  | IMCR(598))
#define PIN_EMIOS1_CH6_4            (PIN_PORTE | PIN26 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(598))
#define PIN_EMIOS1_CH6_5            (PIN_PORTF | PIN22 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT5  | IMCR(598))
#define PIN_EMIOS1_CH7_1            (PIN_PORTA | PIN24 | PIN_INPUT_ALT2  | IMCR(599))
#define PIN_EMIOS1_CH7_2            (PIN_PORTB | PIN17 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT7  | IMCR(599))
#define PIN_EMIOS1_CH7_3            (PIN_PORTC | PIN7  | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT4  | IMCR(599))
#define PIN_EMIOS1_CH7_4            (PIN_PORTC | PIN28 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(599))
#define PIN_EMIOS1_CH7_5            (PIN_PORTE | PIN27 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(599))
#define PIN_EMIOS1_CH7_6            (PIN_PORTF | PIN23 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT5  | IMCR(599))
#define PIN_EMIOS1_CH8_1            (PIN_PORTA | PIN25 | PIN_INPUT_ALT2  | IMCR(600))
#define PIN_EMIOS1_CH8_2            (PIN_PORTC | PIN9  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT1  | IMCR(600))
#define PIN_EMIOS1_CH8_3            (PIN_PORTE | PIN2  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT4  | IMCR(600))
#define PIN_EMIOS1_CH8_4            (PIN_PORTF | PIN24 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(600))
#define PIN_EMIOS1_CH9_1            (PIN_PORTA | PIN26 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(601))
#define PIN_EMIOS1_CH9_2            (PIN_PORTC | PIN8  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT1  | IMCR(601))
#define PIN_EMIOS1_CH9_3            (PIN_PORTC | PIN16 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT5  | IMCR(601))
#define PIN_EMIOS1_CH9_4            (PIN_PORTF | PIN25 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(601))
#define PIN_EMIOS1_CH10_1           (PIN_PORTA | PIN27 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(602))
#define PIN_EMIOS1_CH10_2           (PIN_PORTB | PIN4  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT6  | IMCR(602))
#define PIN_EMIOS1_CH10_3           (PIN_PORTC | PIN29 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(602))
#define PIN_EMIOS1_CH10_4           (PIN_PORTD | PIN10 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(602))
#define PIN_EMIOS1_CH10_5           (PIN_PORTF | PIN26 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(602))
#define PIN_EMIOS1_CH11_1           (PIN_PORTA | PIN7  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(603))
#define PIN_EMIOS1_CH11_2           (PIN_PORTA | PIN28 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(603))
#define PIN_EMIOS1_CH11_3           (PIN_PORTB | PIN5  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT5  | IMCR(603))
#define PIN_EMIOS1_CH11_4           (PIN_PORTF | PIN27 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(603))
#define PIN_EMIOS1_CH12_1           (PIN_PORTA | PIN8  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(604))
#define PIN_EMIOS1_CH12_2           (PIN_PORTA | PIN29 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(604))
#define PIN_EMIOS1_CH12_3           (PIN_PORTC | PIN30 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(604))
#define PIN_EMIOS1_CH12_4           (PIN_PORTD | PIN6  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT5  | IMCR(604))
#define PIN_EMIOS1_CH12_5           (PIN_PORTF | PIN28 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(604))
#define PIN_EMIOS1_CH13_1           (PIN_PORTA | PIN6  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT1  | IMCR(605))
#define PIN_EMIOS1_CH13_2           (PIN_PORTA | PIN30 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(605))
#define PIN_EMIOS1_CH13_3           (PIN_PORTE | PIN9  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT5  | IMCR(605))
#define PIN_EMIOS1_CH13_4           (PIN_PORTF | PIN29 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(605))
#define PIN_EMIOS1_CH14_1           (PIN_PORTA | PIN31 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(606))
#define PIN_EMIOS1_CH14_2           (PIN_PORTC | PIN31 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(606))
#define PIN_EMIOS1_CH14_3           (PIN_PORTD | PIN15 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT6  | IMCR(606))
#define PIN_EMIOS1_CH14_4           (PIN_PORTE | PIN6  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT5  | IMCR(606))
#define PIN_EMIOS1_CH14_5           (PIN_PORTF | PIN30 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(606))
#define PIN_EMIOS1_CH15_1           (PIN_PORTB | PIN8  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT6  | IMCR(607))
#define PIN_EMIOS1_CH15_2           (PIN_PORTB | PIN18 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(607))
#define PIN_EMIOS1_CH15_3           (PIN_PORTB | PIN19 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(607))
#define PIN_EMIOS1_CH15_4           (PIN_PORTD | PIN16 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT7  | IMCR(607))
#define PIN_EMIOS1_CH15_5           (PIN_PORTD | PIN18 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(607))
#define PIN_EMIOS1_CH15_6           (PIN_PORTF | PIN31 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(607))
#define PIN_EMIOS1_CH16_1           (PIN_PORTA | PIN18 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT1  | IMCR(608))
#define PIN_EMIOS1_CH16_2           (PIN_PORTB | PIN9  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT5  | IMCR(608))
#define PIN_EMIOS1_CH16_3           (PIN_PORTB | PIN20 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(608))
#define PIN_EMIOS1_CH16_4           (PIN_PORTD | PIN19 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(608))
#define PIN_EMIOS1_CH16_5           (PIN_PORTG | PIN0  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(608))
#define PIN_EMIOS1_CH17_1           (PIN_PORTB | PIN10 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT4  | IMCR(609))
#define PIN_EMIOS1_CH17_2           (PIN_PORTB | PIN21 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(609))
#define PIN_EMIOS1_CH17_3           (PIN_PORTD | PIN20 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(609))
#define PIN_EMIOS1_CH17_4           (PIN_PORTG | PIN1  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(609))
#define PIN_EMIOS1_CH18_1           (PIN_PORTB | PIN11 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT4  | IMCR(610))
#define PIN_EMIOS1_CH18_2           (PIN_PORTB | PIN22 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(610))
#define PIN_EMIOS1_CH18_3           (PIN_PORTD | PIN21 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(610))
#define PIN_EMIOS1_CH18_4           (PIN_PORTG | PIN2  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(610))
#define PIN_EMIOS1_CH19_1           (PIN_PORTA | PIN2  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT4  | IMCR(611))
#define PIN_EMIOS1_CH19_2           (PIN_PORTB | PIN23 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(611))
#define PIN_EMIOS1_CH19_3           (PIN_PORTD | PIN22 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(611))
#define PIN_EMIOS1_CH19_4           (PIN_PORTG | PIN3  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(611))
#define PIN_EMIOS1_CH20_1           (PIN_PORTA | PIN3  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT4  | IMCR(612))
#define PIN_EMIOS1_CH20_2           (PIN_PORTB | PIN24 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(612))
#define PIN_EMIOS1_CH20_3           (PIN_PORTD | PIN23 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(612))
#define PIN_EMIOS1_CH20_4           (PIN_PORTG | PIN4  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(612))
#define PIN_EMIOS1_CH21_1           (PIN_PORTB | PIN25 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(613))
#define PIN_EMIOS1_CH21_2           (PIN_PORTD | PIN2  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT4  | IMCR(613))
#define PIN_EMIOS1_CH21_3           (PIN_PORTD | PIN24 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(613))
#define PIN_EMIOS1_CH21_4           (PIN_PORTG | PIN5  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(613))
#define PIN_EMIOS1_CH22_1           (PIN_PORTB | PIN26 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(614))
#define PIN_EMIOS1_CH22_2           (PIN_PORTD | PIN3  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT4  | IMCR(614))
#define PIN_EMIOS1_CH22_3           (PIN_PORTD | PIN25 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(614))
#define PIN_EMIOS1_CH22_4           (PIN_PORTG | PIN6  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(614))
#define PIN_EMIOS1_CH23_1           (PIN_PORTB | PIN27 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(615))
#define PIN_EMIOS1_CH23_2           (PIN_PORTD | PIN4  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT4  | IMCR(615))
#define PIN_EMIOS1_CH23_3           (PIN_PORTD | PIN26 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(615))
#define PIN_EMIOS1_CH23_4           (PIN_PORTG | PIN7  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(615))

#define PIN_EMIOS2_CH0_1            (PIN_PORTA | PIN18 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(624))
#define PIN_EMIOS2_CH0_2            (PIN_PORTC | PIN24 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT5  | IMCR(624))
#define PIN_EMIOS2_CH0_3            (PIN_PORTD | PIN20 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(624))
#define PIN_EMIOS2_CH0_4            (PIN_PORTD | PIN28 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT4  | IMCR(624))
#define PIN_EMIOS2_CH0_5            (PIN_PORTF | PIN0  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(624))
#define PIN_EMIOS2_CH1_1            (PIN_PORTA | PIN19 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(625))
#define PIN_EMIOS2_CH1_2            (PIN_PORTC | PIN25 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(625))
#define PIN_EMIOS2_CH1_3            (PIN_PORTF | PIN1  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(625))
#define PIN_EMIOS2_CH2_1            (PIN_PORTA | PIN20 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(626))
#define PIN_EMIOS2_CH2_2            (PIN_PORTC | PIN26 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(626))
#define PIN_EMIOS2_CH2_3            (PIN_PORTF | PIN2  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(626))
#define PIN_EMIOS2_CH3_1            (PIN_PORTA | PIN21 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(627))
#define PIN_EMIOS2_CH3_2            (PIN_PORTC | PIN27 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(627))
#define PIN_EMIOS2_CH3_3            (PIN_PORTF | PIN3  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(627))
#define PIN_EMIOS2_CH4_1            (PIN_PORTC | PIN29 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(628))
#define PIN_EMIOS2_CH4_2            (PIN_PORTE | PIN24 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(628))
#define PIN_EMIOS2_CH4_3            (PIN_PORTF | PIN4  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(628))
#define PIN_EMIOS2_CH5_1            (PIN_PORTC | PIN30 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(629))
#define PIN_EMIOS2_CH5_2            (PIN_PORTE | PIN25 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(629))
#define PIN_EMIOS2_CH5_3            (PIN_PORTF | PIN5  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(629))
#define PIN_EMIOS2_CH6_1            (PIN_PORTC | PIN31 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT2  | IMCR(630))
#define PIN_EMIOS2_CH6_2            (PIN_PORTE | PIN26 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(630))
#define PIN_EMIOS2_CH6_3            (PIN_PORTF | PIN6  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(630))
#define PIN_EMIOS2_CH7_1            (PIN_PORTA | PIN8  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT3  | IMCR(631))
#define PIN_EMIOS2_CH7_2            (PIN_PORTD | PIN26 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT1  | IMCR(631))
#define PIN_EMIOS2_CH7_3            (PIN_PORTF | PIN7  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(631))
#define PIN_EMIOS2_CH8_1            (PIN_PORTA | PIN25 | PIN_INPUT_ALT2  | IMCR(632))
#define PIN_EMIOS2_CH8_2            (PIN_PORTD | PIN21 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(632))
#define PIN_EMIOS2_CH8_3            (PIN_PORTD | PIN29 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT4  | IMCR(632))
#define PIN_EMIOS2_CH8_4            (PIN_PORTF | PIN8  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(632))
#define PIN_EMIOS2_CH9_1            (PIN_PORTA | PIN26 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(633))
#define PIN_EMIOS2_CH9_2            (PIN_PORTC | PIN16 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(633))
#define PIN_EMIOS2_CH9_3            (PIN_PORTD | PIN27 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(633))
#define PIN_EMIOS2_CH9_4            (PIN_PORTF | PIN9  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(633))
#define PIN_EMIOS2_CH10_1           (PIN_PORTA | PIN27 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(634))
#define PIN_EMIOS2_CH10_2           (PIN_PORTB | PIN28 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(634))
#define PIN_EMIOS2_CH10_3           (PIN_PORTF | PIN10 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(634))
#define PIN_EMIOS2_CH11_1           (PIN_PORTA | PIN28 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(635))
#define PIN_EMIOS2_CH11_2           (PIN_PORTB | PIN29 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(635))
#define PIN_EMIOS2_CH11_3           (PIN_PORTF | PIN11 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(635))
#define PIN_EMIOS2_CH12_1           (PIN_PORTA | PIN29 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(636))
#define PIN_EMIOS2_CH12_2           (PIN_PORTC | PIN18 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(636))
#define PIN_EMIOS2_CH12_3           (PIN_PORTF | PIN12 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(636))
#define PIN_EMIOS2_CH13_1           (PIN_PORTA | PIN30 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(637))
#define PIN_EMIOS2_CH13_2           (PIN_PORTC | PIN19 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(637))
#define PIN_EMIOS2_CH13_3           (PIN_PORTF | PIN13 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(637))
#define PIN_EMIOS2_CH14_1           (PIN_PORTB | PIN18 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT2  | IMCR(638))
#define PIN_EMIOS2_CH14_2           (PIN_PORTC | PIN20 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(638))
#define PIN_EMIOS2_CH14_3           (PIN_PORTF | PIN14 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(638))
#define PIN_EMIOS2_CH15_1           (PIN_PORTB | PIN19 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT3  | IMCR(639))
#define PIN_EMIOS2_CH15_2           (PIN_PORTC | PIN21 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT5  | IMCR(639))
#define PIN_EMIOS2_CH15_3           (PIN_PORTF | PIN15 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(639))
#define PIN_EMIOS2_CH15_4           (PIN_PORTG | PIN15 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT4  | IMCR(639))
#define PIN_EMIOS2_CH16_1           (PIN_PORTB | PIN20 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT2  | IMCR(640))
#define PIN_EMIOS2_CH16_2           (PIN_PORTD | PIN30 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(640))
#define PIN_EMIOS2_CH16_3           (PIN_PORTF | PIN16 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT4  | IMCR(640))
#define PIN_EMIOS2_CH16_4           (PIN_PORTG | PIN16 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(640))
#define PIN_EMIOS2_CH17_1           (PIN_PORTB | PIN21 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT2  | IMCR(641))
#define PIN_EMIOS2_CH17_2           (PIN_PORTE | PIN18 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT4  | IMCR(641))
#define PIN_EMIOS2_CH17_3           (PIN_PORTF | PIN17 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(641))
#define PIN_EMIOS2_CH17_4           (PIN_PORTG | PIN17 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(641))
#define PIN_EMIOS2_CH18_1           (PIN_PORTA | PIN14 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT4  | IMCR(642))
#define PIN_EMIOS2_CH18_2           (PIN_PORTB | PIN22 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(642))
#define PIN_EMIOS2_CH18_3           (PIN_PORTF | PIN18 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(642))
#define PIN_EMIOS2_CH18_4           (PIN_PORTG | PIN18 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(642))
#define PIN_EMIOS2_CH19_1           (PIN_PORTB | PIN23 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(643))
#define PIN_EMIOS2_CH19_2           (PIN_PORTE | PIN21 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT4  | IMCR(643))
#define PIN_EMIOS2_CH19_3           (PIN_PORTF | PIN19 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(643))
#define PIN_EMIOS2_CH19_4           (PIN_PORTG | PIN19 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(643))
#define PIN_EMIOS2_CH20_1           (PIN_PORTB | PIN24 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(644))
#define PIN_EMIOS2_CH20_2           (PIN_PORTE | PIN22 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT4  | IMCR(644))
#define PIN_EMIOS2_CH20_3           (PIN_PORTF | PIN20 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(644))
#define PIN_EMIOS2_CH20_4           (PIN_PORTG | PIN20 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(644))
#define PIN_EMIOS2_CH21_1           (PIN_PORTB | PIN25 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(645))
#define PIN_EMIOS2_CH21_2           (PIN_PORTE | PIN23 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT4  | IMCR(645))
#define PIN_EMIOS2_CH21_3           (PIN_PORTF | PIN21 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(645))
#define PIN_EMIOS2_CH21_4           (PIN_PORTG | PIN21 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(645))
#define PIN_EMIOS2_CH22_1           (PIN_PORTB | PIN26 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(646))
#define PIN_EMIOS2_CH22_2           (PIN_PORTD | PIN22 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(646))
#define PIN_EMIOS2_CH22_3           (PIN_PORTD | PIN31 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT4  | IMCR(646))
#define PIN_EMIOS2_CH22_4           (PIN_PORTF | PIN22 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT5  | IMCR(646))
#define PIN_EMIOS2_CH22_5           (PIN_PORTG | PIN22 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(646))
#define PIN_EMIOS2_CH23_1           (PIN_PORTB | PIN27 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(647))
#define PIN_EMIOS2_CH23_2           (PIN_PORTD | PIN23 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(647))
#define PIN_EMIOS2_CH23_3           (PIN_PORTE | PIN17 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT4  | IMCR(647))
#define PIN_EMIOS2_CH23_4           (PIN_PORTF | PIN23 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT5  | IMCR(647))
#define PIN_EMIOS2_CH23_5           (PIN_PORTG | PIN23 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(647))

/* Fault Collection and Control Unit (FCCU) */

#define PIN_FCCU_ERR_IN0_1          (PIN_PORTA | PIN2  | PIN_INPUT_ALT1  | IMCR(660))
#define PIN_FCCU_ERR_IN0_2          (PIN_PORTE | PIN15 | PIN_INPUT_ALT2  | IMCR(660))
#define PIN_FCCU_ERR_IN0_3          (PIN_PORTF | PIN14 | PIN_INPUT_ALT3  | IMCR(660))
#define PIN_FCCU_ERR_IN1_1          (PIN_PORTA | PIN3  | PIN_INPUT_ALT1  | IMCR(661))
#define PIN_FCCU_ERR_IN1_2          (PIN_PORTE | PIN16 | PIN_INPUT_ALT2  | IMCR(661))
#define PIN_FCCU_ERR_IN1_3          (PIN_PORTF | PIN15 | PIN_INPUT_ALT3  | IMCR(661))
#define PIN_FCCU_ERR_OUT0_1         (PIN_PORTA | PIN2  | PIN_OUTPUT_ALT1)
#define PIN_FCCU_ERR_OUT0_2         (PIN_PORTE | PIN15 | PIN_OUTPUT_ALT1)
#define PIN_FCCU_ERR_OUT0_3         (PIN_PORTF | PIN14 | PIN_OUTPUT_ALT4)
#define PIN_FCCU_ERR_OUT1_1         (PIN_PORTA | PIN3  | PIN_OUTPUT_ALT1)
#define PIN_FCCU_ERR_OUT1_2         (PIN_PORTE | PIN16 | PIN_OUTPUT_ALT1)
#define PIN_FCCU_ERR_OUT1_3         (PIN_PORTF | PIN15 | PIN_OUTPUT_ALT5)

/* FlexIO */

#define PIN_FXIO_D0_1               (PIN_PORTA | PIN10 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(664))
#define PIN_FXIO_D0_2               (PIN_PORTA | PIN21 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(664))
#define PIN_FXIO_D0_3               (PIN_PORTA | PIN31 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT6  | IMCR(664))
#define PIN_FXIO_D0_4               (PIN_PORTC | PIN30 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(664))
#define PIN_FXIO_D0_5               (PIN_PORTD | PIN0  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(664))
#define PIN_FXIO_D0_6               (PIN_PORTD | PIN9  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT5  | IMCR(664))
#define PIN_FXIO_D0_7               (PIN_PORTF | PIN4  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT7  | IMCR(664))
#define PIN_FXIO_D1_1               (PIN_PORTA | PIN11 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(665))
#define PIN_FXIO_D1_2               (PIN_PORTA | PIN22 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(665))
#define PIN_FXIO_D1_3               (PIN_PORTA | PIN26 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT7  | IMCR(665))
#define PIN_FXIO_D1_4               (PIN_PORTB | PIN18 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT6  | IMCR(665))
#define PIN_FXIO_D1_5               (PIN_PORTC | PIN31 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(665))
#define PIN_FXIO_D1_6               (PIN_PORTD | PIN1  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(665))
#define PIN_FXIO_D1_7               (PIN_PORTD | PIN8  | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT5  | IMCR(665))
#define PIN_FXIO_D1_8               (PIN_PORTF | PIN7  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT8  | IMCR(665))
#define PIN_FXIO_D2_1               (PIN_PORTA | PIN0  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(666))
#define PIN_FXIO_D2_2               (PIN_PORTA | PIN23 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(666))
#define PIN_FXIO_D2_3               (PIN_PORTA | PIN25 | PIN_INPUT_ALT6  | IMCR(666))
#define PIN_FXIO_D2_4               (PIN_PORTB | PIN19 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT5  | IMCR(666))
#define PIN_FXIO_D2_5               (PIN_PORTC | PIN28 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT7  | IMCR(666))
#define PIN_FXIO_D2_6               (PIN_PORTD | PIN18 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(666))
#define PIN_FXIO_D2_7               (PIN_PORTE | PIN1  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT8  | IMCR(666))
#define PIN_FXIO_D2_8               (PIN_PORTE | PIN15 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(666))
#define PIN_FXIO_D2_9               (PIN_PORTF | PIN8  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT9  | IMCR(666))
#define PIN_FXIO_D3_1               (PIN_PORTA | PIN1  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT1  | IMCR(667))
#define PIN_FXIO_D3_2               (PIN_PORTA | PIN24 | PIN_INPUT_ALT3  | IMCR(667))
#define PIN_FXIO_D3_3               (PIN_PORTB | PIN20 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT5  | IMCR(667))
#define PIN_FXIO_D3_4               (PIN_PORTC | PIN29 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT6  | IMCR(667))
#define PIN_FXIO_D3_5               (PIN_PORTD | PIN19 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(667))
#define PIN_FXIO_D3_6               (PIN_PORTE | PIN0  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT7  | IMCR(667))
#define PIN_FXIO_D3_7               (PIN_PORTE | PIN16 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(667))
#define PIN_FXIO_D3_8               (PIN_PORTF | PIN14 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT8  | IMCR(667))
#define PIN_FXIO_D4_1               (PIN_PORTA | PIN2  | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT3  | IMCR(668))
#define PIN_FXIO_D4_2               (PIN_PORTB | PIN21 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT5  | IMCR(668))
#define PIN_FXIO_D4_3               (PIN_PORTB | PIN23 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT6  | IMCR(668))
#define PIN_FXIO_D4_4               (PIN_PORTC | PIN5  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT7  | IMCR(668))
#define PIN_FXIO_D4_5               (PIN_PORTD | PIN2  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT1  | IMCR(668))
#define PIN_FXIO_D4_6               (PIN_PORTE | PIN10 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(668))
#define PIN_FXIO_D4_7               (PIN_PORTE | PIN18 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(668))
#define PIN_FXIO_D4_8               (PIN_PORTF | PIN20 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT8  | IMCR(668))
#define PIN_FXIO_D5_1               (PIN_PORTA | PIN3  | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT3  | IMCR(669))
#define PIN_FXIO_D5_2               (PIN_PORTA | PIN27 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT9  | IMCR(669))
#define PIN_FXIO_D5_3               (PIN_PORTB | PIN24 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT6  | IMCR(669))
#define PIN_FXIO_D5_4               (PIN_PORTC | PIN1  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT7  | IMCR(669))
#define PIN_FXIO_D5_5               (PIN_PORTC | PIN4  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT8  | IMCR(669))
#define PIN_FXIO_D5_6               (PIN_PORTD | PIN3  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(669))
#define PIN_FXIO_D5_7               (PIN_PORTE | PIN11 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(669))
#define PIN_FXIO_D5_8               (PIN_PORTE | PIN13 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT5  | IMCR(669))
#define PIN_FXIO_D5_9               (PIN_PORTE | PIN17 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(669))
#define PIN_FXIO_D5_10              (PIN_PORTE | PIN24 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT10 | IMCR(669))
#define PIN_FXIO_D5_11              (PIN_PORTF | PIN21 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT11 | IMCR(669))
#define PIN_FXIO_D6_1               (PIN_PORTA | PIN4  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT8  | IMCR(670))
#define PIN_FXIO_D6_2               (PIN_PORTA | PIN8  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(670))
#define PIN_FXIO_D6_3               (PIN_PORTB | PIN25 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT5  | IMCR(670))
#define PIN_FXIO_D6_4               (PIN_PORTC | PIN18 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT9  | IMCR(670))
#define PIN_FXIO_D6_5               (PIN_PORTD | PIN2  | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT3  | IMCR(670))
#define PIN_FXIO_D6_6               (PIN_PORTD | PIN15 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT7  | IMCR(670))
#define PIN_FXIO_D6_7               (PIN_PORTD | PIN31 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(670))
#define PIN_FXIO_D6_8               (PIN_PORTE | PIN3  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT6  | IMCR(670))
#define PIN_FXIO_D6_9               (PIN_PORTE | PIN4  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(670))
#define PIN_FXIO_D6_10              (PIN_PORTF | PIN25 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT10 | IMCR(670))
#define PIN_FXIO_D7_1               (PIN_PORTA | PIN9  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(671))
#define PIN_FXIO_D7_2               (PIN_PORTB | PIN26 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT6  | IMCR(671))
#define PIN_FXIO_D7_3               (PIN_PORTD | PIN3  | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT3  | IMCR(671))
#define PIN_FXIO_D7_4               (PIN_PORTD | PIN13 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT7  | IMCR(671))
#define PIN_FXIO_D7_5               (PIN_PORTD | PIN26 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(671))
#define PIN_FXIO_D7_6               (PIN_PORTE | PIN5  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(671))
#define PIN_FXIO_D7_7               (PIN_PORTE | PIN14 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT5  | IMCR(671))
#define PIN_FXIO_D7_8               (PIN_PORTF | PIN28 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT8  | IMCR(671))
#define PIN_FXIO_D8_1               (PIN_PORTA | PIN13 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT4  | IMCR(672))
#define PIN_FXIO_D8_2               (PIN_PORTB | PIN13 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(672))
#define PIN_FXIO_D8_3               (PIN_PORTB | PIN27 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(672))
#define PIN_FXIO_D8_4               (PIN_PORTE | PIN8  | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT5  | IMCR(672))
#define PIN_FXIO_D8_5               (PIN_PORTE | PIN12 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(672))
#define PIN_FXIO_D8_6               (PIN_PORTF | PIN29 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT6  | IMCR(672))
#define PIN_FXIO_D9_1               (PIN_PORTA | PIN7  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT3  | IMCR(673))
#define PIN_FXIO_D9_2               (PIN_PORTA | PIN12 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT4  | IMCR(673))
#define PIN_FXIO_D9_3               (PIN_PORTB | PIN28 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(673))
#define PIN_FXIO_D9_4               (PIN_PORTD | PIN17 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(673))
#define PIN_FXIO_D9_5               (PIN_PORTF | PIN31 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT5  | IMCR(673))
#define PIN_FXIO_D10_1              (PIN_PORTB | PIN29 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(674))
#define PIN_FXIO_D10_2              (PIN_PORTC | PIN7  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT3  | IMCR(674))
#define PIN_FXIO_D10_3              (PIN_PORTD | PIN9  | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT4  | IMCR(674))
#define PIN_FXIO_D10_4              (PIN_PORTD | PIN15 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(674))
#define PIN_FXIO_D10_5              (PIN_PORTG | PIN6  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT5  | IMCR(674))
#define PIN_FXIO_D11_1              (PIN_PORTB | PIN30 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(675))
#define PIN_FXIO_D11_2              (PIN_PORTC | PIN6  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(675))
#define PIN_FXIO_D11_3              (PIN_PORTD | PIN8  | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT5  | IMCR(675))
#define PIN_FXIO_D11_4              (PIN_PORTE | PIN7  | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT4  | IMCR(675))
#define PIN_FXIO_D11_5              (PIN_PORTE | PIN9  | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(675))
#define PIN_FXIO_D11_6              (PIN_PORTE | PIN24 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT6  | IMCR(675))
#define PIN_FXIO_D11_7              (PIN_PORTG | PIN7  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT7  | IMCR(675))
#define PIN_FXIO_D12_1              (PIN_PORTC | PIN8  | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT3  | IMCR(676))
#define PIN_FXIO_D12_2              (PIN_PORTC | PIN18 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(676))
#define PIN_FXIO_D12_3              (PIN_PORTE | PIN6  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT4  | IMCR(676))
#define PIN_FXIO_D12_4              (PIN_PORTE | PIN8  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT1  | IMCR(676))
#define PIN_FXIO_D12_5              (PIN_PORTG | PIN8  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT5  | IMCR(676))
#define PIN_FXIO_D13_1              (PIN_PORTC | PIN9  | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT3  | IMCR(677))
#define PIN_FXIO_D13_2              (PIN_PORTC | PIN19 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(677))
#define PIN_FXIO_D13_3              (PIN_PORTD | PIN6  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(677))
#define PIN_FXIO_D13_4              (PIN_PORTE | PIN2  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT4  | IMCR(677))
#define PIN_FXIO_D13_5              (PIN_PORTG | PIN13 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT5  | IMCR(677))
#define PIN_FXIO_D14_1              (PIN_PORTA | PIN14 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT4  | IMCR(678))
#define PIN_FXIO_D14_2              (PIN_PORTB | PIN0  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(678))
#define PIN_FXIO_D14_3              (PIN_PORTC | PIN17 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(678))
#define PIN_FXIO_D14_4              (PIN_PORTC | PIN20 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(678))
#define PIN_FXIO_D14_5              (PIN_PORTG | PIN15 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT5  | IMCR(678))
#define PIN_FXIO_D15_1              (PIN_PORTB | PIN22 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT5  | IMCR(679))
#define PIN_FXIO_D15_2              (PIN_PORTC | PIN11 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(679))
#define PIN_FXIO_D15_3              (PIN_PORTC | PIN16 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(679))
#define PIN_FXIO_D15_4              (PIN_PORTC | PIN21 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(679))
#define PIN_FXIO_D15_5              (PIN_PORTD | PIN5  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT4  | IMCR(679))
#define PIN_FXIO_D15_6              (PIN_PORTG | PIN16 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT6  | IMCR(679))
#define PIN_FXIO_D16_1              (PIN_PORTC | PIN13 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT3  | IMCR(680))
#define PIN_FXIO_D16_2              (PIN_PORTC | PIN14 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(680))
#define PIN_FXIO_D16_3              (PIN_PORTC | PIN23 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(680))
#define PIN_FXIO_D16_4              (PIN_PORTG | PIN17 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(680))
#define PIN_FXIO_D17_1              (PIN_PORTB | PIN3  | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(681))
#define PIN_FXIO_D17_2              (PIN_PORTC | PIN24 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(681))
#define PIN_FXIO_D17_3              (PIN_PORTG | PIN18 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(681))
#define PIN_FXIO_D18_1              (PIN_PORTB | PIN2  | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(682))
#define PIN_FXIO_D18_2              (PIN_PORTC | PIN25 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(682))
#define PIN_FXIO_D18_3              (PIN_PORTG | PIN19 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(682))
#define PIN_FXIO_D19_1              (PIN_PORTA | PIN6  | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT4  | IMCR(683))
#define PIN_FXIO_D19_2              (PIN_PORTA | PIN17 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(683))
#define PIN_FXIO_D19_3              (PIN_PORTC | PIN11 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT3  | IMCR(683))
#define PIN_FXIO_D19_4              (PIN_PORTC | PIN12 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT5  | IMCR(683))
#define PIN_FXIO_D19_5              (PIN_PORTC | PIN26 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(683))
#define PIN_FXIO_D19_6              (PIN_PORTG | PIN20 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT6  | IMCR(683))
#define PIN_FXIO_D20_1              (PIN_PORTB | PIN17 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(684))
#define PIN_FXIO_D20_2              (PIN_PORTC | PIN27 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(684))
#define PIN_FXIO_D20_3              (PIN_PORTG | PIN21 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(684))
#define PIN_FXIO_D21_1              (PIN_PORTB | PIN16 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(685))
#define PIN_FXIO_D21_2              (PIN_PORTC | PIN28 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(685))
#define PIN_FXIO_D21_3              (PIN_PORTG | PIN22 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(685))
#define PIN_FXIO_D22_1              (PIN_PORTB | PIN15 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(686))
#define PIN_FXIO_D22_2              (PIN_PORTC | PIN29 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(686))
#define PIN_FXIO_D22_3              (PIN_PORTG | PIN23 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(686))
#define PIN_FXIO_D23_1              (PIN_PORTB | PIN14 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(687))
#define PIN_FXIO_D23_2              (PIN_PORTC | PIN30 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT2  | IMCR(687))
#define PIN_FXIO_D23_3              (PIN_PORTG | PIN24 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(687))
#define PIN_FXIO_D24_1              (PIN_PORTB | PIN13 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(688))
#define PIN_FXIO_D24_2              (PIN_PORTC | PIN31 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT2  | IMCR(688))
#define PIN_FXIO_D24_3              (PIN_PORTG | PIN25 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(688))
#define PIN_FXIO_D25_1              (PIN_PORTB | PIN12 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(689))
#define PIN_FXIO_D25_2              (PIN_PORTD | PIN20 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(689))
#define PIN_FXIO_D25_3              (PIN_PORTG | PIN26 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(689))
#define PIN_FXIO_D26_1              (PIN_PORTB | PIN11 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(690))
#define PIN_FXIO_D26_2              (PIN_PORTD | PIN21 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(690))
#define PIN_FXIO_D26_3              (PIN_PORTG | PIN27 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(690))
#define PIN_FXIO_D27_1              (PIN_PORTB | PIN10 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(691))
#define PIN_FXIO_D27_2              (PIN_PORTD | PIN22 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(691))
#define PIN_FXIO_D28_1              (PIN_PORTB | PIN9  | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(692))
#define PIN_FXIO_D28_2              (PIN_PORTD | PIN23 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(692))
#define PIN_FXIO_D29_1              (PIN_PORTB | PIN8  | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(693))
#define PIN_FXIO_D29_2              (PIN_PORTD | PIN24 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(693))
#define PIN_FXIO_D30_1              (PIN_PORTA | PIN16 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(694))
#define PIN_FXIO_D30_2              (PIN_PORTD | PIN26 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT2  | IMCR(694))
#define PIN_FXIO_D31_1              (PIN_PORTA | PIN15 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(695))
#define PIN_FXIO_D31_2              (PIN_PORTD | PIN27 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT2  | IMCR(695))

/* Hardware Security Engine (HSE) */

#define PIN_HSE_TAMPER_EXTIN0_1     (PIN_PORTB | PIN1  | PIN_INPUT_ALT1  | IMCR(855))
#define PIN_HSE_TAMPER_EXTIN0_2     (PIN_PORTB | PIN31 | PIN_INPUT_ALT3  | IMCR(855))
#define PIN_HSE_TAMPER_EXTIN0_3     (PIN_PORTD | PIN24 | PIN_INPUT_ALT2  | IMCR(855))
#define PIN_HSE_TAMPER_EXTIN0_4     (PIN_PORTF | PIN11 | PIN_INPUT_ALT4  | IMCR(855))
#define PIN_HSE_TAMPER_LOOP_OUT0_1  (PIN_PORTB | PIN0  | PIN_OUTPUT_ALT7)
#define PIN_HSE_TAMPER_LOOP_OUT0_2  (PIN_PORTB | PIN30 | PIN_OUTPUT_ALT5)
#define PIN_HSE_TAMPER_LOOP_OUT0_3  (PIN_PORTD | PIN23 | PIN_OUTPUT_ALT5)
#define PIN_HSE_TAMPER_LOOP_OUT0_4  (PIN_PORTF | PIN19 | PIN_OUTPUT_ALT6)

/* JTAG Debug */

#define PIN_JTAG_TCK                (PIN_PORTC | PIN4  | PIN_INPUT_ALT0  | IMCR(696))
#define PIN_JTAG_TDI                (PIN_PORTC | PIN5  | PIN_INPUT_ALT0  | IMCR(697))
#define PIN_JTAG_TDO                (PIN_PORTA | PIN10 | PIN_OUTPUT_ALT7)
#define PIN_JTAG_TMS                (PIN_PORTA | PIN4  | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT0  | IMCR(698))

/* Logic Control Unit (LCU) */

#define PIN_LCU0_OUT0_1             (PIN_PORTD | PIN3  | PIN_OUTPUT_ALT6)
#define PIN_LCU0_OUT0_2             (PIN_PORTD | PIN26 | PIN_OUTPUT_ALT7)
#define PIN_LCU0_OUT1_1             (PIN_PORTD | PIN2  | PIN_OUTPUT_ALT1)
#define PIN_LCU0_OUT1_2             (PIN_PORTD | PIN27 | PIN_OUTPUT_ALT7)
#define PIN_LCU0_OUT2_1             (PIN_PORTA | PIN3  | PIN_OUTPUT_ALT4)
#define PIN_LCU0_OUT2_2             (PIN_PORTB | PIN12 | PIN_OUTPUT_ALT6)
#define PIN_LCU0_OUT2_3             (PIN_PORTD | PIN28 | PIN_OUTPUT_ALT5)
#define PIN_LCU0_OUT3_1             (PIN_PORTA | PIN2  | PIN_OUTPUT_ALT6)
#define PIN_LCU0_OUT3_2             (PIN_PORTB | PIN13 | PIN_OUTPUT_ALT5)
#define PIN_LCU0_OUT3_3             (PIN_PORTD | PIN29 | PIN_OUTPUT_ALT5)
#define PIN_LCU0_OUT4_1             (PIN_PORTA | PIN0  | PIN_OUTPUT_ALT3)
#define PIN_LCU0_OUT4_2             (PIN_PORTD | PIN21 | PIN_OUTPUT_ALT5)
#define PIN_LCU0_OUT5_1             (PIN_PORTA | PIN1  | PIN_OUTPUT_ALT5)
#define PIN_LCU0_OUT5_2             (PIN_PORTD | PIN22 | PIN_OUTPUT_ALT6)
#define PIN_LCU0_OUT6_1             (PIN_PORTC | PIN7  | PIN_OUTPUT_ALT4)
#define PIN_LCU0_OUT6_2             (PIN_PORTD | PIN4  | PIN_OUTPUT_ALT5)
#define PIN_LCU0_OUT7_1             (PIN_PORTB | PIN14 | PIN_OUTPUT_ALT4)
#define PIN_LCU0_OUT7_2             (PIN_PORTC | PIN6  | PIN_OUTPUT_ALT4)
#define PIN_LCU0_OUT8_1             (PIN_PORTB | PIN11 | PIN_OUTPUT_ALT5)
#define PIN_LCU0_OUT8_2             (PIN_PORTD | PIN30 | PIN_OUTPUT_ALT5)
#define PIN_LCU0_OUT9_1             (PIN_PORTB | PIN10 | PIN_OUTPUT_ALT6)
#define PIN_LCU0_OUT9_2             (PIN_PORTD | PIN31 | PIN_OUTPUT_ALT5)
#define PIN_LCU0_OUT10_1            (PIN_PORTB | PIN9  | PIN_OUTPUT_ALT6)
#define PIN_LCU0_OUT10_2            (PIN_PORTD | PIN23 | PIN_OUTPUT_ALT6)
#define PIN_LCU0_OUT11_1            (PIN_PORTB | PIN8  | PIN_OUTPUT_ALT5)
#define PIN_LCU0_OUT11_2            (PIN_PORTD | PIN24 | PIN_OUTPUT_ALT6)

#define PIN_LCU1_OUT0_1             (PIN_PORTC | PIN15 | PIN_OUTPUT_ALT6)
#define PIN_LCU1_OUT0_2             (PIN_PORTC | PIN23 | PIN_OUTPUT_ALT6)
#define PIN_LCU1_OUT1_1             (PIN_PORTC | PIN14 | PIN_OUTPUT_ALT6)
#define PIN_LCU1_OUT1_2             (PIN_PORTC | PIN24 | PIN_OUTPUT_ALT6)
#define PIN_LCU1_OUT2_1             (PIN_PORTB | PIN3  | PIN_OUTPUT_ALT6)
#define PIN_LCU1_OUT2_2             (PIN_PORTC | PIN25 | PIN_OUTPUT_ALT6)
#define PIN_LCU1_OUT3_1             (PIN_PORTB | PIN2  | PIN_OUTPUT_ALT5)
#define PIN_LCU1_OUT3_2             (PIN_PORTC | PIN27 | PIN_OUTPUT_ALT6)
#define PIN_LCU1_OUT4_1             (PIN_PORTB | PIN1  | PIN_OUTPUT_ALT7)
#define PIN_LCU1_OUT4_2             (PIN_PORTC | PIN21 | PIN_OUTPUT_ALT6)
#define PIN_LCU1_OUT5_1             (PIN_PORTB | PIN0  | PIN_OUTPUT_ALT5)
#define PIN_LCU1_OUT5_2             (PIN_PORTC | PIN20 | PIN_OUTPUT_ALT6)
#define PIN_LCU1_OUT6_1             (PIN_PORTC | PIN9  | PIN_OUTPUT_ALT5)
#define PIN_LCU1_OUT6_2             (PIN_PORTC | PIN19 | PIN_OUTPUT_ALT6)
#define PIN_LCU1_OUT7_1             (PIN_PORTC | PIN8  | PIN_OUTPUT_ALT5)
#define PIN_LCU1_OUT7_2             (PIN_PORTC | PIN18 | PIN_OUTPUT_ALT6)
#define PIN_LCU1_OUT8_1             (PIN_PORTC | PIN13 | PIN_OUTPUT_ALT6)
#define PIN_LCU1_OUT8_2             (PIN_PORTC | PIN28 | PIN_OUTPUT_ALT6)
#define PIN_LCU1_OUT9_1             (PIN_PORTC | PIN12 | PIN_OUTPUT_ALT6)
#define PIN_LCU1_OUT9_2             (PIN_PORTC | PIN26 | PIN_OUTPUT_ALT6)
#define PIN_LCU1_OUT10_1            (PIN_PORTB | PIN29 | PIN_OUTPUT_ALT6)
#define PIN_LCU1_OUT10_2            (PIN_PORTC | PIN11 | PIN_OUTPUT_ALT7)
#define PIN_LCU1_OUT11_1            (PIN_PORTB | PIN28 | PIN_OUTPUT_ALT6)
#define PIN_LCU1_OUT11_2            (PIN_PORTC | PIN10 | PIN_OUTPUT_ALT6)

/* LPI2C */

#define PIN_LPI2C0_HREQ_1           (PIN_PORTB | PIN11 | PIN_INPUT_ALT1  | IMCR(723))
#define PIN_LPI2C0_HREQ_2           (PIN_PORTC | PIN7  | PIN_INPUT_ALT2  | IMCR(723))
#define PIN_LPI2C0_SCL_1            (PIN_PORTC | PIN8  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(724))
#define PIN_LPI2C0_SCL_2            (PIN_PORTD | PIN14 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(724))
#define PIN_LPI2C0_SCL_3            (PIN_PORTF | PIN20 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(724))
#define PIN_LPI2C0_SCLS             (PIN_PORTB | PIN1  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(725))
#define PIN_LPI2C0_SDA_1            (PIN_PORTC | PIN9  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(726))
#define PIN_LPI2C0_SDA_2            (PIN_PORTD | PIN13 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(726))
#define PIN_LPI2C0_SDA_3            (PIN_PORTF | PIN21 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(726))
#define PIN_LPI2C0_SDAS             (PIN_PORTB | PIN0  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(727))

#define PIN_LPI2C1_HREQ_1           (PIN_PORTC | PIN5  | PIN_INPUT_ALT2  | IMCR(728))
#define PIN_LPI2C1_HREQ_2           (PIN_PORTD | PIN12 | PIN_INPUT_ALT1  | IMCR(728))
#define PIN_LPI2C1_SCL_1            (PIN_PORTC | PIN7  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(729))
#define PIN_LPI2C1_SCL_2            (PIN_PORTC | PIN15 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT6  | IMCR(729))
#define PIN_LPI2C1_SCL_3            (PIN_PORTC | PIN28 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT4  | IMCR(729))
#define PIN_LPI2C1_SCL_4            (PIN_PORTD | PIN9  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(729))
#define PIN_LPI2C1_SCL_5            (PIN_PORTF | PIN7  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT5  | IMCR(729))
#define PIN_LPI2C1_SCLS             (PIN_PORTC | PIN17 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(730))
#define PIN_LPI2C1_SDA_1            (PIN_PORTC | PIN6  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT2  | IMCR(731))
#define PIN_LPI2C1_SDA_2            (PIN_PORTC | PIN16 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT5  | IMCR(731))
#define PIN_LPI2C1_SDA_3            (PIN_PORTC | PIN29 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT3  | IMCR(731))
#define PIN_LPI2C1_SDA_4            (PIN_PORTD | PIN8  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(731))
#define PIN_LPI2C1_SDA_5            (PIN_PORTF | PIN8  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(731))
#define PIN_LPI2C1_SDAS             (PIN_PORTC | PIN16 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT1  | IMCR(732))

/* LPSPI */

#define PIN_LPSPI0_PCS0_1           (PIN_PORTA | PIN26 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(733))
#define PIN_LPSPI0_PCS0_2           (PIN_PORTB | PIN0  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(733))
#define PIN_LPSPI0_PCS0_3           (PIN_PORTB | PIN5  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(733))
#define PIN_LPSPI0_PCS0_4           (PIN_PORTC | PIN7  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT6  | IMCR(733))
#define PIN_LPSPI0_PCS0_5           (PIN_PORTD | PIN6  | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT7  | IMCR(733))
#define PIN_LPSPI0_PCS0_6           (PIN_PORTE | PIN4  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT5  | IMCR(733))
#define PIN_LPSPI0_PCS0_7           (PIN_PORTF | PIN28 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT4  | IMCR(733))
#define PIN_LPSPI0_PCS1_1           (PIN_PORTA | PIN7  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(734))
#define PIN_LPSPI0_PCS1_2           (PIN_PORTA | PIN31 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(734))
#define PIN_LPSPI0_PCS1_3           (PIN_PORTB | PIN5  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(734))
#define PIN_LPSPI0_PCS1_4           (PIN_PORTC | PIN6  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT4  | IMCR(734))
#define PIN_LPSPI0_PCS1_5           (PIN_PORTD | PIN5  | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT5  | IMCR(734))
#define PIN_LPSPI0_PCS2_1           (PIN_PORTC | PIN2  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(735))
#define PIN_LPSPI0_PCS2_2           (PIN_PORTE | PIN6  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(735))
#define PIN_LPSPI0_PCS3_1           (PIN_PORTA | PIN15 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(736))
#define PIN_LPSPI0_PCS3_2           (PIN_PORTD | PIN7  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(736))
#define PIN_LPSPI0_PCS4             (PIN_PORTA | PIN16 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT1  | IMCR(737))
#define PIN_LPSPI0_PCS5             (PIN_PORTB | PIN8  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(738))
#define PIN_LPSPI0_PCS6             (PIN_PORTA | PIN1  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(739))
#define PIN_LPSPI0_PCS7             (PIN_PORTA | PIN0  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(740))
#define PIN_LPSPI0_SCK_1            (PIN_PORTC | PIN8  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(741))
#define PIN_LPSPI0_SCK_2            (PIN_PORTD | PIN11 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT5  | IMCR(741))
#define PIN_LPSPI0_SCK_3            (PIN_PORTD | PIN15 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(741))
#define PIN_LPSPI0_SCK_4            (PIN_PORTE | PIN1  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(741))
#define PIN_LPSPI0_SIN_1            (PIN_PORTC | PIN9  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(742))
#define PIN_LPSPI0_SIN_2            (PIN_PORTD | PIN10 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT4  | IMCR(742))
#define PIN_LPSPI0_SIN_3            (PIN_PORTD | PIN16 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(742))
#define PIN_LPSPI0_SIN_4            (PIN_PORTE | PIN0  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(742))
#define PIN_LPSPI0_SOUT_1           (PIN_PORTA | PIN30 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT4  | IMCR(743))
#define PIN_LPSPI0_SOUT_2           (PIN_PORTB | PIN1  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(743))
#define PIN_LPSPI0_SOUT_3           (PIN_PORTB | PIN4  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(743))
#define PIN_LPSPI0_SOUT_4           (PIN_PORTD | PIN12 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT5  | IMCR(743))
#define PIN_LPSPI0_SOUT_5           (PIN_PORTE | PIN2  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(743))

#define PIN_LPSPI1_PCS0_1           (PIN_PORTA | PIN11 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(744))
#define PIN_LPSPI1_PCS0_2           (PIN_PORTA | PIN21 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(744))
#define PIN_LPSPI1_PCS0_3           (PIN_PORTA | PIN26 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT5  | IMCR(744))
#define PIN_LPSPI1_PCS0_4           (PIN_PORTD | PIN3  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(744))
#define PIN_LPSPI1_PCS0_5           (PIN_PORTF | PIN21 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT4  | IMCR(744))
#define PIN_LPSPI1_PCS1_1           (PIN_PORTA | PIN6  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(745))
#define PIN_LPSPI1_PCS1_2           (PIN_PORTA | PIN22 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(745))
#define PIN_LPSPI1_PCS1_3           (PIN_PORTB | PIN18 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(745))
#define PIN_LPSPI1_PCS1_4           (PIN_PORTC | PIN6  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(745))
#define PIN_LPSPI1_PCS1_5           (PIN_PORTD | PIN4  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT6  | IMCR(745))
#define PIN_LPSPI1_PCS1_6           (PIN_PORTE | PIN4  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT5  | IMCR(745))
#define PIN_LPSPI1_PCS2_1           (PIN_PORTA | PIN16 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(746))
#define PIN_LPSPI1_PCS2_2           (PIN_PORTD | PIN20 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT1  | IMCR(746))
#define PIN_LPSPI1_PCS3_1           (PIN_PORTA | PIN14 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(747))
#define PIN_LPSPI1_PCS3_2           (PIN_PORTB | PIN17 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(747))
#define PIN_LPSPI1_PCS4             (PIN_PORTA | PIN13 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(748))
#define PIN_LPSPI1_PCS5             (PIN_PORTA | PIN12 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(749))
#define PIN_LPSPI1_SCK_1            (PIN_PORTA | PIN3  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(750))
#define PIN_LPSPI1_SCK_2            (PIN_PORTA | PIN19 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(750))
#define PIN_LPSPI1_SCK_3            (PIN_PORTA | PIN28 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(750))
#define PIN_LPSPI1_SCK_4            (PIN_PORTB | PIN14 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(750))
#define PIN_LPSPI1_SIN_1            (PIN_PORTA | PIN2  | PIN_INPUT_ALT2  | IMCR(751))
#define PIN_LPSPI1_SIN_2            (PIN_PORTA | PIN20 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(751))
#define PIN_LPSPI1_SIN_3            (PIN_PORTA | PIN29 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT4  | IMCR(751))
#define PIN_LPSPI1_SIN_4            (PIN_PORTB | PIN15 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(751))
#define PIN_LPSPI1_SOUT_1           (PIN_PORTA | PIN18 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT4  | IMCR(752))
#define PIN_LPSPI1_SOUT_2           (PIN_PORTA | PIN30 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT5  | IMCR(752))
#define PIN_LPSPI1_SOUT_3           (PIN_PORTB | PIN16 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(752))
#define PIN_LPSPI1_SOUT_4           (PIN_PORTD | PIN2  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(752))

#define PIN_LPSPI2_PCS0_1           (PIN_PORTA | PIN9  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(753))
#define PIN_LPSPI2_PCS0_2           (PIN_PORTB | PIN25 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT4  | IMCR(753))
#define PIN_LPSPI2_PCS0_3           (PIN_PORTC | PIN14 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(753))
#define PIN_LPSPI2_PCS0_4           (PIN_PORTE | PIN11 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(753))
#define PIN_LPSPI2_PCS0_5           (PIN_PORTE | PIN13 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT5  | IMCR(753))
#define PIN_LPSPI2_PCS0_6           (PIN_PORTF | PIN3  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT6  | IMCR(753))
#define PIN_LPSPI2_PCS1_1           (PIN_PORTC | PIN10 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(754))
#define PIN_LPSPI2_PCS1_2           (PIN_PORTC | PIN12 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT4  | IMCR(754))
#define PIN_LPSPI2_PCS1_3           (PIN_PORTC | PIN19 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT2  | IMCR(754))
#define PIN_LPSPI2_PCS1_4           (PIN_PORTE | PIN10 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(754))
#define PIN_LPSPI2_PCS2_1           (PIN_PORTA | PIN21 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(755))
#define PIN_LPSPI2_PCS2_2           (PIN_PORTE | PIN13 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(755))
#define PIN_LPSPI2_PCS2_3           (PIN_PORTF | PIN25 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT3  | IMCR(755))
#define PIN_LPSPI2_PCS3_1           (PIN_PORTA | PIN15 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT1  | IMCR(756))
#define PIN_LPSPI2_PCS3_2           (PIN_PORTF | PIN26 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT2  | IMCR(756))
#define PIN_LPSPI2_SCK_1            (PIN_PORTB | PIN29 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT3  | IMCR(757))
#define PIN_LPSPI2_SCK_2            (PIN_PORTC | PIN15 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(757))
#define PIN_LPSPI2_SCK_3            (PIN_PORTE | PIN15 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(757))
#define PIN_LPSPI2_SCK_4            (PIN_PORTF | PIN0  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(757))
#define PIN_LPSPI2_SIN_1            (PIN_PORTB | PIN2  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(758))
#define PIN_LPSPI2_SIN_2            (PIN_PORTB | PIN28 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT3  | IMCR(758))
#define PIN_LPSPI2_SIN_3            (PIN_PORTE | PIN16 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(758))
#define PIN_LPSPI2_SIN_4            (PIN_PORTF | PIN1  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(758))
#define PIN_LPSPI2_SOUT_1           (PIN_PORTA | PIN8  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(759))
#define PIN_LPSPI2_SOUT_2           (PIN_PORTB | PIN3  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT2  | IMCR(759))
#define PIN_LPSPI2_SOUT_3           (PIN_PORTB | PIN27 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT3  | IMCR(759))
#define PIN_LPSPI2_SOUT_4           (PIN_PORTF | PIN2  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(759))

#define PIN_LPSPI3_PCS0_1           (PIN_PORTA | PIN9  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT3  | IMCR(760))
#define PIN_LPSPI3_PCS0_2           (PIN_PORTB | PIN17 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(760))
#define PIN_LPSPI3_PCS0_3           (PIN_PORTD | PIN17 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT1  | IMCR(760))
#define PIN_LPSPI3_PCS0_4           (PIN_PORTF | PIN16 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT4  | IMCR(760))
#define PIN_LPSPI3_PCS1_1           (PIN_PORTA | PIN6  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT5  | IMCR(761))
#define PIN_LPSPI3_PCS1_2           (PIN_PORTB | PIN22 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(761))
#define PIN_LPSPI3_PCS1_3           (PIN_PORTE | PIN8  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(761))
#define PIN_LPSPI3_PCS1_4           (PIN_PORTF | PIN18 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT2  | IMCR(761))
#define PIN_LPSPI3_PCS1_5           (PIN_PORTF | PIN29 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT3  | IMCR(761))
#define PIN_LPSPI3_PCS2_1           (PIN_PORTB | PIN13 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(762))
#define PIN_LPSPI3_PCS2_2           (PIN_PORTC | PIN2  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(762))
#define PIN_LPSPI3_PCS2_3           (PIN_PORTF | PIN19 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT2  | IMCR(762))
#define PIN_LPSPI3_PCS3_1           (PIN_PORTB | PIN12 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(763))
#define PIN_LPSPI3_PCS3_2           (PIN_PORTD | PIN7  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT5  | IMCR(763))
#define PIN_LPSPI3_PCS3_3           (PIN_PORTF | PIN20 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT2  | IMCR(763))
#define PIN_LPSPI3_PCS3_4           (PIN_PORTF | PIN23 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT3  | IMCR(763))
#define PIN_LPSPI3_SCK_1            (PIN_PORTC | PIN17 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT3  | IMCR(764))
#define PIN_LPSPI3_SCK_2            (PIN_PORTD | PIN1  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(764))
#define PIN_LPSPI3_SCK_3            (PIN_PORTE | PIN7  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(764))
#define PIN_LPSPI3_SCK_4            (PIN_PORTF | PIN13 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT4  | IMCR(764))
#define PIN_LPSPI3_SIN_1            (PIN_PORTC | PIN16 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT3  | IMCR(765))
#define PIN_LPSPI3_SIN_2            (PIN_PORTD | PIN20 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(765))
#define PIN_LPSPI3_SIN_3            (PIN_PORTE | PIN10 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(765))
#define PIN_LPSPI3_SIN_4            (PIN_PORTF | PIN12 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT4  | IMCR(765))
#define PIN_LPSPI3_SOUT_1           (PIN_PORTA | PIN17 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(766))
#define PIN_LPSPI3_SOUT_2           (PIN_PORTD | PIN0  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(766))
#define PIN_LPSPI3_SOUT_3           (PIN_PORTD | PIN8  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT3  | IMCR(766))
#define PIN_LPSPI3_SOUT_4           (PIN_PORTF | PIN15 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT4  | IMCR(766))

#define PIN_LPSPI4_PCS0_1           (PIN_PORTB | PIN8  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT2  | IMCR(767))
#define PIN_LPSPI4_PCS0_2           (PIN_PORTC | PIN10 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT1  | IMCR(767))
#define PIN_LPSPI4_PCS0_3           (PIN_PORTE | PIN23 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT4  | IMCR(767))
#define PIN_LPSPI4_PCS0_4           (PIN_PORTF | PIN25 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT3  | IMCR(767))
#define PIN_LPSPI4_PCS1_1           (PIN_PORTA | PIN1  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(768))
#define PIN_LPSPI4_PCS1_2           (PIN_PORTC | PIN25 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT4  | IMCR(768))
#define PIN_LPSPI4_PCS1_3           (PIN_PORTE | PIN24 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT3  | IMCR(768))
#define PIN_LPSPI4_PCS1_4           (PIN_PORTF | PIN26 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(768))
#define PIN_LPSPI4_PCS2_1           (PIN_PORTA | PIN0  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(769))
#define PIN_LPSPI4_PCS2_2           (PIN_PORTF | PIN22 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(769))
#define PIN_LPSPI4_PCS2_3           (PIN_PORTF | PIN27 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT3  | IMCR(769))
#define PIN_LPSPI4_PCS3_1           (PIN_PORTA | PIN16 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(770))
#define PIN_LPSPI4_PCS3_2           (PIN_PORTF | PIN28 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(770))
#define PIN_LPSPI4_PCS3_3           (PIN_PORTG | PIN12 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT3  | IMCR(770))
#define PIN_LPSPI4_SCK_1            (PIN_PORTB | PIN10 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT2  | IMCR(771))
#define PIN_LPSPI4_SCK_2            (PIN_PORTC | PIN27 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(771))
#define PIN_LPSPI4_SCK_3            (PIN_PORTE | PIN22 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT3  | IMCR(771))
#define PIN_LPSPI4_SIN_1            (PIN_PORTB | PIN11 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT2  | IMCR(772))
#define PIN_LPSPI4_SIN_2            (PIN_PORTC | PIN26 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(772))
#define PIN_LPSPI4_SIN_3            (PIN_PORTE | PIN21 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT3  | IMCR(772))
#define PIN_LPSPI4_SOUT_1           (PIN_PORTB | PIN9  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT2  | IMCR(773))
#define PIN_LPSPI4_SOUT_2           (PIN_PORTC | PIN11 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT1  | IMCR(773))
#define PIN_LPSPI4_SOUT_3           (PIN_PORTE | PIN25 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT3  | IMCR(773))

#define PIN_LPSPI5_PCS0_1           (PIN_PORTA | PIN15 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT3  | IMCR(774))
#define PIN_LPSPI5_PCS0_2           (PIN_PORTD | PIN4  | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT2  | IMCR(774))
#define PIN_LPSPI5_PCS0_3           (PIN_PORTD | PIN17 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(774))
#define PIN_LPSPI5_PCS1_1           (PIN_PORTA | PIN14 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT2  | IMCR(775))
#define PIN_LPSPI5_PCS1_2           (PIN_PORTE | PIN8  | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT1  | IMCR(775))
#define PIN_LPSPI5_PCS2             (PIN_PORTD | PIN29 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(776))
#define PIN_LPSPI5_PCS3             (PIN_PORTD | PIN30 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(777))
#define PIN_LPSPI5_SCK_1            (PIN_PORTA | PIN3  | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT2  | IMCR(778))
#define PIN_LPSPI5_SCK_2            (PIN_PORTD | PIN14 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(778))
#define PIN_LPSPI5_SCK_3            (PIN_PORTD | PIN26 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT3  | IMCR(778))
#define PIN_LPSPI5_SIN_1            (PIN_PORTA | PIN2  | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT2  | IMCR(779))
#define PIN_LPSPI5_SIN_2            (PIN_PORTD | PIN13 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(779))
#define PIN_LPSPI5_SIN_3            (PIN_PORTD | PIN28 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT3  | IMCR(779))
#define PIN_LPSPI5_SOUT_1           (PIN_PORTD | PIN2  | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT2  | IMCR(780))
#define PIN_LPSPI5_SOUT_2           (PIN_PORTD | PIN27 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT3  | IMCR(780))
#define PIN_LPSPI5_SOUT_3           (PIN_PORTE | PIN9  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(780))

/* LPUART */

#define PIN_LPUART0_CTS_1           (PIN_PORTA | PIN0  | PIN_INPUT_ALT1  | IMCR(872))
#define PIN_LPUART0_CTS_2           (PIN_PORTC | PIN8  | PIN_INPUT_ALT2  | IMCR(872))
#define PIN_LPUART0_RTS_1           (PIN_PORTA | PIN1  | PIN_OUTPUT_ALT3)
#define PIN_LPUART0_RTS_2           (PIN_PORTC | PIN9  | PIN_OUTPUT_ALT3)
#define PIN_LPUART0_RX_1            (PIN_PORTA | PIN2  | PIN_INPUT_ALT1  | IMCR(699))
#define PIN_LPUART0_RX_2            (PIN_PORTA | PIN28 | PIN_INPUT_ALT4  | IMCR(699))
#define PIN_LPUART0_RX_3            (PIN_PORTB | PIN0  | PIN_INPUT_ALT2  | IMCR(699))
#define PIN_LPUART0_RX_4            (PIN_PORTC | PIN2  | PIN_INPUT_ALT3  | IMCR(699))
#define PIN_LPUART0_TX_1            (PIN_PORTA | PIN3  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(875))
#define PIN_LPUART0_TX_2            (PIN_PORTA | PIN27 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT4  | IMCR(875))
#define PIN_LPUART0_TX_3            (PIN_PORTB | PIN1  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(875))
#define PIN_LPUART0_TX_4            (PIN_PORTC | PIN3  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT3  | IMCR(875))

#define PIN_LPUART1_CTS_1           (PIN_PORTA | PIN6  | PIN_INPUT_ALT2  | IMCR(873))
#define PIN_LPUART1_CTS_2           (PIN_PORTE | PIN2  | PIN_INPUT_ALT1  | IMCR(873))
#define PIN_LPUART1_CTS_3           (PIN_PORTE | PIN15 | PIN_INPUT_ALT3  | IMCR(873))
#define PIN_LPUART1_RTS_1           (PIN_PORTA | PIN7  | PIN_OUTPUT_ALT5)
#define PIN_LPUART1_RTS_2           (PIN_PORTE | PIN6  | PIN_OUTPUT_ALT3)
#define PIN_LPUART1_RTS_3           (PIN_PORTE | PIN16 | PIN_OUTPUT_ALT5)
#define PIN_LPUART1_RX_1            (PIN_PORTA | PIN19 | PIN_INPUT_ALT5  | IMCR(700))
#define PIN_LPUART1_RX_2            (PIN_PORTB | PIN23 | PIN_INPUT_ALT4  | IMCR(700))
#define PIN_LPUART1_RX_3            (PIN_PORTC | PIN6  | PIN_INPUT_ALT1  | IMCR(700))
#define PIN_LPUART1_RX_4            (PIN_PORTC | PIN8  | PIN_INPUT_ALT2  | IMCR(700))
#define PIN_LPUART1_RX_5            (PIN_PORTD | PIN13 | PIN_INPUT_ALT3  | IMCR(700))
#define PIN_LPUART1_TX_1            (PIN_PORTA | PIN18 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(876))
#define PIN_LPUART1_TX_2            (PIN_PORTB | PIN22 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT5  | IMCR(876))
#define PIN_LPUART1_TX_3            (PIN_PORTC | PIN7  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(876))
#define PIN_LPUART1_TX_4            (PIN_PORTC | PIN9  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(876))
#define PIN_LPUART1_TX_5            (PIN_PORTD | PIN14 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT3  | IMCR(876))

#define PIN_LPUART2_CTS_1           (PIN_PORTD | PIN11 | PIN_INPUT_ALT1  | IMCR(874))
#define PIN_LPUART2_CTS_2           (PIN_PORTD | PIN15 | PIN_INPUT_ALT2  | IMCR(874))
#define PIN_LPUART2_CTS_3           (PIN_PORTE | PIN9  | PIN_INPUT_ALT3  | IMCR(874))
#define PIN_LPUART2_RTS_1           (PIN_PORTD | PIN12 | PIN_OUTPUT_ALT3)
#define PIN_LPUART2_RTS_2           (PIN_PORTD | PIN16 | PIN_OUTPUT_ALT6)
#define PIN_LPUART2_RTS_3           (PIN_PORTE | PIN3  | PIN_OUTPUT_ALT5)
#define PIN_LPUART2_RX_1            (PIN_PORTA | PIN8  | PIN_INPUT_ALT3  | IMCR(701))
#define PIN_LPUART2_RX_2            (PIN_PORTA | PIN30 | PIN_INPUT_ALT4  | IMCR(701))
#define PIN_LPUART2_RX_3            (PIN_PORTC | PIN16 | PIN_INPUT_ALT5  | IMCR(701))
#define PIN_LPUART2_RX_4            (PIN_PORTD | PIN6  | PIN_INPUT_ALT1  | IMCR(701))
#define PIN_LPUART2_RX_5            (PIN_PORTD | PIN17 | PIN_INPUT_ALT2  | IMCR(701))
#define PIN_LPUART2_TX_1            (PIN_PORTA | PIN9  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT1  | IMCR(877))
#define PIN_LPUART2_TX_2            (PIN_PORTA | PIN29 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT5  | IMCR(877))
#define PIN_LPUART2_TX_3            (PIN_PORTC | PIN15 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT2  | IMCR(877))
#define PIN_LPUART2_TX_4            (PIN_PORTD | PIN7  | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(877))
#define PIN_LPUART2_TX_5            (PIN_PORTE | PIN12 | PIN_OUTPUT_ALT3 | PIN_INPUT_ALT4  | IMCR(877))

#define PIN_LPUART3_RX_1            (PIN_PORTA | PIN6  | PIN_INPUT_ALT2  | IMCR(702))
#define PIN_LPUART3_RX_2            (PIN_PORTD | PIN3  | PIN_INPUT_ALT3  | IMCR(702))
#define PIN_LPUART3_RX_3            (PIN_PORTE | PIN15 | PIN_INPUT_ALT1  | IMCR(702))
#define PIN_LPUART3_TX_1            (PIN_PORTA | PIN7  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(878))
#define PIN_LPUART3_TX_2            (PIN_PORTD | PIN2  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT2  | IMCR(878))
#define PIN_LPUART3_TX_3            (PIN_PORTE | PIN16 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT3  | IMCR(878))

#define PIN_LPUART4_RX_1            (PIN_PORTB | PIN17 | PIN_INPUT_ALT3  | IMCR(703))
#define PIN_LPUART4_RX_2            (PIN_PORTE | PIN7  | PIN_INPUT_ALT4  | IMCR(703))
#define PIN_LPUART4_RX_3            (PIN_PORTE | PIN10 | PIN_INPUT_ALT2  | IMCR(703))
#define PIN_LPUART4_TX_1            (PIN_PORTA | PIN17 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT1  | IMCR(879))
#define PIN_LPUART4_TX_2            (PIN_PORTB | PIN16 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(879))
#define PIN_LPUART4_TX_3            (PIN_PORTE | PIN11 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT3  | IMCR(879))

#define PIN_LPUART5_RX_1            (PIN_PORTB | PIN28 | PIN_INPUT_ALT4  | IMCR(704))
#define PIN_LPUART5_RX_2            (PIN_PORTD | PIN0  | PIN_INPUT_ALT2  | IMCR(704))
#define PIN_LPUART5_RX_3            (PIN_PORTE | PIN3  | PIN_INPUT_ALT1  | IMCR(704))
#define PIN_LPUART5_RX_4            (PIN_PORTF | PIN1  | PIN_INPUT_ALT3  | IMCR(704))
#define PIN_LPUART5_TX_1            (PIN_PORTB | PIN27 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT3  | IMCR(880))
#define PIN_LPUART5_TX_2            (PIN_PORTD | PIN1  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(880))
#define PIN_LPUART5_TX_3            (PIN_PORTE | PIN14 | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(880))
#define PIN_LPUART5_TX_4            (PIN_PORTF | PIN0  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT4  | IMCR(880))

#define PIN_LPUART6_RX_1            (PIN_PORTA | PIN15 | PIN_INPUT_ALT2  | IMCR(705))
#define PIN_LPUART6_RX_2            (PIN_PORTC | PIN18 | PIN_INPUT_ALT4  | IMCR(705))
#define PIN_LPUART6_RX_3            (PIN_PORTD | PIN8  | PIN_INPUT_ALT1  | IMCR(705))
#define PIN_LPUART6_RX_4            (PIN_PORTF | PIN3  | PIN_INPUT_ALT3  | IMCR(705))
#define PIN_LPUART6_TX_1            (PIN_PORTA | PIN16 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT1  | IMCR(881))
#define PIN_LPUART6_TX_2            (PIN_PORTB | PIN29 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT3  | IMCR(881))
#define PIN_LPUART6_TX_3            (PIN_PORTD | PIN9  | PIN_OUTPUT_ALT4 | PIN_INPUT_ALT2  | IMCR(881))
#define PIN_LPUART6_TX_4            (PIN_PORTF | PIN2  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT4  | IMCR(881))

#define PIN_LPUART7_RX_1            (PIN_PORTB | PIN14 | PIN_INPUT_ALT1  | IMCR(706))
#define PIN_LPUART7_RX_2            (PIN_PORTC | PIN20 | PIN_INPUT_ALT4  | IMCR(706))
#define PIN_LPUART7_RX_3            (PIN_PORTE | PIN0  | PIN_INPUT_ALT2  | IMCR(706))
#define PIN_LPUART7_RX_4            (PIN_PORTF | PIN19 | PIN_INPUT_ALT3  | IMCR(706))
#define PIN_LPUART7_TX_1            (PIN_PORTB | PIN15 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(882))
#define PIN_LPUART7_TX_2            (PIN_PORTC | PIN19 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT2  | IMCR(882))
#define PIN_LPUART7_TX_3            (PIN_PORTE | PIN1  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT3  | IMCR(882))
#define PIN_LPUART7_TX_4            (PIN_PORTF | PIN18 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT4  | IMCR(882))

#define PIN_LPUART8_RX_1            (PIN_PORTB | PIN12 | PIN_INPUT_ALT1  | IMCR(707))
#define PIN_LPUART8_RX_2            (PIN_PORTD | PIN15 | PIN_INPUT_ALT2  | IMCR(707))
#define PIN_LPUART8_RX_3            (PIN_PORTF | PIN21 | PIN_INPUT_ALT3  | IMCR(707))
#define PIN_LPUART8_TX_1            (PIN_PORTB | PIN13 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(883))
#define PIN_LPUART8_TX_2            (PIN_PORTD | PIN16 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT2  | IMCR(883))
#define PIN_LPUART8_TX_3            (PIN_PORTF | PIN20 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT3  | IMCR(883))

#define PIN_LPUART9_RX_1            (PIN_PORTB | PIN2  | PIN_INPUT_ALT2  | IMCR(708))
#define PIN_LPUART9_RX_2            (PIN_PORTB | PIN9  | PIN_INPUT_ALT1  | IMCR(708))
#define PIN_LPUART9_RX_3            (PIN_PORTF | PIN24 | PIN_INPUT_ALT3  | IMCR(708))
#define PIN_LPUART9_TX_1            (PIN_PORTB | PIN3  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(884))
#define PIN_LPUART9_TX_2            (PIN_PORTB | PIN10 | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT2  | IMCR(884))
#define PIN_LPUART9_TX_3            (PIN_PORTF | PIN23 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT3  | IMCR(884))

#define PIN_LPUART10_RX_1           (PIN_PORTC | PIN12 | PIN_INPUT_ALT2  | IMCR(709))
#define PIN_LPUART10_RX_2           (PIN_PORTE | PIN2  | PIN_INPUT_ALT1  | IMCR(709))
#define PIN_LPUART10_RX_3           (PIN_PORTF | PIN13 | PIN_INPUT_ALT3  | IMCR(709))
#define PIN_LPUART10_TX_1           (PIN_PORTC | PIN13 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(885))
#define PIN_LPUART10_TX_2           (PIN_PORTE | PIN6  | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT2  | IMCR(885))
#define PIN_LPUART10_TX_3           (PIN_PORTF | PIN12 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT3  | IMCR(885))

#define PIN_LPUART11_RX_1           (PIN_PORTA | PIN12 | PIN_INPUT_ALT2  | IMCR(710))
#define PIN_LPUART11_RX_2           (PIN_PORTC | PIN11 | PIN_INPUT_ALT1  | IMCR(710))
#define PIN_LPUART11_RX_3           (PIN_PORTF | PIN28 | PIN_INPUT_ALT3  | IMCR(710))
#define PIN_LPUART11_TX_1           (PIN_PORTA | PIN13 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(886))
#define PIN_LPUART11_TX_2           (PIN_PORTC | PIN10 | PIN_OUTPUT_ALT2 | PIN_INPUT_ALT2  | IMCR(886))
#define PIN_LPUART11_TX_3           (PIN_PORTF | PIN27 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT3  | IMCR(886))

#define PIN_LPUART12_RX_1           (PIN_PORTC | PIN25 | PIN_INPUT_ALT2  | IMCR(711))
#define PIN_LPUART12_RX_2           (PIN_PORTE | PIN5  | PIN_INPUT_ALT1  | IMCR(711))
#define PIN_LPUART12_RX_3           (PIN_PORTF | PIN17 | PIN_INPUT_ALT3  | IMCR(711))
#define PIN_LPUART12_TX_1           (PIN_PORTC | PIN24 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(887))
#define PIN_LPUART12_TX_2           (PIN_PORTE | PIN4  | PIN_OUTPUT_ALT5 | PIN_INPUT_ALT2  | IMCR(887))
#define PIN_LPUART12_TX_3           (PIN_PORTF | PIN16 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT3  | IMCR(887))

#define PIN_LPUART13_RX_1           (PIN_PORTB | PIN19 | PIN_INPUT_ALT1  | IMCR(712))
#define PIN_LPUART13_RX_2           (PIN_PORTC | PIN27 | PIN_INPUT_ALT2  | IMCR(712))
#define PIN_LPUART13_RX_3           (PIN_PORTG | PIN1  | PIN_INPUT_ALT3  | IMCR(712))
#define PIN_LPUART13_TX_1           (PIN_PORTB | PIN18 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(888))
#define PIN_LPUART13_TX_2           (PIN_PORTC | PIN26 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT2  | IMCR(888))
#define PIN_LPUART13_TX_3           (PIN_PORTG | PIN0  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT3  | IMCR(888))

#define PIN_LPUART14_RX_1           (PIN_PORTB | PIN21 | PIN_INPUT_ALT1  | IMCR(713))
#define PIN_LPUART14_RX_2           (PIN_PORTD | PIN27 | PIN_INPUT_ALT2  | IMCR(713))
#define PIN_LPUART14_RX_3           (PIN_PORTG | PIN3  | PIN_INPUT_ALT3  | IMCR(713))
#define PIN_LPUART14_TX_1           (PIN_PORTB | PIN20 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(889))
#define PIN_LPUART14_TX_2           (PIN_PORTD | PIN26 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT2  | IMCR(889))
#define PIN_LPUART14_TX_3           (PIN_PORTG | PIN2  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT3  | IMCR(889))

#define PIN_LPUART15_RX_1           (PIN_PORTB | PIN26 | PIN_INPUT_ALT1  | IMCR(714))
#define PIN_LPUART15_RX_2           (PIN_PORTD | PIN29 | PIN_INPUT_ALT2  | IMCR(714))
#define PIN_LPUART15_RX_3           (PIN_PORTG | PIN5  | PIN_INPUT_ALT3  | IMCR(714))
#define PIN_LPUART15_TX_1           (PIN_PORTB | PIN25 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT1  | IMCR(890))
#define PIN_LPUART15_TX_2           (PIN_PORTD | PIN28 | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT2  | IMCR(890))
#define PIN_LPUART15_TX_3           (PIN_PORTG | PIN4  | PIN_OUTPUT_ALT1 | PIN_INPUT_ALT3  | IMCR(890))

/* GPIO */

#define PIN_PTA0                    (PIN_PORTA | PIN0)
#define PIN_PTA1                    (PIN_PORTA | PIN1)
#define PIN_PTA2                    (PIN_PORTA | PIN2)
#define PIN_PTA3                    (PIN_PORTA | PIN3)
#define PIN_PTA4                    (PIN_PORTA | PIN4)
#define PIN_PTA5                    (PIN_PORTA | PIN5)
#define PIN_PTA6                    (PIN_PORTA | PIN6)
#define PIN_PTA7                    (PIN_PORTA | PIN7)
#define PIN_PTA8                    (PIN_PORTA | PIN8)
#define PIN_PTA9                    (PIN_PORTA | PIN9)
#define PIN_PTA10                   (PIN_PORTA | PIN10)
#define PIN_PTA11                   (PIN_PORTA | PIN11)
#define PIN_PTA12                   (PIN_PORTA | PIN12)
#define PIN_PTA13                   (PIN_PORTA | PIN13)
#define PIN_PTA14                   (PIN_PORTA | PIN14)
#define PIN_PTA15                   (PIN_PORTA | PIN15)
#define PIN_PTA16                   (PIN_PORTA | PIN16)
#define PIN_PTA17                   (PIN_PORTA | PIN17)
#define PIN_PTA18                   (PIN_PORTA | PIN18)
#define PIN_PTA19                   (PIN_PORTA | PIN19)
#define PIN_PTA20                   (PIN_PORTA | PIN20)
#define PIN_PTA21                   (PIN_PORTA | PIN21)
#define PIN_PTA22                   (PIN_PORTA | PIN22)
#define PIN_PTA23                   (PIN_PORTA | PIN23)
#define PIN_PTA24                   (PIN_PORTA | PIN24)
#define PIN_PTA25                   (PIN_PORTA | PIN25)
#define PIN_PTA26                   (PIN_PORTA | PIN26)
#define PIN_PTA27                   (PIN_PORTA | PIN27)
#define PIN_PTA28                   (PIN_PORTA | PIN28)
#define PIN_PTA29                   (PIN_PORTA | PIN29)
#define PIN_PTA30                   (PIN_PORTA | PIN30)
#define PIN_PTA31                   (PIN_PORTA | PIN31)

#define PIN_PTB0                    (PIN_PORTB | PIN0)
#define PIN_PTB1                    (PIN_PORTB | PIN1)
#define PIN_PTB2                    (PIN_PORTB | PIN2)
#define PIN_PTB3                    (PIN_PORTB | PIN3)
#define PIN_PTB4                    (PIN_PORTB | PIN4)
#define PIN_PTB5                    (PIN_PORTB | PIN5)
#define PIN_PTB8                    (PIN_PORTB | PIN8)
#define PIN_PTB9                    (PIN_PORTB | PIN9)
#define PIN_PTB10                   (PIN_PORTB | PIN10)
#define PIN_PTB11                   (PIN_PORTB | PIN11)
#define PIN_PTB12                   (PIN_PORTB | PIN12)
#define PIN_PTB13                   (PIN_PORTB | PIN13)
#define PIN_PTB14                   (PIN_PORTB | PIN14)
#define PIN_PTB15                   (PIN_PORTB | PIN15)
#define PIN_PTB16                   (PIN_PORTB | PIN16)
#define PIN_PTB17                   (PIN_PORTB | PIN17)
#define PIN_PTB18                   (PIN_PORTB | PIN18)
#define PIN_PTB19                   (PIN_PORTB | PIN19)
#define PIN_PTB20                   (PIN_PORTB | PIN20)
#define PIN_PTB21                   (PIN_PORTB | PIN21)
#define PIN_PTB22                   (PIN_PORTB | PIN22)
#define PIN_PTB23                   (PIN_PORTB | PIN23)
#define PIN_PTB24                   (PIN_PORTB | PIN24)
#define PIN_PTB25                   (PIN_PORTB | PIN25)
#define PIN_PTB26                   (PIN_PORTB | PIN26)
#define PIN_PTB27                   (PIN_PORTB | PIN27)
#define PIN_PTB28                   (PIN_PORTB | PIN28)
#define PIN_PTB29                   (PIN_PORTB | PIN29)
#define PIN_PTB30                   (PIN_PORTB | PIN30)
#define PIN_PTB31                   (PIN_PORTB | PIN31)

#define PIN_PTC0                    (PIN_PORTC | PIN0)
#define PIN_PTC1                    (PIN_PORTC | PIN1)
#define PIN_PTC2                    (PIN_PORTC | PIN2)
#define PIN_PTC3                    (PIN_PORTC | PIN3)
#define PIN_PTC4                    (PIN_PORTC | PIN4)
#define PIN_PTC5                    (PIN_PORTC | PIN5)
#define PIN_PTC6                    (PIN_PORTC | PIN6)
#define PIN_PTC7                    (PIN_PORTC | PIN7)
#define PIN_PTC8                    (PIN_PORTC | PIN8)
#define PIN_PTC9                    (PIN_PORTC | PIN9)
#define PIN_PTC10                   (PIN_PORTC | PIN10)
#define PIN_PTC11                   (PIN_PORTC | PIN11)
#define PIN_PTC12                   (PIN_PORTC | PIN12)
#define PIN_PTC13                   (PIN_PORTC | PIN13)
#define PIN_PTC14                   (PIN_PORTC | PIN14)
#define PIN_PTC15                   (PIN_PORTC | PIN15)
#define PIN_PTC16                   (PIN_PORTC | PIN16)
#define PIN_PTC17                   (PIN_PORTC | PIN17)
#define PIN_PTC18                   (PIN_PORTC | PIN18)
#define PIN_PTC19                   (PIN_PORTC | PIN19)
#define PIN_PTC20                   (PIN_PORTC | PIN20)
#define PIN_PTC21                   (PIN_PORTC | PIN21)
#define PIN_PTC22                   (PIN_PORTC | PIN22)
#define PIN_PTC23                   (PIN_PORTC | PIN23)
#define PIN_PTC24                   (PIN_PORTC | PIN24)
#define PIN_PTC25                   (PIN_PORTC | PIN25)
#define PIN_PTC26                   (PIN_PORTC | PIN26)
#define PIN_PTC27                   (PIN_PORTC | PIN27)
#define PIN_PTC28                   (PIN_PORTC | PIN28)
#define PIN_PTC29                   (PIN_PORTC | PIN29)
#define PIN_PTC30                   (PIN_PORTC | PIN30)
#define PIN_PTC31                   (PIN_PORTC | PIN31)

#define PIN_PTD0                    (PIN_PORTD | PIN0)
#define PIN_PTD1                    (PIN_PORTD | PIN1)
#define PIN_PTD2                    (PIN_PORTD | PIN2)
#define PIN_PTD3                    (PIN_PORTD | PIN3)
#define PIN_PTD4                    (PIN_PORTD | PIN4)
#define PIN_PTD5                    (PIN_PORTD | PIN5)
#define PIN_PTD6                    (PIN_PORTD | PIN6)
#define PIN_PTD7                    (PIN_PORTD | PIN7)
#define PIN_PTD8                    (PIN_PORTD | PIN8)
#define PIN_PTD9                    (PIN_PORTD | PIN9)
#define PIN_PTD10                   (PIN_PORTD | PIN10)
#define PIN_PTD11                   (PIN_PORTD | PIN11)
#define PIN_PTD12                   (PIN_PORTD | PIN12)
#define PIN_PTD13                   (PIN_PORTD | PIN13)
#define PIN_PTD14                   (PIN_PORTD | PIN14)
#define PIN_PTD15                   (PIN_PORTD | PIN15)
#define PIN_PTD16                   (PIN_PORTD | PIN16)
#define PIN_PTD17                   (PIN_PORTD | PIN17)
#define PIN_PTD18                   (PIN_PORTD | PIN18)
#define PIN_PTD19                   (PIN_PORTD | PIN19)
#define PIN_PTD20                   (PIN_PORTD | PIN20)
#define PIN_PTD21                   (PIN_PORTD | PIN21)
#define PIN_PTD22                   (PIN_PORTD | PIN22)
#define PIN_PTD23                   (PIN_PORTD | PIN23)
#define PIN_PTD24                   (PIN_PORTD | PIN24)
#define PIN_PTD25                   (PIN_PORTD | PIN25)
#define PIN_PTD26                   (PIN_PORTD | PIN26)
#define PIN_PTD27                   (PIN_PORTD | PIN27)
#define PIN_PTD28                   (PIN_PORTD | PIN28)
#define PIN_PTD29                   (PIN_PORTD | PIN29)
#define PIN_PTD30                   (PIN_PORTD | PIN30)
#define PIN_PTD31                   (PIN_PORTD | PIN31)

#define PIN_PTE0                    (PIN_PORTE | PIN0)
#define PIN_PTE1                    (PIN_PORTE | PIN1)
#define PIN_PTE2                    (PIN_PORTE | PIN2)
#define PIN_PTE3                    (PIN_PORTE | PIN3)
#define PIN_PTE4                    (PIN_PORTE | PIN4)
#define PIN_PTE5                    (PIN_PORTE | PIN5)
#define PIN_PTE6                    (PIN_PORTE | PIN6)
#define PIN_PTE7                    (PIN_PORTE | PIN7)
#define PIN_PTE8                    (PIN_PORTE | PIN8)
#define PIN_PTE9                    (PIN_PORTE | PIN9)
#define PIN_PTE10                   (PIN_PORTE | PIN10)
#define PIN_PTE11                   (PIN_PORTE | PIN11)
#define PIN_PTE12                   (PIN_PORTE | PIN12)
#define PIN_PTE13                   (PIN_PORTE | PIN13)
#define PIN_PTE14                   (PIN_PORTE | PIN14)
#define PIN_PTE15                   (PIN_PORTE | PIN15)
#define PIN_PTE16                   (PIN_PORTE | PIN16)
#define PIN_PTE17                   (PIN_PORTE | PIN17)
#define PIN_PTE18                   (PIN_PORTE | PIN18)
#define PIN_PTE19                   (PIN_PORTE | PIN19)
#define PIN_PTE20                   (PIN_PORTE | PIN20)
#define PIN_PTE21                   (PIN_PORTE | PIN21)
#define PIN_PTE22                   (PIN_PORTE | PIN22)
#define PIN_PTE23                   (PIN_PORTE | PIN23)
#define PIN_PTE24                   (PIN_PORTE | PIN24)
#define PIN_PTE25                   (PIN_PORTE | PIN25)
#define PIN_PTE26                   (PIN_PORTE | PIN26)
#define PIN_PTE27                   (PIN_PORTE | PIN27)
#define PIN_PTE28                   (PIN_PORTE | PIN28)
#define PIN_PTE29                   (PIN_PORTE | PIN29)
#define PIN_PTE30                   (PIN_PORTE | PIN30)
#define PIN_PTE31                   (PIN_PORTE | PIN31)

#define PIN_PTF0                    (PIN_PORTF | PIN0)
#define PIN_PTF1                    (PIN_PORTF | PIN1)
#define PIN_PTF2                    (PIN_PORTF | PIN2)
#define PIN_PTF3                    (PIN_PORTF | PIN3)
#define PIN_PTF4                    (PIN_PORTF | PIN4)
#define PIN_PTF5                    (PIN_PORTF | PIN5)
#define PIN_PTF6                    (PIN_PORTF | PIN6)
#define PIN_PTF7                    (PIN_PORTF | PIN7)
#define PIN_PTF8                    (PIN_PORTF | PIN8)
#define PIN_PTF9                    (PIN_PORTF | PIN9)
#define PIN_PTF10                   (PIN_PORTF | PIN10)
#define PIN_PTF11                   (PIN_PORTF | PIN11)
#define PIN_PTF12                   (PIN_PORTF | PIN12)
#define PIN_PTF13                   (PIN_PORTF | PIN13)
#define PIN_PTF14                   (PIN_PORTF | PIN14)
#define PIN_PTF15                   (PIN_PORTF | PIN15)
#define PIN_PTF16                   (PIN_PORTF | PIN16)
#define PIN_PTF17                   (PIN_PORTF | PIN17)
#define PIN_PTF18                   (PIN_PORTF | PIN18)
#define PIN_PTF19                   (PIN_PORTF | PIN19)
#define PIN_PTF20                   (PIN_PORTF | PIN20)
#define PIN_PTF21                   (PIN_PORTF | PIN21)
#define PIN_PTF22                   (PIN_PORTF | PIN22)
#define PIN_PTF23                   (PIN_PORTF | PIN23)
#define PIN_PTF24                   (PIN_PORTF | PIN24)
#define PIN_PTF25                   (PIN_PORTF | PIN25)
#define PIN_PTF26                   (PIN_PORTF | PIN26)
#define PIN_PTF27                   (PIN_PORTF | PIN27)
#define PIN_PTF28                   (PIN_PORTF | PIN28)
#define PIN_PTF29                   (PIN_PORTF | PIN29)
#define PIN_PTF30                   (PIN_PORTF | PIN30)
#define PIN_PTF31                   (PIN_PORTF | PIN31)

#define PIN_PTG0                    (PIN_PORTG | PIN0)
#define PIN_PTG1                    (PIN_PORTG | PIN1)
#define PIN_PTG2                    (PIN_PORTG | PIN2)
#define PIN_PTG3                    (PIN_PORTG | PIN3)
#define PIN_PTG4                    (PIN_PORTG | PIN4)
#define PIN_PTG5                    (PIN_PORTG | PIN5)
#define PIN_PTG6                    (PIN_PORTG | PIN6)
#define PIN_PTG7                    (PIN_PORTG | PIN7)
#define PIN_PTG8                    (PIN_PORTG | PIN8)
#define PIN_PTG9                    (PIN_PORTG | PIN9)
#define PIN_PTG10                   (PIN_PORTG | PIN10)
#define PIN_PTG11                   (PIN_PORTG | PIN11)
#define PIN_PTG12                   (PIN_PORTG | PIN12)
#define PIN_PTG13                   (PIN_PORTG | PIN13)
#define PIN_PTG14                   (PIN_PORTG | PIN14)
#define PIN_PTG15                   (PIN_PORTG | PIN15)
#define PIN_PTG16                   (PIN_PORTG | PIN16)
#define PIN_PTG17                   (PIN_PORTG | PIN17)
#define PIN_PTG18                   (PIN_PORTG | PIN18)
#define PIN_PTG19                   (PIN_PORTG | PIN19)
#define PIN_PTG20                   (PIN_PORTG | PIN20)
#define PIN_PTG21                   (PIN_PORTG | PIN21)
#define PIN_PTG22                   (PIN_PORTG | PIN22)
#define PIN_PTG23                   (PIN_PORTG | PIN23)
#define PIN_PTG24                   (PIN_PORTG | PIN24)
#define PIN_PTG25                   (PIN_PORTG | PIN25)
#define PIN_PTG26                   (PIN_PORTG | PIN26)
#define PIN_PTG27                   (PIN_PORTG | PIN27)

/* QuadSPI */

#define PIN_QSPI_IOFA0              (PIN_PORTD | PIN11 | PIN_OUTPUT_MODE_ALT7 | PIN_INPUT_ALT1  | IMCR(817))
#define PIN_QSPI_IOFA1              (PIN_PORTD | PIN7  | PIN_OUTPUT_MODE_ALT7 | PIN_INPUT_ALT1  | IMCR(818))
#define PIN_QSPI_IOFA2              (PIN_PORTD | PIN12 | PIN_OUTPUT_MODE_ALT7 | PIN_INPUT_ALT1  | IMCR(819))
#define PIN_QSPI_IOFA3              (PIN_PORTC | PIN2  | PIN_OUTPUT_MODE_ALT7 | PIN_INPUT_ALT1  | IMCR(820))
#define PIN_QSPI_PCSFA              (PIN_PORTC | PIN3  | PIN_OUTPUT_MODE_ALT6)
#define PIN_QSPI_SCKFA              (PIN_PORTD | PIN10 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(821))

/* Reset */

#define PIN_RESET                   (PIN_PORTA | PIN5  | PIN_OUTPUT_ALT7)

/* Synchronous Audio Interface (SAI) */

#define PIN_SAI0_BCLK               (PIN_PORTC | PIN12 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(827))
#define PIN_SAI0_D0                 (PIN_PORTB | PIN2  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(828))
#define PIN_SAI0_D1                 (PIN_PORTB | PIN29 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(829))
#define PIN_SAI0_D2                 (PIN_PORTC | PIN18 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(830))
#define PIN_SAI0_D3                 (PIN_PORTC | PIN19 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(831))
#define PIN_SAI0_MCLK               (PIN_PORTB | PIN3  | PIN_INPUT_ALT1  | IMCR(832))
#define PIN_SAI0_SYNC               (PIN_PORTC | PIN13 | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT1  | IMCR(833))

#define PIN_SAI1_BCLK               (PIN_PORTE | PIN8  | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(834))
#define PIN_SAI1_D0                 (PIN_PORTD | PIN13 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(835))
#define PIN_SAI1_MCLK               (PIN_PORTD | PIN14 | PIN_INPUT_ALT1  | IMCR(836))
#define PIN_SAI1_SYNC               (PIN_PORTD | PIN15 | PIN_OUTPUT_ALT6 | PIN_INPUT_ALT1  | IMCR(837))

/* SWD Debug */

#define PIN_SWD_CLK                 (PIN_PORTC | PIN4  | PIN_INPUT_ALT0  | IMCR(696))
#define PIN_SWD_DIO                 (PIN_PORTA | PIN4  | PIN_OUTPUT_ALT7 | PIN_INPUT_ALT0  | IMCR(698))
#define PIN_TRACE_SWO               (PIN_PORTA | PIN10 | PIN_OUTPUT_ALT7)

/* ETM Trace Debug */

#define PIN_TRACE_ETM_CLKOUT_0      (PIN_PORTC | PIN2  | PIN_OUTPUT_ALT6)
#define PIN_TRACE_ETM_CLKOUT_1      (PIN_PORTG | PIN6  | PIN_OUTPUT_ALT7)
#define PIN_TRACE_ETM_D0_0          (PIN_PORTD | PIN7  | PIN_OUTPUT_ALT6)
#define PIN_TRACE_ETM_D0_1          (PIN_PORTG | PIN7  | PIN_OUTPUT_ALT7)
#define PIN_TRACE_ETM_D1_0          (PIN_PORTD | PIN12 | PIN_OUTPUT_ALT4)
#define PIN_TRACE_ETM_D1_1          (PIN_PORTG | PIN15 | PIN_OUTPUT_ALT7)
#define PIN_TRACE_ETM_D2_0          (PIN_PORTD | PIN11 | PIN_OUTPUT_ALT4)
#define PIN_TRACE_ETM_D2_1          (PIN_PORTG | PIN16 | PIN_OUTPUT_ALT7)
#define PIN_TRACE_ETM_D3_0          (PIN_PORTD | PIN10 | PIN_OUTPUT_ALT4)
#define PIN_TRACE_ETM_D3_1          (PIN_PORTF | PIN31 | PIN_OUTPUT_ALT7)
#define PIN_TRACE_ETM_D4            (PIN_PORTG | PIN17 | PIN_OUTPUT_ALT7)
#define PIN_TRACE_ETM_D5            (PIN_PORTF | PIN28 | PIN_OUTPUT_ALT7)
#define PIN_TRACE_ETM_D6            (PIN_PORTG | PIN18 | PIN_OUTPUT_ALT7)
#define PIN_TRACE_ETM_D7            (PIN_PORTG | PIN19 | PIN_OUTPUT_ALT7)
#define PIN_TRACE_ETM_D8            (PIN_PORTG | PIN20 | PIN_OUTPUT_ALT7)
#define PIN_TRACE_ETM_D9            (PIN_PORTG | PIN21 | PIN_OUTPUT_ALT7)
#define PIN_TRACE_ETM_D10           (PIN_PORTG | PIN22 | PIN_OUTPUT_ALT7)
#define PIN_TRACE_ETM_D11           (PIN_PORTG | PIN23 | PIN_OUTPUT_ALT7)
#define PIN_TRACE_ETM_D12           (PIN_PORTG | PIN24 | PIN_OUTPUT_ALT7)
#define PIN_TRACE_ETM_D13           (PIN_PORTG | PIN25 | PIN_OUTPUT_ALT7)
#define PIN_TRACE_ETM_D14           (PIN_PORTG | PIN26 | PIN_OUTPUT_ALT7)
#define PIN_TRACE_ETM_D15           (PIN_PORTG | PIN27 | PIN_OUTPUT_ALT7)

/* Trigger MUX (TRGMUX) */

#define PIN_TRGMUX_IN0              (PIN_PORTB | PIN5  | PIN_INPUT_ALT1  | IMCR(856))
#define PIN_TRGMUX_IN1              (PIN_PORTB | PIN4  | PIN_INPUT_ALT1  | IMCR(857))
#define PIN_TRGMUX_IN2              (PIN_PORTB | PIN3  | PIN_INPUT_ALT1  | IMCR(858))
#define PIN_TRGMUX_IN3              (PIN_PORTB | PIN2  | PIN_INPUT_ALT1  | IMCR(859))
#define PIN_TRGMUX_IN4              (PIN_PORTD | PIN3  | PIN_INPUT_ALT1  | IMCR(860))
#define PIN_TRGMUX_IN5              (PIN_PORTD | PIN2  | PIN_INPUT_ALT1  | IMCR(861))
#define PIN_TRGMUX_IN6              (PIN_PORTE | PIN3  | PIN_INPUT_ALT1  | IMCR(862))
#define PIN_TRGMUX_IN7              (PIN_PORTD | PIN5  | PIN_INPUT_ALT1  | IMCR(863))
#define PIN_TRGMUX_IN8              (PIN_PORTC | PIN15 | PIN_INPUT_ALT1  | IMCR(864))
#define PIN_TRGMUX_IN9              (PIN_PORTC | PIN14 | PIN_INPUT_ALT1  | IMCR(865))
#define PIN_TRGMUX_IN10             (PIN_PORTC | PIN11 | PIN_INPUT_ALT1  | IMCR(866))
#define PIN_TRGMUX_IN11             (PIN_PORTC | PIN10 | PIN_INPUT_ALT1  | IMCR(867))
#define PIN_TRGMUX_IN12             (PIN_PORTA | PIN18 | PIN_INPUT_ALT1  | IMCR(868))
#define PIN_TRGMUX_IN13             (PIN_PORTA | PIN19 | PIN_INPUT_ALT1  | IMCR(869))
#define PIN_TRGMUX_IN14             (PIN_PORTA | PIN20 | PIN_INPUT_ALT1  | IMCR(870))
#define PIN_TRGMUX_IN15             (PIN_PORTA | PIN21 | PIN_INPUT_ALT1  | IMCR(871))
#define PIN_TRGMUX_OUT0             (PIN_PORTA | PIN1  | PIN_OUTPUT_ALT7)
#define PIN_TRGMUX_OUT1             (PIN_PORTD | PIN0  | PIN_OUTPUT_ALT7)
#define PIN_TRGMUX_OUT2             (PIN_PORTD | PIN1  | PIN_OUTPUT_ALT7)
#define PIN_TRGMUX_OUT3             (PIN_PORTA | PIN0  | PIN_OUTPUT_ALT7)
#define PIN_TRGMUX_OUT4             (PIN_PORTE | PIN10 | PIN_OUTPUT_ALT7)
#define PIN_TRGMUX_OUT5             (PIN_PORTE | PIN11 | PIN_OUTPUT_ALT7)
#define PIN_TRGMUX_OUT6             (PIN_PORTE | PIN15 | PIN_OUTPUT_ALT7)
#define PIN_TRGMUX_OUT7             (PIN_PORTE | PIN16 | PIN_OUTPUT_ALT7)
#define PIN_TRGMUX_OUT8             (PIN_PORTA | PIN31 | PIN_OUTPUT_ALT7)
#define PIN_TRGMUX_OUT9             (PIN_PORTB | PIN18 | PIN_OUTPUT_ALT7)
#define PIN_TRGMUX_OUT9             (PIN_PORTB | PIN18 | PIN_OUTPUT_ALT7)
#define PIN_TRGMUX_OUT10            (PIN_PORTB | PIN19 | PIN_OUTPUT_ALT7)
#define PIN_TRGMUX_OUT11            (PIN_PORTB | PIN20 | PIN_OUTPUT_ALT7)
#define PIN_TRGMUX_OUT12            (PIN_PORTB | PIN21 | PIN_OUTPUT_ALT7)
#define PIN_TRGMUX_OUT13            (PIN_PORTB | PIN22 | PIN_OUTPUT_ALT7)
#define PIN_TRGMUX_OUT14            (PIN_PORTB | PIN23 | PIN_OUTPUT_ALT7)
#define PIN_TRGMUX_OUT15            (PIN_PORTC | PIN24 | PIN_OUTPUT_ALT7)

/* Wakeup Unit (WKPU) */

#define PIN_WKPU0                   (PIN_PORTA | PIN2  | PIN_INPUT_WKPU  | WPKU(0))
#define PIN_WKPU1                   (PIN_PORTD | PIN3  | PIN_INPUT_WKPU  | WPKU(1))
#define PIN_WKPU2                   (PIN_PORTC | PIN7  | PIN_INPUT_WKPU  | WPKU(2))
#define PIN_WKPU3                   (PIN_PORTC | PIN6  | PIN_INPUT_WKPU  | WPKU(3))
#define PIN_WKPU4                   (PIN_PORTA | PIN13 | PIN_INPUT_WKPU  | WPKU(4))
#define PIN_WKPU5                   (PIN_PORTA | PIN1  | PIN_INPUT_WKPU  | WPKU(5))
#define PIN_WKPU6                   (PIN_PORTD | PIN0  | PIN_INPUT_WKPU  | WPKU(6))
#define PIN_WKPU7                   (PIN_PORTB | PIN0  | PIN_INPUT_WKPU  | WPKU(7))
#define PIN_WKPU8                   (PIN_PORTB | PIN2  | PIN_INPUT_WKPU  | WPKU(8))
#define PIN_WKPU9                   (PIN_PORTD | PIN2  | PIN_INPUT_WKPU  | WPKU(9))
#define PIN_WKPU10                  (PIN_PORTC | PIN9  | PIN_INPUT_WKPU  | WPKU(10))
#define PIN_WKPU11                  (PIN_PORTB | PIN13 | PIN_INPUT_WKPU  | WPKU(11))
#define PIN_WKPU12                  (PIN_PORTB | PIN12 | PIN_INPUT_WKPU  | WPKU(12))
#define PIN_WKPU13                  (PIN_PORTB | PIN16 | PIN_INPUT_WKPU  | WPKU(13))
#define PIN_WKPU14                  (PIN_PORTB | PIN17 | PIN_INPUT_WKPU  | WPKU(14))
#define PIN_WKPU15                  (PIN_PORTA | PIN6  | PIN_INPUT_WKPU  | WPKU(15))
#define PIN_WKPU16                  (PIN_PORTB | PIN11 | PIN_INPUT_WKPU  | WPKU(16))
#define PIN_WKPU17                  (PIN_PORTB | PIN9  | PIN_INPUT_WKPU  | WPKU(17))
#define PIN_WKPU18                  (PIN_PORTC | PIN11 | PIN_INPUT_WKPU  | WPKU(18))
#define PIN_WKPU19                  (PIN_PORTE | PIN16 | PIN_INPUT_WKPU  | WPKU(19))
#define PIN_WKPU20                  (PIN_PORTA | PIN15 | PIN_INPUT_WKPU  | WPKU(20))
#define PIN_WKPU21                  (PIN_PORTA | PIN9  | PIN_INPUT_WKPU  | WPKU(21))
#define PIN_WKPU22                  (PIN_PORTD | PIN4  | PIN_INPUT_WKPU  | WPKU(22))
#define PIN_WKPU23                  (PIN_PORTA | PIN8  | PIN_INPUT_WKPU  | WPKU(23))
#define PIN_WKPU24                  (PIN_PORTD | PIN13 | PIN_INPUT_WKPU  | WPKU(24))
#define PIN_WKPU25                  (PIN_PORTB | PIN8  | PIN_INPUT_WKPU  | WPKU(25))
#define PIN_WKPU26                  (PIN_PORTE | PIN0  | PIN_INPUT_WKPU  | WPKU(26))
#define PIN_WKPU27                  (PIN_PORTE | PIN2  | PIN_INPUT_WKPU  | WPKU(27))
#define PIN_WKPU28                  (PIN_PORTE | PIN11 | PIN_INPUT_WKPU  | WPKU(28))
#define PIN_WKPU29                  (PIN_PORTE | PIN6  | PIN_INPUT_WKPU  | WPKU(29))
#define PIN_WKPU30                  (PIN_PORTE | PIN14 | PIN_INPUT_WKPU  | WPKU(30))
#define PIN_WKPU31                  (PIN_PORTA | PIN16 | PIN_INPUT_WKPU  | WPKU(31))
#define PIN_WKPU32                  (PIN_PORTE | PIN5  | PIN_INPUT_WKPU  | WPKU(32))
#define PIN_WKPU33                  (PIN_PORTB | PIN15 | PIN_INPUT_WKPU  | WPKU(33))
#define PIN_WKPU34                  (PIN_PORTA | PIN25 | PIN_INPUT_WKPU  | WPKU(34))
#define PIN_WKPU35                  (PIN_PORTA | PIN26 | PIN_INPUT_WKPU  | WPKU(35))
#define PIN_WKPU36                  (PIN_PORTC | PIN18 | PIN_INPUT_WKPU  | WPKU(36))
#define PIN_WKPU37                  (PIN_PORTA | PIN30 | PIN_INPUT_WKPU  | WPKU(37))
#define PIN_WKPU38                  (PIN_PORTB | PIN19 | PIN_INPUT_WKPU  | WPKU(38))
#define PIN_WKPU39                  (PIN_PORTB | PIN21 | PIN_INPUT_WKPU  | WPKU(39))
#define PIN_WKPU40                  (PIN_PORTB | PIN23 | PIN_INPUT_WKPU  | WPKU(40))
#define PIN_WKPU41                  (PIN_PORTB | PIN26 | PIN_INPUT_WKPU  | WPKU(41))
#define PIN_WKPU42                  (PIN_PORTB | PIN28 | PIN_INPUT_WKPU  | WPKU(42))
#define PIN_WKPU43                  (PIN_PORTC | PIN20 | PIN_INPUT_WKPU  | WPKU(43))
#define PIN_WKPU44                  (PIN_PORTC | PIN23 | PIN_INPUT_WKPU  | WPKU(44))
#define PIN_WKPU45                  (PIN_PORTC | PIN25 | PIN_INPUT_WKPU  | WPKU(45))
#define PIN_WKPU46                  (PIN_PORTC | PIN24 | PIN_INPUT_WKPU  | WPKU(46))
#define PIN_WKPU47                  (PIN_PORTC | PIN29 | PIN_INPUT_WKPU  | WPKU(47))
#define PIN_WKPU48                  (PIN_PORTC | PIN26 | PIN_INPUT_WKPU  | WPKU(48))
#define PIN_WKPU49                  (PIN_PORTC | PIN31 | PIN_INPUT_WKPU  | WPKU(49))
#define PIN_WKPU50                  (PIN_PORTD | PIN23 | PIN_INPUT_WKPU  | WPKU(50))
#define PIN_WKPU51                  (PIN_PORTD | PIN27 | PIN_INPUT_WKPU  | WPKU(51))
#define PIN_WKPU52                  (PIN_PORTD | PIN29 | PIN_INPUT_WKPU  | WPKU(52))
#define PIN_WKPU53                  (PIN_PORTD | PIN31 | PIN_INPUT_WKPU  | WPKU(53))
#define PIN_WKPU54                  (PIN_PORTD | PIN20 | PIN_INPUT_WKPU  | WPKU(54))
#define PIN_WKPU55                  (PIN_PORTE | PIN18 | PIN_INPUT_WKPU  | WPKU(55))
#define PIN_WKPU56                  (PIN_PORTE | PIN21 | PIN_INPUT_WKPU  | WPKU(56))
#define PIN_WKPU57                  (PIN_PORTE | PIN23 | PIN_INPUT_WKPU  | WPKU(57))
#define PIN_WKPU58                  (PIN_PORTE | PIN25 | PIN_INPUT_WKPU  | WPKU(58))
#define PIN_WKPU59                  (PIN_PORTA | PIN20 | PIN_INPUT_WKPU  | WPKU(59))

#endif /* __ARCH_ARM_SRC_S32K3XX_HARDWARE_S32K344_PINMUX_H */
