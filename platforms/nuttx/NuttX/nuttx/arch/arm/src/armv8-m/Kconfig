#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

comment "ARMv8-M Configuration Options"

config ARMV8M_HAVE_ICACHE
	bool
	default n

config ARMV8M_HAVE_DCACHE
	bool
	default n

config ARMV8M_USEBASEPRI
	bool "Use BASEPRI Register"
	default y if ARCH_HIPRI_INTERRUPT
	---help---
		Use the BASEPRI register to enable and disable interrupts. By
		default, the PRIMASK register is used for this purpose. This
		usually results in hardfaults when supervisor calls are made.
		Though, these hardfaults are properly handled by the RTOS, the
		hardfaults can confuse some debuggers. With the BASEPRI
		register, these hardfaults, will be avoided. For more details see
		https://cwiki.apache.org/confluence/display/NUTTX/ARMv7-M+Hardfaults%2C+SVCALL%2C+and+Debuggers

		WARNING:  If CONFIG_ARCH_HIPRI_INTERRUPT is selected, then you
		MUST select CONFIG_ARMV8M_USEBASEPRI.  The Kconfig dependencies
		here will permit to select an invalid configuration because it
		cannot enforce that requirement.  If you create this invalid
		configuration, you will encounter some problems that may be
		very difficult to debug.

config ARMV8M_ICACHE
	bool "Use I-Cache"
	default n
	depends on ARMV8M_HAVE_ICACHE
	select ARCH_ICACHE

config ARMV8M_DCACHE
	bool "Use D-Cache"
	default n
	depends on ARMV8M_HAVE_DCACHE
	select ARCH_DCACHE

config ARMV8M_DCACHE_WRITETHROUGH
	bool "D-Cache Write-Through"
	default n
	depends on ARMV8M_DCACHE

config ARMV8M_HAVE_ITCM
	bool
	default n

config ARMV8M_HAVE_DTCM
	bool
	default n

config ARMV8M_ITCM
	bool "Use ITCM"
	default n
	depends on ARMV8M_HAVE_ITCM

config ARMV8M_DTCM
	bool "Use DTCM"
	default n
	depends on ARMV8M_HAVE_DTCM

choice
	prompt "Toolchain Selection"
	default ARMV8M_TOOLCHAIN_GNU_EABI

config ARMV8M_TOOLCHAIN_BUILDROOT
	bool "Buildroot (Cygwin or Linux)"
	depends on !WINDOWS_NATIVE
	select ARCH_TOOLCHAIN_GNU

config ARMV8M_TOOLCHAIN_GNU_EABI
	bool "Generic GNU EABI toolchain"
	select ARCH_TOOLCHAIN_GNU
	---help---
		This option should work for any modern GNU toolchain (GCC 4.5 or newer)
		configured for arm-none-eabi.

config ARMV8M_TOOLCHAIN_CLANG
	bool "Generic Clang toolchain"
	select ARCH_TOOLCHAIN_CLANG

endchoice

config ARMV8M_OABI_TOOLCHAIN
	bool "OABI (vs EABI)"
	default n
	depends on ARMV8M_TOOLCHAIN_BUILDROOT
	---help---
		Most of the older buildroot toolchains are OABI and are named
		arm-nuttx-elf- vs. arm-nuttx-eabi-

config ARMV8M_TARGET2_PREL
	bool "R_ARM_TARGET2 is PC relative"
	default n if !CXX_EXCEPTION
	default y if CXX_EXCEPTION
	depends on LIBC_ARCH_ELF
	---help---
		Perform a PC relative relocation for relocation type R_ARM_TARGET2

choice
	prompt "Select the stack protection Schema"
	default ARMV8M_STACKCHECK_NONE

config ARMV8M_STACKCHECK_NONE
	bool "Do not check for stack overflow"

config ARMV8M_STACKCHECK
	bool "Check for stack overflow on each function call"
	---help---
		This check uses R10 to check for a stack overflow within each
		function call. This has performances and code size impacts, but it
		will be able to catch hard to find stack overflows.

		Currently only available only for the STM32, SAM3/4 and SAMA5D
		architectures.  The changes are not complex and patches for
		other architectures will be accepted.

		This option requires that you are using a GCC toolchain and that
		you also include -finstrument-functions in your CFLAGS when you
		compile.  This addition to your CFLAGS should probably be added
		to the definition of the CFFLAGS in your board Make.defs file.

config ARMV8M_STACKCHECK_HARDWARE
	bool "Check for stack overflow by stack pointer limit register"
	---help---
		This option signifies the CPU has the MSPLIM, PSPLIM registers.

		The stack pointer limit registers, MSPLIM, PSPLIM, limit the
		extend to which the Main and Process Stack Pointers, respectively,
		can descend. MSPLIM, PSPLIM are always present in ARMv8-M
		MCUs that implement the ARMv8-M Main Extension (Mainline).

		In an ARMv8-M Mainline implementation with the Security Extension
		the MSPLIM, PSPLIM registers have additional Secure instances.
		In an ARMv8-M Baseline implementation with the Security Extension
		the MSPLIM, PSPLIM registers have only Secure instances.

endchoice

config ARMV8M_ITMSYSLOG
	bool "ITM SYSLOG support"
	default n
	select ARCH_SYSLOG
	select SYSLOG
	---help---
		Enable hooks to support ITM syslog output.  This requires additional
		MCU support in order to be used.  See arch/arm/src/armv8-m/itm_syslog.h
		for additional initialization information.

if ARMV8M_ITMSYSLOG

config ARMV8M_ITMSYSLOG_PORT
	int "ITM SYSLOG Port"
	default 0
	range 0 31

config ARMV8M_ITMSYSLOG_SWODIV
	int "ITM SYSLOG SWO divider"
	default 15
	range 1 8192

endif # ARMV8M_ITMSYSLOG

config ARMV8M_SYSTICK
	bool "SysTick timer driver"
	depends on TIMER
	---help---
		Enable SysTick timer driver.
