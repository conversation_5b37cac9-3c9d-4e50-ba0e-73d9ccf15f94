############################################################################
# arch/arm/src/nrf52/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

include armv7-m/Make.defs

ifeq ($(CONFIG_NRF52_SYSTIMER_SYSTICK),y)
CHIP_CSRCS += nrf52_systick.c
else
ifeq ($(CONFIG_NRF52_SYSTIMER_RTC),y)
CHIP_CSRCS += nrf52_tickless_rtc.c
endif
endif

CHIP_CSRCS += nrf52_start.c nrf52_clockconfig.c nrf52_irq.c nrf52_utils.c
CHIP_CSRCS += nrf52_allocateheap.c nrf52_lowputc.c nrf52_gpio.c nrf52_nvmc.c
CHIP_CSRCS += nrf52_uid.c

ifeq ($(CONFIG_ARCH_CHIP_NRF52832),y)
CHIP_CSRCS += nrf52832_errdata.c
endif

ifeq ($(CONFIG_BUILD_PROTECTED),y)
CHIP_CSRCS += nrf52_userspace.c nrf52_mpuinit.c
endif

ifneq ($(CONFIG_ARCH_IDLE_CUSTOM),y)
CHIP_CSRCS += nrf52_idle.c
endif

ifeq ($(CONFIG_NRF52_GPIOTE),y)
CHIP_CSRCS += nrf52_gpiote.c
endif

ifeq ($(CONFIG_NRF52_UART),y)
CHIP_CSRCS += nrf52_serial.c
endif

ifeq ($(CONFIG_NRF52_PROGMEM),y)
CHIP_CSRCS  += nrf52_flash.c
endif

ifeq ($(CONFIG_NRF52_WDT),y)
CHIP_CSRCS += nrf52_wdt.c

ifeq ($(CONFIG_WATCHDOG),y)
CHIP_CSRCS += nrf52_wdt_lowerhalf.c
endif
endif

ifeq ($(CONFIG_NRF52_RNG),y)
CHIP_CSRCS += nrf52_rng.c
endif

ifeq ($(CONFIG_NRF52_SPI_MASTER),y)
CHIP_CSRCS += nrf52_spi.c
endif

ifeq ($(CONFIG_NRF52_I2C_MASTER),y)
CHIP_CSRCS += nrf52_i2c.c
endif

ifeq ($(CONFIG_I2C_BITBANG),y)
CHIP_CSRCS += nrf52_i2c_bitbang.c
endif

ifeq ($(CONFIG_NRF52_PPI),y)
CHIP_CSRCS += nrf52_ppi.c
endif

ifeq ($(CONFIG_NRF52_RADIO),y)
CHIP_CSRCS += nrf52_radio.c
endif

ifeq ($(CONFIG_NRF52_TIMER),y)
CHIP_CSRCS += nrf52_tim.c
ifeq ($(CONFIG_TIMER),y)
CHIP_CSRCS += nrf52_tim_lowerhalf.c
endif
endif

ifeq ($(CONFIG_NRF52_RTC),y)
CHIP_CSRCS += nrf52_rtc.c
endif

ifeq ($(CONFIG_NRF52_PWM),y)
CHIP_CSRCS += nrf52_pwm.c
endif

ifeq ($(CONFIG_NRF52_SAADC),y)
CHIP_CSRCS += nrf52_adc.c
endif

ifeq ($(CONFIG_PM),y)
CHIP_CSRCS += nrf52_pminitialize.c
endif

ifeq ($(CONFIG_NRF52_SOFTDEVICE_CONTROLLER),y)

NRFXLIB_UNPACK  := sdk-nrfxlib
NRFXLIB_VER     := 1.4.2
NRFXLIB_REF     := v$(NRFXLIB_VER)
NRFXLIB_TGZ     := $(NRFXLIB_REF).tar.gz
NRFXLIB_URL     := https://github.com/nrfconnect/sdk-nrfxlib/archive

$(NRFXLIB_TGZ):
	$(Q) echo "Downloading: NRFXLIB"
	$(Q) curl -L $(NRFXLIB_URL)/$(NRFXLIB_TGZ) -o chip/$(NRFXLIB_TGZ)

chip/$(NRFXLIB_UNPACK): $(NRFXLIB_TGZ)
	$(Q) echo "Unpacking: NRXFLIB"
	$(Q) cd chip && tar zxf $(NRFXLIB_TGZ)
	$(Q) mv chip/$(NRFXLIB_UNPACK)-$(NRFXLIB_VER)* chip/$(NRFXLIB_UNPACK)
	$(Q) touch chip/$(NRFXLIB_UNPACK)

context:: chip/$(NRFXLIB_UNPACK)

distclean::
	$(call DELFILE, chip/$(NRFXLIB_TGZ))
	$(call DELDIR, chip/$(NRFXLIB_UNPACK))

CHIP_CSRCS += nrf52_sdc.c

NRFX_DIR = $(ARCH_SRCDIR)$(DELIM)chip$(DELIM)nrfx
NRFXLIB_DIR = $(ARCH_SRCDIR)$(DELIM)chip$(DELIM)sdk-nrfxlib
CMSIS_DIR = $(ARCH_SRCDIR)$(DELIM)chip$(DELIM)CMSIS_5

INCLUDES += \
	$(shell $(INCDIR) "$(CC)" $(ARCH_SRCDIR)$(DELIM)chip$(DELIM)sdc) \
	$(shell $(INCDIR) "$(CC)" $(NRFXLIB_DIR)$(DELIM)mpsl$(DELIM)include) \
	$(shell $(INCDIR) "$(CC)" $(NRFXLIB_DIR)$(DELIM)softdevice_controller$(DELIM)include)

ifeq ($(CONFIG_ARCH_CHIP_NRF52832),y)
	CFLAGS += -DNRF52832_XXAB
else
	ifeq ($(CONFIG_ARCH_CHIP_NRF52840),y)
		CFLAGS += -DNRF52840_XXAB
	endif
endif

ifeq ($(CONFIG_ARCH_FPU),y)
	LIB_VARIANT=hard-float
else
	LIB_VARIANT=soft-float
endif

EXTRA_LIBPATHS += \
	-L $(NRFXLIB_DIR)$(DELIM)mpsl$(DELIM)lib$(DELIM)cortex-m4$(DELIM)$(LIB_VARIANT) \
	-L $(NRFXLIB_DIR)$(DELIM)softdevice_controller$(DELIM)lib$(DELIM)cortex-m4$(DELIM)$(LIB_VARIANT)

EXTRA_LIBS += -lmpsl -lsoftdevice_controller_s$(CONFIG_NRF52_SDC_VERSION)

endif
