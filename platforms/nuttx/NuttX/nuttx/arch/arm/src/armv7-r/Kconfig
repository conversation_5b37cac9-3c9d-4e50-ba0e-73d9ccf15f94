#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

comment "ARMv7-R Configuration Options"

config ARMV7R_HAVE_GICv2
	bool "ARMV7R_GICv2 support"
	default y
	---help---
		Selected by the configuration tool if the architecture supports the
		Generic Interrupt Controller (GIC)

config ARMV7R_MEMINIT
	bool
	default y if BOOT_SDRAM_DATA
	default n if !BOOT_SDRAM_DATA
	---help---
		If this configuration *not* selected, then it is assumed that all
		memory resources are initialized via arm_data_initialize() and
		available at power-up reset time.  Other memories, such as SDRAM or
		some ECC SRAM memories, require some platform-specific
		initialization first.  In that case, this option should be selected
		and the platform-specific implementation of arm_boot() must perform
		the memory initialization first, then explicitly call
		arm_data_initialize().

config ARMV7R_HAVE_L2CC
	bool
	default n
	---help---
		Selected by the configuration tool if the architecture supports any
		kind of L2 cache.

config ARMV7R_HAVE_L2CC_PL310
	bool
	default n
	select ARMV7R_HAVE_L2CC
	---help---
		Set by architecture-specific code if the hardware supports a PL310
		r3p2 L2 cache (only version r3p2 is supported).

if ARMV7R_HAVE_L2CC

menu "L2 Cache Configuration"

config ARMV7R_L2CC_PL310
	bool "ARMv7-A L2CC P310 Support"
	default n
	depends on ARMV7R_HAVE_L2CC_PL310 && EXPERIMENTAL
	select ARCH_L2CACHE
	---help---
		Enable the 2 Cache Controller (L2CC) is based on the L2CC-PL310 ARM
		multi-way cache macrocell, version r3p2.  The addition of an on-chip
		secondary cache, also referred to as a Level 2 or L2 cache, is a
		method of improving the system performance when significant memory
		traffic is generated by the processor.

if ARCH_L2CACHE
if ARMV7R_L2CC_PL310

config PL310_LOCKDOWN_BY_MASTER
	bool "PL310 Lockdown by Master"
	default n

config PL310_LOCKDOWN_BY_LINE
	bool "PL310 Lockdown by Line"
	default n

config PL310_ADDRESS_FILTERING
	bool "PL310 Address Filtering by Line"
	default n

endif # ARMV7R_L2CC_PL310

choice
	prompt "L2 Cache Associativity"
	default ARMV7R_ASSOCIATIVITY_8WAY
	depends on ARCH_L2CACHE
	---help---
		This choice specifies the associativity of L2 cache in terms of the
		number of ways.  This value could be obtained by querying cache
		configuration registers.  However, by defining a configuration
		setting instead, we can avoid using RAM memory to hold information
		about properties of the memory.

config ARMV7R_ASSOCIATIVITY_8WAY
	bool "8-Way Associativity"

config ARMV7R_ASSOCIATIVITY_16WAY
	bool "16-Way Associativity"

endchoice # L2 Cache Associativity

choice
	prompt "L2 Cache Way Size"
	default ARMV7R_WAYSIZE_16KB
	depends on ARCH_L2CACHE
	---help---
		This choice specifies size of each way. This value can be obtained
		by querying cache configuration registers.  However, by defining a
		configuration setting instead, we can avoid using RAM memory to hold
		information

config ARMV7R_WAYSIZE_16KB
	bool "16 KiB"

config ARMV7R_WAYSIZE_32KB
	bool "32 KiB"

config ARMV7R_WAYSIZE_64KB
	bool "64 KiB"

config ARMV7R_WAYSIZE_128KB
	bool "128 KiB"

config ARMV7R_WAYSIZE_256KB
	bool "256 KiB"

config ARMV7R_WAYSIZE_512KB
	bool "512 KiB"

endchoice # L2 Cache Associativity
endif # ARCH_L2CACHE
endmenu # L2 Cache Configuration
endif # ARMV7R_HAVE_L2CC

choice
	prompt "Toolchain Selection"
	default ARMV7R_TOOLCHAIN_GNU_EABI

config ARMV7R_TOOLCHAIN_BUILDROOT
	bool "Buildroot (Cygwin or Linux)"
	select ARCH_TOOLCHAIN_GNU
	depends on !WINDOWS_NATIVE

config ARMV7R_TOOLCHAIN_GNU_EABI
	bool "Generic GNU EABI toolchain"
	select ARCH_TOOLCHAIN_GNU
	---help---
		This option should work for any modern GNU toolchain (GCC 4.5 or newer)
		configured for arm-none-eabi-.

config ARMV7R_TOOLCHAIN_GNU_OABI
	bool "Generic GNU OABI toolchain"
	select ARCH_TOOLCHAIN_GNU
	---help---
		This option should work for any GNU toolchain configured for arm-elf-.

endchoice # Toolchain Selection

config ARMV7R_OABI_TOOLCHAIN
	bool "OABI (vs EABI)"
	default n
	depends on ARMV7R_TOOLCHAIN_BUILDROOT
	---help---
		Most of the older buildroot toolchains are OABI and are named
		arm-nuttx-elf- vs. arm-nuttx-eabi-

config ARMV7R_HAVE_DECODEFIQ
	bool
	default n

config ARMV7R_DECODEFIQ
	bool "FIQ Handler"
	default n
	depends on ARMV7R_HAVE_DECODEFIQ
	---help---
		Select this option if your platform supports the function
		arm_decodefiq().
