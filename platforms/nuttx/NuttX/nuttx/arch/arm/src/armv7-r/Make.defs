############################################################################
# arch/arm/src/armv7-r/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

# Common ARM files

include common/Make.defs

# The vector table is the "head" object, i.e., the one that must forced into
# the link in order to draw in all of the other components

HEAD_ASRC += arm_vectortab.S

# Common assembly language files

CMN_CSRCS += arm_cache.c arm_dataabort.c arm_doirq.c arm_gicv2.c
CMN_CSRCS += arm_initialstate.c arm_prefetchabort.c
CMN_CSRCS += arm_schedulesigaction.c arm_sigdeliver.c
CMN_CSRCS += arm_syscall.c arm_tcbinfo.c arm_undefinedinsn.c
CMN_CSRCS += arm_perf.c

# Common C source files

CMN_ASRCS += arm_head.S arm_vectoraddrexcptn.S
CMN_ASRCS += arm_vectors.S cp15_cache_size.S
CMN_ASRCS += cp15_clean_dcache_all.S cp15_clean_dcache.S
CMN_ASRCS += cp15_coherent_dcache.S cp15_flush_dcache_all.S
CMN_ASRCS += cp15_flush_dcache.S cp15_invalidate_dcache_all.S
CMN_ASRCS += cp15_invalidate_dcache.S


ifeq ($(CONFIG_BUILD_PROTECTED),y)
  CMN_CSRCS += arm_mpu.c
endif

ifeq ($(CONFIG_ARMV7R_L2CC_PL310),y)
  CMN_CSRCS += arm_l2cc_pl310.c
endif

ifeq ($(CONFIG_ARCH_FPU),y)
  CMN_CSRCS += arm_fpucmp.c
  CMN_ASRCS += arm_fpuconfig.S
endif
