############################################################################
# arch/avr/src/avr32/Toolchain.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

# Setup for the selected toolchain

#
# Since all of the supported toolchains are variants of the Atmel-patched
# GCC, the only interesting question to answer here is whether or not
# the build is hosted on Windows, and how to override the configuration.
#
# NOTE: There is a logic error in the following:  CONFIG_HOST_WINDOWS means
# that we are operating on a Windows platform.  But in the case where we
# have an AVR32 toolchain built under Cygwin, the correct setting would be
# GNU, not AVRTOOLSW.
#

ifneq ($(CONFIG_DEBUG_NOOPT),y)
  ARCHOPTIMIZATION += -fno-strict-aliasing
endif

ifeq ($(CONFIG_DEBUG_SYMBOLS),y)
  ARCHOPTIMIZATION += -g
endif

ifeq ($(CONFIG_FRAME_POINTER),y)
  ARCHOPTIMIZATION += -fno-omit-frame-pointer -fno-optimize-sibling-calls
else
  ARCHOPTIMIZATION += -fomit-frame-pointer
endif

ARCHCFLAGS += -fno-common
ARCHCXXFLAGS += -fno-common -nostdinc++

ARCHCFLAGS += -Wall -Wstrict-prototypes -Wshadow -Wundef
ARCHCXXFLAGS += -Wall -Wshadow -Wundef

ifneq ($(CONFIG_CXX_EXCEPTION),y)
  ARCHCXXFLAGS += -fno-exceptions -fcheck-new
endif

ifneq ($(CONFIG_CXX_RTTI),y)
  ARCHCXXFLAGS += -fno-rtti
endif

LDFLAGS += -nostdlib

CROSSDEV = avr32-
ARCHCPUFLAGS = -mpart=uc3b0256

# AVR Tools or avr32-toolchain from https://github.com/jsnyder/avr32-toolchain

# Default toolchain

CC = $(CROSSDEV)gcc
CXX = $(CROSSDEV)g++
CPP = $(CROSSDEV)gcc -E -P -x c
LD = $(CROSSDEV)ld
STRIP = $(CROSSDEV)strip --strip-unneeded
AR = $(CROSSDEV)ar rcs
NM = $(CROSSDEV)nm
OBJCOPY = $(CROSSDEV)objcopy
OBJDUMP = $(CROSSDEV)objdump

# Add the builtin library

EXTRA_LIBS += $(wildcard $(shell $(CC) $(ARCHCPUFLAGS) --print-libgcc-file-name))

ifneq ($(CONFIG_LIBM),y)
  EXTRA_LIBS += $(wildcard $(shell $(CC) $(ARCHCPUFLAGS) --print-file-name=libm.a))
endif

ifeq ($(CONFIG_LIBSUPCXX),y)
  EXTRA_LIBS += $(wildcard $(shell $(CC) $(ARCHCPUFLAGS) --print-file-name=libsupc++.a))
endif
