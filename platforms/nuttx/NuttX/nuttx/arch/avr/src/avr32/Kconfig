#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

if ARCH_FAMILY_AVR32
comment "AVR32 Configuration Options"

choice
	prompt "Toolchain"
	default AVR32_AVRTOOLSW if TOOLCHAIN_WINDOWS
	default AVR32_AVRTOOLSL if HOST_LINUX

config AVR32_AVRTOOLSW
	bool "AVR tools for Windows"
	depends on TOOLCHAIN_WINDOWS
	select CYGWIN_WINTOOL if WINDOWS_CYGWIN

config AVR32_AVRTOOLSL
	bool "AVR tools for Linux"
	depends on HOST_LINUX

endchoice # Toolchain
endif
