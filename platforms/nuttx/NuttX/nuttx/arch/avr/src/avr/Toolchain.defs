############################################################################
# arch/avr/src/avr/Toolchain.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

# Setup for the selected toolchain

#
# Handle old-style chip-specific toolchain names in the absence of
# a new-style toolchain specification, force the selection of a single
# toolchain and allow the selected toolchain to be overridden by a
# command-line selection.
#

ifeq ($(filter y, \
      $(CONFIG_AVR_BUILDROOT_TOOLCHAIN) \
    ),y)
  CONFIG_AVR_TOOLCHAIN ?= BUILDROOT
endif

ifeq ($(filter y, \
      $(CONFIG_AVR_CROSSPACK_TOOLCHAIN) \
    ),y)
  CONFIG_AVR_TOOLCHAIN ?= CROSSPACK
endif

ifeq ($(filter y, \
      $(CONFIG_AVR_LINUXGCC_TOOLCHAIN) \
    ),y)
  CONFIG_AVR_TOOLCHAIN ?= LINUXGCC
endif

ifeq ($(filter y, \
      $(CONFIG_AVR_WINAVR_TOOLCHAIN) \
    ),y)
  CONFIG_AVR_TOOLCHAIN ?= WINAVR
endif

ifeq ($(filter y, \
      $(CONFIG_AVR_ATMEL_AVR_TOOLCHAIN) \
    ),y)
  CONFIG_AVR_TOOLCHAIN ?= WINAVR
endif

# Chip-specific CPU flags

ifeq ($(CONFIG_ARCH_CHIP_ATMEGA128),y)
  ARCHCPUFLAGS += -mmcu=atmega128
  LDFLAGS += -mavr51
else ifeq ($(CONFIG_ARCH_CHIP_ATMEGA1284P),y)
  ARCHCPUFLAGS += -mmcu=atmega1284p
  LDFLAGS += -mavr51
else ifeq ($(CONFIG_ARCH_CHIP_AT90USB646),y)
  ARCHCPUFLAGS += -mmcu=at90usb646
  LDFLAGS += -mavr5
else ifeq ($(CONFIG_ARCH_CHIP_AT90USB647),y)
  ARCHCPUFLAGS += -mmcu=at90usb647
  LDFLAGS += -mavr5
else ifeq ($(CONFIG_ARCH_CHIP_AT90USB1286),y)
  ARCHCPUFLAGS += -mmcu=at90usb1286
  LDFLAGS += -mavr51
else ifeq ($(CONFIG_ARCH_CHIP_AT90USB1287),y)
  ARCHCPUFLAGS += -mmcu=at90usb1287
  LDFLAGS += -mavr51
else ifeq ($(CONFIG_ARCH_CHIP_ATMEGA2560),y)
  ARCHCPUFLAGS += -mmcu=atmega2560
  LDFLAGS += -mavr6
endif

ifeq ($(CONFIG_DEBUG_CUSTOMOPT),y)
  ARCHOPTIMIZATION += $(CONFIG_DEBUG_OPTLEVEL)
else ifeq ($(CONFIG_DEBUG_FULLOPT),y)
  ARCHOPTIMIZATION += -O2
endif

ifneq ($(CONFIG_DEBUG_NOOPT),y)
  ARCHOPTIMIZATION += -fno-strict-aliasing
endif

ifeq ($(CONFIG_FRAME_POINTER),y)
  ARCHOPTIMIZATION += -fno-omit-frame-pointer -fno-optimize-sibling-calls
else
  ARCHOPTIMIZATION += -fomit-frame-pointer
endif

ARCHCFLAGS += -fno-common
ARCHCXXFLAGS += -fno-common -nostdinc++

ARCHCFLAGS += -Wall -Wstrict-prototypes -Wshadow -Wundef
ARCHCXXFLAGS += -Wall -Wshadow -Wundef

ifneq ($(CONFIG_CXX_EXCEPTION),y)
  ARCHCXXFLAGS += -fno-exceptions -fcheck-new
endif

ifneq ($(CONFIG_CXX_RTTI),y)
  ARCHCXXFLAGS += -fno-rtti
endif

LDFLAGS += -nostdlib

# NuttX buildroot GCC toolchain under Linux or Cygwin

ifeq ($(CONFIG_AVR_TOOLCHAIN),BUILDROOT)
  CROSSDEV ?= avr-nuttx-elf-
endif

# AVR CrossPack under macOS

ifeq ($(CONFIG_AVR_TOOLCHAIN),CROSSPACK)
  CROSSDEV ?= avr-
endif

# GCC toolchain under Linux

ifeq ($(CONFIG_AVR_TOOLCHAIN),LINUXGCC)
  CROSSDEV ?= avr-
endif

ifeq ($(CONFIG_AVR_TOOLCHAIN),WINAVR)
  CROSSDEV ?= avr-
endif

# Default toolchain

CC = $(CROSSDEV)gcc
CXX = $(CROSSDEV)g++
CPP = $(CROSSDEV)gcc -E -P -x c
LD = $(CROSSDEV)ld
STRIP = $(CROSSDEV)strip --strip-unneeded
AR = $(CROSSDEV)ar rcs
NM = $(CROSSDEV)nm
OBJCOPY = $(CROSSDEV)objcopy
OBJDUMP = $(CROSSDEV)objdump

# Add the builtin library

EXTRA_LIBS += $(wildcard $(shell $(CC) $(ARCHCPUFLAGS) --print-libgcc-file-name))

ifneq ($(CONFIG_LIBM),y)
  EXTRA_LIBS += $(wildcard $(shell $(CC) $(ARCHCPUFLAGS) --print-file-name=libm.a))
endif

ifeq ($(CONFIG_LIBSUPCXX),y)
  EXTRA_LIBS += $(wildcard $(shell $(CC) $(ARCHCPUFLAGS) --print-file-name=libsupc++.a))
endif
