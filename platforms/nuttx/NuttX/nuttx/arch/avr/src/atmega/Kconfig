#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

if ARCH_CHIP_ATMEGA
comment "ATMega Configuration Options"

choice
	prompt "Atmel ATmega chip selection"
	default ARCH_CHIP_ATMEGA128

config ARCH_CHIP_ATMEGA128
	bool "ATMega128"
	---help---
		Atmel ATMega128 8-bit AVR.

config ARCH_CHIP_ATMEGA1284P
	bool "ATMega1284P"
	---help---
		Atmel ATMega1284P 8-bit AVR.

config ARCH_CHIP_ATMEGA2560
	bool "ATMega2560"
	---help---
		Atmel ATMega2560 8-bit AVR.

endchoice # ATMega Configuration Options

menu "ATMega Peripheral Selections"

config AVR_USART0
	bool "USART0"
	select USART0_SERIALDRIVER
	select ARCH_HAVE_SERIAL_TERMIOS

config AVR_USART1
	bool "USART1"
	select USART1_SERIALDRIVER
	select <PERSON><PERSON>_HAVE_SERIAL_TERMIOS

endmenu # ATMega Peripheral Selections
endif
