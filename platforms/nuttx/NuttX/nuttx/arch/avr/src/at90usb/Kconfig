#
# For a description of the syntax of this configuration file,
# see the file kconfig-language.txt in the NuttX tools repository.
#

if ARCH_CHIP_AT90USB
comment "AT90USB Configuration Options"

choice
	prompt "Atmel ATmega chip selection"
	default ARCH_CHIP_AT90USB646

config ARCH_CHIP_AT90USB646
	bool "AT90USB646"
	---help---
		Atmel AT90USB646 8-bit AVR.

config ARCH_CHIP_AT90USB647
	bool "AT90USB647"
	---help---
		Atmel AT90USB647 8-bit AVR.

config ARCH_CHIP_AT90USB1286
	bool "AT90USB1286"
	---help---
		Atmel AT90USB1286 8-bit AVR.

config ARCH_CHIP_AT90USB1287
	bool "AT90USB1287"
	---help---
		Atmel AT90USB1287 8-bit AVR.

endchoice # Atmel ATmega chip selection

menu "AT90USB Peripheral Selections"

config AVR_SPI
	bool "SPI"
	default n

config AVR_USART1
	bool "USART1"
	default n
	select USART1_SERIALDRIVER

config AVR_USBDEV
	bool "USB device"
	default n

config AVR_WDT
	bool "Watchdog"
	default n

endmenu # AT90USB Peripheral Selections

config AVR_GPIOIRQ
	bool "GPIO pin interrupts"
	default n
	---help---
		Enable support for interrupting GPIO pins

endif # ARCH_CHIP_AT90USB
