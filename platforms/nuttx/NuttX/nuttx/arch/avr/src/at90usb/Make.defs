############################################################################
# arch/avr/src/at90usb/Make.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

# The start-up, "head", file

HEAD_ASRC = at90usb_head.S

# Common AVR files

CMN_ASRCS  = up_switchcontext.S
CMN_CSRCS  = up_allocateheap.c up_assert.c up_blocktask.c up_copystate.c
CMN_CSRCS += up_createstack.c up_doirq.c up_exit.c  up_idle.c up_initialize.c
CMN_CSRCS += up_initialstate.c up_irq.c up_lowputs.c
CMN_CSRCS += up_mdelay.c up_modifyreg8.c up_modifyreg16.c up_modifyreg32.c
CMN_CSRCS += up_nputs.c up_releasepending.c up_releasestack.c
CMN_CSRCS += up_reprioritizertr.c up_schedulesigaction.c up_sigdeliver.c
CMN_CSRCS += up_stackframe.c up_udelay.c up_unblocktask.c up_usestack.c

# Configuration-dependent common files

ifeq ($(CONFIG_ARCH_STACKDUMP),y)
CMN_CSRCS += up_dumpstate.c
endif

ifeq ($(CONFIG_AVR_SPI),y)
CMN_CSRCS += up_spi.c
endif

ifeq ($(CONFIG_STACK_COLORATION),y)
CMN_CSRCS += up_checkstack.c
endif

ifeq ($(CONFIG_ARCH_ROMGETC),y)
CMN_CSRCS += up_romgetc.c
endif

# Required AT90USB files

CHIP_ASRCS = at90usb_exceptions.S
CHIP_CSRCS = at90usb_lowconsole.c at90usb_lowinit.c at90usb_serial.c

# Configuration-dependent aT90USB files

ifneq ($(CONFIG_SCHED_TICKLESS),y)
CHIP_CSRCS += at90usb_timerisr.c
endif

ifeq ($(CONFIG_AVR_USBDEV),y)
CHIP_CSRCS += at90usb_usbdev.c
endif
