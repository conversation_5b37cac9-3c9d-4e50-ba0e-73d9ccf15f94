############################################################################
# arch/ceva/src/xm6/Toolchain.defs
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.  The
# ASF licenses this file to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance with the
# License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
# License for the specific language governing permissions and limitations
# under the License.
#
############################################################################

RTL_VERSION := $(CONFIG_ARCH_RTL_MAJOR).$(CONFIG_ARCH_RTL_MINOR).$(CONFIG_ARCH_RTL_REVISION)

ITCM_KB := $(shell expr $(CONFIG_ARCH_ITCM_SIZE) / 1024)
DTCM_KB := $(shell expr $(CONFIG_ARCH_DTCM_SIZE) / 1024)

#
# Supported toolchains
#
# Each toolchain definition should set:
#
#  CROSSDEV         The GNU toolchain triple (command prefix)
#  ARCROSSDEV       If required, an alternative prefix used when
#                   invoking ar and nm.
#  ARCHCPUFLAGS     CPU-specific flags selecting the instruction set
#                   FPU options, etc.
#  ARCHOPTIMIZATION The optimization level that results in
#                   reliable code generation.
#

ARCROSSDEV ?= $(CROSSDEV)
ifneq ($(CROSSDEV),)
  export LD_LIBRARY_PATH := $(CROSSDEV):$(LD_LIBRARY_PATH)
  export PATH            := $(CROSSDEV):$(PATH)
  export CEVAXMTOOLS     := $(CROSSDEV)
endif

ifeq ($(CONFIG_DEBUG_CUSTOMOPT),y)
  ARCHOPTIMIZATION += $(CONFIG_DEBUG_OPTLEVEL)
else ifeq ($(CONFIG_DEBUG_FULLOPT),y)
  ARCHOPTIMIZATION += -O3 -Os3
endif

ifneq ($(CONFIG_DEBUG_NOOPT),y)
  ARCHOPTIMIZATION += -fno-strict-aliasing
endif

ifeq ($(CONFIG_FRAME_POINTER),y)
  ARCHOPTIMIZATION += -fno-omit-frame-pointer -fno-optimize-sibling-calls
else
  ARCHOPTIMIZATION += -fomit-frame-pointer
endif

ifeq ($(CONFIG_DEBUG_SYMBOLS),y)
  ARCHOPTIMIZATION += -g
endif

ARCHCFLAGS += -fno-common
ARCHCXXFLAGS += -fno-common -nostdinc++

ARCHCFLAGS += -Wall -Wstrict-prototypes -Wshadow -Wundef
ARCHCXXFLAGS += -Wall -Wshadow -Wundef

ARCHCPUFLAGS  = -mrtl-version-$(RTL_VERSION) -Wa,-rtl$(RTL_VERSION)
ARCHCPUFLAGS += -Wa,-p

LDFLAGS += -alignAllSections,c:0x20,d:0x20
LDFLAGS += -internalCode$(ITCM_KB) -internalData$(DTCM_KB)

LIBGCC  = $(CROSSDEV)libs/cevaxm6/cevaxm6lib.lib
LIBGCC += $(CROSSDEV)libs/cevaxm6/complexlib.lib
LIBGCC += $(CROSSDEV)libs/cevaxm6/libcc.lib

ifneq ($(CONFIG_ARCH_NR_FPUS),)
  ARCHCPUFLAGS += -CG:SPU_FP_num=$(CONFIG_ARCH_NR_FPUS) -Wa,-fp=$(CONFIG_ARCH_NR_FPUS)
endif

CC  = $(CROSSDEV)/cevaxm6cc -mquiet -Wa,-quiet
CXX = $(CROSSDEV)/cevaxm6cc -mquiet -Wa,-quiet -x c++
CPP = $(CROSSDEV)/cevaxm6cc -mquiet -Wa,-quiet -E -P -x c
