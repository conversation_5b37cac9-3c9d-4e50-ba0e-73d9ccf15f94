/****************************************************************************
 * arch/arm64/src/common/arm64_cpu_idle.S
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

#include <nuttx/config.h>

#include "arm64_arch.h"
#include "arm64_macro.inc"

/****************************************************************************
 * Public Symbols
 ****************************************************************************/

    .file    "arm64_cpu_idle.S"

/****************************************************************************
 * Assembly Macros
 ****************************************************************************/

/****************************************************************************
 * Private Functions
 ****************************************************************************/

/****************************************************************************
 * Public Functions
 ****************************************************************************/

GTEXT(arch_cpu_idle)
SECTION_FUNC(text, arch_cpu_idle)
#ifdef CONFIG_TRACING_IDLE
    stp    xzr, x30, [sp, #-16]!
    bl     sys_trace_idle
    ldp    xzr, x30, [sp], #16
#endif
    dsb    sy
    wfi
    msr    daifclr, #(DAIFCLR_IRQ_BIT)
    ret

GTEXT(arch_cpu_atomic_idle)
SECTION_FUNC(text, arch_cpu_atomic_idle)
#ifdef CONFIG_TRACING_IDLE
    stp    xzr, x30, [sp, #-16]!
    bl  sys_trace_idle
    ldp    xzr, x30, [sp], #16
#endif
    msr    daifset, #(DAIFSET_IRQ_BIT)
    isb
    wfe
    tst    x0, #(DAIF_IRQ_BIT)
    beq    _irq_disabled
    msr    daifclr, #(DAIFCLR_IRQ_BIT)
_irq_disabled:
    ret
